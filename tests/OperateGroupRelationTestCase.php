<?php

namespace tests;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_SAVE_MODE;
use addon\operateGroup\model\OperateGroup;
use addon\operateGroup\model\OperateGroupRelation;
use addon\operateGroup\service\OperateGroupRelationService;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use app\Exceptions\ServerException;
use think\helper\Str;

/**
 * 运营组关联数据服务测试
 * Class OperateGroupRelationTestCase
 */
class OperateGroupRelationTestCase extends BaseTestCase
{
    /**
     * 实例化服务
     * @return OperateGroupRelationService
     */
    public function testNewService(): OperateGroupRelationService {
        $service = new OperateGroupRelationService();
        $this->assertTrue(get_class($service) == OperateGroupRelationService::class, '实例化失败');
        return $service;
    }

    /**
     * 测试校验关联数据方法
     * @param string $relation_type
     * @return array
     * @throws ServerException
     */
    public function testCheckRelationData(string $relation_type = OPERATE_GROUP_RELATION_TYPE::GOODS): array {
        $service = $this->testNewService();
        // 实例化关联类
        $relation_type = new $relation_type;
        // 找出10个存在的主键
        $random_index = $relation_type->limit(10)->column($relation_type->getPk());
        $this->assertCount(0, array_diff($service->checkRelationData(get_class($relation_type), $random_index), $random_index), '包含不存在的主键');
        try {
            // 设置一个不存在的主键
            $not_exists_index = -1;
            array_push($random_index, $not_exists_index);
            $this->assertNotCount(0, array_diff($service->checkRelationData(get_class($relation_type), $random_index), $random_index), '混入不存在主键:' . $not_exists_index);
        } catch (ServerException $exception) {
            // 会进入异常表示拦截成功
            array_pop($random_index);
        }
        return [$relation_type, $random_index];
    }

    // 根据运营组ID查找信息
    public function testFind() {
        $service = $this->testNewService();
        // 提取一条运营组关联数据
        $first_data = (new OperateGroupRelation())->where(true)->find();
        $data = $service->selectByOperateGroupId($first_data->operate_group_id);
        $find_pk = array_unique($data->column('operate_group_id'));
        $this->assertCount(1, $find_pk, '存在多个运营组ID');
        $this->assertEquals($first_data->operate_group_id, $find_pk[0], '返回运营组ID不正确');
    }

    // 测试保存数据
    public function testSaveData() {
        $service = $this->testNewService();
        $index_column = 'operate_group_id';
        // 提取一条运营组关联数据
        $id = (new OperateGroupRelation())->where(true)->value($index_column) ?: (new OperateGroup())->where(true)->value($index_column);
        $this->assertNotNull($id, '关联表数据为空');
        $test_data = $this->testCheckRelationData();
        $service->save($id, get_class($test_data[0]), $test_data[1]);
    }
}