<?php

namespace tests;

use app\model\web\DiyView;
use app\service\operateGroup\OperateService;
use think\helper\Str;

/**
 * 装修店铺测试
 * Class OperateGroupTestCase
 */
class DiyViewTestCase extends BaseTestCase
{

    /**
     * @dataProvider additionProvider
     *
     * @param [type] $operate_group_id
     * @return void
     */
    public function testDiyIndex($operate_group_id, $name){
        $diyViewModel = new DiyView();
        $res = $diyViewModel->findGroupDiyView($operate_group_id, $name);
        $this->assertNotEmpty($res);
    }

    public function additionProvider(){
        return array(
            [1, "DIYVIEW_INDEX"],
            [0, "DIYVIEW_INDEX"],
            [2, "DIYVIEW_INDEX"],
        );
    }


}