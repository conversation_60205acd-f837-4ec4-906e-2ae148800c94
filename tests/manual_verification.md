# 短信功能手动验证文档

本文档用于指导如何手动验证短信发送功能，特别是验证使用新的模板系统发送短信验证码的功能。

## 验证步骤

### 1. 自动化测试

运行单元测试来验证基本功能：

```bash
cd /path/to/project
vendor/bin/phpunit tests/Sms/SmsTest.php
```

可以运行单个测试方法：

```bash
cd /path/to/project
vendor/bin/phpunit --filter testSendMobileCode tests/Sms/SmsTest.php
```

### 2. API 接口测试

使用 Postman 或其他 API 测试工具发送请求：

**请求示例**:
- URL: `/api/member/sendMobileCode`
- 方法: POST
- 参数:
  ```json
  {
    "mobile": "***********",
    "type": 4,
    "captcha": "captcha" 
  }
  ```

**期望结果**:
- 状态码: 200
- 响应体:
  ```json
  {
    "code": 0,
    "message": "操作成功"
  }
  ```

### 3. 数据库验证

验证验证码是否正确存储在数据库中:

```sql
SELECT * FROM verify_code WHERE mobile = '***********' ORDER BY instime DESC LIMIT 1;
```

应该能看到一条包含以下字段的记录:
- `mobile`: 手机号
- `type`: 验证码类型
- `code`: 4位数字验证码
- `instime`: 插入时间
- `cnt`: 发送计数

### 4. 缓存验证

检查缓存中是否存储了验证码:

1. 获取生成的缓存键值:
   ```
   {mobile}-{type}
   ```
   例如: `***********-4`

2. 检查缓存内容:
   应该包含手机号、类型和验证码

### 5. 短信发送验证

确认是否成功使用模板发送了短信:

1. 检查 `message_sms_records` 表中的记录:
   ```sql
   SELECT * FROM message_sms_records WHERE account = '***********' ORDER BY create_time DESC LIMIT 1;
   ```

2. 确认记录中:
   - `keywords` 字段为 `VERIFICATION_CODE`
   - `status` 字段为 `1` (发送成功)
   - `content` 字段包含验证码

3. 如果能够接收短信，检查手机是否收到包含正确验证码的短信

### 6. 关键问题排查

如果遇到问题，检查以下可能的原因:

1. 验证码生成问题:
   - 检查 `extend/Sms.php` 中的 `sms_rqcode` 方法是否正确生成验证码
   - 确认 `$use_new_sms` 参数是否正确传递

2. 模板问题:
   - 确认 `VERIFICATION_CODE` 关键字对应的模板是否存在
   - 检查 `message` 表中是否有对应的模板记录

3. 短信发送问题:
   - 检查短信服务商配置是否正确
   - 验证短信账户余额是否充足

## 切换测试

要测试不同的短信发送方式，可以在 `app/api/controller/Member.php` 中的 `sendMobileCode` 方法中修改 `sms_rqcode` 的最后一个参数:

```php
// 使用新系统
$res = (new \extend\Sms())->sms_rqcode($this->params['mobile'], $this->params['type'], true, true);

// 使用旧系统
$res = (new \extend\Sms())->sms_rqcode($this->params['mobile'], $this->params['type'], true, false);
``` 