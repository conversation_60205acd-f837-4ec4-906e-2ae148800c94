<?php

namespace tests;

use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;
use think\App;

require __DIR__ . '/../vendor/autoload.php';

/**
 * 单元测试基类
 * Class BaseTestCase
 */
class BaseTestCase extends \PHPUnit\Framework\TestCase
{
    /**
     * @var Client 请求类
     */
    public $client;

    public function __construct(?string $name = null, array $data = [], $dataName = '') {
        parent::__construct($name, $data, $dataName);

        // 初始化TP框架
        (new App())->http->run();

        // 设置默认参数
        $this->client = new Client([
            'base_uri' => 'https://youpin-dev.jiufuwangluo.com/',
            'timeout' => 10,
            'verify' => false
        ]);
    }

    /**
     * 获取请求数据
     * @param ResponseInterface $res
     * @param bool $isJson 是否json解码
     * @return array|bool|float|int|object|string|null
     */
    public function getContents(ResponseInterface $res, $isJson = true) {
        $content = (string)$res->getBody();
        if ($isJson) {
            $content = \GuzzleHttp\json_decode($content, true);
        }
        return $content;
    }

    /**
     * 是否请求成功
     * @param ResponseInterface $res
     * @return bool
     */
    public function isSuccessful(ResponseInterface $res): bool {
        return $this->getContents($res)['code'] === 0;
    }

    /**
     * 获取异常消息
     * @param ResponseInterface $res
     * @return string
     */
    public function getMessage(ResponseInterface $res): string {
        return $this->getContents($res)['message'];
    }
}