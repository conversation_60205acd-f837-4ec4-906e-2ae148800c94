<?php

namespace tests;

use addon\operateGroup\service\OperateGroupService;
use think\helper\Str;

/**
 * 运营组服务测试
 * Class OperateGroupTestCase
 */
class OperateGroupTestCase extends BaseTestCase
{
    /**
     * 实例化服务
     * @return OperateGroupService
     */
    public function testNewService(): OperateGroupService {
        $service = new OperateGroupService();
        $this->assertTrue(get_class($service) == OperateGroupService::class, '实例化失败');
        return $service;
    }

    /**
     * 创建测试数据
     * @param array $bastData
     * @return array
     */
    private function createTestData(array $bastData = []): array {
        return array_merge([
            'operate_group_name' => Str::random(8),
            'status' => random_int(0, 1),
            'description' => Str::random(20)
        ], $bastData);
    }

    // CURD
    public function testCURD($newData = []) {
        $service = $this->testNewService();
        $is_save = false;
        if (!empty($newData) && isset($newData['operate_group_id'])) {
            $data = $newData;
            $operateGroup = $service->save($newData['operate_group_id'], $newData);
            $this->assertNotFalse($operateGroup, '数据更新失败');
            $is_save = true;
        } else {
            $data = $this->createTestData();
            $operateGroup = $service->create($data['operate_group_name'], $data['status'], $data['description']);
            $this->assertNotNull($operateGroup, '数据创建失败');
        }
        $lastData = $service->find($operateGroup->getKey());
        $this->assertNotNull($lastData, '查找数据失败');
        foreach ($data as $key => $value) {
            $this->assertEquals($value, $lastData[$key], "{$key}字段数据不一致");
        }

        if (!$is_save) {
            $this->testCURD($this->createTestData([$lastData->getPk() => $lastData->getKey()]));
        } else {
            $this->assertTrue($service->delete($lastData->getKey()), '数据删除失败');
        }
    }
}