<?php


namespace tests;


use app\model\clue\ClueUsers;
use app\model\clue\ClueUsersTags;
use app\model\enterpriseWx\EnterpriseTagModel;
use app\model\enterpriseWx\MemberEnterpriseTagModel;
use app\model\enterpriseWx\MemberEnterpriseWechatModel;
use app\model\member\MemberModel;
use app\service\enterpriseWx\EnterpriseService;

class ClueTest extends BaseTestCase
{
    public function testRegisterMember()
    {
        $mobile = "13510358977";
        $member = MemberModel::where("mobile", $mobile)->where("status",1)->find();
        event("MemberRegisterEvent", ["mobile" => $mobile, "member_id"=>$member->member_id]);

        $member = MemberModel::where("mobile", $mobile)->where("status",1)->find();
        $hasEwehcat = MemberEnterpriseWechatModel::where("member_id", $member->member_id)->count();
        $clueUser = ClueUsers::where("mobile", $mobile)->find();

        $cTags = ClueUsersTags::where("mobile", $mobile)->select();
        if($member->enterpriseWechat && $cTags->count() > 0)
        {
            $cTagIds = EnterpriseTagModel::whereIn("tag_id", $cTags->column("tag_id"))->where("is_sync_enterprise_wx", 1)->column("enterprise_wechat_tag_id");
            $eTagIds = [];
            $eService = new EnterpriseService();
            $result = $eService->getEnterpriseCustomerDetailByUserId($member->enterpriseWechat->external_userid);
            dd($result);
            foreach($result['follow_user'][0]['tags'] as $t)
            {
                $eTagIds[] = $t['tag_id'];
            }

            $diffArr = array_diff($cTagIds, $eTagIds);
            $this->assertEquals([], $diffArr);

            $tagCount = MemberEnterpriseTagModel::where("member_id", $member->member_id)->count();
            $this->assertGreaterThan(0, $tagCount);
        }


        $this->assertGreaterThan(0, $hasEwehcat);
        $this->assertEquals(1, $clueUser->is_reg);
        $this->assertGreaterThan(0, $clueUser->status);
    }
}