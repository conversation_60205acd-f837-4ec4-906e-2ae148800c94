<?php

namespace tests;
use app\model\member\MemberModel;
use app\service\Activity\SeckillOrderService;
use Carbon\Carbon;

/**
 * HTTP测试
 * Class HttpTestCase
 */
class HttpTestCase extends BaseTestCase
{
    // 测试首页
    public function testWetSite() {
        $aa = '牙  ./_、  。\-囗*#: ～牙丷克丷囗口丷屮彡十冫丶卩 亻彳凵〇冖扌卩阝\+；，丶克丶石1,# 丨, 、卐卍,牙卐克石401、、。、〇、😊 ! @ # $ % ^ & * ( ) - _ = + [ ] { } ; :, . / ? \ | ~ ` < >、 。 〃 〄 々 〆 〇 〈 〉 《 》 「 」 『 』 【 】 〒 〓± × ÷ ≠ ≈ ≤ ≥ ∑ ∞ ∫ √ ∂ ∆ ∇ ∈ ∩ ∪ ⊂ ⊃€ £ ¥ ₩ ₹ $ ₿ ¢ ₪ ฿↑ ↓ ← → ↖ ↗ ↘ ↙ ⇧ ⇩ ⇦ ⇨ ↔ ↕■ □ ▢ ▣ ▪ ▫ ▲ △ ▼ ▽ ◆ ◇ ◈ ★ ☆ ⬛ ⬜☀ ☁ ☂ ☃ ☄ ☈ ☉ ☊ ☋ ☌ ☍ ☎ ☏ ☑ ☒ ☐ ☓ ☔ ☕Ⓐ Ⓑ Ⓒ Ⓓ Ⓔ Ⓕ Ⓖ Ⓗ Ⓘ Ⓙ Ⓚ Ⓛ Ⓜ Ⓝ Ⓞ Ⓟ Ⓠ Ⓡ Ⓢ Ⓣ Ⓤ Ⓥ Ⓦ Ⓧ Ⓨ Ⓩ𓀀 𓀁 𓀂 𓀃 𓀄 𓀅 𓀆 𓀇 𓀈 𓀉 𓀊 𓀋 𓀌😀 😁 😂 🤣 😃 〇 ✈ 🚀 🚗 ∞ ☀ ☆ ★ Ⓐ Ⓩ🎉 ❤️〃、〈 。〇〇~ 克11 石市蓝莓123小区5号楼一单元401牙克丨石市';
        $aa = preg_replace("/[^\p{Han}]/u", "", $aa);
        $aa = preg_replace("/[\x{3000}-\x{303F}]/u", "", $aa);
        // 步骤二：移除所有符号类别的汉字字符
        $aa = preg_replace("/\p{So}|\p{Sc}|\p{Sk}|\p{Sm}/u", "", $aa);
//        $aa = preg_replace("/[\x{2E80}-\x{2EFF}\x{2F00}-\x{2FDF}\x{3000}-\x{303F}]/u", "", $aa);

        // 如果需要，进一步移除特定的偏旁部首
        $aa = preg_replace("/[\x{5350}\x{534D}\x{4E36}]/u", "", $aa);
        $exclude_chars = [
            '丨','一','囗', '丷', '口', '屮', '彡', '十', '冫', '卩', '亻',
            '彳', '凵', '冖', '扌', '阝', '氵', '忄', '艹','讠', '钅', '礻',
            '囗', '卐', '卍', '丶'
        ];
        $aa = str_replace($exclude_chars, "", $aa);
        dd($aa);

        $res = $this->client->request('get', 'api/website/index');
        $this->assertTrue($this->isSuccessful($res), $this->getMessage($res));
        $data = $this->getContents($res, false);
        $this->assertJson($data);
//        echo $data;
    }

    // 测试登录
    public function testLogin() {

        $aa = checkRealName("441427199508131526", "罗小芳", "18218186067");
        dd($aa);

        $res = $this->client->post('/shopapi/login/appShopLogin', ['form_params' => ['username' => '15936945218', 'password' => '123456']]);
        $this->assertTrue($this->isSuccessful($res), $this->getMessage($res));
    }
}