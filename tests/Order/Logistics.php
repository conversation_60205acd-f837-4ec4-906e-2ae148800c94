<?php

namespace tests\Order;

use app\listener\UpdateLogisticsInfoFromSupplyChain;
use tests\BaseTestCase;

class Logistics extends BaseTestCase
{
    // 同步供应链物流信息
    public function testSyncSupplyChain() {
        $listener = new UpdateLogisticsInfoFromSupplyChain();
        $data = array(
            "order_goods_id" => 6559,
            "admin_remark" => '管理员备注',
            "refund_refuse_reason" => '退款拒绝原因'
        );
        dd($listener->handle($data));
    }
}
