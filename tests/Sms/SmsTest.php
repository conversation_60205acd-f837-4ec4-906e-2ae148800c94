<?php

namespace tests\Sms;

use tests\BaseTestCase;

/**
 * 短信功能测试
 * Class SmsTest
 */
class SmsTest extends BaseTestCase
{
    /**
     * 测试用手机号
     * @var string
     */
    protected $testMobile = '14716986676';
    
    /**
     * 测试前先清理数据，避免频率限制
     */
    protected function setUp()
    {
        parent::setUp();
        
        // 清理验证码记录以避免频率限制
        try {
            \think\facade\Db::name('verify_code')
                ->where('mobile', $this->testMobile)
                ->delete();
                
            // 等待1秒钟
            sleep(1);
        } catch (\Exception $e) {
            // 忽略错误
        }
    }

    /**
     * 测试发送短信验证码 - 新系统
     * @return void
     */
    public function testSendMobileCode()
    {
        // 构造请求参数
        $formParams = [
            'mobile' => $this->testMobile,
            'type' => 4, // 默认type为4，绑定手机
            'captcha' => 'captcha' // 假设验证码已通过
        ];
        
        // 发送请求
        $res = $this->client->post('/api/member/sendMobileCode', [
            'form_params' => $formParams
        ]);
        dump($this->getContents($res, false));

        // 如果失败，检查是否因为频率限制
        $content = $this->getContents($res);
        if (isset($content['code']) && $content['code'] < 0) {
            if (strpos($content['message'], '频繁') !== false || strpos($content['message'], '限制') !== false) {
                $this->markTestSkipped('测试跳过：发送频率受限，请稍后再试');
            }
        }
        
        // 断言
        $this->assertTrue($this->isSuccessful($res), '短信发送失败: ' . $this->getMessage($res));
        $this->assertArrayHasKey('code', $this->getContents($res));
        $this->assertEquals(0, $this->getContents($res)['code']);
        
        echo "测试成功：短信发送到 {$this->testMobile}\n";
        
        // 验证数据库记录
        $verify = \think\facade\Db::name('verify_code')
            ->where('mobile', $this->testMobile)
            ->where('type', 4)
            ->order('instime', 'desc')
            ->find();
            
        $this->assertNotEmpty($verify, '验证码未保存到数据库');
        $this->assertNotEmpty($verify['instime'], '验证码创建时间不正确');
        
        // 验证短信记录
        $smsRecord = \think\facade\Db::name('message_sms_records')
            ->where('account', $this->testMobile)
            ->order('create_time', 'desc')
            ->find();
            
        $this->assertNotEmpty($smsRecord, '未发现短信发送记录');
        $this->assertEquals('VERIFICATION_CODE', $smsRecord['keywords'], '短信关键字不正确');
        $this->assertEquals(1, $smsRecord['status'], '短信发送状态不正确');
    }
    
    /**
     * 测试验证码生成但不发送短信
     * @return void
     */
    public function testGenerateCodeWithoutSending()
    {
        // 调用sms_rqcode方法生成验证码但不发送短信
        $sms = new \extend\Sms();
        $result = $sms->sms_rqcode($this->testMobile, 4, true, true);
        
        // 如果因为频率限制失败，则跳过测试
        if (isset($result['code']) && $result['code'] < 0) {
            if (strpos($result['msg'], '频繁') !== false) {
                $this->markTestSkipped('测试跳过：发送频率受限，请稍后再试');
            } else {
                $this->fail('验证码生成失败: ' . ($result['msg'] ?? '未知错误'));
            }
        }
        
        // 断言
        $this->assertEquals(0, $result['code'], '验证码生成失败: ' . ($result['msg'] ?? '未知错误'));
        $this->assertNotEmpty($result['sms'], '未获取到验证码');
        $this->assertTrue(is_numeric($result['sms']), '验证码不是数字');
        $this->assertRegExp('/^\d{4}$/', (string)$result['sms'], '验证码应为4位数字');
        
        echo "测试成功：生成验证码但不发送短信，手机号: {$this->testMobile}，验证码: {$result['sms']}\n";
    }
    
    /**
     * 测试新旧短信系统切换
     * @return void
     */
    public function testSwitchingSmsSystem()
    {
        // 这个测试只验证接口调用，不发送实际短信
        $sms = new \extend\Sms();
        
        // 测试新系统
        $resultNew = $sms->sms_rqcode($this->testMobile, mt_rand(100, 999), false, true);
        
        // 如果因为频率限制失败，则跳过测试
        if (isset($resultNew['code']) && $resultNew['code'] < 0) {
            if (strpos($resultNew['msg'], '频繁') !== false) {
                $this->markTestSkipped('测试跳过：发送频率受限，请稍后再试');
            }
        }
        
        $this->assertEquals(0, $resultNew['code'], '新系统验证码生成失败');
        $codeNew = $resultNew['sms'];
        
        // 测试旧系统
        $resultOld = $sms->sms_rqcode($this->testMobile, mt_rand(100, 999), false, false);
        
        // 如果因为频率限制失败，则跳过测试
        if (isset($resultOld['code']) && $resultOld['code'] < 0) {
            if (strpos($resultOld['msg'], '频繁') !== false) {
                $this->markTestSkipped('测试跳过：发送频率受限，请稍后再试');
            }
        }
        
        $this->assertEquals(0, $resultOld['code'], '旧系统验证码生成失败');
        $codeOld = $resultOld['sms'];
        
        echo "测试成功：切换系统测试通过，新系统验证码: {$codeNew}，旧系统验证码: {$codeOld}\n";
    }
} 