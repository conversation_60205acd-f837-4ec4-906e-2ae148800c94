<?php

namespace tests;

use addon\appModule\model\Modules;
use think\helper\Str;

/**
 * Class AppModuleTestCase
 */
class AppModuleTestCase extends BaseTestCase
{
    /**
     * @return Modules
     */
    public function testNewModel(): Modules {
        $model = new Modules();
        $this->assertTrue(get_class($model) == Modules::class, '实例化失败');
        return $model;
    }

    /**
     * 创建测试数据
     * @param array $bastData
     * @return array
     */
    private function createTestData(array $bastData = []): array {
        return array_merge([
            'module' => Str::random(8),
            'module_name' => Str::random(8),
            'status' => random_int(0, 1),
            'description' => Str::random(8),
        ], $bastData);
    }

    // CURD
    public function testCURD($newData = []) {
        $model = $this->testNewModel();
        $is_save = false;
        if (!empty($newData) && isset($newData[$model->getPk()])) {
            $data = $newData;
            $appModel = $model->find($newData[$model->getPk()]);
            $this->assertTrue($appModel->save($data), '数据更新失败');
            $is_save = true;
        } else {
            $data = $this->createTestData();
            $appModel = $model->create($data);
            $this->assertNotNull($appModel, '数据创建失败');
        }
        $lastData = $model->find($appModel->getKey());
        $this->assertNotNull($lastData, '查找数据失败');
        foreach ($data as $key => $value) {
            $this->assertEquals($value, $lastData[$key], "{$key}字段数据不一致");
        }

        if (!$is_save) {
            $this->testCURD($this->createTestData([$lastData->getPk() => $lastData->getKey()]));
        }else{
            $this->assertTrue($lastData->delete(), '数据删除失败');
        }
    }

    //
}