<?php
namespace tests\Customs;

use tests\BaseTestCase;
use addon\customsChinaport\service\ChinaportService;
use addon\customsChinaport\model\ChinaportConfig;
use addon\customsChinaport\model\ChinaportTask;
use ReflectionClass;

/**
 * 海关接口单元测试
 * Class ChinaportApiTest
 */
class ChinaportApiTest extends BaseTestCase
{
    /**
     * @var ChinaportService
     */
    protected $service;

    /**
     * @var ChinaportConfig
     */
    protected $config;

    /**
     * 初始化
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ChinaportService();
        $this->config = new ChinaportConfig();
        
        // 确保测试环境配置正确
        $this->setupTestConfig();
    }

    /**
     * 设置测试配置
     */
    protected function setupTestConfig()
    {
        // 设置测试配置
        $test_config = [
            'client_sign_key' => 'test_sign_key',
            'goods_url_prefix' => 'https://test.example.com/goods',
            'recp_account' => 'test_account',
            'recp_name' => 'Test Name'
        ];
        
        $this->config->setConfig($test_config);
    }

    /**
     * 测试验证签名方法 - 有效签名
     */
    public function testVerifySignWithValidSign()
    {
        // 设置测试签名密钥
        $sign_key = 'test_sign_key';
        $this->config->setConfig(['client_sign_key' => $sign_key]);
        
        // 生成有效签名
        $timestamp = time();
        $valid_sign = md5($timestamp . $sign_key);
        
        // 验证有效签名
        $result = $this->service->verifySign($timestamp, $valid_sign);
        $this->assertTrue($result);
    }
    
    /**
     * 测试验证签名方法 - 无效签名
     */
    public function testVerifySignWithInvalidSign()
    {
        // 设置测试签名密钥
        $sign_key = 'test_sign_key';
        $this->config->setConfig(['client_sign_key' => $sign_key]);
        
        // 生成无效签名
        $timestamp = time();
        $invalid_sign = md5($timestamp . 'wrong_key');
        
        // 验证无效签名
        $result = $this->service->verifySign($timestamp, $invalid_sign);
        $this->assertFalse($result);
    }
    
    /**
     * 测试验证签名方法 - 过期时间戳
     */
    public function testVerifySignWithExpiredTimestamp()
    {
        // 设置测试签名密钥
        $sign_key = 'test_sign_key';
        $this->config->setConfig(['client_sign_key' => $sign_key]);
        
        // 验证过期时间戳
        $expired_timestamp = time() - 60; // 60秒前的时间戳
        $expired_sign = md5($expired_timestamp . $sign_key);
        
        $result = $this->service->verifySign($expired_timestamp, $expired_sign);
        $this->assertFalse($result);
    }
} 