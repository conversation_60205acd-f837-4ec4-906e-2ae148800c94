<?php
namespace tests\Customs\Chinaport;

use tests\BaseTestCase;
use addon\customsChinaport\service\SignatureService;

/**
 * 签名服务测试类的测试子类
 * 用于覆盖私有方法以便测试
 */
class TestableSignatureService extends SignatureService
{
    /**
     * 覆盖构造函数以避免配置依赖
     */
    public function __construct()
    {
        // 不调用父类构造函数，避免依赖配置
    }
    
    /**
     * 覆盖签名XML方法，返回固定测试数据
     */
    public function signXml($xmlData)
    {
        return [
            'code' => '10000',
            'message' => '签名成功',
            'signedData' => 'mockSignedData123456',
            'certificate' => 'mockCertificate123456',
            'digestValue' => 'mockDigestValue123456',
            'signatureNode' => '<ds:SignedInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"></ds:CanonicalizationMethod><ds:SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"></ds:SignatureMethod><ds:Reference URI=""><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"></ds:Transform></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"></ds:DigestMethod><ds:DigestValue>mockDigestValue123456</ds:DigestValue></ds:Reference></ds:SignedInfo>',
            'certNo' => 'mockCertNo123456',
            'success' => true
        ];
    }
    
    /**
     * 覆盖签名179方法，返回固定测试数据
     */
    public function sign179($data179)
    {
        return [
            'code' => '10000',
            'message' => '签名成功',
            'signedData' => 'mockSignedData123456',
            'certNo' => 'mockCertNo123456',
            'success' => true
        ];
    }
    
    /**
     * 检测是否为XML数据
     */
    public function isXmlDataTest($data)
    {
        $data = trim($data);
        return (stripos($data, '<?xml') === 0 || stripos($data, '<ceb:') === 0);
    }
    
    /**
     * 检测是否为179数据
     */
    public function is179DataTest($data)
    {
        $data = trim($data);
        return (stripos($data, '"sessionID":"') !== false && stripos($data, '"payExchangeInfoHead":"') !== false);
    }
    
    /**
     * 构建带有签名的完整XML 
     * 简化版，不依赖DOM操作，仅用于测试
     */
    public function buildSignedXml($xmlData, $signResult)
    {
        // 检查签名结果是否成功
        if ($signResult['code'] != '10000' || empty($signResult['signatureNode']) || empty($signResult['signedData'])) {
            throw new \Exception('签名结果不完整，无法构建签名XML');
        }
        
        // 返回一个简化的签名XML
        return '<?xml version="1.0" encoding="UTF-8"?>' .
               '<ceb:CEB311Message xmlns:ceb="http://www.chinaport.gov.cn/ceb" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" guid="TEST-GUID-123" version="1.0">' .
               '<ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#">' .
               $signResult['signatureNode'] .
               '<ds:SignatureValue>' . $signResult['signedData'] . '</ds:SignatureValue>' .
               '<ds:KeyInfo><ds:X509Data><ds:X509Certificate>' . $signResult['certificate'] . '</ds:X509Certificate></ds:X509Data></ds:KeyInfo>' .
               '</ds:Signature>' .
               '</ceb:CEB311Message>';
    }
}

/**
 * 签名服务测试类
 */
class SignatureServiceTest extends BaseTestCase
{
    /**
     * @var TestableSignatureService
     */
    protected $signatureService;
    
    /**
     * 设置测试环境
     */
    public function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用子类实例
        $this->signatureService = new TestableSignatureService();
    }
    
    /**
     * 清理测试环境
     */
    public function tearDown(): void
    {
        $this->signatureService = null;
        parent::tearDown();
    }
    
    /**
     * 测试签名服务 - XML数据
     */
    public function testSignXmlService()
    {
        // 创建XML测试数据
        $xmlData = '<?xml version="1.0" encoding="UTF-8"?><ceb:CEB311Message xmlns:ceb="http://www.chinaport.gov.cn/ceb" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" guid="TEST-GUID-123" version="1.0"></ceb:CEB311Message>';
        
        // 调用签名服务
        $result = $this->signatureService->signXml($xmlData);
        
        // 验证返回值
        $this->assertIsArray($result);
        $this->assertArrayHasKey('code', $result);
        $this->assertEquals('10000', $result['code']);
        $this->assertArrayHasKey('signedData', $result);
        $this->assertNotEmpty($result['signedData']);
        $this->assertArrayHasKey('certificate', $result);
        $this->assertArrayHasKey('digestValue', $result);
        $this->assertArrayHasKey('signatureNode', $result);
        $this->assertArrayHasKey('certNo', $result);
    }
    
    /**
     * 测试签名服务 - 179数据
     */
    public function test179SignService()
    {
        // 创建179测试数据
        $data179 = '"sessionID":"test"||"payExchangeInfoHead":"test"||"payExchangeInfoLists":"test"||"serviceTime":"123456789"';
        
        // 调用签名服务
        $result = $this->signatureService->sign179($data179);
        
        // 验证返回值
        $this->assertIsArray($result);
        $this->assertArrayHasKey('code', $result);
        $this->assertEquals('10000', $result['code']);
        $this->assertArrayHasKey('signedData', $result);
        $this->assertNotEmpty($result['signedData']);
        $this->assertArrayHasKey('certNo', $result);
    }
    
    /**
     * 测试自动检测数据类型功能
     */
    public function testAutoDetectDataType()
    {
        // 测试XML检测
        $xmlData = '<?xml version="1.0" encoding="UTF-8"?><ceb:CEB311Message xmlns:ceb="http://www.chinaport.gov.cn/ceb"></ceb:CEB311Message>';
        $this->assertTrue($this->signatureService->isXmlDataTest($xmlData));
        
        // 测试179数据检测
        $data179 = '"sessionID":"test"||"payExchangeInfoHead":"test"';
        $this->assertTrue($this->signatureService->is179DataTest($data179));
        
        // 测试未知数据类型
        $unknownData = '{"test":"value"}';
        $this->assertFalse($this->signatureService->is179DataTest($unknownData));
    }
    
    /**
     * 测试构建签名XML
     */
    public function testBuildSignedXml()
    {
        // 创建XML测试数据
        $xmlData = '<?xml version="1.0" encoding="UTF-8"?><ceb:CEB311Message xmlns:ceb="http://www.chinaport.gov.cn/ceb" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" guid="TEST-GUID-123" version="1.0"></ceb:CEB311Message>';
        
        // 创建签名结果数据，注意要包含code字段
        $signResult = [
            'code' => '10000',
            'signedData' => 'mockSignedData123456',
            'certificate' => 'mockCertificate123456',
            'digestValue' => 'mockDigestValue123456',
            'signatureNode' => '<ds:SignedInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"></ds:CanonicalizationMethod><ds:SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"></ds:SignatureMethod><ds:Reference URI=""><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"></ds:Transform></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"></ds:DigestMethod><ds:DigestValue>mockDigestValue123456</ds:DigestValue></ds:Reference></ds:SignedInfo>'
        ];
        
        // 调用构建签名XML方法
        $result = $this->signatureService->buildSignedXml($xmlData, $signResult);
        
        // 验证结果
        $this->assertIsString($result);
        $this->assertStringContainsString('<ds:Signature', $result);
        $this->assertStringContainsString('<ds:SignatureValue>', $result);
        $this->assertStringContainsString('<ds:X509Certificate>', $result);
    }
} 