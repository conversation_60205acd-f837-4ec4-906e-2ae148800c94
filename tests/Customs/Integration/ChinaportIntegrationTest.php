<?php
namespace tests\Customs\Integration;

use tests\BaseTestCase;

/**
 * 海关接口集成测试类
 */
class ChinaportIntegrationTest extends BaseTestCase
{
    /**
     * 设置测试环境
     */
    public function setUp(): void
    {
        parent::setUp();
    }
    
    /**
     * 清理测试环境
     */
    public function tearDown(): void
    {
        parent::tearDown();
    }
    
    /**
     * 测试海关数据获取接口
     */
    public function testPlatDataOpen()
    {
        // 构造测试请求数据
        $requestData = [
            'orderNo' => 'TEST_ORDER_' . date('YmdHis'),
            'sessionID' => 'TEST_SESSION_' . uniqid(),
            'serviceTime' => time() * 1000
        ];
        
        try {
            // 发送请求
            $response = $this->client->post('customs_chinaport/chinaport/platDataOpen', [
                'form_params' => $requestData
            ]);
            
            // 获取响应数据
            $responseData = $this->getContents($response);
            
            // 输出响应结果，便于调试
            echo "响应数据：\n";
            print_r($responseData);
            
            // 验证响应结构
            $this->assertIsArray($responseData);
            $this->assertArrayHasKey('code', $responseData);
            
            // 如果成功返回了有效数据
            if ($responseData['code'] == '10000') {
                $this->assertEquals('10000', $responseData['code']);
                $this->assertArrayHasKey('data', $responseData);
                $this->assertArrayHasKey('payExInfoStr', $responseData['data']);
                $this->assertNotEmpty($responseData['data']['payExInfoStr']);
            } else {
                // 记录失败信息但不直接断言失败
                echo "注意：接口调用返回错误，错误码：{$responseData['code']}\n";
                echo "错误信息：{$responseData['message']}\n";
            }
        } catch (\Exception $e) {
            $this->fail("请求异常：" . $e->getMessage());
        }
    }
    
    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        // 缺少必要参数的请求
        $invalidRequest = [
            'orderNo' => 'TEST_ORDER_INVALID'
            // 缺少sessionID和serviceTime
        ];
        
        try {
            // 发送请求
            $response = $this->client->post('customs_chinaport/chinaport/platDataOpen', [
                'form_params' => $invalidRequest
            ]);
            
            // 获取响应数据
            $responseData = $this->getContents($response);
            
            // 验证响应
            $this->assertIsArray($responseData);
            $this->assertArrayHasKey('code', $responseData);
            $this->assertNotEquals('10000', $responseData['code']); // 不应该返回成功
            
            echo "参数验证测试响应：\n";
            print_r($responseData);
        } catch (\Exception $e) {
            $this->fail("请求异常：" . $e->getMessage());
        }
    }
} 