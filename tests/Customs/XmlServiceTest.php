<?php
namespace tests\Customs;

use addon\customsChinaport\service\XmlService;
use tests\BaseTestCase;

/**
 * XML服务测试类
 */
class XmlServiceTest extends BaseTestCase
{
    /**
     * @var XmlService
     */
    protected $service;
    
    /**
     * 设置
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new XmlService();
    }
    
    /**
     * 测试生成订单XML
     */
    public function testGenerateOrderXml()
    {
        // 准备测试数据
        $order_data = [
            'ebp_code' => 'TEST_EBP_CODE',
            'ebp_name' => '测试电商平台',
            'order_no' => 'TEST_ORDER_20240703001',
            'order_time' => strtotime('2024-07-03 12:00:00'),
            'buyer_name' => '张三',
            'buyer_id_number' => '110101199001011234',
            'buyer_telephone' => '13800138000',
            'consignee' => '李四',
            'consignee_address' => '北京市朝阳区某某街道1号',
            'consignee_telephone' => '13900139000',
            'goods_list' => [
                [
                    'goods_id' => 'G001',
                    'goods_name' => '测试商品1',
                    'goods_quantity' => 2,
                    'goods_unit' => '个',
                    'goods_price' => '100.00',
                    'goods_total' => '200.00',
                    'currency' => 'CNY',
                    'country' => 'CN'
                ],
                [
                    'goods_id' => 'G002',
                    'goods_name' => '测试商品2',
                    'goods_quantity' => 1,
                    'goods_unit' => '个',
                    'goods_price' => '50.00',
                    'goods_total' => '50.00',
                    'currency' => 'CNY',
                    'country' => 'CN'
                ]
            ]
        ];
        
        // 执行测试
        $xml = $this->service->generateOrderXml($order_data);
        
        // 断言
        $this->assertNotEmpty($xml);
        $this->assertStringContainsString('<?xml version="1.0" encoding="UTF-8"?>', $xml);
        $this->assertStringContainsString('<CEB311Message', $xml);
        $this->assertStringContainsString('<Order>', $xml);
        $this->assertStringContainsString('<ebpCode>TEST_EBP_CODE</ebpCode>', $xml);
        $this->assertStringContainsString('<orderNo>TEST_ORDER_20240703001</orderNo>', $xml);
        $this->assertStringContainsString('<OrderList>', $xml);
        $this->assertStringContainsString('<gnum>G001</gnum>', $xml);
        $this->assertStringContainsString('<gname>测试商品1</gname>', $xml);
    }
    
    /**
     * 测试生成支付XML
     */
    public function testGeneratePaymentXml()
    {
        // 准备测试数据
        $payment_data = [
            'pay_code' => 'TEST_PAY_CODE',
            'pay_name' => '测试支付企业',
            'payment_no' => 'PAY_20240703001',
            'order_no' => 'TEST_ORDER_20240703001',
            'pay_time' => strtotime('2024-07-03 12:30:00'),
            'pay_amount' => '250.00',
            'currency' => 'CNY',
            'payer_id_number' => '110101199001011234',
            'payer_name' => '张三'
        ];
        
        // 执行测试
        $xml = $this->service->generatePaymentXml($payment_data);
        
        // 断言
        $this->assertNotEmpty($xml);
        $this->assertStringContainsString('<?xml version="1.0" encoding="UTF-8"?>', $xml);
        $this->assertStringContainsString('<CEB411Message', $xml);
        $this->assertStringContainsString('<Payment>', $xml);
        $this->assertStringContainsString('<payCode>TEST_PAY_CODE</payCode>', $xml);
        $this->assertStringContainsString('<payTransactionId>PAY_20240703001</payTransactionId>', $xml);
        $this->assertStringContainsString('<orderNo>TEST_ORDER_20240703001</orderNo>', $xml);
        $this->assertStringContainsString('<totalAmount>250.00</totalAmount>', $xml);
    }
    
    /**
     * 测试解析订单回执XML
     */
    public function testParseOrderReceiptXml()
    {
        // 准备测试数据
        $xml = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<CEB312Message xmlns="http://www.chinaport.gov.cn/ceb" version="1.0">
    <OrderReturn>
        <returnInfo>RECEIPT_ID_001</returnInfo>
        <customsCode>1234</customsCode>
        <customsName>测试海关</customsName>
        <returnStatus>2</returnStatus>
        <returnInfo>处理成功</returnInfo>
        <returnNote>测试回执</returnNote>
        <orderNo>TEST_ORDER_20240703001</orderNo>
    </OrderReturn>
</CEB312Message>
XML;
        
        // 执行测试
        $result = $this->service->parseReceiptXml($xml);
        
        // 断言
        $this->assertTrue($result['success']);
        $this->assertEquals('order', $result['receipt_type']);
        $this->assertEquals('RECEIPT_ID_001', $result['receipt_id']);
        $this->assertEquals('1234', $result['customs_code']);
        $this->assertEquals('测试海关', $result['customs_name']);
        $this->assertEquals('2', $result['status']);
        $this->assertEquals('RECEIPT_ID_001', $result['status_desc']);
        $this->assertEquals('测试回执', $result['note']);
        $this->assertEquals('TEST_ORDER_20240703001', $result['order_no']);
    }
    
    /**
     * 测试解析支付回执XML
     */
    public function testParsePaymentReceiptXml()
    {
        // 准备测试数据
        $xml = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<CEB412Message xmlns="http://www.chinaport.gov.cn/ceb" version="1.0">
    <PaymentReturn>
        <returnInfo>RECEIPT_ID_002</returnInfo>
        <customsCode>1234</customsCode>
        <customsName>测试海关</customsName>
        <returnStatus>2</returnStatus>
        <returnInfo>处理成功</returnInfo>
        <returnNote>测试回执</returnNote>
        <orderNo>TEST_ORDER_20240703001</orderNo>
        <payTransactionId>PAY_20240703001</payTransactionId>
    </PaymentReturn>
</CEB412Message>
XML;
        
        // 执行测试
        $result = $this->service->parseReceiptXml($xml);
        
        // 断言
        $this->assertTrue($result['success']);
        $this->assertEquals('payment', $result['receipt_type']);
        $this->assertEquals('RECEIPT_ID_002', $result['receipt_id']);
        $this->assertEquals('1234', $result['customs_code']);
        $this->assertEquals('测试海关', $result['customs_name']);
        $this->assertEquals('2', $result['status']);
        $this->assertEquals('RECEIPT_ID_002', $result['status_desc']);
        $this->assertEquals('测试回执', $result['note']);
        $this->assertEquals('TEST_ORDER_20240703001', $result['order_no']);
        $this->assertEquals('PAY_20240703001', $result['payment_no']);
    }
    
    /**
     * 测试解析无效XML
     */
    public function testParseInvalidXml()
    {
        // 准备测试数据
        $xml = '<InvalidXML>这不是有效的回执XML</InvalidXML>';
        
        // 执行测试
        $result = $this->service->parseReceiptXml($xml);
        
        // 断言
        $this->assertFalse($result['success']);
    }
} 