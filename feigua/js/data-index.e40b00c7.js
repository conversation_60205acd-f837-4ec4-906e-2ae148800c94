(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["data-index"],{"929d":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"main"},[i("div",{staticClass:"main-content"},[i("div",{staticStyle:{height:"300px"}},[i("el-steps",{attrs:{active:1,direction:"vertical"}},[i("el-step",{attrs:{title:"添加商品",description:"根据要匹配的商品设置检索条件",icon:"el-icon-edit"}}),i("el-step",{attrs:{title:"匹配结果",description:"从数据库检索出匹配数据源",icon:"el-icon-s-help"}}),i("el-step",{attrs:{title:"筛选结果",description:"进行二次精准筛选",icon:"el-icon-finished"}})],1)],1)])])},c=[],s={name:"index"},a=s,l=(i("b429"),i("2877")),o=Object(l["a"])(a,n,c,!1,null,"612b4996",null);e["default"]=o.exports},b429:function(t,e,i){"use strict";i("c2c7")},c2c7:function(t,e,i){}}]);
//# sourceMappingURL=data-index.e40b00c7.js.map