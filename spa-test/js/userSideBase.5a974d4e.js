(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["userSideBase"],{"5bb7":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("keep-alive",[e.$route.meta.keepAlive?a("router-view"):e._e()],1),e.$route.meta.keepAlive?e._e():a("router-view")],1)},o=[],n=a("6917"),u=a("ce3a"),c=a("c391"),r={name:"userSideBase",created:function(){Object(n["g"])({hide:!0,bounces:!0,nativeNav:!0}),this.$axios.post(Object(c["a"])(u["a"].defaultImg),{}).then((function(e){var t=e.data;localStorage.setItem("default_goods_img",t.data.default_goods_img)}))}},s=r,d=a("2877"),l=Object(d["a"])(s,i,o,!1,null,"88c939dc",null);t["default"]=l.exports}}]);