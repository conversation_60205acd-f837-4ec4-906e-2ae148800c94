(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["appBase"],{fadb:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("keep-alive",[e.$route.meta.keepAlive?n("router-view"):e._e()],1),e.$route.meta.keepAlive?e._e():n("router-view")],1)},i=[],o=n("6917"),r={name:"appBase",methods:{getToken:function(){}},created:function(){Object(o["g"])({hide:!0,bounces:!0,nativeNav:!0})}},u=r,c=n("2877"),p=Object(c["a"])(u,a,i,!1,null,"239256be",null);t["default"]=p.exports}}]);