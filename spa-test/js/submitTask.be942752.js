(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["submitTask"],{"116e":function(e,t,a){"use strict";var s=a("33d6"),r=a.n(s);r.a},"33d6":function(e,t,a){},"64a3":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"task"},[a("van-nav-bar",{attrs:{title:e.title,"left-arrow":"",fixed:""},on:{"click-left":e.goBack}}),a("div",{staticClass:"task-info"},[a("div",{staticClass:"task-info-row"},[a("p",{staticClass:"task-info-row-name"},[e._v("你的视频号名称")]),a("div",{staticClass:"task-info-row-input"},[a("van-field",{attrs:{placeholder:""},model:{value:e.video_name,callback:function(t){e.video_name=t},expression:"video_name"}})],1),a("p",{staticClass:"task-info-row-tip"},[e._v("注意：请确保填写的视频号名称能搜索到你发布的任务视频，否则无法通过审核")])]),a("div",{staticClass:"task-info-row"},[a("p",{staticClass:"task-info-row-name"},[e._v("发布的视频描述")]),a("div",{staticClass:"task-info-row-input"},[a("van-field",{attrs:{placeholder:"",type:"textarea",rows:"3"},model:{value:e.video_describe,callback:function(t){e.video_describe=t},expression:"video_describe"}})],1)]),a("div",{staticClass:"task-info-row"},[a("p",{staticClass:"task-info-row-name"},[e._v("上传任务视频截图")]),a("div",{staticClass:"task-info-row-upload"},[a("van-uploader",{attrs:{multiple:"","after-read":e.uploadImage},scopedSlots:e._u([{key:"default",fn:function(){return[a("div",{staticClass:"task-info-row-upload-area"},[a("span",[e._v("点击上传")])])]},proxy:!0}]),model:{value:e.imageList,callback:function(t){e.imageList=t},expression:"imageList"}})],1)]),a("div",{staticClass:"task-info-op"},[a("span",{on:{click:e.submitTask}},[e._v("确认提交")])])])],1)},r=[],i=(a("d81d"),a("ac1f"),a("5319"),a("e17f"),a("2241")),n=(a("e7e5"),a("d399")),c=(a("96cf"),a("1da1")),o=a("5530"),u=a("2f62"),d=a("c391"),l=a("ce3a"),m={name:"submitTask",data:function(){return{title:"",video_name:"",video_describe:"",imageList:[],user_task_id:null}},computed:Object(o["a"])({},Object(u["b"])(["token"])),created:function(){this.title=this.$route.meta.title,this.user_task_id=this.$route.query.user_task_id},methods:{goBack:function(){this.$router.back()},uploadImage:function(e){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var s,r,i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.status="uploading",e.message="上传中...",s=new FormData,s.append("file",e.file),a.prev=4,a.next=7,t.$axios.post(Object(d["a"])(l["a"].xmUserTaskVideoImageUrl),s,{headers:{"Content-Type":"multipart/form-data"}});case 7:if(r=a.sent,i=r.data,10067==i.code){a.next=15;break}return e.status="failed",e.message="上传失败",a.abrupt("return");case 15:t.imageList[t.imageList.length-1]={url:i.data.pic_path},e.status="success",e.message="上传成功";case 18:a.next=24;break;case 20:a.prev=20,a.t0=a["catch"](4),e.status="failed",e.message="上传失败";case 24:case"end":return a.stop()}}),a,null,[[4,20]])})))()},submitTask:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.video_name){t.next=3;break}return Object(n["a"])("请填写视频名字"),t.abrupt("return");case 3:if(e.video_describe){t.next=6;break}return Object(n["a"])("请填写视频描述"),t.abrupt("return");case 6:if(e.imageList.length){t.next=9;break}return Object(n["a"])("请上传视频截图"),t.abrupt("return");case 9:i["a"].confirm({title:"提示",message:"确认提交当前任务？"}).then(Object(c["a"])(regeneratorRuntime.mark((function t(){var a,s,r,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=n["a"].loading({duration:0,forbidClick:!0,message:"提交中..."}),s={token:e.token,user_task_id:e.user_task_id,video_name:e.video_name,video_describe:e.video_describe,video_images:e.imageList.map((function(e){return e.url}))},t.prev=2,t.next=5,e.$axios.post(Object(d["a"])(l["a"].xmUserTaskSubmitUrl),s);case 5:if(r=t.sent,a.clear(),i=r.data,0==i.code){t.next=13;break}return Object(n["a"])(i.message),t.abrupt("return");case 13:Object(n["a"])({message:i.message,onClose:function(){e.$router.replace({name:"taskDetail",query:{user_task_id:e.user_task_id}})}});case 14:t.next=20;break;case 16:t.prev=16,t.t0=t["catch"](2),Object(n["a"])(t.t0.message),a.clear();case 20:case"end":return t.stop()}}),t,null,[[2,16]])})))).catch((function(){}));case 10:case"end":return t.stop()}}),t)})))()}}},f=m,p=(a("116e"),a("2877")),v=Object(p["a"])(f,s,r,!1,null,"3c07db83",null);t["default"]=v.exports},d81d:function(e,t,a){"use strict";var s=a("23e7"),r=a("b727").map,i=a("1dde"),n=a("ae40"),c=i("map"),o=n("map");s({target:"Array",proto:!0,forced:!c||!o},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},e7e5:function(e,t,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")}}]);