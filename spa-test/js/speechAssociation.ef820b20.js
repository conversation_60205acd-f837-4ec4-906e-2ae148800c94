(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["speechAssociation"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"265f":function(e,t,n){"use strict";var a=n("9853"),s=n.n(a);s.a},"841c":function(e,t,n){"use strict";var a=n("d784"),s=n("825a"),r=n("1d80"),i=n("129f"),c=n("14c3");a("search",1,(function(e,t,n){return[function(t){var n=r(this),a=void 0==t?void 0:t[e];return void 0!==a?a.call(t,n):new RegExp(t)[e](String(n))},function(e){var a=n(t,e,this);if(a.done)return a.value;var r=s(e),o=String(this),u=r.lastIndex;i(u,0)||(r.lastIndex=0);var d=c(r,o);return i(r.lastIndex,u)||(r.lastIndex=u),null===d?-1:d.index}]}))},9853:function(e,t,n){},"9b7e8":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"main"},[n("div",{staticClass:"main-header"},[n("van-field",{staticClass:"main-header-search",attrs:{"left-icon":"search",placeholder:"请输入搜索内容"},on:{input:e.searchChange},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})],1),n("div",{staticClass:"main-content"},[e.list.length>0||!e.finished?n("van-list",{attrs:{finished:e.finished,"finished-text":"没有更多了"},on:{load:e.onLoad},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,(function(t,a){return n("div",{key:a,staticClass:"main-content-one"},[n("div",{staticClass:"main-content-one-title"},[e._v("# "+e._s(t.title))]),n("div",{staticClass:"main-content-one-text"},[e._v(e._s(t.content))]),n("div",{staticClass:"main-content-one-op"},[n("van-button",{staticClass:"main-content-one-op-send",attrs:{type:"info",round:"",size:"small"},on:{click:function(t){return e.singleSend(a)}}},[e._v("发送")])],1)])})),0):e._e()],1)])},s=[],r=(n("d81d"),n("ac1f"),n("841c"),n("e7e5"),n("d399")),i=(n("96cf"),n("1da1")),c=n("5530"),o=n("2a9b"),u=n("c391"),d=n("ce3a"),l=n("2f62"),f=n("860d"),p={name:"speechAssociation",data:function(){return{search:"",searchlist:[],speak_ids:[],page:1,page_size:10,loading:!1,finished:!1,list:[],is_focus:!1}},computed:Object(c["a"])({},Object(l["b"])(["token","miniToken","shop_id","wxEnterpriseUserInfo","wxEnterpriseInfo","wxEnterpriseUserId","staffUserId"])),created:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=navigator.userAgent.toLowerCase(),!/wxwork/i.test(n)&&!/micromessenger/i.test(n)||e.$store.state.wxEnterpriseUserId){t.next=7;break}return t.next=4,Object(o["g"])();case 4:if(!e.$store.state.wxEnterpriseUserId){t.next=7;break}return t.next=7,Object(o["f"])(e.$store.state.wxEnterpriseUserId);case 7:return t.prev=7,a=new f["a"],t.next=11,a.init();case 11:t.next=15;break;case 13:t.prev=13,t.t0=t["catch"](7);case 15:case"end":return t.stop()}}),t,null,[[7,13]])})))()},methods:{resetParams:function(){this.speak_ids=[],this.page=1,this.loading=!1,this.finished=!1,this.list=[]},onLoad:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var n,a,s,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n={token:e.staffUserId,speak_ids:e.speak_ids,page:e.page,page_size:e.page_size},t.prev=1,t.next=4,e.$axios.post(Object(u["a"])(d["a"].speakListUrl),n);case 4:if(a=t.sent,s=a.data,0!=s.code)Object(r["a"])(s.message);else if(s.data.list){for(i=0;i<s.data.list.length;i++)e.$set(e.list,e.list.length,s.data.list[i]);e.loading=!1,e.page>=s.data.page_count&&(e.finished=!0),e.page=e.page+1}else e.loading=!1,e.finished=!0;t.next=15;break;case 9:t.prev=9,t.t0=t["catch"](1),Object(r["a"])(t.t0.message),e.loading=!1,e.page=1,e.finished=!0;case 15:case"end":return t.stop()}}),t,null,[[1,9]])})))()},singleSend:function(e){var t=this;this.sendChatMessage({msgtype:"text",enterChat:!1,text:{content:this.list[e].content}},Object(i["a"])(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.$axios.post(Object(u["a"])(d["a"].sendMsgUrl),{speak_id:t.list[e].speak_id,token:t.staffUserId});case 3:n.sent,n.next=8;break;case 6:n.prev=6,n.t0=n["catch"](0);case 8:case"end":return n.stop()}}),n,null,[[0,6]])}))),(function(){}))},searchChange:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e){n.next=4;break}setTimeout(Object(i["a"])(regeneratorRuntime.mark((function n(){var a,s,r;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a={search_text:e,token:t.staffUserId},n.prev=1,n.next=4,t.$axios.post(Object(u["a"])(d["a"].speakSearchUrl),a);case 4:if(s=n.sent,r=s.data,0!=r.code){n.next=12;break}return t.searchlist=r.data,t.resetParams(),t.speak_ids=r.data.map((function(e){return e.speak_id})),n.next=12,t.onLoad();case 12:n.next=16;break;case 14:n.prev=14,n.t0=n["catch"](1);case 16:case"end":return n.stop()}}),n,null,[[1,14]])}))),200),n.next=8;break;case 4:return t.searchlist=[],t.resetParams(),n.next=8,t.onLoad();case 8:case"end":return n.stop()}}),n)})))()},searchSelect:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.search=e.title,n.next=3,t.searchChange(t.search);case 3:t.is_focus=!1;case 4:case"end":return n.stop()}}),n)})))()},sendChatMessage:function(e,t,n){wx.invoke("sendChatMessage",e,(function(e){"sendChatMessage:ok"==e.err_msg?t&&"function"==typeof t&&t(e):n&&"function"==typeof n&&n(e)}))}}},h=p,g=(n("265f"),n("2877")),v=Object(g["a"])(h,a,s,!1,null,"b5c0fac8",null);t["default"]=v.exports},d81d:function(e,t,n){"use strict";var a=n("23e7"),s=n("b727").map,r=n("1dde"),i=n("ae40"),c=r("map"),o=i("map");a({target:"Array",proto:!0,forced:!c||!o},{map:function(e){return s(this,e,arguments.length>1?arguments[1]:void 0)}})},e7e5:function(e,t,n){"use strict";n("68ef"),n("a71a"),n("9d70"),n("3743"),n("4d75"),n("e3b3"),n("b258")}}]);