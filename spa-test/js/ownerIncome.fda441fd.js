(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["ownerIncome"],{"3aab":function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"page"},[i("van-nav-bar",{attrs:{title:"销售数据","left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),i("div",{staticClass:"tabbar"},[i("div",{staticClass:"tabbar_item",class:{active:0==t.type},on:{click:function(a){return t.changeTab(0)}}},[t._v("今日")]),i("div",{staticClass:"tabbar_item",class:{active:1==t.type},on:{click:function(a){return t.changeTab(1)}}},[t._v("昨日")]),i("div",{staticClass:"tabbar_item",class:{active:2==t.type},on:{click:function(a){return t.changeTab(2)}}},[t._v("近7日")]),i("div",{staticClass:"tabbar_item",class:{active:3==t.type},on:{click:function(a){return t.changeTab(3)}}},[t._v("近30日")])]),i("div",{staticClass:"bontain_box"},[!t.finished||t.finished&&t.datalist.length>0?i("van-list",{staticClass:"van-list",attrs:{"error-text":"请求失败，点击重新加载",finished:t.finished,"finished-text":"没有更多了"},on:{load:t.getList},model:{value:t.loading,callback:function(a){t.loading=a},expression:"loading"}},[i("div",{staticClass:"xian"}),i("div",{staticClass:"middle_box"},[i("div",{staticClass:"middle_item on"},[i("div",{staticClass:"num_middle"},[t._v(t._s(t.salenum))]),i("div",{staticClass:"desc_middle"},[t._v("笔数合计")])]),i("div",{staticClass:"middle_item on"},[i("div",{staticClass:"num_middle"},[i("span",[t._v("￥")]),t._v(t._s(t.salemoney)+" ")]),i("div",{staticClass:"desc_middle"},[t._v("销售金额合计")])]),i("div",{staticClass:"middle_item"},[i("div",{staticClass:"num_middle"},[i("span",[t._v("￥")]),t._v(t._s(t.salebenefit)+" ")]),i("div",{staticClass:"desc_middle"},[t._v("预计收益合计")])])]),i("div",{staticClass:"xian"}),i("div",{staticClass:"list_box"},[i("div",{staticClass:"list_top"},[i("div",{staticClass:"users"},[i("span",[t._v("用户")])]),i("div",{staticClass:"ordersnum"},[t._v("笔数")]),i("div",{staticClass:"salenum"},[t._v("销售额")]),i("div",{staticClass:"getbenefit"},[t._v("预估收益")])]),i("div",{staticClass:"list_contain"},t._l(t.datalist,(function(a,s){return i("div",{key:s,staticClass:"list_item"},[i("div",{staticClass:"users_contain"},[i("img",{attrs:{src:a.imgUrl,alt:""}}),i("span",[t._v(t._s(a.nickName))])]),i("div",{staticClass:"ordersnum_contain"},[t._v(t._s(a.count))]),i("div",{staticClass:"salenum_contain"},[i("span",[t._v("￥")]),t._v(" "+t._s(a.amount)+" ")]),i("div",{staticClass:"getbenefit_contain"},[i("span",[t._v("￥")]),t._v(" "+t._s(a.income)+" ")])])})),0)])]):t._e(),t.finished&&0==t.datalist.length?i("div",{staticClass:"group-default"},[i("img",{attrs:{src:s("f1b1")}}),i("p",[t._v("暂无数据")])]):t._e()],1)],1)},e=[],n=(s("99af"),s("e7e5"),s("d399")),c=s("5530"),l=s("6917"),d=s("ce3a"),o=s("c391"),r=s("2f62"),v={data:function(){return{datalist:[],type:0,page:1,loading:!1,finished:!1,reference:0,salenum:"",salemoney:"",salebenefit:""}},computed:Object(c["a"])({},Object(r["b"])(["token"])),created:function(){this.reference=this.$route.query.reference?this.$route.query.reference:0},methods:{goBack:function(){this.reference?this.$router.back():Object(l["a"])("0")},changeTab:function(t){this.datalist=[],this.page=1,this.finished=!1,this.loading=!1,this.type=t,this.getList()},getList:function(){var t=this,a=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:this.token,type:this.type,page:this.page,page_size:10};this.loading=!0,this.$axios.post(Object(o["a"])(d["a"].appSales),s).then((function(i){a.clear();var e=i.data;if(0==e.code){if(console.log(e),t.salenum=e.data.countTotal,t.salemoney=e.data.amountTotal,t.salebenefit=e.data.incomeTotal,t.datalist=t.datalist.concat(e.data.salesData),e.data.salesData.length<s.page_size)return t.finished=!0,void(t.loading=!0);t.loading=!1,t.page+=1}else Object(n["a"])(e.message)})).catch((function(t){a.clear()}))}}},u=v,_=(s("e2e8"),s("2877")),f=Object(_["a"])(u,i,e,!1,null,"4c6ad205",null);a["default"]=f.exports},"5be5":function(t,a,s){},e2e8:function(t,a,s){"use strict";var i=s("5be5"),e=s.n(i);e.a},e7e5:function(t,a,s){"use strict";s("68ef"),s("a71a"),s("9d70"),s("3743"),s("4d75"),s("e3b3"),s("b258")},f1b1:function(t,a,s){t.exports=s.p+"img/img-shopper-default.cb55b4c9.png"}}]);