(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["shopkeeperBase"],{aa8d:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("keep-alive",[e.$route.meta.keepAlive?a("router-view"):e._e()],1),e.$route.meta.keepAlive?e._e():a("router-view")],1)},r=[],i=a("6917"),o={name:"shopkeeperBase",created:function(){Object(i["g"])({hide:!0,bounces:!0,nativeNav:!0})}},u=o,c=a("2877"),p=Object(c["a"])(u,n,r,!1,null,"6a8b356c",null);t["default"]=p.exports}}]);