(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["taskLists"],{"1ffa":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"tasks"},[s("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),s("div",{staticClass:"tasks-info"},[s("van-tabs",{on:{change:t.tabChange},model:{value:t.active,callback:function(a){t.active=a},expression:"active"}},[s("van-tab",{attrs:{title:"我的任务"}},[0==t.active?[s("div",{staticClass:"tasks-info-list"},[!t.finished||t.finished&&t.dataList.length>0?s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(a){t.loading=a},expression:"loading"}},t._l(t.dataList,(function(a,e){return s("div",{key:e,staticClass:"tasks-info-list-one",on:{click:function(e){return t.toDetail(a)}}},[s("van-image",{staticClass:"tasks-info-list-one-img",attrs:{src:a.materials_img[0],fit:"contain"},scopedSlots:t._u([{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),s("div",{staticClass:"tasks-info-list-one-right"},[s("p",{staticClass:"tasks-info-list-one-right-title"},[t._v(t._s(a.title))]),s("p",{staticClass:"tasks-info-list-one-right-time"},[t._v("任务时限： "+t._s(a.time_limit)+"天")]),s("p",{staticClass:"tasks-info-list-one-right-income"},[t._v("预估收益： ￥"+t._s(a.estimate_profit)+"元")]),s("div",{staticClass:"tasks-info-list-one-right-op"},[s("span",[t._v(t._s(a.status_text))])])])],1)})),0):t._e(),t.finished&&t.dataList.length<1?s("div",{staticClass:"group-default"},[s("img",{attrs:{src:e("f1b1")}}),s("p",[t._v("你还没领取过任务，快去任务大厅看看吧")])]):t._e()],1)]:t._e()],2),s("van-tab",{attrs:{title:"任务大厅",badge:t.pendingList.length?t.pendingList.length:""}},[1==t.active?[s("div",{staticClass:"tasks-info-list"},[t.pendingList.length?t._l(t.pendingList,(function(a,e){return s("div",{key:e,staticClass:"tasks-info-list-one",on:{click:function(e){return t.toPendingDetail(a)}}},[s("van-image",{staticClass:"tasks-info-list-one-img",attrs:{src:a.materials_img[0],fit:"contain"},scopedSlots:t._u([{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),s("div",{staticClass:"tasks-info-list-one-right"},[s("p",{staticClass:"tasks-info-list-one-right-title"},[t._v(t._s(a.title))]),s("p",{staticClass:"tasks-info-list-one-right-time"},[t._v("任务时限： "+t._s(a.time_limit)+"天")]),s("p",{staticClass:"tasks-info-list-one-right-income"},[t._v("预估收益： ￥"+t._s(a.estimate_profit)+"元")]),s("div",{staticClass:"tasks-info-list-one-right-op"},[s("span",[t._v("领取任务")])])])],1)})):s("div",{staticClass:"group-default"},[s("img",{attrs:{src:e("f1b1")}}),s("p",[t._v("暂时还没有你可以领取的任务哦")])])],2)]:t._e()],2)],1)],1)],1)},i=[],n=(e("99af"),e("e7e5"),e("d399")),r=e("5530"),o=(e("96cf"),e("1da1")),c=e("2f62"),l=e("6917"),u=e("c391"),d=e("ce3a"),f={name:"taskLists",data:function(){return{title:"",active:0,status:"",img:e("d996"),pendingList:[],dataList:[],page:1,loading:!1,finished:!1}},created:function(){this.title=this.$route.meta.title},mounted:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.getPendingData();case 2:case"end":return a.stop()}}),a)})))()},computed:Object(r["a"])({},Object(c["b"])(["token"])),methods:{goBack:function(){Object(l["a"])("0")},getPendingData:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){var e,s,i,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:t.token},a.prev=2,a.next=5,t.$axios.post(Object(u["a"])(d["a"].xmTaskListUrl),s);case 5:if(i=a.sent,e.clear(),r=i.data,0==r.code){a.next=11;break}return Object(n["a"])(r.message),a.abrupt("return");case 11:t.pendingList=r.data,a.next=18;break;case 14:a.prev=14,a.t0=a["catch"](2),Object(n["a"])(a.t0.message),e.clear();case 18:case"end":return a.stop()}}),a,null,[[2,14]])})))()},onLoad:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){var e,s,i,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),t.loading=!0,s={token:t.token,page:t.page,status:t.status},a.prev=3,a.next=6,t.$axios.post(Object(u["a"])(d["a"].xmUserTaskListUrl),s);case 6:if(i=a.sent,e.clear(),r=i.data,0==r.code){a.next=12;break}return Object(n["a"])(r.message),a.abrupt("return");case 12:t.dataList=t.dataList.concat(r.data.list),t.dataList.length>=r.data.count&&(t.finished=!0),t.loading=!1,t.page+=1,a.next=22;break;case 18:a.prev=18,a.t0=a["catch"](3),Object(n["a"])(a.t0.message),e.clear();case 22:case"end":return a.stop()}}),a,null,[[3,18]])})))()},resetData:function(){this.loading=!1,this.finished=!1,this.page=1,this.dataList=[]},tabChange:function(t){switch(t){case 0:this.status="",this.resetData();break;case 1:this.getPendingData();break;case 2:this.status=1,this.resetData();break;case 3:this.status=2,this.resetData();break;case 4:this.status=9,this.resetData();break;case 5:this.status=-1,this.resetData();break}},toPendingDetail:function(t){this.$router.push({name:"taskDetail",query:{task_id:t.task_id}})},toDetail:function(t){this.$router.push({name:"taskDetail",query:{user_task_id:t.user_task_id}})}}},g=f,p=(e("d9fc"),e("2877")),h=Object(p["a"])(g,s,i,!1,null,"3fd21a22",null);a["default"]=h.exports},"6b6e":function(t,a,e){},d996:function(t,a,e){t.exports=e.p+"img/bg-ranking .9661437d.png"},d9fc:function(t,a,e){"use strict";var s=e("6b6e"),i=e.n(s);i.a},e7e5:function(t,a,e){"use strict";e("68ef"),e("a71a"),e("9d70"),e("3743"),e("4d75"),e("e3b3"),e("b258")},f1b1:function(t,a,e){t.exports=e.p+"img/img-shopper-default.cb55b4c9.png"}}]);