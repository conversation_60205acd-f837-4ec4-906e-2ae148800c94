(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["search"],{7901:function(t,e,o){},"83d4":function(t,e,o){"use strict";o.r(e);var s=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"search"},[o("van-nav-bar",{attrs:{title:"搜索","left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),o("van-search",{attrs:{shape:"round",placeholder:"搜索你喜欢的商品","show-action":""},on:{search:t.onSearch},scopedSlots:t._u([{key:"action",fn:function(){return[o("div",{staticStyle:{padding:"0 6px"},on:{click:t.goBack}},[t._v("取消")])]},proxy:!0}]),model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}}),t.historyList.length?o("div",{staticClass:"search-list"},[o("div",{staticClass:"title"},[t._v("历史搜索"),o("van-icon",{attrs:{name:"delete",size:"20"},on:{click:function(e){return t.deleteSearch(-1)}}})],1),o("div",{staticClass:"item"},t._l(t.historyList,(function(e,s){return o("span",{key:s,on:{click:function(o){return t.onSearch(e)},touchstart:function(e){return t.gotouchstart(s)},touchmove:t.gotouchmove,touchend:t.gotouchend}},[t._v(t._s(e))])})),0)]):t._e(),t.hotWordsList.length?o("div",{staticClass:"search-list"},[o("div",{staticClass:"title"},[t._v("热门搜索")]),o("div",{staticClass:"item"},t._l(t.hotWordsList,(function(e,s){return o("span",{key:s,on:{click:function(o){return t.onSearch(e.word)}}},[t._v(t._s(e.word))])})),0)]):t._e()],1)},i=[],r=(o("4de4"),o("498a"),o("e17f"),o("2241")),a=(o("e7e5"),o("d399")),c=o("5530"),n=o("6917"),h=o("ce3a"),u=o("c391"),d=o("2f62"),l={name:"search",data:function(){return{reference:0,keyword:"",historyList:[],hotWordsList:[],timeOutEvent:0,categoryId:0,categoryName:"",category_level:0}},computed:Object(c["a"])({},Object(d["b"])(["shop_id"])),created:function(){var t=this;this.reference=this.$route.query.reference||0,this.categoryId=this.$route.query.categoryId,this.categoryName=this.$route.query.categoryName,this.category_level=this.$route.query.category_level,this.findHistoryList(),this.shop_id?this.getHotWords():this.$axios.post(Object(u["a"])(h["a"].defaultShop),{}).then((function(e){var o=e.data;0==o.code?(t.$store.dispatch("setShopId",o.data.site_id),t.getHotWords()):Object(a["a"])(o.message)}))},methods:{getHotWords:function(){var t=this;this.$axios.post(Object(u["a"])(h["a"].goodsHotWords),{shop_id:this.shop_id}).then((function(e){var o=e.data;0==o.code?t.hotWordsList=o.data:Object(a["a"])(o.message)}))},gotouchstart:function(t){var e=this;clearTimeout(this.timeOutEvent),this.timeOutEvent=0,this.timeOutEvent=setTimeout((function(){e.deleteSearch(t)}),600)},gotouchend:function(){clearTimeout(this.timeOutEvent),this.timeOutEvent=0},gotouchmove:function(){clearTimeout(this.timeOutEvent),this.timeOutEvent=0},findHistoryList:function(){var t=localStorage.getItem("search")?JSON.parse(localStorage.getItem("search")):[];this.historyList=t.reverse()},deleteSearch:function(t){var e=this;r["a"].confirm({title:"提示",message:-1==t?"确定要删除所有搜索历史吗？":"确定要删除该搜索历史吗？"}).then((function(){-1==t?localStorage.setItem("search","[]"):(e.$delete(e.historyList,t),localStorage.setItem("search",JSON.stringify(e.historyList))),e.findHistoryList()})).catch((function(){}))},onSearch:function(t){var e=t.trim();if(""!=e){var o=localStorage.getItem("search")?JSON.parse(localStorage.getItem("search")):[],s=[];o.length&&(s=o.filter((function(t){return t!=e}))),s.push(e),localStorage.setItem("search",JSON.stringify(s)),this.findHistoryList();var i={reference:1,keyword:t};this.categoryId?(i.categoryId=this.categoryId,i.categoryName=this.categoryName,i.category_level=this.category_level):i.entrance="search",this.$router.push({name:"goodsList",query:i})}else Object(a["a"])("搜索内容不能为空哦")},goBack:function(){this.reference?this.$router.go(-1):Object(n["a"])("0")}}},f=l,g=(o("f261"),o("2877")),v=Object(g["a"])(f,s,i,!1,null,"1fe29972",null);e["default"]=v.exports},e7e5:function(t,e,o){"use strict";o("68ef"),o("a71a"),o("9d70"),o("3743"),o("4d75"),o("e3b3"),o("b258")},f261:function(t,e,o){"use strict";var s=o("7901"),i=o.n(s);i.a}}]);