(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["VIPShopkeeper"],{"0866":function(t,i,e){"use strict";e.d(i,"d",(function(){return o})),e.d(i,"a",(function(){return c})),e.d(i,"e",(function(){return r})),e.d(i,"b",(function(){return l})),e.d(i,"c",(function(){return p}));var s=navigator.userAgent.toLowerCase()||window.navigator.userAgent.toLowerCase(),a=(/youpin/i.test(s),/xianmai/i.test(s)),n="";n=a?"xm://xianmai":"yp://youpin";"".concat(n,"?position=login");var o="".concat(n,"?web="),c="".concat(n,"?position=authentication"),r="".concat(n,"?position=withdraw"),l="".concat(n,"?openBrowser="),p="".concat(n,"?position=wantToJoinIn");"".concat(n,"?position=mywallet")},"08c3":function(t,i,e){},"49f0":function(t,i,e){"use strict";var s=e("08c3"),a=e.n(s);a.a},"55b8":function(t,i,e){t.exports=e.p+"img/bg-vipshopkeeper.757d143b.png"},"8c0c":function(t,i,e){"use strict";e.r(i);var s=function(){var t=this,i=t.$createElement,s=t._self._c||i;return s("div",{staticClass:"VIPShopkeeper"},[s("van-nav-bar",{attrs:{title:t.title,"left-arrow":"","right-text":"升级店铺",fixed:""},on:{"click-left":t.goBack,"click-right":t.toShopUpgrade}}),s("div",{staticClass:"header"},[s("img",{staticClass:"bg",attrs:{src:e("55b8")}}),s("div",{staticClass:"header-info"},[s("div",{staticClass:"box"},[s("div",{staticClass:"top"},[t._v(t._s(t.open_shop_num))]),s("div",{staticClass:"bottom"},[t._v("已开通店铺")])]),s("div",{staticClass:"box"},[s("div",{staticClass:"top"},[t._v(t._s(t.residue_shop_num))]),s("div",{staticClass:"bottom"},[t._v("剩余店铺")])]),s("div",{staticClass:"box"},[s("div",{staticClass:"top"},[t._v(t._s(t.verify_shop_num))]),s("div",{staticClass:"bottom"},[t._v("待审核店铺")])])])]),s("div",{staticClass:"tab-box"},[s("van-tabs",{attrs:{"title-active-color":"#F1270C"},on:{click:t.tabChange},model:{value:t.activeName,callback:function(i){t.activeName=i},expression:"activeName"}},[s("van-tab",{attrs:{title:"已开通",name:"opened"}},[t.list.length>0?s("div",{staticClass:"group"},[s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:function(i){return t.getData(1)}},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.list,(function(i,a){return s("div",{key:a,staticClass:"item",on:{click:function(e){return t.toShopInfo(i.shop_id)}}},[s("div",{staticClass:"head"},[s("van-image",{attrs:{src:i.avatar_url},scopedSlots:t._u([{key:"error",fn:function(){return[s("img",{attrs:{src:e("f79e")}})]},proxy:!0},{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)})],1),s("div",{staticClass:"info"},[s("div",{staticClass:"title-box"},[s("div",{staticClass:"title"},[t._v(t._s(i.shop_name))]),s("div",{staticClass:"level"},[t._v(t._s(i.level_name))])]),s("div",{staticClass:"info-text"},[s("div",{staticClass:"today"},[t._v("今日卖货"+t._s(i.today_num)+"件")]),s("div",{staticClass:"total"},[t._v("累计卖货"+t._s(i.total_num)+"件")])]),s("div",{staticClass:"time"},[t._v(t._s(i.join_date))])])])})),0)],1):s("div",{staticClass:"group-default"},[s("img",{attrs:{src:e("f1b1")}}),s("p",[t._v("暂无数据")])]),s("div",{staticClass:"to-invite",on:{click:function(i){return t.toInvite(2)}}},[t._v("邀请VIP店主")])]),s("van-tab",{attrs:{title:"待审核",name:"pending"}},[t.list.length>0?s("div",{staticClass:"group"},[s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:function(i){return t.getData(2)}},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.list,(function(i,a){return s("div",{key:a,staticClass:"item no_opened",on:{click:function(e){return t.toShopInfo(i.member_id)}}},[s("div",{staticClass:"head"},[s("van-image",{attrs:{src:i.avatar_url},scopedSlots:t._u([{key:"error",fn:function(){return[s("img",{attrs:{src:e("f79e")}})]},proxy:!0},{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)})],1),s("div",{staticClass:"info"},[s("div",{staticClass:"title-box"},[s("div",{staticClass:"title"},[t._v(t._s(i.mobile))])]),s("div",{staticClass:"time"},[t._v(t._s(i.create_time))])]),s("div",{staticClass:"toDetail"},[t._v("待审核")])])})),0)],1):s("div",{staticClass:"group-default"},[s("img",{attrs:{src:e("f1b1")}}),s("p",[t._v("暂无数据")])]),s("div",{staticClass:"to-invite",on:{click:function(i){return t.toInvite(2)}}},[t._v("邀请VIP店主")])])],1)],1),s("van-popup",{model:{value:t.passPopupShow,callback:function(i){t.passPopupShow=i},expression:"passPopupShow"}},[s("div",{staticClass:"popup-box"},[s("div",{staticClass:"title"},[t._v("提示")]),s("div",{staticClass:"info text-center"},[t._v("需要实名认证才可以邀请VIP店主")]),s("div",{staticClass:"btns-box"},[s("div",{staticClass:"btn",on:{click:function(i){t.passPopupShow=!1}}},[t._v("不了，谢谢")]),s("div",{staticClass:"btn",on:{click:t.toAuthentication}},[t._v("去实名认证")])])])])],1)},a=[],n=(e("d3b7"),e("e7e5"),e("d399")),o=e("5530"),c=e("6917"),r=e("0866"),l=e("ce3a"),p=e("c391"),u=e("2f62"),d={name:"VIPShopkeeper",components:{},data:function(){return{title:"",open_shop_num:"",residue_shop_num:"",verify_shop_num:"",activeName:"opened",page:1,page_size:10,open_state:1,loading:!1,finished:!1,list:[],shopId:"",reference:"0",isRealName:0,passPopupShow:!1}},computed:Object(o["a"])({},Object(u["b"])(["token"])),methods:{getData:function(t){var i=this,e={token:this.token,open_state:t,page:this.page,page_size:this.page_size};this.$axios.post(Object(p["a"])(l["a"].appVipShopLists),e).then((function(t){var e=t.data;if(0!=e.code)Object(n["a"])(e.message);else{for(var s=0;s<e.data.shop_info.list.length;s++)i.$set(i.list,i.list.length,e.data.shop_info.list[s]);i.loading=!1,i.list.length>=e.data.shop_info.total&&(i.finished=!0),i.page=i.page+1}})).catch((function(t){Object(n["a"])(t.message),i.loading=!1,i.page=1,i.list.length>=result.data.shop_info.total&&(i.finished=!0)})).finally((function(){}))},getShopNum:function(){var t=this,i={token:this.token};this.$axios.post(Object(p["a"])(l["a"].appVipShopNum),i).then((function(i){var e=i.data;0!=e.code?Object(n["a"])(e.message):(t.open_shop_num=e.data.open_shop_num,t.residue_shop_num=e.data.residue_shop_num,t.verify_shop_num=e.data.verify_shop_num)})).catch((function(t){Object(n["a"])(t.message)})).finally((function(){}))},goBack:function(){Object(c["a"])("0")},toInvite:function(t){0==this.isRealName?this.passPopupShow=!0:1==this.isRealName&&this.$router.push({name:"invitePoster",query:{type:t,reference:this.reference}})},toShopUpgrade:function(){this.$router.push({name:"shopUpgrade",query:{shopId:this.shopId,reference:this.reference}})},tabChange:function(t){this.page=1,this.page_size=10,this.list=[],this.loading=!1,this.finished=!1,"opened"==t?this.open_state=1:"pending"==t&&(this.open_state=2),this.getData(this.open_state)},toShopInfo:function(t){this.$router.push({name:"VIPShopkeeperInfo",query:{infoId:t,open_state:this.open_state}})},toAuthentication:function(){Object(c["e"])(r["a"])}},created:function(){this.title=this.$route.meta.title,this.$route.query.shop_id&&(this.shopId=this.$route.query.shop_id),this.$route.query.isRealName&&(this.isRealName=this.$route.query.isRealName),this.getShopNum(),this.getData(1)}},h=d,v=(e("49f0"),e("2877")),f=Object(v["a"])(h,s,a,!1,null,"7b1c8a83",null);i["default"]=f.exports},e7e5:function(t,i,e){"use strict";e("68ef"),e("a71a"),e("9d70"),e("3743"),e("4d75"),e("e3b3"),e("b258")},f1b1:function(t,i,e){t.exports=e.p+"img/img-shopper-default.cb55b4c9.png"},f79e:function(t,i,e){t.exports=e.p+"img/img-shopper-head.bef43d9e.png"}}]);