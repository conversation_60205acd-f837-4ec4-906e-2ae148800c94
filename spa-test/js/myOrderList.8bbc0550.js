(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["myOrderList"],{"18df":function(t,e,o){},"2cec":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"main"},[o("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),o("div",{staticClass:"header"},t._l(t.statusList,(function(e,i){return o("div",{key:i,staticClass:"header-item",class:{"header-item-active":e.status==t.orderStatus},attrs:{"data-current":i},on:{click:t.changeTab}},[t._v(t._s(e.name))])})),0),o("div",{staticClass:"list"},[t.orderList.length>0||!t.finished?o("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.getDataList},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.orderList,(function(e,i){return o("div",{key:i,staticClass:"list-item",on:{click:function(o){return t.orderDetail(e)}}},[o("div",{staticClass:"list-item-header"},[o("span",{staticClass:"list-item-header-left"},[t._v(t._s(e.order_no))]),o("span",{staticClass:"list-item-header-right"},[t._v(t._s(e.order_status_name))])]),o("div",t._l(e.order_goods,(function(i,s){return o("div",{key:s,staticClass:"list-item-product"},[o("van-image",{staticClass:"list-item-product-left",attrs:{src:i.sku_image},scopedSlots:t._u([{key:"loading",fn:function(){return[o("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),o("div",{staticClass:"list-item-product-right"},[o("p",{staticClass:"list-item-product-right-one"},[t._v(t._s(i.goods_name))]),o("div",{staticClass:"list-item-product-right-two"},[o("span",[t._v(t._s(i.spec_name))]),o("span",[t._v("x "+t._s(i.num))])]),1==e.is_maidou_pay?o("p",{staticClass:"list-item-product-right-three"},[t._v(t._s(i.price)+"迈豆")]):o("p",{staticClass:"list-item-product-right-three"},[t._v("￥"+t._s(i.price))])])],1)})),0),o("p",{staticClass:"list-item-price"},[t._v("共"+t._s(e.goods_num)+"件商品 总计："),1==e.is_maidou_pay?o("span",[t._v(t._s(e.pay_money)+"迈豆")]):o("span",[t._v("￥"+t._s("BALANCE"==e.pay_type?e.balance_money:e.pay_money))])])])})),0):o("div",{staticClass:"not-product"},[o("p",{staticClass:"not-product-tip"},[t._v(t._s(t.text))])])],1),o("to-top")],1)},s=[],r=(o("e7e5"),o("d399")),a=o("5530"),l=o("c391"),n=o("ce3a"),c=o("5d30"),d=o("2f62"),f={name:"myOrderList",data:function(){return{title:"",sku_image:"",orderStatus:"all",statusList:[{status:"all",name:"全部",id:"status_0"},{status:"waitpay",name:"待付款",id:"status_1"},{status:"waitsend",name:"待发货",id:"status_2"},{status:"waitconfirm",name:"待收货",id:"status_3"},{status:"waitrate",name:"已完成",id:"status_4"}],text:"您还暂无相关订单",orderList:[],page:1,page_size:10,loading:!1,finished:!1}},components:{toTop:c["a"]},computed:Object(a["a"])({},Object(d["b"])(["miniToken","shop_id"])),created:function(){this.title=this.$route.meta.title},methods:{goBack:function(){this.$router.go(-1)},countDown:function(t){var e=0,o=0,i=0,s=0;return t>0&&(e=Math.floor(t/86400),o=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*o,s=Math.floor(t)-24*e*60*60-60*o*60-60*i),{d:e,h:o,i:i,s:s}},getDataList:function(){var t=this,e=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),o={token:this.miniToken,shop_id:this.shop_id,page:this.page,page_size:this.page_size,order_status:this.orderStatus};this.$axios.post(Object(l["a"])(n["a"].orderListsUrl),o).then((function(o){var i=o.data;if(0!=i.code)Object(r["a"])(i.message);else{for(var s=0;s<i.data.list.length;s++)t.$set(t.orderList,t.orderList.length,i.data.list[s]);for(var a=0;a<t.orderList.length;a++)("number"==typeof t.orderList[a].rest_pay_time||t.orderList[a].rest_pay_time)&&(t.orderList[a].discountTimeMachine=t.countDown(t.orderList[a].rest_pay_time));t.loading=!1,e.clear(),t.orderList.length>=i.data.count&&(t.finished=!0),t.page=t.page+1}})).catch((function(e){Object(r["a"])(e.message),t.loading=!1,t.page=1,t.finished=!0}))},changeTab:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,"all"==this.orderStatus?this.text="您还暂无相关订单":"waitpay"==this.orderStatus?this.text="您还暂无待付款订单":"waitsend"==this.orderStatus?this.text="您还暂无待发货订单":"waitconfirm"==this.orderStatus?this.text="您还暂无待收货订单":this.text="您还暂无已完成订单",this.resetTab()},resetTab:function(){this.page=1,this.orderList=[],this.finished=!1,this.loading=!0,this.getDataList()},orderDetail:function(t){this.$router.push({name:"myOrderDetail",query:{order_id:t.order_id}})}}},u=f,p=(o("b56f"),o("2877")),h=Object(p["a"])(u,i,s,!1,null,"03e0f87b",null);e["default"]=h.exports},"5d30":function(t,e,o){"use strict";var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("img",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],style:{bottom:t.pointBottom,right:t.pointRight},attrs:{src:o("849b"),alt:""},on:{click:t.toTop}})},s=[],r=(o("a9e3"),o("7707")),a=o.n(r),l={name:"toTop",props:{pointBottom:{default:"5.6875rem",type:String},pointRight:{default:"0px",type:String},scrollShow:{default:500,type:Number}},data:function(){return{isShow:!1}},methods:{onScroll:function(t){var e=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;e>this.scrollShow&&!this.isShow?this.isShow=!0:e<=this.scrollShow&&this.isShow&&(this.isShow=!1)},toTop:function(){window.scroll({top:0,behavior:"smooth"})}},mounted:function(){a.a.polyfill(),window.addEventListener("scroll",this.onScroll)}},n=l,c=(o("86e1"),o("2877")),d=Object(c["a"])(n,i,s,!1,null,"793636d7",null);e["a"]=d.exports},7707:function(t,e,o){(function(){"use strict";function e(){var t=window,e=document;if(!("scrollBehavior"in e.documentElement.style&&!0!==t.__forceSmoothScrollPolyfill__)){var o=t.HTMLElement||t.Element,i=468,s={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:o.prototype.scroll||n,scrollIntoView:o.prototype.scrollIntoView},r=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now,a=l(t.navigator.userAgent)?1:0;t.scroll=t.scrollTo=function(){void 0!==arguments[0]&&(!0!==d(arguments[0])?v.call(t,e.body,void 0!==arguments[0].left?~~arguments[0].left:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:t.scrollY||t.pageYOffset):s.scroll.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:t.scrollY||t.pageYOffset))},t.scrollBy=function(){void 0!==arguments[0]&&(d(arguments[0])?s.scrollBy.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):v.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset)))},o.prototype.scroll=o.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==d(arguments[0])){var t=arguments[0].left,e=arguments[0].top;v.call(this,this,"undefined"===typeof t?this.scrollLeft:~~t,"undefined"===typeof e?this.scrollTop:~~e)}else{if("number"===typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!==typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},o.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==d(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},o.prototype.scrollIntoView=function(){if(!0!==d(arguments[0])){var o=h(this),i=o.getBoundingClientRect(),r=this.getBoundingClientRect();o!==e.body?(v.call(this,o,o.scrollLeft+r.left-i.left,o.scrollTop+r.top-i.top),"fixed"!==t.getComputedStyle(o).position&&t.scrollBy({left:i.left,top:i.top,behavior:"smooth"})):t.scrollBy({left:r.left,top:r.top,behavior:"smooth"})}else s.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function l(t){var e=["MSIE ","Trident/","Edge/"];return new RegExp(e.join("|")).test(t)}function n(t,e){this.scrollLeft=t,this.scrollTop=e}function c(t){return.5*(1-Math.cos(Math.PI*t))}function d(t){if(null===t||"object"!==typeof t||void 0===t.behavior||"auto"===t.behavior||"instant"===t.behavior)return!0;if("object"===typeof t&&"smooth"===t.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+t.behavior+" is not a valid value for enumeration ScrollBehavior.")}function f(t,e){return"Y"===e?t.clientHeight+a<t.scrollHeight:"X"===e?t.clientWidth+a<t.scrollWidth:void 0}function u(e,o){var i=t.getComputedStyle(e,null)["overflow"+o];return"auto"===i||"scroll"===i}function p(t){var e=f(t,"Y")&&u(t,"Y"),o=f(t,"X")&&u(t,"X");return e||o}function h(t){while(t!==e.body&&!1===p(t))t=t.parentNode||t.host;return t}function m(e){var o,s,a,l=r(),n=(l-e.startTime)/i;n=n>1?1:n,o=c(n),s=e.startX+(e.x-e.startX)*o,a=e.startY+(e.y-e.startY)*o,e.method.call(e.scrollable,s,a),s===e.x&&a===e.y||t.requestAnimationFrame(m.bind(t,e))}function v(o,i,a){var l,c,d,f,u=r();o===e.body?(l=t,c=t.scrollX||t.pageXOffset,d=t.scrollY||t.pageYOffset,f=s.scroll):(l=o,c=o.scrollLeft,d=o.scrollTop,f=n),m({scrollable:l,method:f,startTime:u,startX:c,startY:d,x:i,y:a})}}t.exports={polyfill:e}})()},"849b":function(t,e,o){t.exports=o.p+"img/to-top.f8c6e860.png"},"86e1":function(t,e,o){"use strict";var i=o("18df"),s=o.n(i);s.a},"9cfc":function(t,e,o){},a9e3:function(t,e,o){"use strict";var i=o("83ab"),s=o("da84"),r=o("94ca"),a=o("6eeb"),l=o("5135"),n=o("c6b6"),c=o("7156"),d=o("c04e"),f=o("d039"),u=o("7c73"),p=o("241c").f,h=o("06cf").f,m=o("9bf2").f,v=o("58a8").trim,g="Number",_=s[g],y=_.prototype,b=n(u(y))==g,w=function(t){var e,o,i,s,r,a,l,n,c=d(t,!1);if("string"==typeof c&&c.length>2)if(c=v(c),e=c.charCodeAt(0),43===e||45===e){if(o=c.charCodeAt(2),88===o||120===o)return NaN}else if(48===e){switch(c.charCodeAt(1)){case 66:case 98:i=2,s=49;break;case 79:case 111:i=8,s=55;break;default:return+c}for(r=c.slice(2),a=r.length,l=0;l<a;l++)if(n=r.charCodeAt(l),n<48||n>s)return NaN;return parseInt(r,i)}return+c};if(r(g,!_(" 0o1")||!_("0b1")||_("+0x1"))){for(var S,T=function(t){var e=arguments.length<1?0:t,o=this;return o instanceof T&&(b?f((function(){y.valueOf.call(o)})):n(o)!=g)?c(new _(w(e)),o,T):w(e)},L=i?p(_):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),C=0;L.length>C;C++)l(_,S=L[C])&&!l(T,S)&&m(T,S,h(_,S));T.prototype=y,y.constructor=T,a(s,g,T)}},b56f:function(t,e,o){"use strict";var i=o("9cfc"),s=o.n(i);s.a},e7e5:function(t,e,o){"use strict";o("68ef"),o("a71a"),o("9d70"),o("3743"),o("4d75"),o("e3b3"),o("b258")}}]);