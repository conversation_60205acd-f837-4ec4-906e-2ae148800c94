(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["myFans"],{"0866":function(t,e,o){"use strict";o.d(e,"d",(function(){return i})),o.d(e,"a",(function(){return r})),o.d(e,"e",(function(){return c})),o.d(e,"b",(function(){return l})),o.d(e,"c",(function(){return f}));var n=navigator.userAgent.toLowerCase()||window.navigator.userAgent.toLowerCase(),s=(/youpin/i.test(n),/xianmai/i.test(n)),a="";a=s?"xm://xianmai":"yp://youpin";"".concat(a,"?position=login");var i="".concat(a,"?web="),r="".concat(a,"?position=authentication"),c="".concat(a,"?position=withdraw"),l="".concat(a,"?openBrowser="),f="".concat(a,"?position=wantToJoinIn");"".concat(a,"?position=mywallet")},"18df":function(t,e,o){},3814:function(t,e,o){t.exports=o.p+"img/copy.8e3c61d3.svg"},"3b22":function(t,e,o){"use strict";var n=o("c0f5"),s=o.n(n);s.a},"5d30":function(t,e,o){"use strict";var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("img",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],style:{bottom:t.pointBottom,right:t.pointRight},attrs:{src:o("849b"),alt:""},on:{click:t.toTop}})},s=[],a=(o("a9e3"),o("7707")),i=o.n(a),r={name:"toTop",props:{pointBottom:{default:"5.6875rem",type:String},pointRight:{default:"0px",type:String},scrollShow:{default:500,type:Number}},data:function(){return{isShow:!1}},methods:{onScroll:function(t){var e=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;e>this.scrollShow&&!this.isShow?this.isShow=!0:e<=this.scrollShow&&this.isShow&&(this.isShow=!1)},toTop:function(){window.scroll({top:0,behavior:"smooth"})}},mounted:function(){i.a.polyfill(),window.addEventListener("scroll",this.onScroll)}},c=r,l=(o("86e1"),o("2877")),f=Object(l["a"])(c,n,s,!1,null,"793636d7",null);e["a"]=f.exports},7707:function(t,e,o){(function(){"use strict";function e(){var t=window,e=document;if(!("scrollBehavior"in e.documentElement.style&&!0!==t.__forceSmoothScrollPolyfill__)){var o=t.HTMLElement||t.Element,n=468,s={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:o.prototype.scroll||c,scrollIntoView:o.prototype.scrollIntoView},a=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now,i=r(t.navigator.userAgent)?1:0;t.scroll=t.scrollTo=function(){void 0!==arguments[0]&&(!0!==f(arguments[0])?v.call(t,e.body,void 0!==arguments[0].left?~~arguments[0].left:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:t.scrollY||t.pageYOffset):s.scroll.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:t.scrollY||t.pageYOffset))},t.scrollBy=function(){void 0!==arguments[0]&&(f(arguments[0])?s.scrollBy.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):v.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset)))},o.prototype.scroll=o.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==f(arguments[0])){var t=arguments[0].left,e=arguments[0].top;v.call(this,this,"undefined"===typeof t?this.scrollLeft:~~t,"undefined"===typeof e?this.scrollTop:~~e)}else{if("number"===typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!==typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},o.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==f(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},o.prototype.scrollIntoView=function(){if(!0!==f(arguments[0])){var o=m(this),n=o.getBoundingClientRect(),a=this.getBoundingClientRect();o!==e.body?(v.call(this,o,o.scrollLeft+a.left-n.left,o.scrollTop+a.top-n.top),"fixed"!==t.getComputedStyle(o).position&&t.scrollBy({left:n.left,top:n.top,behavior:"smooth"})):t.scrollBy({left:a.left,top:a.top,behavior:"smooth"})}else s.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function r(t){var e=["MSIE ","Trident/","Edge/"];return new RegExp(e.join("|")).test(t)}function c(t,e){this.scrollLeft=t,this.scrollTop=e}function l(t){return.5*(1-Math.cos(Math.PI*t))}function f(t){if(null===t||"object"!==typeof t||void 0===t.behavior||"auto"===t.behavior||"instant"===t.behavior)return!0;if("object"===typeof t&&"smooth"===t.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+t.behavior+" is not a valid value for enumeration ScrollBehavior.")}function u(t,e){return"Y"===e?t.clientHeight+i<t.scrollHeight:"X"===e?t.clientWidth+i<t.scrollWidth:void 0}function p(e,o){var n=t.getComputedStyle(e,null)["overflow"+o];return"auto"===n||"scroll"===n}function d(t){var e=u(t,"Y")&&p(t,"Y"),o=u(t,"X")&&p(t,"X");return e||o}function m(t){while(t!==e.body&&!1===d(t))t=t.parentNode||t.host;return t}function h(e){var o,s,i,r=a(),c=(r-e.startTime)/n;c=c>1?1:c,o=l(c),s=e.startX+(e.x-e.startX)*o,i=e.startY+(e.y-e.startY)*o,e.method.call(e.scrollable,s,i),s===e.x&&i===e.y||t.requestAnimationFrame(h.bind(t,e))}function v(o,n,i){var r,l,f,u,p=a();o===e.body?(r=t,l=t.scrollX||t.pageXOffset,f=t.scrollY||t.pageYOffset,u=s.scroll):(r=o,l=o.scrollLeft,f=o.scrollTop,u=c),h({scrollable:r,method:u,startTime:p,startX:l,startY:f,x:n,y:i})}}t.exports={polyfill:e}})()},"849b":function(t,e,o){t.exports=o.p+"img/to-top.f8c6e860.png"},"86e1":function(t,e,o){"use strict";var n=o("18df"),s=o.n(n);s.a},a9e3:function(t,e,o){"use strict";var n=o("83ab"),s=o("da84"),a=o("94ca"),i=o("6eeb"),r=o("5135"),c=o("c6b6"),l=o("7156"),f=o("c04e"),u=o("d039"),p=o("7c73"),d=o("241c").f,m=o("06cf").f,h=o("9bf2").f,v=o("58a8").trim,b="Number",g=s[b],_=g.prototype,w=c(p(_))==b,y=function(t){var e,o,n,s,a,i,r,c,l=f(t,!1);if("string"==typeof l&&l.length>2)if(l=v(l),e=l.charCodeAt(0),43===e||45===e){if(o=l.charCodeAt(2),88===o||120===o)return NaN}else if(48===e){switch(l.charCodeAt(1)){case 66:case 98:n=2,s=49;break;case 79:case 111:n=8,s=55;break;default:return+l}for(a=l.slice(2),i=a.length,r=0;r<i;r++)if(c=a.charCodeAt(r),c<48||c>s)return NaN;return parseInt(a,n)}return+l};if(a(b,!g(" 0o1")||!g("0b1")||g("+0x1"))){for(var x,C=function(t){var e=arguments.length<1?0:t,o=this;return o instanceof C&&(w?u((function(){_.valueOf.call(o)})):c(o)!=b)?l(new g(y(e)),o,C):y(e)},S=n?d(g):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),I=0;S.length>I;I++)r(g,x=S[I])&&!r(C,x)&&h(C,x,m(g,x));C.prototype=_,_.constructor=C,i(s,b,C)}},c0f5:function(t,e,o){},dee9:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"main"},[n("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),n("div",{staticClass:"main-header"},[n("div",{staticClass:"main-header-row"},[n("van-image",{staticClass:"main-header-row-avater",attrs:{src:t.phoneUserInfo.headimg,round:""}}),n("div",{staticClass:"main-header-row-center"},[n("p",{staticClass:"main-header-row-center-name"},[t._v(t._s(t.phoneUserInfo.nickname))]),n("p",{staticClass:"main-header-row-center-phone"},[t._v("手机号："+t._s(t.phoneUserInfo.mobile?t.addStar(t.phoneUserInfo.mobile,3,4):"")+" "),n("img",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:t.phoneUserInfo.mobile,expression:"phoneUserInfo.mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:t.onCopied,expression:"onCopied",arg:"success"}],staticClass:"main-header-row-center-phone-copy",attrs:{src:o("3814")}})]),n("p",{staticClass:"main-header-row-center-referrer"},[t._v("注册于："+t._s(t.phoneUserInfo.reg_date))])])],1)]),t.phoneUserInfo.member_tags&&t.phoneUserInfo.member_tags.length?n("div",{staticClass:"tags"},[n("p",{staticClass:"tags-title"},[t._v("用户标签")]),n("div",{staticClass:"tags-list"},t._l(t.phoneUserInfo.member_tags,(function(e,o){return n("span",{staticClass:"tags-list-one"},[t._v(t._s(e))])})),0)]):t._e(),n("div",{staticClass:"one-statistics"},[t._m(0),n("div",{staticClass:"one-statistics-info"},[t._l(t.infoList,(function(e,o){return[n("div",{staticClass:"one-statistics-info-item",style:e.notShow?"visibility: hidden":""},[n("span",{staticClass:"one-statistics-info-item-number"},[t._v(t._s(e.number))]),n("span",{staticClass:"one-statistics-info-item-text"},[t._v(t._s(e.text))]),e.tip?n("span",{staticClass:"one-statistics-info-item-tip"},[t._v(t._s(e.tip))]):t._e()])]}))],2)]),n("div",{staticClass:"situation"},[n("p",{staticClass:"situation-title"},[t._v("消费情况")]),n("div",{staticClass:"situation-list"},[n("van-list",{attrs:{finished:t.fansFinished,"finished-text":"没有更多了"},on:{load:t.onLoadFans},model:{value:t.fansLoading,callback:function(e){t.fansLoading=e},expression:"fansLoading"}},t._l(t.fansList,(function(e,o){return n("div",{key:o,staticClass:"situation-list-one"},[n("div",{staticClass:"situation-list-one-left"},[n("van-image",{staticClass:"situation-list-one-left-img",attrs:{src:e.sku_image},scopedSlots:t._u([{key:"loading",fn:function(){return[n("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),n("div",{staticClass:"situation-list-one-left-info"},[n("p",{staticClass:"situation-list-one-left-info-name"},[t._v(t._s(e.order_no))]),n("p",{staticClass:"situation-list-one-left-info-time"},[t._v(t._s(e.create_time))])])],1),n("div",{staticClass:"situation-list-one-right"},[n("p",{staticClass:"situation-list-one-right-price"},[t._v(t._s(e.order_money))]),parseFloat(e.income)>0?n("p",{staticClass:"situation-list-one-right-op"},[t._v("赚"+t._s(e.income))]):t._e()])])})),0)],1)]),n("to-top")],1)},s=[function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"one-statistics-header"},[o("span",{staticClass:"one-statistics-header-left"},[t._v("消费情况")])])}],a=(o("99af"),o("96cf"),o("1da1")),i=(o("e7e5"),o("d399")),r=o("5530"),c=o("2f62"),l=o("6917"),f=(o("0866"),o("c391")),u=o("ce3a"),p=o("5d30"),d=o("2a9b"),m={name:"myFans",data:function(){return{reference:0,title:"我的粉丝",phoneUserInfo:{},infoList:[{number:0,text:"下单量"},{number:0,text:"消费总额"},{number:0,text:"售后单量"},{number:0,text:"产生佣金"},{number:0,text:"推荐好友"},{number:0,text:"推荐好友",notShow:!0}],fansPage:1,fans_page_count:1,fansList:[],fansLoading:!1,fansFinished:!1,member_id_turn:null}},computed:Object(r["a"])({},Object(c["b"])(["token"])),components:{toTop:p["a"]},methods:{addStar:d["a"],goBack:function(){this.reference?this.$router.back():Object(l["a"])("0")},onCopied:function(t){Object(i["a"])("复制成功")},getPhoneUserInfo:function(){var t=this;return Object(a["a"])(regeneratorRuntime.mark((function e(){var o,n,s,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=i["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),n={token:t.token,member_id_turn:t.member_id_turn},e.prev=2,e.next=5,t.$axios.post(Object(f["a"])(u["a"].ShopInfoUrl),n);case 5:s=e.sent,a=s.data,0!=a.code?Object(i["a"])(a.message):(a.data.member_info&&(t.phoneUserInfo=a.data.member_info),o.clear()),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](2),Object(i["a"])(e.t0.message);case 13:case"end":return e.stop()}}),e,null,[[2,10]])})))()},getSaleStatistics:function(){var t=this;return Object(a["a"])(regeneratorRuntime.mark((function e(){var o,n,s,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=i["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),n={token:t.token,child_id:t.member_id_turn},e.prev=2,e.next=5,t.$axios.post(Object(f["a"])(u["a"].saleStatisticsUrl),n);case 5:s=e.sent,a=s.data,0!=a.code?Object(i["a"])(a.message):(o.clear(),t.infoList=[{number:a.data.order_pay_nums,text:"下单量"},{number:a.data.sale_price,text:"消费总额"},{number:a.data.after_sale_nums,text:"售后单量"},{number:a.data.income_money,text:"产生佣金"},{number:a.data.fans_nums,text:"推荐好友"},{number:a.data.team_shop_nums,text:"推荐好友",notShow:!0}]),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](2),Object(i["a"])(e.t0.message);case 13:case"end":return e.stop()}}),e,null,[[2,10]])})))()},onLoadFans:function(){var t=this;return Object(a["a"])(regeneratorRuntime.mark((function e(){var o,n,s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o={token:t.token,page:t.fansPage,child_id:t.member_id_turn},e.prev=1,e.next=4,t.$axios.post(Object(f["a"])(u["a"].saleOrderDataUrl),o);case 4:n=e.sent,s=n.data,0!=s.code?Object(i["a"])(s.message):(t.fansList=t.fansList.concat(s.data.list),t.fans_page_count=s.data.page_count,t.fansLoading=!1,t.fansPage>=t.fans_page_count?t.fansFinished=!0:t.fansPage+=1),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),Object(i["a"])(e.t0.message);case 12:case"end":return e.stop()}}),e,null,[[1,9]])})))()}},created:function(){var t=this;return Object(a["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.reference=t.$route.query.reference?t.$route.query.reference:0,t.member_id_turn=t.$route.query.member_id_turn||null,e.next=4,t.getPhoneUserInfo();case 4:return e.next=6,t.getSaleStatistics();case 6:case"end":return e.stop()}}),e)})))()}},h=m,v=(o("3b22"),o("2877")),b=Object(v["a"])(h,n,s,!1,null,"5a5b7af2",null);e["default"]=b.exports},e7e5:function(t,e,o){"use strict";o("68ef"),o("a71a"),o("9d70"),o("3743"),o("4d75"),o("e3b3"),o("b258")}}]);