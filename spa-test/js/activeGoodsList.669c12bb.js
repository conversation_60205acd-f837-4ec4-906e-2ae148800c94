(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["activeGoodsList"],{"0730":function(t,i,e){"use strict";var a=e("5200"),s=e.n(a);s.a},5200:function(t,i,e){},e7e5:function(t,i,e){"use strict";e("68ef"),e("a71a"),e("9d70"),e("3743"),e("4d75"),e("e3b3"),e("b258")},f00d:function(t,i,e){"use strict";e.r(i);var a=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("div",{staticClass:"active-goods-list"},[a("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),a("div",{staticClass:"active-list-box"},[!t.finished||t.finished&&t.activeListData.length>0?a("van-list",{staticClass:"van-list",attrs:{error:t.error,"error-text":"请求失败，点击重新加载",finished:t.finished,"finished-text":"没有更多了"},on:{"update:error":function(i){t.error=i},load:t.getData},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.activeListData,(function(i,e){return a("div",{key:e,staticClass:"active-list"},[a("div",{staticClass:"active-list-products",on:{click:function(e){return t.toDetails(i)}}},[a("div",{staticClass:"active-list-products-img"},[a("van-image",{attrs:{src:i.img}})],1),a("div",{staticClass:"active-list-products-price"},[a("div",{staticClass:"item-name"},[t._v(t._s(i.name?i.name:"-"))]),a("div",{staticClass:"item-price"},[t._v("￥"),a("span",[t._v(t._s(i.price?i.price:"-"))])]),a("div",{staticClass:"item-status"},[a("div",[t._v("活动类型 "+t._s(i.activity_type_txt?i.activity_type_txt:"-"))]),a("div",[a("span",[t._v("佣金 ")]),a("span",{staticClass:"price-word-two"},[t._v("￥"+t._s(i.profit?i.profit:"-"))])])]),a("div",{staticClass:"item-time"},[t._v(t._s(i.start_time)+" ~ "+t._s(i.end_time))])])])])})),0):a("div",{staticClass:"active-list-default"},[a("img",{attrs:{src:e("f1b1")}}),a("p",[t._v("暂无数据")])])],1)],1)},s=[],c=(e("d3b7"),e("e7e5"),e("d399")),n=e("5530"),o=e("6917"),r=e("ce3a"),l=e("c391"),d=e("2f62"),v={name:"activeGoodsList",components:{},data:function(){return{title:"",page:1,page_size:10,error:!1,loading:!1,finished:!1,activeListData:[],shopId:"",reference:0}},computed:Object(n["a"])({},Object(d["b"])(["token"])),created:function(){this.title=this.$route.meta.title,this.reference=this.$route.query.reference||0,this.$route.query.shop_id&&(this.shopId=this.$route.query.shop_id)},methods:{getData:function(){var t=this,i=c["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),e={token:this.token,page:this.page,size:this.page_size};this.$axios.post(Object(l["a"])(r["a"].activeProductList),e).then((function(e){var a=e.data;if(0!=a.code)Object(c["a"])(a.message);else{for(var s=0;s<a.data.list.length;s++)t.$set(t.activeListData,t.activeListData.length,a.data.list[s]);i.clear(),t.loading=!1,t.activeListData.length>=a.data.total&&(t.finished=!0),t.page=t.page+1}})).catch((function(e){i.clear(),t.loading=!1,t.page=1,t.finished=!0,t.error=!0})).finally((function(){}))},goBack:function(){this.reference?this.$router.back():Object(o["a"])("0")},toDetails:function(t){console.log(t),Object(o["c"])(t.wechat_path,(function(){}))}}},f=v,u=(e("0730"),e("2877")),p=Object(u["a"])(f,a,s,!1,null,"256cdd23",null);i["default"]=p.exports},f1b1:function(t,i,e){t.exports=e.p+"img/img-shopper-default.cb55b4c9.png"}}]);