(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["workOrderDetail"],{"2c54":function(e,t,a){},"3f67":function(e,t,a){"use strict";var r=a("2c54"),n=a.n(r);n.a},"44d0":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"main"},[r("van-nav-bar",{attrs:{title:e.title,"left-text":"返回","left-arrow":"",fixed:!0},on:{"click-left":e.onClickLeft}}),r("div",{staticClass:"order-list"},[e.dataDict.records.length?r("div",{staticClass:"order-list-one"},[r("div",{staticClass:"order-list-one-header"},[r("span",{staticClass:"order-list-one-header-title"},[e._v(e._s(e.dataDict.question_title))]),r("van-tag",{attrs:{type:"primary"}},[e._v(e._s(e.dataDict.records[0].create_user))])],1),r("div",{staticClass:"order-list-one-time"},[r("div",{staticClass:"order-list-one-time-left"},[e._v(e._s(e.dataDict.records[0].customer_mobile)+" "),r("van-icon",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.dataDict.records[0].customer_mobile,expression:"dataDict.records[0].customer_mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.firstCopySuccess,expression:"firstCopySuccess",arg:"success"},{name:"clipboard",rawName:"v-clipboard:error",value:e.firstCopyError,expression:"firstCopyError",arg:"error"}],staticStyle:{"margin-left":"0.3125rem"},attrs:{name:a("f42c"),color:"#666666"}})],1),r("div",{staticClass:"order-list-one-time-right"},[e._v(e._s(e._f("dateStr")(e.dataDict.records[0].create_time))),r("van-icon",{staticStyle:{"margin-left":"0.1875rem"},attrs:{name:"underway-o",color:"#666666"}})],1)]),r("div",{staticClass:"order-list-one-desc"},[e._v(e._s(e.dataDict.records[0].content))]),r("div",{staticClass:"order-list-one-annex"},[e._l(e.dataDict.records[0].files,(function(t,a){return["image"===e.checkMediaType(t)?r("van-image",{staticClass:"order-list-one-annex-one",attrs:{width:"6.25rem",height:"6.25rem",fit:"cover",src:t},on:{click:function(a){return e.toPreviewImages(e.dataDict.records[0].files,t)}}}):e._e(),"video"===e.checkMediaType(t)?r("video",{staticClass:"order-list-one-annex-one order-list-one-annex-video",attrs:{controls:"",src:t}}):e._e()]}))],2)]):e._e()]),e.dataDict.records.length&&e.dataDict.records.filter((function(e,t){return 0!=t})).length?r("div",{staticClass:"follow-list"},e._l(e.dataDict.records.filter((function(e,t){return 0!=t})),(function(t,a){return r("div",{staticClass:"follow-list-one"},[r("div",{staticClass:"follow-list-one-header"},[r("span",{staticClass:"follow-list-one-header-title"},[e._v(e._s(e._f("dateStr")(t.create_time))+"跟进情况")]),r("van-tag",{attrs:{type:"primary"}},[e._v(e._s(t.create_user))])],1),r("div",{staticClass:"follow-list-one-desc"},[e._v(e._s(t.content))]),r("div",{staticClass:"follow-list-one-annex"},[e._l(t.files,(function(a,n){return["image"===e.checkMediaType(a)?r("van-image",{staticClass:"follow-list-one-annex-one",attrs:{width:"6.25rem",height:"6.25rem",fit:"cover",src:a},on:{click:function(r){return e.toPreviewImages(t.files,a)}}}):e._e(),"video"===e.checkMediaType(a)?r("video",{staticClass:"follow-list-one-annex-one order-list-one-annex-video",attrs:{controls:"",src:a}}):e._e()]}))],2)])})),0):e._e(),r("div",{staticClass:"order-op"},[r("van-button",{staticClass:"order-op-button",attrs:{type:"info",round:"",size:"small"},on:{click:e.newFollow}},[e._v("新增跟进")])],1),r("van-popup",{attrs:{round:"",position:"bottom","close-on-click-overlay":!1},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[r("div",{staticClass:"edit-follow"},[r("div",{staticClass:"edit-follow-header"},[r("van-switch",{attrs:{size:"20"},model:{value:e.editForm.is_end_follow,callback:function(t){e.$set(e.editForm,"is_end_follow",t)},expression:"editForm.is_end_follow"}}),r("span",[e._v(e._s(e.editForm.is_end_follow?"结束该工单":"需继续跟进"))])],1),r("div",{staticClass:"edit-follow-info"},[r("van-field",{staticStyle:{padding:"0",height:"6.875rem"},attrs:{type:"textarea",placeholder:"请填写工单跟进情况"},model:{value:e.editForm.content,callback:function(t){e.$set(e.editForm,"content",t)},expression:"editForm.content"}}),r("van-uploader",{attrs:{multiple:"","max-count":9,accept:"image/*,video/*","max-size":107374182400,"after-read":e.uploadFile,"before-delete":e.DeleteFile},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),r("div",{staticClass:"edit-follow-op"},[r("van-button",{staticClass:"edit-follow-op-button",attrs:{type:"info",round:"",hairline:"",plain:"",size:"small"},on:{click:function(t){e.show=!1}}},[e._v("取消")]),r("van-button",{staticClass:"edit-follow-op-button",attrs:{type:"info",round:"",size:"small"},on:{click:e.submitFollow}},[e._v("确认")])],1)])])],1)},n=[],i=(a("4de4"),a("caad"),a("c975"),a("baa5"),a("a434"),a("ac1f"),a("5319"),a("498a"),a("e17f"),a("2241")),s=(a("4662"),a("28a2")),o=(a("e7e5"),a("d399")),c=(a("96cf"),a("1da1")),l=a("5530"),d=a("2f62"),u=a("ce3a"),f=a("c391"),p={name:"workOrderDetail",data:function(){return{title:"",reception_id:"",cs_order_id:"",show:!1,editForm:{is_end_follow:!1,content:"",files:[]},fileList:[],dataDict:{records:[]}}},computed:Object(l["a"])({},Object(d["b"])(["staffUserId"])),filters:{dateStr:function(e){var t=new Date(e);return t.getFullYear()+"年"+(t.getMonth()+1)+"月"+t.getDate()+"日 "+t.getHours()+":"+t.getMinutes()}},created:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.title=e.$route.meta.title,e.cs_order_id=e.$route.query.cs_order_id,e.reception_id=e.$route.query.reception_id,t.next=5,e.getData();case 5:case"end":return t.stop()}}),t)})))()},methods:{onClickLeft:function(){this.$router.replace({name:"serviceData"})},firstCopySuccess:function(e){Object(o["a"])("复制成功")},firstCopyError:function(e){Object(o["a"])("复制失败!")},checkMediaType:function(e){e=e.toLowerCase();var t=[".jpg",".jpeg",".png",".gif",".bmp",".webp"],a=[".mp4",".webm",".mov",".avi",".mkv",".flv"],r=e.substring(e.lastIndexOf("."));return t.includes(r)?"image":a.includes(r)?"video":"unknown"},toPreviewImages:function(e,t){var a=this;e=e.filter((function(e){return"image"===a.checkMediaType(e)}));var r=e.indexOf(t);Object(s["a"])({images:e,startPosition:r})},getData:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post(Object(f["a"])(u["a"].followingDetailUrl),{cs_order_id:e.cs_order_id,token:e.staffUserId});case 3:a=t.sent,r=a.data,0!=r.code?Object(o["a"])(r.message):e.dataDict=r.data,t.next=10;break;case 8:t.prev=8,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,null,[[0,8]])})))()},newFollow:function(){this.show=!0},uploadImage:function(e,t){var a=this;return Object(c["a"])(regeneratorRuntime.mark((function r(){var n,i,s;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.status="uploading",e.message="上传中...",n=new FormData,n.append("file",e.file),r.prev=4,r.next=7,a.$axios.post(Object(f["a"])(u["a"].xmUserTaskVideoImageUrl),n,{headers:{"Content-Type":"multipart/form-data"}});case 7:if(i=r.sent,s=i.data,10067==s.code){r.next=15;break}return e.status="failed",e.message="上传失败",r.abrupt("return");case 15:a.editForm.files[t]=s.data.pic_path,e.status="success",e.message="上传成功";case 18:r.next=24;break;case 20:r.prev=20,r.t0=r["catch"](4),e.status="failed",e.message="上传失败";case 24:case"end":return r.stop()}}),r,null,[[4,20]])})))()},uploadVideo:function(e,t){var a=this;return Object(c["a"])(regeneratorRuntime.mark((function r(){var n,i,s;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.status="uploading",e.message="上传中...",n=new FormData,n.append("file",e.file),r.prev=4,r.next=7,a.$axios.post(Object(f["a"])(u["a"].userShareVideoUrl),n,{headers:{"Content-Type":"multipart/form-data"}});case 7:if(i=r.sent,s=i.data,10067==s.code){r.next=15;break}return e.status="failed",e.message="上传失败",r.abrupt("return");case 15:a.editForm.files[t]=s.data.path,e.status="success",e.message="上传成功";case 18:r.next=24;break;case 20:r.prev=20,r.t0=r["catch"](4),e.status="failed",e.message="上传失败";case 24:case"end":return r.stop()}}),r,null,[[4,20]])})))()},uploadFile:function(e){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(console.log(e),!(e instanceof Array)){a.next=17;break}r=0;case 3:if(!(r<e.length)){a.next=15;break}if(-1==e[r].file.type.indexOf("image")){a.next=9;break}return a.next=7,t.uploadImage(e[r],t.editForm.files.length);case 7:a.next=12;break;case 9:if(-1==e[r].file.type.indexOf("video")){a.next=12;break}return a.next=12,t.uploadVideo(e[r],t.editForm.files.length);case 12:r++,a.next=3;break;case 15:a.next=25;break;case 17:if(-1==e.file.type.indexOf("image")){a.next=22;break}return a.next=20,t.uploadImage(e,t.editForm.files.length);case 20:a.next=25;break;case 22:if(-1==e.file.type.indexOf("video")){a.next=25;break}return a.next=25,t.uploadVideo(e,t.editForm.files.length);case 25:case"end":return a.stop()}}),a)})))()},DeleteFile:function(e,t){var a=this;return Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.editForm.files.splice(t.index,1);case 1:case"end":return e.stop()}}),e)})))()},submitFollow:function(){var e=this;this.editForm.content.trim()?i["a"].confirm({title:"提示",message:"确认保存工单？"}).then(Object(c["a"])(regeneratorRuntime.mark((function t(){var a,r,n,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=o["a"].loading({duration:0,forbidClick:!0,message:"提交中..."}),r=Object(l["a"])({token:e.staffUserId,reception_id:e.reception_id,cs_order_id:e.cs_order_id},e.editForm),t.prev=2,t.next=5,e.$axios.post(Object(f["a"])(u["a"].addFollowOrderUrl),r);case 5:if(n=t.sent,i=n.data,0==i.code){t.next=13;break}return a.clear(),Object(o["a"])(i.message),t.abrupt("return");case 13:if(e.show=!1,e.editForm.content="",e.editForm.files=[],e.fileList=[],!e.editForm.is_end_follow){t.next=22;break}return t.next=20,e.endFollow(a);case 20:t.next=24;break;case 22:a.clear(),Object(o["a"])({message:i.message,onClose:function(){e.onClickLeft()}});case 24:return t.next=26,e.getData();case 26:t.next=32;break;case 28:t.prev=28,t.t0=t["catch"](2),Object(o["a"])(t.t0.message),a.clear();case 32:case"end":return t.stop()}}),t,null,[[2,28]])})))).catch((function(){})):Object(o["a"])("请填写内容")},endFollow:function(e){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var r,n,i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r={token:t.staffUserId,reception_id:t.reception_id,cs_order_id:t.cs_order_id},a.prev=1,a.next=4,t.$axios.post(Object(f["a"])(u["a"].endFollowUrl),r);case 4:if(n=a.sent,i=n.data,e.clear(),0==i.code){a.next=12;break}return Object(o["a"])(i.message),a.abrupt("return");case 12:t.show=!1,t.editForm.content="",t.editForm.files=[],Object(o["a"])({message:i.message,onClose:function(){}});case 16:a.next=21;break;case 18:a.prev=18,a.t0=a["catch"](1),Object(o["a"])(a.t0.message);case 21:case"end":return a.stop()}}),a,null,[[1,18]])})))()}}},m=p,v=(a("3f67"),a("2877")),g=Object(v["a"])(m,r,n,!1,null,"129b1e7e",null);t["default"]=g.exports},a434:function(e,t,a){"use strict";var r=a("23e7"),n=a("23cb"),i=a("a691"),s=a("50c4"),o=a("7b0b"),c=a("65f0"),l=a("8418"),d=a("1dde"),u=a("ae40"),f=d("splice"),p=u("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,v=Math.min,g=9007199254740991,h="Maximum allowed length exceeded";r({target:"Array",proto:!0,forced:!f||!p},{splice:function(e,t){var a,r,d,u,f,p,b=o(this),w=s(b.length),x=n(e,w),_=arguments.length;if(0===_?a=r=0:1===_?(a=0,r=w-x):(a=_-2,r=v(m(i(t),0),w-x)),w+a-r>g)throw TypeError(h);for(d=c(b,r),u=0;u<r;u++)f=x+u,f in b&&l(d,u,b[f]);if(d.length=r,a<r){for(u=x;u<w-r;u++)f=u+r,p=u+a,f in b?b[p]=b[f]:delete b[p];for(u=w;u>w-r+a;u--)delete b[u-1]}else if(a>r)for(u=w-r;u>x;u--)f=u+r-1,p=u+a-1,f in b?b[p]=b[f]:delete b[p];for(u=0;u<a;u++)b[u+x]=arguments[u+2];return b.length=w-r+a,d}})},baa5:function(e,t,a){var r=a("23e7"),n=a("e58c");r({target:"Array",proto:!0,forced:n!==[].lastIndexOf},{lastIndexOf:n})},caad:function(e,t,a){"use strict";var r=a("23e7"),n=a("4d64").includes,i=a("44d2"),s=a("ae40"),o=s("indexOf",{ACCESSORS:!0,1:0});r({target:"Array",proto:!0,forced:!o},{includes:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),i("includes")},e58c:function(e,t,a){"use strict";var r=a("fc6a"),n=a("a691"),i=a("50c4"),s=a("a640"),o=a("ae40"),c=Math.min,l=[].lastIndexOf,d=!!l&&1/[1].lastIndexOf(1,-0)<0,u=s("lastIndexOf"),f=o("indexOf",{ACCESSORS:!0,1:0}),p=d||!u||!f;e.exports=p?function(e){if(d)return l.apply(this,arguments)||0;var t=r(this),a=i(t.length),s=a-1;for(arguments.length>1&&(s=c(s,n(arguments[1]))),s<0&&(s=a+s);s>=0;s--)if(s in t&&t[s]===e)return s||0;return-1}:l},e7e5:function(e,t,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")},f42c:function(e,t,a){e.exports=a.p+"img/copy-four.c067862f.png"}}]);