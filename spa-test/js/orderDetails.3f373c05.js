(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["orderDetails"],{3438:function(s,t){s.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA5CAMAAAC7xnO3AAABklBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ZVs66AAAAhXRSTlMAAQIDBAUGBwgJCwwNDg8QExQVFhsdHh8gISMkJTQ4OT1GSElKS0xNTlFTVFtcXWJjbW5vc3Z4e3x+f4CBgoOEhYaHiIyNj5CRkpaXmJqhoqOkpaanqaquubq7vL2+v8nKy8zNzs/Q0djZ2tvc3eHj5OXm5+jp7O3x8vP09vf4+fr7/P3+dii+4QAAA0JJREFUSMell21D01YYhp9ig8zS2jKsWh0vQstEHEO36aTaOi3uRUELwyKVrSiOoWMKq1VMQ9vk+t/7QEqhyUnT7f7Wk1xNznnu85w7Ik5p0cSlG4svtj/WPm6/WLwxlohq4keh0TuFtyYtmW8Ld0ZDHbmTU6X3DdrVeF+aOunJhS+VUKk0FlaDQ/Of7Ntqr4uPH2Rnsw8eF1/X7LFP80MKLjizY0/vr9sjZwdCWo/0aKGBsyO339gT3rkWdAOj9w0Aq7L2ZW/bpd6JtYoFYOSiTnDwyT6AvpTqc/nbvtSiDrD/ZNABrpoAu5fDAdepBMKXdwHM1TY0mjeBeuG8x8KfL9QBM3/shYO5GmA+PONZszMPTaCWO7pMMwZgZTsZJZS1AGOmNYehHaD+qL+jw/of1YGdoeZaRPImUIz7MHW8CJj5iP1zXAfKF3zthwtlQE/atXoJVK847/ri9w85h1evVIHSgf2nAWv5tJP8Dfa+ah+MLFvAlIhIeB2oTLgYoAHW9w5LpCrAelhEkhVgzc1yALNOI64BlaSIdrcBjItvUsaBxl1NYs+ATa0LUtsEnsXkYtn9upqUW0D5oiQtMIa7IocNsJKSBrbiXZHxLSAtT4GVWFdkbAV4Kq+A+VNdkafmgT+kDMxpXZHaHFAWHcj2KMmW9g493JMBdDGVRWkjWx4OpAHkg/qZVtsR8a09fuIeoMvf6nmu1I+C5maz6r0/AmXZUK/t59/cmm3pu5HA8bX1qKdKdj09PKRsRn8CaS/fqmT71mOvqGTvFY/9qZC2CazGvHqCQs2e4NGH3NX33O5Ddu9LBXyCR3qffA1YyxGf5EG/vSoiIp9tANVJn+TkkR4vSR34J+ELTJQBPfW/z7L/fn7aZ7aZ6XhmZ0zAuO7MCb90yAk/O3PCYTY55wGec8smIoPFgzw0eVqRhyKT7nnoMINVlxUZ7NcqwH5+0CX35Zq5b6K73CcSvLZrZ8036eH4QEgLyIne0EB8eLaZNXevB1X5dqGZb42t4sJcJv3DTwvFLaOZbxc8Wkd4bEOZqV95ZWoR6Zted83x69Odd3D/aKbw7ti3w7tCZrTfX6OJJsZuLm2UDXNv++XSTcX3yr9q9buPteVJggAAAABJRU5ErkJggg=="},"4c2b":function(s,t,M){s.exports=M.p+"img/bg-order-detail.02befba6.png"},"60fb":function(s,t,M){"use strict";var e=M("9401"),a=M.n(e);a.a},"669d":function(s,t){s.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJkAAABDCAIAAAAAmzCkAAAA/UlEQVR4Ae3TwREAMAiEwCT9t+vfSRs72AHg3Zk5HWHgERRBfAO1dP6glrV0DDgk7bKWjgGHpF3W0jHgkLTLWjoGHJJ2WUvHgEPSLmvpGHBI2mUtHQMOSbuspWPAIWmXtXQMOCTtspaOAYekXdbSMeCQtMtaOgYcknZZS8eAQ9Iua+kYcEjaZS0dAw5Ju6ylY8AhaZe1dAw4JO2ylo4Bh6Rd1tIx4JC0y1o6BhySdllLx4BD0i5r6RhwSNplLR0DDkm7rKVjwCFpl7V0DDgk7bKWjgGHpF3W0jHgkLTLWjoGHJJ2WUvHgEPSLmvpGHBI2mUtHQMOSbuspWPAIVmyBQNliMD++QAAAABJRU5ErkJggg=="},"75bb":function(s,t){s.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAACK1BMVEUAAAAAAAAAAACAgIAAAABVVVVAQEAzMzMrKytAQEA5OTkzMzMuLi4rKysnJyc7Ozs3NzczMzM2NjYzMzMxMTEuLi46Ojo3Nzc1NTUzMzMxMTEvLy8uLi41NTUzMzMwMDA4ODg1NTUyMjIwMDAzMzMyMjIxMTE1NTU0NDQzMzM2NjY1NTU0NDQzMzMyMjIxMTE1NTU0NDQzMzMxMTEzMzMyMjIxMTExMTEzMzMyMjI1NTU0NDQzMzMyMjIyMjIxMTE0NDQyMjI1NTU0NDQzMzMyMjI0NDQzMzM0NDQ0NDQ0NDQzMzMzMzM1NTUyMjI0NDQyMjIzMzMzMzM0NDQzMzMzMzM0NDQzMzMzMzMzMzMzMzMzMzMzMzMyMjIyMjIzMzMzMzM0NDQ0NDQzMzMzMzMzMzM0NDQ0NDQzMzMzMzMyMjIyMjIzMzM0NDQyMjIzMzMyMjIzMzMzMzMyMjIyMjIyMjIzMzM0NDQzMzMzMzMyMjI0NDQ0NDQzMzMzMzMzMzMzMzMyMjI0NDQzMzMzMzMzMzM0NDQzMzMzMzMzMzMzMzM0NDQzMzMzMzMzMzM0NDQzMzMzMzMzMzMzMzMzMzMzMzMzMzM0NDQzMzMzMzMzMzMzMzMyMjIzMzMzMzMzMzM0NDQzMzMzMzMzMzMzMzMzMzMzMzMzMzMyMjIzMzMzMzMzMzMzMzMzMzMyMjIzMzMzMzMzMzMzMzMzMzM0NDQyMjIzMzMzMzMzMzMCb2u1AAAAuHRSTlMAAQICAwMEBQYICQoLDA0NDg8TFBUWFhcYGRobHB0eICAiJCUoKSorLC0vMDEyMzQ1Njc5PD0+P0FCREVGR0xOT1JSVFVWXl9iY2doamprbHBzdHZ9fn+Bg4eIi42Oj5CSk5SVlpucnp+goqOkp6iqra+wsbKzs7e4ubu7vL2+wsTFxsfIycvMzc7Pz9DR1NTV2Nna29zd3t/h4uPk5ebo6Orr7O7v8PHz8/T19vf4+Pn6+/z8/f3+LKbjJgAAAitJREFUOMvtlVdTU1EUhdeNkgIqpKCQYMEuYlBRLLFXFGwoRo0VexQbShNjCZYAdlCKRDAQIUESEr13/Twf0nAmN/LiDA+up7PO/ubM2bP3PgeYDFKVNPopK39jiTpGClv6mFLfdiuiqPa2JL6puSenlyE26KOo2ct3RvnrpT9gYF10bScrUqVSOMIaAQCwdJS9M1KhwlWOFQGA0k7xCJC2zJJMGxcrgIIBVisBmHv5IR8o/J48e08+kHlD6jUDSmtYOqsGlkvJ0UAeIOwYDFWmYbaL/esBoLjsQBLtLwCA6U4256I0zFrNX+u5TQruQhNpmUDpm9mEIQ5OmUCbVNAPkV2JjbxVU8eFdXmK+LqUXnQzpIv57Fr3sQSZ0/Z2ddyc4StcIM+lR4piOE8O7MmIxBT5DvL9kmj7zfwiWbGih8OnFgrAvH0Ojv6i5/JmLaCYf+h5+GeAXdYiFaBaU8v2RVAdHWGw23n/aaePP06UDVHyvLZXPewcYbC8KsQx94srFx9/FX3lSkBd2SeSpOR1bRWw4G5/1D0phvpgu08iybD7cCTDtbbG1pb609u1AJCx4fitR447tp16ADDtvdTU1tpwcmUsPbXeZNLF50eRZTBolTGn+SMGmDYZBdluNVpyE05TL97MkkMzr4t10+JuVgddssOV84yf5yRcB1tMsqiTnXP/o/8YNXykt17uzawb4KfEOWnXUj/F1apxs2nrGZaVuyp7UnxD+A00LlU0h/PiQAAAAABJRU5ErkJggg=="},9401:function(s,t,M){},d3e3:function(s,t,M){"use strict";M.r(t);var e=function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("div",{staticClass:"orderDetails"},[e("van-nav-bar",{attrs:{title:s.title,"left-arrow":"",fixed:""},on:{"click-left":s.goBack}}),s.show?e("div",{staticClass:"order-status"},[e("img",{staticClass:"bg-img",attrs:{src:M("4c2b")}}),e("div",{staticClass:"box"},[e("div",{staticClass:"status-name"},[e("img",{attrs:{src:M("3438")}}),s._v(s._s(s.order.status_name))]),0==s.order.status?e("div",{staticClass:"status-info"},[s._v("需支付：￥"),e("strong",[s._v(s._s(s.order.pay_amount))]),s._v(" 剩余："),e("strong",[s._v("01:58:30")])]):s._e()])]):s._e(),s.show?e("div",{staticClass:"group"},[e("div",{staticClass:"supply-shop"},[e("div",{staticClass:"supply-shop-name"},[e("div",{staticClass:"name"},[e("img",{attrs:{src:M("75bb")}}),s._v(s._s(s.order.supply_name))])]),s._l(s.order.goods_list,(function(t,a){return e("div",{key:a,staticClass:"item"},[e("div",{staticClass:"good-item"},[e("div",{staticClass:"good-img"},[e("van-image",{attrs:{src:t.img_url,fit:"cover"},scopedSlots:s._u([{key:"error",fn:function(){return[e("img",{attrs:{src:M("669d")}})]},proxy:!0},{key:"loading",fn:function(){return[e("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)})],1),e("div",{staticClass:"good-info"},[e("div",{staticClass:"good-title"},[s._v(" "+s._s(t.name)+" ")]),e("div",{staticClass:"spec-name-num"},[e("span",{staticClass:"spec-name"},s._l(t.spec_names,(function(t,M){return e("span",{key:M},[s._v(" "+s._s(t.item)),e("span",[s._v("，")])])})),0),e("span",[s._v("x"+s._s(t.num))])]),e("div",{staticClass:"earnings-price"},["MAIDOU"==s.order.pay_type?e("span",[s._v(s._s(s.order.maidou_money)+"迈豆")]):e("span",[s._v("￥"+s._s(t.price))])])])])])})),e("div",{staticClass:"goods-total"},[e("div",{staticClass:"box"},[e("span",[s._v("共"+s._s(s.order.goods_num)+"件商品")]),"MAIDOU"==s.order.pay_type?e("span",[s._v(s._s(s.order.maidou_money)+"迈豆")]):e("span",[s._v("￥"+s._s(s.order.goods_money))])]),e("div",{staticClass:"box"},[e("span",[s._v("运费")]),e("span",[s._v("￥"+s._s(s.order.shipping_fee))])]),e("div",{staticClass:"box"},[e("span",[s._v("实付金额")]),"MAIDOU"==s.order.pay_type?e("span",{staticClass:"actually-pay"},[s._v(s._s(s.order.maidou_money)+"迈豆")]):e("span",{staticClass:"actually-pay"},[s._v(s._s(s.order.pay_amount))])])])],2),e("div",{staticClass:"order-info"},s._l(s.order.progress_order_msg,(function(t,M){return e("div",{key:M,staticClass:"box"},[e("div",{staticClass:"title"},[s._v(s._s(t.key)+"：")]),e("div",{staticClass:"info"},[s._v(" "+s._s(t.value)+" "),"买家"==t.key||"订单号"==t.key?e("span",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:t.value,expression:"item.value",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:s.onCopied,expression:"onCopied",arg:"success"}],staticClass:"copy"},[s._v(" 复制")]):s._e(),"物流信息"==t.key&&s.order.status>=3?e("span",{staticClass:"copy",on:{click:s.gotologistics}},[s._v(" 查看")]):s._e()])])})),0)]):s._e()],1)},a=[],o=(M("d3b7"),M("e7e5"),M("d399")),i=M("5530"),z=M("c391"),r=M("ce3a"),n=M("2f62"),A={name:"orderDetails",components:{},data:function(){return{order_id:null,goods_id:null,type:null,order:{},orderStatus:[],show:!1}},computed:Object(i["a"])({},Object(n["b"])(["token"])),methods:{getData:function(){var s=this,t={token:this.token,order_id:this.order_id,goods_id:this.goods_id,type:this.type};this.$axios.post(Object(z["a"])(r["a"].shopkeeperOrderInfo),t).then((function(t){var M=t.data;0!=M.code?Object(o["a"])(M.message):(console.log(M),s.order=M.data.order,s.show=!0)})).catch((function(s){Object(o["a"])(s.message),console.log(s.message)})).finally((function(){}))},gotologistics:function(){this.$router.push("/shopkeeper/logistics_details?order_id="+this.order.order_id)},onCopied:function(s){Object(o["a"])("复制成功")},goBack:function(){this.$router.go(-1)}},created:function(){this.title=this.$route.meta.title,this.order_id=this.$route.query.order_id,this.goods_id=this.$route.query.goods_id,this.type=this.$route.query.type,this.getData()}},d=A,c=(M("60fb"),M("2877")),p=Object(c["a"])(d,e,a,!1,null,"50304012",null);t["default"]=p.exports},e7e5:function(s,t,M){"use strict";M("68ef"),M("a71a"),M("9d70"),M("3743"),M("4d75"),M("e3b3"),M("b258")}}]);