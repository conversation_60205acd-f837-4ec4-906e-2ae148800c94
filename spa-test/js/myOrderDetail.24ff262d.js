(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["myOrderDetail"],{"1a11":function(a,t){a.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAmVBMVEUAAAD/MDD/MzP/MjL/LS3/Jyf/MjL/MjL/MjL/MjL/MTH/MjL/MjL/MjL/MjL/MjL/MjL/MTH/MjL/MTH/MjL/MjL/MjL/MTH/MTH/MDD/Ly//MDD/MzP/MjL/MTH/MjL/MTH/MDD/MzP/HBz/MzP/MjL/MjL/MTH/MjL/MjL/MDD/LCz/KCj/MjL/MzP/MTH/MjL/MjL/MzNMz+gqAAAAMnRSTlMAJfzrEAb25ty+afnw08e1blSnoY2IZ05INzAg45l7dGEsCgnYzZ+Tf3g8GAytq4FbUTmVVkgAAAF3SURBVEjHxdTJloIwEAXQJ4OICqKiIs5zO/SU//+4Pr0QXkFIXPQ5fZfPFIZKBfyDfZYOjq7TXsajbgtW81FfkWizh0lroKrcrmH9uqc04qaNeaHS68+gs4hUE0e3rUekDIaoGSiTdo6KqzLrPCr97CmL1L4hyRHNzWudDGp/eQYZKxZtPQC3sSPSnkdH1lbkzX/OVaBYUhYMOZ/SWYqKEIULxSsxu+Is/CKPKb2DvXPFrojdMhxByLjgu4iphZUxO9B6+s0wZS63r0ipq1tI3PB1kVL3PiHctQfBXTpB2HJBefHOlGYgfocLyvHr8uB7VDDh9S7dBs7DsiKhWB7RSZHlkD9S+o5PK/cxTabr0JFh4PPLLZXVBuzLur7jQwhtV/oGaeGaCyaoyhzT+pA3xJ1i+tMUx9okWEBrrPTcHYi9op+j0Ur3/BwGaf19WzBKlBR7sJj1ef2HD6tWVM7DFa84PF/9OMeLZsHv4ycHvGy/CS87/I0fN1/PaNUXSR8AAAAASUVORK5CYII="},"2a97":function(a,t,s){},3438:function(a,t){a.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA5CAMAAAC7xnO3AAABklBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ZVs66AAAAhXRSTlMAAQIDBAUGBwgJCwwNDg8QExQVFhsdHh8gISMkJTQ4OT1GSElKS0xNTlFTVFtcXWJjbW5vc3Z4e3x+f4CBgoOEhYaHiIyNj5CRkpaXmJqhoqOkpaanqaquubq7vL2+v8nKy8zNzs/Q0djZ2tvc3eHj5OXm5+jp7O3x8vP09vf4+fr7/P3+dii+4QAAA0JJREFUSMell21D01YYhp9ig8zS2jKsWh0vQstEHEO36aTaOi3uRUELwyKVrSiOoWMKq1VMQ9vk+t/7QEqhyUnT7f7Wk1xNznnu85w7Ik5p0cSlG4svtj/WPm6/WLwxlohq4keh0TuFtyYtmW8Ld0ZDHbmTU6X3DdrVeF+aOunJhS+VUKk0FlaDQ/Of7Ntqr4uPH2Rnsw8eF1/X7LFP80MKLjizY0/vr9sjZwdCWo/0aKGBsyO339gT3rkWdAOj9w0Aq7L2ZW/bpd6JtYoFYOSiTnDwyT6AvpTqc/nbvtSiDrD/ZNABrpoAu5fDAdepBMKXdwHM1TY0mjeBeuG8x8KfL9QBM3/shYO5GmA+PONZszMPTaCWO7pMMwZgZTsZJZS1AGOmNYehHaD+qL+jw/of1YGdoeZaRPImUIz7MHW8CJj5iP1zXAfKF3zthwtlQE/atXoJVK847/ri9w85h1evVIHSgf2nAWv5tJP8Dfa+ah+MLFvAlIhIeB2oTLgYoAHW9w5LpCrAelhEkhVgzc1yALNOI64BlaSIdrcBjItvUsaBxl1NYs+ATa0LUtsEnsXkYtn9upqUW0D5oiQtMIa7IocNsJKSBrbiXZHxLSAtT4GVWFdkbAV4Kq+A+VNdkafmgT+kDMxpXZHaHFAWHcj2KMmW9g493JMBdDGVRWkjWx4OpAHkg/qZVtsR8a09fuIeoMvf6nmu1I+C5maz6r0/AmXZUK/t59/cmm3pu5HA8bX1qKdKdj09PKRsRn8CaS/fqmT71mOvqGTvFY/9qZC2CazGvHqCQs2e4NGH3NX33O5Ddu9LBXyCR3qffA1YyxGf5EG/vSoiIp9tANVJn+TkkR4vSR34J+ELTJQBPfW/z7L/fn7aZ7aZ6XhmZ0zAuO7MCb90yAk/O3PCYTY55wGec8smIoPFgzw0eVqRhyKT7nnoMINVlxUZ7NcqwH5+0CX35Zq5b6K73CcSvLZrZ8036eH4QEgLyIne0EB8eLaZNXevB1X5dqGZb42t4sJcJv3DTwvFLaOZbxc8Wkd4bEOZqV95ZWoR6Zted83x69Odd3D/aKbw7ti3w7tCZrTfX6OJJsZuLm2UDXNv++XSTcX3yr9q9buPteVJggAAAABJRU5ErkJggg=="},da64:function(a,t,s){"use strict";var e=s("2a97"),i=s.n(e);i.a},e193:function(a,t,s){"use strict";s.r(t);var e=function(){var a=this,t=a.$createElement,e=a._self._c||t;return a.isShow?e("div",{staticClass:"main"},[e("van-nav-bar",{attrs:{title:a.title,"left-arrow":"",fixed:""},on:{"click-left":a.goBack}}),e("div",{staticClass:"header"},[e("div",{staticClass:"header-one"},[e("img",{attrs:{src:s("3438"),alt:""}}),e("span",[a._v(a._s(a.orderData.order_status_name))])]),1==a.orderData.order_status?e("div",{staticClass:"header-show"},[a._v("订单已支付，请等待发货")]):a._e(),3==a.orderData.order_status?e("div",{staticClass:"header-show"},[a._v("订单已发货，请等待收货")]):a._e(),0==a.orderData.order_status?e("div",{staticClass:"header-two"},[e("div",{staticClass:"header-two-price"},[a._v("需支付："),e("p",{staticClass:"header-two-price-info"},[e("span",[a._v("￥")]),a._v(a._s(a.orderData.pay_money))])]),e("div",{staticClass:"header-two-time"},[e("span",[a._v("剩余：")]),e("van-count-down",{attrs:{time:1e3*a.orderData.reset_pay_time}})],1)]):a._e()]),e("div",{staticClass:"info"},[e("div",{staticClass:"info-address"},[e("img",{staticClass:"info-address-icon",attrs:{src:s("1a11"),alt:""}}),e("div",{staticClass:"info-address-right"},[e("div",{staticClass:"info-address-one"},[e("span",{staticClass:"info-address-one-name"},[a._v(a._s(a.orderData.name))]),e("span",{staticClass:"info-address-one-phone"},[a._v(a._s(a.orderData.mobile||a.orderData.telephone))])]),e("p",{staticClass:"info-address-two"},[a._v(a._s(a.orderData.full_address)+" "+a._s(a.orderData.address))])])]),e("div",{staticClass:"info-sum"},[e("p",{staticClass:"info-sum-title"},[a._v(a._s(a.orderData.order_no))]),e("div",{staticClass:"info-sum-two"},a._l(a.orderData.order_goods,(function(t,s){return e("div",{key:s,staticClass:"info-sum-product"},[e("van-image",{staticClass:"info-sum-product-left",attrs:{src:t.sku_image},scopedSlots:a._u([{key:"loading",fn:function(){return[e("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),e("div",{staticClass:"info-sum-product-right"},[e("p",{staticClass:"info-sum-product-right-name"},[a._v(a._s(t.sku_name))]),e("div",{staticClass:"info-sum-product-right-desc"},[e("span",[a._v(a._s(t.spec_name))]),e("span",[a._v("x "+a._s(t.num))])]),1!=a.orderData.is_maidou_pay?e("p",{staticClass:"info-sum-product-right-price"},[a._v("￥"+a._s(a.orderData.periodInfo?a.orderData.periodInfo.buy_price:t.price))]):a._e(),1==a.orderData.is_maidou_pay?e("p",{staticClass:"info-sum-product-right-price"},[a._v(a._s(t.price)+"迈豆")]):a._e()])],1)})),0),e("div",[e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("共"+a._s(a.orderData.goods_num)+"件商品")]),1==a.orderData.is_maidou_pay?e("div",{staticClass:"info-sum-cell-right"},[a._v(a._s(a.orderData.goods_money)+" 迈豆")]):a._e(),a.orderData.periodInfo||1==a.orderData.is_maidou_pay?a._e():e("div",{staticClass:"info-sum-cell-right"},[a._v("￥"+a._s(a.orderData.goods_money))]),a.orderData.periodInfo?e("div",{staticClass:"info-sum-cell-right"},[a._v("￥"+a._s(a.orderData.periodInfo.total_price))]):a._e()]),e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("运费")]),e("div",{staticClass:"info-sum-cell-right"},[a._v("￥"+a._s(a.orderData.delivery_money))])]),5==a.orderData.order_create_type?e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("优惠")]),e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("砍价优惠 ￥"+a._s(a.orderData.bargain_promotion))])]):a._e(),a.orderData.goodscoupon_id?e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("优惠券")]),e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("-￥"+a._s(a.orderData.goodscoupon_money))])]):a._e(),a.orderData.promotion_money&&parseFloat(a.orderData.promotion_money)?e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("分销商优惠")]),e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("-￥"+a._s(a.orderData.promotion_money))])]):a._e(),parseFloat(a.orderData.multiple_discount_money)>0?e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("多件折扣")]),e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("-￥"+a._s(a.orderData.multiple_discount_money))])]):a._e(),e("div",{staticClass:"info-sum-cell"},[e("div",{staticClass:"info-sum-cell-left"},[a._v("实付金额")]),1==a.orderData.is_maidou_pay?e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("￥"+a._s(a.orderData.pay_money)+"迈豆")]):a._e(),1!=a.orderData.is_maidou_pay?e("div",{staticClass:"info-sum-cell-right info-sum-cell-right-red"},[a._v("￥"+a._s(a.orderData.periodInfo?a.orderData.periodInfo.total_price:"BALANCE"==a.orderData.pay_type?a.orderData.balance_money:a.orderData.pay_money))]):a._e()])])]),e("div",{staticClass:"info-desc"},[e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("订单号：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.order_no))])]),e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("交易号：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.out_trade_no))])]),e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("下单时间：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.create_time)))])]),e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("下单时间：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.create_time)))])]),a.orderData.pay_status>0?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("支付时间：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.pay_time)))])]):a._e(),a.orderData.app_type_name?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("支付方式：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.app_type_name))])]):a._e(),a.orderData.delivery_time>0?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("发货时间：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.delivery_time)))])]):a._e(),a.orderData.package_list.length>1?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("物流信息：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.package_list.length)+"个包裹")])]):a._e(),1==a.orderData.package_list.length&&a.orderData.package_list[0].express_company_name?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("快递类型：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.package_list.length>0&&a.orderData.package_list[0].express_company_name))])]):a._e(),1==a.orderData.package_list.length&&a.orderData.package_list[0].delivery_no?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("快递单号：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.package_list.length>0&&a.orderData.package_list[0].delivery_no))])]):a._e(),a.orderData.sign_time>0?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("确认收货：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.sign_time)))])]):a._e(),a.orderData.finish_time>0?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("成交时间：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.timeStampTurnTime(a.orderData.finish_time)))])]):a._e(),e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("订单备注：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.buyer_message))])]),a.orderData.remark?e("div",{staticClass:"info-desc-cell"},[e("div",{staticClass:"info-desc-cell-left"},[a._v("商家备注：")]),e("div",{staticClass:"info-desc-cell-right"},[a._v(a._s(a.orderData.remark))])]):a._e()])])],1):a._e()},i=[],r=(s("d3b7"),s("e7e5"),s("d399")),o=s("5530"),d=s("c391"),l=s("ce3a"),c=s("2f62"),n={name:"myOrderDetail",data:function(){return{title:"",isShow:!1,order_id:"",sku_image:"",orderData:{}}},computed:Object(o["a"])({},Object(c["b"])(["miniToken","shop_id"])),created:function(){this.order_id=this.$route.query.order_id?this.$route.query.order_id:"",this.title=this.$route.meta.title,this.getData()},methods:{goBack:function(){this.$router.go(-1)},countDown:function(a){var t=0,s=0,e=0,i=0;return a>0&&(t=Math.floor(a/86400),s=Math.floor(a/3600)-24*t,e=Math.floor(a/60)-24*t*60-60*s,i=Math.floor(a)-24*t*60*60-60*s*60-60*e),{d:t,h:s,i:e,s:i}},timeStampTurnTime:function(a){if(void 0!=a&&""!=a&&a>0){var t=new Date;t.setTime(1e3*a);var s=t.getFullYear(),e=t.getMonth()+1;e=e<10?"0"+e:e;var i=t.getDate();i=i<10?"0"+i:i;var r=t.getHours();r=r<10?"0"+r:r;var o=t.getMinutes(),d=t.getSeconds();return o=o<10?"0"+o:o,d=d<10?"0"+d:d,s+"-"+e+"-"+i+" "+r+":"+o+":"+d}return""},getData:function(){var a=this,t=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:this.miniToken,shop_id:this.shop_id,order_id:this.order_id};this.$axios.post(Object(d["a"])(l["a"].orderDetailUrl),s).then((function(s){var e=s.data;0!=e.code?Object(r["a"])(e.message):(a.orderData=e.data,a.orderData.reset_pay_time&&(a.orderData.discountTimeMachine=a.countDown(a.orderData.reset_pay_time)),t.clear(),a.isShow=!0)})).catch((function(a){Object(r["a"])(a.message)})).finally((function(){}))}}},_=n,v=(s("da64"),s("2877")),f=Object(v["a"])(_,e,i,!1,null,"4b52b872",null);t["default"]=f.exports},e7e5:function(a,t,s){"use strict";s("68ef"),s("a71a"),s("9d70"),s("3743"),s("4d75"),s("e3b3"),s("b258")}}]);