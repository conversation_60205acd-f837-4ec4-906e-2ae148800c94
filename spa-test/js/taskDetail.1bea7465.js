(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["taskDetail"],{1811:function(t,a,e){t.exports=e.p+"img/4.50d7d689.png"},1969:function(t,a,e){},"19d8":function(t,a,e){"use strict";var s=e("1969"),i=e.n(s);i.a},"260a":function(t,a,e){t.exports=e.p+"img/1.b3e1cf73.png"},"3c80":function(t,a,e){t.exports=e.p+"img/5.350bbf1f.png"},"446e":function(t,a,e){t.exports=e.p+"img/8.530c2fae.png"},"7a18":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return t.loaded?e("div",{staticClass:"detail"},[e("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),e("div",{staticClass:"detail-bg"}),e("div",{staticClass:"detail-info"},[e("p",{staticClass:"detail-info-title"},[t._v(t._s(t.task_id?"待领取":t.taskData.status_text))]),e("div",{staticClass:"detail-info-card"},[e("p",{staticClass:"detail-info-card-title"},[t._v(t._s(t.taskData.title))]),e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("任务领取: ")]),e("p",{staticClass:"detail-info-card-row-right"},[t._v(t._s(t.taskData.receive_end_time)+"截止")])]),e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("任务时限: ")]),t.task_id?e("p",{staticClass:"detail-info-card-row-right"},[t._v("领取后"+t._s(t.taskData.time_limit)+"天内完成并提交")]):[1==t.taskData.status?e("p",{staticClass:"detail-info-card-row-right color-red"},[t._v("请在"+t._s(t.taskData.finish_time_out)+"前提交任务")]):-2==t.taskData.status?e("p",{staticClass:"detail-info-card-row-right color-red"},[t._v("请在"+t._s(t.taskData.overrule_submit_limit)+"前提交任务")]):2==t.taskData.status||9==t.taskData.status||10==t.taskData.status?e("p",{staticClass:"detail-info-card-row-right"},[t._v("领取后"+t._s(t.taskData.time_limit)+"天内完成并提交")]):t._e()]],2),e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("任务说明: ")]),e("p",{staticClass:"detail-info-card-row-right"},[t._v("按要求上传文字及视频素材到微信视频号，当该条视频数据达标后，提交任务到平台审核，审核通过后，可获得奖励"),e("span",[t._v(t._s(t.task_id?t.taskData.estimate_profit:t.taskData.reward)+"元")])])]),e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("达标要求: ")]),e("div",{staticClass:"detail-info-card-row-request"},[t.taskData.target_info.video_admire_nums?e("span",{staticClass:"detail-info-card-row-request-top"},[t._v("【点赞数 "+t._s(t.taskData.target_info.video_admire_nums)+"】")]):t._e(),t.taskData.target_info.video_commit_nums?e("span",{staticClass:"detail-info-card-row-request-top"},[t._v("【评论数 "+t._s(t.taskData.target_info.video_commit_nums)+"】")]):t._e(),t.taskData.target_info.video_send_nums?e("span",{staticClass:"detail-info-card-row-request-top"},[t._v("【转发数 "+t._s(t.taskData.target_info.video_send_nums)+"】")]):t._e(),t.taskData.target_info.video_collect_nums?e("span",{staticClass:"detail-info-card-row-request-top"},[t._v("【收藏数 "+t._s(t.taskData.target_info.video_collect_nums)+"】")]):t._e()])]),!t.user_task_id||-2!=t.taskData.status&&-1!=t.taskData.status?t._e():e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("驳回理由: ")]),e("p",{staticClass:"detail-info-card-row-right",class:-1==t.taskData.status?"color-red":""},[t._v(t._s(t.taskData.fail_reason))])]),!t.user_task_id||9!=t.taskData.status&&10!=t.taskData.status?t._e():e("div",{staticClass:"detail-info-card-row"},[e("span",{staticClass:"detail-info-card-row-left"},[t._v("奖励发放: ")]),e("p",{staticClass:"detail-info-card-row-right color-red"},[t._v(t._s(t.taskData.reward)+"元"),e("span",{staticStyle:{"margin-left":"0.3125rem"}},[t._v(t._s(t.taskData.reward_send_time))]),t._v(t._s(t.taskData.reward_is_send?"已发放":"未发放"))])]),e("div",{staticClass:"detail-info-card-op"},[t.task_id?e("span",{on:{click:t.toSubmit}},[t._v("领取任务")]):[1==t.taskData.status?e("span",{on:{click:t.toSubmit}},[t._v("提交任务")]):2==t.taskData.status?e("span",{staticClass:"color-gray"},[t._v("待审核")]):-2==t.taskData.status?e("span",{on:{click:t.toSubmit}},[t._v("重新提交")]):t._e()]],2)]),t.user_task_id?e("div",{staticClass:"detail-info-material"},[e("p",{staticClass:"detail-info-material-title"},[t._v("任务素材")]),e("div",{staticClass:"detail-info-material-list"},[e("div",{staticClass:"detail-info-material-list-one"},[e("span",{staticClass:"detail-info-material-list-one-left"},[t._v("描述文案:")]),e("div",{staticClass:"detail-info-material-list-one-right"},[e("p",{staticClass:"detail-info-material-list-one-right-desc"},[t._v(t._s(t.taskData.describe))]),e("p",{staticClass:"detail-info-material-list-one-right-copy",on:{click:function(a){return t.copyDesc(t.taskData.describe)}}},[t._v("【点击复制文案】")])])]),e("div",{staticClass:"detail-info-material-list-one"},[e("span",{staticClass:"detail-info-material-list-one-left"},[t._v("视频素材:")]),t._l(t.taskData.materials,(function(a,s){return e("div",{key:s,staticClass:"detail-info-material-list-one-right"},[e("video",{staticClass:"detail-info-material-list-one-right-video",attrs:{src:a,controls:""}}),e("p",{staticClass:"detail-info-material-list-one-right-copy",on:{click:function(e){return t.saveVideo(a)}}},[t._v("【点击下载视频素材】")])])}))],2)])]):t._e(),t._m(0)])],1):t._e()},i=[function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"detail-info-guide"},[s("p",{staticClass:"detail-info-guide-title"},[t._v("任务指引")]),s("div",{staticClass:"detail-info-guide-step"},[s("div",{staticClass:"detail-info-guide-step-one"},[s("p",{staticClass:"detail-info-guide-step-one-desc"},[t._v("第一步：先下载好素材内容，打开微信，点击底部菜单【我】，再点击打开【视频号】进入主页")])]),s("div",{staticClass:"detail-info-guide-step-one"},[s("p",{staticClass:"detail-info-guide-step-one-desc"},[t._v("第二步：点击主页中右上角的相机图标（如图指标），然后选择【发表视频】菜单")]),s("div",{staticClass:"detail-info-guide-step-one-imgs"},[s("img",{attrs:{src:e("260a"),alt:""}}),s("img",{attrs:{src:e("c212"),alt:""}})])]),s("div",{staticClass:"detail-info-guide-step-one"},[s("p",{staticClass:"detail-info-guide-step-one-desc"},[t._v("第三步：点击【从相册中选择】菜单，然后在相册中找到刚下载好的视频素材，选中并点击下一步")]),s("div",{staticClass:"detail-info-guide-step-one-imgs"},[s("img",{attrs:{src:e("8dd1"),alt:""}}),s("img",{attrs:{src:e("1811"),alt:""}})])]),s("div",{staticClass:"detail-info-guide-step-one"},[s("p",{staticClass:"detail-info-guide-step-one-desc"},[t._v("第四步：点击【完成】，然后将描述文案粘贴到指定位置，点击发表即可。")]),s("div",{staticClass:"detail-info-guide-step-one-imgs"},[s("img",{attrs:{src:e("3c80"),alt:""}}),s("img",{attrs:{src:e("aea0"),alt:""}})])]),s("div",{staticClass:"detail-info-guide-step-one"},[s("p",{staticClass:"detail-info-guide-step-one-desc"},[t._v("第五步：在视频号主页，点击任务视频，可查看任务视频推广数据，当数据达标后，复制你的视频号昵称，截取包含数据的视频详情页图提交任务即可。")]),s("div",{staticClass:"detail-info-guide-step-one-imgs"},[s("img",{attrs:{src:e("8dd6"),alt:""}}),s("img",{attrs:{src:e("446e"),alt:""}})])])])])}],r=(e("ac1f"),e("5319"),e("e17f"),e("2241")),n=(e("e7e5"),e("d399")),o=e("5530"),c=(e("96cf"),e("1da1")),d=e("2f62"),l=e("6917"),u=e("bba6"),f=e("c391"),_=e("ce3a"),p={name:"taskDetail",data:function(){return{loaded:!1,title:"",task_id:null,user_task_id:null,taskData:{target_info:{}}}},created:function(){this.title=this.$route.meta.title,this.task_id=this.$route.query.task_id,this.user_task_id=this.$route.query.user_task_id},mounted:function(){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.getData();case 2:t.loaded=!0;case 3:case"end":return a.stop()}}),a)})))()},computed:Object(o["a"])({},Object(d["b"])(["token"])),methods:{goBack:function(){this.$router.back()},copyDesc:function(t){Object(l["d"])(t)},saveVideo:function(t){u["a"]?this.$router.push({name:"downloadVideoTutorial",query:{url:t}}):(n["a"].loading({duration:0,message:"下载中...",forbidClick:!0,overlay:!0}),setTimeout((function(){Object(l["f"])(t,(function(){n["a"].clear()}))}),500))},toSubmit:function(){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.task_id){a.next=4;break}r["a"].confirm({title:"提示",message:"确认领取当前任务？"}).then(Object(c["a"])(regeneratorRuntime.mark((function a(){var e,s,i,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:t.token,task_id:t.task_id},a.prev=2,a.next=5,t.$axios.post(Object(f["a"])(_["a"].xmTaskReceiveUrl),s);case 5:if(i=a.sent,e.clear(),r=i.data,0==r.code){a.next=13;break}return Object(n["a"])(r.message),a.abrupt("return");case 13:Object(n["a"])({message:r.message,onClose:function(){var a=Object(c["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$router.replace({name:"taskDetail",query:{user_task_id:r.data.user_task_id}});case 2:return a.next=4,t.$router.go(0);case 4:case"end":return a.stop()}}),a)})));function e(){return a.apply(this,arguments)}return e}()});case 14:a.next=20;break;case 16:a.prev=16,a.t0=a["catch"](2),Object(n["a"])(a.t0.message),e.clear();case 20:case"end":return a.stop()}}),a,null,[[2,16]])})))).catch((function(){})),a.next=6;break;case 4:return a.next=6,t.$router.replace({name:"submitTask",query:{user_task_id:t.user_task_id}});case 6:case"end":return a.stop()}}),a)})))()},getData:function(){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var e,s,i,r,o;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:t.token},i="",t.task_id?(s["task_id"]=t.task_id,i=_["a"].xmTaskDetailUrl):(s["user_task_id"]=t.user_task_id,i=_["a"].xmUserTaskDetailUrl),a.prev=4,a.next=7,t.$axios.post(Object(f["a"])(i),s);case 7:if(r=a.sent,e.clear(),o=r.data,0==o.code){a.next=13;break}return Object(n["a"])(o.message),a.abrupt("return");case 13:t.taskData=o.data,a.next=20;break;case 16:a.prev=16,a.t0=a["catch"](4),Object(n["a"])(a.t0.message),e.clear();case 20:case"end":return a.stop()}}),a,null,[[4,16]])})))()}}},v=p,m=(e("19d8"),e("2877")),g=Object(m["a"])(v,s,i,!1,null,"6f600534",null);a["default"]=g.exports},"8dd1":function(t,a,e){t.exports=e.p+"img/3.6f71349d.png"},"8dd6":function(t,a,e){t.exports=e.p+"img/7.194fd120.png"},aea0:function(t,a,e){t.exports=e.p+"img/6.261c1e63.png"},bba6:function(t,a,e){"use strict";e.d(a,"a",(function(){return r}));e("c975"),e("b64b"),e("ac1f"),e("466d"),e("5319");var s=navigator.userAgent||window.navigator.userAgent,i=s.toLowerCase(),r=(i.match(/MicroMessenger/i),i.indexOf(" qq"),/(iPhone|iPad|iPod|iOS)/i.test(i));n().isIOS9,/(android|nexus)/i.test(i),/youpin/i.test(i)||/xianmai/i.test(i);function n(){var t,a,e,r;if((s.indexOf("Android")>-1||s.indexOf("Linux")>-1)&&(t=!0),i.indexOf("like mac os x")>0){var n=/os [\d._]*/gi,o=i.match(n);r=(o+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".")}var c=r+"";return"undefined"!=c&&c.length>0&&(r=parseInt(r),r>=8?e=!0:a=!0),{isAndroid:t,isIOS:a,isIOS9:e}}},c212:function(t,a,e){t.exports=e.p+"img/2.cffeb8b5.png"},e7e5:function(t,a,e){"use strict";e("68ef"),e("a71a"),e("9d70"),e("3743"),e("4d75"),e("e3b3"),e("b258")}}]);