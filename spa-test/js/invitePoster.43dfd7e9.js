(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["invitePoster"],{"0196":function(t,e,r){var n=r("5a88"),o=r("bbf0");function i(t){this.mode=o.BYTE,this.data=n.from(t)}i.getBitsLength=function(t){return 8*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){for(var e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=i},"0425":function(t,e){var r="[0-9]+",n="[A-Z $%*+\\-./:]+",o="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";o=o.replace(/u/g,"\\u");var i="(?:(?![A-Z0-9 $%*+\\-./:]|"+o+")(?:.|[\r\n]))+";e.KANJI=new RegExp(o,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(i,"g"),e.NUMERIC=new RegExp(r,"g"),e.ALPHANUMERIC=new RegExp(n,"g");var a=new RegExp("^"+o+"$"),u=new RegExp("^"+r+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return a.test(t)},e.testNumeric=function(t){return u.test(t)},e.testAlphanumeric=function(t){return s.test(t)}},"0866":function(t,e,r){"use strict";r.d(e,"d",(function(){return a})),r.d(e,"a",(function(){return u})),r.d(e,"e",(function(){return s})),r.d(e,"b",(function(){return f})),r.d(e,"c",(function(){return h}));var n=navigator.userAgent.toLowerCase()||window.navigator.userAgent.toLowerCase(),o=(/youpin/i.test(n),/xianmai/i.test(n)),i="";i=o?"xm://xianmai":"yp://youpin";"".concat(i,"?position=login");var a="".concat(i,"?web="),u="".concat(i,"?position=authentication"),s="".concat(i,"?position=withdraw"),f="".concat(i,"?openBrowser="),h="".concat(i,"?position=wantToJoinIn");"".concat(i,"?position=mywallet")},"0b89":function(t,e,r){"use strict";var n=r("6e03"),o=r.n(n);o.a},"10b0":function(t,e,r){"use strict";var n={single_source_shortest_paths:function(t,e,r){var o={},i={};i[e]=0;var a,u,s,f,h,c,l,g,p,d=n.PriorityQueue.make();d.push(e,0);while(!d.empty())for(s in a=d.pop(),u=a.value,f=a.cost,h=t[u]||{},h)h.hasOwnProperty(s)&&(c=h[s],l=f+c,g=i[s],p="undefined"===typeof i[s],(p||g>l)&&(i[s]=l,d.push(s,l),o[s]=u));if("undefined"!==typeof r&&"undefined"===typeof i[r]){var v=["Could not find a path from ",e," to ",r,"."].join("");throw new Error(v)}return o},extract_shortest_path_from_predecessor_list:function(t,e){var r=[],n=e;while(n)r.push(n),t[n],n=t[n];return r.reverse(),r},find_path:function(t,e,r){var o=n.single_source_shortest_paths(t,e,r);return n.extract_shortest_path_from_predecessor_list(o,r)},PriorityQueue:{make:function(t){var e,r=n.PriorityQueue,o={};for(e in t=t||{},r)r.hasOwnProperty(e)&&(o[e]=r[e]);return o.queue=[],o.sorter=t.sorter||r.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var r={value:t,cost:e};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=n},"1fb5":function(t,e,r){"use strict";e.byteLength=h,e.toByteArray=l,e.fromByteArray=d;for(var n=[],o=[],i="undefined"!==typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u=0,s=a.length;u<s;++u)n[u]=a[u],o[a.charCodeAt(u)]=u;function f(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}function h(t){var e=f(t),r=e[0],n=e[1];return 3*(r+n)/4-n}function c(t,e,r){return 3*(e+r)/4-r}function l(t){var e,r,n=f(t),a=n[0],u=n[1],s=new i(c(t,a,u)),h=0,l=u>0?a-4:a;for(r=0;r<l;r+=4)e=o[t.charCodeAt(r)]<<18|o[t.charCodeAt(r+1)]<<12|o[t.charCodeAt(r+2)]<<6|o[t.charCodeAt(r+3)],s[h++]=e>>16&255,s[h++]=e>>8&255,s[h++]=255&e;return 2===u&&(e=o[t.charCodeAt(r)]<<2|o[t.charCodeAt(r+1)]>>4,s[h++]=255&e),1===u&&(e=o[t.charCodeAt(r)]<<10|o[t.charCodeAt(r+1)]<<4|o[t.charCodeAt(r+2)]>>2,s[h++]=e>>8&255,s[h++]=255&e),s}function g(t){return n[t>>18&63]+n[t>>12&63]+n[t>>6&63]+n[63&t]}function p(t,e,r){for(var n,o=[],i=e;i<r;i+=3)n=(t[i]<<16&16711680)+(t[i+1]<<8&65280)+(255&t[i+2]),o.push(g(n));return o.join("")}function d(t){for(var e,r=t.length,o=r%3,i=[],a=16383,u=0,s=r-o;u<s;u+=a)i.push(p(t,u,u+a>s?s:u+a));return 1===o?(e=t[r-1],i.push(n[e>>2]+n[e<<4&63]+"==")):2===o&&(e=(t[r-2]<<8)+t[r-1],i.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"=")),i.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},2732:function(t,e,r){var n=r("5a88"),o=r("699e");e.mul=function(t,e){for(var r=n.alloc(t.length+e.length-1),i=0;i<t.length;i++)for(var a=0;a<e.length;a++)r[i+a]^=o.mul(t[i],e[a]);return r},e.mod=function(t,e){var r=n.from(t);while(r.length-e.length>=0){for(var i=r[0],a=0;a<e.length;a++)r[a]^=o.mul(e[a],i);var u=0;while(u<r.length&&0===r[u])u++;r=r.slice(u)}return r},e.generateECPolynomial=function(t){for(var r=n.from([1]),i=0;i<t;i++)r=e.mul(r,[1,o.exp(i)]);return r}},"27a3":function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},"2f3a":function(t,e,r){var n=r("bbf0"),o=r("7bf0");function i(t){this.mode=n.KANJI,this.data=t}i.getBitsLength=function(t){return 13*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var e;for(e=0;e<this.data.length;e++){var r=o.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13)}},t.exports=i},"34fc":function(t,e,r){var n=r("7a43"),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case n.L:return o[4*(t-1)+0];case n.M:return o[4*(t-1)+1];case n.Q:return o[4*(t-1)+2];case n.H:return o[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case n.L:return i[4*(t-1)+0];case n.M:return i[4*(t-1)+1];case n.Q:return i[4*(t-1)+2];case n.H:return i[4*(t-1)+3];default:return}}},4006:function(t,e,r){var n=r("45be");function o(t,e){var r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function i(t,e,r){var n=t+e;return"undefined"!==typeof r&&(n+=" "+r),n}function a(t,e,r){for(var n="",o=0,a=!1,u=0,s=0;s<t.length;s++){var f=Math.floor(s%e),h=Math.floor(s/e);f||a||(a=!0),t[s]?(u++,s>0&&f>0&&t[s-1]||(n+=a?i("M",f+r,.5+h+r):i("m",o,0),o=0,a=!1),f+1<e&&t[s+1]||(n+=i("h",u),u=0)):o++}return n}e.render=function(t,e,r){var i=n.getOptions(e),u=t.modules.size,s=t.modules.data,f=u+2*i.margin,h=i.color.light.a?"<path "+o(i.color.light,"fill")+' d="M0 0h'+f+"v"+f+'H0z"/>':"",c="<path "+o(i.color.dark,"stroke")+' d="'+a(s,u,i.margin)+'"/>',l='viewBox="0 0 '+f+" "+f+'"',g=i.width?'width="'+i.width+'" height="'+i.width+'" ':"",p='<svg xmlns="http://www.w3.org/2000/svg" '+g+l+' shape-rendering="crispEdges">'+h+c+"</svg>\n";return"function"===typeof r&&r(null,p),p}},4146:function(t,e,r){var n=r("45be");function o(t,e,r){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=r,e.width=r,e.style.height=r+"px",e.style.width=r+"px"}function i(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}e.render=function(t,e,r){var a=r,u=e;"undefined"!==typeof a||e&&e.getContext||(a=e,e=void 0),e||(u=i()),a=n.getOptions(a);var s=n.getImageWidth(t.modules.size,a),f=u.getContext("2d"),h=f.createImageData(s,s);return n.qrToImageData(h.data,t,a),o(f,u,s),f.putImageData(h,0,0),u},e.renderToDataURL=function(t,r,n){var o=n;"undefined"!==typeof o||r&&r.getContext||(o=r,r=void 0),o||(o={});var i=e.render(t,r,o),a=o.type||"image/png",u=o.rendererOpts||{};return i.toDataURL(a,u.quality)}},"45be":function(t,e){function r(t){if("number"===typeof t&&(t=t.toString()),"string"!==typeof t)throw new Error("Color should be defined as hex string");var e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");var r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});var e="undefined"===typeof t.margin||null===t.margin||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:n,scale:n?4:o,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){var n=e.getScale(t,r);return Math.floor((t+2*r.margin)*n)},e.qrToImageData=function(t,r,n){for(var o=r.modules.size,i=r.modules.data,a=e.getScale(o,n),u=Math.floor((o+2*n.margin)*a),s=n.margin*a,f=[n.color.light,n.color.dark],h=0;h<u;h++)for(var c=0;c<u;c++){var l=4*(h*u+c),g=n.color.light;if(h>=s&&c>=s&&h<u-s&&c<u-s){var p=Math.floor((h-s)/a),d=Math.floor((c-s)/a);g=f[i[p*o+d]?1:0]}t[l++]=g.r,t[l++]=g.g,t[l++]=g.b,t[l]=g.a}}},"4c45":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAABpBAMAAADLrBAJAAAAMFBMVEVHcEz////////////////////////////////q8f7l7v+1yer////x9/+/0e/P3fZ3aHJYAAAACnRSTlMArVN+FO3L3i/3XihPjAAAA4BJREFUWMPdmL1uE0EQxx3ifJQJn40LUIBQpAgREhQpDEiEgiIQQBRGihAoFC6AQESRIoCg4gl4hOMO9znJFg6OkTX3Atb5BXKS09tSzO35cr69mf1KyRSu/NPM/GdnbndyOcruz81ufHiyNbt0Oadtny9AYm9v6THT58M/ex3HspxOBxqweVED+rINXttKzPGhoXZ3CSDFRBzAaQV0lXOUuHsnhb6BZxHmwwsJdJuGGHZWCJ0SQQxbFED5shAKsf0iTV2Btphy4BEJjcsghpExFrwfMiqMkYDugSU3B14SUrQUlGU3kSArKlehwWNzV4SzSQ1XobNMZoWWDmX/ztTK0jK+Zt89Pcqvp7UotfUop5HSY0zTVehsfUQtt3Qpe888QBbijizASuC61QER4kyiYIuEQuvhEBMVyzjAvuvSmNNMxgWCfsWQW8OFjkfxJE4rOKaqyJm/OqQWUFoVN7EDFGKcWAGl9XNEVRE1HATTINSCGU4sqtg4TisFYRWHFZvwJGkRidmrSjFcd5eu83LbjHKihi5ZZpTVoCVUUExEQkIF5S/SbSLVMOrnKaKPA1m9IukfEJT0bFj237Alie5PncMaMQXqVLnkZz6k9mgqFWKPoB5SfZLu5V1qUIWHo0xOsL6oleMOoynhjIqPVIkel5W+wJOUkn6P/l9q25xqCuo16A7tcCCgiBNVSfVXF3Ns3BAjKt2UbrdHnUNMcZBbrfWITlnIUkduxg6IrkQTIMhS2ePIJsBES+EKOWPTZqwlnjT07LDX0RStBJjKtBmbopmJXSGgTIjR97ykSitLNfBnuU9RNfwl4gtGpcV/0oeXAF56NWX/ie5ez82o4decF5GmeujmwF++AqUa8fWLk4OqcvWAuI1OcadjcNTlwKB7yB0N+0180/NMhoYfX+nz222zUSO66kku2XXz63wYYPLAmQaDCbpzgleAszd6ckzqvzjep1Ya2sN+9HQwUNGun+jVNsM99spahXYyD+0Vz1QL7VPlcFow+6rhzH+G9klqZ9iVzpMePubM1wc2uU0ZU21F6IXPglQQf02wYJNue5o7giXRXRCm1oEbktVXW5TU66J4+TVPY6K10rH8N6n1lw1r8p1efh5jPmwWVQvETwAdXgfl+pDZnXMhF6fnhMxTvY1qfu4V26P6PvvdXzLYwl7fKAE0ts5cyxlaXiLBP4GUWDnJxb2+AAAAAElFTkSuQmCC"},"577e":function(t,e,r){var n=r("5a88");function o(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=n.alloc(t*t),this.reservedBit=n.alloc(t*t)}o.prototype.set=function(t,e,r,n){var o=t*this.size+e;this.data[o]=r,n&&(this.reservedBit[o]=!0)},o.prototype.get=function(t,e){return this.data[t*this.size+e]},o.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},o.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=o},"5a88":function(t,e,r){"use strict";var n=r("eee5");function o(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(e){return!1}}a.TYPED_ARRAY_SUPPORT=o();var i=a.TYPED_ARRAY_SUPPORT?**********:**********;function a(t,e,r){return a.TYPED_ARRAY_SUPPORT||this instanceof a?"number"===typeof t?h(this,t):m(this,t,e,r):new a(t,e,r)}function u(t){if(t>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|t}function s(t){return t!==t}function f(t,e){var r;return a.TYPED_ARRAY_SUPPORT?(r=new Uint8Array(e),r.__proto__=a.prototype):(r=t,null===r&&(r=new a(e)),r.length=e),r}function h(t,e){var r=f(t,e<0?0:0|u(e));if(!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)r[n]=0;return r}function c(t,e){var r=0|v(e),n=f(t,r),o=n.write(e);return o!==r&&(n=n.slice(0,o)),n}function l(t,e){for(var r=e.length<0?0:0|u(e.length),n=f(t,r),o=0;o<r;o+=1)n[o]=255&e[o];return n}function g(t,e,r,n){if(r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");var o;return o=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n),a.TYPED_ARRAY_SUPPORT?o.__proto__=a.prototype:o=l(t,o),o}function p(t,e){if(a.isBuffer(e)){var r=0|u(e.length),n=f(t,r);return 0===n.length?n:(e.copy(n,0,0,r),n)}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||s(e.length)?f(t,0):l(t,e);if("Buffer"===e.type&&Array.isArray(e.data))return l(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function d(t,e){var r;e=e||1/0;for(var n=t.length,o=null,i=[],a=0;a<n;++a){if(r=t.charCodeAt(a),r>55295&&r<57344){if(!o){if(r>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(e-=3)>-1&&i.push(239,191,189),o=r;continue}r=65536+(o-55296<<10|r-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((e-=1)<0)break;i.push(r)}else if(r<2048){if((e-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function v(t){if(a.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var e=t.length;return 0===e?0:d(t).length}function y(t,e,r,n){for(var o=0;o<n;++o){if(o+r>=e.length||o>=t.length)break;e[o+r]=t[o]}return o}function w(t,e,r,n){return y(d(e,t.length-r),t,r,n)}function m(t,e,r,n){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?g(t,e,r,n):"string"===typeof e?c(t,e,r):p(t,e)}a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1})),a.prototype.write=function(t,e,r){void 0===e?(r=this.length,e=0):void 0===r&&"string"===typeof e?(r=this.length,e=0):isFinite(e)&&(e|=0,isFinite(r)?r|=0:r=void 0);var n=this.length-e;if((void 0===r||r>n)&&(r=n),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");return w(this,t,e,r)},a.prototype.slice=function(t,e){var r,n=this.length;if(t=~~t,e=void 0===e?n:~~e,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t),a.TYPED_ARRAY_SUPPORT)r=this.subarray(t,e),r.__proto__=a.prototype;else{var o=e-t;r=new a(o,void 0);for(var i=0;i<o;++i)r[i]=this[i+t]}return r},a.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var o,i=n-r;if(this===t&&r<e&&e<n)for(o=i-1;o>=0;--o)t[o+e]=this[o+r];else if(i<1e3||!a.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+i),e);return i},a.prototype.fill=function(t,e,r){if("string"===typeof t){if("string"===typeof e?(e=0,r=this.length):"string"===typeof r&&(r=this.length),1===t.length){var n=t.charCodeAt(0);n<256&&(t=n)}}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"===typeof t)for(o=e;o<r;++o)this[o]=t;else{var i=a.isBuffer(t)?t:new a(t),u=i.length;for(o=0;o<r-e;++o)this[o+e]=i[o%u]}return this},a.concat=function(t,e){if(!n(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f(null,0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var o=h(null,e),i=0;for(r=0;r<t.length;++r){var u=t[r];if(!a.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(o,i),i+=u.length}return o},a.byteLength=v,a.prototype._isBuffer=!0,a.isBuffer=function(t){return!(null==t||!t._isBuffer)},t.exports.alloc=function(t){var e=new a(t);return e.fill(0),e},t.exports.from=function(t){return new a(t)}},"67dd":function(t,e){t.exports=function(){return"function"===typeof Promise&&Promise.prototype&&Promise.prototype.then}},"699e":function(t,e,r){var n=r("5a88"),o=n.alloc(512),i=n.alloc(256);(function(){for(var t=1,e=0;e<255;e++)o[e]=t,i[t]=e,t<<=1,256&t&&(t^=285);for(e=255;e<512;e++)o[e]=o[e-255]})(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return i[t]},e.exp=function(t){return o[t]},e.mul=function(t,e){return 0===t||0===e?0:o[i[t]+i[e]]}},"6e03":function(t,e,r){},7903:function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var r={N1:3,N2:3,N3:40,N4:10};function n(t,r,n){switch(t){case e.Patterns.PATTERN000:return(r+n)%2===0;case e.Patterns.PATTERN001:return r%2===0;case e.Patterns.PATTERN010:return n%3===0;case e.Patterns.PATTERN011:return(r+n)%3===0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2===0;case e.Patterns.PATTERN101:return r*n%2+r*n%3===0;case e.Patterns.PATTERN110:return(r*n%2+r*n%3)%2===0;case e.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){for(var e=t.size,n=0,o=0,i=0,a=null,u=null,s=0;s<e;s++){o=i=0,a=u=null;for(var f=0;f<e;f++){var h=t.get(s,f);h===a?o++:(o>=5&&(n+=r.N1+(o-5)),a=h,o=1),h=t.get(f,s),h===u?i++:(i>=5&&(n+=r.N1+(i-5)),u=h,i=1)}o>=5&&(n+=r.N1+(o-5)),i>=5&&(n+=r.N1+(i-5))}return n},e.getPenaltyN2=function(t){for(var e=t.size,n=0,o=0;o<e-1;o++)for(var i=0;i<e-1;i++){var a=t.get(o,i)+t.get(o,i+1)+t.get(o+1,i)+t.get(o+1,i+1);4!==a&&0!==a||n++}return n*r.N2},e.getPenaltyN3=function(t){for(var e=t.size,n=0,o=0,i=0,a=0;a<e;a++){o=i=0;for(var u=0;u<e;u++)o=o<<1&2047|t.get(a,u),u>=10&&(1488===o||93===o)&&n++,i=i<<1&2047|t.get(u,a),u>=10&&(1488===i||93===i)&&n++}return n*r.N3},e.getPenaltyN4=function(t){for(var e=0,n=t.data.length,o=0;o<n;o++)e+=t.data[o];var i=Math.abs(Math.ceil(100*e/n/5)-10);return i*r.N4},e.applyMask=function(t,e){for(var r=e.size,o=0;o<r;o++)for(var i=0;i<r;i++)e.isReserved(i,o)||e.xor(i,o,n(t,i,o))},e.getBestMask=function(t,r){for(var n=Object.keys(e.Patterns).length,o=0,i=1/0,a=0;a<n;a++){r(a),e.applyMask(a,t);var u=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(a,t),u<i&&(i=u,o=a)}return o}},"7a43":function(t,e){function r(t){if("string"!==typeof t)throw new Error("Param is not a string");var r=t.toLowerCase();switch(r){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&"undefined"!==typeof t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return r(t)}catch(o){return n}}},"7ba0":function(t,e){function r(){this.buffer=[],this.length=0}r.prototype={get:function(t){var e=Math.floor(t/8);return 1===(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var r=0;r<e;r++)this.putBit(1===(t>>>e-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=r},"7bf0":function(t,e){var r,n=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return n[t]},e.getBCHDigit=function(t){var e=0;while(0!==t)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!==typeof t)throw new Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return"undefined"!==typeof r},e.toSJIS=function(t){return r(t)}},"8d23":function(t,e,r){var n=r("5a88"),o=r("2732"),i=r("b639").Buffer;function a(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}a.prototype.initialize=function(t){this.degree=t,this.genPoly=o.generateECPolynomial(this.degree)},a.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");var e=n.alloc(this.degree),r=i.concat([t,e],t.length+this.degree),a=o.mod(r,this.genPoly),u=this.degree-a.length;if(u>0){var s=n.alloc(this.degree);return a.copy(s,u),s}return a},t.exports=a},9152:function(t,e){e.read=function(t,e,r,n,o){var i,a,u=8*o-n-1,s=(1<<u)-1,f=s>>1,h=-7,c=r?o-1:0,l=r?-1:1,g=t[e+c];for(c+=l,i=g&(1<<-h)-1,g>>=-h,h+=u;h>0;i=256*i+t[e+c],c+=l,h-=8);for(a=i&(1<<-h)-1,i>>=-h,h+=n;h>0;a=256*a+t[e+c],c+=l,h-=8);if(0===i)i=1-f;else{if(i===s)return a?NaN:1/0*(g?-1:1);a+=Math.pow(2,n),i-=f}return(g?-1:1)*a*Math.pow(2,i-n)},e.write=function(t,e,r,n,o,i){var a,u,s,f=8*i-o-1,h=(1<<f)-1,c=h>>1,l=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,g=n?0:i-1,p=n?1:-1,d=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,a=h):(a=Math.floor(Math.log(e)/Math.LN2),e*(s=Math.pow(2,-a))<1&&(a--,s*=2),e+=a+c>=1?l/s:l*Math.pow(2,1-c),e*s>=2&&(a++,s/=2),a+c>=h?(u=0,a=h):a+c>=1?(u=(e*s-1)*Math.pow(2,o),a+=c):(u=e*Math.pow(2,c-1)*Math.pow(2,o),a=0));o>=8;t[r+g]=255&u,g+=p,u/=256,o-=8);for(a=a<<o|u,f+=o;f>0;t[r+g]=255&a,g+=p,a/=256,f-=8);t[r+g-p]|=128*d}},"924f":function(t,e,r){var n=r("7bf0").getSymbolSize,o=7;e.getPositions=function(t){var e=n(t);return[[0,0],[e-o,0],[0,e-o]]}},9582:function(t,e,r){var n=r("7bf0"),o=1335,i=21522,a=n.getBCHDigit(o);e.getEncodedBits=function(t,e){var r=t.bit<<3|e,u=r<<10;while(n.getBCHDigit(u)-a>=0)u^=o<<n.getBCHDigit(u)-a;return(r<<10|u)^i}},"9d94":function(t,e,r){var n=r("bbf0"),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=n.ALPHANUMERIC,this.data=t}i.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var e;for(e=0;e+2<=this.data.length;e+=2){var r=45*o.indexOf(this.data[e]);r+=o.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},t.exports=i},aa63:function(t,e,r){var n=r("5a88"),o=r("7bf0"),i=r("7a43"),a=r("7ba0"),u=r("577e"),s=r("d6c0"),f=r("924f"),h=r("7903"),c=r("34fc"),l=r("8d23"),g=r("c8aa"),p=r("9582"),d=r("bbf0"),v=r("befa"),y=r("eee5");function w(t,e){for(var r=t.size,n=f.getPositions(e),o=0;o<n.length;o++)for(var i=n[o][0],a=n[o][1],u=-1;u<=7;u++)if(!(i+u<=-1||r<=i+u))for(var s=-1;s<=7;s++)a+s<=-1||r<=a+s||(u>=0&&u<=6&&(0===s||6===s)||s>=0&&s<=6&&(0===u||6===u)||u>=2&&u<=4&&s>=2&&s<=4?t.set(i+u,a+s,!0,!0):t.set(i+u,a+s,!1,!0))}function m(t){for(var e=t.size,r=8;r<e-8;r++){var n=r%2===0;t.set(r,6,n,!0),t.set(6,r,n,!0)}}function b(t,e){for(var r=s.getPositions(e),n=0;n<r.length;n++)for(var o=r[n][0],i=r[n][1],a=-2;a<=2;a++)for(var u=-2;u<=2;u++)-2===a||2===a||-2===u||2===u||0===a&&0===u?t.set(o+a,i+u,!0,!0):t.set(o+a,i+u,!1,!0)}function E(t,e){for(var r,n,o,i=t.size,a=g.getEncodedBits(e),u=0;u<18;u++)r=Math.floor(u/3),n=u%3+i-8-3,o=1===(a>>u&1),t.set(r,n,o,!0),t.set(n,r,o,!0)}function A(t,e,r){var n,o,i=t.size,a=p.getEncodedBits(e,r);for(n=0;n<15;n++)o=1===(a>>n&1),n<6?t.set(n,8,o,!0):n<8?t.set(n+1,8,o,!0):t.set(i-15+n,8,o,!0),n<8?t.set(8,i-n-1,o,!0):n<9?t.set(8,15-n-1+1,o,!0):t.set(8,15-n-1,o,!0);t.set(i-8,8,1,!0)}function P(t,e){for(var r=t.size,n=-1,o=r-1,i=7,a=0,u=r-1;u>0;u-=2){6===u&&u--;while(1){for(var s=0;s<2;s++)if(!t.isReserved(o,u-s)){var f=!1;a<e.length&&(f=1===(e[a]>>>i&1)),t.set(o,u-s,f),i--,-1===i&&(a++,i=7)}if(o+=n,o<0||r<=o){o-=n,n=-n;break}}}}function R(t,e,r){var n=new a;r.forEach((function(e){n.put(e.mode.bit,4),n.put(e.getLength(),d.getCharCountIndicator(e.mode,t)),e.write(n)}));var i=o.getSymbolTotalCodewords(t),u=c.getTotalCodewordsCount(t,e),s=8*(i-u);n.getLengthInBits()+4<=s&&n.put(0,4);while(n.getLengthInBits()%8!==0)n.putBit(0);for(var f=(s-n.getLengthInBits())/8,h=0;h<f;h++)n.put(h%2?17:236,8);return B(n,t,e)}function B(t,e,r){for(var i=o.getSymbolTotalCodewords(e),a=c.getTotalCodewordsCount(e,r),u=i-a,s=c.getBlocksCount(e,r),f=i%s,h=s-f,g=Math.floor(i/s),p=Math.floor(u/s),d=p+1,v=g-p,y=new l(v),w=0,m=new Array(s),b=new Array(s),E=0,A=n.from(t.buffer),P=0;P<s;P++){var R=P<h?p:d;m[P]=A.slice(w,w+R),b[P]=y.encode(m[P]),w+=R,E=Math.max(E,R)}var B,T,_=n.alloc(i),C=0;for(B=0;B<E;B++)for(T=0;T<s;T++)B<m[T].length&&(_[C++]=m[T][B]);for(B=0;B<v;B++)for(T=0;T<s;T++)_[C++]=b[T][B];return _}function T(t,e,r,n){var i;if(y(t))i=v.fromArray(t);else{if("string"!==typeof t)throw new Error("Invalid data");var a=e;if(!a){var s=v.rawSplit(t);a=g.getBestVersionForData(s,r)}i=v.fromString(t,a||40)}var f=g.getBestVersionForData(i,r);if(!f)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<f)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+f+".\n")}else e=f;var c=R(e,r,i),l=o.getSymbolSize(e),p=new u(l);return w(p,e),m(p),b(p,e),A(p,r,0),e>=7&&E(p,e),P(p,c),isNaN(n)&&(n=h.getBestMask(p,A.bind(null,p,r))),h.applyMask(n,p),A(p,r,n),{modules:p,version:e,errorCorrectionLevel:r,maskPattern:n,segments:i}}e.create=function(t,e){if("undefined"===typeof t||""===t)throw new Error("No input text");var r,n,a=i.M;return"undefined"!==typeof e&&(a=i.from(e.errorCorrectionLevel,i.M),r=g.from(e.version),n=h.from(e.maskPattern),e.toSJISFunc&&o.setToSJISFunction(e.toSJISFunc)),T(t,r,a,n)}},b146:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"page"},[r("van-nav-bar",{attrs:{title:1==t.type?"邀请先迈店主":"邀请VIP会员","right-text":"分享","left-arrow":"",fixed:""},on:{"click-right":t.share,"click-left":t.goBack}}),t._m(0),r("div",{staticClass:"share",on:{click:t.openBrowser}},[t._v("分享")]),r("img",{ref:"qrcode",staticStyle:{display:"none"},attrs:{src:"",alt:"",id:"qrcode"}})],1)},o=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"canvas"},[r("img",{attrs:{id:"canvasId",src:"",alt:""}})])}],i=(r("e7e5"),r("d399")),a=r("5530"),u=r("a662"),s=r("6917"),f=r("ce3a"),h=r("c391"),c=r("2f62");
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
function l(t,e,r,n){function o(t){return t instanceof r?t:new r((function(e){e(t)}))}return new(r||(r=Promise))((function(r,i){function a(t){try{s(n.next(t))}catch(e){i(e)}}function u(t){try{s(n["throw"](t))}catch(e){i(e)}}function s(t){t.done?r(t.value):o(t.value).then(a,u)}s((n=n.apply(t,e||[])).next())}))}function g(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(t){return function(e){return s([t,e])}}function s(i){if(r)throw new TypeError("Generator is already executing.");while(a)try{if(r=1,n&&(o=2&i[0]?n["return"]:i[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(o=a.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(u){i=[6,u],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}}var p=function(t){return function(){var e=Array.prototype.slice.call(arguments);return new Promise((function(r,n){e.push((function(t,e){t?n(t):r(e)})),t.apply(null,e)}))}};function d(t){return"function"===typeof t}function v(t){return"string"===typeof t}var y=r("d055"),w=p(y.toCanvas),m=function(t){var e=t.canvas,r=t.content,n=t.width,o=void 0===n?0:n,i=t.nodeQrCodeOptions,a=void 0===i?{}:i;return a.errorCorrectionLevel=a.errorCorrectionLevel||E(r),b(r,a).then((function(t){return a.scale=0===o?void 0:o/t*4,w(e,r,a)}))},b=function(t,e){var r=document.createElement("canvas");return w(r,t,e).then((function(){return r.width}))},E=function(t){return t.length>36?"M":t.length>16?"Q":"H"},A=function(t){var e=t.canvas,r=t.logo;if(!r)return Promise.resolve();if(""===r)return Promise.resolve();var n=e.width;v(r)&&(r={src:r});var o=r,i=o.logoSize,a=void 0===i?.15:i,u=o.borderColor,s=void 0===u?"#ffffff":u,f=o.bgColor,h=void 0===f?s||"#ffffff":f,c=o.borderSize,l=void 0===c?.05:c,g=o.crossOrigin,p=o.borderRadius,d=void 0===p?8:p,y=o.logoRadius,w=void 0===y?0:y,m="string"===typeof r?r:r.src,b=n*a,E=n*(1-a)/2,A=n*(a+l),R=n*(1-a-l)/2,B=e.getContext("2d");P(B)(R,R,A,A,d),B.fillStyle=h,B.fill();var T=new Image;T.setAttribute("crossOrigin",g||"anonymous"),T.src=m;var _=function(t){B.drawImage(t,E,E,b,b)},C=function(t){var e=document.createElement("canvas");e.width=E+b,e.height=E+b,e.getContext("2d").drawImage(t,E,E,b,b),P(B)(E,E,b,b,w),B.fillStyle=B.createPattern(e,"no-repeat"),B.fill()};return new Promise((function(t){T.onload=function(){w?C(T):_(T),t()}}))},P=function(t){return function(e,r,n,o,i){var a=Math.min(n,o);return i>a/2&&(i=a/2),t.beginPath(),t.moveTo(e+i,r),t.arcTo(e+n,r,e+n,r+o,i),t.arcTo(e+n,r+o,e,r+o,i),t.arcTo(e,r+o,e,r,i),t.arcTo(e,r,e+n,r,i),t.closePath(),t}},R=function(t){return m(t).then((function(){return A(t)}))},B=function(t){return l(this,void 0,void 0,(function(){var e,r,n,o,i,a,u;return g(this,(function(s){switch(s.label){case 0:return e=t.canvas,t.logo&&(v(t.logo)&&(t.logo={src:t.logo}),t.logo.crossOrigin="Anonymous"),this.ifCanvasDrawed?[3,2]:[4,R(t)];case 1:s.sent(),s.label=2;case 2:if(r=t.image,n=void 0===r?new Image:r,o=t.downloadName,i=void 0===o?"qr-code":o,a=t.download,!e.toDataURL())throw new Error("Can not get the canvas DataURL");return n.src=e.toDataURL(),this.ifImageCreated=!0,!0===a||d(a)?(a=!0===a?function(t){return t()}:a,u=function(){T(n,i)},a&&a(u),[2,Promise.resolve()]):[2]}}))}))},T=function(t,e){var r=t.src,n=document.createElement("a");n.download=e,n.href=r,n.dispatchEvent(new MouseEvent("click"))},_=(function(){function t(t){this.ifCanvasDrawed=!1,this.ifImageCreated=!1,this.defaultOption={canvas:document.createElement("canvas"),image:new Image,content:""},this.option=Object.assign(this.defaultOption,t)}t.prototype.toCanvas=function(){var t=this;return R.call(this,this.option).then((function(){return t.ifCanvasDrawed=!0,Promise.resolve()}))},t.prototype.toImage=function(){return B.call(this,this.option)},t.prototype.downloadImage=function(t){return l(this,void 0,void 0,(function(){return g(this,(function(e){switch(e.label){case 0:return this.ifImageCreated?[3,2]:[4,this.toImage()];case 1:e.sent(),e.label=2;case 2:return T(this.option.image,t),[2]}}))}))},t.prototype.getCanvas=function(){return l(this,void 0,Promise,(function(){return g(this,(function(t){switch(t.label){case 0:return this.ifCanvasDrawed?[3,2]:[4,this.toCanvas()];case 1:t.sent(),t.label=2;case 2:return[2,this.option.canvas]}}))}))}}(),r("0866"),{name:"invite-poster",data:function(){return{bgWidth:750,bgHeight:1334,src:"",type:1,posterDetail:{},reference:0,qrcode:"",url:""}},computed:Object(a["a"])({},Object(c["b"])(["token"])),created:function(){this.type=this.$route.query.type,this.reference=this.$route.query.reference?this.$route.query.reference:0,this.getShopPoster()},methods:{share:function(){if(this.src){var t={showImageData:this.src,platform:["wechat","timeline"]};Object(s["i"])(t)}else Object(i["a"])("海报生成失败")},openBrowser:function(){var t={title:1==this.type?"邀请先迈店主":"邀请VIP会员",webpageUrl:"",thumbImage:this.posterDetail.bg_path,path:this.posterDetail.url};Object(s["h"])(t)},goBack:function(){this.reference?this.$router.back():Object(s["a"])("0")},getShopPoster:function(){var t=this,e={token:this.token,type:this.type};this.$axios.post(Object(h["a"])(f["a"].getH5ShopPoster),e).then((function(e){var r=e.data;0==r.code?(t.posterDetail=r.data,t.drawPoster()):Object(i["a"])(r.message)}))},drawPoster:function(){var t=this,e=[{type:"image",url:this.posterDetail.bg_path,width:this.bgWidth,height:1133},{type:"image",url:this.posterDetail.avatar_url||r("4c45"),width:100,height:100,x:26,y:1180,radius:1},{type:"text",text:this.posterDetail.shop_name,size:"36px",x:146,y:1186},{type:"text",text:"邀请你加入VIP会员",size:"26px",x:146,y:1250,color:"#666"},{type:"image",url:this.posterDetail.qrcode,width:140,height:140,x:416,y:1155},{type:"text",text:"长按保存二维码",size:"20px",x:416,y:1306,color:"#9a9a9a"},{type:"image",url:r("b828"),width:140,height:140,x:590,y:1155},{type:"text",text:"接受邀请",size:"20px",x:620,y:1306,color:"#9a9a9a"}];u["a"].makeImage({type:"url",parts:e,width:this.bgWidth,height:this.bgHeight},(function(e,r){document.getElementById("canvasId").src=r,t.src=r}))}}}),C=_,I=(r("0b89"),r("2877")),S=Object(I["a"])(C,n,o,!1,null,"74f1b5ae",null);e["default"]=S.exports},b639:function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r("1fb5"),o=r("9152"),i=r("e3db");function a(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"===typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(e){return!1}}function u(){return f.TYPED_ARRAY_SUPPORT?**********:**********}function s(t,e){if(u()<e)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=f.prototype):(null===t&&(t=new f(e)),t.length=e),t}function f(t,e,r){if(!f.TYPED_ARRAY_SUPPORT&&!(this instanceof f))return new f(t,e,r);if("number"===typeof t){if("string"===typeof e)throw new Error("If encoding is specified then the first argument must be a string");return g(this,t)}return h(this,t,e,r)}function h(t,e,r,n){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?v(t,e,r,n):"string"===typeof e?p(t,e,r):y(t,e)}function c(t){if("number"!==typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function l(t,e,r,n){return c(e),e<=0?s(t,e):void 0!==r?"string"===typeof n?s(t,e).fill(r,n):s(t,e).fill(r):s(t,e)}function g(t,e){if(c(e),t=s(t,e<0?0:0|w(e)),!f.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function p(t,e,r){if("string"===typeof r&&""!==r||(r="utf8"),!f.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|b(e,r);t=s(t,n);var o=t.write(e,r);return o!==n&&(t=t.slice(0,o)),t}function d(t,e){var r=e.length<0?0:0|w(e.length);t=s(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function v(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n),f.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=f.prototype):t=d(t,e),t}function y(t,e){if(f.isBuffer(e)){var r=0|w(e.length);return t=s(t,r),0===t.length?t:(e.copy(t,0,0,r),t)}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||et(e.length)?s(t,0):d(t,e);if("Buffer"===e.type&&i(e.data))return d(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function w(t){if(t>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|t}function m(t){return+t!=t&&(t=0),f.alloc(+t)}function b(t,e){if(f.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return W(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return $(t).length;default:if(n)return W(t).length;e=(""+e).toLowerCase(),n=!0}}function E(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if(r>>>=0,e>>>=0,r<=e)return"";t||(t="utf8");while(1)switch(t){case"hex":return O(this,e,r);case"utf8":case"utf-8":return M(this,e,r);case"ascii":return L(this,e,r);case"latin1":case"binary":return Y(this,e,r);case"base64":return U(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function A(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function P(t,e,r,n,o){if(0===t.length)return-1;if("string"===typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=o?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(o)return-1;r=t.length-1}else if(r<0){if(!o)return-1;r=0}if("string"===typeof e&&(e=f.from(e,n)),f.isBuffer(e))return 0===e.length?-1:R(t,e,r,n,o);if("number"===typeof e)return e&=255,f.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):R(t,[e],r,n,o);throw new TypeError("val must be string, number or Buffer")}function R(t,e,r,n,o){var i,a=1,u=t.length,s=e.length;if(void 0!==n&&(n=String(n).toLowerCase(),"ucs2"===n||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a=2,u/=2,s/=2,r/=2}function f(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(o){var h=-1;for(i=r;i<u;i++)if(f(t,i)===f(e,-1===h?0:i-h)){if(-1===h&&(h=i),i-h+1===s)return h*a}else-1!==h&&(i-=i-h),h=-1}else for(r+s>u&&(r=u-s),i=r;i>=0;i--){for(var c=!0,l=0;l<s;l++)if(f(t,i+l)!==f(e,l)){c=!1;break}if(c)return i}return-1}function B(t,e,r,n){r=Number(r)||0;var o=t.length-r;n?(n=Number(n),n>o&&(n=o)):n=o;var i=e.length;if(i%2!==0)throw new TypeError("Invalid hex string");n>i/2&&(n=i/2);for(var a=0;a<n;++a){var u=parseInt(e.substr(2*a,2),16);if(isNaN(u))return a;t[r+a]=u}return a}function T(t,e,r,n){return tt(W(e,t.length-r),t,r,n)}function _(t,e,r,n){return tt(Z(e),t,r,n)}function C(t,e,r,n){return _(t,e,r,n)}function I(t,e,r,n){return tt($(e),t,r,n)}function S(t,e,r,n){return tt(G(e,t.length-r),t,r,n)}function U(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function M(t,e,r){r=Math.min(t.length,r);var n=[],o=e;while(o<r){var i,a,u,s,f=t[o],h=null,c=f>239?4:f>223?3:f>191?2:1;if(o+c<=r)switch(c){case 1:f<128&&(h=f);break;case 2:i=t[o+1],128===(192&i)&&(s=(31&f)<<6|63&i,s>127&&(h=s));break;case 3:i=t[o+1],a=t[o+2],128===(192&i)&&128===(192&a)&&(s=(15&f)<<12|(63&i)<<6|63&a,s>2047&&(s<55296||s>57343)&&(h=s));break;case 4:i=t[o+1],a=t[o+2],u=t[o+3],128===(192&i)&&128===(192&a)&&128===(192&u)&&(s=(15&f)<<18|(63&i)<<12|(63&a)<<6|63&u,s>65535&&s<1114112&&(h=s))}null===h?(h=65533,c=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),o+=c}return N(n)}e.Buffer=f,e.SlowBuffer=m,e.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:a(),e.kMaxLength=u(),f.poolSize=8192,f._augment=function(t){return t.__proto__=f.prototype,t},f.from=function(t,e,r){return h(null,t,e,r)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(t,e,r){return l(null,t,e,r)},f.allocUnsafe=function(t){return g(null,t)},f.allocUnsafeSlow=function(t){return g(null,t)},f.isBuffer=function(t){return!(null==t||!t._isBuffer)},f.compare=function(t,e){if(!f.isBuffer(t)||!f.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,o=0,i=Math.min(r,n);o<i;++o)if(t[o]!==e[o]){r=t[o],n=e[o];break}return r<n?-1:n<r?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){if(!i(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=f.allocUnsafe(e),o=0;for(r=0;r<t.length;++r){var a=t[r];if(!f.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,o),o+=a.length}return n},f.byteLength=b,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)A(this,e,e+1);return this},f.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)A(this,e,e+3),A(this,e+1,e+2);return this},f.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)A(this,e,e+7),A(this,e+1,e+6),A(this,e+2,e+5),A(this,e+3,e+4);return this},f.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?M(this,0,t):E.apply(this,arguments)},f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},f.prototype.compare=function(t,e,r,n,o){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),e<0||r>t.length||n<0||o>this.length)throw new RangeError("out of range index");if(n>=o&&e>=r)return 0;if(n>=o)return-1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,o>>>=0,this===t)return 0;for(var i=o-n,a=r-e,u=Math.min(i,a),s=this.slice(n,o),h=t.slice(e,r),c=0;c<u;++c)if(s[c]!==h[c]){i=s[c],a=h[c];break}return i<a?-1:a<i?1:0},f.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},f.prototype.indexOf=function(t,e,r){return P(this,t,e,r,!0)},f.prototype.lastIndexOf=function(t,e,r){return P(this,t,e,r,!1)},f.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"===typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var o=this.length-e;if((void 0===r||r>o)&&(r=o),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return B(this,t,e,r);case"utf8":case"utf-8":return T(this,t,e,r);case"ascii":return _(this,t,e,r);case"latin1":case"binary":return C(this,t,e,r);case"base64":return I(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,e,r);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var x=4096;function N(t){var e=t.length;if(e<=x)return String.fromCharCode.apply(String,t);var r="",n=0;while(n<e)r+=String.fromCharCode.apply(String,t.slice(n,n+=x));return r}function L(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(127&t[o]);return n}function Y(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(t[o]);return n}function O(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var o="",i=e;i<r;++i)o+=X(t[i]);return o}function k(t,e,r){for(var n=t.slice(e,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}function D(t,e,r){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function z(t,e,r,n,o,i){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function F(t,e,r,n){e<0&&(e=65535+e+1);for(var o=0,i=Math.min(t.length-r,2);o<i;++o)t[r+o]=(e&255<<8*(n?o:1-o))>>>8*(n?o:1-o)}function j(t,e,r,n){e<0&&(e=4294967295+e+1);for(var o=0,i=Math.min(t.length-r,4);o<i;++o)t[r+o]=e>>>8*(n?o:3-o)&255}function V(t,e,r,n,o,i){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function J(t,e,r,n,i){return i||V(t,e,r,4,34028234663852886e22,-34028234663852886e22),o.write(t,e,r,n,23,4),r+4}function H(t,e,r,n,i){return i||V(t,e,r,8,17976931348623157e292,-17976931348623157e292),o.write(t,e,r,n,52,8),r+8}f.prototype.slice=function(t,e){var r,n=this.length;if(t=~~t,e=void 0===e?n:~~e,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t),f.TYPED_ARRAY_SUPPORT)r=this.subarray(t,e),r.__proto__=f.prototype;else{var o=e-t;r=new f(o,void 0);for(var i=0;i<o;++i)r[i]=this[i+t]}return r},f.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||D(t,e,this.length);var n=this[t],o=1,i=0;while(++i<e&&(o*=256))n+=this[t+i]*o;return n},f.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||D(t,e,this.length);var n=this[t+--e],o=1;while(e>0&&(o*=256))n+=this[t+--e]*o;return n},f.prototype.readUInt8=function(t,e){return e||D(t,1,this.length),this[t]},f.prototype.readUInt16LE=function(t,e){return e||D(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUInt16BE=function(t,e){return e||D(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUInt32LE=function(t,e){return e||D(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},f.prototype.readUInt32BE=function(t,e){return e||D(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||D(t,e,this.length);var n=this[t],o=1,i=0;while(++i<e&&(o*=256))n+=this[t+i]*o;return o*=128,n>=o&&(n-=Math.pow(2,8*e)),n},f.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||D(t,e,this.length);var n=e,o=1,i=this[t+--n];while(n>0&&(o*=256))i+=this[t+--n]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*e)),i},f.prototype.readInt8=function(t,e){return e||D(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},f.prototype.readInt16LE=function(t,e){e||D(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(t,e){e||D(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(t,e){return e||D(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return e||D(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readFloatLE=function(t,e){return e||D(t,4,this.length),o.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return e||D(t,4,this.length),o.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return e||D(t,8,this.length),o.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return e||D(t,8,this.length),o.read(this,t,!1,52,8)},f.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e|=0,r|=0,!n){var o=Math.pow(2,8*r)-1;z(this,t,e,r,o,0)}var i=1,a=0;this[e]=255&t;while(++a<r&&(i*=256))this[e+a]=t/i&255;return e+r},f.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e|=0,r|=0,!n){var o=Math.pow(2,8*r)-1;z(this,t,e,r,o,0)}var i=r-1,a=1;this[e+i]=255&t;while(--i>=0&&(a*=256))this[e+i]=t/a&255;return e+r},f.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,1,255,0),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},f.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):F(this,t,e,!0),e+2},f.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):F(this,t,e,!1),e+2},f.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):j(this,t,e,!0),e+4},f.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):j(this,t,e,!1),e+4},f.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var o=Math.pow(2,8*r-1);z(this,t,e,r,o-1,-o)}var i=0,a=1,u=0;this[e]=255&t;while(++i<r&&(a*=256))t<0&&0===u&&0!==this[e+i-1]&&(u=1),this[e+i]=(t/a>>0)-u&255;return e+r},f.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var o=Math.pow(2,8*r-1);z(this,t,e,r,o-1,-o)}var i=r-1,a=1,u=0;this[e+i]=255&t;while(--i>=0&&(a*=256))t<0&&0===u&&0!==this[e+i+1]&&(u=1),this[e+i]=(t/a>>0)-u&255;return e+r},f.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,1,127,-128),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},f.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):F(this,t,e,!0),e+2},f.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):F(this,t,e,!1),e+2},f.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,4,**********,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):j(this,t,e,!0),e+4},f.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||z(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):j(this,t,e,!1),e+4},f.prototype.writeFloatLE=function(t,e,r){return J(this,t,e,!0,r)},f.prototype.writeFloatBE=function(t,e,r){return J(this,t,e,!1,r)},f.prototype.writeDoubleLE=function(t,e,r){return H(this,t,e,!0,r)},f.prototype.writeDoubleBE=function(t,e,r){return H(this,t,e,!1,r)},f.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var o,i=n-r;if(this===t&&r<e&&e<n)for(o=i-1;o>=0;--o)t[o+e]=this[o+r];else if(i<1e3||!f.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+i),e);return i},f.prototype.fill=function(t,e,r,n){if("string"===typeof t){if("string"===typeof e?(n=e,e=0,r=this.length):"string"===typeof r&&(n=r,r=this.length),1===t.length){var o=t.charCodeAt(0);o<256&&(t=o)}if(void 0!==n&&"string"!==typeof n)throw new TypeError("encoding must be a string");if("string"===typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var i;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"===typeof t)for(i=e;i<r;++i)this[i]=t;else{var a=f.isBuffer(t)?t:W(new f(t,n).toString()),u=a.length;for(i=0;i<r-e;++i)this[i+e]=a[i%u]}return this};var K=/[^+\/0-9A-Za-z-_]/g;function Q(t){if(t=q(t).replace(K,""),t.length<2)return"";while(t.length%4!==0)t+="=";return t}function q(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function X(t){return t<16?"0"+t.toString(16):t.toString(16)}function W(t,e){var r;e=e||1/0;for(var n=t.length,o=null,i=[],a=0;a<n;++a){if(r=t.charCodeAt(a),r>55295&&r<57344){if(!o){if(r>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(e-=3)>-1&&i.push(239,191,189),o=r;continue}r=65536+(o-55296<<10|r-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((e-=1)<0)break;i.push(r)}else if(r<2048){if((e-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function Z(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function G(t,e){for(var r,n,o,i=[],a=0;a<t.length;++a){if((e-=2)<0)break;r=t.charCodeAt(a),n=r>>8,o=r%256,i.push(o),i.push(n)}return i}function $(t){return n.toByteArray(Q(t))}function tt(t,e,r,n){for(var o=0;o<n;++o){if(o+r>=e.length||o>=t.length)break;e[o+r]=t[o]}return o}function et(t){return t!==t}}).call(this,r("c8ba"))},b828:function(t,e,r){t.exports=r.p+"img/longtap-icon.6e8d5fe0.png"},bbf0:function(t,e,r){var n=r("27a3"),o=r("0425");function i(t){if("string"!==typeof t)throw new Error("Param is not a string");var r=t.toLowerCase();switch(r){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!n.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return o.testNumeric(t)?e.NUMERIC:o.testAlphanumeric(t)?e.ALPHANUMERIC:o.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return i(t)}catch(n){return r}}},befa:function(t,e,r){var n=r("bbf0"),o=r("dd7e"),i=r("9d94"),a=r("0196"),u=r("2f3a"),s=r("0425"),f=r("7bf0"),h=r("10b0");function c(t){return unescape(encodeURIComponent(t)).length}function l(t,e,r){var n,o=[];while(null!==(n=t.exec(r)))o.push({data:n[0],index:n.index,mode:e,length:n[0].length});return o}function g(t){var e,r,o=l(s.NUMERIC,n.NUMERIC,t),i=l(s.ALPHANUMERIC,n.ALPHANUMERIC,t);f.isKanjiModeEnabled()?(e=l(s.BYTE,n.BYTE,t),r=l(s.KANJI,n.KANJI,t)):(e=l(s.BYTE_KANJI,n.BYTE,t),r=[]);var a=o.concat(i,e,r);return a.sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function p(t,e){switch(e){case n.NUMERIC:return o.getBitsLength(t);case n.ALPHANUMERIC:return i.getBitsLength(t);case n.KANJI:return u.getBitsLength(t);case n.BYTE:return a.getBitsLength(t)}}function d(t){return t.reduce((function(t,e){var r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}function v(t){for(var e=[],r=0;r<t.length;r++){var o=t[r];switch(o.mode){case n.NUMERIC:e.push([o,{data:o.data,mode:n.ALPHANUMERIC,length:o.length},{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.ALPHANUMERIC:e.push([o,{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.KANJI:e.push([o,{data:o.data,mode:n.BYTE,length:c(o.data)}]);break;case n.BYTE:e.push([{data:o.data,mode:n.BYTE,length:c(o.data)}])}}return e}function y(t,e){for(var r={},o={start:{}},i=["start"],a=0;a<t.length;a++){for(var u=t[a],s=[],f=0;f<u.length;f++){var h=u[f],c=""+a+f;s.push(c),r[c]={node:h,lastCount:0},o[c]={};for(var l=0;l<i.length;l++){var g=i[l];r[g]&&r[g].node.mode===h.mode?(o[g][c]=p(r[g].lastCount+h.length,h.mode)-p(r[g].lastCount,h.mode),r[g].lastCount+=h.length):(r[g]&&(r[g].lastCount=h.length),o[g][c]=p(h.length,h.mode)+4+n.getCharCountIndicator(h.mode,e))}}i=s}for(l=0;l<i.length;l++)o[i[l]]["end"]=0;return{map:o,table:r}}function w(t,e){var r,s=n.getBestModeForData(t);if(r=n.from(e,s),r!==n.BYTE&&r.bit<s.bit)throw new Error('"'+t+'" cannot be encoded with mode '+n.toString(r)+".\n Suggested mode is: "+n.toString(s));switch(r!==n.KANJI||f.isKanjiModeEnabled()||(r=n.BYTE),r){case n.NUMERIC:return new o(t);case n.ALPHANUMERIC:return new i(t);case n.KANJI:return new u(t);case n.BYTE:return new a(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"===typeof e?t.push(w(e,null)):e.data&&t.push(w(e.data,e.mode)),t}),[])},e.fromString=function(t,r){for(var n=g(t,f.isKanjiModeEnabled()),o=v(n),i=y(o,r),a=h.find_path(i.map,"start","end"),u=[],s=1;s<a.length-1;s++)u.push(i.table[a[s]].node);return e.fromArray(d(u))},e.rawSplit=function(t){return e.fromArray(g(t,f.isKanjiModeEnabled()))}},c8aa:function(t,e,r){var n=r("7bf0"),o=r("34fc"),i=r("7a43"),a=r("bbf0"),u=r("27a3"),s=r("eee5"),f=7973,h=n.getBCHDigit(f);function c(t,r,n){for(var o=1;o<=40;o++)if(r<=e.getCapacity(o,n,t))return o}function l(t,e){return a.getCharCountIndicator(t,e)+4}function g(t,e){var r=0;return t.forEach((function(t){var n=l(t.mode,e);r+=n+t.getBitsLength()})),r}function p(t,r){for(var n=1;n<=40;n++){var o=g(t,n);if(o<=e.getCapacity(n,r,a.MIXED))return n}}e.from=function(t,e){return u.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!u.isValid(t))throw new Error("Invalid QR Code version");"undefined"===typeof r&&(r=a.BYTE);var i=n.getSymbolTotalCodewords(t),s=o.getTotalCodewordsCount(t,e),f=8*(i-s);if(r===a.MIXED)return f;var h=f-l(r,t);switch(r){case a.NUMERIC:return Math.floor(h/10*3);case a.ALPHANUMERIC:return Math.floor(h/11*2);case a.KANJI:return Math.floor(h/13);case a.BYTE:default:return Math.floor(h/8)}},e.getBestVersionForData=function(t,e){var r,n=i.from(e,i.M);if(s(t)){if(t.length>1)return p(t,n);if(0===t.length)return 1;r=t[0]}else r=t;return c(r.mode,r.getLength(),n)},e.getEncodedBits=function(t){if(!u.isValid(t)||t<7)throw new Error("Invalid QR Code version");var e=t<<12;while(n.getBCHDigit(e)-h>=0)e^=f<<n.getBCHDigit(e)-h;return t<<12|e}},d055:function(t,e,r){var n=r("67dd"),o=r("aa63"),i=r("4146"),a=r("4006");function u(t,e,r,i,a){var u=[].slice.call(arguments,1),s=u.length,f="function"===typeof u[s-1];if(!f&&!n())throw new Error("Callback required as last argument");if(!f){if(s<1)throw new Error("Too few arguments provided");return 1===s?(r=e,e=i=void 0):2!==s||e.getContext||(i=r,r=e,e=void 0),new Promise((function(n,a){try{var u=o.create(r,i);n(t(u,e,i))}catch(s){a(s)}}))}if(s<2)throw new Error("Too few arguments provided");2===s?(a=r,r=e,e=i=void 0):3===s&&(e.getContext&&"undefined"===typeof a?(a=i,i=void 0):(a=i,i=r,r=e,e=void 0));try{var h=o.create(r,i);a(null,t(h,e,i))}catch(c){a(c)}}e.create=o.create,e.toCanvas=u.bind(null,i.render),e.toDataURL=u.bind(null,i.renderToDataURL),e.toString=u.bind(null,(function(t,e,r){return a.render(t,r)}))},d6c0:function(t,e,r){var n=r("7bf0").getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];for(var e=Math.floor(t/7)+2,r=n(t),o=145===r?26:2*Math.ceil((r-13)/(2*e-2)),i=[r-7],a=1;a<e-1;a++)i[a]=i[a-1]-o;return i.push(6),i.reverse()},e.getPositions=function(t){for(var r=[],n=e.getRowColCoords(t),o=n.length,i=0;i<o;i++)for(var a=0;a<o;a++)0===i&&0===a||0===i&&a===o-1||i===o-1&&0===a||r.push([n[i],n[a]]);return r}},dd7e:function(t,e,r){var n=r("bbf0");function o(t){this.mode=n.NUMERIC,this.data=t.toString()}o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){var e,r,n;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),n=parseInt(r,10),t.put(n,10);var o=this.data.length-e;o>0&&(r=this.data.substr(e),n=parseInt(r,10),t.put(n,3*o+1))},t.exports=o},e3db:function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},eee5:function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}}}]);