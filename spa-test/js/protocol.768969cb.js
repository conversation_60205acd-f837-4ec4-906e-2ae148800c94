(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["protocol"],{"115d":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"main"},[a("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),a("div",{staticClass:"content"},[a("div",{domProps:{innerHTML:t._s(t.html)}})])],1)},c=[],i=(a("d3b7"),a("e7e5"),a("d399")),o=a("6917"),r=a("c391"),s=a("ce3a"),u=a("2a9b"),l={name:"protocol",data:function(){return{title:"",html:"",type:null,reference:0}},methods:{goBack:function(){this.reference?this.$router.back():Object(o["a"])("0")},getData:function(){var t=this,e={type:this.type},a=i["a"].loading({duration:0,forbidClick:!0,message:"加载中..."});this.$axios.post(Object(r["a"])(s["a"].protocolInfo),e).then((function(e){var n=e.data;a.clear(),0!=n.code?Object(i["a"])(n.message):(t.title=n.data.type,t.html=Object(u["k"])(n.data.content))})).catch((function(t){Object(i["a"])(t.message)})).finally((function(){}))}},created:function(){this.type=this.$route.query.type,this.reference=this.$route.query.reference?this.$route.query.reference:0,this.getData()}},f=l,d=(a("d596"),a("2877")),h=Object(d["a"])(f,n,c,!1,null,"48a216e4",null);e["default"]=h.exports},"51f3":function(t,e,a){},d596:function(t,e,a){"use strict";var n=a("51f3"),c=a.n(n);c.a},e7e5:function(t,e,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")}}]);