(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["joinBase"],{2997:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("keep-alive",[e.$route.meta.keepAlive?n("router-view"):e._e()],1),e.$route.meta.keepAlive?e._e():n("router-view")],1)},a=[],o=n("6917"),r={name:"joinBase",methods:{},created:function(){Object(o["g"])({hide:!0,bounces:!0,nativeNav:!0})}},u=r,c=n("2877"),s=Object(c["a"])(u,i,a,!1,null,"3905565e",null);t["default"]=s.exports}}]);