(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["clueDetail"],{4559:function(e,a,t){},"61af":function(e,a,t){"use strict";t.r(a);var s=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"main"},[t("van-nav-bar",{attrs:{title:e.title,"left-arrow":"",fixed:""},on:{"click-left":e.goBack}}),t("van-tabs",{attrs:{"line-width":"1.25rem"},model:{value:e.active,callback:function(a){e.active=a},expression:"active"}},[t("van-tab",{attrs:{title:"基础信息"}},[t("div",{staticClass:"info"},[t("div",{staticClass:"base"},[t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("手机号码：")])]),t("div",{staticClass:"base-row-right"},[t("p",[e._v(e._s(e.detailDict.mobile))])])]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("线索来源：")])]),t("div",{staticClass:"base-row-right"},[t("p",[e._v(e._s(e.detailDict.source_text))])])]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("导入时间：")])]),t("div",{staticClass:"base-row-right"},[t("p",[e._v(e._s(e.detailDict.create_time))])])]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("标签：")])]),t("div",{staticClass:"base-row-right"},e._l(e.detailDict.tags,(function(a,s){return t("span",{staticClass:"base-row-right-tag"},[e._v(e._s(a.tag_name))])})),0)]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("所属管理员：")])]),t("div",{staticClass:"base-row-right"},[t("p",[e._v(e._s(e.detailDict.belong_user))])])]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("是否有效：")])]),t("div",{staticClass:"base-row-right"},[t("van-radio-group",{attrs:{direction:"horizontal"},model:{value:e.validRadio,callback:function(a){e.validRadio=a},expression:"validRadio"}},[t("van-radio",{attrs:{name:"1","checked-color":"#FF3333"}},[e._v("有效")]),t("van-radio",{attrs:{name:"0","checked-color":"#FF3333"}},[e._v("无效")])],1)],1)]),t("div",{staticClass:"base-row"},[t("div",{staticClass:"base-row-left"},[t("p",[e._v("添加微信：")])]),t("div",{staticClass:"base-row-right"},[t("van-radio-group",{attrs:{direction:"horizontal"},model:{value:e.addRadio,callback:function(a){e.addRadio=a},expression:"addRadio"}},[t("van-radio",{attrs:{name:"1","checked-color":"#FF3333"}},[e._v("已添加")]),t("van-radio",{attrs:{name:"0","checked-color":"#FF3333"}},[e._v("未添加")])],1)],1)])])])]),t("van-tab",{attrs:{title:"订单信息"}},[t("div",{staticClass:"info"},[t("van-list",{attrs:{finished:e.finished,"finished-text":"没有更多了"},on:{load:e.getOrderList},model:{value:e.loading,callback:function(a){e.loading=a},expression:"loading"}},[t("div",e._l(e.orderList,(function(a,s){return t("div",{staticClass:"order"},[t("div",{staticClass:"order-row"},[t("div",{staticClass:"order-row-left"},[t("span",[e._v("下单时间："+e._s(a.order_create_time))]),t("span",[e._v("订单编号："+e._s(a.order_no))]),t("span",[e._v("商品名称："+e._s(a.goods_info))])]),t("div",{staticClass:"order-row-right"},[t("span",{staticClass:"order-row-right-one"},[e._v(e._s(a.source_text))]),t("span",{staticClass:"order-row-right-two"},[e._v("￥"+e._s(a.order_amount))])])]),t("p",{staticClass:"order-cate"},[e._v(e._s(a.goods_cate))])])})),0)])],1)])],1),t("div",{staticClass:"op"},[t("span",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.detailDict.mobile,expression:"detailDict.mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.onCopied,expression:"onCopied",arg:"success"}],staticClass:"op-copy"},[e._v("复制手机号码")]),t("span",{staticClass:"op-submit",on:{click:e.submitData}},[e._v("提交")])])],1)},i=[],r=(t("e17f"),t("2241")),c=(t("e7e5"),t("d399")),n=(t("96cf"),t("1da1")),o=t("5530"),d=t("c391"),l=t("ce3a"),u=t("2f62"),v={name:"clueDetail",data:function(){return{id:null,title:"线索详情",active:0,validRadio:"1",addRadio:"1",detailDict:{},loading:!1,finished:!1,page:1,page_size:10,orderList:[]}},computed:Object(o["a"])({},Object(u["b"])(["staffUserId"])),created:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.id=e.$route.query.id,a.next=3,e.getData();case 3:case"end":return a.stop()}}),a)})))()},methods:{goBack:function(){this.$router.go(-1)},onCopied:function(e){var a=this;return Object(n["a"])(regeneratorRuntime.mark((function e(){var t,s,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t={token:a.staffUserId,clue_users_id:a.id},e.prev=1,e.next=4,a.$axios.post(Object(d["a"])(l["a"].clueCopyUrl),t);case 4:s=e.sent,i=s.data,0!=i.code?Object(c["a"])(i.message):Object(c["a"])("复制成功"),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),Object(c["a"])(e.t0.message);case 12:case"end":return e.stop()}}),e,null,[[1,9]])})))()},getData:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function a(){var t,s,i,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t=c["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={clue_users_id:e.id,token:e.staffUserId},a.next=5,e.$axios.post(Object(d["a"])(l["a"].clueDetail),s);case 5:i=a.sent,r=i.data,0!=r.code?Object(c["a"])(r.message):(t.clear(),e.detailDict=r.data,e.validRadio="".concat(e.detailDict.status),e.addRadio="".concat(e.detailDict.is_add_wx)),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](0),Object(c["a"])(a.t0.message);case 13:case"end":return a.stop()}}),a,null,[[0,10]])})))()},getOrderList:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function a(){var t,s,i,r,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t=c["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:e.staffUserId,clue_users_id:e.id,page:e.page,page_size:e.page_size},a.prev=2,a.next=5,e.$axios.post(Object(d["a"])(l["a"].clueOrderListUrl),s);case 5:if(i=a.sent,r=i.data,0!=r.code)Object(c["a"])(r.message);else{for(n=0;n<r.data.list.length;n++)e.$set(e.orderList,e.orderList.length,r.data.list[n]);e.loading=!1,t.clear(),e.orderList.length>=r.data.count&&(e.finished=!0),e.page=e.page+1}a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](2),Object(c["a"])(a.t0.message);case 13:case"end":return a.stop()}}),a,null,[[2,10]])})))()},submitData:function(){var e=this;r["a"].confirm({title:"提示",message:"确认修改信息？"}).then(Object(n["a"])(regeneratorRuntime.mark((function a(){var t,s,i,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t=c["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={clue_users_id:e.id,token:e.staffUserId,is_add_wx:e.addRadio,status:e.validRadio},a.next=5,e.$axios.post(Object(d["a"])(l["a"].editCluesUrl),s);case 5:i=a.sent,r=i.data,0!=r.code?Object(c["a"])(r.message):(t.clear(),Object(c["a"])(r.message)),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](0),Object(c["a"])(a.t0.message);case 13:case"end":return a.stop()}}),a,null,[[0,10]])})))).catch((function(){}))}}},p=v,b=(t("bc5f"),t("2877")),f=Object(b["a"])(p,s,i,!1,null,"53122ce4",null);a["default"]=f.exports},bc5f:function(e,a,t){"use strict";var s=t("4559"),i=t.n(s);i.a},e7e5:function(e,a,t){"use strict";t("68ef"),t("a71a"),t("9d70"),t("3743"),t("4d75"),t("e3b3"),t("b258")}}]);