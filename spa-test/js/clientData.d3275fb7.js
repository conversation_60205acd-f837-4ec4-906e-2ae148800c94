(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["clientData"],{5250:function(t,e,a){"use strict";var s=a("c2fd"),r=a.n(s);r.a},acc4:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAArhJREFUSEtjZKAzYKSzfQyjFoJD/O6FPWqHN/eVMfz/I0dMFPz++eOLrIblBo/YzkXo6gkG6Ys7F8VWTY1Z/ObJdT52Ln4GDi4BvHb+/feH4cu7pwyMTEwMNoFV010jGlAsJWjhofWdAXuWVZeLyes98EqZ2aCobvKJkC8PbuwK3be0OlJa2expesexEGT1BC08tWeGx84FZfWm7pnTsQURNsu/vXkj3ZEpsUZJ1+lLQt0uV4IW/v//n/nqkdWWj24fZ/7969t/EXE1pTcvb93DZriQhAqbgqb1dVl1q6cw+f///0vWh7GuI8rCi0fXiV06MK/79oUdaoSCDibPJyT9w9a3uMfcJ28rSIwkC9dOjm+7eGipo6CY4mdhKY2nTEzMzPgs/v3zK+uD6wcV+IXlfobmL4oG+ZQkC+fVO297eOOwYHTx+kw1M+8LxPhyfpPb0vuX9ynZBlW1uEY2bSXJwgVNbrvfPr/LUzTtThAjI+NzYiycVKDX/+bpNQvPxP4Jll65K0ctRAm1IRmkT26e1J9VYz1DRtX8fVrbUS+8GZ8SH3onT5r85uktwce3T3g8u3tGRMPU/2RU2doCmlkYnL/o8IZpabZ/f/9gkFEzf+cQ3pirpueCUkJhlKWU+BCULRiYmT/9+v6FUcc28IiwsCpGQY/VwjdPb/IUz3hAdD6cWWEx+endMyZOYbV9DqH1q/HlXQwL59Q5bnh0/bC4W1z3XGufgkWMjIy/8Bnw6PYZ5Y3Tkye8fnxVxCWus8bOt3gvSRbuWFwed3xLfyZIE5+QLMGC5tePLwzfv7xlkFQ2fuWR3hGuqOj4gyQLQYq3zc1PfXrvrO+nd89FCdnIys75g09Y+qqVf2kfegLBphdvBfz//38hBgYGdgKWviUU7AQrYEK+okSeYBODEsNJDlJqWwYyDwABraQsw9QakAAAAABJRU5ErkJggg=="},c2fd:function(t,e,a){},d695:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.isShow?s("div",{staticClass:"main"},[s("div",{staticClass:"header"},[s("div",{staticClass:"header-one"},[s("van-image",{staticClass:"header-avatar",attrs:{src:t.wxEnterpriseInfo.avatar},scopedSlots:t._u([{key:"error",fn:function(){return[s("img",{attrs:{src:a("f79e")}})]},proxy:!0},{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!1,2334954007)}),s("div",{staticClass:"header-info"},[s("h3",{staticClass:"header-info-name"},[t._v(t._s(t.wxEnterpriseInfo.name))]),t.isXianMaiMember?t._e():s("p",{staticClass:"header-info-name-not"},[t._v("账号：非先迈用户")]),t.miniToken?s("div",{staticClass:"header-info-two",on:{click:function(e){t.show=!t.show}}},[s("span",{staticClass:"header-info-two-phone"},[t._v("账号："+t._s(t.columns[t.phoneIndex]))]),s("van-icon",{attrs:{name:a("05cd"),color:"#666666"}})],1):t._e(),t.currentUserInfo.recommend_mobile?s("div",{staticClass:"header-info-two"},[s("span",{staticClass:"header-info-two-recommend"},[t._v("推荐人："+t._s(t.currentUserInfo.recommend_mobile))]),s("van-icon",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:t.currentUserInfo.recommend_mobile,expression:"currentUserInfo.recommend_mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:t.firstCopySuccess,expression:"firstCopySuccess",arg:"success"},{name:"clipboard",rawName:"v-clipboard:error",value:t.firstCopyError,expression:"firstCopyError",arg:"error"}],attrs:{name:a("acc4"),color:"#666666"}})],1):t._e()])],1)]),s("van-tabs",{on:{change:t.changeTab},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[s("van-tab",{attrs:{title:"消费数据","title-class":"one-tab-title"}},[t.isXianMaiMember?s("div",{staticClass:"tab-1"},[s("div",{staticClass:"tab-1-money"},[s("span",{staticClass:"tab-1-money-left"},[t._v("钱包余额")]),s("span",{staticClass:"tab-1-money-right"},[t._v("￥"+t._s(t.phoneUserInfo.balance_money))])]),s("div",{staticClass:"tab-1-tags"},[s("div",{staticClass:"tab-1-tags-header"},[s("span",{staticClass:"tab-1-tags-header-left"},[t._v("用户标签")]),s("span",{staticClass:"tab-1-tags-header-right"},[t._v(t._s(t.phoneUserInfo.reg_date)+"注册")])]),t.phoneUserInfo.member_tags.length>0||t.phoneUserInfo.xm_group_name?[s("div",{ref:"tagsList",staticClass:"tab-1-tags-list",class:{"tab-1-tags-list-all":t.groupShowMore}},[t.phoneUserInfo.xm_group_name?s("span",{staticClass:"tab-1-tags-list-one tab-1-tags-list-first"},[t._v(t._s(t.phoneUserInfo.xm_group_name))]):t._e(),t._l(t.phoneUserInfo.member_tags,(function(e,a){return s("span",{key:a,staticClass:"tab-1-tags-list-one"},[t._v(t._s(e))])}))],2),t.showGroupButton?s("van-icon",{staticClass:"tab-1-tags-more",attrs:{name:t.groupShowMore?"arrow-up":"arrow-down",color:"#666666"},on:{click:function(e){t.groupShowMore=!t.groupShowMore}}}):t._e()]:s("div",{staticClass:"tab-1-tags-empty"},[t._v("该用户暂无标签")])],2),s("div",{staticClass:"tab-1-orders"},[s("div",{staticClass:"tab-1-orders-tip"},[s("span",{staticClass:"tab-1-orders-tip-left"},[t._v("订单数据")]),s("span",{staticClass:"tab-1-orders-tip-right",on:{click:t.toMyOrder}},[t._v("明细"),s("van-icon",{attrs:{name:"arrow",color:"#666666"}})],1)]),s("div",{staticClass:"tab-1-orders-date"},[s("span",{staticClass:"tab-1-orders-date-item",class:{"tab-1-orders-date-item-active":0==t.dateIndex},on:{click:function(e){return t.selectDate(0)}}},[t._v("近七天")]),s("span",{staticClass:"tab-1-orders-date-item",class:{"tab-1-orders-date-item-active":1==t.dateIndex},on:{click:function(e){return t.selectDate(1)}}},[t._v("全部")]),s("span",{staticClass:"tab-1-orders-date-item",class:{"tab-1-orders-date-item-active":2==t.dateIndex},on:{click:function(e){return t.selectDate(2)}}},[t._v(t._s(t.date?t.date:"自定义"))])]),s("div",{staticClass:"tab-1-orders-data"},[s("div",{staticClass:"tab-1-orders-data-item"},[s("span",{staticClass:"tab-1-orders-data-item-number"},[t._v(t._s(t.orderData.order_num))]),s("span",{staticClass:"tab-1-orders-data-item-tip"},[t._v("订单总量")])]),s("div",{staticClass:"tab-1-orders-data-item"},[s("span",{staticClass:"tab-1-orders-data-item-number"},[t._v(t._s(t.orderData.order_sale))]),s("span",{staticClass:"tab-1-orders-data-item-tip"},[t._v("订单金额")])]),s("div",{staticClass:"tab-1-orders-data-item"},[s("span",{staticClass:"tab-1-orders-data-item-number"},[t._v(t._s(t.orderData.order_refund_num))]),s("span",{staticClass:"tab-1-orders-data-item-tip",on:{click:t.toRefundList}},[t._v("售后单量"),s("van-icon",{attrs:{name:"arrow",color:"#666666"}})],1)])])]),s("div",{staticClass:"tab-1-browse"},[s("div",{staticClass:"tab-1-browse-tabs"},[s("p",{staticClass:"tab-1-browse-title"},[t._v("Ta最近浏览")]),s("p",{staticClass:"tab-1-browse-title",staticStyle:{"font-weight":"400"},on:{click:t.toMyOrder}},[t._v("消费记录")])]),s("div",{staticClass:"tab-1-browse-list"},[t.browseList.length>0||!t.finished?s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.browseList,(function(e,r){return s("div",{key:r,staticClass:"tab-1-browse-list-item"},[s("p",{staticClass:"tab-1-browse-list-item-date"},[t._v(t._s(e.browse_date))]),s("div",{staticClass:"tab-1-browse-list-item-list"},t._l(e.goods,(function(e,r){return s("div",{key:r,staticClass:"tab-1-browse-list-item-list-item",on:{click:function(a){return t.toDetail(e)}}},[s("van-image",{staticClass:"tab-1-browse-list-item-list-item-img",attrs:{src:e.goods_image},scopedSlots:t._u([{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0},{key:"error",fn:function(){return[s("img",{attrs:{src:a("077e")}})]},proxy:!0}],null,!0)}),s("div",{staticClass:"tab-1-browse-list-item-list-item-right"},[s("p",{staticClass:"tab-1-browse-list-item-list-item-right-title"},[t._v(t._s(e.goods_name))]),s("p",{staticClass:"tab-1-browse-list-item-list-item-right-desc"},[t._v("于"+t._s(e.show_time)+"浏览改商品")]),s("p",{staticClass:"tab-1-browse-list-item-list-item-right-price"},[s("span",[t._v("￥")]),t._v(t._s(e.retail_price))])])],1)})),0)])})),0):s("div",{staticClass:"not-product"},[s("img",{staticClass:"not-product-img",attrs:{src:a("ac78"),alt:""}}),s("p",{staticClass:"not-product-tip"},[t._v("Ta还没浏览过商品")])])],1)])]):s("div",{staticClass:"not-member"},[s("img",{staticClass:"not-member-img",attrs:{src:a("1dd5"),alt:""}}),s("p",{staticClass:"not-member-tip"},[t._v("该微信用户未在先迈商城注册")])])]),s("van-tab",{attrs:{title:"店铺分销","title-class":"one-tab-title"}},[t.isXianMaiMember?[t.currentUserInfo.hasOwnProperty("is_shopper")&&t.currentUserInfo.is_shopper?s("div",{staticClass:"tab-2"},[s("div",{staticClass:"tab-2-fan"},[s("span",{staticClass:"tab-2-fan-left"},[t._v("粉丝数量")]),s("span",{staticClass:"tab-2-fan-right"},[t._v(t._s(t.phoneUserInfo.fans))])]),s("div",{staticClass:"tab-2-orders"},[s("div",{staticClass:"tab-2-orders-tip"},[s("span",{staticClass:"tab-2-orders-tip-left"},[t._v("店铺订单")]),s("span",{staticClass:"tab-2-orders-tip-right",on:{click:t.toShopOrder}},[t._v("明细"),s("van-icon",{attrs:{name:"arrow",color:"#666666"}})],1)]),s("div",{staticClass:"tab-2-orders-date"},[s("span",{staticClass:"tab-2-orders-date-item",class:{"tab-2-orders-date-item-active":0==t.productDateIndex},on:{click:function(e){return t.productSelectDate(0)}}},[t._v("近七天")]),s("span",{staticClass:"tab-2-orders-date-item",class:{"tab-2-orders-date-item-active":1==t.productDateIndex},on:{click:function(e){return t.productSelectDate(1)}}},[t._v("全部")]),s("span",{staticClass:"tab-2-orders-date-item",class:{"tab-2-orders-date-item-active":2==t.productDateIndex},on:{click:function(e){return t.productSelectDate(2)}}},[t._v(t._s(t.productDate?t.productDate:"自定义"))])]),s("div",{staticClass:"tab-2-orders-data"},[s("div",{staticClass:"tab-2-orders-data-item"},[s("span",{staticClass:"tab-2-orders-data-item-number"},[t._v(t._s(t.shopOrderData.order_num))]),s("span",{staticClass:"tab-2-orders-data-item-tip"},[t._v("销售单量")])]),s("div",{staticClass:"tab-2-orders-data-item"},[s("span",{staticClass:"tab-2-orders-data-item-number"},[t._v(t._s(t.shopOrderData.order_sale))]),s("span",{staticClass:"tab-2-orders-data-item-tip"},[t._v("销售金额")])]),s("div",{staticClass:"tab-2-orders-data-item"},[s("span",{staticClass:"tab-2-orders-data-item-number"},[t._v(t._s(t.shopOrderData.order_reward))]),s("span",{staticClass:"tab-2-orders-data-item-tip"},[t._v("佣金收益")])])]),s("p",{staticClass:"tab-2-orders-desc"},[t._v(t._s(t.shopOrderData.shop_own_sales_tips))])]),s("div",{staticClass:"tab-2-product"},[s("p",{staticClass:"tab-2-product-title"},[t._v("销售商品排行")]),s("div",{staticClass:"tab-2-product-list"},[t.productList.length>0||!t.productFinished?s("van-list",{attrs:{finished:t.productFinished,"finished-text":"没有更多了"},on:{load:t.onLoadProduct},model:{value:t.productLoading,callback:function(e){t.productLoading=e},expression:"productLoading"}},t._l(t.productList,(function(e,r){return s("div",{key:r,staticClass:"tab-2-product-list-item",on:{click:function(a){return t.toDetail(e)}}},[s("van-image",{staticClass:"tab-2-product-list-item-img",attrs:{src:e.goods_image},scopedSlots:t._u([{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0},{key:"error",fn:function(){return[s("img",{attrs:{src:a("077e")}})]},proxy:!0}],null,!0)}),s("div",{staticClass:"tab-2-product-list-item-right"},[s("p",{staticClass:"tab-2-product-list-item-right-name"},[t._v(t._s(e.goods_name))]),s("div",{staticClass:"tab-2-product-list-item-right-two"},[s("p",{staticClass:"tab-2-product-list-item-right-two-data"},[s("span",{staticClass:"tab-2-product-list-item-right-two-data-tip"},[t._v("销售额")]),s("span",{staticClass:"tab-2-product-list-item-right-two-data-money"},[t._v("￥")]),t._v(t._s(e.sales_money))]),s("p",{staticClass:"tab-2-product-list-item-right-two-data"},[s("span",{staticClass:"tab-2-product-list-item-right-two-data-tip"},[t._v("省赚")]),s("span",{staticClass:"tab-2-product-list-item-right-two-data-money"},[t._v("￥")]),t._v(t._s(e.income))])])])],1)})),0):s("div",{staticClass:"not-product"},[s("img",{staticClass:"not-product-img",attrs:{src:a("ac78"),alt:""}}),s("p",{staticClass:"not-product-tip"},[t._v("店铺还没销售过商品")])])],1)])]):s("div",{staticClass:"not-member"},[s("img",{staticClass:"not-member-img",attrs:{src:a("1dd5"),alt:""}}),s("p",{staticClass:"not-member-tip"},[t._v("该微信用户还不是店主")])])]:[s("div",{staticClass:"not-member"},[s("img",{staticClass:"not-member-img",attrs:{src:a("1dd5"),alt:""}}),s("p",{staticClass:"not-member-tip"},[t._v("该微信用户未在先迈商城注册")])])]],2)],1),s("van-popup",{staticClass:"phone-popup",attrs:{position:"bottom",round:"","close-on-click-overlay":!1},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[s("van-picker",{attrs:{"show-toolbar":"","default-index":t.phoneIndex,columns:t.columns},on:{confirm:t.onConfirm,cancel:t.onCancel}})],1),s("van-calendar",{attrs:{type:"range","min-date":t.minDate,"max-date":t.maxDate,"allow-same-day":!0},on:{confirm:t.onDateConfirm},model:{value:t.showDate,callback:function(e){t.showDate=e},expression:"showDate"}}),s("to-top")],1):t._e()},r=[],n=(a("99af"),a("4de4"),a("d81d"),a("fb6a"),a("d3b7"),a("ac1f"),a("25f0"),a("1276"),a("e7e5"),a("d399")),i=a("3835"),o=(a("96cf"),a("1da1")),c=a("5530"),d=a("5d30"),l=(a("860d"),a("c391")),u=a("ce3a"),p=a("2f62"),m=a("2a9b"),b={name:"clientData",data:function(){return{isShow:!1,isXianMaiMember:!0,currentUserInfo:{},phoneIndex:0,show:!1,showDate:!1,groupShowMore:!0,showGroupButton:!1,columns:[],phoneUserInfo:{headimg:"",member_tags:[]},active:0,dateIndex:0,date:"",orderData:{order_num:0,order_sale:0,order_refund_num:0},productDateIndex:0,productDate:"",shopOrderData:{order_num:0,order_sale:0,order_reward:0},minDate:new Date(2010,0,1),maxDate:new Date,browseList:[],page:1,page_size:3,loading:!1,finished:!1,productList:[],product_page:1,product_page_size:10,productLoading:!1,productFinished:!1}},computed:Object(c["a"])({},Object(p["b"])(["token","miniToken","shop_id","wxEnterpriseUserInfo","wxEnterpriseInfo"])),components:{toTop:d["a"]},mounted:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.wxEnterpriseUserInfo.length<1?t.isXianMaiMember=!1:(a=t.wxEnterpriseUserInfo.filter((function(e){return e.token==t.miniToken})),a.length>0?t.currentUserInfo=a[0]:t.isXianMaiMember=!1,t.columns=t.wxEnterpriseUserInfo.map((function(t){return t.mobile}))),e.next=3,t.getPhoneUserInfo();case 3:return e.next=5,t.tag1LoadData();case 5:return t.isShow=!0,e.next=8,t.chengTagsHeightOverflow();case 8:case"end":return e.stop()}}),e)})))()},methods:{chengTagsHeightOverflow:function(){var t=this;this.$nextTick((function(){var e=136/750*window.innerWidth;if(t.$refs.tagsList){var a=t.$refs.tagsList.getBoundingClientRect().height;a>e&&(t.groupShowMore=!1,t.showGroupButton=!0)}}))},onConfirm:function(t,e){var a=this;return Object(o["a"])(regeneratorRuntime.mark((function s(){var r,n;return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(t!=a.columns[a.phoneIndex]){s.next=3;break}return a.show=!1,s.abrupt("return");case 3:if(r=a.wxEnterpriseUserInfo.filter((function(e){return e.mobile==t})),!(r.length<1)){s.next=7;break}return a.show=!1,s.abrupt("return");case 7:return n=r[0],a.currentUserInfo=n,s.next=11,a.$store.dispatch("setShopId",n.shop_id);case 11:return s.next=13,a.$store.dispatch("setToken",n.shop_token);case 13:return s.next=15,a.$store.dispatch("setMiniToken",n.token);case 15:return a.phoneIndex=e,a.show=!1,s.next=19,a.getPhoneUserInfo();case 19:if(0!=a.active){s.next=24;break}return s.next=22,a.tag1LoadData();case 22:s.next=26;break;case 24:return s.next=26,a.tag2LoadData();case 26:case"end":return s.stop()}}),s)})))()},onCancel:function(){this.show=!1},formatDate:function(t){return"".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate())},onDateConfirm:function(t){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){var s,r,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(s=Object(i["a"])(t,2),r=s[0],n=s[1],e.showDate=!1,0!=e.active){a.next=9;break}return e.dateIndex=2,e.date="".concat(e.formatDate(r)," - ").concat(e.formatDate(n)),a.next=7,e.getOrderStatistics();case 7:a.next=14;break;case 9:if(1!=e.active){a.next=14;break}return e.productDateIndex=2,e.productDate="".concat(e.formatDate(r)," - ").concat(e.formatDate(n)),a.next=14,e.getOrderStatistics();case 14:case"end":return a.stop()}}),a)})))()},selectDate:function(t){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(2!=t){a.next=4;break}e.showDate=!0,a.next=7;break;case 4:return e.dateIndex=t,a.next=7,e.getOrderStatistics();case 7:case"end":return a.stop()}}),a)})))()},getPhoneUserInfo:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a,s,r,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.miniToken){e.next=2;break}return e.abrupt("return");case 2:return a=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s={token:t.miniToken,shop_id:t.shop_id},e.prev=4,e.next=7,t.$axios.post(Object(l["a"])(u["a"].enterpriseMemberUrl),s);case 7:r=e.sent,i=r.data,0!=i.code?Object(n["a"])(i.message):(i.data.member_info&&(t.phoneUserInfo=i.data.member_info),a.clear()),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](4),Object(n["a"])(e.t0.message);case 15:case"end":return e.stop()}}),e,null,[[4,12]])})))()},productSelectDate:function(t){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(2!=t){a.next=4;break}e.showDate=!0,a.next=7;break;case 4:return e.productDateIndex=t,a.next=7,e.getOrderStatistics();case 7:case"end":return a.stop()}}),a)})))()},getOrderStatistics:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a,s,r,i,o,c,d;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.miniToken){e.next=2;break}return e.abrupt("return");case 2:return a=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),s=t.active,r={token:t.miniToken,shop_id:t.shop_id,statistics_type:0==s?"order":"shop_order",time_type:0==s?t.dateIndex+1:t.productDateIndex+1},i=0==s?t.date:t.productDate,i&&(o=i.split("-"),6==o.length&&(r["begin_time"]="".concat(o[0],"-").concat(o[1],"-").concat(o[2]),r["end_time"]="".concat(o[3],"-").concat(o[4],"-").concat(o[5]))),e.prev=7,e.next=10,t.$axios.post(Object(l["a"])(u["a"].OrderStatisticsUrl),r);case 10:c=e.sent,d=c.data,0!=d.code?Object(n["a"])(d.message):(0==s?"Object"==Object.prototype.toString.call(d.data).slice(8,-1)&&(t.orderData=d.data):"Object"==Object.prototype.toString.call(d.data).slice(8,-1)&&(t.shopOrderData=d.data),a.clear()),e.next=18;break;case 15:e.prev=15,e.t0=e["catch"](7),Object(n["a"])(e.t0.message);case 18:case"end":return e.stop()}}),e,null,[[7,15]])})))()},onLoad:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a,s,r,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.miniToken){e.next=2;break}return e.abrupt("return");case 2:return a={token:t.miniToken,shop_id:t.shop_id,page:t.page,page_size:t.page_size},e.prev=3,e.next=6,t.$axios.post(Object(l["a"])(u["a"].historyBrowsingUrl),a);case 6:if(s=e.sent,r=s.data,0!=r.code)Object(n["a"])(r.message);else if(r.data.list){for(i=0;i<r.data.list.length;i++)t.$set(t.browseList,t.browseList.length,r.data.list[i]);t.loading=!1,t.page>=r.data.page_count&&(t.finished=!0),t.page=t.page+1}else t.loading=!1,t.finished=!0;e.next=17;break;case 11:e.prev=11,e.t0=e["catch"](3),Object(n["a"])(e.t0.message),t.loading=!1,t.page=1,t.finished=!0;case 17:case"end":return e.stop()}}),e,null,[[3,11]])})))()},onLoadProduct:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a,s,r,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={token:t.miniToken,shop_id:t.shop_id,page:t.product_page,page_size:t.product_page_size},e.prev=1,e.next=4,t.$axios.post(Object(l["a"])(u["a"].goodsSalesRankUrl),a);case 4:if(s=e.sent,r=s.data,0!=r.code)Object(n["a"])(r.message);else if(r.data.list){for(i=0;i<r.data.list.length;i++)t.$set(t.productList,t.productList.length,r.data.list[i]);t.productLoading=!1,t.product_page>=r.data.page_count&&(t.productFinished=!0),t.product_page=t.product_page+1}else t.loading=!1,t.finished=!0;e.next=15;break;case 9:e.prev=9,e.t0=e["catch"](1),Object(n["a"])(e.t0.message),t.productLoading=!1,t.product_page=1,t.productFinished=!0;case 15:case"end":return e.stop()}}),e,null,[[1,9]])})))()},resetTab1:function(){this.dateIndex=0,this.date="",this.page=1,this.browseList=[],this.finished=!1,this.loading=!0},tag1LoadData:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.resetTab1(),e.next=3,t.getOrderStatistics();case 3:return e.next=5,t.onLoad();case 5:case"end":return e.stop()}}),e)})))()},resetTab2:function(){this.productDateIndex=0,this.productDate="",this.product_page=1,this.productList=[],this.productFinished=!1,this.productLoading=!0},tag2LoadData:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.resetTab2(),e.next=3,t.getOrderStatistics();case 3:return e.next=5,t.onLoadProduct();case 5:case"end":return e.stop()}}),e)})))()},changeTab:function(t){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(0!=t){a.next=5;break}return a.next=3,e.tag1LoadData();case 3:a.next=8;break;case 5:if(!e.currentUserInfo.hasOwnProperty("is_shopper")||!e.currentUserInfo.is_shopper){a.next=8;break}return a.next=8,e.tag2LoadData();case 8:case"end":return a.stop()}}),a)})))()},toMyOrder:function(){this.$router.push({name:"myOrderList"})},toRefundList:function(){this.$router.push({name:"refundList"})},toShopOrder:function(){this.$router.push({name:"orderLists"})},toDetail:function(t){var e="";e="pintuan"==t.goods_type?"/promotionpages/pintuan/detail/detail?id=".concat(t.pintuan_id,"&sku_id=").concat(t.sku_id):"seckill"==t.goods_type?"/promotionpages/new_seckill/detail/detail?sku_id=".concat(t.sku_id):"/pages/goods/detail/detail?sku_id=".concat(t.sku_id),Object(m["d"])(e)},firstCopySuccess:function(t){Object(n["a"])("复制成功")},firstCopyError:function(t){Object(n["a"])("复制失败!")}}},f=b,h=(a("5250"),a("2877")),g=Object(h["a"])(f,s,r,!1,null,"2a9b57d4",null);e["default"]=g.exports}}]);