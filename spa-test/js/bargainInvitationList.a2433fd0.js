(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["bargainInvitationList"],{"6a92":function(t,i,a){"use strict";a.r(i);var e=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"active-goods-list"},[e("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),e("div",{staticClass:"active-list-box"},[!t.finished||t.finished&&t.activeListData.length>0?e("van-list",{staticClass:"van-list",attrs:{error:t.error,"error-text":"请求失败，点击重新加载",finished:t.finished,"finished-text":"没有更多了"},on:{"update:error":function(i){t.error=i},load:t.getData},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.activeListData,(function(i,a){return e("div",{key:a,staticClass:"active-list"},[e("div",{staticClass:"active-list-products",on:{click:function(a){return t.toDetails(i)}}},[e("div",{staticClass:"active-list-products-img"},[e("van-image",{attrs:{src:i.img}})],1),e("div",{staticClass:"active-list-products-price"},[e("div",{staticClass:"item-name"},[t._v(t._s(i.name?i.name:"-"))]),e("div",{staticClass:"item-price"},[t._v("￥"),e("span",[t._v(t._s(i.price?i.price:"-"))])]),e("div",{staticClass:"item-status"},[e("div",[t._v("活动类型 "+t._s(i.activity_type_txt?i.activity_type_txt:"-"))]),e("div",[e("span",[t._v("佣金 ")]),e("span",{staticClass:"price-word-two"},[t._v("￥"+t._s(i.profit?i.profit:"-"))])])]),e("div",{staticClass:"item-time"},[t._v(t._s(i.start_time)+" ~ "+t._s(i.end_time))])])])])})),0):e("div",{staticClass:"active-list-default"},[e("img",{attrs:{src:a("f1b1")}}),e("p",[t._v("暂无数据")])])],1)],1)},s=[],n=(a("d3b7"),a("e7e5"),a("d399")),c=a("5530"),o=a("6917"),r=a("ce3a"),l=a("c391"),d=a("2f62"),v={name:"bargainInvitationList",components:{},data:function(){return{title:"砍价活动列表",page:1,page_size:10,error:!1,loading:!1,finished:!1,activeListData:[],shopId:""}},computed:Object(c["a"])({},Object(d["b"])(["token"])),methods:{getData:function(){var t=this,i=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),a={token:this.token,page:this.page,size:this.page_size};this.$axios.post(Object(l["a"])(r["a"].bargainingList),a).then((function(a){var e=a.data;if(0!=e.code)Object(n["a"])(e.message);else{for(var s=0;s<e.data.list.length;s++)t.$set(t.activeListData,t.activeListData.length,e.data.list[s]);i.clear(),t.loading=!1,t.activeListData.length>=e.data.total&&(t.finished=!0),t.page=t.page+1}})).catch((function(a){i.clear(),t.loading=!1,t.page=1,t.finished=!0,t.error=!0})).finally((function(){}))},goBack:function(){Object(o["a"])("0")},toDetails:function(t){this.$router.push({name:"bargainInvitationDetails",query:{activity_id:t.activity_id}})}}},f=v,p=(a("fdc9"),a("2877")),u=Object(p["a"])(f,e,s,!1,null,"3962a384",null);i["default"]=u.exports},8501:function(t,i,a){},e7e5:function(t,i,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")},f1b1:function(t,i,a){t.exports=a.p+"img/img-shopper-default.cb55b4c9.png"},fdc9:function(t,i,a){"use strict";var e=a("8501"),s=a.n(e);s.a}}]);