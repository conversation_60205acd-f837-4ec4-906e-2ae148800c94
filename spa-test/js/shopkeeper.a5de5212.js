(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["shopkeeper"],{"684fc":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"shopkeeper"},[s("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),t.list.length>0?s("div",{staticClass:"group"},[s("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.getData},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,(function(e,a){return s("div",{key:a,staticClass:"item"},[s("div",{staticClass:"head"},[s("van-image",{attrs:{src:e.avatar_url},scopedSlots:t._u([{key:"error",fn:function(){return[s("img",{attrs:{src:i("f79e")}})]},proxy:!0},{key:"loading",fn:function(){return[s("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)})],1),s("div",{staticClass:"info"},[s("div",{staticClass:"title"},[t._v(t._s(e.shop_name))]),s("div",{staticClass:"info-text"},[s("div",{staticClass:"level"},[t._v("LV."+t._s(e.level))]),s("div",{staticClass:"today"},[t._v("今日卖货"+t._s(e.today_num)+"件")]),s("div",{staticClass:"total"},[t._v("累计卖货"+t._s(e.total_num)+"件")])]),s("div",{staticClass:"time"},[t._v(t._s(e.join_date))])])])})),0)],1):s("div",{staticClass:"group-default"},[s("img",{attrs:{src:i("f1b1")}}),s("p",[t._v("暂无数据")])]),s("div",{staticClass:"to-invite",on:{click:function(e){return t.toInvite()}}},[t._v("邀请店主")])],1)},a=[],n=(i("d3b7"),i("e7e5"),i("d399")),o=i("5530"),l=i("6917"),c=i("ce3a"),r=i("c391"),p=i("2f62"),d={name:"shopkeeper",components:{},data:function(){return{title:"",shop_id:"",type:1,page:1,page_size:10,loading:!1,finished:!1,list:[]}},computed:Object(o["a"])({},Object(p["b"])(["token"])),methods:{getData:function(){var t=this,e={shop_id:this.shop_id,type:this.type,page:this.page,page_size:this.page_size};this.$axios.post(Object(r["a"])(c["a"].appRelation),e).then((function(e){var i=e.data;if(0!=i.code)Object(n["a"])(i.message);else{console.log(i.data.list);for(var s=0;s<i.data.list.length;s++)t.$set(t.list,t.list.length,i.data.list[s]);t.loading=!1,t.page_size>i.data.list.length&&(t.finished=!0),t.page=t.page+1}})).catch((function(e){Object(n["a"])(e.message),t.loading=!1,t.page=1,t.page_size>result.data.list.length&&(t.finished=!0)})).finally((function(){}))},goBack:function(){Object(l["a"])("0")},toInvite:function(){this.$router.push({name:"invitePoster",query:{type:this.type}})}},created:function(){this.type=this.$route.query.type,this.shop_id=this.$route.query.shop_id,1==this.type?this.title="店主":2==this.type?this.title="VIP店主":this.title="店主",this.getData()}},u=d,f=(i("a3e2b"),i("2877")),h=Object(f["a"])(u,s,a,!1,null,"3ec8786e",null);e["default"]=h.exports},a3e2b:function(t,e,i){"use strict";var s=i("fa48"),a=i.n(s);a.a},e7e5:function(t,e,i){"use strict";i("68ef"),i("a71a"),i("9d70"),i("3743"),i("4d75"),i("e3b3"),i("b258")},f1b1:function(t,e,i){t.exports=i.p+"img/img-shopper-default.cb55b4c9.png"},f79e:function(t,e,i){t.exports=i.p+"img/img-shopper-head.bef43d9e.png"},fa48:function(t,e,i){}}]);