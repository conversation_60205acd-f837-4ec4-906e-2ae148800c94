(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["upgradeShopkeeper"],{"0564":function(e,t,a){},"0be6":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAdVBMVEUAAACpqamqqqqpqampqampqamfn5+pqamoqKioqKipqamfn5+pqampqampqamrq6upqampqampqampqamqqqqoqKipqampqamqqqqpqampqamqqqqpqampqamqqqqpqampqampqampqampqampqampqampqalT1Mh2AAAAJnRSTlMA99jQ/O4IHxYSkwTqnokr5N6zcj0O88KOUDatm3hZybulRIQmYpr8zK0AAAFDSURBVEjH7ZTZbsIwEEVvknH2leyBhLL0/v8nloJaCIkt8YjEebKUOZHtuR58eG/81BbqEDv1MadSFNvRYQvjalbvKXYB9AQdlYcHEoYwE3KHB2KWMFMyxgMkR5gYSc4EaZhCT8pG5oI1KO6gIdpRDdaTgCzn1sUa7pZ5hoUAz+bXmlF/0fawIsB3uA+WLdjT8bEqoG6v32Z4NtsaGgHuhsV5Vn8uuHGhFRAljDPcGWImEfTChY6qukeyYQeYhZC0jrjRW2RoFqIDnVGkjH7Xpcjo8HBZmw69DzBZzJNTktOaELSmQ9ftrXXZVkjKJrv+Q3+tvvMfjvrYH4O/Xeoa5xWa+IUs1qKR5dqAn9bCNyjDoytlEW9p5Bt6Jnl+QLR6Te29h/MhMMFMz/jVMZO8NMhCKm8xKgtbRyFU1cvD+MN78wPDqCQUaZo6OgAAAABJRU5ErkJggg=="},"78de":function(e,t,a){"use strict";var o=a("0564"),i=a.n(o);i.a},"833d":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"main"},[o("div",{staticClass:"content"},[o("img",{staticClass:"content-bg",attrs:{src:a("e2bf"),alt:""}}),o("div",{staticClass:"form"},[o("div",{staticClass:"form-group"},[o("img",{staticClass:"form-group-icon",attrs:{src:a("850d"),alt:""}}),o("input",{directives:[{name:"model",rawName:"v-model",value:e.phone,expression:"phone"}],staticClass:"form-group-phone",attrs:{type:"text",maxlength:"20",placeholder:"请输入手机号"},domProps:{value:e.phone},on:{input:function(t){t.target.composing||(e.phone=t.target.value)}}})]),o("div",{staticClass:"form-group"},[o("img",{staticClass:"form-group-icon",attrs:{src:a("0be6"),alt:""}}),o("input",{directives:[{name:"model",rawName:"v-model",value:e.code,expression:"code"}],staticClass:"form-group-code",attrs:{type:"text",placeholder:"请输入短信验证码"},domProps:{value:e.code},on:{input:function(t){t.target.composing||(e.code=t.target.value)}}}),o("span",{staticClass:"form-group-get",on:{click:e.getCode}},[e._v(e._s(e.timing>=0?e.timing+" S":"获取验证码"))])]),o("div",{staticClass:"form-protocol"},[o("van-checkbox",{attrs:{shape:"square","icon-size":"0.8125rem"},model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),o("p",{staticClass:"form-protocol-text"},[e._v("开通即视为同意"),o("span",{on:{click:e.toProtocol}},[e._v("《用户协议》")])])],1),o("button",{staticClass:"form-op",attrs:{type:"button"},on:{click:e.toUpgrade}},[e._v("我要升级")])])])])},i=[],n=(a("d3b7"),a("e7e5"),a("d399")),s=a("6917"),c=a("c391"),r=a("ce3a"),p={name:"upgradeShopkeeper",data:function(){return{title:"升级店主",reference:0,checked:!1,phone:"",code:"",timing:-1,intervalObj:null,type:1,invitation_shop_id:null}},methods:{goBack:function(){this.reference?this.$router.back():Object(s["a"])("0")},checkPhone:function(e){var t=/^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/;return t.test(e)},getCode:function(){var e=this;if(this.phone&&this.checkPhone(this.phone)){var t={type:6,mobile:this.phone},a=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."});this.$axios.post(Object(c["a"])(r["a"].sendCode),t).then((function(t){var o=t.data;a.clear(),0!=o.code?Object(n["a"])(o.message):e.runTiming()})).catch((function(e){Object(n["a"])(e.message)})).finally((function(){}))}else Object(n["a"])("请输入正确手机号码")},runTiming:function(){var e=this;this.timing>0||(this.timing=60,this.intervalObj=setInterval((function(){e.timing<0?clearInterval(e.intervalObj):e.timing--}),1e3))},toProtocol:function(){this.$router.push({name:"protocol",query:{type:1,reference:1}})},toUpgrade:function(){if(this.phone&&this.checkPhone(this.phone))if(this.code)if(this.checked){var e={invitation_shop_id:this.invitation_shop_id,mobile:this.phone,code:this.code,open_type:this.type,app_type:"h5"},t=n["a"].loading({duration:0,forbidClick:!0,message:"加载中..."});this.$axios.post(Object(c["a"])(r["a"].openShop),e).then((function(e){var a=e.data;t.clear(),0!=a.code?Object(n["a"])(a.message):n["a"].success({message:"申请成功,等待审核",duration:1e4,forbidClick:!0})})).catch((function(e){Object(n["a"])(e.message)})).finally((function(){}))}else Object(n["a"])("请勾选用户协议");else Object(n["a"])("请输入验证码");else Object(n["a"])("请输入正确手机号码")}},created:function(){this.type=this.$route.query.type,this.reference=this.$route.query.reference?this.$route.query.reference:0,this.invitation_shop_id=this.$route.query.invitation_shop_id},beforeDestroy:function(){clearInterval(this.intervalObj)}},l=p,m=(a("78de"),a("2877")),u=Object(m["a"])(l,o,i,!1,null,"f423023e",null);t["default"]=u.exports},"850d":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAALVBMVEUAAACmpqanp6epqamoqKipqamoqKipqampqampqamoqKimpqaAgICkpKSpqanfV5MFAAAADnRSTlMASCDgceqbfNWsqCsCDgO+i4AAAABoSURBVDjLY6AbcH6HAI8dEOLs75DBc4QE6zMlBFB7ipDge4hkLOO7UYlRiYGSeGqMAKbIEiiJGlnimQsCuGHacegAdsu57ayxS3C+e4ldguvdK+wSTH1NODzIC/UgoTyImWtx53MqAwBT65E1sI3CdwAAAABJRU5ErkJggg=="},e2bf:function(e,t,a){e.exports=a.p+"img/upgrade-bg.871c74d8.png"},e7e5:function(e,t,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")}}]);