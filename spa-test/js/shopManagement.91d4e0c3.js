(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["shopManagement"],{"0866":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return i})),a.d(t,"e",(function(){return c})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return f}));var n=navigator.userAgent.toLowerCase()||window.navigator.userAgent.toLowerCase(),s=(/youpin/i.test(n),/xianmai/i.test(n)),o="";o=s?"xm://xianmai":"yp://youpin";"".concat(o,"?position=login");var r="".concat(o,"?web="),i="".concat(o,"?position=authentication"),c="".concat(o,"?position=withdraw"),l="".concat(o,"?openBrowser="),f="".concat(o,"?position=wantToJoinIn");"".concat(o,"?position=mywallet")},"088a":function(e,t,a){"use strict";var n=a("db9c"),s=a.n(n);s.a},"18df":function(e,t,a){},3814:function(e,t,a){e.exports=a.p+"img/copy.8e3c61d3.svg"},5118:function(e,t,a){(function(e){var n="undefined"!==typeof e&&e||"undefined"!==typeof self&&self||window,s=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(s.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new o(s.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},a("6017"),t.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof e&&e.clearImmediate||this&&this.clearImmediate}).call(this,a("c8ba"))},"5d30":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("img",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}],style:{bottom:e.pointBottom,right:e.pointRight},attrs:{src:a("849b"),alt:""},on:{click:e.toTop}})},s=[],o=(a("a9e3"),a("7707")),r=a.n(o),i={name:"toTop",props:{pointBottom:{default:"5.6875rem",type:String},pointRight:{default:"0px",type:String},scrollShow:{default:500,type:Number}},data:function(){return{isShow:!1}},methods:{onScroll:function(e){var t=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;t>this.scrollShow&&!this.isShow?this.isShow=!0:t<=this.scrollShow&&this.isShow&&(this.isShow=!1)},toTop:function(){window.scroll({top:0,behavior:"smooth"})}},mounted:function(){r.a.polyfill(),window.addEventListener("scroll",this.onScroll)}},c=i,l=(a("86e1"),a("2877")),f=Object(l["a"])(c,n,s,!1,null,"793636d7",null);t["a"]=f.exports},6017:function(e,t,a){(function(e,t){(function(e,a){"use strict";if(!e.setImmediate){var n,s=1,o={},r=!1,i=e.document,c=Object.getPrototypeOf&&Object.getPrototypeOf(e);c=c&&c.setTimeout?c:e,"[object process]"==={}.toString.call(e.process)?d():m()?h():e.MessageChannel?v():i&&"onreadystatechange"in i.createElement("script")?g():b(),c.setImmediate=l,c.clearImmediate=f}function l(e){"function"!==typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),a=0;a<t.length;a++)t[a]=arguments[a+1];var r={callback:e,args:t};return o[s]=r,n(s),s++}function f(e){delete o[e]}function u(e){var t=e.callback,n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(a,n);break}}function p(e){if(r)setTimeout(p,0,e);else{var t=o[e];if(t){r=!0;try{u(t)}finally{f(e),r=!1}}}}function d(){n=function(e){t.nextTick((function(){p(e)}))}}function m(){if(e.postMessage&&!e.importScripts){var t=!0,a=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=a,t}}function h(){var t="setImmediate$"+Math.random()+"$",a=function(a){a.source===e&&"string"===typeof a.data&&0===a.data.indexOf(t)&&p(+a.data.slice(t.length))};e.addEventListener?e.addEventListener("message",a,!1):e.attachEvent("onmessage",a),n=function(a){e.postMessage(t+a,"*")}}function v(){var e=new MessageChannel;e.port1.onmessage=function(e){var t=e.data;p(t)},n=function(t){e.port2.postMessage(t)}}function g(){var e=i.documentElement;n=function(t){var a=i.createElement("script");a.onreadystatechange=function(){p(t),a.onreadystatechange=null,e.removeChild(a),a=null},e.appendChild(a)}}function b(){n=function(e){setTimeout(p,0,e)}}})("undefined"===typeof self?"undefined"===typeof e?this:e:self)}).call(this,a("c8ba"),a("4362"))},7707:function(e,t,a){(function(){"use strict";function t(){var e=window,t=document;if(!("scrollBehavior"in t.documentElement.style&&!0!==e.__forceSmoothScrollPolyfill__)){var a=e.HTMLElement||e.Element,n=468,s={scroll:e.scroll||e.scrollTo,scrollBy:e.scrollBy,elementScroll:a.prototype.scroll||c,scrollIntoView:a.prototype.scrollIntoView},o=e.performance&&e.performance.now?e.performance.now.bind(e.performance):Date.now,r=i(e.navigator.userAgent)?1:0;e.scroll=e.scrollTo=function(){void 0!==arguments[0]&&(!0!==f(arguments[0])?v.call(e,t.body,void 0!==arguments[0].left?~~arguments[0].left:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:e.scrollY||e.pageYOffset):s.scroll.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:e.scrollY||e.pageYOffset))},e.scrollBy=function(){void 0!==arguments[0]&&(f(arguments[0])?s.scrollBy.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!==typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):v.call(e,t.body,~~arguments[0].left+(e.scrollX||e.pageXOffset),~~arguments[0].top+(e.scrollY||e.pageYOffset)))},a.prototype.scroll=a.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==f(arguments[0])){var e=arguments[0].left,t=arguments[0].top;v.call(this,this,"undefined"===typeof e?this.scrollLeft:~~e,"undefined"===typeof t?this.scrollTop:~~t)}else{if("number"===typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!==typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},a.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==f(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):s.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},a.prototype.scrollIntoView=function(){if(!0!==f(arguments[0])){var a=m(this),n=a.getBoundingClientRect(),o=this.getBoundingClientRect();a!==t.body?(v.call(this,a,a.scrollLeft+o.left-n.left,a.scrollTop+o.top-n.top),"fixed"!==e.getComputedStyle(a).position&&e.scrollBy({left:n.left,top:n.top,behavior:"smooth"})):e.scrollBy({left:o.left,top:o.top,behavior:"smooth"})}else s.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function i(e){var t=["MSIE ","Trident/","Edge/"];return new RegExp(t.join("|")).test(e)}function c(e,t){this.scrollLeft=e,this.scrollTop=t}function l(e){return.5*(1-Math.cos(Math.PI*e))}function f(e){if(null===e||"object"!==typeof e||void 0===e.behavior||"auto"===e.behavior||"instant"===e.behavior)return!0;if("object"===typeof e&&"smooth"===e.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+e.behavior+" is not a valid value for enumeration ScrollBehavior.")}function u(e,t){return"Y"===t?e.clientHeight+r<e.scrollHeight:"X"===t?e.clientWidth+r<e.scrollWidth:void 0}function p(t,a){var n=e.getComputedStyle(t,null)["overflow"+a];return"auto"===n||"scroll"===n}function d(e){var t=u(e,"Y")&&p(e,"Y"),a=u(e,"X")&&p(e,"X");return t||a}function m(e){while(e!==t.body&&!1===d(e))e=e.parentNode||e.host;return e}function h(t){var a,s,r,i=o(),c=(i-t.startTime)/n;c=c>1?1:c,a=l(c),s=t.startX+(t.x-t.startX)*a,r=t.startY+(t.y-t.startY)*a,t.method.call(t.scrollable,s,r),s===t.x&&r===t.y||e.requestAnimationFrame(h.bind(e,t))}function v(a,n,r){var i,l,f,u,p=o();a===t.body?(i=e,l=e.scrollX||e.pageXOffset,f=e.scrollY||e.pageYOffset,u=s.scroll):(i=a,l=a.scrollLeft,f=a.scrollTop,u=c),h({scrollable:i,method:u,startTime:p,startX:l,startY:f,x:n,y:r})}}e.exports={polyfill:t}})()},"849b":function(e,t,a){e.exports=a.p+"img/to-top.f8c6e860.png"},"86e1":function(e,t,a){"use strict";var n=a("18df"),s=a.n(n);s.a},a9e3:function(e,t,a){"use strict";var n=a("83ab"),s=a("da84"),o=a("94ca"),r=a("6eeb"),i=a("5135"),c=a("c6b6"),l=a("7156"),f=a("c04e"),u=a("d039"),p=a("7c73"),d=a("241c").f,m=a("06cf").f,h=a("9bf2").f,v=a("58a8").trim,g="Number",b=s[g],w=b.prototype,_=c(p(w))==g,y=function(e){var t,a,n,s,o,r,i,c,l=f(e,!1);if("string"==typeof l&&l.length>2)if(l=v(l),t=l.charCodeAt(0),43===t||45===t){if(a=l.charCodeAt(2),88===a||120===a)return NaN}else if(48===t){switch(l.charCodeAt(1)){case 66:case 98:n=2,s=49;break;case 79:case 111:n=8,s=55;break;default:return+l}for(o=l.slice(2),r=o.length,i=0;i<r;i++)if(c=o.charCodeAt(i),c<48||c>s)return NaN;return parseInt(o,n)}return+l};if(o(g,!b(" 0o1")||!b("0b1")||b("+0x1"))){for(var k,C=function(e){var t=arguments.length<1?0:e,a=this;return a instanceof C&&(_?u((function(){w.valueOf.call(a)})):c(a)!=g)?l(new b(y(t)),a,C):y(t)},x=n?d(b):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),S=0;x.length>S;S++)i(b,k=x[S])&&!i(C,k)&&h(C,k,m(b,k));C.prototype=w,w.constructor=C,r(s,g,C)}},db9c:function(e,t,a){},e7e5:function(e,t,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")},f04a:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"main"},[n("van-nav-bar",{attrs:{title:e.title,"left-arrow":"",fixed:""},on:{"click-left":e.goBack}}),e.isShowCheckPassword?e._e():[n("div",{staticClass:"main-header"},[n("div",{staticClass:"main-header-row"},[n("van-image",{staticClass:"main-header-row-avater",attrs:{src:e.phoneUserInfo.headimg,round:""}}),n("div",{staticClass:"main-header-row-center"},[n("p",{staticClass:"main-header-row-center-name"},[e._v(e._s(e.phoneUserInfo.nickname))]),n("p",{staticClass:"main-header-row-center-phone"},[e._v("手机号："+e._s(e.phoneUserInfo.mobile)+" "),n("img",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.phoneUserInfo.mobile,expression:"phoneUserInfo.mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.onCopied,expression:"onCopied",arg:"success"}],staticClass:"main-header-row-center-phone-copy",attrs:{src:a("3814")}})]),n("p",{staticClass:"main-header-row-center-referrer"},[e._v("推荐人："+e._s(e.phoneUserInfo.r_mobile))])])],1)]),n("van-tabs",{model:{value:e.active,callback:function(t){e.active=t},expression:"active"}},[n("van-tab",{attrs:{title:"店铺概况"}},[n("div",{staticClass:"one-statistics"},[n("div",{staticClass:"one-statistics-header"},[n("span",{staticClass:"one-statistics-header-left"},[e._v("业绩统计")]),n("div",{staticClass:"one-statistics-header-right"},[n("span",{staticClass:"one-statistics-header-right-time",class:{"one-statistics-header-right-time-active":"yesterday"==e.type},on:{click:function(t){return e.changeStatistics("yesterday")}}},[e._v("昨天")]),n("span",{staticClass:"one-statistics-header-right-time",class:{"one-statistics-header-right-time-active":"last_week"==e.type},on:{click:function(t){return e.changeStatistics("last_week")}}},[e._v("近7天")]),n("span",{staticClass:"one-statistics-header-right-time",class:{"one-statistics-header-right-time-active":"last_month"==e.type},on:{click:function(t){return e.changeStatistics("last_month")}}},[e._v("近30天")])])]),n("div",{staticClass:"one-statistics-info"},e._l(e.infoList,(function(t,a){return n("div",{key:a,staticClass:"one-statistics-info-item"},[n("span",{staticClass:"one-statistics-info-item-number"},[e._v(e._s(t.number))]),n("span",{staticClass:"one-statistics-info-item-text"},[e._v(e._s(t.text))]),t.tip?n("span",{staticClass:"one-statistics-info-item-tip",style:t.direction?"color:red;":""},[e._v(e._s(t.tip))]):e._e()])})),0),n("p",{staticClass:"one-statistics-query",on:{click:e.viewObvious}},[e._v("查看业绩明细")])]),n("div",{staticClass:"one-products"},[n("p",{staticClass:"one-products-header"},[e._v("商品销量排行")]),n("div",{staticClass:"one-products-list"},[n("van-list",{attrs:{finished:e.finished,"finished-text":"没有更多了"},on:{load:e.onLoad},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,(function(t,a){return n("div",{key:a,staticClass:"one-products-list-one"},[n("van-image",{staticClass:"one-products-list-one-img",attrs:{src:t.goods_image},scopedSlots:e._u([{key:"loading",fn:function(){return[n("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),n("div",{staticClass:"one-products-list-one-right"},[n("p",{staticClass:"one-products-list-one-right-title"},[e._v(e._s(t.goods_name))]),n("p",{staticClass:"one-products-list-one-right-price"},[e._v(e._s(t.price))])])],1)})),0)],1)])]),n("van-tab",{attrs:{title:"我的粉丝"}},[n("div",{staticClass:"two-fans"},[n("div",{staticClass:"two-fans-search"},[n("van-field",{staticClass:"two-fans-search-input",attrs:{placeholder:"输入粉丝手机号搜索",clearable:""},model:{value:e.fansPhone,callback:function(t){e.fansPhone=t},expression:"fansPhone"}}),n("span",{staticClass:"two-fans-search-op",on:{click:e.fansSearch}},[e._v("搜索")])],1),n("div",{staticClass:"two-fans-list"},[n("van-list",{attrs:{finished:e.fansFinished,"finished-text":"没有更多了"},on:{load:e.onLoadFans},model:{value:e.fansLoading,callback:function(t){e.fansLoading=t},expression:"fansLoading"}},e._l(e.fansList,(function(t,a){return n("div",{key:a,staticClass:"two-fans-list-one"},[n("div",{staticClass:"two-fans-list-one-left"},[n("van-image",{staticClass:"two-fans-list-one-left-img",attrs:{src:t.headimg,round:""},scopedSlots:e._u([{key:"loading",fn:function(){return[n("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),n("div",{staticClass:"two-fans-list-one-left-info"},[n("p",{staticClass:"two-fans-list-one-left-info-name"},[e._v(e._s(t.nickname))]),n("p",{staticClass:"two-fans-list-one-left-info-time"},[e._v(e._s(t.reg_time)+" 注册")])])],1),n("div",{staticClass:"two-fans-list-one-right"},[n("p",{staticClass:"two-fans-list-one-right-price"},[e._v(e._s(t.sale_price))]),n("p",{staticClass:"two-fans-list-one-right-op",on:{click:function(a){return e.toFans(a,t.member_id)}}},[e._v("消费情况 >")])])])})),0)],1)])]),"1"==e.phoneUserInfo.is_group_manager?n("van-tab",{attrs:{title:"团队业绩"}},[n("div",{staticClass:"three-team"},[n("div",{staticClass:"three-team-search"},[n("van-field",{staticClass:"three-team-search-input",attrs:{placeholder:"输入用户手机号搜索",clearable:""},model:{value:e.performancePhone,callback:function(t){e.performancePhone=t},expression:"performancePhone"}}),n("span",{staticClass:"three-team-search-op",on:{click:e.performanceSearch}},[e._v("搜索")])],1),n("div",{staticClass:"three-team-list"},[n("van-list",{attrs:{finished:e.performanceFinished,"finished-text":"没有更多了"},on:{load:e.onLoadPerformance},model:{value:e.performanceLoading,callback:function(t){e.performanceLoading=t},expression:"performanceLoading"}},e._l(e.performanceList,(function(t,a){return n("div",{key:a,staticClass:"three-team-list-one"},[n("div",{staticClass:"three-team-list-one-left"},[n("van-image",{staticClass:"three-team-list-one-left-img",attrs:{src:t.headimg,round:""},scopedSlots:e._u([{key:"loading",fn:function(){return[n("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)}),n("div",{staticClass:"three-team-list-one-left-info"},[n("p",{staticClass:"three-team-list-one-left-info-name"},[e._v(e._s(t.nickname))]),n("p",{staticClass:"three-team-list-one-left-info-time"},[e._v(e._s(t.create_name))])])],1),n("div",{staticClass:"three-team-list-one-right"},[n("p",{staticClass:"three-team-list-one-right-price"},[e._v(e._s(t.fun_num)+" "),n("span",{staticClass:"three-team-list-one-right-price-tip"},[e._v("粉丝")])]),n("p",{staticClass:"three-team-list-one-right-op",on:{click:function(a){return e.toSubShopManagement(a,t.member_id,t.site_id)}}},[e._v("店铺概况 >")])])])})),0)],1)])]):e._e()],1)],n("van-dialog",{attrs:{"show-cancel-button":"","close-on-click-overlay":!1,confirmButtonText:"确认验证",cancelButtonText:"返回个人中心",beforeClose:e.passwordConfirm},model:{value:e.isShowCheckPassword,callback:function(t){e.isShowCheckPassword=t},expression:"isShowCheckPassword"}},[n("div",{staticClass:"check-password"},[n("van-image",{staticClass:"check-password-img",attrs:{src:e.phoneUserInfo.headimg,round:""}}),n("p",{staticClass:"check-password-name"},[e._v(e._s(e.phoneUserInfo.nickname))]),n("van-field",{staticClass:"check-password-input",attrs:{type:"password",border:!1},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}}),n("p",{staticClass:"check-password-tip"},[e._v("请输入您的支付密码验证")])],1)]),n("van-number-keyboard",{attrs:{show:e.showKeyboard,"z-index":"99999"},on:{blur:function(t){e.showKeyboard=!1}},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}}),n("to-top")],2)},s=[],o=(a("99af"),a("96cf"),a("1da1")),r=(a("e7e5"),a("d399")),i=a("5530"),c=a("2f62"),l=a("6917"),f=(a("0866"),a("c391")),u=a("ce3a"),p=a("5d30"),d=(a("5118"),{name:"shopManagement",data:function(){return{reference:0,title:"店铺管理",phoneUserInfo:{},type:"last_week",active:0,infoList:[{number:0,text:"订单量"},{number:0,text:"店铺销售额"},{number:0,text:"售后单量"},{number:0,text:"合计省赚"},{number:0,text:"粉丝总数",tip:"同比0",direction:!0},{number:0,text:"团队店铺",tip:"同比0",direction:!0}],list:[],loading:!1,finished:!1,page:1,page_count:1,fansPhone:"",fansPage:1,fans_page_count:1,fansList:[],fansLoading:!1,fansFinished:!1,performancePhone:"",performanceList:[],performancePage:1,performance_page_count:1,performanceLoading:!1,performanceFinished:!1,isShowCheckPassword:!1,isCheckPasswordSuccess:!1,password:"",showKeyboard:!1}},computed:Object(i["a"])({},Object(c["b"])(["token"])),components:{toTop:p["a"]},methods:{goBack:function(){this.reference?this.$router.back():Object(l["a"])("0")},viewObvious:function(){this.$router.push({name:"performanceCenter",query:{reference:1}})},onCopied:function(e){Object(r["a"])("复制成功")},getPhoneUserInfo:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n,s,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),n={token:e.token},t.prev=2,t.next=5,e.$axios.post(Object(f["a"])(u["a"].ShopInfoUrl),n);case 5:s=t.sent,o=s.data,0!=o.code?Object(r["a"])(o.message):(!o.data.member_info||(e.phoneUserInfo=o.data.member_info),a.clear()),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),Object(r["a"])(t.t0.message);case 13:case"end":return t.stop()}}),t,null,[[2,10]])})))()},getSaleStatistics:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n,s,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),n={token:e.token,type:e.type},t.prev=2,t.next=5,e.$axios.post(Object(f["a"])(u["a"].saleStatisticsUrl),n);case 5:s=t.sent,o=s.data,0!=o.code?Object(r["a"])(o.message):(a.clear(),e.infoList=[{number:o.data.order_pay_nums,text:"订单量"},{number:o.data.sale_price,text:"店铺销售额"},{number:o.data.after_sale_nums,text:"售后单量"},{number:o.data.income_money,text:"合计省赚"},{number:o.data.fans_nums,text:"粉丝总数",tip:"".concat(parseFloat(o.data.fans_nums_compare)>0?"+":"").concat(o.data.fans_nums_compare),direction:parseFloat(o.data.fans_nums_compare)>0},{number:o.data.team_shop_nums,text:"团队店铺",tip:"".concat(parseFloat(o.data.team_shop_compare)>0?"+":"").concat(o.data.team_shop_compare),direction:parseFloat(o.data.team_shop_compare)>0}]),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),Object(r["a"])(t.t0.message);case 13:case"end":return t.stop()}}),t,null,[[2,10]])})))()},changeStatistics:function(e){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.type==e){a.next=4;break}return t.type=e,a.next=4,t.getSaleStatistics();case 4:case"end":return a.stop()}}),a)})))()},onLoad:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={token:e.token,page:e.page},t.prev=1,t.next=4,e.$axios.post(Object(f["a"])(u["a"].SaleGoodsOrderUrl),a);case 4:n=t.sent,s=n.data,0!=s.code?Object(r["a"])(s.message):(e.list=e.list.concat(s.data.list),e.page_count=s.data.page_count,e.loading=!1,e.page>=e.page_count?e.finished=!0:e.page+=1),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),Object(r["a"])(t.t0.message);case 12:case"end":return t.stop()}}),t,null,[[1,9]])})))()},onLoadFans:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={token:e.token,page:e.fansPage,mobile:e.fansPhone},t.prev=1,t.next=4,e.$axios.post(Object(f["a"])(u["a"].childListUrl),a);case 4:n=t.sent,s=n.data,0!=s.code?Object(r["a"])(s.message):(e.fansList=e.fansList.concat(s.data.list),e.fans_page_count=s.data.page_count,e.fansLoading=!1,e.fansPage>=e.fans_page_count?e.fansFinished=!0:e.fansPage+=1),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),Object(r["a"])(t.t0.message);case 12:case"end":return t.stop()}}),t,null,[[1,9]])})))()},fansSearch:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.fansPage=1,e.fans_page_count=1,e.fansList=[],e.fansLoading=!1,e.fansFinished=!1;case 5:case"end":return t.stop()}}),t)})))()},onLoadPerformance:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={token:e.token,page:e.performancePage,mobile:e.performancePhone},t.prev=1,t.next=4,e.$axios.post(Object(f["a"])(u["a"].GroupPerformanceUrl),a);case 4:n=t.sent,s=n.data,0!=s.code?Object(r["a"])(s.message):(e.performanceList=e.performanceList.concat(s.data.list),e.performance_page_count=s.data.page_count,e.performanceLoading=!1,e.performancePage>=e.performance_page_count?e.performanceFinished=!0:e.performancePage+=1),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),Object(r["a"])(t.t0.message);case 12:case"end":return t.stop()}}),t,null,[[1,9]])})))()},performanceSearch:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.performancePage=1,e.performance_page_count=1,e.performanceList=[],e.performanceLoading=!1,e.performanceFinished=!1;case 5:case"end":return t.stop()}}),t)})))()},toFans:function(e,t){this.$router.push({name:"myFans",query:{reference:1,member_id_turn:t}})},toSubShopManagement:function(e,t,a){this.$router.push({name:"subShopManagement",query:{reference:1,member_id_turn:t,shop_id:a}})},passwordConfirm:function(e,t){var a=this;return Object(o["a"])(regeneratorRuntime.mark((function n(){var s,o,i,c;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!=e){n.next=17;break}return s=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),o={token:a.token,pay_password:a.password},n.prev=3,n.next=6,a.$axios.post(Object(f["a"])(u["a"].PayCheckUrl),o);case 6:i=n.sent,c=i.data,0==c.code&&1==c.data.is_pass?(t(),window.localStorage.setItem("payPasswordTime",new Date),Object(r["a"])(c.message)):(s.clear(),Object(r["a"])(c.message),t(!1)),n.next=15;break;case 11:n.prev=11,n.t0=n["catch"](3),Object(r["a"])(n.t0.message),t(!1);case 15:n.next=19;break;case 17:t(!1),a.goBack();case 19:case"end":return n.stop()}}),n,null,[[3,11]])})))()}},created:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.reference=e.$route.query.reference?e.$route.query.reference:0,t.next=3,e.getPhoneUserInfo();case 3:return t.next=5,e.getSaleStatistics();case 5:a=window.localStorage.getItem("payPasswordTime"),a?(n=((new Date).getTime()-new Date(a).getTime())/36e5,e.isShowCheckPassword=n>=1):e.isShowCheckPassword=!0;case 7:case"end":return t.stop()}}),t)})))()}}),m=d,h=(a("088a"),a("2877")),v=Object(h["a"])(m,n,s,!1,null,"61f15e94",null);t["default"]=v.exports}}]);