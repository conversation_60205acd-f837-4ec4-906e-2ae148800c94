(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["inviteShopkeeper"],{2619:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"shopkeeper"},[i("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),t.list.length>0?i("div",{staticClass:"group"},[i("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.getData},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,(function(e,s){return i("div",{key:s,staticClass:"item"},[i("div",{staticClass:"head"},[i("van-image",{attrs:{src:e.avatar_url},scopedSlots:t._u([{key:"error",fn:function(){return[i("img",{attrs:{src:a("f79e")}})]},proxy:!0},{key:"loading",fn:function(){return[i("van-loading",{attrs:{type:"spinner",size:"20"}})]},proxy:!0}],null,!0)})],1),i("div",{staticClass:"info"},[i("div",{staticClass:"title"},[t._v(t._s(e.shop_name))]),i("div",{staticClass:"info-text"},[i("div",{staticClass:"level"},[t._v("LV."+t._s(e.level))]),i("div",{staticClass:"today"},[t._v("今日卖货"+t._s(e.today_num)+"件")]),i("div",{staticClass:"total"},[t._v("累计卖货"+t._s(e.total_num)+"件")])]),i("div",{staticClass:"time"},[t._v(t._s(e.join_date))])])])})),0)],1):i("div",{staticClass:"group-default"},[i("img",{attrs:{src:a("f1b1")}}),i("p",[t._v("暂无数据")])])],1)},s=[],n=(a("d3b7"),a("e7e5"),a("d399")),o=(a("96cf"),a("1da1")),r=a("5530"),c=a("6917"),l=a("ce3a"),d=a("c391"),p=a("2f62"),u={name:"inviteShopkeeper",components:{},data:function(){return{title:"",shop_id:"",xm_uid:null,isTransform:!1,type:1,page:1,page_size:10,loading:!1,finished:!1,list:[]}},computed:Object(r["a"])({},Object(p["b"])(["token"])),methods:{getData:function(){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isTransform){e.next=9;break}return e.next=3,t.$axios.post(Object(d["a"])(l["a"].transformParam),{shop_id:0,xm_uid:t.xm_uid});case 3:if(a=e.sent,0==a.data.code){e.next=7;break}return Object(n["a"])(result.message),e.abrupt("return");case 7:t.shop_id=a.data.data.shop_id,t.isTransform=!0;case 9:i={shop_id:t.shop_id,type:t.type,page:t.page,page_size:t.page_size},t.$axios.post(Object(d["a"])(l["a"].appRelation),i).then((function(e){var a=e.data;if(0!=a.code)Object(n["a"])(a.message);else{console.log(a.data.list);for(var i=0;i<a.data.list.length;i++)t.$set(t.list,t.list.length,a.data.list[i]);t.loading=!1,t.page_size>a.data.list.length&&(t.finished=!0),t.page=t.page+1}})).catch((function(e){Object(n["a"])(e.message),t.loading=!1,t.page=1,t.page_size>result.data.list.length&&(t.finished=!0)})).finally((function(){}));case 11:case"end":return e.stop()}}),e)})))()},goBack:function(){Object(c["a"])("0")},toInvite:function(){this.$router.push({name:"invitePoster",query:{type:this.type}})}},created:function(){this.type=this.$route.query.type,this.shop_id=this.$route.query.shop_id,this.xm_uid=this.$route.query.xm_uid,1==this.type?this.title="店主":2==this.type?this.title="VIP店主":this.title="店主",this.getData()}},f=u,h=(a("6a80"),a("2877")),g=Object(h["a"])(f,i,s,!1,null,"f3d71a32",null);e["default"]=g.exports},"6a80":function(t,e,a){"use strict";var i=a("6f8c"),s=a.n(i);s.a},"6f8c":function(t,e,a){},e7e5:function(t,e,a){"use strict";a("68ef"),a("a71a"),a("9d70"),a("3743"),a("4d75"),a("e3b3"),a("b258")},f1b1:function(t,e,a){t.exports=a.p+"img/img-shopper-default.cb55b4c9.png"},f79e:function(t,e,a){t.exports=a.p+"img/img-shopper-head.bef43d9e.png"}}]);