(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["learnArticleDetails"],{"0459":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"main"},[n("van-nav-bar",{attrs:{title:e.title,"right-text":"分享","left-arrow":"",fixed:""},on:{"click-right":e.popupShare,"click-left":e.goBack}}),n("div",{staticClass:"article"},[e.articleDict.hasOwnProperty("learning_content")?n("div",{domProps:{innerHTML:e._s(e.articleDict.learning_content)}}):e._e(),e.video_link?n("video-play",{staticClass:"helpContent--video",attrs:{width:"22.1875rem",height:"12.5rem","video-url":e.video_link,poster:e.video_cover,options:{hideVideoControlsOnLoad:!0}}}):e._e()],1)],1)},o=[],r=(n("d3b7"),n("9911"),n("e7e5"),n("d399")),a=n("6917"),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{style:{width:e.width,height:e.height}},[e.videoUrl?n("video",{attrs:{width:"100%",height:"100%",poster:e.poster,id:e.videoId}},[n("source",{attrs:{src:e.videoUrl,type:"video/mp4"}})]):e._e()])},l=[],d=(n("c975"),n("bf28"),n("0e1d"),n("2a9b")),u={name:"videoPlay",props:{width:{default:"auto",type:String},height:{default:"auto",type:String},videoUrl:{required:!0,type:String},poster:{type:String},options:{type:Object,default:function(){return{}}}},data:function(){return{videoId:""}},methods:{initVideo:function(){var e=this,t=-1,n=-1,i=parseFloat(getComputedStyle(document.documentElement).fontSize);-1!=this.height.indexOf("px")?t=parseFloat(this.height):-1!=this.height.indexOf("rem")&&(t=parseFloat(this.height)*i),-1!=this.width.indexOf("px")?n=parseFloat(this.width):-1!=this.width.indexOf("rem")&&(n=parseFloat(this.width)*i);var o={features:["playpause","current","progress","duration","volume","fullscreen","airplay"],videoHeight:t,videoWidth:n};o=Object.assign(o,this.options),setTimeout((function(){new MediaElementPlayer(e.videoId,o)}),500)}},created:function(){this.videoId=Object(d["m"])(16)},mounted:function(){this.videoUrl?this.initVideo():console.log("video not url")},watch:{videoUrl:function(e,t){e?this.initVideo():console.log("watch video not url")}}},c=u,f=(n("a4bd"),n("2877")),p=Object(f["a"])(c,s,l,!1,null,"3013c78f",null),m=p.exports,h=n("ce3a"),v=n("c391"),g={name:"learnArticleDetails",data:function(){return{id:null,title:"",content:'<p>测撒谎的吉萨大就撒开绿灯就立刻升级的撒</p><img src="https://dev.xianmai88.com/attachment/goods/2020/08/04/a91ce22205730a7ee10699414bc30616.jpg"/><img src="https://dev.xianmai88.com/attachment/goods/2020/08/04/a91ce22205730a7ee10699414bc30616.jpg"/><img src="https://dev.xianmai88.com/attachment/goods/2020/08/04/a91ce22205730a7ee10699414bc30616.jpg"/>',articleDict:{},video_link:"",video_cover:""}},components:{videoPlay:m},methods:{goBack:function(){Object(a["a"])("0")},popupShare:function(){var e={title:this.articleDict.title,text:this.articleDict.intro,image:this.articleDict.icon,url:this.articleDict.link,platform:["wechat","timeline"]};Object(a["i"])(e)},getData:function(){var e=this,t=r["a"].loading({duration:0,forbidClick:!0,message:"加载中..."});this.$axios.get(Object(v["a"])(h["a"].learningDetail)+"?id=".concat(this.id)).then((function(n){var i=n.data;0!=i.code?Object(r["a"])(i.message):(e.articleDict=i.data,e.title=e.articleDict.title,e.video_link=i.data.video_link,e.video_cover=i.data.video_cover,t.clear())})).catch((function(e){Object(r["a"])("获取数据失败！")})).finally((function(e){}))}},created:function(){this.id=this.$route.query.id,this.getData()}},y=g,E=(n("6e4d"),Object(f["a"])(y,i,o,!1,null,"54603cfc",null));t["default"]=E.exports},"0e1d":function(e,t,n){e.exports=n("7ac6")},4219:function(e,t,n){},"6e4d":function(e,t,n){"use strict";var i=n("4219"),o=n.n(i);o.a},"7ac6":function(e,t,n){(function(e){var t;
/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, John Dyer (http://j.hn/)
 * License: MIT
 *
 */
(function(){function e(n,i,o){function r(s,l){if(!i[s]){if(!n[s]){var d="function"==typeof t&&t;if(!l&&d)return t(s,!0);if(a)return a(s,!0);var u=new Error("Cannot find module '"+s+"'");throw u.code="MODULE_NOT_FOUND",u}var c=i[s]={exports:{}};n[s][0].call(c.exports,(function(e){var t=n[s][1][e];return r(t||e)}),c,c.exports,e,n,i,o)}return i[s].exports}for(var a="function"==typeof t&&t,s=0;s<o.length;s++)r(o[s]);return r}return e})()({1:[function(e,t,n){},{}],2:[function(t,n,i){(function(e){var i,o="undefined"!==typeof e?e:"undefined"!==typeof window?window:{},r=t(1);"undefined"!==typeof document?i=document:(i=o["__GLOBAL_DOCUMENT_CACHE@4"],i||(i=o["__GLOBAL_DOCUMENT_CACHE@4"]=r)),n.exports=i}).call(this,"undefined"!==typeof e?e:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{1:1}],3:[function(t,n,i){(function(e){var t;t="undefined"!==typeof window?window:"undefined"!==typeof e?e:"undefined"!==typeof self?self:{},n.exports=t}).call(this,"undefined"!==typeof e?e:"undefined"!==typeof self?self:"undefined"!==typeof window?window:{})},{}],4:[function(e,t,n){(function(e){var n=setTimeout;function i(){}function o(e,t){return function(){e.apply(t,arguments)}}function r(e){if("object"!==typeof this)throw new TypeError("Promises must be constructed via new");if("function"!==typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],c(e,this)}function a(e,t){while(3===e._state)e=e._value;0!==e._state?(e._handled=!0,r._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var i;try{i=n(e._value)}catch(o){return void l(t.promise,o)}s(t.promise,i)}else(1===e._state?s:l)(t.promise,e._value)}))):e._deferreds.push(t)}function s(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"===typeof t||"function"===typeof t)){var n=t.then;if(t instanceof r)return e._state=3,e._value=t,void d(e);if("function"===typeof n)return void c(o(n,t),e)}e._state=1,e._value=t,d(e)}catch(i){l(e,i)}}function l(e,t){e._state=2,e._value=t,d(e)}function d(e){2===e._state&&0===e._deferreds.length&&r._immediateFn((function(){e._handled||r._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)a(e,e._deferreds[t]);e._deferreds=null}function u(e,t,n){this.onFulfilled="function"===typeof e?e:null,this.onRejected="function"===typeof t?t:null,this.promise=n}function c(e,t){var n=!1;try{e((function(e){n||(n=!0,s(t,e))}),(function(e){n||(n=!0,l(t,e))}))}catch(i){if(n)return;n=!0,l(t,i)}}r.prototype["catch"]=function(e){return this.then(null,e)},r.prototype.then=function(e,t){var n=new this.constructor(i);return a(this,new u(e,t,n)),n},r.all=function(e){var t=Array.prototype.slice.call(e);return new r((function(e,n){if(0===t.length)return e([]);var i=t.length;function o(r,a){try{if(a&&("object"===typeof a||"function"===typeof a)){var s=a.then;if("function"===typeof s)return void s.call(a,(function(e){o(r,e)}),n)}t[r]=a,0===--i&&e(t)}catch(l){n(l)}}for(var r=0;r<t.length;r++)o(r,t[r])}))},r.resolve=function(e){return e&&"object"===typeof e&&e.constructor===r?e:new r((function(t){t(e)}))},r.reject=function(e){return new r((function(t,n){n(e)}))},r.race=function(e){return new r((function(t,n){for(var i=0,o=e.length;i<o;i++)e[i].then(t,n)}))},r._immediateFn="function"===typeof setImmediate&&function(e){setImmediate(e)}||function(e){n(e,0)},r._unhandledRejectionFn=function(e){"undefined"!==typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},r._setImmediateFn=function(e){r._immediateFn=e},r._setUnhandledRejectionFn=function(e){r._unhandledRejectionFn=e},"undefined"!==typeof t&&t.exports?t.exports=r:e.Promise||(e.Promise=r)})(this)},{}],5:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(7),r=l(o),a=e(15),s=e(27);function l(e){return e&&e.__esModule?e:{default:e}}var d={lang:"en",en:a.EN,language:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(null!==t&&void 0!==t&&t.length){if("string"!==typeof t[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2,3}((\-|_)[a-z]{2})?$/i.test(t[0]))throw new TypeError("Language code must have format 2-3 letters and. optionally, hyphen, underscore followed by 2 more letters");d.lang=t[0],void 0===d[t[0]]?(t[1]=null!==t[1]&&void 0!==t[1]&&"object"===i(t[1])?t[1]:{},d[t[0]]=(0,s.isObjectEmpty)(t[1])?a.EN:t[1]):null!==t[1]&&void 0!==t[1]&&"object"===i(t[1])&&(d[t[0]]=t[1])}return d.lang},t:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"===typeof e&&e.length){var n=void 0,o=void 0,r=d.language(),a=function(e,t,n){if("object"!==("undefined"===typeof e?"undefined":i(e))||"number"!==typeof t||"number"!==typeof n)return e;var o=function(){return[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10===1&&(arguments.length<=0?void 0:arguments[0])%100!==11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>0&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10===1&&(arguments.length<=0?void 0:arguments[0])%100!==11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10===1&&(arguments.length<=0?void 0:arguments[0])%100!==11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])>=2&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100===1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100===2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100===3||(arguments.length<=0?void 0:arguments[0])%100===4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])>6&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100>=3&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:(arguments.length<=0?void 0:arguments[0])%100>=11?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>1&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100>10&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10===1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10===2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10===1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}]}();return o[n].apply(null,[t].concat(e))};return void 0!==d[r]&&(n=d[r][e],null!==t&&"number"===typeof t&&(o=d[r]["mejs.plural-form"],n=a.apply(null,[n,t,o]))),!n&&d.en&&(n=d.en[e],null!==t&&"number"===typeof t&&(o=d.en["mejs.plural-form"],n=a.apply(null,[n,t,o]))),n=n||e,null!==t&&"number"===typeof t&&(n=n.replace("%1",t)),(0,s.escapeHTML)(n)}return e}};r.default.i18n=d,"undefined"!==typeof mejsL10n&&r.default.i18n.language(mejsL10n.language,mejsL10n.strings),n.default=d},{15:15,27:27,7:7}],6:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(3),r=m(o),a=e(2),s=m(a),l=e(7),d=m(l),u=e(27),c=e(28),f=e(8),p=e(25);function m(e){return e&&e.__esModule?e:{default:e}}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var v=function e(t,n,o){var a=this;h(this,e);var l=this;o=Array.isArray(o)?o:null,l.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain"},n=Object.assign(l.defaults,n),l.mediaElement=s.default.createElement(n.fakeNodeName);var m=t,v=!1;if("string"===typeof t?l.mediaElement.originalNode=s.default.getElementById(t):(l.mediaElement.originalNode=t,m=t.id),void 0===l.mediaElement.originalNode||null===l.mediaElement.originalNode)return null;l.mediaElement.options=n,m=m||"mejs_"+Math.random().toString().slice(2),l.mediaElement.originalNode.setAttribute("id",m+"_from_mejs");var g=l.mediaElement.originalNode.tagName.toLowerCase();["video","audio"].indexOf(g)>-1&&!l.mediaElement.originalNode.getAttribute("preload")&&l.mediaElement.originalNode.setAttribute("preload","none"),l.mediaElement.originalNode.parentNode.insertBefore(l.mediaElement,l.mediaElement.originalNode),l.mediaElement.appendChild(l.mediaElement.originalNode);var y=function(e,t){if("https:"===r.default.location.protocol&&0===e.indexOf("http:")&&p.IS_IOS&&d.default.html5media.mediaTypes.indexOf(t)>-1){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var t=r.default.URL||r.default.webkitURL,n=t.createObjectURL(this.response);return l.mediaElement.originalNode.setAttribute("src",n),n}return e},n.open("GET",e),n.responseType="blob",n.send()}return e},E=void 0;if(null!==o)E=o;else if(null!==l.mediaElement.originalNode)switch(E=[],l.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":E.push({type:"",src:l.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var b=l.mediaElement.originalNode.children.length,S=l.mediaElement.originalNode.getAttribute("src");if(S){var x=l.mediaElement.originalNode,w=(0,c.formatType)(S,x.getAttribute("type"));E.push({type:w,src:y(S,w)})}for(var P=0;P<b;P++){var T=l.mediaElement.originalNode.children[P];if("source"===T.tagName.toLowerCase()){var k=T.getAttribute("src"),C=(0,c.formatType)(k,T.getAttribute("type"));E.push({type:C,src:y(k,C)})}}break}l.mediaElement.id=m,l.mediaElement.renderers={},l.mediaElement.events={},l.mediaElement.promises=[],l.mediaElement.renderer=null,l.mediaElement.rendererName=null,l.mediaElement.changeRenderer=function(e,t){var n=a,i=Object.keys(t[0]).length>2?t[0]:t[0].src;if(void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&n.mediaElement.renderer.name===e)return n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.show(),n.mediaElement.renderer.setSrc(i),!0;void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&(n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.hide());var o=n.mediaElement.renderers[e],r=null;if(void 0!==o&&null!==o)return o.show(),o.setSrc(i),n.mediaElement.renderer=o,n.mediaElement.rendererName=e,!0;for(var s=n.mediaElement.options.renderers.length?n.mediaElement.options.renderers:f.renderer.order,l=0,d=s.length;l<d;l++){var u=s[l];if(u===e){var c=f.renderer.renderers;r=c[u];var p=Object.assign(r.options,n.mediaElement.options);return o=r.create(n.mediaElement,p,t),o.name=e,n.mediaElement.renderers[r.name]=o,n.mediaElement.renderer=o,n.mediaElement.rendererName=e,o.show(),!0}}return!1},l.mediaElement.setSize=function(e,t){void 0!==l.mediaElement.renderer&&null!==l.mediaElement.renderer&&l.mediaElement.renderer.setSize(e,t)},l.mediaElement.generateError=function(e,t){e=e||"",t=Array.isArray(t)?t:[];var n=(0,u.createEvent)("error",l.mediaElement);n.message=e,n.urls=t,l.mediaElement.dispatchEvent(n),v=!0};var _=d.default.html5media.properties,N=d.default.html5media.methods,A=function(e,t,n,i){var o=e[t],r=function(){return n.apply(e,[o])},a=function(t){return o=i.apply(e,[t]),o};Object.defineProperty(e,t,{get:r,set:a})},L=function(e){if("src"!==e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1),n=function(){return void 0!==l.mediaElement.renderer&&null!==l.mediaElement.renderer&&"function"===typeof l.mediaElement.renderer["get"+t]?l.mediaElement.renderer["get"+t]():null},i=function(e){void 0!==l.mediaElement.renderer&&null!==l.mediaElement.renderer&&"function"===typeof l.mediaElement.renderer["set"+t]&&l.mediaElement.renderer["set"+t](e)};A(l.mediaElement,e,n,i),l.mediaElement["get"+t]=n,l.mediaElement["set"+t]=i}},F=function(){return void 0!==l.mediaElement.renderer&&null!==l.mediaElement.renderer?l.mediaElement.renderer.getSrc():null},j=function(e){var t=[];if("string"===typeof e)t.push({src:e,type:e?(0,c.getTypeFromFile)(e):""});else if("object"===("undefined"===typeof e?"undefined":i(e))&&void 0!==e.src){var n=(0,c.absolutizeUrl)(e.src),o=e.type,r=Object.assign(e,{src:n,type:""!==o&&null!==o&&void 0!==o||!n?o:(0,c.getTypeFromFile)(n)});t.push(r)}else if(Array.isArray(e))for(var a=0,s=e.length;a<s;a++){var d=(0,c.absolutizeUrl)(e[a].src),p=e[a].type,m=Object.assign(e[a],{src:d,type:""!==p&&null!==p&&void 0!==p||!d?p:(0,c.getTypeFromFile)(d)});t.push(m)}var h=f.renderer.select(t,l.mediaElement.options.renderers.length?l.mediaElement.options.renderers:[]),v=void 0;if(l.mediaElement.paused||null==l.mediaElement.src||""===l.mediaElement.src||(l.mediaElement.pause(),v=(0,u.createEvent)("pause",l.mediaElement),l.mediaElement.dispatchEvent(v)),l.mediaElement.originalNode.src=t[0].src||"",null!==h||!t[0].src){var g=!(null==t[0].src||""===t[0].src);return g?l.mediaElement.changeRenderer(h.rendererName,t):null}l.mediaElement.generateError("No renderer found",t)},I=function(e,t){try{if("play"!==e||"native_dash"!==l.mediaElement.rendererName&&"native_hls"!==l.mediaElement.rendererName&&"vimeo_iframe"!==l.mediaElement.rendererName)l.mediaElement.renderer[e](t);else{var n=l.mediaElement.renderer[e](t);n&&"function"===typeof n.then&&n.catch((function(){l.mediaElement.paused&&setTimeout((function(){var e=l.mediaElement.renderer.play();void 0!==e&&e.catch((function(){l.mediaElement.renderer.paused||l.mediaElement.renderer.pause()}))}),150)}))}}catch(i){l.mediaElement.generateError(i,E)}},M=function(e){l.mediaElement[e]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return void 0!==l.mediaElement.renderer&&null!==l.mediaElement.renderer&&"function"===typeof l.mediaElement.renderer[e]&&(l.mediaElement.promises.length?Promise.all(l.mediaElement.promises).then((function(){I(e,n)})).catch((function(e){l.mediaElement.generateError(e,E)})):I(e,n)),null}};A(l.mediaElement,"src",F,j),l.mediaElement.getSrc=F,l.mediaElement.setSrc=j;for(var O=0,D=_.length;O<D;O++)L(_[O]);for(var V=0,R=N.length;V<R;V++)M(N[V]);return l.mediaElement.addEventListener=function(e,t){l.mediaElement.events[e]=l.mediaElement.events[e]||[],l.mediaElement.events[e].push(t)},l.mediaElement.removeEventListener=function(e,t){if(!e)return l.mediaElement.events={},!0;var n=l.mediaElement.events[e];if(!n)return!0;if(!t)return l.mediaElement.events[e]=[],!0;for(var i=0;i<n.length;i++)if(n[i]===t)return l.mediaElement.events[e].splice(i,1),!0;return!1},l.mediaElement.dispatchEvent=function(e){var t=l.mediaElement.events[e.type];if(t)for(var n=0;n<t.length;n++)t[n].apply(null,[e])},l.mediaElement.destroy=function(){var e=l.mediaElement.originalNode.cloneNode(!0),t=l.mediaElement.parentElement;e.removeAttribute("id"),e.remove(),l.mediaElement.remove(),t.appendChild(e)},E.length&&(l.mediaElement.src=E),l.mediaElement.promises.length?Promise.all(l.mediaElement.promises).then((function(){l.mediaElement.options.success&&l.mediaElement.options.success(l.mediaElement,l.mediaElement.originalNode)})).catch((function(){v&&l.mediaElement.options.error&&l.mediaElement.options.error(l.mediaElement,l.mediaElement.originalNode)})):(l.mediaElement.options.success&&l.mediaElement.options.success(l.mediaElement,l.mediaElement.originalNode),v&&l.mediaElement.options.error&&l.mediaElement.options.error(l.mediaElement,l.mediaElement.originalNode)),l.mediaElement};r.default.MediaElement=v,d.default.MediaElement=v,n.default=v},{2:2,25:25,27:27,28:28,3:3,7:7,8:8}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=e(3),o=r(i);function r(e){return e&&e.__esModule?e:{default:e}}var a={version:"4.2.16",html5media:{properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]}};o.default.mejs=a,n.default=a},{3:3}],8:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.renderer=void 0;var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=e(7),a=s(r);function s(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var d=function(){function e(){l(this,e),this.renderers={},this.order=[]}return o(e,[{key:"add",value:function(e){if(void 0===e.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[e.name]=e,this.order.push(e.name)}},{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=t.length;if(t=t.length?t:this.order,!n){var i=[/^(html5|native)/i,/^flash/i,/iframe$/i],o=function(e){for(var t=0,n=i.length;t<n;t++)if(i[t].test(e))return t;return i.length};t.sort((function(e,t){return o(e)-o(t)}))}for(var r=0,a=t.length;r<a;r++){var s=t[r],l=this.renderers[s];if(null!==l&&void 0!==l)for(var d=0,u=e.length;d<u;d++)if("function"===typeof l.canPlayType&&"string"===typeof e[d].type&&l.canPlayType(e[d].type))return{rendererName:l.name,src:e[d].src}}return null}},{key:"order",set:function(e){if(!Array.isArray(e))throw new TypeError("order must be an array of strings.");this._order=e},get:function(){return this._order}},{key:"renderers",set:function(e){if(null!==e&&"object"!==("undefined"===typeof e?"undefined":i(e)))throw new TypeError("renderers must be an array of objects.");this._renderers=e},get:function(){return this._renderers}}]),e}(),u=n.renderer=new d;a.default.Renderers=u},{7:7}],9:[function(e,t,n){"use strict";var i=e(3),o=g(i),r=e(2),a=g(r),s=e(5),l=g(s),d=e(16),u=g(d),c=e(25),f=v(c),p=e(27),m=e(26),h=e(28);function v(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function g(e){return e&&e.__esModule?e:{default:e}}Object.assign(d.config,{usePluginFullScreen:!0,fullscreenText:null,useFakeFullscreen:!1}),Object.assign(u.default.prototype,{isFullScreen:!1,isNativeFullScreen:!1,isInIframe:!1,isPluginClickThroughCreated:!1,fullscreenMode:"",containerSizeTimeout:null,buildfullscreen:function(e){if(e.isVideo){e.isInIframe=o.default.location!==o.default.parent.location,e.detectFullscreenMode();var t=this,n=(0,p.isString)(t.options.fullscreenText)?t.options.fullscreenText:l.default.t("mejs.fullscreen"),i=a.default.createElement("div");if(i.className=t.options.classPrefix+"button "+t.options.classPrefix+"fullscreen-button",i.innerHTML='<button type="button" aria-controls="'+t.id+'" title="'+n+'" aria-label="'+n+'" tabindex="0"></button>',t.addControlElement(i,"fullscreen"),i.addEventListener("click",(function(){var t=f.HAS_TRUE_NATIVE_FULLSCREEN&&f.IS_FULLSCREEN||e.isFullScreen;t?e.exitFullScreen():e.enterFullScreen()})),e.fullscreenBtn=i,t.options.keyActions.push({keys:[70],action:function(e,t,n,i){i.ctrlKey||"undefined"!==typeof e.enterFullScreen&&(e.isFullScreen?e.exitFullScreen():e.enterFullScreen())}}),t.exitFullscreenCallback=function(n){var i=n.which||n.keyCode||0;t.options.enableKeyboard&&27===i&&(f.HAS_TRUE_NATIVE_FULLSCREEN&&f.IS_FULLSCREEN||t.isFullScreen)&&e.exitFullScreen()},t.globalBind("keydown",t.exitFullscreenCallback),t.normalHeight=0,t.normalWidth=0,f.HAS_TRUE_NATIVE_FULLSCREEN){var r=function(){e.isFullScreen&&(f.isFullScreen()?(e.isNativeFullScreen=!0,e.setControlsSize()):(e.isNativeFullScreen=!1,e.exitFullScreen()))};e.globalBind(f.FULLSCREEN_EVENT_NAME,r)}}},cleanfullscreen:function(e){e.exitFullScreen(),e.globalUnbind("keydown",e.exitFullscreenCallback)},detectFullscreenMode:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName),n="";return f.HAS_TRUE_NATIVE_FULLSCREEN&&t?n="native-native":f.HAS_TRUE_NATIVE_FULLSCREEN&&!t?n="plugin-native":e.usePluginFullScreen&&f.SUPPORT_POINTER_EVENTS&&(n="plugin-click"),e.fullscreenMode=n,n},enterFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(html5|native)/i.test(e.media.rendererName),n=getComputedStyle(e.getElement(e.container));if(e.isVideo)if(!1===e.options.useFakeFullscreen&&f.IS_IOS&&f.HAS_IOS_FULLSCREEN&&"function"===typeof e.media.originalNode.webkitEnterFullscreen&&e.media.originalNode.canPlayType((0,h.getTypeFromFile)(e.media.getSrc())))e.media.originalNode.webkitEnterFullscreen();else{if((0,m.addClass)(a.default.documentElement,e.options.classPrefix+"fullscreen"),(0,m.addClass)(e.getElement(e.container),e.options.classPrefix+"container-fullscreen"),e.normalHeight=parseFloat(n.height),e.normalWidth=parseFloat(n.width),"native-native"!==e.fullscreenMode&&"plugin-native"!==e.fullscreenMode||(f.requestFullScreen(e.getElement(e.container)),e.isInIframe&&setTimeout((function t(){if(e.isNativeFullScreen){var n=.002,i=o.default.innerWidth||a.default.documentElement.clientWidth||a.default.body.clientWidth,r=screen.width,s=Math.abs(r-i),l=r*n;s>l?e.exitFullScreen():setTimeout(t,500)}}),1e3)),e.getElement(e.container).style.width="100%",e.getElement(e.container).style.height="100%",e.containerSizeTimeout=setTimeout((function(){e.getElement(e.container).style.width="100%",e.getElement(e.container).style.height="100%",e.setControlsSize()}),500),t)e.node.style.width="100%",e.node.style.height="100%";else for(var i=e.getElement(e.container).querySelectorAll("embed, object, video"),r=i.length,s=0;s<r;s++)i[s].style.width="100%",i[s].style.height="100%";e.options.setDimensions&&"function"===typeof e.media.setSize&&e.media.setSize(screen.width,screen.height);for(var l=e.getElement(e.layers).children,d=l.length,u=0;u<d;u++)l[u].style.width="100%",l[u].style.height="100%";e.fullscreenBtn&&((0,m.removeClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen"),(0,m.addClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen")),e.setControlsSize(),e.isFullScreen=!0;var c=Math.min(screen.width/e.width,screen.height/e.height),v=e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-text");v&&(v.style.fontSize=100*c+"%",v.style.lineHeight="normal",e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-position").style.bottom=(screen.height-e.normalHeight)/2-e.getElement(e.controls).offsetHeight/2+c+15+"px");var g=(0,p.createEvent)("enteredfullscreen",e.getElement(e.container));e.getElement(e.container).dispatchEvent(g)}},exitFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName);if(e.isVideo){if(clearTimeout(e.containerSizeTimeout),f.HAS_TRUE_NATIVE_FULLSCREEN&&(f.IS_FULLSCREEN||e.isFullScreen)&&f.cancelFullScreen(),(0,m.removeClass)(a.default.documentElement,e.options.classPrefix+"fullscreen"),(0,m.removeClass)(e.getElement(e.container),e.options.classPrefix+"container-fullscreen"),e.options.setDimensions){if(e.getElement(e.container).style.width=e.normalWidth+"px",e.getElement(e.container).style.height=e.normalHeight+"px",t)e.node.style.width=e.normalWidth+"px",e.node.style.height=e.normalHeight+"px";else for(var n=e.getElement(e.container).querySelectorAll("embed, object, video"),i=n.length,o=0;o<i;o++)n[o].style.width=e.normalWidth+"px",n[o].style.height=e.normalHeight+"px";"function"===typeof e.media.setSize&&e.media.setSize(e.normalWidth,e.normalHeight);for(var r=e.getElement(e.layers).children,s=r.length,l=0;l<s;l++)r[l].style.width=e.normalWidth+"px",r[l].style.height=e.normalHeight+"px"}e.fullscreenBtn&&((0,m.removeClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen"),(0,m.addClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen")),e.setControlsSize(),e.isFullScreen=!1;var d=e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-text");d&&(d.style.fontSize="",d.style.lineHeight="",e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-position").style.bottom="");var u=(0,p.createEvent)("exitedfullscreen",e.getElement(e.container));e.getElement(e.container).dispatchEvent(u)}}})},{16:16,2:2,25:25,26:26,27:27,28:28,3:3,5:5}],10:[function(e,t,n){"use strict";var i=e(2),o=c(i),r=e(16),a=c(r),s=e(5),l=c(s),d=e(27),u=e(26);function c(e){return e&&e.__esModule?e:{default:e}}Object.assign(r.config,{playText:null,pauseText:null}),Object.assign(a.default.prototype,{buildplaypause:function(e,t,n,i){var r=this,a=r.options,s=(0,d.isString)(a.playText)?a.playText:l.default.t("mejs.play"),c=(0,d.isString)(a.pauseText)?a.pauseText:l.default.t("mejs.pause"),f=o.default.createElement("div");f.className=r.options.classPrefix+"button "+r.options.classPrefix+"playpause-button "+r.options.classPrefix+"play",f.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+s+'" aria-label="'+c+'" tabindex="0"></button>',f.addEventListener("click",(function(){r.paused?r.play():r.pause()}));var p=f.querySelector("button");function m(e){"play"===e?((0,u.removeClass)(f,r.options.classPrefix+"play"),(0,u.removeClass)(f,r.options.classPrefix+"replay"),(0,u.addClass)(f,r.options.classPrefix+"pause"),p.setAttribute("title",c),p.setAttribute("aria-label",c)):((0,u.removeClass)(f,r.options.classPrefix+"pause"),(0,u.removeClass)(f,r.options.classPrefix+"replay"),(0,u.addClass)(f,r.options.classPrefix+"play"),p.setAttribute("title",s),p.setAttribute("aria-label",s))}r.addControlElement(f,"playpause"),m("pse"),i.addEventListener("loadedmetadata",(function(){-1===i.rendererName.indexOf("flash")&&m("pse")})),i.addEventListener("play",(function(){m("play")})),i.addEventListener("playing",(function(){m("play")})),i.addEventListener("pause",(function(){m("pse")})),i.addEventListener("ended",(function(){e.options.loop||((0,u.removeClass)(f,r.options.classPrefix+"pause"),(0,u.removeClass)(f,r.options.classPrefix+"play"),(0,u.addClass)(f,r.options.classPrefix+"replay"),p.setAttribute("title",s),p.setAttribute("aria-label",s))}))}})},{16:16,2:2,26:26,27:27,5:5}],11:[function(e,t,n){"use strict";var i=e(2),o=f(i),r=e(16),a=f(r),s=e(5),l=f(s),d=e(25),u=e(30),c=e(26);function f(e){return e&&e.__esModule?e:{default:e}}Object.assign(r.config,{enableProgressTooltip:!0,useSmoothHover:!0,forceLive:!1}),Object.assign(a.default.prototype,{buildprogress:function(e,t,n,i){var r=0,a=!1,s=!1,f=this,p=e.options.autoRewind,m=e.options.enableProgressTooltip?'<span class="'+f.options.classPrefix+'time-float"><span class="'+f.options.classPrefix+'time-float-current">00:00</span><span class="'+f.options.classPrefix+'time-float-corner"></span></span>':"",h=o.default.createElement("div");h.className=f.options.classPrefix+"time-rail",h.innerHTML='<span class="'+f.options.classPrefix+"time-total "+f.options.classPrefix+'time-slider"><span class="'+f.options.classPrefix+'time-buffering"></span><span class="'+f.options.classPrefix+'time-loaded"></span><span class="'+f.options.classPrefix+'time-current"></span><span class="'+f.options.classPrefix+'time-hovered no-hover"></span><span class="'+f.options.classPrefix+'time-handle"><span class="'+f.options.classPrefix+'time-handle-content"></span></span>'+m+"</span>",f.addControlElement(h,"progress"),f.options.keyActions.push({keys:[37,227],action:function(e){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=e.getElement(e.container).querySelector("."+f.options.classPrefix+"time-total");t&&t.focus();var n=Math.max(e.currentTime-e.options.defaultSeekBackwardInterval(e),0);e.paused||e.pause(),setTimeout((function(){e.setCurrentTime(n)}),0),setTimeout((function(){e.play()}),0)}}},{keys:[39,228],action:function(e){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=e.getElement(e.container).querySelector("."+f.options.classPrefix+"time-total");t&&t.focus();var n=Math.min(e.currentTime+e.options.defaultSeekForwardInterval(e),e.duration);e.paused||e.pause(),setTimeout((function(){e.setCurrentTime(n)}),0),setTimeout((function(){e.play()}),0)}}}),f.rail=t.querySelector("."+f.options.classPrefix+"time-rail"),f.total=t.querySelector("."+f.options.classPrefix+"time-total"),f.loaded=t.querySelector("."+f.options.classPrefix+"time-loaded"),f.current=t.querySelector("."+f.options.classPrefix+"time-current"),f.handle=t.querySelector("."+f.options.classPrefix+"time-handle"),f.timefloat=t.querySelector("."+f.options.classPrefix+"time-float"),f.timefloatcurrent=t.querySelector("."+f.options.classPrefix+"time-float-current"),f.slider=t.querySelector("."+f.options.classPrefix+"time-slider"),f.hovered=t.querySelector("."+f.options.classPrefix+"time-hovered"),f.buffer=t.querySelector("."+f.options.classPrefix+"time-buffering"),f.newTime=0,f.forcedHandlePause=!1,f.setTransformStyle=function(e,t){e.style.transform=t,e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t},f.buffer.style.display="none";var v=function(t){var n=getComputedStyle(f.total),i=(0,c.offset)(f.total),o=f.total.offsetWidth,r=function(){return void 0!==n.webkitTransform?"webkitTransform":void 0!==n.mozTransform?"mozTransform ":void 0!==n.oTransform?"oTransform":void 0!==n.msTransform?"msTransform":"transform"}(),s=function(){return"WebKitCSSMatrix"in window?"WebKitCSSMatrix":"MSCSSMatrix"in window?"MSCSSMatrix":"CSSMatrix"in window?"CSSMatrix":void 0}(),l=0,p=0,m=0,h=void 0;if(h=t.originalEvent&&t.originalEvent.changedTouches?t.originalEvent.changedTouches[0].pageX:t.changedTouches?t.changedTouches[0].pageX:t.pageX,f.getDuration()){if(h<i.left?h=i.left:h>o+i.left&&(h=o+i.left),m=h-i.left,l=m/o,f.newTime=l*f.getDuration(),a&&null!==f.getCurrentTime()&&f.newTime.toFixed(4)!==f.getCurrentTime().toFixed(4)&&(f.setCurrentRailHandle(f.newTime),f.updateCurrent(f.newTime)),!d.IS_IOS&&!d.IS_ANDROID){if(m<0&&(m=0),f.options.useSmoothHover&&null!==s&&"undefined"!==typeof window[s]){var v=new window[s](getComputedStyle(f.handle)[r]),g=v.m41,y=m/parseFloat(getComputedStyle(f.total).width)-g/parseFloat(getComputedStyle(f.total).width);f.hovered.style.left=g+"px",f.setTransformStyle(f.hovered,"scaleX("+y+")"),f.hovered.setAttribute("pos",m),y>=0?(0,c.removeClass)(f.hovered,"negative"):(0,c.addClass)(f.hovered,"negative")}if(f.timefloat){var E=f.timefloat.offsetWidth/2,b=mejs.Utils.offset(f.getElement(f.container)),S=getComputedStyle(f.timefloat);p=h-b.left<f.timefloat.offsetWidth?E:h-b.left>=f.getElement(f.container).offsetWidth-E?f.total.offsetWidth-E:m,(0,c.hasClass)(f.getElement(f.container),f.options.classPrefix+"long-video")&&(p+=parseFloat(S.marginLeft)/2+f.timefloat.offsetWidth/2),f.timefloat.style.left=p+"px",f.timefloatcurrent.innerHTML=(0,u.secondsToTimeCode)(f.newTime,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength,e.options.timeFormat),f.timefloat.style.display="block"}}}else d.IS_IOS||d.IS_ANDROID||!f.timefloat||(p=f.timefloat.offsetWidth+o>=f.getElement(f.container).offsetWidth?f.timefloat.offsetWidth/2:0,f.timefloat.style.left=p+"px",f.timefloat.style.left=p+"px",f.timefloat.style.display="block")},g=function(){var t=f.getCurrentTime(),n=l.default.t("mejs.time-slider"),o=(0,u.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength,e.options.timeFormat),r=f.getDuration();f.slider.setAttribute("role","slider"),f.slider.tabIndex=0,i.paused?(f.slider.setAttribute("aria-label",n),f.slider.setAttribute("aria-valuemin",0),f.slider.setAttribute("aria-valuemax",isNaN(r)?0:r),f.slider.setAttribute("aria-valuenow",t),f.slider.setAttribute("aria-valuetext",o)):(f.slider.removeAttribute("aria-label"),f.slider.removeAttribute("aria-valuemin"),f.slider.removeAttribute("aria-valuemax"),f.slider.removeAttribute("aria-valuenow"),f.slider.removeAttribute("aria-valuetext"))},y=function(){new Date-r>=1e3&&f.play()},E=function(){a&&null!==f.getCurrentTime()&&f.newTime.toFixed(4)!==f.getCurrentTime().toFixed(4)&&(f.setCurrentTime(f.newTime),f.setCurrentRailHandle(f.newTime),f.updateCurrent(f.newTime)),f.forcedHandlePause&&(f.slider.focus(),f.play()),f.forcedHandlePause=!1};f.slider.addEventListener("focus",(function(){e.options.autoRewind=!1})),f.slider.addEventListener("blur",(function(){e.options.autoRewind=p})),f.slider.addEventListener("keydown",(function(t){if(new Date-r>=1e3&&(s=f.paused),f.options.enableKeyboard&&f.options.keyActions.length){var n=t.which||t.keyCode||0,o=f.getDuration(),a=e.options.defaultSeekForwardInterval(i),l=e.options.defaultSeekBackwardInterval(i),u=f.getCurrentTime(),c=f.getElement(f.container).querySelector("."+f.options.classPrefix+"volume-slider");if(38===n||40===n){c&&(c.style.display="block"),f.isVideo&&(f.showControls(),f.startControlsTimer());var p=38===n?Math.min(f.volume+.1,1):Math.max(f.volume-.1,0),m=p<=0;return f.setVolume(p),void f.setMuted(m)}switch(c&&(c.style.display="none"),n){case 37:f.getDuration()!==1/0&&(u-=l);break;case 39:f.getDuration()!==1/0&&(u+=a);break;case 36:u=0;break;case 35:u=o;break;case 13:case 32:return void(d.IS_FIREFOX&&(f.paused?f.play():f.pause()));default:return}u=u<0||isNaN(u)?0:u>=o?o:Math.floor(u),r=new Date,s||e.pause(),setTimeout((function(){f.setCurrentTime(u)}),0),u<f.getDuration()&&!s&&setTimeout(y,1100),e.showControls(),t.preventDefault(),t.stopPropagation()}}));var b=["mousedown","touchstart"];f.slider.addEventListener("dragstart",(function(){return!1}));for(var S=0,x=b.length;S<x;S++)f.slider.addEventListener(b[S],(function(e){if(f.forcedHandlePause=!1,f.getDuration()!==1/0&&(1===e.which||0===e.which)){f.paused||(f.pause(),f.forcedHandlePause=!0),a=!0,v(e);for(var t=["mouseup","touchend"],n=0,i=t.length;n<i;n++)f.getElement(f.container).addEventListener(t[n],(function(e){var t=e.target;(t===f.slider||t.closest("."+f.options.classPrefix+"time-slider"))&&v(e)}));f.globalBind("mouseup.dur touchend.dur",(function(){E(),a=!1,f.timefloat&&(f.timefloat.style.display="none")}))}}),!(!d.SUPPORT_PASSIVE_EVENT||"touchstart"!==b[S])&&{passive:!0});f.slider.addEventListener("mouseenter",(function(e){e.target===f.slider&&f.getDuration()!==1/0&&(f.getElement(f.container).addEventListener("mousemove",(function(e){var t=e.target;(t===f.slider||t.closest("."+f.options.classPrefix+"time-slider"))&&v(e)})),!f.timefloat||d.IS_IOS||d.IS_ANDROID||(f.timefloat.style.display="block"),f.hovered&&!d.IS_IOS&&!d.IS_ANDROID&&f.options.useSmoothHover&&(0,c.removeClass)(f.hovered,"no-hover"))})),f.slider.addEventListener("mouseleave",(function(){f.getDuration()!==1/0&&(a||(f.timefloat&&(f.timefloat.style.display="none"),f.hovered&&f.options.useSmoothHover&&(0,c.addClass)(f.hovered,"no-hover")))})),f.broadcastCallback=function(n){var i=t.querySelector("."+f.options.classPrefix+"broadcast");if(f.options.forceLive||f.getDuration()===1/0){if(!i&&f.options.forceLive){var r=o.default.createElement("span");r.className=f.options.classPrefix+"broadcast",r.innerText=l.default.t("mejs.live-broadcast"),f.slider.style.display="none",f.rail.appendChild(r)}}else i&&(f.slider.style.display="",i.remove()),e.setProgressRail(n),f.forcedHandlePause||e.setCurrentRail(n),g()},i.addEventListener("progress",f.broadcastCallback),i.addEventListener("timeupdate",f.broadcastCallback),i.addEventListener("play",(function(){f.buffer.style.display="none"})),i.addEventListener("playing",(function(){f.buffer.style.display="none"})),i.addEventListener("seeking",(function(){f.buffer.style.display=""})),i.addEventListener("seeked",(function(){f.buffer.style.display="none"})),i.addEventListener("pause",(function(){f.buffer.style.display="none"})),i.addEventListener("waiting",(function(){f.buffer.style.display=""})),i.addEventListener("loadeddata",(function(){f.buffer.style.display=""})),i.addEventListener("canplay",(function(){f.buffer.style.display="none"})),i.addEventListener("error",(function(){f.buffer.style.display="none"})),f.getElement(f.container).addEventListener("controlsresize",(function(t){f.getDuration()!==1/0&&(e.setProgressRail(t),f.forcedHandlePause||e.setCurrentRail(t))}))},cleanprogress:function(e,t,n,i){i.removeEventListener("progress",e.broadcastCallback),i.removeEventListener("timeupdate",e.broadcastCallback),e.rail&&e.rail.remove()},setProgressRail:function(e){var t=this,n=void 0!==e?e.detail.target||e.target:t.media,i=null;n&&n.buffered&&n.buffered.length>0&&n.buffered.end&&t.getDuration()?i=n.buffered.end(n.buffered.length-1)/t.getDuration():n&&void 0!==n.bytesTotal&&n.bytesTotal>0&&void 0!==n.bufferedBytes?i=n.bufferedBytes/n.bytesTotal:e&&e.lengthComputable&&0!==e.total&&(i=e.loaded/e.total),null!==i&&(i=Math.min(1,Math.max(0,i)),t.loaded&&t.setTransformStyle(t.loaded,"scaleX("+i+")"))},setCurrentRailHandle:function(e){var t=this;t.setCurrentRailMain(t,e)},setCurrentRail:function(){var e=this;e.setCurrentRailMain(e)},setCurrentRailMain:function(e,t){if(void 0!==e.getCurrentTime()&&e.getDuration()){var n="undefined"===typeof t?e.getCurrentTime():t;if(e.total&&e.handle){var i=parseFloat(getComputedStyle(e.total).width),o=Math.round(i*n/e.getDuration()),r=o-Math.round(e.handle.offsetWidth/2);if(r=r<0?0:r,e.setTransformStyle(e.current,"scaleX("+o/i+")"),e.setTransformStyle(e.handle,"translateX("+r+"px)"),e.options.useSmoothHover&&!(0,c.hasClass)(e.hovered,"no-hover")){var a=parseInt(e.hovered.getAttribute("pos"),10);a=isNaN(a)?0:a;var s=a/i-r/i;e.hovered.style.left=r+"px",e.setTransformStyle(e.hovered,"scaleX("+s+")"),s>=0?(0,c.removeClass)(e.hovered,"negative"):(0,c.addClass)(e.hovered,"negative")}}}}})},{16:16,2:2,25:25,26:26,30:30,5:5}],12:[function(e,t,n){"use strict";var i=e(2),o=d(i),r=e(16),a=d(r),s=e(30),l=e(26);function d(e){return e&&e.__esModule?e:{default:e}}Object.assign(r.config,{duration:0,timeAndDurationSeparator:"<span> | </span>"}),Object.assign(a.default.prototype,{buildcurrent:function(e,t,n,i){var r=this,a=o.default.createElement("div");a.className=r.options.classPrefix+"time",a.setAttribute("role","timer"),a.setAttribute("aria-live","off"),a.innerHTML='<span class="'+r.options.classPrefix+'currenttime">'+(0,s.secondsToTimeCode)(0,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength,e.options.timeFormat)+"</span>",r.addControlElement(a,"current"),e.updateCurrent(),r.updateTimeCallback=function(){r.controlsAreVisible&&e.updateCurrent()},i.addEventListener("timeupdate",r.updateTimeCallback)},cleancurrent:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateTimeCallback)},buildduration:function(e,t,n,i){var r=this,a=t.lastChild.querySelector("."+r.options.classPrefix+"currenttime");if(a)t.querySelector("."+r.options.classPrefix+"time").innerHTML+=r.options.timeAndDurationSeparator+'<span class="'+r.options.classPrefix+'duration">'+(0,s.secondsToTimeCode)(r.options.duration,r.options.alwaysShowHours,r.options.showTimecodeFrameCount,r.options.framesPerSecond,r.options.secondsDecimalLength,r.options.timeFormat)+"</span>";else{t.querySelector("."+r.options.classPrefix+"currenttime")&&(0,l.addClass)(t.querySelector("."+r.options.classPrefix+"currenttime").parentNode,r.options.classPrefix+"currenttime-container");var d=o.default.createElement("div");d.className=r.options.classPrefix+"time "+r.options.classPrefix+"duration-container",d.innerHTML='<span class="'+r.options.classPrefix+'duration">'+(0,s.secondsToTimeCode)(r.options.duration,r.options.alwaysShowHours,r.options.showTimecodeFrameCount,r.options.framesPerSecond,r.options.secondsDecimalLength,r.options.timeFormat)+"</span>",r.addControlElement(d,"duration")}r.updateDurationCallback=function(){r.controlsAreVisible&&e.updateDuration()},i.addEventListener("timeupdate",r.updateDurationCallback)},cleanduration:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateDurationCallback)},updateCurrent:function(){var e=this,t=e.getCurrentTime();isNaN(t)&&(t=0);var n=(0,s.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength,e.options.timeFormat);n.length>5?(0,l.addClass)(e.getElement(e.container),e.options.classPrefix+"long-video"):(0,l.removeClass)(e.getElement(e.container),e.options.classPrefix+"long-video"),e.getElement(e.controls).querySelector("."+e.options.classPrefix+"currenttime")&&(e.getElement(e.controls).querySelector("."+e.options.classPrefix+"currenttime").innerText=n)},updateDuration:function(){var e=this,t=e.getDuration();void 0!==e.media&&(isNaN(t)||t===1/0||t<0)&&(e.media.duration=e.options.duration=t=0),e.options.duration>0&&(t=e.options.duration);var n=(0,s.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength,e.options.timeFormat);n.length>5?(0,l.addClass)(e.getElement(e.container),e.options.classPrefix+"long-video"):(0,l.removeClass)(e.getElement(e.container),e.options.classPrefix+"long-video"),e.getElement(e.controls).querySelector("."+e.options.classPrefix+"duration")&&t>0&&(e.getElement(e.controls).querySelector("."+e.options.classPrefix+"duration").innerHTML=n)}})},{16:16,2:2,26:26,30:30}],13:[function(e,t,n){"use strict";var i=e(2),o=m(i),r=e(7),a=m(r),s=e(5),l=m(s),d=e(16),u=m(d),c=e(30),f=e(27),p=e(26);function m(e){return e&&e.__esModule?e:{default:e}}Object.assign(d.config,{startLanguage:"",tracksText:null,chaptersText:null,tracksAriaLive:!1,hideCaptionsButtonWhenEmpty:!0,toggleCaptionsButtonWhenOnlyOne:!1,slidesSelector:""}),Object.assign(u.default.prototype,{hasChapters:!1,buildtracks:function(e,t,n,i){if(this.findTracks(),e.tracks.length||e.trackFiles&&0!==!e.trackFiles.length){var r=this,a=r.options.tracksAriaLive?' role="log" aria-live="assertive" aria-atomic="false"':"",s=(0,f.isString)(r.options.tracksText)?r.options.tracksText:l.default.t("mejs.captions-subtitles"),d=(0,f.isString)(r.options.chaptersText)?r.options.chaptersText:l.default.t("mejs.captions-chapters"),u=null===e.trackFiles?e.tracks.length:e.trackFiles.length;if(r.domNode.textTracks)for(var c=r.domNode.textTracks.length-1;c>=0;c--)r.domNode.textTracks[c].mode="hidden";r.cleartracks(e),e.captions=o.default.createElement("div"),e.captions.className=r.options.classPrefix+"captions-layer "+r.options.classPrefix+"layer",e.captions.innerHTML='<div class="'+r.options.classPrefix+"captions-position "+r.options.classPrefix+'captions-position-hover"'+a+'><span class="'+r.options.classPrefix+'captions-text"></span></div>',e.captions.style.display="none",n.insertBefore(e.captions,n.firstChild),e.captionsText=e.captions.querySelector("."+r.options.classPrefix+"captions-text"),e.captionsButton=o.default.createElement("div"),e.captionsButton.className=r.options.classPrefix+"button "+r.options.classPrefix+"captions-button",e.captionsButton.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button><div class="'+r.options.classPrefix+"captions-selector "+r.options.classPrefix+'offscreen"><ul class="'+r.options.classPrefix+'captions-selector-list"><li class="'+r.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+r.options.classPrefix+'captions-selector-input" name="'+e.id+'_captions" id="'+e.id+'_captions_none" value="none" checked disabled><label class="'+r.options.classPrefix+"captions-selector-label "+r.options.classPrefix+'captions-selected" for="'+e.id+'_captions_none">'+l.default.t("mejs.none")+"</label></li></ul></div>",r.addControlElement(e.captionsButton,"tracks"),e.captionsButton.querySelector("."+r.options.classPrefix+"captions-selector-input").disabled=!1,e.chaptersButton=o.default.createElement("div"),e.chaptersButton.className=r.options.classPrefix+"button "+r.options.classPrefix+"chapters-button",e.chaptersButton.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+d+'" aria-label="'+d+'" tabindex="0"></button><div class="'+r.options.classPrefix+"chapters-selector "+r.options.classPrefix+'offscreen"><ul class="'+r.options.classPrefix+'chapters-selector-list"></ul></div>';for(var m=0,h=0;h<u;h++){var v=e.tracks[h].kind,g=e.tracks[h].src;g.trim()&&("subtitles"===v||"captions"===v?m++:"chapters"!==v||t.querySelector("."+r.options.classPrefix+"chapter-selector")||e.captionsButton.parentNode.insertBefore(e.chaptersButton,e.captionsButton))}e.trackToLoad=-1,e.selectedTrack=null,e.isLoadingTrack=!1;for(var y=0;y<u;y++){var E=e.tracks[y].kind;!e.tracks[y].src.trim()||"subtitles"!==E&&"captions"!==E||e.addTrackButton(e.tracks[y].trackId,e.tracks[y].srclang,e.tracks[y].label)}e.loadNextTrack();var b=["mouseenter","focusin"],S=["mouseleave","focusout"];if(r.options.toggleCaptionsButtonWhenOnlyOne&&1===m)e.captionsButton.addEventListener("click",(function(t){var n="none";null===e.selectedTrack&&(n=e.tracks[0].trackId);var i=t.keyCode||t.which;e.setTrack(n,"undefined"!==typeof i)}));else{for(var x=e.captionsButton.querySelectorAll("."+r.options.classPrefix+"captions-selector-label"),w=e.captionsButton.querySelectorAll("input[type=radio]"),P=0,T=b.length;P<T;P++)e.captionsButton.addEventListener(b[P],(function(){(0,p.removeClass)(this.querySelector("."+r.options.classPrefix+"captions-selector"),r.options.classPrefix+"offscreen")}));for(var k=0,C=S.length;k<C;k++)e.captionsButton.addEventListener(S[k],(function(){(0,p.addClass)(this.querySelector("."+r.options.classPrefix+"captions-selector"),r.options.classPrefix+"offscreen")}));for(var _=0,N=w.length;_<N;_++)w[_].addEventListener("click",(function(t){var n=t.keyCode||t.which;e.setTrack(this.value,"undefined"!==typeof n)}));for(var A=0,L=x.length;A<L;A++)x[A].addEventListener("click",(function(e){var t=(0,p.siblings)(this,(function(e){return"INPUT"===e.tagName}))[0],n=(0,f.createEvent)("click",t);t.dispatchEvent(n),e.preventDefault()}));e.captionsButton.addEventListener("keydown",(function(e){e.stopPropagation()}))}for(var F=0,j=b.length;F<j;F++)e.chaptersButton.addEventListener(b[F],(function(){this.querySelector("."+r.options.classPrefix+"chapters-selector-list").children.length&&(0,p.removeClass)(this.querySelector("."+r.options.classPrefix+"chapters-selector"),r.options.classPrefix+"offscreen")}));for(var I=0,M=S.length;I<M;I++)e.chaptersButton.addEventListener(S[I],(function(){(0,p.addClass)(this.querySelector("."+r.options.classPrefix+"chapters-selector"),r.options.classPrefix+"offscreen")}));e.chaptersButton.addEventListener("keydown",(function(e){e.stopPropagation()})),e.options.alwaysShowControls?(0,p.addClass)(e.getElement(e.container).querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover"):(e.getElement(e.container).addEventListener("controlsshown",(function(){(0,p.addClass)(e.getElement(e.container).querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover")})),e.getElement(e.container).addEventListener("controlshidden",(function(){i.paused||(0,p.removeClass)(e.getElement(e.container).querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover")}))),i.addEventListener("timeupdate",(function(){e.displayCaptions()})),""!==e.options.slidesSelector&&(e.slidesContainer=o.default.querySelectorAll(e.options.slidesSelector),i.addEventListener("timeupdate",(function(){e.displaySlides()})))}},cleartracks:function(e){e&&(e.captions&&e.captions.remove(),e.chapters&&e.chapters.remove(),e.captionsText&&e.captionsText.remove(),e.captionsButton&&e.captionsButton.remove(),e.chaptersButton&&e.chaptersButton.remove())},rebuildtracks:function(){var e=this;e.findTracks(),e.buildtracks(e,e.getElement(e.controls),e.getElement(e.layers),e.media)},findTracks:function(){var e=this,t=null===e.trackFiles?e.node.querySelectorAll("track"):e.trackFiles,n=t.length;e.tracks=[];for(var i=0;i<n;i++){var o=t[i],r=o.getAttribute("srclang").toLowerCase()||"",a=e.id+"_track_"+i+"_"+o.getAttribute("kind")+"_"+r;e.tracks.push({trackId:a,srclang:r,src:o.getAttribute("src"),kind:o.getAttribute("kind"),label:o.getAttribute("label")||"",entries:[],isLoaded:!1})}},setTrack:function(e,t){for(var n=this,i=n.captionsButton.querySelectorAll('input[type="radio"]'),o=n.captionsButton.querySelectorAll("."+n.options.classPrefix+"captions-selected"),r=n.captionsButton.querySelector('input[value="'+e+'"]'),a=0,s=i.length;a<s;a++)i[a].checked=!1;for(var l=0,d=o.length;l<d;l++)(0,p.removeClass)(o[l],n.options.classPrefix+"captions-selected");r.checked=!0;for(var u=(0,p.siblings)(r,(function(e){return(0,p.hasClass)(e,n.options.classPrefix+"captions-selector-label")})),c=0,m=u.length;c<m;c++)(0,p.addClass)(u[c],n.options.classPrefix+"captions-selected");if("none"===e)n.selectedTrack=null,(0,p.removeClass)(n.captionsButton,n.options.classPrefix+"captions-enabled");else for(var h=0,v=n.tracks.length;h<v;h++){var g=n.tracks[h];if(g.trackId===e){null===n.selectedTrack&&(0,p.addClass)(n.captionsButton,n.options.classPrefix+"captions-enabled"),n.selectedTrack=g,n.captions.setAttribute("lang",n.selectedTrack.srclang),n.displayCaptions();break}}var y=(0,f.createEvent)("captionschange",n.media);y.detail.caption=n.selectedTrack,n.media.dispatchEvent(y),t||setTimeout((function(){n.getElement(n.container).focus()}),500)},loadNextTrack:function(){var e=this;e.trackToLoad++,e.trackToLoad<e.tracks.length?(e.isLoadingTrack=!0,e.loadTrack(e.trackToLoad)):(e.isLoadingTrack=!1,e.checkForTracks())},loadTrack:function(e){var t=this,n=t.tracks[e];void 0===n||void 0===n.src&&""===n.src||(0,p.ajax)(n.src,"text",(function(e){n.entries="string"===typeof e&&/<tt\s+xml/gi.exec(e)?a.default.TrackFormatParser.dfxp.parse(e):a.default.TrackFormatParser.webvtt.parse(e),n.isLoaded=!0,t.enableTrackButton(n),t.loadNextTrack(),"slides"===n.kind?t.setupSlides(n):"chapters"!==n.kind||t.hasChapters||(t.drawChapters(n),t.hasChapters=!0)}),(function(){t.removeTrackButton(n.trackId),t.loadNextTrack()}))},enableTrackButton:function(e){var t=this,n=e.srclang,i=o.default.getElementById(""+e.trackId);if(i){var r=e.label;""===r&&(r=l.default.t(a.default.language.codes[n])||n),i.disabled=!1;for(var s=(0,p.siblings)(i,(function(e){return(0,p.hasClass)(e,t.options.classPrefix+"captions-selector-label")})),d=0,u=s.length;d<u;d++)s[d].innerHTML=r;if(t.options.startLanguage===n){i.checked=!0;var c=(0,f.createEvent)("click",i);i.dispatchEvent(c)}}},removeTrackButton:function(e){var t=o.default.getElementById(""+e);if(t){var n=t.closest("li");n&&n.remove()}},addTrackButton:function(e,t,n){var i=this;""===n&&(n=l.default.t(a.default.language.codes[t])||t),i.captionsButton.querySelector("ul").innerHTML+='<li class="'+i.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+i.options.classPrefix+'captions-selector-input" name="'+i.id+'_captions" id="'+e+'" value="'+e+'" disabled><label class="'+i.options.classPrefix+'captions-selector-label"for="'+e+'">'+n+" (loading)</label></li>"},checkForTracks:function(){var e=this,t=!1;if(e.options.hideCaptionsButtonWhenEmpty){for(var n=0,i=e.tracks.length;n<i;n++){var o=e.tracks[n].kind;if(("subtitles"===o||"captions"===o)&&e.tracks[n].isLoaded){t=!0;break}}e.captionsButton.style.display=t?"":"none",e.setControlsSize()}},displayCaptions:function(){if(void 0!==this.tracks){var e=this,t=e.selectedTrack,n=function(e){var t=o.default.createElement("div");t.innerHTML=e;var n=t.getElementsByTagName("script"),i=n.length;while(i--)n[i].remove();for(var r=t.getElementsByTagName("*"),a=0,s=r.length;a<s;a++)for(var l=r[a].attributes,d=Array.prototype.slice.call(l),u=0,c=d.length;u<c;u++)d[u].name.startsWith("on")||d[u].value.startsWith("javascript")?r[a].remove():"style"===d[u].name&&r[a].removeAttribute(d[u].name);return t.innerHTML};if(null!==t&&t.isLoaded){var i=e.searchTrackPosition(t.entries,e.media.currentTime);if(i>-1){var r=t.entries[i].text;return"function"===typeof e.options.captionTextPreprocessor&&(r=e.options.captionTextPreprocessor(r)),e.captionsText.innerHTML=n(r),e.captionsText.className=e.options.classPrefix+"captions-text "+(t.entries[i].identifier||""),e.captions.style.display="",void(e.captions.style.height="0px")}e.captions.style.display="none"}else e.captions.style.display="none"}},setupSlides:function(e){var t=this;t.slides=e,t.slides.entries.imgs=[t.slides.entries.length],t.showSlide(0)},showSlide:function(e){var t=this,n=this;if(void 0!==n.tracks&&void 0!==n.slidesContainer){var i=n.slides.entries[e].text,r=n.slides.entries[e].imgs;if(void 0===r||void 0===r.fadeIn){var a=o.default.createElement("img");a.src=i,a.addEventListener("load",(function(){var e=t,i=(0,p.siblings)(e,(function(e){return i(e)}));e.style.display="none",n.slidesContainer.innerHTML+=e.innerHTML,(0,p.fadeIn)(n.slidesContainer.querySelector(a));for(var o=0,r=i.length;o<r;o++)(0,p.fadeOut)(i[o],400)})),n.slides.entries[e].imgs=r=a}else if(!(0,p.visible)(r)){var s=(0,p.siblings)(self,(function(e){return s(e)}));(0,p.fadeIn)(n.slidesContainer.querySelector(r));for(var l=0,d=s.length;l<d;l++)(0,p.fadeOut)(s[l])}}},displaySlides:function(){var e=this;if(void 0!==this.slides){var t=e.slides,n=e.searchTrackPosition(t.entries,e.media.currentTime);n>-1&&e.showSlide(n)}},drawChapters:function(e){var t=this,n=e.entries.length;if(n){t.chaptersButton.querySelector("ul").innerHTML="";for(var i=0;i<n;i++)t.chaptersButton.querySelector("ul").innerHTML+='<li class="'+t.options.classPrefix+'chapters-selector-list-item" role="menuitemcheckbox" aria-live="polite" aria-disabled="false" aria-checked="false"><input type="radio" class="'+t.options.classPrefix+'captions-selector-input" name="'+t.id+'_chapters" id="'+t.id+"_chapters_"+i+'" value="'+e.entries[i].start+'" disabled><label class="'+t.options.classPrefix+'chapters-selector-label"for="'+t.id+"_chapters_"+i+'">'+e.entries[i].text+"</label></li>";for(var o=t.chaptersButton.querySelectorAll('input[type="radio"]'),r=t.chaptersButton.querySelectorAll("."+t.options.classPrefix+"chapters-selector-label"),a=0,s=o.length;a<s;a++)o[a].disabled=!1,o[a].checked=!1,o[a].addEventListener("click",(function(e){var n=this,i=t.chaptersButton.querySelectorAll("li"),o=(0,p.siblings)(n,(function(e){return(0,p.hasClass)(e,t.options.classPrefix+"chapters-selector-label")}))[0];n.checked=!0,n.parentNode.setAttribute("aria-checked",!0),(0,p.addClass)(o,t.options.classPrefix+"chapters-selected"),(0,p.removeClass)(t.chaptersButton.querySelector("."+t.options.classPrefix+"chapters-selected"),t.options.classPrefix+"chapters-selected");for(var r=0,a=i.length;r<a;r++)i[r].setAttribute("aria-checked",!1);var s=e.keyCode||e.which;"undefined"===typeof s&&setTimeout((function(){t.getElement(t.container).focus()}),500),t.media.setCurrentTime(parseFloat(n.value)),t.media.paused&&t.media.play()}));for(var l=0,d=r.length;l<d;l++)r[l].addEventListener("click",(function(e){var t=(0,p.siblings)(this,(function(e){return"INPUT"===e.tagName}))[0],n=(0,f.createEvent)("click",t);t.dispatchEvent(n),e.preventDefault()}))}},searchTrackPosition:function(e,t){var n=0,i=e.length-1,o=void 0,r=void 0,a=void 0;while(n<=i){if(o=n+i>>1,r=e[o].start,a=e[o].stop,t>=r&&t<a)return o;r<t?n=o+1:r>t&&(i=o-1)}return-1}}),a.default.language={codes:{af:"mejs.afrikaans",sq:"mejs.albanian",ar:"mejs.arabic",be:"mejs.belarusian",bg:"mejs.bulgarian",ca:"mejs.catalan",zh:"mejs.chinese","zh-cn":"mejs.chinese-simplified","zh-tw":"mejs.chines-traditional",hr:"mejs.croatian",cs:"mejs.czech",da:"mejs.danish",nl:"mejs.dutch",en:"mejs.english",et:"mejs.estonian",fl:"mejs.filipino",fi:"mejs.finnish",fr:"mejs.french",gl:"mejs.galician",de:"mejs.german",el:"mejs.greek",ht:"mejs.haitian-creole",iw:"mejs.hebrew",hi:"mejs.hindi",hu:"mejs.hungarian",is:"mejs.icelandic",id:"mejs.indonesian",ga:"mejs.irish",it:"mejs.italian",ja:"mejs.japanese",ko:"mejs.korean",lv:"mejs.latvian",lt:"mejs.lithuanian",mk:"mejs.macedonian",ms:"mejs.malay",mt:"mejs.maltese",no:"mejs.norwegian",fa:"mejs.persian",pl:"mejs.polish",pt:"mejs.portuguese",ro:"mejs.romanian",ru:"mejs.russian",sr:"mejs.serbian",sk:"mejs.slovak",sl:"mejs.slovenian",es:"mejs.spanish",sw:"mejs.swahili",sv:"mejs.swedish",tl:"mejs.tagalog",th:"mejs.thai",tr:"mejs.turkish",uk:"mejs.ukrainian",vi:"mejs.vietnamese",cy:"mejs.welsh",yi:"mejs.yiddish"}},a.default.TrackFormatParser={webvtt:{pattern:/^((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{1,3})?) --\> ((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{3})?)(.*)$/,parse:function(e){for(var t=e.split(/\r?\n/),n=[],i=void 0,o=void 0,r=void 0,a=0,s=t.length;a<s;a++){if(i=this.pattern.exec(t[a]),i&&a<t.length){a-1>=0&&""!==t[a-1]&&(r=t[a-1]),a++,o=t[a],a++;while(""!==t[a]&&a<t.length)o=o+"\n"+t[a],a++;o=null===o?"":o.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),n.push({identifier:r,start:0===(0,c.convertSMPTEtoSeconds)(i[1])?.2:(0,c.convertSMPTEtoSeconds)(i[1]),stop:(0,c.convertSMPTEtoSeconds)(i[3]),text:o,settings:i[5]})}r=""}return n}},dfxp:{parse:function(e){e=$(e).filter("tt");var t=e.firstChild,n=t.querySelectorAll("p"),i=e.getElementById(""+t.attr("style")),o=[],r=void 0;if(i.length){i.removeAttribute("id");var a=i.attributes;if(a.length){r={};for(var s=0,l=a.length;s<l;s++)r[a[s].name.split(":")[1]]=a[s].value}}for(var d=0,u=n.length;d<u;d++){var f=void 0,p={start:null,stop:null,style:null,text:null};if(n.eq(d).attr("begin")&&(p.start=(0,c.convertSMPTEtoSeconds)(n.eq(d).attr("begin"))),!p.start&&n.eq(d-1).attr("end")&&(p.start=(0,c.convertSMPTEtoSeconds)(n.eq(d-1).attr("end"))),n.eq(d).attr("end")&&(p.stop=(0,c.convertSMPTEtoSeconds)(n.eq(d).attr("end"))),!p.stop&&n.eq(d+1).attr("begin")&&(p.stop=(0,c.convertSMPTEtoSeconds)(n.eq(d+1).attr("begin"))),r)for(var m in f="",r)f+=m+":"+r[m]+";";f&&(p.style=f),0===p.start&&(p.start=.2),p.text=n.eq(d).innerHTML.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),o.push(p)}return o}}}},{16:16,2:2,26:26,27:27,30:30,5:5,7:7}],14:[function(e,t,n){"use strict";var i=e(2),o=f(i),r=e(16),a=f(r),s=e(5),l=f(s),d=e(25),u=e(27),c=e(26);function f(e){return e&&e.__esModule?e:{default:e}}Object.assign(r.config,{muteText:null,unmuteText:null,allyVolumeControlText:null,hideVolumeOnTouchDevices:!0,audioVolume:"horizontal",videoVolume:"vertical",startVolume:.8}),Object.assign(a.default.prototype,{buildvolume:function(e,t,n,i){if(!d.IS_ANDROID&&!d.IS_IOS||!this.options.hideVolumeOnTouchDevices){var r=this,a=r.isVideo?r.options.videoVolume:r.options.audioVolume,s=(0,u.isString)(r.options.muteText)?r.options.muteText:l.default.t("mejs.mute"),f=(0,u.isString)(r.options.unmuteText)?r.options.unmuteText:l.default.t("mejs.unmute"),p=(0,u.isString)(r.options.allyVolumeControlText)?r.options.allyVolumeControlText:l.default.t("mejs.volume-help-text"),m=o.default.createElement("div");if(m.className=r.options.classPrefix+"button "+r.options.classPrefix+"volume-button "+r.options.classPrefix+"mute",m.innerHTML="horizontal"===a?'<button type="button" aria-controls="'+r.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button>':'<button type="button" aria-controls="'+r.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button><a href="javascript:void(0);" class="'+r.options.classPrefix+'volume-slider" aria-label="'+l.default.t("mejs.volume-slider")+'" aria-valuemin="0" aria-valuemax="100" role="slider" aria-orientation="vertical"><span class="'+r.options.classPrefix+'offscreen">'+p+'</span><div class="'+r.options.classPrefix+'volume-total"><div class="'+r.options.classPrefix+'volume-current"></div><div class="'+r.options.classPrefix+'volume-handle"></div></div></a>',r.addControlElement(m,"volume"),r.options.keyActions.push({keys:[38],action:function(e){var t=e.getElement(e.container).querySelector("."+r.options.classPrefix+"volume-slider");t&&t.matches(":focus")&&(t.style.display="block"),e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.min(e.volume+.1,1);e.setVolume(n),n>0&&e.setMuted(!1)}},{keys:[40],action:function(e){var t=e.getElement(e.container).querySelector("."+r.options.classPrefix+"volume-slider");t&&(t.style.display="block"),e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.max(e.volume-.1,0);e.setVolume(n),n<=.1&&e.setMuted(!0)}},{keys:[77],action:function(e){var t=e.getElement(e.container).querySelector("."+r.options.classPrefix+"volume-slider");t&&(t.style.display="block"),e.isVideo&&(e.showControls(),e.startControlsTimer()),e.media.muted?e.setMuted(!1):e.setMuted(!0)}}),"horizontal"===a){var h=o.default.createElement("a");h.className=r.options.classPrefix+"horizontal-volume-slider",h.href="javascript:void(0);",h.setAttribute("aria-label",l.default.t("mejs.volume-slider")),h.setAttribute("aria-valuemin",0),h.setAttribute("aria-valuemax",100),h.setAttribute("aria-valuenow",100),h.setAttribute("role","slider"),h.innerHTML+='<span class="'+r.options.classPrefix+'offscreen">'+p+'</span><div class="'+r.options.classPrefix+'horizontal-volume-total"><div class="'+r.options.classPrefix+'horizontal-volume-current"></div><div class="'+r.options.classPrefix+'horizontal-volume-handle"></div></div>',m.parentNode.insertBefore(h,m.nextSibling)}var v=!1,g=!1,y=!1,E=function(){var e=Math.floor(100*i.volume);b.setAttribute("aria-valuenow",e),b.setAttribute("aria-valuetext",e+"%")},b="vertical"===a?r.getElement(r.container).querySelector("."+r.options.classPrefix+"volume-slider"):r.getElement(r.container).querySelector("."+r.options.classPrefix+"horizontal-volume-slider"),S="vertical"===a?r.getElement(r.container).querySelector("."+r.options.classPrefix+"volume-total"):r.getElement(r.container).querySelector("."+r.options.classPrefix+"horizontal-volume-total"),x="vertical"===a?r.getElement(r.container).querySelector("."+r.options.classPrefix+"volume-current"):r.getElement(r.container).querySelector("."+r.options.classPrefix+"horizontal-volume-current"),w="vertical"===a?r.getElement(r.container).querySelector("."+r.options.classPrefix+"volume-handle"):r.getElement(r.container).querySelector("."+r.options.classPrefix+"horizontal-volume-handle"),P=function(e){if(null!==e&&!isNaN(e)&&void 0!==e){if(e=Math.max(0,e),e=Math.min(e,1),0===e){(0,c.removeClass)(m,r.options.classPrefix+"mute"),(0,c.addClass)(m,r.options.classPrefix+"unmute");var t=m.firstElementChild;t.setAttribute("title",f),t.setAttribute("aria-label",f)}else{(0,c.removeClass)(m,r.options.classPrefix+"unmute"),(0,c.addClass)(m,r.options.classPrefix+"mute");var n=m.firstElementChild;n.setAttribute("title",s),n.setAttribute("aria-label",s)}var i=100*e+"%",o=getComputedStyle(w);"vertical"===a?(x.style.bottom=0,x.style.height=i,w.style.bottom=i,w.style.marginBottom=-parseFloat(o.height)/2+"px"):(x.style.left=0,x.style.width=i,w.style.left=i,w.style.marginLeft=-parseFloat(o.width)/2+"px")}},T=function(e){var t=(0,c.offset)(S),n=getComputedStyle(S);y=!0;var i=null;if("vertical"===a){var o=parseFloat(n.height),s=e.pageY-t.top;if(i=(o-s)/o,0===t.top||0===t.left)return}else{var l=parseFloat(n.width),d=e.pageX-t.left;i=d/l}i=Math.max(0,i),i=Math.min(i,1),P(i),r.setMuted(0===i),r.setVolume(i),e.preventDefault(),e.stopPropagation()},k=function(){r.muted?(P(0),(0,c.removeClass)(m,r.options.classPrefix+"mute"),(0,c.addClass)(m,r.options.classPrefix+"unmute")):(P(i.volume),(0,c.removeClass)(m,r.options.classPrefix+"unmute"),(0,c.addClass)(m,r.options.classPrefix+"mute"))};e.getElement(e.container).addEventListener("keydown",(function(e){var t=!!e.target.closest("."+r.options.classPrefix+"container");t||"vertical"!==a||(b.style.display="none")})),m.addEventListener("mouseenter",(function(e){e.target===m&&(b.style.display="block",g=!0,e.preventDefault(),e.stopPropagation())})),m.addEventListener("focusin",(function(){b.style.display="block",g=!0})),m.addEventListener("focusout",(function(e){e.relatedTarget&&(!e.relatedTarget||e.relatedTarget.matches("."+r.options.classPrefix+"volume-slider"))||"vertical"!==a||(b.style.display="none")})),m.addEventListener("mouseleave",(function(){g=!1,v||"vertical"!==a||(b.style.display="none")})),m.addEventListener("focusout",(function(){g=!1})),m.addEventListener("keydown",(function(e){if(r.options.enableKeyboard&&r.options.keyActions.length){var t=e.which||e.keyCode||0,n=i.volume;switch(t){case 38:n=Math.min(n+.1,1);break;case 40:n=Math.max(0,n-.1);break;default:return!0}v=!1,P(n),i.setVolume(n),e.preventDefault(),e.stopPropagation()}})),m.querySelector("button").addEventListener("click",(function(){i.setMuted(!i.muted);var e=(0,u.createEvent)("volumechange",i);i.dispatchEvent(e)})),b.addEventListener("dragstart",(function(){return!1})),b.addEventListener("mouseover",(function(){g=!0})),b.addEventListener("focusin",(function(){b.style.display="block",g=!0})),b.addEventListener("focusout",(function(){g=!1,v||"vertical"!==a||(b.style.display="none")})),b.addEventListener("mousedown",(function(e){T(e),r.globalBind("mousemove.vol",(function(e){var t=e.target;v&&(t===b||t.closest("vertical"===a?"."+r.options.classPrefix+"volume-slider":"."+r.options.classPrefix+"horizontal-volume-slider"))&&T(e)})),r.globalBind("mouseup.vol",(function(){v=!1,g||"vertical"!==a||(b.style.display="none")})),v=!0,e.preventDefault(),e.stopPropagation()})),i.addEventListener("volumechange",(function(e){v||k(),E(e)}));var C=!1;i.addEventListener("rendererready",(function(){y||setTimeout((function(){C=!0,(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0),i.setVolume(e.options.startVolume),r.setControlsSize()}),250)})),i.addEventListener("loadedmetadata",(function(){setTimeout((function(){y||C||((0===e.options.startVolume||i.originalNode.muted)&&i.setMuted(!0),i.setVolume(e.options.startVolume),r.setControlsSize()),C=!1}),250)})),(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0,k()),r.getElement(r.container).addEventListener("controlsresize",(function(){k()}))}}})},{16:16,2:2,25:25,26:26,27:27,5:5}],15:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"None","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"English","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"French","mejs.galician":"Galician","mejs.german":"German","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italian","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguese","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Spanish","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],16:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.config=void 0;var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=e(3),a=P(r),s=e(2),l=P(s),d=e(7),u=P(d),c=e(6),f=P(c),p=e(17),m=P(p),h=e(5),v=P(h),g=e(25),y=e(27),E=e(30),b=e(28),S=e(26),x=w(S);function w(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function P(e){return e&&e.__esModule?e:{default:e}}function T(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}u.default.mepIndex=0,u.default.players={};var k=n.config={poster:"",showPosterWhenEnded:!1,showPosterWhenPaused:!1,defaultVideoWidth:480,defaultVideoHeight:270,videoWidth:-1,videoHeight:-1,defaultAudioWidth:400,defaultAudioHeight:40,defaultSeekBackwardInterval:function(e){return.05*e.getDuration()},defaultSeekForwardInterval:function(e){return.05*e.getDuration()},setDimensions:!0,audioWidth:-1,audioHeight:-1,loop:!1,autoRewind:!0,enableAutosize:!0,timeFormat:"",alwaysShowHours:!1,showTimecodeFrameCount:!1,framesPerSecond:25,alwaysShowControls:!1,hideVideoControlsOnLoad:!1,hideVideoControlsOnPause:!1,clickToPlayPause:!0,controlsTimeoutDefault:1500,controlsTimeoutMouseEnter:2500,controlsTimeoutMouseLeave:1e3,iPadUseNativeControls:!1,iPhoneUseNativeControls:!1,AndroidUseNativeControls:!1,features:["playpause","current","progress","duration","tracks","volume","fullscreen"],useDefaultControls:!1,isVideo:!0,stretching:"auto",classPrefix:"mejs__",enableKeyboard:!0,pauseOtherPlayers:!0,secondsDecimalLength:0,customError:null,keyActions:[{keys:[32,179],action:function(e){g.IS_FIREFOX||(e.paused||e.ended?e.play():e.pause())}}]};u.default.MepDefaults=k;var C=function(){function e(t,n){T(this,e);var i=this,o="string"===typeof t?l.default.getElementById(t):t;if(!(i instanceof e))return new e(o,n);if(i.node=i.media=o,i.node){if(i.media.player)return i.media.player;if(i.hasFocus=!1,i.controlsAreVisible=!0,i.controlsEnabled=!0,i.controlsTimer=null,i.currentMediaTime=0,i.proxy=null,void 0===n){var r=i.node.getAttribute("data-mejsoptions");n=r?JSON.parse(r):{}}return i.options=Object.assign({},k,n),i.options.loop&&!i.media.getAttribute("loop")?(i.media.loop=!0,i.node.loop=!0):i.media.loop&&(i.options.loop=!0),i.options.timeFormat||(i.options.timeFormat="mm:ss",i.options.alwaysShowHours&&(i.options.timeFormat="hh:mm:ss"),i.options.showTimecodeFrameCount&&(i.options.timeFormat+=":ff")),(0,E.calculateTimeFormat)(0,i.options,i.options.framesPerSecond||25),i.id="mep_"+u.default.mepIndex++,u.default.players[i.id]=i,i.init(),i}}return o(e,[{key:"getElement",value:function(e){return e}},{key:"init",value:function(){var e=this,t=Object.assign({},e.options,{success:function(t,n){e._meReady(t,n)},error:function(t){e._handleError(t)}}),n=e.node.tagName.toLowerCase();if(e.isDynamic="audio"!==n&&"video"!==n&&"iframe"!==n,e.isVideo=e.isDynamic?e.options.isVideo:"audio"!==n&&e.options.isVideo,e.mediaFiles=null,e.trackFiles=null,g.IS_IPAD&&e.options.iPadUseNativeControls||g.IS_IPHONE&&e.options.iPhoneUseNativeControls)e.node.setAttribute("controls",!0),g.IS_IPAD&&e.node.getAttribute("autoplay")&&e.play();else if(!e.isVideo&&(e.isVideo||!e.options.features.length&&!e.options.useDefaultControls)||g.IS_ANDROID&&e.options.AndroidUseNativeControls)e.isVideo||e.options.features.length||e.options.useDefaultControls||(e.node.style.display="none");else{e.node.removeAttribute("controls");var i=e.isVideo?v.default.t("mejs.video-player"):v.default.t("mejs.audio-player"),o=l.default.createElement("span");if(o.className=e.options.classPrefix+"offscreen",o.innerText=i,e.media.parentNode.insertBefore(o,e.media),e.container=l.default.createElement("div"),e.getElement(e.container).id=e.id,e.getElement(e.container).className=e.options.classPrefix+"container "+e.options.classPrefix+"container-keyboard-inactive "+e.media.className,e.getElement(e.container).tabIndex=0,e.getElement(e.container).setAttribute("role","application"),e.getElement(e.container).setAttribute("aria-label",i),e.getElement(e.container).innerHTML='<div class="'+e.options.classPrefix+'inner"><div class="'+e.options.classPrefix+'mediaelement"></div><div class="'+e.options.classPrefix+'layers"></div><div class="'+e.options.classPrefix+'controls"></div></div>',e.getElement(e.container).addEventListener("focus",(function(t){if(!e.controlsAreVisible&&!e.hasFocus&&e.controlsEnabled){e.showControls(!0);var n=(0,y.isNodeAfter)(t.relatedTarget,e.getElement(e.container))?"."+e.options.classPrefix+"controls ."+e.options.classPrefix+"button:last-child > button":"."+e.options.classPrefix+"playpause-button > button",i=e.getElement(e.container).querySelector(n);i.focus()}})),e.node.parentNode.insertBefore(e.getElement(e.container),e.node),e.options.features.length||e.options.useDefaultControls||(e.getElement(e.container).style.background="transparent",e.getElement(e.container).querySelector("."+e.options.classPrefix+"controls").style.display="none"),e.isVideo&&"fill"===e.options.stretching&&!x.hasClass(e.getElement(e.container).parentNode,e.options.classPrefix+"fill-container")){e.outerContainer=e.media.parentNode;var r=l.default.createElement("div");r.className=e.options.classPrefix+"fill-container",e.getElement(e.container).parentNode.insertBefore(r,e.getElement(e.container)),r.appendChild(e.getElement(e.container))}g.IS_ANDROID&&x.addClass(e.getElement(e.container),e.options.classPrefix+"android"),g.IS_IOS&&x.addClass(e.getElement(e.container),e.options.classPrefix+"ios"),g.IS_IPAD&&x.addClass(e.getElement(e.container),e.options.classPrefix+"ipad"),g.IS_IPHONE&&x.addClass(e.getElement(e.container),e.options.classPrefix+"iphone"),x.addClass(e.getElement(e.container),e.isVideo?e.options.classPrefix+"video":e.options.classPrefix+"audio"),e.getElement(e.container).querySelector("."+e.options.classPrefix+"mediaelement").appendChild(e.node),e.media.player=e,e.controls=e.getElement(e.container).querySelector("."+e.options.classPrefix+"controls"),e.layers=e.getElement(e.container).querySelector("."+e.options.classPrefix+"layers");var a=e.isVideo?"video":"audio",s=a.substring(0,1).toUpperCase()+a.substring(1);e.options[a+"Width"]>0||e.options[a+"Width"].toString().indexOf("%")>-1?e.width=e.options[a+"Width"]:""!==e.node.style.width&&null!==e.node.style.width?e.width=e.node.style.width:e.node.getAttribute("width")?e.width=e.node.getAttribute("width"):e.width=e.options["default"+s+"Width"],e.options[a+"Height"]>0||e.options[a+"Height"].toString().indexOf("%")>-1?e.height=e.options[a+"Height"]:""!==e.node.style.height&&null!==e.node.style.height?e.height=e.node.style.height:e.node.getAttribute("height")?e.height=e.node.getAttribute("height"):e.height=e.options["default"+s+"Height"],e.initialAspectRatio=e.height>=e.width?e.width/e.height:e.height/e.width,e.setPlayerSize(e.width,e.height),t.pluginWidth=e.width,t.pluginHeight=e.height}if(u.default.MepDefaults=t,new f.default(e.media,t,e.mediaFiles),void 0!==e.getElement(e.container)&&e.options.features.length&&e.controlsAreVisible&&!e.options.hideVideoControlsOnLoad){var d=(0,y.createEvent)("controlsshown",e.getElement(e.container));e.getElement(e.container).dispatchEvent(d)}}},{key:"showControls",value:function(e){var t=this;if(e=void 0===e||e,!t.controlsAreVisible&&t.isVideo){if(e)(function(){x.fadeIn(t.getElement(t.controls),200,(function(){x.removeClass(t.getElement(t.controls),t.options.classPrefix+"offscreen");var e=(0,y.createEvent)("controlsshown",t.getElement(t.container));t.getElement(t.container).dispatchEvent(e)}));for(var e=t.getElement(t.container).querySelectorAll("."+t.options.classPrefix+"control"),n=function(n,i){x.fadeIn(e[n],200,(function(){x.removeClass(e[n],t.options.classPrefix+"offscreen")}))},i=0,o=e.length;i<o;i++)n(i,o)})();else{x.removeClass(t.getElement(t.controls),t.options.classPrefix+"offscreen"),t.getElement(t.controls).style.display="",t.getElement(t.controls).style.opacity=1;for(var n=t.getElement(t.container).querySelectorAll("."+t.options.classPrefix+"control"),i=0,o=n.length;i<o;i++)x.removeClass(n[i],t.options.classPrefix+"offscreen"),n[i].style.display="";var r=(0,y.createEvent)("controlsshown",t.getElement(t.container));t.getElement(t.container).dispatchEvent(r)}t.controlsAreVisible=!0,t.setControlsSize()}}},{key:"hideControls",value:function(e,t){var n=this;if(e=void 0===e||e,!0===t||!(!n.controlsAreVisible||n.options.alwaysShowControls||n.paused&&4===n.readyState&&(!n.options.hideVideoControlsOnLoad&&n.currentTime<=0||!n.options.hideVideoControlsOnPause&&n.currentTime>0)||n.isVideo&&!n.options.hideVideoControlsOnLoad&&!n.readyState||n.ended)){if(e)(function(){x.fadeOut(n.getElement(n.controls),200,(function(){x.addClass(n.getElement(n.controls),n.options.classPrefix+"offscreen"),n.getElement(n.controls).style.display="";var e=(0,y.createEvent)("controlshidden",n.getElement(n.container));n.getElement(n.container).dispatchEvent(e)}));for(var e=n.getElement(n.container).querySelectorAll("."+n.options.classPrefix+"control"),t=function(t,i){x.fadeOut(e[t],200,(function(){x.addClass(e[t],n.options.classPrefix+"offscreen"),e[t].style.display=""}))},i=0,o=e.length;i<o;i++)t(i,o)})();else{x.addClass(n.getElement(n.controls),n.options.classPrefix+"offscreen"),n.getElement(n.controls).style.display="",n.getElement(n.controls).style.opacity=0;for(var i=n.getElement(n.container).querySelectorAll("."+n.options.classPrefix+"control"),o=0,r=i.length;o<r;o++)x.addClass(i[o],n.options.classPrefix+"offscreen"),i[o].style.display="";var a=(0,y.createEvent)("controlshidden",n.getElement(n.container));n.getElement(n.container).dispatchEvent(a)}n.controlsAreVisible=!1}}},{key:"startControlsTimer",value:function(e){var t=this;e="undefined"!==typeof e?e:t.options.controlsTimeoutDefault,t.killControlsTimer("start"),t.controlsTimer=setTimeout((function(){t.hideControls(),t.killControlsTimer("hide")}),e)}},{key:"killControlsTimer",value:function(){var e=this;null!==e.controlsTimer&&(clearTimeout(e.controlsTimer),delete e.controlsTimer,e.controlsTimer=null)}},{key:"disableControls",value:function(){var e=this;e.killControlsTimer(),e.controlsEnabled=!1,e.hideControls(!1,!0)}},{key:"enableControls",value:function(){var e=this;e.controlsEnabled=!0,e.showControls(!1)}},{key:"_setDefaultPlayer",value:function(){var e=this;e.proxy&&e.proxy.pause(),e.proxy=new m.default(e),e.media.addEventListener("loadedmetadata",(function(){e.getCurrentTime()>0&&e.currentMediaTime>0&&(e.setCurrentTime(e.currentMediaTime),g.IS_IOS||g.IS_ANDROID||e.play())}))}},{key:"_meReady",value:function(e,t){var n=this,i=t.getAttribute("autoplay"),o=!(void 0===i||null===i||"false"===i),r=null!==e.rendererName&&/(native|html5)/i.test(n.media.rendererName);if(n.getElement(n.controls)&&n.enableControls(),n.getElement(n.container)&&n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-play")&&(n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-play").style.display=""),!n.created){if(n.created=!0,n.media=e,n.domNode=t,(!g.IS_ANDROID||!n.options.AndroidUseNativeControls)&&(!g.IS_IPAD||!n.options.iPadUseNativeControls)&&(!g.IS_IPHONE||!n.options.iPhoneUseNativeControls)){if(!n.isVideo&&!n.options.features.length&&!n.options.useDefaultControls)return o&&r&&n.play(),void(n.options.success&&("string"===typeof n.options.success?a.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n)));if(n.featurePosition={},n._setDefaultPlayer(),n.buildposter(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.buildkeyboard(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.buildoverlays(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.options.useDefaultControls){var s=["playpause","current","progress","duration","tracks","volume","fullscreen"];n.options.features=s.concat(n.options.features.filter((function(e){return-1===s.indexOf(e)})))}n.buildfeatures(n,n.getElement(n.controls),n.getElement(n.layers),n.media);var d=(0,y.createEvent)("controlsready",n.getElement(n.container));n.getElement(n.container).dispatchEvent(d),n.setPlayerSize(n.width,n.height),n.setControlsSize(),n.isVideo&&(n.clickToPlayPauseCallback=function(){if(n.options.clickToPlayPause){var e=n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");n.paused&&t?n.pause():n.paused?n.play():n.pause(),e.setAttribute("aria-pressed",!t),n.getElement(n.container).focus()}},n.createIframeLayer(),n.media.addEventListener("click",n.clickToPlayPauseCallback),!g.IS_ANDROID&&!g.IS_IOS||n.options.alwaysShowControls?(n.getElement(n.container).addEventListener("mouseenter",(function(){n.controlsEnabled&&(n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter)))})),n.getElement(n.container).addEventListener("mousemove",(function(){n.controlsEnabled&&(n.controlsAreVisible||n.showControls(),n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseEnter))})),n.getElement(n.container).addEventListener("mouseleave",(function(){n.controlsEnabled&&(n.paused||n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))}))):n.node.addEventListener("touchstart",(function(){n.controlsAreVisible?n.hideControls(!1):n.controlsEnabled&&n.showControls(!1)}),!!g.SUPPORT_PASSIVE_EVENT&&{passive:!0}),n.options.hideVideoControlsOnLoad&&n.hideControls(!1),n.options.enableAutosize&&n.media.addEventListener("loadedmetadata",(function(e){var t=void 0!==e?e.detail.target||e.target:n.media;n.options.videoHeight<=0&&!n.domNode.getAttribute("height")&&!n.domNode.style.height&&null!==t&&!isNaN(t.videoHeight)&&(n.setPlayerSize(t.videoWidth,t.videoHeight),n.setControlsSize(),n.media.setSize(t.videoWidth,t.videoHeight))}))),n.media.addEventListener("play",(function(){for(var e in n.hasFocus=!0,u.default.players)if(u.default.players.hasOwnProperty(e)){var t=u.default.players[e];t.id===n.id||!n.options.pauseOtherPlayers||t.paused||t.ended||!0===t.options.ignorePauseOtherPlayersOption||(t.pause(),t.hasFocus=!1)}g.IS_ANDROID||g.IS_IOS||n.options.alwaysShowControls||!n.isVideo||n.hideControls()})),n.media.addEventListener("ended",(function(){if(n.options.autoRewind)try{n.setCurrentTime(0),setTimeout((function(){var e=n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-loading");e&&e.parentNode&&(e.parentNode.style.display="none")}),20)}catch(e){}"function"===typeof n.media.renderer.stop?n.media.renderer.stop():n.pause(),n.setProgressRail&&n.setProgressRail(),n.setCurrentRail&&n.setCurrentRail(),n.options.loop?n.play():!n.options.alwaysShowControls&&n.controlsEnabled&&n.showControls()})),n.media.addEventListener("loadedmetadata",(function(){(0,E.calculateTimeFormat)(n.getDuration(),n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.isFullScreen||(n.setPlayerSize(n.width,n.height),n.setControlsSize())}));var c=null;n.media.addEventListener("timeupdate",(function(){isNaN(n.getDuration())||c===n.getDuration()||(c=n.getDuration(),(0,E.calculateTimeFormat)(c,n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.setControlsSize())})),n.getElement(n.container).addEventListener("click",(function(e){x.addClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive")})),n.getElement(n.container).addEventListener("focusin",(function(e){x.removeClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive"),!n.isVideo||g.IS_ANDROID||g.IS_IOS||!n.controlsEnabled||n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter))})),n.getElement(n.container).addEventListener("focusout",(function(e){setTimeout((function(){e.relatedTarget&&n.keyboardAction&&!e.relatedTarget.closest("."+n.options.classPrefix+"container")&&(n.keyboardAction=!1,!n.isVideo||n.options.alwaysShowControls||n.paused||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))}),0)})),setTimeout((function(){n.setPlayerSize(n.width,n.height),n.setControlsSize()}),0),n.globalResizeCallback=function(){n.isFullScreen||g.HAS_TRUE_NATIVE_FULLSCREEN&&l.default.webkitIsFullScreen||n.setPlayerSize(n.width,n.height),n.setControlsSize()},n.globalBind("resize",n.globalResizeCallback)}o&&r&&n.play(),n.options.success&&("string"===typeof n.options.success?a.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n))}}},{key:"_handleError",value:function(e,t,n){var i=this,o=i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-play");o&&(o.style.display="none"),i.options.error&&i.options.error(e,t,n),i.getElement(i.container).querySelector("."+i.options.classPrefix+"cannotplay")&&i.getElement(i.container).querySelector("."+i.options.classPrefix+"cannotplay").remove();var r=l.default.createElement("div");r.className=i.options.classPrefix+"cannotplay",r.style.width="100%",r.style.height="100%";var a="function"===typeof i.options.customError?i.options.customError(i.media,i.media.originalNode):i.options.customError,s="";if(!a){var d=i.media.originalNode.getAttribute("poster");if(d&&(s='<img src="'+d+'" alt="'+u.default.i18n.t("mejs.download-file")+'">'),e.message&&(a="<p>"+e.message+"</p>"),e.urls)for(var c=0,f=e.urls.length;c<f;c++){var p=e.urls[c];a+='<a href="'+p.src+'" data-type="'+p.type+'"><span>'+u.default.i18n.t("mejs.download-file")+": "+p.src+"</span></a>"}}a&&i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error")&&(r.innerHTML=a,i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error").innerHTML=""+s+r.outerHTML,i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error").parentNode.style.display="block"),i.controlsEnabled&&i.disableControls()}},{key:"setPlayerSize",value:function(e,t){var n=this;if(!n.options.setDimensions)return!1;switch("undefined"!==typeof e&&(n.width=e),"undefined"!==typeof t&&(n.height=t),n.options.stretching){case"fill":n.isVideo?n.setFillMode():n.setDimensions(n.width,n.height);break;case"responsive":n.setResponsiveMode();break;case"none":n.setDimensions(n.width,n.height);break;default:!0===n.hasFluidMode()?n.setResponsiveMode():n.setDimensions(n.width,n.height);break}}},{key:"hasFluidMode",value:function(){var e=this;return-1!==e.height.toString().indexOf("%")||e.node&&e.node.style.maxWidth&&"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width||e.node&&e.node.currentStyle&&"100%"===e.node.currentStyle.maxWidth}},{key:"setResponsiveMode",value:function(){var e=this,t=function(){var t=void 0,n=e.getElement(e.container);while(n){try{if(g.IS_FIREFOX&&"html"===n.tagName.toLowerCase()&&a.default.self!==a.default.top&&null!==a.default.frameElement)return a.default.frameElement;t=n.parentElement}catch(i){t=n.parentElement}if(t&&x.visible(t))return t;n=t}return null}(),n=t?getComputedStyle(t,null):getComputedStyle(l.default.body,null),i=function(){return e.isVideo?e.node.videoWidth&&e.node.videoWidth>0?e.node.videoWidth:e.node.getAttribute("width")?e.node.getAttribute("width"):e.options.defaultVideoWidth:e.options.defaultAudioWidth}(),o=function(){return e.isVideo?e.node.videoHeight&&e.node.videoHeight>0?e.node.videoHeight:e.node.getAttribute("height")?e.node.getAttribute("height"):e.options.defaultVideoHeight:e.options.defaultAudioHeight}(),r=function(){var t=1;return e.isVideo?(t=e.node.videoWidth&&e.node.videoWidth>0&&e.node.videoHeight&&e.node.videoHeight>0?e.height>=e.width?e.node.videoWidth/e.node.videoHeight:e.node.videoHeight/e.node.videoWidth:e.initialAspectRatio,(isNaN(t)||t<.01||t>100)&&(t=1),t):t}(),s=parseFloat(n.height),d=void 0,u=parseFloat(n.width);if(d=e.isVideo?"100%"===e.height?parseFloat(u*o/i,10):e.height>=e.width?parseFloat(u/r,10):parseFloat(u*r,10):o,isNaN(d)&&(d=s),e.getElement(e.container).parentNode.length>0&&"body"===e.getElement(e.container).parentNode.tagName.toLowerCase()&&(u=a.default.innerWidth||l.default.documentElement.clientWidth||l.default.body.clientWidth,d=a.default.innerHeight||l.default.documentElement.clientHeight||l.default.body.clientHeight),d&&u){e.getElement(e.container).style.width=u+"px",e.getElement(e.container).style.height=d+"px",e.node.style.width="100%",e.node.style.height="100%",e.isVideo&&e.media.setSize&&e.media.setSize(u,d);for(var c=e.getElement(e.layers).children,f=0,p=c.length;f<p;f++)c[f].style.width="100%",c[f].style.height="100%"}}},{key:"setFillMode",value:function(){var e=this,t=a.default.self!==a.default.top&&null!==a.default.frameElement,n=function(){var t=void 0,n=e.getElement(e.container);while(n){try{if(g.IS_FIREFOX&&"html"===n.tagName.toLowerCase()&&a.default.self!==a.default.top&&null!==a.default.frameElement)return a.default.frameElement;t=n.parentElement}catch(i){t=n.parentElement}if(t&&x.visible(t))return t;n=t}return null}(),i=n?getComputedStyle(n,null):getComputedStyle(l.default.body,null);"none"!==e.node.style.height&&e.node.style.height!==e.height&&(e.node.style.height="auto"),"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width&&(e.node.style.maxWidth="none"),"none"!==e.node.style.maxHeight&&e.node.style.maxHeight!==e.height&&(e.node.style.maxHeight="none"),e.node.currentStyle&&("100%"===e.node.currentStyle.height&&(e.node.currentStyle.height="auto"),"100%"===e.node.currentStyle.maxWidth&&(e.node.currentStyle.maxWidth="none"),"100%"===e.node.currentStyle.maxHeight&&(e.node.currentStyle.maxHeight="none")),t||parseFloat(i.width)||(n.style.width=e.media.offsetWidth+"px"),t||parseFloat(i.height)||(n.style.height=e.media.offsetHeight+"px"),i=getComputedStyle(n);var o=parseFloat(i.width),r=parseFloat(i.height);e.setDimensions("100%","100%");var s=e.getElement(e.container).querySelector("."+e.options.classPrefix+"poster>img");s&&(s.style.display="");for(var d=e.getElement(e.container).querySelectorAll("object, embed, iframe, video"),u=e.height,c=e.width,f=o,p=u*o/c,m=c*r/u,h=r,v=m>o===!1,y=v?Math.floor(f):Math.floor(m),E=v?Math.floor(p):Math.floor(h),b=v?o+"px":y+"px",S=v?E+"px":r+"px",w=0,P=d.length;w<P;w++)d[w].style.height=S,d[w].style.width=b,e.media.setSize&&e.media.setSize(b,S),d[w].style.marginLeft=Math.floor((o-y)/2)+"px",d[w].style.marginTop=0}},{key:"setDimensions",value:function(e,t){var n=this;e=(0,y.isString)(e)&&e.indexOf("%")>-1?e:parseFloat(e)+"px",t=(0,y.isString)(t)&&t.indexOf("%")>-1?t:parseFloat(t)+"px",n.getElement(n.container).style.width=e,n.getElement(n.container).style.height=t;for(var i=n.getElement(n.layers).children,o=0,r=i.length;o<r;o++)i[o].style.width=e,i[o].style.height=t}},{key:"setControlsSize",value:function(){var e=this;if(x.visible(e.getElement(e.container)))if(e.rail&&x.visible(e.rail)){for(var t=e.total?getComputedStyle(e.total,null):null,n=t?parseFloat(t.marginLeft)+parseFloat(t.marginRight):0,i=getComputedStyle(e.rail),o=parseFloat(i.marginLeft)+parseFloat(i.marginRight),r=0,a=x.siblings(e.rail,(function(t){return t!==e.rail})),s=a.length,l=0;l<s;l++)r+=a[l].offsetWidth;r+=n+(0===n?2*o:o)+1,e.getElement(e.container).style.minWidth=r+"px";var d=(0,y.createEvent)("controlsresize",e.getElement(e.container));e.getElement(e.container).dispatchEvent(d)}else{for(var u=e.getElement(e.controls).children,c=0,f=0,p=u.length;f<p;f++)c+=u[f].offsetWidth;e.getElement(e.container).style.minWidth=c+"px"}}},{key:"addControlElement",value:function(e,t){var n=this;if(void 0!==n.featurePosition[t]){var i=n.getElement(n.controls).children[n.featurePosition[t]-1];i.parentNode.insertBefore(e,i.nextSibling)}else{n.getElement(n.controls).appendChild(e);for(var o=n.getElement(n.controls).children,r=0,a=o.length;r<a;r++)if(e===o[r]){n.featurePosition[t]=r;break}}}},{key:"createIframeLayer",value:function(){var e=this;if(e.isVideo&&null!==e.media.rendererName&&e.media.rendererName.indexOf("iframe")>-1&&!l.default.getElementById(e.media.id+"-iframe-overlay")){var t=l.default.createElement("div"),n=l.default.getElementById(e.media.id+"_"+e.media.rendererName);t.id=e.media.id+"-iframe-overlay",t.className=e.options.classPrefix+"iframe-overlay",t.addEventListener("click",(function(t){e.options.clickToPlayPause&&(e.paused?e.play():e.pause(),t.preventDefault(),t.stopPropagation())})),n.parentNode.insertBefore(t,n)}}},{key:"resetSize",value:function(){var e=this;setTimeout((function(){e.setPlayerSize(e.width,e.height),e.setControlsSize()}),50)}},{key:"setPoster",value:function(e){var t=this;if(t.getElement(t.container)){var n=t.getElement(t.container).querySelector("."+t.options.classPrefix+"poster");n||(n=l.default.createElement("div"),n.className=t.options.classPrefix+"poster "+t.options.classPrefix+"layer",t.getElement(t.layers).appendChild(n));var i=n.querySelector("img");!i&&e&&(i=l.default.createElement("img"),i.className=t.options.classPrefix+"poster-img",i.width="100%",i.height="100%",n.style.display="",n.appendChild(i)),e?(i.setAttribute("src",e),n.style.backgroundImage='url("'+e+'")',n.style.display=""):i?(n.style.backgroundImage="none",n.style.display="none",i.remove()):n.style.display="none"}else(g.IS_IPAD&&t.options.iPadUseNativeControls||g.IS_IPHONE&&t.options.iPhoneUseNativeControls||g.IS_ANDROID&&t.options.AndroidUseNativeControls)&&(t.media.originalNode.poster=e)}},{key:"changeSkin",value:function(e){var t=this;t.getElement(t.container).className=t.options.classPrefix+"container "+e,t.setPlayerSize(t.width,t.height),t.setControlsSize()}},{key:"globalBind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:l.default;if(e=(0,y.splitEvents)(e,n.id),e.d)for(var o=e.d.split(" "),r=0,s=o.length;r<s;r++)o[r].split(".").reduce((function(e,n){return i.addEventListener(n,t,!1),n}),"");if(e.w)for(var d=e.w.split(" "),u=0,c=d.length;u<c;u++)d[u].split(".").reduce((function(e,n){return a.default.addEventListener(n,t,!1),n}),"")}},{key:"globalUnbind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:l.default;if(e=(0,y.splitEvents)(e,n.id),e.d)for(var o=e.d.split(" "),r=0,s=o.length;r<s;r++)o[r].split(".").reduce((function(e,n){return i.removeEventListener(n,t,!1),n}),"");if(e.w)for(var d=e.w.split(" "),u=0,c=d.length;u<c;u++)d[u].split(".").reduce((function(e,n){return a.default.removeEventListener(n,t,!1),n}),"")}},{key:"buildfeatures",value:function(e,t,n,i){for(var o=this,r=0,a=o.options.features.length;r<a;r++){var s=o.options.features[r];if(o["build"+s])try{o["build"+s](e,t,n,i)}catch(l){console.error("error building "+s,l)}}}},{key:"buildposter",value:function(e,t,n,i){var o=this,r=l.default.createElement("div");r.className=o.options.classPrefix+"poster "+o.options.classPrefix+"layer",n.appendChild(r);var a=i.originalNode.getAttribute("poster");""!==e.options.poster&&(a&&g.IS_IOS&&i.originalNode.removeAttribute("poster"),a=e.options.poster),a?o.setPoster(a):null!==o.media.renderer&&"function"===typeof o.media.renderer.getPosterUrl?o.setPoster(o.media.renderer.getPosterUrl()):r.style.display="none",i.addEventListener("play",(function(){r.style.display="none"})),i.addEventListener("playing",(function(){r.style.display="none"})),e.options.showPosterWhenEnded&&e.options.autoRewind&&i.addEventListener("ended",(function(){r.style.display=""})),i.addEventListener("error",(function(){r.style.display="none"})),e.options.showPosterWhenPaused&&i.addEventListener("pause",(function(){e.ended||(r.style.display="")}))}},{key:"buildoverlays",value:function(e,t,n,i){if(e.isVideo){var o=this,r=l.default.createElement("div"),a=l.default.createElement("div"),s=l.default.createElement("div");r.style.display="none",r.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",r.innerHTML='<div class="'+o.options.classPrefix+'overlay-loading"><span class="'+o.options.classPrefix+'overlay-loading-bg-img"></span></div>',n.appendChild(r),a.style.display="none",a.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",a.innerHTML='<div class="'+o.options.classPrefix+'overlay-error"></div>',n.appendChild(a),s.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer "+o.options.classPrefix+"overlay-play",s.innerHTML='<div class="'+o.options.classPrefix+'overlay-button" role="button" tabindex="0" aria-label="'+v.default.t("mejs.play")+'" aria-pressed="false"></div>',s.addEventListener("click",(function(){if(o.options.clickToPlayPause){var e=o.getElement(o.container).querySelector("."+o.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");o.paused?o.play():o.pause(),e.setAttribute("aria-pressed",!!t),o.getElement(o.container).focus()}})),s.addEventListener("keydown",(function(e){var t=e.keyCode||e.which||0;if(13===t||g.IS_FIREFOX&&32===t){var n=(0,y.createEvent)("click",s);return s.dispatchEvent(n),!1}})),n.appendChild(s),null!==o.media.rendererName&&(/(youtube|facebook)/i.test(o.media.rendererName)&&!(o.media.originalNode.getAttribute("poster")||e.options.poster||"function"===typeof o.media.renderer.getPosterUrl&&o.media.renderer.getPosterUrl())||g.IS_STOCK_ANDROID||o.media.originalNode.getAttribute("autoplay"))&&(s.style.display="none");var d=!1;i.addEventListener("play",(function(){s.style.display="none",r.style.display="none",a.style.display="none",d=!1})),i.addEventListener("playing",(function(){s.style.display="none",r.style.display="none",a.style.display="none",d=!1})),i.addEventListener("seeking",(function(){s.style.display="none",r.style.display="",d=!1})),i.addEventListener("seeked",(function(){s.style.display=o.paused&&!g.IS_STOCK_ANDROID?"":"none",r.style.display="none",d=!1})),i.addEventListener("pause",(function(){r.style.display="none",g.IS_STOCK_ANDROID||d||(s.style.display=""),d=!1})),i.addEventListener("waiting",(function(){r.style.display="",d=!1})),i.addEventListener("loadeddata",(function(){r.style.display="",g.IS_ANDROID&&(i.canplayTimeout=setTimeout((function(){if(l.default.createEvent){var e=l.default.createEvent("HTMLEvents");return e.initEvent("canplay",!0,!0),i.dispatchEvent(e)}}),300)),d=!1})),i.addEventListener("canplay",(function(){r.style.display="none",clearTimeout(i.canplayTimeout),d=!1})),i.addEventListener("error",(function(e){o._handleError(e,o.media,o.node),r.style.display="none",s.style.display="none",d=!0})),i.addEventListener("loadedmetadata",(function(){o.controlsEnabled||o.enableControls()})),i.addEventListener("keydown",(function(t){o.onkeydown(e,i,t),d=!1}))}}},{key:"buildkeyboard",value:function(e,t,n,i){var o=this;o.getElement(o.container).addEventListener("keydown",(function(){o.keyboardAction=!0})),o.globalKeydownCallback=function(t){var n=l.default.activeElement.closest("."+o.options.classPrefix+"container"),r=o.media.closest("."+o.options.classPrefix+"container");return o.hasFocus=!(!n||!r||n.id!==r.id),o.onkeydown(e,i,t)},o.globalClickCallback=function(e){o.hasFocus=!!e.target.closest("."+o.options.classPrefix+"container")},o.globalBind("keydown",o.globalKeydownCallback),o.globalBind("click",o.globalClickCallback)}},{key:"onkeydown",value:function(e,t,n){if(e.hasFocus&&e.options.enableKeyboard)for(var i=0,o=e.options.keyActions.length;i<o;i++)for(var r=e.options.keyActions[i],a=0,s=r.keys.length;a<s;a++)if(n.keyCode===r.keys[a])return r.action(e,t,n.keyCode,n),n.preventDefault(),void n.stopPropagation();return!0}},{key:"play",value:function(){this.proxy.play()}},{key:"pause",value:function(){this.proxy.pause()}},{key:"load",value:function(){this.proxy.load()}},{key:"setCurrentTime",value:function(e){this.proxy.setCurrentTime(e)}},{key:"getCurrentTime",value:function(){return this.proxy.currentTime}},{key:"getDuration",value:function(){return this.proxy.duration}},{key:"setVolume",value:function(e){this.proxy.volume=e}},{key:"getVolume",value:function(){return this.proxy.getVolume()}},{key:"setMuted",value:function(e){this.proxy.setMuted(e)}},{key:"setSrc",value:function(e){this.controlsEnabled||this.enableControls(),this.proxy.setSrc(e)}},{key:"getSrc",value:function(){return this.proxy.getSrc()}},{key:"canPlayType",value:function(e){return this.proxy.canPlayType(e)}},{key:"remove",value:function(){var e=this,t=e.media.rendererName,n=e.media.originalNode.src;for(var o in e.options.features){var r=e.options.features[o];if(e["clean"+r])try{e["clean"+r](e,e.getElement(e.layers),e.getElement(e.controls),e.media)}catch(c){console.error("error cleaning "+r,c)}}var a=e.node.getAttribute("width"),s=e.node.getAttribute("height");if(a?-1===a.indexOf("%")&&(a+="px"):a="auto",s?-1===s.indexOf("%")&&(s+="px"):s="auto",e.node.style.width=a,e.node.style.height=s,e.setPlayerSize(0,0),e.isDynamic?e.getElement(e.container).parentNode.insertBefore(e.node,e.getElement(e.container)):function(){e.node.setAttribute("controls",!0),e.node.setAttribute("id",e.node.getAttribute("id").replace("_"+t,"").replace("_from_mejs",""));var i=e.getElement(e.container).querySelector("."+e.options.classPrefix+"poster>img");if(i&&e.node.setAttribute("poster",i.src),delete e.node.autoplay,e.node.setAttribute("src",""),""!==e.media.canPlayType((0,b.getTypeFromFile)(n))&&e.node.setAttribute("src",n),t&&t.indexOf("iframe")>-1){var o=l.default.getElementById(e.media.id+"-iframe-overlay");o.remove()}var r=e.node.cloneNode();if(r.style.display="",e.getElement(e.container).parentNode.insertBefore(r,e.getElement(e.container)),e.node.remove(),e.mediaFiles)for(var a=0,s=e.mediaFiles.length;a<s;a++){var d=l.default.createElement("source");d.setAttribute("src",e.mediaFiles[a].src),d.setAttribute("type",e.mediaFiles[a].type),r.appendChild(d)}if(e.trackFiles)for(var u=function(t,n){var i=e.trackFiles[t],o=l.default.createElement("track");o.kind=i.kind,o.label=i.label,o.srclang=i.srclang,o.src=i.src,r.appendChild(o),o.addEventListener("load",(function(){this.mode="showing",r.textTracks[t].mode="showing"}))},c=0,f=e.trackFiles.length;c<f;c++)u(c,f);delete e.node,delete e.mediaFiles,delete e.trackFiles}(),e.media.renderer&&"function"===typeof e.media.renderer.destroy&&e.media.renderer.destroy(),delete u.default.players[e.id],"object"===i(e.getElement(e.container))){var d=e.getElement(e.container).parentNode.querySelector("."+e.options.classPrefix+"offscreen");d.remove(),e.getElement(e.container).remove()}e.globalUnbind("resize",e.globalResizeCallback),e.globalUnbind("keydown",e.globalKeydownCallback),e.globalUnbind("click",e.globalClickCallback),delete e.media.player}},{key:"paused",get:function(){return this.proxy.paused}},{key:"muted",get:function(){return this.proxy.muted},set:function(e){this.setMuted(e)}},{key:"ended",get:function(){return this.proxy.ended}},{key:"readyState",get:function(){return this.proxy.readyState}},{key:"currentTime",set:function(e){this.setCurrentTime(e)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(e){this.setVolume(e)},get:function(){return this.getVolume()}},{key:"src",set:function(e){this.setSrc(e)},get:function(){return this.getSrc()}}]),e}();a.default.MediaElementPlayer=C,u.default.MediaElementPlayer=C,n.default=C},{17:17,2:2,25:25,26:26,27:27,28:28,3:3,30:30,5:5,6:6,7:7}],17:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o=e(3),r=a(o);function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l=function(){function e(t){return s(this,e),this.media=t.media,this.isVideo=t.isVideo,this.classPrefix=t.options.classPrefix,this.createIframeLayer=function(){return t.createIframeLayer()},this.setPoster=function(e){return t.setPoster(e)},this}return i(e,[{key:"play",value:function(){this.media.play()}},{key:"pause",value:function(){this.media.pause()}},{key:"load",value:function(){var e=this;e.isLoaded||e.media.load(),e.isLoaded=!0}},{key:"setCurrentTime",value:function(e){this.media.setCurrentTime(e)}},{key:"getCurrentTime",value:function(){return this.media.currentTime}},{key:"getDuration",value:function(){var e=this.media.getDuration();return e===1/0&&this.media.seekable&&this.media.seekable.length&&(e=this.media.seekable.end(0)),e}},{key:"setVolume",value:function(e){this.media.setVolume(e)}},{key:"getVolume",value:function(){return this.media.getVolume()}},{key:"setMuted",value:function(e){this.media.setMuted(e)}},{key:"setSrc",value:function(e){var t=this,n=document.getElementById(t.media.id+"-iframe-overlay");n&&n.remove(),t.media.setSrc(e),t.createIframeLayer(),null!==t.media.renderer&&"function"===typeof t.media.renderer.getPosterUrl&&t.setPoster(t.media.renderer.getPosterUrl())}},{key:"getSrc",value:function(){return this.media.getSrc()}},{key:"canPlayType",value:function(e){return this.media.canPlayType(e)}},{key:"paused",get:function(){return this.media.paused}},{key:"muted",set:function(e){this.setMuted(e)},get:function(){return this.media.muted}},{key:"ended",get:function(){return this.media.ended}},{key:"readyState",get:function(){return this.media.readyState}},{key:"currentTime",set:function(e){this.setCurrentTime(e)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"remainingTime",get:function(){return this.getDuration()-this.currentTime()}},{key:"volume",set:function(e){this.setVolume(e)},get:function(){return this.getVolume()}},{key:"src",set:function(e){this.setSrc(e)},get:function(){return this.getSrc()}}]),e}();n.default=l,r.default.DefaultPlayer=l},{3:3}],18:[function(e,t,n){"use strict";var i=e(3),o=(l(i),e(7)),r=l(o),a=e(16),s=l(a);function l(e){return e&&e.__esModule?e:{default:e}}"undefined"!==typeof jQuery?r.default.$=jQuery:"undefined"!==typeof Zepto?r.default.$=Zepto:"undefined"!==typeof ender&&(r.default.$=ender),function(e){"undefined"!==typeof e&&(e.fn.mediaelementplayer=function(t){return!1===t?this.each((function(){var t=e(this).data("mediaelementplayer");t&&t.remove(),e(this).removeData("mediaelementplayer")})):this.each((function(){e(this).data("mediaelementplayer",new s.default(this,t))})),this},e(document).ready((function(){e("."+r.default.MepDefaults.classPrefix+"player").mediaelementplayer()})))}(r.default.$)},{16:16,3:3,7:7}],19:[function(e,t,n){"use strict";var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(3),r=p(o),a=e(7),s=p(a),l=e(8),d=e(27),u=e(28),c=e(25),f=e(26);function p(e){return e&&e.__esModule?e:{default:e}}var m={promise:null,load:function(e){return"undefined"!==typeof dashjs?m.promise=new Promise((function(e){e()})).then((function(){m._createPlayer(e)})):(e.options.path="string"===typeof e.options.path?e.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",m.promise=m.promise||(0,f.loadScript)(e.options.path),m.promise.then((function(){m._createPlayer(e)}))),m.promise},_createPlayer:function(e){var t=dashjs.MediaPlayer().create();return r.default["__ready__"+e.id](t),t}},h={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(e){return c.HAS_MSE&&["application/dash+xml"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var o=e.originalNode,a=e.id+"_"+t.prefix,u=o.autoplay,c=o.children,f=null,p=null;o.removeAttribute("type");for(var h=0,v=c.length;h<v;h++)c[h].removeAttribute("type");f=o.cloneNode(!0),t=Object.assign(t,e.options);for(var g=s.default.html5media.properties,y=s.default.html5media.events.concat(["click","mouseover","mouseout"]).filter((function(e){return"error"!==e})),E=function(t){var n=(0,d.createEvent)(t.type,e);e.dispatchEvent(n)},b=function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);f["get"+n]=function(){return null!==p?f[e]:null},f["set"+n]=function(n){if(-1===s.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){var o="object"===("undefined"===typeof n?"undefined":i(n))&&n.src?n.src:n;if(f[e]=o,null!==p){p.reset();for(var r=0,l=y.length;r<l;r++)f.removeEventListener(y[r],E);p=m._createPlayer({options:t.dash,id:a}),n&&"object"===("undefined"===typeof n?"undefined":i(n))&&"object"===i(n.drm)&&(p.setProtectionData(n.drm),(0,d.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(o),u&&p.play()}}else f[e]=n}},S=0,x=g.length;S<x;S++)b(g[S]);if(r.default["__ready__"+a]=function(n){e.dashPlayer=p=n;for(var o=dashjs.MediaPlayer.events,r=function(e){"loadedmetadata"===e&&(p.initialize(),p.attachView(f),p.setAutoPlay(!1),"object"!==i(t.dash.drm)||s.default.Utils.isObjectEmpty(t.dash.drm)||(p.setProtectionData(t.dash.drm),(0,d.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(f.getSrc())),f.addEventListener(e,E)},a=0,l=y.length;a<l;a++)r(y[a]);var u=function(t){if("error"===t.type.toLowerCase())e.generateError(t.message,f.src),console.error(t);else{var n=(0,d.createEvent)(t.type,e);n.data=t,e.dispatchEvent(n)}};for(var c in o)o.hasOwnProperty(c)&&p.on(o[c],(function(e){return u(e)}))},n&&n.length>0)for(var w=0,P=n.length;w<P;w++)if(l.renderer.renderers[t.prefix].canPlayType(n[w].type)){f.setAttribute("src",n[w].src),"undefined"!==typeof n[w].drm&&(t.dash.drm=n[w].drm);break}f.setAttribute("id",a),o.parentNode.insertBefore(f,o),o.autoplay=!1,o.style.display="none",f.setSize=function(e,t){return f.style.width=e+"px",f.style.height=t+"px",f},f.hide=function(){return f.pause(),f.style.display="none",f},f.show=function(){return f.style.display="",f},f.destroy=function(){null!==p&&p.reset()};var T=(0,d.createEvent)("rendererready",f);return e.dispatchEvent(T),e.promises.push(m.load({options:t.dash,id:a})),f}};u.typeChecks.push((function(e){return~e.toLowerCase().indexOf(".mpd")?"application/dash+xml":null})),l.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],20:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.PluginDetector=void 0;var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(3),r=v(o),a=e(2),s=v(a),l=e(7),d=v(l),u=e(5),c=v(u),f=e(8),p=e(27),m=e(25),h=e(28);function v(e){return e&&e.__esModule?e:{default:e}}var g=n.PluginDetector={plugins:[],hasPluginVersion:function(e,t){var n=g.plugins[e];return t[1]=t[1]||0,t[2]=t[2]||0,n[0]>t[0]||n[0]===t[0]&&n[1]>t[1]||n[0]===t[0]&&n[1]===t[1]&&n[2]>=t[2]},addPlugin:function(e,t,n,i,o){g.plugins[e]=g.detectPlugin(t,n,i,o)},detectPlugin:function(e,t,n,o){var a=[0,0,0],s=void 0,l=void 0;if(null!==m.NAV.plugins&&void 0!==m.NAV.plugins&&"object"===i(m.NAV.plugins[e])){if(s=m.NAV.plugins[e].description,s&&("undefined"===typeof m.NAV.mimeTypes||!m.NAV.mimeTypes[t]||m.NAV.mimeTypes[t].enabledPlugin)){a=s.replace(e,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".");for(var d=0,u=a.length;d<u;d++)a[d]=parseInt(a[d].match(/\d+/),10)}}else if(void 0!==r.default.ActiveXObject)try{l=new ActiveXObject(n),l&&(a=o(l))}catch(c){}return a}};g.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",(function(e){var t=[],n=e.GetVariable("$version");return n&&(n=n.split(" ")[1].split(","),t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]),t}));var y={create:function(e,t,n){var i={},o=!1;i.options=t,i.id=e.id+"_"+i.options.prefix,i.mediaElement=e,i.flashState={},i.flashApi=null,i.flashApiStack=[];for(var a=d.default.html5media.properties,l=function(e){i.flashState[e]=null;var t=""+e.substring(0,1).toUpperCase()+e.substring(1);i["get"+t]=function(){if(null!==i.flashApi){if("function"===typeof i.flashApi["get_"+e]){var t=i.flashApi["get_"+e]();return"buffered"===e?{start:function(){return 0},end:function(){return t},length:1}:t}return null}return null},i["set"+t]=function(t){if("src"===e&&(t=(0,h.absolutizeUrl)(t)),null!==i.flashApi&&void 0!==i.flashApi["set_"+e])try{i.flashApi["set_"+e](t)}catch(n){}else i.flashApiStack.push({type:"set",propName:e,value:t})}},u=0,v=a.length;u<v;u++)l(a[u]);var g=d.default.html5media.methods,y=function(e){i[e]=function(){if(o)if(null!==i.flashApi){if(i.flashApi["fire_"+e])try{i.flashApi["fire_"+e]()}catch(t){}}else i.flashApiStack.push({type:"call",methodName:e})}};g.push("stop");for(var E=0,b=g.length;E<b;E++)y(g[E]);for(var S=["rendererready"],x=0,w=S.length;x<w;x++){var P=(0,p.createEvent)(S[x],i);e.dispatchEvent(P)}r.default["__ready__"+i.id]=function(){if(i.flashReady=!0,i.flashApi=s.default.getElementById("__"+i.id),i.flashApiStack.length)for(var e=0,t=i.flashApiStack.length;e<t;e++){var n=i.flashApiStack[e];if("set"===n.type){var o=n.propName,r=""+o.substring(0,1).toUpperCase()+o.substring(1);i["set"+r](n.value)}else"call"===n.type&&i[n.methodName]()}},r.default["__event__"+i.id]=function(e,t){var n=(0,p.createEvent)(e,i);if(t)try{n.data=JSON.parse(t),n.details.data=JSON.parse(t)}catch(o){n.message=t}i.mediaElement.dispatchEvent(n)},i.flashWrapper=s.default.createElement("div"),-1===["always","sameDomain"].indexOf(i.options.shimScriptAccess)&&(i.options.shimScriptAccess="sameDomain");var T=e.originalNode.autoplay,k=["uid="+i.id,"autoplay="+T,"allowScriptAccess="+i.options.shimScriptAccess,"preload="+(e.originalNode.getAttribute("preload")||"")],C=null!==e.originalNode&&"video"===e.originalNode.tagName.toLowerCase(),_=C?e.originalNode.height:1,N=C?e.originalNode.width:1;e.originalNode.getAttribute("src")&&k.push("src="+e.originalNode.getAttribute("src")),!0===i.options.enablePseudoStreaming&&(k.push("pseudostreamstart="+i.options.pseudoStreamingStartQueryParam),k.push("pseudostreamtype="+i.options.pseudoStreamingType)),i.options.streamDelimiter&&k.push("streamdelimiter="+encodeURIComponent(i.options.streamDelimiter)),i.options.proxyType&&k.push("proxytype="+i.options.proxyType),e.appendChild(i.flashWrapper),e.originalNode.style.display="none";var A=[];if(m.IS_IE||m.IS_EDGE){var L=s.default.createElement("div");i.flashWrapper.appendChild(L),A=m.IS_EDGE?['type="application/x-shockwave-flash"','data="'+i.options.pluginPath+i.options.filename+'"','id="__'+i.id+'"','width="'+N+'"','height="'+_+"'\""]:['classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+i.id+'"','width="'+N+'"','height="'+_+'"'],C||A.push('style="clip: rect(0 0 0 0); position: absolute;"'),L.outerHTML="<object "+A.join(" ")+'><param name="movie" value="'+i.options.pluginPath+i.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+k.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+i.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+c.default.t("mejs.install-flash")+"</div></object>"}else A=['id="__'+i.id+'"','name="__'+i.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+i.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+i.options.pluginPath+i.options.filename+'"','flashvars="'+k.join("&")+'"'],C?(A.push('width="'+N+'"'),A.push('height="'+_+'"')):A.push('style="position: fixed; left: -9999em; top: -9999em;"'),i.flashWrapper.innerHTML="<embed "+A.join(" ")+">";if(i.flashNode=i.flashWrapper.lastChild,i.hide=function(){o=!1,C&&(i.flashNode.style.display="none")},i.show=function(){o=!0,C&&(i.flashNode.style.display="")},i.setSize=function(e,t){i.flashNode.style.width=e+"px",i.flashNode.style.height=t+"px",null!==i.flashApi&&"function"===typeof i.flashApi.fire_setSize&&i.flashApi.fire_setSize(e,t)},i.destroy=function(){i.flashNode.remove()},n&&n.length>0)for(var F=0,j=n.length;F<j;F++)if(f.renderer.renderers[t.prefix].canPlayType(n[F].type)){i.setSrc(n[F].src);break}return i}},E=g.hasPluginVersion("flash",[10,0,0]);if(E){h.typeChecks.push((function(e){return e=e.toLowerCase(),e.startsWith("rtmp")?~e.indexOf(".mp3")?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(e)?"audio/ogg":~e.indexOf(".m3u8")?"application/x-mpegURL":~e.indexOf(".mpd")?"application/dash+xml":~e.indexOf(".flv")?"video/flv":null}));var b={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte",proxyType:"",streamDelimiter:""},canPlayType:function(e){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(e.toLowerCase())},create:y.create};f.renderer.add(b);var S={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(e){return~["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:y.create};f.renderer.add(S);var x={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(e){return~["application/dash+xml"].indexOf(e.toLowerCase())},create:y.create};f.renderer.add(x);var w={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(e){return~["audio/mp3"].indexOf(e.toLowerCase())},create:y.create};f.renderer.add(w);var P={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(e){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(e.toLowerCase())},create:y.create};f.renderer.add(P)}},{2:2,25:25,27:27,28:28,3:3,5:5,7:7,8:8}],21:[function(e,t,n){"use strict";var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(3),r=p(o),a=e(7),s=p(a),l=e(8),d=e(27),u=e(25),c=e(28),f=e(26);function p(e){return e&&e.__esModule?e:{default:e}}var m={promise:null,load:function(e){return"undefined"!==typeof flvjs?m.promise=new Promise((function(e){e()})).then((function(){m._createPlayer(e)})):(e.options.path="string"===typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/flv.js@latest",m.promise=m.promise||(0,f.loadScript)(e.options.path),m.promise.then((function(){m._createPlayer(e)}))),m.promise},_createPlayer:function(e){flvjs.LoggingControl.enableDebug=e.options.debug,flvjs.LoggingControl.enableVerbose=e.options.debug;var t=flvjs.createPlayer(e.options,e.configs);return r.default["__ready__"+e.id](t),t}},h={name:"native_flv",options:{prefix:"native_flv",flv:{path:"https://cdn.jsdelivr.net/npm/flv.js@latest",cors:!0,debug:!1}},canPlayType:function(e){return u.HAS_MSE&&["video/x-flv","video/flv"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var o=e.originalNode,a=e.id+"_"+t.prefix,u=null,c=null;u=o.cloneNode(!0),t=Object.assign(t,e.options);for(var f=s.default.html5media.properties,p=s.default.html5media.events.concat(["click","mouseover","mouseout"]).filter((function(e){return"error"!==e})),h=function(t){var n=(0,d.createEvent)(t.type,e);e.dispatchEvent(n)},v=function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);u["get"+n]=function(){return null!==c?u[e]:null},u["set"+n]=function(n){if(-1===s.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(u[e]="object"===("undefined"===typeof n?"undefined":i(n))&&n.src?n.src:n,null!==c){var o={type:"flv"};o.url=n,o.cors=t.flv.cors,o.debug=t.flv.debug,o.path=t.flv.path;var r=t.flv.configs;c.destroy();for(var l=0,d=p.length;l<d;l++)u.removeEventListener(p[l],h);c=m._createPlayer({options:o,configs:r,id:a}),c.attachMediaElement(u),c.load()}}else u[e]=n}},g=0,y=f.length;g<y;g++)v(f[g]);if(r.default["__ready__"+a]=function(t){e.flvPlayer=c=t;for(var n=flvjs.Events,i=function(e){"loadedmetadata"===e&&(c.unload(),c.detachMediaElement(),c.attachMediaElement(u),c.load()),u.addEventListener(e,h)},o=0,r=p.length;o<r;o++)i(p[o]);var a=function(t,n){if("error"===t){var i=n[0]+": "+n[1]+" "+n[2].msg;e.generateError(i,u.src)}else{var o=(0,d.createEvent)(t,e);o.data=n,e.dispatchEvent(o)}},s=function(e){n.hasOwnProperty(e)&&c.on(n[e],(function(){for(var t=arguments.length,i=Array(t),o=0;o<t;o++)i[o]=arguments[o];return a(n[e],i)}))};for(var l in n)s(l)},n&&n.length>0)for(var E=0,b=n.length;E<b;E++)if(l.renderer.renderers[t.prefix].canPlayType(n[E].type)){u.setAttribute("src",n[E].src);break}u.setAttribute("id",a),o.parentNode.insertBefore(u,o),o.autoplay=!1,o.style.display="none";var S={type:"flv"};S.url=u.src,S.cors=t.flv.cors,S.debug=t.flv.debug,S.path=t.flv.path;var x=t.flv.configs;u.setSize=function(e,t){return u.style.width=e+"px",u.style.height=t+"px",u},u.hide=function(){return null!==c&&c.pause(),u.style.display="none",u},u.show=function(){return u.style.display="",u},u.destroy=function(){null!==c&&c.destroy()};var w=(0,d.createEvent)("rendererready",u);return e.dispatchEvent(w),e.promises.push(m.load({options:S,configs:x,id:a})),u}};c.typeChecks.push((function(e){return~e.toLowerCase().indexOf(".flv")?"video/flv":null})),l.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],22:[function(e,t,n){"use strict";var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=e(3),r=p(o),a=e(7),s=p(a),l=e(8),d=e(27),u=e(25),c=e(28),f=e(26);function p(e){return e&&e.__esModule?e:{default:e}}var m={promise:null,load:function(e){return"undefined"!==typeof Hls?m.promise=new Promise((function(e){e()})).then((function(){m._createPlayer(e)})):(e.options.path="string"===typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/hls.js@latest",m.promise=m.promise||(0,f.loadScript)(e.options.path),m.promise.then((function(){m._createPlayer(e)}))),m.promise},_createPlayer:function(e){var t=new Hls(e.options);return r.default["__ready__"+e.id](t),t}},h={name:"native_hls",options:{prefix:"native_hls",hls:{path:"https://cdn.jsdelivr.net/npm/hls.js@latest",autoStartLoad:!1,debug:!1}},canPlayType:function(e){return u.HAS_MSE&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var o=e.originalNode,a=e.id+"_"+t.prefix,u=o.getAttribute("preload"),c=o.autoplay,f=null,p=null,h=0,v=n.length;p=o.cloneNode(!0),t=Object.assign(t,e.options),t.hls.autoStartLoad=u&&"none"!==u||c;for(var g=s.default.html5media.properties,y=s.default.html5media.events.concat(["click","mouseover","mouseout"]).filter((function(e){return"error"!==e})),E=function(t){var n=(0,d.createEvent)(t.type,e);e.dispatchEvent(n)},b=function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);p["get"+n]=function(){return null!==f?p[e]:null},p["set"+n]=function(n){if(-1===s.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(p[e]="object"===("undefined"===typeof n?"undefined":i(n))&&n.src?n.src:n,null!==f){f.destroy();for(var o=0,r=y.length;o<r;o++)p.removeEventListener(y[o],E);f=m._createPlayer({options:t.hls,id:a}),f.loadSource(n),f.attachMedia(p)}}else p[e]=n}},S=0,x=g.length;S<x;S++)b(g[S]);if(r.default["__ready__"+a]=function(t){e.hlsPlayer=f=t;for(var i=Hls.Events,o=function(t){if("loadedmetadata"===t){var n=e.originalNode.src;f.detachMedia(),f.loadSource(n),f.attachMedia(p)}p.addEventListener(t,E)},r=0,a=y.length;r<a;r++)o(y[r]);var s=void 0,l=void 0,u=function(t,i){if("hlsError"===t&&(console.warn(i),i=i[1],i.fatal))switch(i.type){case"mediaError":var o=(new Date).getTime();if(!s||o-s>3e3)s=(new Date).getTime(),f.recoverMediaError();else if(!l||o-l>3e3)l=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),f.swapAudioCodec(),f.recoverMediaError();else{var r="Cannot recover, last media error recovery failed";e.generateError(r,p.src),console.error(r)}break;case"networkError":if("manifestLoadError"===i.details)if(h<v&&void 0!==n[h+1])p.setSrc(n[h++].src),p.load(),p.play();else{var a="Network error";e.generateError(a,n),console.error(a)}else{var u="Network error";e.generateError(u,n),console.error(u)}break;default:f.destroy();break}else{var c=(0,d.createEvent)(t,e);c.data=i,e.dispatchEvent(c)}},c=function(e){i.hasOwnProperty(e)&&f.on(i[e],(function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return u(i[e],n)}))};for(var m in i)c(m)},v>0)for(;h<v;h++)if(l.renderer.renderers[t.prefix].canPlayType(n[h].type)){p.setAttribute("src",n[h].src);break}"auto"===u||c||(p.addEventListener("play",(function(){null!==f&&f.startLoad()})),p.addEventListener("pause",(function(){null!==f&&f.stopLoad()}))),p.setAttribute("id",a),o.parentNode.insertBefore(p,o),o.autoplay=!1,o.style.display="none",p.setSize=function(e,t){return p.style.width=e+"px",p.style.height=t+"px",p},p.hide=function(){return p.pause(),p.style.display="none",p},p.show=function(){return p.style.display="",p},p.destroy=function(){null!==f&&(f.stopLoad(),f.destroy())};var w=(0,d.createEvent)("rendererready",p);return e.dispatchEvent(w),e.promises.push(m.load({options:t.hls,id:a})),p}};c.typeChecks.push((function(e){return~e.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null})),l.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],23:[function(e,t,n){"use strict";var i=e(3),o=f(i),r=e(2),a=f(r),s=e(7),l=f(s),d=e(8),u=e(27),c=e(25);function f(e){return e&&e.__esModule?e:{default:e}}var p={name:"html5",options:{prefix:"html5"},canPlayType:function(e){var t=a.default.createElement("video");return c.IS_ANDROID&&/\/mp(3|4)$/i.test(e)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())&&c.SUPPORTS_NATIVE_HLS?"yes":t.canPlayType?t.canPlayType(e.toLowerCase()).replace(/no/,""):""},create:function(e,t,n){var i=e.id+"_"+t.prefix,o=!1,r=null;void 0===e.originalNode||null===e.originalNode?(r=a.default.createElement("audio"),e.appendChild(r)):r=e.originalNode,r.setAttribute("id",i);for(var s=l.default.html5media.properties,c=function(e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1);r["get"+t]=function(){return r[e]},r["set"+t]=function(t){-1===l.default.html5media.readOnlyProperties.indexOf(e)&&(r[e]=t)}},f=0,p=s.length;f<p;f++)c(s[f]);for(var m=l.default.html5media.events.concat(["click","mouseover","mouseout"]).filter((function(e){return"error"!==e})),h=function(t){r.addEventListener(t,(function(t){if(o){var n=(0,u.createEvent)(t.type,t.target);e.dispatchEvent(n)}}))},v=0,g=m.length;v<g;v++)h(m[v]);r.setSize=function(e,t){return r.style.width=e+"px",r.style.height=t+"px",r},r.hide=function(){return o=!1,r.style.display="none",r},r.show=function(){return o=!0,r.style.display="",r};var y=0,E=n.length;if(E>0)for(;y<E;y++)if(d.renderer.renderers[t.prefix].canPlayType(n[y].type)){r.setAttribute("src",n[y].src);break}r.addEventListener("error",(function(t){t&&t.target&&t.target.error&&4===t.target.error.code&&o&&(y<E&&void 0!==n[y+1]?(r.src=n[y++].src,r.load(),r.play()):e.generateError("Media error: Format(s) not supported or source(s) not found",n))}));var b=(0,u.createEvent)("rendererready",r);return e.dispatchEvent(b),r}};o.default.HtmlMediaElement=l.default.HtmlMediaElement=p,d.renderer.add(p)},{2:2,25:25,27:27,3:3,7:7,8:8}],24:[function(e,t,n){"use strict";var i=e(3),o=p(i),r=e(2),a=p(r),s=e(7),l=p(s),d=e(8),u=e(27),c=e(28),f=e(26);function p(e){return e&&e.__esModule?e:{default:e}}var m={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(e){m.isLoaded="undefined"!==typeof YT&&YT.loaded,m.isLoaded?m.createIframe(e):(m.loadIframeApi(),m.iframeQueue.push(e))},loadIframeApi:function(){m.isIframeStarted||((0,f.loadScript)("https://www.youtube.com/player_api"),m.isIframeStarted=!0)},iFrameReady:function(){m.isLoaded=!0,m.isIframeLoaded=!0;while(m.iframeQueue.length>0){var e=m.iframeQueue.pop();m.createIframe(e)}},createIframe:function(e){return new YT.Player(e.containerId,e)},getYouTubeId:function(e){var t="";e.indexOf("?")>0?(t=m.getYouTubeIdFromParam(e),""===t&&(t=m.getYouTubeIdFromUrl(e))):t=m.getYouTubeIdFromUrl(e);var n=t.substring(t.lastIndexOf("/")+1);return t=n.split("?"),t[0]},getYouTubeIdFromParam:function(e){if(void 0===e||null===e||!e.trim().length)return null;for(var t=e.split("?"),n=t[1].split("&"),i="",o=0,r=n.length;o<r;o++){var a=n[o].split("=");if("v"===a[0]){i=a[1];break}}return i},getYouTubeIdFromUrl:function(e){if(void 0===e||null===e||!e.trim().length)return null;var t=e.split("?");return e=t[0],e.substring(e.lastIndexOf("/")+1)},getYouTubeNoCookieUrl:function(e){if(void 0===e||null===e||!e.trim().length||-1===e.indexOf("//www.youtube"))return e;var t=e.split("/");return t[2]=t[2].replace(".com","-nocookie.com"),t.join("/")}},h={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1,imageQuality:null}},canPlayType:function(e){return~["video/youtube","video/x-youtube"].indexOf(e.toLowerCase())},create:function(e,t,n){var i={},r=[],s=4,d=null,c=!0,f=!1,p=null,h=1;i.options=t,i.id=e.id+"_"+t.prefix,i.mediaElement=e;for(var v=l.default.html5media.properties,g=function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);i["get"+n]=function(){if(null!==d){var e=null;switch(t){case"currentTime":return d.getCurrentTime();case"duration":return d.getDuration();case"volume":return h=d.getVolume()/100,h;case"playbackRate":return d.getPlaybackRate();case"paused":return c;case"ended":return f;case"muted":return d.isMuted();case"buffered":var n=d.getVideoLoadedFraction(),i=d.getDuration();return{start:function(){return 0},end:function(){return n*i},length:1};case"src":return d.getVideoUrl();case"readyState":return s}return e}return null},i["set"+n]=function(n){if(null!==d)switch(t){case"src":var o="string"===typeof n?n:n[0].src,a=m.getYouTubeId(o);e.originalNode.autoplay?d.loadVideoById(a):d.cueVideoById(a);break;case"currentTime":d.seekTo(n);break;case"muted":n?d.mute():d.unMute(),setTimeout((function(){var t=(0,u.createEvent)("volumechange",i);e.dispatchEvent(t)}),50);break;case"volume":h=n,d.setVolume(100*n),setTimeout((function(){var t=(0,u.createEvent)("volumechange",i);e.dispatchEvent(t)}),50);break;case"playbackRate":d.setPlaybackRate(n),setTimeout((function(){var t=(0,u.createEvent)("ratechange",i);e.dispatchEvent(t)}),50);break;case"readyState":var s=(0,u.createEvent)("canplay",i);e.dispatchEvent(s);break;default:break}else r.push({type:"set",propName:t,value:n})}},y=0,E=v.length;y<E;y++)g(v[y]);for(var b=l.default.html5media.methods,S=function(e){i[e]=function(){if(null!==d)switch(e){case"play":return c=!1,d.playVideo();case"pause":return c=!0,d.pauseVideo();case"load":return null}else r.push({type:"call",methodName:e})}},x=0,w=b.length;x<w;x++)S(b[x]);var P=function(t){var i="";switch(t.data){case 2:i="The request contains an invalid parameter value. Verify that video ID has 11 characters and that contains no invalid characters, such as exclamation points or asterisks.";break;case 5:i="The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.";break;case 100:i="The video requested was not found. Either video has been removed or has been marked as private.";break;case 101:case 105:i="The owner of the requested video does not allow it to be played in embedded players.";break;default:i="Unknown error.";break}e.generateError("Code "+t.data+": "+i,n)},T=a.default.createElement("div");T.id=i.id,i.options.youtube.nocookie&&(e.originalNode.src=m.getYouTubeNoCookieUrl(n[0].src)),e.originalNode.parentNode.insertBefore(T,e.originalNode),e.originalNode.style.display="none";var k="audio"===e.originalNode.tagName.toLowerCase(),C=k?"1":e.originalNode.height,_=k?"1":e.originalNode.width,N=m.getYouTubeId(n[0].src),A={id:i.id,containerId:T.id,videoId:N,height:C,width:_,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,iv_load_policy:3},i.options.youtube),origin:o.default.location.host,events:{onReady:function(t){if(e.youTubeApi=d=t.target,e.youTubeState={paused:!0,ended:!1},r.length)for(var n=0,o=r.length;n<o;n++){var a=r[n];if("set"===a.type){var s=a.propName,l=""+s.substring(0,1).toUpperCase()+s.substring(1);i["set"+l](a.value)}else"call"===a.type&&i[a.methodName]()}p=d.getIframe(),e.originalNode.muted&&d.mute();for(var c=["mouseover","mouseout"],f=function(t){var n=(0,u.createEvent)(t.type,i);e.dispatchEvent(n)},m=0,h=c.length;m<h;m++)p.addEventListener(c[m],f,!1);for(var v=["rendererready","loadedmetadata","loadeddata","canplay"],g=0,y=v.length;g<y;g++){var E=(0,u.createEvent)(v[g],i);e.dispatchEvent(E)}},onStateChange:function(t){var n=[];switch(t.data){case-1:n=["loadedmetadata"],c=!0,f=!1;break;case 0:n=["ended"],c=!1,f=!i.options.youtube.loop,i.options.youtube.loop||i.stopInterval();break;case 1:n=["play","playing"],c=!1,f=!1,i.startInterval();break;case 2:n=["pause"],c=!0,f=!1,i.stopInterval();break;case 3:n=["progress"],f=!1;break;case 5:n=["loadeddata","loadedmetadata","canplay"],c=!0,f=!1;break}for(var o=0,r=n.length;o<r;o++){var a=(0,u.createEvent)(n[o],i);e.dispatchEvent(a)}},onError:function(e){return P(e)}}};return(k||e.originalNode.hasAttribute("playsinline"))&&(A.playerVars.playsinline=1),e.originalNode.controls&&(A.playerVars.controls=1),e.originalNode.autoplay&&(A.playerVars.autoplay=1),e.originalNode.loop&&(A.playerVars.loop=1),(A.playerVars.loop&&1===parseInt(A.playerVars.loop,10)||e.originalNode.src.indexOf("loop=")>-1)&&!A.playerVars.playlist&&-1===e.originalNode.src.indexOf("playlist=")&&(A.playerVars.playlist=m.getYouTubeId(e.originalNode.src)),m.enqueueIframe(A),i.onEvent=function(t,n,i){null!==i&&void 0!==i&&(e.youTubeState=i)},i.setSize=function(e,t){null!==d&&d.setSize(e,t)},i.hide=function(){i.stopInterval(),i.pause(),p&&(p.style.display="none")},i.show=function(){p&&(p.style.display="")},i.destroy=function(){d.destroy()},i.interval=null,i.startInterval=function(){i.interval=setInterval((function(){var t=(0,u.createEvent)("timeupdate",i);e.dispatchEvent(t)}),250)},i.stopInterval=function(){i.interval&&clearInterval(i.interval)},i.getPosterUrl=function(){var n=t.youtube.imageQuality,i=["default","hqdefault","mqdefault","sddefault","maxresdefault"],o=m.getYouTubeId(e.originalNode.src);return n&&i.indexOf(n)>-1&&o?"https://img.youtube.com/vi/"+o+"/"+n+".jpg":""},i}};o.default.onYouTubePlayerAPIReady=function(){m.iFrameReady()},c.typeChecks.push((function(e){return/\/\/(www\.youtube|youtu\.?be)/i.test(e)?"video/x-youtube":null})),d.renderer.add(h)},{2:2,26:26,27:27,28:28,3:3,7:7,8:8}],25:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.cancelFullScreen=n.requestFullScreen=n.isFullScreen=n.FULLSCREEN_EVENT_NAME=n.HAS_NATIVE_FULLSCREEN_ENABLED=n.HAS_TRUE_NATIVE_FULLSCREEN=n.HAS_IOS_FULLSCREEN=n.HAS_MS_NATIVE_FULLSCREEN=n.HAS_MOZ_NATIVE_FULLSCREEN=n.HAS_WEBKIT_NATIVE_FULLSCREEN=n.HAS_NATIVE_FULLSCREEN=n.SUPPORTS_NATIVE_HLS=n.SUPPORT_PASSIVE_EVENT=n.SUPPORT_POINTER_EVENTS=n.HAS_MSE=n.IS_STOCK_ANDROID=n.IS_SAFARI=n.IS_FIREFOX=n.IS_CHROME=n.IS_EDGE=n.IS_IE=n.IS_ANDROID=n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=n.UA=n.NAV=void 0;var i=e(3),o=d(i),r=e(2),a=d(r),s=e(7),l=d(s);function d(e){return e&&e.__esModule?e:{default:e}}for(var u=n.NAV=o.default.navigator,c=n.UA=u.userAgent.toLowerCase(),f=n.IS_IPAD=/ipad/i.test(c)&&!o.default.MSStream,p=n.IS_IPHONE=/iphone/i.test(c)&&!o.default.MSStream,m=n.IS_IPOD=/ipod/i.test(c)&&!o.default.MSStream,h=(n.IS_IOS=/ipad|iphone|ipod/i.test(c)&&!o.default.MSStream,n.IS_ANDROID=/android/i.test(c)),v=n.IS_IE=/(trident|microsoft)/i.test(u.appName),g=(n.IS_EDGE="msLaunchUri"in u&&!("documentMode"in a.default)),y=n.IS_CHROME=/chrome/i.test(c),E=n.IS_FIREFOX=/firefox/i.test(c),b=n.IS_SAFARI=/safari/i.test(c)&&!y,S=n.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(c),x=(n.HAS_MSE="MediaSource"in o.default),w=n.SUPPORT_POINTER_EVENTS=function(){var e=a.default.createElement("x"),t=a.default.documentElement,n=o.default.getComputedStyle;if(!("pointerEvents"in e.style))return!1;e.style.pointerEvents="auto",e.style.pointerEvents="x",t.appendChild(e);var i=n&&"auto"===(n(e,"")||{}).pointerEvents;return e.remove(),!!i}(),P=n.SUPPORT_PASSIVE_EVENT=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});o.default.addEventListener("test",null,t)}catch(n){}return e}(),T=["source","track","audio","video"],k=void 0,C=0,_=T.length;C<_;C++)k=a.default.createElement(T[C]);var N=n.SUPPORTS_NATIVE_HLS=b||v&&/edge/i.test(c),A=void 0!==k.webkitEnterFullscreen,L=void 0!==k.requestFullscreen;A&&/mac os x 10_5/i.test(c)&&(L=!1,A=!1);var F=void 0!==k.webkitRequestFullScreen,j=void 0!==k.mozRequestFullScreen,I=void 0!==k.msRequestFullscreen,M=F||j||I,O=M,D="",V=void 0,R=void 0,H=void 0;j?O=a.default.mozFullScreenEnabled:I&&(O=a.default.msFullscreenEnabled),y&&(A=!1),M&&(F?D="webkitfullscreenchange":j?D="fullscreenchange":I&&(D="MSFullscreenChange"),n.isFullScreen=V=function(){return j?a.default.mozFullScreen:F?a.default.webkitIsFullScreen:I?null!==a.default.msFullscreenElement:void 0},n.requestFullScreen=R=function(e){F?e.webkitRequestFullScreen():j?e.mozRequestFullScreen():I&&e.msRequestFullscreen()},n.cancelFullScreen=H=function(){F?a.default.webkitCancelFullScreen():j?a.default.mozCancelFullScreen():I&&a.default.msExitFullscreen()});var U=n.HAS_NATIVE_FULLSCREEN=L,q=n.HAS_WEBKIT_NATIVE_FULLSCREEN=F,B=n.HAS_MOZ_NATIVE_FULLSCREEN=j,z=n.HAS_MS_NATIVE_FULLSCREEN=I,W=n.HAS_IOS_FULLSCREEN=A,X=n.HAS_TRUE_NATIVE_FULLSCREEN=M,Y=n.HAS_NATIVE_FULLSCREEN_ENABLED=O,K=n.FULLSCREEN_EVENT_NAME=D;n.isFullScreen=V,n.requestFullScreen=R,n.cancelFullScreen=H,l.default.Features=l.default.Features||{},l.default.Features.isiPad=f,l.default.Features.isiPod=m,l.default.Features.isiPhone=p,l.default.Features.isiOS=l.default.Features.isiPhone||l.default.Features.isiPad,l.default.Features.isAndroid=h,l.default.Features.isIE=v,l.default.Features.isEdge=g,l.default.Features.isChrome=y,l.default.Features.isFirefox=E,l.default.Features.isSafari=b,l.default.Features.isStockAndroid=S,l.default.Features.hasMSE=x,l.default.Features.supportsNativeHLS=N,l.default.Features.supportsPointerEvents=w,l.default.Features.supportsPassiveEvent=P,l.default.Features.hasiOSFullScreen=W,l.default.Features.hasNativeFullscreen=U,l.default.Features.hasWebkitNativeFullScreen=q,l.default.Features.hasMozNativeFullScreen=B,l.default.Features.hasMsNativeFullScreen=z,l.default.Features.hasTrueNativeFullScreen=X,l.default.Features.nativeFullScreenEnabled=Y,l.default.Features.fullScreenEventName=K,l.default.Features.isFullScreen=V,l.default.Features.requestFullScreen=R,l.default.Features.cancelFullScreen=H},{2:2,3:3,7:7}],26:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.removeClass=n.addClass=n.hasClass=void 0,n.loadScript=u,n.offset=c,n.toggleClass=y,n.fadeOut=E,n.fadeIn=b,n.siblings=S,n.visible=x,n.ajax=w;var i=e(3),o=d(i),r=e(2),a=d(r),s=e(7),l=d(s);function d(e){return e&&e.__esModule?e:{default:e}}function u(e){return new Promise((function(t,n){var i=a.default.createElement("script");i.src=e,i.async=!0,i.onload=function(){i.remove(),t()},i.onerror=function(){i.remove(),n()},a.default.head.appendChild(i)}))}function c(e){var t=e.getBoundingClientRect(),n=o.default.pageXOffset||a.default.documentElement.scrollLeft,i=o.default.pageYOffset||a.default.documentElement.scrollTop;return{top:t.top+i,left:t.left+n}}var f=void 0,p=void 0,m=void 0;"classList"in a.default.documentElement?(f=function(e,t){return void 0!==e.classList&&e.classList.contains(t)},p=function(e,t){return e.classList.add(t)},m=function(e,t){return e.classList.remove(t)}):(f=function(e,t){return new RegExp("\\b"+t+"\\b").test(e.className)},p=function(e,t){h(e,t)||(e.className+=" "+t)},m=function(e,t){e.className=e.className.replace(new RegExp("\\b"+t+"\\b","g"),"")});var h=n.hasClass=f,v=n.addClass=p,g=n.removeClass=m;function y(e,t){h(e,t)?g(e,t):v(e,t)}function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=1);var i=null;o.default.requestAnimationFrame((function r(a){i=i||a;var s=a-i,l=parseFloat(1-s/t,2);e.style.opacity=l<0?0:l,s>t?n&&"function"===typeof n&&n():o.default.requestAnimationFrame(r)}))}function b(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=0);var i=null;o.default.requestAnimationFrame((function r(a){i=i||a;var s=a-i,l=parseFloat(s/t,2);e.style.opacity=l>1?1:l,s>t?n&&"function"===typeof n&&n():o.default.requestAnimationFrame(r)}))}function S(e,t){var n=[];e=e.parentNode.firstChild;do{t&&!t(e)||n.push(e)}while(e=e.nextSibling);return n}function x(e){return void 0!==e.getClientRects&&"function"===e.getClientRects?!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length):!(!e.offsetWidth&&!e.offsetHeight)}function w(e,t,n,i){var r=o.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),a="application/x-www-form-urlencoded; charset=UTF-8",s=!1,l="*/".concat("*");switch(t){case"text":a="text/plain";break;case"json":a="application/json, text/javascript";break;case"html":a="text/html";break;case"xml":a="application/xml, text/xml";break}"application/x-www-form-urlencoded"!==a&&(l=a+", */*; q=0.01"),r&&(r.open("GET",e,!0),r.setRequestHeader("Accept",l),r.onreadystatechange=function(){if(!s&&4===r.readyState)if(200===r.status){s=!0;var e=void 0;switch(t){case"json":e=JSON.parse(r.responseText);break;case"xml":e=r.responseXML;break;default:e=r.responseText;break}n(e)}else"function"===typeof i&&i(r.status)},r.send())}l.default.Utils=l.default.Utils||{},l.default.Utils.offset=c,l.default.Utils.hasClass=h,l.default.Utils.addClass=v,l.default.Utils.removeClass=g,l.default.Utils.toggleClass=y,l.default.Utils.fadeIn=b,l.default.Utils.fadeOut=E,l.default.Utils.siblings=S,l.default.Utils.visible=x,l.default.Utils.ajax=w,l.default.Utils.loadScript=u},{2:2,3:3,7:7}],27:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.escapeHTML=a,n.debounce=s,n.isObjectEmpty=l,n.splitEvents=d,n.createEvent=u,n.isNodeAfter=c,n.isString=f;var i=e(7),o=r(i);function r(e){return e&&e.__esModule?e:{default:e}}function a(e){if("string"!==typeof e)throw new Error("Argument passed must be a string");var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,(function(e){return t[e]}))}function s(e,t){var n=this,i=arguments,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"!==typeof e)throw new Error("First argument must be a function");if("number"!==typeof t)throw new Error("Second argument must be a numeric value");var r=void 0;return function(){var a=n,s=i,l=function(){r=null,o||e.apply(a,s)},d=o&&!r;clearTimeout(r),r=setTimeout(l,t),d&&e.apply(a,s)}}function l(e){return Object.getOwnPropertyNames(e).length<=0}function d(e,t){var n=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,i={d:[],w:[]};return(e||"").split(" ").forEach((function(e){var o=e+(t?"."+t:"");o.startsWith(".")?(i.d.push(o),i.w.push(o)):i[n.test(e)?"w":"d"].push(o)})),i.d=i.d.join(" "),i.w=i.w.join(" "),i}function u(e,t){if("string"!==typeof e)throw new Error("Event name must be a string");var n=e.match(/([a-z]+\.([a-z]+))/i),i={target:t};return null!==n&&(e=n[1],i.namespace=n[2]),new window.CustomEvent(e,{detail:i})}function c(e,t){return!!(e&&t&&2&e.compareDocumentPosition(t))}function f(e){return"string"===typeof e}o.default.Utils=o.default.Utils||{},o.default.Utils.escapeHTML=a,o.default.Utils.debounce=s,o.default.Utils.isObjectEmpty=l,o.default.Utils.splitEvents=d,o.default.Utils.createEvent=u,o.default.Utils.isNodeAfter=c,o.default.Utils.isString=f},{7:7}],28:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.typeChecks=void 0,n.absolutizeUrl=l,n.formatType=d,n.getMimeFromType=u,n.getTypeFromFile=c,n.getExtension=f,n.normalizeExtension=p;var i=e(7),o=a(i),r=e(27);function a(e){return e&&e.__esModule?e:{default:e}}var s=n.typeChecks=[];function l(e){if("string"!==typeof e)throw new Error("`url` argument must be a string");var t=document.createElement("div");return t.innerHTML='<a href="'+(0,r.escapeHTML)(e)+'">x</a>',t.firstChild.href}function d(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&!t?c(e):t}function u(e){if("string"!==typeof e)throw new Error("`type` argument must be a string");return e&&e.indexOf(";")>-1?e.substr(0,e.indexOf(";")):e}function c(e){if("string"!==typeof e)throw new Error("`url` argument must be a string");for(var t=0,n=s.length;t<n;t++){var i=s[t](e);if(i)return i}var o=f(e),r=p(o),a="video/mp4";return r&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg","mov"].indexOf(r)?a="video/"+r:~["mp3","oga","wav","mid","midi"].indexOf(r)&&(a="audio/"+r)),a}function f(e){if("string"!==typeof e)throw new Error("`url` argument must be a string");var t=e.split("?")[0],n=t.split("\\").pop().split("/").pop();return~n.indexOf(".")?n.substring(n.lastIndexOf(".")+1):""}function p(e){if("string"!==typeof e)throw new Error("`extension` argument must be a string");switch(e){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return e}}o.default.Utils=o.default.Utils||{},o.default.Utils.typeChecks=s,o.default.Utils.absolutizeUrl=l,o.default.Utils.formatType=d,o.default.Utils.getMimeFromType=u,o.default.Utils.getTypeFromFile=c,o.default.Utils.getExtension=f,o.default.Utils.normalizeExtension=p},{27:27,7:7}],29:[function(e,t,n){"use strict";var i=e(2),o=s(i),r=e(4),a=s(r);function s(e){return e&&e.__esModule?e:{default:e}}if(function(e){e.forEach((function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})}))}([Element.prototype,CharacterData.prototype,DocumentType.prototype]),function(){if("function"===typeof window.CustomEvent)return!1;function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=o.default.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}e.prototype=window.Event.prototype,window.CustomEvent=e}(),"function"!==typeof Object.assign&&(Object.assign=function(e){if(null===e||void 0===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,i=arguments.length;n<i;n++){var o=arguments[n];if(null!==o)for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(t[r]=o[r])}return t}),String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length-1;while(--n>=0&&t.item(n)!==this);return n>-1}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=void 0,i=this;do{n=t.length;while(--n>=0&&t.item(n)!==i);}while(n<0&&(i=i.parentElement));return i}),function(){for(var e=0,t=["ms","moz","webkit","o"],n=0;n<t.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[t[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[n]+"CancelAnimationFrame"]||window[t[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t){var n=(new Date).getTime(),i=Math.max(0,16-(n-e)),o=window.setTimeout((function(){t(n+i)}),i);return e=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),/firefox/i.test(navigator.userAgent)){var l=window.getComputedStyle;window.getComputedStyle=function(e,t){var n=l(e,t);return null===n?{getPropertyValue:function(){}}:n}}window.Promise||(window.Promise=a.default),function(e){e&&e.prototype&&null===e.prototype.children&&Object.defineProperty(e.prototype,"children",{get:function(){var e=0,t=void 0,n=this.childNodes,i=[];while(t=n[e++])1===t.nodeType&&i.push(t);return i}})}(window.Node||window.Element)},{2:2,4:4}],30:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.isDropFrame=a,n.secondsToTimeCode=s,n.timeCodeToSeconds=l,n.calculateTimeFormat=d,n.convertSMPTEtoSeconds=u;var i=e(7),o=r(i);function r(e){return e&&e.__esModule?e:{default:e}}function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:25;return!(e%1===0)}function s(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:25,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"hh:mm:ss";e=!e||"number"!==typeof e||e<0?0:e;var s=Math.round(.066666*i),l=Math.round(i),d=24*Math.round(3600*i),u=Math.round(600*i),c=a(i)?";":":",f=void 0,p=void 0,m=void 0,h=void 0,v=Math.round(e*i);if(a(i)){v<0&&(v=d+v),v%=d;var g=Math.floor(v/u),y=v%u;v+=9*s*g,y>s&&(v+=s*Math.floor((y-s)/Math.round(60*l-s)));var E=Math.floor(v/l);f=Math.floor(Math.floor(E/60)/60),p=Math.floor(E/60)%60,m=n?E%60:Math.floor(v/l%60).toFixed(o)}else f=Math.floor(e/3600)%24,p=Math.floor(e/60)%60,m=n?Math.floor(e%60):Math.floor(e%60).toFixed(o);f=f<=0?0:f,p=p<=0?0:p,m=m<=0?0:m,m=60===m?0:m,p=60===p?0:p;for(var b=r.split(":"),S={},x=0,w=b.length;x<w;++x){for(var P="",T=0,k=b[x].length;T<k;T++)P.indexOf(b[x][T])<0&&(P+=b[x][T]);~["f","s","m","h"].indexOf(P)&&(S[P]=b[x].length)}var C=t||f>0?(f<10&&S.h>1?"0"+f:f)+":":"";return C+=(p<10&&S.m>1?"0"+p:p)+":",C+=""+(m<10&&S.s>1?"0"+m:m),n&&(h=(v%l).toFixed(0),h=h<=0?0:h,C+=h<10&&S.f?c+"0"+h:""+c+h),C}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25;if("string"!==typeof e)throw new TypeError("Time must be a string");if(e.indexOf(";")>0&&(e=e.replace(";",":")),!/\d{2}(\:\d{2}){0,3}/i.test(e))throw new TypeError("Time code must have the format `00:00:00`");var n=e.split(":"),i=void 0,o=0,r=0,s=0,l=0,d=0,u=Math.round(.066666*t),c=Math.round(t),f=3600*c,p=60*c;switch(n.length){default:case 1:s=parseInt(n[0],10);break;case 2:r=parseInt(n[0],10),s=parseInt(n[1],10);break;case 3:o=parseInt(n[0],10),r=parseInt(n[1],10),s=parseInt(n[2],10);break;case 4:o=parseInt(n[0],10),r=parseInt(n[1],10),s=parseInt(n[2],10),l=parseInt(n[3],10);break}return a(t)?(d=60*o+r,i=f*o+p*r+c*s+l-u*(d-Math.floor(d/10))):i=(f*o+p*r+t*s+l)/t,parseFloat(i.toFixed(3))}function d(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:25;e=!e||"number"!==typeof e||e<0?0:e;for(var i=Math.floor(e/3600)%24,o=Math.floor(e/60)%60,r=Math.floor(e%60),a=Math.floor((e%1*n).toFixed(3)),s=[[a,"f"],[r,"s"],[o,"m"],[i,"h"]],l=t.timeFormat,d=l[1]===l[0],u=d?2:1,c=l.length<u?l[u]:":",f=l[0],p=!1,m=0,h=s.length;m<h;m++)if(~l.indexOf(s[m][1]))p=!0;else if(p){for(var v=!1,g=m;g<h;g++)if(s[g][0]>0){v=!0;break}if(!v)break;d||(l=f+l),l=s[m][1]+c+l,d&&(l=s[m][1]+l),f=s[m][1]}t.timeFormat=l}function u(e){if("string"!==typeof e)throw new TypeError("Argument must be a string value");e=e.replace(",",".");var t=~e.indexOf(".")?e.split(".")[1].length:0,n=0,i=1;e=e.split(":").reverse();for(var o=0,r=e.length;o<r;o++)i=1,o>0&&(i=Math.pow(60,o)),n+=Number(e[o])*i;return Number(n.toFixed(t))}o.default.Utils=o.default.Utils||{},o.default.Utils.secondsToTimeCode=s,o.default.Utils.timeCodeToSeconds=l,o.default.Utils.calculateTimeFormat=d,o.default.Utils.convertSMPTEtoSeconds=u},{7:7}]},{},[29,6,5,15,23,20,19,21,22,24,16,18,17,9,10,11,12,13,14])}).call(this,n("c8ba"))},"857a":function(e,t,n){var i=n("1d80"),o=/"/g;e.exports=function(e,t,n,r){var a=String(i(e)),s="<"+t;return""!==n&&(s+=" "+n+'="'+String(r).replace(o,"&quot;")+'"'),s+">"+a+"</"+t+">"}},9911:function(e,t,n){"use strict";var i=n("23e7"),o=n("857a"),r=n("af03");i({target:"String",proto:!0,forced:r("link")},{link:function(e){return o(this,"a","href",e)}})},a4bd:function(e,t,n){"use strict";var i=n("ab08"),o=n.n(i);o.a},ab08:function(e,t,n){},af03:function(e,t,n){var i=n("d039");e.exports=function(e){return i((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3}))}},bf28:function(e,t,n){},e7e5:function(e,t,n){"use strict";n("68ef"),n("a71a"),n("9d70"),n("3743"),n("4d75"),n("e3b3"),n("b258")}}]);