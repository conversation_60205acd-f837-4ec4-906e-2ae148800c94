(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["wallet"],{"0866":function(t,a,e){"use strict";e.d(a,"d",(function(){return l})),e.d(a,"a",(function(){return o})),e.d(a,"e",(function(){return c})),e.d(a,"b",(function(){return r})),e.d(a,"c",(function(){return d}));var i=navigator.userAgent.toLowerCase()||window.navigator.userAgent.toLowerCase(),n=(/youpin/i.test(i),/xianmai/i.test(i)),s="";s=n?"xm://xianmai":"yp://youpin";"".concat(s,"?position=login");var l="".concat(s,"?web="),o="".concat(s,"?position=authentication"),c="".concat(s,"?position=withdraw"),r="".concat(s,"?openBrowser="),d="".concat(s,"?position=wantToJoinIn");"".concat(s,"?position=mywallet")},"0e66":function(t,a,e){},1581:function(t,a,e){"use strict";var i=e("0e66"),n=e.n(i);n.a},"7ef7":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"main"},[i("van-nav-bar",{attrs:{title:t.title,"left-arrow":"",fixed:""},on:{"click-left":t.goBack}}),i("div",{staticClass:"wallet"},[i("div",{staticClass:"wallet--card"},[i("div",{staticClass:"wallet--card--block"},[i("p",[i("span",[t._v("￥")]),t._v(t._s(t.balance))]),i("p",[t._v("可用余额")]),i("button",{style:t.isInit&&t.withdraw_btn?"":"visibility: hidden",on:{click:t.toWallet}},[t._v("转到先迈提现")])])]),i("div",{staticClass:"wallet--list"},[!t.finished||t.finished&&t.list.length>0?i("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.getData},model:{value:t.loading,callback:function(a){t.loading=a},expression:"loading"}},[i("div",{staticClass:"wallet--list--row wallet--list--header"},[i("div",{staticClass:"wallet--list--row--col wallet--list--header--col"},[t._v("收益时间")]),i("div",{staticClass:"wallet--list--row--col wallet--list--header--col"},[t._v("类型")]),i("div",{staticClass:"wallet--list--row--col wallet--list--header--col"},[t._v("收益")])]),t._l(t.list,(function(a,e){return i("div",{key:e,staticClass:"wallet--list--row"},[i("div",{staticClass:"wallet--list--row--col wallet--list--row--time"},[i("p",[t._v(t._s(a.create_time.split(" ")[0]))]),i("p",[t._v(t._s(a.create_time.split(" ")[1]))])]),i("div",{staticClass:"wallet--list--row--col wallet--list--row--name"},[i("p",[t._v(t._s(a.type_txt))])]),i("div",{staticClass:"wallet--list--row--col wallet--list--row--avail"},[t._v("￥"+t._s(a.account_data))])])}))],2):i("div",{staticClass:"wallet--list--default"},[i("img",{attrs:{src:e("f1b1")}}),i("p",[t._v("当前无流水数据")])])],1)])],1)},n=[],s=(e("4160"),e("d3b7"),e("159b"),e("e7e5"),e("d399")),l=e("5530"),o=e("2f62"),c=e("6917"),r=e("0866"),d=e("c391"),u=e("ce3a"),f={name:"wallet",data:function(){return{reference:0,title:"钱包管理",loading:!1,finished:!1,list:[],page:1,balance:0,maidou:0,is_vip:0,withdraw_btn:1,change_btn:1,isInit:!1}},computed:Object(l["a"])({},Object(o["b"])(["token"])),methods:{goBack:function(){this.reference?this.$router.back():Object(c["a"])("0")},getData:function(){var t=this,a={token:this.token,page:this.page,page_size:10},e=s["a"].loading({duration:0,forbidClick:!0,message:"加载中..."});this.loading=!0,this.$axios.post(Object(d["a"])(u["a"].userWallet),a).then((function(a){e.clear();var i=a.data;if(0==i.code){if(t.isInit=!0,t.balance=i.data.balance,t.maidou=i.data.maidou,t.is_vip=i.data.is_vip,t.withdraw_btn=i.data.withdraw_btn,t.change_btn=i.data.change_btn,i.data.flow.forEach((function(a){t.list.push(a)})),i.data.is_last_page)return t.finished=!0,void(t.loading=!0);t.loading=!1,t.page+=1}else Object(s["a"])(i.message)})).catch((function(t){Object(s["a"])(t.message)}))},toWallet:function(){var t=this,a=s["a"].loading({duration:0,forbidClick:!0,message:"加载中..."}),e={token:this.token};this.$axios.post(Object(d["a"])(u["a"].balanceChangeXm),e).then((function(e){var i=e.data;a.clear(),0!=i.code?Object(s["a"])(i.message):(t.balance=0,Object(c["e"])(r["e"]))})).catch((function(t){Object(s["a"])(t.message)})).finally((function(){}))},toTransform:function(){this.$router.push({name:"beanConversion",query:{reference:1}})}},created:function(){this.reference=this.$route.query.reference?this.$route.query.reference:0}},w=f,h=(e("1581"),e("2877")),v=Object(h["a"])(w,i,n,!1,null,"383baf18",null);a["default"]=v.exports},e7e5:function(t,a,e){"use strict";e("68ef"),e("a71a"),e("9d70"),e("3743"),e("4d75"),e("e3b3"),e("b258")},f1b1:function(t,a,e){t.exports=e.p+"img/img-shopper-default.cb55b4c9.png"}}]);