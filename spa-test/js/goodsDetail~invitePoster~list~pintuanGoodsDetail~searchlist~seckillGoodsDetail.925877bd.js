(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["goodsDetail~invitePoster~list~pintuanGoodsDetail~searchlist~seckillGoodsDetail"],{"0b25":function(t,e,r){var n=r("a691"),i=r("50c4");t.exports=function(t){if(void 0===t)return 0;var e=n(t),r=i(e);if(e!==r)throw RangeError("Wrong length or index");return r}},"0d3b":function(t,e,r){var n=r("d039"),i=r("b622"),a=r("c430"),o=i("iterator");t.exports=!n((function(){var t=new URL("b?a=1&b=2&c=3","http://a"),e=t.searchParams,r="";return t.pathname="c%20d",e.forEach((function(t,n){e["delete"]("b"),r+=n+t})),a&&!t.toJSON||!e.sort||"http://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host}))},"145e":function(t,e,r){"use strict";var n=r("7b0b"),i=r("23cb"),a=r("50c4"),o=Math.min;t.exports=[].copyWithin||function(t,e){var r=n(this),s=a(r.length),u=i(t,s),h=i(e,s),c=arguments.length>2?arguments[2]:void 0,l=o((void 0===c?s:i(c,s))-h,s-u),f=1;h<u&&u<h+l&&(f=-1,h+=l-1,u+=l-1);while(l-- >0)h in r?r[u]=r[h]:delete r[u],u+=f,h+=f;return r}},"170b":function(t,e,r){"use strict";var n=r("ebb5"),i=r("50c4"),a=r("23cb"),o=r("4840"),s=n.aTypedArray,u=n.exportTypedArrayMethod;u("subarray",(function(t,e){var r=s(this),n=r.length,u=a(t,n);return new(o(r,r.constructor))(r.buffer,r.byteOffset+u*r.BYTES_PER_ELEMENT,i((void 0===e?n:a(e,n))-u))}))},"182d":function(t,e,r){var n=r("f8cd");t.exports=function(t,e){var r=n(t);if(r%e)throw RangeError("Wrong offset");return r}},"219c":function(t,e,r){"use strict";var n=r("ebb5"),i=n.aTypedArray,a=n.exportTypedArrayMethod,o=[].sort;a("sort",(function(t){return o.call(i(this),t)}))},"25a1":function(t,e,r){"use strict";var n=r("ebb5"),i=r("d58f").right,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("reduceRight",(function(t){return i(a(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},2954:function(t,e,r){"use strict";var n=r("ebb5"),i=r("4840"),a=r("d039"),o=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod,h=[].slice,c=a((function(){new Int8Array(1).slice()}));u("slice",(function(t,e){var r=h.call(o(this),t,e),n=i(this,this.constructor),a=0,u=r.length,c=new(s(n))(u);while(u>a)c[a]=r[a++];return c}),c)},"2b3d":function(t,e,r){"use strict";r("3ca3");var n,i=r("23e7"),a=r("83ab"),o=r("0d3b"),s=r("da84"),u=r("37e8"),h=r("6eeb"),c=r("19aa"),l=r("5135"),f=r("60da"),d=r("4df4"),p=r("6547").codeAt,g=r("5fb2"),v=r("d44e"),y=r("9861"),m=r("69f3"),b=s.URL,w=y.URLSearchParams,A=y.getState,T=m.set,x=m.getterFor("URL"),L=Math.floor,_=Math.pow,k="Invalid authority",E="Invalid scheme",C="Invalid host",R="Invalid port",B=/[A-Za-z]/,I=/[\d+\-.A-Za-z]/,D=/\d/,P=/^(0x|0X)/,S=/^[0-7]+$/,M=/^\d+$/,U=/^[\dA-Fa-f]+$/,O=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,N=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,F=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,H=/[\u0009\u000A\u000D]/g,q=function(t,e){var r,n,i;if("["==e.charAt(0)){if("]"!=e.charAt(e.length-1))return C;if(r=G(e.slice(1,-1)),!r)return C;t.host=r}else if($(t)){if(e=g(e),O.test(e))return C;if(r=j(e),null===r)return C;t.host=r}else{if(N.test(e))return C;for(r="",n=d(e),i=0;i<n.length;i++)r+=Q(n[i],W);t.host=r}},j=function(t){var e,r,n,i,a,o,s,u=t.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),e=u.length,e>4)return t;for(r=[],n=0;n<e;n++){if(i=u[n],""==i)return t;if(a=10,i.length>1&&"0"==i.charAt(0)&&(a=P.test(i)?16:8,i=i.slice(8==a?1:2)),""===i)o=0;else{if(!(10==a?M:8==a?S:U).test(i))return t;o=parseInt(i,a)}r.push(o)}for(n=0;n<e;n++)if(o=r[n],n==e-1){if(o>=_(256,5-e))return null}else if(o>255)return null;for(s=r.pop(),n=0;n<r.length;n++)s+=r[n]*_(256,3-n);return s},G=function(t){var e,r,n,i,a,o,s,u=[0,0,0,0,0,0,0,0],h=0,c=null,l=0,f=function(){return t.charAt(l)};if(":"==f()){if(":"!=t.charAt(1))return;l+=2,h++,c=h}while(f()){if(8==h)return;if(":"!=f()){e=r=0;while(r<4&&U.test(f()))e=16*e+parseInt(f(),16),l++,r++;if("."==f()){if(0==r)return;if(l-=r,h>6)return;n=0;while(f()){if(i=null,n>0){if(!("."==f()&&n<4))return;l++}if(!D.test(f()))return;while(D.test(f())){if(a=parseInt(f(),10),null===i)i=a;else{if(0==i)return;i=10*i+a}if(i>255)return;l++}u[h]=256*u[h]+i,n++,2!=n&&4!=n||h++}if(4!=n)return;break}if(":"==f()){if(l++,!f())return}else if(f())return;u[h++]=e}else{if(null!==c)return;l++,h++,c=h}}if(null!==c){o=h-c,h=7;while(0!=h&&o>0)s=u[h],u[h--]=u[c+o-1],u[c+--o]=s}else if(8!=h)return;return u},Y=function(t){for(var e=null,r=1,n=null,i=0,a=0;a<8;a++)0!==t[a]?(i>r&&(e=n,r=i),n=null,i=0):(null===n&&(n=a),++i);return i>r&&(e=n,r=i),e},V=function(t){var e,r,n,i;if("number"==typeof t){for(e=[],r=0;r<4;r++)e.unshift(t%256),t=L(t/256);return e.join(".")}if("object"==typeof t){for(e="",n=Y(t),r=0;r<8;r++)i&&0===t[r]||(i&&(i=!1),n===r?(e+=r?":":"::",i=!0):(e+=t[r].toString(16),r<7&&(e+=":")));return"["+e+"]"}return t},W={},K=f({},W,{" ":1,'"':1,"<":1,">":1,"`":1}),X=f({},K,{"#":1,"?":1,"{":1,"}":1}),J=f({},X,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Q=function(t,e){var r=p(t,0);return r>32&&r<127&&!l(e,t)?t:encodeURIComponent(t)},z={ftp:21,file:null,http:80,https:443,ws:80,wss:443},$=function(t){return l(z,t.scheme)},Z=function(t){return""!=t.username||""!=t.password},tt=function(t){return!t.host||t.cannotBeABaseURL||"file"==t.scheme},et=function(t,e){var r;return 2==t.length&&B.test(t.charAt(0))&&(":"==(r=t.charAt(1))||!e&&"|"==r)},rt=function(t){var e;return t.length>1&&et(t.slice(0,2))&&(2==t.length||"/"===(e=t.charAt(2))||"\\"===e||"?"===e||"#"===e)},nt=function(t){var e=t.path,r=e.length;!r||"file"==t.scheme&&1==r&&et(e[0],!0)||e.pop()},it=function(t){return"."===t||"%2e"===t.toLowerCase()},at=function(t){return t=t.toLowerCase(),".."===t||"%2e."===t||".%2e"===t||"%2e%2e"===t},ot={},st={},ut={},ht={},ct={},lt={},ft={},dt={},pt={},gt={},vt={},yt={},mt={},bt={},wt={},At={},Tt={},xt={},Lt={},_t={},kt={},Et=function(t,e,r,i){var a,o,s,u,h=r||ot,c=0,f="",p=!1,g=!1,v=!1;r||(t.scheme="",t.username="",t.password="",t.host=null,t.port=null,t.path=[],t.query=null,t.fragment=null,t.cannotBeABaseURL=!1,e=e.replace(F,"")),e=e.replace(H,""),a=d(e);while(c<=a.length){switch(o=a[c],h){case ot:if(!o||!B.test(o)){if(r)return E;h=ut;continue}f+=o.toLowerCase(),h=st;break;case st:if(o&&(I.test(o)||"+"==o||"-"==o||"."==o))f+=o.toLowerCase();else{if(":"!=o){if(r)return E;f="",h=ut,c=0;continue}if(r&&($(t)!=l(z,f)||"file"==f&&(Z(t)||null!==t.port)||"file"==t.scheme&&!t.host))return;if(t.scheme=f,r)return void($(t)&&z[t.scheme]==t.port&&(t.port=null));f="","file"==t.scheme?h=bt:$(t)&&i&&i.scheme==t.scheme?h=ht:$(t)?h=dt:"/"==a[c+1]?(h=ct,c++):(t.cannotBeABaseURL=!0,t.path.push(""),h=Lt)}break;case ut:if(!i||i.cannotBeABaseURL&&"#"!=o)return E;if(i.cannotBeABaseURL&&"#"==o){t.scheme=i.scheme,t.path=i.path.slice(),t.query=i.query,t.fragment="",t.cannotBeABaseURL=!0,h=kt;break}h="file"==i.scheme?bt:lt;continue;case ht:if("/"!=o||"/"!=a[c+1]){h=lt;continue}h=pt,c++;break;case ct:if("/"==o){h=gt;break}h=xt;continue;case lt:if(t.scheme=i.scheme,o==n)t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query=i.query;else if("/"==o||"\\"==o&&$(t))h=ft;else if("?"==o)t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query="",h=_t;else{if("#"!=o){t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.path.pop(),h=xt;continue}t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query=i.query,t.fragment="",h=kt}break;case ft:if(!$(t)||"/"!=o&&"\\"!=o){if("/"!=o){t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,h=xt;continue}h=gt}else h=pt;break;case dt:if(h=pt,"/"!=o||"/"!=f.charAt(c+1))continue;c++;break;case pt:if("/"!=o&&"\\"!=o){h=gt;continue}break;case gt:if("@"==o){p&&(f="%40"+f),p=!0,s=d(f);for(var y=0;y<s.length;y++){var m=s[y];if(":"!=m||v){var b=Q(m,J);v?t.password+=b:t.username+=b}else v=!0}f=""}else if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(t)){if(p&&""==f)return k;c-=d(f).length+1,f="",h=vt}else f+=o;break;case vt:case yt:if(r&&"file"==t.scheme){h=At;continue}if(":"!=o||g){if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(t)){if($(t)&&""==f)return C;if(r&&""==f&&(Z(t)||null!==t.port))return;if(u=q(t,f),u)return u;if(f="",h=Tt,r)return;continue}"["==o?g=!0:"]"==o&&(g=!1),f+=o}else{if(""==f)return C;if(u=q(t,f),u)return u;if(f="",h=mt,r==yt)return}break;case mt:if(!D.test(o)){if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(t)||r){if(""!=f){var w=parseInt(f,10);if(w>65535)return R;t.port=$(t)&&w===z[t.scheme]?null:w,f=""}if(r)return;h=Tt;continue}return R}f+=o;break;case bt:if(t.scheme="file","/"==o||"\\"==o)h=wt;else{if(!i||"file"!=i.scheme){h=xt;continue}if(o==n)t.host=i.host,t.path=i.path.slice(),t.query=i.query;else if("?"==o)t.host=i.host,t.path=i.path.slice(),t.query="",h=_t;else{if("#"!=o){rt(a.slice(c).join(""))||(t.host=i.host,t.path=i.path.slice(),nt(t)),h=xt;continue}t.host=i.host,t.path=i.path.slice(),t.query=i.query,t.fragment="",h=kt}}break;case wt:if("/"==o||"\\"==o){h=At;break}i&&"file"==i.scheme&&!rt(a.slice(c).join(""))&&(et(i.path[0],!0)?t.path.push(i.path[0]):t.host=i.host),h=xt;continue;case At:if(o==n||"/"==o||"\\"==o||"?"==o||"#"==o){if(!r&&et(f))h=xt;else if(""==f){if(t.host="",r)return;h=Tt}else{if(u=q(t,f),u)return u;if("localhost"==t.host&&(t.host=""),r)return;f="",h=Tt}continue}f+=o;break;case Tt:if($(t)){if(h=xt,"/"!=o&&"\\"!=o)continue}else if(r||"?"!=o)if(r||"#"!=o){if(o!=n&&(h=xt,"/"!=o))continue}else t.fragment="",h=kt;else t.query="",h=_t;break;case xt:if(o==n||"/"==o||"\\"==o&&$(t)||!r&&("?"==o||"#"==o)){if(at(f)?(nt(t),"/"==o||"\\"==o&&$(t)||t.path.push("")):it(f)?"/"==o||"\\"==o&&$(t)||t.path.push(""):("file"==t.scheme&&!t.path.length&&et(f)&&(t.host&&(t.host=""),f=f.charAt(0)+":"),t.path.push(f)),f="","file"==t.scheme&&(o==n||"?"==o||"#"==o))while(t.path.length>1&&""===t.path[0])t.path.shift();"?"==o?(t.query="",h=_t):"#"==o&&(t.fragment="",h=kt)}else f+=Q(o,X);break;case Lt:"?"==o?(t.query="",h=_t):"#"==o?(t.fragment="",h=kt):o!=n&&(t.path[0]+=Q(o,W));break;case _t:r||"#"!=o?o!=n&&("'"==o&&$(t)?t.query+="%27":t.query+="#"==o?"%23":Q(o,W)):(t.fragment="",h=kt);break;case kt:o!=n&&(t.fragment+=Q(o,K));break}c++}},Ct=function(t){var e,r,n=c(this,Ct,"URL"),i=arguments.length>1?arguments[1]:void 0,o=String(t),s=T(n,{type:"URL"});if(void 0!==i)if(i instanceof Ct)e=x(i);else if(r=Et(e={},String(i)),r)throw TypeError(r);if(r=Et(s,o,null,e),r)throw TypeError(r);var u=s.searchParams=new w,h=A(u);h.updateSearchParams(s.query),h.updateURL=function(){s.query=String(u)||null},a||(n.href=Bt.call(n),n.origin=It.call(n),n.protocol=Dt.call(n),n.username=Pt.call(n),n.password=St.call(n),n.host=Mt.call(n),n.hostname=Ut.call(n),n.port=Ot.call(n),n.pathname=Nt.call(n),n.search=Ft.call(n),n.searchParams=Ht.call(n),n.hash=qt.call(n))},Rt=Ct.prototype,Bt=function(){var t=x(this),e=t.scheme,r=t.username,n=t.password,i=t.host,a=t.port,o=t.path,s=t.query,u=t.fragment,h=e+":";return null!==i?(h+="//",Z(t)&&(h+=r+(n?":"+n:"")+"@"),h+=V(i),null!==a&&(h+=":"+a)):"file"==e&&(h+="//"),h+=t.cannotBeABaseURL?o[0]:o.length?"/"+o.join("/"):"",null!==s&&(h+="?"+s),null!==u&&(h+="#"+u),h},It=function(){var t=x(this),e=t.scheme,r=t.port;if("blob"==e)try{return new URL(e.path[0]).origin}catch(n){return"null"}return"file"!=e&&$(t)?e+"://"+V(t.host)+(null!==r?":"+r:""):"null"},Dt=function(){return x(this).scheme+":"},Pt=function(){return x(this).username},St=function(){return x(this).password},Mt=function(){var t=x(this),e=t.host,r=t.port;return null===e?"":null===r?V(e):V(e)+":"+r},Ut=function(){var t=x(this).host;return null===t?"":V(t)},Ot=function(){var t=x(this).port;return null===t?"":String(t)},Nt=function(){var t=x(this),e=t.path;return t.cannotBeABaseURL?e[0]:e.length?"/"+e.join("/"):""},Ft=function(){var t=x(this).query;return t?"?"+t:""},Ht=function(){return x(this).searchParams},qt=function(){var t=x(this).fragment;return t?"#"+t:""},jt=function(t,e){return{get:t,set:e,configurable:!0,enumerable:!0}};if(a&&u(Rt,{href:jt(Bt,(function(t){var e=x(this),r=String(t),n=Et(e,r);if(n)throw TypeError(n);A(e.searchParams).updateSearchParams(e.query)})),origin:jt(It),protocol:jt(Dt,(function(t){var e=x(this);Et(e,String(t)+":",ot)})),username:jt(Pt,(function(t){var e=x(this),r=d(String(t));if(!tt(e)){e.username="";for(var n=0;n<r.length;n++)e.username+=Q(r[n],J)}})),password:jt(St,(function(t){var e=x(this),r=d(String(t));if(!tt(e)){e.password="";for(var n=0;n<r.length;n++)e.password+=Q(r[n],J)}})),host:jt(Mt,(function(t){var e=x(this);e.cannotBeABaseURL||Et(e,String(t),vt)})),hostname:jt(Ut,(function(t){var e=x(this);e.cannotBeABaseURL||Et(e,String(t),yt)})),port:jt(Ot,(function(t){var e=x(this);tt(e)||(t=String(t),""==t?e.port=null:Et(e,t,mt))})),pathname:jt(Nt,(function(t){var e=x(this);e.cannotBeABaseURL||(e.path=[],Et(e,t+"",Tt))})),search:jt(Ft,(function(t){var e=x(this);t=String(t),""==t?e.query=null:("?"==t.charAt(0)&&(t=t.slice(1)),e.query="",Et(e,t,_t)),A(e.searchParams).updateSearchParams(e.query)})),searchParams:jt(Ht),hash:jt(qt,(function(t){var e=x(this);t=String(t),""!=t?("#"==t.charAt(0)&&(t=t.slice(1)),e.fragment="",Et(e,t,kt)):e.fragment=null}))}),h(Rt,"toJSON",(function(){return Bt.call(this)}),{enumerable:!0}),h(Rt,"toString",(function(){return Bt.call(this)}),{enumerable:!0}),b){var Gt=b.createObjectURL,Yt=b.revokeObjectURL;Gt&&h(Ct,"createObjectURL",(function(t){return Gt.apply(b,arguments)})),Yt&&h(Ct,"revokeObjectURL",(function(t){return Yt.apply(b,arguments)}))}v(Ct,"URL"),i({global:!0,forced:!o,sham:!a},{URL:Ct})},"3280b":function(t,e,r){"use strict";var n=r("ebb5"),i=r("e58c"),a=n.aTypedArray,o=n.exportTypedArrayMethod;o("lastIndexOf",(function(t){return i.apply(a(this),arguments)}))},"3a7b":function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").findIndex,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("findIndex",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},"3c5d":function(t,e,r){"use strict";var n=r("ebb5"),i=r("50c4"),a=r("182d"),o=r("7b0b"),s=r("d039"),u=n.aTypedArray,h=n.exportTypedArrayMethod,c=s((function(){new Int8Array(1).set({})}));h("set",(function(t){u(this);var e=a(arguments.length>1?arguments[1]:void 0,1),r=this.length,n=o(t),s=i(n.length),h=0;if(s+e>r)throw RangeError("Wrong length");while(h<s)this[e+h]=n[h++]}),c)},"3ca3":function(t,e,r){"use strict";var n=r("6547").charAt,i=r("69f3"),a=r("7dd0"),o="String Iterator",s=i.set,u=i.getterFor(o);a(String,"String",(function(t){s(this,{type:o,string:String(t),index:0})}),(function(){var t,e=u(this),r=e.string,i=e.index;return i>=r.length?{value:void 0,done:!0}:(t=n(r,i),e.index+=t.length,{value:t,done:!1})}))},"3fcc":function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").map,a=r("4840"),o=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod;u("map",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(s(a(t,t.constructor)))(e)}))}))},"4df4":function(t,e,r){"use strict";var n=r("0366"),i=r("7b0b"),a=r("9bdd"),o=r("e95a"),s=r("50c4"),u=r("8418"),h=r("35a1");t.exports=function(t){var e,r,c,l,f,d,p=i(t),g="function"==typeof this?this:Array,v=arguments.length,y=v>1?arguments[1]:void 0,m=void 0!==y,b=h(p),w=0;if(m&&(y=n(y,v>2?arguments[2]:void 0,2)),void 0==b||g==Array&&o(b))for(e=s(p.length),r=new g(e);e>w;w++)d=m?y(p[w],w):p[w],u(r,w,d);else for(l=b.call(p),f=l.next,r=new g;!(c=f.call(l)).done;w++)d=m?a(l,y,[c.value,w],!0):c.value,u(r,w,d);return r.length=w,r}},"5cc6":function(t,e,r){var n=r("74e8");n("Uint8",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},"5f96":function(t,e,r){"use strict";var n=r("ebb5"),i=n.aTypedArray,a=n.exportTypedArrayMethod,o=[].join;a("join",(function(t){return o.apply(i(this),arguments)}))},"5fb2":function(t,e,r){"use strict";var n=2147483647,i=36,a=1,o=26,s=38,u=700,h=72,c=128,l="-",f=/[^\0-\u007E]/,d=/[.\u3002\uFF0E\uFF61]/g,p="Overflow: input needs wider integers to process",g=i-a,v=Math.floor,y=String.fromCharCode,m=function(t){var e=[],r=0,n=t.length;while(r<n){var i=t.charCodeAt(r++);if(i>=55296&&i<=56319&&r<n){var a=t.charCodeAt(r++);56320==(64512&a)?e.push(((1023&i)<<10)+(1023&a)+65536):(e.push(i),r--)}else e.push(i)}return e},b=function(t){return t+22+75*(t<26)},w=function(t,e,r){var n=0;for(t=r?v(t/u):t>>1,t+=v(t/e);t>g*o>>1;n+=i)t=v(t/g);return v(n+(g+1)*t/(t+s))},A=function(t){var e=[];t=m(t);var r,s,u=t.length,f=c,d=0,g=h;for(r=0;r<t.length;r++)s=t[r],s<128&&e.push(y(s));var A=e.length,T=A;A&&e.push(l);while(T<u){var x=n;for(r=0;r<t.length;r++)s=t[r],s>=f&&s<x&&(x=s);var L=T+1;if(x-f>v((n-d)/L))throw RangeError(p);for(d+=(x-f)*L,f=x,r=0;r<t.length;r++){if(s=t[r],s<f&&++d>n)throw RangeError(p);if(s==f){for(var _=d,k=i;;k+=i){var E=k<=g?a:k>=g+o?o:k-g;if(_<E)break;var C=_-E,R=i-E;e.push(y(b(E+C%R))),_=v(C/R)}e.push(y(b(_))),g=w(d,L,T==A),d=0,++T}}++d,++f}return e.join("")};t.exports=function(t){var e,r,n=[],i=t.toLowerCase().replace(d,".").split(".");for(e=0;e<i.length;e++)r=i[e],n.push(f.test(r)?"xn--"+A(r):r);return n.join(".")}},"60bd":function(t,e,r){"use strict";var n=r("da84"),i=r("ebb5"),a=r("e260"),o=r("b622"),s=o("iterator"),u=n.Uint8Array,h=a.values,c=a.keys,l=a.entries,f=i.aTypedArray,d=i.exportTypedArrayMethod,p=u&&u.prototype[s],g=!!p&&("values"==p.name||void 0==p.name),v=function(){return h.call(f(this))};d("entries",(function(){return l.call(f(this))})),d("keys",(function(){return c.call(f(this))})),d("values",v,!g),d(s,v,!g)},"621a":function(t,e,r){"use strict";var n=r("da84"),i=r("83ab"),a=r("a981"),o=r("9112"),s=r("e2cc"),u=r("d039"),h=r("19aa"),c=r("a691"),l=r("50c4"),f=r("0b25"),d=r("77a7"),p=r("e163"),g=r("d2bb"),v=r("241c").f,y=r("9bf2").f,m=r("81d5"),b=r("d44e"),w=r("69f3"),A=w.get,T=w.set,x="ArrayBuffer",L="DataView",_="prototype",k="Wrong length",E="Wrong index",C=n[x],R=C,B=n[L],I=B&&B[_],D=Object.prototype,P=n.RangeError,S=d.pack,M=d.unpack,U=function(t){return[255&t]},O=function(t){return[255&t,t>>8&255]},N=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},F=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},H=function(t){return S(t,23,4)},q=function(t){return S(t,52,8)},j=function(t,e){y(t[_],e,{get:function(){return A(this)[e]}})},G=function(t,e,r,n){var i=f(r),a=A(t);if(i+e>a.byteLength)throw P(E);var o=A(a.buffer).bytes,s=i+a.byteOffset,u=o.slice(s,s+e);return n?u:u.reverse()},Y=function(t,e,r,n,i,a){var o=f(r),s=A(t);if(o+e>s.byteLength)throw P(E);for(var u=A(s.buffer).bytes,h=o+s.byteOffset,c=n(+i),l=0;l<e;l++)u[h+l]=c[a?l:e-l-1]};if(a){if(!u((function(){C(1)}))||!u((function(){new C(-1)}))||u((function(){return new C,new C(1.5),new C(NaN),C.name!=x}))){R=function(t){return h(this,R),new C(f(t))};for(var V,W=R[_]=C[_],K=v(C),X=0;K.length>X;)(V=K[X++])in R||o(R,V,C[V]);W.constructor=R}g&&p(I)!==D&&g(I,D);var J=new B(new R(2)),Q=I.setInt8;J.setInt8(0,2147483648),J.setInt8(1,2147483649),!J.getInt8(0)&&J.getInt8(1)||s(I,{setInt8:function(t,e){Q.call(this,t,e<<24>>24)},setUint8:function(t,e){Q.call(this,t,e<<24>>24)}},{unsafe:!0})}else R=function(t){h(this,R,x);var e=f(t);T(this,{bytes:m.call(new Array(e),0),byteLength:e}),i||(this.byteLength=e)},B=function(t,e,r){h(this,B,L),h(t,R,L);var n=A(t).byteLength,a=c(e);if(a<0||a>n)throw P("Wrong offset");if(r=void 0===r?n-a:l(r),a+r>n)throw P(k);T(this,{buffer:t,byteLength:r,byteOffset:a}),i||(this.buffer=t,this.byteLength=r,this.byteOffset=a)},i&&(j(R,"byteLength"),j(B,"buffer"),j(B,"byteLength"),j(B,"byteOffset")),s(B[_],{getInt8:function(t){return G(this,1,t)[0]<<24>>24},getUint8:function(t){return G(this,1,t)[0]},getInt16:function(t){var e=G(this,2,t,arguments.length>1?arguments[1]:void 0);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=G(this,2,t,arguments.length>1?arguments[1]:void 0);return e[1]<<8|e[0]},getInt32:function(t){return F(G(this,4,t,arguments.length>1?arguments[1]:void 0))},getUint32:function(t){return F(G(this,4,t,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(t){return M(G(this,4,t,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(t){return M(G(this,8,t,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(t,e){Y(this,1,t,U,e)},setUint8:function(t,e){Y(this,1,t,U,e)},setInt16:function(t,e){Y(this,2,t,O,e,arguments.length>2?arguments[2]:void 0)},setUint16:function(t,e){Y(this,2,t,O,e,arguments.length>2?arguments[2]:void 0)},setInt32:function(t,e){Y(this,4,t,N,e,arguments.length>2?arguments[2]:void 0)},setUint32:function(t,e){Y(this,4,t,N,e,arguments.length>2?arguments[2]:void 0)},setFloat32:function(t,e){Y(this,4,t,H,e,arguments.length>2?arguments[2]:void 0)},setFloat64:function(t,e){Y(this,8,t,q,e,arguments.length>2?arguments[2]:void 0)}});b(R,x),b(B,L),t.exports={ArrayBuffer:R,DataView:B}},"649e":function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").some,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("some",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},"72f7":function(t,e,r){"use strict";var n=r("ebb5").exportTypedArrayMethod,i=r("d039"),a=r("da84"),o=a.Uint8Array,s=o&&o.prototype||{},u=[].toString,h=[].join;i((function(){u.call({})}))&&(u=function(){return h.call(this)});var c=s.toString!=u;n("toString",u,c)},"735e":function(t,e,r){"use strict";var n=r("ebb5"),i=r("81d5"),a=n.aTypedArray,o=n.exportTypedArrayMethod;o("fill",(function(t){return i.apply(a(this),arguments)}))},"74e8":function(t,e,r){"use strict";var n=r("23e7"),i=r("da84"),a=r("83ab"),o=r("8aa7"),s=r("ebb5"),u=r("621a"),h=r("19aa"),c=r("5c6c"),l=r("9112"),f=r("50c4"),d=r("0b25"),p=r("182d"),g=r("c04e"),v=r("5135"),y=r("f5df"),m=r("861d"),b=r("7c73"),w=r("d2bb"),A=r("241c").f,T=r("a078"),x=r("b727").forEach,L=r("2626"),_=r("9bf2"),k=r("06cf"),E=r("69f3"),C=r("7156"),R=E.get,B=E.set,I=_.f,D=k.f,P=Math.round,S=i.RangeError,M=u.ArrayBuffer,U=u.DataView,O=s.NATIVE_ARRAY_BUFFER_VIEWS,N=s.TYPED_ARRAY_TAG,F=s.TypedArray,H=s.TypedArrayPrototype,q=s.aTypedArrayConstructor,j=s.isTypedArray,G="BYTES_PER_ELEMENT",Y="Wrong length",V=function(t,e){var r=0,n=e.length,i=new(q(t))(n);while(n>r)i[r]=e[r++];return i},W=function(t,e){I(t,e,{get:function(){return R(this)[e]}})},K=function(t){var e;return t instanceof M||"ArrayBuffer"==(e=y(t))||"SharedArrayBuffer"==e},X=function(t,e){return j(t)&&"symbol"!=typeof e&&e in t&&String(+e)==String(e)},J=function(t,e){return X(t,e=g(e,!0))?c(2,t[e]):D(t,e)},Q=function(t,e,r){return!(X(t,e=g(e,!0))&&m(r)&&v(r,"value"))||v(r,"get")||v(r,"set")||r.configurable||v(r,"writable")&&!r.writable||v(r,"enumerable")&&!r.enumerable?I(t,e,r):(t[e]=r.value,t)};a?(O||(k.f=J,_.f=Q,W(H,"buffer"),W(H,"byteOffset"),W(H,"byteLength"),W(H,"length")),n({target:"Object",stat:!0,forced:!O},{getOwnPropertyDescriptor:J,defineProperty:Q}),t.exports=function(t,e,r){var a=t.match(/\d+$/)[0]/8,s=t+(r?"Clamped":"")+"Array",u="get"+t,c="set"+t,g=i[s],v=g,y=v&&v.prototype,_={},k=function(t,e){var r=R(t);return r.view[u](e*a+r.byteOffset,!0)},E=function(t,e,n){var i=R(t);r&&(n=(n=P(n))<0?0:n>255?255:255&n),i.view[c](e*a+i.byteOffset,n,!0)},D=function(t,e){I(t,e,{get:function(){return k(this,e)},set:function(t){return E(this,e,t)},enumerable:!0})};O?o&&(v=e((function(t,e,r,n){return h(t,v,s),C(function(){return m(e)?K(e)?void 0!==n?new g(e,p(r,a),n):void 0!==r?new g(e,p(r,a)):new g(e):j(e)?V(v,e):T.call(v,e):new g(d(e))}(),t,v)})),w&&w(v,F),x(A(g),(function(t){t in v||l(v,t,g[t])})),v.prototype=y):(v=e((function(t,e,r,n){h(t,v,s);var i,o,u,c=0,l=0;if(m(e)){if(!K(e))return j(e)?V(v,e):T.call(v,e);i=e,l=p(r,a);var g=e.byteLength;if(void 0===n){if(g%a)throw S(Y);if(o=g-l,o<0)throw S(Y)}else if(o=f(n)*a,o+l>g)throw S(Y);u=o/a}else u=d(e),o=u*a,i=new M(o);B(t,{buffer:i,byteOffset:l,byteLength:o,length:u,view:new U(i)});while(c<u)D(t,c++)})),w&&w(v,F),y=v.prototype=b(H)),y.constructor!==v&&l(y,"constructor",v),N&&l(y,N,s),_[s]=v,n({global:!0,forced:v!=g,sham:!O},_),G in v||l(v,G,a),G in y||l(y,G,a),L(s)}):t.exports=function(){}},"77a7":function(t,e){var r=1/0,n=Math.abs,i=Math.pow,a=Math.floor,o=Math.log,s=Math.LN2,u=function(t,e,u){var h,c,l,f=new Array(u),d=8*u-e-1,p=(1<<d)-1,g=p>>1,v=23===e?i(2,-24)-i(2,-77):0,y=t<0||0===t&&1/t<0?1:0,m=0;for(t=n(t),t!=t||t===r?(c=t!=t?1:0,h=p):(h=a(o(t)/s),t*(l=i(2,-h))<1&&(h--,l*=2),t+=h+g>=1?v/l:v*i(2,1-g),t*l>=2&&(h++,l/=2),h+g>=p?(c=0,h=p):h+g>=1?(c=(t*l-1)*i(2,e),h+=g):(c=t*i(2,g-1)*i(2,e),h=0));e>=8;f[m++]=255&c,c/=256,e-=8);for(h=h<<e|c,d+=e;d>0;f[m++]=255&h,h/=256,d-=8);return f[--m]|=128*y,f},h=function(t,e){var n,a=t.length,o=8*a-e-1,s=(1<<o)-1,u=s>>1,h=o-7,c=a-1,l=t[c--],f=127&l;for(l>>=7;h>0;f=256*f+t[c],c--,h-=8);for(n=f&(1<<-h)-1,f>>=-h,h+=e;h>0;n=256*n+t[c],c--,h-=8);if(0===f)f=1-u;else{if(f===s)return n?NaN:l?-r:r;n+=i(2,e),f-=u}return(l?-1:1)*n*i(2,f-e)};t.exports={pack:u,unpack:h}},"81d5":function(t,e,r){"use strict";var n=r("7b0b"),i=r("23cb"),a=r("50c4");t.exports=function(t){var e=n(this),r=a(e.length),o=arguments.length,s=i(o>1?arguments[1]:void 0,r),u=o>2?arguments[2]:void 0,h=void 0===u?r:i(u,r);while(h>s)e[s++]=t;return e}},"82f8":function(t,e,r){"use strict";var n=r("ebb5"),i=r("4d64").includes,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("includes",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},"857a":function(t,e,r){var n=r("1d80"),i=/"/g;t.exports=function(t,e,r,a){var o=String(n(t)),s="<"+e;return""!==r&&(s+=" "+r+'="'+String(a).replace(i,"&quot;")+'"'),s+">"+o+"</"+e+">"}},"8aa7":function(t,e,r){var n=r("da84"),i=r("d039"),a=r("1c7e"),o=r("ebb5").NATIVE_ARRAY_BUFFER_VIEWS,s=n.ArrayBuffer,u=n.Int8Array;t.exports=!o||!i((function(){u(1)}))||!i((function(){new u(-1)}))||!a((function(t){new u,new u(null),new u(1.5),new u(t)}),!0)||i((function(){return 1!==new u(new s(2),1,void 0).length}))},9861:function(t,e,r){"use strict";r("e260");var n=r("23e7"),i=r("d066"),a=r("0d3b"),o=r("6eeb"),s=r("e2cc"),u=r("d44e"),h=r("9ed3"),c=r("69f3"),l=r("19aa"),f=r("5135"),d=r("0366"),p=r("f5df"),g=r("825a"),v=r("861d"),y=r("7c73"),m=r("5c6c"),b=r("9a1f"),w=r("35a1"),A=r("b622"),T=i("fetch"),x=i("Headers"),L=A("iterator"),_="URLSearchParams",k=_+"Iterator",E=c.set,C=c.getterFor(_),R=c.getterFor(k),B=/\+/g,I=Array(4),D=function(t){return I[t-1]||(I[t-1]=RegExp("((?:%[\\da-f]{2}){"+t+"})","gi"))},P=function(t){try{return decodeURIComponent(t)}catch(e){return t}},S=function(t){var e=t.replace(B," "),r=4;try{return decodeURIComponent(e)}catch(n){while(r)e=e.replace(D(r--),P);return e}},M=/[!'()~]|%20/g,U={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},O=function(t){return U[t]},N=function(t){return encodeURIComponent(t).replace(M,O)},F=function(t,e){if(e){var r,n,i=e.split("&"),a=0;while(a<i.length)r=i[a++],r.length&&(n=r.split("="),t.push({key:S(n.shift()),value:S(n.join("="))}))}},H=function(t){this.entries.length=0,F(this.entries,t)},q=function(t,e){if(t<e)throw TypeError("Not enough arguments")},j=h((function(t,e){E(this,{type:k,iterator:b(C(t).entries),kind:e})}),"Iterator",(function(){var t=R(this),e=t.kind,r=t.iterator.next(),n=r.value;return r.done||(r.value="keys"===e?n.key:"values"===e?n.value:[n.key,n.value]),r})),G=function(){l(this,G,_);var t,e,r,n,i,a,o,s,u,h=arguments.length>0?arguments[0]:void 0,c=this,d=[];if(E(c,{type:_,entries:d,updateURL:function(){},updateSearchParams:H}),void 0!==h)if(v(h))if(t=w(h),"function"===typeof t){e=t.call(h),r=e.next;while(!(n=r.call(e)).done){if(i=b(g(n.value)),a=i.next,(o=a.call(i)).done||(s=a.call(i)).done||!a.call(i).done)throw TypeError("Expected sequence with length 2");d.push({key:o.value+"",value:s.value+""})}}else for(u in h)f(h,u)&&d.push({key:u,value:h[u]+""});else F(d,"string"===typeof h?"?"===h.charAt(0)?h.slice(1):h:h+"")},Y=G.prototype;s(Y,{append:function(t,e){q(arguments.length,2);var r=C(this);r.entries.push({key:t+"",value:e+""}),r.updateURL()},delete:function(t){q(arguments.length,1);var e=C(this),r=e.entries,n=t+"",i=0;while(i<r.length)r[i].key===n?r.splice(i,1):i++;e.updateURL()},get:function(t){q(arguments.length,1);for(var e=C(this).entries,r=t+"",n=0;n<e.length;n++)if(e[n].key===r)return e[n].value;return null},getAll:function(t){q(arguments.length,1);for(var e=C(this).entries,r=t+"",n=[],i=0;i<e.length;i++)e[i].key===r&&n.push(e[i].value);return n},has:function(t){q(arguments.length,1);var e=C(this).entries,r=t+"",n=0;while(n<e.length)if(e[n++].key===r)return!0;return!1},set:function(t,e){q(arguments.length,1);for(var r,n=C(this),i=n.entries,a=!1,o=t+"",s=e+"",u=0;u<i.length;u++)r=i[u],r.key===o&&(a?i.splice(u--,1):(a=!0,r.value=s));a||i.push({key:o,value:s}),n.updateURL()},sort:function(){var t,e,r,n=C(this),i=n.entries,a=i.slice();for(i.length=0,r=0;r<a.length;r++){for(t=a[r],e=0;e<r;e++)if(i[e].key>t.key){i.splice(e,0,t);break}e===r&&i.push(t)}n.updateURL()},forEach:function(t){var e,r=C(this).entries,n=d(t,arguments.length>1?arguments[1]:void 0,3),i=0;while(i<r.length)e=r[i++],n(e.value,e.key,this)},keys:function(){return new j(this,"keys")},values:function(){return new j(this,"values")},entries:function(){return new j(this,"entries")}},{enumerable:!0}),o(Y,L,Y.entries),o(Y,"toString",(function(){var t,e=C(this).entries,r=[],n=0;while(n<e.length)t=e[n++],r.push(N(t.key)+"="+N(t.value));return r.join("&")}),{enumerable:!0}),u(G,_),n({global:!0,forced:!a},{URLSearchParams:G}),a||"function"!=typeof T||"function"!=typeof x||n({global:!0,enumerable:!0,forced:!0},{fetch:function(t){var e,r,n,i=[t];return arguments.length>1&&(e=arguments[1],v(e)&&(r=e.body,p(r)===_&&(n=e.headers?new x(e.headers):new x,n.has("content-type")||n.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),e=y(e,{body:m(0,String(r)),headers:m(0,n)}))),i.push(e)),T.apply(this,i)}}),t.exports={URLSearchParams:G,getState:C}},"9a1f":function(t,e,r){var n=r("825a"),i=r("35a1");t.exports=function(t){var e=i(t);if("function"!=typeof e)throw TypeError(String(t)+" is not iterable");return n(e.call(t))}},"9a8c":function(t,e,r){"use strict";var n=r("ebb5"),i=r("145e"),a=n.aTypedArray,o=n.exportTypedArrayMethod;o("copyWithin",(function(t,e){return i.call(a(this),t,e,arguments.length>2?arguments[2]:void 0)}))},a078:function(t,e,r){var n=r("7b0b"),i=r("50c4"),a=r("35a1"),o=r("e95a"),s=r("0366"),u=r("ebb5").aTypedArrayConstructor;t.exports=function(t){var e,r,h,c,l,f,d=n(t),p=arguments.length,g=p>1?arguments[1]:void 0,v=void 0!==g,y=a(d);if(void 0!=y&&!o(y)){l=y.call(d),f=l.next,d=[];while(!(c=f.call(l)).done)d.push(c.value)}for(v&&p>2&&(g=s(g,arguments[2],2)),r=i(d.length),h=new(u(this))(r),e=0;r>e;e++)h[e]=v?g(d[e],e):d[e];return h}},a662:function(t,e,r){"use strict";var n;r("99af"),r("cb29"),r("4de4"),r("c975"),r("d81d"),r("c19f"),r("ace4"),r("d3b7"),r("ac1f"),r("25f0"),r("3ca3"),r("1276"),r("cc71"),r("5cc6"),r("9a8c"),r("a975"),r("735e"),r("c1ac"),r("d139"),r("3a7b"),r("d5d6"),r("82f8"),r("e91f"),r("60bd"),r("5f96"),r("3280b"),r("3fcc"),r("ca91"),r("25a1"),r("cd26"),r("3c5d"),r("2954"),r("649e"),r("219c"),r("170b"),r("b39a"),r("72f7"),r("ddb0"),r("2b3d"),r("a15b"),r("466d"),r("5319");(function(){function t(t){this.mode=r.MODE_8BIT_BYTE,this.data=t,this.parsedData=[];for(var e=0,n=this.data.length;e<n;e++){var i=[],a=this.data.charCodeAt(e);a>65536?(i[0]=240|(1835008&a)>>>18,i[1]=128|(258048&a)>>>12,i[2]=128|(4032&a)>>>6,i[3]=128|63&a):a>2048?(i[0]=224|(61440&a)>>>12,i[1]=128|(4032&a)>>>6,i[2]=128|63&a):a>128?(i[0]=192|(1984&a)>>>6,i[1]=128|63&a):i[0]=a,this.parsedData.push(i)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function e(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}t.prototype={getLength:function(t){return this.parsedData.length},write:function(t){for(var e=0,r=this.parsedData.length;e<r;e++)t.put(this.parsedData[e],8)}},e.prototype={addData:function(e){var r=new t(e);this.dataList.push(r),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,r){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[n][i]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,r),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,r)},setupPositionProbePattern:function(t,e){for(var r=-1;r<=7;r++)if(!(t+r<=-1||this.moduleCount<=t+r))for(var n=-1;n<=7;n++)e+n<=-1||this.moduleCount<=e+n||(this.modules[t+r][e+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)},getBestMaskPattern:function(){for(var t=0,e=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=o.getLostPoint(this);(0==r||t>n)&&(t=n,e=r)}return e},createMovieClip:function(t,e,r){var n=t.createEmptyMovieClip(e,r),i=1;this.make();for(var a=0;a<this.modules.length;a++)for(var o=a*i,s=0;s<this.modules[a].length;s++){var u=s*i,h=this.modules[a][s];h&&(n.beginFill(0,100),n.moveTo(u,o),n.lineTo(u+i,o),n.lineTo(u+i,o+i),n.lineTo(u,o+i),n.endFill())}return n},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=o.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var r=0;r<t.length;r++){var n=t[e],i=t[r];if(null==this.modules[n][i])for(var a=-2;a<=2;a++)for(var s=-2;s<=2;s++)this.modules[n+a][i+s]=-2==a||2==a||-2==s||2==s||0==a&&0==s}},setupTypeNumber:function(t){for(var e=o.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!t&&1==(e>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(r=0;r<18;r++){n=!t&&1==(e>>r&1);this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n}},setupTypeInfo:function(t,e){for(var r=this.errorCorrectLevel<<3|e,n=o.getBCHTypeInfo(r),i=0;i<15;i++){var a=!t&&1==(n>>i&1);i<6?this.modules[i][8]=a:i<8?this.modules[i+1][8]=a:this.modules[this.moduleCount-15+i][8]=a}for(i=0;i<15;i++){a=!t&&1==(n>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=a:i<9?this.modules[8][15-i-1+1]=a:this.modules[8][15-i-1]=a}this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var r=-1,n=this.moduleCount-1,i=7,a=0,s=this.moduleCount-1;s>0;s-=2){6==s&&s--;while(1){for(var u=0;u<2;u++)if(null==this.modules[n][s-u]){var h=!1;a<t.length&&(h=1==(t[a]>>>i&1));var c=o.getMask(e,n,s-u);c&&(h=!h),this.modules[n][s-u]=h,i--,-1==i&&(a++,i=7)}if(n+=r,n<0||this.moduleCount<=n){n-=r,r=-r;break}}}}},e.PAD0=236,e.PAD1=17,e.createData=function(t,r,n){for(var i=c.getRSBlocks(t,r),a=new l,s=0;s<n.length;s++){var u=n[s];a.put(u.mode,4),a.put(u.getLength(),o.getLengthInBits(u.mode,t)),u.write(a)}var h=0;for(s=0;s<i.length;s++)h+=i[s].dataCount;if(a.getLengthInBits()>8*h)throw new Error("code length overflow. ("+a.getLengthInBits()+">"+8*h+")");a.getLengthInBits()+4<=8*h&&a.put(0,4);while(a.getLengthInBits()%8!=0)a.putBit(!1);while(1){if(a.getLengthInBits()>=8*h)break;if(a.put(e.PAD0,8),a.getLengthInBits()>=8*h)break;a.put(e.PAD1,8)}return e.createBytes(a,i)},e.createBytes=function(t,e){for(var r=0,n=0,i=0,a=new Array(e.length),s=new Array(e.length),u=0;u<e.length;u++){var c=e[u].dataCount,l=e[u].totalCount-c;n=Math.max(n,c),i=Math.max(i,l),a[u]=new Array(c);for(var f=0;f<a[u].length;f++)a[u][f]=255&t.buffer[f+r];r+=c;var d=o.getErrorCorrectPolynomial(l),p=new h(a[u],d.getLength()-1),g=p.mod(d);s[u]=new Array(d.getLength()-1);for(f=0;f<s[u].length;f++){var v=f+g.getLength()-s[u].length;s[u][f]=v>=0?g.get(v):0}}var y=0;for(f=0;f<e.length;f++)y+=e[f].totalCount;var m=new Array(y),b=0;for(f=0;f<n;f++)for(u=0;u<e.length;u++)f<a[u].length&&(m[b++]=a[u][f]);for(f=0;f<i;f++)for(u=0;u<e.length;u++)f<s[u].length&&(m[b++]=s[u][f]);return m};for(var r={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},i={L:1,M:0,Q:3,H:2},a={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},o={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(o.getBCHDigit(e)-o.getBCHDigit(o.G15)>=0)e^=o.G15<<o.getBCHDigit(e)-o.getBCHDigit(o.G15);return(t<<10|e)^o.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(o.getBCHDigit(e)-o.getBCHDigit(o.G18)>=0)e^=o.G18<<o.getBCHDigit(e)-o.getBCHDigit(o.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return o.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,r){switch(t){case a.PATTERN000:return(e+r)%2==0;case a.PATTERN001:return e%2==0;case a.PATTERN010:return r%3==0;case a.PATTERN011:return(e+r)%3==0;case a.PATTERN100:return(Math.floor(e/2)+Math.floor(r/3))%2==0;case a.PATTERN101:return e*r%2+e*r%3==0;case a.PATTERN110:return(e*r%2+e*r%3)%2==0;case a.PATTERN111:return(e*r%3+(e+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new h([1],0),r=0;r<t;r++)e=e.multiply(new h([1,s.gexp(r)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case r.MODE_NUMBER:return 10;case r.MODE_ALPHA_NUM:return 9;case r.MODE_8BIT_BYTE:return 8;case r.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case r.MODE_NUMBER:return 12;case r.MODE_ALPHA_NUM:return 11;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case r.MODE_NUMBER:return 14;case r.MODE_ALPHA_NUM:return 13;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),r=0,n=0;n<e;n++)for(var i=0;i<e;i++){for(var a=0,o=t.isDark(n,i),s=-1;s<=1;s++)if(!(n+s<0||e<=n+s))for(var u=-1;u<=1;u++)i+u<0||e<=i+u||0==s&&0==u||o==t.isDark(n+s,i+u)&&a++;a>5&&(r+=3+a-5)}for(n=0;n<e-1;n++)for(i=0;i<e-1;i++){var h=0;t.isDark(n,i)&&h++,t.isDark(n+1,i)&&h++,t.isDark(n,i+1)&&h++,t.isDark(n+1,i+1)&&h++,0!=h&&4!=h||(r+=3)}for(n=0;n<e;n++)for(i=0;i<e-6;i++)t.isDark(n,i)&&!t.isDark(n,i+1)&&t.isDark(n,i+2)&&t.isDark(n,i+3)&&t.isDark(n,i+4)&&!t.isDark(n,i+5)&&t.isDark(n,i+6)&&(r+=40);for(i=0;i<e;i++)for(n=0;n<e-6;n++)t.isDark(n,i)&&!t.isDark(n+1,i)&&t.isDark(n+2,i)&&t.isDark(n+3,i)&&t.isDark(n+4,i)&&!t.isDark(n+5,i)&&t.isDark(n+6,i)&&(r+=40);var c=0;for(i=0;i<e;i++)for(n=0;n<e;n++)t.isDark(n,i)&&c++;var l=Math.abs(100*c/e/e-50)/5;return r+=10*l,r}},s={glog:function(t){if(t<1)throw new Error("glog("+t+")");return s.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return s.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},u=0;u<8;u++)s.EXP_TABLE[u]=1<<u;for(u=8;u<256;u++)s.EXP_TABLE[u]=s.EXP_TABLE[u-4]^s.EXP_TABLE[u-5]^s.EXP_TABLE[u-6]^s.EXP_TABLE[u-8];for(u=0;u<255;u++)s.LOG_TABLE[s.EXP_TABLE[u]]=u;function h(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var r=0;while(r<t.length&&0==t[r])r++;this.num=new Array(t.length-r+e);for(var n=0;n<t.length-r;n++)this.num[n]=t[n+r]}function c(t,e){this.totalCount=t,this.dataCount=e}function l(){this.buffer=[],this.length=0}h.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),r=0;r<this.getLength();r++)for(var n=0;n<t.getLength();n++)e[r+n]^=s.gexp(s.glog(this.get(r))+s.glog(t.get(n)));return new h(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=s.glog(this.get(0))-s.glog(t.get(0)),r=new Array(this.getLength()),n=0;n<this.getLength();n++)r[n]=this.get(n);for(n=0;n<t.getLength();n++)r[n]^=s.gexp(s.glog(t.get(n))+e);return new h(r,0).mod(t)}},c.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],c.getRSBlocks=function(t,e){var r=c.getRsBlockTable(t,e);if(void 0==r)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var n=r.length/3,i=[],a=0;a<n;a++)for(var o=r[3*a+0],s=r[3*a+1],u=r[3*a+2],h=0;h<o;h++)i.push(new c(s,u));return i},c.getRsBlockTable=function(t,e){switch(e){case i.L:return c.RS_BLOCK_TABLE[4*(t-1)+0];case i.M:return c.RS_BLOCK_TABLE[4*(t-1)+1];case i.Q:return c.RS_BLOCK_TABLE[4*(t-1)+2];case i.H:return c.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},l.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var f=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function d(){return"undefined"!=typeof CanvasRenderingContext2D}function p(){var t=!1,e=navigator.userAgent;if(/android/i.test(e)){t=10;var r=e.toString().match(/android ([0-9](?:\.[0-9])?)/i);r&&r[1]&&(t=parseFloat(r[1]))}return t}var g=function(){var t=function(t,e){this._el=t,this._htOption=e};return t.prototype.draw=function(t){var e=this._htOption,r=this._el,n=t.getModuleCount();Math.floor(e.width/n),Math.floor(e.height/n);function i(t,e){var r=document.createElementNS("http://www.w3.org/2000/svg",t);for(var n in e)e.hasOwnProperty(n)&&r.setAttribute(n,e[n]);return r}this.clear();var a=i("svg",{viewBox:"0 0 "+String(n)+" "+String(n),width:"100%",height:"100%",fill:e.colorLight});a.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),r.appendChild(a),a.appendChild(i("rect",{fill:e.colorLight,width:"100%",height:"100%"})),a.appendChild(i("rect",{fill:e.colorDark,width:"1",height:"1",id:"template"}));for(var o=0;o<n;o++)for(var s=0;s<n;s++)if(t.isDark(o,s)){var u=i("use",{x:String(s),y:String(o)});u.setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),a.appendChild(u)}},t.prototype.clear=function(){while(this._el.hasChildNodes())this._el.removeChild(this._el.lastChild)},t}(),v="svg"===document.documentElement.tagName.toLowerCase(),y=v?g:d()?function(){function t(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}if(p()&&p()<=.9){var e=1/window.devicePixelRatio,r=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(t,n,i,a,o,s,u,h,c){if("nodeName"in t&&/img/i.test(t.nodeName))for(var l=arguments.length-1;l>=1;l--)arguments[l]=arguments[l]*e;else"undefined"==typeof h&&(arguments[1]*=e,arguments[2]*=e,arguments[3]*=e,arguments[4]*=e);r.apply(this,arguments)}}function n(t,e){var r=this;if(r._fFail=e,r._fSuccess=t,null===r._bSupportDataURI){var n=document.createElement("img"),i=function(){r._bSupportDataURI=!1,r._fFail&&r._fFail.call(r)},a=function(){r._bSupportDataURI=!0,r._fSuccess&&r._fSuccess.call(r)};return n.onabort=i,n.onerror=i,n.onload=a,void(n.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==")}!0===r._bSupportDataURI&&r._fSuccess?r._fSuccess.call(r):!1===r._bSupportDataURI&&r._fFail&&r._fFail.call(r)}var i=function(t,e){this._bIsPainted=!1,this._android=p(),this._htOption=e,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=e.width,this._elCanvas.height=e.height,t&&t.appendChild(this._elCanvas),this._el=t,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.alt="Scan me!",this._elImage.style.display="none",t&&this._el.appendChild(this._elImage),this._bSupportDataURI=null};return i.prototype.draw=function(t){var e=this._elImage,r=this._oContext,n=this._htOption,i=t.getModuleCount(),a=n.width/i,o=n.height/i,s=Math.round(a),u=Math.round(o);e.style.display="none",this.clear();for(var h=0;h<i;h++)for(var c=0;c<i;c++){var l=t.isDark(h,c),f=c*a,d=h*o;r.strokeStyle=l?n.colorDark:n.colorLight,r.lineWidth=1,r.fillStyle=l?n.colorDark:n.colorLight,r.fillRect(f,d,a,o),r.strokeRect(Math.floor(f)+.5,Math.floor(d)+.5,s,u),r.strokeRect(Math.ceil(f)-.5,Math.ceil(d)-.5,s,u)}this._bIsPainted=!0},i.prototype.makeImage=function(){this._bIsPainted&&n.call(this,t)},i.prototype.isPainted=function(){return this._bIsPainted},i.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},i.prototype.round=function(t){return t?Math.floor(1e3*t)/1e3:t},i}():function(){var t=function(t,e){this._el=t,this._htOption=e};return t.prototype.draw=function(t){for(var e=this._htOption,r=this._el,n=t.getModuleCount(),i=Math.floor(e.width/n),a=Math.floor(e.height/n),o=['<table style="border:0;border-collapse:collapse;">'],s=0;s<n;s++){o.push("<tr>");for(var u=0;u<n;u++)o.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+i+"px;height:"+a+"px;background-color:"+(t.isDark(s,u)?e.colorDark:e.colorLight)+';"></td>');o.push("</tr>")}o.push("</table>"),r.innerHTML=o.join("");var h=r.childNodes[0],c=(e.width-h.offsetWidth)/2,l=(e.height-h.offsetHeight)/2;c>0&&l>0&&(h.style.margin=l+"px "+c+"px")},t.prototype.clear=function(){this._el.innerHTML=""},t}();function m(t,e){for(var r=1,n=b(t),a=0,o=f.length;a<=o;a++){var s=0;switch(e){case i.L:s=f[a][0];break;case i.M:s=f[a][1];break;case i.Q:s=f[a][2];break;case i.H:s=f[a][3];break}if(n<=s)break;r++}if(r>f.length)throw new Error("Too long data");return r}function b(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=t?3:0)}n=function(t,e){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:i.H},"string"===typeof e&&(e={text:e}),e)for(var r in e)this._htOption[r]=e[r];"string"==typeof t&&(t=document.getElementById(t)),this._htOption.useSVG&&(y=g),this._android=p(),this._el=t,this._oQRCode=null,this._oDrawing=new y(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)},n.prototype.makeCode=function(t){this._oQRCode=new e(m(t,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(t),this._oQRCode.make(),this._el&&(this._el.title=t),this._oDrawing.draw(this._oQRCode),this.makeImage()},n.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||this._android>=1)&&this._oDrawing.makeImage()},n.prototype.clear=function(){this._oDrawing.clear()},n.CorrectLevel=i})();var i=n;function a(t,e){var r=t.parts,n=t.width,a=t.height,o=null,s=document.createElement("canvas"),u=s.getContext("2d");function h(e,r,n){var i,a;return e<0&&(i=t.width+e-n.width),r<0&&(a=t.height+r-n.height),i=i||e||0,a=a||r||0,"middle"===n.lineAlign?a-=n.height/2*n.lineNum-t.height/2:"bottom"===n.lineAlign&&(a-=n.height*n.lineNum-t.height),"center"===n.textAlign?i-=n.width/2-t.width/2:"right"===n.textAlign&&(i-=n.width-t.width),void 0!==e||"qrcode"!==n.type&&"image"!==n.type||(i-=n.width/2-t.width/2),{x:i,y:a}}function c(t,e,r,n,i){u.save(),u.beginPath(),u.moveTo(t+i,e),u.arcTo(t+r,e,t+r,e+n,i),u.arcTo(t+r,e+n,t,e+n,i),u.arcTo(t,e+n,t,e,i),u.arcTo(t,e,t+r,e,i),u.clip(),u.closePath()}function l(t){var e=t.image,r=t.x,n=t.y,i=t.width,a=t.height,o=t.radius,s=t.padding,h=t.background,l=t.clipOptions;if(c(r-s,n-s,i,a,o),u.fillStyle=h||"#fff",u.fill(),u.restore(),c(r,n,i-2*s,a-2*s,o),l)if(l.x=l.x||0,l.y=l.y||0,l.zoom){var f,d,p=0;e.height>e.width?(f=i-2*s,d=e.height*i/e.width-2*s):(f=e.width*a/e.height-2*s,d=a-2*s),"center"===l.align&&(p=Math.abs((f-d)/2)),u.drawImage(e,r-l.x-(f>d?p:0),n-l.y-(d>f?p:0),f,d)}else if("center"===l.align){var g=Math.abs((e.width-i-s)/2),v=Math.abs((e.height-a-s)/2);u.drawImage(e,r-g,r-v)}else u.drawImage(e,r-l.x,n-l.y);else u.drawImage(e,r,n,i-2*s,a-2*s);u.restore()}function f(t,e){var r=getComputedStyle(document.body);if(!t.text||"string"!==typeof t.text)return e();for(var n=t.text.toString().split("\n"),i=1.2*parseFloat(t.size||r.fontSize),a=0,o=n.length;a<o;a++){u.textBaseline="top",u.font="".concat(t.bold?"bold ":"").concat(t.size||r.fontSize," ").concat(r.fontFamily),u.fillStyle=t.color||r.color,u.textAlign="left",u.globalAlpha=t.opacity||1;var s=h(t.x||0,(t.y||0)+i*a,{lineNum:o,lineAlign:t.lineAlign,textAlign:t.textAlign,height:i,width:u.measureText(n[a]).width});if(t.textBaseline){u.beginPath();var c=t.textBaselineColor||"#999999";u.strokeStyle=c,u.moveTo(s.x,s.y),u.lineTo(s.x+u.measureText(n[a]).width+10,s.y),u.stroke(),u.textBaseline=t.textBaseline}if(t.width&&n[a]){for(var l=n[a].split(""),f="",d=[],p=t.width,g=t.lineNum||1,v=t.lineHeight||20,y=0;y<l.length;y++)u.measureText(f).width<p&&u.measureText(f+l[y]).width<=p?(f+=l[y],y==l.length-1&&d.push(f)):(d.push(f),f=l[y]);if(g<=d.length)for(var m=0;m<g;m++)u.fillText(d[m],s.x,s.y),s.y=s.y+v;else for(var b=0;b<d.length;b++)u.fillText(d[b],s.x,s.y),s.y=s.y+v}else u.fillText(n[a],s.x,s.y)}e&&e()}function d(t){for(var e=atob(t.split(",")[1]),r=t.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),i=new Uint8Array(n),a=0;a<e.length;a++)i[a]=e.charCodeAt(a);return new Blob([n],{type:r})}function p(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,r=t.type,n=t.width,i=t.height,a=t.x,s=t.y,c=t.url;if(!c)return console.error("缺失绘制的图片 url");var f=t.padding||0,p=new Image,g=h(a,s,{width:n,height:i,type:r});p.crossOrigin="anonymous",p.src=~c.indexOf("data:image/")?URL.createObjectURL(d(c)):c,p.onerror=function(t){o=t,e&&e()},p.onload=function(){u.globalAlpha=t.opacity||1,t.radius||f>0?l({image:p,x:g.x+f,y:g.y+f,width:n||p.width,height:i||p.height,radius:((i||p.height)-2*f)/2*(t.radius||0),padding:f,background:t.background,clipOptions:t.clipOptions}):u.drawImage(p,g.x+f,g.y+f,(n||p.width)-2*f,(i||p.height)-2*f),e&&e()}}function g(t,e){var r=t.text,n=t.width,a=t.height,o=t.level;if(n=n||200,a=a||n||200,!r)return console.error("缺失绘制的二维码的 text");var s=new i(null,{text:r,width:n,height:a,correctLevel:o||3,colorDark:"#000000",colorLight:"#ffffff"}),u=s._oDrawing._elImage;u.onload=function(){if(p(Object.assign(t,{url:u.src}),!t.logo&&e),t.logo){var r=.35,i=t.x<0?-1:1;p({type:"image",url:t.logo||"http://via.placeholder.com/100x100",width:n*r,height:a*r,x:i*n*(.5-r/2)+(t.x||0),y:i*a*(.5-r/2)+(t.y||0),padding:2},e)}}}s.width=n,s.height=a,u.fillStyle=t.background||"#fff",u.fillRect(0,0,n,a),u.save();var v=r.length,y=0,m=function n(){var i=function(){y++,v-y?n():e&&e(o,s.toDataURL("image/jpeg",t.compress||.8))};if(v-y)switch(r[y].type){case"text":f(r[y],i);break;case"image":p(r[y],i);break;case"qrcode":g(r[y],i);break;default:}};m()}function o(t,e,r){function n(t){var e={};for(var r in t)e[r]=t[r];return e}var i=n(e);return i.parts=i.parts.filter((function(t){return t.editable&&"text"!==t.type})),a(i,(function(n,i){function s(t){var r,n,i=e.parts.filter((function(t,e){return t._key=e,t.editable&&"image"===t.type}));n=r=i.length;var a=function(){var e=new Image;e.src=i[r].url,e.onload=function(r){return function(){i[r].width=i[r].width||e.width,i[r].height=i[r].height||e.height,n--,n||t&&t(i)}}(r)};while(r--)a()}function u(n,i){e.parts.map((function(t){return t._key===~~i?(t.url=n,t):t})),o(t,e,r)}function h(t,e){var r=new FileReader;return r.addEventListener("load",(function(){e(this.result)}),!1),r.readAsDataURL(t.target.files[0]),t}s((function(n){for(var s="",c=e.parts.filter((function(t,e){return t._key=e,t.editable&&"text"===t.type})),l=c.length;l--;)s+='\n            <textarea\n              class="x-textarea-container"\n              data-key="'.concat(c[l]._key,'"\n              style="\n                left: ').concat(c[l].x||0,"px;\n                top: ").concat(c[l].y||0,"px;\n                color: ").concat(c[l].color,";\n                font-size: ").concat(c[l].size,';\n              "\n              placeholder="').concat(c[l].placeholder,'"\n              maxlength="').concat(c[l].maxLength,'"\n            >').concat(c[l].text,"</textarea>\n          ");for(var f=n.length;f--;)s+='<div\n            class="x-input-container"\n            style="\n              left: '.concat(n[f].x||0,"px;\n              top: ").concat(n[f].y||0,"px;\n              width: ").concat(n[f].width,"px;\n              height: ").concat(n[f].height,'px;\n              "\n            >\n              <input\n                class="x-input"\n                data-key="').concat(n[f]._key,'"\n                data-click="').concat(n[f].selectImage,'"\n                type="').concat(n[f].selectImage?"button":"file",'"\n                value="点击替换图片"\n              />\n              <a>点击替换图片</a>\n            </div>');t.innerHTML='<div class="x-imaging-box">\n          <img src="'.concat(i,'" />\n          ').concat(s,"\n          ").concat(null!==e.buttonText?e.buttonText?'<a class="x-make-image">'.concat(e.buttonText,"</a>"):'<a class="x-make-image">绘制画布</a>':"","\n          </div>");var d=function(t){if("x-input"===t.target.className){var e=t.target.getAttribute("data-key");h(t,(function(t){return u(t,e)}))}};t.addEventListener("change",d,!1);var p=function n(i){if("x-input"===i.target.className){var s=i.target.getAttribute("data-key"),h=function(t){return u(t,s)};e.parts[s].selectImage&&e.parts[s].selectImage(h)}if("x-make-image"===i.target.className){for(var c=document.getElementsByClassName("x-textarea-container"),l=c.length;l--;){var f=c[l].getAttribute("data-key");e.parts[f].text=c[l].value}a(e,(function(r,n){t.innerHTML='<div class="x-imaging-box">\n                <img src="'.concat(n,'" />\n                ').concat(null!==e.resetButtonText?e.resetButtonText?'<a class="x-again-make-image">'.concat(e.resetButtonText,"</a>"):'<a class="x-again-make-image">重新编辑</a>':"","\n              </div>")}))}"x-again-make-image"===i.target.className&&(t.removeEventListener("click",n,!1),t.removeEventListener("change",d,!1),o(t,e,r))};t.addEventListener("click",p,!1),r&&r(i)}))})),{getValue:function(){return e},makeImage:function(t){a(e,t)}}}e["a"]={makeImage:a,renderEditor:o}},a975:function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").every,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("every",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},a981:function(t,e){t.exports="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof DataView},ace4:function(t,e,r){"use strict";var n=r("23e7"),i=r("d039"),a=r("621a"),o=r("825a"),s=r("23cb"),u=r("50c4"),h=r("4840"),c=a.ArrayBuffer,l=a.DataView,f=c.prototype.slice,d=i((function(){return!new c(2).slice(1,void 0).byteLength}));n({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:d},{slice:function(t,e){if(void 0!==f&&void 0===e)return f.call(o(this),t);var r=o(this).byteLength,n=s(t,r),i=s(void 0===e?r:e,r),a=new(h(this,c))(u(i-n)),d=new l(this),p=new l(a),g=0;while(n<i)p.setUint8(g++,d.getUint8(n++));return a}})},af03:function(t,e,r){var n=r("d039");t.exports=function(t){return n((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},b39a:function(t,e,r){"use strict";var n=r("da84"),i=r("ebb5"),a=r("d039"),o=n.Int8Array,s=i.aTypedArray,u=i.exportTypedArrayMethod,h=[].toLocaleString,c=[].slice,l=!!o&&a((function(){h.call(new o(1))})),f=a((function(){return[1,2].toLocaleString()!=new o([1,2]).toLocaleString()}))||!a((function(){o.prototype.toLocaleString.call([1,2])}));u("toLocaleString",(function(){return h.apply(l?c.call(s(this)):s(this),arguments)}),f)},c19f:function(t,e,r){"use strict";var n=r("23e7"),i=r("da84"),a=r("621a"),o=r("2626"),s="ArrayBuffer",u=a[s],h=i[s];n({global:!0,forced:h!==u},{ArrayBuffer:u}),o(s)},c1ac:function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").filter,a=r("4840"),o=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod;u("filter",(function(t){var e=i(o(this),t,arguments.length>1?arguments[1]:void 0),r=a(this,this.constructor),n=0,u=e.length,h=new(s(r))(u);while(u>n)h[n]=e[n++];return h}))},ca91:function(t,e,r){"use strict";var n=r("ebb5"),i=r("d58f").left,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("reduce",(function(t){return i(a(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},cb29:function(t,e,r){var n=r("23e7"),i=r("81d5"),a=r("44d2");n({target:"Array",proto:!0},{fill:i}),a("fill")},cc71:function(t,e,r){"use strict";var n=r("23e7"),i=r("857a"),a=r("af03");n({target:"String",proto:!0,forced:a("bold")},{bold:function(){return i(this,"b","","")}})},cd26:function(t,e,r){"use strict";var n=r("ebb5"),i=n.aTypedArray,a=n.exportTypedArrayMethod,o=Math.floor;a("reverse",(function(){var t,e=this,r=i(e).length,n=o(r/2),a=0;while(a<n)t=e[a],e[a++]=e[--r],e[r]=t;return e}))},d139:function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").find,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("find",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},d58f:function(t,e,r){var n=r("1c0b"),i=r("7b0b"),a=r("44ad"),o=r("50c4"),s=function(t){return function(e,r,s,u){n(r);var h=i(e),c=a(h),l=o(h.length),f=t?l-1:0,d=t?-1:1;if(s<2)while(1){if(f in c){u=c[f],f+=d;break}if(f+=d,t?f<0:l<=f)throw TypeError("Reduce of empty array with no initial value")}for(;t?f>=0:l>f;f+=d)f in c&&(u=r(u,c[f],f,h));return u}};t.exports={left:s(!1),right:s(!0)}},d5d6:function(t,e,r){"use strict";var n=r("ebb5"),i=r("b727").forEach,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("forEach",(function(t){i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},d81d:function(t,e,r){"use strict";var n=r("23e7"),i=r("b727").map,a=r("1dde"),o=r("ae40"),s=a("map"),u=o("map");n({target:"Array",proto:!0,forced:!s||!u},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},ddb0:function(t,e,r){var n=r("da84"),i=r("fdbc"),a=r("e260"),o=r("9112"),s=r("b622"),u=s("iterator"),h=s("toStringTag"),c=a.values;for(var l in i){var f=n[l],d=f&&f.prototype;if(d){if(d[u]!==c)try{o(d,u,c)}catch(g){d[u]=c}if(d[h]||o(d,h,l),i[l])for(var p in a)if(d[p]!==a[p])try{o(d,p,a[p])}catch(g){d[p]=a[p]}}}},e58c:function(t,e,r){"use strict";var n=r("fc6a"),i=r("a691"),a=r("50c4"),o=r("a640"),s=r("ae40"),u=Math.min,h=[].lastIndexOf,c=!!h&&1/[1].lastIndexOf(1,-0)<0,l=o("lastIndexOf"),f=s("indexOf",{ACCESSORS:!0,1:0}),d=c||!l||!f;t.exports=d?function(t){if(c)return h.apply(this,arguments)||0;var e=n(this),r=a(e.length),o=r-1;for(arguments.length>1&&(o=u(o,i(arguments[1]))),o<0&&(o=r+o);o>=0;o--)if(o in e&&e[o]===t)return o||0;return-1}:h},e7e5:function(t,e,r){"use strict";r("68ef"),r("a71a"),r("9d70"),r("3743"),r("4d75"),r("e3b3"),r("b258")},e91f:function(t,e,r){"use strict";var n=r("ebb5"),i=r("4d64").indexOf,a=n.aTypedArray,o=n.exportTypedArrayMethod;o("indexOf",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0)}))},ebb5:function(t,e,r){"use strict";var n,i=r("a981"),a=r("83ab"),o=r("da84"),s=r("861d"),u=r("5135"),h=r("f5df"),c=r("9112"),l=r("6eeb"),f=r("9bf2").f,d=r("e163"),p=r("d2bb"),g=r("b622"),v=r("90e3"),y=o.Int8Array,m=y&&y.prototype,b=o.Uint8ClampedArray,w=b&&b.prototype,A=y&&d(y),T=m&&d(m),x=Object.prototype,L=x.isPrototypeOf,_=g("toStringTag"),k=v("TYPED_ARRAY_TAG"),E=i&&!!p&&"Opera"!==h(o.opera),C=!1,R={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B=function(t){var e=h(t);return"DataView"===e||u(R,e)},I=function(t){return s(t)&&u(R,h(t))},D=function(t){if(I(t))return t;throw TypeError("Target is not a typed array")},P=function(t){if(p){if(L.call(A,t))return t}else for(var e in R)if(u(R,n)){var r=o[e];if(r&&(t===r||L.call(r,t)))return t}throw TypeError("Target is not a typed array constructor")},S=function(t,e,r){if(a){if(r)for(var n in R){var i=o[n];i&&u(i.prototype,t)&&delete i.prototype[t]}T[t]&&!r||l(T,t,r?e:E&&m[t]||e)}},M=function(t,e,r){var n,i;if(a){if(p){if(r)for(n in R)i=o[n],i&&u(i,t)&&delete i[t];if(A[t]&&!r)return;try{return l(A,t,r?e:E&&y[t]||e)}catch(s){}}for(n in R)i=o[n],!i||i[t]&&!r||l(i,t,e)}};for(n in R)o[n]||(E=!1);if((!E||"function"!=typeof A||A===Function.prototype)&&(A=function(){throw TypeError("Incorrect invocation")},E))for(n in R)o[n]&&p(o[n],A);if((!E||!T||T===x)&&(T=A.prototype,E))for(n in R)o[n]&&p(o[n].prototype,T);if(E&&d(w)!==T&&p(w,T),a&&!u(T,_))for(n in C=!0,f(T,_,{get:function(){return s(this)?this[k]:void 0}}),R)o[n]&&c(o[n],k,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:E,TYPED_ARRAY_TAG:C&&k,aTypedArray:D,aTypedArrayConstructor:P,exportTypedArrayMethod:S,exportTypedArrayStaticMethod:M,isView:B,isTypedArray:I,TypedArray:A,TypedArrayPrototype:T}},f8cd:function(t,e,r){var n=r("a691");t.exports=function(t){var e=n(t);if(e<0)throw RangeError("The argument can't be less than 0");return e}}}]);