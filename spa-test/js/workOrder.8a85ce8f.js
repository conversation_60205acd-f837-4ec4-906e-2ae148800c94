(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["workOrder"],{"2e46":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("van-nav-bar",{attrs:{title:e.title,"left-text":"返回","left-arrow":"",fixed:!0},on:{"click-left":e.onClickLeft}}),a("div",{staticClass:"create"},[e.is_create?a("div",{staticClass:"create-info"},[a("div",{staticClass:"create-info-title"},[e._v(e._s(e.question_title))]),a("van-field",{staticStyle:{padding:"0"},attrs:{type:"textarea",placeholder:"请填写信息"},model:{value:e.editForm.content,callback:function(t){e.$set(e.editForm,"content",t)},expression:"editForm.content"}}),a("div",{staticClass:"create-info-annex"},[a("p",{staticClass:"create-info-annex-title"},[e._v("工单附件（支持jpg,png,mp4格式）")]),a("van-uploader",{attrs:{multiple:"","max-count":9,accept:"image/*,video/*","max-size":107374182400,"after-read":e.uploadFile,"before-delete":e.DeleteFile},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("div",{staticClass:"create-info-op"},[a("van-button",{staticClass:"create-info-op-button",attrs:{type:"info",round:"",size:"small"},on:{click:e.createWorkOrder}},[e._v("保存工单")])],1)],1):a("van-button",{staticClass:"create-button",attrs:{type:"info",round:"",size:"small"},on:{click:function(t){e.is_create=!0}}},[e._v("创建新工单")])],1),e.follow_list.length?a("p",{staticClass:"title"},[e._v("该用户有以下跟进中的工单")]):e._e(),a("div",{staticClass:"order-list"},e._l(e.follow_list,(function(t,n){return a("div",{key:n,staticClass:"order-list-one"},[a("div",{staticClass:"order-list-one-header"},[a("span",{staticClass:"order-list-one-header-title"},[e._v(e._s(t.question_title))]),a("van-tag",{attrs:{type:"primary"}},[e._v(e._s(t.create_user))])],1),a("div",{staticClass:"order-list-one-time"},[a("div",{staticClass:"order-list-one-time-left"},[e._v(e._s(t.customer_mobile)+" "),a("van-icon",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:t.customer_mobile,expression:"item.customer_mobile",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.firstCopySuccess,expression:"firstCopySuccess",arg:"success"},{name:"clipboard",rawName:"v-clipboard:error",value:e.firstCopyError,expression:"firstCopyError",arg:"error"}],staticStyle:{"margin-left":"0.3125rem"},attrs:{name:r("f42c"),color:"#666666"}})],1),a("div",{staticClass:"order-list-one-time-right"},[e._v(e._s(e._f("dateStr")(t.create_time))),a("van-icon",{staticStyle:{"margin-left":"0.1875rem"},attrs:{name:"underway-o",color:"#666666"}})],1)]),a("div",{staticClass:"order-list-one-desc"},[e._v(e._s(t.content))]),a("div",{staticClass:"order-list-one-annex"},[e._l(t.files,(function(r,n){return["image"===e.checkMediaType(r)?a("van-image",{staticClass:"order-list-one-annex-one",attrs:{width:"6.25rem",height:"6.25rem",fit:"cover",src:r},on:{click:function(a){return e.toPreviewImages(t.files,r)}}}):e._e(),"video"===e.checkMediaType(r)?a("video",{staticClass:"order-list-one-annex-one order-list-one-annex-video",attrs:{controls:"",src:r}}):e._e()]}))],2),a("div",{staticClass:"order-list-one-op"},[a("van-button",{staticClass:"order-list-one-op-button",attrs:{type:"info",round:"",size:"small"},on:{click:function(r){return e.toFollow(t)}}},[e._v("继续跟进")])],1)])})),0)],1)},n=[],i=(r("4de4"),r("caad"),r("c975"),r("baa5"),r("a434"),r("ac1f"),r("5319"),r("498a"),r("e17f"),r("2241")),s=(r("4662"),r("28a2")),o=(r("e7e5"),r("d399")),c=(r("96cf"),r("1da1")),l=r("5530"),u=r("2f62"),d=r("c391"),f=r("ce3a"),p={name:"workOrder",data:function(){return{title:"",reception_id:"",external_userid:"",question_title:"",is_create:!1,editForm:{content:"订单编号：\n供应链单号：\n供应商：\n商品：\n数量：\n收货人：\n联系电话：\n收货地址：\n快递公司：\n快递单号：\n问题：",files:[]},fileList:[],follow_list:[]}},computed:Object(l["a"])({},Object(u["b"])(["staffUserId"])),filters:{dateStr:function(e){var t=new Date(e);return t.getFullYear()+"年"+(t.getMonth()+1)+"月"+t.getDate()+"日 "+t.getHours()+":"+t.getMinutes()}},created:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.title=e.$route.meta.title,e.reception_id=e.$route.query.reception_id,e.external_userid=e.$route.query.external_userid,e.question_title=e.$route.query.question_title,t.next=6,e.getFollowList();case 6:case"end":return t.stop()}}),t)})))()},methods:{onClickLeft:function(){this.$router.replace({name:"serviceData"})},firstCopySuccess:function(e){Object(o["a"])("复制成功")},firstCopyError:function(e){Object(o["a"])("复制失败!")},checkMediaType:function(e){e=e.toLowerCase();var t=[".jpg",".jpeg",".png",".gif",".bmp",".webp"],r=[".mp4",".webm",".mov",".avi",".mkv",".flv"],a=e.substring(e.lastIndexOf("."));return t.includes(a)?"image":r.includes(a)?"video":"unknown"},toPreviewImages:function(e,t){var r=this;e=e.filter((function(e){return"image"===r.checkMediaType(e)}));var a=e.indexOf(t);Object(s["a"])({images:e,startPosition:a})},toFollow:function(e){this.$router.push({name:"workOrderDetail",query:{reception_id:this.reception_id,cs_order_id:e.cs_order_id}})},getFollowList:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post(Object(d["a"])(f["a"].followingListUrl),{token:e.staffUserId,external_userid:e.external_userid});case 3:r=t.sent,a=r.data,0==a.code&&(e.follow_list=a.data,e.follow_list.length<1&&(e.is_create=!0)),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),console.log(t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},uploadImage:function(e,t){var r=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var n,i,s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.status="uploading",e.message="上传中...",n=new FormData,n.append("file",e.file),a.prev=4,a.next=7,r.$axios.post(Object(d["a"])(f["a"].xmUserTaskVideoImageUrl),n,{headers:{"Content-Type":"multipart/form-data"}});case 7:if(i=a.sent,s=i.data,10067==s.code){a.next=15;break}return e.status="failed",e.message="上传失败",a.abrupt("return");case 15:r.editForm.files[t]=s.data.pic_path,e.status="success",e.message="上传成功";case 18:a.next=24;break;case 20:a.prev=20,a.t0=a["catch"](4),e.status="failed",e.message="上传失败";case 24:case"end":return a.stop()}}),a,null,[[4,20]])})))()},uploadVideo:function(e,t){var r=this;return Object(c["a"])(regeneratorRuntime.mark((function a(){var n,i,s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.status="uploading",e.message="上传中...",n=new FormData,n.append("file",e.file),a.prev=4,a.next=7,r.$axios.post(Object(d["a"])(f["a"].userShareVideoUrl),n,{headers:{"Content-Type":"multipart/form-data"}});case 7:if(i=a.sent,s=i.data,10067==s.code){a.next=15;break}return e.status="failed",e.message="上传失败",a.abrupt("return");case 15:r.editForm.files[t]=s.data.path,e.status="success",e.message="上传成功";case 18:a.next=24;break;case 20:a.prev=20,a.t0=a["catch"](4),e.status="failed",e.message="上传失败";case 24:case"end":return a.stop()}}),a,null,[[4,20]])})))()},uploadFile:function(e){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:a=0;case 1:if(!(a<e.length)){r.next=13;break}if(-1==e[a].file.type.indexOf("image")){r.next=7;break}return r.next=5,t.uploadImage(e[a],a);case 5:r.next=10;break;case 7:if(-1==e[a].file.type.indexOf("video")){r.next=10;break}return r.next=10,t.uploadVideo(e[a],a);case 10:a++,r.next=1;break;case 13:case"end":return r.stop()}}),r)})))()},DeleteFile:function(e,t){var r=this;return Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r.editForm.files.splice(t.index,1);case 1:case"end":return e.stop()}}),e)})))()},resetEditForm:function(){this.editForm={content:"订单编号：\n供应链单号：\n供应商：\n商品：\n数量：\n收货人：\n联系电话：\n收货地址：\n快递公司：\n快递单号：\n问题：",files:[]},this.fileList=[]},createWorkOrder:function(){var e=this;return Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.editForm.content.trim()){t.next=3;break}return Object(o["a"])("请填写内容"),t.abrupt("return");case 3:i["a"].confirm({title:"提示",message:"确认保存？"}).then(Object(c["a"])(regeneratorRuntime.mark((function t(){var r,a,n,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=o["a"].loading({duration:0,forbidClick:!0,message:"提交中..."}),a=Object(l["a"])({token:e.staffUserId,reception_id:e.reception_id},e.editForm),t.prev=2,t.next=5,e.$axios.post(Object(d["a"])(f["a"].createWorkOrderUrl),a);case 5:if(n=t.sent,r.clear(),i=n.data,0==i.code){t.next=13;break}return Object(o["a"])(i.message),t.abrupt("return");case 13:e.resetEditForm(),e.is_create=!1,Object(o["a"])({message:i.message,onClose:function(){e.onClickLeft()}});case 16:t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](2),Object(o["a"])(t.t0.message),r.clear();case 22:case"end":return t.stop()}}),t,null,[[2,18]])})))).catch((function(){}));case 4:case"end":return t.stop()}}),t)})))()}}},m=p,v=(r("ac6e"),r("2877")),g=Object(v["a"])(m,a,n,!1,null,"35677d4c",null);t["default"]=g.exports},"704b":function(e,t,r){},a434:function(e,t,r){"use strict";var a=r("23e7"),n=r("23cb"),i=r("a691"),s=r("50c4"),o=r("7b0b"),c=r("65f0"),l=r("8418"),u=r("1dde"),d=r("ae40"),f=u("splice"),p=d("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,v=Math.min,g=9007199254740991,b="Maximum allowed length exceeded";a({target:"Array",proto:!0,forced:!f||!p},{splice:function(e,t){var r,a,u,d,f,p,h=o(this),x=s(h.length),_=n(e,x),w=arguments.length;if(0===w?r=a=0:1===w?(r=0,a=x-_):(r=w-2,a=v(m(i(t),0),x-_)),x+r-a>g)throw TypeError(b);for(u=c(h,a),d=0;d<a;d++)f=_+d,f in h&&l(u,d,h[f]);if(u.length=a,r<a){for(d=_;d<x-a;d++)f=d+a,p=d+r,f in h?h[p]=h[f]:delete h[p];for(d=x;d>x-a+r;d--)delete h[d-1]}else if(r>a)for(d=x-a;d>_;d--)f=d+a-1,p=d+r-1,f in h?h[p]=h[f]:delete h[p];for(d=0;d<r;d++)h[d+_]=arguments[d+2];return h.length=x-a+r,u}})},ac6e:function(e,t,r){"use strict";var a=r("704b"),n=r.n(a);n.a},baa5:function(e,t,r){var a=r("23e7"),n=r("e58c");a({target:"Array",proto:!0,forced:n!==[].lastIndexOf},{lastIndexOf:n})},caad:function(e,t,r){"use strict";var a=r("23e7"),n=r("4d64").includes,i=r("44d2"),s=r("ae40"),o=s("indexOf",{ACCESSORS:!0,1:0});a({target:"Array",proto:!0,forced:!o},{includes:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),i("includes")},e58c:function(e,t,r){"use strict";var a=r("fc6a"),n=r("a691"),i=r("50c4"),s=r("a640"),o=r("ae40"),c=Math.min,l=[].lastIndexOf,u=!!l&&1/[1].lastIndexOf(1,-0)<0,d=s("lastIndexOf"),f=o("indexOf",{ACCESSORS:!0,1:0}),p=u||!d||!f;e.exports=p?function(e){if(u)return l.apply(this,arguments)||0;var t=a(this),r=i(t.length),s=r-1;for(arguments.length>1&&(s=c(s,n(arguments[1]))),s<0&&(s=r+s);s>=0;s--)if(s in t&&t[s]===e)return s||0;return-1}:l},e7e5:function(e,t,r){"use strict";r("68ef"),r("a71a"),r("9d70"),r("3743"),r("4d75"),r("e3b3"),r("b258")},f42c:function(e,t,r){e.exports=r.p+"img/copy-four.c067862f.png"}}]);