# 海关跨境电商数据对接插件数据模型设计

## 1. 数据库表设计

### 1.1 配置表 (customs_chinaport_config)

用于存储插件配置信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| ----- | --- | ---- | ----- | ----- | ---- |
| id | int | 11 | 否 | 自增 | 主键ID |
| config_key | varchar | 50 | 否 | 无 | 配置键名 |
| config_value | text | - | 是 | null | 配置值 |
| config_desc | varchar | 255 | 是 | null | 配置描述 |
| create_time | int | 11 | 否 | 0 | 创建时间 |
| update_time | int | 11 | 否 | 0 | 更新时间 |

**索引**：
- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_config_key` (`config_key`)

**初始配置项**：
- `api_url_test`: 测试环境API地址
- `api_url_prod`: 生产环境API地址
- `environment`: 当前环境配置（test/prod）
- `signature_service_url`: 签名服务地址
- `signature_service_timeout`: 签名服务超时时间
- `signature_service_token`: 签名服务认证令牌（加密存储）
- `ebp_code`: 电商平台代码
- `pay_code`: 支付企业代码
- `retry_interval`: 重试初始间隔（秒）
- `retry_max_times`: 最大重试次数

### 1.2 日志表 (customs_chinaport_log)

记录接口调用和处理日志。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| ----- | --- | ---- | ----- | ----- | ---- |
| id | int | 11 | 否 | 自增 | 主键ID |
| type | tinyint | 1 | 否 | 1 | 日志类型: 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试 |
| request_data | text | - | 是 | null | 请求数据 |
| response_data | text | - | 是 | null | 响应数据 |
| status | tinyint | 1 | 否 | 0 | 状态：0-失败 1-成功 |
| error_msg | varchar | 255 | 是 | null | 错误信息 |
| create_time | int | 10 | 否 | 0 | 创建时间 |
| update_time | int | 10 | 否 | 0 | 更新时间 |

**索引**：
- PRIMARY KEY (`id`)
- KEY `idx_type` (`type`)
- KEY `idx_status` (`status`)
- KEY `idx_create_time` (`create_time`)

### 1.3 回执记录表 (customs_chinaport_receipt)

记录海关回执信息。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| ----- | --- | ---- | ----- | ----- | ---- |
| id | int | 11 | 否 | 自增 | 主键ID |
| receipt_id | varchar | 100 | 否 | 无 | 回执ID |
| receipt_type | varchar | 20 | 否 | 无 | 回执类型: order-订单 payment-支付 |
| original_xml | text | - | 是 | null | 原始XML数据 |
| customs_code | varchar | 50 | 是 | '' | 海关编码 |
| customs_name | varchar | 100 | 是 | '' | 海关名称 |
| status | varchar | 10 | 是 | '' | 回执状态 |
| status_desc | varchar | 100 | 是 | '' | 状态描述 |
| order_no | varchar | 50 | 是 | '' | 订单号 |
| payment_no | varchar | 50 | 是 | '' | 支付单号 |
| note | varchar | 500 | 是 | '' | 回执信息 |
| receipt_time | int | 11 | 否 | 0 | 回执时间 |
| process_status | tinyint | 1 | 否 | 0 | 处理状态: 0-未处理 1-已处理 |
| process_message | varchar | 500 | 是 | '' | 处理消息 |
| process_time | int | 11 | 否 | 0 | 处理时间 |
| create_time | int | 11 | 否 | 0 | 创建时间 |
| update_time | int | 11 | 否 | 0 | 更新时间 |

**索引**：
- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_receipt_id` (`receipt_id`)
- KEY `idx_receipt_type` (`receipt_type`)
- KEY `idx_status` (`status`)
- KEY `idx_order_no` (`order_no`)
- KEY `idx_payment_no` (`payment_no`)
- KEY `idx_process_status` (`process_status`)
- KEY `idx_receipt_time` (`receipt_time`)
- KEY `idx_create_time` (`create_time`)

### 1.4 任务队列表 (customs_chinaport_task)

存储待处理和重试的任务。

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
| ----- | --- | ---- | ----- | ----- | ---- |
| id | int | 11 | 否 | 自增 | 主键ID |
| task_id | varchar | 50 | 否 | 无 | 任务ID（全局唯一） |
| task_type | varchar | 20 | 否 | 无 | 任务类型：order-订单上报 payment-支付上报 |
| business_id | varchar | 50 | 否 | 无 | 业务ID（订单号/支付号） |
| data | text | 是 | null | 任务数据（JSON格式） |
| status | tinyint | 1 | 否 | 0 | 任务状态：0-待处理 1-处理中 2-处理成功 3-处理失败 |
| retry_count | int | 11 | 否 | 0 | 重试次数 |
| max_retry | int | 11 | 否 | 5 | 最大重试次数 |
| next_retry_time | int | 11 | 否 | 0 | 下次重试时间 |
| last_error | varchar | 500 | 是 | '' | 最后一次错误信息 |
| create_time | int | 11 | 否 | 0 | 创建时间 |
| update_time | int | 11 | 否 | 0 | 更新时间 |

**索引**：
- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_task_id` (`task_id`)
- KEY `idx_task_type` (`task_type`)
- KEY `idx_business_id` (`business_id`)
- KEY `idx_status` (`status`)
- KEY `idx_next_retry_time` (`next_retry_time`)
- KEY `idx_create_time` (`create_time`)

## 2. 数据模型类设计

### 2.1 ConfigModel

```php
namespace addon\customs_chinaport\model;

use think\Model;

class ConfigModel extends Model
{
    protected $name = 'customs_chinaport_config';
    
    /**
     * 获取配置项
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        // 实现代码
    }
    
    /**
     * 设置配置项
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param string $desc 配置描述
     * @return boolean
     */
    public function setConfig($key, $value, $desc = '')
    {
        // 实现代码
    }
    
    /**
     * 获取所有配置
     * @return array
     */
    public function getAllConfig()
    {
        // 实现代码
    }
}
```

### 2.2 LogModel

```php
namespace addon\customs_chinaport\model;

use think\Model;

class LogModel extends Model
{
    protected $name = 'customs_chinaport_log';
    
    // 日志类型常量
    const TYPE_REQUEST = 1;     // 接收请求
    const TYPE_XML = 2;         // 生成XML
    const TYPE_ORDER = 3;       // 提交订单
    const TYPE_PAYMENT = 4;     // 提交支付
    const TYPE_RECEIPT = 5;     // 回执处理
    const TYPE_RETRY = 6;       // 限流重试
    
    /**
     * 记录日志
     * @param int $type 日志类型
     * @param mixed $requestData 请求数据
     * @param mixed $responseData 响应数据
     * @param int $status 状态：0-失败 1-成功
     * @param string $errorMsg 错误信息
     * @return int 日志ID
     */
    public function addLog($type, $requestData, $responseData = null, $status = 1, $errorMsg = '')
    {
        // 实现代码
    }
    
    /**
     * 获取日志列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getLogList($condition = [], $field = '*', $order = 'create_time desc', $page = 1, $pageSize = 20)
    {
        // 实现代码
    }
}
```

### 2.3 ReceiptModel

```php
namespace addon\customs_chinaport\model;

use think\Model;

class ReceiptModel extends Model
{
    protected $name = 'customs_chinaport_receipt';
    
    /**
     * 添加回执记录
     * @param array $data 回执数据
     * @return int 回执ID
     */
    public function addReceipt($data)
    {
        // 实现代码
    }
    
    /**
     * 更新回执处理状态
     * @param string $receiptId 回执ID
     * @param int $processStatus 处理状态
     * @param string $processMessage 处理消息
     * @return boolean
     */
    public function updateProcessStatus($receiptId, $processStatus, $processMessage = '')
    {
        // 实现代码
    }
    
    /**
     * 获取回执列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getReceiptList($condition = [], $field = '*', $order = 'create_time desc', $page = 1, $pageSize = 20)
    {
        // 实现代码
    }
}
```

### 2.4 TaskModel

```php
namespace addon\customs_chinaport\model;

use think\Model;

class TaskModel extends Model
{
    protected $name = 'customs_chinaport_task';
    
    // 任务类型常量
    const TYPE_ORDER = 'order';       // 订单上报
    const TYPE_PAYMENT = 'payment';   // 支付上报
    
    // 任务状态常量
    const STATUS_PENDING = 0;    // 待处理
    const STATUS_PROCESSING = 1; // 处理中
    const STATUS_SUCCESS = 2;    // 处理成功
    const STATUS_FAILED = 3;     // 处理失败
    
    /**
     * 添加任务
     * @param string $taskType 任务类型
     * @param string $businessId 业务ID
     * @param array $data 任务数据
     * @param int $maxRetry 最大重试次数
     * @return string 任务ID
     */
    public function addTask($taskType, $businessId, $data, $maxRetry = 5)
    {
        // 实现代码
    }
    
    /**
     * 获取待处理任务
     * @param int $limit 获取数量
     * @return array
     */
    public function getPendingTasks($limit = 10)
    {
        // 实现代码
    }
    
    /**
     * 更新任务状态
     * @param string $taskId 任务ID
     * @param int $status 任务状态
     * @param string $error 错误信息
     * @return boolean
     */
    public function updateTaskStatus($taskId, $status, $error = '')
    {
        // 实现代码
    }
    
    /**
     * 标记任务重试
     * @param string $taskId 任务ID
     * @param string $error 错误信息
     * @return boolean
     */
    public function markTaskRetry($taskId, $error = '')
    {
        // 实现代码
    }
}
```

## 3. 数据关系图

```
+----------------------+       +----------------------+
|  customs_chinaport_  |       |  customs_chinaport_  |
|       config         |       |        log          |
+----------------------+       +----------------------+
| - id                 |       | - id                 |
| - config_key         |       | - type               |
| - config_value       |       | - request_data       |
| - config_desc        |       | - response_data      |
| - create_time        |       | - status             |
| - update_time        |       | - error_msg          |
+----------------------+       | - create_time        |
                               | - update_time        |
                               +----------------------+
                                        
+----------------------+       +----------------------+
|  customs_chinaport_  |       |  customs_chinaport_  |
|      receipt         |       |        task         |
+----------------------+       +----------------------+
| - id                 |       | - id                 |
| - receipt_id         |       | - task_id            |
| - receipt_type       |       | - task_type          |
| - original_xml       |       | - business_id        |
| - customs_code       |       | - data               |
| - customs_name       |       | - status             |
| - status             |       | - retry_count        |
| - status_desc        |       | - max_retry          |
| - order_no           |       | - next_retry_time    |
| - payment_no         |       | - last_error         |
| - note               |       | - create_time        |
| - receipt_time       |       | - update_time        |
| - process_status     |       +----------------------+
| - process_message    |
| - process_time       |
| - create_time        |
| - update_time        |
+----------------------+
```

## 4. 数据初始化

在插件安装时，将执行以下数据初始化操作：

1. 创建上述四张数据表
2. 初始化配置表的基本配置项
3. 创建索引以优化查询性能

## 5. 数据安全措施

1. **敏感配置加密**：敏感配置项（如签名服务认证令牌）将使用加密方式存储
2. **数据脱敏**：在日志记录中对敏感信息进行脱敏处理
3. **权限控制**：限制对数据表的直接访问权限
4. **定期清理**：设置数据清理策略，定期清理过期日志数据

## 6. 数据优化策略

1. **索引优化**：为常用查询条件建立适当的索引
2. **分表策略**：针对日志表，预留按时间分表的扩展能力
3. **缓存机制**：对频繁访问的配置项实施缓存策略
4. **批量操作**：使用批量插入和更新操作减少数据库负载 