# 海关跨境电商数据对接插件架构设计

## 1. 设计目标

重新设计海关跨境电商数据对接插件，实现以下目标：

1. **高可靠性**：保证数据传输的可靠性，包括重试机制和错误处理
2. **高安全性**：确保签名验证机制的安全可靠
3. **易维护性**：代码结构清晰，便于后续维护和升级
4. **易配置性**：提供友好的配置界面，便于管理员配置和监控

## 2. 整体架构

### 2.1 架构图

```
+------------------------------------------+
|            海关接口插件                   |
+------------------------------------------+
|                                          |
|  +-------------+      +---------------+  |
|  | 配置管理模块  |<---->|  管理员界面   |  |
|  +-------------+      +---------------+  |
|        ^                                 |
|        |                                 |
|        v                                 |
|  +-------------+      +---------------+  |
|  | 数据处理模块  |<---->|  数据模型层   |  |
|  +-------------+      +---------------+  |
|        ^                     ^           |
|        |                     |           |
|        v                     v           |
|  +-------------+      +---------------+  |
|  |  签名服务   |      |  日志服务     |  |
|  +-------------+      +---------------+  |
|        ^                     ^           |
|        |                     |           |
|        v                     v           |
|  +-------------+      +---------------+  |
|  | 数据传输模块 |<---->|  回执处理模块 |  |
|  +-------------+      +---------------+  |
|        ^                     ^           |
|        |                     |           |
+--------|---------------------|------------
         |                     |
         v                     v
+------------------+    +------------------+
|    海关接口      |    |    系统事件      |
+------------------+    +------------------+
```

### 2.2 主要模块和职责

1. **配置管理模块**：负责插件配置的管理，包括API地址、签名服务配置等
2. **数据处理模块**：负责数据格式化和业务逻辑处理
3. **签名服务**：负责调用电子口岸签名服务，生成签名值
4. **日志服务**：负责记录各种操作日志和错误日志
5. **数据传输模块**：负责与海关接口进行数据交互
6. **回执处理模块**：负责处理海关返回的回执信息
7. **数据模型层**：负责数据库操作

## 3. 数据流处理方案

### 3.1 订单数据上报流程

```
+-------------+     +------------+     +-------------+     +------------+
| 订单事件触发 |---->| 数据处理模块 |---->| 签名服务模块 |---->| 数据传输模块 |
+-------------+     +------------+     +-------------+     +------------+
                                                               |
+-------------+     +------------+     +-------------+         |
| 日志记录    |<----| 回执处理模块 |<----| 接收回执    |<--------+
+-------------+     +------------+     +-------------+
```

1. 订单事件（创建、支付）触发数据上报
2. 数据处理模块格式化订单数据
3. 签名服务模块调用电子口岸签名服务
4. 数据传输模块将签名后的数据发送到海关接口
5. 接收并处理海关回执
6. 记录操作日志

### 3.2 支付数据上报流程

```
+-------------+     +------------+     +-------------+     +------------+
| 支付事件触发 |---->| 数据处理模块 |---->| 签名服务模块 |---->| 数据传输模块 |
+-------------+     +------------+     +-------------+     +------------+
                                                               |
+-------------+     +------------+     +-------------+         |
| 日志记录    |<----| 回执处理模块 |<----| 接收回执    |<--------+
+-------------+     +------------+     +-------------+
```

1. 支付完成事件触发数据上报
2. 数据处理模块格式化支付数据（包含微信支付原始XML数据）
3. 签名服务模块调用电子口岸签名服务
4. 数据传输模块将签名后的数据发送到海关接口
5. 接收并处理海关回执
6. 记录操作日志

### 3.3 回执处理流程

```
+-------------+     +------------+     +-------------+
| 接收回执    |---->| 回执处理模块 |---->| 更新订单状态 |
+-------------+     +------------+     +-------------+
                         |
                         v
                   +-------------+
                   | 日志记录    |
                   +-------------+
```

1. 接收海关回执数据
2. 回执处理模块解析回执状态和信息
3. 更新本地订单/支付数据状态
4. 记录回执日志

## 4. 技术选型

### 4.1 数据库设计

1. **customs_chinaport_config**: 插件配置表
2. **customs_chinaport_log**: 日志表（保持原表结构）
3. **customs_chinaport_receipt**: 回执记录表（保持原表结构）
4. **customs_chinaport_task**: 任务队列表（新增，用于重试机制）

### 4.2 核心类设计

```
- CustomsService: 海关数据服务主类
  - OrderService: 订单数据处理服务
  - PaymentService: 支付数据处理服务
  - SignatureService: 签名服务
  - ReceiptService: 回执处理服务
  - TaskService: 任务队列服务
- CustomsModel: 数据模型
  - ConfigModel: 配置模型
  - LogModel: 日志模型
  - ReceiptModel: 回执模型
  - TaskModel: 任务队列模型
```

### 4.3 接口对接

1. **事件监听器**：
   - `app\event\OrderPaySuccess`: 订单支付成功事件
   - `app\event\PaymentSuccess`: 支付成功事件

2. **电子口岸签名服务**:
   - 通过HTTP调用本地签名服务接口
   - 处理签名结果和验证

## 5. 错误处理与重试机制

### 5.1 错误处理策略

1. **网络错误**：记录错误日志，将任务加入重试队列
2. **签名错误**：记录详细错误信息，触发告警，需人工干预
3. **数据格式错误**：记录错误详情，进行数据修正后重试
4. **海关系统错误**：根据错误类型决定是否重试

### 5.2 重试机制设计

1. **重试队列**：使用数据库表存储待重试任务
2. **重试策略**：指数退避算法，初始等待30秒，最大等待4小时
3. **最大重试次数**：5次
4. **监控与告警**：连续失败3次触发系统告警

## 6. 配置管理设计

### 6.1 配置项

1. **环境配置**：测试/生产环境切换
2. **API地址配置**：海关接口地址
3. **签名服务配置**：签名服务地址、超时设置、认证令牌
4. **业务参数配置**：电商平台代码、支付企业代码等
5. **重试策略配置**：重试间隔、最大重试次数

### 6.2 配置界面

管理后台新增"海关接口配置"页面，包含以下功能：
1. 基本配置管理
2. 日志查询
3. 回执记录查询
4. 任务队列管理

## 7. 安装/卸载机制

### 7.1 安装过程

1. 创建数据表：customs_chinaport_config, customs_chinaport_log, customs_chinaport_receipt, customs_chinaport_task
2. 添加基本配置项
3. 注册事件监听器

### 7.2 卸载过程

1. 删除数据表
2. 删除配置项
3. 取消事件监听器注册

## 8. 数据安全考虑

1. **敏感数据加密**：配置中的认证信息使用加密存储
2. **签名验证**：所有海关交互数据必须进行签名验证
3. **日志脱敏**：日志记录中对敏感信息进行脱敏处理

## 9. 性能优化考虑

1. **异步处理**：使用队列进行异步数据处理
2. **批量处理**：支持批量数据提交（如果海关接口支持）
3. **数据压缩**：减少网络传输量

## 10. 总结

本设计方案通过模块化架构，清晰的数据流处理，完善的错误处理和重试机制，以及友好的配置管理界面，实现了海关跨境电商数据对接插件的重新设计，满足了高可靠性、高安全性、易维护性和易配置性的设计目标。

在实施过程中，将逐步完成各个模块的开发和测试，确保整个插件的稳定运行。 