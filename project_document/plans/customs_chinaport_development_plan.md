# 海关跨境电商数据对接插件开发计划

## 1. 目录结构设计

插件遵循YouPin的插件规范，采用以下目录结构：

```
addon/customs_chinaport/
  ├── admin/                # 管理后台相关代码
  │   ├── controller/       # 后台控制器
  │   │   └── Chinaport.php # 主控制器
  │   └── view/             # 后台视图
  │       └── chinaport/    # 视图文件夹
  │           ├── index.html  # 概览页
  │           ├── config.html # 配置页
  │           └── log.html    # 日志页
  ├── config/               # 配置文件目录
  │   ├── info.php          # 插件基本信息
  │   ├── config.php        # 插件配置
  │   ├── menu_admin.php    # 后台菜单配置
  │   ├── menu_shop.php     # 店铺菜单配置
  │   ├── api.php           # API配置
  │   └── validate.php      # 数据验证规则
  ├── data/                 # 数据相关文件
  │   ├── install.sql       # 安装SQL
  │   └── uninstall.sql     # 卸载SQL
  ├── event/                # 事件处理文件
  │   ├── Install.php       # 安装事件处理
  │   ├── Uninstall.php     # 卸载事件处理
  │   ├── OrderPaySuccess.php # 订单支付成功事件
  │   └── PaymentSuccess.php  # 支付成功事件
  ├── model/                # 数据模型
  │   ├── Config.php        # 配置模型
  │   ├── Log.php           # 日志模型
  │   ├── Receipt.php       # 回执模型
  │   └── Task.php          # 任务队列模型
  ├── service/              # 业务逻辑服务
  │   ├── CustomsService.php # 海关服务主类
  │   ├── OrderService.php   # 订单数据处理服务
  │   ├── PaymentService.php # 支付数据处理服务
  │   ├── SignatureService.php # 签名服务
  │   ├── ReceiptService.php # 回执处理服务
  │   └── TaskService.php    # 任务队列服务
  ├── controller/           # 控制器
  │   └── Api.php           # API控制器
  └── icon.png              # 插件图标
```

## 2. 数据表SQL设计

### 2.1 install.sql

```sql
-- 配置表
CREATE TABLE IF NOT EXISTS `customs_chinaport_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键名',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关接口配置表';

-- 日志表
CREATE TABLE IF NOT EXISTS `customs_chinaport_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '日志类型: 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-失败 1-成功',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关接口日志表';

-- 回执记录表
CREATE TABLE IF NOT EXISTS `customs_chinaport_receipt` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `receipt_id` varchar(100) NOT NULL COMMENT '回执ID',
  `receipt_type` varchar(20) NOT NULL COMMENT '回执类型: order-订单 payment-支付',
  `original_xml` text COMMENT '原始XML数据',
  `customs_code` varchar(50) DEFAULT '' COMMENT '海关编码',
  `customs_name` varchar(100) DEFAULT '' COMMENT '海关名称',
  `status` varchar(10) DEFAULT '' COMMENT '回执状态',
  `status_desc` varchar(100) DEFAULT '' COMMENT '状态描述',
  `order_no` varchar(50) DEFAULT '' COMMENT '订单号',
  `payment_no` varchar(50) DEFAULT '' COMMENT '支付单号',
  `note` varchar(500) DEFAULT '' COMMENT '回执信息',
  `receipt_time` int(11) NOT NULL DEFAULT '0' COMMENT '回执时间',
  `process_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态: 0-未处理 1-已处理',
  `process_message` varchar(500) DEFAULT '' COMMENT '处理消息',
  `process_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_type` (`receipt_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_no` (`payment_no`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_receipt_time` (`receipt_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关回执记录表';

-- 任务队列表
CREATE TABLE IF NOT EXISTS `customs_chinaport_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` varchar(50) NOT NULL COMMENT '任务ID（全局唯一）',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型：order-订单上报 payment-支付上报',
  `business_id` varchar(50) NOT NULL COMMENT '业务ID（订单号/支付号）',
  `data` text COMMENT '任务数据（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务状态：0-待处理 1-处理中 2-处理成功 3-处理失败',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry` int(11) NOT NULL DEFAULT '5' COMMENT '最大重试次数',
  `next_retry_time` int(11) NOT NULL DEFAULT '0' COMMENT '下次重试时间',
  `last_error` varchar(500) DEFAULT '' COMMENT '最后一次错误信息',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_id` (`task_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_status` (`status`),
  KEY `idx_next_retry_time` (`next_retry_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关任务队列表';

-- 初始配置数据
INSERT INTO `customs_chinaport_config` (`config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES
('api_url_test', 'https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload', '测试环境API地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('api_url_prod', 'https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload', '生产环境API地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('environment', 'test', '当前环境配置（test/prod）', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_url', 'http://localhost:8080/rpc/eport/signature', '签名服务地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_timeout', '10', '签名服务超时时间', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_token', '', '签名服务认证令牌', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('ebp_code', '', '电商平台代码', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('pay_code', '', '支付企业代码', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('retry_interval', '30', '重试初始间隔（秒）', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('retry_max_times', '5', '最大重试次数', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

### 2.2 uninstall.sql

```sql
-- 删除配置表
DROP TABLE IF EXISTS `customs_chinaport_config`;

-- 删除日志表
DROP TABLE IF EXISTS `customs_chinaport_log`;

-- 删除回执记录表
DROP TABLE IF EXISTS `customs_chinaport_receipt`;

-- 删除任务队列表
DROP TABLE IF EXISTS `customs_chinaport_task`;
```

## 3. 主要类和方法设计

### 3.1 模型类

#### 3.1.1 Config.php

```php
<?php
namespace addon\customs_chinaport\model;

use think\Model;

class Config extends Model
{
    protected $name = 'customs_chinaport_config';
    
    /**
     * 获取配置项
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        $config = $this->where(['config_key' => $key])->find();
        return $config ? $config['config_value'] : $default;
    }
    
    /**
     * 设置配置项
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param string $desc 配置描述
     * @return boolean
     */
    public function setConfig($key, $value, $desc = '')
    {
        $time = time();
        $config = $this->where(['config_key' => $key])->find();
        
        if ($config) {
            return $this->where(['config_key' => $key])->update([
                'config_value' => $value,
                'config_desc' => $desc ?: $config['config_desc'],
                'update_time' => $time
            ]);
        } else {
            return $this->insert([
                'config_key' => $key,
                'config_value' => $value,
                'config_desc' => $desc,
                'create_time' => $time,
                'update_time' => $time
            ]);
        }
    }
    
    /**
     * 获取所有配置
     * @return array
     */
    public function getAllConfig()
    {
        $configs = $this->select()->toArray();
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config['config_key']] = $config['config_value'];
        }
        
        return $result;
    }
}
```

#### 3.1.2 Log.php

```php
<?php
namespace addon\customs_chinaport\model;

use think\Model;

class Log extends Model
{
    protected $name = 'customs_chinaport_log';
    
    // 日志类型常量
    const TYPE_REQUEST = 1;     // 接收请求
    const TYPE_XML = 2;         // 生成XML
    const TYPE_ORDER = 3;       // 提交订单
    const TYPE_PAYMENT = 4;     // 提交支付
    const TYPE_RECEIPT = 5;     // 回执处理
    const TYPE_RETRY = 6;       // 限流重试
    
    /**
     * 记录日志
     * @param int $type 日志类型
     * @param mixed $requestData 请求数据
     * @param mixed $responseData 响应数据
     * @param int $status 状态：0-失败 1-成功
     * @param string $errorMsg 错误信息
     * @return int 日志ID
     */
    public function addLog($type, $requestData, $responseData = null, $status = 1, $errorMsg = '')
    {
        $time = time();
        
        // 处理敏感数据脱敏
        $requestData = $this->maskSensitiveData($requestData);
        $responseData = $this->maskSensitiveData($responseData);
        
        $data = [
            'type' => $type,
            'request_data' => is_array($requestData) ? json_encode($requestData, JSON_UNESCAPED_UNICODE) : $requestData,
            'response_data' => is_array($responseData) ? json_encode($responseData, JSON_UNESCAPED_UNICODE) : $responseData,
            'status' => $status,
            'error_msg' => $errorMsg,
            'create_time' => $time,
            'update_time' => $time
        ];
        
        $this->insert($data);
        return $this->getLastInsID();
    }
    
    /**
     * 获取日志列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getLogList($condition = [], $field = '*', $order = 'create_time desc', $page = 1, $pageSize = 20)
    {
        $list = $this->where($condition)
            ->field($field)
            ->order($order)
            ->page($page, $pageSize)
            ->select()
            ->toArray();
            
        $count = $this->where($condition)->count();
        
        return ['list' => $list, 'count' => $count];
    }
    
    /**
     * 敏感数据脱敏处理
     * @param mixed $data 原始数据
     * @return mixed 脱敏后的数据
     */
    private function maskSensitiveData($data)
    {
        // 实现数据脱敏逻辑
        return $data;
    }
}
```

### 3.2 服务类

#### 3.2.1 CustomsService.php

```php
<?php
namespace addon\customs_chinaport\service;

use addon\customs_chinaport\model\Config;

/**
 * 海关服务主类
 */
class CustomsService
{
    /**
     * 获取实例
     * @return CustomsService
     */
    public static function instance()
    {
        static $instance;
        if (null === $instance) {
            $instance = new static();
        }
        return $instance;
    }
    
    /**
     * 获取订单服务
     * @return OrderService
     */
    public function order()
    {
        return OrderService::instance();
    }
    
    /**
     * 获取支付服务
     * @return PaymentService
     */
    public function payment()
    {
        return PaymentService::instance();
    }
    
    /**
     * 获取签名服务
     * @return SignatureService
     */
    public function signature()
    {
        return SignatureService::instance();
    }
    
    /**
     * 获取回执服务
     * @return ReceiptService
     */
    public function receipt()
    {
        return ReceiptService::instance();
    }
    
    /**
     * 获取任务服务
     * @return TaskService
     */
    public function task()
    {
        return TaskService::instance();
    }
    
    /**
     * 获取配置
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        return (new Config())->getConfig($key, $default);
    }
    
    /**
     * 获取API地址
     * @return string
     */
    public function getApiUrl()
    {
        $env = $this->getConfig('environment', 'test');
        return $this->getConfig('api_url_' . $env);
    }
}
```

### 3.3 事件监听器

#### 3.3.1 OrderPaySuccess.php

```php
<?php
namespace addon\customs_chinaport\event;

use addon\customs_chinaport\service\CustomsService;
use addon\customs_chinaport\model\Log;

/**
 * 订单支付成功事件
 */
class OrderPaySuccess
{
    /**
     * 执行事件
     * @param array $param 事件参数
     * @return void
     */
    public function handle($param)
    {
        try {
            // 记录事件日志
            $logModel = new Log();
            $logId = $logModel->addLog(Log::TYPE_REQUEST, $param, null, 1, '订单支付成功事件');
            
            // 获取订单信息
            $orderNo = $param['order_no'] ?? '';
            if (empty($orderNo)) {
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'error_msg' => '订单号为空'
                ]);
                return;
            }
            
            // 添加订单上报任务
            CustomsService::instance()->task()->addTask('order', $orderNo, $param);
            
        } catch (\Exception $e) {
            // 记录异常日志
            if (isset($logId)) {
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'error_msg' => '异常：' . $e->getMessage()
                ]);
            }
        }
    }
}
```

## 4. 开发任务分解

### 4.1 基础架构开发 (Day 1-2)

1. 创建插件目录结构
2. 创建基本配置文件
3. 实现安装/卸载脚本
4. 创建数据表
5. 实现基本模型类

### 4.2 核心功能开发 (Day 3-5)

1. 实现配置管理功能
2. 实现签名服务对接
3. 实现订单数据处理服务
4. 实现支付数据处理服务
5. 实现回执处理服务
6. 实现任务队列服务

### 4.3 管理界面开发 (Day 6-7)

1. 实现配置管理界面
2. 实现日志查询界面
3. 实现回执记录查询界面
4. 实现任务队列管理界面

### 4.4 测试与优化 (Day 8-10)

1. 单元测试开发
2. 集成测试开发
3. 性能优化
4. 问题修复

## 5. 接口规范

### 5.1 订单数据格式

```json
{
  "sessionID": "唯一会话ID",
  "orderInfo": {
    "orderNo": "订单编号",
    "ebpCode": "电商平台代码",
    "orderInfo": {
      // 订单详细信息...
    }
  }
}
```

### 5.2 支付数据格式

```json
{
  "sessionID": "唯一会话ID",
  "payExchangeInfoHead": {
    "guid": "全局唯一ID",
    "initalRequest": "原始请求XML数据",
    "initalResponse": "原始响应XML数据",
    "ebpCode": "电商平台代码",
    "payCode": "支付企业代码",
    "payTransactionId": "交易流水号",
    "totalAmount": "总金额",
    "currency": "币制",
    "verDept": "验核企业",
    "payType": "支付类型",
    "tradingTime": "交易时间",
    "note": "备注"
  },
  "payExchangeInfoLists": [
    {
      "orderNo": "订单编号",
      "goodsInfo": [
        {
          "gname": "商品名称",
          "itemLink": "商品链接"
        }
      ],
      "recpAccount": "收款账号",
      "recpCode": "收款企业代码",
      "recpName": "收款企业名称"
    }
  ],
  "serviceTime": "服务器时间戳"
}
```

## 6. 测试计划

### 6.1 单元测试

1. 配置模型测试
2. 日志模型测试
3. 回执模型测试
4. 任务队列模型测试
5. 签名服务测试
6. 数据处理服务测试

### 6.2 集成测试

1. 订单上报流程测试
2. 支付上报流程测试
3. 回执处理流程测试
4. 重试机制测试
5. 异常处理测试

### 6.3 端到端测试

1. 与测试环境的连接测试
2. 完整业务流程测试
3. 性能压力测试

## 7. 风险评估与应对策略

### 7.1 潜在风险

1. 签名服务不可用
2. 海关接口不稳定
3. 数据格式变更
4. 性能瓶颈

### 7.2 应对策略

1. 实现完善的错误处理和重试机制
2. 建立监控告警系统
3. 保持与海关政策和接口变更的同步
4. 定期进行性能测试和优化

## 8. 部署与维护计划

### 8.1 部署步骤

1. 通过后台插件管理页面上传插件
2. 进行插件安装
3. 配置插件参数
4. 进行测试环境验证
5. 切换到生产环境

### 8.2 维护计划

1. 定期检查日志和错误记录
2. 监控接口调用成功率
3. 定期清理过期日志数据
4. 根据海关政策变化进行更新维护

## 9. 总结

本开发计划详细描述了海关跨境电商数据对接插件的开发细节，包括目录结构、数据表设计、类和方法设计、开发任务分解、接口规范、测试计划、风险评估和部署维护计划。通过按照本计划进行开发，将能够实现一个高质量、高可靠性的海关数据对接插件。 