# 海关跨境电商数据对接插件文件模板

本文档提供海关跨境电商数据对接插件的关键文件模板，用于指导开发人员按照规范实现插件功能。

## 1. 配置文件模板

### 1.1 info.php

```php
<?php
return [
    'name' => 'customs_chinaport',
    'title' => '海关跨境电商接口',
    'description' => '对接海关跨境电商进口统一版信息化系统平台，实现数据实时获取接口功能',
    'status' => 1,
    'author' => 'Youpin Team',
    'version' => '1.0.0',
    'content' => '',
    'type' => 'tool',
];
```

### 1.2 config.php

```php
<?php
/**
 * 海关接口模块配置文件
 */
return [
    'name' => '海关跨境电商接口',
    'title' => '海关跨境电商进口统一版信息化系统平台对接模块',
    'description' => '对接海关跨境电商进口统一版信息化系统平台，实现数据实时获取接口功能',
    'status' => 1,
    'author' => 'Youpin Team',
    'version' => '1.0.0',
    'api_url' => [
        'test' => 'https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload',  // 测试环境URL
        'prod' => 'https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload',   // 生产环境URL
    ],
    'signature_service' => [
        'url' => 'http://localhost:8080/rpc/eport/signature', // 签名服务地址
        'timeout' => 10, // 超时时间（秒）
        'auth_token' => '', // 认证令牌
    ],
    'env' => 'test', // 环境配置: test - 测试环境, prod - 生产环境
];
```

### 1.3 menu_admin.php

```php
<?php
/**
 * 海关接口模块菜单配置文件
 */
return [
    [
        'name' => 'CUSTOMS_CHINAPORT',
        'title' => '海关接口',
        'url' => 'customs_chinaport/admin/chinaport/index',
        'parent' => 'CONFIG_ROOT',
        'is_show' => 1,
        'is_menu' => 1,
        'sort' => 8,
        'child_list' => [
            [
                'name' => 'CUSTOMS_CHINAPORT_INDEX',
                'title' => '接口概览',
                'url' => 'customs_chinaport/admin/chinaport/index',
                'is_show' => 1,
                'is_menu' => 1,
                'sort' => 1,
            ],
            [
                'name' => 'CUSTOMS_CHINAPORT_LOG',
                'title' => '接口日志',
                'url' => 'customs_chinaport/admin/chinaport/log',
                'is_show' => 1,
                'is_menu' => 1,
                'sort' => 5,
            ],
            [
                'name' => 'CUSTOMS_CHINAPORT_RECEIPT',
                'title' => '回执管理',
                'url' => 'customs_chinaport/admin/chinaport/receipt',
                'is_show' => 1,
                'is_menu' => 1,
                'sort' => 8,
            ],
            [
                'name' => 'CUSTOMS_CHINAPORT_TASK',
                'title' => '任务队列',
                'url' => 'customs_chinaport/admin/chinaport/task',
                'is_show' => 1,
                'is_menu' => 1,
                'sort' => 9,
            ],
            [
                'name' => 'CUSTOMS_CHINAPORT_CONFIG',
                'title' => '接口配置',
                'url' => 'customs_chinaport/admin/chinaport/config',
                'is_show' => 1,
                'is_menu' => 1,
                'sort' => 10,
            ]
        ]
    ]
];
```

## 2. 事件处理文件模板

### 2.1 Install.php

```php
<?php
namespace addon\customs_chinaport\event;

/**
 * 应用安装
 */
class Install
{
    /**
     * 执行安装
     */
    public function handle()
    {
        try {
            execute_sql('addon/customs_chinaport/data/install.sql');
            return success();
        } catch (\Exception $e) {
            return error(-1, '执行安装SQL失败：' . $e->getMessage());
        }
    }
}
```

### 2.2 Uninstall.php

```php
<?php
namespace addon\customs_chinaport\event;

/**
 * 应用卸载
 */
class Uninstall
{
    /**
     * 执行卸载
     */
    public function handle()
    {
        try {
            // 执行卸载SQL
            if (file_exists('addon/customs_chinaport/data/uninstall.sql')) {
                execute_sql('addon/customs_chinaport/data/uninstall.sql');
            }
            
            // 清理配置
            \think\facade\Db::name('config')->where([
                ['app_module', '=', 'admin'],
                ['config_key', 'like', 'customs_chinaport%']
            ])->delete();
            
            return success();
        } catch (\Exception $e) {
            return error(-1, '卸载插件失败：' . $e->getMessage());
        }
    }
}
```

### 2.3 OrderPaySuccess.php

```php
<?php
namespace addon\customs_chinaport\event;

use addon\customs_chinaport\service\CustomsService;
use addon\customs_chinaport\model\Log;

/**
 * 订单支付成功事件
 */
class OrderPaySuccess
{
    /**
     * 执行事件
     * @param array $param 事件参数
     * @return void
     */
    public function handle($param)
    {
        try {
            // 记录事件日志
            $logModel = new Log();
            $logId = $logModel->addLog(Log::TYPE_REQUEST, $param, null, 1, '订单支付成功事件');
            
            // 获取订单信息
            $orderNo = $param['order_no'] ?? '';
            if (empty($orderNo)) {
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'error_msg' => '订单号为空'
                ]);
                return;
            }
            
            // 添加订单上报任务
            CustomsService::instance()->task()->addTask('order', $orderNo, $param);
            
        } catch (\Exception $e) {
            // 记录异常日志
            if (isset($logId)) {
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'error_msg' => '异常：' . $e->getMessage()
                ]);
            }
        }
    }
}
```

## 3. 服务类模板

### 3.1 CustomsService.php

```php
<?php
namespace addon\customs_chinaport\service;

use addon\customs_chinaport\model\Config;

/**
 * 海关服务主类
 */
class CustomsService
{
    /**
     * 获取实例
     * @return CustomsService
     */
    public static function instance()
    {
        static $instance;
        if (null === $instance) {
            $instance = new static();
        }
        return $instance;
    }
    
    /**
     * 获取订单服务
     * @return OrderService
     */
    public function order()
    {
        return OrderService::instance();
    }
    
    /**
     * 获取支付服务
     * @return PaymentService
     */
    public function payment()
    {
        return PaymentService::instance();
    }
    
    /**
     * 获取签名服务
     * @return SignatureService
     */
    public function signature()
    {
        return SignatureService::instance();
    }
    
    /**
     * 获取回执服务
     * @return ReceiptService
     */
    public function receipt()
    {
        return ReceiptService::instance();
    }
    
    /**
     * 获取任务服务
     * @return TaskService
     */
    public function task()
    {
        return TaskService::instance();
    }
    
    /**
     * 获取配置
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        return (new Config())->getConfig($key, $default);
    }
    
    /**
     * 获取API地址
     * @return string
     */
    public function getApiUrl()
    {
        $env = $this->getConfig('environment', 'test');
        return $this->getConfig('api_url_' . $env);
    }
}
```

### 3.2 SignatureService.php (部分代码)

```php
<?php
namespace addon\customs_chinaport\service;

use addon\customs_chinaport\model\Log;

/**
 * 签名服务
 */
class SignatureService
{
    /**
     * 获取实例
     * @return SignatureService
     */
    public static function instance()
    {
        static $instance;
        if (null === $instance) {
            $instance = new static();
        }
        return $instance;
    }
    
    /**
     * 生成签名
     * @param string $data 待签名数据
     * @return array 签名结果 [status => bool, signature => string, message => string]
     */
    public function generateSignature($data)
    {
        // 获取签名服务配置
        $customsService = CustomsService::instance();
        $signServiceUrl = $customsService->getConfig('signature_service_url');
        $timeout = intval($customsService->getConfig('signature_service_timeout', 10));
        $authToken = $customsService->getConfig('signature_service_token', '');
        
        // 请求签名服务
        try {
            // 记录日志
            $logModel = new Log();
            $logId = $logModel->addLog(Log::TYPE_XML, $data, null, 1, '生成签名');
            
            // 构建请求
            $requestData = [
                'data' => base64_encode($data),
                'auth_token' => $authToken
            ];
            
            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $signServiceUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 处理响应
            if ($httpCode != 200 || !$response) {
                $errorMsg = "签名服务请求失败: HTTP代码 $httpCode, 错误: $error";
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'response_data' => $errorMsg,
                    'error_msg' => $errorMsg
                ]);
                return ['status' => false, 'signature' => '', 'message' => $errorMsg];
            }
            
            $result = json_decode($response, true);
            if (!$result || !isset($result['status'])) {
                $errorMsg = "签名服务响应解析失败: $response";
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'response_data' => $response,
                    'error_msg' => $errorMsg
                ]);
                return ['status' => false, 'signature' => '', 'message' => $errorMsg];
            }
            
            // 更新日志
            $logModel->where(['id' => $logId])->update([
                'status' => $result['status'] ? 1 : 0,
                'response_data' => $response,
                'error_msg' => $result['status'] ? '' : ($result['message'] ?? '签名失败')
            ]);
            
            return [
                'status' => $result['status'],
                'signature' => $result['signature'] ?? '',
                'message' => $result['message'] ?? ''
            ];
            
        } catch (\Exception $e) {
            // 记录异常
            if (isset($logId)) {
                $logModel->where(['id' => $logId])->update([
                    'status' => 0,
                    'error_msg' => '异常：' . $e->getMessage()
                ]);
            }
            return ['status' => false, 'signature' => '', 'message' => $e->getMessage()];
        }
    }
}
```

## 4. 后台控制器模板

### 4.1 Chinaport.php (部分代码)

```php
<?php
namespace addon\customs_chinaport\admin\controller;

use app\admin\controller\BaseAdmin;
use addon\customs_chinaport\model\Config;
use addon\customs_chinaport\model\Log;
use addon\customs_chinaport\model\Receipt;
use addon\customs_chinaport\model\Task;

/**
 * 海关接口管理控制器
 */
class Chinaport extends BaseAdmin
{
    /**
     * 接口概览
     */
    public function index()
    {
        // 获取统计信息
        $logModel = new Log();
        $receiptModel = new Receipt();
        $taskModel = new Task();
        
        // 获取今日日志统计
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = $todayStart + 86399;
        $todayLogs = $logModel->where([
            ['create_time', 'between', [$todayStart, $todayEnd]]
        ])->count();
        
        $todaySuccessLogs = $logModel->where([
            ['create_time', 'between', [$todayStart, $todayEnd]],
            ['status', '=', 1]
        ])->count();
        
        // 获取回执统计
        $totalReceipts = $receiptModel->count();
        $pendingReceipts = $receiptModel->where(['process_status' => 0])->count();
        
        // 获取任务队列统计
        $pendingTasks = $taskModel->where(['status' => Task::STATUS_PENDING])->count();
        $failedTasks = $taskModel->where(['status' => Task::STATUS_FAILED])->count();
        
        // 统计信息
        $statistics = [
            'today_logs' => $todayLogs,
            'today_success_rate' => $todayLogs > 0 ? round($todaySuccessLogs * 100 / $todayLogs, 2) : 0,
            'total_receipts' => $totalReceipts,
            'pending_receipts' => $pendingReceipts,
            'pending_tasks' => $pendingTasks,
            'failed_tasks' => $failedTasks
        ];
        
        $this->assign('statistics', $statistics);
        return $this->fetch();
    }
    
    /**
     * 接口配置
     */
    public function config()
    {
        if (request()->isAjax()) {
            $configModel = new Config();
            $data = input('post.');
            
            // 更新配置
            foreach ($data as $key => $value) {
                $configModel->setConfig($key, $value);
            }
            
            return success('保存成功');
        } else {
            // 获取所有配置
            $configModel = new Config();
            $configs = $configModel->getAllConfig();
            
            $this->assign('configs', $configs);
            return $this->fetch();
        }
    }
}
```

## 5. 数据模型模板

上面已经提供了主要的模型类和服务类模板，这里不再重复。完整的模型类和服务类可以参考开发计划文档中的详细设计。

## 6. 总结

以上提供了海关跨境电商数据对接插件的关键文件模板，开发人员可以基于这些模板进行进一步的开发和定制。在实际开发过程中，需要结合具体业务需求和系统环境进行适当调整。 