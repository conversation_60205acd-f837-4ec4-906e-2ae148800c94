# 海关跨境电商进口统一版信息化系统平台数据实时获取接口文档

## 背景
项目 ID: CUSTOMS-CHINAPORT-179
任务文件名: customs_chinaport_api.md
创建于: 2023-05-25 14:30:00
关联协议: RIPER-5 v5.0

## 概述

本文档描述了海关跨境电商进口统一版信息化系统平台（简称"海关179"）数据实时获取接口的使用方法。该接口用于满足海关对跨境电商平台数据的实时获取需求，支持订单数据和支付数据的上报。

## API 接口说明

### 1. 海关数据获取接口 (platDataOpen)

#### 接口说明
该接口用于接收海关发起的数据获取请求，当海关系统需要获取特定订单的数据时，会调用此接口。

#### 请求方式
- **HTTP方法**: POST
- **接口路径**: `/addon/customsChinaport/api/api/platDataOpen`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| orderNo | String | 是 | 订单编号，最大长度60 |
| sessionID | String | 是 | 会话ID，最大长度36 |
| serviceTime | Long | 是 | 服务时间戳（毫秒） |

#### 请求示例
```json
{
  "orderNo": "************",
  "sessionID": "SESSION_ID_12345678",
  "serviceTime": *************
}
```

#### 返回参数

| 参数名 | 类型 | 说明 |
|-------|------|-----|
| code | String | 状态码：10000-成功，20000-参数错误 |
| message | String | 错误消息（成功时为空） |
| serviceTime | Long | 服务器时间戳（毫秒） |

#### 返回示例
```json
{
  "code": "10000",
  "message": "",
  "serviceTime": 1684997450000
}
```

#### 错误码说明

| 错误码 | 说明 |
|-------|------|
| 10000 | 成功 |
| 20000 | 参数错误 |

#### 特殊说明
- 即使处理过程中发生异常，接口也会返回成功状态码（10000），避免海关系统重复推送请求
- 接口接收到请求后会创建异步任务，实际数据处理在后台进行

### 2. 订单数据下发接口 (getOrderData)

#### 接口说明
该接口供客户端定期请求获取需要报关的订单数据。客户端需要提供有效的时间戳和签名进行身份验证。

#### 请求方式
- **HTTP方法**: POST
- **接口路径**: `/addon/customsChinaport/api/api/getOrderData`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| timestamp | Integer | 是 | 当前时间戳（秒） |
| sign | String | 是 | 签名，MD5(timestamp + client_sign_key) |

#### 请求示例
```json
{
  "timestamp": 1684997400,
  "sign": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
}
```

#### 返回参数
返回订单数据数组，每个订单包含以下字段：

| 参数名 | 类型 | 说明 |
|-------|------|-----|
| initalRequest | String | 支付初始请求数据 |
| initalResponse | String | 支付初始响应数据 |
| payTransactionId | String | 支付交易ID |
| verDept | String | 核验机构，默认为"3"（其他） |
| totalAmount | Float | 订单总金额 |
| tradingTime | String | 交易时间，格式：YYYYMMDDHHmmss |
| goods | Array | 商品信息数组 |
| ebpCode | String | 电商平台代码 |
| payCode | String | 支付企业代码 |
| currency | String | 币种，默认"142"（人民币） |
| recpAccount | String | 收款账户 |
| recpName | String | 收款人姓名 |
| orderNo | String | 订单编号 |
| sessionID | String | 会话ID |

其中，goods数组中的每个商品包含以下字段：

| 参数名 | 类型 | 说明 |
|-------|------|-----|
| gname | String | 商品名称 |
| itemLink | String | 商品链接 |

#### 返回示例
```json
[
  {
    "initalRequest": "支付初始请求数据",
    "initalResponse": "支付初始响应数据",
    "payTransactionId": "PAY123456789",
    "verDept": "3",
    "totalAmount": 199.99,
    "tradingTime": "**************",
    "goods": [
      {
        "gname": "进口食品",
        "itemLink": "https://example.com/goods?id=123"
      }
    ],
    "ebpCode": "EBPCODE123",
    "payCode": "4403169D3W",
    "currency": "142",
    "recpAccount": "收款账户",
    "recpName": "收款人姓名",
    "orderNo": "************",
    "sessionID": "SESSION_ID_12345678"
  }
]
```

#### 特殊说明
- 如果签名验证失败或无待处理订单，接口将返回空数组 `[]`
- 签名验证时，时间戳有效期为30秒，超过30秒的请求将被拒绝
- 每次请求最多返回15条订单数据

## 签名算法

### 签名生成方法
1. 将时间戳（秒级）与签名密钥拼接
2. 对拼接后的字符串进行MD5加密（32位小写）

```php
$timestamp = time();
$sign_key = 'your_sign_key'; // 从配置中获取
$sign = md5($timestamp . $sign_key);
```

### 签名验证规则
1. 时间戳必须在当前时间的前后30秒内
2. 签名必须与服务器端使用相同算法生成的签名一致

## 配置项说明

以下是系统中与海关接口相关的主要配置项：

| 配置键 | 说明 | 默认值 |
|-------|------|-------|
| api_url_test | 测试环境API地址 | https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload |
| api_url_prod | 生产环境API地址 | https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload |
| environment | 当前环境配置 | test |
| client_sign_key | 客户端签名密钥 | default_sign_key_please_change |
| goods_url_prefix | 商品URL前缀 | https://example.com/goods |
| recp_account | 收款账户 | - |
| recp_name | 收款人姓名 | - |

## 安全性说明

1. **签名验证**: 所有客户端请求都需要通过签名验证，确保请求的合法性
2. **时间戳验证**: 防止重放攻击，请求必须在30秒有效期内
3. **错误处理**: 接口不会返回详细的错误信息给客户端，避免信息泄露
4. **数据加密**: 敏感数据在传输过程中应使用HTTPS协议加密

## 测试方法

### 测试海关数据获取接口
```bash
curl -X POST https://youpin/addon/customsChinaport/api/api/platDataOpen \
  -H "Content-Type: application/json" \
  -d '{"orderNo":"TEST20230525001","sessionID":"TEST_SESSION_001","serviceTime":*************}'
```

### 测试订单数据下发接口
```php
// 生成签名
$timestamp = time();
$sign_key = 'test_sign_key'; // 测试环境签名密钥
$sign = md5($timestamp . $sign_key);

// 发送请求
curl -X POST https://youpin/addon/customsChinaport/api/api/getOrderData \
  -H "Content-Type: application/json" \
  -d '{"timestamp":'.$timestamp.',"sign":"'.$sign.'"}'
```

## 常见问题

1. **Q: 签名验证失败怎么办？**  
   A: 检查签名密钥是否正确，确认时间戳是否在有效期内，检查签名算法是否正确实现。

2. **Q: 接口返回空数据怎么办？**  
   A: 如果getOrderData接口返回空数组，可能是因为：签名验证失败、没有待处理的订单数据、或者订单数据不完整。

3. **Q: 如何修改配置项？**  
   A: 可以通过后台管理界面修改配置，或直接操作数据库中的`xm_customs_chinaport_config`表。

## 更新历史

| 日期 | 版本 | 说明 |
|------|------|------|
| 2023-05-25 | 1.0.0 | 初始版本 