<?php
namespace extend\yeepay;
/**�ױ�֧������
 * Class Yeepay
 * @package extend\yeepay
 */
class Yeepay {

    /** config/yeepay.php����
     * @array
     */
    protected $config;

    protected $merchantNo;
    protected $parentMerchantNo;
    protected $private_key;
    protected $yop_public_key;
    protected $appkey;

    public function __construct()
    {
        //��ʼ�����ò���
        $this->initConfig();
    }


    public function initConfig()
    {
        $this->config           = config('yeepay');
        $this->merchantNo       = $this->config['merchantNo'] ?? '';
        $this->parentMerchantNo = $this->config['parentMerchantNo'] ?? '';
        $this->private_key      = $this->config['private_key'] ?? '';
        $this->yop_public_key   = $this->config['yop_public_key'] ?? '';
        $this->appkey           = $this->config['appkey'] ?? '';
    }
}