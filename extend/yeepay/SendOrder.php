<?php
namespace extend\yeepay;


use YopClient3;
use YopSignUtils;
use YopRequest;

require_once ("lib/YopClient3.php");
require_once ("lib/Util/YopSignUtils.php");


class SendOrder extends Yeepay {

    function getString($response){
        $str="";

        foreach ($response as $key => $value) {
            $str .= $key . "=" . $value . "&";
        }
        $getSign = substr($str, 0, strlen($str) - 1);
        return $getSign;
    }

    function  getUrl($response,$private_key)
    {
        $content=$this->getString($response);
        $sign=YopSignUtils::signRsa($content,$private_key);
        $url=$content."&sign=".$sign;
        return  $url;
    }

    function object_array($array) {
        if(is_object($array)) {
            $array = (array)$array;
        } if(is_array($array)) {
            foreach($array as $key=>$value) {
                $array[$key] = $this->object_array($value);
            }
        }
        return $array;
    }

    /**创建订单接口
     * @see https://open.yeepay.com/docs/v2/products/mini-program/apis/options__rest__v1.0__std__trade__order/index.html
     * @param $data
     * @return array|mixed|string
     */
    function order($data){
         $merchantNo = $this->merchantNo;
         $parentMerchantNo= $this->parentMerchantNo;
         $private_key= $this->private_key;
         $yop_public_key= $this->yop_public_key;
        $request = new YopRequest($this->appkey, $private_key, "https://open.yeepay.com/yop-center",$yop_public_key);

        $request->addParam("parentMerchantNo", $parentMerchantNo);
        $request->addParam("merchantNo", $merchantNo);
        $request->addParam("orderId", $data['orderId']);
        $request->addParam("orderAmount", $data['orderAmount']);
        $request->addParam("timeoutExpress", $data['timeoutExpress']);
        $request->addParam("requestDate", $data['requestDate']);
        $request->addParam("redirectUrl", $data['redirectUrl']);
        $request->addParam("notifyUrl", $data['notifyUrl']);
        $request->addParam("goodsParamExt", $data['goodsParamExt']);
        $request->addParam("paymentParamExt", $data['paymentParamExt']);
        $request->addParam("industryParamExt", $data['industryParamExt']);
        $request->addParam("memo", $data['memo']);
        $request->addParam("riskParamExt", $data['riskParamExt']);
        $request->addParam("csUrl", $data['csUrl']);
        $request->addParam("fundProcessType", $data['fundProcessType']);
        $request->addParam("divideDetail", $data['divideDetail']);
        $request->addParam("divideNotifyUrl", $data['divideNotifyUrl']);

        //     var_dump($request);

        $response = YopClient3::post("/rest/v1.0/sys/trade/order", $request);

        if($response->validSign==1){
            echo "返回结果签名验证成功!\n";
        }
        //取得返回结果
        $data= $this->object_array($response);

        // echo  $data;
        $token=$data['result']['token'];
        $cashter = array(
            "merchantNo" => $merchantNo ,
            "token" => $token,
            "timestamp" => $data['timestamp'],
            "directPayType" => $data['directPayType'],
            "cardType" => $data['cardType'],
            "userNo" => $data['userNo'],
            "userType" => $data['userType'],
            "ext" => $data['ext'],
        );

        $getUrl = $this->getUrl($cashter, $private_key);
        $getUrl=str_replace("&timestamp","&amp;timestamp",$getUrl);
        //print_r($getUrl );
        $url = "https://cash.yeepay.com/cashier/std?" . $getUrl;

        return $url ;

    }
}


?>

