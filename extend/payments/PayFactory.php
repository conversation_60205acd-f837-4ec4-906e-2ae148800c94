<?php
namespace payments;
/**
 * 支付实例
 * Class payFactory
 * @method static \payments\Services\newProtocolPay\Application   newProtocolPay()  新生支付应用
 */
class PayFactory
{
    public static function make($name, array $config = [])
    {
        $application = "\\payments\\Services\\{$name}\\Application";

        return new $application($config);
    }

    public static function __callStatic($name, $arguments)
    {
        return self::make($name, ...$arguments);
    }

}
