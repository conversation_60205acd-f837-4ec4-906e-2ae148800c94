<?php


namespace payments\kernel;


use GuzzleHttp\Exception\RequestException;

class Clients extends BaseClient
{
    public function sendRequest(APIRequest $request,$resultDefiniation) {
        $urlRequest = $request->getServerHost();

        $requestData = $request->getBody();

        $options['headers'] = $request->getHeaders();

        // 区分不同请求参数
        $request_method = strtolower($request->getMethod());
        switch ($request_method) {
            case 'post':
                $options['form_params'] = $requestData;
                break;
            case 'get':
                $options['query'] = $requestData;
                break;
            default:
                $options['body'] = $requestData;
        }

        try {
            $client = $this->getHttpClient()->request($request_method, $request->getServerHost(), $options);

            $resultDefiniation->setResponse($client);
            $resultDefiniation->setRequestData($requestData);
            $resultDefiniation->exception();
            return $resultDefiniation;
//        } catch (RequestException $requestException) {
//
//            throw new RequestException($requestException->getMessage(), $requestException->getCode());
        } catch (PayException $payException) {
            throw new PayException($payException->getMessage(), $payException->getCode());
        }

    }

}
