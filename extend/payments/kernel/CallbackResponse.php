<?php


namespace payments\kernel;


class CallbackResponse extends Response
{
    public function __construct(array $array)
    {
        $this->response = $array;
    }

    protected function getResponse(string $field = null)
    {
        $result = $this->response ?? false;
        return $result ? ($field ? $result[$field] ?? null : $result) : ($field ? null : []);
    }

    public function getRawResponse(): string
    {
        return json_encode($this->response);
    }

    public function getMsg(): string
    {
        return $this->getResponse('message') ?? '';
    }

    public function getBody(): array
    {
        return json_decode($this->getResponse('data'), true) ?? [];
    }


}
