<?php


namespace payments\kernel;

/**
 * 请求客户端基类
 * Class BaseClient
 * @package App\Services\Yihui\Kernel
 */
class BaseClient
{
    protected $defaultConfig = [];

    protected $userConfig = [];

    protected $request;

    public function __construct(array $config = [])
    {
        $this->userConfig = $config;
    }

    public function getConfig()
    {
        $base = [
            // https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html
            'http' => [
                'timeout' => 30.0,
                'verify' => false,
            ],
        ];
        return $base;
    }


    protected function getHttpClient()
    {
        if (empty($this->request)) {
            $this->request = new \GuzzleHttp\Client($this->getConfig()['http']);
        }
        return $this->request;
    }
}
