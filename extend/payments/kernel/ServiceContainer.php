<?php


namespace payments\kernel;


use phpDocumentor\Reflection\DocBlock\Tags\Method;

class ServiceContainer extends Clients
{
    /**
     * 映射方法，不存在则使用公共父类
     * @param string $name 方法名，自动转换形式 search_new=>SearchNew, search=>Search
     * @param array $arguments
     * @return Response
     */
    public function __call(string $name, array $arguments)
    {
        if ($name=='callback'){
            $method = str_replace('Application', $name . '\\application', get_class($this));
            return new $method();
        }
        // $method = str_replace('Application', ucfirst(Utils::camelize($name,'-')) . '\\Method', get_class($this));
        $method = str_replace('Application', $name . '\\paramRequest', get_class($this));
        $resultDefiniation = str_replace('Application', 'AppResponse', get_class($this));

        // 如果方法不存在，则使用公共父类
        if (!class_exists($method)) {
            $method = 'payments\kernel\APIRequest';
        }

        if (!class_exists($resultDefiniation)){
            $resultDefiniation = 'payments\kernel\Response';
        }
        // 实例化方法并发送请求
        return $this->sendRequest(new $method(...$arguments),new $resultDefiniation());
    }
}
