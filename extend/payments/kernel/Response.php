<?php
namespace payments\kernel;

use Psr\Http\Message\ResponseInterface;

/**
 * 响应类
 * Class Response
 */
class Response
{
    protected $response;
    protected $requestData;

    /**
     * @param mixed $response
     */
    public function setResponse($response)
    {
        $this->response = $response;
    }

    public function setRequestData($param){
        $this->requestData = $param;
    }

    protected function getResponse(string $field = null)
    {
        $result = !is_array($this->response)? json_decode($this->response->getStatusCode() === 200 ? (string)$this->response->getBody() : false, true): $this->response;
        return $result ? ($field ? $result[$field] ?? null : $result) : ($field ? null : []);
    }

    public function getRawResponse(): string
    {
        return is_array($this->response)? json_encode($this->response) : (string)$this->response->getBody();
    }

    public function isSuccessful(): bool
    {
        return $this->getCode() === 0;
    }

    public function getMsg(): string
    {
        return $this->getResponse('message') ?? "{$this->response->getStatusCode()} {$this->response->getReasonPhrase()}";
    }

    public function getBody(): array
    {
        $body = $this->getResponse();
        if (empty($body) || ($body['code'] != 0 && empty($body['data']))) {
            Log::info("支付返回异常");
            Log::info($this->getRawResponse());
        }

        return $body;
    }

    public function getCode()
    {
        return $this->getBody()['code'] ?? $this->getResponse()['code'] ?? '';
    }

    /**
     * 抛出异常（如有）
     * @throws PayException
     */
    public function exception()
    {
        if (!$this->isSuccessful()) {
            throw new PayException($this->getMsg(), $this->getCode());
        }
        return false;
    }
}
