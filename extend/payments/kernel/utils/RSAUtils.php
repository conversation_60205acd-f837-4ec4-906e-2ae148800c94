<?php
namespace payments\kernel\utils;

class RSAUtils
{

    public $pubKey;
    public $privKey;
    public $expPubKey;

    function __construct($privKey, $pubKey, $expPubKey='')
    {
        $this->privKey = file_get_contents($privKey);
        $this->pubKey = file_get_contents($pubKey);
        $expPubKey && $this->expPubKey = file_get_contents($expPubKey);
        //商户加密私钥
//        $this->privKey = file_get_contents(storage_path('app/newpay/11000003575_PublicKey_10.pem'));
//        //商户加密公钥
//        $this->pubKey = file_get_contents(storage_path('app/newpay/11000003575_PublicKey_10.pem'));
//        //快捷类新生公钥
//        $this->expPubKey = file_get_contents(storage_path('app/newpay/HnapayExpPublicKey.pem'));
    }

    /**
     * 生成签名
     * @param $signParam
     * @param $private_key_path
     * @return string
     * @throws \Exception
     */
    public function sign($signParam,$private_key_path)
    {
        if(!empty($private_key_path)){
            $priKey = file_get_contents($private_key_path);
        }else{
            $priKey = $this->privKey;
        }
        $priKey = $this->formatPriKey($priKey);
        if (!$priKey) {
            throw new \Exception("Failed to read private key file！");
        }
        if (!openssl_pkey_get_private($priKey)) {
            throw new \Exception("Private key is not available");
        }
        $signature = "";
        $res = openssl_get_privatekey($priKey);
        openssl_sign($signParam, $signature, $res, OPENSSL_ALGO_SHA1);
        openssl_free_key($res);
        return base64_encode($signature);
    }

    /**
     * 公钥加密
     * @param $data
     * @param $public_key_path
     * @return string
     * @throws \Exception
     */
    public function encrypt($data,$public_key_path)
    {
        if(!empty($public_key_path)){
            $pubKey = file_get_contents($public_key_path);
        }else{
            $pubKey = $this->expPubKey;
        }
        $pubKey = $this->formatPubKey($pubKey);
        $encrypted = "";
        if (!$pubKey) {
            throw new \Exception("Failed to read public key file！");
        }
        if (!openssl_pkey_get_public($pubKey)) {
            throw new \Exception("Public key is not available");
        }

        foreach (str_split($data, 117) as $chunk) {
            openssl_public_encrypt($chunk, $encryptData, $pubKey);
            $encrypted .= $encryptData;
        }
        if (!$encrypted)
            throw new \Exception('Unable to encrypt data.');
        return base64_encode($encrypted);
    }

    /**
     * 验证签名：
     * @param $signParam 原文
     * @param $signature 签名
     * @param $public_key_path
     * @return bool
     */
    public function verity($signParam, $signature,$public_key_path)
    {
        if(!empty($public_key_path)){
            $pubKey = file_get_contents($public_key_path);
        }else{
            $pubKey = $this->expPubKey;
        }
        $pubKey = $this->formatPubKey($pubKey);
        $res = openssl_get_publickey($pubKey);
        $result = openssl_verify($signParam, base64_decode($signature), $res);
        openssl_free_key($res);
        if ($result) {
            return true;
        } else {
            return false;
        }
    }

    public function formatPubKey($key){
        $key = str_replace("-----BEGIN PUBLIC KEY-----",'',$key);
        $key = str_replace("-----END PUBLIC KEY-----",'',$key);
        $key = str_replace("\n",'',$key);

        $key = "-----BEGIN PUBLIC KEY-----\n" .
            wordwrap($key, 64, "\n", true) .
            "\n-----END PUBLIC KEY-----";
        return $key;
    }

    public function formatPriKey($key){
        $key = str_replace("-----BEGIN RSA PRIVATE KEY-----",'',$key);
        $key = str_replace("-----END RSA PRIVATE KEY-----",'',$key);
        $key = str_replace("\n",'',$key);

        $key = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($key, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";

        return $key;
    }

    /**
     * 私钥解密
     * @param $encryptData
     * @param $private_key_path
     * @return string
     * @throws \Exception
     */
    public function decrypt($encryptData,$private_key_path)
    {
        if(!empty($private_key_path)){
            $priKey = file_get_contents($private_key_path);
        }else{
            $priKey = $this->privKey;
        }

        if (!$priKey) {
            throw new \Exception("Failed to read private key file！");
        }
        if (!openssl_pkey_get_private($priKey)) {
            throw new \Exception("Private key is not available");
        }
        $decrypted = '';
        foreach (str_split(base64_decode($encryptData), 128) as $chunk) {
            openssl_private_decrypt($chunk, $decryptData, $priKey);
            $decrypted .= $decryptData;
        }
        if (!$decrypted)
            throw new \Exception('Unable to decrypt data.');
        return $decrypted;
    }
}
