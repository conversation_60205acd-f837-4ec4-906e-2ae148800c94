<?php
namespace payments\kernel;



use think\helper\Str;

/**
 * 请求方法基类
 * Class APIRequest
 */
class APIRequest
{
    /**
     * @var string  请求方式，post|get
     */
    protected $method = 'post';

    /**
     * @var string 请求地址
     */
    protected $serverHost = '';

    /**
     * @var array 请求报文
     */
    protected $body = [];

    private $signature = '';
    private $headers = [];

    /**
     * @var bool 是否需要签名
     */
    protected $isSign = true;

    public function __construct(array $arguments = [])
    {
        $this->body = $arguments;

        // 如没设置path，则通过堆栈拿关联信息动态生成path
        if (empty($this->path) && false) {
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 2);
            $paths = [];
            if (count($backtrace) >= 2) {
                $namespaces = explode('\\', get_class($backtrace[1]['object']));
                krsort($namespaces);
                $paths[] = strtolower(array_values($namespaces)[1]);
                $paths[] = Utils::uncamelize($backtrace[1]['args'][0],'-');
            }
            $this->path = implode('/', $paths);
        }
    }

    /**
     * @return bool
     */
    public function isSign(): bool
    {
        return $this->isSign;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return $this->body;
    }

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * @return string
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * @return string
     */
    public function setHeaders(array $header = [])
    {
        $this->headers = $header;
    }

    /**
     * @param string $val
     */
    public function setServerHost(string $val)
    {
        $this->serverHost = $val;
    }

    /**
     * @return string
     */
    public function getServerHost(): string
    {
        return $this->serverHost;
    }

    /**
     * 签名
     * @param string $sign
     */
    public function setSignature(string $sign)
    {
        $this->signature = $sign;
    }

    /**
     * 获取签名
     * @return string
     */
    public function getSignature(): string
    {
        return $this->signature;
    }

    /**
     * 魔术方法，获取数据对象的值
     * @access public
     * @param string $name 名称
     * @return mixed
     */
    public function __get(string $name)
    {
        return call_user_func([$this, 'get' . Str::studly($name)]);
    }

    /**
     * @return string
     */
    public function __set(string $name, $val)
    {
        return $this->$name = $val;
    }

    /**
     * 魔术方法，检测数据对象的值
     * @access public
     * @param string $name 名称
     * @return bool
     */
    public function __isset(string $name): bool
    {
        return method_exists($this, 'get' . Str::studly($name));
    }

    /**
     * 魔术方法，销毁数据对象的值
     * @access public
     * @param string $name 名称
     * @return void
     */
    public function __unset(string $name): void
    {
        call_user_func([$this, 'set' . Str::studly($name)], null);
    }
}
