<?php

/**
 * 新生支付工具类
 */
namespace payments\Services\newProtocolPay;


use payments\kernel\utils\RSAUtils;
use payments\Services\newProtocolPay\ExpConstant;

class ExpUtils {

    /**
     * 生成签名
     * @param $tranCode
     * @param $params
     * @param $private_key_path
     * @return string
     * @throws \Exception
     */
    public static function sign($tranCode, $params,$pemPath){
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }
        $signParam = self::getStringData($expCode['signField'], $params);
        $resUtils = new RSAUtils($pemPath['private_key_path'],$pemPath['public_key_path'],$pemPath['hnapay_exp_publicKey']);
        return $resUtils->sign($signParam,$pemPath['private_key_path']);
    }

    /**
     * 验证签名
     * @param $tranCode
     * @param $params
     * @param $signature
     * @param $pemPath
     * @return bool
     * @throws \Exception
     */
    public static function verify($tranCode, $params, $signature,$pemPath){
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }
        $signParam = self::getStringData($expCode['verifyField'], $params);
        $resUtils = new RSAUtils($pemPath['private_key_path'],$pemPath['public_key_path'],$pemPath['hnapay_exp_publicKey']);
        return $resUtils->verity($signParam, $signature,$pemPath['hnapay_exp_publicKey']);
    }

    public static function notifyVerify($tranCode, $params, $signature,$pemPath){
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }
        if (empty($expCode['notifyVerifyField'])) return true;
        $signParam = self::getStringData($expCode['notifyVerifyField'] ?? $expCode['verifyField'], $params);
        $resUtils = new RSAUtils($pemPath['private_key_path'],$pemPath['public_key_path'],$pemPath['hnapay_exp_publicKey']);
        return $resUtils->verity($signParam, $signature,$pemPath['hnapay_exp_publicKey']);
    }

    /**
     * @param $tranCode
     * @param $params
     * @param $public_key_path
     * @return string
     * @throws \Exception
     */
    public static function encrypt($tranCode,$params,$pemPath){
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }

        $encryptData = self::getJsonData($expCode['encryptField'], $params);

        $resUtils = new RSAUtils($pemPath['private_key_path'],$pemPath['public_key_path'],$pemPath['hnapay_exp_publicKey']);
        return $resUtils->encrypt($encryptData,$pemPath['hnapay_exp_publicKey']);
    }

    /**
     * 私钥解密, 本demo中暂时用不到
     * @param $tranCode
     * @param $decryptData
     * @return string
     * @throws \Exception
     */
    public static function decrypt($tranCode,$decryptData){
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }
        $resUtils = new RSAUtils();
        return $resUtils->decrypt($decryptData);
    }

    /**
     * @param $data
     * @param $params
     * @return string
     * @throws \Exception
     */
    private static function getStringData($data, $params) {
        $fieldString = "";
        foreach($data as $field){
            if (!isset($params[$field])){
                throw new \Exception("参数无效！".$field);
                break;
            }
            $fieldString .= $field . "=[" . (is_array($params[$field]) ? json_encode($params[$field],JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE): $params[$field]) . "]";
        }
        return $fieldString;
    }

    /**
     * @param $data
     * @param $params
     * @return string
     * @throws \Exception
     */
    private static function getJsonData($data, $params) {
        $fieldString = [];
        foreach($data as $field){
            if (!isset($params[$field])){
                throw new \Exception($field."参数无效！");
                break;
            }
            $fieldString[$field] = $params[$field];
        }
        return json_encode($fieldString);
    }

    public static function checkPostParam($data){
        $tranCode = $data['tranCode'];
        if(!$tranCode){
            throw new \Exception("TranCode coding error！");
        }
        $expCode = ExpConstant::getExpParamByTranCode($tranCode);
        if (empty($expCode)){
            throw new \Exception("TranCode coding error！");
        }
        $flag = 1;
        foreach ($expCode['submitField'] as $index){
            if(!isset($data[$index])){
                $flag = 0;
                break;
            }
        }
        return $flag;
    }

    /**
     * POST 方式请求地址
     * @param $url
     * @param $data
     * @param bool $ssl
     * @return bool|mixed
     * @throws \Exception
     */
    public static function post($url, $data, $ssl = FALSE) {
        if(empty($data) || !self::checkPostParam($data)){
            throw new \Exception("Invalid transfer parameter！");
        }
        //模拟提交数据函数
        $curl = curl_init(); // 启动一个CURL会话
        curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
        if ($ssl) {
            //curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
            //curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2); // 从证书中检查SSL加密算法是否存在
            //curl_setopt($curl, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1);
        }
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
//        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
        curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // Post提交的数据包
        curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
        curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
        $tmpInfo = curl_exec($curl); // 执行操作
        if (curl_errno($curl)) {
            return FALSE;
        }
        curl_close($curl); // 关闭CURL会话
        return $tmpInfo; // 返回数据
    }

    /**
     * 通curl发送post请求
     * @param array $param
     * @param $postStr
     * @return mixed
     */
    public static function doCurlPost(array $param, $postStr, $url = '') {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 7); //Timeout after 7 seconds
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //Return data instead printing directly in Browser
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded; charset=UTF-8', 'Connection: Keep-Alive'));
        curl_setopt($curl, CURLOPT_POST, count($param) + 1); //number of parameters sent
        curl_setopt($curl, CURLOPT_POSTFIELDS, $postStr); //parameters data
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }



}
