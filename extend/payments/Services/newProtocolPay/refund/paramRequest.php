<?php


namespace payments\Services\newProtocolPay\refund;


use payments\kernel\APIRequest;
use payments\Services\newProtocolPay\Baseparam;

class paramRequest extends APIRequest
{
    private $baseApp;
    private $requestParam = [
        'version'   => '2.0',
        'tranCode'  => 'EXP09',
        'merId'     => '',
        'charset'   => '1',
        'signType'  => '1',
        'signValue' => '',
        'msgCiphertext'=> '',
        'merAttach' => '',
        'merOrderId'=> '',
        'submitTime'=> 'YmdHis',
        'orgMerOrderId'=> '原商户支付订单号',
        'orgSubmitTime' => '原订单支付下单请求时间(YYYYMMDDHHMMSS)',
        'orderAmt'=>'原订单金额',
        'refundOrderAmt'  => '退款金额',
        'notifyUrl'  => '商户异步通知地址'
    ];

    /**
     * paramRequest constructor.
     * @param array $arguments [merOrderId退款订单号,submitTime请求提交时间,merAttach附加数据（可空）,orgMerOrderId原商户支付订单号，orgSubmitTime原订单支付下单请求时间(YYYYMMDDHHMMSS),notifyUrl异步通知地址,orderAmt原订单金额,refundOrderAmt退款金额]
     */
    public function __construct(array $arguments = [])
    {
        $this->requestParam = (array_merge($this->requestParam,$arguments));
        parent::__construct($this->requestParam);
        $this->baseApp = new Baseparam($this->requestParam);
        $this->setParams($this->requestParam);
    }

    public function setParams($params){
        $payChannel = $params['pay_channel'] ?? 'wechat';
        $payConfig = $this->baseApp->getPayConfig();
        $typeConfig = $payConfig[$payChannel] ?? $payConfig['wechat'];
        $typeConfig['merId'] = $payConfig['info']['partnerID'] ?? '';
        $params['notifyServerUrl'] = $params['notifyUrl'] ?? '';
        $params = array_merge($params,$typeConfig);
        $this->baseApp->setParams($params);
        $this->setServerHost($this->getApi($payChannel));
        $params['msgCiphertext'] =$this->baseApp->createCipher();
        $params['signValue'] = $this->baseApp->makeSign();
        $this->__set('body',$params);
    }

    /**
     * 根据渠道获取发起支付地址
     * @param $payChanner
     * @return string
     */
    private function getApi($payChanner){
        $api = '';
        switch (strtolower($payChanner)){
            case 'h5':
                $api = 'https://gateway.hnapay.com/exp/refund.do';
                break;
            case 'wechat':
                $api = 'https://gateway.hnapay.com/exp/refund.do';
                break;
            default:
                break;
        }
        return $api;
    }
}
