<?php


namespace payments\Services\newProtocolPay\query;


use payments\kernel\APIRequest;
use payments\Services\newProtocolPay\Baseparam;

class paramRequest extends APIRequest
{
    private $baseApp;
    private $requestParam = [
        'version'   => '2.0',
        'tranCode'  => 'MUP09',
        'merId'     => '',
        'charset'   => '1',
        'signType'  => '1',
        'signValue' => '',
        'merAttach' => '',
        'merOrderId'=> '',
        'submitTime'=>'',
    ];

    /**
     * paramRequest constructor.
     * @param array $arguments [merOrderId查询的订单号,submitTime订单请求时间（YYYYMMDD）]
     */
    public function __construct(array $arguments = [])
    {
        $this->requestParam = (array_merge($this->requestParam,$arguments));
        parent::__construct($this->requestParam);
        $this->baseApp = new Baseparam($this->requestParam);
        $this->setParams($this->requestParam);
    }

    public function setParams($params){
        $payChannel = $params['pay_channel'] ?? 'wechat';
        $payConfig = $this->baseApp->getPayConfig();
        $typeConfig = $payConfig[$payChannel] ?? $payConfig['wechat'];
        $typeConfig['merchantId'] = $payConfig['info']['partnerID'];
        $typeConfig['merUserId'] = $payConfig['info']['partnerID'] ?? '';
        $typeConfig['merId'] = $payConfig['info']['partnerID'] ?? '';
        $params['notifyServerUrl'] = $params['notifyUrl'] ?? '';
        $params = array_merge($params,$typeConfig);
        $this->setServerHost($this->getApi($payChannel));
        $this->baseApp->setParams($params);
        $params['signValue'] = $this->baseApp->makeSign();
        $this->__set('body',$params);
    }

    /**
     * 根据渠道获取发起支付地址
     * @param $payChanner
     * @return string
     */
    private function getApi($payChanner){
        $api = '';
        switch (strtolower($payChanner)){
            case 'h5':
                $api = 'https://gateway.hnapay.com/multipay/query.do';
                break;
            case 'wechat':
                $api = 'https://gateway.hnapay.com/exp/query.do';
                break;
            default:
                break;
        }
        return $api;
    }
}
