<?php


namespace payments\Services\newProtocolPay;


use payments\kernel\PayException;
use payments\kernel\Response;
use think\facade\Log;

class AppResponse extends Response
{
    private $payConfig;
    public function __construct()
    {
        // 加载配置
        $config = config('newpay');
        $this->payConfig = env('APP_DEBUG') ? $config['dev']: $config['prod'];
    }

    /**
     * 获取返回结果
     * @return array
     */
    public function getBody(): array
    {
        $body = $this->getResponse();
        if (empty($body) || !empty($body['errorCode'])) {
            Log::info("支付返回异常");
            Log::info($this->getRawResponse());
        }

        return $body;
    }

    /**
     * 获取请求参数
     * @return mixed
     */
    public function getRequestData(){
        return $this->requestData;
    }

    /**
     * 第三方交易单号
     * @return array|mixed|null
     */
    public function getTradeNo(){
        return $this->getResponse('hnapayOrderId');
    }

    /**
     * 商户交易单号
     * @return array|mixed|null
     */
    public function getOutTradeNo(){
        return $this->getResponse('merOrderId');
    }


    /**
     * 获取平台交易单号（微信orderId，支付宝tradeNo）
     * @return mixed|string
     */
    public function getPartyTradeNo(){
        $payInfo = $this->getResponse('payInfo');
        return $payInfo['tradeNO'] ?? ($payInfo['orderId'] ?? '');
    }


    /**
     * 判断请求结果是否成功
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this->getResponse('resultCode') === '0000';
    }

    /**
     * 获取错误信息
     * @return array|mixed|null
     */
    public function getErrorMsg(){
        return $this->getResponse('errorMsg');
    }

    /**
     * @return array|mixed|null
     */
    public function getErrorCode()
    {
        return $this->getResponse('errorCode') ?? '';
    }

    /**
     * 获取请求code
     * @return array|mixed|string
     */
    public function getCode()
    {
        return $this->getResponse('resultCode') ?? '0';
    }

    /**
     * 抛出异常（如有）
     * @throws PayException
     */
    public function exception()
    {
        try{
            if (!$this->isSuccessful()) {
                throw new PayException('错误代码：‘'.$this->getErrorCode().'’'.$this->getErrorMsg(), (int)$this->getCode());
            }
            //验签
            $verify = ExpUtils::verify($this->getResponse('tranCode'), $this->getResponse(), $this->getResponse('signValue'),$this->payConfig['protocol']);
            Log::info('返回参数: ');
            Log::info($this->getResponse());
            if(!$verify) {
                throw new PayException('支付请求结果返回验签失败');
            }
        }catch (PayException $e){
            Log::error('新生支付错误, 请联系管理员');
            Log::error($this->requestData);
            Log::error($this->getRawResponse());
            throw new PayException($e->getMessage(),$e->getCode()??-1);
        }

        return false;
    }

    public function notifyException(){
        try{
            if (!$this->isSuccessful()) {
                throw new PayException('错误代码：‘'.$this->getErrorCode().'’'.$this->getErrorMsg(), (int)$this->getCode());
            }
            //验签
            $verify = ExpUtils::notifyVerify($this->getResponse('tranCode'), $this->getResponse(), $this->getResponse('signValue'),$this->payConfig['protocol']);
            if(!$verify) {
                throw new PayException('支付回调验签失败',-1);
            }
        }catch (PayException $e){
            Log::error('新生支付错误, 请联系管理员');
            Log::error($this->requestData);
            Log::error('新生支付错误请求结果：');
            Log::error($this->getResponse());
            throw new PayException($e->getMessage(),$e->getCode()??-1);
        }
    }
}
