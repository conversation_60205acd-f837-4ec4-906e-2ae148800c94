<?php


namespace payments\Services\newProtocolPay\confirmPay;

use payments\kernel\APIRequest;
use payments\Services\newProtocolPay\Baseparam;
use payments\Services\newProtocolPay\ExpUtils;

class paramRequest extends APIRequest
{
    private $baseApp;
    private $requestParam = [
        'version'   => '1.0',
        'tranCode'  => 'ITA10',
        'merId'     => '',
        'merOrderId'=> '',
        'submitTime'=>'',
        'msgCiphertext'=> '',
        'signType'  => '1',
        'remark'    => '',
        'merAttach' => '',
        'charset'   => '1',
        'signValue' => '',
        'riskExpand'=> '',
        'expirTime' => '',
        'orgCode'=>'WECHATPAY',
        'payLimit'  => '',
        'aliAppId'  => '',
        'buyerLogonId'=> '',
        'buyerId'   => '',
        'merchantId'=> '2106221140012945609',
        'holderName'=> '',
        'identityType'=> '',
        'identityCode'=> '',
        'minAge'    => '',
    ];

    /**
     * paramRequest constructor.
     * @param array $arguments [appId,openId,tranAmt金额,payType付款方式,merUserId用户id,orderExpireTime订单过期时长,frontUrl前台通知地址,notifyUrl异步通知地址,goodsInfo商品信息,orderSubject订单标题,orderDesc订单描述,merUserIp交易ip]
     */
    public function __construct(array $arguments = [])
    {
        $this->requestParam = (array_merge($this->requestParam,$arguments));
        parent::__construct($this->requestParam);
        $this->baseApp = new Baseparam($this->requestParam);
        $this->setParams($this->requestParam);
    }

    public function setParams($params){
        $payChannel = $params['pay_channel'] ?? 'wechat';
        $payConfig = $this->baseApp->getPayConfig();
        $typeConfig = $payConfig[$payChannel] ?? $payConfig['wechat'];
        $typeConfig['merchantId'] = $payConfig['info']['subMerchantId'];
        $typeConfig['merUserId'] = $payConfig['info']['partnerID'] ?? '';
        $typeConfig['merId'] = $payConfig['info']['partnerID'] ?? '';
        $params['notifyServerUrl'] = $params['notifyUrl'] ?? '';
        $params = array_merge($params,$typeConfig);
        $this->baseApp->setParams($params);
        $this->setServerHost($this->getApi($payChannel));
        $params['msgCiphertext'] =$this->baseApp->createCipher();
        $params['signValue'] = $this->baseApp->makeSign();
        $this->__set('body',$params);
//        $res = ExpUtils::post($this->getApi('wechat'),$params);
//        dd($this->getApi('wechat'),'请求参数：',$params,'请求结果：',json_decode($res,true));

    }

    /**
     * 根据渠道获取发起支付地址
     * @param $payChanner
     * @return string
     */
    private function getApi($payChanner){
        $api = '';
        switch (strtolower($payChanner)){
            case 'h5':
                $api = 'https://gateway.hnapay.com/multipay/h5.do';
                break;
            case 'wechat':
                $api = 'https://gateway.hnapay.com/ita/inCharge.do';
                break;
            default:
                break;
        }
        return $api;
    }
}
