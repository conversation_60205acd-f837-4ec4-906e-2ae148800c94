<?php


namespace payments\Services\newProtocolPay\callback;


use payments\kernel\CallbackResponse;
use payments\kernel\PayException;
use payments\Services\newProtocolPay\AppResponse;

class application extends AppResponse
{
    public function __construct()
    {

        parent::__construct();
        $post_data = input();
        $this->setResponse($post_data);
        if (empty($post_data)) throw new PayException('错误的请求数据',-1);

        $this->notifyException();
        return new CallbackResponse($post_data);
    }

    /**
     * 返回码
     * @return string[]
     */
    public function returnCode(){
        return ['success'=>'200','fail'=>'fail'];
    }

}
