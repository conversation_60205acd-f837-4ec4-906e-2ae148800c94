<?php


namespace payments\Services\newProtocolPay;


use payments\Services\newProtocolPay\ExpUtils;

class Baseparam
{
    private $payConfig;
    private $params;
    private $defaultConfig = [
        'notifyUrl' => ''
    ];

    public function __construct($param)
    {
        // 加载配置
        $config = config('newpay');
        $this->payConfig = env('APP_DEBUG') ? $config['dev']: $config['prod'];
        $this->params = $param;
    }

    /**
     * @return \Illuminate\Config\Repository|\Illuminate\Contracts\Foundation\Application|mixed
     */
    public function getPayConfig()
    {
        return $this->payConfig;
    }

    /**
     * 生成签名
     * @return string
     * @throws \Exception
     */
    public function makeSign(){
        $this->params['signValue'] = ExpUtils::sign($this->params['tranCode'], $this->params,$this->payConfig['protocol']);
        return $this->params['signValue'];
    }

    /**
     * 生成密文
     * @return mixed
     */
    public function createCipher()
    {
        $this->params['msgCiphertext'] = ExpUtils::encrypt($this->params['tranCode'], $this->params,$this->payConfig['protocol']);
        return $this->params['msgCiphertext'];
    }

    /**
     * @return mixed
     */
    public function setParams($param)
    {
        $this->params = $param;
    }
}
