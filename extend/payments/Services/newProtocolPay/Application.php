<?php


namespace payments\Services\newProtocolPay;


use payments\kernel\ServiceContainer;

/**
 * 新生支付应用
 * Class Application
 * @package payments\Service\NewProtocolPay
 * @method static \payments\Services\newProtocolPay\confirmPay\paramRequest confirmPay() // 支付
 * @method static \payments\Services\newProtocolPay\refund\paramRequest refund()        // 退款
 * @method static \payments\Services\newProtocolPay\query\paramRequest query()          // 查询订单
 * @method static \payments\Services\newProtocolPay\callback\application callback()     // 回调
 */
class Application extends ServiceContainer
{

}
