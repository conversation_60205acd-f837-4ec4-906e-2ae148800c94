<?php
/**
 * 新生签名model.
 */
namespace payments\Services\newProtocolPay;

class ExpConstant
{
    //新生签名使用的算法
    const ALGORITHM = "RSA";

    const VERSION = "version";
    const TRANCODE = "tranCode";
    const MERID = "merId";
    const MERORDERID = "merOrderId";
    const SUBMITTIME = "submitTime";
    const MSGCIPHERTEXT = "msgCiphertext";
    const SIGNTYPE = "signType";
    const SIGNVALUE = "signValue";
    const MERATTACH = "merAttach";
    const CHARSET = "charset";
    const TRANAMT = "tranAmt";
    const PAYTYPE = "payType";
    const CARDTYPE = "cardType";
    const BANKCODE = "bankCode";
    const CARDNO = "cardNo";
    const BIZPROTOCOLNO = "bizProtocolNo";
    const PAYPROTOCOLNO = "payProtocolNo";
    const FRONTURL = "frontUrl";
    const NOTIFYURL = "notifyUrl";
    const ORDEREXPIRETIME = "orderExpireTime";
    const MERUSERIP = "merUserIp";
    const RISKEXPAND = "riskExpand";
    const GOODSINFO = "goodsInfo";
    const HNAPAYORDERID = "hnapayOrderId";//新生订单号
    const HOLDNAME = "holderName";//持卡人姓名
    const CARDAVAILABLEDATE = "cardAvailableDate";//信用卡有效期
    const CVV2 = "cvv2";//信用卡CVV2
    const MOBILENO = "mobileNo";//银行签约手机号
    const IDENTITYTYPE = "identityType";//证件类型
    const IDENTITYCODE = "identityCode";//证件号码
    const MERUSERID = "merUserId";//商户用户ID
    const PAYFACTORS = "payFactors";//支付要素
    const RESULTCODE="resultCode";//处理结果码
    const ERRORCODE="errorCode";//异常代码
    const ORGMERORDERID = "orgMerOrderId";  // 原商户支付订单号
    const ORGSUBMITTIME = "orgSubmitTime";  // 原订单支付下单请求时间
    const ORDERAMT = "orderAmt";            // 原订单金额
    const REFUNDORDERAMT = "refundOrderAmt";// 退款金额
    const SMSCODE = "smsCode";//签约短信验证码

    //以下为签名字段及后台通知时的验签字段
    const SIGN_FIELD = array("version", "tranCode", "merId", "merOrderId", "submitTime", "msgCiphertext");
    const H5_SIGN_FIELD = array("version", "tranCode", "merId", "merOrderId", "submitTime", "signType", "charset", "msgCiphertext");
    const SUBMIT_FIELD = array("version", "tranCode", "merId", "merOrderId", "submitTime", "msgCiphertext", "signType", "signValue", "merAttach", "charset");

    const EXP01 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("payType", "cardType", "bankCode", "cardNo", "merUserId", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "payFactors")
    );
    const EXP02 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "cardNo", "holderName", "cardAvailableDate", "cvv2", "mobileNo", "identityType", "identityCode", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId")
    );
    const EXP03 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "smsCode", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "bizProtocolNo", "payProtocolNo", "bankCode", "cardType", "shortCardNo")
    );
    const EXP04 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("bizProtocolNo", "payProtocolNo", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode")
    );
    const EXP05 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("tranAmt", "payType", "cardType", "bankCode", "cardNo", "bizProtocolNo", "payProtocolNo", "frontUrl", "notifyUrl", "orderExpireTime", "merUserIp", "riskExpand", "goodsInfo"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "payFactors")
    );
    const EXP06 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "cardNo", "holderName", "cardAvailableDate", "cvv2", "mobileNo", "identityType", "identityCode", "merUserId", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId")
    );
    const EXP07 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "smsCode", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "bizProtocolNo", "payProtocolNo", "tranAmt", "checkDate", "bankCode", "cardType", "shortCardNo")
    );
    // 微信支付查询
    const EXP08 = array(
        "signField" => array("version", "tranCode", "merId", "merOrderId", "submitTime"),
        "submitField" => array("version", "tranCode", "merId", "merOrderId", "submitTime", "signType", "signValue", "merAttach", "charset"),
        "encryptField" => array(),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "tranAmt", "refundAmt", "orderStatus")
    );
    // H5，微信退款
    const EXP09 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("orgMerOrderId", "orgSubmitTime", "orderAmt", "refundOrderAmt", "notifyUrl"),
//        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "merAttach", "charset", "signType", "resultCode", "errorCode", "errorMsg", "hnapayOrderId", "orgMerOrderId", "tranAmt", "refundAmt", "orderStatus","signValue")
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "orgMerOrderId", "tranAmt", "refundAmt", "orderStatus"),
        "notifyVerifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "hnapayOrderId", "orgMerOrderId", "tranAmt", "refundAmt", "orderStatus"),
    );
    const EXP10 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("cardNo", "holderName", "cardAvailableDate", "cvv2", "mobileNo", "identityType", "identityCode", "merUserId" ,"merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId")
    );
    const EXP11 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "smsCode", "merUserIp"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "bizProtocolNo", "payProtocolNo", "bankCode", "cardType", "shortCardNo")
    );
    const EXP12 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("tranAmt", "payType", "cardNo", "holderName", "cardAvailableDate", "cvv2", "mobileNo", "identityType", "identityCode", "bizProtocolNo", "payProtocolNo", "frontUrl", "notifyUrl", "orderExpireTime", "merUserId",'merUserIp','riskExpand','goodsInfo', 'subMerchantId'),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "submitTime")
    );
    const EXP13 = array(
        "signField" => self::SIGN_FIELD,
        "submitField" => self::SUBMIT_FIELD,
        "encryptField" => array("hnapayOrderId", "smsCode", "merUserIp","paymentTerminalInfo","receiverTerminalInfo","deviceInfo"),
        "verifyField" => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "bizProtocolNo", "payProtocolNo", "tranAmt", "checkDate", "bankCode", "cardType", "shortCardNo")
    );

    // 微信支付
    const ITA10 = array(
        "signField"         => self::SIGN_FIELD,
        "submitField"       => self::SUBMIT_FIELD,
        "encryptField"      => array("tranAmt", "orgCode", "notifyServerUrl", "merUserIp", "expirTime", 'riskExpand', 'goodsInfo', "orderSubject", "orderDesc", "payLimit", "appId", "openId", "aliAppId", "buyerLogonId", "buyerId", "merchantId", "holderName", "identityType", "identityCode", "minAge"),
        "verifyField"       => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "payInfo"),
        'notifyVerifyField' => array("version","tranCode","merOrderId","merId","merAttach","charset","signType","hnapayOrderId","resultCode","tranAmt","submitTime","tranFinishTime")
    );
    // H5支付
    const MUP11 = array(
        "signField"         => self::H5_SIGN_FIELD,
        "submitField"       => self::SUBMIT_FIELD,
        "encryptField"      => array("tranAmt", "payType", "exPayMode", "cardNo", "holderName", "identityCode", "bizProtocolNo", "payProtocolNo", "merUserId", "orderExpireTime", "frontUrl", "notifyUrl",'riskExpand','goodsInfo', 'orderSubject', 'orderDesc', 'merchantId', 'subMerchantId','bizProtocolNo','payProtocolNo','merUserIp','payLimit'),
        "verifyField"       => array("version", "tranCode", "merOrderId", "merId", "charset", "signType", "resultCode", "errorCode", "hnapayOrderId", "bizProtocolNo", "payProtocolNo", "tranAmt", "checkDate", "bankCode", "cardType", "shortCardNo"),
        "notifyVerifyField" => array("version","tranCode","merOrderId","merId","charset","signType","resultCode","hnapayOrderId")
    );
    // H5查询
    const MUP09 = array(
        "signField"         => ['version','tranCode','merId','merOrderId','submitTime'],
        "submitField"       => ['version','tranCode','merId','merOrderId','submitTime','signType','signValue','merAttach','charset'],
        "encryptField"      => [],
        "verifyField"       => ['version','tranCode','merOrderId','merId','charset','signType','resultCode','errorCode','hnapayOrderId','tranAmt','refundAmt','orderStatus']
    );

    public static function getExpParamByTranCode($tranCode){
        return constant('self::' . $tranCode);
    }


}

