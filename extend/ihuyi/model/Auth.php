<?php


namespace extend\ihuyi\model;


use think\facade\Config;
use think\facade\Log;

class Auth
{
    public function checkIdCard($name, $id_card_no, $mobile)
    {
        try
        {
            $config = config('ihuyi');
            $account = $config['APPID'];
            $password = $config['APPKEY'];
            $app = 'oper3m';//固定值
            $time = time();

            $name = md5($name);
            $id_card_no = md5($id_card_no);
            $mobile = md5($mobile);
            $password = md5($account.$password.$app.$name.$id_card_no.$mobile.$time);
            $target = "https://api.ihuyi.com/idcard/Submit.json";
            $post_data = "account=".$account."&password=".$password."&app=".$app."&name=".$name."&id_card_no=".$id_card_no."&mobile=".$mobile."&time=".$time;

            $res =  $this->Post($post_data, $target);
            $res_json = json_decode($res,true);
            if($res_json['code']!=2){
                throw new \Exception("认证信息提交失败:".$res);
            }
            if($res_json['result']['status']==2){
                return true;
            }
            else
                return false;
        }
        catch (\Exception $e)
        {
            Log::error("认证异常");
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return false;
        }
    }

    public function Post($curlPost,$url){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_NOBODY, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $curlPost);
        $return_str = curl_exec($curl);
        curl_close($curl);
        return $return_str;
    }
}