<?php

namespace extend;

use think\facade\Db as dbName;

/**
 * 手机短信类
 * 广州享迅信息科技有限公司 接口文档
 * http://www.17int.cn/web/API_document.html
 * Class Sms
 * <AUTHOR> cnn
 * @date : 2020.09.08
 */
class Sms
{
    /*------------- 短信配置  -------------*/
    private $sms = [
        'http' => 'http://www.17int.cn/xxsmsweb/smsapi/send.json',//官方API
        'account' => 's11030053',//用户账号
        'password' => 'G46S5eR6f#t9UKkQ',//密码
        'prefit' => '【先迈网】',
        'expire' => 2, //分钟内有效
        'cnt' => 3, // 最多次数
        'reset' => 10, // 重置的时间差（分钟）
        'type' => [1 => '注册', '修改密码', '短信登录', '安全验证', '修改用户账号'], // 事件类型
    ];

    /**
     * 获取短信验证码
     * @param int $mobile 手机号
     * @param int $type 类型  1：注册；2：找回密码；3：直接登录; 4：绑定手机
     * @param bool $sendsms 是否发送信，为否时则返回验证码
     * @param bool $use_new_sms 是否使用新的短信发送系统，默认为false使用旧系统
     */
    public function sms_rqcode($mobile, $type, $sendsms = true, $use_new_sms = false)
    {

        $nowTime = $_SERVER['REQUEST_TIME'];
        // 验证码
        /* if ( ! captcha_check( input( config('captcha.id') ) ) )
        {
            return ['code'=>-1, 'msg'=>'验证码错误'];
        } */

        if (!preg_match('/^1\d{10}$/', $mobile) || !$type) return ['code' => -1, 'msg' => '手机格式错误'];
        $Verify = dbName::name('verify_code');
        $map = ['mobile' => $mobile, 'type' => $type];
        $data = $Verify->where($map)->find();
        $smsConf = $this->sms;
        //还没有记录
        if (!$data) {
            $code = mt_rand(1000, 9999);
        } else {
            $inter = $nowTime - $data['instime'];
            //隔了n个小时
            if ($inter > $smsConf['reset'] * 60) {
                $code = mt_rand(1000, 9999);
                $data['cnt'] = 0;
            } //次数还没达到限制
            elseif ($data['cnt'] < $smsConf['cnt']) {
                //时间还在有效期内
                if ($inter <= $smsConf['expire'] * 60) {
                    $code = $data['code'];
                } else {
                    $code = mt_rand(1000, 9999);
                }
            }
        }

        if (empty($code)) return ['code' => -1, 'msg' => '发送过于频繁,系统繁忙，请稍候再试']; // 抱歉，系统繁忙，请稍候再试
        
        // 根据sendsms决定是否执行发送短信
        if ($sendsms) {
            if ($use_new_sms) {
                // 使用新的短信发送系统
                $sms = new \app\model\sms\Sms();
                $keywords = 'VERIFICATION_CODE'; // 使用统一的验证码模板关键字
                $params = [
                    $code, // 验证码参数
                ];
                $send_res = $sms->send($keywords, $mobile, $params);
                $res = $send_res && isset($send_res['code']) && $send_res['code'] >= 0 ? 
                    ['errorCode' => 'ALLSuccess'] : 
                    ['errorCode' => 'SendFailed', 'message' => $send_res['message'] ?? '发送失败'];
            } else {
                // 使用旧的短信发送系统
                $res = $this->sms_send($mobile, lang('hello_sms_code_is') . "：{$code}，" . lang('please_zai') . $smsConf['expire'] . lang('input_in_minute'));
            }
        } else {
            $res = ['errorCode' => 'ALLSuccess']; // 不发送短信时视为成功
        }

        //发送失败
        if ($res['errorCode'] != 'ALLSuccess') {
            return ['code' => -1, 'msg' => $res['message'] ?? lang('operat_limit_please') . $this->sms['reset'] . lang('minute_retry')];
        }

        $saveData = [
            'code' => $code,
            'instime' => $nowTime,
            'cnt' => isset($data['cnt']) ? ++$data['cnt'] : 1
        ];
        if ($data) {
            $Verify->where($map)->update($saveData);
        } else {
            $Verify->insert(array_merge($map, $saveData));
        }
        //发送成功
        return ['code' => 0, 'msg' => lang('verify_code_sended'), 'sms' => $code];

    }

    /**
     * 验证短信验证码
     * @param $mobile
     * @param $code
     * @param $type
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sms_verify($mobile, $code, $type)
    {
        $nowTime = $_SERVER['REQUEST_TIME'];

        $data = dbName::name('verify_code')->where(['mobile' => $mobile, 'type' => $type])->find();
        // 验证码在有效期内
        return !empty($data['cnt']) && $data['code'] == $code && $nowTime - $data['instime'] < $this->sms['expire'] * 60;
    }

    /**
     * curl发送短信
     * @param $mobile 手机号 支持多个号码 ********,*********,**********
     * @param $content 发送的内容
     * @return mixed
     * ALLSuccess 所有成功:["requestId" => "**********","status" => "10","batchId" => "1075","errorCode" => "ALLSuccess"]
     * AllFailed 所有失败:["requestId" => "**********","status" => "20","errorCode" => "AllFailed"]
     * @throws \Exception
     */
    public function sms_send($mobile, $content)
    {
        $nowTime = $_SERVER['REQUEST_TIME'];

        $data = json_encode([
            'account' => $this->sms['account'], //用户帐号，由系统管理员提供
            'password' => strtoupper(md5($this->sms['password'])), //用户帐号对应的密码，由系统管理员提供(32位MD5加密)
            'mobile' => $mobile, //发送的目的号码.多个号码之间用英文半角逗号隔开。 每次请求最多支持500个号码，需要提交更多号码请与平台运维协商。
            'content' => $this->sms['prefit'] . $content, //短信的内容，内容需要UTF-8编码,长短信自动拆分,单条短信最长70个字，超过则按67个字一条拆分
            'requestId' => uniqid($mobile . $nowTime), //唯一的用户请求ID, 服务器不检查唯一性，只作记录
            'extno' => ''
        ]);

        return json_decode(curl($this->sms['http'], $data, 1, ['Content-Type: application/json']), true);
    }
}