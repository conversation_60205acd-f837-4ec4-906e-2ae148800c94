<?php


namespace app\Domain\Services\WeappNotice;


use app\constant\weapp_notice\WEAPP_NOTICE_SCENE;
use app\constant\weapp_notice\WEAPP_NOTICE_SUBSCRIBE_SOURCE;
use app\constant\weapp_notice\WEAPP_NOTICE_SUBSCRIBE_STATUS;
use app\Domain\Models\WeappNotice\WeappNoticeRepository;
use app\model\notice\WeappNoticeModel;
use app\model\notice\WeappNoticeSubscribe;
use app\model\order\PintuanOrderModel;
use app\service\member\MemberService;
use app\service\pintuan\PintuanOrder;
use app\service\wechat\WxAppService;
use think\Exception;
use think\facade\Log;
use app\Exceptions\ModelException;

class SendOpenPintuanNoticeService
{
    protected $noticeRepository;
    public function __construct(WeappNoticeRepository $noticeRepository)
    {
        $this->noticeRepository = $noticeRepository;
    }

    public function execute(int $pinOrderId)
    {
        try
        {

            $pintuanOrder = PintuanOrderModel::find($pinOrderId);
            if(!$pintuanOrder || !$pintuanOrder->belongGroup || !$pintuanOrder->belongMember)
            {
                throw new ModelException("拼团关联数据不存在");
            }
            $openid = (new MemberService())->getMemberOpenid($pintuanOrder->belongMember->member_id);
            if(empty($openid))
            {
                throw new ModelException("openid不存在");
            }
            $member_id = $pintuanOrder->belongMember->member_id;

            $subscribeM = $this->noticeRepository->getMemberSubscribeByNoSend($member_id, WEAPP_NOTICE_SCENE::OPEN_PINTUAN, WEAPP_NOTICE_SUBSCRIBE_SOURCE::PINTUAN_ORDER, $pinOrderId);

            if(!$subscribeM)
                return;

            $pintuan_order_model  = new PintuanOrder();
            $closeTimeLimit = $pintuan_order_model->getPintuanSetTime('pintuan_failed_auto_close_time') * 60;       //剩余的是分钟，需要转换
            $surplusSeconds = strtotime($pintuanOrder->belongGroup->create_time) + $closeTimeLimit - time();
            $surplusTimeFormat = $surplusSeconds >= 0 ? secToTime($surplusSeconds) : secToTime(0);

            $str = "客服：400-000-4801";
            $sku_name = mb_substr($pintuanOrder->sku_name,0,20);

            $data =  [         // 模板内容，格式形如 { "key1": { "value": any }, "key2": { "value": any } }
                'thing1' => [
                    'value' => $sku_name,
                ],
                'amount2' => [
                    'value' => "¥".$pintuanOrder->pay_money,
                ],
                'thing3' => [
                    'value' => $pintuanOrder->belongGroup->pintuan_num,
                ],
                'time5' => [
                    'value' => $surplusTimeFormat
                ],
                'thing6' => [
                    'value' =>$str,
                ],
            ];
            $page = '/promotionpages/pintuan/order/detail/detail?order_id='.$pinOrderId;
            $template_key = WeappNoticeModel::where("scene", WEAPP_NOTICE_SCENE::OPEN_PINTUAN)->value("template_key");
            $wxAppService = new WxAppService();
            $ret = $wxAppService->sendSubMsg($openid, $template_key, $data, $page);
            if($ret['code'] == 0)
            {
                $subscribeM->status = WEAPP_NOTICE_SUBSCRIBE_STATUS::SEND;
                $subscribeM->updated_at = time();
                $subscribeM->save();
            }
            else
            {
                Log::error("消息推送失败：{$ret['message']}");
            }
        }
        catch(\Throwable $e)
        {
            Log::error("消息推送失败：{$e->getMessage()}");
            return;
        }
    }

}