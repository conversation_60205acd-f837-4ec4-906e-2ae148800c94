<?php
declare (strict_types = 1);

namespace app\event;
use app\service\shop\ShopService;
use think\facade\Log;

class ShopTaskEvent {

    public function handle($data)
    {
        $orderId = $data['order_id'] ?? 0;
        if ($orderId > 0) {
            try {
                $service =new ShopService();
                return $service->taskReward($orderId);
            } catch (\Exception $e) {
                Log::error('订单支付成功，店铺任务事件异常, 订单数据data如下：');
                Log::error($data);
                Log::error('异常信息如下:');
                Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            }

        }
    }
}