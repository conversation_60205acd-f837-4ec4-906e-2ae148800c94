<?php


namespace app\model\pintuan;


use app\model\order\PintuanGroupModel;
use app\model\order\PintuanOrderModel;
use Carbon\Carbon;
use think\facade\Db;

class PintuanGoodsStatistics
{
    protected $pintuanId = 0;
    protected $goodsId = 0;
    protected $startTime = '';
    protected $endTime = '';

    public function __construct($pintuanId, $startTime='', $endTime='')
    {
        $this->pintuanId = $pintuanId;
    }

    public function setSearchTime($startTime, $endTime)
    {
        $this->startTime = $startTime;
        $this->endTime = $endTime;
    }

    public function setGoodsId($goodsId)
    {
        $this->goodsId = $goodsId;
    }

    /**
     * 开团数
     * @return int
     */
    public function getGroupNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['goods_id', '=', $this->goodsId];
        $where[] = ['status', '>', 0];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['create_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanGroupModel::where($where)->count();
    }

    /**
     * 参团数
     * @return mixed
     */
    public function joinGroupNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['pay_status', '=', 1];
        $where[] = ['goods_id', '=', $this->goodsId];
        $where[] = ['', 'exp', Db::raw('`head_id` != `member_id`')];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['pay_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanOrderModel::where($where)->count();
    }

    /**
     * 中奖订单数
     * @return int
     */
    public function winOrderNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['pay_status', '=', 1];
        $where[] = ['win_status', '=', 1];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['pay_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanOrderModel::where($where)->count();
    }

    /**
     * 拼团商品订单金额
     * @return float
     */
    public function winOrderMoney()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['pay_status', '=', 1];
        $where[] = ['win_status', '=', 1];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['pay_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanOrderModel::where($where)->sum('pay_money');
    }

    /**
     * 分享次数
     * @return int
     */
    public function shareNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['create_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanShareLog::where($where)->count();
    }

    /**
     * 打开分享次数
     * @return int
     */
    public function openShareNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['create_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanOpenShareLog::where($where)->count();
    }

    /**
     * 分享人数
     * @return int
     */
    public function sharePeopleNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['create_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanShareLog::where($where)->group("member_id")->count();
    }

    /**
     * 打开分享人数
     * @return int
     */
    public function openSharePeopleNums()
    {
        $where[] = ['pintuan_id', '=', $this->pintuanId];
        $where[] = ['goods_id', '=', $this->goodsId];
        if($this->startTime && $this->endTime)
        {
            $where[] = ['create_time', 'between', [$this->startTime, $this->endTime]];
        }
        return PintuanOpenShareLog::where($where)->group("member_id")->count();
    }
}