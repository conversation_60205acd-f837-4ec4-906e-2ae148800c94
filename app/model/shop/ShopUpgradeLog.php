<?php
/**
 * 店铺升级记录
 */

namespace app\model\shop;


use app\model\BaseModel;
use \app\service\shop\ShopService;

class ShopUpgradeLog extends BaseModel
{
    public function getLogList($condition = [], $field = '*', $order = '', $page = 1, $page_size = PAGE_LIST_ROWS)
    {
        $alias = "log";
        $join = [
            [
                'shop s',
                'log.shop_id = s.site_id',
                'inner'
            ],
        ];
        $list = model('shop_upgrade_log')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
        if($list['list']){
            $vip_level = ShopService::getInstance()->vip_level;
            foreach ($list['list'] as $key => $val) {
                foreach ($vip_level as $k => $v) {
                    if ($val['old_vip_level'] == $k) {
                        $list['list'][$key]['old_vip_level_name'] = $v; // 店铺等级对应的名称
                    }
                    if ($val['new_vip_level'] == $k) {
                        $list['list'][$key]['new_vip_level_name'] = $v; // 店铺等级对应的名称
                    }
                }
            }
        }
        return $this->success($list);
    }
}