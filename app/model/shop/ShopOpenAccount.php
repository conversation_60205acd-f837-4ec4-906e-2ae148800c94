<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\shop;

use app\model\BaseModel;

/**
 * 店铺入驻费用
 */
class ShopOpenAccount extends BaseModel
{

	/**
	 * 获取入驻费用列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param string $limit
	 */
	public function getShopOpenAccountList($condition = [], $field = '*', $order = '', $limit = null)
	{
		
		$list = model('shop_open_account')->getList($condition, $field, $order, '', '', '', $limit);
		return $this->success($list);
	}


	/**
	 * 获取入驻费用分页列表
	 * @param array $condition
	 * @param number $page
	 * @param string $page_size
	 * @param string $order
	 * @param string $field
	 */
	public function getShopOpenAccountPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*')
	{
		
		$list = model('shop_open_account')->pageList($condition, $field, $order, $page, $page_size);
		return $this->success($list);
	}
	

}