<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\content;

use app\model\BaseModel;
use app\model\system\Document as DocumentModel;
use think\Exception;


class Config extends BaseModel
{

    /**
     * 获取如何成为店长
     */
    public function getBecomeManagerConfig()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "BECOME_MANAGER" ] ]);
        return $info;
    }

    /**
     * 设置如何成为店长
     * @param string $title
     * @param unknown $content
     */
	public function setBecomeManagerConfig($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "BECOME_MANAGER" ] ]);
        return $res ;
    }


    /**
     * [getOwnerIntroConfig 店主介绍]
     * <AUTHOR>
     * @DateTime 2020-10-13T11:32:49+0800
     * @return   [type]                   [description]
     */
    public function getOwnerIntroConfig()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "OWNER_INTRO" ] ]);
        return $info;
    }

    /**
     * 设置店主介绍
     * @param string $title
     * @param unknown $content
     */
    public function setOwnerIntroConfig($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "OWNER_INTRO" ] ]);
        return $res ;
    }

    /**
     * 获取提现文案
     */
    public function getWithdrawCopyConfig()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_COPYWRITER" ] ]);
        return $info;
    }

    /**
     * 设置提现文案
     * @param string $title
     * @param unknown $content
     */
    public function setWithdrawCopyConfig($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_COPYWRITER" ] ]);
        return $res ;
    }

    /**
     * 获取提现文案-小程序
     */
    public function getWithdrawCopyMiniConfig()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_COPYWRITER_MINI" ] ]);
        return $info;
    }

    /**
     * 设置提现文案-小程序
     * @param string $title
     * @param unknown $content
     */
    public function setWithdrawCopyMiniConfig($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_COPYWRITER_MINI" ] ]);
        return $res ;
    }

    /**
     * 设置分销客申请内容
     * @param string $title
     * @param unknow $content
     * @param array $others
     */
    public function setSaleCustomerApply($title, $content)
    {
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "SALE_CUSTOMER_APPLY" ] ]);
        return $res ;
    }

    /**
     * 获取分销客申请内容
     */
    public function getSaleCustomerApply()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "SALE_CUSTOMER_APPLY" ] ]);
        return $info;
    }

    /**
     * 设置微信地址说明
     * @param string $title
     * @param unknow $content
     * @param array $others
     */
    public function setWechatSiteSpecification($title, $content, $others)
    {
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WECHAT_SITE_SPECIFICATION" ] ], $others);
        return $res ;
    }

    /**
     * 获取微信地址说明
     * @param string $title
     * @param unknow $content
     * @param array $others
     */
    public function getWechatSiteSpecification()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WECHAT_SITE_SPECIFICATION" ] ]);
        return $info;
    }

    /**
     * 获取实名认证提示
     */
    public function getCertificationTips()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "CERTIFICATION_TIPS" ] ]);
        return $info;
    }

    /**
     * 设置实名认证提示
     * @param string $title
     * @param unknown $content
     */
    public function setCertificationTips($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "CERTIFICATION_TIPS" ] ]);
        return $res ;
    }


    /**
     * 获取提现温馨提示
     */
    public function getWithdrawWarmTips()
    {
        $document = new DocumentModel();
        $info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_WARM_TIPS" ] ]);
        return $info;
    }

    /**
     * 设置提现温馨提示
     * @param string $title
     * @param unknown $content
     */
    public function setWithdrawWarmTips($title, $content){
        $document = new DocumentModel();
        $res = $document->setDocument($title, $content, [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "WITHDRAW_WARM_TIPS" ] ]);
        return $res ;
    }

    /**
     * 获取分销客收益说明
     */
    public function getSaleCustomerProfit()
    {
        $document = new DocumentModel();

        //今日收益说明文案
        $today_profit_info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "TODAY_PROFIT_CONFIG" ] ]);

        //已赚收益说明文案
        $earn_profit_info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "EARN_PROFIT_CONFIG" ] ]);

        //预估收益说明文案
        $estimate_profit_info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "ESTIMATE_PROFIT_CONFIG" ] ]);

        //分销客销售统计说明文案
        $sale_stat_info = $document->getDocument([ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "SALE_STAT_CONFIG" ] ]);

        $res = [
            'today_profit_info' => $today_profit_info
            ,'earn_profit_info' => $earn_profit_info
            ,'estimate_profit_info' => $estimate_profit_info
            ,'sale_stat_info' => $sale_stat_info

        ];
        return $res;
    }


    /**
     * 设置分销客收益说明
     * @param array $param
     *
     */
    public function setSaleCustomerProfit($param){
        $document = new DocumentModel();

        model()->startTrans();
        try{
            //今日收益说明文案
            $today_profit = $param['today_profit'];
            $res_today_profit = $document->setDocument($today_profit['title'], $today_profit['content'], [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "TODAY_PROFIT_CONFIG" ] ]);
            if($res_today_profit['code'] != 0) throw new Exception("今日收益说明文案设置失败");

            //已赚收益说明文案
            $earn_profit = $param['earn_profit'];
            $res_earn_profit = $document->setDocument($earn_profit['title'], $earn_profit['content'], [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "EARN_PROFIT_CONFIG" ] ]);
            if($res_earn_profit['code'] != 0) throw new Exception("已赚收益说明文案设置失败");

            //预估收益说明文案
            $estimate_profit = $param['estimate_profit'];
            $res_estimate_profit = $document->setDocument($estimate_profit['title'], $estimate_profit['content'], [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "ESTIMATE_PROFIT_CONFIG" ] ]);
            if($res_estimate_profit['code'] != 0) throw new Exception("预估收益说明文案设置失败");

            //分销客销售统计说明文案
           /* $sale_stat = $param['sale_stat'];
            $res_sale_stat = $document->setDocument($sale_stat['title'], $sale_stat['content'], [ [ 'site_id', '=', 0 ], [ 'app_module', '=', 'admin' ], [ 'document_key', '=', "SALE_STAT_CONFIG" ] ]);
            if($res_sale_stat['code'] != 0) throw new Exception("分销客销售统计说明文案设置失败");*/

            model()->commit();
            return $this->success();
        }catch (\Exception $e){
            model()->rollback();
            return $this->error('',$e->getMessage());
        }

    }

}