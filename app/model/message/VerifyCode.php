<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\message;

use app\model\BaseModel;
use overtrue\EasySms\Strategies\OrderStrategy;

/**
 * 手机短信验证码管理类
 */
class VerifyCode extends BaseModel
{
    public $config = [
        // HTTP 请求的超时时间（秒）
        'timeout' => 5.0,
        // 默认发送配置
        'default' => [
            // 网关调用策略，默认：顺序调用
            'strategy' => OrderStrategy::class,
            'gateways' => [],
        ],
    ];

    /**
     * 手机短信验证码记录分页列表
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @return array
     */
    public function getSmsRecordsPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'instime desc', $field = '*')
    {
        $list = model('verify_code')->pageList($condition, $field, $order, $page, $page_size);
        return $this->success($list);
    }
}