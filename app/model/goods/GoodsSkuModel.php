<?php


namespace app\model\goods;


use think\Model;

class GoodsSkuModel extends Model
{
    protected $table = 'xm_goods_sku';
    protected $pk = 'sku_id';

    public function belongGoods()
    {
        return $this->belongsTo(GoodsModel::class, "goods_id", "goods_id");
    }

    public function calculateSalePrice()
    {
        return round($this->price * (1 + $this->reward_shop_rate / 100), 2);
    }
}