<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\model\goods;

use app\Domain\Models\ManageConfig\DataType\CONFIG_KEY;
use app\Domain\Models\ManageConfig\DataType\CONFIG_TYPE;
use app\Domain\Services\ManageConfig\CategoryPermissionFilterService;
use app\model\shop\Shop;
use think\facade\Cache;
use app\model\BaseModel;
use app\service\init\CommonService;

/**
 * 商品分类
 */
class GoodsCategory extends BaseModel
{
    private $shopId;

    /**
	 * 添加商品分类
	 * @param array $data
	 * @return array
	 */
	public function addCategory($data)
	{
        // 判断当前分类是否关联商品
        if($data['pid']){
            // $condition[] = ['category_id|category_id_1|category_id_2|category_id_3', '=', $data['pid']];
            $condition[] = ['category_id', '=', $data['pid']];
            $count = model('goods')->getCount($condition);
            if($count){
                return error(-1, '无法添加子类，该父类已有关联商品');
            }
        }
		$category_id = model('goods_category')->add($data);
		Cache::tag("goods_category")->clear();
		return $this->success($category_id);
	}

    /**
     * 修改商品分类
     * @param array $data 提交的数据
     * @param array $old_data 编辑前的数据
     * @return array
     */
	public function editCategory($data, $old_data = [])
	{
	    // 如果是1级分类变2级分类
	    if ($old_data && ($old_data['level'] == 1 && $data['level'] == 2)) {

	        // 如果有3级分类，不允许修改
	        $son_level = $this->checkSonLevel($data['category_id']);
	        if ($son_level == 3) {
                return error(-1, '无法修改该分类的等级');
            }

	        // 找到自己的夫类
            $parent_info = $this->getCategoryInfo(['category_id' => $data['pid']]);
	        // 找到自己的子类
            $son_cate_list = $this->getCategoryList(['pid' => $data['category_id']]);

            try {
                model()->startTrans();
                // 组装名称
                $category_full_name = $parent_info['data']['category_name'] . '/' . $data['category_name'];
                // 如果有子类，先把子类的数据修正
                if ($son_cate_list) {
                    foreach ($son_cate_list['data'] as $cate) {
                        // 重新修改分类关系和组装名字
                        $son_data = [
                            'category_id_1' => $data['pid'],
                            'category_id_2' => $data['category_id'],
                            'category_id_3' => $cate['category_id'],
                            'category_full_name' => $category_full_name. '/' . $cate['category_name'],
                            'level' => $data['level'] + 1,
//                            'reward_company_rate' => $data['reward_company_rate'],
                        ];
                        model('goods_category')->update($son_data, [ [ 'category_id', '=', $cate['category_id'] ] ]);
                    }
                }

                $data['category_id_2'] = $data['category_id'];
                $data['category_full_name'] = $category_full_name;
                model('goods_category')->update($data, [ [ 'category_id', '=', $data['category_id'] ] ]);

                model()->commit();
                Cache::tag("goods_category")->clear();
                return success(0, '修改成功');
            }catch (\Exception $exception){

                model()->rollback();
                return error(-1, '修改失败：'.$exception->getMessage().$exception->getLine());

            }
        }


		$res = model('goods_category')->update($data, [ [ 'category_id', '=', $data['category_id'] ] ]);
		if ($res) {
			if (isset($data['commission_rate'])) {

				//修改对应商品的佣金比率
				model('goods')->update([ 'commission_rate' => $data['commission_rate'] ], [ [ 'category_id', '=', $data['category_id'] ] ]);
				model('goods_sku')->update([ 'commission_rate' => $data['commission_rate'] ], [ [ 'category_id', '=', $data['category_id'] ] ]);
			}
		}
		Cache::tag("goods_category")->clear();
		return $this->success($res);
	}

	/**
	 * 删除分类
	 * @param $category_id
	 * @return \multitype
	 */
	public function deleteCategory($category_id)
	{
		$goods_category_info = $this->getCategoryInfo([
			[ 'category_id', '=', $category_id ]
		], "level");
		$goods_category_info = $goods_category_info['data'];
		$field = "category_id_" . $goods_category_info['level'];
		$res = model('goods_category')->delete([ [ $field, '=', $category_id ] ]);
		
		Cache::tag("goods_category")->clear();
		return $this->success($res);
	}
	
	/**
	 * 获取商品分类信息
	 * @param array $condition
	 * @param string $field
	 */
	public function getCategoryInfo($condition, $field = 'category_id,category_name,short_name,pid,level,is_show,sort,image,keywords,description,attr_class_id,attr_class_name,category_id_1,category_id_2,category_id_3,category_full_name,commission_rate,reward_company_rate,image_adv,xm_id')
	{
		
		$data = json_encode([ $condition, $field ]);
		$cache = Cache::get("goods_category_getCategoryInfo_" . $data);
		if (!empty($cache)) {
			return $this->success($cache);
		}
		$res = model('goods_category')->getInfo($condition, $field);
		Cache::tag("goods_category")->set("goods_category_getCategoryInfo_" . $data, $res);
		return $this->success($res);
	}
	
	/**
	 * 获取商品分类列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param null $limit
	 * @return \multitype
	 */
	public function getCategoryList($condition = [], $field = 'category_id,category_name,short_name,pid,level,is_show,sort,image,attr_class_id,attr_class_name,category_id_1,category_id_2,category_id_3,commission_rate,image_adv', $order = '', $limit = null)
	{
		$data = json_encode([ $condition, $field, $order, $limit ]);
        $list = Cache::get("goods_category_getCategoryList_" . $data);
		if (empty($list)) {
            $list = model('goods_category')->getList($condition, $field, $order, '', '', '', $limit);
            Cache::tag("goods_category")->set("goods_category_getCategoryList_" . $data, $list);
        }

		return $this->success($this->perimissionFilterReturn($list));
	}
	
	/**
	 * 获取商品分类树结构
	 * @param int $level 查询等级
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param null $limit
	 * @return \multitype
	 */
	public function getCategoryTree($condition = [], $field = 'xm_id,category_id,category_name,short_name,pid,level,is_show,sort,image,attr_class_name,category_id_1,category_id_2,category_id_3,commission_rate,reward_company_rate,is_recommend', $order = 'sort desc,category_id desc', $limit = null)
	{
		$data = json_encode([ $condition, $field, $order, $limit ]);
		$cache = Cache::get("goods_category_getCategoryTree_" . $data);
		if (!empty($cache)) {
			return $this->success($this->perimissionFilterReturn($cache));
		}
		
		$list = model('goods_category')->getList($condition, $field, $order, '', '', '', $limit);

		$goods_category_list = [];
		
		//遍历一级商品分类
		foreach ($list as $k => $v) {
			if ($v['level'] == 1) {
				$goods_category_list[] = $v;
				unset($list[ $k ]);
			}
		}
		
		$list = array_values($list);
		
		//遍历二级商品分类
		foreach ($list as $k => $v) {
			foreach ($goods_category_list as $ck => $cv) {
				if ($v['level'] == 2 && $cv['category_id'] == $v['pid']) {
					$goods_category_list[ $ck ]['child_list'][] = $v;
					unset($list[ $k ]);
				}
			}
		}
		
		$list = array_values($list);
		
		//遍历三级商品分类
		foreach ($list as $k => $v) {
			foreach ($goods_category_list as $ck => $cv) {
				
				if (!empty($cv['child_list'])) {
					foreach ($cv['child_list'] as $third_k => $third_v) {
						
						if ($v['level'] == 3 && $third_v['category_id'] == $v['pid']) {
							$goods_category_list[ $ck ]['child_list'][ $third_k ]['child_list'][] = $v;
							unset($list[ $k ]);
						}
					}
				}
			}
		}
		
		Cache::tag("goods_category")->set("goods_category_getCategoryTree_" . $data, $goods_category_list);

		return $this->success($this->perimissionFilterReturn($goods_category_list));
	}
	
	/**
	 * 获取商品分类分页列表
	 * @param array $condition
	 * @param int $page
	 * @param int $page_size
	 * @param string $order
	 * @param string $field
	 * @return \multitype
	 */
	public function getCategoryPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = 'category_id,category_name,short_name,pid,level,is_show,sort,image,category_id_1,category_id_2,category_id_3,category_full_name,commission_rate')
	{
		$data = json_encode([ $condition, $field, $order, $page, $page_size ]);
		$cache = Cache::get("goods_category_getCategoryPageList_" . $data);
		if (!empty($cache)) {
			return $this->success($cache);
		}
		$list = model('goods_category')->pageList($condition, $field, $order, $page, $page_size);
		Cache::tag("goods_category")->set("goods_category_getCategoryPageList_" . $data, $list);
		return $this->success($list);
	}
	
	/**
	 * 获取商品分类列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param null $limit
	 * @return \multitype
	 */
	public function getCategoryByParent($condition = [], $field = 'category_id,category_name,short_name,pid,level,is_show,sort,image,attr_class_id,attr_class_name,category_id_1,category_id_2,category_id_3,commission_rate', $order = '', $limit = null)
	{
		$data = json_encode([ $condition, $field, $order, $limit ]);
		$cache = Cache::get("goods_category_getCategoryByParent_" . $data);
		if (!empty($cache)) {
			return $this->success($cache);
		}
		$list = model('goods_category')->getList($condition, $field, $order, '', '', '', $limit);
		foreach ($list as $k => $v) {
			$child_count = model('goods_category')->getCount([ 'pid' => $v['category_id'] ]);
			$list[ $k ]['child_count'] = $child_count;
		}
		
		Cache::tag("goods_category")->set("goods_category_getCategoryByParent_" . $data, $list);
		
		return $this->success($list);
	}
	
	/**
	 * 修改排序
	 * @param int $sort
	 * @param int $category_id
	 */
	public function modifyGoodsCategorySort($sort, $category_id)
	{
		$res = model('goods_category')->update([ 'sort' => $sort ], [ [ 'category_id', '=', $category_id ] ]);
		Cache::tag("goods_category")->clear();
		return $this->success($res);
	}

	/**
     * 检查当前分类有几级
     */
    public function checkSonLevel($category_id)
    {
        $all_cate = $this->getCategoryTree();
        $son_level = 0;
        foreach ($all_cate['data'] as $cate) {
            if ($cate['category_id'] != $category_id) {
                continue;
            }
            $son_level = 1;
            if (isset($cate['child_list'])) {
                $son_level = 2;
                foreach ($cate['child_list'] as $value) {
                    if (isset($value['child_list'])) {
                        $son_level = 3;
                        break;
                    }
                }
            }
        }
        return $son_level;
	}

	/**
	 * [getGoodsCate 获取商品分类]
	 * <AUTHOR>
	 * @DateTime 2020-11-03T10:35:01+0800
	 * @param    integer                  $pid    [description]
	 * @return   [type]                           [description]
	 */
	public function getGoodsCate($pid=0)
    {
        $fields = 'xm_goods_category.*';
        if ($pid==0){
            $fields = '
            xm_goods_category.category_name,
            xm_goods_category.short_name,
            xm_goods_category.level,
            xm_goods_category.pid,
            xm_goods_category.category_id,
            xm_goods_category.sort
            ';
        }
        $order = 'xm_goods_category.sort desc,xm_goods_category.category_id DESC';
        /**
        $where = [
            ['goods.goods_state', '=', 1],
            ['goods.verify_state', '=', 1],
            ['goods_category.category_id','>',0]
        ];
        $join_where = empty($pid)?'goods_category.category_id=goods.category_id_1':'(goods_category.category_id=goods.category_id_1 or goods_category.category_id=goods.category_id_2 or goods_category.category_id=goods.category_id_3)';

        $join = [
            ['goods goods', $join_where, 'left'],
        ];
         */
        $where = [
            'is_show'   => 1,
            'pid'       => $pid
        ];
        $data = $this->getCacheCate($where, $fields, $order);
        foreach ($data as $key=>$item) {
            $data[$key]['category_name'] = $item['short_name'] ?: $item['category_name'];
        }
        $data = $this->perimissionFilterReturn($data);
        $list = [];
        foreach ($data as $k => $v) {
            if ($k>=5) break;
            $list[] = $v;
        }
        return $list;
    }

    /**
     * [getCacheCate 获取分类]
     * <AUTHOR>
     * @DateTime 2020-11-03T10:43:46+0800
     * @param    array                    $condition [description]
     * @param    string                   $field     [description]
     * @param    string                   $order     [description]
     * @param    [type]                   $limit     [description]
     * @param    array                    $join      [description]
     * @return   [type]                              [description]
     */
    public function getCacheCate($condition = [], $field = '*', $order = '', $limit = null, $join=[],$groupBy=''){
    	$data  = json_encode([$condition, $field, $order, $limit]);
    	$cache = Cache::get("goods_getGoodsCategory_" . $data);
        if (!empty($cache)) {
            return $cache;
        }
        $cateList = model('goods_category')->getList($condition, $field, $order, 'goods_category', $join, $groupBy, $limit);
        Cache::tag("goods")->set("goods_getGoodsCategory_" . $data, $cateList, 600);
        return $cateList;
    }

    /**
     * 过滤经理不运营的分类id
     * @param $shop_id
     * @param $list
     * @param CategoryPermissionFilterService $categoryPermissionFilterService
     * @return array
     */
    public function perimissionFilterReturn($list){
        if ($this->shopId){
            $shopModel = new Shop();
            $manager_id = $shopModel->getManagerByShop($this->shopId);

//            $manager_id = 268641;
            $manager_id && $list = app('app\Domain\Services\ManageConfig\CategoryPermissionFilterService')->filterCatList((int)$manager_id,$list);
        }

        return $list;
    }

    /**
     * 设置店铺
     * @param $shop_id
     */
    public function setShopId($shop_id){
        $this->shopId = (int)$shop_id;
    }

	/**
     * 获取排除的分类id或者闭包sql
     *
     * @param [type] $shop_id
     * @param boolean $type true:id数组   false:sql
     * @return void
     */
	public function getManagerGoodsCategoryQuery($shop_id, $type = true){
		$shopModel = new Shop();
        $xm_manage_uid = $shopModel->getManagerByShop($shop_id);  //查找经理id是否存在
        // $xm_manage_uid = 268641;
        if($xm_manage_uid > 0){
            $manageConfigApp = app('app\Domain\Models\ManageConfig\ManageConfigPrivateRepository');
            $config_type = CONFIG_TYPE::$CATEGORY;
            $open = $manageConfigApp->isPrivate($config_type, $xm_manage_uid);
            if($open){  //开启了
				if($type){
					$res = $manageConfigApp->getPrivateValue($config_type, CONFIG_KEY::$EXCLUDE_MANAGE, $xm_manage_uid);
				}else{
					$res = $manageConfigApp->getSubQuery($config_type, CONFIG_KEY::$EXCLUDE_MANAGE, $xm_manage_uid);
				}
            }
        }
        return isset($res) ? $res : false;
	}


    /**
     * 修改分类
     * @param array $data
     * @param array $where
     * @return bool
     * <AUTHOR>
     */
    public function updateCategory(array $data, array $where)
    {
        model('goods_category')->update($data, $where);
        Cache::tag("goods_category")->clear();
        return true;
    }



    /**
     * 获取商品分类树结构
     * @param int $level 查询等级
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param null $limit
     * @return \multitype
     */
    public function getCategoryTreeXm($condition = [], $field = 'xm_id,category_id,category_name,short_name,pid,level,is_show,sort,image,attr_class_name,category_id_1,category_id_2,category_id_3,commission_rate,reward_company_rate,is_recommend', $order = 'sort desc,category_id desc', $limit = null)
    {
        $data = json_encode([ $condition, $field, $order, $limit ]);
        $cache = Cache::get("goods_category_getCategoryTree_" . $data);
        if (!empty($cache)) {
            //return $this->success($this->perimissionFilterReturn($cache));
        }

        $list = model('goods_category')->getList($condition, $field, $order, '', '', '', $limit);

        $res = CommonService::getInstance()->getLeagueCateName(0);
        $xmCateAll = $res['xmLeagueCate'];
        $goods_category_list = [];


        foreach ($list as $k => $v) {
            $v['xmCateName'] =  '无';
            if($v['xm_id'] >0 and isset($xmCateAll[$v['xm_id']])) {
                $list[$k]['xmCateName'] = $xmCateAll[$v['xm_id']];
            }
        }

        //遍历一级商品分类
        foreach ($list as $k => $v) {
            if ($v['level'] == 1) {
                $v['xmCateName'] = $v['level'] < 3 ? '无' : '';
                if($v['xm_id'] >0 and isset($xmCateAll[$v['xm_id']])) {
                    $v['xmCateName'] = $xmCateAll[$v['xm_id']];
                }
                $goods_category_list[] = $v;
                unset($list[ $k ]);
            }
        }

        $list = array_values($list);

        //遍历二级商品分类
        foreach ($list as $k => $v) {
            foreach ($goods_category_list as $ck => $cv) {
                if ($v['level'] == 2 && $cv['category_id'] == $v['pid']) {
                    $v['xmCateName'] =  '无';
                    if($v['xm_id'] >0 and isset($xmCateAll[$v['xm_id']])) {
                        $v['xmCateName'] = $xmCateAll[$v['xm_id']];
                    }
                    $goods_category_list[ $ck ]['child_list'][] = $v;
                    unset($list[ $k ]);
                }
            }
        }

        $list = array_values($list);

        //遍历三级商品分类
        foreach ($list as $k => $v) {
            foreach ($goods_category_list as $ck => $cv) {

                if (!empty($cv['child_list'])) {
                    foreach ($cv['child_list'] as $third_k => $third_v) {
                        $v['xmCateName'] =  '';
                        if($v['xm_id'] >0 and isset($xmCateAll[$v['xm_id']])) {
                            $v['xmCateName'] = $xmCateAll[$v['xm_id']];
                        }
                        if ($v['level'] == 3 && $third_v['category_id'] == $v['pid']) {
                            $goods_category_list[ $ck ]['child_list'][ $third_k ]['child_list'][] = $v;
                            unset($list[ $k ]);
                        }
                    }
                }
            }
        }

        Cache::tag("goods_category")->set("goods_category_getCategoryTree_" . $data, $goods_category_list);

        return $this->success($this->perimissionFilterReturn($goods_category_list));
    }
}