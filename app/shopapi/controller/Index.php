<?php
/**
 * Index.php
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2015-2025 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: http://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 * <AUTHOR> niuteam
 * @date : 2015.1.17
 * @version : v1.0.0.0
 */

namespace app\shopapi\controller;

use app\model\app\App;
use app\model\shop\Shop as ShopModel;
use app\model\shop\ShopReopen as ShopReopenModel;
use app\model\system\Stat;
use app\service\shopapi\ShopService;
use app\service\shopapi\OrderService;
use app\service\shopapi\ShopCertService;
use Carbon\Carbon;
use app\model\web\WebSite as WebsiteModel;
use app\model\goods\Goods as GoodsModel;
use app\model\system\User as ShopUser;
use app\model\shop\ShopMember;
use think\facade\Db;
use app\service\wechat\WxAppService;

use app\model\app\App as AppModel;
use app\service\shop\ShopService as PcShopService;

class Index extends BaseApi
{

    //小程序首页地址(分享用)
    protected $homeUrl = '/otherpages/shop/home/<USER>';

    protected $app_module = "shop";

    //按天统计用户访问量
    private $userClickDay = [0 => 0, 1 => 0, 7 => 0, 30 => 0];

    //是否开启身份证校验
    private $IdCardCheck = true;

    //获取今天 昨天 7天内 30天内销售
    private $sumDays = [0 => 0.00, 1 => 0.00, 7 => 0.00, 30 => 0.00];

    public function __construct()
    {
        parent::__construct();
        //判断是否登录
        if(!in_array(request()->action(),['version','download']))$this->getCheckToken();
    }

    /**
     * version 获取版本
     *
     * @return \Illuminate\Http\Response
     */
    public function version() {
        $p = input('p', '');//终端
        $vc = input('vc', '');//编译代码
        $mk = input('mk', '');//渠道号；如小米，华为等应用商店
        $appid = input('appid', '');// appid，对应渠道code，跟mk同作用
        $data = array();

        $appModel = new App();
        if (!empty($appid)) {
            $mk = $appid;
        }

        $mks = explode(',', $mk);
        $vcs = explode(',', $vc);
        // 是否批量检测
        $is_batch_check = count($mks) > 1;
        if (isset($p) && count($mks) == count($vcs)) {
            foreach ($mks as $mk_key => $mk) {
                $vc = $vcs[$mk_key];
                $version_data = [];
                //查询最低强更版本
                $must_update = $appModel->getInfoFromAppid($mk, $p, [
                    'is_block' => 'N',
                    'is_must_update' => 1
                ]);
                // 是否需要强制更新
                $mustUpdate = ($must_update && $vc < $must_update['build']) ? 1 : 0;
                //查询最新版本号
                $versionInfo = $appModel->getInfoFromAppid($mk, $p, [
                    'is_block' => 'N',
                ]);
                if ($versionInfo) {
                    $app_paths = explode('/', $versionInfo['app_path']);
                    $version_data['appid'] = $mk;
                    $version_data['uni_appid'] = explode('.', end($app_paths))[0];
                    $version_data['app_version'] = $versionInfo['version'];
                    $version_data['app_message'] = $versionInfo['note'];
                    $version_data['app_code'] = $versionInfo['build'];
                    if ($mustUpdate != 1 && ($vc < $versionInfo['build'])) {
                        $mustUpdate = 2;
                    }
                    $vc = $versionInfo['build'];
                    $version_data['update'] = $mustUpdate; //是否强更
                    $version_data['is_pass'] = AppModel::checkAppPlatformCheckStatus($appid, $vc) ? 0 : 1; //是否审核通过
//                    $version_data['app_path'] = __ROOT__ . '/' . $versionInfo['app_path'];
                    $version_data['app_path'] = url('shopapi/index/download', ['id' => $versionInfo['id'], 'appid' => $version_data['appid']]);
                    $data[] = $version_data;
                }
            }
            if (!empty($data = $is_batch_check ? $data : current($data))) {
                return $this->resJson($this->success($data));
            }
        }
        return $this->resJson($this->error('', "获取版本信息失败"));
    }

    // 下载应用安装包
    public function download() {
        $id = input('id', '');//安装包id
        $appid = input('appid', $id);//appid
        $data = (new App())->getInfo(['id' => $id]);
        if (!empty($data['data']) && file_exists($app_path = root_path() . $data['data']['app_path'])) {
            return download($app_path, $appid . '.wgt')->expire(300);
        }
        return response("", 404);
    }

    /**
     * 首页
     * @return mixed
     */
    public function index()
    {
        return $this->resJson($this->error('', "该应用已停用"));
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        //店铺基础信息
        $shop_model = new ShopModel();
        $shop_info = $shop_model->getShopInfo([['site_id', '=', $this->site_id]], 'site_id, expire_time, site_name, username, website_id, 
	        cert_id, is_own, level_id, level_name, category_id, category_name, group_id, group_name, member_id, member_name,
	        shop_status, close_info, sort, start_time, end_time, logo, avatar, banner, seo_keywords, seo_description, qq, ww, 
	        telephone, is_recommend, shop_desccredit, shop_servicecredit, shop_deliverycredit, workingtime, shop_baozh,
	        shop_baozhopen, shop_baozhrmb, shop_qtian, shop_zhping, shop_erxiaoshi, shop_tuihuo, shop_shiyong, shop_shiti, 
	        shop_xiaoxie, shop_free_time, shop_sales, shop_adv, account, account_withdraw, work_week, province, province_name, 
	        city, city_name, district, district_name, community, community_name, address, full_address, longitude, latitude, 
	        sub_num');

        $shop_info = $shop_info['data'];

        if ($shop_info['expire_time'] == 0) {

            $shop_info['is_reopen'] = 1;//永久有效
        } elseif ($shop_info['expire_time'] > time()) {

            $cha = $shop_info['expire_time'] - time();
            $date = ceil(($cha / 86400));
            if ($date < 30) {
                $shop_info['is_reopen'] = 2;//离到期一月内才可以申请续签
                $shop_info['expires_date'] = (int)$date;
            }
        } else {
            $shop_info['is_reopen'] = 3;
            $shop_info['expires_date'] = 0;
        }
        $data['shop_info'] = $shop_info;

        //判断是否有续签
        $reopen_model = new ShopReopenModel();
        $reopen_info = $reopen_model->getReopenInfo([['sr.site_id', '=', $this->site_id], ['sr.apply_state', 'in', [1, -1]]]);
        if (empty($reopen_info['data'])) {
            $is_reopen = 1;
        } else {
            $is_reopen = 2;
        }
        $data['is_reopen'] = $is_reopen;
        //基础统计信息
        $stat_shop_model = new Stat();
        $today = Carbon::now();
        $yesterday = Carbon::yesterday();
        $stat_today = $stat_shop_model->getStatShop($this->site_id, $today->year, $today->month, $today->day);
        $stat_yesterday = $stat_shop_model->getStatShop($this->site_id, $yesterday->year, $yesterday->month, $yesterday->day);

        $data['stat_day'] = $stat_today['data'];
        $data['stat_yesterday'] = $stat_yesterday['data'];
        $data['today'] = $today;

        //获取总数
        $shop_stat_sum = $stat_shop_model->getShopStatSum($this->site_id);
        $goods_model = new GoodsModel();
        $goods_sum = $goods_model->getGoodsTotalCount(['site_id' => $this->site_id]);
        $shop_stat_sum['data']['goods_count'] = $goods_sum['data'];
        $data['shop_stat_sum'] = $shop_stat_sum['data'];

        //平台配置信息
        $website_model = new WebsiteModel();
        $website_info = $website_model->getWebSite([['site_id', '=', 0]], 'web_qrcode,web_phone');
        $data['website_info'] = $website_info['data'];

        //会员基础信息
        $user_model = new ShopUser();
        $user_info = $user_model->getUserInfo([['uid', '=', $this->uid]], 'username,group_name,login_time');
        $data['shop_user_info'] = $user_info['data'];
        return $this->response($this->success($data));
    }

    /**
     * 店铺主页
     * @return false|string|\think\response\Json
     */
    public function shopHome()
    {
        return $this->resJson($this->error('', "该应用已停用"));
        $token = $this->checkToken();
        if (!$token['data']) return $this->resJson($token);
        //店铺基础信息
        $fields = 'site_id, expire_time, site_name, username, website_id, cert_id, logo, avatar, shop_status';
        $data = $this->appShopInfo($this->site_id, $fields);
        $time = time();

        //商品数量统计
        $ShopServer = new ShopService();
        $data['goods_count'] = $ShopServer->getShopGoodsCount($this->site_id);
        //店铺是否正常 1正常 0已打烊 店铺经营状态shop_status（0.已到期 1.正常 2.冻结） 到期时间expire_time（必须有过期时间）
        $data['shop_is_open'] = 1;
        if (!in_array($data['shop_info']['shop_status'], [1]) || ($data['shop_info']['expire_time'] < $time && $data['shop_info']['expire_time']!=0)) $data['shop_is_open'] = 0;
        !empty($data['shop_info']['logo']) && $data['shop_info']['logo'] = img($data['shop_info']['logo']);
        !empty($data['shop_info']['avatar']) && $data['shop_info']['avatar'] = img($data['shop_info']['avatar']);
        //分享店铺小程序信息
        $data['share_info'] = $ShopServer->shareInfo($data['shop_info']);
        //活动收益
        $data['ac_url'] = __ROOT__.'/spa/app/activeEarningsList';

        //用户访问量统计 今天 昨天 7天内 30天内的访问量 //销售统计信息 今天 昨天 7天内 30天内的销售
        $OrderServer = new OrderService();
        foreach ($this->userClickDay as $ck => $cv) {
            $sTime = strtotime(date('Ymd', $time - ($ck * 86400)));
            $eTime = $sTime + 86399;
            in_array($ck, [7, 30]) && $eTime = $time;
            $userClick['day' . $ck] = $ShopServer->getShopClick($this->site_id, $sTime, $eTime); //用户访问量统计
            $orderSale['day' . $ck] = $OrderServer->getShopSale($this->site_id, $sTime, $eTime); //销售统计信息
        }
        $data['user_click'] = $userClick + ['time' => date('Y-m-d H:i:s', $time)];
        $data['order_sale'] = $orderSale + ['time' => date('Y-m-d H:i:s', $time)];
        return $this->resJson($this->success($data));
    }

    /**
     * 我的中心
     * @return false|string|\think\response\Json
     */
    public function myCenter()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        $time = time();

        //店铺基础信息
        $fields = 'site_id, expire_time, site_name, username, website_id, cert_id,(case cert_id when 0 then 0 else 1 end) as confirm, logo , avatar, (account-account_withdraw_apply) as account, shop_status, vip_level, vip_expired_time, youli_withdraw as youli, mentor_id';
        $data = $this->appShopInfo($this->site_id, $fields);

        //判断店铺是否过期    店铺经营状态shop_status（0.已到期 1.正常 2.冻结） 到期时间expire_time（必须有过期时间）
        $data['shop_info']['shop_desc'] = '';
        // if (!in_array($data['shop_info']['shop_status'], [1]) || $data['shop_info']['expire_time'] < $time) $data['shop_info']['shop_desc'] = '已打烊';

        //店铺有效期一律改为VIP的有效期
        $data['shop_info']['expire_time'] = $data['shop_info']['vip_expired_time'];
        $data['shop_info']['expire_date'] = $data['shop_info']['vip_expired_time'] && $data['shop_info']['vip_expired_time'] > 0 ?
                                            'VIP有效期至'.date('Y-m-d', $data['shop_info']['vip_expired_time']) : 'VIP已过期';

        if($data['shop_info']['vip_level'] == 0){
            $data['shop_info']['expire_date'] = '';
        }

        //累计会员数(粉丝)
        $member = new ShopMember();
        $data['shop_info']['fans_count'] = $member->getMemberCount([['site_id', '=', $this->site_id]])['data'];
        !empty($data['shop_info']['logo']) && $data['shop_info']['logo'] = img($data['shop_info']['logo']);
        !empty($data['shop_info']['avatar']) && $data['shop_info']['avatar'] = img($data['shop_info']['avatar']);
        //统计今天 昨天 本月 的收益
        $todayTime = strtotime(date('Ymd', $time)); //今天
        $yesterdayTime = strtotime(date('Ymd', $time - 86400)); //昨天
        $monthTime = strtotime(date('Ym01', $time)); //月
        $income = [
            'today' => [$todayTime, $todayTime + 86399],
            'yesterday' => [$yesterdayTime, $yesterdayTime + 86399],
            'month' => [$monthTime, $time]
        ];
        $shopSer = new ShopService();
        foreach ($income as $ak => $av) {
            //店铺个人收益统计
            $data['income'][$ak] = $shopSer->getMyIncome($this->site_id, $av[0], $av[1], 'reward_money');
        }

        //查询店长的银行卡信息
        $shopCertService = new ShopCertService();
        $pcShopService = new PcShopService();
        $bank_card = $shopCertService->getInfo(['site_id' => $this->site_id], 'settlement_bank_account_number');
        $data['shop_info']['bind_bank_card'] = empty($bank_card['settlement_bank_account_number']) ? 0 : 1;
        $data['shop_info']['shop_level'] =isset($pcShopService->vip_level[$data['shop_info']['vip_level']]) ?
                                                $pcShopService->vip_level[$data['shop_info']['vip_level']] : '0元店铺' ;

        $manager_num = $shopSer->getMyManager($this->site_id);
        $data['shop_info']['manager'] = isset($manager_num[0]) ? $manager_num[0]['total'] : 0;
        $data['shop_info']['manager_url'] = __ROOT__.'/spa/app/shopkeeper?shop_id='.$this->site_id.'&type=1';

        $data['shop_info']['show_mentor_qrcode'] = 0;
        if($data['shop_info']['vip_level'] > 0 && $data['shop_info']['vip_expired_time'] > time()){

            $data['shop_info']['vip_manager'] = isset($manager_num[1]) ? $manager_num[1]['total'] : 0;
            // $data['shop_info']['vip_manager_url'] = __ROOT__.'/spa/app/shopkeeper?shop_id='.$this->site_id.'&type=2';
            $data['shop_info']['vip_manager_url'] = __ROOT__.'/spa/app/vip_shopkeeper?shop_id='.$this->site_id.'&isRealName='.($data['shop_info']['confirm'] == 1 ? 1: 0);
            $data['shop_info']['show_mentor_qrcode'] = 1;

        }

        $data['income']['will_statements'] = 0;
        $data['income']['will_statements_points'] = 0;
        $data['income']['today_orders'] = 0;

        // $data['shop_info']['can_upgrade'] = $data['shop_info']['vip_level'] == 0 ? 1: 0;
        $data['shop_info']['can_upgrade'] = 1;
        $data['shop_info']['upgrade_url'] = __ROOT__.'/spa/app/upgrade_vip?shop_id='.$this->site_id;
        if($data['shop_info']['vip_level'] > 0){
            $data['shop_info']['upgrade_url'] = __ROOT__.'/spa/app/shopUpgrade?shopId='.$this->site_id;
        }


        $res = (new ShopService())->getMentorQCode($data['shop_info']['mentor_id']);
        $data['shop_info']['mentor_qrcode'] = empty($res['MentorQrCode']) ? '' : img($res['MentorQrCode']);
        if($data['shop_info']['mentor_qrcode'] == ''){
            $data['shop_info']['show_mentor_qrcode'] = 0;
        }

        return $this->resJson($this->success($data));
    }

    /**
     * 店铺管理
     * @return false|string|\think\response\Json
     */
    public function appShopManage()
    {
        //店铺基础信息  是否认证(confirm)
        $fields = 'site_id, expire_time,FROM_UNIXTIME(expire_time,"%Y-%m-%d %H:%i:%S") as expire_date , site_name, username, website_id, cert_id,(case cert_id when 0 then 0 else 1 end) as confirm, shop_status';
        $res = $this->appShopInfo($this->site_id, $fields);

        unset($res['shop_info']['expires_date']);//删除原始业务字段
        if($res['shop_info']['expire_time'] == 0){
            $res['shop_info']['expire_date'] = '永久有效';
        }
        return $this->resJson($this->success($res));
    }

    /**
     * 实名认证
     * @return false|string|\think\response\Json
     */
    public function realNameCert()
    {

        $cert_data['site_id'] = $this->site_id;
        if (empty($this->site_id)) return $this->resJson($this->error([], "商家ID为空!"));

        $cert_data['contacts_name'] = input('realName', '');
        $cert_data['contacts_card_no'] = input('idCard', '');
        $cert_data['cert_type'] = 1; // 申请类型1.个人2.公司

        if (mb_strlen($cert_data['contacts_name']) < 2) return $this->resJson($this->error('', "姓名不能为空-请输入姓名全称!"));
        if (strlen($cert_data['contacts_card_no']) != 18) return $this->resJson($this->error('', "身份证号为空或者没有18位!"));
        //身份证号校验
        if ($this->IdCardCheck) {
            $res = checkIdCard($cert_data['contacts_card_no']);
            if (!$res) return $this->resJson($this->error('', "身份证号校验失败! 请输入正确的身份证号"));
        }

        //店铺基础信息
        $shop_model = new ShopModel();
        $shop_info = $shop_model->getShopInfo([['site_id', '=', $this->site_id]], 'site_id, cert_id');
        if (empty($shop_info['data'])) return $this->resJson($this->error('', "店铺不存在!"));
        if (!empty($shop_info['data']['cert_id'])) return $this->resJson($this->error('', "店铺已认证!"));
        //添加店铺认证信息
        $cert_id = model("shop_cert")->add($cert_data);
        if (!$cert_id) return $this->resJson($this->error('', "认证数据保存失败!"));
        $site_id = model('shop')->update(['cert_id' => $cert_id], ['site_id' => $this->site_id]);
        //处理显示样式 唐** ,3258*********0202
        $len = mb_strlen($cert_data['contacts_name']) - 1;
        $name = substr($cert_data['contacts_name'], 0, 3) . [0 => '*', 1 => '*', 2 => '**', 3 => '***'][$len > 3 ? 3 : $len];
        $card = formatCard($cert_data['contacts_card_no'], 1);
        return $this->resJson($this->success(['realName' => $name, 'idCard' => $card]));

    }


    /**
     * 我的粉丝列表
     */
    public function myFansList()
    {
        ///-###给APP的测试数据
        // $this->site_id = 1;

        try {
            $return = [];

            /// 粉丝数据   2020-09-04 经需求确认（ccn,cjb），取消关注的也当成粉丝列出来，也就是说但凡在shop_member表中当前店铺下的人都算是粉丝，无论是否关注或者取消关注
            $fans = Db::name('shop_member')->alias('sm')
                ->join('member m', 'sm.member_id=m.member_id')
                ->field('sm.create_time as join_time, m.order_complete_money as monetary, m.nickname as wechat, m.headimg as head_img, sm.member_id')
                ->where('sm.site_id', $this->site_id)->order('join_time desc')
                ->select()->toArray();


            if (!empty($fans)) {

                $member_id = array_column($fans, 'member_id');

                $monetary = Db::name('order')->alias('o')->leftjoin('order_goods og', 'o.order_id=og.order_id')
                    ->where('o.site_id', $this->site_id)
                    ->whereIn('o.member_id', $member_id)->where('o.pay_status', 1)
                    ->where('og.refund_time', 0)->group('o.member_id')
                    ->fieldRaw('o.member_id, sum(og.goods_money) as monetary')
                    ->select()->toArray();

                $monetary = array_column($monetary, null, 'member_id');
                foreach ($fans as $k => $v) {

                    $fans[$k]['join_time'] = !empty($v['join_time']) ? date('Y-m-d H:i', $v['join_time']) : '';
                    $fans[$k]['head_img'] = !empty($v['head_img']) ? img($v['head_img']) : '';
                    $fans[$k]['monetary'] = isset($monetary[$v['member_id']]) ? $monetary[$v['member_id']]['monetary'] : 0;
                }
            }

            $return['fans'] = empty($fans) ? [] : $fans;

        } catch (\Exception $err) {

            return $this->resJson($this->error('', $err->getMessage()));
        }

        return $this->resJson($this->success($return));
    }

    /**
     * 店铺基础信息
     * @param int $site_id 店铺ID
     * @param string $field
     * @return mixed
     */
    private function appShopInfo($site_id = 0, $field = '')
    {
        $site_id = $site_id ?: $this->site_id;
        if (!$site_id) return $this->resJson($this->error('', '店铺ID(site_id)不存在'));
        return (new ShopService())->shopInfo($site_id, $field);
    }

    /**
     * 获取用户信息
     */
    public function appShopUser()
    {
        if (empty($this->user_info) || !is_array($this->user_info) || empty($this->user_info['site_id'])) {
            return $this->resJson($this->error('', '店铺ID(site_id)不存在'));
        }
        $userInfo = (new ShopService())->loginShopInfo($this->user_info['site_id'] ?? 0);
        if (!$userInfo || !is_array($userInfo)) return $this->resJson($this->error('', '用户信息不存在'));
        !empty($userInfo['logo']) && $userInfo['logo'] = img($userInfo['logo']);
        !empty($userInfo['avatar']) && $userInfo['avatar'] = img($userInfo['avatar']);
        return $this->resJson($this->success(['token' => input('token', '')] + $userInfo));
    }

    /**
     * 生成店铺App二维码
     * @param int $site_id 店铺ID
     * @param int $width 二维码宽度
     * @return \think\response\Json
     */
    public function getShopQCode($site_id = 0, $width = 0, $imgName = '', $qrcode_url='')
    {
        try {
            $site_id = $site_id ?: $this->site_id;
            $width = $width ?: input('width', 430);
            if (!$site_id) return $this->resJson($this->error('', '店铺ID(site_id)不存在'));
            $migPath = __UPLOAD__ . "/qrcode_xm";
            if (!is_dir($migPath)) {
                @mkdir($migPath, 0775, true);
            }
            $migPath = $migPath . "/index";
            if (!is_dir($migPath)) {
                @mkdir($migPath, 0775, true);
            }
            if($imgName){
                $imgPath = $migPath . '/'.$imgName. '.jpg';
            }else{
                $imgPath = $migPath . "/shop_$site_id" . '.jpg';
            }

            if(!$qrcode_url){
                $qrcode_url = $this->homeUrl . 's=' . $site_id;
            }

            //如果存在 直接返回文件
            if(is_file($imgPath))return $this->resJson($this->success(['site_id' => $site_id, 'imgUrl' => img($imgPath)]));

            // 兼容旧方式，分割出 page 和 scene
            $paths = explode('?', $qrcode_url);
            $scene = '';
            if (count($paths) > 1) {
                $scene = $paths[1];
                $page = $paths[0];
            } else {
                $page = $paths[0];
            }
            $res = (new WxAppService())->wxaCodeGetUnlimited($scene, $page, $width);
            if (!$res) return $this->resJson($this->error('', '二维码生成错误'));

            $ret = file_put_contents($imgPath, $res, true);
            if (!$ret) return $this->resJson($this->error('', '二维码保存错误'));
            return $this->resJson($this->success(['site_id' => $site_id, 'imgUrl' => img($imgPath)]));
        } catch (\Throwable $t) {
            return $this->resJson($this->error('', '二维码生成错误'));
        }

    }

    /**
     * 导师二维码接口
     * @param
     * @return
     */
    public function getShopMentorQCode(){
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        $res = (new ShopService())->getMentorQCode();

        if(empty($res['MentorQrCode'])) return $this->resJson($this->error('', '请先设置导师二维码'));

        $data = [];
        $data['mentor_qrcode'] = img($res['MentorQrCode']);
        // var_dump($data);exit; 
        return $this->resJson($this->success($data));
    }

    /**
     * 海报接口
     * @param
     * @return
     */
    public function getShopPoster(){
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);

        $type = input('type', 1);
        $res = (new ShopService())->ShopPoster($type);
        $shop_model = new ShopModel();
        $shop_info = $shop_model->getShopInfo([['site_id', '=', $this->site_id]], 'site_name shop_name,avatar avatar_url');
        $open_type = $type == 2 ? 1: 0;
        // $open_type = 0;  //取消在线支付

        $data = [];
        $data['bg_path'] = img(empty($res['bg_path']) ? 'upload/uniapp/default_headimg.png' : $res['bg_path']);
        $data['shop_name'] = $shop_info['data']['shop_name'];
        $data['avatar_url'] = img(empty($shop_info['data']['avatar_url']) ? 'upload/uniapp/default_headimg.png' : $shop_info['data']['avatar_url']);
        // 柚品VIP掌柜海报路径
        $codeUrl = '/otherpages/member/open_shopkeeper/open_shopkeeper?invitation_shop_id='.$this->site_id.'&open_type='.$open_type;
        $qrcode_url = 'otherpages/member/open_shopkeeper/open_shopkeeper?v='.$this->site_id.'&t='.$open_type;
        $ret = $this->getShopQCode($this->site_id, 0, "invitation_shop_".$this->site_id.'_'.$open_type, $qrcode_url);
        $qrcde_res = json_decode($ret->getContent(),true);
        $data['shop_qrcode'] = $qrcde_res['data']['imgUrl'];
        $data['shop_qrcode_url'] = $codeUrl;

        return $this->resJson($this->success($data));
    }

    /**
     * 店长签到接口
     * @param
     * @return
     */
    public function signIn(){
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        $res = (new ShopService())->signInShop($this->site_id);

        // var_dump($data);exit; 
        return $this->resJson($res);
    }
}