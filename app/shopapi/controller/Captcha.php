<?php

namespace app\shopapi\controller;

use think\captcha\facade\Captcha as ThinkCaptcha;
use think\facade\Cache;
use extend\Sms;
use app\service\shopapi\ShopService;
use app\service\init\CommonService;

class Captcha extends BaseApi
{

    //手机短信验证码时间(分)
    private $smsTime = 2;

    /**
     * 验证码
     */
    public function captcha()
    {
        if (isset($this->params['captcha_id']) && !empty($this->params['captcha_id'])) {
            Cache::delete($this->params['captcha_id']);
        }

        $captcha_data = ThinkCaptcha::create(null, true);
        $captcha_id = md5(uniqid(null, true));
        // 验证码10分钟有效
        Cache::set($captcha_id, $captcha_data['code'], 600);
        return $this->response($this->success(['id' => $captcha_id, 'img' => $captcha_data['img']]));
    }

    /**
     * 检测验证码
     * @param boolean $snapchat 阅后即焚
     */
    public function checkCaptcha($snapchat = true): array
    {
        //如果没有验证码ID 默认用手机md5($mobile)
        if(!isset($this->params['captcha_id'])|| empty($this->params['captcha_id'])){
            $mobile = input('mobile','');
            $mobile && $this->params['captcha_id'] = md5($mobile);
        }
        if (!isset($this->params['captcha_id']) || empty($this->params['captcha_id'])) {
            return $this->error('', 'REQUEST_CAPTCHA_ID');
        }

        if (!isset($this->params['captcha_code']) || empty($this->params['captcha_code'])) {
            return $this->error('', 'REQUEST_CAPTCHA_CODE');
        }

        if ($snapchat) $captcha_data = Cache::pull($this->params['captcha_id']);
        else $captcha_data = Cache::get($this->params['captcha_id']);
        if (empty($captcha_data)) return $this->error('', 'CAPTCHA_FAILURE');
        if ($this->params['captcha_code'] != $captcha_data) return $this->error('', 'CAPTCHA_ERROR');

        return $this->success();
    }

    /**
     *  获取手机验证码
     * @param int $mobile
     * @param int $checkUser 检查用户是否存在
     * @param int $type 短信类型 1 '注册' 2 '找回密码' 3 '短信登录' 4 '获取验证码' 5 '修改用户账号'
     * @return false|string|\think\response\Json
     */
    public function mobileSmsCaptcha($mobile = 0, $checkUser = 1, $type = 3)
    {
        $mobile = $mobile ?: input('mobile', '');
        if (empty($mobile)) return $this->resJson($this->error('', "手机号不能为空!"));
        if (!preg_match('/^1\d{10}$/', $mobile)) return $this->resJson($this->error('', "手机号码不正确!"));

        //检查用户是否存在
        if ($checkUser) {
            $userKey = 'user_sms_key_' . $mobile;
            $user = Cache::get($userKey);
            if ($user == null) {
                $user = (new ShopService())->checkUserIsExist(['username' => $mobile], 'User', 'username') ?: -1;
                $userTtl = $user == -1 ? 60 : 3600;  //用户不存在 缓存1分钟  用户存在 缓存1小时
                Cache::set($userKey, $user, $userTtl);
            }
            //请求先迈数据接口
            $XMRes = 0;
            !is_array($user) && $XMRes = (new CommonService())->xianMaiIsUser($mobile,'');
            if (!is_array($user) && !$XMRes) return $this->resJson($this->error('', "用户不存在!"));
        }
        $captcha_id = md5($mobile);
        // 校验验证码是否有效
        $check_res = Cache::get($captcha_id);
        if ($check_res) return $this->resJson(($this->success(['captcha_id' => $captcha_id], '短信已发送成功')));
        //开始发送短信
        $resSms = $this->smsSend($mobile, $type);
        //发送失败
        if ($resSms['code'] != 0) return $this->resJson(($this->error('', $resSms['msg'])));
        //验证码
        $sms = $resSms['sms'];
        // 验证码2分钟有效
        Cache::set($captcha_id, $sms, ($this->smsTime * 60));
        return $this->resJson(($this->success(['captcha_id' => $captcha_id], '短信发送成功')));
    }

    /**
     * 获取手机验证码（不检查用户是否存在）
     */
    public function getMobileSms()
    {
        $this->getCheckToken();

        return $this->mobileSmsCaptcha(input('mobile', ''), 0, $this->sms_type);
    }

    /**
     * 校验手机验证码
     * @return false|string
     */
    public function checkSmsCaptcha()
    {
        if (!isset($this->params['mobile']) || empty($this->params['mobile'])) {
            return $this->resJson($this->error('', '手机不能为空'));
        }
        //校验验证码 查文件缓存 判断是否有效
        $check_res = $this->checkCaptcha(false);
        //查数据表  verify_code 判断是否在效
        $res = $this->smsVerify($this->params["mobile"], $this->params["captcha_code"],  $this->sms_type);
        if ($res) return $this->resJson($this->success('', '验证码有效'));
        return $this->resJson($this->error('', '验证码无效'));

    }

    /**
     * 获取短信验证码
     * @param int $mobile 手机号
     * @param int $type 类型  1：注册；2：找回密码；3：直接登录; 4：绑定手机
     * @param bool $sendsms 是否发送信，为否时则返回验证码
     * @return array
     */
    public function smsSend($mobile = 0, $type = 3, $sendsms = true)
    {
        $res = (new Sms())->sms_rqcode($mobile, $type, $sendsms);
        return $res;
    }

    /**
     * 验证短信验证码
     * @param int $mobile 手机号
     * @param int $code 短信码
     * @param int $type 类型  1：注册；2：找回密码；3：直接登录; 4：绑定手机
     * @return bool
     */
    public function smsVerify($mobile, $code, $type)
    {
        $res = (new Sms())->sms_verify($mobile, $code, $type);
        return $res;
    }

}