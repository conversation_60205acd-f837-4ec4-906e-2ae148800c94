<nc-component v-bind:data="data[index]" class="component-shoperrecommand">
  <!-- 预览 -->
  <template slot="preview">
    <div class="shoperrecommand-head">
      <div class="title-wrap">
        <div class="line"></div>
        <span class="name">{{data[index].name}}</span>
      </div>
      <!-- <div class="more">{{data[index].moreText}}<span>></span></div> -->
    </div>
    <div class="list-wrap">
      <div class="item" v-for="it in [1,2,3]">
        <div class="img-wrap">
          <img src="STATIC_EXT/diyview/img/crack_figure.png" />
        </div>
        <span class="good-name">商品名称商品名称</span>
      </div>
    </div>
  </template>

  <!-- 编辑 -->
  <template slot="edit">
    <template v-if="nc.lazyLoad">
        <shoper-recommand></shoper-recommand>
    </template>
  </template>

  <!-- 资源 -->
  <template slot="resource">
    <css src="{$resource_path}/shoperrecommand/css/design.css"></css>
    <js src="{$resource_path}/shoperrecommand/js/design.js"></js>
  </template>
</nc-component>
