<?php


namespace app\listener;


use addon\goodscoupon\domainService\DistributionGoodsCouponMoneyService;
use addon\goodscoupon\model\GoodscouponModel;
use app\model\order\OrderGoodsModel;
use app\model\order\OrderModel;
use think\facade\Db;
use think\facade\Log;

class UseCouponOrderSplit
{
    public function handle(array $orderInfo)
    {
        try
        {
            $orderM = null;
            if(isset($orderInfo['order_no']))
                $orderM = OrderModel::where("order_no", $orderInfo['order_no'])->where("goodscoupon_id", ">",0)->whereNotNull("split_order")->find();
            if(isset($orderInfo['order_id']))
                $orderM = OrderModel::where("order_id", $orderInfo['order_id'])->where("goodscoupon_id", ">",0)->whereNotNull("split_order")->find();
            if(isset($orderInfo['out_trade_no']))
                $orderM = OrderModel::where("out_trade_no", $orderInfo['out_trade_no'])->where("goodscoupon_id", ">",0)->whereNotNull("split_order")->find();
            
            if(!$orderM || empty($orderM->split_order))
            {
                return;
            }

            $splitOrders = array_filter(explode(",", $orderM->split_order));

            if(count($splitOrders) <= 1)
                return;

            $out_trade_no = '';
            Db::startTrans();
            foreach($splitOrders as $splitOrderId)
            {
                if(empty($splitOrderId))
                    continue;
                $splitOrder = OrderModel::find($splitOrderId);
                $splitOrder->member_id = $orderM->member_id;
                $splitOrder->out_trade_no = $orderM->out_trade_no;  //支付流水有在未付款订单支付时会发生变更,但拆分的订单支付流水没有跟着变更，需要统一
                $splitOrder->save();
                OrderGoodsModel::where("order_no", $splitOrder->order_no)->update(["order_id"=>$splitOrder->order_id]);
                $out_trade_no = $splitOrder->out_trade_no;
            }
            $orderM->delete();
            Db::commit();
        }
        catch (\Throwable $e)
        {
            Db::rollback();
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
        }
    }
}