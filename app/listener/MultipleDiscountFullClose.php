<?php


namespace app\listener;


use addon\multipleDiscount\api\controller\MultipleDiscount;
use addon\multipleDiscount\constant\MULTIPLE_DISCOUNT_STATUS;
use addon\multipleDiscount\model\MultipleDiscountAction;
use addon\multipleDiscount\model\MultipleDiscountModel;
use addon\multipleDiscount\model\OrderMultipleDiscountModel;
use app\Domain\Services\WeappNotice\SendOpenPintuanNoticeService;
use app\model\order\OrderModel;
use think\facade\Db;

class MultipleDiscountFullClose
{
    public function __construct()
    {

    }

    public function handle(array $orderInfo)
    {
        $orders = OrderModel::where("out_trade_no", $orderInfo['out_trade_no'])->where("order_status", ">=", 1)->select();

        $multipleDiscountIds  =[];
        foreach($orders as $order)
        {
            foreach($order->multipleDiscounts as $multipleDiscount)
            {
                $multipleDiscountIds[] = $multipleDiscount->multiple_discount_id;
            }
        }
        $multipleDiscountIds = array_unique($multipleDiscountIds);

        foreach($multipleDiscountIds as $multipleDiscountId)
        {
            $mDiscount = MultipleDiscountModel::where("status", MULTIPLE_DISCOUNT_STATUS::START)->find($multipleDiscountId);
            $useCounts = OrderMultipleDiscountModel::where("multiple_discount_id", $multipleDiscountId)->count();
            if($mDiscount->max_fetch != 0 && $useCounts >= $mDiscount->max_fetch)
                (new MultipleDiscountAction($mDiscount))->end();
        }
    }
}