<?php

declare (strict_types=1);

namespace app\command;

use app\model\order\Config as ConfigModel;
use app\model\order\OrderCommon;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class OrderConfirm extends Command
{
    const CACHE_KEY = 'CRON_ORDER_CONFIRM';

    protected function configure()
    {
        // 指令配置
        $this->setName('orderConfirm')
            ->addArgument('order_id', Argument::OPTIONAL, "订单id", '')
            ->addArgument('order_status', Argument::OPTIONAL, "订单状态", '')
            ->setDescription('订单自动确认收货');
    }

    protected function execute(Input $input, Output $output)
    {
        
        set_time_limit(0);
//        Log::info('订单自动确认收货开始');
        if(cache(self::CACHE_KEY)){
//            Log::info('订单自动确认收货并发');
            cache(self::CACHE_KEY, null);
            return;
        }
        cache(self::CACHE_KEY, 1);

        //获取输入参数
        $order_id       = trim($input->getArgument('order_id'));
        $order_status   = trim($input->getArgument('order_status'));
        // 指定订单id
        if ($order_id) {

            if( $order_status ){
            
                $this->doEvent($order_id, $order_status);
            }else {
                $this->doEvent($order_id);
            }
            cache(self::CACHE_KEY, null);
            $output->writeln('订单自动确认收货：'. $order_id);
            return;
        }

        //获取自动关闭订单的时间(分钟)，0表示不自动关闭订单
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig();
        $confirmTime = (int)($order_event_time_config['data']['value']['auto_confirm'] ?? 0);
        $completeTime = (int)($order_event_time_config['data']['value']['auto_complete'] ?? 0);

        if(!$confirmTime){
            cache(self::CACHE_KEY, null);
            return;
        }

        // 更新订单状态
        $orderCommon = new OrderCommon();
        $lastConfirmTime = strtotime(date('Y-m-d H:i:s').' -'.$confirmTime.' day');
        $completeTime = strtotime(date('Y-m-d H:i:s').' -'.$completeTime.' day');
        Db::name('order')
            // ->whereIn('order_status', [$orderCommon::ORDER_DELIVERY, $orderCommon::ORDER_TAKE_DELIVERY])
            ->where('order_status','=' ,$orderCommon::ORDER_DELIVERY)
            ->where('delivery_time', '<', $lastConfirmTime)
            ->where('is_lock', '=', 0)
            ->chunk(
                50,
                function ($orders) {
                    foreach ($orders as $order) {
                        $this->doEvent($order['order_id'], $order['order_status']);
                    }
                }
            );
        Db::name('order')
            ->where('order_status','=' ,$orderCommon::ORDER_TAKE_DELIVERY)
            ->where('sign_time', '<', $completeTime)
            ->where('is_lock', '=', 0)
            ->chunk(
                50,
                function ($orders) {
                    foreach ($orders as $order) {
                        $this->doEvent($order['order_id'], $order['order_status']);
                    }
                }
            );
        cache(self::CACHE_KEY, null);
        return;
        // 指令输出
//    	$output->writeln('订单自动确认收货：'. $order_id);
    }

    public function doEvent($order_id, $order_status=3){
        try {
//            Log::info('订单自动确认收货：'.$order_id);
            if( $order_status==3 ){
            
                event('CronOrderTakeDelivery', ['relate_id' => $order_id]);
            }elseif( $order_status==4 ){
            
                event('CronOrderComplete', ['relate_id' => $order_id]);
            }
        } catch (\Exception $exception) {
            Log::error(
                '订单自动确认收货失败-----order_id:'.$order_id.'-----'.$exception->getFile(
                ).'-----'.$exception->getLine().'-----'.$exception->getMessage()
            );
        }
    }
}
