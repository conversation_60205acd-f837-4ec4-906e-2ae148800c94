<?php


namespace app\command;


use addon\leaguePoints\domainModel\LeagueTaskPointActivityRepository;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use Carbon\Carbon;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\Db;

class SendLeaguePointsForSaleTask extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('SendLeaguePointsForSaleTask')
            ->setDescription('发放月度销售任务贡献值');
    }

    protected function execute(Input $input, Output $output)
    {
        $activityRepository = new LeagueTaskPointActivityRepository();
        $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();
        foreach ($taskKeys as $taskKey)
        {
            $activity = $activityRepository->findTaskByCid(0, $taskKey);
            if($activity->isSaleTaskEnable())
            {
                $this->process();
            }
        }
    }

    public function getTaskOrderData($leagueTaskKey)
    {
        $nowTime = Carbon::now()->timestamp;
        $month = Carbon::now()->format('Y-m');
        $monthStart = Carbon::parse($month)->timestamp;
        $monthEnd = Carbon::parse($month)->addMonth()->timestamp - 1;

        $orderList = Db::name('league_task_sale_task_goods')
            ->alias('tg')
            ->join('order_goods og', 'og.goods_id=tg.goods_id')
            ->join('order o', 'o.order_id=og.order_id')
            ->where(function ($query) use ($nowTime){
                $query->where('status', 1)->whereOr('disable_start_time', ">", $nowTime);
            })
            ->where('o.pay_status', 1)
            ->whereBetween('pay_time',[$monthStart, $monthEnd])
            ->field('o.order_id,o.order_status,og.goods_id,og.num,tg.month_sales,reward_points')
            ->select();



        foreach ($orderList as $order)
        {
            //$order['']
        }
    }
}