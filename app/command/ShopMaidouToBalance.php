<?php

declare(strict_types=1);

namespace app\command;

use app\service\Youli\YouliService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

class ShopMaidouToBalance extends Command
{
    const CACHE_KEY = 'CONSOLE_SHOP_MAIDOU_TO_BALANCE';
    protected function configure()
    {
        // 指令配置
        $this->setName('shopMaidouToBalance')
            ->setDescription('the app\command\shopMaidouToBalance command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 指令输出
        $output->writeln('shopMaidouToBalance start');
        Log::info('shopMaidouToBalance start');

        set_time_limit(0);
        // Cache::delete(self::CACHE_KEY);
        if (Cache::get(self::CACHE_KEY)) {
            $output->writeln("店主迈豆转余额正在进行中，请勿重复操作");
            return;
        }
        Cache::set(self::CACHE_KEY, 1);

        try {
            $where = [
                ['youli_withdraw', '>', 0],
                ['is_vip', '=', 1]
            ];
            Db::name("shop")->where($where)->chunk(500, function ($data) {
                foreach ($data as $row) {
                    // if ($row['is_vip'] == 0) continue;   //0元店主 
                    if ($row['vip_expired_time'] < time()) continue; //暂时过期不转
                    $res = YouliService::getInstance()->youliToBalance($row['site_id'], $row['youli_withdraw']);
                    if($res['code'] < 0) {
                        Log::error("店主迈豆转余额异常：店主id(" .$row['site_id'].')');
                    }
                }
            });
            Cache::delete(self::CACHE_KEY);
        } catch (\Exception $e) {
            Cache::delete(self::CACHE_KEY);

            $output->writeln("店主迈豆转余额异常：");
            $output->writeln($e->getMessage());
            $output->writeln($e->getTraceAsString());
            Log::error("店主迈豆转余额异常：" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        }
        Log::info('shopMaidouToBalance end');
        $output->writeln('shopMaidouToBalance end');
       
    }
}
