/**
 * 渲染订单列表
 */
Order = function(){};

/**
 * 设置数据集
 */
Order.prototype.setData = function(data){
    Order.prototype.data = data;
};

/**
 * 列名数据
 */
Order.prototype.cols = [
    /*
    {
        title : '<span style="margin-left:10px;" class="ns-line-hiding" title="商品信息">商品信息</span>',
        width : "25%",
        className : "product-info",
        template : function(orderitem,order){

            var h = '<div class="img-block" >';
            h += '<img layer-src src="'+ ns.img(orderitem.sku_image) +'">';
            h += '</div>';
            h += '<div class="info">';
            h += '<a href="javascript:;" class="ns-multi-line-hiding" title="'+ orderitem.sku_name +'">' + orderitem.sku_name + '</a>';
            h += '<p>'+ orderitem.goods_class_name +'</p>';
            if(orderitem.refund_status_name != ''){
                h += '<br/><a href="'+ ns.url("admin/refund/detail",{order_goods_id:orderitem.order_goods_id})+'"  target="_blank" style="color: #ff0052;">'+orderitem.refund_status_name+'</a>&nbsp;&nbsp;';
            }
            h += '</div>';
            return h;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="单价/数量">单价/数量</span>',
        width : "11%",
        align : "right",
        className : "order-price",
        template : function(orderitem,order){
            var h = '<div style="padding-right: 15px;">';
            h += '<span class="ns-line-hiding" title="￥'+ orderitem.price + '\/' + orderitem.num +'">￥' + orderitem.price + '\/' + orderitem.num + '</span>';
            h += '</div>';
            return h;
        }
    },
    {
        title : '<p class="ns-line-hiding" title="实付金额">实付金额</p>',
        width : "11%",
        align : "right",
        className : "order-money",
        merge : true,
        template : function(orderitem,order){
            var h = '<div style="padding-right: 15px;">';
            h += '<span class="ns-line-hiding" title="￥'+ order.order_money +'">￥' + order.order_money + '</span>';
            h += '</div>';
            return h;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="收货信息">收货信息</span>',
        width : "12%",
        align : "left",
        className : "buyers",
        merge : true,
        template : function(orderitem,order){
            var h = '<p>';
            h += '<span class="ns-line-hiding" title="'+ order.mobile +'">' + order.mobile + '</span>';
            h += '</p>';
            if(order.order_type != 4){
                h += '<p>';
                h += '<a href="javascript:;" class="ns-line-hiding" title="'+ order.name +'">' + order.name + '</a>';
                h += '</p>';
                h += '<span class="ns-line-hiding" title="' + order.full_address + order.address + '">' + order.full_address + order.address + '</span>';
            }
            return h;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="商家名称">商家名称</span>',
        width : "10%",
        align : "center",
        className : "shop-info",
        merge : true,
        template : function(orderitem,order){
            var h = '<div>';
            h += '<span class="ns-line-hiding" title="'+ order.site_name +'">' + order.site_name + '</span>';
            h += '</div>';
            return h;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="交易状态">交易状态</span>',
        width : "11%",
        align : "center",
        className : "transaction-status",
        merge : true,
        template : function(orderitem,order){
            var html = '<div>' + order.order_status_name + '</div>';
            html += '<div>' + order.promotion_type_name;
            html += order.promotion_status_name != '' ? '(' + order.promotion_status_name + ')' : '';
            html += '</div>';
            return html;
        }
    },*/
    // {
    //     title : '<span class="ns-line-hiding" title="下单时间">下单时间</span>',
    //     width : "11%",
    //     align : "center",
    //     className : "create-time",
    //     merge : true,
    //     template : function(orderitem,order){
    //         return '<div class="ns-line-hiding" title="'+ ns.time_to_date(order.create_time) +'">' + ns.time_to_date(order.create_time) + '</div>';
    //     }
    // },
    // {
    //     title : "结算状态",
    //     width : "10%",
    //     align : "center",
    //     className : "settlement",
    //     merge : true,
    //     template : function(orderitem,order){
    //         var settlement_name = order.is_settlement == 1 ? "已结算" : "待结算";
    //         return '<div>'+settlement_name+'</div>';
    //     }
    // },
    /*
    {
        title : "操作",
        width : "9%",
        align : "left",
        className : "operation",
        merge : true,
        template : function(orderitem,order){
            var url = "admin/order/detail";
            var html = '<div class="ns-table-btn">';
            html += '<a href="'+ns.url(url,{order_id:order.order_id})+'" class="layui-btn ns-line-hiding" target="_blank">查看详情</a>';//默认存在
			html += '</div>';
            return html;

        }
    }*/

    {
        type: 'checkbox',
        fixed: 'left',
        width: '4%',
        merge: true,
        template: function (orderitem, order) {
            var json = {}
            json.order_id = order.order_id;
            //json.order_no = order.order_no;
            //json.full_address = order.full_address;

            var h = '<div class="sub-selected-checkbox" data-json='+ JSON.stringify(json) +'>';
            h += '<input type="checkbox" lay-skin="primary" lay-filter="subCheckbox" name="">';
            h += '</div>';
            return h;
        }
    },
    {
        title : '<span style="margin-left:10px;" class="ns-line-hiding" title="商品名称">商品名称</span>',
        width : "15%",
        align: 'left',
        className : "product-info",
        template : function(orderitem,order){
            return orderitem.sku_name;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="单价/数量">单价/数量</span>',
        width : "10%",
        align : "right",
        className : "order-price",
        template : function(orderitem,order){
            var h = '<div style="padding-right: 15px;">';
            h += '<span class="ns-line-hiding" title="￥'+ orderitem.price + '\/' + orderitem.num +'">￥' + orderitem.price + '\/' + orderitem.num + '</span>';
            h += '</div>';
            return h;
        }
    },
    {
        title : '<span class="ns-line-hiding" title="优惠">总优惠</span>',
        width : "8%",
        align: "center",
        template : function(orderitem,order){
            return (parseFloat(orderitem.promotion_money) + parseFloat(orderitem.goodscoupon_money) + parseFloat(orderitem.multiple_discount_money)).toFixed(2);
        }
    },
    {
        title : '<span class="ns-line-hiding" title="运费">运费</span>',
        width : "8%",
        align: "center",
        template : function(orderitem,order){
            return order.delivery_money;
        }
    },
    {
        title : '<p class="ns-line-hiding" title="实付金额">实付金额</p>',
        width : "8%",
        align: "center",
        template : function(orderitem,order){
            if(order.pay_type == 'MAIDOU'){
                let maidou = orderitem.real_goods_money * 100;
                return parseInt(maidou) + "迈豆";
            }else{
                // return (orderitem.goods_money - orderitem.promotion_money - orderitem.multiple_discount_money - orderitem.goodscoupon_money).toFixed(2);
                var html = '<div>' + orderitem.real_pay_money + '</div>';
                if(parseFloat(orderitem.real_pay_money) < parseFloat(orderitem.cost_money)) html += '<div style="background:orangered;color: white">价格异常</div>';
                return html;
            }
        }
    },
    {
        title : '<p class="ns-line-hiding" title="流水号">流水号</p>',
        width : "8%",
        align: "center",
        template : function(orderitem,order){
            return '<a target="_blank" href="'+ ns.url("paymentconfirm://admin/PaymentConfirm/list", {"out_trade_no": order.out_trade_no}) + '"> ' + order.out_trade_no + ' </a>'
        }
    },
    {
        title : '<span class="ns-line-hiding" title="收货信息">收货人信息</span>',
        width : "15%",
        align : "left",
        className : "buyers",
        merge : true,
        template : function(orderitem,order){
            var h = '';
            if(order.order_type != 4){
                h += '<p><span class="ns-line-hiding" title="'+ order.name +'">' + order.name + '</span></p>';
            }
            h += '<p><span class="ns-line-hiding" title="'+ order.mobile +'">' + order.mobile + '</span></p>';
            if(order.order_type != 4){
                h += '<p><span class="ns-line-hiding" title="' + order.full_address + order.address + '">' + order.full_address + order.address + '</span></p>';
            }
            return h;
        }
    },
    {
        title: '<span class="ns-line-hiding" title="交易状态">订单状态</span>',
        width: "10%",
        align: "center",
        className: "transaction-status",
        merge: true,
        template: function (orderitem, order) {
            var html = '<div>' + order.order_status_name + '</div>';
            html += '<div>' + order.promotion_type_name;
            html += order.promotion_status_name != '' ? '(' + order.promotion_status_name + ')' : '';
            html += '</div>';
            if(order.sync_text != null) html += '<div style="color:#CE0000">' + order.sync_text+'</div>';
            return html;
        }
    },
    {
        title : "操作",
        width : "18%",
        align : "left",
        //className : "operation",
        merge : true,
        template : function(orderitem,order){
            var url = "admin/order/detail";
            var html = '<div class="ns-table-btn">';
            html += '<a href="'+ns.url(url,{order_id:order.order_id})+'" class="layui-btn ns-line-hiding" target="_blank">查看</a>';//默认存在
            if (order.pay_status == 1) {
                // 没同步到供应链的订单操作
                if (order.sync_status != 2) {
                    // 待发货允许手动同步
                    if (order.order_status == 1) {
                        html += '<a class="layui-btn ns-line-hiding" onclick="orderHandSync(' + order.order_id + ')">手动同步</a>';
                    }
                }
                // 待发货、已发货 允许提交物流
                if ([1, 3].indexOf(order.order_status) !== -1) {
                    let delivery_text = order.order_status == 1 ? '发货' : '修改物流';
                    html += '<a class="layui-btn ns-line-hiding" onclick="orderDelivery(' + order.order_id + ')">' + delivery_text + '</a>';
                }
            }

            let order_create_type = [1,3,6,7],
                order_status = [1,3,4];
            /* if(order.pay_status == 1 && order_status.indexOf(order.order_status) > -1 &&  order_create_type.indexOf(order.order_create_type) > -1){
                html += '<a class="layui-btn ns-line-hiding" onclick="orderComplain('+order.order_id+')">申请售后</a>';
            } */

            if(order.pay_status == 1 && order_create_type.indexOf(order.order_create_type) > -1 ){
                if(  (order.sync_status == 2 && order.order_status == 1) ||  (order.sync_status != 2 && order_status.indexOf(order.order_status) > -1) )
                html += '<a class="layui-btn ns-line-hiding" onclick="orderComplain('+order.order_id+')">申请售后</a>';
            }
            html += '<a class="layui-btn ns-line-hiding" onclick="sellerRemark('+order.order_id+',\'seller\')">备注</a>';
            html += '<a class="layui-btn ns-line-hiding" onclick="sellerRemark('+order.order_id+',\'admin\')">管理员备注</a>';
            html += '</div>';
            return html;
        }
    },



];

/**
 * 渲染表头
 */
Order.prototype.header = function(hasThead){
    var colgroup = '<colgroup>';
    var thead = '<thead><tr>';

    for(var i=0;i<this.cols.length;i++){
        var align = this.cols[i].align ? "text-align:" + this.cols[i].align : "";

        /*
        colgroup += '<col width="' + this.cols[i].width + '">';
        thead += '<th style="' + align + '" class="' + (this.cols[i].className || "") + '">';
        thead += '<div class="layui-table-cell">' + this.cols[i].title + '</div>';
        thead += '</th>';
        */

        colgroup += '<col width="' + this.cols[i].width + '">';
        if (hasThead) {
            thead += '<th style="' + align + '" class="' + (this.cols[i].className || "") + '">';
            thead += '<div class="layui-table-cell">';
            if(this.cols[i].type){
                thead += '<div class="all-selected-checkbox">';
                thead += '<input type="checkbox" lay-skin="primary" lay-filter="allCheckbox" name="">';
                thead += '</div>';
            }else{
                thead +=  this.cols[i].title;
            }
            thead += '</div>';
            thead += '</th>';
        }

    }
    colgroup += '</colgroup>';
    /*thead += '</tr></thead>';*/
    if (hasThead) thead += '</tr></thead>';
    return colgroup + thead;
};

/**
 * 渲染内容
 */
Order.prototype.tbody = function(){

    var tbody = '<tbody>';
    for(var i=0;i<this.data.list.length;i++){

        var order = this.data.list[i];
        var orderitemList = order.order_goods;
        let payment_method = [];
        order.pay_type_name && payment_method.push(order.pay_type_name)
        order.app_type_name && payment_method.push(order.app_type_name)
        payment_method = payment_method.join('-')

        var total_discount = (parseFloat(order.promotion_money) + parseFloat(order.goodscoupon_money) + parseFloat(order.multiple_discount_money)).toFixed(2)
        //分割行
        // tbody += '<tr class="separation-row">';
        // tbody += '<td colspan="' + this.cols.length + '"></td>';
        // tbody += '</tr>';

        //订单项头部

        tbody += '<tr class="separation-row"><td colspan="10"></td></tr>';
        tbody += '<tr class="header-row">';
        tbody += '<td colspan="10">';
        tbody += '<span class="order-item-header" style="margin-right:50px;">订单编号：' + order.order_no + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">会员ID：' + order.member_id + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">会员手机号：' + order.member_mobile + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">店手机号：' + order.shop_phone + '</span>';
        //tbody += '<span class="order-item-header" style="margin-right:50px;">供应商：' + order.supplier_title + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">供应商：' + order.supply_shop_name + '</span>';
        //tbody += '<span class="order-item-header" style="margin-right:50px;">订单总金额(元)：' + order.order_money + '</span>';
        //tbody += '<span class="order-item-header" style="margin-right:50px;">订单总运费(元)：' + order.delivery_money + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">订单总优惠(元)：' + total_discount +'</span>';
		tbody += '<span class="order-item-header" style="margin-right:50px;">下单时间：' + ns.time_to_date(order.create_time) + '</span>';
        //tbody += '<span class="order-item-header" style="margin-right:50px;">订单类型：' + order.order_type_name + '</span>';

        if(order.pay_status == 1) tbody += '<span class="order-item-header;" style="margin-right:50px;">支付方式：' + payment_method + '</span>';

        if(order.operate_group_name == null)
            order.operate_group_name = '无';
        tbody += '<span class="order-item-header" style="margin-right:50px;">运营组：' + order.operate_group_name + '</span>';
        tbody += '<span class="order-item-header" style="margin-right:50px;">用户组别：' + order.xm_group_name + '</span>';


        if(order.order_create_type == 2){ //周期购
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：周期购订单</span>';
            tbody += '<span class="order-item-header" style="margin-right:50px;">预计发货时间：'+ ns.time_to_date(order.refund_time) +'</span>';
        }else if(order.order_create_type == 3){ //秒杀
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：秒杀订单</span>';
        }else if(order.order_create_type == 4){ //分享赚
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：分享赚订单</span>';
        }else if(order.order_create_type == 5){ //砍价
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：砍价订单</span>';
        }else if(order.order_create_type == 6){ //迈豆
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：迈豆订单</span>';
        }else if(order.order_create_type == 7){ //新人专享
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：新人专享</span>';
        }else if(order.order_create_type == 8){ //拼团订单
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;color: dodgerblue;">订单类型：拼团订单</span>';
        }else{ //普通订单
            tbody += '<br/><span class="order-item-header" style="margin-right:50px;">订单类型：普通订单</span>';
        }

        if(order.live_info)
        {
            tbody += '<span class="order-item-header" style="margin-right:50px;">订单来源：小程序直播</span>';
            tbody += '<span class="order-item-header" style="margin-right:50px;">直播间：' + order.live_info + '</span>';
        }

        if(order.sync_status == 2){
            tbody += '<span class="order-item-header" style="margin-right:50px;color: dodgerblue;">供应链单号：'+order.supply_order_no+'</span>';
        }
        tbody += '</td>';
        tbody += '</tr>';

        var orderitemHtml = '';
		loadImgMagnify();
        for(var j=0;j<orderitemList.length;j++){

            var orderitem = orderitemList[j];
            orderitemHtml += '<tr class="content-row">';
            for(var k=0;k<this.cols.length;k++){

                if(j == 0 && this.cols[k].merge && this.cols[k].template){

                    orderitemHtml += '<td class="' + (this.cols[k].className || "") + '" align="' + (this.cols[k].align || "") + '" style="' + (this.cols[k].style || "") + '" rowspan="' + orderitemList.length + '">';
                    orderitemHtml += this.cols[k].template(orderitem,order);
                    orderitemHtml += '</td>';

                }else if(this.cols[k].template && !this.cols[k].merge){

                    orderitemHtml += '<td class="' + (this.cols[k].className || "") + '" align="' + (this.cols[k].align || "") + '" style="' + (this.cols[k].style || "") + '">';
                    orderitemHtml += this.cols[k].template(orderitem,order);
                    orderitemHtml += '</td>';

                }
            }
            orderitemHtml += '</tr>';
        }
        tbody += orderitemHtml;

        if (order.remark != '') {
            tbody += '<tr class="remark-row">';
            tbody += '<td colspan="' + this.cols.length + '">商家备注：' + order.remark + '</td>';
            tbody += '</tr>';
        }
    }

    tbody += '</tbody>';
    return tbody;
};

/**
 * 渲染表格
 */
/*
Order.prototype.fetch = function(){
    if(this.data.list.length > 0){
        return '<table class="layui-table order-list-table layui-form">' + this.header(true) + this.tbody() + '</table>';
    }else{
        return '<table class="layui-table order-list-table layui-form">' + this.header(true) + '</table>'+'<div class="order-no-data-block"><ul><li><i class="layui-icon layui-icon-tabs"></i> </li><li>暂无订单</li></ul></div>';
    }
};*/
Order.prototype.fetch = function () {
    if (this.data.list.length > 0) {
        return '<table class="layui-table layui-form">' + this.header(true) + '</table><table class="layui-table order-list-table layui-form">' + this.header(false) + this.tbody() + '</table>';
    } else {
        return '<table class="layui-table order-list-table layui-form">' + this.header(true) + '</table>' + '<div class="order-no-data-block"><ul><li><i class="layui-icon layui-icon-tabs"></i> </li><li>暂无订单</li></ul></div>';
    }
};
