<?php

namespace app\admin\controller;

// 客服管理

use app\model\wxCustomerService\DataType\CUSTOMER_SERVICE_EVALUATE;
use app\service\enterpriseWx\EnterpriseCustomerStatService;
use app\service\WxCustomerService\WxCustomerServiceOrder;
use Carbon\Carbon;

class ServiceManage extends BaseAdmin
{
    // 客服接待记录
    public function receptionRecordList()
    {
        $yesterdayStart = Carbon::yesterday()->timestamp;
        $yesterdayEnd = Carbon::yesterday()->addDay()->timestamp - 1;

        $statData['kf_reception'] = EnterpriseCustomerStatService::statKfReceptionTimeRange($yesterdayStart, $yesterdayEnd);
        $statData['robot_reception'] = EnterpriseCustomerStatService::statRobotReceptionTimeRange($yesterdayStart, $yesterdayEnd);
        $statData['evaluate_good'] = EnterpriseCustomerStatService::statEvaluateTimeRange(CUSTOMER_SERVICE_EVALUATE::$SATISFIED, $yesterdayStart, $yesterdayEnd);
        $statData['evaluate_normal'] =  EnterpriseCustomerStatService::statEvaluateTimeRange(CUSTOMER_SERVICE_EVALUATE::$GENERAL, $yesterdayStart, $yesterdayEnd);;
        $statData['evaluate_bad'] =  EnterpriseCustomerStatService::statEvaluateTimeRange(CUSTOMER_SERVICE_EVALUATE::$DISSATISFIED, $yesterdayStart, $yesterdayEnd);;

        $this->assign('statData', $statData);
        return $this->fetch('service_manage/reception_record_list');
    }
    // 客服类型管理
    public function typeManage()
    {
        return $this->fetch('service_manage/type_manage');
    }
    // 话术管理
    public function discourseManage()
    {
        return $this->fetch('service_manage/discourse_manage');
    }
    public function customerServiceConfiguration()
    {
        return $this->fetch('service_manage/customer_service_configuration');
    }
    public function serviceOrderList()
    {
        $start_time = Carbon::yesterday()->timestamp;
        $end_time = Carbon::today()->timestamp - 1;
        $statistics_data = (new WxCustomerServiceOrder())->serviceOrderStatistics($start_time,$end_time);
        $statistics_str = "昨日：企微接待用户{$statistics_data['reception_count']}位，新建工单{$statistics_data['new_service_order_count']}，完结工单{$statistics_data['end_service_order_count']}";
        return $this->fetch('service_manage/service_order_list',["statistics_str"=>$statistics_str]);
    }
    public function serviceOnlineList()
    {
        return $this->fetch('service_manage/service_online_list');
    }
}