<?php
/**
 * APP平台渠道
 */

namespace app\admin\controller;


use app\model\app\AppPlatformChannel;

class AppChannel extends BaseAdmin
{
    /**
     * 渠道列表
     * @return array|mixed
     */
    public function lists()
    {
        $app_platform_channel_model = new AppPlatformChannel();
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);

            return $app_platform_channel_model->getAppChannelPageList([], $page, $page_size);
        }
        return $this->fetch('app_channel/lists');
    }


    /**
     * 添加渠道
     * @return array|mixed
     */
    public function addChannel()
    {
        $app_platform_channel_model = new AppPlatformChannel();
        if (request()->isAjax()) {
            $type = input('type', 2);
            $code = input('code', '');
            $name = input('name', '');

            $data = [
                'type' => $type,
                'code' => $code,
                'name' => $name,
                'create_time' => time(),
            ];

            $res = $app_platform_channel_model->addAppChannel($data);
            if($res['code'] == 0){
                $this->addLog('添加app渠道id：'.$res['data']);
            }
            return $res;
        }
        return $this->fetch('app_channel/add');
    }

    /**
     * 编辑渠道
     * @return array|mixed
     */
    public function editChannel()
    {
        $app_platform_channel_model = new AppPlatformChannel();

        $channel_id = input('channel_id', 0);
        $info = $app_platform_channel_model->getInfo(['id'=>$channel_id]);

        if (request()->isAjax()) {
            $type = input('type', 2);
            $code = input('code', '');
            $name = input('name', '');

            $data = [
                'type' => $type,
                'code' => $code,
                'name' => $name,
                'update_time' => time(),
            ];

            $res =  $app_platform_channel_model->editAppChannel($data, ['id' => $channel_id]);
            if($res['code'] == 0){
                $log_data = [$info['data'], $data];
                $this->addLog('编辑app渠道id：'.$channel_id, $log_data);
            }
            return $res;
        }

        $this->assign('info', $info['data']);

        return $this->fetch('app_channel/edit');
    }
}