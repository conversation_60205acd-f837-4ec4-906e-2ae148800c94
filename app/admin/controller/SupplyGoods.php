<?php

/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\admin\controller;

use addon\supply\model\Supplier as SupplierModel;
use app\event\GoodsUpEvent;
use app\Frame\Model\Admin\WeappGoods;
use app\model\express\ExpressTemplate as ExpressTemplateModel;
use app\model\goods\Goods as GoodsModel;
use app\model\goods\Config as GoodsConfigModel;
use app\model\goods\GoodsAttribute as GoodsAttributeModel;
use app\model\goods\GoodsBrand as GoodsBrandModel;
use app\model\goods\GoodsCategory as GoodsCategoryModel;
use app\model\goods\GoodsEvaluate;
use app\model\goods\GoodsPriceChangeLogModel;
use app\model\goods\GoodsShopCategory as GoodsShopCategoryModel;
use app\model\message\Message;
use app\model\SupplyChain\GoodsAbnormal;
use app\Request;
use app\service\goods\GoodsSyncService;
use app\service\supplychaincategory\CategoryService;
use app\service\goods\GoodsService;
use app\service\goods\SupplyChainAbnormalService;
use app\service\goods\SupplyChainGoodsService;
use app\service\shop\ShopGoodsService;
use app\service\supply\SupplyService;
use app\service\SupplyChain\Factory;
use app\service\Tag\Tag;
use extend\Poster;
use think\facade\Cache;
use think\facade\Console;
use think\facade\Db;
use think\facade\Db as FacadeDb;
use think\facade\Log;
use think\Model;

/**
 * 供应链管理 控制器
 */
class SupplyGoods extends BaseAdmin
{

    public $goods_attr_class = 4;
    public $goods_attr_name = '供应链';
    /**
     * 已选商品列表
     *
     * @return void
     */
    public function lists(){
		$goods_model = new GoodsModel();
        // $abnormal = new SupplyChainAbnormalService();
        // $abnormal->check(1934);
		if (request()->isAjax()) {
            $page_index = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$search_text = input('search_text', "");
			$search_text_type = input('search_text_type', "goods_name");
			$goods_state = input('goods_state', "");
			$verify_state = input('verify_state', "");
			$category_id = input('category_id', "");
			$brand_id = input('goods_brand', '');
			$goods_attr_class = input("goods_attr_class", "");
//			$site_id = input("site_id", "");
			$goods_class = input('goods_class', "");
            $goods_id = input('goods_id', "");
            $supplier_name = input('supplier_name', "");
            $order_field = input('field', '');// 排序字段
            $order_asc = input('order', '');// desc = 倒序 asc = 顺序
            $goods_tag_id = input('goods_tag_id', 0);// 商品标签id，对应xm_tag
            $is_broadcast = input('is_broadcast',0);
            $is_league = input('is_league',0);
            $spu = input('spu',0); // 商品货号

			$condition = [['is_delete', '=', 0]];
			if (!empty($search_text)) {
				$condition[] = [$search_text_type, 'like', '%' . sqlEncode($search_text) . '%'];
			}

            if (!empty($supplier_name)) {
                //$condition[] = ['site_name', 'like', '%' . sqlEncode($supplier_name) . '%'];
                $condition[] = function($query) use ($supplier_name){

                    $map1 = [
                        ['site_name', "like", '%' . sqlEncode($supplier_name) . '%'],
                    ];
                    $map2 = [
                        ['title', 'like', '%玖富_%'],
                        ['username', 'like', '%' . sqlEncode($supplier_name) . '%'],
                    ];
                    $query->whereOr([ $map1, $map2 ]);
                };
            }

            if (!empty($goods_id)) {
                $condition[] = [ 'goods.goods_id', '=', $goods_id ];
            }

			if ($goods_class !== "") {
				$condition[] = ['goods_class', '=', $goods_class];
			}
			if ($goods_state !== '') {
				$condition[] = ['goods_state', '=', $goods_state];
			}
			if ($verify_state !== '') {
				$condition[] = ['verify_state', '=', $verify_state];
			}
			if (!empty($category_id)) {
				$condition[] = ['category_id|category_id_1|category_id_2|category_id_3', '=', $category_id];
			}
			if ($brand_id) {
				$condition[] = ['brand_id', '=', $brand_id];
			}
			if ($goods_attr_class) {
				$condition[] = ['goods_attr_class', '=', $goods_attr_class];
			}
            if ($spu = trim($spu)) {
                $condition[] = function ($query) use ($spu) {
                    $map1 = [
                        ['sn', "=", $spu],
                    ];

                    $map2 = [
                        ['goods.goods_id', "in", Db::raw(Db::name("goods_sku")
                            ->alias("sku")
                            ->where("sku.sku_no", 'like', '%' . sqlEncode('_' . $spu))
                            ->field("sku.goods_id")
                            ->buildSql())],
                    ];

                    $query->whereOr([$map1, $map2]);
                };
            }

            if($order_field != '') {
                $order = $order_field . ' ' . $order_asc;
            } else {
                $order = 'goods.create_time desc';
            }

            // 直播商品筛选
            if (!empty($is_broadcast)){
                $broadcast_goods_sql = (new WeappGoods())->field('distinct self_goods_id')->buildSql();
                $condition[] = ['', 'exp', Db::raw("goods.goods_id ".(($is_broadcast==1) ? ' in ':' not in').$broadcast_goods_sql)];
            }

            if(!empty($is_league))
            {
                $no_league_goods_sql = Db::name("promotion_topic")
                    ->alias("pt")
                    ->join("promotion_topic_goods ptg", "pt.topic_id=ptg.topic_id")
                    ->join("goods_sku gs", "ptg.sku_id=gs.sku_id")
                    ->where("pt.is_league", 0)
                    ->group("gs.goods_id")
                    ->field("gs.goods_id")
                    ->buildSql();

                $condition[] = ['', 'exp', Db::raw("goods.goods_id ".(($is_league==2) ? ' in ':' not in').$no_league_goods_sql)];
            }

//			if (!empty($site_id)) {
//				$condition[] = ['site_id', '=', $site_id];
//			}
            // $join = ['supply_chain_goods_relation'];
            $join = [
                ['supply_chain_goods_relation r','r.goods_id=goods.goods_id','inner'],
                ['goods_abnormal ab','ab.goods_id=goods.goods_id','left'],
                ['supplier s','s.supplier_id = goods.site_id','left']
            ];
            $condition[] = ['r.delete_time','=',0];
            $condition[] = ['','exp',Db::Raw('(ab.abnormal_id is null)')];
            $field = 'goods.goods_id,goods_name,category_name,site_id,site_name,brand_name,goods_image,is_own,goods_state,verify_state,price,market_price,cost_price,goods_stock,sale_num,is_virtual,goods_class,is_fenxiao,fenxiao_type,sku_id,goods.reward_shop_rate,reward_company_rate,virtual_sale_num,s.title,s.username,sn,sort';
            // 兼容商品标签搜索
            if (!empty($goods_tag_id)) {
                $join[] = ['tag_config_relation tcr', 'tcr.relation_id=goods.goods_id', 'left'];
                $condition[] = ['module', '=', 'goods'];
                $condition[] = ['tag_id', '=', $goods_tag_id];
            }
            $field .= ',goods.create_time';

            $res = $goods_model->getGoodsPageList($condition, $page_index, $page_size,$order, $field, $join);
            // $res['data']['list'] = [];
            // 扣掉售后中的销量

            if($res['data']['list']){
                foreach ($res['data']['list'] as $key => $value) {
                    $res['data']['list'][$key]['cost_price'] = floatval($value['cost_price']);
                    $res['data']['list'][$key]['reward_company_rate'] = floatval($value['reward_company_rate']);
                    $res['data']['list'][$key]['price'] = floatval($value['price']);
                    $res['data']['list'][$key]['market_price'] = floatval($value['market_price']);
                    $res['data']['list'][$key]['sale_price'] = floatval(sprintf('%.2f',($value['price']*(1+$value['reward_shop_rate']*0.01))));
                    $res['data']['list'][$key]['reward_shop'] = floatval(sprintf('%.2f',($value['price']*$value['reward_shop_rate']*0.01)));


                     // 商品标签
                     $res['data']['list'][$key]['tag'] = Tag::relation_id($value['goods_id'], 'goods', 'tag');
                     // 针对迈豆专区商品特殊处理
                     if ($res['data']['list'][$key]['tag']->where('key', 'maidou')->count()) {
                         $res['data']['list'][$key]['reward_shop'] = 0;
                         $res['data']['list'][$key]['reward_shop_rate'] = 0;
                     }

                    $res['data']['list'][$key]['site_name'] = SupplyService::getInstance()->supplierChainFormat($value);

                }
            } else {
                if (!empty($spu)) {
                    return error(-1, SupplyChainGoodsService::checkProductLocation($spu)['format_text']);
                }
            }
            return $res;

        }else{
            $goods_category_model = new GoodsCategoryModel();
            $condition = [
                [ 'pid', '=', 0 ]
            ];
            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate,reward_company_rate');
            $goods_category_list = $goods_category_list[ 'data' ];

            $this->assign("goods_category_list", $goods_category_list);
            $this->assign("goods_tags", $goods_model->getGoodsTags());
            $this->assign('is_refreshing', Cache::get(GoodsSyncService::$CACHE_ABNORMAL_LOCK_KEY, 0) == 1);
            if($lastRefreshInfo = GoodsSyncService::getLastRefreshInfo('检测供应链选品库中的已选商品异常%')){
                $this->assign('last_refresh_information', $lastRefreshInfo['text']);
            }
			return $this->fetch('supply_goods/lists');
        }
        // echo '已选商品';
    }

    /**
     * 编辑商品
     * @return mixed
     */
    public function editGoods()
    {
        $goods_model = new GoodsModel();
        $goods_id = input("goods_id", 0);
        $goods_info = $goods_model->getGoodsInfo([ [ 'goods_id', '=', $goods_id ]]);
        $goods_info = $goods_info[ 'data' ];
        if (request()->isAjax()) {
            $goods_id = input("goods_id", 0);// 商品id
            $goods_name = input("goods_name", "");// 商品名称
            $goods_attr_class = input("goods_attr_class", $this->goods_attr_class);// 商品类型id
            $goods_attr_name = input("goods_attr_name", $this->goods_attr_name);// 商品类型名称
            $category_id = input("category_id", 0);// 分类id
            $category_id_1 = input("category_id_1", 0);// 一级分类id
            $category_id_2 = input("category_id_2", 0);// 二级分类id
            $category_id_3 = input("category_id_3", 0);// 三级分类id
            $category_name = input("category_name", "");// 所属分类名称
            $commission_rate = input("commission_rate", 0);// 分佣比率(按照分类)
            $brand_id = input("brand_id", 0);// 品牌id
            $brand_name = input("brand_name", "");// 所属品牌名称
            $goods_shop_category_ids = input("goods_shop_category_ids", "");// 店内分类id,逗号隔开
            $goods_image = input("goods_image", "");// 商品主图路径
            $goods_content = input("goods_content", "");// 商品详情
            $goods_state = input("goods_state", "");// 商品状态（1.正常0下架）
            $goods_stock = input("goods_stock", 0);// 商品库存（总和）
            $goods_stock_alarm = input("goods_stock_alarm", 0);// 库存预警
            $is_free_shipping = input("is_free_shipping", 1);// 是否免邮
            $shipping_template = input("shipping_template", 0);// 指定运费模板
            $goods_spec_format = input("goods_spec_format", "");// 商品规格格式
            $goods_attr_format = input("goods_attr_format", "");// 商品属性格式
            $introduction = input("introduction", "");// 促销语
            $keywords = input("keywords", "");// 关键词
            $unit = input("unit", "");// 单位
            $sort = input("sort", 0);// 排序
            $video_url = input("video_url", "");// 视频
            $goods_sku_data = input("goods_sku_data", "");// SKU商品数据
            $supplier_id = input("supplier_id", "");// 供应商id
            $supplier_name = input("supplier_name", "");// 供应商名称
            $reward_shop_rate = input("reward_shop_rate", "");// 店主返佣比例
            $reward_company_rate = input("reward_company_rate", 0);// 公司返佣比例
            $tag = input('tag', 0);   //迈豆专区
            $virtual_sale_num = input('virtual_sale_num', 0); //虚拟销量
            $use_pay_type = input('use_pay_type', ""); //支付渠道
            $goods_special = input('goods_special', 0); //是否活动商品
            $goods_invite = input('goods_invite', 0); //是否邀请商品
            $tag_ids = input('tag_ids', '');
            $tag_status = input('tag_status', 0);//标签查询方式
            $goods_invite_tags = [];

            if($goods_invite == 1 && !empty($tag_ids)){
                $goods_invite_tags = ['tag_ids' => $tag_ids, 'tag_status' => $tag_status];
            }

            if ($goods_info['xm_id'] > 0) {
                // return error(-1, '先迈同步的数据，不可编辑');
            }

            if($goods_info['goods_state'] == 1){
                return error(-1, '商品为上架状态，不可编辑');
            }

            $taginfo = $goods_model->getAllTags(['id' => $tag], true);
            if(!empty($taginfo) && $taginfo['key'] == 'maidou'){
                $reward_shop_rate = 0;
            }else{
                if($reward_shop_rate < 0){
                    return error(-1, '店主返佣比例设置错误');
                }
            }
            /* if($reward_shop_rate == 0 || $reward_shop_rate > 99){
                return error(-1, '店主返佣比例设置错误');
            } */

            if($reward_company_rate <= 0 || $reward_company_rate >= 100){
                // return error(-1, '公司返佣比例设置错误');
            }

            $sku_data = json_decode($goods_sku_data, true);
            if($sku_data){
                foreach ($sku_data as $key => $val){
                    if($val['market_price'] <= $val['price']){
                        return error(-1, '原价必须大于供货价');
                    }
                }
            }

            //单规格需要
            $price = input("price", 0);// 商品价格（取第一个sku）
            $market_price = input("market_price", 0);// 市场价格（取第一个sku）
            $cost_price = input("cost_price", 0);// 成本价（取第一个sku）
            $sku_no = input("sku_no", "");// 商品sku编码
            $weight = input("weight", "");// 重量
            $volume = input("volume", "");// 体积
            // 更新前的goods数据
            $old_goods_obj = \app\model\goods\GoodsModel::find($goods_id);

            $data = [
                'goods_id' => $goods_id,
                'goods_name' => $goods_name,
                'goods_attr_class' => $goods_attr_class,
                'goods_attr_name' => $goods_attr_name,
                'site_id' => $supplier_id,
                'category_id' => $category_id,
                'category_id_1' => $category_id_1,
                'category_id_2' => $category_id_2,
                'category_id_3' => $category_id_3,
                'category_name' => $category_name,
                'brand_id' => $brand_id,
                'brand_name' => $brand_name,
                'goods_image' => $goods_image,
                'goods_content' => $goods_content,
                'goods_state' => $goods_state,
                'price' => $price,
                'market_price' => $market_price,
                'cost_price' => $cost_price,
                'sku_no' => $sku_no,
                'weight' => $weight,
                'volume' => $volume,
                'goods_stock' => $goods_stock,
                'goods_stock_alarm' => $goods_stock_alarm,
                'is_free_shipping' => $is_free_shipping,
                'shipping_template' => $shipping_template,
                'goods_spec_format' => $goods_spec_format,
                'goods_attr_format' => $goods_attr_format,
                'introduction' => $introduction,
                'keywords' => $keywords,
                'unit' => $unit,
                'sort' => $sort,
                'commission_rate' => $commission_rate,
                'video_url' => $video_url,
                'goods_sku_data' => $goods_sku_data,
                'goods_shop_category_ids' => $goods_shop_category_ids,
                'supplier_id' => $supplier_id,
                'supplier_name' => $supplier_name,
                'reward_shop_rate' => $reward_shop_rate,
                'reward_company_rate' => $reward_company_rate,
                'tag' => $tag,
                'virtual_sale_num' => $virtual_sale_num,
                'use_pay_type' => $use_pay_type,
                'is_special' => $goods_special,
                'goods_invite_tags'=> empty($goods_invite_tags) ? '' : json_encode($goods_invite_tags),
            ];
            $res = $goods_model->editGoods($data);

            // 上架时,删除异常商品
            if ($goods_state==1){
                // 异常删除商品
                $del_where = ['goods_id' => $goods_id, 'type' => [1, 4]];
                SupplyChainAbnormalService::getInstance()->deleteAbnormal($del_where);
                event(new GoodsUpEvent($old_goods_obj));
            }

            $log_data = [$goods_info, $data];
            $this->addLog("编辑商品id:".$goods_id, $log_data);
            return $res;
        } else {

            $goods_sku_list = $goods_model->getGoodsSkuList([ [ 'goods_id', '=', $goods_id ], [ 'is_delete', '=', 0 ]], "sku_id,sku_name,sku_no,sku_spec_format,price,market_price,cost_price,stock,weight,volume,sku_image,sku_images,goods_spec_format,spec_name,goods_state,reward_shop_rate,reward_company_rate", "sku_name asc,sku_id asc");
            $goods_sku_list = $goods_sku_list[ 'data' ];
            $goods_sku_list = $this->sortSkuList($goods_info['goods_spec_format'] , $goods_sku_list);
            $goods_info[ 'sku_list' ] = $goods_sku_list;

            // 供应商产品信息
            $goods_info['goods_spec_format']=='[]' && $goods_info['goods_spec_format'] = '';
            $supply_goods = SupplyChainGoodsService::getInstance()->productByLocal($goods_info['goods_id']);

            $goods_info['supply_goods_name'] = $supply_goods['title'] ?? '';
            $goods_info['supply_pro_no'] = $supply_goods['pro_no'] ?? '';
            $goods_info['goods_invite'] = 0;
            if(!empty($goods_info['goods_invite_tags'])){
                try {
                    $goods_invite_tags = json_decode($goods_info['goods_invite_tags'],true);
                    if(!empty($goods_invite_tags['tag_ids'])){
                        $goods_info['goods_invite'] = 1;
                        $this->assign('tag_ids',$goods_invite_tags['tag_ids']);
                        $this->assign('tag_status',$goods_invite_tags['tag_status']);
                    }
                }catch (Exception $e) {}
            }
            $this->assign("goods_info", $goods_info);

            //获取一级商品分类
            $goods_category_model = new GoodsCategoryModel();
            $condition = [
                [ 'pid', '=', 0 ]
            ];

            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate,reward_company_rate');
            $goods_category_list = $goods_category_list[ 'data' ];
            $this->assign("goods_category_list", $goods_category_list);

            //获取品牌;
            $goods_brand_model = new GoodsBrandModel();
            $brand_list = $goods_brand_model->getBrandList([], "brand_id, brand_name");
            $brand_list = $brand_list[ 'data' ];
            $this->assign("brand_list", $brand_list);

            //获取店内分类
            $goods_shop_category_model = new GoodsShopCategoryModel();
            $goods_shop_category_list = $goods_shop_category_model->getShopCategoryTree([], 'category_id,category_name,pid,level');
            $goods_shop_category_list = $goods_shop_category_list[ 'data' ];
            $this->assign("goods_shop_category_list", $goods_shop_category_list);

            //获取运费模板
            $express_template_model = new ExpressTemplateModel();
            $express_template_list = $express_template_model->getExpressTemplateList([], 'template_id,template_name', 'is_default desc');
            $express_template_list = $express_template_list[ 'data' ];
            $this->assign("express_template_list", $express_template_list);

            //获取商品类型
            $goods_attr_model = new GoodsAttributeModel();
            $attr_class_list = $goods_attr_model->getAttrClassList([], 'class_id,class_name');
            $attr_class_list = $attr_class_list[ 'data' ];
            $this->assign("attr_class_list", $attr_class_list);

            //获取特殊分区
            $tagList = $goods_model->getAllTags([['key', 'in', ['maidou', 'newhand', 'excellent','subsidized']], ['enable', '=', 1]]);
            $this->assign("tag_list", $tagList);

            //获取商品所属特殊分区
            $goodsTag = $goods_model->getGoodsTag($goods_id);
            $this->assign("goods_tag", $goodsTag);

            $is_install_supply = addon_is_exit("supply");
            if ($is_install_supply) {
                $supplier_model = new SupplierModel();
                // $supplier_list = $supplier_model->getSupplierPageList([], 1, PAGE_LIST_ROWS, 'supplier_id desc');
                $supplier_list = $supplier_model->getSupplierList([], 'supplier_id desc');
                // $supplier_list = $supplier_list[ 'data' ][ 'list' ];
                $supplier_list = $supplier_list[ 'data' ];
                $this->assign("supplier_list", $supplier_list);
            }
            $this->assign("is_install_supply", $is_install_supply);

            $changeLogs = GoodsPriceChangeLogModel::where("goods_id", $goods_id)->order("id", "desc")->limit(10)->select();
            $this->assign("change_logs", $changeLogs);

            return $this->fetch("supply_goods/edit_goods");
        }
    }

     /**
     * 商品详情
     * @return mixed
     */
    public function show()
    {
        $goods_model = new GoodsModel();
        $goods_id = input("goods_id", 0);
        $goods_info = $goods_model->getGoodsInfo([ [ 'goods_id', '=', $goods_id ]]);
        $goods_info = $goods_info[ 'data' ];
        // 供应商产品信息
        $goods_info['goods_spec_format']=='[]' && $goods_info['goods_spec_format'] = '';
        $supply_goods = SupplyChainGoodsService::getInstance()->productByLocal($goods_info['goods_id']);

        $goods_info['supply_goods_name'] = $supply_goods['title'] ?? '';
        $goods_info['supply_pro_no'] = $supply_goods['pro_no'] ?? '';

        $goods_sku_list = $goods_model->getGoodsSkuList([ [ 'goods_id', '=', $goods_id ], [ 'is_delete', '=', 0 ]], "sku_id,sku_name,sku_no,sku_spec_format,price,market_price,cost_price,stock,weight,volume,sku_image,sku_images,goods_spec_format,spec_name,goods_state,reward_shop_rate,reward_company_rate", "sku_name asc,sku_id asc");
        $goods_sku_list = $goods_sku_list[ 'data' ];
        $goods_sku_list = $this->sortSkuList($goods_info['goods_spec_format'] , $goods_sku_list);
        $goods_info[ 'sku_list' ] = $goods_sku_list;
        $goods_info['goods_invite'] = 0;
        if(!empty($goods_info['goods_invite_tags'])){
            try {
                $goods_invite_tags = json_decode($goods_info['goods_invite_tags'],true);
                if(!empty($goods_invite_tags['tag_ids'])){
                    $goods_info['goods_invite'] = 1;
                    $this->assign('tag_ids',$goods_invite_tags['tag_ids']);
                    $this->assign('tag_status',$goods_invite_tags['tag_status']);
                }
            }catch (Exception $e) {}
        }
        $this->assign("goods_info", $goods_info);

        //获取一级商品分类
        $goods_category_model = new GoodsCategoryModel();
        $condition = [
            [ 'pid', '=', 0 ]
        ];

        $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate');
        $goods_category_list = $goods_category_list[ 'data' ];
        $this->assign("goods_category_list", $goods_category_list);

        //获取品牌;
        $goods_brand_model = new GoodsBrandModel();
        $brand_list = $goods_brand_model->getBrandList([], "brand_id, brand_name");
        $brand_list = $brand_list[ 'data' ];
        $this->assign("brand_list", $brand_list);

        //获取店内分类
        $goods_shop_category_model = new GoodsShopCategoryModel();
        $goods_shop_category_list = $goods_shop_category_model->getShopCategoryTree([], 'category_id,category_name,pid,level');
        $goods_shop_category_list = $goods_shop_category_list[ 'data' ];
        $this->assign("goods_shop_category_list", $goods_shop_category_list);

        //获取运费模板
        $express_template_model = new ExpressTemplateModel();
        $express_template_list = $express_template_model->getExpressTemplateList([], 'template_id,template_name', 'is_default desc');
        $express_template_list = $express_template_list[ 'data' ];
        $this->assign("express_template_list", $express_template_list);

        //获取商品类型
        $goods_attr_model = new GoodsAttributeModel();
        $attr_class_list = $goods_attr_model->getAttrClassList([], 'class_id,class_name');
        $attr_class_list = $attr_class_list[ 'data' ];
        $this->assign("attr_class_list", $attr_class_list);

        //获取特殊分区
        $tagList = $goods_model->getAllTags([['key', 'in', ['maidou', 'newhand', 'excellent','subsidized']], ['enable', '=', 1]]);
        $this->assign("tag_list", $tagList);

        //获取商品所属特殊分区
        $goodsTag = $goods_model->getGoodsTag($goods_id);
        $this->assign("goods_tag", $goodsTag);

        $is_install_supply = addon_is_exit("supply");
        if ($is_install_supply) {
            $supplier_model = new SupplierModel();
            // $supplier_list = $supplier_model->getSupplierPageList([], 1, PAGE_LIST_ROWS, 'supplier_id desc');
            $supplier_list = $supplier_model->getSupplierList([], 'supplier_id desc');
            // $supplier_list = $supplier_list[ 'data' ][ 'list' ];
            $supplier_list = $supplier_list[ 'data' ];
            $this->assign("supplier_list", $supplier_list);
        }
        $this->assign("is_install_supply", $is_install_supply);

        $changeLogs = GoodsPriceChangeLogModel::where("goods_id", $goods_id)->order("id", "desc")->limit(10)->select();
        $this->assign("change_logs", $changeLogs);

        return $this->fetch("supply_goods/show");
    }

    /**
     * 批量修改商品分类
     *
     * @return void
     */
    public function changCategory(){
        if(request()->isAjax()){
            $category_id = input('category_id',0);
            $category_id_1 = input('category_id_1',0);
            $category_id_2 = input('category_id_2',0);
            $category_id_3 = input('category_id_3',0);
            $category_name = input("category_name", "");// 所属分类名称
            $goods_ids = input('goods_ids','');
            if(empty($goods_ids)){
                return error(-1, '请选择操作商品');
            }
            if($category_id == 0){
                return error(-1, '请选择分类');
            }
            $goods_model = new GoodsModel();
            $param = [
                'category_id' => $category_id,
                'category_id_1' => $category_id_1,
                'category_id_2' => $category_id_2,
                'category_id_3' => $category_id_3,
                'category_name' => $category_name,
                'goods_ids' => $goods_ids
            ];
            $res = $goods_model->batchChangeCategory($param);
            return $res;
        }else{
            return '别乱来';
        }
    }

    /**
     * 上/下架商品
     */
    public function editStatus()
    {
        $goods_ids = input('goods_ids', '');
        $goods_state = input('goods_state', '');
        $goods_service = new GoodsService();
        $res = $goods_service->setGoodsState($goods_ids, $goods_state, 'supply');
        $str = $goods_state ? '上架商品id:' : '下架商品id:';
        $this->addLog($str . $goods_ids);

        if($goods_state == 1)
        {
            $goods_ids = explode(",", $goods_ids);
            foreach ($goods_ids as $goods_id)
                $goods_service->checkPriceChange($goods_id);
        }
        return $res;
    }


    /**
     * 修改店主比例
     *
     * @return void
     */
    public function changeShopReward(){
        $goods_ids = input('goods_ids','');
        $reward_shop_rate = input('reward_shop_rate', 0);
        if(empty($goods_ids)){
            return error(-1, '请选择操作商品');
        }

        if($reward_shop_rate < 0){
            return error(-1, '佣金比例为必须大于0');
        }
        if(request()->isAjax()){
            $param = [
                'goods_ids' => $goods_ids,
                'reward_shop_rate' => $reward_shop_rate
            ];
            $goods_model = new GoodsModel();
            $res = $goods_model->batchChangeShopRewardRate($param);
            return $res;
        }

    }

    /**
     * 修改店主比例
     *
     * @return void
     */
    public function changeCompanyReward(){
        $goods_ids = input('goods_ids','');
        $reward_company_rate = input('reward_company_rate', 0);
        if(empty($goods_ids)){
            return error(-1, '请选择操作商品');
        }

        if($reward_company_rate < 0){
            return error(-1, '佣金比例为必须大于0');
        }
        if(request()->isAjax()){
            try{
                $param = [
                    'goods_ids' => $goods_ids,
                    'reward_company_rate' => $reward_company_rate
                ];
                $goods_model = new GoodsModel();
                $res = $goods_model->batchChangeCompanyRewardRate($param);
                return $res;
            }catch(\Exception $e){
                dd($e->getMessage());
            }

        }

    }

    /**
     * 异常列表
     *
     * @return void
     */
    public function abnormal(){
        if(Request()->isAjax()){
            $page_index = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
			$search_text = input('search_text', "");
            $abnormal_type = input('abnormal_type','');
            $start_date = input('reg_time_start','');
            $end_date = input('reg_time_end','');
            $spu_no = trim(input('spu_no', ""));

            $condition = [
                ['g.is_delete', '=', 0],
                ['ga.delete_time', '=', 0]
            ];
            if (!empty($search_text)) {
				$condition[] = ['g.goods_name', 'like', '%' . sqlEncode($search_text) . '%'];
			}

            if (!empty($abnormal_type)) {
				$condition[] = ['ga.type', '=', $abnormal_type];
			}

            if (!empty($spu_no)) {
				$condition[] = ['g.sn', 'like', '%' . sqlEncode($spu_no) . '%'];
			}

            if ($start_date != '' && $end_date != '') {
                $condition[] = ['abnormal_time', 'between', [$start_date, $end_date]];
            } else if ($start_date != '' && $end_date == '') {
                $condition[] = ['abnormal_time', '>=', $start_date];
            } else if ($start_date == '' && $end_date != '') {
                $condition[] = ['abnormal_time', '<=', $end_date];
            }

            $field = 'g.goods_name,g.sn,g.goods_image,g.goods_state,ga.*';
            $join = [
                ['goods g','g.goods_id = ga.goods_id','inner']
            ];
            $field .= ',scgr.relation_time';
            $join[] = ['supply_chain_goods_relation scgr','scgr.goods_id = ga.goods_id','inner'];

            $alias = 'ga';
            $order = 'abnormal_time desc';
            $res = SupplyChainAbnormalService::getInstance()->getAbnormalPageList($condition, $page_index, $page_size, $order, $field, $alias, $join);
            $res['data']['list'] = SupplyChainAbnormalService::getInstance()->formatAbnormalList($res['data']['list']);
            if (count($res['data']['list']) === 0 && !empty($spu_no)) {
                return error(-1, SupplyChainGoodsService::checkProductLocation($spu_no)['format_text']);
            }
            return $res;
        }else{
            //异常状态
            $abnormalTypeList = SupplyChainAbnormalService::getInstance()->getAbnormalTypeList();
            $this->assign('abnormalTypeList', $abnormalTypeList);
            return $this->fetch('supply_goods/abnormal_lists');
        }
    }

    /**
     * 异常商品上架
     *
     * @return void
     */
    public function onGoods(){

        $abnormal_id = input('abnormal_id',0);
        $goods_state = input('goods_state', 1);
        if($abnormal_id < 0){
            return error(-1,'参数错误');
        }

        $abnormal = model('goods_abnormal')->getInfo([['abnormal_id','=',$abnormal_id]],'*');
        $goods_ids = (string)$abnormal['goods_id'];
        $goods_service = new GoodsService();
        $res = $goods_service->setSupplyGoodsState($goods_ids, $goods_state, $abnormal_id);

        $str = $goods_state ? '上架商品id:' : '下架商品id:';
        $this->addLog($str . $goods_ids);
        return $res;
    }

    public function deleteAbnormal(){
        $abnormal_id = input('abnormal_id',0);
        if($abnormal_id < 0){
            return error(-1,'参数错误');
        }

        $param = array(
            'abnormal_id' => $abnormal_id
        );
        $res = SupplyChainAbnormalService::getInstance()->delete($param);
        $this->addLog('删除供应链异常商品:'. $abnormal_id);
        return $res;
    }

    public function batchDeleteAbnormal(){
        try {
            $abnormal_ids = input('abnormal_ids/s','');
            throw_if(empty($abnormal_ids), 'Exception', '参数错误');
            $abnormal_ids = explode(',', $abnormal_ids);
            foreach ($abnormal_ids as $abnormal_id) {
                $param = array(
                    'abnormal_id' => $abnormal_id
                );
                $res = SupplyChainAbnormalService::getInstance()->delete($param);
                if ($res['code']!=0){
                    $this->addLog('批量删除供应链异常商品失败:'. $abnormal_id, $res);
                }
            }
            $this->addLog('批量删除供应链异常商品', $abnormal_ids);
            return $res;

        } catch (\Exception $e) {
            return error(-1,$e->getMessage());
        }

    }

    /**
     * 待选商品列表
     * @return mixed
     */
    public function waitingLists(){
        if (request()->isAjax()) {

            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $search['keyword'] = input('keyword', "");
            $search['pro_id'] = input('pro_id', "");
            $search['sku_no'] = trim(input('sku_no', ""));
            $search['brand_cid'] = input('brand_id', "");
            $search['cid'] = input('category_id', "");

            $order['field'] = input('field', "");
            $order['order'] = input('order', "asc");
            $up_start_time = input('up_start_time', "");
            $up_end_time = input('up_end_time', "");

            $order = empty($order['field']) ? '' : implode(' ', $order);

            if(!empty($up_start_time))
                $search['up_start_time'] = $up_start_time." 00:00:00";
            if(!empty($up_end_time))
                $search['up_end_time'] = $up_end_time." 23:59:59";
            elseif(isset($search['up_start_time']) && empty($up_end_time))
                $search['up_end_time'] = date("Y-m-d H:i:s", time());

            $data = SupplyChainGoodsService::getInstance()->productList($search,$page,$page_size, $order);
            return $data;
            $product = Factory::Product();
            $res = $product->SearchNew($search);
            $data = [];
            if ($res->getCode()!=200){
                $data = ['code'=>-1,'msg'=>$res->getMsg(),'data'=>['list'=>[],'count'=>0,'page_count'=>0]];
            }else{
                $list = $res->getBody()['data'];
                $list = ['list'=>$list['list'],'count'=>100*$list['total'],'page_count'=>$list['total']];
                $data = ['code'=>0,'data'=>$list,'msg'=>$res->getMsg()];
            }

            return $data;
        }else{
            //获取一级商品分类
            $goods_category_model = new GoodsCategoryModel();
            $condition = [
                [ 'pid', '=', 0 ]
            ];

            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate,reward_company_rate');
            $goods_category_list = $goods_category_list[ 'data' ];
            $this->assign("goods_category_list", $goods_category_list);
            $this->assign('is_refreshing', Cache::get(GoodsSyncService::$CACHE_REFRESH_LOCK_KEY, 0) == 1);
            if($lastRefreshInfo = GoodsSyncService::getLastRefreshInfo()){
                $this->assign('last_refresh_information', $lastRefreshInfo['text']);
            }
            return $this->fetch('supply_goods/waiting_lists');
        }
    }

    /***
     * 待选商品详情
     * @return mixed
     */
    public function waitingInfo(){
        $proNo = input('proNo','');
        if (empty($proNo)) return $this->redirect('waitingLists');
        $product = SupplyChainGoodsService::getInstance()->productInfo($proNo);
        if ($product['code']!=0) return $this->error("供应链异常：{$product['message']}");
        $this->assign('info',$product['data']);
        return $this->fetch('supply_goods/waiting_show');
    }

    /**
     * 供应链分类
     * @return array
     */
    public function supplyChainCate(){
        $pid = input('pid',-1);
        $level = input('level',0);
        $type = input('type',1);

        $data = CategoryService::getInstance()->supplyChainCate($type,(int)$pid,(int)$level);

        return success(0,'succ',$data) ;
    }

    /**
     * 刷新选品库
     * @return array
     */
    public function supplyChainReload()
    {
        $goodsSyncService = new GoodsSyncService();
        $msg = $goodsSyncService->setRefreshStatus();
        $this->addLog('刷新供应链选品库->同步中', ['msg'=>$msg]);
        return success(0, $msg);
//        $num = $goodsSyncService->refresh();
//        return success(0, "成功同步到{$num}件商品", $num);
    }

    /**
     * 批量检测异常商品
     * @return array
     */
    public function batchCheckAbnormal()
    {
        if(Cache::get(GoodsSyncService::$CACHE_ABNORMAL_LOCK_KEY) == 1){
            return error(-1,'有异常商品检测任务在运行，请稍后再试！');
        }
        Cache::delete(GoodsSyncService::$CACHE_ABNORMAL_LOCK_KEY);
        Console::call('supplyGoodsAbnormal',['1',json_encode($this->user_info)]);
        return success(0, "检测完成");
    }

    /**
     * 供应链选品
     * @return array
     */
    public function syncGoods()
    {
        try{
            $param['reward_shop_rate'] = input('reward_shop_rate',0);
            $param['reward_company_rate'] = input('reward_company_rate',0);
            $param['category_name'] = input('category_name','');
            $param['category_id'] = input('category_id',0);
            $param['category_id_1'] = input('category_id_1',0);
            $param['category_id_2'] = input('category_id_2',0);
            $param['category_id_3'] = input('category_id_3',0);
            $param['category_name'] = input("category_name", "");// 所属分类名称
            $param['pro_no'] = input('goods_ids','');
            $param['pro_no'] = explode(',', $param['pro_no']);

            if (empty($param['category_id'])) throw new \Exception('商品分类不能为空！');

            if (empty($param['pro_no'])) throw new \Exception('请选择商品！');
            if ($param['reward_company_rate']<0) throw new \Exception('公司返佣比例必须大于0');

            // 同步商品
            $goodsSyncService = new GoodsSyncService();
            $res = $goodsSyncService->supplyChain($param['pro_no'],(int)$param['category_id'],(float)$param['reward_shop_rate'],(float)$param['reward_company_rate']);

            if (empty($res['fail'])){
                return success(0,$res['success'][0]['message']?? '选品完成',$res);
            }else{
                return error(-1,$res['fail'][0]['message'] ?? '选品失败',$res);
            }

        } catch (\Exception $e) {
            return error(-1, $e->getMessage());
        }
    }

    /**
	 * 获取商品分类
	 * @return \multitype
	 */
	public function getCategoryByParent()
	{
		$pid = input('pid', 0); // 上级id
        $level = input('level', 0); // 层级
        $category_str = input('category_str','336,354,392');

        // if($pid == 0) $pid = -1;  //供应链那边-1
        $data = CategoryService::getInstance()->supplyChainCate(1, -1, 0, true);
        $list = CategoryService::getInstance()->getSelectList($data, $level, $category_str);
		return $list;
    }
     // 供应链已选品--编辑之前再同步一次最新供应链商品信息
    public function selectedGoodsReload(){
        $goods_id = input('goods_id', 0);
        $re = GoodsService::getInstance()->reloadAbnormalGoods($goods_id);
        return $re;
    }

    public function abnormalGoodsReload(){
        $goods_id = input('goods_id', 0);
        $re = GoodsService::getInstance()->reloadAbnormalGoods($goods_id);
        return $re;
    }


    public function sortSkuList($goods_spec_format, $sku_list){
        if(!is_array($goods_spec_format)){
            $goods_spec_format = json_decode($goods_spec_format, true);
        }
        if(empty($goods_spec_format)) return $sku_list;
        $array = [];
        foreach($goods_spec_format as $spec_list){
            $temp = [];
            foreach($spec_list['value'] as $spec){
                $temp[] = $spec['spec_value_id'];
            }
            $array[] = $temp;
        }
        $zuhe_array = $this->combination($array, count($array));

        $sku_list_result = [];
        foreach($zuhe_array as $zuhe){
            if(is_array($zuhe)) $zuhe_str = implode('_',$zuhe);
            else $zuhe_str= $zuhe;
            foreach($sku_list as $sku){
                $sku_spec_format = json_decode($sku['sku_spec_format'] ,true);
                if (empty($sku_spec_format)) {
                    continue;
                }
                $spec_id_str = implode('_',array_column($sku_spec_format, 'spec_value_id'));
                if($zuhe_str == $spec_id_str){
                    $sku_list_result[] = $sku;
                    continue 2;
                } 
            }
        }

        return $sku_list_result;
    }


    /**
     * 数组排列组合
     *
     * @param array $arr
     * @return array
     */
    public function combination(array $arr)
    {
        $num = count($arr);
        if ($num === 0) return false;
        if ($num === 1) return $arr[0];
        while(count($arr) > 1) {
            $arr_first = array_shift($arr);
            $arr_second = array_shift($arr);
            $c = array();
            foreach ($arr_first as $v) {
                $v = (array) $v;
                foreach ($arr_second as $val) {
                    $c[] = array_merge($v, (array) $val);
                }
            }
            array_unshift($arr, $c);
            unset($c);
        }
        return $arr[0];
    }
}
