<?php


namespace app\admin\controller;

use \app\model\president\President as President<PERSON><PERSON><PERSON>;
use think\facade\Config;
use app\service\member\MemberService;

class President extends BaseAdmin
{
    /**
     * 列表
     * @return array|mixed
     */
    public function lists()
    {
        $president_model = new PresidentModel();
        if(request()->isAjax()){
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $id = input('id', 0);
            $username = input('username', '');
            $contact = input('contact', '');
            $condition = [];
            if(!empty($id)) $condition[] = ['id', '=', $id];
            if(!empty($username)) $condition[] = ['username', 'like', '%' . sqlEncode($username) . '%'];
            if(!empty($contact)) $condition[] = ['contact', '=' , $contact];


            return $president_model->getPresidentPageList($condition, $page, $page_size);
        }
        return $this->fetch('president/president_lists');
    }


    /**
     * 添加会长
     * @return array|mixed
     */
    public function add(){
        if(request()->isAjax()){
            $president_model = new PresidentModel();
            $param = [
                "username" => input("username",''),
                "sex" => input("sex",''),
                "age" => input("age",''),
                "contact" => input("contact",''),
                "wx_number" => input("wx_number",''),
                "team_num" => input("team_num",''),
                "member_no" => input("member_no",''),
//                "qr_code" => input("qr_code",''),
            ];
            return $president_model->addPresident($param);
        }else{
            return $this->fetch('president/add');
        }
    }


    /**
     * 编辑会长
     * @return array|mixed
     */
    public function edit(){
        if(request()->isAjax()){
            $president_model = new PresidentModel();
            $id = input('id',0);
            $param = [
                "username" => input("username",''),
                "sex" => input("sex",''),
                "age" => input("age",''),
                "contact" => input("contact",''),
                "wx_number" => input("wx_number",''),
                "member_no" => input("member_no",''),
                "status" => input("status",''),
//                "qr_code" => input("qr_code",''),
            ];
            return $president_model->editPresident($param, $id);
        }else{
            $id = input('id', 0);
            $condition[] = ['id','=',$id];
            $info = model('president')->getInfo($condition);
            $this->assign('info', $info ?? []);
            return $this->fetch('president/edit');
        }
    }

    /**
     * 详情
     * @return mixed
     */
    public function show(){
        $id = input('id', 0);
        $condition[] = ['id','=',$id];

        $president_model = new PresidentModel();
        $info = $president_model->getPresidentDetail($id);


        $this->assign('info', $info ?? []);
        return $this->fetch('president/show');
    }

    /**
     * [orderList 销售订单管理]
     * <AUTHOR>
     * @DateTime 2020-10-12T10:11:15+0800
     * @return   [type]                   [description]
     */
    public function orderMng()
    {
        $presidentId = input("presidentId", 0);//会长id
        $this->assign('presidentId', $presidentId);
        return $this->fetch('president/orderList');
        
    }

    /**
     * [orderList 销售订单]
     * <AUTHOR>
     * @DateTime 2020-10-12T10:16:30+0800
     * @return   [type]                   [description]
     */
    public function orderList()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $presidentId = input('presidentId', 0);//会长id
            $search_text = input('search_text', 0);//h关键字查询
            $condition = [];
            $condition[] = ["order_status", "=", 10];
            if($presidentId > 0){
                $condition[] = ["president_id", "=", $presidentId];
            }
            if(!empty($search_text)){
                $condition[] = [ 'order_no', '=', $search_text];
            }
            $field = 'order_id,order_no,site_name, order_name, order_money, pay_money, balance_money, order_type_name, order_status_name, create_time,site_id';
            // $join = [
            //     ['xm_shop s', 's.site_id = site_id', 'inner'],//订单与店铺关系表
            // ];
            $join = [];
            $res = MemberService::getInstance()->memberOrderList($condition, $page, $page_size, "create_time desc", $field,$join);
            return $res;
        }
    }
}