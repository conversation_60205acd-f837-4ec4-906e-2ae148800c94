<?php
/**
 * 周期购订单
 */

namespace app\admin\controller;


use app\model\periodBuy\PeriodBuyOrder as PeriodBuyOrderModel;
use app\model\order\OrderCommon as OrderCommonModel;
use think\facade\Config;

class PeriodBuyOrder extends BaseAdmin
{
    /**
     * 快递订单列表
     */
    public function lists()
    {

        $out_trade_no = input("out_trade_no",'');
        $member_id = input("member_id", '');
        $mobile = input("mobile",'');
        $goods_name = input("goods_name", '');
        $site_name = input("site_name", '');
        $pay_status = input("pay_status", '');
        $supply_shop_id = input("supply_shop_id",'');

        $order_common_model = new OrderCommonModel();
        $period_buy_order_model = new PeriodBuyOrderModel();
        if (request()->isAjax()) {
            $page_index = input('page', 1);
            $page_size = input('limit', PAGE_LIST_ROWS);
            $condition = [
//                ["order_type", "=", 1]
            ];

            //订单内容 模糊查询
            if ($goods_name != "") {
                $condition[] = ["g.goods_name", 'like', '%'.sqlEncode($goods_name).'%'];
            }
            //订单支付
            if ($pay_status != "") {
                $condition[] = ["a.pay_status", "=", $pay_status];
            }
            if(!empty($member_id))
            {
                $condition[] = ["m.member_id", '=', $member_id];
            }

            if ($mobile != "") {
                $condition[] = ['m.mobile', '=', $mobile];
            }
            if ($site_name != "") {
                $condition[] = ['p.site_name', 'like', '%'.sqlEncode($site_name).'%'];
            }
            if ($out_trade_no != "") {
                $condition[] = ['a.out_trade_no', '=', $out_trade_no];
            }
            if ($supply_shop_id != "") {
                $condition[] = ["g.supplier_id", "=", $supply_shop_id];
            }
            $join = [
                ['member m', 'a.member_id = m.member_id', 'left'],
                ['goods g', 'a.goods_id = g.goods_id', 'left'],
                ['shop p', 'a.shop_id = p.site_id', 'left'],
                ['supplier s', 'g.supplier_id = s.supplier_id', 'left'],
            ];
            $field = 'a.*,g.goods_name,m.mobile as member_mobile,p.site_name,s.title as supplier_title';
            //
            $list = $period_buy_order_model->getOrderPageList($condition, $page_index, $page_size, "a.create_time desc", $field, $alias = 'a', $join);
            return $list;
        }
        //订单来源 (支持端口)
        $order_from = Config::get("app_type");
        $this->assign('order_from_list', $order_from);

        $pay_type = $order_common_model->getPayType();
        $this->assign("pay_type_list", $pay_type);

        $order_type_list = $order_common_model->getOrderTypeStatusList();
        unset($order_type_list[1]['status'][4]);//需求原型没有已收货，unset掉
        $this->assign("order_type_list", $order_type_list);

        //供应商列表数据
        $supplier_list = $order_common_model->getSupplierList();
        $this->assign('supplier_list',$supplier_list);

        return $this->fetch('period_buy_order/lists');
    }

    /**
     * 详情
     */
    public function detail()
    {
        $id = input("id", 0);
        if (request()->isAjax()) {
            $period_buy_order_model = new PeriodBuyOrderModel();
            $res = $period_buy_order_model->getDetail($id);
            return $res;
        }

        $this->assign('id', $id);
        return $this->fetch('period_buy_order/detail');
    }
}