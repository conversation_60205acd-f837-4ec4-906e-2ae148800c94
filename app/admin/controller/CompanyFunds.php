<?php
/**
 * 公司账户流水
 */

namespace app\admin\controller;


use app\model\company\Company;
use app\model\company\CompanyAccount;

class CompanyFunds extends BaseAdmin
{
    /**
     * 账户列表
     * @return mixed
     */
    public function lists()
    {
        $account_model = new CompanyAccount();

        if(request()->isAjax()){
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $account_no = input('account_no', '');
            $member_id = input('member_id', '');
            $mobile = input('mobile', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');
            $from_type = input('from_type', '');

            $condition[] = ['a.account_type', '=', 'balance'];
            $join = [
                ['member m', 'a.member_id = m.member_id', 'left']
            ];

            // 流水号
            if ($account_no != '') {
                $condition[] = ['a.account_no', '=', $account_no];
            }

            if ($member_id != '') {
                $condition[] = ['a.member_id', '=', $member_id];
            }

            // 手机号
            if ($mobile != '') {
                $condition[] = ['m.mobile', '=', $mobile];
            }

            // 类型
            if($from_type != ''){
                $condition[] = ['a.from_type', '=', $from_type];
            }

            // 时间
            if ($start_date != '' && $end_date != '') {
                $condition[] = ['a.create_time', 'between', [strtotime($start_date), strtotime($end_date)]];
            } else {
                if ($start_date != '' && $end_date == '') {
                    $condition[] = ['a.create_time', '>=', strtotime($start_date)];
                } else {
                    if ($start_date == '' && $end_date != '') {
                        $condition[] = ['a.create_time', '<=', strtotime($end_date)];
                    }
                }
            }
            $order = 'a.id desc';
            $field = 'a.*, m.mobile';

            return $account_model->getCompanyAccountPageList($condition, $page, $page_size, $order, $field, 'a', $join);
        }

        $company_model = new Company();
        $info = $company_model->getInfo();
        $this->assign('balance', $info['balance']);

        $from_type_arr = $account_model->getFromTypeArr();
        $this->assign('from_type_arr', $from_type_arr);
        $this->assign('from_type_arr_json', json_encode($from_type_arr));

        return $this->fetch("company_funds/lists");
    }
}