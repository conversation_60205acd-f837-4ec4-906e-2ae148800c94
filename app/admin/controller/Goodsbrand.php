<?php

/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\admin\controller;

use app\model\goods\GoodsBrand as GoodsBrandModel;

/**
 * 商品品牌管理 控制器
 */
class Goodsbrand extends BaseAdmin
{
	/**
	 * 商品品牌列表
	 */
	public function lists()
	{
		if (request()->isAjax()) {
			$page_index = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$search_keys = input('search_keys', "");
			$condition = [];
			if (!empty($search_keys)) {
				$condition[] = ['ngb.brand_name', 'like', '%' . sqlEncode($search_keys) . '%'];
			}

			$goods_brand_model = new GoodsBrandModel();
			$list = $goods_brand_model->getBrandPageList($condition, $page_index, $page_size);

			return $list;
		} else {
			$this->forthMenu();
			return $this->fetch('goodsbrand/lists');
		}
	}

	/**
	 * 商品品牌添加
	 */
	public function addBrand()
	{
		if (request()->isAjax()) {
			$brand_name = input('brand_name', '');
			$brand_initial = input('brand_initial', '');
			$image_url = input('image_url', "");
			$sort = input('sort', 0);
			$is_recommend = input('is_recommend', '');
			$data = [
				'brand_name' => $brand_name,
				'brand_initial' => $brand_initial,
				'image_url' => $image_url,
				'sort' => $sort,
				'is_recommend' => $is_recommend
			];
			$goods_brand_model = new GoodsBrandModel();
			$res = $goods_brand_model->addBrand($data);
			$this->addLog("添加商品品牌id:" . $res['data']);
			return $res;
		} else {
			return $this->fetch('goodsbrand/add_brand');
		}
	}

	/**
	 * 商品品牌编辑
	 */
	public function editBrand()
	{
		$goods_brand_model = new GoodsBrandModel();
		if (request()->isAjax()) {
			$brand_id = input('brand_id', 0);
			$brand_name = input('brand_name', '');
			$brand_initial = input('brand_initial', '');
			$image_url = input('image_url', "");
			$sort = input('sort', 0);
			$is_recommend = input('is_recommend', '');
			$data = [
				'brand_id' => $brand_id,
				'brand_name' => $brand_name,
				'brand_initial' => $brand_initial,
				'image_url' => $image_url,
				'sort' => $sort,
				'is_recommend' => $is_recommend
			];
			$condition = array(
			    ['brand_id', '=', $brand_id]
            );
            $brand_info = $goods_brand_model->getBrandInfo([['brand_id', '=', $brand_id]]);
            $brand_info = $brand_info['data'];
            $log_data = [$brand_info, $data];
			$res = $goods_brand_model->editBrand($data, $condition);
			$this->addLog("编辑商品品牌id:" . $brand_id, $log_data);
			return $res;
		} else {
			$brand_id = input('brand_id', 0);
			$brand_info = $goods_brand_model->getBrandInfo([['brand_id', '=', $brand_id]]);

			$brand_info = $brand_info['data'];
			$this->assign("brand_info", $brand_info);
			return $this->fetch('goodsbrand/edit_brand');
		}
	}

	/**
	 * 商品品牌删除
	 */
	public function deleteBrand()
	{
		if (request()->isAjax()) {
			$brand_ids = input("brand_ids", 0);
			$goods_brand_model = new GoodsBrandModel();
			$condition = [
				["brand_id", 'in', $brand_ids]
			];
			$this->addLog("删除商品品牌id:" . $brand_ids);
			$res = $goods_brand_model->deleteBrand($condition);
			return $res;
		}
	}

	/**
	 * 修改排序
	 */
	public function modifySort()
	{
		$sort = input('sort', 0);
		$brand_id = input('brand_id', 0);
		$goods_brand_model = new GoodsBrandModel();
        $condition = array(
            ['brand_id', '=', $brand_id]
        );
		$res = $goods_brand_model->modifyBrandSort($sort, $condition);
		return $res;
	}

	/**
	 * 转移品牌
	 */
	public function modifySite()
	{
		$brand_ids = input('brand_ids', 0);
		$condition = [
			["brand_id", 'in', $brand_ids]
		];
		$goods_brand_model = new GoodsBrandModel();
		$res = $goods_brand_model->modifyBrandSite(0, $condition);
		return $res;
	}
}
