<?php
/**
 * 大转盘活动
 */

namespace app\admin\controller;

use app\model\activity\LargeTurntableActivity as largeTurnMl;
use app\model\activity\LargeTurntablePrize;
use app\model\activity\DistributionAssistVote;
use app\model\activity\LargeTurnDraw;
use think\facade\Db;

class LargeTurntable extends BaseAdmin
{
    /**
     * [lists 大转盘活动列表]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:49:43+0800
     * @return   [type]                   [description]
     */
    public function lists()
    {
        if (request()->isAjax()) {

            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $title      = input('title', '', 'sqlEncode');
            $start_time = input('start_time', '');
            $end_time   = input('end_time', '');
            $status     = input('status', '');

            $condition  = [];
            if($status !== ''){
                $condition[] = ['status', '=', $status];
            }

            if($title){
                $condition[] = ['name', 'like', '%' . $title . '%'];
            }

            if (!empty($start_time)) {
                $condition[] = ['start_time', '>=', strtotime($start_time . ' 00:00:00')];
            }
            if (!empty($end_time)) {
                $condition[] = ['end_time', '<=', strtotime($end_time . ' 23:59:59')];
            }
            $order = 'id DESC,start_time asc,create_time desc';
            $field = '*';
            
            $model = new largeTurnMl();
            return $model->getActivityPageList($condition, $field, $order, $page_index, $page_size);

        }
        return $this->fetch("large_turntable/lists");
    }

    /**
     * [add 添加大转盘活动]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:50:08+0800
     */
    public function add()
    {
        if(request()->isAjax()){
            $data = [
                'name' => trim(input('name', '')),
                'start_time'  => strtotime(input('start_time', '')),
                'end_time'    => strtotime(input('end_time', '')),
                'price'       => input('price', 0.00),
                'target_people_num'        => input('target_people_num/d', 0),
                'awards_people_num' => input('awards_people_num', 0),
                'everyday_luck_draw_num'   => input('everyday_luck_draw_num/d', 1),
                'everyday_luck_max_num'   => input('everyday_luck_max_num/d', 1),
                'crowd'   => trim(input('crowd', '')),
                'content' => trim(input('content', '')),
                'tips'    => trim(input('tips', '')),
                'banner'  => trim(input('banner', '')),
                'status'      => input('status', 0)
            ];

            $model = new largeTurnMl();
            return $model->addEditTurnTableActivity($data);
        }else{
            return $this->fetch("large_turntable/add");
        }
    }

    /**
     * [edit 编辑大转盘活动]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:50:32+0800
     * @return   [type]                   [description]
     */
    public function edit()
    {
        $id = input('id', 0);
        if(request()->isAjax()){
            $data = [
                'name' => trim(input('name', '')),
                'start_time'  => strtotime(input('start_time', '')),
                'end_time'    => strtotime(input('end_time', '')),
                'price'       => input('price', 0.00),
                'target_people_num'        => input('target_people_num/d', 0),
                'awards_people_num' => input('awards_people_num', 0),
                'everyday_luck_draw_num'   => input('everyday_luck_draw_num/d', 1),
                'everyday_luck_max_num'   => input('everyday_luck_max_num/d', 1),
                'crowd'   => trim(input('crowd', '')),
                'content' => trim(input('content', '')),
                'tips'    => trim(input('tips', '')),
                'banner'  => trim(input('banner', '')),
                'status'      => input('status', 0)
            ];


            $model = new largeTurnMl();
            return $model->addEditTurnTableActivity($data,$id);
        }else{
            $this->assign('id', $id);

            $service = new largeTurnMl();
            $info = $service->getActivityDetail($id);
            $this->assign('info', $info['data']);

            return $this->fetch("large_turntable/edit");
        }
    }

    /**
     * [show 大转盘活动详情]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:50:50+0800
     * @return   [type]                   [description]
     */
    public function show()
    {
        $id = input('id', 0);

        $service = new largeTurnMl();
        $info = $service->getActivityDetail($id);

        $this->assign('info',$info['data']);
        return $this->fetch('large_turntable/show');
    }

    /**
     * [updateStatus 更新大转盘活动]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:51:10+0800
     * @return   [type]                   [description]
     */
    public function updateStatus()
    {
        if(request()->isAjax()){
            $id = input('id', 0);
            $status  = input('status', 0);
            $service = new largeTurnMl();

            return $service->updateTurnStatus($id, $status);
        }
    }

    /**
     * [prizeLists 大转盘奖品列表]
     * <AUTHOR>
     * @DateTime 2020-10-21T17:34:41+0800
     * @return   [type]                   [description]
     */
    public function prizeLists()
    {
        $id = input('id',0);
        $model = new largeTurnMl();
        if (request()->isAjax()) {

            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);

            $condition   = [];
            $condition[] = ['activity_id','=',$id];

            $order = 'p.edit_time desc,p.id desc';
            $field = 'p.*,a.name,a.status';

            $alias = 'p';
            $join  = [
                ['large_turntable_activity a','a.id=p.activity_id','left'],
            ];
            
            return $model->getPrizePageList($condition, $field, $order, $page_index, $page_size,$alias,$join);

        }
        $activity = $model->getActivityDetail($id,'status')['data'];
        $this->assign('id', $id);
        $this->assign('status', $activity['status']);
        return $this->fetch("large_turntable/prize_lists");
    }

    /**
     * [addPrize 添加活动奖品]
     * <AUTHOR>
     * @DateTime 2020-10-21T18:05:40+0800
     */
    public function addPrize()
    {
        $activity_id = input('activity_id/d',0);
        $model = new largeTurnMl();
        if(request()->isAjax()){

            $model = new LargeTurntablePrize();
            return $model->addEditTurnTablePrize($_POST);
        }else{

            $info = $model->getActivityDetail($activity_id,'name');
            $this->assign('activityInfo', $info['data']);
            $this->assign('activity_id', $activity_id);
            $this->assign('prizeType', $model->prizeType);
            return $this->fetch("large_turntable/add_prize");
        }
    }

    /**
     * [editPrize 编辑奖品]
     * <AUTHOR>
     * @DateTime 2020-10-22T16:30:20+0800
     * @return   [type]                   [description]
     */
    public function editPrize()
    {
        $id = input('id', 0);
        $model = new LargeTurntablePrize();
        if(request()->isAjax()){
            // p($_POST);
            return $model->addEditTurnTablePrize($_POST,$id);
        }else{

            $activityMl = new largeTurnMl();
            $info = $model->getPrizeDetail($id)['data'];
            $activityInfo = $activityMl->getActivityDetail($info['activity_id'],'name')['data'];
            $this->assign('id', $id);
            $this->assign('activity_id', $info['activity_id']);
            $this->assign('info', $info);
            $this->assign('activityInfo', $activityInfo);
            $this->assign('prizeType', $activityMl->prizeType);

            return $this->fetch("large_turntable/edit_prize");
        }
    }

    /**
     * [shopList 大转盘活动参与店铺]
     * <AUTHOR>
     * @DateTime 2020-10-19T10:51:30+0800
     * @return   [type]                   [description]
     */
    public function shopList()
    {
        $id = input('id/d',0);  //活动id
        if (request()->isAjax()) {

            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $title      = input('title', '', 'sqlEncode');
            $username   = input('username', '', 'sqlEncode');
            $mobile     = input('mobile/w',0);
            $start_time = input('start_time', '');
            $end_time   = input('end_time', '');
            $status     = input('status', '');

            $condition  = [];
            $condition[] = ['a.id', '=', $id];
            // $condition[] = ['al.activity_id', '=', $id];
            $condition[] = ['d.site_id', '>', 0];
            if($status !== ''){
                $condition[] = ['a.status', '=', $status];
            }

            if($title){
                $condition[] = ['a.title', 'like', '%' . $title . '%'];
            }

            if (!empty($start_time)) {
                $condition[] = ['a.start_time', '>=', strtotime($start_time . ' 00:00:00')];
            }
            if (!empty($end_time)) {
                $condition[] = ['a.end_time', '<=', strtotime($end_time . ' 23:59:59')];
            }

            if ($username) {
                $condition[] = ['s.username', 'like', '%' . $username . '%'];
            }
            if ($mobile) {
                $condition[] = ['s.mobile', '=', $mobile];
            }
            $field = 'd.*,a.id,a.target_people_num,a.status,a.price,a.start_time,a.end_time,a.name,s.site_name,s.username,s.mobile,count(d.id) as actualNum';
            // $field = ['d.*','a.id','a.start_time','a.end_time','a.status','a.aim_num','a.title','s.site_name','s.username','s.mobile','a.reward','sum(d.times)' => 'finishNum','r.is_reward'];
            $orderRaw = 'actualNum desc';

            // $order = 'updtime ASC ';
            $order = 'ymd DESC';

            $alias = 'd';
            $join  = [
                ['large_turntable_activity a','a.id=d.activity_id','left'],
                ['shop s','s.site_id=d.site_id','left'],
                // ['large_turntable_award_log al','al.site_id=d.site_id','left'],
            ];
            $group = 'd.site_id';
            
            $model = new LargeTurnDraw();
            $res = $model->staticDrawPageList($condition, $field, $order, $page_index, $page_size,$alias, $join, $group,null,$orderRaw);

            if ($res['data']['list']) {
                foreach ($res['data']['list'] as $key => &$value) {
                    $where = [];
                    $where[] = ['site_id','=',$value['site_id']];
                    $where[] = ['activity_id','=',$id];
                    // $where['site_id'] = $value['site_id'];
                    // $where['activity_id'] = $id;
                    $value['is_reward'] = model('large_turntable_shop_award')->getValue($where,'is_reward');

                    $cond = [];
                    $cond[] = ['site_id','=',$value['site_id']];
                    $cond[] = ['activity_id','=',$id];
                    // $cond['site_id'] = $value['site_id'];
                    // $cond['activity_id'] = $id;
                    $value['awardNum'] = model('large_turntable_award_log')->getCount($cond);
                    // 已完成目标
                    if ($value['actualNum']>=$value['target_people_num']) {
                        $value['isFinish']  = 1;
                        // 达成目标且未加入奖励列表
                    }else{
                        $value['isFinish']  = 0;
                    }

                }
            }

            return $res;

        }
        $this->assign('id', $id);
        return $this->fetch("large_turntable/shopList");
    }

    /**
     * [drawLists 抽奖列表]
     * <AUTHOR>
     * @DateTime 2020-10-23T14:45:51+0800
     * @return   [type]                   [description]
     */
    public function drawLists()
    {
        $id      = input('activity_id/d',0);  //活动id
        $site_id = input('site_id/d',0);  //店铺id
        if (request()->isAjax()) {

            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $title      = input('title', '', 'sqlEncode');
            $username   = input('username', '', 'sqlEncode');
            $mobile     = input('mobile/w',0);
            $start_time = input('start_time', '');
            $end_time   = input('end_time', '');
            $status     = input('status', '');

            $condition  = [];
            $condition[] = ['a.id', '=', $id];
            $condition[] = ['d.site_id', '=', $site_id];
            if($status !== ''){
                $condition[] = ['a.status', '=', $status];
            }

            if($title){
                $condition[] = ['a.title', 'like', '%' . $title . '%'];
            }

            if (!empty($start_time)) {
                $condition[] = ['a.start_time', '>=', strtotime($start_time . ' 00:00:00')];
            }
            if (!empty($end_time)) {
                $condition[] = ['a.end_time', '<=', strtotime($end_time . ' 23:59:59')];
            }

            if ($username) {
                $condition[] = ['s.username', 'like', '%' . $username . '%'];
            }
            if ($mobile) {
                $condition[] = ['s.mobile', '=', $mobile];
            }
            $field = 'd.*,a.id,a.target_people_num,a.status,a.price,a.start_time,a.end_time,a.name,m.username,m.nickname,m.mobile,m.member_id,sum(d.times) as actualNum';
            // $field = ['d.*','a.id','a.start_time','a.end_time','a.status','a.aim_num','a.title','s.site_name','s.username','s.mobile','a.reward','sum(d.times)' => 'finishNum','r.is_reward'];
            $orderRaw = 'actualNum desc';

            // $order = 'updtime ASC ';
            $order = '';

            $alias = 'd';
            $join  = [
                ['large_turntable_activity a','a.id=d.activity_id','left'],
                ['member m','m.member_id=d.member_id','left'],
                // ['large_turntable_award_log al','al.site_id=d.site_id','left'],
            ];
            $group = 'd.member_id';
            
            $model = new LargeTurnDraw();
            $res = $model->staticDrawPageList($condition, $field, $order, $page_index, $page_size,$alias, $join, $group,null,$orderRaw);
            // p($res);

            if ($res['data']['list']) {
                foreach ($res['data']['list'] as $key => &$value) {
                    $where = [];
                    $where[] = ['site_id','=',$value['site_id']];
                    $where[] = ['activity_id','=',$id];
                    $where[] = ['member_id','=',$value['member_id']];
                    $value['awardNum'] = model('large_turntable_award_log')->getCount($where);
                    // 已抽中
                    if ($value['awardNum']>=1) {
                        $value['isLuck']  = '是';
                    }else{
                        $value['isLuck']  = '否';
                    }
                }
            }

            return $res;

        }
        $this->assign('id', $id);
        $this->assign('site_id', $site_id);
        return $this->fetch("large_turntable/drawList");
    }

    /**
     * [luckList 个人中奖详情]
     * <AUTHOR>
     * @DateTime 2020-10-23T17:03:28+0800
     * @return   [type]                   [description]
     */
    public function luckLists()
    {
        $id        = input('activity_id/d',0);  //活动id
        $site_id   = input('site_id/d',0);  //店铺id
        $member_id = input('member_id/d',0);  //用户id
        if (request()->isAjax()) {
            if (!$id || !$site_id || !$member_id) {
                return error(-1, '参数错误');
            }
            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $order  = 'a.id DESC';

            $condition  = [];
            $condition[] = ['a.activity_id', '=', $id];
            $condition[] = ['a.member_id', '=', $member_id];
            $condition[] = ['a.site_id', '=', $site_id];

            $field = 'a.*,m.nickname';
            $alias = 'a';
            $join  = [
                ['member m','m.member_id=a.member_id','left'],
            ];

            $model = new LargeTurnDraw();
            $res = $model->getDrawAwardPageList($condition, $field, $order, $page_index, $page_size,$alias, $join);

            return $res;

        }
        $this->assign('id', $id);
        $this->assign('site_id', $site_id);
        $this->assign('member_id', $member_id);
        return $this->fetch("large_turntable/luckList");
    }

    /**
     * [rewardShop 发放奖励店铺]
     * <AUTHOR>
     * @DateTime 2020-10-19T17:20:48+0800
     * @return   [type]                   [description]
     */
    public function rewardShop()
    {
        if(request()->isAjax()){
            $site_id = input('site_id', 0);
            $activity_id = input('activity_id', 0);
            $model = new LargeTurnDraw();

            if (!$site_id || !$activity_id) {
                return error(-1, '参数错误');
            }
            return $model->drawRewardLogic($site_id,$activity_id);
        }
    }

}