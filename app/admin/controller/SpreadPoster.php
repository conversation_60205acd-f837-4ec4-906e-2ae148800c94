<?php


namespace app\admin\controller;

use app\model\spread\SpreadPoster as SpreadPosterModel;
/**
 * Class SpreadPoster
 * 推广海报
 * @package app\admin\controller\
 */
class SpreadPoster extends BaseAdmin
{
    /**
     * 列表
     * @return array|mixed
     */
    public function lists()
    {
        return $this->redirect('/admin/spreadPoster/edit?id=1');

        $material_model = new MaterialModel();
        if (request()->isAjax()) {
            $title = input('title', '');
            $condition = [];
            if ($title) {
                $condition[] = ['title', 'like', '%' . sqlEncode($title) . '%'];
            }
            return $material_model->getMaterialPageList($condition);
        }
        return $this->fetch('spread/lists');
    }

    /**
     * 新增
     * @return array|mixed
     */
    public function add()
    {
        if (request()->isAjax()) {
            $material_model = new SpreadPosterModel();
            $poster_name = input('poster_name', '');
            $wx_qrcode = input('wx_qrcode', '');
            $path = input('path', '');
            $wechat_no = input('wechat_no','');
            $mobile = input('mobile','');
            $data = [
                'poster_name'   => $poster_name,
                'wx_qrcode'     => $wx_qrcode,
                'path'          => $path,
                'wechat_no'     => $wechat_no,
                'mobile'        => $mobile,
                'is_enable'     => 1,
                'update_time'   => time(),
                'create_time'   => time(),
            ];
            $res = $material_model->add($data);
            if($res['code'] == 0){
                $this->addLog('添加素材id：'.$res['data']);
            }
            return $res;
        }
        return $this->fetch('spread/add');
    }


    /**
     * 编辑
     * @return array|mixed
     */
    public function edit()
    {
        $poster_model = new SpreadPosterModel();
        $id = input('id', '0');
        $info = $poster_model->getInfo($id);
        if (request()->isAjax()) {
            $poster_name = input('poster_name', '');
            $wx_qrcode = input('wx_qrcode', '');
            $path = input('path', '');
            $wechat_no = input('wechat_no','');
            $mobile = input('mobile','');
            $data = [
                'poster_name'   => $poster_name,
                'wx_qrcode'     => $wx_qrcode,
                'path'          => $path,
                'wechat_no'     => $wechat_no,
                'mobile'        => $mobile,
                'update_time'   => time(),
            ];
            $res = $poster_model->edit($id, $data);
            if($res['code'] == 0){
                $log_data = [$info['data'], $data];
                $this->addLog('编辑推广海报id：'.$id, $log_data);
            }
            return $res;
        }

        if (empty($info['data'])){
            return $this->redirect('/admin/spreadPoster/add');
        }
        $share_link = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $share_link .= $_SERVER['HTTP_HOST'].'/spread/index/index?id='.$id;
        $this->assign('link',$share_link);
        $this->assign('info', $info['data']);
        return $this->fetch('spread/edit');
    }


    /**
     * 详情
     * @return array|mixed
     */
    public function show()
    {
        $material_model = new MaterialModel();
        $id = input('id', '');
        $info = $material_model->getInfo(['id' => $id]);
        $this->assign('info', $info['data']);
        return $this->fetch('spread/show');
    }

}