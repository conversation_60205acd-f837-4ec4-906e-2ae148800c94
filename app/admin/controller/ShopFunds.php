<?php


namespace app\admin\controller;


use app\service\shopapi\WithdrawService;
use app\model\shop\ShopAccount;
use \app\model\president\President as President<PERSON><PERSON><PERSON>;
use \app\model\president\PresidentAccount;

class ShopFunds extends BaseAdmin
{
    public function lists()
    {
        $account_model = new ShopAccount();

        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $account_no = input('account_no', '');
            $site_id = input('site_id', '');
            $mobile = input('mobile', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');
            $from_type = input('from_type', '');

            $condition[] = ['u.app_module', '=', 'shop'];
            $join = [
                ['user u', 'u.site_id = a.site_id', 'left']
            ];

            // 流水号
            if ($account_no != '') {
                $condition[] = ['a.account_no', '=', $account_no];
            }

            // 店主id
            if ($site_id != '') {
                $condition[] = ['a.site_id', '=', $site_id];
            }

            // 手机号，关联user查询用户登录账号
            if ($mobile != '') {
                $condition[] = ['u.username', '=', $mobile];
            }

            // 类型
            if($from_type != ''){
                $condition[] = ['a.from_type', '=', $from_type];
            }

            // 时间
            if ($start_date != '' && $end_date != '') {
                $condition[] = ['a.create_time', 'between', [strtotime($start_date), strtotime($end_date)]];
            } else {
                if ($start_date != '' && $end_date == '') {
                    $condition[] = ['a.create_time', '>=', strtotime($start_date)];
                } else {
                    if ($start_date == '' && $end_date != '') {
                        $condition[] = ['a.create_time', '<=', strtotime($end_date)];
                    }
                }
            }
            $order = 'a.id desc';
            $field = 'a.*, u.username';

            return $account_model->getAccountPageList($condition, $page, $page_size, $order, $field, 'a', $join);
        }

        $charge_fee = config('withdraw.shop_charge_fee');
        $this->assign('charge_fee', $charge_fee);

        $from_type_arr = $account_model->getFromTypeArr();
        $this->assign('from_type_arr', $from_type_arr);
        $this->assign('from_type_arr_json', json_encode($from_type_arr));

        return $this->fetch("shop_funds/lists");
    }

    /**
     * [presidentMng 会长流水管理]
     * <AUTHOR>
     * @DateTime 2020-10-12T14:02:19+0800
     * @return   [type]                   [description]
     */
    public function presidentMng()
    {
        $presidentMl = new PresidentModel();
        if(request()->isAjax()){
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $username  = input('username', '');
            $contact  = input('contact', '');

            $condition = [];
            if(!empty($username)){
                $condition[] = ['username', 'like', '%' . sqlEncode($username) . '%'];
            }
            if(!empty($contact)){
                $condition[] = ['contact', '=', $contact];
            }

            return $presidentMl->getPresidentPageList($condition, $page, $page_size);
        }
        return $this->fetch('shop_funds/president_lists');
    }

    /**
     * [addPreAccount 添加会长结算佣金]
     * <AUTHOR>
     * @DateTime 2020-10-12T15:01:24+0800
     */
    public function addPreAccount(){
        $id = input('id',0);
        if(request()->isAjax()){
            $preAccountMl = new PresidentAccount();
            $num = input('withdraw_money',0);
            if ($num<=0) {
                return error('','结算金额必须大于03');
            }
            return $preAccountMl->changeAccount($id,-$num,'balance','admin');
        }else{
            $id = input('id', 0);
            $condition[] = ['id','=',$id];
            $info = model('president')->getInfo($condition,'balance,balance_withdraw');
            $this->assign('info', $info ?? []);
            $this->assign('id', $id);
            return $this->fetch('shop_funds/add_pre_account');
        }
    }

    /**
     * [accountList 结算明细]
     * <AUTHOR>
     * @DateTime 2020-10-12T18:54:40+0800
     * @return   [type]                   [description]
     */
    public function accountList()
    {
        $pid = input('id', '');//id
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $create_time_start = input('create_time_start', '');//开始时间
            $create_time_end   = input('create_time_end', '');//结束时间

            $preAccountMl = new PresidentAccount();

            // $condition = [
            //     ['account_type', '=', 'youli_withdraw']
            // ];
            $condition = [];

            //会长id
            if ($pid <= 0) {
                return error('-1', '请输入店铺id');
            }
            $condition[] = ['president_id', '=', $pid];

            //开始时间
            if ($create_time_start != '') {
                $condition[] = [ 'create_time', '>=', strtotime($create_time_start) ];
            }
            //结束时间
            if ($create_time_end != '') {
                $condition[] = [ 'create_time', '<=', strtotime($create_time_end.' 23:59:59') ];
            }

            $order = 'create_time desc';
            $field = '*';

            $res = $preAccountMl->getPreAccountList($condition, $field, $order, $page, $page_size);
            return $res;
        } else {
            $this->assign('pid', $pid);
            return $this->fetch('shop_funds/accountList');
        }
    }
}