<?php


namespace app\admin\controller;

use app\model\enterpriseWx\EnterpriseTagGroupModel;
use app\model\enterpriseWx\EnterpriseTagModel;
use app\model\enterpriseWx\EnterpriseTagRuleMiddleModel;
use app\service\tagRule\MemberTagRuleService;
use app\service\tagRule\MemberTagService;
use think\facade\Db;
use think\facade\Log;

class MemberTags extends BaseAdmin
{
    public function valueList()
    {
        $tab_groups = EnterpriseTagGroupModel::order('order desc')->column('group_name', 'tag_group_id');
        $execute_rule = MemberTagRuleService::getInstance()->listenerExecuteRule()['data'] ?? [];
        $this->assign('tab_groups', $tab_groups);
        $this->assign('execute_rule', $execute_rule);
        // dd($execute_rule);
        $this->forthMenu([]);
        return $this->fetch('member/tag_value_list');
    }

    /**
     * @date: 2022/1/7 15:35
     * @desc：标签列表
     * @return array
     */
    public function dataList()
    {
        try {
            if (request()->isAjax()) {
                $service = new MemberTagService();
                $page = input('page', 1);
                $size = input('page_size', 10);
                $tag_group_id = input('tag_group_id', 0);
                $tag_name = input('tag_name', '');
                $where = [];
                $tag_group_id && $where[] = ['tag_group_id', '=', $tag_group_id];
                $tag_name && $where[] = ['tag_name', 'like', '%' . $tag_name . '%'];
                $this->addLog('标签值列表查询', input());
                $retData = $service->getEnterpriseTagList($where, 'belongGroup', $page, $size);

                $execute_rule_status = model("jobs")->getCount(['queue' => "execute_rule"]) > 0;
                if($retData['code'] == 0)
                {
                    foreach ($retData['data']['list'] as $i=>$v)
                    {
                        $retData['data']['list'][$i]['execute_status'] = $execute_rule_status || (new MemberTagRuleService())->checkRepeatTagAllQueue("default", [$v['tag_id']]);
                    }
                }
                return $retData;
            }
        }
        catch (\Exception $e) {
            return error('-1', $e->getMessage());
        }
    }

    /**
     * @date: 2022/1/7 15:35
     * @desc：标签排序编辑
     * @return array
     */
    public function editSort()
    {
        try {
            $tag_id = input('id', '');
            $order = input('sort', 0);
            EnterpriseTagModel::where(['tag_id' => $tag_id])->update(['order' => $order]);
            return success(0, '操作成功');
        }
        catch (\Exception $e) {
            return error('-1', $e->getMessage());
        }
    }

    public function groupList()
    {
        if (request()->isAjax()) {
            $service = new MemberTagService();
            $page = input('page', 1);
            $size = input('page_size', 10);
            $group_name = input('group_name', '');
            $where = [];
            $group_name && $where[] = ['group_name', 'like', '%' . $group_name . '%'];
            $this->addLog('标签组列表查询', input());
            return $service->getEnterpriseTagGroupList($where, [], $page, $size);
        }else{
            $this->forthMenu([]);
            return $this->fetch('member/tag_group_list');
        }
    }

    public function tagValueAdd(MemberTagRuleService $service)
    {
        $tag_id = input('tag_id', 0);
        if (request()->isAjax()) {

            if($tag_id > 0){
                $data = EnterpriseTagRuleMiddleModel::where(['tag_id'=>$tag_id])->select()->toArray();
            }

            $param = input();
            $res = $service->saveTagRule($param, $tag_id);

            if($tag_id > 0){
                $log = $this->addLog("编辑标签id：".$tag_id, $data ?? []);
            }

            return $res;
        }else{
            //标签组
            $tag_info = EnterpriseTagModel::find($tag_id);
            $tag_group = EnterpriseTagGroupModel::select();
            $this->assign("tag_group", $tag_group);
            $this->assign("tag_info", $tag_info);
            $this->forthMenu([]);
            return $this->fetch('member/tag_value_add');
        }
        
    }

    public function tagRulesList(MemberTagRuleService $service){
        if (request()->isAjax()) {
            $tag_id = input('tag_id', 0);
            $res = $service->tagRuleList($tag_id);
            return $res ;
        }
    }

    /**
     * 执行规则
     *
     * @param MemberTagRuleService $service
     * @return JSON
     */
    public function executeRule(MemberTagRuleService $service){
        $tag_id = input('tag_id', 0);
        $res = $service->executeTagRule($tag_id);
        return $res ; 
    }

    public function executeAllRule(MemberTagRuleService $service){
        $res = $service->executeAllTagRule();
        return $res ; 
    }

    public function executeRulePercent(MemberTagRuleService $service){
        $res = $service->listenerExecuteRule();
        return $res ; 
    }

    public function addTagGroup()
    {
        $groupName = input('group_name', '');
        $tagData = input('tag', []);
        try
        {
            Db::startTrans();
            $service = new \app\service\member\MemberTagService();
            $service->addGroup($groupName, $tagData);
            Db::commit();
            return success(0, '添加成功');
        }
        catch(\Throwable $e)
        {
            Db::rollback();
            Log::error('标签组添加失败'.$e->getMessage().PHP_EOL.$e->getTraceAsString());
            return error('-1', $e->getMessage());
        }
    }


    public function editTagGroup()
    {
        $groupId = input('group_id', '');
        $groupName = input('group_name', '');

        try
        {
            Db::startTrans();
            $service = new \app\service\member\MemberTagService();
            $service->editGroup($groupId, $groupName);
            Db::commit();
            return success(0, '编辑成功');
        }
        catch(\Throwable $e)
        {
            Db::rollback();
            Log::error('标签编辑失败'.$e->getMessage().PHP_EOL.$e->getTraceAsString());
            return error('-1', '编辑失败'.$e->getMessage());
        }

    }

    public function delTagGroup()
    {
        $groupId = input('group_id', 0);
        try
        {
            Db::startTrans();
            $service = new \app\service\member\MemberTagService();
            $service->delGroup($groupId);
            Db::commit();
            return success(0, '删除成功');
        }
        catch(\Throwable $e)
        {
            Db::rollback();
            Log::error('标签组删除失败'.$e->getMessage().PHP_EOL.$e->getTraceAsString());
            return error('-1', '删除失败'.$e->getMessage());
        }
    }

    public function delTag()
    {
        $tagId = input('tag_id', 0);
        try
        {
            Db::startTrans();
            $service = new \app\service\member\MemberTagService();
            $service->del($tagId);
            Db::commit();
            return success(0, '删除成功');
        }
        catch(\Throwable $e)
        {
            Db::rollback();
            Log::error('标签删除失败'.$e->getMessage().PHP_EOL.$e->getTraceAsString());
            return error('-1', '删除失败'.$e->getMessage());
        }
    }
}