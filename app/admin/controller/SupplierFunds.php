<?php
namespace app\admin\controller;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\supplier\SupplierSale;
use app\model\supplier\SupplierRecharge;


class supplierFunds extends BaseAdmin
{
    /**列表*/
    public function lists()
    {
        $start_time = input("start_time", '');
        $end_time = input("end_time", '');
        $order_common_model = new OrderCommonModel();
        if (request()->isAjax()) {
            $supply_shop_id = input('supply_shop_id', 0);
            $page_index = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $condition = [];
            
            $condition[] = ['sd.amount','>',0];
            //供应商
            if ($supply_shop_id != "") {
                $condition[] = ["s.supplier_id", "=", $supply_shop_id];
            }
            if (!$start_time || !$end_time) {
                return error('','请输入时间');
            }

            $start_time = dayTimestamp(0, $start_time);
            $end_time = dayTimestamp(1, $end_time);

            $field  = 's.supplier_id,s.title';
            $order  = 's.supplier_id DESC';
            $group  = 's.supplier_id';
            $alias  = 's';
            $join   = [
                ['supplier_sale_every_day sd','s.supplier_id=sd.supplier_id','left'],
            ];
            $group  = 's.supplier_id';

            $saleMl   = new SupplierSale();
            $chargeMl = new SupplierRecharge();
            $data   = model('supplier')->pageList($condition , $field , $order , $page_index , $page_size, $alias , $join,$group);
            $rechargeCond = [];
            $rechargeCond[] = ['add_time','>=',$start_time];
            $rechargeCond[] = ['add_time','<=',$end_time];
            if ($data['list']) {
                foreach ($data['list'] as $key => $value) {
                    $spid = $value['supplier_id'];
                    $value['startBalance']  = $saleMl->getPeriodBalance($spid,$start_time,'','start');
                    $value['curIncBalance'] = $saleMl->getSaleAmount($spid,$start_time,$end_time,'between');
                    $value['curDecBalance'] = $chargeMl->getRechargeAmount($rechargeCond,$spid);
                    $value['endBalance']    = $saleMl->getPeriodBalance($spid,'',$end_time,'end');
                    $data['list'][$key] = $value;
                }
            }

            // $list   = $saleMl->geSalePageList($condition, $field, $order, $page_index, $page_size,'a',[],$group);
            return success(0,'查询成功',$data);
            // return $list;
        } else {
            $supplier_list = $order_common_model->getSupplierList(); //供应商
            $this->assign([
                            'supplier_list' => $supplier_list,
                            'start_time' => $start_time,
                            'end_time' => $end_time,
            ]);
            return $this->fetch('supplier_funds/lists');
        }
    }

    /**
     * [showDetail 供应商营业收入成本表]
     * <AUTHOR>
     * @DateTime 2020-12-01T11:48:41+0800
     * @return   [type]                   [description]
     */
    public function showDetail()
    {
        $start_time = input("start_time", '');
        $end_time   = input("end_time", '');
        $spid       = input("spid", '');
        if (request()->isAjax()) {
            $order_common_model = new OrderCommonModel();
            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $condition  = [];

            $condition[] = ['o.order_status','=',10];     //已完成
            $condition[] = ['o.pay_status','=',1];     //已完成
            $condition[] = ['o.supply_shop_id','=',$spid];
            $condition[] = ['og.supply_shop_id','=',$spid];
            // p($condition);
            //下单时间
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["o.finish_time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["o.finish_time", "<=", date_to_time($end_time)+60*60*24-1];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = [ 'o.finish_time', 'between', [ date_to_time($start_time), (date_to_time($end_time)+60*60*24-1) ] ];
            }
            
            $list = $order_common_model->getOrderGoodsPageList($condition, $page_index, $page_size, "og.order_goods_id DESC");
            return $list;
        } else {
            $this->assign([
                            'start_time' => $start_time,
                            'end_time' => $end_time,
                            'spid' => $spid,
            ]);
            return $this->fetch('supplier_funds/showDetail');
        }
    }

    /**
     * [recharge 供应商充值]
     * <AUTHOR>
     * @DateTime 2020-12-01T11:49:03+0800
     * @return   [type]                   [description]
     */
    public function recharge(){
        if(request()->isAjax()){
            $chargeMl = new SupplierRecharge();
            $spid  = input('supplier_id',0);
            $money = input('money',0);

            return $chargeMl->preRecharge($spid,$money,$this->uid,$this->user_info['username']);
        }else{
            $spid = input('spid', 0);
            $condition[] = ['supplier_id','=',$spid];
            $info = model('supplier')->getInfo($condition,'supplier_id,title');
            $this->assign('info', $info ?? []);
            return $this->fetch('supplier_funds/recharge');
        }
    }

    /**
     * [rechargeLog 供应商充值记录]
     * <AUTHOR>
     * @DateTime 2020-12-01T11:48:41+0800
     * @return   [type]                   [description]
     */
    public function rechargeLog()
    {
        $spid       = input("spid", '');
        if (request()->isAjax()) {
            $chargeMl = new SupplierRecharge();
            $start_time = input("start_time", '');
            $end_time   = input("end_time", '');
            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $condition  = [];

            $condition[] = ['supplier_id','=',$spid];
            // p($spid);
            //下单时间
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["add_time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["add_time", "<=", date_to_time($end_time)];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = [ 'add_time', 'between', [ date_to_time($start_time), date_to_time($end_time) ] ];
            }
            $order = 'id DESC';
            $list  = $chargeMl->getRechargeList($condition, '*', $order, $page_index, $page_size);
            return $list;
        } else {
            $this->assign('spid', $spid);
            return $this->fetch('supplier_funds/rechargeLog');
        }
    }
}