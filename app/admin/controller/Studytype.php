<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\admin\controller;

use app\service\study\StudyTypeService;
use think\facade\Log;

/**
 * 学习类型管理 控制器
 */
class Studytype extends BaseAdmin
{

    /******************************* 学习类型列表及相关操作 ***************************/

    /**
     * 学习类型列表
     */
    public function lists()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $status = input('status', '-1');//状态
            $name = input('name', '');//状态名称

            $condition = [];
            //状态
            if(in_array($status, [0, 1])){
                $condition[] = ['a.status', '=', $status];
            }
            //学习标题
            if ($name != '') {
                $name = str_replace("_", "\_", $name);
                $name = str_replace("%", "\%", $name);
                $condition[] = ['a.name', 'like', '%'.$name.'%'];
            }

            $order = 'a.id desc';

            $field = 'a.*';

            $res = StudyTypeService::getInstance()->index($condition, $page, $page_size, $order, $field);
            return $res;
        } else {
            $studyInfo = StudyTypeService::getInstance()->getLists();
            $this->assign('studyTypeInfo', $studyInfo['data']);

            //会员详情四级菜单
            $this->forthMenu([]);

            return $this->fetch('study_type/lists');
        }
    }

    /**
     * 学习类型添加
     */
    public function add()
    {
        if (request()->isAjax()) {
            $reqData = input('param.');//全部请求参数

            //学习管理信息
            $info = [
                'name' => $reqData['add_name']??'',//学习标题
            ];

            $res = StudyTypeService::getInstance()->store($info);

            return $res;
        }
    }

    /**
     * update 更新
     *
     * @return array
     */
    public function update()
    {
        if (request()->isAjax()) {
            $reqData = input('param.');//全部请求参数
            $id = $reqData['add_id']??0;//学习管理id

            //学习管理信息
            $info = [
                'name' => $reqData['add_name']??'',//学习标题
            ];

            $res = StudyTypeService::getInstance()->update($id, $info);

            return $res;
        }
    }

    /**
     * change_status 修改状态
     *
     * @return array
     */
    public function change_status()
    {
        if (request()->isAjax()) {
            $id = input('id', 0);
            $status = input('status', 0);
            if (empty($id)) {
                return error(-1, '参数错误！');
            }
            $res = StudyTypeService::getInstance()->changeStatus($id,$status);

            return $res;
        }
    }
}