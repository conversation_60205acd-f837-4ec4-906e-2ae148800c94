<?php
/**
 * 推广页面管理
 */

namespace app\admin\controller;


use app\model\promotion\PromotionPage as PromotionPageModel;
use app\model\promotion\PromotionApply as PromotionApplyModel;

/**
 * 推广页面管理 控制器
 */
class PromotionPage extends BaseAdmin
{
    /**
     * 列表
     * @return array|mixed
     */
    public function lists()
    {
        $promotion_model = new PromotionPageModel();
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $status = input('status', 0);
            $name = input('name', '');
            $identification = input('identification', '');
            $condition = [];
            if ($status != '') {
                $condition[] = ['status', '=', $status];
            }
            if ($name != '') {
                $condition[] = ['name', 'like', '%'.sqlEncode($name).'%'];
            }
            if ($identification != '') {
                $condition[] = ['identification', 'like', '%'.sqlEncode($identification).'%'];
            }

            return $promotion_model->getPromotionPageList($condition, $page, $page_size);
        }
        $this->assign('status_search', $promotion_model->status_search);
        return $this->fetch('promotion_page/list');
    }


    /**
     * 新增
     * @return array|mixed
     */
    public function add()
    {
        $promotion_model = new PromotionPageModel();

        if (request()->isAjax()) {
            $name = input('name', '');
            $identification = input('identification', '');
            $path = input('path', '');
            $data = [
                'name' => $name,
                'identification' => $identification,
                'banners' => trim($path,','),
                'status' => 0,
                'create_time' => time(),
            ];
            $res = $promotion_model->add($data);
            if ($res['code'] == 0) {
                $this->addLog('添加推广页id：'.$res['data']);
            }
            return $res;
        }
        return $this->fetch('promotion_page/add');
    }

    /**
     * [applylists 推广报名列表页]
     * @return [type] [description]
     */
    public function applylists()
    {
        $id = input('id', 0);
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 1);

            return  (new PromotionApplyModel())->getPromotionApplyList(['apply_id' => $id], $page, $page_size);
        }
        $this->assign('id', $id);
        return $this->fetch('promotion_page/applylists');
    }

    /**
     * 编辑
     * @return array|mixed
     */
    public function edit()
    {
        $promotion_model = new PromotionPageModel();
        $id = input('id', '0');
        $info = $promotion_model->getInfo($id);

        if (request()->isAjax()) {
            $name = input('name', '');
            $identification = input('identification', '');
            $path = input('path', '');
            $data = [
                'name' => $name,
                'identification' => $identification,
                'banners' => trim($path,','),
                'status' => 0,
                'modify_time' => time(),
            ];
            $res = $promotion_model->edit($id, $data);
            if ($res['code'] == 0) {
                $log_data = [$info['data'], $data];
                $this->addLog('编辑推广页id：'.$id, $log_data);
            }
            return $res;
        }

        if (empty($info['data'])) {
            return $this->redirect('/admin/spreadPoster/add');
        }

        $this->assign('info', $info['data']);
        return $this->fetch('promotion_page/edit');
    }


    /**
     * 上下架
     * @return array
     */
    public function changeStatus()
    {
        $promotion_model = new PromotionPageModel();
        if (request()->isAjax()) {
            $id = input('id', 0);
            $status = input('status', 0);
            if (empty($id)) {
                return error(-1, '参数错误！');
            }
            $data = [
                'status' => $status,
                'modify_time' => time(),
            ];
            $res = $promotion_model->edit($id, $data);
            if ($res['code'] == 0) {
                $str = $status ? '上架推广页id：' : '下架推广页id：';
                $this->addLog($str.$id);
            }
            return $res;
        }
    }
}
