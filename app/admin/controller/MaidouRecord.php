<?php
/**
 * 迈豆流水
 */

namespace app\admin\controller;


use app\model\member\MemberAccount;

class MaidouRecord extends BaseAdmin
{
    /**
     * 账户列表
     * @return mixed
     */
    public function lists()
    {
        $account_model = new MemberAccount();
        $account_type = 'maidou_money';

        if(request()->isAjax()){
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $account_no = input('account_no', '');
            $member_id = input('member_id', '');
            $mobile = input('mobile', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');
            $from_type = input('from_type', '');

            $condition[] = ['a.account_type', '=', $account_type];

            $join = [
                ['member m', 'a.member_id = m.member_id', 'left']
            ];

            // 流水号
            if ($account_no != '') {
                $condition[] = ['a.account_no', '=', $account_no];
            }

            if ($member_id != '') {
                $condition[] = ['a.member_id', '=', $member_id];
            }

            // 手机号
            if ($mobile != '') {
                $condition[] = ['m.mobile', '=', $mobile];
            }

            // 类型
            if($from_type != ''){
                $condition[] = ['a.from_type', '=', $from_type];
            }

            // 时间
            if ($start_date != '' && $end_date != '') {
                $condition[] = ['a.create_time', 'between', [strtotime($start_date), strtotime($end_date)]];
            } else {
                if ($start_date != '' && $end_date == '') {
                    $condition[] = ['a.create_time', '>=', strtotime($start_date)];
                } else {
                    if ($start_date == '' && $end_date != '') {
                        $condition[] = ['a.create_time', '<=', strtotime($end_date)];
                    }
                }
            }
            $order = 'a.id desc';
            $field = 'a.*, m.mobile';
            $lists = $account_model->getMemberAccountPageList($condition, $page, $page_size, $order, $field, 'a', $join);
            foreach($lists['data']['list'] as &$row){
                $row['account_data'] = round($row['account_data']);
                $row['balance'] = round($row['balance']);
            }
            return $lists;
            // return
        }

        $charge_fee = config('withdraw.member_charge_fee');
        $this->assign('charge_fee', $charge_fee);

        $from_type_arr = $account_model->getFromTypeExtArr($account_type);
        $this->assign('from_type_arr', $from_type_arr);
        $this->assign('from_type_arr_json', json_encode($from_type_arr));

        return $this->fetch("maidou_record/lists");
    }

    /**
     * 迈豆流水数据统计
     *
     * @return void
     */
    public function stat(){
        $account_no = input('account_no', '');
        $member_id = input('member_id', '');
        $mobile = input('mobile', '');
        $start_date = input('start_date', '');
        $end_date = input('end_date', '');
        $from_type = input('from_type', '');

        $condition = [];
        $condition[] = ['account_type','=','maidou_money'];

        if ($account_no != '') {
            $condition[] = ['account_no', '=', $account_no];
        }

        if ($member_id != '') {
            $condition[] = ['member_id', '=', $member_id];
        }

        // 手机号
        if ($mobile != '') {
            $condition[] = ['mobile', '=', $mobile];
        }

        // 类型
        if($from_type != ''){
            $condition[] = ['from_type', '=', $from_type];
        }

        // 时间
        if ($start_date != '' && $end_date != '') {
            $condition[] = ['create_time', 'between', [strtotime($start_date), strtotime($end_date)]];
        } else {
            if ($start_date != '' && $end_date == '') {
                $condition[] = ['create_time', '>=', strtotime($start_date)];
            } else {
                if ($start_date == '' && $end_date != '') {
                    $condition[] = ['create_time', '<=', strtotime($end_date)];
                }
            }
        }

        $grant_condition = [
            'from_type', 'in', 'buy_money,refund_pintuan_order,refund_order,pintuan_winning_award,pintuan_not_winning_award,pintuan_invite_award,pintuan_invite_new_member_award,invite_new_register_award,pintuan_complete_leader_award'
        ];

        $consume_condition = [
            'from_type', 'in', 'create_order,create_pintuan_order'
        ];

        $result = [];

        $grant_condition = array_merge($condition, [$grant_condition]);
        $grant_field = "sum(account_data) as grant_maidou, count(id) as grant_times, count(distinct member_id) as grant_people";
        $grant_columns = model('member_account')->getInfo($grant_condition, $grant_field);

        $consume_condition = array_merge($condition, [$consume_condition]);
        $consume_field = "sum(account_data) as consume_maidou, count(id) as consume_times, count(distinct member_id) as consume_people";
        $consume_columns = model('member_account')->getInfo($consume_condition, $consume_field);

        $result = [
            'grant_maidou' => abs((int)$grant_columns['grant_maidou']),
            'grant_times' => $grant_columns['grant_times'],
            'grant_people' => $grant_columns['grant_people'],
            'consume_maidou' =>abs((int) $consume_columns['consume_maidou']),
            'consume_times' => $consume_columns['consume_times'],
            'consume_people' => $consume_columns['consume_people']
        ];
        return success(0, '', $result);
    }

}