<?php
/**
 * APP版本
 */

namespace app\admin\controller;


use app\model\app\App;
use app\model\app\AppChannelRelation;
use app\model\app\AppPlatformChannel;

class AppVersion extends BaseAdmin
{
    /**
     * 渠道列表
     * @return array|mixed
     */
    public function lists()
    {
        $app_model = new App();
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);

            return $app_model->getAppPageList([], $page, $page_size);
        }
        return $this->fetch('app_version/lists');
    }


    /**
     * 添加渠道
     * @return array|mixed
     */
    public function add()
    {
        $app_model = new App();
        if (request()->isAjax()) {
            $platform = input('platform', '');
            $version = input('version', '');
            $build = input('build', '');
            $timlimit = input('timlimit', 0);
            $is_block = input('is_block', 'N');
            $is_must_update = input('is_must_update', 0);
            $app_path = input('app_path', '');
            $note = input('note', '');

            $data = [
                'platform' => $platform,
                'version' => $version,
                'build' => $build,
                'timlimit' => $timlimit,
                'is_block' => $is_block,
                'is_must_update' => $is_must_update,
                'app_path' => $app_path,
                'note' => $note,
                'create_time' => time(),
            ];

            $res = $app_model->addApp($data);
            if($res['code'] == 0){
                $this->addLog('添加APP-id：'.$res['data']);
            }
            return $res;
        }
        return $this->fetch('app_version/add');
    }

    /**
     * 编辑渠道
     * @return array|mixed
     */
    public function edit()
    {
        $app_model = new App();

        $id = input('id', 0);
        $info = $app_model->getInfo(['id'=> $id]);

        if (request()->isAjax()) {
            $platform = input('platform', '');
            $version = input('version', '');
            $build = input('build', '');
            $timlimit = input('timlimit', 0);
            $is_block = input('is_block', 'N');
            $is_must_update = input('is_must_update', 0);
            $app_path = input('app_path', '');
            $note = input('note', '');

            $data = [
                'platform' => $platform,
                'version' => $version,
                'build' => $build,
                'timlimit' => $timlimit,
                'is_block' => $is_block,
                'is_must_update' => $is_must_update,
                'app_path' => $app_path,
                'note' => $note,
                'update_time' => time(),
            ];

            $res = $app_model->editApp($data, ['id' => $id]);
            if($res['code'] == 0){
                $log_data = [$info['data'], $data];
                $this->addLog('编辑APP-id：'.$id, $log_data);
            }
            return $res;
        }

        $this->assign('info', $info['data']);

        return $this->fetch('app_version/edit');
    }

    /**
     * 开启|禁用审核模式
     * @return array
     */
    public function check()
    {
        $app_model = new App();
        $id = input('id', 0);
        $check_on_off = input('check_on_off', 0);

        $res = $app_model->editApp(['check_on_off' => $check_on_off], ['id' => $id]);
        $str = $check_on_off ? '开启APP审核模式id：' : '关闭APP审核模式id：';
        if($res['code'] == 0){
            $this->addLog($str.$id);
        }
        return $res;
    }

    /**
     * 选择渠道
     * @return array|mixed
     */
    public function choose()
    {
        $app_model = new App();
        $app_platform_channel_model = new AppPlatformChannel();
        $app_channel_relation_model = new AppChannelRelation();
        $id = input('id', 0);
        $info = $app_model->getInfo(['id'=> $id]);

        if(request()->isAjax()){
            $app_id = input('app_id', 0);
            $channel_ids = input('channel_ids', '');

            if(!$app_id || !$channel_ids){
                return error(-1,'非法请求');
            }
            $data = [];
            foreach ($channel_ids as $val){
                $data[] = [
                    'app_id' => $app_id,
                    'channel_id' => $val,
                ];
            }

            $res = $app_channel_relation_model->addList($app_id, $data);
            return $res;
        }

        $channel_list = $app_platform_channel_model->getAppChannelList(['type' => 3], $id);
        $this->assign('channel_list', $channel_list['data']);

        $this->assign('info', $info['data']);

        return $this->fetch('app_version/choose');
    }
}