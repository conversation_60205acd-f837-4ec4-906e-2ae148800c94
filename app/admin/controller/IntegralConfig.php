<?php
/**
 * 积分设置
 */

namespace app\admin\controller;


use app\service\agent\IntegralConfigService;

class IntegralConfig extends BaseAdmin
{
    /**
     * 列表
     * @return array|mixed
     */
    public function lists()
    {
        $integral_config_model = new \app\model\agent\IntegralConfig();
        if (request()->isAjax()) {
            $page_index = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $point_min = input('point_min', '');
            $point_max = input('point_max', '');
            $price_min = input('price_min', '');
            $price_max = input('price_max', '');

            $condition = [];


            if (!empty($point_min) && empty($point_max)) {
                $condition[] = ["integral", ">=", $point_min];
            } elseif (empty($point_min) && !empty($point_max)) {
                $condition[] = ["integral", "<=", $point_max];
            } elseif (!empty($point_min) && !empty($point_max)) {
                $condition[] = ['integral', 'between', [$point_min, $point_max]];
            }

            if (!empty($price_min) && empty($price_max)) {
                $condition[] = ["sales_amount", ">=", $price_min];
            } elseif (empty($price_min) && !empty($price_max)) {
                $condition[] = ["sales_amount", "<=", $price_max];
            } elseif (!empty($price_min) && !empty($price_max)) {
                $condition[] = ['sales_amount', 'between', [$price_min, $price_max]];
            }

            $res = $integral_config_model->getList($condition, $page_index, $page_size);
            return $res;
        } else {
            return $this->fetch('integral_config/lists');
        }
    }


    /**
     * 新增
     * @return array|mixed
     */
    public function add()
    {
        if (request()->isAjax()) {
            $integral = input('integral', 0);
            $sales_amount = input('sales_amount', 0);
            $discount = input('discount', 0);
            if(!$integral || !$sales_amount || !$discount){
                return error(-1, '参数有误');
            }
            $data = [
                'integral' => $integral,
                'sales_amount' => $sales_amount,
                'discount' => $discount
            ];
            $res = IntegralConfigService::getInstance()->add($data);
            $this->addLog('添加积分设置id:' . $res);
            return success(0, '添加成功');
        }
        return $this->fetch('integral_config/add');
    }

    /**
     * 编辑
     * @return array|mixed
     */
    public function edit()
    {
        $integral_config_model = new \app\model\agent\IntegralConfig();
        $id = input('id', 0);
        $condition = [ ['id', '=', $id] ];
        $res = $integral_config_model->getInfo($condition);
        $info = $res['data'];
        if (request()->isAjax()) {
            $id = input('id', 0);
            $integral = input('integral', 0);
            $sales_amount = input('sales_amount', 0);
            $discount = input('discount', 0);
            if(!$integral || !$sales_amount || !$discount){
                return error(-1, '参数有误');
            }
            $data = [
                'id' => $id,
                'integral' => $integral,
                'sales_amount' => $sales_amount,
                'discount' => $discount
            ];
            $res = IntegralConfigService::getInstance()->edit($data);
            $log_data = [$info, $data];
            $this->addLog('编辑积分设置id:'.$id, $log_data);
            return $res;
        }

        $this->assign('info', $info);

        return $this->fetch('integral_config/edit');
    }
}