
/**
 * 控制开关操作
 * @param {object} _this 
 * @param {string} url 
 * @param {intger} value 
 * @param {intger} manager_id 
 * @param {intger} config_type 
 */
function switchClick(_this, url, value, manager_id, config_type) {
    /* if($(_this).is(':checked')){      //打开按钮
        //请求独立配置
        $.ajax({
            url: url,
            data: {open_value:value, manager_id:manager_id, config_type:config_type},
            dataType: 'JSON',
            type: 'POST',
            success: function (res) {
                if (res.code == 0) {
                    layer.msg(res.message);
                    let obj_name = $(_this).attr('name');
                    $('input[name='+obj_name+']').not(_this).prop('checked',false);
                    $('input[name='+obj_name+']').not(_this).siblings('.layui-form-switch').removeClass('layui-form-onswitch');
                    table.reload();
                }else{
                    layer.msg(res.message);
                    $(_this).prop('checked',false);
                    $(_this).siblings('.layui-form-switch').removeClass('layui-form-onswitch');
                }
            }
        });
    }else{
        layer.msg('不可以全部关闭');
        $(_this).prop('checked',true);
        $(_this).siblings('.layui-form-switch').addClass('layui-form-onswitch');
    } */

    $.ajax({
        url: url,
        data: { open_value: value, manager_id: manager_id, config_type: config_type },
        dataType: 'JSON',
        type: 'POST',
        success: function (res) {
            layer.msg(res.message);
            if (res.code == 0) {
                // let obj_name = $(_this).attr('name');
                // $('input[name='+obj_name+']').not(_this).prop('checked',false);
                // $('input[name='+obj_name+']').not(_this).siblings('.layui-form-switch').removeClass('layui-form-onswitch');
                table.reload();
                rechangeOpenText($('.ns-head-open label'), '', value);
            } else {
                // $(_this).prop('checked',false);
                // $(_this).siblings('.layui-form-switch').removeClass('layui-form-onswitch');
                if (value == 1) {
                    $(_this).prop('checked', false);
                    $(_this).siblings('.layui-form-switch').removeClass('layui-form-onswitch');
			        rechangeOpenText($('.ns-head-open label'), '', 0);
                } else {
                    $(_this).prop('checked',true);
                    $(_this).siblings('.layui-form-switch').addClass('layui-form-onswitch');
			        rechangeOpenText($('.ns-head-open label'), '', 1);
                }
            }
        }
    });

}

/**
 * 
 * @param {int} manager_id 
 * @param {string} url 
 * @param {integer} config_type 
 * @param {func} func 
 */
function reloadOpenConfig(manager_id, url, config_type, callbackFunc) {
    $.ajax({
        url: url,
        data: { status: status, goods_ids: goods_ids, manager_id: manager_id, config_type: config_type },
        dataType: 'JSON',
        type: 'POST',
        success: function (res) {
            if (res.code == 0) {
                if (callbackFunc != null && typeof callbackFunc == 'function') {
                    callbackFunc(res.data);
                }
            } else {
                layer.msg(res.message);
            }
        }
    });
}

/**
 * 设置经理名称
 * @param {object} obj 
 * @param {string} manager_name 
 */
function rechangeManagerName(obj, manager_name) {
    obj.text(manager_name);
}

/**
 *  按钮文案修改
 * @param {object} obj 
 * @param {string} text 
 * @param {int} value 
 */
function rechangeOpenText(obj, text, value) {
    if (text != null && text != '') {   //有传text
        obj.text(text);
        return;
    }
    console.log(value);
    if (value == 1) {
        var text = "已启用，商品独立控制";
    } else {
        var text = "已关闭，商品由平台控制";
    }
    obj.text(text);
}