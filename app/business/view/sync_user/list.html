{extend name="base"/}
{block name="resources"}
<style>
	/*.ns-reason-box{display: none;width: 350px;box-sizing: border-box;padding: 20px;border: 1px solid #aaa;border-radius: 5px;background-color: #FFF;position: absolute;top: 50px;z-index: 999;color: #666;line-height: 30px;left: 0px;font-weight: normal;}
	.ns-balance-box {text-align: left; left: unset; right: -270px;}
	.ns-reason-box:before, .ns-reason-box:after{content: "";border: solid transparent;height: 0;position: absolute;width: 0;}
	.ns-reason-box:before{border-width: 12px;border-bottom-color: #aaa;top: -12px;left: 43px;border-top: none;}
	.ns-reason-growth:before{left: 56px;}
	.ns-reason-box:after{border-width: 10px;border-bottom-color: #FFF;top: -20px;left: 45px;}
	.ns-reason-growth:after{left: 58px;}
	.ns-reason-box p{white-space: normal;line-height: 1.5;}
	.layui-table-header{overflow: inherit;}
	.layui-table-header .layui-table-cell{overflow: inherit;}
	.ns-prompt .iconfont{font-size: 16px;color: rgba(0,0,0,0.7);cursor: pointer;font-weight: 500;margin-left: 3px;}
	.ns-prompt-block.balance {justify-content: flex-end;}
	.layui-form-item .layui-form-checkbox[lay-skin=primary] {margin-top: 0;}*/
	.layui-card-body{display: flex;justify-content: space-around;}
    .layui-card-body .money{font-size: 20px;color: #666;font-weight: bold;margin-top: 10px;text-align: center;max-width: 250px;}
    .layui-card-body .subhead{font-size: 12px;margin-left: 3px;cursor: pointer;}
    .ns-shop-account{display: flex;align-items: center;position: relative;padding: 15px;box-sizing: border-box;}
    .ns-shop-detail p{display: inline-block;width: 300px;line-height: 30px;}
    .ns-shop-account>a{position: absolute;right: 15px;bottom: 15px;cursor: pointer;}
    .ns-item-block-parent{margin-top: 10px;}
</style>
<link rel="stylesheet" type="text/css" href="ADMIN_CSS/member.css" />
{/block}


{block name="main"}
{include file="select/manage" /}
<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <div>
            <span class="ns-card-title">销售概况</span>
        </div>
    </div>
    <div class="layui-card-body">
        <div class="content">
            <p class="title">用户类型</p>
            <p class="money">{$info.roleStr}</p>
        </div>
        <div class="content">
            <p class="title">销售总数量</p>
            <p class="money">{$info.saleNum}</p>
        </div>
        <div class="content">
            <p class="title">销售总金额</p>
            <p class="money">{$info.saleAmount}</p>
        </div>

    </div>
</div>

<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">筛选</h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<div class="layui-input-inline">
						<label class="layui-form-label">店铺名称</label>
						<input type="text" name="site_name" placeholder="" autocomplete="off" class="layui-input">
					</div>
					<!-- <div class="layui-input-inline">
						<label class="layui-form-label">店主昵称</label>
						<input type="text" name="username" placeholder="" autocomplete="off" class="layui-input">
					</div> -->
					<div class="layui-input-inline">
						<label class="layui-form-label">店主手机号</label>
						<input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
					</div>
					<!-- <div class="layui-inline">
						<label class="layui-form-label">下单时间</label>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" name="reg_time_start" id="reg_start_date" placeholder="请输入开始时间" autocomplete="off" readonly>
						</div>
						<div class="layui-input-inline ns-split">-</div>
						<div class="layui-input-inline end-time">
							<input type="text" class="layui-input" name="reg_time_end" id="reg_end_date" placeholder="请输入结束时间" autocomplete="off" readonly>
						</div>
					</div> -->
				</div>
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="export">批量导出</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
			<!-- <div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">下单时间</label>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" name="reg_time_start" id="reg_start_date" placeholder="请输入开始时间" autocomplete="off" readonly>
						</div>
						<div class="layui-input-inline ns-split">-</div>
						<div class="layui-input-inline end-time">
							<input type="text" class="layui-input" name="reg_time_end" id="reg_end_date" placeholder="请输入结束时间" autocomplete="off" readonly>
						</div>
					</div>
			</div> -->
			<!-- <div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="export">批量导出</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div> -->
		</form>
	</div>
</div>

<!-- 列表 -->
<table id="member_list" lay-filter="member_list"></table>

<!-- 用户信息 -->
<script type="text/html" id="userdetail">
	<div class='ns-table-tuwen-box'>
		<div class='ns-img-box'>
            <img layer-src src="{{ns.img(d.headimg)}}" onerror="this.src = 'ADMIN_IMG/default_headimg.png' ">
		</div>
		<div class='ns-font-box' style="display: none">
			<p class="layui-elip">{{d.nickname}}</p>
		</div>
	</div>
</script>

<!-- 会员标签 -->
<script id="member_label" type="text/html">
	{{# if (d.member_label_name != null) { }}
		{{# var arr = d.member_label_name.split(",") }}
		<div id="member_label_dl">
		{{# for (var index in arr) { }}
			{{'<span>' + arr[index] + '</span>'}}
		{{# } }}
		</div>
	{{# } }}
</script>


{/block}
{block name="script"}
<script type="text/javascript">
	var table, form, laytpl, laydate, 
		repeat_flag = false, 
		currentDate = new Date(), 
		minDate = "",
		layer_pass,
		layer_label;
		
	layui.use(['form', 'laytpl', 'laydate'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		laydate = layui.laydate;
		currentDate.setDate(currentDate.getDate() - 7);
		form.render();

		//注册开始时间
		laydate.render({
			elem: '#reg_start_date',
			type: 'datetime'
		});

		//注册结束时间
		laydate.render({
			elem: '#reg_end_date',
			type: 'datetime'
		});

		/**
		 * 重新渲染结束时间
		 * */
		function reRender() {
			$("#reg_end_date").remove();
			$(".end-time").html('<input type="text" class="layui-input" name="reg_end_date" id="reg_end_date" placeholder="请输入结束时间">');
			laydate.render({
				elem: '#reg_end_date',
				min: minDate
			});
		}


		/**
		 * 加载表格
		 */
		table = new Table({
			elem: '#member_list',
			url: ns.url("business/sync_user/list"),
			cols: [
				[
					{
						field: 'xm_uid',
						title: 'ID',
						width: '5%',
						unresize: 'false',
					}, {
						field: 'uni_mobile',
						title: '手机号',
						width: '8%',
						unresize: 'false',
						templet: '#mobile'
					// }, {
					// 	field: 'roleStr',
					// 	title: '用户类型',
					// 	width: '10%',
					// 	unresize: 'false',
					}, {
						field: 'site_name',
						title: '店铺名称',
						width: '8%',
						unresize: 'false'
					}, {
						field: 'saleNum',
						title: '销售数量',
						width: '8%',
						unresize: 'false'
					}, {
						field: 'saleAmount',
						title: '销售金额',
						width: '8%',
						unresize: 'false'
					}
				]
			],
			// bottomToolbar: "#batchOperation"
		});
		
		/**
		 * 监听工具栏操作
		 */
		 table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'info': //编辑
					location.href = ns.url("business/sync_user/editMember?member_id=" + data.member_id);
					break;
				case 'point_list': //积分明细
					location.href = ns.url("business/sync_user/pointList?member_id=" + data.member_id);
					break;
				case 'delete': //删除
					delMember(data.member_id);
					break;
				case 'reset_pass': //重置密码
					resetPassword(data);
					break;
				case 'set_label': //设置标签
					settingLabels(data.member_id);
					break;
				case 'more': //更多
					$('.more-operation').css('display', 'none');
					$(obj.tr).find('.more-operation').css('display', 'none');
					break;
			}
		});

		$(document).click(function(event) {
			if ($(event.target).attr('lay-event') != 'more' && $('.more-operation').not(':hidden').length) {
				$('.more-operation').css('display', 'none');
			}
		});

		/**
		 * 批量操作
		 */
		table.bottomToolbar(function(obj) {
			
			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}

			switch (obj.event) {
				case "del":
					var id_array = new Array();
					for (i in obj.data) id_array.push(obj.data[i].member_id);
					delMember(id_array.toString());
					break;
				case "setlabel":
					var id_array = new Array();
					for (i in obj.data) id_array.push(obj.data[i].member_id);
					settingLabels(id_array.toString());
					break;
			}
		});

		/**
		 * 删除
		 */
		function delMember(member_ids) {

			if (repeat_flag) return false;
			repeat_flag = true;
			
			layer.confirm('删除该会员，同时会删除相关账户，请谨慎操作！', function() {
				$.ajax({
					url: ns.url("business/sync_user/deleteMember"),
					data: {member_ids},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 重置密码
		 */
		function resetPassword(data) {
			laytpl($("#pass_change").html()).render(data, function(html) {
				layer_pass = layer.open({
					title: '重置密码',
					skin: 'layer-tips-class',
					type: 1,
					area: ['550px'],
					content: html,
				});
			});
		}
		
		form.on('submit(repass)', function(data) {
			
			if (repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				type: "POST",
				url: ns.url("business/sync_user/modifyPassword"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.closeAll('page');
					}
				}
			});
		});

		
		
		form.on('submit(setlabel)', function(obj) {
			if (repeat_flag) return false;
			repeat_flag = true;

			var field = obj.field;
			var arr_id = [];
			
			for (var prop in field) {
				if (prop == 'member_ids') {
					continue;
				}
				arr_id.push(field[prop]);
			}
			
			$.ajax({
				type: "POST",
				url: ns.url("business/sync_user/modifyLabel"),
				data: {
					'member_ids': field.member_ids,
					'label_ids': arr_id.toString()
				},
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						table.reload();
						layer.closeAll('page');
					}
				}
			});
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});

        /**
         *  导出
         */
        form.on('submit(export)', function(data) {
            location.href = ns.url("business/sync_user/export",data.field);
            return false;
        });

		$(".search-form").click(function() {
			$(".layui-form-search").show();
			$(this).hide();
		});

		$(".form-hide-btn").click(function() {
			$(".layui-form-search").hide();
			$(".search-form").show();
		});

		/**
		 * 表单验证
		 */
		form.verify({
			repass: function(value) {
				if (value != $(".new_pass").val()) {
					return "输入错误,两次密码不一致!";
				}
			}
		});

	});
	
	function closePass() {
		layer.close(layer_pass);
	}
	
	function closeLabel() {
		layer.close(layer_label);
	}

	function export_all() {
		var id_array = new Array();
		$('input:checkbox:checked').each(function(i, item){
			console.log(i);
			console.log(item);
			// console.log(this.val());
		});
		return false;
	}
</script>

<!-- 重置密码弹框html -->
<script type="text/html" id="pass_change">
	<div class="layui-form" id="reset_pass" lay-filter="form">

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码：</label>
			<div class="layui-input-block">
				<input type="password" name="password" lay-verify="required" class="layui-input ns-len-mid new_pass" maxlength="18">
			</div>
		</div>
		
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码：</label>
			<div class="layui-input-block">
				<input type="password" name="password" lay-verify="repass|required" class="layui-input ns-len-mid" maxlength="18">
			</div>
			<div class="ns-word-aux mid">请再一次输入密码，两次输入密码须一致</div>
		</div>
		
		<div class="ns-form-row mid">
			<button class="layui-btn ns-bg-color" lay-submit lay-filter="repass">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>

		<input class="reset-pass-id" type="hidden" name="member_ids" value="{{d.member_id}}"/>
	</div>
</script>

{/block}