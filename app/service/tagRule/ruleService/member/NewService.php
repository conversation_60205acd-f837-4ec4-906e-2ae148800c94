<?php


namespace app\service\tagRule\ruleService\member;

use app\model\member\MemberModel;
use app\model\order\OrderModel;
use app\service\tagRule\ruleService\TagRuleBaseService;
use Exception;
use think\facade\Db;

class NewService extends TagRuleBaseService
{
    /**
     *
     * @param array $param
     * @return array   memberids 用户id集合
     */
    public function handle($param = []){
        $rule_value = json_decode($param['rule_value'], true);
        $select_1 = $rule_value[0]['content'];
        // dd($select_1, array_column($select_1, 'value','selected'));
        $value_1 = array_column($select_1, 'value','selected')[1];
        if($value_1 == 1){  //新用户 1
            $memberids = MemberModel::where("member_id", "NOT IN", function($query){
                $query->table("xm_order")->where([['pay_status', '=', 1]])->group("member_id")->field("member_id");
            })->column("member_id");
        }else{  //旧用户 2
            $memberids = OrderModel::where([['pay_status', '=', 1]])->group("member_id")->column("member_id");
        }
        return $memberids;
    }
}

