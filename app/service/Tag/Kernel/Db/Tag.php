<?php


namespace app\service\Tag\Kernel\Db;


use think\Exception;

class Tag extends TagDbBase
{
    // 内部自己实现的检查方法，不做通用
    private function check($data)
    {
        // 检查key重复
        if (isset($data['key']) && $this->exists(['key' => $data['key']])) {
            throw new Exception('存在同样的key');
        }
    }

    public function add(array $data)
    {
        $this->check($data);
        return parent::add($data);
    }

    public function edit(int $id, array $data)
    {
        $this->check($data);
        return parent::edit($id, $data);
    }
}