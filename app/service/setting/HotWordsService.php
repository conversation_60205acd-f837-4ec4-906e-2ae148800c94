<?php
/**
 * Created By luoshiqiang
 * User: luoshiqiang
 * Date: 2021/8/27
 * Time: 2:36 下午
 */


namespace app\service\setting;

use app\service\BaseService;

class HotWordsService extends BaseService
{
    /**
     * 获取热门词列表
     * @param $param
     * @return array
     */
    public function getHotWordPageList($param)
    {
        $condition = [];
        if (isset($param['keyword']) && $param['keyword']) {
            $condition[] = ['word', 'like', '%'. $param['keyword'] .'%'];
        }

        if(!empty($param['start_time'])){
            $condition[] = ["create_time", ">=", strtotime($param['start_time'] . " 00:00:00")];
        }

        if(!empty($param['end_time'])){
            $condition[] = ["create_time", "<=", strtotime($param['end_time'] . " 23:59:59")];
        }

        $condition[] = ['status', '>', -1];

        $field = "id, word, create_time, update_time, status, sort";
        $order = $param['order'] ?? 'id desc';
        $page = $param['page'];
        $page_size = $param['page_size'];

        $lists = model('shop_goods_hot_words')->pageList($condition, $field, $order, $page, $page_size);
        $lists = $this->formatterListData($lists);
        return $this->success($lists);
    }


    /**
     * Notes: 格式化列表数据
     * User: luoshiqiang
     * Date: 2021/8/27
     * Time: 4:52 下午
     * @param array $data
     * @return array
     */
    protected function formatterListData(array $data)
    {
        if (!empty($data['list'])) {
            foreach ($data['list'] as $key => $value) {
                $data['list'][$key]['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $data['list'][$key]['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
            }
        }
        return $data;
    }


    /**
     * Notes: 添加关键字
     * User: luoshiqiang
     * Date: 2021/8/27
     * Time: 4:52 下午
     * @param $data
     * @return array
     * @throws \Exception
     */
    public function addHotWord($data){
        $checkRepeat = model('shop_goods_hot_words')->getValue([['word', '=', $data['word']], ['status', '<>', -1]], 'id');
        if ($checkRepeat) {
            throw new \Exception('关键字已存在, 请勿重复添加');
        }

        $data['update_time'] = time();
        $data['create_time'] = time();

        $res = model("shop_goods_hot_words")->add($data);
        return $this->success($res);
    }


    /**
     * Notes: 更新关键字
     * User: luoshiqiang
     * Date: 2021/8/27
     * Time: 4:52 下午
     * @param array $data
     * @param int $id
     * @return array
     * @throws \Exception
     */
    public function updateHotWord(array $data, int $id)
    {
        $checkRepeat = model('shop_goods_hot_words')->getValue([['word', '=', $data['word']], ['id', '<>', $id], ['status', '<>', -1]], 'id');
        if ($checkRepeat) {
            throw new \Exception('关键字已存在, 请勿重复添加');
        }

        $data['update_time'] = time();
        $res = model('shop_goods_hot_words')->update($data, ['id' => $id]);
        return $this->success($res);

    }


    /**
     * Notes:通过id获取关键字详情
     * User: luoshiqiang
     * Date: 2021/8/27
     * Time: 4:52 下午
     * @param int $id
     * @return mixed
     */
    public function getInfoById(int $id){
        return model('shop_goods_hot_words')->getInfo(['id' => $id], '*');
    }


    /**
     * Notes:更改状态
     * User: luoshiqiang
     * Date: 2021/8/27
     * Time: 3:03 下午
     * @param int $id
     * @param int $status
     * @return array
     */
    public function changeStatus(int $id, int $status)
    {
        $res = model("shop_goods_hot_words")->update([
            'status' => $status,
            'update_time' => time()
        ], ['id' => $id]);
        return $this->success($res);
    }
}