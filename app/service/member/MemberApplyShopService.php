<?php


namespace app\service\member;


use app\service\BaseService;
use app\service\shop\MemberOpenShopService;

/**
 * 会员申请开店
 * Class MemberApplyShopService
 * @package app\service\member
 */
class MemberApplyShopService extends BaseService
{
    /**
     * 申请开店条件
     * @param $member_id 会员id
     * @return array [consumption=>本人累计消费金额,recommend=>有效推荐人数]
     */
    public function upgradeShopCondition($member_id){
        // 会员累计消费金额
        $consumption = model('member')->getValue(['member_id'=>$member_id],'order_complete_money',0);
        // 有效推荐消费人数
        $recommend = model('members_order_recommend')->getCount([['parent_member','=',$member_id],['status','<',3]]);

        return ['consumption' => (float)$consumption,'recommend' => $recommend];
    }

    /**
     * 获取会员申请信息
     * @param $member_id 申请会员
     * @return mixed
     */
    public function applyInfo($member_id){
        $info = model('member_apply_shop')->getInfo(['member_id'=>$member_id],'shop_id,member_id,status,wechat_no,real_name,mobile');
        return $info;
    }

    /**
     * 会员申请开店--重新申请则修改status为未审核即可
     * @param $member_id
     * @param $shop_id
     * @param $data [mobile=>手机号，必填,real_name=>真实姓名,wechat_no=>微信号]
     * @return array
     */
    public function apply($member_id,$shop_id,$data){
        if (empty($data['mobile']) || strlen($data['mobile'])<11 || !is_numeric ($data['mobile'])){
            return $this->error('','手机号格式不正确！');
        }
        $applyInfo = $this->applyInfo($member_id);
        if ($applyInfo){
            $applyInfo['status']!=1 && model('member_apply_shop')->update(['status'=>0,'shop_id'=>$shop_id,'update_time'=>time(),'wechat_no'=>$data['wechat_no']??'','real_name'=>$data['real_name']??''],['member_id'=>$member_id]);
            return $this->success();
        }
        $parent_member = model('member_recommend')->getValue(['member_id'=>$member_id],'pid',0);

        $arr = [
            'member_id'     => $member_id,
            'shop_id'       => $shop_id,
            'parent_member' => $parent_member,
            'status'        => 0,
            'wechat_no'     => $data['wechat_no'] ?? '',
            'real_name'     => $data['real_name'] ?? '',
            'mobile'        => $data['mobile'],
            'update_time'   => time(),
            'create_time'   => time()
        ];

        model('member_apply_shop')->add($arr);
        return $this->success();
    }

    /**
     * 待审核店铺列表
     * @param $condition
     * @param string $alias
     * @param string $field
     * @param string $order
     * @param int $page
     * @param int $page_size
     * @return array
     */
    public function pendingShop($condition,$alias='a',$field='*',$order='',$page=1,$page_size=PAGE_LIST_ROWS){
        $alias = empty($alias)? 'a':$alias;
        $field = empty($field)? '*':$field;
        $order = empty($order)? $alias.'.update_time desc':$order;
        $join = [
            ['shop s',$alias.'.shop_id=s.site_id','inner'],
            ['member m',$alias.'.member_id=m.member_id','inner']
        ];
        $list = model('member_apply_shop')->pageList($condition,$field,$order,$page,$page_size,$alias,$join);
        $member_ids = array_column($list['list'],'member_id');

        $recommend = model('member_recommend')->getList(['mr.pid'=>$member_ids],'mr.pid,count(*) recommend,count(mor.id) recommend_order,sum(mor.order_money) recommend_money','','mr',[['members_order_recommend mor','mr.pid=mor.parent_member','left']],'mr.pid');
        $recommend_count = [];
        foreach ($recommend as $vr){
            $recommend_count[$vr['pid']] = $vr;
        }

        foreach ($list['list'] as $key => $v){
            $list['list'][$key]['recommend'] = $recommend_count[$v['member_id']]['recommend'] ?? 0;
            $list['list'][$key]['recommend_order'] = $recommend_count[$v['member_id']]['recommend_order'] ?? 0;
            $list['list'][$key]['recommend_money'] = $recommend_count[$v['member_id']]['recommend_money'] ?? 0;

            if(!empty($value['mobile']))
                $list['list'][$key]['mobile'] = substr_replace($v['mobile'], '****', 3, 4);//联系电话
        }
        return $this->success($list);

    }

    /**
     * 申请开店详情
     * @param $id
     * @return array
     */
    public function pendingShopInfo($id){
        $join = [
            ['shop s','a.shop_id=s.site_id','inner'],
            ['member m','m.member_id=a.member_id','inner'],
        ];
        $applyInfo = model('member_apply_shop')->getInfo(['a.id'=>$id],'a.*,s.username,s.site_name,m.order_complete_money,m.nickname','a',$join);
        if (empty($applyInfo)) return $this->error('','参数错误！');

        $applyInfo['recommend_mobile'] = model('member')->getValue(['member_id'=>$applyInfo['parent_member']],'mobile');

        $recommend = model('member_recommend')->getInfo(['mr.pid'=>$applyInfo['member_id']],'mr.pid,count(*) recommend,count(mor.id) recommend_order,sum(mor.order_money) recommend_money','mr',[['members_order_recommend mor','mr.pid=mor.parent_member','left']]);
        $applyInfo['recommend'] = $recommend['recommend'] ?: 0;
        $applyInfo['recommend_order'] = $recommend['recommend_order'] ?: 0;
        $applyInfo['recommend_money'] = $recommend['recommend_money'] ?: 0;
        $applyInfo['update_time'] = date('Y-m-d H:i:s', $applyInfo['update_time']);
        $applyInfo['extend'] = model('member_apply_shop_extend')->getInfo(['member_apply_shop_id'=>$id],"CONCAT_WS(' ',province,city,area) as position,profession,skill,society_experience,society_num,images");
        $applyInfo['extend'] && $applyInfo['extend']['images'] = json_decode($applyInfo['extend']['images'],true);

        return $applyInfo;
    }

    /**
     * 审核会员的开店申请
     * @param $id 申请id
     * @param $status 审核状态【0=待审核，1=审核通过，2=审核不通过】
     * @return array
     */
    public function adjustMemberShop($id,$status){
        $info = model('member_apply_shop')->getInfo(['id'=>$id],'shop_id,member_id,status,wechat_no,real_name,mobile');
        $arr = ['status'=>$status,'update_time'=>time()];

        if ($info['status']!=0)
            return $this->error('','慢了一步，被其他人审核了');
        model('member_apply_shop')->startTrans();

        if ($status==1){
            $condition = $this->upgradeShopCondition($info['member_id']);
            // 申请开店的条件
            $shop_condition = config('member_upgrade');
            $shop_condition = isTest() ? $shop_condition['upgrade_shop']['dev'] : $shop_condition['upgrade_shop']['prod'];
            if (true ||$condition['consumption']>=$shop_condition['consumption'] || $condition['recommend']>=$shop_condition['recommend']){
                // 开店
                $openLog['member_id']   = $info['member_id'];
                $openLog['mobile']      = $info['mobile'];
                $openLog['from_shop']   = $info['shop_id'];
                $openLog['money']       = 0; //开通vip店铺价格配置
                $openLog['expire_days'] = config('open_vip_shop.1.day') ?? 0; //开通vip店铺天数配置
                $openLog['is_vip']      = 1; // 开通为vip店铺

                $res = MemberOpenShopService::getInstance()->implementOpenShop('',$openLog,2);
                if ($res['code']==0){
                    model('member_apply_shop')->update($arr,['id'=>$id]);
//                    $dr = $this->dealMemberRecommend($info['member_id'],$res['data']);$res['dr'] = $dr;
                    model('member_apply_shop')->commit();
                }else{
                    model('member_apply_shop')->rollback();
                }
                return $res;
            }else{
                model('member_apply_shop')->rollback();
                return $this->error('','审核失败，该会员尚未达到开店条件！');
            }
        }

        model('member_apply_shop')->update($arr,['id'=>$id]);
        model('member_apply_shop')->commit();
        return $this->success('','审核完成！');
    }

    /**
     * 处理会员的推荐关系
     * @param $member_id
     * @param $new_shop_id 需要新绑的店铺
     * @return array
     */
    public function dealMemberRecommend($member_id,$new_shop_id){

        $recommend = model('member_recommend')->getValue(['member_id'=>$member_id],'related_ids');
        if (empty($recommend))
            return $this->error('','不存在推荐关系');

        $time = time();
        model('member_recommend')->startTrans();

        // 创建临时表，
        $sql = "CREATE TEMPORARY table  if not exists tmp_shop_member (
                member_id int(10) unsigned NOT NULL DEFAULT '0',
                shop_id int(10) unsigned NOT NULL DEFAULT '0'
            )";
        model()->query($sql);
        // 添加需要处理关系的数据
        $sql1 = "insert into tmp_shop_member(member_id,shop_id) 
                SELECT mr.member_id, sm.site_id from xm_member_recommend mr 
                left join xm_shop_member sm  on sm.member_id=mr.member_id and (sm.expire_time>=".$time." or sm.expire_time=-1) 
                where mr.related_ids like '".$recommend."%' and mr.member_id!=".$member_id;
        model()->query($sql1);
        // 解绑会员现绑的店铺
        $sql6 = "insert into xm_shop_member_change_log (shop_id,member_id,action,create_time,remark) 
                SELECT shop_id,member_id,2,".$time.",'上级开店，同步推荐关系，解除原绑的店铺' from tmp_shop_member WHERE shop_id>0";
        model()->query($sql6);

        $sql2 = "update xm_shop_member sm INNER JOIN tmp_shop_member tmp on tmp.member_id=sm.member_id and tmp.shop_id=sm.site_id set unlock_time=".$time.", sm.expire_time=0,is_subscribe=0 WHERE tmp.shop_id>0";
        model()->query($sql2);
        // 绑定新店铺，日志
        $sql3 = "insert into xm_shop_member (member_id,site_id,subscribe_time,create_time,expire_time,lock_time) 
              SELECT member_id,".$new_shop_id.",".$time.",".$time.",-1,".$time." from tmp_shop_member";
        model()->query($sql3);
        $sql4 = "insert into xm_shop_member_change_log (shop_id,member_id,action,create_time,remark) 
              SELECT ".$new_shop_id.",member_id,1,".$time.",'上级开店，同步推荐关系，绑定到新店铺' from tmp_shop_member";
        model()->query($sql4);
        // 更新推荐关系
        $sql5 = "update xm_member_recommend set related_ids = REPLACE(related_ids,'".$recommend."','-".$member_id."-') where related_ids like '".$recommend."%'";
        model()->query($sql5);
        // 删除临时表
        model()->query('DROP TABLE tmp_shop_member');

        model('member_recommend')->commit();

    }
}