<?php
/**
 * 初始化商品规格
 */

namespace app\service\init;

use app\service\BaseService;
use think\facade\Db;
use think\facade\Log;

class GoodsCategoryService extends BaseService
{
    public static $c_arr = [];
    public static $c_arr_temp = [];
    // 初始化
    public function init()
    {
        $this->setCategoryList();
    }

    /**
     * 分类转移
     */
    public function setCategoryList(){
//        $condition = [['id' , '<' , 10],['id','>',2]];
        $condition = [];
        $list = $this->getList($condition);
        self::formatTree($list);
        $res = $this->insertCategory(self::$c_arr);
    }

    /**
     * 获取分类列表
     * @param array $condition
     * @return mixed
     */
    public function getList($condition = []){
        return model('goods_category')->getList($condition);
    }

    /**
     * 插入数据
     * @param $list
     */
    public function insertCategory($list){
        $insert = [];
        $xm_category_column = $this->getXmCategoryColumn();
        $goods_category_model = model('goods_category');
        Db::startTrans();
        try{
            foreach ($list as &$row){
//            $image_path = GoodsService::getInstance()->getXmImgUrlById($row['icon']);
                $temp = array(
                    'category_name' => str_replace('/','、',$row['title']),
                    'pid' => $row['pid'],
                    'sort' => $row['sort'],
                    'description' => $row['desc'],
                    'is_show' => $row['is_hide'] == 0?1:0,
//                    'level' => $row['level'],
                    'image' => $row['image'],
//                    'category_full_name' => $row['category_full_name'],  //TODO 分割符当前是斜杠，后面需要换
//                    'category_id_1' => $row['category_id_arr'][0],
//                    'category_id_2' => $row['category_id_arr'][1],
//                    'category_id_3' => $row['category_id_arr'][2],
                    'reward_company_rate' => config('reward_rate')['reward_company_rate']
                );

                if(in_array($row['id'], $xm_category_column)){  #存在了
                    $where = [['xm_id','=', $row['id']]];
                    $update = $temp;
                    $goods_category_model->update($update, $where);
                    $row['action'] = 'update';
                    continue;
                }

//            $temp['category_id'] = $row['id'];
                $temp['xm_id'] = $row['id'];
                $row['action'] = 'insert';
                $insert[] = $temp;
            }

            model('goods_category')->addList($insert);
            $this->updateInsertCategoryLevel();  //更新分类等级
            Db::commit();
            return 0;
        }catch (\Exception $e){
            Db::rollback();
            Log::error('同步分类失败：'.$e->getMessage());
            return 1;
        }

    }

    /**
     * 递归格式化
     * @param $list
     * @param array $category_ida
     * @param array $category_full_name
     * @param int $level
     * @param int $pid
     * @return mixed
     */
    public static function formatTree($list, $category_ida = [], $category_full_name = [],  $level = 0, $pid = 0){
        foreach ($list as $key => &$row){
            if($pid == $row['pid']){
                if(empty($row['category_id_arr']) && $pid == 0){
                    $category_full_name = [];
                    $category_ida = [0, 0, 0];
                }
                if($level < 3) $category_ida[$level] = $row['id'];

                $temp = $category_full_name;
                $temp[] = $row['title'];
                $row['category_full_name'] = implode('/',$temp);
                $row['category_id_arr'] = $category_ida;
                $row['level'] = $level + 1;
                self::$c_arr[$row['id']] = $row;
                self::formatTree($list, $category_ida, $temp, $level + 1, $row['id']);
            }
        }
        return $list;
    }

    /**
     * 递归格式化
     * @param $list
     * @param array $category_ida
     * @param array $category_full_name
     * @param int $level
     * @param int $pid
     * @return mixed
     */
    public static function formatTree_v2($list, $category_ida = [], $category_full_name = [],  $level = 0, $pid = 0){
        foreach ($list as $key => &$row){
            if($pid == $row['pid']){
                if(empty($row['category_id_arr']) && $pid == 0){
                    $category_full_name = [];
                    $category_ida = [0, 0, 0];
                }

//                if($level < 3) $category_ida[$level] = $row['id'];
                if($level > 2){
                    $level = 2;
                    $row['pid'] = $category_ida[1];  // 当前分类等级大于3级 ，父id修改为二级id
                    array_pop($category_full_name);
                }

                $category_ida[$level] = $row['xm_id'];

                $temp = $category_full_name;
                $temp[] = $row['category_name'];
                $row['category_full_name'] = implode('/',$temp);
                $row['category_id_arr'] = $category_ida;
                $row['level'] = $level + 1;
                self::$c_arr[$row['xm_id']] = $row;
                self::formatTree_v2($list, $category_ida, $temp, $level + 1, $row['xm_id']);
            }
        }
        return $list;
    }



    /**
     * 获取分类信息
     * @param $xm_id
     * @return mixed
     */
    public function categoryLevel($xm_id){
        if(empty(self::$c_arr_temp)){  //当前没有才重新去取
            $list = $this->getList();
            self::$c_arr_temp = $this->formatCategoryKey($list);
        }
        return self::$c_arr_temp[$xm_id];
    }

    /**
     * 格式化数据，方便获取
     * @param $list
     */
    public function formatCategoryKey($list){
        $res = [];
        foreach ($list as $row){
            $res[$row['xm_id']] = $row;
        }
        return $res;
    }

    /**
     * 获取先迈分类的id组
     * @return array
     */
    public function getXmCategoryColumn(){
        $condition = [
            ['xm_id', '>', 0]
        ];
        return model('goods_category')->getColumn($condition,'xm_id');
    }


    /**
     * 新插入的数据需要更新分类等级
     * @param $list
     */
    public function updateInsertCategoryLevel(){
        $res = $this->getFormatCategoryList();
//        dump($res);exit;
        foreach ($res as $row){
            $category_level = $row['category_id_arr']??[];
            $update = array(
//                'pid' => $res[$row['pid']]['category_id'],
                'category_id_1' => $res[$category_level[0]]['category_id']??0,
                'category_id_2' => $res[$category_level[1]]['category_id']??0,
                'category_id_3' => $res[$category_level[2]]['category_id']??0,
                'level' => $row['level'],
                'category_full_name' => $row['category_full_name']
            );
            if($row['pid'] > 0) $update['pid'] = $res[$row['pid']]['category_id'];
//            if($row['pid'] > 0) dump($res[$row['pid']]['category_id']);

//            $condition = [['xm_id','=',$row['id']]];
            $condition = [['category_id','=',$row['category_id']]];
            model('goods_category')->update($update,$condition);
        }
    }


    public function getFormatCategoryList(){
        $condition = [['xm_id','>',0]];
        $list = $this->getList($condition);
        self::formatTree_v2($list);
        return self::$c_arr;
    }
}