<?php
/**
 * 学习管理服务层
 */
namespace app\service\study;

use app\model\integral\IntegralConfig;
use app\model\system\Group;
use app\service\BaseService;
use think\facade\Db;
use think\facade\Log;

class StudyService extends BaseService
{
    public function __construct()
    {

    }

    /**
     * store 创建
     *
     * @param array $data 信息
     * @return array
     */
    public function store($data)
    {
        //启动事务
        Db::startTrans();
        try {
            $data['update_time'] = $data['create_time'] = time();

            //添加分销客
            $res = model("study")->add($data);

            Db::commit();
            return $this->success($res);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage());
            Log::error($e->getTraceAsString());
            return $this->error("", $e->getMessage());
        }
    }

    /**
     * edit 详情
     *
     * @param int $id id
     * @return array
     */
    public function edit($id)
    {
        $res = model('study')->getInfo([['id', '=', $id]], '*');

        return $this->success($res);
    }

    /**
     * update 更新
     * @param array $id  id
     * @param array $data 信息
     * @param $condition
     * @return array
     */
    public function update($id, $data)
    {
        $condition = [['id', '=', $id]];
        //启动事务
        Db::startTrans();
        try {
            $studyInfo = model('study')->getInfo($condition, 'id');

            if (!$studyInfo) {
                return $this->error('请填写正确的id');
            }

            $res = model('study')->update($data, $condition);

            if ($res) {
                Db::commit();
                return $this->success($res);
            } else {
                Db::rollback();
                return $this->error('编辑错误，请稍后再试');
            }
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error("", $e->getMessage());
        }
    }

    /**
     * index 获取列表
     *
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @param array $join
     * @param string $group
     * @return array
     */
    public function index($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*', $join = [])
    {
        $list = model('study')->rawPageList($condition, $field, $order, $page, $page_size, 'a', $join);

        return $this->success($list);
    }

    /**
     * changeStatus 修改状态
     *
     * @param int $id id
     * @param int $status 状态，要修改的状态
     * @return array
     */
    public function changeStatus($id, $status)
    {
        $res = model("study")->update(['status' => $status],['id' => $id]);
        return $this->success($res);
    }
}