<?php

namespace app\service\shopapi;


use app\service\BaseService;
use \think\facade\Db;
use think\facade\Cache;
use think\facade\Log;


class ShareBuyService extends BaseService
{
    /**
     * 获取分享赚活动列表数据
     */
    public function getActivityList($params){
    
        /// 初始化参数
        $total          = 0;
        $is_last_page   = 0;
        $page           = $params['page'];
        $size           = $params['size'];
        $shopService    = new ShopService();

        $query = Db::name('share_buy_activity')->alias('sba')->where([
            ['sba.status', '=', 1]
        ]);
        $total = $query->count();
        $query->removeOption('field');
    
        /// 数据
        $field = '
            g.goods_id,
            g.goods_name,
            g.goods_image,
            sba.price,
            sba.shop_money,
            sba.id as sba_id,
            sba.start_time,
            sba.end_time
        ';
        $list = $query->field($field)
        ->leftjoin('goods g', 'g.goods_id = sba.goods_id')
        ->page($page, $size)->select()->toArray(); 

        # 组装数据
        $_l = [];
        if( !empty($list) ){
        
            foreach( $list as $k=>$v){

                $_img = explode(',', $v['goods_image']);

                $_l[$k]['goods_id']                 = $v['goods_id'];
                $_l[$k]['name']                     = $v['goods_name'];
                $_l[$k]['img']                      = $_img[0] ? img($_img[0]) : '';
                $_l[$k]['price']                    = $v['price'];
                $_l[$k]['profit']                   = number_format($v['shop_money'], 2,".","");
                $_l[$k]['share_buy_activity_id']    = $v['sba_id'];
                $_l[$k]['activity_type_txt']        = '分享赚';
                $_l[$k]['start_time']               = empty($v['start_time']) ? '' : date('Y-m-d H:i:s', $v['start_time']);
                $_l[$k]['end_time']                 = empty($v['end_time']) ? '' : date('Y-m-d H:i:s', $v['end_time']);
                $_l[$k]['wechat_path']              = '/promotionpages/fenxiangzhuan/goodsDetail/goodsDetail?activity_id='.$v['sba_id'].'&shop_id='.$params['site_id'];
            }
        }

        $is_last_page = ceil($total/$size) <= $page ? 1 : 0;
        return [
            'wechat_id'     => $shopService->getAdminConfig(['site_id' => 0, 'app_module' => 'admin', 'config_key' => 'WEAPP_CONFIG'], 'weapp_original'),
            'total'         => $total,
            'is_last_page'  => $is_last_page,
            'list'          => $_l
        ];
    }

    /**
     * 获取分享赚活动收益列表数据
     */
    public function getActivityProfit($params){
    
        /// 初始化参数
        $total          = 0;
        $is_last_page   = 0;
        $page           = $params['page'];
        $size           = $params['size'];
        $from_type      = ['activity_share'=>'分享赚', 'xxx'=>'砍价'];# 砍价flag待定

        $query = Db::name('shop_youli_account')->alias('sya')->where('site_id',$params['site_id'])->whereIn('sya.from_type', array_keys($from_type));
        $total = $query->count();
        $query->removeOption('field');
    
        /// 数据
        $field = '
            sya.id,
            sya.from_type,
            sya.account_data,
            CASE
                WHEN sya.from_type="activity_share" THEN
                (select m.mobile from xm_member m left join xm_member_share_buy_activity msba on msba.member_id=m.member_id where msba.id=sya.account_data)
                WHEN sya.from_type="xxx" THEN
                (select m.mobile from xm_member m left join xm_promotion_bargain_record pbr on pbr.member_id=m.member_id where pbr.id=sya.account_data)
            END mobile
        ';
        $list = $query->field($field)->order('sya.id', 'desc')
        ->page($page, $size)->select()->toArray(); 
        
        # 组装数据
        $_l = [];
        if( !empty($list) ){
        
            foreach( $list as $k=>$v){

                $_l[$k]['share_buy_activity_id']    = $v['id'];
                $_l[$k]['activity_type_txt']        = isset($from_type[$v['from_type']]) ? $from_type[$v['from_type']] : '';
                $_l[$k]['mobile']                   = empty($v['mobile']) ? '' : $v['mobile'];
                $_l[$k]['profit']                   = empty($v['account_data']) ? '0' : $v['account_data']/100;# 柚粒数/100
            }
        }

        $is_last_page = ceil($total/$size) <= $page ? 1 : 0;
        return [
            'total'         => $total,
            'is_last_page'  => $is_last_page,
            'list'          => $_l
        ];
    }

    /**
     * 抛出异常操作
     */
    private function throwErr($data){

        if( is_array($data) ){
            $data = json_encode($data);
        }
        throw new \Exception($data);
    }
}