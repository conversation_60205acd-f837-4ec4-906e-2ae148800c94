/*! @sentry/browser (Performance Monitoring) 8.18.0 (c9ea6b8) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){t=window.Sentry||{};const n=Object.prototype.toString;function e(t){switch(n.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return h(t,Error)}}function r(t,e){return n.call(t)===`[object ${e}]`}function o(t){return r(t,"ErrorEvent")}function i(t){return r(t,"DOMError")}function s(t){return r(t,"String")}function c(t){return"object"==typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function u(t){return null===t||c(t)||"object"!=typeof t&&"function"!=typeof t}function a(t){return r(t,"Object")}function f(t){return"undefined"!=typeof Event&&h(t,Event)}function d(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function h(t,n){try{return t instanceof n}catch(t){return!1}}function l(t){return!("object"!=typeof t||null===t||!t.__isVue&&!t.t)}function p(t,n=0){return"string"!=typeof t||0===n||t.length<=n?t:`${t.slice(0,n)}...`}function m(t,n){if(!Array.isArray(t))return"";const e=[];for(let n=0;n<t.length;n++){const r=t[n];try{l(r)?e.push("[VueViewModel]"):e.push(String(r))}catch(t){e.push("[value cannot be serialized]")}}return e.join(n)}function g(t,n,e=!1){return!!s(t)&&(r(n,"RegExp")?n.test(t):!!s(n)&&(e?t===n:t.includes(n)))}function v(t,n=[],e=!1){return n.some((n=>g(t,n,e)))}function y(t,n,e=250,r,o,i,s){if(!(i.exception&&i.exception.values&&s&&h(s.originalException,Error)))return;const c=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;var u,a;c&&(i.exception.values=(u=b(t,n,o,s.originalException,r,i.exception.values,c,0),a=e,u.map((t=>(t.value&&(t.value=p(t.value,a)),t)))))}function b(t,n,e,r,o,i,s,c){if(i.length>=e+1)return i;let u=[...i];if(h(r[o],Error)){_(s,c);const i=t(n,r[o]),a=u.length;w(i,o,a,c),u=b(t,n,e,r[o],o,[i,...u],i,a)}return Array.isArray(r.errors)&&r.errors.forEach(((r,i)=>{if(h(r,Error)){_(s,c);const a=t(n,r),f=u.length;w(a,`errors[${i}]`,f,c),u=b(t,n,e,r,o,[a,...u],a,f)}})),u}function _(t,n){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,..."AggregateError"===t.type&&{is_exception_group:!0},exception_id:n}}function w(t,n,e,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:n,exception_id:e,parent_id:r}}const S="8.18.0",E=globalThis;function $(t,n,e){const r=e||E,o=r.__SENTRY__=r.__SENTRY__||{},i=o[S]=o[S]||{};return i[t]||(i[t]=n())}const T=E,k=80;function I(t,n={}){if(!t)return"<unknown>";try{let e=t;const r=5,o=[];let i=0,s=0;const c=" > ",u=c.length;let a;const f=Array.isArray(n)?n:n.keyAttrs,d=!Array.isArray(n)&&n.maxStringLength||k;for(;e&&i++<r&&(a=x(e,f),!("html"===a||i>1&&s+o.length*u+a.length>=d));)o.push(a),s+=a.length,e=e.parentNode;return o.reverse().join(c)}catch(t){return"<unknown>"}}function x(t,n){const e=t,r=[];if(!e||!e.tagName)return"";if(T.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());const o=n&&n.length?n.filter((t=>e.getAttribute(t))).map((t=>[t,e.getAttribute(t)])):null;if(o&&o.length)o.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else{e.id&&r.push(`#${e.id}`);const t=e.className;if(t&&s(t)){const n=t.split(/\s+/);for(const t of n)r.push(`.${t}`)}}const i=["aria-label","type","name","title","alt"];for(const t of i){const n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}function j(t){if(!T.HTMLElement)return null;let n=t;for(let t=0;t<5;t++){if(!n)return null;if(n instanceof HTMLElement){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}n=n.parentNode}return null}const R=["debug","info","warn","error","log","assert","trace"],M={};function O(t){if(!("console"in E))return t();const n=E.console,e={},r=Object.keys(M);r.forEach((t=>{const r=M[t];e[t]=n[t],n[t]=r}));try{return t()}finally{r.forEach((t=>{n[t]=e[t]}))}}const C=function(){let t=!1;const n={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return R.forEach((t=>{n[t]=()=>{}})),n}(),A=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function L(t,n=!1){const{host:e,path:r,pass:o,port:i,projectId:s,protocol:c,publicKey:u}=t;return`${c}://${u}${n&&o?`:${o}`:""}@${e}${i?`:${i}`:""}/${r?`${r}/`:r}${s}`}function N(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function P(t){const n="string"==typeof t?function(t){const n=A.exec(t);if(!n)return void O((()=>{console.error(`Invalid Sentry Dsn: ${t}`)}));const[e,r,o="",i="",s="",c=""]=n.slice(1);let u="",a=c;const f=a.split("/");if(f.length>1&&(u=f.slice(0,-1).join("/"),a=f.pop()),a){const t=a.match(/^\d+/);t&&(a=t[0])}return N({host:i,pass:o,path:u,projectId:a,port:s,protocol:e,publicKey:r})}(t):N(t);if(n)return n}class D extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}function F(t,n,e){if(!(n in t))return;const r=t[n],o=e(r);"function"==typeof o&&U(o,r),t[n]=o}function q(t,n,e){try{Object.defineProperty(t,n,{value:e,writable:!0,configurable:!0})}catch(t){}}function U(t,n){try{const e=n.prototype||{};t.prototype=n.prototype=e,q(t,"__sentry_original__",n)}catch(t){}}function H(t){return t.__sentry_original__}function B(t){if(e(t))return{message:t.message,name:t.name,stack:t.stack,...W(t)};if(f(t)){const n={type:t.type,target:z(t.target),currentTarget:z(t.currentTarget),...W(t)};return"undefined"!=typeof CustomEvent&&h(t,CustomEvent)&&(n.detail=t.detail),n}return t}function z(t){try{return n=t,"undefined"!=typeof Element&&h(n,Element)?I(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var n}function W(t){if("object"==typeof t&&null!==t){const n={};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=t[e]);return n}return{}}function X(t){return G(t,new Map)}function G(t,n){if(function(t){if(!a(t))return!1;try{const n=Object.getPrototypeOf(t).constructor.name;return!n||"Object"===n}catch(t){return!0}}(t)){const e=n.get(t);if(void 0!==e)return e;const r={};n.set(t,r);for(const e of Object.keys(t))void 0!==t[e]&&(r[e]=G(t[e],n));return r}if(Array.isArray(t)){const e=n.get(t);if(void 0!==e)return e;const r=[];return n.set(t,r),t.forEach((t=>{r.push(G(t,n))})),r}return t}const J=50,Y="?",K=/\(error: (.*)\)/,V=/captureMessage|captureException/;function Q(...t){const n=t.sort(((t,n)=>t[0]-n[0])).map((t=>t[1]));return(t,e=0,r=0)=>{const o=[],i=t.split("\n");for(let t=e;t<i.length;t++){const e=i[t];if(e.length>1024)continue;const s=K.test(e)?e.replace(K,"$1"):e;if(!s.match(/\S*Error: /)){for(const t of n){const n=t(s);if(n){o.push(n);break}}if(o.length>=J+r)break}}return function(t){if(!t.length)return[];const n=Array.from(t);/sentryWrapped/.test(Z(n).function||"")&&n.pop();n.reverse(),V.test(Z(n).function||"")&&(n.pop(),V.test(Z(n).function||"")&&n.pop());return n.slice(0,J).map((t=>({...t,filename:t.filename||Z(n).filename,function:t.function||Y})))}(o.slice(r))}}function Z(t){return t[t.length-1]||{}}const tt="<anonymous>";function nt(t){try{return t&&"function"==typeof t&&t.name||tt}catch(t){return tt}}function et(t){const n=t.exception;if(n){const t=[];try{return n.values.forEach((n=>{n.stacktrace.frames&&t.push(...n.stacktrace.frames)})),t}catch(t){return}}}const rt={},ot={};function it(t,n){rt[t]=rt[t]||[],rt[t].push(n)}function st(t,n){ot[t]||(n(),ot[t]=!0)}function ct(t,n){const e=t&&rt[t];if(e)for(const t of e)try{t(n)}catch(t){}}function ut(){"console"in E&&R.forEach((function(t){t in E.console&&F(E.console,t,(function(n){return M[t]=n,function(...n){ct("console",{args:n,level:t});const e=M[t];e&&e.apply(E.console,n)}}))}))}const at=E;function ft(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function dt(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in at))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}())return!1;if(ft(at.fetch))return!0;let t=!1;const n=at.document;if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e),e.contentWindow&&e.contentWindow.fetch&&(t=ft(e.contentWindow.fetch)),n.head.removeChild(e)}catch(t){}return t}const ht=1e3;function lt(){return Date.now()/ht}const pt=function(){const{performance:t}=E;if(!t||!t.now)return lt;const n=Date.now()-t.now(),e=null==t.timeOrigin?n:t.timeOrigin;return()=>(e+t.now())/ht}(),mt=(()=>{const{performance:t}=E;if(!t||!t.now)return;const n=36e5,e=t.now(),r=Date.now(),o=t.timeOrigin?Math.abs(t.timeOrigin+e-r):n,i=o<n,s=t.timing&&t.timing.navigationStart,c="number"==typeof s?Math.abs(s+e-r):n;return i||c<n?o<=c?t.timeOrigin:s:r})();function gt(t){const n="fetch";it(n,t),st(n,vt)}function vt(){dt()&&F(E,"fetch",(function(t){return function(...n){const{method:r,url:o}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[n,e]=t;return{url:bt(n),method:yt(e,"method")?String(e.method).toUpperCase():"GET"}}const n=t[0];return{url:bt(n),method:yt(n,"method")?String(n.method).toUpperCase():"GET"}}(n),i={args:n,fetchData:{method:r,url:o},startTimestamp:1e3*pt()};ct("fetch",{...i});const s=(new Error).stack;return t.apply(E,n).then((t=>(ct("fetch",{...i,endTimestamp:1e3*pt(),response:t}),t)),(t=>{throw ct("fetch",{...i,endTimestamp:1e3*pt(),error:t}),e(t)&&void 0===t.stack&&(t.stack=s,q(t,"framesToPop",1)),t}))}}))}function yt(t,n){return!!t&&"object"==typeof t&&!!t[n]}function bt(t){return"string"==typeof t?t:t?yt(t,"url")?t.url:t.toString?t.toString():"":""}let _t=null;function wt(t){const n="error";it(n,t),st(n,St)}function St(){_t=E.onerror,E.onerror=function(t,n,e,r,o){return ct("error",{column:r,error:o,line:e,msg:t,url:n}),!(!_t||_t.__SENTRY_LOADER__)&&_t.apply(this,arguments)},E.onerror.__SENTRY_INSTRUMENTED__=!0}let Et=null;function $t(t){const n="unhandledrejection";it(n,t),st(n,Tt)}function Tt(){Et=E.onunhandledrejection,E.onunhandledrejection=function(t){return ct("unhandledrejection",t),!(Et&&!Et.__SENTRY_LOADER__)||Et.apply(this,arguments)},E.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function kt(){const t=E,n=t.crypto||t.msCrypto;let e=()=>16*Math.random();try{if(n&&n.randomUUID)return n.randomUUID().replace(/-/g,"");n&&n.getRandomValues&&(e=()=>{const t=new Uint8Array(1);return n.getRandomValues(t),t[0]})}catch(t){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&e())>>t/4).toString(16)))}function It(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function xt(t){const{message:n,event_id:e}=t;if(n)return n;const r=It(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||e||"<unknown>":e||"<unknown>"}function jt(t,n,e){const r=t.exception=t.exception||{},o=r.values=r.values||[],i=o[0]=o[0]||{};i.value||(i.value=n||""),i.type||(i.type=e||"Error")}function Rt(t,n){const e=It(t);if(!e)return;const r=e.mechanism;if(e.mechanism={type:"generic",handled:!0,...r,...n},n&&"data"in n){const t={...r&&r.data,...n.data};e.mechanism.data=t}}function Mt(t){if(t&&t.__sentry_captured__)return!0;try{q(t,"__sentry_captured__",!0)}catch(t){}return!1}function Ot(t){return Array.isArray(t)?t:[t]}function Ct(t,n=100,e=1/0){try{return Lt("",t,n,e)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function At(t,n=3,e=102400){const r=Ct(t,n);return o=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(o))>e?At(t,n-1,e):r;var o}function Lt(t,n,e=1/0,r=1/0,o=function(){const t="function"==typeof WeakSet,n=t?new WeakSet:[];return[function(e){if(t)return!!n.has(e)||(n.add(e),!1);for(let t=0;t<n.length;t++)if(n[t]===e)return!0;return n.push(e),!1},function(e){if(t)n.delete(e);else for(let t=0;t<n.length;t++)if(n[t]===e){n.splice(t,1);break}}]}()){const[i,s]=o;if(null==n||["number","boolean","string"].includes(typeof n)&&!Number.isNaN(n))return n;const c=function(t,n){try{if("domain"===t&&n&&"object"==typeof n&&n.o)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!=typeof global&&n===global)return"[Global]";if("undefined"!=typeof window&&n===window)return"[Window]";if("undefined"!=typeof document&&n===document)return"[Document]";if(l(n))return"[VueViewModel]";if(a(e=n)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e)return"[SyntheticEvent]";if("number"==typeof n&&n!=n)return"[NaN]";if("function"==typeof n)return`[Function: ${nt(n)}]`;if("symbol"==typeof n)return`[${String(n)}]`;if("bigint"==typeof n)return`[BigInt: ${String(n)}]`;const r=function(t){const n=Object.getPrototypeOf(t);return n?n.constructor.name:"null prototype"}(n);return/^HTML(\w*)Element$/.test(r)?`[HTMLElement: ${r}]`:`[object ${r}]`}catch(t){return`**non-serializable** (${t})`}var e}(t,n);if(!c.startsWith("[object "))return c;if(n.__sentry_skip_normalization__)return n;const u="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:e;if(0===u)return c.replace("object ","");if(i(n))return"[Circular ~]";const f=n;if(f&&"function"==typeof f.toJSON)try{return Lt("",f.toJSON(),u-1,r,o)}catch(t){}const d=Array.isArray(n)?[]:{};let h=0;const p=B(n);for(const t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(h>=r){d[t]="[MaxProperties ~]";break}const n=p[t];d[t]=Lt(t,n,u-1,r,o),h++}return s(n),d}var Nt;function Pt(t){return new Ft((n=>{n(t)}))}function Dt(t){return new Ft(((n,e)=>{e(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(Nt||(Nt={}));class Ft{constructor(t){Ft.prototype.__init.call(this),Ft.prototype.__init2.call(this),Ft.prototype.__init3.call(this),Ft.prototype.__init4.call(this),this.i=Nt.PENDING,this.u=[];try{t(this.h,this.l)}catch(t){this.l(t)}}then(t,n){return new Ft(((e,r)=>{this.u.push([!1,n=>{if(t)try{e(t(n))}catch(t){r(t)}else e(n)},t=>{if(n)try{e(n(t))}catch(t){r(t)}else r(t)}]),this.p()}))}catch(t){return this.then((t=>t),t)}finally(t){return new Ft(((n,e)=>{let r,o;return this.then((n=>{o=!1,r=n,t&&t()}),(n=>{o=!0,r=n,t&&t()})).then((()=>{o?e(r):n(r)}))}))}__init(){this.h=t=>{this.m(Nt.RESOLVED,t)}}__init2(){this.l=t=>{this.m(Nt.REJECTED,t)}}__init3(){this.m=(t,n)=>{this.i===Nt.PENDING&&(d(n)?n.then(this.h,this.l):(this.i=t,this.v=n,this.p()))}}__init4(){this.p=()=>{if(this.i===Nt.PENDING)return;const t=this.u.slice();this.u=[],t.forEach((t=>{t[0]||(this.i===Nt.RESOLVED&&t[1](this.v),this.i===Nt.REJECTED&&t[2](this.v),t[0]=!0)}))}}}function qt(t){const n=[];function e(t){return n.splice(n.indexOf(t),1)[0]||Promise.resolve(void 0)}return{$:n,add:function(r){if(!(void 0===t||n.length<t))return Dt(new D("Not adding Promise because buffer limit was reached."));const o=r();return-1===n.indexOf(o)&&n.push(o),o.then((()=>e(o))).then(null,(()=>e(o).then(null,(()=>{})))),o},drain:function(t){return new Ft(((e,r)=>{let o=n.length;if(!o)return e(!0);const i=setTimeout((()=>{t&&t>0&&e(!1)}),t);n.forEach((t=>{Pt(t).then((()=>{--o||(clearTimeout(i),e(!0))}),r)}))}))}}}function Ut(t){if(!t)return{};const n=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};const e=n[6]||"",r=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],search:e,hash:r,relative:n[5]+e+r}}const Ht=["fatal","error","warning","log","info","debug"];function Bt(t){return"warn"===t?"warning":Ht.includes(t)?t:"log"}const zt="baggage",Wt="sentry-",Xt=/^sentry-/,Gt=8192;function Jt(t){const n=function(t){if(!t||!s(t)&&!Array.isArray(t))return;if(Array.isArray(t))return t.reduce(((t,n)=>{const e=Kt(n);return Object.entries(e).forEach((([n,e])=>{t[n]=e})),t}),{});return Kt(t)}(t);if(!n)return;const e=Object.entries(n).reduce(((t,[n,e])=>{if(n.match(Xt)){t[n.slice(Wt.length)]=e}return t}),{});return Object.keys(e).length>0?e:void 0}function Yt(t){if(!t)return;return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce(((t,[n,e],r)=>{const o=`${encodeURIComponent(n)}=${encodeURIComponent(e)}`,i=0===r?o:`${t},${o}`;return i.length>Gt?t:i}),"")}(Object.entries(t).reduce(((t,[n,e])=>(e&&(t[`${Wt}${n}`]=e),t)),{}))}function Kt(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[n,e])=>(n&&e&&(t[n]=e),t)),{})}const Vt=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Qt(t,n){const e=function(t){if(!t)return;const n=t.match(Vt);if(!n)return;let e;return"1"===n[3]?e=!0:"0"===n[3]&&(e=!1),{traceId:n[1],parentSampled:e,parentSpanId:n[2]}}(t),r=Jt(n),{traceId:o,parentSpanId:i,parentSampled:s}=e||{};return e?{traceId:o||kt(),parentSpanId:i||kt().substring(16),spanId:kt().substring(16),sampled:s,dsc:r||{}}:{traceId:o||kt(),spanId:kt().substring(16)}}function Zt(t=kt(),n=kt().substring(16),e){let r="";return void 0!==e&&(r=e?"-1":"-0"),`${t}-${n}${r}`}function tn(t,n=[]){return[t,n]}function nn(t,n){const[e,r]=t;return[e,[...r,n]]}function en(t,n){const e=t[1];for(const t of e){if(n(t,t[0].type))return!0}return!1}function rn(t){return E.__SENTRY__&&E.__SENTRY__.encodePolyfill?E.__SENTRY__.encodePolyfill(t):(new TextEncoder).encode(t)}function on(t){const[n,e]=t;let r=JSON.stringify(n);function o(t){"string"==typeof r?r="string"==typeof t?r+t:[rn(r),t]:r.push("string"==typeof t?rn(t):t)}for(const t of e){const[n,e]=t;if(o(`\n${JSON.stringify(n)}\n`),"string"==typeof e||e instanceof Uint8Array)o(e);else{let t;try{t=JSON.stringify(e)}catch(n){t=JSON.stringify(Ct(e))}o(t)}}return"string"==typeof r?r:function(t){const n=t.reduce(((t,n)=>t+n.length),0),e=new Uint8Array(n);let r=0;for(const n of t)e.set(n,r),r+=n.length;return e}(r)}function sn(t){return[{type:"span"},t]}function cn(t){const n="string"==typeof t.data?rn(t.data):t.data;return[X({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}const un={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function an(t){return un[t]}function fn(t){if(!t||!t.sdk)return;const{name:n,version:e}=t.sdk;return{name:n,version:e}}const dn=6e4;function hn(t,{statusCode:n,headers:e},r=Date.now()){const o={...t},i=e&&e["x-sentry-rate-limits"],s=e&&e["retry-after"];if(i)for(const t of i.trim().split(",")){const[n,e,,,i]=t.split(":",5),s=parseInt(n,10),c=1e3*(isNaN(s)?60:s);if(e)for(const t of e.split(";"))"metric_bucket"===t&&i&&!i.split(";").includes("custom")||(o[t]=r+c);else o.all=r+c}else s?o.all=r+function(t,n=Date.now()){const e=parseInt(`${t}`,10);if(!isNaN(e))return 1e3*e;const r=Date.parse(`${t}`);return isNaN(r)?dn:r-n}(s,r):429===n&&(o.all=r+6e4);return o}function ln(){return{traceId:kt(),spanId:kt().substring(16)}}const pn=E;const mn=()=>{},gn=["attachTo","createWidget","remove"];function vn(t){return O((()=>{console.warn("You are using feedbackIntegration() even though this bundle does not include feedback.")})),{name:"Feedback",...gn.reduce(((t,n)=>(t[n]=mn,t)),{})}}const yn=["start","stop","flush"];function bn(){return _n(E),E}function _n(t){const n=t.__SENTRY__=t.__SENTRY__||{};return n.version=n.version||S,n[S]=n[S]||{}}function wn(t){const n=pt(),e={sid:kt(),init:!0,timestamp:n,started:n,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return X({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(e)};return t&&Sn(e,t),e}function Sn(t,n={}){if(n.user&&(!t.ipAddress&&n.user.ip_address&&(t.ipAddress=n.user.ip_address),t.did||n.did||(t.did=n.user.id||n.user.email||n.user.username)),t.timestamp=n.timestamp||pt(),n.abnormal_mechanism&&(t.abnormal_mechanism=n.abnormal_mechanism),n.ignoreDuration&&(t.ignoreDuration=n.ignoreDuration),n.sid&&(t.sid=32===n.sid.length?n.sid:kt()),void 0!==n.init&&(t.init=n.init),!t.did&&n.did&&(t.did=`${n.did}`),"number"==typeof n.started&&(t.started=n.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof n.duration)t.duration=n.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}n.release&&(t.release=n.release),n.environment&&(t.environment=n.environment),!t.ipAddress&&n.ipAddress&&(t.ipAddress=n.ipAddress),!t.userAgent&&n.userAgent&&(t.userAgent=n.userAgent),"number"==typeof n.errors&&(t.errors=n.errors),n.status&&(t.status=n.status)}const En="_sentrySpan";function $n(t,n){n?q(t,En,n):delete t[En]}function Tn(t){return t[En]}class kn{constructor(){this._=!1,this.S=[],this.T=[],this.k=[],this.I=[],this.j={},this.R={},this.M={},this.O={},this.C={},this.A=ln()}clone(){const t=new kn;return t.k=[...this.k],t.R={...this.R},t.M={...this.M},t.O={...this.O},t.j=this.j,t.L=this.L,t.N=this.N,t.P=this.P,t.D=this.D,t.T=[...this.T],t.F=this.F,t.I=[...this.I],t.C={...this.C},t.A={...this.A},t.q=this.q,t.U=this.U,$n(t,Tn(this)),t}setClient(t){this.q=t}setLastEventId(t){this.U=t}getClient(){return this.q}lastEventId(){return this.U}addScopeListener(t){this.S.push(t)}addEventProcessor(t){return this.T.push(t),this}setUser(t){return this.j=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this.N&&Sn(this.N,{user:t}),this.H(),this}getUser(){return this.j}getRequestSession(){return this.F}setRequestSession(t){return this.F=t,this}setTags(t){return this.R={...this.R,...t},this.H(),this}setTag(t,n){return this.R={...this.R,[t]:n},this.H(),this}setExtras(t){return this.M={...this.M,...t},this.H(),this}setExtra(t,n){return this.M={...this.M,[t]:n},this.H(),this}setFingerprint(t){return this.D=t,this.H(),this}setLevel(t){return this.L=t,this.H(),this}setTransactionName(t){return this.P=t,this.H(),this}setContext(t,n){return null===n?delete this.O[t]:this.O[t]=n,this.H(),this}setSession(t){return t?this.N=t:delete this.N,this.H(),this}getSession(){return this.N}update(t){if(!t)return this;const n="function"==typeof t?t(this):t,[e,r]=n instanceof In?[n.getScopeData(),n.getRequestSession()]:a(n)?[t,t.requestSession]:[],{tags:o,extra:i,user:s,contexts:c,level:u,fingerprint:f=[],propagationContext:d}=e||{};return this.R={...this.R,...o},this.M={...this.M,...i},this.O={...this.O,...c},s&&Object.keys(s).length&&(this.j=s),u&&(this.L=u),f.length&&(this.D=f),d&&(this.A=d),r&&(this.F=r),this}clear(){return this.k=[],this.R={},this.M={},this.j={},this.O={},this.L=void 0,this.P=void 0,this.D=void 0,this.F=void 0,this.N=void 0,$n(this,void 0),this.I=[],this.A=ln(),this.H(),this}addBreadcrumb(t,n){const e="number"==typeof n?n:100;if(e<=0)return this;const r={timestamp:lt(),...t},o=this.k;return o.push(r),this.k=o.length>e?o.slice(-e):o,this.H(),this}getLastBreadcrumb(){return this.k[this.k.length-1]}clearBreadcrumbs(){return this.k=[],this.H(),this}addAttachment(t){return this.I.push(t),this}clearAttachments(){return this.I=[],this}getScopeData(){return{breadcrumbs:this.k,attachments:this.I,contexts:this.O,tags:this.R,extra:this.M,user:this.j,level:this.L,fingerprint:this.D||[],eventProcessors:this.T,propagationContext:this.A,sdkProcessingMetadata:this.C,transactionName:this.P,span:Tn(this)}}setSDKProcessingMetadata(t){return this.C={...this.C,...t},this}setPropagationContext(t){return this.A=t,this}getPropagationContext(){return this.A}captureException(t,n){const e=n&&n.event_id?n.event_id:kt();if(!this.q)return C.warn("No client configured on scope - will not capture exception!"),e;const r=new Error("Sentry syntheticException");return this.q.captureException(t,{originalException:t,syntheticException:r,...n,event_id:e},this),e}captureMessage(t,n,e){const r=e&&e.event_id?e.event_id:kt();if(!this.q)return C.warn("No client configured on scope - will not capture message!"),r;const o=new Error(t);return this.q.captureMessage(t,n,{originalException:t,syntheticException:o,...e,event_id:r},this),r}captureEvent(t,n){const e=n&&n.event_id?n.event_id:kt();return this.q?(this.q.captureEvent(t,{...n,event_id:e},this),e):(C.warn("No client configured on scope - will not capture event!"),e)}H(){this._||(this._=!0,this.S.forEach((t=>{t(this)})),this._=!1)}}const In=kn;class xn{constructor(t,n){let e,r;e=t||new In,r=n||new In,this.B=[{scope:e}],this.W=r}withScope(t){const n=this.X();let e;try{e=t(n)}catch(t){throw this.G(),t}return d(e)?e.then((t=>(this.G(),t)),(t=>{throw this.G(),t})):(this.G(),e)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this.W}getStackTop(){return this.B[this.B.length-1]}X(){const t=this.getScope().clone();return this.B.push({client:this.getClient(),scope:t}),t}G(){return!(this.B.length<=1)&&!!this.B.pop()}}function jn(){const t=_n(bn());return t.stack=t.stack||new xn($("defaultCurrentScope",(()=>new In)),$("defaultIsolationScope",(()=>new In)))}function Rn(t){return jn().withScope(t)}function Mn(t,n){const e=jn();return e.withScope((()=>(e.getStackTop().scope=t,n(t))))}function On(t){return jn().withScope((()=>t(jn().getIsolationScope())))}function Cn(t){const n=_n(t);return n.acs?n.acs:{withIsolationScope:On,withScope:Rn,withSetScope:Mn,withSetIsolationScope:(t,n)=>On(n),getCurrentScope:()=>jn().getScope(),getIsolationScope:()=>jn().getIsolationScope()}}function An(){return Cn(bn()).getCurrentScope()}function Ln(){return Cn(bn()).getIsolationScope()}function Nn(){return $("globalScope",(()=>new In))}function Pn(...t){const n=Cn(bn());if(2===t.length){const[e,r]=t;return e?n.withSetScope(e,r):n.withScope(r)}return n.withScope(t[0])}function Dn(){return An().getClient()}const Fn="_sentryMetrics";function qn(t){const n=t[Fn];if(!n)return;const e={};for(const[,[t,r]]of n){(e[t]||(e[t]=[])).push(X(r))}return e}const Un="sentry.source",Hn="sentry.sample_rate",Bn="sentry.op",zn="sentry.origin",Wn="sentry.idle_span_finish_reason",Xn="sentry.measurement_unit",Gn="sentry.measurement_value",Jn="sentry.exclusive_time",Yn=0,Kn=1,Vn=2;function Qn(t,n){t.setAttribute("http.response.status_code",n);const e=function(t){if(t<400&&t>=100)return{code:Kn};if(t>=400&&t<500)switch(t){case 401:return{code:Vn,message:"unauthenticated"};case 403:return{code:Vn,message:"permission_denied"};case 404:return{code:Vn,message:"not_found"};case 409:return{code:Vn,message:"already_exists"};case 413:return{code:Vn,message:"failed_precondition"};case 429:return{code:Vn,message:"resource_exhausted"};case 499:return{code:Vn,message:"cancelled"};default:return{code:Vn,message:"invalid_argument"}}if(t>=500&&t<600)switch(t){case 501:return{code:Vn,message:"unimplemented"};case 503:return{code:Vn,message:"unavailable"};case 504:return{code:Vn,message:"deadline_exceeded"};default:return{code:Vn,message:"internal_error"}}return{code:Vn,message:"unknown_error"}}(n);"unknown_error"!==e.message&&t.setStatus(e)}const Zn=1;function te(t){const{spanId:n,traceId:e}=t.spanContext(),{data:r,op:o,parent_span_id:i,status:s,origin:c}=ie(t);return X({parent_span_id:i,span_id:n,trace_id:e,data:r,op:o,status:s,origin:c})}function ne(t){const{spanId:n,traceId:e}=t.spanContext(),{parent_span_id:r}=ie(t);return X({parent_span_id:r,span_id:n,trace_id:e})}function ee(t){const{traceId:n,spanId:e}=t.spanContext();return Zt(n,e,se(t))}function re(t){return"number"==typeof t?oe(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?oe(t.getTime()):pt()}function oe(t){return t>9999999999?t/1e3:t}function ie(t){if(function(t){return"function"==typeof t.getSpanJSON}(t))return t.getSpanJSON();try{const{spanId:n,traceId:e}=t.spanContext();if(function(t){const n=t;return!!(n.attributes&&n.startTime&&n.name&&n.endTime&&n.status)}(t)){const{attributes:r,startTime:o,name:i,endTime:s,parentSpanId:c,status:u}=t;return X({span_id:n,trace_id:e,data:r,description:i,parent_span_id:c,start_timestamp:re(o),timestamp:re(s)||void 0,status:ce(u),op:r[Bn],origin:r[zn],_metrics_summary:qn(t)})}return{span_id:n,trace_id:e}}catch(t){return{}}}function se(t){const{traceFlags:n}=t.spanContext();return n===Zn}function ce(t){if(t&&t.code!==Yn)return t.code===Kn?"ok":t.message||"unknown_error"}const ue="_sentryChildSpans",ae="_sentryRootSpan";function fe(t,n){const e=t[ae]||t;q(n,ae,e),t[ue]?t[ue].add(n):q(t,ue,new Set([n]))}function de(t){const n=new Set;return function t(e){if(!n.has(e)&&se(e)){n.add(e);const r=e[ue]?Array.from(e[ue]):[];for(const n of r)t(n)}}(t),Array.from(n)}function he(t){return t[ae]||t}function le(){const t=Cn(bn());return t.getActiveSpan?t.getActiveSpan():Tn(An())}function pe(t,n,e,r,o,i){const s=le();s&&function(t,n,e,r,o,i,s){const c=t[Fn]||(t[Fn]=new Map),u=`${n}:${e}@${o}`,a=c.get(s);if(a){const[,t]=a;c.set(s,[u,{min:Math.min(t.min,r),max:Math.max(t.max,r),count:t.count+=1,sum:t.sum+=r,tags:t.tags}])}else c.set(s,[u,{min:r,max:r,count:1,sum:r,tags:i}])}(s,t,n,e,r,o,i)}let me=!1;function ge(){me||(me=!0,wt(ve),$t(ve))}function ve(){const t=le(),n=t&&he(t);if(n){const t="internal_error";n.setStatus({code:Vn,message:t})}}ve.tag="sentry_tracingErrorCallback";const ye="_sentryScope",be="_sentryIsolationScope";function _e(t){return{scope:t[ye],isolationScope:t[be]}}function we(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const n=t||function(){const t=Dn();return t&&t.getOptions()}();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}class Se{constructor(t={}){this.J=t.traceId||kt(),this.Y=t.spanId||kt().substring(16)}spanContext(){return{spanId:this.Y,traceId:this.J,traceFlags:0}}end(t){}setAttribute(t,n){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,n,e){return this}}function Ee(t,n,e=(()=>{})){let r;try{r=t()}catch(t){throw n(t),e(),t}return function(t,n,e){if(d(t))return t.then((t=>(e(),t)),(t=>{throw n(t),e(),t}));return e(),t}(r,n,e)}const $e="production",Te="_frozenDsc";function ke(t,n){q(t,Te,n)}function Ie(t,n){const e=n.getOptions(),{publicKey:r}=n.getDsn()||{},o=X({environment:e.environment||$e,release:e.release,public_key:r,trace_id:t});return n.emit("createDsc",o),o}function xe(t){const n=Dn();if(!n)return{};const e=Ie(ie(t).trace_id||"",n),r=he(t),o=r[Te];if(o)return o;const i=r.spanContext().traceState,s=i&&i.get("sentry.dsc"),c=s&&Jt(s);if(c)return c;const u=ie(r),a=u.data||{},f=a[Hn];null!=f&&(e.sample_rate=`${f}`);const d=a[Un],h=u.description;return"url"!==d&&h&&(e.transaction=h),e.sampled=String(se(r)),n.emit("createDsc",e,r),e}function je(t){if("boolean"==typeof t)return Number(t);const n="string"==typeof t?parseFloat(t):t;return"number"!=typeof n||isNaN(n)||n<0||n>1?void 0:n}function Re(t,n,e,r){const o=fn(e),i=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,n){n&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=[...t.sdk.integrations||[],...n.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...n.packages||[]])}(t,e&&e.sdk);const s=function(t,n,e,r){const o=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&{sdk:n},...!!e&&r&&{dsn:L(r)},...o&&{trace:X({...o})}}}(t,o,r,n);delete t.sdkProcessingMetadata;return tn(s,[[{type:i},t]])}function Me(t,n,e){const r=le(),o=r&&he(r);o&&o.addEvent(t,{[Gn]:n,[Xn]:e})}function Oe(t){if(!t||0===t.length)return;const n={};return t.forEach((t=>{const e=t.attributes||{},r=e[Xn],o=e[Gn];"string"==typeof r&&"number"==typeof o&&(n[t.name]={value:o,unit:r})})),n}class Ce{constructor(t={}){this.J=t.traceId||kt(),this.Y=t.spanId||kt().substring(16),this.K=t.startTimestamp||pt(),this.V={},this.setAttributes({[zn]:"manual",[Bn]:t.op,...t.attributes}),this.Z=t.name,t.parentSpanId&&(this.tt=t.parentSpanId),"sampled"in t&&(this.nt=t.sampled),t.endTimestamp&&(this.et=t.endTimestamp),this.o=[],this.rt=t.isStandalone,this.et&&this.ot()}spanContext(){const{Y:t,J:n,nt:e}=this;return{spanId:t,traceId:n,traceFlags:e?Zn:0}}setAttribute(t,n){void 0===n?delete this.V[t]:this.V[t]=n}setAttributes(t){Object.keys(t).forEach((n=>this.setAttribute(n,t[n])))}updateStartTime(t){this.K=re(t)}setStatus(t){return this.it=t,this}updateName(t){return this.Z=t,this}end(t){this.et||(this.et=re(t),this.ot())}getSpanJSON(){return X({data:this.V,description:this.Z,op:this.V[Bn],parent_span_id:this.tt,span_id:this.Y,start_timestamp:this.K,status:ce(this.it),timestamp:this.et,trace_id:this.J,origin:this.V[zn],_metrics_summary:qn(this),profile_id:this.V["sentry.profile_id"],exclusive_time:this.V[Jn],measurements:Oe(this.o),is_segment:this.rt&&he(this)===this||void 0,segment_id:this.rt?he(this).spanContext().spanId:void 0})}isRecording(){return!this.et&&!!this.nt}addEvent(t,n,e){const r=Ae(n)?n:e||pt(),o=Ae(n)?{}:n||{},i={name:t,time:re(r),attributes:o};return this.o.push(i),this}isStandaloneSpan(){return!!this.rt}ot(){const t=Dn();t&&t.emit("spanEnd",this);if(!(this.rt||this===he(this)))return;if(this.rt)return void(this.nt?function(t){const n=Dn();if(!n)return;const e=t[1];if(!e||0===e.length)return void n.recordDroppedEvent("before_send","span");const r=n.getTransport();r&&r.send(t).then(null,(t=>{}))}(function(t,n){const e=xe(t[0]),r=n&&n.getDsn(),o=n&&n.getOptions().tunnel,i={sent_at:(new Date).toISOString(),...function(t){return!!t.trace_id&&!!t.public_key}(e)&&{trace:e},...!!o&&r&&{dsn:L(r)}},s=n&&n.getOptions().beforeSendSpan,c=s?t=>s(ie(t)):t=>ie(t),u=[];for(const n of t){const t=c(n);t&&u.push(sn(t))}return tn(i,u)}([this],t)):t&&t.recordDroppedEvent("sample_rate","span"));const n=this.st();if(n){(_e(this).scope||An()).captureEvent(n)}}st(){if(!Le(ie(this)))return;this.Z||(this.Z="<unlabeled transaction>");const{scope:t,isolationScope:n}=_e(this),e=(t||An()).getClient()||Dn();if(!0!==this.nt)return void(e&&e.recordDroppedEvent("sample_rate","transaction"));const r=de(this).filter((t=>t!==this&&!function(t){return t instanceof Ce&&t.isStandaloneSpan()}(t))).map((t=>ie(t))).filter(Le),o=this.V[Un],i={contexts:{trace:te(this)},spans:r.length>1e3?r.sort(((t,n)=>t.start_timestamp-n.start_timestamp)).slice(0,1e3):r,start_timestamp:this.K,timestamp:this.et,transaction:this.Z,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:n,...X({dynamicSamplingContext:xe(this)})},_metrics_summary:qn(this),...o&&{transaction_info:{source:o}}},s=Oe(this.o);return s&&Object.keys(s).length&&(i.measurements=s),i}}function Ae(t){return t&&"number"==typeof t||t instanceof Date||Array.isArray(t)}function Le(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const Ne="__SENTRY_SUPPRESS_TRACING__";function Pe(t,n){const e=He();if(e.startSpanManual)return e.startSpanManual(t,n);const r=Ue(t),{forceTransaction:o,parentSpan:i}=t;return Pn(t.scope,(()=>We(i)((()=>{const e=An(),i=ze(e),s=t.onlyIfParent&&!i?new Se:qe({parentSpan:i,spanArguments:r,forceTransaction:o,scope:e});function c(){s.end()}return $n(e,s),Ee((()=>n(s,c)),(()=>{const{status:t}=ie(s);!s.isRecording()||t&&"ok"!==t||s.setStatus({code:Vn,message:"internal_error"})}))}))))}function De(t){const n=He();if(n.startInactiveSpan)return n.startInactiveSpan(t);const e=Ue(t),{forceTransaction:r,parentSpan:o}=t;return(t.scope?n=>Pn(t.scope,n):void 0!==o?t=>Fe(o,t):t=>t())((()=>{const n=An(),o=ze(n);return t.onlyIfParent&&!o?new Se:qe({parentSpan:o,spanArguments:e,forceTransaction:r,scope:n})}))}function Fe(t,n){const e=He();return e.withActiveSpan?e.withActiveSpan(t,n):Pn((e=>($n(e,t||void 0),n(e))))}function qe({parentSpan:t,spanArguments:n,forceTransaction:e,scope:r}){if(!we())return new Se;const o=Ln();let i;if(t&&!e)i=function(t,n,e){const{spanId:r,traceId:o}=t.spanContext(),i=!n.getScopeData().sdkProcessingMetadata[Ne]&&se(t),s=i?new Ce({...e,parentSpanId:r,traceId:o,sampled:i}):new Se({traceId:o});fe(t,s);const c=Dn();c&&(c.emit("spanStart",s),e.endTimestamp&&c.emit("spanEnd",s));return s}(t,r,n),fe(t,i);else if(t){const e=xe(t),{traceId:o,spanId:s}=t.spanContext(),c=se(t);i=Be({traceId:o,parentSpanId:s,...n},r,c),ke(i,e)}else{const{traceId:t,dsc:e,parentSpanId:s,sampled:c}={...o.getPropagationContext(),...r.getPropagationContext()};i=Be({traceId:t,parentSpanId:s,...n},r,c),e&&ke(i,e)}return function(t,n,e){t&&(q(t,be,e),q(t,ye,n))}(i,r,o),i}function Ue(t){const n={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){const e={...n};return e.startTimestamp=re(t.startTime),delete e.startTime,e}return n}function He(){return Cn(bn())}function Be(t,n,e){const r=Dn(),o=r&&r.getOptions()||{},{name:i="",attributes:s}=t,[c,u]=n.getScopeData().sdkProcessingMetadata[Ne]?[!1]:function(t,n){if(!we(t))return[!1];let e;e="function"==typeof t.tracesSampler?t.tracesSampler(n):void 0!==n.parentSampled?n.parentSampled:void 0!==t.tracesSampleRate?t.tracesSampleRate:1;const r=je(e);return void 0===r?[!1]:r&&Math.random()<r?[!0,r]:[!1,r]}(o,{name:i,parentSampled:e,attributes:s,transactionContext:{name:i,parentSampled:e}}),a=new Ce({...t,attributes:{[Un]:"custom",...t.attributes},sampled:c});return void 0!==u&&a.setAttribute(Hn,u),r&&r.emit("spanStart",a),a}function ze(t){const n=Tn(t);if(!n)return;const e=Dn();return(e?e.getOptions():{}).parentSpanIsAlwaysRootSpan?he(n):n}function We(t){return void 0!==t?n=>Fe(t,n):t=>t()}const Xe={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},Ge="heartbeatFailed",Je="idleTimeout",Ye="finalTimeout",Ke="externalFinish";function Ve(t,n={}){const e=new Map;let r,o=!1,i=Ke,s=!n.disableAutoFinish;const c=[],{idleTimeout:u=Xe.idleTimeout,finalTimeout:a=Xe.finalTimeout,childSpanTimeout:f=Xe.childSpanTimeout,beforeSpanEnd:d}=n,h=Dn();if(!h||!we())return new Se;const l=An(),p=le(),m=function(t){const n=De(t);return $n(An(),n),n}(t);function g(){r&&(clearTimeout(r),r=void 0)}function v(t){g(),r=setTimeout((()=>{!o&&0===e.size&&s&&(i=Je,m.end(t))}),u)}function y(t){r=setTimeout((()=>{!o&&s&&(i=Ge,m.end(t))}),f)}function b(t){o=!0,e.clear(),c.forEach((t=>t())),$n(l,p);const n=ie(m),{start_timestamp:r}=n;if(!r)return;(n.data||{})[Wn]||m.setAttribute(Wn,i),C.log(`[Tracing] Idle span "${n.op}" finished`);const s=de(m).filter((t=>t!==m));let f=0;s.forEach((n=>{n.isRecording()&&(n.setStatus({code:Vn,message:"cancelled"}),n.end(t));const e=ie(n),{timestamp:r=0,start_timestamp:o=0}=e;r-o<=(a+u)/1e3&&o<=t||(!function(t,n){t[ue]&&t[ue].delete(n)}(m,n),f++)})),f>0&&m.setAttribute("sentry.idle_span_discarded_spans",f)}return m.end=new Proxy(m.end,{apply(t,n,e){d&&d(m);const[r,...o]=e,i=re(r||pt()),s=de(m).filter((t=>t!==m));if(!s.length)return b(i),Reflect.apply(t,n,[i,...o]);const c=s.map((t=>ie(t).timestamp)).filter((t=>!!t)),u=c.length?Math.max(...c):void 0,f=ie(m).start_timestamp,h=Math.min(f?f+a/1e3:1/0,Math.max(f||-1/0,Math.min(i,u||1/0)));return b(h),Reflect.apply(t,n,[h,...o])}}),c.push(h.on("spanStart",(t=>{if(o||t===m||ie(t).timestamp)return;var n;de(m).includes(t)&&(n=t.spanContext().spanId,g(),e.set(n,!0),y(pt()+f/1e3))}))),c.push(h.on("spanEnd",(t=>{var n;o||(n=t.spanContext().spanId,e.has(n)&&e.delete(n),0===e.size&&v(pt()+u/1e3))}))),c.push(h.on("idleSpanEnableAutoFinish",(t=>{t===m&&(s=!0,v(),e.size&&y())}))),n.disableAutoFinish||v(),setTimeout((()=>{o||(m.setStatus({code:Vn,message:"deadline_exceeded"}),i=Ye,m.end())}),a),m}function Qe(t,n,e,r=0){return new Ft(((o,i)=>{const s=t[r];if(null===n||"function"!=typeof s)o(n);else{const c=s({...n},e);d(c)?c.then((n=>Qe(t,n,e,r+1).then(o))).then(null,i):Qe(t,c,e,r+1).then(o).then(null,i)}}))}function Ze(t,n){const{fingerprint:e,span:r,breadcrumbs:o,sdkProcessingMetadata:i}=n;!function(t,n){const{extra:e,tags:r,user:o,contexts:i,level:s,transactionName:c}=n,u=X(e);u&&Object.keys(u).length&&(t.extra={...u,...t.extra});const a=X(r);a&&Object.keys(a).length&&(t.tags={...a,...t.tags});const f=X(o);f&&Object.keys(f).length&&(t.user={...f,...t.user});const d=X(i);d&&Object.keys(d).length&&(t.contexts={...d,...t.contexts});s&&(t.level=s);c&&"transaction"!==t.type&&(t.transaction=c)}(t,n),r&&function(t,n){t.contexts={trace:ne(n),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:xe(n),...t.sdkProcessingMetadata};const e=he(n),r=ie(e).description;r&&!t.transaction&&"transaction"===t.type&&(t.transaction=r)}(t,r),function(t,n){t.fingerprint=t.fingerprint?Ot(t.fingerprint):[],n&&(t.fingerprint=t.fingerprint.concat(n));t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}(t,e),function(t,n){const e=[...t.breadcrumbs||[],...n];t.breadcrumbs=e.length?e:void 0}(t,o),function(t,n){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...n}}(t,i)}function tr(t,n){const{extra:e,tags:r,user:o,contexts:i,level:s,sdkProcessingMetadata:c,breadcrumbs:u,fingerprint:a,eventProcessors:f,attachments:d,propagationContext:h,transactionName:l,span:p}=n;nr(t,"extra",e),nr(t,"tags",r),nr(t,"user",o),nr(t,"contexts",i),nr(t,"sdkProcessingMetadata",c),s&&(t.level=s),l&&(t.transactionName=l),p&&(t.span=p),u.length&&(t.breadcrumbs=[...t.breadcrumbs,...u]),a.length&&(t.fingerprint=[...t.fingerprint,...a]),f.length&&(t.eventProcessors=[...t.eventProcessors,...f]),d.length&&(t.attachments=[...t.attachments,...d]),t.propagationContext={...t.propagationContext,...h}}function nr(t,n,e){if(e&&Object.keys(e).length){t[n]={...t[n]};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[n][r]=e[r])}}function er(t,n,e,r,o,i){const{normalizeDepth:s=3,normalizeMaxBreadth:c=1e3}=t,u={...n,event_id:n.event_id||e.event_id||kt(),timestamp:n.timestamp||lt()},a=e.integrations||t.integrations.map((t=>t.name));!function(t,n){const{environment:e,release:r,dist:o,maxValueLength:i=250}=n;"environment"in t||(t.environment="environment"in n?e:$e);void 0===t.release&&void 0!==r&&(t.release=r);void 0===t.dist&&void 0!==o&&(t.dist=o);t.message&&(t.message=p(t.message,i));const s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=p(s.value,i));const c=t.request;c&&c.url&&(c.url=p(c.url,i))}(u,t),function(t,n){n.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...n])}(u,a),o&&o.emit("applyFrameMetadata",n),void 0===n.type&&function(t,n){const e=E._sentryDebugIds;if(!e)return;let r;const o=rr.get(n);o?r=o:(r=new Map,rr.set(n,r));const i=Object.entries(e).reduce(((t,[e,o])=>{let i;const s=r.get(e);s?i=s:(i=n(e),r.set(e,i));for(let n=i.length-1;n>=0;n--){const e=i[n];if(e.filename){t[e.filename]=o;break}}return t}),{});try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.filename&&(t.debug_id=i[t.filename])}))}))}catch(t){}}(u,t.stackParser);const f=function(t,n){if(!n)return t;const e=t?t.clone():new In;return e.update(n),e}(r,e.captureContext);e.mechanism&&Rt(u,e.mechanism);const d=o?o.getEventProcessors():[],h=Nn().getScopeData();if(i){tr(h,i.getScopeData())}if(f){tr(h,f.getScopeData())}const l=[...e.attachments||[],...h.attachments];l.length&&(e.attachments=l),Ze(u,h);return Qe([...d,...h.eventProcessors],u,e).then((t=>(t&&function(t){const n={};try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.debug_id&&(t.abs_path?n[t.abs_path]=t.debug_id:t.filename&&(n[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(t){}if(0===Object.keys(n).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const e=t.debug_meta.images;Object.entries(n).forEach((([t,n])=>{e.push({type:"sourcemap",code_file:t,debug_id:n})}))}(t),"number"==typeof s&&s>0?function(t,n,e){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:Ct(t.data,n,e)}})))},...t.user&&{user:Ct(t.user,n,e)},...t.contexts&&{contexts:Ct(t.contexts,n,e)},...t.extra&&{extra:Ct(t.extra,n,e)}};t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=Ct(t.contexts.trace.data,n,e)));t.spans&&(r.spans=t.spans.map((t=>({...t,...t.data&&{data:Ct(t.data,n,e)}}))));return r}(t,s,c):t)))}const rr=new WeakMap;function or(t){if(t)return function(t){return t instanceof In||"function"==typeof t}(t)||function(t){return Object.keys(t).some((t=>ir.includes(t)))}(t)?{captureContext:t}:t}const ir=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function captureException(t,n){return An().captureException(t,or(n))}function sr(t,n){return An().captureEvent(t,n)}function cr(t,n){Ln().setContext(t,n)}function ur(t){Ln().setExtras(t)}function ar(t,n){Ln().setExtra(t,n)}function fr(t){Ln().setTags(t)}function dr(t,n){Ln().setTag(t,n)}function hr(t){Ln().setUser(t)}function lr(){return Ln().lastEventId()}function pr(t){const n=Dn(),e=Ln(),r=An(),{release:o,environment:i=$e}=n&&n.getOptions()||{},{userAgent:s}=E.navigator||{},c=wn({release:o,environment:i,user:r.getUser()||e.getUser(),...s&&{userAgent:s},...t}),u=e.getSession();return u&&"ok"===u.status&&Sn(u,{status:"exited"}),mr(),e.setSession(c),r.setSession(c),c}function mr(){const t=Ln(),n=An(),e=n.getSession()||t.getSession();e&&function(t,n){let e={};n?e={status:n}:"ok"===t.status&&(e={status:"exited"}),Sn(t,e)}(e),gr(),t.setSession(),n.setSession()}function gr(){const t=Ln(),n=An(),e=Dn(),r=n.getSession()||t.getSession();r&&e&&e.captureSession(r)}function vr(t=!1){t?mr():gr()}const yr="7";function br(t){const n=t.protocol?`${t.protocol}:`:"",e=t.port?`:${t.port}`:"";return`${n}//${t.host}${e}${t.path?`/${t.path}`:""}/api/`}function _r(t,n){return e={sentry_key:t.publicKey,sentry_version:yr,...n&&{sentry_client:`${n.name}/${n.version}`}},Object.keys(e).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`)).join("&");var e}function wr(t,n,e){return n||`${function(t){return`${br(t)}${t.projectId}/envelope/`}(t)}?${_r(t,e)}`}const Sr=[];function Er(t){const n=t.defaultIntegrations||[],e=t.integrations;let r;n.forEach((t=>{t.isDefaultInstance=!0})),r=Array.isArray(e)?[...n,...e]:"function"==typeof e?Ot(e(n)):n;const o=function(t){const n={};return t.forEach((t=>{const{name:e}=t,r=n[e];r&&!r.isDefaultInstance&&t.isDefaultInstance||(n[e]=t)})),Object.values(n)}(r),i=o.findIndex((t=>"Debug"===t.name));if(i>-1){const[t]=o.splice(i,1);o.push(t)}return o}function $r(t,n){for(const e of n)e&&e.afterAllSetup&&e.afterAllSetup(t)}function Tr(t,n,e){if(!e[n.name]){if(e[n.name]=n,-1===Sr.indexOf(n.name)&&"function"==typeof n.setupOnce&&(n.setupOnce(),Sr.push(n.name)),n.setup&&"function"==typeof n.setup&&n.setup(t),"function"==typeof n.preprocessEvent){const e=n.preprocessEvent.bind(n);t.on("preprocessEvent",((n,r)=>e(n,r,t)))}if("function"==typeof n.processEvent){const e=n.processEvent.bind(n),r=Object.assign(((n,r)=>e(n,r,t)),{id:n.name});t.addEventProcessor(r)}}}class kr{constructor(t){if(this.ct=t,this._integrations={},this.ut=0,this.ft={},this.dt={},this.T=[],t.dsn&&(this.ht=P(t.dsn)),this.ht){const n=wr(this.ht,t.tunnel,t.lt?t.lt.sdk:void 0);this.gt=t.transport({tunnel:this.ct.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,e){const r=kt();if(Mt(t))return r;const o={event_id:r,...n};return this.vt(this.eventFromException(t,o).then((t=>this.yt(t,o,e)))),o.event_id}captureMessage(t,n,e,r){const o={event_id:kt(),...e},i=c(t)?t:String(t),s=u(t)?this.eventFromMessage(i,n,o):this.eventFromException(t,o);return this.vt(s.then((t=>this.yt(t,o,r)))),o.event_id}captureEvent(t,n,e){const r=kt();if(n&&n.originalException&&Mt(n.originalException))return r;const o={event_id:r,...n},i=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this.vt(this.yt(t,o,i||e)),o.event_id}captureSession(t){"string"!=typeof t.release||(this.sendSession(t),Sn(t,{init:!1}))}getDsn(){return this.ht}getOptions(){return this.ct}getSdkMetadata(){return this.ct.lt}getTransport(){return this.gt}flush(t){const n=this.gt;return n?(this.emit("flush"),this.bt(t).then((e=>n.flush(t).then((t=>e&&t))))):Pt(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,this.emit("close"),t)))}getEventProcessors(){return this.T}addEventProcessor(t){this.T.push(t)}init(){this._t()&&this.wt()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];Tr(this,t,this._integrations),n||$r(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let e=Re(t,this.ht,this.ct.lt,this.ct.tunnel);for(const t of n.attachments||[])e=nn(e,cn(t));const r=this.sendEnvelope(e);r&&r.then((n=>this.emit("afterSendEvent",t,n)),null)}sendSession(t){const n=function(t,n,e,r){const o=fn(e);return tn({sent_at:(new Date).toISOString(),...o&&{sdk:o},...!!r&&n&&{dsn:L(n)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(t,this.ht,this.ct.lt,this.ct.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,e){if(this.ct.sendClientReports){const e=`${t}:${n}`;this.ft[e]=(this.ft[e]||0)+1}}on(t,n){const e=this.dt[t]=this.dt[t]||[];return e.push(n),()=>{const t=e.indexOf(n);t>-1&&e.splice(t,1)}}emit(t,...n){const e=this.dt[t];e&&e.forEach((t=>t(...n)))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._t()&&this.gt?this.gt.send(t).then(null,(t=>t)):Pt({})}wt(){const{integrations:t}=this.ct;this._integrations=function(t,n){const e={};return n.forEach((n=>{n&&Tr(t,n,e)})),e}(this,t),$r(this,t)}St(t,n){let e=!1,r=!1;const o=n.exception&&n.exception.values;if(o){r=!0;for(const t of o){const n=t.mechanism;if(n&&!1===n.handled){e=!0;break}}}const i="ok"===t.status;(i&&0===t.errors||i&&e)&&(Sn(t,{...e&&{status:"crashed"},errors:t.errors||Number(r||e)}),this.captureSession(t))}bt(t){return new Ft((n=>{let e=0;const r=setInterval((()=>{0==this.ut?(clearInterval(r),n(!0)):(e+=1,t&&e>=t&&(clearInterval(r),n(!1)))}),1)}))}_t(){return!1!==this.getOptions().enabled&&void 0!==this.gt}Et(t,n,e,r=Ln()){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&i.length>0&&(n.integrations=i),this.emit("preprocessEvent",t,n),t.type||r.setLastEventId(t.event_id||n.event_id),er(o,t,n,e,this,r).then((t=>{if(null===t)return t;const n={...r.getPropagationContext(),...e?e.getPropagationContext():void 0};if(!(t.contexts&&t.contexts.trace)&&n){const{traceId:e,spanId:r,parentSpanId:o,dsc:i}=n;t.contexts={trace:X({trace_id:e,span_id:r,parent_span_id:o}),...t.contexts};const s=i||Ie(e,this);t.sdkProcessingMetadata={dynamicSamplingContext:s,...t.sdkProcessingMetadata}}return t}))}yt(t,n={},e){return this.$t(t,n,e).then((t=>t.event_id),(t=>{}))}$t(t,n,e){const r=this.getOptions(),{sampleRate:o}=r,i=xr(t),s=Ir(t),c=t.type||"error",u=`before send for type \`${c}\``,f=void 0===o?void 0:je(o);if(s&&"number"==typeof f&&Math.random()>f)return this.recordDroppedEvent("sample_rate","error",t),Dt(new D(`Discarding event because it's not included in the random sample (sampling rate = ${o})`,"log"));const h="replay_event"===c?"replay":c,l=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this.Et(t,n,e,l).then((e=>{if(null===e)throw this.recordDroppedEvent("event_processor",h,t),new D("An event processor returned `null`, will not send event.","log");if(n.data&&!0===n.data.__sentry__)return e;const o=function(t,n,e,r){const{beforeSend:o,beforeSendTransaction:i,beforeSendSpan:s}=n;if(Ir(e)&&o)return o(e,r);if(xr(e)){if(e.spans&&s){const n=[];for(const r of e.spans){const e=s(r);e?n.push(e):t.recordDroppedEvent("before_send","span")}e.spans=n}if(i)return i(e,r)}return e}(this,r,e,n);return function(t,n){const e=`${n} must return \`null\` or a valid event.`;if(d(t))return t.then((t=>{if(!a(t)&&null!==t)throw new D(e);return t}),(t=>{throw new D(`${n} rejected with ${t}`)}));if(!a(t)&&null!==t)throw new D(e);return t}(o,u)})).then((r=>{if(null===r){if(this.recordDroppedEvent("before_send",h,t),xr(t)){const n=1+(t.spans||[]).length;this.ft.span=(this.ft.span||0)+n}throw new D(`${u} returned \`null\`, will not send event.`,"log")}const o=e&&e.getSession();!i&&o&&this.St(o,r);const s=r.transaction_info;if(i&&s&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...s,source:t}}return this.sendEvent(r,n),r})).then(null,(t=>{if(t instanceof D)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new D(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}vt(t){this.ut++,t.then((t=>(this.ut--,t)),(t=>(this.ut--,t)))}Tt(){const t=this.ft;return this.ft={},Object.entries(t).map((([t,n])=>{const[e,r]=t.split(":");return{reason:e,category:r,quantity:n}}))}}function Ir(t){return void 0===t.type}function xr(t){return"transaction"===t.type}function jr(t){An().setClient(t)}const Rr=64;function Mr(t,n,e=qt(t.bufferSize||Rr)){let r={};return{send:function(o){const i=[];if(en(o,((n,e)=>{const o=an(e);if(function(t,n,e=Date.now()){return function(t,n){return t[n]||t.all||0}(t,n)>e}(r,o)){const r=Or(n,e);t.recordDroppedEvent("ratelimit_backoff",o,r)}else i.push(n)})),0===i.length)return Pt({});const s=tn(o[0],i),c=n=>{en(s,((e,r)=>{const o=Or(e,r);t.recordDroppedEvent(n,an(r),o)}))};return e.add((()=>n({body:on(s)}).then((t=>(r=hn(r,t),t)),(t=>{throw c("network_error"),t})))).then((t=>t),(t=>{if(t instanceof D)return c("queue_overflow"),Pt({});throw t}))},flush:t=>e.drain(t)}}function Or(t,n){if("event"===n||"transaction"===n)return Array.isArray(t)?t[1]:void 0}const Cr=100;function Ar(t,n){const e=Dn(),r=Ln();if(!e)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:i=Cr}=e.getOptions();if(i<=0)return;const s={timestamp:lt(),...t},c=o?O((()=>o(s,n))):s;null!==c&&(e.emit&&e.emit("beforeAddBreadcrumb",c,n),r.addBreadcrumb(c,i))}let Lr;const Nr=new WeakMap,Pr=()=>({name:"FunctionToString",setupOnce(){Lr=Function.prototype.toString;try{Function.prototype.toString=function(...t){const n=H(this),e=Nr.has(Dn())&&void 0!==n?n:this;return Lr.apply(e,t)}}catch(t){}},setup(t){Nr.set(t,!0)}}),Dr=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"],Fr=(t={})=>({name:"InboundFilters",processEvent(n,e,r){const o=r.getOptions(),i=function(t={},n={}){return{allowUrls:[...t.allowUrls||[],...n.allowUrls||[]],denyUrls:[...t.denyUrls||[],...n.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...n.ignoreErrors||[],...t.disableErrorDefaults?[]:Dr],ignoreTransactions:[...t.ignoreTransactions||[],...n.ignoreTransactions||[]],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(t,o);return function(t,n){if(n.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return!0;if(function(t,n){if(t.type||!n||!n.length)return!1;return function(t){const n=[];t.message&&n.push(t.message);let e;try{e=t.exception.values[t.exception.values.length-1]}catch(t){}e&&e.value&&(n.push(e.value),e.type&&n.push(`${e.type}: ${e.value}`));return n}(t).some((t=>v(t,n)))}(t,n.ignoreErrors))return!0;if(function(t){if(t.type)return!1;if(!t.exception||!t.exception.values||0===t.exception.values.length)return!1;return!t.message&&!t.exception.values.some((t=>t.stacktrace||t.type&&"Error"!==t.type||t.value))}(t))return!0;if(function(t,n){if("transaction"!==t.type||!n||!n.length)return!1;const e=t.transaction;return!!e&&v(e,n)}(t,n.ignoreTransactions))return!0;if(function(t,n){if(!n||!n.length)return!1;const e=qr(t);return!!e&&v(e,n)}(t,n.denyUrls))return!0;if(!function(t,n){if(!n||!n.length)return!0;const e=qr(t);return!e||v(e,n)}(t,n.allowUrls))return!0;return!1}(n,i)?null:n}});function qr(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(t){}return n?function(t=[]){for(let n=t.length-1;n>=0;n--){const e=t[n];if(e&&"<anonymous>"!==e.filename&&"[native code]"!==e.filename)return e.filename||null}return null}(n):null}catch(t){return null}}const Ur=()=>{let t;return{name:"Dedupe",processEvent(n){if(n.type)return n;try{if(function(t,n){if(!n)return!1;if(function(t,n){const e=t.message,r=n.message;if(!e&&!r)return!1;if(e&&!r||!e&&r)return!1;if(e!==r)return!1;if(!Br(t,n))return!1;if(!Hr(t,n))return!1;return!0}(t,n))return!0;if(function(t,n){const e=zr(n),r=zr(t);if(!e||!r)return!1;if(e.type!==r.type||e.value!==r.value)return!1;if(!Br(t,n))return!1;if(!Hr(t,n))return!1;return!0}(t,n))return!0;return!1}(n,t))return null}catch(t){}return t=n}}};function Hr(t,n){let e=et(t),r=et(n);if(!e&&!r)return!0;if(e&&!r||!e&&r)return!1;if(r.length!==e.length)return!1;for(let t=0;t<r.length;t++){const n=r[t],o=e[t];if(n.filename!==o.filename||n.lineno!==o.lineno||n.colno!==o.colno||n.function!==o.function)return!1}return!0}function Br(t,n){let e=t.fingerprint,r=n.fingerprint;if(!e&&!r)return!0;if(e&&!r||!e&&r)return!1;try{return!(e.join("")!==r.join(""))}catch(t){return!1}}function zr(t){return t.exception&&t.exception.values&&t.exception.values[0]}const Wr="d";function Xr(t,n){const e=$("globalMetricsAggregators",(()=>new WeakMap)),r=e.get(t);if(r)return r;const o=new n(t);return t.on("flush",(()=>o.flush())),t.on("close",(()=>o.close())),e.set(t,o),o}function Gr(t,n,e,r,o={}){const i=o.client||Dn();if(!i)return;const s=le(),c=s?he(s):void 0,u=c&&ie(c).description,{unit:a,tags:f,timestamp:d}=o,{release:h,environment:l}=i.getOptions(),p={};h&&(p.release=h),l&&(p.environment=l),u&&(p.transaction=u);Xr(i,t).add(n,e,r,a,{...p,...f},d)}function Jr(t,n,e,r){Gr(t,Wr,n,Kr(e),r)}const Yr={increment:function(t,n,e=1,r){Gr(t,"c",n,Kr(e),r)},distribution:Jr,set:function(t,n,e,r){Gr(t,"s",n,e,r)},gauge:function(t,n,e,r){Gr(t,"g",n,Kr(e),r)},timing:function(t,n,e,r="second",o){if("function"==typeof e){const r=pt();return Pe({op:"metrics.timing",name:n,startTime:r,onlyIfParent:!0},(i=>Ee((()=>e()),(()=>{}),(()=>{const e=pt();Jr(t,n,e-r,{...o,unit:"second"}),i.end(e)}))))}Jr(t,n,e,{...o,unit:r})},getMetricsAggregatorForClient:Xr};function Kr(t){return"string"==typeof t?parseInt(t):t}function Vr(t){return t.replace(/[^\w\-./]+/gi,"")}const Qr=[["\n","\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function Zr(t){return[...t].reduce(((t,n)=>t+function(t){for(const[n,e]of Qr)if(t===n)return e;return t}(n)),"")}function to(t,n){C.log(`Flushing aggregated metrics, number of metrics: ${n.length}`);const e=function(t,n,e,r){const o={sent_at:(new Date).toISOString()};e&&e.sdk&&(o.sdk={name:e.sdk.name,version:e.sdk.version});r&&n&&(o.dsn=L(n));const i=function(t){const n=function(t){let n="";for(const e of t){const t=Object.entries(e.tags),r=t.length>0?`|#${t.map((([t,n])=>`${t}:${n}`)).join(",")}`:"";n+=`${e.name}@${e.unit}:${e.metric}|${e.metricType}${r}|T${e.timestamp}\n`}return n}(t);return[{type:"statsd",length:n.length},n]}(t);return tn(o,[i])}(n,t.getDsn(),t.getSdkMetadata(),t.getOptions().tunnel);t.sendEnvelope(e)}const no={c:class{constructor(t){this.v=t}get weight(){return 1}add(t){this.v+=t}toString(){return`${this.v}`}},g:class{constructor(t){this.kt=t,this.It=t,this.xt=t,this.jt=t,this.Rt=1}get weight(){return 5}add(t){this.kt=t,t<this.It&&(this.It=t),t>this.xt&&(this.xt=t),this.jt+=t,this.Rt++}toString(){return`${this.kt}:${this.It}:${this.xt}:${this.jt}:${this.Rt}`}},[Wr]:class{constructor(t){this.v=[t]}get weight(){return this.v.length}add(t){this.v.push(t)}toString(){return this.v.join(":")}},s:class{constructor(t){this.first=t,this.v=new Set([t])}get weight(){return this.v.size}add(t){this.v.add(t)}toString(){return Array.from(this.v).map((t=>"string"==typeof t?function(t){let n=0;for(let e=0;e<t.length;e++)n=(n<<5)-n+t.charCodeAt(e),n&=n;return n>>>0}(t):t)).join(":")}}};class eo{constructor(t){this.q=t,this.Mt=new Map,this.Ot=setInterval((()=>this.flush()),5e3)}add(t,n,e,r="none",o={},i=pt()){const s=Math.floor(i),c=n.replace(/[^\w\-.]+/gi,"_");const u=function(t){const n={};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[Vr(e)]=Zr(String(t[e])));return n}(o),a=function(t){return t.replace(/[^\w]+/gi,"_")}(r),f=function(t,n,e,r){return`${t}${n}${e}${Object.entries(X(r)).sort(((t,n)=>t[0].localeCompare(n[0])))}`}(t,c,a,u);let d=this.Mt.get(f);const h=d&&"s"===t?d.metric.weight:0;d?(d.metric.add(e),d.timestamp<s&&(d.timestamp=s)):(d={metric:new no[t](e),timestamp:s,metricType:t,name:c,unit:a,tags:u},this.Mt.set(f,d));pe(t,c,"string"==typeof e?d.metric.weight-h:e,a,o,f)}flush(){if(0===this.Mt.size)return;const t=Array.from(this.Mt.values());to(this.q,t),this.Mt.clear()}close(){clearInterval(this.Ot),this.flush()}}function ro(t,n,e,r,o="auto.http.browser"){if(!t.fetchData)return;const i=we()&&n(t.fetchData.url);if(t.endTimestamp&&i){const n=t.fetchData.__span;if(!n)return;const e=r[n];return void(e&&(!function(t,n){if(n.response){Qn(t,n.response.status);const e=n.response&&n.response.headers&&n.response.headers.get("content-length");if(e){const n=parseInt(e);n>0&&t.setAttribute("http.response_content_length",n)}}else n.error&&t.setStatus({code:Vn,message:"internal_error"});t.end()}(e,t),delete r[n]))}const s=An(),c=Dn(),{method:u,url:a}=t.fetchData,f=function(t){try{return new URL(t).href}catch(t){return}}(a),d=f?Ut(f).host:void 0,l=!!le(),p=i&&l?De({name:`${u} ${a}`,attributes:{url:a,type:"fetch","http.method":u,"http.url":f,"server.address":d,[zn]:o,[Bn]:"http.client"}}):new Se;if(t.fetchData.__span=p.spanContext().spanId,r[p.spanContext().spanId]=p,e(t.fetchData.url)&&c){const n=t.args[0];t.args[1]=t.args[1]||{};const e=t.args[1];e.headers=function(t,n,e,r,o){const i=Ln(),{traceId:s,spanId:c,sampled:u,dsc:a}={...i.getPropagationContext(),...e.getPropagationContext()},f=o?ee(o):Zt(s,c,u),d=Yt(a||(o?xe(o):Ie(s,n))),l=r.headers||("undefined"!=typeof Request&&h(t,Request)?t.headers:void 0);if(l){if("undefined"!=typeof Headers&&h(l,Headers)){const t=new Headers(l);return t.append("sentry-trace",f),d&&t.append(zt,d),t}if(Array.isArray(l)){const t=[...l,["sentry-trace",f]];return d&&t.push([zt,d]),t}{const t="baggage"in l?l.baggage:void 0,n=[];return Array.isArray(t)?n.push(...t):t&&n.push(t),d&&n.push(d),{...l,"sentry-trace":f,baggage:n.length>0?n.join(","):void 0}}}return{"sentry-trace":f,baggage:d}}(n,c,s,e,we()&&l?p:void 0)}return p}const oo=function(){return{bindClient(t){An().setClient(t)},withScope:Pn,getClient:()=>Dn(),getScope:An,getIsolationScope:Ln,captureException:(t,n)=>An().captureException(t,n),captureMessage:(t,n,e)=>An().captureMessage(t,n,e),captureEvent:sr,addBreadcrumb:Ar,setUser:hr,setTags:fr,setTag:dr,setExtra:ar,setExtras:ur,setContext:cr,getIntegration(t){const n=Dn();return n&&n.getIntegrationByName(t.id)||null},startSession:pr,endSession:mr,captureSession(t){if(t)return mr();!function(){const t=An(),n=Dn(),e=t.getSession();n&&e&&n.captureSession(e)}()}}};const io=E;let so=0;function co(){return so>0}function uo(t,n={},e){if("function"!=typeof t)return t;try{const n=t.__sentry_wrapped__;if(n)return n;if(H(t))return t}catch(n){return t}const sentryWrapped=function(){const r=Array.prototype.slice.call(arguments);try{e&&"function"==typeof e&&e.apply(this,arguments);const o=r.map((t=>uo(t,n)));return t.apply(this,o)}catch(t){throw so++,setTimeout((()=>{so--})),Pn((e=>{e.addEventProcessor((t=>(n.mechanism&&(jt(t,void 0,void 0),Rt(t,n.mechanism)),t.extra={...t.extra,arguments:r},t))),captureException(t)})),t}};try{for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(sentryWrapped[n]=t[n])}catch(t){}U(sentryWrapped,t),q(t,"__sentry_wrapped__",sentryWrapped);try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:()=>t.name})}catch(t){}return sentryWrapped}function ao(t,n){const e=lo(t,n),r={type:n&&n.name,value:mo(n)};return e.length&&(r.stacktrace={frames:e}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function fo(t,n,e,r){const o=Dn(),i=o&&o.getOptions().normalizeDepth,s=function(t){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const e=t[n];if(e instanceof Error)return e}return}(n),c={__serialized__:At(n,i)};if(s)return{exception:{values:[ao(t,s)]},extra:c};const u={exception:{values:[{type:f(n)?n.constructor.name:r?"UnhandledRejection":"Error",value:_o(n,{isUnhandledRejection:r})}]},extra:c};if(e){const n=lo(t,e);n.length&&(u.exception.values[0].stacktrace={frames:n})}return u}function ho(t,n){return{exception:{values:[ao(t,n)]}}}function lo(t,n){const e=n.stacktrace||n.stack||"",r=function(t){if(t&&po.test(t.message))return 1;return 0}(n),o=function(t){if("number"==typeof t.framesToPop)return t.framesToPop;return 0}(n);try{return t(e,r,o)}catch(t){}return[]}const po=/Minified React error #\d+;/i;function mo(t){const n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}function go(t,n,e,r){const o=yo(t,n,e&&e.syntheticException||void 0,r);return Rt(o),o.level="error",e&&e.event_id&&(o.event_id=e.event_id),Pt(o)}function vo(t,n,e="info",r,o){const i=bo(t,n,r&&r.syntheticException||void 0,o);return i.level=e,r&&r.event_id&&(i.event_id=r.event_id),Pt(i)}function yo(t,n,s,c,u){let d;if(o(n)&&n.error){return ho(t,n.error)}if(i(n)||r(n,"DOMException")){const e=n;if("stack"in n)d=ho(t,n);else{const n=e.name||(i(e)?"DOMError":"DOMException"),r=e.message?`${n}: ${e.message}`:n;d=bo(t,r,s,c),jt(d,r)}return"code"in e&&(d.tags={...d.tags,"DOMException.code":`${e.code}`}),d}if(e(n))return ho(t,n);if(a(n)||f(n)){return d=fo(t,n,s,u),Rt(d,{synthetic:!0}),d}return d=bo(t,n,s,c),jt(d,`${n}`,void 0),Rt(d,{synthetic:!0}),d}function bo(t,n,e,r){const o={};if(r&&e){const r=lo(t,e);r.length&&(o.exception={values:[{value:n,stacktrace:{frames:r}}]})}if(c(n)){const{__sentry_template_string__:t,__sentry_template_values__:e}=n;return o.logentry={message:t,params:e},o}return o.message=n,o}function _o(t,{isUnhandledRejection:n}){const e=function(t,n=40){const e=Object.keys(B(t));e.sort();const r=e[0];if(!r)return"[object has no keys]";if(r.length>=n)return p(r,n);for(let t=e.length;t>0;t--){const r=e.slice(0,t).join(", ");if(!(r.length>n))return t===e.length?r:p(r,n)}return""}(t),r=n?"promise rejection":"exception";if(o(t))return`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``;if(f(t)){return`Event \`${function(t){try{const n=Object.getPrototypeOf(t);return n?n.constructor.name:void 0}catch(t){}}(t)}\` (type=${t.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${e}`}function wo(t,{metadata:n,tunnel:e,dsn:r}){const o={event_id:t.event_id,sent_at:(new Date).toISOString(),...n&&n.sdk&&{sdk:{name:n.sdk.name,version:n.sdk.version}},...!!e&&!!r&&{dsn:L(r)}},i=function(t){return[{type:"user_report"},t]}(t);return tn(o,[i])}class So extends kr{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t};!function(t,n,e=[n],r="npm"){const o=t.lt||{};o.sdk||(o.sdk={name:`sentry.javascript.${n}`,packages:e.map((t=>({name:`${r}:@sentry/${t}`,version:S}))),version:S}),t.lt=o}(n,"browser",["browser"],io.SENTRY_SDK_SOURCE||"npm"),super(n),n.sendClientReports&&io.document&&io.document.addEventListener("visibilitychange",(()=>{"hidden"===io.document.visibilityState&&this.Ct()}))}eventFromException(t,n){return go(this.ct.stackParser,t,n,this.ct.attachStacktrace)}eventFromMessage(t,n="info",e){return vo(this.ct.stackParser,t,n,e,this.ct.attachStacktrace)}captureUserFeedback(t){if(!this._t())return;const n=wo(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}Et(t,n,e){return t.platform=t.platform||"javascript",super.Et(t,n,e)}Ct(){const t=this.Tt();if(0===t.length)return;if(!this.ht)return;const n=(e=t,tn((r=this.ct.tunnel&&L(this.ht))?{dsn:r}:{},[[{type:"client_report"},{timestamp:o||lt(),discarded_events:e}]]));var e,r,o;this.sendEnvelope(n)}}const Eo=(t,n,e,r)=>{let o,i;return s=>{n.value>=0&&(s||r)&&(i=n.value-(o||0),(i||void 0===o)&&(o=n.value,n.delta=i,n.rating=((t,n)=>t>n[1]?"poor":t>n[0]?"needs-improvement":"good")(n.value,e),t(n)))}},$o=E,To=()=>$o.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0],ko=()=>{const t=To();return t&&t.activationStart||0},Io=(t,n)=>{const e=To();let r="navigate";e&&($o.document&&$o.document.prerendering||ko()>0?r="prerender":$o.document&&$o.document.wasDiscarded?r="restore":e.type&&(r=e.type.replace(/_/g,"-")));return{name:t,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},xo=(t,n,e)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver((t=>{Promise.resolve().then((()=>{n(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},e||{})),r}}catch(t){}},jo=t=>{const n=n=>{("pagehide"===n.type||$o.document&&"hidden"===$o.document.visibilityState)&&t(n)};$o.document&&(addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0))},Ro=t=>{let n=!1;return e=>{n||(t(e),n=!0)}};let Mo=-1;const Oo=t=>{"hidden"===$o.document.visibilityState&&Mo>-1&&(Mo="visibilitychange"===t.type?t.timeStamp:0,removeEventListener("visibilitychange",Oo,!0),removeEventListener("prerenderingchange",Oo,!0))},Co=()=>($o.document&&Mo<0&&(Mo="hidden"!==$o.document.visibilityState||$o.document.prerendering?1/0:0,addEventListener("visibilitychange",Oo,!0),addEventListener("prerenderingchange",Oo,!0)),{get firstHiddenTime(){return Mo}}),Ao=t=>{$o.document&&$o.document.prerendering?addEventListener("prerenderingchange",(()=>t()),!0):t()},Lo=[1800,3e3],No=[.1,.25],Po=(t,n={})=>{((t,n={})=>{Ao((()=>{const e=Co(),r=Io("FCP");let o;const i=xo("paint",(t=>{t.forEach((t=>{"first-contentful-paint"===t.name&&(i.disconnect(),t.startTime<e.firstHiddenTime&&(r.value=Math.max(t.startTime-ko(),0),r.entries.push(t),o(!0)))}))}));i&&(o=Eo(t,r,Lo,n.reportAllChanges))}))})(Ro((()=>{const e=Io("CLS",0);let r,o=0,i=[];const s=t=>{t.forEach((t=>{if(!t.hadRecentInput){const n=i[0],e=i[i.length-1];o&&n&&e&&t.startTime-e.startTime<1e3&&t.startTime-n.startTime<5e3?(o+=t.value,i.push(t)):(o=t.value,i=[t])}})),o>e.value&&(e.value=o,e.entries=i,r())},c=xo("layout-shift",s);c&&(r=Eo(t,e,No,n.reportAllChanges),jo((()=>{s(c.takeRecords()),r(!0)})),setTimeout(r,0))})))},Do=[100,300],Fo=(t,n={})=>{Ao((()=>{const e=Co(),r=Io("FID");let o;const i=t=>{t.startTime<e.firstHiddenTime&&(r.value=t.processingStart-t.startTime,r.entries.push(t),o(!0))},s=t=>{t.forEach(i)},c=xo("first-input",s);o=Eo(t,r,Do,n.reportAllChanges),c&&jo(Ro((()=>{s(c.takeRecords()),c.disconnect()})))}))};let qo=0,Uo=1/0,Ho=0;const Bo=t=>{t.forEach((t=>{t.interactionId&&(Uo=Math.min(Uo,t.interactionId),Ho=Math.max(Ho,t.interactionId),qo=Ho?(Ho-Uo)/7+1:0)}))};let zo;const Wo=()=>{"interactionCount"in performance||zo||(zo=xo("event",Bo,{type:"event",buffered:!0,durationThreshold:0}))},Xo=[200,500],Go=()=>(zo?qo:performance.interactionCount||0)-0,Jo=[],Yo={},Ko=t=>{const n=Jo[Jo.length-1],e=Yo[t.interactionId];if(e||Jo.length<10||n&&t.duration>n.latency){if(e)e.entries.push(t),e.latency=Math.max(e.latency,t.duration);else{const n={id:t.interactionId,latency:t.duration,entries:[t]};Yo[n.id]=n,Jo.push(n)}Jo.sort(((t,n)=>n.latency-t.latency)),Jo.splice(10).forEach((t=>{delete Yo[t.id]}))}},Vo=(t,n={})=>{Ao((()=>{Wo();const e=Io("INP");let r;const o=t=>{t.forEach((t=>{if(t.interactionId&&Ko(t),"first-input"===t.entryType){!Jo.some((n=>n.entries.some((n=>t.duration===n.duration&&t.startTime===n.startTime))))&&Ko(t)}}));const n=(()=>{const t=Math.min(Jo.length-1,Math.floor(Go()/50));return Jo[t]})();n&&n.latency!==e.value&&(e.value=n.latency,e.entries=n.entries,r())},i=xo("event",o,{durationThreshold:null!=n.durationThreshold?n.durationThreshold:40});r=Eo(t,e,Xo,n.reportAllChanges),i&&("PerformanceEventTiming"in $o&&"interactionId"in PerformanceEventTiming.prototype&&i.observe({type:"first-input",buffered:!0}),jo((()=>{o(i.takeRecords()),e.value<0&&Go()>0&&(e.value=0,e.entries=[]),r(!0)})))}))},Qo=[2500,4e3],Zo={},ti=(t,n={})=>{Ao((()=>{const e=Co(),r=Io("LCP");let o;const i=t=>{const n=t[t.length-1];n&&n.startTime<e.firstHiddenTime&&(r.value=Math.max(n.startTime-ko(),0),r.entries=[n],o())},s=xo("largest-contentful-paint",i);if(s){o=Eo(t,r,Qo,n.reportAllChanges);const e=Ro((()=>{Zo[r.id]||(i(s.takeRecords()),s.disconnect(),Zo[r.id]=!0,o(!0))}));["keydown","click"].forEach((t=>{$o.document&&addEventListener(t,(()=>setTimeout(e,0)),!0)})),jo(e)}}))},ni=[800,1800],ei=t=>{$o.document&&$o.document.prerendering?Ao((()=>ei(t))):$o.document&&"complete"!==$o.document.readyState?addEventListener("load",(()=>ei(t)),!0):setTimeout(t,0)},ri=(t,n={})=>{const e=Io("TTFB"),r=Eo(t,e,ni,n.reportAllChanges);ei((()=>{const t=To();if(t){const n=t.responseStart;if(n<=0||n>performance.now())return;e.value=Math.max(n-ko(),0),e.entries=[t],r(!0)}}))},oi={},ii={};let si,ci,ui,ai,fi;function di(t,n){return bi(t,n),ii[t]||(!function(t){const n={};"event"===t&&(n.durationThreshold=0);xo(t,(n=>{hi(t,{entries:n})}),n)}(t),ii[t]=!0),_i(t,n)}function hi(t,n){const e=oi[t];if(e&&e.length)for(const t of e)try{t(n)}catch(t){}}function li(){return Po((t=>{hi("cls",{metric:t}),si=t}),{reportAllChanges:!0})}function pi(){return Fo((t=>{hi("fid",{metric:t}),ci=t}))}function mi(){return ti((t=>{hi("lcp",{metric:t}),ui=t}),{reportAllChanges:!0})}function gi(){return ri((t=>{hi("ttfb",{metric:t}),ai=t}))}function vi(){return Vo((t=>{hi("inp",{metric:t}),fi=t}))}function yi(t,n,e,r,o=!1){let i;return bi(t,n),ii[t]||(i=e(),ii[t]=!0),r&&n({metric:r}),_i(t,n,o?i:void 0)}function bi(t,n){oi[t]=oi[t]||[],oi[t].push(n)}function _i(t,n,e){return()=>{e&&e();const r=oi[t];if(!r)return;const o=r.indexOf(n);-1!==o&&r.splice(o,1)}}function wi(t){return"number"==typeof t&&isFinite(t)}function Si(t,n,e,{...r}){const o=ie(t).start_timestamp;return o&&o>n&&"function"==typeof t.updateStartTime&&t.updateStartTime(n),Fe(t,(()=>{const t=De({startTime:n,...r});return t&&t.end(e),t}))}function Ei(){return $o&&$o.addEventListener&&$o.performance}function $i(t){return t/1e3}const Ti=2147483647;let ki,Ii,xi=0,ji={};function Ri(){const t=Ei();if(t&&mt){t.mark&&$o.performance.mark("sentry-tracing-init");const n=yi("fid",(({metric:t})=>{const n=t.entries[t.entries.length-1];if(!n)return;const e=$i(mt),r=$i(n.startTime);ji.fid={value:t.value,unit:"millisecond"},ji["mark.fid"]={value:e+r,unit:"second"}}),pi,ci),e=function(t,n=!1){return yi("cls",t,li,si,n)}((({metric:t})=>{const n=t.entries[t.entries.length-1];n&&(ji.cls={value:t.value,unit:""},Ii=n)}),!0),r=function(t,n=!1){return yi("lcp",t,mi,ui,n)}((({metric:t})=>{const n=t.entries[t.entries.length-1];n&&(ji.lcp={value:t.value,unit:"millisecond"},ki=n)}),!0),o=function(t){return yi("ttfb",t,gi,ai)}((({metric:t})=>{t.entries[t.entries.length-1]&&(ji.ttfb={value:t.value,unit:"millisecond"})}));return()=>{n(),e(),r(),o()}}return()=>{}}function Mi(t){const n=Ei();if(!n||!$o.performance.getEntries||!mt)return;const e=$i(mt),r=n.getEntries(),{op:o,start_timestamp:i}=ie(t);if(r.slice(xi).forEach((n=>{const r=$i(n.startTime),s=$i(n.duration);if(!("navigation"===o&&i&&e+r<i))switch(n.entryType){case"navigation":!function(t,n,e){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{Oi(t,n,r,e)})),Oi(t,n,"secureConnection",e,"TLS/SSL","connectEnd"),Oi(t,n,"fetch",e,"cache","domainLookupStart"),Oi(t,n,"domainLookup",e,"DNS"),function(t,n,e){const r=e+$i(n.requestStart),o=e+$i(n.responseEnd),i=e+$i(n.responseStart);n.responseEnd&&(Si(t,r,o,{op:"browser",name:"request",attributes:{[zn]:"auto.ui.browser.metrics"}}),Si(t,i,o,{op:"browser",name:"response",attributes:{[zn]:"auto.ui.browser.metrics"}}))}(t,n,e)}(t,n,e);break;case"mark":case"paint":case"measure":{!function(t,n,e,r,o){const i=To(),s=$i(i?i.requestStart:0),c=o+Math.max(e,s),u=o+e,a=u+r,f={[zn]:"auto.resource.browser.metrics"};c!==u&&(f["sentry.browser.measure_happened_before_request"]=!0,f["sentry.browser.measure_start_time"]=c);Si(t,c,a,{name:n.name,op:n.entryType,attributes:f})}(t,n,r,s,e);const o=Co(),i=n.startTime<o.firstHiddenTime;"first-paint"===n.name&&i&&(ji.fp={value:n.startTime,unit:"millisecond"}),"first-contentful-paint"===n.name&&i&&(ji.fcp={value:n.startTime,unit:"millisecond"});break}case"resource":!function(t,n,e,r,o,i){if("xmlhttprequest"===n.initiatorType||"fetch"===n.initiatorType)return;const s=Ut(e),c={[zn]:"auto.resource.browser.metrics"};Ci(c,n,"transferSize","http.response_transfer_size"),Ci(c,n,"encodedBodySize","http.response_content_length"),Ci(c,n,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in n&&(c["resource.render_blocking_status"]=n.renderBlockingStatus);s.protocol&&(c["url.scheme"]=s.protocol.split(":").pop());s.host&&(c["server.address"]=s.host);c["url.same_origin"]=e.includes($o.location.origin);const u=i+r,a=u+o;Si(t,u,a,{name:e.replace($o.location.origin,""),op:n.initiatorType?`resource.${n.initiatorType}`:"resource.other",attributes:c})}(t,n,n.name,r,s,e)}})),xi=Math.max(r.length-1,0),function(t){const n=$o.navigator;if(!n)return;const e=n.connection;e&&(e.effectiveType&&t.setAttribute("effectiveConnectionType",e.effectiveType),e.type&&t.setAttribute("connectionType",e.type),wi(e.rtt)&&(ji["connection.rtt"]={value:e.rtt,unit:"millisecond"}));wi(n.deviceMemory)&&t.setAttribute("deviceMemory",`${n.deviceMemory} GB`);wi(n.hardwareConcurrency)&&t.setAttribute("hardwareConcurrency",String(n.hardwareConcurrency))}(t),"pageload"===o){!function(t){const n=To();if(!n)return;const{responseStart:e,requestStart:r}=n;r<=e&&(t["ttfb.requestTime"]={value:e-r,unit:"millisecond"})}(ji),["fcp","fp","lcp"].forEach((t=>{const n=ji[t];if(!n||!i||e>=i)return;const r=n.value,o=e+$i(r),s=Math.abs(1e3*(o-i));n.value=s}));const n=ji["mark.fid"];n&&ji.fid&&(Si(t,n.value,n.value+$i(ji.fid.value),{name:"first input delay",op:"ui.action",attributes:{[zn]:"auto.ui.browser.metrics"}}),delete ji["mark.fid"]),"fcp"in ji||delete ji.cls,Object.entries(ji).forEach((([t,n])=>{Me(t,n.value,n.unit)})),function(t){ki&&(ki.element&&t.setAttribute("lcp.element",I(ki.element)),ki.id&&t.setAttribute("lcp.id",ki.id),ki.url&&t.setAttribute("lcp.url",ki.url.trim().slice(0,200)),t.setAttribute("lcp.size",ki.size));Ii&&Ii.sources&&Ii.sources.forEach(((n,e)=>t.setAttribute(`cls.source.${e+1}`,I(n.node))))}(t)}ki=void 0,Ii=void 0,ji={}}function Oi(t,n,e,r,o,i){const s=i?n[i]:n[`${e}End`],c=n[`${e}Start`];c&&s&&Si(t,r+$i(c),r+$i(s),{op:"browser",name:o||e,attributes:{[zn]:"auto.ui.browser.metrics"}})}function Ci(t,n,e,r){const o=n[e];null!=o&&o<Ti&&(t[r]=o)}const Ai=1e3;let Li,Ni,Pi,Di;function Fi(){if(!$o.document)return;const t=ct.bind(null,"dom"),n=qi(t,!0);$o.document.addEventListener("click",n,!1),$o.document.addEventListener("keypress",n,!1),["EventTarget","Node"].forEach((n=>{const e=$o[n]&&$o[n].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(F(e,"addEventListener",(function(n){return function(e,r,o){if("click"===e||"keypress"==e)try{const r=this,i=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},s=i[e]=i[e]||{refCount:0};if(!s.handler){const r=qi(t);s.handler=r,n.call(this,e,r,o)}s.refCount++}catch(t){}return n.call(this,e,r,o)}})),F(e,"removeEventListener",(function(t){return function(n,e,r){if("click"===n||"keypress"==n)try{const e=this,o=e.__sentry_instrumentation_handlers__||{},i=o[n];i&&(i.refCount--,i.refCount<=0&&(t.call(this,n,i.handler,r),i.handler=void 0,delete o[n]),0===Object.keys(o).length&&delete e.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,n,e,r)}})))}))}function qi(t,n=!1){return e=>{if(!e||e._sentryCaptured)return;const r=function(t){try{return t.target}catch(t){return null}}(e);if(function(t,n){return"keypress"===t&&(!n||!n.tagName||"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&!n.isContentEditable)}(e.type,r))return;q(e,"_sentryCaptured",!0),r&&!r._sentryId&&q(r,"_sentryId",kt());const o="keypress"===e.type?"input":e.type;if(!function(t){if(t.type!==Ni)return!1;try{if(!t.target||t.target._sentryId!==Pi)return!1}catch(t){}return!0}(e)){t({event:e,name:o,global:n}),Ni=e.type,Pi=r?r._sentryId:void 0}clearTimeout(Li),Li=$o.setTimeout((()=>{Pi=void 0,Ni=void 0}),Ai)}}function Ui(t){const n="history";it(n,t),st(n,Hi)}function Hi(){if(!function(){const t=pn.chrome,n=t&&t.app&&t.app.runtime,e="history"in pn&&!!pn.history.pushState&&!!pn.history.replaceState;return!n&&e}())return;const t=$o.onpopstate;function n(t){return function(...n){const e=n.length>2?n[2]:void 0;if(e){const t=Di,n=String(e);Di=n;ct("history",{from:t,to:n})}return t.apply(this,n)}}$o.onpopstate=function(...n){const e=$o.location.href,r=Di;Di=e;if(ct("history",{from:r,to:e}),t)try{return t.apply(this,n)}catch(t){}},F($o.history,"pushState",n),F($o.history,"replaceState",n)}const Bi={};function zi(t){Bi[t]=void 0}const Wi="__sentry_xhr_v3__";function Xi(t){it("xhr",t),st("xhr",Gi)}function Gi(){if(!$o.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;F(t,"open",(function(t){return function(...n){const e=1e3*pt(),r=s(n[0])?n[0].toUpperCase():void 0,o=function(t){if(s(t))return t;try{return t.toString()}catch(t){}return}(n[1]);if(!r||!o)return t.apply(this,n);this[Wi]={method:r,url:o,request_headers:{}},"POST"===r&&o.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const i=()=>{const t=this[Wi];if(t&&4===this.readyState){try{t.status_code=this.status}catch(t){}ct("xhr",{endTimestamp:1e3*pt(),startTimestamp:e,xhr:this})}};return"onreadystatechange"in this&&"function"==typeof this.onreadystatechange?F(this,"onreadystatechange",(function(t){return function(...n){return i(),t.apply(this,n)}})):this.addEventListener("readystatechange",i),F(this,"setRequestHeader",(function(t){return function(...n){const[e,r]=n,o=this[Wi];return o&&s(e)&&s(r)&&(o.request_headers[e.toLowerCase()]=r),t.apply(this,n)}})),t.apply(this,n)}})),F(t,"send",(function(t){return function(...n){const e=this[Wi];if(!e)return t.apply(this,n);void 0!==n[0]&&(e.body=n[0]);return ct("xhr",{startTimestamp:1e3*pt(),xhr:this}),t.apply(this,n)}}))}const Ji=[],Yi=new Map;function Ki(){if(Ei()&&mt){const t=yi("inp",(({metric:t})=>{const n=Dn();if(!n||null==t.value)return;const e=t.entries.find((n=>n.duration===t.value&&Vi[n.name]));if(!e)return;const{interactionId:r}=e,o=Vi[e.name],i=n.getOptions(),s=$i(mt+e.startTime),c=$i(t.value),u=An(),a=le(),f=a?he(a):void 0,d=(null!=r?Yi.get(r):void 0)||f,h=d?ie(d).description:u.getScopeData().transactionName,l=u.getUser(),p=n.getIntegrationByName("Replay"),m=p&&p.getReplayId(),g=void 0!==l?l.email||l.id||l.ip_address:void 0;let v;try{v=u.getScopeData().contexts.profile.profile_id}catch(t){}const y=De({name:I(e.target),op:`ui.interaction.${o}`,attributes:X({release:i.release,environment:i.environment,transaction:h,[Jn]:t.value,[zn]:"auto.http.browser.inp",user:g||void 0,profile_id:v||void 0,replay_id:m||void 0,"user_agent.original":$o.navigator&&$o.navigator.userAgent}),startTime:s,experimental:{standalone:!0}});y.addEvent("inp",{[Xn]:"millisecond",[Gn]:t.value}),y.end(s+c)}),vi,fi);return()=>{t()}}return()=>{}}const Vi={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Qi(t,n=function(t){const n=Bi[t];if(n)return n;let e=$o[t];if(ft(e))return Bi[t]=e.bind($o);const r=$o.document;if(r&&"function"==typeof r.createElement)try{const n=r.createElement("iframe");n.hidden=!0,r.head.appendChild(n);const o=n.contentWindow;o&&o[t]&&(e=o[t]),r.head.removeChild(n)}catch(t){}return e?Bi[t]=e.bind($o):e}("fetch")){let e=0,r=0;return Mr(t,(function(o){const i=o.body.length;e+=i,r++;const s={body:o.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:e<=6e4&&r<15,...t.fetchOptions};if(!n)return zi("fetch"),Dt("No fetch implementation available");try{return n(t.url,s).then((t=>(e-=i,r--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(t){return zi("fetch"),e-=i,r--,Dt(t)}}))}function Zi(t,n,e,r){const o={filename:t,function:"<anonymous>"===n?Y:n,in_app:!0};return void 0!==e&&(o.lineno=e),void 0!==r&&(o.colno=r),o}const ts=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,ns=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,es=/\((\S*)(?::(\d+))(?::(\d+))\)/,rs=[30,t=>{const n=ts.exec(t);if(n){const[,t,e,r]=n;return Zi(t,Y,+e,+r)}const e=ns.exec(t);if(e){if(e[2]&&0===e[2].indexOf("eval")){const t=es.exec(e[2]);t&&(e[2]=t[1],e[3]=t[2],e[4]=t[3])}const[t,n]=ms(e[1]||Y,e[2]);return Zi(n,t,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}}],os=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,is=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,ss=[50,t=>{const n=os.exec(t);if(n){if(n[3]&&n[3].indexOf(" > eval")>-1){const t=is.exec(n[3]);t&&(n[1]=n[1]||"eval",n[3]=t[1],n[4]=t[2],n[5]="")}let t=n[3],e=n[1]||Y;return[e,t]=ms(e,t),Zi(t,e,n[4]?+n[4]:void 0,n[5]?+n[5]:void 0)}}],cs=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,us=[40,t=>{const n=cs.exec(t);return n?Zi(n[2],n[1]||Y,+n[3],n[4]?+n[4]:void 0):void 0}],as=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,fs=[10,t=>{const n=as.exec(t);return n?Zi(n[2],n[3]||Y,+n[1]):void 0}],ds=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,hs=[20,t=>{const n=ds.exec(t);return n?Zi(n[5],n[3]||n[4]||Y,+n[1],+n[2]):void 0}],ls=[rs,ss],ps=Q(...ls),ms=(t,n)=>{const e=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return e||r?[-1!==t.indexOf("@")?t.split("@")[0]:Y,e?`safari-extension:${n}`:`safari-web-extension:${n}`]:[t,n]},gs=1024,vs=(t={})=>{const n={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(t){var e;n.console&&function(t){const n="console";it(n,t),st(n,ut)}(function(t){return function(n){if(Dn()!==t)return;const e={category:"console",data:{arguments:n.args,logger:"console"},level:Bt(n.level),message:m(n.args," ")};if("assert"===n.level){if(!1!==n.args[0])return;e.message=`Assertion failed: ${m(n.args.slice(1)," ")||"console.assert"}`,e.data.arguments=n.args.slice(1)}Ar(e,{input:n.args,level:n.level})}}(t)),n.dom&&(e=function(t,n){return function(e){if(Dn()!==t)return;let r,o,i="object"==typeof n?n.serializeAttribute:void 0,s="object"==typeof n&&"number"==typeof n.maxStringLength?n.maxStringLength:void 0;s&&s>gs&&(s=gs),"string"==typeof i&&(i=[i]);try{const t=e.event,n=function(t){return!!t&&!!t.target}(t)?t.target:t;r=I(n,{keyAttrs:i,maxStringLength:s}),o=j(n)}catch(t){r="<unknown>"}if(0===r.length)return;const c={category:`ui.${e.name}`,message:r};o&&(c.data={"ui.component_name":o}),Ar(c,{event:e.event,name:e.name,global:e.global})}}(t,n.dom),it("dom",e),st("dom",Fi)),n.xhr&&Xi(function(t){return function(n){if(Dn()!==t)return;const{startTimestamp:e,endTimestamp:r}=n,o=n.xhr[Wi];if(!e||!r||!o)return;const{method:i,url:s,status_code:c,body:u}=o;Ar({category:"xhr",data:{method:i,url:s,status_code:c},type:"http"},{xhr:n.xhr,input:u,startTimestamp:e,endTimestamp:r})}}(t)),n.fetch&&gt(function(t){return function(n){if(Dn()!==t)return;const{startTimestamp:e,endTimestamp:r}=n;if(r&&(!n.fetchData.url.match(/sentry_key/)||"POST"!==n.fetchData.method))if(n.error){Ar({category:"fetch",data:n.fetchData,level:"error",type:"http"},{data:n.error,input:n.args,startTimestamp:e,endTimestamp:r})}else{const t=n.response;Ar({category:"fetch",data:{...n.fetchData,status_code:t&&t.status},type:"http"},{input:n.args,response:t,startTimestamp:e,endTimestamp:r})}}}(t)),n.history&&Ui(function(t){return function(n){if(Dn()!==t)return;let e=n.from,r=n.to;const o=Ut(io.location.href);let i=e?Ut(e):void 0;const s=Ut(r);i&&i.path||(i=o),o.protocol===s.protocol&&o.host===s.host&&(r=s.relative),o.protocol===i.protocol&&o.host===i.host&&(e=i.relative),Ar({category:"navigation",data:{from:e,to:r}})}}(t)),n.sentry&&t.on("beforeSendEvent",function(t){return function(n){Dn()===t&&Ar({category:"sentry."+("transaction"===n.type?"transaction":"event"),event_id:n.event_id,level:n.level,message:xt(n)},{event:n})}}(t))}}};const ys=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],bs=(t={})=>{const n={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){n.setTimeout&&F(io,"setTimeout",_s),n.setInterval&&F(io,"setInterval",_s),n.requestAnimationFrame&&F(io,"requestAnimationFrame",ws),n.XMLHttpRequest&&"XMLHttpRequest"in io&&F(XMLHttpRequest.prototype,"send",Ss);const t=n.eventTarget;if(t){(Array.isArray(t)?t:ys).forEach(Es)}}}};function _s(t){return function(...n){const e=n[0];return n[0]=uo(e,{mechanism:{data:{function:nt(t)},handled:!1,type:"instrument"}}),t.apply(this,n)}}function ws(t){return function(n){return t.apply(this,[uo(n,{mechanism:{data:{function:"requestAnimationFrame",handler:nt(t)},handled:!1,type:"instrument"}})])}}function Ss(t){return function(...n){const e=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in e&&"function"==typeof e[t]&&F(e,t,(function(n){const e={mechanism:{data:{function:t,handler:nt(n)},handled:!1,type:"instrument"}},r=H(n);return r&&(e.mechanism.data.handler=nt(r)),uo(n,e)}))})),t.apply(this,n)}}function Es(t){const n=io,e=n[t]&&n[t].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(F(e,"addEventListener",(function(n){return function(e,r,o){try{"function"==typeof r.handleEvent&&(r.handleEvent=uo(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:nt(r),target:t},handled:!1,type:"instrument"}}))}catch(t){}return n.apply(this,[e,uo(r,{mechanism:{data:{function:"addEventListener",handler:nt(r),target:t},handled:!1,type:"instrument"}}),o])}})),F(e,"removeEventListener",(function(t){return function(n,e,r){const o=e;try{const e=o&&o.__sentry_wrapped__;e&&t.call(this,n,e,r)}catch(t){}return t.call(this,n,o,r)}})))}const $s=(t={})=>{const n={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(t){n.onerror&&function(t){wt((n=>{const{stackParser:e,attachStacktrace:r}=Ts();if(Dn()!==t||co())return;const{msg:o,url:i,line:c,column:u,error:a}=n,f=function(t,n,e,r){const o=t.exception=t.exception||{},i=o.values=o.values||[],c=i[0]=i[0]||{},u=c.stacktrace=c.stacktrace||{},a=u.frames=u.frames||[],f=isNaN(parseInt(r,10))?void 0:r,d=isNaN(parseInt(e,10))?void 0:e,h=s(n)&&n.length>0?n:function(){try{return T.document.location.href}catch(t){return""}}();0===a.length&&a.push({colno:f,filename:h,function:Y,in_app:!0,lineno:d});return t}(yo(e,a||o,void 0,r,!1),i,c,u);f.level="error",sr(f,{originalException:a,mechanism:{handled:!1,type:"onerror"}})}))}(t),n.onunhandledrejection&&function(t){$t((n=>{const{stackParser:e,attachStacktrace:r}=Ts();if(Dn()!==t||co())return;const o=function(t){if(u(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch(t){}return t}(n),i=u(o)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(o)}`}]}}:yo(e,o,void 0,r,!0);i.level="error",sr(i,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}(t)}}};function Ts(){const t=Dn();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const ks=()=>({name:"HttpContext",preprocessEvent(t){if(!io.navigator&&!io.location&&!io.document)return;const n=t.request&&t.request.url||io.location&&io.location.href,{referrer:e}=io.document||{},{userAgent:r}=io.navigator||{},o={...t.request&&t.request.headers,...e&&{Referer:e},...r&&{"User-Agent":r}},i={...t.request,...n&&{url:n},headers:o};t.request=i}}),Is=(t={})=>{const n=t.limit||5,e=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(t,r,o){const i=o.getOptions();y(ao,i.stackParser,i.maxValueLength,e,n,t,r)}}};function xs(t){return[Fr(),Pr(),bs(),vs(),$s(),Is(),Ur(),ks()]}const js={replayIntegration:"replay",replayCanvasIntegration:"replay-canvas",feedbackIntegration:"feedback",feedbackModalIntegration:"feedback-modal",feedbackScreenshotIntegration:"feedback-screenshot",captureConsoleIntegration:"captureconsole",contextLinesIntegration:"contextlines",linkedErrorsIntegration:"linkederrors",debugIntegration:"debug",dedupeIntegration:"dedupe",extraErrorDataIntegration:"extraerrordata",httpClientIntegration:"httpclient",reportingObserverIntegration:"reportingobserver",rewriteFramesIntegration:"rewriteframes",sessionTimingIntegration:"sessiontiming",browserProfilingIntegration:"browserprofiling"},Rs=io;const Ms={increment:function(t,n=1,e){Yr.increment(eo,t,n,e)},distribution:function(t,n,e){Yr.distribution(eo,t,n,e)},set:function(t,n,e){Yr.set(eo,t,n,e)},gauge:function(t,n,e){Yr.gauge(eo,t,n,e)},timing:function(t,n,e="second",r){return Yr.timing(eo,t,n,e,r)}};const Os={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0};function Cs(t){const{traceFetch:n,traceXHR:e,shouldCreateSpanForRequest:r,enableHTTPTimings:o,tracePropagationTargets:i}={traceFetch:Os.traceFetch,traceXHR:Os.traceXHR,...t},s="function"==typeof r?r:t=>!0,c=t=>function(t,n){const e=io.location&&io.location.href;if(e){let r,o;try{r=new URL(t,e),o=new URL(e).origin}catch(t){return!1}const i=r.origin===o;return n?v(r.toString(),n)||i&&v(r.pathname,n):i}{const e=!!t.match(/^\/(?!\/)/);return n?v(t,n):e}}(t,i),u={};n&&gt((t=>{const n=ro(t,s,c,u);if(n){const e=Ns(t.fetchData.url),r=e?Ut(e).host:void 0;n.setAttributes({"http.url":e,"server.address":r})}o&&n&&As(n)})),e&&Xi((t=>{const n=function(t,n,e,r){const o=t.xhr,i=o&&o[Wi];if(!o||o.__sentry_own_request__||!i)return;const s=we()&&n(i.url);if(t.endTimestamp&&s){const t=o.__sentry_xhr_span_id__;if(!t)return;const n=r[t];return void(n&&void 0!==i.status_code&&(Qn(n,i.status_code),n.end(),delete r[t]))}const c=Ns(i.url),u=c?Ut(c).host:void 0,a=!!le(),f=s&&a?De({name:`${i.method} ${i.url}`,attributes:{type:"xhr","http.method":i.method,"http.url":c,url:i.url,"server.address":u,[zn]:"auto.http.browser",[Bn]:"http.client"}}):new Se;o.__sentry_xhr_span_id__=f.spanContext().spanId,r[o.__sentry_xhr_span_id__]=f;const d=Dn();o.setRequestHeader&&e(i.url)&&d&&function(t,n,e){const r=An(),o=Ln(),{traceId:i,spanId:s,sampled:c,dsc:u}={...o.getPropagationContext(),...r.getPropagationContext()},a=e&&we()?ee(e):Zt(i,s,c),f=Yt(u||(e?xe(e):Ie(i,n)));!function(t,n,e){try{t.setRequestHeader("sentry-trace",n),e&&t.setRequestHeader(zt,e)}catch(t){}}(t,a,f)}(o,d,we()&&a?f:void 0);return f}(t,s,c,u);o&&n&&As(n)}))}function As(t){const{url:n}=ie(t).data||{};if(!n||"string"!=typeof n)return;const e=di("resource",(({entries:r})=>{r.forEach((r=>{if(function(t){return"resource"===t.entryType&&"initiatorType"in t&&"string"==typeof t.nextHopProtocol&&("fetch"===t.initiatorType||"xmlhttprequest"===t.initiatorType)}(r)&&r.name.endsWith(n)){(function(t){const{name:n,version:e}=function(t){let n="unknown",e="unknown",r="";for(const o of t){if("/"===o){[n,e]=t.split("/");break}if(!isNaN(Number(o))){n="h"===r?"http":r,e=t.split(r)[1];break}r+=o}r===t&&(n=r);return{name:n,version:e}}(t.nextHopProtocol),r=[];if(r.push(["network.protocol.version",e],["network.protocol.name",n]),!mt)return r;return[...r,["http.request.redirect_start",Ls(t.redirectStart)],["http.request.fetch_start",Ls(t.fetchStart)],["http.request.domain_lookup_start",Ls(t.domainLookupStart)],["http.request.domain_lookup_end",Ls(t.domainLookupEnd)],["http.request.connect_start",Ls(t.connectStart)],["http.request.secure_connection_start",Ls(t.secureConnectionStart)],["http.request.connection_end",Ls(t.connectEnd)],["http.request.request_start",Ls(t.requestStart)],["http.request.response_start",Ls(t.responseStart)],["http.request.response_end",Ls(t.responseEnd)]]})(r).forEach((n=>t.setAttribute(...n))),setTimeout(e)}}))}))}function Ls(t=0){return((mt||performance.timeOrigin)+t)/1e3}function Ns(t){try{return new URL(t,io.location.origin).href}catch(t){return}}const Ps={...Xe,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!1,enableInp:!0,_experiments:{},...Os};function Ds(t,n,e){t.emit("startPageLoadSpan",n,e),An().setTransactionName(n.name);const r=le();return"pageload"===(r&&ie(r).op)?r:void 0}function Fs(t,n){Ln().setPropagationContext(ln()),An().setPropagationContext(ln()),t.emit("startNavigationSpan",n),An().setTransactionName(n.name);const e=le();return"navigation"===(e&&ie(e).op)?e:void 0}function qs(t){const n=(e=`meta[name=${t}]`,T.document&&T.document.querySelector?T.document.querySelector(e):null);var e;return n?n.getAttribute("content"):void 0}return ge(),t.BrowserClient=So,t.SDK_VERSION=S,t.SEMANTIC_ATTRIBUTE_SENTRY_OP=Bn,t.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=zn,t.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=Hn,t.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=Un,t.Scope=In,t.WINDOW=io,t.addBreadcrumb=Ar,t.addEventProcessor=function(t){Ln().addEventProcessor(t)},t.addIntegration=function(t){const n=Dn();n&&n.addIntegration(t)},t.breadcrumbsIntegration=vs,t.browserApiErrorsIntegration=bs,t.browserTracingIntegration=(t={})=>{ge();const{enableInp:n,enableLongTask:e,enableLongAnimationFrame:r,_experiments:{enableInteractions:o},beforeStartSpan:i,idleTimeout:s,finalTimeout:c,childSpanTimeout:u,markBackgroundSpan:a,traceFetch:f,traceXHR:d,shouldCreateSpanForRequest:h,enableHTTPTimings:l,instrumentPageLoad:p,instrumentNavigation:m}={...Ps,...t},g=Ri();n&&Ki(),r&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver((t=>{for(const n of t.getEntries()){if(!le())return;if(!n.scripts[0])return;const t=$i(mt+n.startTime),e=$i(n.duration),r={[zn]:"auto.ui.browser.metrics"},o=n.scripts[0];if(o){const{invoker:t,invokerType:n,sourceURL:e,sourceFunctionName:i,sourceCharPosition:s}=o;r["browser.script.invoker"]=t,r["browser.script.invoker_type"]=n,e&&(r["code.filepath"]=e),i&&(r["code.function"]=i),-1!==s&&(r["browser.script.source_char_position"]=s)}const i=De({name:"Main UI thread blocked",op:"ui.long-animation-frame",startTime:t,attributes:r});i&&i.end(t+e)}})).observe({type:"long-animation-frame",buffered:!0}):e&&di("longtask",(({entries:t})=>{for(const n of t){if(!le())return;const t=$i(mt+n.startTime),e=$i(n.duration),r=De({name:"Main UI thread blocked",op:"ui.long-task",startTime:t,attributes:{[zn]:"auto.ui.browser.metrics"}});r&&r.end(t+e)}})),o&&di("event",(({entries:t})=>{for(const n of t){if(!le())return;if("click"===n.name){const t=$i(mt+n.startTime),e=$i(n.duration),r={name:I(n.target),op:`ui.interaction.${n.name}`,startTime:t,attributes:{[zn]:"auto.ui.browser.metrics"}},o=j(n.target);o&&(r.attributes["ui.component_name"]=o);const i=De(r);i&&i.end(t+e)}}}));const v={name:void 0,source:void 0};function y(t,n){const e="pageload"===n.op,r=i?i(n):n,o=r.attributes||{};n.name!==r.name&&(o[Un]="custom",r.attributes=o),v.name=r.name,v.source=o[Un];const a=Ve(r,{idleTimeout:s,finalTimeout:c,childSpanTimeout:u,disableAutoFinish:e,beforeSpanEnd:t=>{g(),Mi(t)}});function f(){["interactive","complete"].includes(io.document.readyState)&&t.emit("idleSpanEnableAutoFinish",a)}return e&&io.document&&(io.document.addEventListener("readystatechange",(()=>{f()})),f()),a}return{name:"BrowserTracing",afterAllSetup(t){let e,r=io.location&&io.location.href;t.on("startNavigationSpan",(n=>{Dn()===t&&(e&&!ie(e).timestamp&&e.end(),e=y(t,{op:"navigation",...n}))})),t.on("startPageLoadSpan",((n,r={})=>{if(Dn()!==t)return;e&&!ie(e).timestamp&&e.end();const o=Qt(r.sentryTrace||qs("sentry-trace"),r.baggage||qs("baggage"));An().setPropagationContext(o),e=y(t,{op:"pageload",...n})})),t.on("spanEnd",(t=>{const n=ie(t).op;if(t!==he(t)||"navigation"!==n&&"pageload"!==n)return;const e=An(),r=e.getPropagationContext();e.setPropagationContext({...r,sampled:void 0!==r.sampled?r.sampled:se(t),dsc:r.dsc||xe(t)})})),io.location&&(p&&Ds(t,{name:io.location.pathname,startTime:mt?mt/1e3:void 0,attributes:{[Un]:"url",[zn]:"auto.pageload.browser"}}),m&&Ui((({to:n,from:e})=>{void 0===e&&r&&-1!==r.indexOf(n)?r=void 0:e!==n&&(r=void 0,Fs(t,{name:io.location.pathname,attributes:{[Un]:"url",[zn]:"auto.navigation.browser"}}))}))),a&&io&&io.document&&io.document.addEventListener("visibilitychange",(()=>{const t=le();if(!t)return;const n=he(t);if(io.document.hidden&&n){const t="cancelled",{op:e,status:r}=ie(n);r||n.setStatus({code:Vn,message:t}),n.setAttribute("sentry.cancellation_reason","document.hidden"),n.end()}})),o&&function(t,n,e,r){let o;const i=()=>{const i="ui.action.click",s=le(),c=s&&he(s);if(c){const t=ie(c).op;if(["navigation","pageload"].includes(t))return}o&&(o.setAttribute(Wn,"interactionInterrupted"),o.end(),o=void 0),r.name&&(o=Ve({name:r.name,op:i,attributes:{[Un]:r.source||"url"}},{idleTimeout:t,finalTimeout:n,childSpanTimeout:e}))};io.document&&addEventListener("click",i,{once:!1,capture:!0})}(s,c,u,v),n&&function(t){const n=({entries:t})=>{const n=le(),e=n&&he(n);t.forEach((t=>{if(!function(t){return"duration"in t}(t)||!e)return;const n=t.interactionId;if(null!=n&&!Yi.has(n)){if(Ji.length>10){const t=Ji.shift();Yi.delete(t)}Ji.push(n),Yi.set(n,e)}}))};di("event",n),di("first-input",n)}(),Cs({traceFetch:f,traceXHR:d,tracePropagationTargets:t.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:h,enableHTTPTimings:l})}}},t.captureEvent=sr,t.captureException=captureException,t.captureMessage=function(t,n){const e="string"==typeof n?n:void 0,r="string"!=typeof n?{captureContext:n}:void 0;return An().captureMessage(t,e,r)},t.captureSession=vr,t.captureUserFeedback=function(t){const n=Dn();n&&n.captureUserFeedback(t)},t.chromeStackLineParser=rs,t.close=async function(t){const n=Dn();return n?n.close(t):Promise.resolve(!1)},t.continueTrace=({sentryTrace:t,baggage:n},e)=>Pn((r=>{const o=Qt(t,n);return r.setPropagationContext(o),e()})),t.createTransport=Mr,t.createUserFeedbackEnvelope=wo,t.dedupeIntegration=Ur,t.defaultStackLineParsers=ls,t.defaultStackParser=ps,t.endSession=mr,t.eventFromException=go,t.eventFromMessage=vo,t.exceptionFromError=ao,t.feedbackAsyncIntegration=vn,t.feedbackIntegration=vn,t.flush=async function(t){const n=Dn();return n?n.flush(t):Promise.resolve(!1)},t.forceLoad=function(){},t.functionToStringIntegration=Pr,t.geckoStackLineParser=ss,t.getActiveSpan=le,t.getClient=Dn,t.getCurrentHub=oo,t.getCurrentScope=An,t.getDefaultIntegrations=xs,t.getGlobalScope=Nn,t.getIsolationScope=Ln,t.getRootSpan=he,t.getSpanDescendants=de,t.globalHandlersIntegration=$s,t.httpContextIntegration=ks,t.inboundFiltersIntegration=Fr,t.init=function(t={}){const n=function(t={}){return{defaultIntegrations:xs(),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:io.SENTRY_RELEASE&&io.SENTRY_RELEASE.id?io.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0,...t}}(t);if(function(){const t=io,n=t[t.chrome?"chrome":"browser"],e=n&&n.runtime&&n.runtime.id,r=io.location&&io.location.href||"",o=!!e&&io===io.top&&["chrome-extension:","moz-extension:","ms-browser-extension:"].some((t=>r.startsWith(`${t}//`))),i=void 0!==t.nw;return!!e&&!o&&!i}())return void O((()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));const e={...n,stackParser:(r=n.stackParser||ps,Array.isArray(r)?Q(...r):r),integrations:Er(n),transport:n.transport||Qi};var r;const o=function(t,n){!0===n.debug&&O((()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),An().update(n.initialScope);const e=new t(n);return jr(e),e.init(),e}(So,e);return n.autoSessionTracking&&function(){if(void 0===io.document)return;pr({ignoreDuration:!0}),vr(),Ui((({from:t,to:n})=>{void 0!==t&&t!==n&&(pr({ignoreDuration:!0}),vr())}))}(),o},t.isInitialized=function(){return!!Dn()},t.lastEventId=lr,t.lazyLoadIntegration=async function(t){const n=js[t],e=Rs.Sentry=Rs.Sentry||{};if(!n)throw new Error(`Cannot lazy load integration: ${t}`);const r=e[t];if("function"==typeof r)return r;const o=function(t){const n=Dn(),e=n&&n.getOptions(),r=e&&e.cdnBaseUrl||"https://browser.sentry-cdn.com";return new URL(`/${S}/${t}.min.js`,r).toString()}(n),i=io.document.createElement("script");i.src=o,i.crossOrigin="anonymous",i.referrerPolicy="origin";const s=new Promise(((t,n)=>{i.addEventListener("load",(()=>t())),i.addEventListener("error",n)}));io.document.body.appendChild(i);try{await s}catch(n){throw new Error(`Error when loading integration: ${t}`)}const c=e[t];if("function"!=typeof c)throw new Error(`Could not load integration: ${t}`);return c},t.linkedErrorsIntegration=Is,t.makeFetchTransport=Qi,t.metrics=Ms,t.onLoad=function(t){t()},t.opera10StackLineParser=fs,t.opera11StackLineParser=hs,t.parameterize=function(t,...n){const e=new String(String.raw(t,...n));return e.__sentry_template_string__=t.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),e.__sentry_template_values__=n,e},t.replayIntegration=function(t){return O((()=>{console.warn("You are using replayIntegration() even though this bundle does not include replay.")})),{name:"Replay",...yn.reduce(((t,n)=>(t[n]=mn,t)),{})}},t.setContext=cr,t.setCurrentClient=jr,t.setExtra=ar,t.setExtras=ur,t.setMeasurement=Me,t.setTag=dr,t.setTags=fr,t.setUser=hr,t.showReportDialog=function(t={}){if(!io.document)return;const n=An(),e=n.getClient(),r=e&&e.getDsn();if(!r)return;if(n&&(t.user={...n.getUser(),...t.user}),!t.eventId){const n=lr();n&&(t.eventId=n)}const o=io.document.createElement("script");o.async=!0,o.crossOrigin="anonymous",o.src=function(t,n){const e=P(t);if(!e)return"";const r=`${br(e)}embed/error-page/`;let o=`dsn=${L(e)}`;for(const t in n)if("dsn"!==t&&"onClose"!==t)if("user"===t){const t=n.user;if(!t)continue;t.name&&(o+=`&name=${encodeURIComponent(t.name)}`),t.email&&(o+=`&email=${encodeURIComponent(t.email)}`)}else o+=`&${encodeURIComponent(t)}=${encodeURIComponent(n[t])}`;return`${r}?${o}`}(r,t),t.onLoad&&(o.onload=t.onLoad);const{onClose:i}=t;if(i){const t=n=>{if("__sentry_reportdialog_closed__"===n.data)try{i()}finally{io.removeEventListener("message",t)}};io.addEventListener("message",t)}const s=io.document.head||io.document.body;s&&s.appendChild(o)},t.spanToBaggageHeader=function(t){return Yt(xe(t))},t.spanToJSON=ie,t.spanToTraceHeader=ee,t.startBrowserTracingNavigationSpan=Fs,t.startBrowserTracingPageLoadSpan=Ds,t.startInactiveSpan=De,t.startNewTrace=function(t){return Pn((n=>(n.setPropagationContext(ln()),Fe(null,t))))},t.startSession=pr,t.startSpan=function(t,n){const e=He();if(e.startSpan)return e.startSpan(t,n);const r=Ue(t),{forceTransaction:o,parentSpan:i}=t;return Pn(t.scope,(()=>We(i)((()=>{const e=An(),i=ze(e),s=t.onlyIfParent&&!i?new Se:qe({parentSpan:i,spanArguments:r,forceTransaction:o,scope:e});return $n(e,s),Ee((()=>n(s)),(()=>{const{status:t}=ie(s);!s.isRecording()||t&&"ok"!==t||s.setStatus({code:Vn,message:"internal_error"})}),(()=>s.end()))}))))},t.startSpanManual=Pe,t.winjsStackLineParser=us,t.withActiveSpan=Fe,t.withIsolationScope=function(...t){const n=Cn(bn());if(2===t.length){const[e,r]=t;return e?n.withSetIsolationScope(e,r):n.withIsolationScope(r)}return n.withIsolationScope(t[0])},t.withScope=Pn,t}({});
//# sourceMappingURL=bundle.tracing.min.js.map
