.colorSelector{display: inline-block;vertical-align: middle;margin-right:20px;}
.layui-fluid{overflow: initial;}
#bottomNav{position: relative;}
#bottomNav .preview{width: 320px;background-repeat: no-repeat;background-size: 100%;float: left;padding-left: 20px;}
#bottomNav .preview .preview-head{background: url("../img/preview_head.png") no-repeat;position: relative;}
#bottomNav .preview .preview-head>span{color: #ffffff;font-size: 16px;display: block;text-align: center;margin-left: 50px;height: 64px;line-height: 82px;margin-right: 40px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;cursor: pointer;}
#bottomNav .preview .preview-block{border-left: 1px solid #e5e5e5;border-right: 1px solid #e5e5e5;border-bottom: 1px solid #e5e5e5;min-height: 100px;position: relative;}
.preview-block ul{overflow: hidden;display: flex;position: absolute;bottom: 0;width: 100%;border-top:1px solid #e5e5e5;}
.preview-block ul li{text-align: center;flex: 1;margin: 5px 0;}
.preview-block ul li div{margin-bottom: 2px;}
.preview-block ul li img{width: 20px;}
.preview-block ul li span{text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display: block;}
#bottomNav .edit-attribute{position: relative;background: #ffffff;border: 1px solid #e5e5e5;width:400px;padding: 10px;float: left;margin-left: 20px;}
.edit-attribute:before,.edit-attribute:after{right: 100%;top: 20px;border: solid transparent;content: " ";height: 0;width: 0;position: absolute;pointer-events: none;}
.edit-attribute:before{border-color: transparent;border-right-color: #e5e5e5;border-width: 8px;margin-top: -6px;}
.edit-attribute:after{border-color: transparent;border-right-color: #ffffff;border-width: 7px;margin-top: -5px;}
.del{background: #999;color: #FFFFFF;position: absolute;border-radius: 50%;width: 20px;height: 20px;font-size: 12px;font-style: normal;line-height: 18px;text-align: center;right: -10px;top: -10px;cursor: pointer;z-index: 1;display: none;}
.edit-attribute .img-block{display:inline-block;padding:14px;border:1px dashed #e5e5e5;margin-right:10px;cursor:pointer;vertical-align: top;}
.edit-attribute .img-block i.add{display:block;font-style: normal;text-align: center;font-size:30px;}
.edit-attribute .img-block i.del{display: block;}
.edit-attribute .img-block.has-choose-image{width: 84px;height: 84px;border: 1px dashed #e5e5e5;display: inline-block;vertical-align: top;position: relative;line-height: 84px;margin-right: 10px;text-align: center;padding: 0;}
.edit-attribute .img-block.has-choose-image>div{width: 84px;height: 84px;}
.edit-attribute .img-block.has-choose-image img{width: auto;height: auto;max-width: 100%;max-height: 100%;margin-bottom: 10px;}
.edit-attribute .img-block.has-choose-image span{position: absolute;bottom: 0;left: 0;width: 100%;text-align: center;font-size:12px;background: rgba(0,0,0,.6);color:#ffffff;line-height: initial;cursor:pointer;}
.edit-attribute .bottom-menu-config>ul>li{padding:10px;background: #ffffff;border:1px dashed #e5e5e5;position:relative;margin-top:10px;}
.edit-attribute .bottom-menu-config>ul>li:first-child{margin-top:0;}
.edit-attribute .bottom-menu-config>ul>li .image-block{float:left;}
.edit-attribute .bottom-menu-config>ul>li .content-block{display:inline-block;width:71%;}
.edit-attribute .bottom-menu-config>ul>li .content-block .layui-form-label{width:60px;}
.edit-attribute .bottom-menu-config>ul>li .content-block .layui-input-block{margin-left:60px;}
.edit-attribute .bottom-menu-config>ul>li .content-block div{margin-top:10px;}
.edit-attribute .bottom-menu-config>ul>li .content-block div:last-child{margin-top:0;}
.edit-attribute .bottom-menu-config>ul>li:hover .del{display:block;}
.edit-attribute .bottom-menu-config>ul>li .img-hover-block{clear: both;}
.edit-attribute .bottom-menu-config>ul>li .img-hover-block .img-block{margin-top: 10px;}
.edit-attribute .bottom-menu-config .add-item{padding: 10px;border: 1px dashed #e5e5e5;margin: 10px 0;cursor: pointer;}
.edit-attribute .bottom-menu-config .add-item i{font-size: 18px;vertical-align: middle;margin-right: 10px;font-style: normal;}
.edit-attribute .bottom-menu-config .add-item span{vertical-align: middle;}
.edit-attribute .bottom-menu-config p.hint{font-size: 12px;color: #999;margin: 10px;}
.edit-attribute .bottom-menu-config .error-msg{margin-top: 5px;color: #f44;display: none;}
.edit-attribute .bottom-menu-config .layui-form-checkbox span{height: initial;}
/*.ump-btn-reset{border: 1px solid #0d73f5; background-color: transparent; color: #0d73f5; padding: 3px 10px; font-size: 12px; border-radius: 2px; cursor: pointer;}*/
.custom-save{margin-top: 20px;text-align:center;}
.layui-btn.layui-btn-primary.sm{margin-top: 5px;padding: 5px 10px !important;height: auto; font-size: 12px;vertical-align: baseline;}
.layui-form-label, .layui-form-radiot, .layui-input, .layui-select, .layui-textarea{font-size: 12px;}