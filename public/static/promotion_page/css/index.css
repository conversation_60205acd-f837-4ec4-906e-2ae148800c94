body{
    padding-top: 2.75rem;
    box-sizing: border-box;
    background-color: #f5f5f5;
}
.header{
    width: 100%;
    height: 2.75rem;
    display: -ms-flex;
    display: -o-flex;
    display: -moz-flex;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    background-color: white;
    z-index: 666;
}
.header-title{
    font-size: 1.125rem;
    font-weight: 500;
    color: #000000;
}
.header-op{
    width: 4.6875rem;
    height: 1.5rem;
    line-height: 1.5rem;
    box-sizing: border-box;
    background: linear-gradient(0deg, #FB331D 0%, #FE5838 100%);
    box-shadow: 0.0625rem 0.125rem 0.3125rem 0rem rgba(177, 30, 51, 0.2);
    border-radius: 0.75rem;
    font-size: 0.8125rem;
    font-weight: 400;
    color: #FFFFFF;
    text-shadow: 0rem 0.09375rem 0.09375rem rgba(177, 30, 51, 0.58);
    text-align: center;
    position: absolute;
    right: 0.75rem;
    top: 50%;
    -o-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    text-decoration: none;
}
.header-op:visited{
    color: #FFFFFF;
}
.info {
    background-color: #f5f5f5;
}
.info img{
    display: block;
    width: 100%;
    height: auto;
}
.dataForm{
    width: 21.9375rem;
    background: white;
    border-radius: 0.625rem;
    margin: 0.625rem auto;
    box-sizing: border-box;
    padding-bottom: 1.25rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.dataForm-fill{
    width: 100%;
    height: 3.4375rem;
    margin-bottom: 1.46875rem;
}
.row{
    display: flex;
    align-items: center;
    width: 18.75rem;
}
.row:not(:first-child){
    margin-top: 0.625rem;
}
.row label{
    font-size: 0.875rem;
    font-weight: 500;
    color: #333333;
    min-width: 3.75rem;
    text-align: right;
    margin-right: 0.5rem;
}
.row input{
    width: 13.75rem;
    height: 1.6875rem;
    background: #FFFFFF;
    border: 1px solid #EEEEEE;
    border-radius: 0.125rem;
    padding-left: 0.625rem;
    box-sizing: border-box;
}
::placeholder {
    font-size: 0.8125rem;
    font-weight: 500;
    color: #CCCCCC;
}
.codeInput{
    position: relative;
}
.codeInput input{
    padding-right: 5.1875rem;
}
.codeInput span{
    width: 5.1875rem;
    height: 1.1875rem;
    line-height: 1.1875rem;
    text-align: center;
    box-sizing: border-box;
    border-left: 1px solid #eeeeee;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    font-weight: 500;
    color: #EF2C17;
}
.row-remember{
    display: flex;
    align-items: center;
    justify-content: center;
}
.row-remember input[type="checkbox"]{
    width: 0.6875rem;
    height: 0.6875rem;
    background: #FFFFFF;
    border: 1px solid #CCCCCC;
    border-radius: 0.0625rem;
    box-sizing: border-box;
}
.row-remember span{
    font-size: 0.75rem;
    font-weight: 500;
    color: #999999;
    margin-left: 0.3125rem;
}
.dataForm-op{
    width: 18.71875rem;
    height: 2.5rem;
    background: linear-gradient(90deg, #EF2C17 0%, #FF3C03 100%);
    box-shadow: 0rem 0.09375rem 0.375rem 0.03125rem rgba(239, 44, 23, 0.42);
    border-radius: 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #FFFFFF;
    border: none;
    margin-top: 0.625rem;
}
