---
description: 
globs: addon/**
alwaysApply: false
---
# 插件开发指南

## 插件目录结构

插件目录位于系统的 `addon` 文件夹下，每个插件都有自己的文件夹：

```
addon/
├─插件名称/               # 例如alipay
│ ├─admin/               # 管理后台相关代码
│ │ ├─controller/        # 后台控制器
│ │ ├─view/              # 后台视图
│ ├─shop/                # shop模块
│ │ ├─controller/        # 控制器
│ │ ├─view/              # 视图
│ ├─config/              # 配置文件目录
│ │ ├─diy_view.php       # 自定义模板配置文件（必存在）
│ │ ├─event.php          # 事件配置文件（必存在）
│ │ ├─info.php           # 插件配置文件（必存在）
│ │ ├─menu_shop.php      # 店铺端菜单配置（必存在）
│ │ ├─menu_admin.php     # 后台菜单配置（必存在）
│ ├─data/                # 插件数据
│ │ ├─install.sql        # 安装SQL
│ │ ├─uninstall.sql      # 卸载SQL
│ ├─event/               # 相关事件文件（钩子）
│ │ ├─Install.php        # 安装插件执行事件（必存在）
│ │ ├─UnInstall.php      # 卸载插件执行事件（必存在）
│ │ ├─Pay.php            # 对应插件执行事件
│ ├─model/               # 插件逻辑模块
│ ├─icon.png             # 插件logo
```

## 插件开发流程

1. **设计插件名称**：确定一个合适的名称，例如支付宝支付插件名称为 `alipay`
2. **添加插件目录**：在 `addon` 文件夹下创建插件目录，如 `addon/alipay`
3. **设计插件logo**：插件logo统一命名为 `icon.png`，推荐使用 66*66 的png图像
4. **创建配置文件**：创建必需的配置文件 `diy_view.php`，`event.php`，`info.php`，`menu_shop.php`，`menu_admin.php`
5. **编写安装/卸载文件**：创建 `event/Install.php` 和 `event/UnInstall.php`
6. **实现功能**：根据插件功能，编写相应的模型、服务、控制器等

## 必需的配置文件

### diy_view.php

模板与链接配置文件，包含三项配置：
- template：模板配置
- util：自定义组件配置
- link：接设置，用于页面跳转

基本结构：
```php
return [
    'template' => [ ],
    'util' => [ ],
    'link' => [ ],
];
```

### event.php

事件配置文件，监听事件会在系统启动后自动加载，存入缓存：

```php
return [
    'bind' => [ ],
    'listen' => [
        // 事件名称 => 处理类数组
        'PayNotify' => [
            'addon\\插件名\\event\\PayNotify'
        ],
    ],
    'subscribe' => [ ],
];
```

### info.php

插件基础配置文件：

```php
return [
    'name' => '插件名',        // 插件名称与插件目录一致
    'title' => '中文名称',     // 插件中文名称
    'description' => '插件简介', // 插件简介
    'type' => 'system',       // 插件类型 system:系统插件 promotion:营销插件 tool:工具插件
    'status' => 1,
    'author' => '',
    'version' => '1.0.0',     // 插件版本
    'version_no' => '年月日时分秒', // 插件序列号，插件升级会按照序列号判断
    'content' => '',          // 插件说明
];
```

### menu_shop.php

店铺后端功能菜单配置文件，没用到则返回空数组：

```php
return [
    [
        'name' => '菜单关键字',   // 不能重复
        'title' => '菜单中文名称',
        'url' => '菜单链接',      // 解析之前
        'parent' => '菜单上级关键字', // 只对配置菜单是根菜单有效
        'is_show' => 1,         // 是否展示，默认1
        'is_control' => 1,      // 是否控制权限，默认1
        'is_icon' => 0,         // 是否使用矢量图
        'picture' => '',        // 菜单未选择图片
        'picture_select' => '', // 菜单选择后图片
        'sort' => 100,          // 排序号
        'child_list' => [       // 子菜单数组
            // 子菜单项
        ]
    ]
];
```

### menu_admin.php

管理后台菜单配置文件，结构与menu_shop.php类似：

```php
return [
    [
        'name' => '菜单关键字',   // 不能重复
        'title' => '菜单中文名称',
        'url' => '菜单链接',      // 解析之前
        'parent' => '菜单上级关键字', // 只对配置菜单是根菜单有效
        'is_show' => 1,         // 是否展示，默认1
        'picture' => '',        // 菜单未选择图片
        'picture_select' => '', // 菜单选择后图片
        'sort' => 100,          // 排序号
        'child_list' => [       // 子菜单数组
            // 子菜单项
        ]
    ]
];
```

## 安装与卸载机制

### Install.php

```php
<?php
namespace addon\插件名\event;

class Install
{
    /**
     * 执行安装
     */
    public function handle()
    {
        try {
            // 执行安装SQL
            execute_sql('addon/插件名/data/install.sql');
            return success();
        } catch (\Exception $e) {
            return error(-1, '执行安装SQL失败：' . $e->getMessage());
        }
    }
}
```

### UnInstall.php

```php
<?php
namespace addon\插件名\event;

class Uninstall
{
    /**
     * 执行卸载
     */
    public function handle()
    {
        try {
            // 执行卸载SQL
            execute_sql('addon/插件名/data/uninstall.sql');
            
            // 清理配置
            \think\facade\Db::name('config')->where([
                ['app_module', '=', 'admin'],
                ['config_key', 'like', '插件名%']
            ])->delete();
            
            return success();
        } catch (\Exception $e) {
            return error(-1, '卸载插件失败：' . $e->getMessage());
        }
    }
}
```

## 事件开发

事件的调用方式：`event('事件名称', [参数数组])`

例如：`event('Pay', ['pay_type' => 'alipay'])`

系统会查询可实现此事件的插件列表，通过实例化对应事件的类文件，建立对象，同时执行对应的handle方法。

## 常用事件

系统预定义了许多事件，可以通过这些事件为系统添加功能：

- **支付相关事件**：
  - `PayNotify`：支付异步回调
  - `PayType`：支付方式查询
  - `Pay`：执行支付
  - `PayClose`：关闭支付
  - `PayRefund`：支付退款
  
- **订单相关事件**：
  - `OrderPayNotify`：订单支付异步执行
  - `OrderCreate`：订单创建后执行
  - `OrderPay`：订单支付成功后执行
  - `OrderDelivery`：订单发货
  - `OrderTakeDelivery`：订单收货
  - `OrderComplete`：订单完成后执行
  - `OrderClose`：订单关闭后执行

- **会员相关事件**：
  - `MemberRegister`：会员注册
  - `MemberLogin`：会员登录
  - `AddMemberAccount`：添加会员账户

## 数据库操作

在插件开发中，安装和卸载SQL应该放在插件的 `data` 文件夹下：
- `install.sql`：安装时执行的SQL
- `uninstall.sql`：卸载时执行的SQL

通过 `execute_sql()` 函数来执行SQL文件：
```php
execute_sql('addon/插件名/data/install.sql');
```

## 注意事项

1. 所有PHP文件不需要包含"Niushop商城系统"的开头版权信息
2. 插件命名应该遵循规范，使用有意义的名称
3. 确保必需的配置文件都存在且正确
4. 插件的安装和卸载应该考虑清理所有相关数据
5. 事件开发中，确保正确处理参数和返回值

## 插件前端开发

前端页面应放在相应的 `view` 目录下，按照模块和控制器分类：
- 后台页面：`admin/view/控制器名/操作.html`
- 店铺页面：`shop/view/控制器名/操作.html`

## 编码规范

遵循系统的编码规范，保持代码的一致性和可维护性：
- 类名使用大驼峰命名法（首字母大写）
- 方法名使用小驼峰命名法（首字母小写）
- 变量使用小驼峰命名法



- 常量全部大写，单词间用下划线分隔 