# 海关接口插件目录结构调整记录

## 背景
根据niushop框架规范，customs_chinaport插件需要调整目录结构，确保模板文件可以被正确加载。发现当前插件模板目录结构不符合规范，导致访问页面时出现"模板文件不存在"的错误。

## 问题分析
访问URL：`https://youpin/customs_chinaport/admin/chinaport/index.html`时，系统报错：
```
模板文件不存在:D:\code\php\youpin\addon\customs_chinaport\admin\view\customs_chinaport\index.html
```

通过分析其他插件（如membersignin）目录结构，发现niushop框架按照以下规则查找模板文件：
1. 控制器: `addon/{插件名}/admin/controller/{控制器}.php`
2. 模板目录: `addon/{插件名}/admin/view/{控制器名小写}/{方法}.html`

对于customs_chinaport插件，控制器为Chinaport，但模板文件被放在了customs_chinaport目录下，导致找不到模板。

## 目录结构调整

### 1. 原始目录结构（存在问题）
```
addon/customs_chinaport/
  ├── admin/
  │   ├── controller/
  │   │   └── Chinaport.php
  │   └── view/
  │       └── customs_chinaport/  <-- 错误的目录名
  │           ├── index.html
  │           ├── log.html
  │           └── config.html
  └── ...
```

### 2. 调整后的目录结构（符合规范）
```
addon/customs_chinaport/
  ├── admin/
  │   ├── controller/
  │   │   └── Chinaport.php
  │   └── view/
  │       └── chinaport/  <-- 正确的目录名，与控制器名一致
  │           ├── index.html
  │           ├── log.html
  │           └── config.html
  └── ...
```

## 具体调整内容

1. 修改控制器中的模板路径：
```php
// 修改前
return $this->fetch('customs_chinaport/index');

// 修改后
return $this->fetch('chinaport/index');
```

2. 确认chinaport目录下已有正确的模板文件：
   - index.html - 海关接口概览页面
   - log.html - 海关接口日志页面
   - config.html - 海关接口配置页面

3. 删除多余的customs_chinaport目录：
```
Remove-Item -Recurse -Force addon\customs_chinaport\admin\view\customs_chinaport
```

4. 修正模板文件格式：
   - 删除多余的{literal}标签
   - 添加正确的基础模板继承：`{extend name="app/admin/view/base.html"/}`
   - 使用正确的区块：`{block name="resources"}`, `{block name="main"}`, `{block name="script"}`
   - 修正模板变量的引用方式：从`{{ variable }}`改为`{$variable}`

5. 测试用例调整：
   - 将测试类从`AppModuleTestCase`改回继承`BaseTestCase`
   - 确保以下测试文件正确继承BaseTestCase：
     - tests/Customs/Chinaport/ChinaportTest.php
     - tests/Customs/Chinaport/ChinavportServiceTest.php
     - tests/Customs/Chinaport/ReceiptServiceTest.php

## 关键发现
通过对比membersignin等其他插件，发现niushop框架中，模板目录结构应遵循以下命名规则：
1. 控制器类名为Chinaport，则对应的模板目录应为chinaport（全小写）
2. 模板文件名应与控制器方法名一致，如index方法对应index.html模板
3. 模板文件应遵循以下结构：
   ```html
   {extend name="app/admin/view/base.html"/}
   {block name="resources"}
   <!-- CSS和其他资源 -->
   {/block}
   {block name="main"}
   <!-- 主要内容 -->
   {/block}
   {block name="script"}
   <!-- JavaScript代码 -->
   {/block}
   ```
4. 模板变量应使用`{$variable}`的方式引用，而不是`{{ variable }}`

同时，在控制器中使用$this->fetch()方法时，应传入相对于view目录的路径，如'chinaport/index'，而不是'customs_chinaport/index'。

## 调整结果
现在可以通过标准URL正确访问插件页面：
- 概览页：`https://youpin/customs_chinaport/admin/chinaport/index.html`
- 日志页：`https://youpin/customs_chinaport/admin/chinaport/log.html`
- 配置页：`https://youpin/customs_chinaport/admin/chinaport/config.html`

通过上述调整，使插件目录结构完全符合niushop框架规范，确保了模板文件能被正确加载。 