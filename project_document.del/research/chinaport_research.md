# 海关跨境电商进口统一版信息化系统平台研究报告

项目 ID: YOUPIN-CUSTOMS-001
任务文件名: chinaport_research.md
创建于: 2025-06-26 13:11:49 +08:00
关联协议: RIPER-5 v5.0

## 1. 海关接口文档分析

### 1.1 接口概览

根据《海关跨境电商进口统一版信息化系统平台数据实时获取接口》文档，该接口主要用于：

- 通关管理系统通过通关服务系统调用企业应用系统的平台支付相关实时数据获取接口
- 企业需返回支付相关实时数据
- 采用http协议，method:post 请求方式

### 1.2 接口类型

文档中定义了两个主要接口：

1. **企业实时数据获取接口（部署在电商平台）**
   - 功能：企业接收海关发起的支付相关实时数据获取请求
   - 接口形式：HTTP

2. **企业返回实时数据接口（部署在通关服务系统）**
   - 功能：企业返回海关所需获取的支付相关实时数据
   - 接口形式：HTTPS
   - 测试环境地址：https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload
   - 线上环境地址：https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload

### 1.3 数据流转过程

1. 海关通过通关服务系统发起请求，携带orderNo、sessionID等参数
2. 企业接收请求，处理数据
3. 企业调用海关提供的接口返回数据，数据内容需要签名
4. 海关接收数据并验证

### 1.4 签名要求

签名是整个接口交互中的关键环节，要求如下：

- 将数据内容所有一级节点使用`||`分割符拼接为连续字符串
- 使用IC卡、Ukey、服务类密码设备进行加签
- 将certNo（证书编号）、signValue（加签结果）补充入请求中

签名原文示例：
```
"sessionID":"fe2374-8fnejf97-32839218"||"payExchangeInfoHead": {data}||"payExchangeInfoList": [{data},{data}]||"serviceTime":1533271903898
```

## 2. chinaport-data-signature项目分析

[chinaport-data-signature](https://github.com/julxxy/chinaport-data-signature) 是一个开源项目，用于实现中国电子口岸海关总署的数据签名功能，包括：

- CEBXxxMessage末三段进出口单报文签名
- 海关179号数据抓取报文签名

项目特点：
- 支持JSON报文直推
- 加签失败时通过邮件进行通知
- 自动重启Windows Websocket客户端（当电子口岸u-key健康状态异常时）
- 开箱即用，无需安装任何中间件或二次编译

### 2.1 签名返回示例

根据项目展示的签名返回示例：

```json
{
  "message": "操作成功",
  "success": true,
  "timestamp": "2023-07-15 12:16:36",
  "code": 200,
  "data": {
    "success": true,
    "certNo": "03000000000cde6f",
    "signatureValue": "pVWbjCXqCy2zk0RRqbw16hWZozjTSP444fdT2k5MjqigJDsLZ/Vfgout3o/Gg0WrPbJnH/2wlI8I0n3niqYiqw=="
  }
}
```

## 3. 技术实现路径分析

基于海关接口文档和chinaport-data-signature项目分析，我们可以确定以下技术实现路径：

### 3.1 模块结构

在PHP项目youpin中添加海关接口模块，应包含以下内容：

1. **接口处理模块**：处理海关发来的数据获取请求
2. **数据签名模块**：实现数据签名功能
3. **数据返回模块**：构造符合要求的数据并返回给海关

### 3.2 签名实现方案

有两种可能的实现方案：

1. **方案A - PHP调用电子口岸控件**：
   - 优点：直接使用官方控件，符合规范
   - 缺点：依赖Windows环境，需要安装额外软件

2. **方案B - 对接chinaport-data-signature**：
   - 优点：开箱即用，无需安装中间件
   - 缺点：需要实现PHP与Java服务通信

### 3.3 数据流转设计

1. 海关请求 → PHP接收处理 → 数据组装 → 调用签名服务 → 返回签名后数据

## 4. 技术选型建议

综合考虑项目现状和实现难度，推荐使用**方案B**，即开发PHP接口调用chinaport-data-signature项目提供的签名服务。具体实现步骤：

1. 搭建chinaport-data-signature服务（Java）
2. 在PHP项目中实现HTTP客户端调用签名服务
3. 实现海关数据获取接口和数据返回功能

该方案可以较好地解耦签名逻辑与业务逻辑，并利用已有的开源项目降低开发难度。 