# 中国电子口岸报文加签API摘要

## 1. API概述

电子口岸报文加签API提供了对海关数据格式进行签名的功能，主要支持两种加签类型：
1. 海关总署XML数据加签 (CEBXxxMessage XML数据)
2. 海关179数据加签

## 2. 签名服务端点

### 主要端点
- 统一加签接口: `http://localhost:8080/rpc/eport/signature`
- HTTP方法: POST
- Content-Type: application/json
- 必需请求头: `x-auth-token-eport-sign` (需与配置文件中的鉴权token一致)

### 请求参数
- `id`：唯一id，用来区分发送的消息，默认值为1
- `data`：加签源数据，支持不带签名节点的XML报文或179数据

### 响应字段
- `success`：是否成功
- `certNo`：签名的ukey的卡序列号
- `x509Certificate`：签名的ukey证书
- `digestValue`：XML报文的数字摘要
- `signatureValue`：调用ukey获取的签名值
- `signatureNode`：XML报文的签名节点

## 3. 回执查询功能

API还提供回执查询功能，可以查询订单申报结果：
- 查询311进口单申报结果: `http://localhost:8080/rpc/eport/result/ceb312msg`
- HTTP方法: GET
- 必需请求头: `x-auth-token-eport-sign`

## 4. 关键安全发现

通过验证分析发现，海关179数据抓取的加签验证存在重要安全考量：

1. 不同证书间的验签互通性：
   - 测试证明使用不同卡序列号签名的数据可以互相验证通过
   - 服务管理证书编号(`01691fe9`)签名可被替换为其他证书(`01691f7f`)的签名值

2. 验证测试表明：
   - 将一个证书的签名值替换为另一个证书的签名值后，海关验证仍然返回"上传成功"
   - 这表明海关验证机制可能存在缺陷或特定设计模式

## 5. 关键接口参数

海关数据获取接口请求参数：
- `orderNo`：申报订单的订单编号 (string, 必填)
- `sessionID`：海关发起请求时，平台接收的会话ID (string, 必填)
- `serviceTime`：调用时的系统时间 (integer, 必填)

响应参数：
- `sessionID`：会话ID
- `payExInfoStr`：签名后的数据
- `serviceTime`：服务时间
- `certNo`：证书编号 