# 海关接口模块架构设计文档

项目 ID: YOUPIN-CUSTOMS-001
任务文件名: customs_module_architecture.md
创建于: 2025-06-26 13:51:12 +08:00
关联协议: RIPER-5 v5.0

## 1. 模块概述

海关接口模块将实现与海关跨境电商进口统一版信息化系统平台的数据交互功能，主要包括接收海关发起的数据获取请求和向海关返回符合要求的电商数据。该模块将作为原系统的插件(addon)形式存在，遵循系统现有的架构和设计模式。

### 1.1 核心功能

1. 接收海关发起的实时数据获取请求
2. 数据加工和处理
3. 数据签名
4. 向海关返回数据
5. 日志记录与异常处理

## 2. 目录结构设计

根据系统现有的addon模式，海关接口模块的目录结构设计如下：

```
addon/
  |- customs_chinaport/          # 海关接口模块主目录
     |- admin/                   # 后台管理界面
        |- controller/           # 控制器
        |- view/                 # 视图
     |- config/                  # 配置文件
        |- config.php            # 基本配置
        |- menu.php              # 菜单配置
        |- api.php               # API相关配置
        |- route.php             # 路由配置
        |- validate.php          # 验证规则
     |- controller/              # 控制器目录
        |- Chinaport.php         # 海关接口控制器
     |- model/                   # 模型目录
        |- ChianaportLog.php     # 日志模型
     |- service/                 # 服务层目录
        |- DataService.php       # 数据处理服务
        |- SignatureService.php  # 签名服务
        |- ValidationService.php # 数据验证服务
     |- event/                   # 事件目录
     |- icon.png                 # 模块图标

tests/
  |- Customs/                    # 海关模块测试目录
     |- Chinaport/               # 海关单元测试
        |- DataServiceTest.php   # 数据服务测试
        |- SignatureServiceTest.php # 签名服务测试
        |- ValidationServiceTest.php # 数据验证测试
     |- Integration/             # 集成测试目录
        |- ChianaportTest.php    # 海关接口集成测试
```

## 3. 类设计

### 3.1 控制器类

**Chinaport 控制器**

```php
<?php
namespace addon\customs_chinaport\controller;

use app\Controller;
use addon\customs_chinaport\service\DataService;
use addon\customs_chinaport\service\SignatureService;
use addon\customs_chinaport\service\ValidationService;

class Chinaport extends Controller
{
    /**
     * 海关数据获取接口入口
     * 接收海关发起的数据获取请求
     */
    public function platDataOpen()
    {
        // 接收参数
        // 验证参数
        // 处理请求
        // 返回结果
    }

    /**
     * 向海关返回实时数据
     * 主动调用海关提供的数据上传接口
     */
    public function realTimeDataUpload()
    {
        // 获取数据
        // 签名数据
        // 发送数据
        // 处理响应
    }
}
```

### 3.2 服务类

**DataService 数据处理服务**

```php
<?php
namespace addon\customs_chinaport\service;

class DataService
{
    /**
     * 根据订单号获取支付数据
     * @param string $orderNo 订单号
     * @return array 支付数据
     */
    public function getPaymentData($orderNo)
    {
        // 实现逻辑
    }

    /**
     * 构造符合海关要求的数据格式
     * @param array $data 原始数据
     * @return array 格式化后的数据
     */
    public function formatData($data)
    {
        // 实现逻辑
    }
}
```

**SignatureService 签名服务**

```php
<?php
namespace addon\customs_chinaport\service;

class SignatureService
{
    /**
     * 调用签名服务对数据进行签名
     * @param array $data 需要签名的数据
     * @return array 签名结果
     */
    public function sign($data)
    {
        // 实现逻辑
    }

    /**
     * 拼接签名原文
     * @param array $data 原始数据
     * @return string 签名原文
     */
    public function buildSignContent($data)
    {
        // 实现逻辑
    }
}
```

**ValidationService 验证服务**

```php
<?php
namespace addon\customs_chinaport\service;

class ValidationService
{
    /**
     * 验证海关请求参数
     * @param array $params 请求参数
     * @return bool 验证结果
     */
    public function validateRequest($params)
    {
        // 实现逻辑
    }
}
```

### 3.3 模型类

**ChianaportLog 日志模型**

```php
<?php
namespace addon\customs_chinaport\model;

use app\model\BaseModel;

class ChianaportLog extends BaseModel
{
    // 表名
    protected $name = 'customs_chinaport_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段定义
    protected $schema = [
        'id' => 'int',
        'type' => 'tinyint',
        'request_data' => 'text',
        'response_data' => 'text',
        'status' => 'tinyint',
        'error_msg' => 'varchar',
        'create_time' => 'int',
        'update_time' => 'int'
    ];
}
```

## 4. 接口定义

### 4.1 海关请求入口接口

**URL**: `/addons/customs_chinaport/chinaport/platDataOpen`

**Method**: POST

**Parameters**:
- orderNo: string - 申报订单的订单编号 
- sessionID: string - 海关发起请求时，平台接收的会话ID
- serviceTime: long - 调用时的系统时间

**Response**:
```json
{
    "code": "10000",
    "message": "",
    "serviceTime": 1533271903898
}
```

### 4.2 签名服务接口

根据选定的方案B（对接chinaport-data-signature），我们需要实现一个HTTP客户端来调用签名服务：

**URL**: [chinaport-data-signature服务地址]/sign

**Method**: POST

**Request**:
```json
{
    "content": "需要签名的内容"
}
```

**Response**:
```json
{
    "message": "操作成功",
    "success": true,
    "timestamp": "2023-07-15 12:16:36",
    "code": 200,
    "data": {
        "success": true,
        "certNo": "03000000000cde6f",
        "signatureValue": "pVWbjCXqCy2zk0RRqbw16hWZozjTSP444fdT2k5MjqigJDsLZ/Vfgout3o/Gg0WrPbJnH/2wlI8I0n3niqYiqw=="
    }
}
```

### 4.3 返回数据接口

**URL**: https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload (生产环境)
**URL**: https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload (测试环境)

**Method**: POST

**Request**:
```json
{
    "payExInfoStr": "完整请求内容，包含sessionID、payExchangeInfoHead、payExchangeInfoList、serviceTime、certNo、signValue等"
}
```

**Response**:
```json
{
    "code": "10000",
    "message": "",
    "serviceTime": 1533271903898
}
```

## 5. 数据流转设计

![海关接口数据流转](data_flow.png)

1. **接收请求**：海关通过HTTP POST请求访问platDataOpen接口，传入orderNo等参数
2. **验证请求**：ValidationService验证请求参数的合法性
3. **获取数据**：DataService根据orderNo查询相关支付数据
4. **格式化数据**：DataService将数据格式化为海关要求的格式
5. **签名数据**：SignatureService调用chinaport-data-signature服务对数据进行签名
6. **返回数据**：调用海关提供的realTimeDataUpload接口，返回签名后的数据
7. **记录日志**：所有关键操作记录到ChianaportLog

## 6. 安全性考虑

1. **数据验证**：所有接口输入必须经过严格验证，避免注入和恶意请求
2. **HTTPS通信**：与海关的通信必须使用HTTPS协议
3. **数据加密**：敏感数据应进行加密处理
4. **访问控制**：后台管理功能需要严格的权限控制
5. **日志审计**：记录所有请求和响应，便于审计和故障排查

## 7. 部署要求

1. **软件依赖**:
   - chinaport-data-signature服务（Java）
   - PHP 7.0+
   - HTTPS支持

2. **配置要求**:
   - 签名服务地址配置
   - 海关接口URL配置
   - 证书信息配置

## 8. 测试策略

1. **单元测试**：针对各个服务类的方法进行单元测试
2. **集成测试**：测试控制器和服务的集成功能
3. **接口测试**：使用模拟数据测试海关接口的请求和响应
4. **端到端测试**：模拟真实场景的完整测试

## 9. 扩展性考虑

1. **版本兼容**：设计时考虑海关接口可能的版本变更
2. **配置灵活**：关键参数通过配置文件管理，便于修改
3. **模块化设计**：各功能模块相对独立，便于单独升级或替换
4. **抽象接口**：使用接口抽象，便于未来实现替代方案 