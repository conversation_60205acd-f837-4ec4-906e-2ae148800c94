<?php


namespace addon\membersignin\model;


use think\Model;

class SignActivityModel extends Model
{
    protected $table = 'xm_sign_activity';
    protected $pk = 'sign_activity_id';

    protected $type = [
        'start_time'  =>  'timestamp',
        'end_time'  =>  'timestamp',
        'enterprise_wx_userids'  =>  'array',
        'complete_rules' => 'array',
        'join_limit' => 'array',
    ];

    public function getStatusTextAttr($value)
    {
        return lang('sign_activity_status')[$value];
    }

    /**
     * 是否开启新人限制
     * @return bool
     */
    public function isInviteLimitNew()
    {
        if($this->use_rules && $this->invite_limit == 'new')
            return true;
        else
            return false;
    }


    /**
     * 排除标签用户
     * @return array
     */
    public function excludeTagsMembers() : array
    {
        return [];
    }
}