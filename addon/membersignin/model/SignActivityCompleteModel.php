<?php


namespace addon\membersignin\model;


use app\model\member\MemberTag;
use app\model\sign\Sign;
use app\model\sign\SignLogModel;
use Carbon\Carbon;
use think\Model;

class SignActivityCompleteModel extends Model
{
    protected $table = 'xm_sign_activity_complete';
    protected $pk = 'id';

    public function getInviteMemberIdsAttr($value)
    {
        if($value)
            return explode(",", $value);
        return [];
    }

    public function isHelpSign($inviteMemberId)
    {
        $inviteMemberIds = $this->invite_member_ids;
        //被邀请人是没有重复邀请且 被邀请和邀请不是同一个人
        if(!in_array($inviteMemberId, $inviteMemberIds) && $inviteMemberId != $this->member_id)
        {
            $signActivity = SignActivityModel::find($this->sign_activity_id);

            $nowDate = Carbon::now()->toDateString();
            //如果开启了新人限制,且邀请的人不是第一次签到
            if($signActivity->isInviteLimitNew() && SignLogModel::where("member_id", $inviteMemberId)->where("sign_date", "<>",$nowDate)->count() > 0)
            {
                return false;
            }
            else
                return true;
        }
        return false;
    }


    /**
     * 是否需要助力
     * @param $memberId
     */
    public function isNeedHelpSign($memberId)
    {
        $inviteNums = 0;
        $signActivity = SignActivityModel::find($this->sign_activity_id);
        $completeRules = $signActivity->complete_rules;

        $sign = new Sign();
        $completeNums = $sign->getMemberCompleteNums($this->sign_activity_id, $memberId);
        $useRule = [];
        if($signActivity->use_rules && $completeRules)
        {
            //按达标次数排序
            $sortKeys = array_column($completeRules, "complete_nums");

            array_multisort($sortKeys, SORT_ASC, $completeRules );

            foreach ($completeRules as $rule)
            {
                if($completeNums >= $rule['complete_nums'])
                {
                    $useRule = $rule;
                }
                else
                    break;
            }
        }

        if($useRule)
        {
            if(!empty($rule['eliminate_tags']) && MemberTag::isMemberHasOneTags($memberId, $rule['eliminate_tags']))
                return false;
            if(!empty($rule['eliminate_all_tags']) && MemberTag::isMemberHasAllTags($memberId, $rule['eliminate_tags']))
                return false;
            return true;
        }
        else
            return false;
    }

}