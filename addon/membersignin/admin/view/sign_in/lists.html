{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .activity-table{
       margin-top: 20px;
    }
    .activity-table-header{
        font-size: 16px;
        color: #797979;
        font-weight: 400;
        position: relative;
        padding-left: 16px;
        box-sizing: border-box;
    }
    .activity-table-header:before{
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 17px;
        background-color: #4685FD;
    }
</style>
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">活动名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">活动状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-filter="status">
                            <option value="">全部</option>
                            <option value="0">等待开始</option>
                            <option value="10">进行中</option>
                            <option value="100">已结束</option>
                        </select>
                    </div>
                </div>
            </div>



            <div class="ns-form-row">
                <button class="layui-btn layui-btn-primary" lay-submit lay-filter="search">搜索</button>
                <button class="layui-btn ns-bg-color" id="create_activity">创建活动</button>
            </div>
        </form>
    </div>
</div>

<div class="activity-table">
    <div class="activity-table-header">活动列表</div>
    <table id="activity_list" lay-filter="activity_list"></table>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="detail">查看</a>
        {{#  if(d.status == 10 || d.status == 0){ }}
        <a class="layui-btn" lay-event="edit">编辑</a>
        {{#  } }}
        <a class="layui-btn" lay-event="complete">达标名单</a>
        <a class="layui-btn" lay-event="copyPath">活动路径</a>
        <a class="layui-btn" lay-event="statistics">每日签到明细</a>
    </div>
</script>

{/block}
{block name="script"}
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js"></script>
<script type="application/javascript">
    function copyMiniProgramPath() {
        let content = $("#copyMiniProgramPath").attr('data-clipboard-text');
        let clipboard = new Clipboard('#copyMiniProgramPath', {
            text: function() {
                return content;
            }
        });
        clipboard.on('success', function(e) {
            alert("复制成功");
            clipboard.destroy();  //解决多次弹窗
        });

        clipboard.on('error', function(e) {
            console.log(e);
        });
    }

    function copyH5Path() {
        let content = $("#copyH5Path").attr('data-clipboard-text');
        let clipboard = new Clipboard('#copyH5Path', {
            text: function() {
                return content;
            }
        });
        clipboard.on('success', function(e) {
            alert("复制成功")
            clipboard.destroy();  //解决多次弹窗;
        });

        clipboard.on('error', function(e) {
            console.log(e);
        });
    }
    layui.use('form', function() {
        var table,
        form = layui.form;
        form.render();

        table = new Table({
            elem: '#activity_list',
            url: '/membersignin/admin/SignIn/lists',
            cols: [
                [
                    { type: 'numbers', title: '序号', width: 80 },//序号列
                    {
                    field: 'name',
                    title: '活动名称',
                    unresize: 'false',
                    width: 200
                },
                    {
                        field: 'join_people_nums',
                        title: '参与人数',
                        unresize: 'false',
                        width: 100,
                        templet: function(data) {
                            return '<div class="ns-table-btn"> <a class="layui-btn" lay-event="statistics">' + data.join_people_nums + '</a> </div>';
                        }
                    },
                    {
                        field: 'complete_nums',
                        title: '完成次数',
                        unresize: 'false',
                        width: 100,
                        templet: function(data) {
                            return '<div class="ns-table-btn"> <a class="layui-btn" lay-event="statistics">' + data.complete_nums + '</a> </div>';
                        }
                    },
                    {
                        field: 'complete_people_nums',
                        title: '完成人数',
                        unresize: 'false',
                        width: 100,
                        templet: function(data) {
                            return '<div class="ns-table-btn"> <a class="layui-btn" lay-event="statistics">' + data.complete_people_nums + '</a> </div>';
                        }
                    },
                    {
                        field: 'add_friend_nums',
                        title: '添加企微好友人数',
                        unresize: 'false',
                        width: 160,
                        templet: function(data) {
                            return '<div class="ns-table-btn"> <a class="layui-btn" lay-event="statistics">' + data.add_friend_nums + '</a> </div>';
                        }
                    },
                    {
                        field: 'start_time',
                        title: '活动开始时间',
                        unresize: 'false',
                        width: 200
                    },
                    {
                        field: 'end_time',
                        title: '活动结束时间',
                        unresize: 'false',
                        width: 200
                    },
                    {
                        field: 'status_text',
                        title: '活动状态',
                        unresize: 'false',
                        width: 120
                    }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
                    width: '22%'
                }]
            ],
        });

        // 搜索
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
            return false;
        });
        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'detail':
                    window.location.href = ns.url("membersignin://admin/signIn/edit")+'?sign_activity_id='+data.sign_activity_id+'&preview=1';
                    break;
                case 'edit':
                    window.location.href = ns.url("membersignin://admin/signIn/edit")+'?sign_activity_id='+data.sign_activity_id+'&edit=1';
                    break;
                case 'complete':
                    window.location.href =ns.url("membersignin://admin/signIn/complete")+'?sign_activity_id='+data.sign_activity_id;
                    break;
                case 'copyPath':
                    let miniProgramPath = '/otherpages/member/signin/sign_in_product_rewards?sign_activity_id='+data.sign_activity_id;
                    let h5Path = window.location.origin+'/mini-h5'+miniProgramPath

                    layer.open({
                        type: 1,
                        area: ['auto', '200px'],
                        content: "小程序路径: " + miniProgramPath  + "&nbsp;&nbsp;&nbsp;<span class='layui-btn layui-btn-primary' style='padding: 2px 2px; display: inline;' data-clipboard-text='" +
                            miniProgramPath + "' id='copyMiniProgramPath' onclick='copyMiniProgramPath();'>复制</span>" +
                            "<br /><br />h5路径: " + h5Path  + "&nbsp;&nbsp;&nbsp;<span class='layui-btn layui-btn-primary' style='padding: 2px 2px; display: inline;' data-clipboard-text='" +
                            h5Path + "' id='copyH5Path' onclick='copyH5Path();'>复制</span>",
                        cancel: function(){
                            //右上角关闭回调

                            //return false 开启该代码可禁止点击该按钮关闭
                        }
                    });
                    break;
                case 'statistics':
                    window.location.href =ns.url("membersignin://admin/signIn/statistics")+'?sign_activity_id='+data.sign_activity_id;
                    break;
            }
        })
        $('#create_activity').on('click',function (e) {
            e.preventDefault();
            window.location.href = ns.url("membersignin://admin/signIn/edit")
        })
    })
</script>
{/block}