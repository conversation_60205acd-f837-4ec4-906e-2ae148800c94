{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .layui-card-header:before{
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 17px;
        background-color: #4685FD;
    }
    .layui-form-item .layui-form-checkbox{
        float: left;
        margin-top: 8px!important;
        margin-left: 40px;
    }
    .service_head{
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
    .support_staff_list{
        width: 1000px;
    }
    .couponOne{
        position: relative;
    }
    .couponOne i{
        font-size: 20px!important;
        color: #C9C9C9;
        position: absolute;
        top: -10px;
        right: -11px;
        margin-right: 0!important;
        /*transform: translate(-50%,50%);*/
    }
    .rule-input{
        margin-left: 150px;
        margin-bottom: 5px;
    }
    .rule-input input{
        float: none!important;
        width: 30px!important;
    }
    .tags{
        width: 300px;
        height: 34px;
        position: relative;
        border: 1px solid #E6E6E6;
        display: inline-flex;
        align-items: center;
        box-sizing: border-box;
        vertical-align: middle;
    }
    .inner_tags_list{
        width: 270px;
        height: 34px;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        flex-wrap: nowrap;
        box-sizing: border-box;
    }
    .tags_more{
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        color: #E6E6E6;
        cursor: pointer;

    }
    .inner_tags_one{
        color: black;
        background-color: #E6E6E6;
        font-size: 14px;
        padding: 0px 10px;
        line-height: 28px;
        box-sizing: border-box;
        margin-left: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }
    .inner_tags_one_close{
        font-size: 16px;
        color: black;
        cursor: pointer;
        margin-left: 5px;
    }
    /*使下拉列表框不被遮挡*/
    .layui-table-cell {
        overflow: visible !important;
    }
    /*使列表框与表格单元格重合*/
    td .layui-form-select {
        margin-top: -10px;
        margin-left: -15px;
        margin-right: -15px;
    }
    .layui-table-view .layui-input{
        width: 100%;
    }
    .layui-form-checkbox[lay-skin=primary] span{
        color: #000;
    }
</style>
<style>
    .inner-user-tag-box {
        display: flex;
        flex-direction: column;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 600px;
        height: 580px;
        background-color: #fff;
        box-shadow: 0 2px 5px #999;
        z-index: 111;
    }

    .inner-user-tag-box .title {
        height: 76px;
        font-size: 20px;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .inner-user-tag-box .inner-close {
        position: absolute;
        right: 30px;
        top: 10px;
        cursor: pointer;
        padding: 10px;
    }

    .inner-user-tag-box .inner-close i {
        font-size: 20px;
    }

    .inner-user-tag-box .content {
        flex: 1;
        overflow: auto;
    }

    .inner-user-tag-box .content .layui-form-label {
        width: 110px;
    }

    .inner-user-tag-box .content .layui-input-block {
        margin-left: 120px;
    }

    .inner-user-tag-box .content .tag-content,
    .no-any-box {
        padding: 0 42px;
    }

    .inner-user-tag-box .content .tag-content .search {
        position: relative;
    }

    .inner-user-tag-box .content .tag-content .search-icon {
        position: absolute;
        right: 10px;
        top: 5px;
        cursor: pointer;
    }

    .inner-user-tag-box .content .tag-content .tag-box,
    .tag-box-search {
        margin-top: 10px;
    }

    .inner-user-tag-box .content .tag-content .tag-info {
        display: flex;
        flex-wrap: wrap;
    }

    .inner-user-tag-box .content .tag-content .tag-info .inner-tag-list {
        padding: 5px 10px;
        border: 1px solid #E6E6E6;
        border-radius: 5px;
        margin: 5px 5px 0 0;
        background: #f7f7f7;
        cursor: pointer;
    }

    .inner-user-tag-box .content .tag-box-search .tag-info {
        display: flex;
        flex-wrap: wrap;
    }

    .inner-user-tag-box .content .tag-box-search .tag-list-search {
        padding: 5px 10px;
        border: 1px solid #E6E6E6;
        border-radius: 5px;
        margin: 5px 5px 0 0;
        background: #f7f7f7;
        cursor: pointer;
    }

    .inner-user-tag-box .content .tag-box-search .active {
        border-color: #12b7f5;
        background-color: #e7f7ff;
        color: #12b7f5;
    }

    .inner-user-tag-box .content .tag-content .tag-info .active {
        border-color: #12b7f5;
        background-color: #e7f7ff;
        color: #12b7f5;
    }

    .inner-user-tag-box .tag-btn-box {
        text-align: right;
        padding: 0 42px 20px;
    }

    .tag-list-info {
        height: 50px;
        overflow: hidden;
    }

    .tag-list-info.show {
        overflow: initial;
        height: initial;
    }

    .expanding {
        cursor: pointer;
    }

    .height-40 {
        height: 40px;
    }
    .primary_color{
        color: #4685FD;
        cursor: pointer;
        font-size: 14px;
    }
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="dataForm">
    <div class="layui-card">
        <div class="layui-card-header">签到规则</div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label mid">活动名称：</label>
                <div class="layui-input-block">
                    <input type="text" name="name" lay-verify="required" value="" autocomplete="off" class="layui-input layui-input-inline ns-len-mid">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid">签到天数：</label>
                <div class="layui-input-block">
                    <input type="number" name="sign_days" value="" autocomplete="off" class="layui-input layui-input-inline ns-len-short">
                    <span style="color:#B2B2B2;font-size:12px; line-height:1.6;">天 连续签到天数达标后即可联系企微客领取礼品，最低1天</span>
                </div>
            </div>
            <div class="layui-form-item">
                <input type="checkbox" name="is_open_new" lay-skin="primary" class="new_people_checked">
                <label class="layui-form-label sm">新人签到：</label>
                <div class="layui-input-block">
                    <input type="number" name="new_sign_days" value="" autocomplete="off" class="layui-input layui-input-inline ns-len-short">
                    <span style="color:#B2B2B2;font-size:12px; line-height:1.6;">天 勾选后，未下过单且未完成过任一签到活动的用户，首次可以以新人签到天数完成任务</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid">活动时间：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" lay-verify="startTime" id="start_time" class="layui-input ns-len-mid" autocomplete="off" placeholder="开始时间">
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" lay-verify="overTime" id="over_time" class="layui-input ns-len-mid" autocomplete="off" placeholder="结束时间">
                    </div>
                </div>
                <div class="ns-word-aux mid" style="color: red">
                    <p>活动到期后，系统会强制结束活动,所有用户(含连续签到中的)均无法再签到，如需保留在签用户，请在下方点击【提前结束】</p>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid img-upload-lable">活动主图：</label>
                <div class="layui-input-block img-upload">
                    <input type="hidden" class="layui-input" name="default_img" lay-verify="image"/>
                    <div class="upload-img-block">
                        <div class="upload-img-box" id="activeMasterChart">
                            <div class="ns-upload-default">
                                <img src="SHOP_IMG/upload_img.png" />
                                <p>上传图片</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ns-word-aux mid">
                    <p>图片要求不大于512K</p>
                    <p>尺寸要求：宽750px*高auto</p>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid">广告时长：</label>
                <div class="layui-input-block">
                    <input type="number" name="look_time" value="" lay-verify="required" autocomplete="off" class="layui-input layui-input-inline ns-len-short">
                    <span style="font-size:12px; line-height:1.6;">秒</span>
                </div>
                <div class="ns-word-aux mid">
                    <p>用户每次点击签到时，页面会弹出宣传内容，用户必需完成浏览时长才能完成签到，中途退出不算签到成功</p>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid img-upload-lable">广告内容：</label>
                <div class="layui-input-block img-upload">
                    <input type="hidden" class="layui-input" name="advertise" lay-verify="ad_content"/>
                    <div class="upload-img-block">
                        <div class="upload-img-box" id="advertisingContent">
                            <div class="ns-upload-default">
                                <img src="SHOP_IMG/upload_img.png" />
                                <p>上传图片或视频</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ns-word-aux mid">
                    <p>图片要求不大于1M，尺寸要求：宽828px*高1792px</p>
                    <p>视频要求不大于5M，尺寸建议按9*16生成</p>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid">广告链接：</label>
                <div class="layui-input-block">
                    <input type="text" name="advertise_link" value="" autocomplete="off" class="layui-input layui-input-inline ns-len-mid" placeholder="请填写广告跳转路径">
                    <span style="color:#B2B2B2;font-size:12px; line-height:1.6;">选填</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid">活动规则：</label>
                <div class="layui-input-block">
                    <textarea type="text" name="rule" value="7" autocomplete="off" class="layui-textarea ns-len-long"></textarea>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">签到用户限制</div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="">
                        <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="not_limit" title="不限制" checked>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="tag" title='仅指定标签的用户可参与活动'>
                    </div>
                    {include file="app/admin/view/member/member_tag_component.html"}
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="realname" title='用户必需先完成实名校验'>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="gender" title='指定用户性别为'>
                    </div>
                    <div class="layui-input-inline">
                        <select name="gender">
                            <option value="1">男</option>
                            <option value="2">女</option>
                        </select>
                    </div>
                    <span class="layui-form-mid ns-text-color-dark-gray">选择该项时，用户必需先完成实名验证</span>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="age" title='指定用户年龄段为'>
                    </div>
                    <div class="layui-input-inline">
                        <input type="number" class="layui-input ns-len-short" name="age_start" value="18">
                    </div>
                    <span class="layui-form-mid ns-text-color-dark-gray">至</span>
                    <div class="layui-input-inline">
                        <input type="number" class="layui-input ns-len-short" name="age_end" value="25">
                    </div>
                    <span class="layui-form-mid ns-text-color-dark-gray">之间（含）选择该项时，用户必需先完成实名验证</span>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">多轮次签到规则配置</div>
        <div class="layui-card-body">
            <h3 style="color: red;">注意：管理员可以配置不同完成轮次奖励的优惠券和对应需邀请的好友人数，并设置其免邀请的特权用户范围。如果不同轮次奖励的券一样，可以点击批量配置，快速完成配置；签到天数达标后，需邀请足够的好友来签到，系统才会发放【核销码】，如配置的邀请人数为0，或用户在指定免邀请范围内的，则不需要邀请，签到完成后直接发放【核销码】。</h3>
<!--            <div class="layui-form-item">-->
<!--                <label class="layui-form-label mid">是否启用：</label>-->
<!--                <div class="layui-input-block">-->
<!--                    <input type="checkbox" name="use_rules" lay-skin="switch" lay-filter="switchRule" autocomplete="off" value="0" class="layui-input layui-input-inline ns-len-mid">-->
<!--                    <span id="rule_switch_text" style="vertical-align: sub;">已关闭</span>-->
<!--                </div>-->
<!--            </div>-->

<!--            <div class="layui-form-item">-->
<!--                <label class="layui-form-label mid">邀请用户限制：</label>-->
<!--                <div class="layui-input-block">-->
<!--                    <input type="radio" name="invite_limit" value="new" title="仅邀请新用户有效" />-->
<!--                    <input type="radio" name="invite_limit" value="none" title="不限制" checked>-->
<!--                </div>-->
<!--                &lt;!&ndash;<label class="layui-form-label"></label>-->
<!--                <div class="layui-form-mid layui-word-aux" style="color: red !important;">拼团不受此设置控制，默认使用汇付天下</div>&ndash;&gt;-->
<!--            </div>-->

<!--            <div class="layui-form-item" id="rule-list">-->
<!--                <label class="layui-form-label mid">规则配置：</label>-->
<!--            </div>-->
            <!-- 列表 -->
            <div id="multiRoundRuleFather">
                <table id="multiRoundRule" lay-filter="multi-round-rule"></table>
                <div>
                    <button type="button" class="layui-btn layui-bg-blue" id="addRule">增加轮次配置</button>
                    <a href="/goodscoupon/admin/goodscoupon/lists.html" target="_blank" style="cursor: pointer;color: #4685FD;">还没有优惠券，去配置</a>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">分享内容配置</div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label mid">分享标题：</label>
                <div class="layui-input-block">
                    <input type="text" name="share_title" value="快来和我一起签到领好礼吧" autocomplete="off" class="layui-input layui-input-inline ns-len-mid">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mid img-upload-lable">分享图片：</label>
                <div class="layui-input-block img-upload">
                    <input type="hidden" class="layui-input" name="share_img" lay-verify="image"/>
                    <div class="upload-img-block">
                        <div class="upload-img-box" id="activeShareImage">
                            <div class="ns-upload-default">
                                <img src="SHOP_IMG/upload_img.png" />
                                <p>上传图片</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ns-word-aux mid">
                    <p>尺寸要求：宽500px*高400px</p>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">客服人员</div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <p class="">请添加客服人员企业微信二维码，用户达标后可添加客服人员为好友后，线下联系领取奖品事宜。 如添加多个客服人员，系统会按顺序轮流展示给用户添加</p>
                    <!--                    <button class="layui-btn ns-bg-color" id="addCustomerService">添加客服</button>-->
                </div>
            </div>
            <div class="support_staff_list">
                <!-- 列表 -->
                <table id="service_list" lay-filter="service_list"></table>
            </div>
        </div>
    </div>
<!--    <div class="layui-card">-->
<!--        <div class="layui-card-header">奖励设置</div>-->
<!--        <div class="layui-card-body">-->
<!--            <div class="layui-form-item">-->
<!--                <div class="layui-input-inline">-->
<!--                    <div style="display: inline-flex;align-items: center">-->
<!--                        <input type="radio" name="coupon_select" value="1" lay-filter="filter" checked="checked" title="奖励商品优惠券">-->
<!--                        <div id="couponHasChoose" style="display: flex;align-items: center;">-->
<!--                            <span style="font-size:12px; line-height:1.6;cursor: pointer;color: #4685FD; vertical-align:sub;" id="chooseCoupon">点击选择优惠券</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <p style="color:#B2B2B2;font-size:12px; line-height:1.6;"><a href="/goodscoupon/admin/goodscoupon/lists.html" target="_blank" style="cursor: pointer;color: #4685FD;">还没有优惠券，去配置</a>为方便管理，请配置内部优惠券，可多次领取，否则会导到发放失败</p>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
    <div class="ns-form-row" id="activity_op">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="sign_close()">提前结束</button>
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>
</div>

<!--绑定组别-->
<script type="text/html" id="bindGroup">
<!--    <select lay-filter="bing_group" data-index="{{d.LAY_INDEX}}">-->
<!--        <option value="" {{# if(d.xm_group_id==''){ }} selected {{#}}}></option>-->
<!--        {{# layui.each(d.xm_group_list,function(index, item){     }}-->
<!--        <option value="{{ item.xm_group_id }}" {{# if(d.xm_group_id==item.xm_group_id){ }} selected {{#}}}>{{ item.xm_group_name }}</option>-->
<!--        {{# });  }}-->
<!--    </select>-->
    <div id="bind_group_{{d.userid}}"></div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
<!--        <a class="layui-btn" lay-event="edit">编辑</a>-->
        <a class="layui-btn" lay-event="remove">删除</a>
    </div>
</script>

<!-- 用户标签 -->
<div class="inner-user-tag-box" style="display: none;">
    <div class="title">选择标签</div>
    <div class="inner-close">
        <i class="layui-icon layui-icon-close"></i>
    </div>
    <div class="content layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">筛选条件</label>
            <div class="layui-input-block">
                <input class="onStatusInner" lay-filter="inner_tag_status" type="radio" name="inner_tag_status" value="0" title="以下标签满足其一" checked>
                <input class="sameStatusInner" lay-filter="inner_tag_status" type="radio" name="inner_tag_status" value="1" title="以下标签同时满足">
                <!-- <input lay-filter="tag_status" type="radio" name="tag_status" value="2" title="无任何标签"> -->
            </div>
        </div>
        <div class="tag-content">
<!--            <div class="search">-->
<!--                <i class="layui-icon layui-icon-search search-icon"></i>-->
<!--                <input type="text" name="search_tag" placeholder="请输入要查找的标签" autocomplete="off" class="layui-input">-->
<!--            </div>-->
            <div class="tag-box">
                {foreach $tags_data as $i => $tags_group}
                <div class="tag-title">{$tags_group['group_name']}：</div>
                <div class="tag-info">
                    {foreach $tags_group['tags'] as $j => $tags}
                    <div class="inner-tag-list" data-tag-id="{$tags['tag_id']}">{$tags['tag_name']}</div>
                    {/foreach}
                </div>
                {/foreach}
            </div>
            <div class="tag-box-search" style="display: none;">

            </div>
        </div>
        <div class="no-any-box" style="display: none;">选择无任何标签后，将筛选出没有被打上过任何标签的客户~</div>
    </div>
    <div class="tag-btn-box">
        <button type="reset" class="layui-btn layui-btn-primary inner-cancel-tag">取消</button>
        <button class="layui-btn ns-bg-color inner-save-tag">保存</button>
    </div>
</div>

{/block}
{block name="script"}
<script type="application/javascript">
    var default_data_list = [{complete_nums:1,invite_nums:0,eliminate_tags:[],eliminate_all_tags:[],goodscoupon_type_id:'',goodscoupon_name:''}];
    var bonusProductCoupons = {}; //奖励商品优惠券的信息  传值给子弹框的数据
    var advertise_type=''
    var serviceList = []
    var cs_info = [];
    var enterprise_wx_userids = []
    var rule_keys = []
    var xm_group_list = JSON.parse('{$xm_group_list|raw}');
    var xm_select_comp_dict = {}; //xmSelect组件对象的存储
    var use_rules = 1;
    var invite_limit = 'none';  //new
    //获取url中的参数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = window.location.search.substr(1).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return null; //返回参数值
    }
    var sign_activity_id = getUrlParam('sign_activity_id') || null;
    var only_preview = getUrlParam('preview') || null;
    var is_edit = getUrlParam('edit') || null;
    if(only_preview){
        $('#activity_op').css('display','none');
    }
    if(is_edit){
        $('input[name=sign_days]').attr('disabled','disabled');
        $('input[name=sign_days]').css('background','#eee');
        $('input[name=is_open_new]').attr('disabled','disabled');
        $('input[name=is_open_new]').css('background','#eee');
        $('input[name=new_sign_days]').attr('disabled','disabled');
        $('input[name=new_sign_days]').css('background','#eee');
    }

    layui.extend({"xmSelect":"__STATIC__/ext/layui/lay/modules/xm-select/xm-select"}).use(['form', 'laytpl','laydate', 'element','table','xmSelect'], function() {
        var laydate = layui.laydate;
        var upload = layui.upload,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        var xmSelect = layui.xmSelect;
        var $ = layui.$;
        var table = layui.table;
        form.render();

        // 时间模块
        laydate.render({
            elem: '#over_time', //指定元素
            type: 'datetime',
        });

        // 时间模块
        laydate.render({
            elem: '#start_time', //指定元素
            type: 'datetime',
        });

        function getData(callback){
            var loadIndex = layer.msg('加载中', {
                icon: 16,
                shade: 0.01
            });
            $.ajax({
                url: '/membersignin/admin/SignIn/detail',
                data: {sign_activity_id:sign_activity_id},
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.close(loadIndex);
                    if (res.code == 0) {
                        advertise_type = res.data.advertise_type
                        enterprise_wx_userids = res.data.enterprise_wx_userids
                        cs_info = res.data.cs_info;
                        form.val('dataForm',{
                            name:res.data.name,
                            sign_days:res.data.sign_days,
                            is_open_new:res.data.is_open_new,
                            new_sign_days:res.data.new_sign_days,
                            start_time:res.data.start_time,
                            end_time:res.data.end_time,
                            default_img:res.data.default_img,
                            look_time:res.data.look_time,
                            advertise:res.data.advertise,
                            advertise_link:res.data.advertise_link,
                            // rule:res.data.rule,
                            share_title: res.data.share_title,
                            share_img: res.data.share_img,
                            // use_rules: res.data.use_rules,
                            // invite_limit: res.data.invite_limit
                        })
                        invite_limit = res.data.invite_limit

                        $("#activeMasterChart").html("<img src=" + ns.img(res.data.default_img) + " >");
                        res.data.share_img && $("#activeShareImage").html("<img src=" + ns.img(res.data.share_img) + " >");
                        if(res.data.advertise_type=='image'){
                            $("#advertisingContent").html("<img src=" + ns.img(res.data.advertise) + " >");
                        }else if(res.data.advertise_type=='video'){
                            $("#advertisingContent").html("" +
                                "<video loop autoplay>"+
                                "<source src='"+ns.img(res.data.advertise)+"' type='video/mp4'>"+
                                "</video>"
                            );
                        }
                        // if(res.data.use_rules){
                        //     $('input[name=use_rules]').val(1)
                        //     $('#rule_switch_text').text('已开启')
                        // }else{
                        //     $('input[name=use_rules]').val(0)
                        //     $('#rule_switch_text').text('已关闭')
                        // }
                        // res.data.complete_rules.map(item=>{
                        //     addRule(item)
                        //     return item
                        // })
                        default_data_list = res.data.complete_rules && res.data.complete_rules.length ? res.data.complete_rules : default_data_list;
                        if(Object.keys(res.data.goodscoupon_type || {}).length){
                            var tmp = default_data_list[0]
                            tmp.goodscoupon_type_id = res.data.goodscoupon_type.goodscoupon_type_id
                            tmp.goodscoupon_name = res.data.goodscoupon_type.goodscoupon_name
                        }
                        renderMultiRoundRule();
                        renderActivityForm(res.data);
                    }
                    if(callback && typeof callback == 'function'){
                        callback()
                    }
                }
            });
        }
        function getServiceList(){
            var loadIndex = layer.msg('加载中', {
                icon: 16,
                shade: 0.01
            });
            $.ajax({
                url: '/membersignin/admin/SignIn/enterpriseWxCs',
                dataType: 'JSON',
                type: 'GET',
                success: function(res) {
                    if(res.code==0){
                        serviceList = res.data.map(function (item) {
                            if(enterprise_wx_userids.includes(item.userid)||enterprise_wx_userids.includes(item.userid.toLowerCase())){
                                item.checked=true
                            }else{
                                item.checked=false
                            }
                            var xm_group_ids = [];
                            if(cs_info && cs_info.length>0){
                                var tmp_list = cs_info.filter(one=>one.userid==item.userid);
                                if(tmp_list.length){
                                    xm_group_ids = tmp_list[0].xm_group_id || [];
                                }
                            }
                            item.xm_group_list = JSON.parse(JSON.stringify(xm_group_list)).map(function (one) {
                                if(xm_group_ids.includes(one.xm_group_id)){
                                    one.selected = true
                                }else{
                                    one.selected = false
                                }
                                return one;
                            });
                            return item
                        })
                        table =  $.extend(table, {config: {checkName: 'checked'}});
                        table.render({
                            elem: '#service_list',
                            id:'queryList',
                            cols: [
                                [
                                    {type: 'checkbox'},
                                    {
                                        field: 'name',
                                        title: '客服昵称',
                                        align: 'left',
                                        unresize: 'false',
                                        width: 200
                                    },
                                    {
                                        title: '头像',
                                        align: 'left',
                                        unresize: 'false',
                                        width: 120,
                                        templet:function (data){
                                            return '<img src="'+data.thumb_avatar+'" class="service_head"/>'
                                        }
                                    },
                                    {
                                        title: '绑定组别',
                                        align: 'left',
                                        unresize: 'false',
                                        width: 600,
                                        templet:'#bindGroup'
                                    }
                                    // {
                                    //     title: '操作',
                                    //     align: 'left',
                                    //     toolbar: '#operation',
                                    //     unresize: 'false',
                                    //     width: 120
                                    // }
                                ]
                            ],
                            data:serviceList,
                            limit:200,
                            done: function(res, curr, count, origin) {
                                var data = res.data;
                                form.render();
                                for (let i = 0; i < data.length; i++) {
                                    var id = 'bind_group_'+data[i].userid;
                                    //渲染多选
                                    xm_select_comp_dict[data[i].userid] = xmSelect.render({
                                        el: '#'+id,
                                        theme: {
                                            color: '#4685FD',
                                        },
                                        prop: {
                                            name: 'xm_group_name',
                                            value: 'xm_group_id',
                                        },
                                        data: data[i].xm_group_list
                                    })
                                }
                            },
                        });
                        // 工具栏事件
                        table.on('toolbar(service_list)', function(obj){

                        })
                        form.on('select(bing_group)', function (data){
                            var index = data.elem.dataset.index-1;
                            serviceList = serviceList.map(function(item,l){
                                if(index==l){
                                    item.xm_group_id = data.value;
                                }
                                return item;
                            })
                            table.reload({
                                data:serviceList
                            });
                        })
                        layer.close(loadIndex);
                    }else{
                        layer.msg(res.message,{icon: 2})
                    }
                }
            })
        }
        if(sign_activity_id){
            getData(function () {
                getServiceList()
            });
        }else{
            renderMultiRoundRule();
            getServiceList()
        }


        // 活动主图上传
        var uploadActiveMasterChart = upload.render({
            elem: '#activeMasterChart',
            accept:'images',
            acceptMime: 'image/*',
            size:512,
            auto: false,  //auto 参数必须设置为false 手动触发上传
            url: ns.url("admin/upload/upload"),
            choose:function (obj) {
                var flag = true;
                obj.preview(function(index, file, result){
                    // console.log(file);            //file表示文件信息，result表示文件src地址
                    var img = new Image();
                    img.src = result;
                    img.onload = function () { //初始化夹在完成后获取上传图片宽高，判断限制上传图片的大小。
                        if(img.width ==750){
                            obj.upload(index, file); //满足条件调用上传方法
                        }else{
                            flag = false;
                            layer.msg("上传的图片尺寸是：宽750*高auto！",{icon: 2});
                            return false;
                        }
                    }
                    return flag;
                });
            },
            done: function(res) {
                if (res.code >= 0) {
                    $("input[name='default_img']").val(res.data.pic_path);
                    $("#activeMasterChart").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });

        // 上传广告图片或者视频
        var uploadAdvertisingContentAcceptMime =''
        var uploadAdvertisingContent = upload.render({
            elem: '#advertisingContent',
            url: ns.url("admin/upload/upload"),
            auto: false,  //auto 参数必须设置为false 手动触发上传
            accept:'file',
            acceptMime:'image/*,video/mp4',
            choose:function (obj) {
                var flag = true;
                obj.preview(function(index, file, result){
                    if(file.type.indexOf('image/')!=-1){
                        uploadAdvertisingContentAcceptMime = 'image'
                        if(file.size<=1*1024*1024){
                            var img = new Image();
                            img.src = result;
                            img.onload = function () { //初始化夹在完成后获取上传图片宽高，判断限制上传图片的大小。
                                if(img.width ==828 && img.height ==1792){
                                    obj.upload(index, file); //满足条件调用上传方法
                                }else{
                                    flag = false;
                                    layer.msg("上传的图片尺寸是：宽828*高1792！",{icon: 2});
                                    return false;
                                }
                            }
                        }else{
                            layer.msg("上传的图片大小不得大于1M！",{icon: 2});
                        }
                    }else if(file.type.indexOf('video/')!=-1){
                        uploadAdvertisingContentAcceptMime = 'video'
                        if(file.size<=5*1024*1024){
                            obj.upload(index, file); //满足条件调用上传方法
                        }else{
                            layer.msg("上传的视频大小不得大于5M！",{icon: 2});
                        }
                    }else{
                        uploadAdvertisingContentAcceptMime= ''
                        layer.msg("上传的文件只能是图片或者视频",{icon: 2});
                    }
                    // console.log(file);            //file表示文件信息，result表示文件src地址
                    return flag;
                });
            },
            before:function (res){
                if(uploadAdvertisingContentAcceptMime=='video'){
                    this.url = ns.url("admin/upload/video")
                }else{
                    this.url = ns.url("admin/upload/upload")
                }
            },
            done: function(res) {
                if (res.code >= 0) {
                    if(uploadAdvertisingContentAcceptMime=='image'){
                        advertise_type='image'
                        $("input[name='advertise']").val(res.data.pic_path);
                        $("#advertisingContent").html("<img src=" + ns.img(res.data.pic_path) + " >");
                    }else{
                        advertise_type='video'
                        $("input[name='advertise']").val(res.data.path);
                        $("#advertisingContent").html("" +
                            "<video loop autoplay>"+
                            "<source src='"+ns.img(res.data.path)+"' type='video/mp4'>"+
                            "</video>"
                        );
                    }
                }
                return layer.msg(res.message);
            }
        });

        // 活动主图上传
        var uploadActiveShareImage = upload.render({
            elem: '#activeShareImage',
            accept:'images',
            acceptMime: 'image/*',
            auto: false,  //auto 参数必须设置为false 手动触发上传
            url: ns.url("admin/upload/upload"),
            choose:function (obj) {
                var flag = true;
                obj.preview(function(index, file, result){
                    // console.log(file);            //file表示文件信息，result表示文件src地址
                    var img = new Image();
                    img.src = result;
                    img.onload = function () { //初始化夹在完成后获取上传图片宽高，判断限制上传图片的大小。
                        if(img.width ==500 && img.height ==400){
                            obj.upload(index, file); //满足条件调用上传方法
                        }else{
                            flag = false;
                            layer.msg("上传的图片尺寸是：宽500*高400！",{icon: 2});
                            return false;
                        }
                    }
                    return flag;
                });
            },
            done: function(res) {
                if (res.code >= 0) {
                    $("input[name='share_img']").val(res.data.pic_path);
                    $("#activeShareImage").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });

        form.on('switch(switchRule)', function(data){
            if(this.checked){
                $('#rule_switch_text').text('已开启')
                $(this).val(1)
            }else{
                $('#rule_switch_text').text('已关闭')
                $(this).val(0)
            }
        });
        // default_data 默认值 如{complete_nums: "1", invite_nums: "2"}
        function addRule() {
            var rounds = 1;
            if(default_data_list.length > 0){
                rounds = parseInt(default_data_list[default_data_list.length -1].complete_nums) + 1;
            }
            default_data_list.push({complete_nums:rounds,invite_nums:0,eliminate_tags:[],eliminate_all_tags:[],goodscoupon_type_id:'',goodscoupon_name:''})
            reloadMultiRoundRule()
        }

        $('#addRule').on('click',function () {
            addRule()
        })

        $('#addCustomerService').on('click',function (e) {
            customerServiceAdd({})
        })
        /**
         * 客服人添加
         */
        function customerServiceAdd(data) {
            laytpl($("#service_add").html()).render(data, function(html) {
                layer_service = layer.open({
                    title: '客服人员添加',
                    skin: 'layer-tips-class',
                    type: 1,
                    area: ['500px'],
                    content: html,
                });
            });
        }
        $('#multiRoundRuleFather').on('click', '.chooseCoupon', function (e){
            var uindex = e.target.dataset.index
            layer.open({
                type: 2,
                title: '选择优惠券',
                area: ['800px', '600px'],
                content: ns.url("membersignin://admin/signIn/selectGoodsCoupon"),
                btn: ['确认', '取消'],
                yes: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];//【核心】
                    bonusProductCoupons = iframeWin.dataToParent();    //调用子页面的方法
                    if(Object.keys(bonusProductCoupons).length){
                        if(uindex == 'all'){
                            for (let i = 0; i < default_data_list.length; i++) {
                                default_data_list[i].goodscoupon_name = bonusProductCoupons.goodscoupon_name;
                                default_data_list[i].goodscoupon_type_id = bonusProductCoupons.goodscoupon_type_id;
                            }
                        }else{
                            var tmp = default_data_list[uindex]
                            tmp.goodscoupon_name = bonusProductCoupons.goodscoupon_name;
                            tmp.goodscoupon_type_id = bonusProductCoupons.goodscoupon_type_id;
                        }

                    }
                    table.reload('multiRoundRule',{
                        data:default_data_list
                    });
                    bonusProductCoupons = {}
                    layer.close(index);			//手动关闭弹出层
                },
            });
        })
        $('#multiRoundRuleFather').on('click', '.couponOneRemove', function (e){
            var uindex = e.target.dataset.index
            var tmp = default_data_list[uindex]
            tmp.goodscoupon_type_id = '';
            tmp.goodscoupon_name = '';
            reloadMultiRoundRule()
        })
        $('#multiRoundRuleFather').on('input','.complete_nums',function (e){
            var uindex = e.target.dataset.index
            var tmp = default_data_list[uindex]
            tmp.complete_nums = e.target.value
            reloadMultiRoundRule()
        })
        $('#multiRoundRuleFather').on('input', '.invite_nums',function (e) {
            var uindex = e.target.dataset.index
            var tmp = default_data_list[uindex]
            tmp.invite_nums = e.target.value
            reloadMultiRoundRule()
        })

        /**
         * 表单监听提交
         */
        form.on('submit(save)', function(data) {
            var field = data.field;
            var check_reslut = submitDataCheck(field);
            if(!check_reslut){
                return;
            }
            if(field.is_open_new){
                field.is_open_new = 1
            }
            if(is_edit){
                field.sign_activity_id = sign_activity_id
            }
            if(default_data_list.filter(function (item) {
                return item.goodscoupon_type_id
            }).length < default_data_list.length){
                layer.msg('请配置轮次的优惠券');
                return;
            }
            field.goodscoupon_type_id = field.goodscoupon_type_id || 0;
            field.invite_limit = field.table_invite_limit == 'on' ? 'new' : 'none'
            field.use_rules = use_rules;
            var complete_rules_fields = ['complete_nums','invite_nums','eliminate_tags','eliminate_all_tags','goodscoupon_type_id','goodscoupon_name']
            field.complete_rules = default_data_list.map(function (item,u) {
                var tmp_tag_id = 'complete_tags_'+u
                if(parseInt(tag_status_ditc[tmp_tag_id])){
                    item.eliminate_all_tags = tags_dict[tmp_tag_id]
                }else{
                    item.eliminate_tags = tags_dict[tmp_tag_id]
                }
                var key_list = Object.keys(item);
                key_list.map(function (key) {
                    if(!complete_rules_fields.includes(key)){
                        delete item[key]
                    }
                    return key;
                })
                return item
            })
            field.advertise_type = advertise_type
            var selectData = layui.table.checkStatus('queryList').data;
            var enterprise_user_group = {}
            enterprise_wx_userids = selectData.map(function (item) {
                enterprise_user_group[item.userid] = xm_select_comp_dict[item.userid].getValue().map(function (one) {
                    return one.xm_group_id;
                });
                return item.userid
            })
            field.enterprise_wx_userids = enterprise_wx_userids
            field.enterprise_user_group = enterprise_user_group;
            if(enterprise_wx_userids.length<1){
                layer.msg('最少需要勾选一个客服人员',{icon: 2})
                return
            }
            field = submitDataConstruct(field);
            layer.confirm('是否确认保存？', {
                title:'操作提示',
                closeBtn: false,
                btn: ['确认', '取消'],
                yes:function () {
                    var loadIndex = layer.msg('加载中', {
                        icon: 16,
                        shade: 0.01
                    });
                    $.ajax({
                        url: is_edit ? '/membersignin/admin/SignIn/edit' : '/membersignin/admin/SignIn/add',
                        data:field,
                        dataType: 'JSON',
                        type: 'POST',
                        success: function(res) {
                            layer.close(loadIndex);
                            if(res.code==0){
                                layer.msg(res.message,{icon: 1},function () {
                                    back()
                                });
                            }else{
                                layer.msg(res.message,{icon: 2})
                            }
                        }
                    })
                }
            })
        })


        // ######################################参与限制的逻辑代码块 start######################

        // 选择不限制，清除其他选项值
        function resetJoinLimit(){
            $('#tag_ids').val('');
            $('#tag_status').val('0');
            $('.select-tag-box').html('<div class="placeholder select-tag" style="width: 100%;">请选择标签</div>');
            $('select[name=sex]').val(1);
            $('input[name=age_start]').val(18);
            $('input[name=age_end]').val(25);
        }

        form.on('checkbox(join_limit)',function (data) {
            var elem = data.elem; // 获得 checkbox 原始 DOM 对象
            var checked = elem.checked; // 获得 checkbox 选中状态
            var value = elem.value; // 获得 checkbox 值
            if(value=='not_limit' && checked){
                $("input[name=join_limit]").prop('checked',false);
                $("input[name=join_limit][value=not_limit]").prop('checked',true);
                resetJoinLimit()
                form.render();
            }
            if(value!='not_limit' && checked){
                $("input[name=join_limit][value=not_limit]").prop('checked',false);
                if(value == 'gender' || value=='age'){
                    $("input[name=join_limit][value=realname]").prop('checked',true);
                }
                form.render();
            }
        })
        function isInteger(obj) {
            return obj%1 === 0
        }

        function submitDataCheck(params){
            if(!isInteger(params.age_start) || !isInteger(params.age_end)) {
                layer.msg('年龄需要是整数');
                return false;
            }
            if(parseInt(params.age_start) > parseInt(params.age_end)){
                layer.msg('年龄范围不正确');
                return false;
            }
            return true;
        }

        // 提交数据构建
        function submitDataConstruct(params) {
            var join_limit = {
                "tag":{"tag_ids":"","tag_status":"0","enable":"0"},
                "gender":{"value":"1","enable":"0"},
                "age":{"value":"18,25","enable":"0"},
                "realname":{"enable":"0"},
                "not_limit":{"enable":"0"}
            };
            $("input[name=join_limit]:checked").each(function (index, ele) {
                var join_limit_value = $(this).val();
                switch (join_limit_value) {
                    case 'not_limit':
                        join_limit.not_limit = {"enable":1}
                        break;
                    case 'tag':
                        join_limit.tag = {
                            "tag_ids": params.tag_ids,
                            "tag_status": params.tag_status,
                            "enable": 1
                        }
                        break;
                    case 'gender':
                        join_limit.gender = {"value":params.gender,"enable":"1"}
                        break;
                    case 'age':
                        join_limit.age = {"value":`${params.age_start},${params.age_end}`,"enable":"1"}
                        break;
                    case 'realname':
                        join_limit.realname = {"enable":"1"}
                        break;
                }
            })
            params.join_limit = join_limit;
            return params;
        }

        // 渲染签到用户限制的字段
        function renderActivityForm(detailData){
            form.val('dataForm',detailData);
            if(Object.keys(detailData).length){
                var join_limit_key = detailData.join_limit ? Object.keys(detailData.join_limit) : [];
                for (let i = 0; i < join_limit_key.length; i++) {
                    if(detailData.join_limit[join_limit_key[i]]['enable']==1){
                        $("input[name=join_limit][value="+join_limit_key[i]+"]").prop('checked',true);
                    }else{
                        $("input[name=join_limit][value="+join_limit_key[i]+"]").prop('checked',false);
                    }
                    if(join_limit_key[i]=='age'){
                        if(detailData.join_limit[join_limit_key[i]]['value']){
                            var age_list = detailData.join_limit[join_limit_key[i]]['value'].split(',');
                            if(age_list.length>1){
                                $('input[name=age_start]').val(age_list[0]);
                                $('input[name=age_end]').val(age_list[1]);
                            }
                        }
                    }else if(join_limit_key[i] == 'gender'){
                        $('select[name=gender]').val(detailData.join_limit[join_limit_key[i]]['value'])
                    }
                }
            }
            form.render();
        }

        // 多轮次签到规则配置table渲染
        function renderMultiRoundRule(){
            default_data_list = default_data_list.map(function (item,index){
               item.key = index;
               return item;
            });
            table.render({
                elem: '#multiRoundRule',
                id:'multiRoundRule',
                cols: [
                    [
                        {
                            field: 'name',
                            title: '签到轮次',
                            align: 'left',
                            unresize: 'false',
                            width: 200,
                            templet:function (data){
                                return '第<input type="number" name="complete_nums_'+data.key+'" value="'+data.complete_nums+'" class="layui-input layui-input-inline complete_nums" ' +
                                    'data-index="'+data.key+'" style="width: 50px;" '+ (data.key ==0 ? 'disabled' : '')+' min="0">次（含）达标后'
                            }
                        },
                        {
                            title: '<div style="display: flex;align-items: center;justify-content: space-between;">奖励优惠券 <span class="primary_color chooseCoupon" data-index="all">批量设置</span></div>',
                            align: 'left',
                            unresize: 'false',
                            width: 200,
                            templet:function (data){
                                if(data.goodscoupon_type_id){
                                    return '<span class="primary_color couponOne ">'+data.goodscoupon_name+'<i class="layui-icon layui-icon-close-fill couponOneRemove" data-index="'+data.key+'"></i></span>'
                                }else{
                                    return '<span class="primary_color chooseCoupon" data-index="'+data.key+'">选择优惠券</span>'
                                }
                            }
                        },
                        {
                            title: '<div style="display: flex;align-items: center;justify-content: space-between;">邀请好友人数 <span class="primary_color">' +
                                '<input type="checkbox" class="table_invite_limit" name="table_invite_limit" lay-skin="primary" '+(invite_limit == "new" ? "checked" : "") +'>勾选仅限新人</span></div>',
                            align: 'left',
                            unresize: 'false',
                            width: 300,
                            templet:function (data) {
                                return '<div style="display: flex;justify-content: center;align-items: center;">' +
                                    '<input type="number" name="invite_nums_'+data.key+'" value="'+data.invite_nums+'" ' +
                                    'class="layui-input layui-input-inline invite_nums" style="width: 50px;text-align: center;" data-index="'+data.key+'" min="0"></div>'
                            }
                        },
                        {
                            field: 'name',
                            title: '指定免邀请的用户范围',
                            align: 'left',
                            unresize: 'false',
                            width: 320,
                            templet:function (data){
                                var tmp_tag_id = 'complete_tags_'+data.key
                                //eliminate_tags  符合任一标签  eliminate_all_tags   符合全部标签
                                if(data.eliminate_tags && Array.isArray(data.eliminate_tags) && data.eliminate_tags.length>0){
                                    tags_dict[tmp_tag_id] = data.eliminate_tags
                                    tag_status_ditc[tmp_tag_id] = 0
                                }
                                if(data.eliminate_all_tags && Array.isArray(data.eliminate_all_tags) && data.eliminate_all_tags.length>0){
                                    tags_dict[tmp_tag_id] = data.eliminate_all_tags
                                    tag_status_ditc[tmp_tag_id] = 1
                                }
                                return '<div id="'+tmp_tag_id+'" class="tags">'+renderTags(tmp_tag_id)+'</div>'
                            }
                        },
                        {
                            title: '操作',
                            align: 'left',
                            toolbar: '#operation',
                            unresize: 'false',
                            width: 120
                        }
                    ]
                ],
                data:default_data_list,
                limit:200,
                done: function(res, curr, count, origin){
                }
            });
            /**
             * 监听工具栏操作
             */
            table.on('tool(multi-round-rule)',function(obj) {
                var data = obj.data;
                var uindex = obj.tr.data('index');
                switch (obj.event) {
                    case 'remove':
                        var tmp_tag_id = 'complete_tags_'+uindex
                        if(uindex == 0){
                            layer.msg('不能删除第一条规则');
                            return;
                        }
                        if(default_data_list.length < 2){
                            layer.msg('最少保留一条规则');
                            return
                        }
                        layer.confirm('是否确认删除规则？', {
                            title: '操作提示',
                            closeBtn: false,
                            btn: ['确认', '取消'],
                            yes: function (index, layero) {
                                default_data_list = default_data_list.filter(function (item,u) {
                                    return u!=uindex
                                })
                                delete tags_dict[tmp_tag_id]
                                delete tag_status_ditc[tmp_tag_id]
                                reloadMultiRoundRule();
                                layer.close(index);			//手动关闭弹出层
                            }
                        })
                        break;
                }
            })
        }
        function reloadMultiRoundRule(){
            renderMultiRoundRule()
        }

        // ######################################参与限制的逻辑代码块 end######################
    })
    function back() {
        location.href = ns.url("membersignin://admin/signIn/lists");
    }
    function sign_close() {
        var sign_activity_id = getUrlParam('sign_activity_id') || null;
        layer.confirm('确认提前结束后，用户第二天开始无法进行新一轮的签到，即今天断签或今天刚达标的，第二天开始均提示活动已终止。只有连续签到中的(即今天及之前已有签到记录的)，可继续签到，待最后一位用户签到成功后，活动自动结束', {
            title:'结束活动',
            btn: ['确认提前结束', '强制结束全部签到进程'],
            yes:function () {
                $.ajax({
                    url: '/membersignin/admin/SignIn/cancel',
                    data:{sign_activity_id:sign_activity_id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        if(res.code==0){
                            layer.msg(res.message,{icon: 1},function () {
                                back()
                            });
                        }else{
                            layer.msg(res.message,{icon: 2})
                        }
                    }
                })
            },
            btn2:function () {
                $.ajax({
                    url: '/membersignin/admin/SignIn/close',
                    data:{sign_activity_id:sign_activity_id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        if(res.code==0){
                            layer.msg(res.message,{icon: 1},function () {
                                back()
                            });
                        }else{
                            layer.msg(res.message,{icon: 2})
                        }
                    }
                })
            }
        })
    }
    function closeService() {
        layer.close(layer_service);
    }
</script>
<script type="application/javascript">
    var tags_id=null;
    var tags_dict={};
    var tag_status_ditc={};
    var tags_data = {:json_encode($tags_data)};
    // 显示标签弹窗
    $(document).on('click', '.tags_more', function () {
        tags_id = $(this).data("id")
        $(".inner-user-tag-box").show();
        if(tag_status_ditc[tags_id] == 1) {
            $('.inner-user-tag-box .sameStatusInner').prop("checked",true);
        }else{
            $('.inner-user-tag-box .onStatusInner').prop("checked",true);
        }
        innerSelectTag()
    })
    // 初始化选中标签
    function innerSelectTag() {
        $('.inner-user-tag-box .inner-tag-list').each((index, element) => {
            var tmp_id = $(element).data('tag-id');
            var tem_list = tags_dict[tags_id] || []
            if(tem_list.includes(String(tmp_id))){
                $(element).addClass("active select")
            }else{
                $(element).removeClass("active")
            }
            //重新渲染
            layui.use('form', function () {
                var form = layui.form;
                form.render();
            });
        })
    }
    // 标签弹窗保存
    $(".inner-save-tag").click(function () {
        $(".inner-user-tag-box").hide();
        tag_status_ditc[tags_id] = parseInt($(':radio[name="inner_tag_status"]:checked').val())
        inner_split_tag_ids()
    })

    // 关闭标签弹窗
    $(".inner-close, .inner-cancel-tag").click(function() {
        $(".inner-user-tag-box").hide();
        innerSelectTag()
    })
    // 关闭
    $(document).on('click', '.inner_tags_one_close', function () {
        var tmp_tag_id = $(this).data('tag-id')
        var tmp_ids = tags_dict[tmp_tag_id]||[]
        tmp_ids = tmp_ids.filter(item=>item!=$(this).data('id'));
        tags_dict[tmp_tag_id] = tmp_ids
        inner_split_tag_ids(tmp_tag_id)
    })

    // 选择标签
    $(document).on('click', '.inner-tag-list', function () {
        var id = $(this).data('tag-id');
        var tm_list = tags_dict[tags_id] || [];
        if($(this).hasClass('active')){
            $(this).removeClass("active")
            tm_list=tm_list.filter(item=>item!=id)
        }else{
            $(this).addClass("active select")
            tm_list.push(String(id))
        }
        tags_dict[tags_id]=tm_list
    })
    function renderTags(id){
        var tag_html='';
        var tmp_ids = tags_dict[id]||[]
        var tags = tags_data.map(item=>item.tags).reduce((one,two)=>one.concat(two)).filter(item=>tmp_ids.includes(String(item.tag_id)))
        for (var i = 0; i < tags.length; i++) {
            tag_html+='<div class="inner_tags_one">'+tags[i].tag_name+'<i class="layui-icon layui-icon-close inner_tags_one_close" data-id="'+tags[i].tag_id+'" data-tag-id="'+id+'"></i></div>'
        }
        if(!tag_html){
            tag_html = '<span style="margin-left: 5px;color: #ccc;">请选择标签</span>'
        }
        var html='<div class="inner_tags_list">'+tag_html+'</div><i class="layui-icon layui-icon-triangle-d tags_more" data-id="'+id+'"></i>'
        return html
    }
    function inner_split_tag_ids(tmp_tag_id) {
        if(tmp_tag_id){
            $('#'+tmp_tag_id).html(renderTags(tmp_tag_id));
        }else{
            $('#'+tags_id).html(renderTags(tags_id));
        }
    }
</script>
{/block}