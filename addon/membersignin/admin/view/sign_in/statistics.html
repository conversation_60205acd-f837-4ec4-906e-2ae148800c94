{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .ns-screen .layui-colla-content .layui-input, .ns-screen .layui-colla-content .layui-form-select{ width: 240px !important }
    .ns-screen .layui-colla-content .layui-input.time{ width: 108px !important }
</style>
{/block}
{block name="main"}
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">筛选</h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <label class="layui-form-label">手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="mobile_search" name="mobile_search" placeholder="" class="layui-input" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">签到时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>

                <div class="layui-input-inline">
                    <label class="layui-form-label">邀请人手机：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="inviter_member" name="inviter_member" placeholder="" class="layui-input" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">参与用户：</label>
                    <div class="layui-input-inline">
                        <select name="is_first_sign" lay-filter="is_first_sign">
                            <option value="" selected>全部</option>
                            <option value="1">仅看新人</option>
                        </select>
                    </div>
                </div>

                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">查询</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <!--<button class="layui-btn layui-btn-primary" lay-submit lay-filter="sale_goods_export">导出</button>-->
                </div>
            </div>

            <input id="sign_activity_id" type="hidden" name="sign_activity_id" value="{$sign_activity_id}">
        </form>
    </div>
</div>

<script type="text/html" id="operation">
    <div style="background-color: #e6f7ff;border: 1px solid #bae7ff;height: 50px;line-height: 50px;display: flex;justify-content:space-evenly;">
        <div>活动访问量：{{d.access_nums}}</div>
        <div>访问人数：{{d.access_people_nums}}</div>
        <div>参与活动人数：{{d.sign_people_nums}}</div>
        <div>参与活动新用户数：{{d.new_person_join_nums}}</div>
        <div>签到次数：{{d.sign_nums}}</div>
        <div>达标数：{{d.complete_nums}}</div>
<!--        <div>断签数：{{d.interrupt_nums}}</div>-->
        <div>分享链接访问量：{{d.share_access_nums}}</div>
    </div>
</script>
<div id="operation_view"></div>

<!-- 列表 -->
<table id="shop_list" lay-filter="shop_list"></table>
<!-- 是否自营 -->
<script type="text/html" id="is_own">
    {{ d.is_own == 1 ? '是' : '否' }}
</script>

{/block}

{block name="script"}
<script>
    layui.use(['form', 'laydate','laytpl'], function() {
        let table, table_website,
            form = layui.form,
            laytpl = layui.laytpl,
            laydate = layui.laydate;
        form.render();

        var sign_activity_id = $('input[name = sign_activity_id]').val();

        //渲染时间
        laydate.render({
            elem: '#start_time',
            // type: 'datetime'
        });

        laydate.render({
            elem: '#end_time',
            // type: 'datetime'
        });
        laydate.render({
            elem: '#start_create_time',
            // type: 'datetime'
        });

        laydate.render({
            elem: '#end_create_time',
            // type: 'datetime'
        });

        /**
         * 渲染表格
         */
        table = new Table({
            elem: '#shop_list',
            url: ns.url("membersignin/admin/SignIn/statistics?sign_activity_id=" + sign_activity_id),
            cols: [
                [{
                    field: 'nickname',
                    title: '签到用户',
                    width: '16%',
                    unresize: 'false',
                }, {
                    field: 'mobile',
                    title: '手机号',
                    width: '16%',
                    unresize: 'false',
                }, {
                    field: 'sign_time',
                    title: '签到时间',
                    width: '16%',
                    unresize: 'false',
                    sort:true
                },{
                    field: 'is_first_sign',
                    title: '是否为新人签到',
                    width: '16%',
                    unresize: 'false',
                    align: 'center',
                    templet: function (data) {
                        if (data.is_first_sign == 1) return '是';
                        return '否';
                    }
                },
                {
                    field: 'source',
                    title: '用户来源',
                    width: '16%',
                    unresize: 'false',
                }, {
                        field: 'inviter_member',
                        title: '邀请人',
                        width: '16%',
                        unresize: 'false',
                    }
                ]
            ],
            parseData:function (res){
                let getTpl = operation.innerHTML,view = document.getElementById('operation_view');
                laytpl(getTpl).render(res.data.statistics, function(html){
                    view.innerHTML = html;
                });
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.message, //解析提示文本
                    "count": res.data.count, //解析数据长度
                    "data": res.data.list //解析数据列表
                };
            }
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
            return false;
        });

        form.on('submit(sale_goods_export)', function(data) {
            layer.msg('导出中，请稍后...', {icon: 16, shade: 0.5, time: 0});
            $.ajax({
                url : ns.url("admin/stat/saleGoodsExport", data.field),
                type : 'GET',
                dataType : 'JSON',
                success : function (res){
                    if(res.code == 0)
                    {
                        location.href = res.data;
                        layer.msg('操作成功')
                    }
                    else
                    {
                        layer.msg(res.message)
                        return false;
                    }
                }
            });
            return false;
        });


        //监听排序事件
        table.on('sort', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload({
                initSort: obj, //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                autoSort: false,
                where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sort_field: obj.field //排序字段
                    ,order: obj.type //排序方式
                },
                page: {
                    curr: 1
                }
            });

            return false;
        });
    });

</script>
{/block}