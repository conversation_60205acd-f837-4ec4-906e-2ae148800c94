{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .activity-table{
        margin-top: 20px;
    }
    .activity-table-header{
        font-size: 16px;
        color: #797979;
        font-weight: 400;
        position: relative;
        padding-left: 16px;
        box-sizing: border-box;
    }
</style>
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input" oninput="value=value.trim()" />
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label mid">完成时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="complete_time_start" lay-verify="startTime" id="start_time" class="layui-input ns-len-mid" autocomplete="off" placeholder="开始时间">
                    </div>
                    <div class="layui-input-inline ns-split">-</div>
                    <div class="layui-input-inline">
                        <input type="text" name="complete_time_end" lay-verify="overTime" id="over_time" class="layui-input ns-len-mid" autocomplete="off" placeholder="结束时间">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">绑定客服状态：</label>
                    <div class="layui-input-inline">
                        <select name="is_bind_cs" lay-filter="is_bind_cs">
                            <option value="">全部</option>
                            <option value="1">已绑</option>
                            <option value="2">未绑</option>
                        </select>
                    </div>
                </div>
                <!--<div class="layui-inline">
                    <label class="layui-form-label">绑定客服名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="cs_name" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>-->

                <div class="layui-inline">
                    <label class="layui-form-label">奖励发放状态：</label>
                    <div class="layui-input-inline">
                        <select name="is_send_reward" lay-filter="status">
                            <option value="">全部</option>
                            <option value="0">没发</option>
                            <option value="1">已发</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">兑换码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="code" placeholder="" autocomplete="off" class="layui-input" oninput="value=value.trim()" />
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label mid">达标轮次：</label>
                    <div class="layui-input-inline">
                        <input type="number" name="start_round" lay-verify="startRound" id="start_round" class="layui-input ns-len-mid" autocomplete="off" placeholder="开始轮次">
                    </div>
                    <div class="layui-input-inline ns-split">-</div>
                    <div class="layui-input-inline">
                        <input type="number" name="over_round" lay-verify="overRound" id="over_round" class="layui-input ns-len-mid" autocomplete="off" placeholder="结束轮次">
                    </div>
                </div>
            </div>


            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">搜索</button>
                <button class="layui-btn layui-btn-primary" lay-submit lay-filter="export" id="export">批量导出</button>
            </div>
        </form>

    </div>
</div>

<div class="activity-table">
    <table id="activity_list" lay-filter="activity_list"></table>
</div>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <div class="activity-table-header">批量处理<button class="layui-btn ns-bg-color layui-btn-sm" style="margin-left: 10px" lay-event="batchSend">批量发放奖励</button></div>
    </div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        {{#  if(d.is_send_reward == 0){ }}
        <a class="layui-btn" lay-event="send">发放奖励</a>
        {{#  } }}
    </div>
</script>

{/block}
{block name="script"}
<script type="application/javascript">
    var search_data = {}
    //获取url中的参数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = window.location.search.substr(1).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return null; //返回参数值
    }
    var sign_activity_id = getUrlParam('sign_activity_id') || null;
    layui.use(['form','laydate','element'], function() {
        var table,
        form = layui.form,
        laydate = layui.laydate;
        form.render();

        // 时间模块
        laydate.render({
            elem: '#over_time', //指定元素
            type: 'datetime',
        });

        // 时间模块
        laydate.render({
            elem: '#start_time', //指定元素
            type: 'datetime',
        });



        table = new Table({
            elem: '#activity_list',
            url: '/membersignin/admin/SignIn/complete',
            toolbar: '#toolbarDemo',
            where: {
                sign_activity_id: sign_activity_id
            },
            cols: [
                [
                    {type: 'checkbox'},
                    { type: 'numbers', title: '序号', width: 80 },//序号列
                    {
                        field: 'nickname',
                        title: '用户昵称',
                        unresize: 'false',
                        width: 200
                    },
                    {
                        field: 'mobile',
                        title: '手机号码',
                        unresize: 'false',
                        width: 200
                    },
                    {
                        field: 'code',
                        title: '兑换码',
                        unresize: 'false',
                        width: 200
                    },
                    {
                        field: 'complete_date_start',
                        title: '签到开始日期',
                        unresize: 'false',
                        width: 120
                    },
                    {
                        field: 'complete_date_end',
                        title: '签到完成日期',
                        unresize: 'false',
                        width: 120
                    },
                    {
                        field: 'bind_cs_name',
                        title: '绑定企微员工',
                        unresize: 'false',
                        width: 120
                    },
                    {
                        field: 'bind_time',
                        title: '绑定企微时间',
                        unresize: 'false',
                        width: 160
                    },
                    {
                        field: 'is_send_reward_text',
                        title: '礼品领取状态',
                        unresize: 'false',
                        width: 120
                    },
                    {
                        title: '已邀请用户',
                        unresize: 'false',
                        width: 120,
                        templet: function(data) {
                            return '<div class="ns-table-btn layui-table-cell"><a class="layui-btn" title="'+ data.invited_member +'">'+ data.invited_member_count +'</a></div>';
                        }
                    },
                    {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
                    width: '20%'
                }]
            ],
        });
        // 搜索
        form.on('submit(search)', function(data) {
            let field = data.field;
            field.sign_activity_id = sign_activity_id
            search_data = field

            if(field.start_round || field.over_round){
                if(field.start_round < 1 || field.over_round < 1){
                    layer.msg('轮次不能小于1',{'icon':2})
                    return false;
                }
                if(field.start_round > field.over_round){
                    layer.msg('开始轮次不能大于结束轮次',{'icon':2})
                    return false;
                }
            }

            table.reload({
                page: {
                    curr: 1
                },
                where: field
            });
            return false;
        });

        
        $("#export").on('click',function (e) {
            if(search_data.start_round || search_data.over_round){
                if(search_data.start_round < 1 || search_data.over_round < 1){
                    layer.msg('轮次不能小于1',{'icon':2})
                    return false;
                }
                if(search_data.start_round > search_data.over_round){
                    layer.msg('开始轮次不能大于结束轮次',{'icon':2})
                    return false;
                }
            }
            $.ajax({
                type: 'POST',
                async: false,
                url: '/membersignin/admin/signIn/exportSignInComplete',
                data: search_data,
                dataType: 'JSON',
                success: function(res) {

                    if(res.code!=0){
                        layer.msg(res.message);
                    }else{
                        var path=res.data.path;
                        location.href=path;
                    }
                }
            });
            return false;
        })


        function distributeRewards(complete_ids=[]){
            if(!complete_ids || complete_ids.length<1 ){
                layer.msg('请选择发放记录',{icon: 2})
                return
            }
            var loadIndex = layer.msg('操作中', {
                icon: 16,
                shade: 0.01
            });
            $.ajax({
                url: '/membersignin/admin/SignIn/batchSendReward',
                data:{
                    complete_ids:complete_ids,
                    sign_activity_id:sign_activity_id
                },
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.close(loadIndex);
                    if(res.code==0){
                        layer.msg('发放成功'+res.data.send_count+'条，发失败'+res.data.fail_count+'条',{icon: 1},function () {
                        });
                        table.reload({
                            page: {
                                curr: 1
                            },
                            where: search_data
                        });
                    }else{
                        layer.msg(res.message,{icon: 2})
                    }
                }
            })
        }
        // 工具栏事件
        table.toolbar(function (obj) {
            var data = obj.data;
            var id = obj.config.id;
            var checkStatus = table.checkStatus(id);
            switch (obj.event) {
                case 'batchSend': //批量发放优惠券
                    var chooseData = checkStatus.data;
                    var complete_ids = chooseData.map(function (item) {
                        return item.id
                    })
                    if(chooseData.length<1){
                        layer.msg('请选勾选需要发放的记录',{icon:2})
                    }else{
                        layer.confirm('当前已选择'+chooseData.length+'个达标用户，确认发放活动奖励？', {
                            title:'奖励发放',
                            btn: ['确认发放奖励', '取消'] //按钮
                        }, function(){
                            distributeRewards(complete_ids)
                        }, function(){

                        });
                    }
                    break
            }
        })

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'send': //单个发优惠券
                    layer.confirm('当前已选择1个达标用户，确认发放活动奖励？', {
                        title:'奖励发放',
                        btn: ['确认发放奖励', '取消'] //按钮
                    }, function(){
                        distributeRewards([data.id])
                    }, function(){

                    });
                    break;
            }
        })
    })
</script>
{/block}