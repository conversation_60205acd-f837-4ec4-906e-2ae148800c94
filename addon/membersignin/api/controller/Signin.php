<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\membersignin\api\controller;

use addon\goodscoupon\model\GoodscouponModel;
use addon\goodscoupon\model\GoodscouponTypeAction;
use addon\goodscoupon\model\GoodscouponTypeModel;
use addon\membersignin\DataType\COMPLETE_STATUS;
use addon\membersignin\DataType\SIGN_STATUS;
use addon\membersignin\model\DataType\SIGN_ACTIVITY_STATUS;
use addon\membersignin\model\SignActivityCompleteModel;
use addon\membersignin\model\SignActivityModel;
use app\api\controller\BaseApi;
use addon\membersignin\model\Signin as SigninModel;
use app\model\enterpriseWx\MemberEnterpriseWechatModel;
use app\model\fansTask\FansTaskRepository;
use app\model\goods\GoodsSkuModel;
use app\model\member\MemberAuthorizeModel;
use app\model\member\MemberLimit;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\model\sign\Sign;
use app\model\sign\SignAccessModel;
use app\model\sign\SignLogModel;
use app\Request;
use app\service\enterpriseWx\EnterpriseService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use EasyWeChatComposer\Laravel\ServiceProvider;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 会员签到
 */
class Signin extends BaseApi
{

	/**
	 * 配置信息
	 */
	public function config()
	{
		$signin_model = new SigninModel();
        $result = $signin_model->getConfig();

		return $this->response($result);
	}

    public function detail(Request $request)
    {
        $rules = [
            'sign_activity_id' => 'require|number',
            'complete_id' => 'number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $sign = new Sign();
        $token = $this->checkToken();
        //if ($token['code'] < 0) return $this->response($token);
        $service = new EnterpriseService(0);
        $activity = SignActivityModel::find($data['sign_activity_id']);

        $ret['sign_activity_id'] = $activity['sign_activity_id'];
        $ret['default_img'] = $activity['default_img'];
        $ret['rule'] = $activity['rule'];
        $ret['share_title'] = $activity['share_title'];
        $ret['share_img'] = $activity['share_img'];

        $ret['complete_sign_nums'] = $sign->getCompleteSignNums($data['sign_activity_id'], $this->member_id);
        $ret['continuous_nums'] = $sign->getContinuousSignNums($data['sign_activity_id'], $this->member_id);
        $ret['advertise'] = $activity['advertise'];
        $ret['advertise_type'] = $activity['advertise_type'];
        $ret['advertise_link'] = $activity['advertise_link'];
        $ret['look_time'] = $activity['look_time'];
        $ret['show_date'] = $this->calculateShowDates($sign, $data['sign_activity_id'], $this->member_id);
        $completeLogs = SignActivityCompleteModel::where("member_id", $this->member_id)->where("sign_activity_id", $data['sign_activity_id'])->order("id","desc")->select();
        $ret['complete_logs'] = [];
        $ret['end_time'] = $activity['end_time'];
        $ret['status'] = $activity['status'];
        // 签到状态是提前结束的，前端重置为进行中
        if ($ret['status'] == SIGN_ACTIVITY_STATUS::$CANCEL) {
            $ret['status'] = SIGN_ACTIVITY_STATUS::$STARTING;
        }
        $residue_time = Carbon::parse($ret['end_time'])->getTimestampMs() - Carbon::now()->getTimestampMs();
        $ret['residue_time'] = $residue_time > 0 ? $residue_time : 0;
        $ret['invite_limit'] = $activity['invite_limit'] ?? "none";
        $ret['complete_nums'] = count($completeLogs);
        $ret['complete_headimg'] = "";
        $ret['complete_nickname'] = "";
        $receive_nums_cache_key = 'receive_nums_'.$data['sign_activity_id'];
        $receive_nums = Cache::get($receive_nums_cache_key, 0);
        if(!$receive_nums){
            $receive_nums = $sign->getSignMemberNums($data['sign_activity_id']);
            Cache::set($receive_nums_cache_key, $receive_nums, 600);
        }
        $ret['receive_nums'] = $receive_nums;
        if(isset($data['complete_id']) && $data['complete_id'] && $complete = SignActivityCompleteModel::find($data['complete_id']))
        {
            $inviteMember = MemberModel::where("member_id", $complete->member_id)->find();
            $ret['complete_headimg'] = $inviteMember ? $inviteMember->headimg : "";
            $ret['complete_nickname'] = $inviteMember ? $inviteMember->nickname : "";
        }
        // xm_sign_activity 表中goodscoupon_type_ids字段废弃，优惠券id需要在complete_rules字段中取goodscoupon_type_id字段
        $ret['use_goods'] = []; //兼容goodscoupon_type_ids废弃之前的数据结构
        $ret['rounds_use_goods'] = [];
        $round_index = 0;
        foreach ($activity->complete_rules as $key=>$rule) {
            if(!isset($rule['goodscoupon_type_id']) || empty($rule['goodscoupon_type_id'])){
                break;
            }
            $rounds = [
                "name" => "第{$rule['complete_nums']}轮起可选",
                "round"=> $rule['complete_nums'],
                "is_current_round" => false,
                "goods"=>[]
            ];
            if($ret['complete_nums'] >= $rule['complete_nums']){
                $round_index = $key;
            }
            $couponAction = new GoodscouponTypeAction(GoodscouponTypeModel::find($rule['goodscoupon_type_id']));
            $rounds['goods'] = $couponAction->getUseGoodsList();
            foreach ($rounds['goods'] as $i=>$g)
            {
                $rounds['goods'][$i]['goods_image'] = explode(",", $g['goods_image'])[0];
                $rounds['goods'][$i]['experiential_price'] = round(($rounds['goods'][$i]['price'] - $couponAction->gCouponModel->money),2); //体验价
                if($rounds['goods'][$i]['experiential_price'] < 0.01){
                    $rounds['goods'][$i]['experiential_price'] = '0.01';
                }
            }
            $ret['rounds_use_goods'][] = $rounds;
        }
        $ret['rounds_use_goods'][$round_index]['is_current_round'] = true;
        foreach ($completeLogs as $i=>$log)
        {
            $ret['complete_logs'][$i]['id'] = $log->id;
            $ret['complete_logs'][$i]['title'] = '签到任务完成';
            $signLogs = SignLogModel::where("member_id", $this->member_id)->where("sign_activity_id", $data['sign_activity_id'])->where("sign_time", "<=", $log->complete_time)->order("sign_time", "desc")->limit($log->continuous_nums)->select();
            $ret['complete_logs'][$i]['complete_date_start'] = $signLogs[count($signLogs)-1]['sign_date'];
            $ret['complete_logs'][$i]['complete_date_end'] = $log->complete_date;
            $ret['complete_logs'][$i]['is_send_reward'] = $log->is_send_reward;
            $ret['complete_logs'][$i]['code'] = !$activity->use_rules || (!empty($this->member_id) && $sign->residueInviteNums($activity['sign_activity_id'], $this->member_id, $log->id) <= 0) ? $log->code : '';
            $ret['complete_logs'][$i]['residue_invite_nums'] = $sign->residueInviteNums($data['sign_activity_id'], $this->member_id, $log->id);
            $goodscouponType = null;
            if($log->reward_goodscoupon_ids)
                $goodscouponType = GoodscouponTypeModel::find($log->reward_goodscoupon_ids);
            $ret['complete_logs'][$i]['goodscoupon_name'] = $goodscouponType ? "满{$goodscouponType->at_least}减{$goodscouponType->money}元" : '';
            $ret['complete_logs'][$i]['goodscoupon_goods'] = $sign->getGoodsCouponBuyGoods($log);
            $ret['complete_logs'][$i]['invite_member'] = $sign->getInviteMember($log);
            $ret['complete_logs'][$i]['complete_status'] = $this->showStatus($log);
            $ret['complete_logs'][$i]['is_old_data'] = $this->isOldData($log);
            $ret['complete_logs'][$i]['cs_info']['name'] = '';
            $ret['complete_logs'][$i]['cs_info']['headpic'] = '';
            $ret['complete_logs'][$i]['cs_info']['qrcode'] = '';
            if($log->enterprise_wx_userid)
            {
                $cs_info = $sign->getCsUserInfo($activity, $this->member_id);
                $ret['complete_logs'][$i]['cs_info'] = $cs_info;
            }
        }
        $ret['invite_list'] = $sign->getInviteAvatarList($activity['sign_activity_id'],count($completeLogs) > 0 ? $completeLogs[0] : null);
        $this->writeAccessLog($data['sign_activity_id']);
        return json(success(0, '', $ret));
    }

    protected function showStatus(SignActivityCompleteModel $complete)
    {
        $sign = new Sign();
        if($complete->is_send_reward)
        {
            if($complete->goodscoupon_id && $sign->getGoodsCouponBuyGoods($complete))
                return COMPLETE_STATUS::$USED;
            else
                return  COMPLETE_STATUS::$SEND;
        }
        else
        {
            if($complete->need_invite_nums > count($complete->invite_member_ids))
                return COMPLETE_STATUS::$WAIT_INVITE;
            else
                return COMPLETE_STATUS::$INVITE_COMPLETE;
        }
    }

    public function isOldData(SignActivityCompleteModel $complete)
    {
        return (bool)(in_array($complete->sign_activity_id, [3,4,6]) && $complete->id <= 9661);
    }

    public function completeLogs(Request $request)
    {
        $rules = [
            'sign_activity_id' => 'require|number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $sign = new Sign();

        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $completeLogs = SignActivityCompleteModel::where("member_id", $this->member_id)->where("sign_activity_id", $data['sign_activity_id'])->order("id","desc")->select();
        $signActivity = SignActivityModel::find($data['sign_activity_id']);
        $ret = [];
        foreach ($completeLogs as $i=>$log)
        {
            $ret[$i]['id'] = $log->id;
            $ret[$i]['title'] = '签到任务完成';
            $signLogs = SignLogModel::where("member_id", $this->member_id)->where("sign_activity_id", $data['sign_activity_id'])->where("sign_time", "<=", $log->complete_time)->order("sign_time", "desc")->limit($log->continuous_nums)->select();
            $ret[$i]['complete_date_start'] = $signLogs[count($signLogs)-1]['sign_date'];
            $ret[$i]['complete_date_end'] = $log->complete_date;
            $ret[$i]['is_send_reward'] = $log->is_send_reward;
            $ret[$i]['cs_info'] = $sign->getCsUserInfo($signActivity, $this->member_id);
            $ret[$i]['code'] = !$signActivity->use_rules || $sign->residueInviteNums($data['sign_activity_id'], $this->member_id, $log->id) <= 0 ? $log->code : '';
            $ret[$i]['complete_status'] = $this->showStatus($log);
            $ret[$i]['residue_invite_nums'] = $sign->residueInviteNums($data['sign_activity_id'], $this->member_id, $log->id);
            $ret[$i]['invite_member'] = $sign->getInviteMember($log);
            $ret[$i]['share_title'] = $signActivity['share_title'];
            $ret[$i]['share_img'] = $signActivity['share_img'];
            $ret[$i]['is_old_data'] = $this->isOldData($log);
            $ret[$i]['goodscoupon_type_id'] = $log->reward_goodscoupon_ids;
            $ret[$i]['need_invite_nums'] = $log->need_invite_nums;
            $ret[$i]['invite_limit'] = $signActivity->invite_limit ?? "none";
            
            $goodscouponType = null;
            if($log->reward_goodscoupon_ids)
                $goodscouponType = GoodscouponTypeModel::find($log->reward_goodscoupon_ids);

            $ret[$i]['goodscoupon_name'] = $goodscouponType ? "满{$goodscouponType->at_least}减{$goodscouponType->money}元" : '';
            $ret[$i]['goodscoupon_goods'] = $sign->getGoodsCouponBuyGoods($log);
        }
        return json(success(0, '', $ret));
    }



    public function sign(Request $request)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'sign_activity_id' => 'require|number',
            'complete_id' => 'number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);
        $sign = new Sign();

        if($sign->todayIsSign($data['sign_activity_id'], $this->member_id))
            return json(error(-1, ' 今天您已经签到过了', []));

        $app_type = $this->params['app_type'] ?? '';
        if($app_type && $app_type != 'weapp')
            return json(error(-1, '请在先迈网小程序上签到', []));

        $signActivity = SignActivityModel::find($data['sign_activity_id']);

        //
        if(
            in_array($signActivity->status, [SIGN_ACTIVITY_STATUS::$CLOSED]) ||
            ($signActivity->status == SIGN_ACTIVITY_STATUS::$CANCEL && $sign->getContinuousSignNums($data['sign_activity_id'], $this->member_id) == 0) ||
            Carbon::now()->timestamp > strtotime($signActivity['end_time'])
        )
        {
            return json(error(-1, '活动已结束', []));
        }
        if($signActivity->status == SIGN_ACTIVITY_STATUS::$WAIT)
            return json(error(-1, '活动未开始', []));

        $unionid = MemberAuthorizeModel::where("member_id", $this->member_id)->value("wechat_unionid");
        if(!in_array($data['sign_activity_id'], [1,2,3]) && empty($unionid))
            return json(error(-10009, '授权数据异常，请重新授权登录', []));

        $msg = '';
        if(!in_array($data['sign_activity_id'], [1,2,3]) && !$sign->checkRepeatSign($data['sign_activity_id'], $unionid, $this->member_id, $msg))
            return json(error(-1, $msg, []));

        $limitData = $signActivity->join_limit;

        if($limitData)
        {
            $failReason = [];
            $memberLimit = new MemberLimit($limitData);
            if($this->member_id && !$memberLimit->check(MemberModel::find($this->member_id), $failReason))
            {
                Log::info("签到限制: member_id: {$this->member_id}, 失败原因: " . json_encode($failReason, JSON_UNESCAPED_UNICODE));
                if(in_array("实名验证不通过", $failReason))
                    return $this->response($this->error('', 'REALNAME_AUTH_ERROR'));
                else
                    return $this->response($this->error('', 'JOIN_LIMIT'));
            }
        }

        try
        {
            Db::startTrans();

            $signData['sign_activity_id'] = $data['sign_activity_id'];
            $signData['member_id'] = $this->member_id;
            $signData['sign_time'] = time();
            $signData['sign_date'] = Carbon::createFromTimestamp($signData['sign_time'])->toDateString();     //parse会有解析时间戳8小时时差
            $signData['continuous_nums'] = $sign->getContinuousSignNums($signData['sign_activity_id'], $this->member_id) + 1;
            $signData['create_time'] = Carbon::now()->toDateTimeString();
            $signData['wx_unionid'] = $unionid;
            $signData['access_id'] = $this->writeAccessTrack();
            $signData['is_first_sign'] = SignLogModel::where("member_id", $this->member_id)->count() > 0 ? 0 : 1;
            $logMl = SignLogModel::create($signData);

            if($signData['continuous_nums'] >= $sign->getCompleteSignNums($data['sign_activity_id'], $this->member_id))
            {
                $completeData['member_id'] = $this->member_id;
                $completeData['comexternal_userid'] = '';
                $completeData['unionid'] = $unionid;
                $completeData['sign_activity_id'] = $data['sign_activity_id'];
                $completeData['continuous_nums'] = $signData['continuous_nums'];
                $completeData['complete_time'] = time();
                $completeData['complete_date'] = Carbon::now()->toDateString();
                $completeData['code'] = $sign->createCode($data['sign_activity_id']);
                $userInfo = $sign->getCsUserInfo($signActivity, $this->member_id);
                $completeData['enterprise_wx_userid'] = $userInfo['userid'];
                $completeData['create_time'] = Carbon::now()->toDateTimeString();
                $completeMl = SignActivityCompleteModel::create($completeData);
                //写入完成记录后更新所需邀请人数和发放奖励优惠券id
                $completeMl->need_invite_nums = $sign->getNeedInviteNums($data['sign_activity_id'], $this->member_id);
                $completeMl->reward_goodscoupon_ids = $sign->getRewardGoodscouponTypeId($data['sign_activity_id'], $this->member_id);
                $completeMl->save();

                $tasks = FansTaskRepository::getStartByCompleteSign();

                $parentMemberId = MemberRecommendModel::where("member_id", $this->member_id)->value('pid');
                foreach ($tasks as $task)
                {
                    if($parentMemberId > 0)
                    {
                        if($task->checkComplete($this->member_id) && $task->checkRewardLimit($parentMemberId))
                        {
                            $task->complete($this->member_id);
                        }
                    }
                }
            }
            //邀请分享人
            $isHelp = false;
            if(isset($data['complete_id']) && $data['complete_id'])
            {
                $complete = SignActivityCompleteModel::find($data['complete_id']);
                if($complete)
                {
                    $inviteMemberIds = $complete->invite_member_ids;
                    //是否助力成功
                    if($complete->isHelpSign($this->member_id))
                    {
                        $isHelp = true;
                        $inviteMemberIds[] = $this->member_id;
                        $complete->invite_member_ids = implode(",", $inviteMemberIds);
                        $complete->save();
                    }
                    $logMl->inviter_member_id = $complete->member_id;
                    $logMl->save();
                }
            }

            Db::commit();
        }
        catch (\Exception $e)
        {
            Db::rollback();
            return json(error(-1, ' 签到失败', $e->getMessage()));
        }

        return json(success(0, ' 签到成功', ['is_help'=>$isHelp]));
    }

    protected function isInviteNew($inviteMemberId)
    {
        if(SignLogModel::where("member_id", $inviteMemberId)->count() > 0)
            return false;
        else
            return true;
    }

    protected function writeAccessLog($signActivityId)
    {
        try
        {
            $data['member_id'] = $this->member_id ?? 0;
            $data['ip'] = get_real_ip();
            $data['page_url'] = request()->baseUrl();
            $data['sign_activity_id'] = $signActivityId;
            $data['date'] = Carbon::now()->toDateString();
            $data['access_id'] = $this->writeAccessTrack();
            return SignAccessModel::create($data)->id;
        }
        catch (\Exception $e)
        {
            Log::error("写入访问失败:{$e->getMessage()}");
            return 0 ;
        }
    }

    protected function calculateShowDates(Sign $sign, $signActivityId, $member_id)
    {
        $showDates = [];
        $completeNums = $sign->getCompleteSignNums($signActivityId, $member_id);
        $today = Carbon::now()->toDateString();
        $yesterday = Carbon::yesterday()->toDateString();

        $yesterdaySign = SignLogModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->where("sign_date", $yesterday)->order("id", "desc")->find();
        $lastComplete = SignActivityCompleteModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->order("id", "desc")->find();
        $retData = [];

        //没断签连续显示直到超过长度限制
        if(($yesterdaySign && !$lastComplete) || ($yesterdaySign && $lastComplete->complete_date != $yesterday))
        {
            $retData[] = ['date'=>$yesterday, 'is_sign'=>true, 'sign_status'=>1];

            $dayIndex = $yesterday;
            do
            {
                $dayIndex = Carbon::parse($dayIndex)->subDay()->toDateString();
                //指针到最后达标记录的那天则跳出
                if($lastComplete && $dayIndex == $lastComplete->complete_date)
                    break;
                $dayIndexSign = SignLogModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->where("sign_date", $dayIndex)->order("id", "desc")->find();
                if($dayIndexSign)
                {
                    $beforeDay = [['date'=>$dayIndexSign->sign_date, 'is_sign'=>true, 'sign_status'=>1]];
                    $retData = array_merge($beforeDay, $retData);
                }
            }while($dayIndexSign && count($retData) < $completeNums - 1);

        }
        //断签的显示上个签到日期往后的时间段，如果超长度则直接从今天开始算
        else
        {
            $where = [];
            if($lastComplete)
                $where[] = ['sign_time', '>', $lastComplete->complete_time];
            $lastSign = SignLogModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->where("sign_date", "<>", $today)->where($where)->order("id", "desc")->find();
            if($lastSign && $lastSign->sign_date != Carbon::yesterday()->toDateString())
            {
                if(Carbon::parse($lastSign->sign_date)->diffInDays(Carbon::now()) < $completeNums)
                {
                    $showDatesCar = CarbonPeriod::create($lastSign->sign_date, Carbon::now()->toDateString());
                    foreach ($showDatesCar as $c)
                        $showDates[] = $c->toDateString();
                }
                foreach ($showDates as $date)
                {
                    $is_sign = (bool)SignLogModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->where("sign_date", $date)->find();
                    $sign_status = $is_sign ? 1 : -1;

                    $retData = array_merge($retData, [['date'=>$date, 'is_sign'=>$is_sign, 'sign_status'=>$sign_status]]);
                }
            }
        }

        if(empty($retData) || $retData[count($retData)-1]['date'] != $today)
        {
            $is_sign = (bool)SignLogModel::where("member_id", $member_id)->where("sign_activity_id", $signActivityId)->where("sign_date", $today)->find();
            $sign_status = $is_sign ? 1 : 0;
            $retData = array_merge($retData, [['date'=>$today, 'is_sign'=>$is_sign, 'sign_status'=>$sign_status]]);
        }



        while(count($retData) < $completeNums)
        {
            $nextDay = Carbon::parse($retData[count($retData)-1]['date'])->addDay()->toDateString();

            $retData = array_merge($retData, [['date'=>$nextDay, 'is_sign'=>false, 'sign_status'=>0]]);
        }

        $sign = new Sign();
        $continuous_index = 0;
        foreach ($retData as $i=>&$v)
        {
            $v['is_now_date'] = $v['date'] == Carbon::now()->toDateString();

            if(Carbon::parse($v['date'])->timestamp == Carbon::parse(date("Y-m-d", time()))->timestamp)
            {
                $continuous_index = $sign->getContinuousSignNums($signActivityId, $member_id);
                $continuous_index = !$sign->todayIsSign($signActivityId, $member_id) ? $continuous_index+1 : $continuous_index;
            }
            if(Carbon::parse($v['date'])->timestamp > Carbon::parse(date("Y-m-d", time()))->timestamp)
            {
                $continuous_index++;
            }
            //如果那天有完成记录，写入奖励节点  重置连续签到计数
            if($sign->dateIsComplete($signActivityId, $member_id, $v['date']))
            {
                $v['is_reward'] = true;
                $continuous_index = 0;
            }
            //没有完成记录的根据连续签到计数计算奖励节点
            else
            {
                $v['is_reward'] = $continuous_index == $completeNums;
            }

            $v['continuous_index'] = $continuous_index;
            $v['sign_status_text'] = SIGN_STATUS::toText()[$v['sign_status']];
            $v['date'] = Carbon::parse($v['date'])->format('m.d');
        }

        return $retData;
    }


}