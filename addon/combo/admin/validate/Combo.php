<?php
declare (strict_types=1);

namespace addon\combo\admin\validate;

use addon\combo\constant\COMBO_STATUS;
use think\Validate;

class Combo extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        "page" => 'require|number',
        "page_size" => 'require|number',
        "combo_id|组合ID" => 'require|number',
        "search.combo_id|组合ID" => 'number',
        "search.combo_name|组合名称" => 'chsDash',
        "search.goods_name|商品名称" => 'chsDash',
        'search.status|状态' => ['in' => [
            COMBO_STATUS::WAIT,
            COMBO_STATUS::START,
            COMBO_STATUS::END,
            COMBO_STATUS::CLOSE,
        ]],
        "search.start_time|开始时间" => 'array',
        "search.create_time|下单时间" => 'array',
        "search.nickname|昵称" => 'chsDash',
        "search.phone|手机号" => 'mobile',

        'name|组合名称' => 'require|chsDash',
        'skus|sku数组' => 'require|array',
        'start_time|开始时间' => 'require|date',
        'end_time|结束时间' => 'require|date',

        'skus.sku_id|SKU ID' => 'require|number',
        'skus.goods_id|商品ID' => 'require|number',
        'skus.combo_price|组合单价' => 'require|float',
        'skus.buy_nums|数量' => 'require|number',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
    ];

    protected $scene = [
        'search' => ['page', 'page_size', 'search.combo_id', 'search.combo_name', 'search.goods_name', 'search.status'],
        'lists_export' => ['search.combo_id', 'search.combo_name', 'search.goods_name', 'search.status'],
        'detail' => ['combo_id'],
        'record' => ['page', 'page_size', 'search.combo_id', 'search.goods_name', 'search.create_time', 'search.nickname', 'search.phone'],
        'record_export' => ['search.combo_id', 'search.goods_name', 'search.create_time', 'search.nickname', 'search.phone'],
        'delete' => ['combo_id'],
        'close' => ['combo_id'],
        'statistics' => ['search.combo_id', 'search.goods_name', 'search.create_time', 'search.nickname', 'search.phone'],
    ];

    public function sceneEdit() {
        return $this->only(['combo_id', 'name', 'skus', 'start_time', 'end_time']);
    }

    public function sceneSku() {
        return $this->only(['skus.sku_id', 'skus.goods_id', 'skus.combo_price', 'skus.buy_nums']);
    }
}
