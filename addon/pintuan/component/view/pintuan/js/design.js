var bargainListHtml = '<div class="goods-list-edit layui-form">' + 
  // '<div class="layui-form-item">'+
  //   '<label class="layui-form-label sm">商品来源</label>'+
  //     '<div class="layui-input-block">'+
  //       '<template v-for="(item,index) in goodsSources" v-bind:k="index">'+
  //         '<div v-on:click="data.sources=item.value" v-bind:class="{ \'layui-unselect layui-form-radio\' : true,\'layui-form-radioed\' : (data.sources==item.value) }"><i class="layui-anim layui-icon">&#xe643;</i><div>{{item.text}}</div></div>'+
  //       '</template>'+
  //     '</div>'+
  //   '</div>'+
    '<div class="layui-form-item">'+
			'<label class="layui-form-label sm">商品数量</label>'+
			'<div class="layui-input-block">'+
				'<input type="number" class="layui-input goods-account" v-on:keyup="shopNum" v-model="data.goodsCount"/>'+
			'</div>'+
		'</div>'+
    '<div class="layui-form-item">'+
			'<label class="layui-form-label sm"></label>'+
			'<div class="layui-input-block">'+
				'<template v-for="(item,index) in goodsNumber" v-bind:k="index">'+
					'<div v-on:click="data.goodsCount=item.value" v-bind:class="{ \'layui-unselect layui-form-radio\' : true,\'layui-form-radioed\' : (data.goodsCount==item.value) }"><i class="layui-anim layui-icon">&#xe643;</i><div>{{item.value}}</div></div>'+
				'</template>'+
			'</div>'+
		'</div>'+
	'</div>'+
'</div>'

 // '<p class="hint">商品数量选择 0 时，前台会自动上拉加载更多</p>';
 

var select_goods_list = []; //配合商品选择器使用
Vue.component("bargain-set", {
  template: bargainListHtml,
  data: function () {
  return {
    data: this.$parent.data,
    goodsSources: [
      // {
      //   text: "默认",
      //   value: "default"
      // },
      {
        text : "手动选择",
        value : "diy"
      }
    ],
    goodsNumber: [
      {
        value: '3'
      },
      {
        value: '6'
      },
      {
        value: '9'
      }
    ]
  }
  },
  created:function() {
    // this.data.sources = "diy"
    // this.data.goodsCount = "3"
    this.$parent.data.verify = this.verify;//加载验证方法
    console.log('????');
  },
  methods: {
    shopNum: function(){
			if (this.$parent.data.goodsCount > 10){
				layer.msg("商品数量最多为10");
				this.$parent.data.goodsCount = 10;
			}e
			if (this.$parent.data.goodsCount.length > 0 && this.$parent.data.goodsCount < 1) {
				layer.msg("商品数量不能小于0");
				this.$parent.data.goodsCount = 1;
			}
		},
  }
});


var infoHtml = '<div class="goods-list-edit layui-form">'+
  '<div class="goods-list-edit layui-form"> '+
    '<label class="layui-form-label sm">标题</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.name" class="layui-input" />'+
    '</div>'+
    '<label class="layui-form-label sm">文本内容</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.moreText" class="layui-input" />'+
    '</div>'+
    '<color v-bind:data="{ field : `textColor`, label : `字体颜色:` }"></color>'+
    '<color v-bind:data="{ field : `backgroundColor`, label : `背景颜色:` }"></color>'+
    '<label class="layui-form-label sm">背景图片：</label>\n' +
    '<div class="layui-input-block">\n' +
    '   <img-upload v-bind:data="{ data : data, field: \'backgroundImg\' }"></img-upload>\n' +
    '</div>\n' +
    '<label class="layui-form-label sm">跳转地址</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" disabled v-model="data.moreUrl" class="layui-input" />'+
    '</div>'+
  '</div>'+
'</div>'

Vue.component("info-list", {
  template: infoHtml,
  data: function () {
  return {
    data: this.$parent.data,
  }
  },
  created:function() {
    // this.data.sources = "diy"
    // this.data.goodsCount = "3"
    
    this.$parent.data.verify = this.verify;//加载验证方法
  },
  methods: {
    verify: function() {
      var res = { code : true, message : "" };
      if(this.data.name == '') {
        res.code = false
        res.message = '请输入拼团模块的标题'
        return res
      }
      if(this.data.moreText == '') {
        res.code = false
        res.message = '请输入拼团模块的文本内容'
        return res
      }
      if(this.data.moreUrl == '') {
        res.code = false
        res.message = '请输入拼团模块的跳转地址'
        return res
      }
      return res
    }
  }
});