@CHARSET "UTF-8";
.component-pintuan .preview-draggable{
  padding: 0;
}
.component-pintuan .pintuan-container{
  border-radius: 20px;
  padding: 10px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.component-pintuan .pintuan-head {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  webkit-align-items: center;
  align-items: center;
  margin-bottom: 15px;
}
.component-pintuan .pintuan-head .title-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.component-pintuan .pintuan-head .title-wrap .title-wrap-icon{
  width: 14px;
  height: 14px;
  margin-right: 5px;
}
.component-pintuan .pintuan-head .title-wrap .name {
  font-weight: bold;
  margin-right: 8px;
}
.component-pintuan .pintuan-head .more {
  display: flex;
  font-size: 12px;
  color: #F2280C;
}
.component-pintuan .pintuan-head .more .to{
  display: flex;
  align-items: center;
  justify-content: center;
  /* display: inline-block; */
  border-radius: 50%;
  border: 1px solid;
  width: 11px;
  height: 11px;
  margin-left: 3px;
}
.component-pintuan .list-wrap{
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}
.component-pintuan .list-wrap .item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 10px;
  width: 92px;
  background-color: white;
  border-radius: 10px 10px 0 0;
}
.component-pintuan .list-wrap .item .img-wrap img{
  border-radius: 10px 10px 0 0!important;
}
.component-pintuan .list-wrap .item .good-name{
  /* text-align: center; */
  margin: 10px 0 8px;
}
.component-pintuan .list-wrap .item .tuan-info{
  display: flex;
  width: 100%;
  background: rgba(246, 93, 114, 0.1);
  border-radius: 40px;
}
.component-pintuan .list-wrap .item .tuan-info div:first-child{
  height: 14px;
  line-height: 14px;
  border-radius: 40px;
  background: rgba(246, 93, 114, 1);
  box-sizing: border-box;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 500;
  color: #FFFFFF;
  display: inline-block;
  /* width: 30px; */
  align-self: baseline;
}
.component-pintuan .list-wrap .item .tuan-info div:last-child{
  height: 14px;
  line-height: 14px;
  font-size: 10px;
  font-weight: 500;
  padding: 0 1px;
  color: rgba(246, 93, 114, 1);
  display: inline-block;
  margin-left: 5px;
}
.component-pintuan .list-wrap .item .real-price {
  font-size: 12px;
  color: #898989;
  text-decoration: line-through;
  margin-top: 2px;
}
.component-pintuan .list-wrap .item .tuan-bottom{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.component-pintuan .list-wrap .item .good-price {
  font-size: 0;
  color: #F84346;
  font-weight: bolder;
  margin-top: 2px;
}
.component-pintuan .list-wrap .item .good-price span:first-child{
  font-size: 13px;
}
.component-pintuan .list-wrap .item .good-price span:last-child {
  font-size: 18px;
}
.component-pintuan .list-wrap .item .img-wrap img {
  width: 92px;
  height: 90px;
  padding: 0;
  margin: 0;
  border-radius: 10px;
}

.component-pintuan .list-wrap .item .new-price {
  font-size: 14px;
  text-align: center;
  /* display: block; */
}
.component-pintuan .list-wrap .item .old-price {
  font-size: 12px;
  text-align: center;
  color: #898989;
  text-decoration: line-through;
}



/* 编辑 */
.component-pintuan h3 {
  font-size: 14px;
  font-weight: 600;
  padding: 5px 10px 10px 10px;
}