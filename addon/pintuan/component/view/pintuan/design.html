<nc-component v-bind:data="data[index]" class="component-pintuan">

	<!-- 预览 -->
	<template slot="preview">
		<div class="pintuan-container" v-bind:style="`background-color:${data[index].backgroundColor};background-image:url('${data[index].backgroundImg}');`">
			<div class="pintuan-head">
				<div class="title-wrap">
					<img src="STATIC_EXT/diyview/img/pintuan-hot.png" class="title-wrap-icon"/>
					<span class="name" v-bind:style="`color: ${data[index].textColor};`">{{data[index].name}}</span>
				</div>
				<div class="more" v-bind:style="`color: ${data[index].textColor};`">{{data[index].moreText}}
					<span class="to" v-bind:style="`border-color: ${data[index].textColor};`"><i class="layui-icon layui-icon-right" v-bind:style="`font-size: 8px;color: ${data[index].textColor};border-color: ${data[index].textColor};`"></i></span>
				</div>
			</div>
			<div class="list-wrap">
				<div class="item" v-for="(item,index) in Array(5)">
					<div class="img-wrap">
						<img src="STATIC_EXT/diyview/img/crack_figure.png" />
					</div>
					<span class="good-name">商品名称商品名称</span>
					<div class="tuan-info">
						<div>6人团</div>
						<div>已开3团</div>
					</div>
					<div class="real-price">￥10.99</div>
					<div class="tuan-bottom">
						<div class="good-price">
							<span>￥</span>
							<span>0.01</span>
						</div>
						<i class="layui-icon layui-icon-add-circle" style="font-size: 20px; color:  rgba(246, 93, 114, 1);"></i>
					</div>

				</div>
			</div>
		</div>
	</template>

	<!-- 编辑 -->
	<template slot="edit">
		<div id="edit">
			<template>
				<!-- <bargain-set></bargain-set> -->
			</template>
			<h3>标题设置</h3>
			<div v-if="nc.lazyLoad">
				<info-list></info-list>
			</div>
		</div>
	</template>
	
	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/pintuan/css/design.css"></css>
		<js src="{$resource_path}/pintuan/js/design.js"></js>
		
	</template>
	
</nc-component>