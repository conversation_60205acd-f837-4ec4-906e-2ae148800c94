<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\pintuan\api\controller;

use addon\pintuan\model\Pintuan as PintuanModel;
use app\api\controller\BaseApi;
use app\model\shop\Shop as ShopModel;
use addon\pintuan\model\Poster;

/**
 * 拼团商品
 */
class Goods extends BaseApi
{
	
	/**
	 * 基础信息
	 */
	public function info()
	{
		$sku_id = isset($this->params['sku_id']) ? $this->params['sku_id'] : 0;
		$pintuan_id = isset($this->params['pintuan_id']) ? $this->params['pintuan_id'] : 0;
		if (empty($sku_id)) {
			return $this->response($this->error('', 'REQUEST_SKU_ID'));
		}
		if (empty($pintuan_id)) {
			return $this->response($this->error('', 'REQUEST_PINTUAN_ID'));
		}
		$goods = new PintuanModel();
		$condition = [
			[ 'sku.sku_id', '=', $sku_id ],
			[ 'ppg.pintuan_id', '=', $pintuan_id ],
			[ 'pp.status', '=', 1 ]
		];
		$info = $goods->getPintuanGoodsDetail($condition);
		return $this->response($info);
	}
	
	/**
	 * 拼团商品详情信息
	 */
	public function detail()
	{
		$id = isset($this->params['id']) ? $this->params['id'] : 0;
		if (empty($id)) {
			return $this->response($this->error('', 'REQUEST_ID'));
		}
		
		$pintuan_model = new PintuanModel();
		$condition = [
			[ 'ppg.id', '=', $id ],
			[ 'pp.status', '=', 1 ]
		];
		$goods_sku_detail = $pintuan_model->getPintuanGoodsDetail($condition);
		$goods_sku_detail = $goods_sku_detail['data'];
		$res['goods_sku_detail'] = $goods_sku_detail;

//		店铺信息
		$shop_model = new ShopModel();
		$shop_info = $shop_model->getShopInfo([ [ 'site_id', '=', $goods_sku_detail['site_id'] ] ], 'site_id,site_name,is_own,logo,avatar,banner,seo_description,qq,ww,telephone,shop_desccredit,shop_servicecredit,shop_deliverycredit,shop_baozh,shop_baozhopen,shop_baozhrmb,shop_qtian,shop_zhping,shop_erxiaoshi,shop_tuihuo,shop_shiyong,shop_shiti,shop_xiaoxie,shop_sales,sub_num');
		
		$shop_info = $shop_info['data'];
		$res['shop_info'] = $shop_info;
		
		return $this->response($this->success($res));
	}
	
	public function page()
	{
		
		$site_id = isset($this->params['site_id']) ? $this->params['site_id'] : 0;
		$page = isset($this->params['page']) ? $this->params['page'] : 1;
		$page_size = isset($this->params['page_size']) ? $this->params['page_size'] : PAGE_LIST_ROWS;
		
		$condition = [
			[ 'pp.status', '=', 1 ],// 状态（0正常 1活动进行中  2活动已结束  3失效  4删除）
			[ 'sku.stock', '>', 0 ],
			[ 'sku.goods_state', '=', 1 ],
			[ 'sku.verify_state', '=', 1 ],
			[ 'sku.is_delete', '=', 0 ],
		];
		if (!empty($site_id)) {
			$condition[] = [ 'sku.site_id', '=', $site_id ];
		}
		
		$pintuan_model = new PintuanModel();
		$list = $pintuan_model->getPintuanGoodsPageList($condition, $page, $page_size);
		
		return $this->response($list);
	}
	
	/**
	 * 获取商品海报
	 */
	public function poster()
	{
		if (!empty($qrcode_param)) return $this->response($this->error('', '缺少必须参数qrcode_param'));
		
		$promotion_type = 'pintuan';
		$qrcode_param = json_decode($this->params['qrcode_param'], true);
		$qrcode_param['source_member'] = $qrcode_param['source_member'] ?? 0;
		$poster = new Poster();
		$res = $poster->goods($this->params['app_type'], $this->params['page'], $qrcode_param, $promotion_type);
		return $this->response($res);
	}
}