{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>团购活动列表展示商品的团购相关信息</li>
		</ul>
	</div>
</div>

<!-- 筛选面板 -->
<div class="ns-single-filter-box">
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="site_name" placeholder="请输入店铺名称" autocomplete="off" class="layui-input">
		</div>
		
		<div class="layui-input-inline">
			<input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- <div class="ns-screen layui-collapse">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">筛选</h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">店铺名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="site_name" placeholder="请输入店铺名称" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
			
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div> -->

<div class="layui-tab ns-table-tab" lay-filter="status">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		<li data-status="1">未开始</li>
		<li data-status="2">进行中</li>
		<li data-status="3">已结束</li>
	</ul>
	
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="groupbuy_list" lay-filter="groupbuy_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods">
	<div class="ns-table-tuwen-box">
		<div class="ns-img-box">
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
		</div>
		<div class="ns-font-box">
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{#  if(d.status == 1){  }}
	<span style="color: red">未开始</span>
	{{#  }else if(d.status == 2){  }}
	<span style="color: green">进行中</span>
	{{#  }else if(d.status == 3){  }}
	<span style="color: gray">已结束</span>
	{{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		{{# if(d.status == 1){ }}
		<a class="layui-btn" lay-event="detail">详情</a>
		<a class="layui-btn" lay-event="del">删除</a>
		{{# }else if(d.status == 2){ }}
		<a class="layui-btn" lay-event="detail">详情</a>
		<a class="layui-btn" lay-event="close">结束</a>
		{{# }else if(d.status == 3){ }}
		<a class="layui-btn" lay-event="detail">详情</a>
		<a class="layui-btn" lay-event="del">删除</a>
		{{# } }}
	</div>
</script>
{/block}

{block name="script"}
<script>
        layui.use(['element', 'form'], function() {
            var table,
                element = layui.element,
				form = layui.form,
                repeat_flag = false; //防重复标识

		table = new Table({
			elem: '#groupbuy_list',
			url: ns.url("groupbuy://admin/groupbuy/lists"),
			cols: [
				[{
					title: '团购商品',
					unresize: 'false',
					width: '20%',
					templet: '#goods'
				},{
					field: 'site_name',
					title: '商家名称',
					unresize: 'false',
					width: '13%',
				}, {
					field: 'goods_price',
					title: '商品原价',
					unresize: 'false',
					width: '11%',
					align: 'right',
					templet: function(data) {
						return '￥'+ data.goods_price;
					}
				}, {
					field: 'groupbuy_price',
					title: '团购价格',
					unresize: 'false',
					width: '10%',
					align: 'right',
					templet: function(data) {
						return '<span>￥'+ data.groupbuy_price +'</span>';
					}
				}, {
					field: 'buy_num',
					title: '起购量',
					unresize: 'false',
					width: '8%'
				}, {
					title: '活动时间',
					unresize: 'false',
					width: '20%',
					templet: '#time'
				}, {
					title: '状态',
					unresize: 'false',
					width: '8%',
					templet: '#status'
				}, {
					title: '操作',
					unresize: 'false',
					width: '10%',
					templet: '#operation'
				}]
			],

		});

		//监听Tab切换
		element.on('tab(status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload( {
				page: {
					curr: 1
				},
				where: {
					'status': status
				}
			});
		});
		
		// 搜索
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
	       
        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'del': //删除
                    del(data.groupbuy_id,data.site_id);
                    break;
                case 'close': //结束
                    close(data.groupbuy_id,data.site_id);
                    break;
				case 'detail': //结束
				    location.href = ns.url("groupbuy://admin/groupbuy/detail", {groupbuy_id: data.groupbuy_id});
				    break;
            }
        });
           
		/**
		 * 删除
		 */
		function del(groupbuy_id,site_id){
			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('确定要删除吗?', function() {
				$.ajax({
					url: ns.url("groupbuy://admin/groupbuy/delete"),
					data: {groupbuy_id,site_id},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
		
		/**
		 * 结束
		 */
		function close(groupbuy_id,site_id){
			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('确定要结束该团购吗?', function() {
				$.ajax({
					url: ns.url("groupbuy://admin/groupbuy/close"),
					data: {groupbuy_id,site_id},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
	});
</script>
{/block}