<?php

/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */

/**
 * 用于检测业务代码死循环或者长时间阻塞等问题
 * 如果发现业务卡死，可以将下面declare打开（去掉//注释），并执行php start.php reload
 * 然后观察一段时间workerman.log看是否有process_timeout异常
 */
//declare(ticks=1);

use \GatewayWorker\Lib\Gateway;
use Workerman\MySQL\Connection;

/**
 * 主逻辑
 * 主要是处理 onConnect onMessage onClose 三个方法
 * onConnect 和 onClose 如果不需要可以不用实现并删除
 */
class Events
{
    protected static $db;
    protected static $prefix;

    /**
     * 进行启动时操作
     *
     * @param mixed $businessWorker
     * @return void
     */
    public static function onWorkerStart($businessWorker)
    {
        $config = require_once __DIR__ . '/config.php';
        $config = $config['db'];
        self::$prefix = $config['prefix'];
        self::$db = new Connection($config['host'], $config['port'], $config['user'], $config['passwd'], $config['dbname']);
    }

    /**
     * 当客户端连接时触发
     * 如果业务不需此回调可以删除onConnect
     * 
     * @param int $client_id 连接id
     */
    public static function onConnect($client_id)
    {
        $message = json_encode(['type' => 'init', 'data' => ['client_id' => $client_id]]);
        // 向当前client_id发送数据 
        Gateway::sendToClient($client_id, $message);
    }

    /**
     * 当客户端发来消息时触发
     * @param int $client_id 连接id
     * @param mixed $message 具体消息
     */
    public static function onMessage($client_id, $message)
    {
        // 向所有人发送 
        // Gateway::sendToAll("$client_id said $message\r\n");
    }

    /**
     * 当用户断开连接时触发
     * @param int $client_id 连接id
     */
    public static function onClose($client_id)
    {
        // 通过Client_id 去找相关会员或者客服
        $member = self::$db->select('id, online')->from(self::$prefix . 'servicer_member')->where("client_id= '$client_id' ")->row();
        if (!empty($member)) {
            if ($member['online'] == 1) {
                self::$db->update(self::$prefix . 'servicer_member')->cols(['online' => 0])->where("client_id= '$client_id' ")->query();
                return;
            }
        }

        $servicer = self::$db->select('id, online')->from(self::$prefix . 'servicer')->where("client_id= '$client_id' ")->row();
        if (!empty($servicer)) {
            if ($servicer['online'] == 1) {
                self::$db->update(self::$prefix . 'servicer')->cols(['online' => 0])->where("client_id= '$client_id' ")->query();
                return;
            }
        }
    }
}
