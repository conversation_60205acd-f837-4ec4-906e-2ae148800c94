{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "1dde1245f9d2a3136070907f6b983547", "packages": [{"name": "workerman/gateway-worker", "version": "v3.0.16", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "f153c28c76cf60cc882d6b66f89b622176268fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/f153c28c76cf60cc882d6b66f89b622176268fb7", "reference": "f153c28c76cf60cc882d6b66f89b622176268fb7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"workerman/workerman": ">=3.5.0"}, "type": "library", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "time": "2020-06-04T02:58:35+00:00"}, {"name": "workerman/mysql", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/walkor/mysql.git", "reference": "28272aa68f9ea1a482f9bb0cf709d169f772d228"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/mysql/zipball/28272aa68f9ea1a482f9bb0cf709d169f772d228", "reference": "28272aa68f9ea1a482f9bb0cf709d169f772d228", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pdo": "*", "ext-pdo_mysql": "*", "php": ">=5.3"}, "type": "library", "autoload": {"psr-4": {"Workerman\\MySQL\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Long-living MySQL connection for daemon.", "homepage": "http://www.workerman.net", "keywords": ["mysql", "pdo", "pdo_mysql"], "time": "2019-08-02T10:43:09+00:00"}, {"name": "workerman/workerman", "version": "v4.0.6", "source": {"type": "git", "url": "https://github.com/walkor/Workerman.git", "reference": "14964ed1f3655e10f8bd0bd45e78fb75104fc9cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Workerman/zipball/14964ed1f3655e10f8bd0bd45e78fb75104fc9cf", "reference": "14964ed1f3655e10f8bd0bd45e78fb75104fc9cf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "time": "2020-06-12T16:21:56+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "1.1.0"}