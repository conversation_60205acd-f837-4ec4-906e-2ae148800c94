<!DOCTYPE html>
<html>

<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title>{$menu_info['title']|default=""} - {$store_info['store_name']|default="多商户客服"}</title>
	<meta name="keywords" content="$shop_info['seo_keywords']}">
	<meta name="description" content="$shop_info['seo_description']}">
	<link rel="icon" type="image/x-icon" href="__STATIC__/img/shop_bitbug_favicon.ico" />
	<link rel="stylesheet" type="text/css" href="STATIC_CSS/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="SERVICER_CSS/common.css" />
	<script src="__STATIC__/js/jquery-3.1.1.js"></script>
	<script src="__STATIC__/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function () { });

		window.ns_url = {
			baseUrl: "ROOT_URL/",
			route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
		};
	</script>
	<script src="__STATIC__/js/common.js"></script>
	<script src="SHOP_JS/common.js"></script>
	<script src="STATIC_JS/vue.js"></script>
	<style>
		.ns-calendar {
			background: url("__STATIC__/img/ns_calendar.png") no-repeat center / 16px 16px;
		}

		.servicer-chat-base {
			width: 100%;
			height: 100%;
			padding-top: 100px;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.servicer-chat-base img {
			width: 100px;
			height: 100px;
			border-radius: 50%;
		}

		.servicer-chat-base-name {
			width: 100%;
			text-align: center;
			margin-top: 30px;
		}

		.servicer-chat-base-code {
			width: 100%;
			text-align: center;
			margin-top: 30px;
		}

		.layui-nav {
			background: #ffffff;
		}

		.layui-nav-item a:hover {
			color: #000000;
		}
	</style>
	{block name="resources"}{/block}
</head>

<body>
	{block name="body"}
	<div class="layui-layout layui-layout-admin">
		{block name='side_menu'}
		<div class="layui-side ns-second-nav">
			<div class="layui-side-scroll">
				<div class="servicer-chat-base">
					<img layer-src src="{:img($shop_info['logo'])}" onerror=src="SHOP_IMG/default_shop.png">
					<div class="servicer-chat-base-name">
						售后客服：{$user_info['username']}
					</div>
				</div>
			</div>
		</div>
		{/block}
		<!-- 面包屑 -->
		{block name='crumbs'}
		<div class="ns-crumbs">
			<span class="layui-breadcrumb" lay-separator="-">
			</span>
			<ul class="layui-nav layui-layout-left">
				<li class="layui-nav-item">
					<a href="index">
						<span>在线聊天</span>
					</a>
				</li>
				<li class="layui-nav-item">
					<a href="history">
						<span>聊天记录</span>
					</a>
				</li>
				<span class="layui-nav-bar" style="left: 34px; top: 55px; width: 0px; opacity: 0;"></span>
			</ul>
			<div class="layui-header ns-user">
				<!-- 账号 -->
				<ul class="layui-nav">
					<li class="layui-nav-item">
						<div class="ns-img-box" id="logo_bot">
							<img layer-src src="{:img($shop_info['logo'])}" onerror=src="SHOP_IMG/default_shop.png">
						</div>

						<a href="javascript:;">
							{$user_info['username']}</a>
						<dl class="layui-nav-child">
							<!-- <dd class="ns-reset-pass" onclick="resetPassword();">
								<a href="javascript:;">修改密码</a>
							</dd> -->
							<dd>
								<a href="{:addon_url('servicer://servicer/login/logout')}" class="login-out">退出登录</a>
							</dd>
						</dl>
					</li>
				</ul>
			</div>
		</div>
		{/block}

		<div class="ns-body layui-body">
			<!-- 四级菜单 -->
			{block name='head'}
			{/block}

			<!-- 内容 -->
			<div class="ns-body-content">
				{block name="main"}{/block}
			</div>
		</div>
	</div>

	<!-- 重置密码弹框html -->
	<div class="layui-form" id="reset_pass" style="display: none;">
		<div class="layui-form-item">
			<label class="layui-form-label"><span class="required">*</span>原密码</label>
			<div class="layui-input-block">
				<input type="password" id="old_pass" name="old_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');"
					onblur="this.setAttribute('readonly',true);">
				<span class="required"></span>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="new_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');"
					onblur="this.setAttribute('readonly',true);">
				<span class="required"></span>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" id="repeat_pass" name="repeat_pass" required class="layui-input ns-len-mid" maxlength="18" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');"
					onblur="this.setAttribute('readonly',true);">
				<span class="required"></span>
			</div>
		</div>

		<div class="ns-form-row">
			<button class="layui-btn ns-bg-color" onclick="repass()">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="closePass()">返回</button>
		</div>
	</div>

	<script type="text/javascript">
		/**
		 * 重置密码
		 */
		var index;

		function resetPassword() {
			index = layer.open({
				type: 1,
				content: $('#reset_pass'),
				offset: 'auto',
				area: ['650px']
			});

			setTimeout(function () {
				$(".ns-reset-pass").removeClass('layui-this');
			}, 1000);
		}

		var repeat_flag = false;

		function repass() {
			var old_pass = $("#old_pass").val();
			var new_pass = $("#new_pass").val();
			var repeat_pass = $("#repeat_pass").val();

			if (old_pass == '') {
				$("#old_pass").focus();
				layer.msg("原密码不能为空");
				return;
			}

			if (new_pass == '') {
				$("#new_pass").focus();
				layer.msg("密码不能为空");
				return;
			} else if ($("#new_pass").val().length < 6) {
				$("#new_pass").focus();
				layer.msg("密码不能少于6位数");
				return;
			}
			if (repeat_pass == '') {
				$("#repeat_pass").focus();
				layer.msg("密码不能为空");
				return;
			} else if ($("#repeat_pass").val().length < 6) {
				$("#repeat_pass").focus();
				layer.msg("密码不能少于6位数");
				return;
			}
			if (new_pass != repeat_pass) {
				$("#repeat_pass").focus();
				layer.msg("两次密码输入不一样，请重新输入");
				return;
			}

			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: "POST",
				dataType: 'JSON',
				url: ns.url("servicer://servicer/login/modifypassword"),
				data: {
					"old_pass": old_pass,
					"new_pass": new_pass
				},
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						layer.close(index);
						location.reload();
					}
				}
			});
		}

		function closePass() {
			layer.close(index);
		}

		$(document).ready(function(){
			if (window.location.href.lastIndexOf("/history") == -1){
				$(".layui-layout-left .layui-nav-item:eq(0) span").addClass("ns-text-color");
			}else{
				$(".layui-layout-left .layui-nav-item:eq(1) span").addClass("ns-text-color");
			}
		});
	</script>
	{/block}
	{block name="script"}
	{/block}
</body>

</html>