/* 公共 */
.ns-dis-input{background-color: #f7f7f7; cursor: no-drop;}
.ns-dis-input:hover{border-color: #e6e6e6 !important;}
/* 头部 */
.apply-header{height: 80px;line-height: 80px;border-bottom: 1px solid #EFEFEF;}
.apply-header .apply-header-box{position: relative;display: flex;justify-content: space-between;align-items: center;width: 1200px;margin: auto;}
.apply-header .apply-header-title{color: #5D5D5D;}
.layui-nav .layui-nav-item a{max-width: 100px;}
.layui-nav .layui-nav-item a, .layui-nav .layui-nav-item a:hover{color: #5D5D5D;}
.apply-header .layui-nav{padding-left: 0;position:relative !important;height: 80px;background-color: transparent;}
.layui-nav .layui-nav-more{border-top-color: #5D5D5D;}
.layui-nav .layui-nav-mored{border-color: transparent transparent #5D5D5D;}
.phone{margin-left: auto;}
.apply-header .layui-nav .layui-nav-bar{width: 0 !important;}
.apply-body{margin: 80px 0;display: flex;justify-content: center;}
/* 选择开店方式*/
.shop-empty .empty-title{height:34px;font-size:34px;font-weight:400;color:rgba(93,93,93,1);}
.shop-empty .empty-content{display: flex;margin-top: 80px;justify-content: center;}
.shop-empty .empty-content li{display: flex;align-items: center;flex-direction: column;margin-left: 30px;width:190px;height:332px;background:rgba(244,244,244,1);border:1px solid rgba(233,233,233,1);border-radius:2px;}
.shop-empty .empty-content li:first-of-type{margin-left: 0;}
.shop-empty .empty-content .empty-img-box{margin-top: 27px;margin-bottom: 27px;width:121px;height:121px;line-height: 121px;text-align: center;background:rgba(255,255,255,1);border-radius:50%;}
.shop-empty .empty-content .empty-content-title{margin-bottom: 14px;font-size:20px;font-weight:400;color:rgba(58,57,57,1);}
.shop-empty .empty-content .empty-content-desc{margin-bottom: 35px;font-weight:400;color:rgba(155,155,154,1);}
/* 入驻协议*/
.settlement-agreement{width: 960px;margin-bottom: 50px;}
.settlement-agreement h2:first-of-type{margin-bottom: 80px;height:34px;font-size:34px;font-weight:400;color:rgba(93,93,93,1);text-align: center;}
.settlement-agreement h2:nth-child(2){margin-bottom: 30px;height:20px;font-size:20px;font-weight:400;color:rgba(0,0,0,1);}
.settlement-agreement .apply-btn-box{margin-top: 30px;text-align: center}
.settlement-agreement .agreement-foot{
    text-align: center;
    margin-top: 30px;
}
.apply-btn-box{margin-top: 40px;text-align: center}
/* 选择店铺等级 */
.store-level{padding: 0 50px 50px;margin: auto;display: flex;flex-wrap: wrap;justify-content: center;}
.store-level > li:first-of-type{margin-left: 0;}
.store-level > li{margin-bottom: 30px;margin-left: 20px;padding: 50px 35px 30px;width: 260px;text-align: center;background-color: #fff;box-sizing: border-box;border: 1px solid #E9E9E9;border-radius: 2px;}
.store-level > li:hover{border-color: transparent; box-shadow: 0 0 20px 0 rgba(0,0,0,.07);}
.store-level .group_name{margin: 10px;font-size: 18px;font-weight: 400;line-height: 25px;color: #323233;}
.store-level .remark{display: inline-block;margin: 0 10px 20px;font-size: 12px;line-height: 19px;color: #646566;}
.store-level-sublevel{padding-top: 20px;border-top: 1px solid #f2f2f2;}
.store-level-sublevel li{text-align: left;height: 28px;line-height: 28px;color: #646566;}
.store-level-sublevel li .is-checked{color: #ccc;}
.store-level button{margin-top: 25px;}
/* 申请类型*/
.application-type h2{margin-bottom: 80px;height:34px;font-size:34px;font-weight:400;color:rgba(93,93,93,1);}
.application-type .cert-type{display: flex;justify-content: center;}
.application-type .cert-type li{margin-left: 40px;text-align: center;}
.application-type .cert-type li:first-of-type{margin-left: 0;}
.application-type .cert-type li{font-size: 16px;color: #D0D0D0;cursor: pointer;}
.application-type .cert-type .cert-img-box{overflow: hidden;width: 81px;height: 81px;margin-bottom: 10px;}
.application-type .cert-type .cert-img-box img{position: relative;}
.application-type li:first-of-type img{left: -91px;}
.application-type .apply-btn-box{margin-top: 150px;}
h2.apply-h2-title{margin-bottom: 80px;height:34px;font-size:34px;font-weight:400;color:rgba(93,93,93,1);text-align: center;}
/* 审核*/
.audit-status{width: 1200px;}
.audit-status .status{display: flex;flex-direction: column;justify-content: center;align-items: center;margin-bottom: 100px;}
.audit-status .status span{font-weight:400;color:rgba(93,93,93,1);}
.audit-status .status-pic{margin-right: 5px;margin-bottom: 10px;width: 100px;height: 100px;line-height: 100px;text-align: center;}
.audit-status .status-pic img{max-height: 100%;max-width: 100%;}
/* 开店成功*/
.shop-succeey{position: relative;width: 1200px;top: 0;bottom: 0;left: 0;right: 0;margin: auto;padding: 68px 0 0;text-align: center;}
.shop-succeey p{font-size: 16px;margin-top: 20px;}