{extend name="addon/servicer/servicer/view/base.html"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="ADMIN_CSS/iconfont.css" />
{/block}
{block name="main"}
<style>
	.ns-body-content{padding: 0 !important;}
	.chat-index-box{width: 100%;display: flex;align-items: center;position: relative;}
	.chat-index-box .sunblind{width: 100%;height: 50px;background: #ffffff;position: absolute;left: 0;top: 0;padding: 0 30px;box-sizing: border-box;line-height: 50px;z-index: 99;color: #ff596d;}
	.chat-index-box .chat-content-left{width: 200px;overflow: hidden;border: 1px solid #E5E5E5;}
	.chat-content-left .left-content{width: 220px;height: 885px;overflow-y: auto;}
	.chat-content-left .left-content .content-item{width: 200px;position: relative;}
	.chat-content-left .left-content .content-item .item-title{width: 100%;height: 53px;background: #f3f3f3;padding: 0 10px;box-sizing: border-box;line-height: 53px;border-bottom: 1px solid #e5e5e5;}
	.chat-content-left .left-content .content-item .item-title .layui-nav-more{content: '';width: 0;height: 0;border-style: solid dashed dashed;border-color: #fff transparent transparent;overflow: hidden;cursor: pointer;transition: all .2s;-webkit-transition: all .2s;position: absolute;top: 27px;right: 20px;margin-top: -3px;border-width: 6px;border-top-color: #000000;}
	.chat-content-left .left-content .content-item .item-title .layui-nav-more.layui-nav-mored{margin-top: -9px;border-style: dashed dashed solid;right: 20px;border-bottom-color: #000000;border-top-color: transparent;}
	.chat-content-left .left-content .content-item .content-people{width: 100%;height: 70px;background: #ffffff;display: flex;justify-content: space-between;align-items: center;padding: 0 30px 0 15px;box-sizing: border-box;border-bottom: 1px solid #e5e5e5;cursor: pointer;}
	.content-item .content-people img{width: 40px;height: 40px;border-radius: 50%;}
	.content-item .content-people .people-info{width: calc(100% - 40px);height: 100%;padding-left: 10px;display: flex;flex-direction: column;justify-content: center;box-sizing: border-box;}
	.content-item .content-people .people-info .name{width: 100%;display: flex;justify-content: space-between;align-items: center;}
	.content-item .content-people .people-info .name .left{font-size: 14px;max-width: calc(100% - 50px);overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
	.content-item .content-people .people-info .name .right{font-size: 12px;color: #838383;}
	.content-item .content-people .people-info .message{width: calc(100% - 10px);font-size: 12px;color: #838383;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;margin-top: 5px;}
	.chat-content-center{width: calc(100% - 220px);height: 885px;}
	.chat-content-center-top{width: 100%;height: 53px;background: #ffffff;border-bottom: 1px solid #E5E5E5;display: flex;align-items: flex-end;padding: 0 10px;box-sizing: border-box;overflow-x: auto;}
	.chat-content-center-top .chat-people{display: inline-block;padding: 6px 12px;background: #F3F3F3;border: 1px solid #E5E5E5;box-sizing: border-box;position: relative;}
	.chat-content-center-top .chat-people .chat-people-box{width: 100%;height: 100%;display: flex;align-items: center;}
	.chat-content-center-top .chat-people.active{background: #ffffff;border-bottom: 1px solid #ffffff;}
	.chat-content-center-top .chat-people .name{max-width: 100px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
	.chat-content-center-top .chat-people .close{background: #f5f5f5;border-radius: 50%;padding: 3px;color: red;margin-left: 6px;}
	.chat-content-center-bottom-box{position: relative;width: 100%;height: calc(885px - 53px);display: flex;}
	.chat-content-center-bottom-left{width: 100%;height: 100%;overflow: hidden;border-right: 1px solid #E5E5E5;border-bottom: 1px solid #E5E5E5;}
	.chat-content-center-bottom{width: 100%;height: 100%;padding: 0 30px 0 15px;box-sizing: border-box;overflow-y: scroll;}
	.chat-content-center-bottom .chat-massage-box{width: 100%;background: #ffffff;display: flex;align-items: flex-start;margin-top: 20px;}
	.chat-content-center-bottom .chat-massage-box.active{width: 100%;background: #ffffff;display: flex;align-items: flex-start;margin-top: 20px;flex-direction: row-reverse;}
	.chat-content-center-bottom .chat-massage-box.active .name{text-align: right;}
	.chat-content-center-bottom .chat-massage-box:last-child{margin-bottom: 20px;}
	.chat-content-center-bottom .chat-massage-box img{width: 40px;height: 40px;border-radius: 50%;}
	.chat-content-center-bottom .chat-massage-box .message-info{width: 80%;padding-left: 10px;box-sizing: border-box;}
	.chat-content-center-bottom .chat-massage-box.active .message-info{padding-right: 10px;}
	.chat-content-center-bottom .chat-massage-box .message-info .name{color: #4685FD;font-size: 14px;margin-bottom: 10px;}
	.chat-content-center-bottom .chat-massage-box .message-info .name.active{color: #3DB272;}
	.chat-content-center-bottom .chat-massage-box .message-info .message{font-size: 12px;}
	.chat-content-center-bottom .chat-massage-box.active .message-info .message{font-size: 12px;text-align: right;}
	.chat-message-box{width: 100%;height: 200px;display: flex;flex-direction: column;}
	.chat-message-box.active{flex-direction:column-reverse;}
	.chat-message-box .chat-top{width: 100%;height: 50px;border: 1px solid #E5E5E5;border-left: none;border-right: none;display: flex;align-items: center;padding-left: 10px;}
	.chat-message-box .chat-top .layui-btn{background: #ffffff;height: auto;line-height: inherit;padding: 0;}
	.chat-message-box .chat-top .iconfont{padding: 10px;font-size: 20px;color: #A9A7A8;}
	.chat-message-box textarea{width: 100%;height: 100px;border: none;}
	.chat-message-box .send-box{width: 100%;height: 50px;display: flex;flex-direction: column;align-items: flex-end;}
	.chat-message-box .send-box .send{display: inline-block;padding: 0 10px;font-size: 14px;height: 28px;background: #E5E5E5;border-radius: 4px;margin-right: 20px;line-height: 28px;text-align: center;color: #ffffff;margin-top: 11px;}
	.chat-message-box .send-box .send.active{background: red;}
	.chat-content-center-bottom-right{width: 40%;height: 100%;display: flex;flex-direction: column;align-items: center;}
	.chat-content-center-bottom-right .goods-box{}
	.chat-content-center-bottom-right .goods-info-title{width: 100%;line-height: 50px;font-size: 14px;text-align: center;}
	.chat-content-center-bottom-right .goods-info-box{display: flex;flex-direction: column;align-items: center;border: 1px solid #E5E5E5;box-sizing: border-box;padding: 10px 15px;border-radius: 10px;}
	.chat-content-center-bottom-right .goods-info-box img{width: 200px;height: 200px;}
	.chat-content-center-bottom-right .goods-info-box .goods-price{width: 200px;line-height: 35px;color: red;font-size: 16px;}
	.chat-content-center-bottom-right .goods-info-box .goods-name{width: 200px;font-size: 14px;font-weight: 600;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;}
	.chat-content-center-bottom-right .goods-info-box .goods-desc{width: 200px;font-size: 12px;margin-top: 5px;color: #838383;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;line-clamp: 3;-webkit-box-orient: vertical;}
	.chat-content-center-bottom-right .order-box{}
	.chat-content-center-bottom-right .order-info-title{width: 100%;line-height: 50px;font-size: 14px;text-align: center;}
	.chat-content-center-bottom-right .order-info-box{display: flex;align-items: center;border: 1px solid #E5E5E5;box-sizing: border-box;padding: 10px 15px;border-radius: 10px;}
	.chat-content-center-bottom-right .order-info-box img{width: 90px;height: 90px;}
	.chat-content-center-bottom-right .order-info-box .order-info{width: 200px;margin-left: 10px;height: 90px;display: flex;flex-direction: column;justify-content: space-between}
	.chat-content-center-bottom-right .order-info-box .follow-color{color: red;}
	.message .goods-info-box{width: 250px;float: left;text-align: left;}
	.active .message .goods-info-box{width: 250px;float: right;text-align:left;}
	.message .goods-info-box img{width: 200px;height: 200px;}
	.goods-info-box{display: flex;flex-direction: column;align-items: center;border: 1px solid #E5E5E5;box-sizing: border-box;padding: 10px 15px;border-radius: 10px;}
	.goods-info-box img{width: 200px;height: 200px;}
	.goods-info-box .goods-price{width: 200px;line-height: 35px;color: red;font-size: 16px;}
	.goods-info-box .goods-name{width: 200px;font-size: 14px;font-weight: 600;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;}
	.goods-info-box .goods-desc{width: 200px;font-size: 12px;margin-top: 5px;color: #838383;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;line-clamp: 3;-webkit-box-orient: vertical;}
	.message .order-info-box{display: flex;align-items: center;border: 1px solid #E5E5E5;box-sizing: border-box;padding: 10px 15px;border-radius: 10px;margin-top: 10px;}
	.message .order-info-box img{width: 90px;height: 90px;}
	.message .order-info-box .order-info{width: 200px;margin-left: 10px;height: 90px;display: flex;flex-direction: column;justify-content: space-between}
	.message .order-info-box .follow-color{color: red;}
	.content-people-box{position: relative;overflow: auto;max-height: 690px;}
	.content-people-box:hover::-webkit-scrollbar-thumb, .chat-content-center-bottom:hover::-webkit-scrollbar-thumb{background-color: rgba(0, 0, 0, .2);}
	.content-people-box::-webkit-scrollbar, .chat-content-center-bottom::-webkit-scrollbar{background-color: transparent;width: 8px;transition: all .3s;}
	.content-people-box::-webkit-scrollbar-track, .chat-content-center-bottom::-webkit-scrollbar-track{background-color: transparent;width: 8px;}
	.content-people-box::-webkit-scrollbar-thumb, .chat-content-center-bottom::-webkit-scrollbar-thumb{border-radius: 20px;background-color: transparent;}
</style>
<div class="chat-index-box" id="chatIndexBox">
	<div class="chat-content-left">
		<div class="left-content">
<!-- 			<div class="content-item">
				<div class="item-title">
					<span class="layui-nav-more layui-nav-mored">

					</span>
					<span>
						正在联系 {{onlineMembers?onlineMembers.length:0}}/{{numberTotal}}
					</span>
				</div>
				<div class="content-people-box">
					<div class="content-people" v-for="(item,index) in onlineMembers" :key="index" @click="openChat(item)">
						<img :src="item.headimg">
						<div class="people-info">
							<div class='name'>
								<div class="left">
									{{item.member_name}}
								</div>
							</div>
						</div>
					</div>
				</div>

			</div> -->
			<div class="content-item">
				<div class="item-title">
					<span class="layui-nav-more">

					</span>
					<span>
						历史联系 {{offlineMembers?offlineMembers.length:0}}/{{numberTotal}}
					</span>
				</div>
				<div class="content-people-box">
					<div class="content-people" v-for="(item,index) in offlineMembers" :key="index" @click="openChat(item)">
						<img :src="item.headimg">
						<div class="people-info">
							<div class='name'>
								<div class="left">
									{{item.member_name}}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="chat-content-center">
		<div class="chat-content-center-top">
			<div class="chat-people" :class="item.select?'active':''" @click="selectTab(item)" v-for="(item,index) in selectMemberList" :key="index">
				<div class="chat-people-box">
					<div class='name'>
						{{item.member_name}}
					</div>
					<div class="close iconfont iconclose" @click="closeChat(item,index)">

					</div>
				</div>
			</div>
		</div>
		<div class="chat-content-center-bottom-box">
			<div class="chat-content-center-bottom-left">
				<div class="chat-content-center-bottom">
					<div class="chat-massage-box" :class="item.type==1?'active':''" v-for="(item,index) in chatMessageList" :key="index">
						<img :src="item.type==1?shopInfo.logo:selectMember.headimg">
						<div class="message-info">
							<div class='name'>
								<span>
									{{item.type==1?shopInfo.site_name:selectMember.member_name}}
								</span>
								<span>
									{{item.create_day}} {{item.create_time}}
								</span>
							</div>
							<div class="message">
								<chat-goods v-if="item.goods_sku_id" :sku-id="item.goods_sku_id"></chat-goods>
								<chat-order v-else-if="item.order_id" :order-id="item.order_id"> </chat-order>
								<span v-else>
									{{item.type==1?item.servicer_say:item.consumer_say}}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

	</div>
</div>
<script type="text/javascript">
	layui.use('upload', function() {
		var upload = layui.upload;

		//执行实例
		var uploadInst = upload.render({
			elem: '#test1' //绑定元素
				,
			url: '/upload/' //上传接口
				,
			done: function(res) {
				//上传完毕回调
			},
			error: function() {
				//请求异常回调
			}
		});
	});


	Vue.component('chat-goods', {
		props: {
			skuId: {
				type: [Number, String]
			}
		},
		data() {
			return {
				goodsInfo: {}
			}
		},
		watch:{
			skuId(){
				console.log("我变了")
			}
		},
		mounted() {
			let that = this;
			$.ajax({
				url: ns.url("servicer://servicer/chat/goodSkuDetial"),
				data: {
					sku_id: that.skuId
				},
				dataType: 'JSON',
				type: 'POST',
				success(res) {
					that.goodsInfo=res.data
					that.goodsInfo.sku_image=ns.img(that.goodsInfo.sku_image);
				}
			});
		},
		template: `<div class="goods-info-box">
				<img :src="goodsInfo.sku_image">
				<div class="goods-price">
					￥{{goodsInfo.price}}
				</div>
				<div class="goods-name">
					{{goodsInfo.sku_name}}
				</div>
				<div class="goods-desc">
					{{goodsInfo.introduction}}
				</div>
			</div>`
	})

	Vue.component('chat-order', {
		props: {
			orderId: {
				type: [Number, String]
			}
		},
		data() {
			return {
				orderInfo: {}
			}
		},
		mounted() {
			let that = this;
			$.ajax({
				url: ns.url("servicer://servicer/chat/orderDetail"),
				data: {
					order_id: that.orderId
				},
				dataType: 'JSON',
				type: 'POST',
				success(res) {
					if(res.code==0){
						that.orderInfo=res.data;
						if(that.orderInfo.order_goods){
							that.orderInfo.order_goods[0].sku_image=ns.img(that.orderInfo.order_goods[0].sku_image)
						}
						console.log(that.orderInfo)
					}
					
				}
			});
		},
		template: `
		<div class="order-info-box">
			<img :src="orderInfo.order_goods?orderInfo.order_goods.sku_img:''">
			<div class="order-info">
				<div class="order-No">
					订单号：<span>{{orderInfo.order_no}}</span>
				</div>
				<div class="order-price">
					订单金额：<span class="follow-color">￥{{orderInfo.order_money}}</span>
				</div>
				<div class="order-status">
					<span class="follow-color">{{orderInfo.order_status_name}}</span>
				</div>
			</div>
		</div>
		`
	})

	var vue = new Vue({
		el: "#chatIndexBox",
		data: {
			tab_repeat_flag:false,
			shopInfo: {},
			userPage:{
				currentPage: 1,
				countPage: 1,
				repeat_flag: false
			},
			chatPage:{
				currentPage: 1,
				countPage: 1,
				repeat_flag: false
			},

			offlineMembers: [], //下线人数
			onlineMembers: [], //在线人数

			selectMemberList: [], //正在聊天列表
			
			selectMember:{},

			otherlist: [], //用户的商品或订单信息

			chatMessageList: [], //聊天信息列表

			/*----------------------------心跳检测相关参数--------------------------------------------------*/
			timeout: 50000, //9分钟发一次心跳
			timeoutObj: null,
			serverTimeoutObj: null,
		},
		mounted() {
			this.requestUserPageData();//给用户列表绑定上拉滚动事件
			this.requestChattingPageData();//给聊天记录绑定上拉滚动事件
			this.userList();
			this.findShopInfo();
		},
		computed: {
			numberTotal() {
				let num1 = this.offlineMembers ? this.offlineMembers.length : 0;
				let num2 = this.onlineMembers ? this.onlineMembers.length : 0;
				return num1 + num2
			},
			showInfo(){
				let obj={};
				let list=this.otherlist;
				let arr=list.filter((v)=>{
					return v.member_id==this.selectMember.member_id
				})
				if(arr){
					obj=arr[0]
				}
				return obj
			}
		},
		methods: {
			//查找当前选中的聊天对象
			edit(){
				let arr = this.selectMemberList;
				let newArr = [];
				if (arr) {
					newArr = arr.filter((v) => {
						return v.select == true;
					})
				}
				if (newArr) {
					this.selectMember = newArr[0];
				} else {
					this.selectMember = {};
				}
			},
			//查询店铺信息
			findShopInfo() {
				let that = this;
				$.ajax({
					url: ns.url("servicer://servicer/chat/siteInfo"),
					data: {},
					dataType: 'JSON',
					type: 'POST',
					success(res) {
						if (res.code == 0) {
							that.shopInfo = res.data.data;
							that.shopInfo.logo = ns.img(that.shopInfo.logo);
						}
					}
				});
			},
			//查询聊天记录
			findChatList(page = 1) {
				let that = this;
				$.ajax({
					url: ns.url("servicer://servicer/index/dialogs"),
					data: {
						member_id: that.selectMember.member_id,
						page:that.chatPage.currentPage
					},
					dataType: 'JSON',
					type: 'POST',
					success(res) {
						that.chatPage.countPage = res.data.page_count;
						that.chatPage.repeat_flag = false;
						if (res.code == 0) {
							that.chatMessageList = res.data.list.concat(that.chatMessageList ? that.chatMessageList : [])
						}
					}
				});
			},
			requestChattingPageData(){
				var that = this;
				$(".chat-content-center-bottom").scroll(function () {
					var currentHeight = $(this).height(),
						subTopHeight = $(this).find(".chat-massage-box:last-of-type").position().top,
						subOneselfHeight = $(this).find(".chat-massage-box:last-of-type").outerHeight(true),
						subHeight = subTopHeight + subOneselfHeight;

					if (subHeight == currentHeight && that.chatPage.currentPage < that.chatPage.countPage && !that.chatPage.repeat_flag){
						that.chatPage.repeat_flag = true;
						that.chatPage.currentPage += 1;
						that.findChatList(that.chatPage.currentPage);
					}

				})
			},
			//请求用户列表
			userList() {
				let that = this;
				$.ajax({
					url: ns.url("servicer://servicer/index/index"),
					data: {},
					dataType: 'JSON',
					type: 'POST',
					success(res) {
						if (res.code == 0) {
							that.userPage.countPage = res.data.offlineMembers.page_count;
							that.offlineMembers = res.data.offlineMembers ? res.data.offlineMembers.list : [];
							that.onlineMembers = res.data.onlineMembers ? res.data.onlineMembers.list : [];
							if (that.offlineMembers) {
								that.offlineMembers.forEach((v) => {
									v.headimg = ns.img(v.headimg);
								})
							}
							if (that.onlineMembers) {
								that.onlineMembers.forEach((v) => {
									v.headimg = ns.img(v.headimg)
								})
							}
						}
					}
				});
			},
			requestUserPageData(){
				var that = this;
				$(".left-content .content-item:eq(0) .content-people-box").scroll(function () {
					var currentHeight = $(this).height(),
						subTopHeight = $(this).find(".content-people:last-of-type").position().top,
						subOneselfHeight = $(this).find(".content-people:last-of-type").outerHeight(),
						subHeight = subTopHeight + subOneselfHeight;

					if (subHeight == currentHeight && that.userPage.currentPage < that.userPage.countPage && !that.userPage.repeat_flag){
						that.userPage.repeat_flag = true;
						that.userPage.currentPage += 1;
						that.userPageData(that.userPage.currentPage);
					}

				})
			},
			//请求用户列表分页数据
			userPageData(page) {
				let that = this;
				$.ajax({
					url: ns.url("servicer://servicer/index/historyMembers"),
					data: {page},
					dataType: 'JSON',
					type: 'POST',
					success(res) {
						that.userPage.repeat_flag = false;
						if (res.code == 0) {
							var arr = res.data.offlineMembers ? res.data.offlineMembers.list : [];
							if (arr) {
								arr.forEach((v) => {
									v.headimg = ns.img(v.headimg);
									that.offlineMembers.push(v);
								})
							}
						}else{
							layer.msg(res.msg);
						}
					}
				});
			},
			//点击选中聊天
			openChat(e) {
				let obj = e;
				e.select = true;
				let arr = this.selectMemberList;
				if (arr) {
					let hasItem = false;
					arr.forEach((v) => {
						if (v.member_id == obj.member_id) {
							hasItem = true;
							v.select = true;
						} else {
							v.select = false;
						}
					})
					if (!hasItem) {
						arr.push(obj);
					}
				} else {
					arr.push(obj);
				}

				this.selectMemberList = arr;
				this.chatPage={
					currentPage: 1,
					countPage: 1,
					repeat_flag: false
				},
				this.chatMessageList=[];
				this.edit();
				this.findChatList();
			},
			// 删除该聊天
			closeChat(e, index) {
				let arr = this.selectMemberList;
				if (!e.select) {
					this.selectMemberList = arr.filter((v) => {
						return v.member_id != e.member_id
					})
					this.edit();
				} else {
					this.selectMemberList = arr.filter((v) => {
						return v.member_id != e.member_id
					})
					if (this.selectMemberList.length > 0 && index <= this.selectMemberList.length - 1) {
						this.selectMemberList[index].select = true;
					} else if (this.selectMemberList.length > 0 && index > this.selectMemberList.length - 1) {
						this.selectMemberList[this.selectMemberList.length - 1].select = true;
					}
					this.edit();
					this.chatPage={
						currentPage: 1,
						countPage: 1,
						repeat_flag: false
					},
					this.chatMessageList=[];
					this.findChatList();
				}
			},
			//切换聊天页面
			selectTab(item){
				for (var i = 0; i < this.selectMemberList.length; i++){
					if(this.selectMemberList[i].id == item.id){
						this.selectMember = this.selectMemberList[i];
						this.$set(this.selectMemberList[i],'select',true)
					}else
						this.$set(this.selectMemberList[i],'select',false)
				}
				this.$forceUpdate();
			
				this.chatMessageList = [];
				this.chatPage={
					currentPage: 1,
					countPage: 1,
					repeat_flag: false
				},
				this.findChatList(true);
			},
		}
	});
</script>
{/block}
{block name="script"}
<script>
</script>
{/block}
