<?php

/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\servicer\servicer\controller;

use addon\servicer\model\Dialogue;
use addon\servicer\model\Member;

/**
 * 门店首页
 * <AUTHOR>
 *
 */
class Index extends BaseServicer
{
	public function Index()
	{
		$page = input('page', 1);

		$memberModel = new Member();
		$condition = [
			['servicer_id', '=', $this->uid],
			['online', '=', 1]
		];
		$onlineMembers = $memberModel->getList($condition, true, '', $page);

		$condition = [
			['servicer_id', '=', $this->uid],
			['online', '=', 0]
		];
		$offlineMembers = $memberModel->getPageList($condition, true, 'last_online_time desc', $page);

		if (request()->isAjax()) {
			return $this->result(['onlineMembers' => $onlineMembers, 'offlineMembers' => $offlineMembers]);
		}

		$this->assign('servicer', $this->user_info);
		$this->assign('online_members', $onlineMembers);
		$this->assign('online_members_count', @count($onlineMembers) ?? 0);
		$this->assign('offline_members', @$offlineMembers['list'] ?? []);
		$this->assign('offline_members_count', @$offlineMembers['count'] ?? 0);

		$this->assign("menu_info", ['title' => "聊天室"]);
		return $this->fetch("index/index", [], $this->replace);
	}

	/**
	 * 获取聊天记录表
	 *
	 * @return json
	 */
	public function dialogs()
	{
		$member_id = input('member_id', 0);
		if (empty($member_id)) {
			return $this->result('', -1, '没有指定会员');
		}

		$page = input('page', 1);
		$limit = input('limit', 15);

		$pagelist = (new Dialogue())->getDialogueList($member_id, $this->uid, $page, $limit);

		return $this->result($pagelist);
	}

	/**
	 * 获取会员详情
	 *
	 * @return void
	 */
	public function getMember()
	{
		$member_id = input('member_id', 0);
		if (empty($member_id)) {
			return $this->result('', -1, '没有指定会员');
		}

		$member = (new Member)->getMember($member_id, $this->uid);
		return $this->result($member);
	}

	/**
	 * 历史聊天会员
	 *
	 * @return void
	 */
	public function historyMembers()
	{
		$page = input('page', 1);

		$memberModel = new Member();

		$condition = [
			['servicer_id', '=', $this->uid],
			['online', '=', 0]
		];
		$offlineMembers = $memberModel->getPageList($condition, true, '', $page);

		return $this->result(['offlineMembers' => $offlineMembers]);
	}

	public function history()
	{
		$this->assign("menu_info", ['title' => "聊天记录"]);
		return $this->fetch("index/history", [], $this->replace);
	}
}
