{extend name="app/shop/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="save">
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分组名：</label>
		<div class="layui-input-block">
			<input name="group_name" type="text" required lay-verify="required" placeholder="请输入分组名" class="layui-input ns-len-long" autocomplete="off">
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">描述：</label>
		<div class="layui-input-block">
			<textarea name="desc" class="layui-textarea ns-len-long" placeholder="请输入客服分组的相关描述"></textarea>
		</div>
	</div>
	
	<!-- 表单操作 -->
	<div class="ns-form-row">
		<button class="layui-btn  ns-bg-color " lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>
{/block}
{block name="script"}

<script>
	layui.use('form', function() {
		form = layui.form;
		form.render();

        form.on('submit(save)', function (data) {

            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
				type: "POST",
				dataType: "JSON",
                url: ns.url("servicer://shop/group/add"),
                data: data.field,
                success: function (res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("servicer://shop/group/index")
							},
							btn2: function() {
								location.href = ns.url("servicer://shop/group/add")
							}
						})
					}else{
						layer.msg(res.message);
					}
                }
            });
        });

        form.verify({
            title: function (value) {
                if (value.length == 0) {
                    return '请输入分组名称';
                }
            }
        });
    });

	function back() {
		location.href = ns.url("servicer://shop/group/index");
	}
</script>
{/block}
