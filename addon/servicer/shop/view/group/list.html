{extend name="app/shop/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>客服分组列表</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加分组</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入分组名" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="group_list" lay-filter="group_list"></table>

<!-- 工具栏操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var table,
			form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		/**
		 * 加载表格
		 */
		table = new Table({
			elem: '#group_list',
			url: ns.url("servicer/shop/group/index"), //数据接口
			cols: [
				[{
					field: 'group_name',
					title: '分组名称',
					width: '40%',
					unresize: 'false'
				}, {
					field: 'desc',
					title: '描述',
					width: '40%',
					unresize: 'false',
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("servicer://shop/group/edit", {"group_id": data.group_id});
					break;
				case 'delete': //删除
					deleteGroup(data.group_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteGroup(group_id) {

			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('确定要删除该分组吗?', function() {
				$.ajax({
					dataType: 'JSON',
					type: 'POST',
					url: ns.url("servicer://shop/group/delete"),
					data: {group_id},
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});

	function add() {
		location.href = ns.url("servicer://shop/group/add");
	}
</script>
{/block}
