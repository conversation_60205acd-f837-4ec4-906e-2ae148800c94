{extend name="base"/}
{block name="resources"}
<link rel="stylesheet" href="STATIC_EXT/color_picker/css/colorpicker.css" />
<link rel="stylesheet" href="STATIC_EXT/diyview/css/diyview.css" />
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>装修漂亮的店铺能够提高顾客的驻留时间和购买欲。</li>
			<li>好的页面可以吸引客户浏览的兴趣，快速找到自己想要买的商品，给客户带来良好的购物体验，最终实现高的转化率。</li>
			<li>创建不同活动页面，实现线上推广转化，提升网店的传播量。</li>
			<li>满足不同商家各种场景下页面的样式及推广诉求。</li>
		</ul>
	</div>
</div>
<div class="diy-view-wrap">
	<div id="diyView" class='layui-form' v-bind:style="{ backgroundColor : global.bgColor,backgroundImage : 'url('+changeImgUrl(global.bgUrl)+')' }">
		
		<div class="preview-head" v-on:click="changeCurrentIndex(-99)">
			<span>{{global.title}}</span>
			<div v-bind:class="{selected : currentIndex==-99}" v-bind:data-sort="-99" style="display:none;" v-show="data.length==0 || currentIndex==-99">
				
				<div class="edit-attribute">
					<div class="layui-form-item">
						<label class="layui-form-label sm">模板名称</label>
						<div class="layui-input-block">
							<input type="text" v-model="global.title" placeholder="请输入模板名称" class="layui-input">
						</div>
					</div>
					
					<div class="layui-form-item">
						<label class="layui-form-label sm">底部导航</label>
						<div class="layui-input-block">
							<div class="layui-unselect layui-form-switch" v-on:click="global.openBottomNav=!global.openBottomNav" v-bind:class="{ 'layui-form-onswitch' : global.openBottomNav }" lay-skin="_switch">
								<em v-if="global.openBottomNav"></em>
								<em v-else></em>
								<i></i>
							</div>
						</div>
					</div>
					
					<color v-bind:data="{ field : 'bgColor', label : '背景颜色', value : '#ffffff' }"></color>
					
					<div class="layui-form-item">
						<label class="layui-form-label sm">背景图片</label>
						<div class="layui-input-block">
							<img-upload v-bind:data="{ data : global, field : 'bgUrl' }"></img-upload>
						</div>
					</div>
				
				</div>
			
			</div>
		
		</div>
		
		<div class="preview-block">
			
			<template v-for="(nc,index) in data" v-bind:k="index">
				
				<div v-bind:data-index="index" v-on:click="changeCurrentIndex(nc.index)" v-bind:class="{ 'draggable-element nc-border-color-selected' : true,selected : currentIndex == nc.index }" v-bind:data-sort="index">
					{foreach name="$diy_view_utils" item="vo"}
					{foreach name="$vo.list" item="li"}
					<template v-if="nc.type == '{$li.name}'">
						{:event('DiyViewUtils',['controller'=>$li['controller'],'addon_name'=>$li['addon_name']],true)}
					</template>
					{/foreach}
					{/foreach}
				</div>
			
			</template>
		
		</div>
		
		<!-- 组件列表 -->
		<nav class="component-list">
			{foreach name="$diy_view_utils" item="vo" key="k"}
			<h3>{$vo.type_name}</h3>
			<ul>
				{foreach name="$vo.list" item="li" key="li_k"}
				<li title="{$li.title}"
				    {if condition="$li.value"}v-on:click='addComponent({$li.value},{ name : "{$li.name}", title : "{$li.title}", max_count : {$li.max_count}, addon_name : "{$li.addon_name}", controller : "{$li.controller}" })'
				    v-bind:class="{ 'disabled' : !checkComponentIsAdd('{$li.name}',{$li.max_count}) }"
				    {if condition="$li.support_diy_view"}
				    class="hot"
				    {/if}
				{else/}class="disabled"{/if}
				>{$li.title}</li>
				{/foreach}
			</ul>
			{/foreach}
		</nav>
		
		<div class="custom-save">
			<button class="layui-btn ns-bg-color save" lay-submit="" lay-filter="save">保存</button>
		</div>

	</div>

	<!-- {notempty name="$qrcode_info"}
	<div class="popup-qrcode-wrap">

		<img src="{:img($qrcode_info.path.h5.img)}" alt="推广二维码">
		<p class="qrcode-item-description">扫码后直接访问页面</p>
		<a class="ns-text-color" href="javascript:ns.copy('h5_url');">复制链接</a>
		<a class="ns-text-color" href="{:img($qrcode_info.path.h5.img)}" download>下载二维码</a>
		<input class="layui-input nc-len-mid" type="text" value="{$qrcode_info.path.h5.url}" id="h5_url" readonly>
	</div>
	{/notempty} -->
</div>

{if condition="!empty($diy_view_info) && !empty($diy_view_info.value)"}
<input type="hidden" id="info" value='{$diy_view_info.value}' />
{/if}
{if condition="!empty($diy_view_info) && !empty($diy_view_info.name)"}
<input type="hidden" id="name" value="{$diy_view_info.name}" />
{elseif condition="!empty($name)"}
<input type="hidden" id="name" value="{$name}" />
{else/}
<input type="hidden" id="name" value="DIY_VIEW_RANDOM_{$time}" />
{/if}
{/block}
{block name="script"}
<script>
	var STATICIMG = 'STATIC_IMG';
	var link_url = 'city://city//diy/link';
	var module = 'city';
</script>
<script src="STATIC_JS/vue.js"></script>
<script src="STATIC_EXT/color_picker/js/colorpicker.js"></script>
<script src="CITY_JS/diyview/js/async_load_css.js"></script>
<script src="CITY_JS/diyview/js/ddsort.js"></script>
<script src="STATIC_EXT/ueditor/ueditor.config.js"></script>
<script src="STATIC_EXT/ueditor/ueditor.all.js"> </script>
<script src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script src="CITY_JS/diyview/js/components.js"></script>
<script src="CITY_JS/diyview/js/custom_template.js"></script>
<script>

	{if condition="!empty($diy_view_info) && (!empty($diy_view_info.value) || !empty($diy_view_info.id) )"}
		var id = "{$diy_view_info.id}";
		var info = JSON.parse($("#info").val().toString());

		if(!$.isEmptyObject(info) && info.value){
			for(var i=0;i<info.value.length;i++) vue.addComponent(info.value[i]);
			vue.setGlobal(info.global);
		}else{
			vue.setGlobal({ title : "{$diy_view_info.title}" });
		}
		vue.title = "{$diy_view_info.title}";
	{else/}
		var id = 0;
	{/if}

	var repeat_flag = false;//防重复标识
	$("button.save").click(function(){

		if(vue.verify()){

			//全局属性
			var global = JSON.stringify(vue.global);
			global = eval("("+global+")");

			//组件属性
			var value = JSON.stringify(vue.data);
			value = eval(value);

			//重新排序
			value.sort(function(a,b){
				return a.sort-b.sort;
			});

			for(var item in value){
				delete value[item].verify;
				delete value[item].lazyLoad;
				delete value[item].lazyLoadCss;
				delete value[item].index;
				delete value[item].sort;
				delete value[item].outerCountJs;
			}

			if(repeat_flag) return;
			repeat_flag = true;

			// console.log(JSON.stringify(value));
			// console.log(JSON.stringify(global));
			var v = {
				global : global,
				value : value
			};
			// console.log(v);
			// console.log(JSON.stringify(v));
			// return;

			$.ajax({
				type : "post",
				url : "{:addon_url('city://city/diy/edit')}",
				data : { id : id, name : $("#name").val(), title : vue.global.title, value : JSON.stringify(v) },
				dataType : "JSON",
				success : function(res) {
					layer.msg(res.message);
					if (res.code == 0) {
						location.reload();
					} else {
						repeat_flag = false;
					}
					// console.log(JSON.stringify(vue.data));
					// console.log(res);
				}
			});
		}
	});
</script>
{/block}