<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+

namespace addon\seckill\admin\controller;

use app\admin\controller\BaseAdmin;
use addon\seckill\model\Seckill as SeckillModel;
use app\model\goods\GoodsModel;

/**
 * 秒杀控制器
 */
class Seckill extends BaseAdmin
{

	/**
	 * 秒杀时间段列表
	 */
	public function lists()
	{
		if (request()->isAjax()) {$status = input('status',0);
            $name = input('name','');
            $page = input('page',1);
            $page_size = input('page_size',PAGE_LIST_ROWS);

            $condition = [];
            $status>0 && $condition[] = ['status', '=', (int)$status-1];
            $name && $condition[] = ['name','like','%'.trim($name).'%'];
            $order = 'seckill_start_time desc,seckill_id desc';
			$field = '*';

			$seckill_model = new SeckillModel();
			$res = $seckill_model->getSeckillPageList($condition, $field, $order, $page, $page_size);
			foreach ($res['data']['list'] as $key => $val) {
				$val = $seckill_model->transformSeckillTime($val);
//				$res['data']['list'][ $key ]['seckill_start_time_show'] = "{$val['start_hour']}:{$val['start_minute']}:{$val['start_second']}";
//                $res['data']['list'][ $key ]['seckill_end_time_show'] = "{$val['end_hour']}:{$val['end_minute']}:{$val['end_second']}";
                $res['data']['list'][ $key ]['start_date'] = $val['start_date'];
                $res['data']['list'][ $key ]['seckill_start_time'] = date('Y-m-d H:i:s',$val['seckill_start_time']);
                $res['data']['list'][ $key ]['seckill_end_time'] = date('Y-m-d H:i:s',$val['seckill_end_time']);
			}
			return $res;
		} else {
			$this->forthMenu();
			return $this->fetch("seckill/lists");
		}
	}

	/**
	 * 添加秒杀时间段
	 */
	public function add()
	{
		if (request()->isAjax()) {
			$seckill_start_time = input('seckill_start_time', '');
			$seckill_end_time = input('seckill_end_time', '');
            try {
                $seckill_start_time = strtotime($seckill_start_time);
                $seckill_end_time = strtotime($seckill_end_time);
            }catch (\Exception $exception){
                return error('-1','请填写正确的时间格式');
            }

			$data = [
				'name' => input('name', ''),
                'keywords'=>input('keywords',''),
                'seckill_start_time' => $seckill_start_time,
                'seckill_end_time' => $seckill_end_time,
				'create_time' => time(),
			];
			$seckill_model = new SeckillModel();
			return $seckill_model->addSeckill($data);
		} else {
			return $this->fetch("seckill/add");
		}
	}

	/**
	 * 编辑秒杀时间段
	 */
	public function edit()
	{
		$seckill_model = new SeckillModel();
		if (request()->isAjax()) {
            $seckill_start_time = input('seckill_start_time', '');
            $seckill_end_time = input('seckill_end_time', '');
            try {
                $seckill_start_time = strtotime($seckill_start_time);
                $seckill_end_time = strtotime($seckill_end_time);
            }catch (\Exception $exception){
                return error('-1','请填写正确的时间格式');
            }

			$data = [
				'name' => input('name', ''),
                'keywords'=>input('keywords',''),
                'seckill_start_time' => $seckill_start_time,
                'seckill_end_time' => $seckill_end_time,
				'create_time' => time(),
				'seckill_id' => input('seckill_id', 0),
			];
			return $seckill_model->editSeckill($data);
		} else {
			$seckill_id = input('seckill_id', 0);
			$this->assign('seckill_id', $seckill_id);

			//秒杀详情
			$seckill_info = $seckill_model->getSeckillInfo([ [ 'seckill_id', '=', $seckill_id ] ]);
			if (!empty($seckill_info['data'])) {
                $seckill_info['data']['seckill_start_time'] = date('Y-m-d H:i:s', $seckill_info['data']['seckill_start_time']);
                $seckill_info['data']['seckill_end_time'] = date('Y-m-d H:i:s', $seckill_info['data']['seckill_end_time']);
			}
			$this->assign('seckill_info', $seckill_info['data']);

			return $this->fetch("seckill/edit");
		}
	}

	/**
	 * 删除秒杀时间段
	 */
	public function delete()
	{
		if (request()->isAjax()) {
			$seckill_id = input('seckill_id', 0);
			$seckill_model = new SeckillModel();
			return $seckill_model->deleteSeckill($seckill_id);
		}
	}

	/**
	 * 秒杀商品,旧
	 */
	public function goodsBack()
	{
		$seckill_model = new SeckillModel();
		$seckill_id    = input('seckill_id', 0);
		if (request()->isAjax()) {
			$page = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$sku_name = input('sku_name', '');

			$condition = [];
			$condition[] = [ 'ngs.sku_name', 'like', '%' . $sku_name . '%' ];
			$condition[] = [ 'npsg.seckill_id', '=', $seckill_id ];
			$order = 'npsg.seckill_id desc';
			$field = 'npsg.*, ngs.sku_name, ngs.price, ngs.sku_image,ngs.site_name';

			$res = $seckill_model->getSeckillGoodsPageList($condition, $page, $page_size, $order, $field);

			foreach ($res['data']['list'] as $key => $val) {
				if ($val['price'] != 0) {
					$discount_rate = floor($val['seckill_price'] / $val['price'] * 100);
				} else {
					$discount_rate = 100;
				}
				$res['data']['list'][ $key ]['discount_rate'] = $discount_rate;
			}
			return $res;

		} else {
			$this->assign('seckill_id', $seckill_id);

			//秒杀详情
			$seckill_info = $seckill_model->getSeckillInfo([ [ 'seckill_id', '=', $seckill_id ] ]);
			if (!empty($seckill_info['data'])) {
				$seckill_info['data'] = $seckill_model->transformSeckillTime($seckill_info['data']);
			}
			$this->assign('seckill_info', $seckill_info['data']);

			$isMng = input('isMng',false);
			if ($isMng) {
				// 可添加、编辑商品
				return $this->fetch("seckill/goodsMng");
			}else{
				// 仅查看商品
				return $this->fetch("seckill/goods");
			}
		}
	}

	/**
	 * [goods 秒杀商品skuid是goodsid]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T17:33:51+0800
	 * @return   [type]                   [description]
	 */
	public function goods()
	{
		$seckill_model = new SeckillModel();
		$seckill_id    = input('seckill_id', 0);
		if (request()->isAjax()) {
			$page = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$goods_name  = input('goods_name', '');

			$condition = [];
			$condition[] = [ 'g.goods_name', 'like', '%' . $goods_name . '%' ];
			$seckill_id && $condition[] = [ 'npsg.seckill_id', '=', $seckill_id ];
			$order = 'npsg.sort desc, npsg.id desc';
			$field = 'npsg.*, g.goods_name ,g.cost_price ,g.goods_id , g.price, g.goods_image,g.site_name,nps.name,nps.seckill_start_time, nps.seckill_end_time,g.goods_state,g.reward_shop_rate as goods_reward_shop_rate,npsg.publicize_img_path';

			$res = $seckill_model->newSeckillGoodsPageList($condition, $page, $page_size, $order, $field);

			foreach ($res['data']['list'] as $key => $val) {
				$difPrice = 0;
				if ($val['price'] != 0) {
					$discount_rate = floor($val['seckill_price'] / $val['price'] * 100);
				} else {
					$discount_rate = 100;
				}
				$res['data']['list'][ $key ]['discount_rate'] = $discount_rate;
				// $res['data']['list'][ $key ]['goods_image'] = $val['goods_image']?explode(',', $val['goods_image']):[];

				// 店主佣金
				// $difPrice = $val['seckill_price']-$val['cost_price'];
				// $res['data']['list'][ $key ]['ownerBrokerage'] = currencyFormat($difPrice<=0?0:$difPrice*$val['reward_shop_rate']*0.01, false);
                //$maxPrice = GoodsModel::find($val['goods_id'])->append(['max_sku_price'])->max_sku_price;
                //$res['data']['list'][ $key ]['sale_price'] = currencyFormat(currencyFormat($maxPrice*$val['goods_reward_shop_rate']*0.01)+$maxPrice);
                $maxPrice = GoodsModel::find($val['goods_id'])->append(['max_market_price'])->max_market_price;
                $res['data']['list'][ $key ]['sale_price'] = $maxPrice;
                $res['data']['list'][ $key ]['ownerBrokerage'] = currencyFormat($val['seckill_price']*$val['reward_shop_rate']*0.01);

                $val = $seckill_model->transformSeckillTime($val);
				$res['data']['list'][ $key ]['seckill_start_time_show'] = "{$val['start_date']} {$val['start_hour']}:{$val['start_minute']}:{$val['start_second']}";
				$res['data']['list'][ $key ]['seckill_end_time_show'] = "{$val['end_date']} {$val['end_hour']}:{$val['end_minute']}:{$val['end_second']}";

                $goods = GoodsModel::find($val['goods_id'])->append(['min_cost_price', 'max_cost_price']);
                $res['data']['list'][ $key ]['min_cost_price'] = $goods->min_cost_price;
                $res['data']['list'][ $key ]['max_cost_price'] = $goods->max_cost_price;
                $res['data']['list'][ $key ]['cost_price'] = $goods->min_cost_price == $goods->max_cost_price ? $goods->max_cost_price : $goods->min_cost_price." ~ ".$goods->max_cost_price;
			}
			return $res;

		} else {
			$this->assign('seckill_id', $seckill_id);

			//秒杀详情
			$seckill_info = $seckill_model->getSeckillInfo([ [ 'seckill_id', '=', $seckill_id ] ]);
			if (!empty($seckill_info['data'])) {
				$seckill_info['data'] = $seckill_model->transformSeckillTime($seckill_info['data']);
			}

			$this->assign('seckill_info', $seckill_info['data']);

			$isMng = input('isMng',false);
			if ($isMng) {
				// 可添加、编辑商品
				return $this->fetch("seckill/goodsMng");
			}else{
				// 仅查看商品
				return $this->fetch("seckill/goods");
			}
		}
	}


	/**
	 * [goodslist 秒杀商品,旧]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T19:09:04+0800
	 * @return   [type]                   [description]
	 */
	public function goodslistBack()
	{
		if (request()->isAjax()) {
			$page = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$sku_name = input('sku_name', '');

			$condition = [];
			$condition[] = [ 'ngs.sku_name', 'like', '%' . $sku_name . '%' ];

			$seckill_model = new SeckillModel();
			$res = $seckill_model->newSeckillGoodsPageList($condition, $page, $page_size);

			foreach ($res['data']['list'] as $key => $val) {
				$res['data']['list']['price'] = currencyFormat(($val['price'] * $val['reward_shop_rate'] * 0.01) + $val['price']);
				if ($val['price'] != 0) {
					$discount_rate = floor($val['seckill_price'] / $val['price'] * 100);
				} else {
					$discount_rate = 100;
				}
				$res['data']['list'][ $key ]['discount_rate'] = $discount_rate;
				$val = $seckill_model->transformSeckillTime($val);
				$res['data']['list'][ $key ]['seckill_start_time_show'] = "{$val['start_date']} {$val['start_hour']}:{$val['start_minute']}:{$val['start_second']}";
				$res['data']['list'][ $key ]['seckill_end_time_show'] = "{$val['end_date']} {$val['end_hour']}:{$val['end_minute']}:{$val['end_second']}";
			}
			return $res;

		} else {
			$this->forthMenu();
			return $this->fetch("seckill/goodslist");
		}
	}

	/**
	 * [goodslist 秒杀商品skuid是goodsid]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T19:09:49+0800
	 * @return   [type]                   [description]
	 */
	public function goodslist()
	{
		if (request()->isAjax()) {
			// $page = input('page', 1);
			// $page_size = input('page_size', PAGE_LIST_ROWS);
			// $sku_name  = input('sku_name', '');

			// $condition = [];
			// $condition[] = [ 'ngs.sku_name', 'like', '%' . $sku_name . '%' ];

			// $seckill_model = new SeckillModel();
			// $res = $seckill_model->getSeckillGoodsPageList($condition, $page, $page_size);

			// foreach ($res['data']['list'] as $key => $val) {
			// 	if ($val['price'] != 0) {
			// 		$discount_rate = floor($val['seckill_price'] / $val['price'] * 100);
			// 	} else {
			// 		$discount_rate = 100;
			// 	}
			// 	$res['data']['list'][ $key ]['discount_rate'] = $discount_rate;
			// 	$val = $seckill_model->transformSeckillTime($val);
			// 	$res['data']['list'][ $key ]['seckill_start_time_show'] = "{$val['start_hour']}:{$val['start_minute']}:{$val['start_second']}";
			// 	$res['data']['list'][ $key ]['seckill_end_time_show'] = "{$val['end_hour']}:{$val['end_minute']}:{$val['end_second']}";
			// }
			// return $res;

		} else {
			$this->forthMenu();
			return $this->fetch("seckill/goodslist");
		}
	}

	/**
	 * 删除商品
	 */
	public function deleteGoods()
	{
		if (request()->isAjax()) {
			$seckill_id = input('seckill_id', 0);
			$sku_id = input('sku_id', '');
			$site_id = input('site_id', 0);
			$seckill_model = new SeckillModel();
			return $seckill_model->deleteSeckillGoods($seckill_id, $site_id, $sku_id);
		}
	}

	/**
	 * [addGoods 添加秒杀商品]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T09:45:17+0800
	 */
	public function addGoods()
	{
		if (request()->isAjax()) {
			$seckill_id = input('seckill_id', 0);
			$goods_ids  = input('goods_ids', '');

			if (!$goods_ids) {
                return error(-1, '请选择商品');
			}

			$seckill_model = new SeckillModel();
			return $seckill_model->adminAddSeckillGoods($seckill_id, $goods_ids);
		}
	}

	/**
	 * [change_status 秒杀商品上下架]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T13:06:48+0800
	 * @return   [type]                   [description]
	 */
	public function change_status()
    {
        if (request()->isAjax()) {
            $id = input('id', 0);
            $status = input('status', 0);
            if (empty($id)) {
                return error(-1, '参数错误！');
            }
            $seckill_model = new SeckillModel();
            return $seckill_model->changeStatus($id,$status);
        }
    }

    /**
     * [changeSeckillStatus 秒杀活动上下架]
     * <AUTHOR>
     * @DateTime 2020-10-14T14:56:28+0800
     * @return   [type]                   [description]
     */
    public function changeSeckillStatus()
    {
        if (request()->isAjax()) {
            $seckill_id = input('seckill_id', 0);
            $status = input('status', 0);
            if (empty($seckill_id)) {
                return error(-1, '参数错误！');
            }
            $seckill_model = new SeckillModel();
            return $seckill_model->changeSeckillStatus($seckill_id,$status);
        }
    }

    /**
     * [editStock 编辑库存]
     * <AUTHOR>
     * @DateTime 2020-10-14T14:15:00+0800
     * @return   [type]                   [description]
     */
    public function editStock()
    {
        if (request()->isAjax()) {
            $id = input('id', 0);
            $stock = input('stock/d', 0);
            // 剩余库存不开放给修改，根据库存和销量计算
//            $reStock = input('reStock/d', 0);
            if (empty($id)) {
                return error(-1, '参数错误！');
            }
//            if (!$stock && !$reStock) {
//                return error(-1, '库存或者剩余库存不能为空！');
//            }
            $seckill_model = new SeckillModel();
            return $seckill_model->editSeckillStock($id,$stock);
        }
    }

    /**
     * [updateGoods 更新商品（秒杀价格）]
     * <AUTHOR>
     * @DateTime 2020-10-14T18:09:18+0800
     * @return   [type]                   [description]
     */
    public function updateGoods()
	{
		if (request()->isAjax()) {
			$seckill_id = input('seckill_id', 0);
			$id      = input('id', '');
			$price   = input('price', 0.00);
			$rate    = input('rate', '');
            $publicizeImg = input('publicize_img_path', null);

			$seckill_model = new SeckillModel();
			$res = $seckill_model->editSeckillGoods($seckill_id, $id, $price,$rate,$publicizeImg);
			if ($res['code']==0) {
				$this->addLog("编辑秒杀活动id:".$id, $res['data']);
			}
			return $res;
		}
	}

    /**
     * 编辑秒杀商品排序
     * @return array
     */
    public function editSort()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            $sort = input('sort', 0, 'intval');

            $seckill_model = new SeckillModel();
            return $seckill_model->editSeckillGoodsSort($id, $sort);
        }
    }

    /**
     * 编辑秒杀商品限购数量
     * @return array
     */
    public function editBuyLimit()
    {
        if (request()->isAjax()) {
            $ids = input('ids', []);
            $buyLimit = input('buy_limit', 1);

            $seckill_model = new SeckillModel();
            return $seckill_model->editBuyLimit($ids, $buyLimit);
        }
    }
}