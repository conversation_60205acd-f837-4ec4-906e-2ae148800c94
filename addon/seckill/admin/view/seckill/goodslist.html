{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>参与该时段秒杀的商品列表</li>
        </ul>
    </div>
</div>
<!-- 搜索框 -->
<div class="ns-single-filter-box">
    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="goods_name" placeholder="请输入商品名称" class="layui-input" autocomplete="off">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="good_list" lay-filter="good_list"></table>

<!-- 商品 -->
<script type="text/html" id="goodIntro">
    <div class="ns-table-tuwen-box">
        <div class="ns-img-box">
            {{#  if(d.goods_image){  }}
            <img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
            <!-- <img layer-src src="{{ns.img(d.goods_image[0])}}"/> -->
            {{#  }  }}
        </div>
        <div class="ns-font-box">
            <a href="javascript:;" class="ns-multi-line-hiding ns-text-color">{{d.goods_name}}</a>
        </div>
    </div>
</script>
<!--价格-->
<script type="text/html" id="price">
    <div class="layui-elip">商品原售价：{{d.sale_price}}</div>
    <div class="layui-elip">秒杀价格：{{d.seckill_price}}</div>
</script>

<!--时间-->
<script type="text/html" id="times">
    <div class="layui-elip">开始时间：{{d.seckill_start_time_show}}</div>
    <div class="layui-elip">结束时间：{{d.seckill_end_time_show}}</div>
</script>

<!--操作-->
<!-- <script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script> -->
{/block}
{block name="script"}
<script>
    var form, table, laytpl,
        repeat_flag = false, //防重复标识
        arr_id_good = [];

    layui.use(['form', 'laytpl'], function() {
        form = layui.form;
        laytpl = layui.laytpl;
        form.render();

        table = new Table({
            elem: '#good_list',
            url: '{:addon_url("seckill://admin/seckill/goods")}',
            async : false,
            parseData: function(res) {
                arr_id_good = [];
                for (var i in res.data.list) {
                    arr_id_good.push(res.data.list[i].sku_id);
                }
                return {
                    "code": res.code,
                    "msg": res.message,
                    "count": res.data.count,
                    "data": res.data.list,
                };
            },
            cols: [
                [{
                    title: '商品',
                    unresize: 'false',
                    width: '20%',
                    templet: '#goodIntro'
                }, {
                    field: 'name',
                    title: '秒杀名称',
                    unresize: 'false',
                    width: '18%',
                }, {
                    field: 'sale_num',
                    title: '销量',
                    unresize: 'false',
                    width: '8%',
                }, {
                    title: '价格',
                    unresize: 'false',
                    width: '10%',
                    templet: '#price'
                }, {
                    title: '秒杀时间段',
                    unresize: 'false',
                    width: '20%',
                    templet: '#times'
                // }, {
                //     title: '操作',
                //     width: '8%',
                //     toolbar: '#operation',
                //     unresize: 'false'
                }]
            ]
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'delete': //查看
                    delGoods(data.sku_id ,data.seckill_id,data.site_id);
                    break;
            }

        });

        /**
         * 删除
         */
        function delGoods(sku_id, seckill_id,site_id) {

            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要删除该商品吗?', function() {
                $.ajax({
                    url: '{:addon_url("seckill://admin/seckill/deleteGoods")}',
                    data: {
                        "sku_id": sku_id,
                        "seckill_id": seckill_id,
                        "site_id":site_id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function() {
                layer.close();
                repeat_flag = false;
            });
        }
    });
</script>
{/block}