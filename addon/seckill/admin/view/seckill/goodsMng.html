{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.upload-img {
		border: 1px dashed #d9d9d9;
		width: 50px;
		height: 50px;
		border-radius: 2px;
		background-color: #fbfbfb;
		text-align: center;
		cursor: pointer;
		margin: 8px;
		display: inline-block;
		padding: 15px 0;
		box-sizing: border-box;
	}
	.operation {
		position: absolute;
		top: 0;
		z-index: 10;
		width: 80px;
		height: 80px;
		background: rgba(0, 0, 0, 0.3);
		color: #fff;
		cursor: pointer;
		line-height: 80px;
		text-align: center;
		display: none;
	}
	.operation i{
		font-size: 25px;
	}
	.img-wrap a {
		display: block;
		width: 80px;
		height: 80px;
		line-height: 80px;
		text-align: center;
		overflow: hidden;
	}
</style>

{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>管理员可以在此页添加/删除参加限时秒杀活动的商品</li>
			<li>秒杀价格为参与秒杀活动时的价格，可在列表页进行编辑</li>
		</ul>
	</div>
</div>

<!-- <div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">筛选</h2>

		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品ID：</label>
					<div class="layui-input-inline">
						<input type="text" id="goods_id" name="goods_id" placeholder="" class="layui-input" autocomplete="off">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">商品名称：</label>
					<div class="layui-input-inline">
						<input type="text" id="goods_name" name="goods_name" placeholder="" class="layui-input" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">状态：</label>
					<div class="layui-input-inline">
						<select name="status" lay-filter="" lay-search="">
							<option value="">全部</option>
							<option value=0>下架</option>
							<option value=1>上架</option>

						</select>
					</div>
				</div>

				<div class="layui-inline">
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
					<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
			</div>

		</form>
	</div>
</div> -->

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="goods()" data-num="{$seckill_info.seckill_id}">添加商品</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="goods_name" placeholder="请输入商品名称" class="layui-input" autocomplete="off">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
			  <i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 每个秒杀活动的ID -->
<input type="hidden" value="{$seckill_info.seckill_id}" name="seckill_id" id="seckill_id" />

<!-- 列表 -->
<table id="good_list" lay-filter="good_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<!-- <a class="layui-btn" lay-event="delete">删除商品</a> -->
		<a class="layui-btn" lay-event="change_status">
			{{# if(d.status == 0){ }}
			上架
			{{#  }else if(d.status == 1){ }}
			下架
			{{# } }}
		</a>
	</div>
</script>

<!-- 商品 -->
<script type="text/html" id="goodIntro">
	<div class="ns-table-title">
		<div class="ns-title-pic">
			{{#  if(d.goods_image){  }}
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
			<!-- <img layer-src src="{{ns.img(d.goods_image[0])}}"/> -->
			{{#  }  }}
		</div>
		<div class="ns-title-content">
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<script type="text/html" id="publicize_img">

		{{# if(d.publicize_img_path == ''){ }}
		<div class="upload-img" data-index="" onclick="uploadImg({{d.id}})"><i class="layui-icon layui-icon-add-1"></i></div>
		{{#  }else{ }}
		<div class="img-wrap"  onmouseenter="showAction(this)" onmouseleave="hideAction(this)">
			<a href="javascript:void(0)">
				<img src="{{d.publicize_img_path}}" layer-src />
			</a>
			<div class="operation">
				<i title="图片预览" class="iconfont iconreview js-preview" onclick="bigImg(this)"></i>
				<i title="删除图片" class="layui-icon layui-icon-delete js-delete" onclick="deleteImg({{d.id}})"></i>
			</div>
		</div>
		{{# } }}
</script>

<!-- 编辑价格 -->
<script type="text/html" id="editSeckillPrice">
	<input name="seckill_price" type="number" onchange="editSeckillPrice({{d.id}}, this)" value="{{d.seckill_price}}" placeholder="请输入价格" class="layui-input edit-sort ns-len-short" autocomplete="off">
</script>
<script type="text/html" id="editShopRate">
	<input name="reward_shop_rate" type="number" onchange="editShopRate({{d.id}}, this)" value="{{d.reward_shop_rate}}" placeholder="请输入佣金比例" class="layui-input edit-sort ns-len-short" style="margin-top: 25px" autocomplete="off">
	<span>佣金：￥{{d.ownerBrokerage}}</span>
</script>
<script type="text/html" id="editStock">
	<input name="remain_stock" type="number" onchange="editStock({{d.id}}, this)" data-reStock="{{d.remain_stock}}" data-saleNum="{{d.sale_num}}" value="{{d.stock}}" placeholder="请输入库存" class="layui-input edit-sort" style="width: 60px" autocomplete="off">
</script>
<script type="text/html" id="editReStock">
	<input name="stock" type="number" onchange="editReStock({{d.id}}, this)" data-stock="{{d.stock}}" value="{{d.remain_stock}}" placeholder="请剩余库存" class="layui-input edit-sort ns-len-short" autocomplete="off">
</script>
<script type="text/html" id="editSort">
	<input name="sort" type="number" min="0" max="999" onchange="editSort({{d.id}}, this)" data-sort="{{d.sort}}" value="{{d.sort}}" placeholder="请输入排序" class="layui-input" style="width: 60px" autocomplete="off">
</script>
<script type="text/html" id="editBuyLimit">
	<input name="buy_limit" type="number" min="1" max="999" onchange="editBuyLimit({{d.id}}, this)" value="{{d.buy_limit}}" placeholder="请输入限购数量" class="layui-input" autocomplete="off">
</script>

<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
	<!-- <button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button> -->
	<button class="layui-btn layui-btn-primary" lay-event="off_goods">批量下架</button>
	<button class="layui-btn layui-btn-primary" lay-event="on_goods">批量上架</button>
</script>
{/block}
{block name="script"}
<script src="ADMIN_JS/common.js"></script>
<script>
	var  form, table, laytpl,
		repeat_flag = false, //防重复标识
		selectedGoodsSkuId = [],
		seckill_id =  $("#seckill_id").val();

	layui.use(['form', 'laytpl'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		form.render();
		let bottomToolbar = __operateGroupBottomToolbar("#batchOperation");
		table = new Table({
			elem: '#good_list',
			url: '{:addon_url("seckill://admin/seckill/goods")}',
			async : false,
			where: {'seckill_id': seckill_id},
			bottomToolbar: bottomToolbar,
			parseData: function(res) {
				selectedGoodsSkuId = [];
				for (var i in res.data.list) {
					selectedGoodsSkuId.push(res.data.list[i].sku_id);
				}
				return {
					"code": res.code,
					"msg": res.message,
					"count": res.data.count,
					"data": res.data.list,
				};
			},
			cols: [
				[{
					type: 'checkbox',
					unresize: 'false',
					width: '3%'
				}, {
					title: '商品',
					unresize: 'false',
					width: '6%',
					templet: '#goodIntro'
				},
				{
					title: '首页主图',
					unresize: 'false',
					width: '6%',
					templet: '#publicize_img'
				},
					{
					field: 'price',
					title: '原价',
					unresize: 'false',
					width: '7%',
					align: 'right',
					templet: function(data) {
						//测试加需求：专题活动/秒杀活动/拼团活动这三个活动一并修改销售价
						return '￥<span class="goods-price">'+ data.sale_price +'</span>'
					}
				},{
					field: 'cost_price',
					title: '成本价',
					unresize: 'false',
					width: '10%',
					align: 'center',
					templet: function(data) {
						return '￥<span>'+ data.cost_price +'</span>'
					}
				}, {
					field: 'seckill_price',
					title: '秒杀价格',
					unresize: 'false',
					width: '10%',
					align: 'center',
					templet: '#editSeckillPrice'
				}, {
					field: 'reward_shop_rate',
					title: '秒杀佣金比例(%)',
					unresize: 'false',
					width: '10%',
					templet: '#editShopRate'
				}/*,  {
					field: 'ownerBrokerage',
					title: '佣金',
					unresize: 'false',
					width: '6%',
					align: 'center',
					templet: function(data) {
						return '<span>￥'+ data.ownerBrokerage +'</span>'
					}
				}*//*,{
					field: 'discount_rate',
					title: '折扣率(%)',
					unresize: 'false',
					width: '6%'
				}*/,{
					field: 'stock',
					title: '库存',
					unresize: 'false',
					width: '5%',
					align: 'center',
					templet: '#editStock'
				},{
					field: 'sale_num',
					title: '实际销量',
					unresize: 'false',
					width: '6%',
					align: 'center'
				},{
					field: 'remain_stock',
					title: '库存剩余',
					unresize: 'false',
					width: '6%',
					align: 'center',
					// templet: '#editReStock'
				},{
					field: 'buy_limit',
					title: '<span>限购数量<a href="javascript:batch_buy_limit();"><i class="layui-icon" lay-event="batch_buy_limit">&#xe630;</i></a></span>',
					unresize: 'false',
					width: '8%',
					align: 'center',
					templet: '#editBuyLimit'
				},{
					field: 'sort',
					title: '排序',
					unresize: 'false',
					width: '5%',
					align: 'center',
					templet: '#editSort'
				},{
					field: 'goods_state',
					title: '商品状态',
					width: '6%',
					align:"center",
					unresize: 'false',
					templet: function(data) {
						return data.goods_state == 1 ? '上架':'下架';
					}
				},{
					field: 'status',
					title: '活动商品状态',
					width: '8%',
					align:"center",
					unresize: 'false',
					templet: function(data) {
						return data.status == 1 ? '上架':'下架';
					}
				},{
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '6%'
				}]
			]
		});
		
		/**
		 * 添加商品
		 */
		goods = function (){

			adminGoodsSelect(function (res) {
	            selectedGoodsSkuId = [];
	            var goods_id = [];
	            for(var i=0;i<res.length;i++) {
	                goods_id.push(res[i].goods_id);
	            }

	            $.ajax({
					type: 'POST',
					async: false,
					url: '{:addon_url("seckill://admin/seckill/addGoods")}',
					data: {
						'goods_ids': goods_id.toString(),
						'seckill_id': seckill_id
					},
					dataType: 'JSON',
					success: function(res) {
						layer.msg(res.message);
						table.reload();
					}
				});

	        }, selectedGoodsSkuId, {mode: "spu"});

			// goodsSelect(function (res) {
			// 	var sku_ids = [];
			// 	for(var i=0;i<res.length;i++) {
			// 		for (var k = 0; k < res[i].selected_sku_list.length; k++) {
			// 			sku_ids.push(res[i].selected_sku_list[k].sku_id);
			// 		}
			// 	}
			// 	$.ajax({
			// 		type: 'POST',
			// 		async: false,
			// 		url: '{:addon_url("seckill://admin/seckill/addGoods")}',
			// 		data: {
			// 			'sku_ids': sku_ids.toString(),
			// 			'seckill_id': seckill_id
			// 		},
			// 		dataType: 'JSON',
			// 		success: function(res) {
			// 			layer.msg(res.message);
			// 			table.reload();
			// 		}
			// 	});
			// },selectedGoodsSkuId, {mode: "sku",disabled:1});
		};
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'delete': //删除
					delGoods(data.sku_id);
					break;
				case 'change_status': //上下架
					change_status(data.id, data.status);
					break;
			}
		});
		// 批量操作

		table.bottomToolbar(function (obj) {
			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}

			var id_array = new Array();
			for (i in obj.data) id_array.push(obj.data[i].id);
			console.log(obj.event);
			switch (obj.event) {
				case 'off_goods':
					//下架
					change_status(id_array, 1);
					break;
				case 'on_goods':
					//上架
					change_status(id_array, 0);
					break;
			}
		});

		function change_status(id,status) {
			let str = "";
			if(status == 0){
				status = 1;
				str = "确定上架吗？";
			}else if(status == 1){
				status = 0;
				str = "确定下架吗？";
			}
			layer.confirm(str, function() {
				$.ajax({
					url: '{:addon_url("seckill://admin/seckill/change_status")}',
					data: {id: id, status: status},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});

		}
		
		/**
		 * 删除
		 */
		function delGoods(sku_id) {

			if (repeat_flag) return false;
			repeat_flag = true;

			layer.confirm('确定要删除该商品吗?', function() {
				$.ajax({
					url: '{:addon_url("seckill://shop/seckill/deleteGoods")}',
					data: {
						"sku_id": sku_id,
						"seckill_id": seckill_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}
	});

	function batch_buy_limit(){
		var dataObject = table.checkStatus('good_list');
		var dataList=dataObject.data;
		if(dataList.length<1){
			layer.msg('请先勾选需设置的秒杀商品');
		}else{
			var $seckillGoodsIds=dataList.map(item=>item.id);
			console.log($seckillGoodsIds);

			layer.open({
				type: 1,
				value : 1,
				area: ['300px'],
				content: '<input id="batch_buy_limit" type="number" value="1" placeholder="设置商品限购数量" class="layui-input">',
				title : '批量设置商品限购数量',
				btn: ['确定', '取消'], //按钮
				yes: function(){
					var batch_buy_limit = $('#batch_buy_limit').val();
					$.ajax({
						url: ns.url("seckill://admin/seckill/editBuyLimit"),
						data: {
							"ids": $seckillGoodsIds,
							"buy_limit":batch_buy_limit
						},
						dataType: 'JSON', //服务器返回json格式数据
						type: 'POST', //HTTP请求类型
						success: function(res) {
							layer.msg(res.message);
							if (res.code == 0) {
								location.reload();
							}
						}
					});
					layer.close();
				},
			});
		}
	}

	// 监听单元格编辑
	function editSeckillPrice(id, event){
		var data = $(event).val();
		var goods_price = $(event).parents("tr").find(".goods-price").text();
		if(data < 0){
			layer.msg("折扣价格不能小于0");
			return;
		}
		// if (data > Number(goods_price)) {
		// 	layer.msg("折扣价格不能大于商品价格");
		// 	return;
		// }
		$.ajax({
			type: 'POST',
			url: ns.url("seckill://admin/seckill/updateGoods"),
			data: {
				"id": id,
				"seckill_id": seckill_id,
				"price": data
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}

	// 监听单元格编辑店主佣金比例
	function editShopRate(id, event){
		var data = $(event).val();
		if(data < 0){
			layer.msg("店主佣金比例不能小于0");
			return;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("seckill://admin/seckill/updateGoods"),
			data: {
				"id": id,
				"seckill_id": seckill_id,
				"rate": data
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}

	// 监听单元格编辑限购数量
	function editBuyLimit(id, event){
		var data = $(event).val();
		if(data < 1){
			layer.msg("限购数量必须大于0");
			return;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("seckill://admin/seckill/editBuyLimit"),
			data: {
				"ids": [id],
				"buy_limit": data
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}

	// 监听库存编辑
	function editStock(id, event){
		var data = $(event).val();
		var reStock = $(event).data('restock');		//剩余库存
		var salenum = $(event).data('salenum');		//销量

		if(data <= 0){
			layer.msg("库存不能小于0");
			return;
		}
		if (data < Number(salenum)) {
			layer.msg("库存必须大于销量");
			return;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("seckill://admin/seckill/editStock"),
			data: {
				"id": id,
				"stock": data
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}

	function editReStock(id, event){
		layer.msg("剩余库存由后台计算，不再开放编辑");
		return;
		var data  = $(event).val();
		var stock = $(event).data('stock');		//剩余库存
		if(data <= 0){
			layer.msg("库存不能小于0");
			return;
		}
		if (data > Number(stock)) {
			layer.msg("剩余库存不得大于库存");
			return;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("seckill://admin/seckill/editStock"),
			data: {
				"id": id,
				"reStock": data
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}

	function editSort(id, event) {
		let sort = $(event).val();

		$.post(ns.url("seckill://admin/seckill/editSort"), {id, sort}, function (res) {
			layer.msg(res.message);
			if (res.code == 0) {
				table.reload();
			}
		}, 'json');
	}

	function uploadImg(id)
	{
		openAlbum(function (data) {
			$.post(ns.url("seckill://admin/seckill/updateGoods"), {id: id,publicize_img_path: data[0].pic_path,seckill_id:seckill_id}, function (res) {
				layer.msg(res.message);
				if (res.code == 0) {
					table.reload();
				}
			}, 'json');
		}, 1);
	}
	//SKU图片放大预览
	function bigImg(event)
	{
		$(event).parent().prev().find("img").click();
	}
	//SKU图片删除
	function deleteImg(id) {
		$.post(ns.url("seckill://admin/seckill/updateGoods"), {id: id,publicize_img_path: "",seckill_id:seckill_id}, function (res) {
			layer.msg(res.message);
			if (res.code == 0) {
				table.reload();
			}
		}, 'json');
	}

	function showAction(event)
	{
		$(event).children(".operation").show();
	}
	function hideAction(event)
	{
		$(event).children(".operation").hide();
	}

</script>
{/block}