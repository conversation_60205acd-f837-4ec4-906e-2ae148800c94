{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>秒杀时段名称将会显示在秒杀列表页的时间段内</li>
			<li>秒杀时间-时：可填入00-23的整数（如果为个位数，需在前面加上0，比如：01）</li>
			<li>秒杀时间-分/秒：可填入00-59的整数（如果为个位数，需在前面加上0，比如：01）</li>
		</ul>
	</div>
</div>

<div class="layui-form ns-form">
<!--	<div class="layui-form-item">-->
<!--		<label class="layui-form-label"><span class="required">*</span>秒杀日期：</label>-->
<!--		<div class="layui-input-block">-->
<!--			<input type="text" name="start_date" id="start_date" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">-->
<!--		</div>-->
<!--	</div>-->
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>秒杀时间段名称：</label>
		<div class="layui-input-block">
			<input type="text" name="name" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">关键词：</label>
		<div class="layui-input-block">
			<input type="text" name="keywords" autocomplete="off" class="layui-input ns-len-long" placeholder="秒杀活动关键词，多个词以,分隔">
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label">开始时间：</label>
			<div class="layui-input-inline">
<!--				<input type="text" name="start_hour" maxlength="2" placeholder="时" lay-verify="timeHour" autocomplete="off" class="layui-input ns-len-short">-->
				<input type="text" name="seckill_start_time" id="seckill_start_time" lay-verify="required" autocomplete="off" class="layui-input ns-len-mid">
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label">结束时间：</label>
			<div class="layui-input-inline">
<!--				<input type="text" name="end_hour" maxlength="2" placeholder="时" lay-verify="timeHour" autocomplete="off" class="layui-input ns-len-short">-->
				<input type="text" name="seckill_end_time" id="seckill_end_time" lay-verify="required" autocomplete="off" class="layui-input ns-len-mid">
			</div>
		</div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

{/block}
{block name="script"}
<script>
	layui.use(['form', 'laydate'], function() {
		var form = layui.form,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();
		// 渲染日期组件
		laydate.render({
			elem: '#start_date'
		});

		laydate.render({
			elem: '#seckill_start_time',
			type: 'datetime',
		});
		laydate.render({
			elem: '#seckill_end_time',
			type: 'datetime',
		});

		// 表单提交监听
		form.on('submit(save)', function(data) {
			var field = data.field;
			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				url: ns.url("seckill://admin/seckill/add"),
				data: data.field,
				type: "post",
				dataType: "JSON",
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("seckill://admin/seckill/lists");
							},
							btn2: function() {
								location.href = ns.url("seckill://admin/seckill/add");
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		form.verify({
			timeHour: function(value) {
				if (!new RegExp("^([01]?[0-9]|2[0-3])$").test(value)) {
					return '时段范围为0-23，且只能是整数';
				}
			},
			timeMinSend: function(value) {
				if (!new RegExp("^([0-5]?[0-9]?)$").test(value)) {
					return '分秒范围为0-59，且只能是整数';
				}
			}
		});
	});

	function back() {
		location.href = ns.url("seckill://admin/seckill/lists");
	}
</script>
{/block}