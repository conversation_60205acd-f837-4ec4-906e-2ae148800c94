{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>展示了秒杀时间段列表</li>
			<li>可对时间段进行新增/编辑/删除/查看详情操作</li>
		</ul>
	</div>
</div>

<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="clickAdd()">添加秒杀时间段</button>
	<div class="layui-form">
		<div class="layui-input-inline">
			<select name="status">
				<option value="0">全部</option>
				<option value="2">上架</option>
				<option value="1">下架</option>
			</select>
		</div>
		<div class="layui-input-inline">
			<input type="text" name="name" placeholder="请输入活动名称" class="layui-input" autocomplete="off">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<table id="seckill_list" lay-filter="seckill_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<!-- <a class="layui-btn" lay-event="detail">查看详情</a> -->
		<a class="layui-btn" lay-event="changeStatus">
			{{# if(d.status == 0){ }}
			上架
			<a class="layui-btn" lay-event="edit">编辑</a>
			{{#  }else if(d.status == 1){ }}
			下架
			{{# } }}
		</a>
		<a class="layui-btn" lay-event="goodsMng">商品管理</a>
		<!-- <a class="layui-btn" lay-event="delete">删除</a> -->
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use('form', function() {
		var form = layui.form;
		var repeat_flag = false; //防重复标识

		var table = new Table({
			elem: '#seckill_list',
			url: ns.url("seckill://admin/seckill/lists"),
			parseData: function(res) { //res 即为原始返回的数据
				return {
					"code": res.code, //解析接口状态
					"msg": res.message,
					"count": res.data.count,
					"data": res.data.list
				};
			},
			cols: [
				[ {
					field: 'start_date',
					title: '开始日期',
					unresize: 'false',
					width: '10%'
				},{
					field: 'name',
					title: '活动名称',
					unresize: 'false',
					width: '20%'
				}, {
					field: 'seckill_start_time',
					title: '开始时间',
					unresize: 'false',
					width: '20%'
				}, {
					field: 'seckill_end_time',
					title: '结束时间',
					unresize: 'false',
					width: '20%'
				},{
					field: 'status',
					title: '状态',
					width: '8%',
					// align:"center",
					unresize: 'false',
					templet: function(data) {
						return data.status == 1 ? '上架':'下架';
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:"center",
					width: '20%'
				}]
			],
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'detail': //详情
					location.href = ns.url("seckill://admin/seckill/goods?seckill_id=" + data.seckill_id);
					break;
				case 'goodsMng': //详情
					location.href = ns.url("seckill://admin/seckill/goods?isMng=1&seckill_id=" + data.seckill_id);
					break;
				case 'delete': //删除
					layer.confirm('确定要删除该秒杀活动吗?', function() {
						$.ajax({
							url: ns.url("seckill://admin/seckill/delete?seckill_id=" + data.seckill_id),
							data: data,
							dataType: 'JSON',
							type: 'POST',
							success: function(res) {
								layer.msg(res.message);
								repeat_flag = false;
								if (res.code == 0) {
									table.reload();
								}
							}
						});
					});
					break;
				case 'edit': //编辑
					location.href = ns.url("seckill://admin/seckill/edit?seckill_id=" + data.seckill_id);
					break;
				case 'changeStatus': //上下架
					changeStatus(data.seckill_id, data.status);
					break;
			}
		});
		
		function changeStatus(seckill_id,status) {
			let str = "";
			if(status == 0){
				status = 1;
				str = "确定上架吗？";
			}else if(status == 1){
				status = 0;
				str = "确定下架吗？";
			}
			layer.confirm(str, function() {
				$.ajax({
					url: '{:addon_url("seckill://admin/seckill/changeSeckillStatus")}',
					data: {seckill_id: seckill_id, status: status},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});

		}
	});


	function clickAdd() {
		location.href = ns.url("seckill://admin/seckill/add");
	}
</script>
{/block}