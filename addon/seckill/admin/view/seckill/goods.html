{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.layui-table-cell {white-space: normal;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>参与该时段秒杀的商品列表</li>
			<li>秒杀时间段 - {$seckill_info.name}</li>
		</ul>
	</div>
</div>

<table id="goods_list" lay-filter="goods_list"></table>

<input type="hidden" name="seckill_id" id="" value="{$seckill_info.seckill_id}" />

<script type="text/html" id="skuPic">
	<div class="ns-table-tuwen-box">
		<div class="ns-img-box">
			<img layer-src src="{{ns.img(d.sku_image)}}" alt="">
		</div>
		<div class="ns-font-box">
			<p class="ns-multi-line-hiding">{{d.sku_name}}</p>
		</div>
	</div>
</script>
{/block}
{block name="script"}
<script>
	var seckill_id = $('input[name = seckill_id]').val();
	var table;

	table = new Table({
		elem: '#goods_list',
		url: ns.url("seckill://admin/seckill/goods?seckill_id=" + seckill_id),
		cols: [
			[{
				field: 'sku_name',
				title: '商品名称',
				unresize: 'false',
				width: '44%',
				templet: '#skuPic'
			}, {
				field: 'site_name',
				title: '商家名称',
				unresize: 'false',
				width: '20%'
			}, {
				field: 'price',
				title: '原价',
				unresize: 'false',
				align: 'right',
				width: '12%',
				templet: function(data) {
					return '￥'+ data.price;
				}
			}, {
				field: 'seckill_price',
				title: '秒杀价',
				unresize: 'false',
				align: 'right',
				width: '12%',
				templet: function(data) {
					return '￥'+ data.seckill_price;
				}
			}, {
				field: 'discount_rate',
				title: '折扣率(%)',
				unresize: 'false',
				align: 'right',
				width: '12%'
			}]
		]
	});

	/**
	 * 监听工具栏操作
	 */
	table.tool(function(obj) {
		var data = obj.data;
		switch (obj.event) {
			case 'detail': //商品管理
				location.href = ns.url("seckill://admin/seckill/goods");
				break;
		}
	});

	function clickAdd() {
		location.href = ns.url("seckill://admin/seckill/add");
	}
</script>
{/block}