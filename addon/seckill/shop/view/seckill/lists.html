{extend name="app/shop/view/base.html"/}
{block name="resources"}
<style>
	.layui-table-view {margin-top: 15px;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>点击商品管理按钮可以对秒杀时段内的商品进行管理</li>
		</ul>
	</div>
</div>

<!-- 列表 -->
<table id="activity_list" lay-filter="activity_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="manage" href='{{ns.url("seckill://shop/seckill/goods?seckill_id=")}}{{d.seckill_id}}'>商品管理</a>
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form'], function() {
		var table,
			form = layui.form;

		table = new Table({
			elem: '#activity_list',
			url: ns.url("seckill://shop/seckill/lists"),
			parseData: function(res) {
				return {
					"code": res.code,
					"msg": res.message,
					"count": res.data.length,
					"data": res.data
				};
			},
			cols: [
				[{
					field: 'name',
					title: '秒杀时段名称',
					unresize: 'false',
					width: '30%'
				}, {
					field: 'seckill_start_time_show',
					title: '每日开始时间',
					unresize: 'false',
					width: '27%'
				}, {
					field: 'seckill_end_time_show',
					title: '每日结束时间',
					unresize: 'false',
					width: '27%'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '16%'
				}]
			],
			page: false
		});
	});
</script>
{/block}