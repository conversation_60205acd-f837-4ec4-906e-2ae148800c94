<nc-component v-bind:data="data[index]" class="component-seckill">

	<!-- 预览 -->
	<template slot="preview">
		<div class="seckill-container" v-bind:style="`background-color:${data[index].backgroundColor};background-image:url('${data[index].backgroundImg}');`" v-if="data[index].selectedTemplate==1">
			<div class="seckill-head">
				<div class="title-wrap">
					<div class="title-wrap-left">
						<img src="STATIC_EXT/diyview/img/seckill-icon.png" class="title-wrap-icon"/>
						<span class="name" v-bind:style="`color: ${data[index].textColor};`">{{data[index].name}}</span>
					</div>
					<div class="time-box">
						<div class="time-text" v-bind:style="`color: ${data[index].textColor};`">距离结束</div>
						<div class="time">
							<div class="num">00</div>
							<div class="colon" style="padding: 0 3px">:</div>
							<div class="num">00</div>
							<div class="colon" style="padding: 0 3px">:</div>
							<div class="num">00</div>
						</div>
					</div>
				</div>
				<div class="more">
					<div v-bind:style="`color: ${data[index].textColor};`">{{data[index].moreText}}</div>
					<span class="to" v-bind:style="`border-color: ${data[index].textColor};`"><i class="layui-icon layui-icon-right" v-bind:style="`font-size: 8px;color: ${data[index].textColor};border-color: ${data[index].textColor};`"></i></span>
				</div>
			</div>
			<div class="list-wrap">
				<div class="item"  v-for="(item,index) in Array(6)">
					<div class="img-wrap">
						<img src="STATIC_EXT/diyview/img/crack_figure.png" />
					</div>
					<span class="good-name">商品名称商品名称</span>
					<span class="good-price-tip">秒杀价</span>
					<div class="good-price">
						<div class="good-price-left">
							<span>￥</span>
							<span>0.01</span>
						</div>
						<div class="good-price-right">抢</div>
					</div>
				</div>
			</div>
		</div>
		<div class="seckill-container" v-if="data[index].selectedTemplate==2">
			<div class="list-wrap-vertical">
				<div class="item-vertical" v-for="(item,index) in Array(1)">
					<div class="img-wrap-vertical">
						<img src="STATIC_EXT/diyview/img/crack_figure.png" />
					</div>
					<div class="good-name">商品名称商品名称</div>
					<div class="good-subname">商品副标题</div>
					<div class="good-price-vertical">
						<div class="good-price-vertical-left">
							<span class="good-price-vertical-left-sell">￥12.00</span>
							<span class="good-price-vertical-left-out">原价￥10.00</span>
						</div>
						<div class="good-price-vertical-right">
							<span>立省￥4.0</span>
						</div>
					</div>
					<div class="seckil-info-vertical">
						<div class="seckil-info-vertical-left">
							<div class="time-text">距离结束</div>
							<div class="time">
								<div class="num">00</div>
								<div class="colon" style="padding: 0 3px">:</div>
								<div class="num">00</div>
								<div class="colon" style="padding: 0 3px">:</div>
								<div class="num">00</div>
							</div>
						</div>
						<div class="seckil-info-vertical-right" style="background-image: url('{$resource_path}/seckill/img/rob-bg.png')">
							<img src="{$resource_path}/seckill/img/rob.png" alt="">
						</div>
					</div>
				</div>
				<div class="more-vertical" v-bind:style="`color: ${data[index].textColor};`">{{data[index].moreText}}</div>
			</div>
		</div>
	</template>

	<!-- 编辑 -->
	<template slot="edit">
		<div v-if="nc.lazyLoad">
			<seckill-list v-bind:index="index"></seckill-list>
			<!-- <div class="goods-list-edit layui-form">
				<div class="goods-list-edit layui-form"> 
					<label class="layui-form-label sm">标题</label>
					<div class="layui-input-block">
						<input type="text" name="title" v-model="data[index].name" class="layui-input" />
					</div>
					<label class="layui-form-label sm">文本内容</label>
					<div class="layui-input-block">
						<input type="text" name="title" v-model="data[index].moreText" class="layui-input" />
					</div>
					<label class="layui-form-label sm">跳转地址</label>
					<div class="layui-input-block">
						<input type="text" name="title" v-model="data[index].moreUrl" class="layui-input" />
					</div>
				</div>
			</div> -->
		</div>

	</template>
	
	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/seckill/css/design.css"></css>
		<js src="{$resource_path}/seckill/js/design.js"></js>
		
	</template>
	
</nc-component>