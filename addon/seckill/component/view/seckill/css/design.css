@CHARSET "UTF-8";
.component-seckill .preview-draggable{
  padding: 0;
}
.component-seckill .seckill-container{
  border-radius: 20px;
  padding: 10px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.component-seckill .seckill-head {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  webkit-align-items: center;
  align-items: center;
  margin-bottom: 15px;
}
.component-seckill .seckill-head .title-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.component-seckill .seckill-head .title-wrap .title-wrap-left{
  display: flex;
  align-items: center;
}
.component-seckill .seckill-head .title-wrap .line{
  width: 3px;
  height: 18px;
  background: #F2280C;
  border-radius: 1px;
  margin-right: 8px;
  display: inline-block;
}
.component-seckill .seckill-head .title-wrap .title-wrap-icon{
  width: 14px;
  height: 14px;
  margin-right: 5px;
}
.component-seckill .seckill-head .title-wrap .name {
  font-weight: bold;
  margin-right: 8px;
  max-width: 50px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.component-seckill .seckill-head .title-wrap .time-box {
  display: flex;
  align-items: center;
}
.component-seckill .seckill-head .title-wrap .time-box .time-text{
  font-size: 11px;
  margin-right: 10px;
}
.component-seckill .seckill-head .title-wrap .time-box .time{
  display: flex;
  align-items: center;
  justify-content: center;
}
.component-seckill .seckill-head .title-wrap .time-box .time .num{
  background-color: rgba(246, 93, 114, 1);
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  border-radius: 3px;
  font-size: 11px;
  text-align: center;
}
.component-seckill .seckill-head .title-wrap .time-box .time .colon{
  color: rgba(246, 93, 114, 1);
}
.component-seckill .seckill-head .more {
  display: flex;
  font-size: 12px;
}
.component-seckill .seckill-head .more span{
  display: flex;
  align-items: center;
  justify-content: center;
  /* display: inline-block; */
  border-radius: 50%;
  width: 14px;
  height: 14px;
  color: #fff;
  margin-left: 3px;
}
.component-seckill .seckill-head .more .to{
  display: flex;
  align-items: center;
  justify-content: center;
  /* display: inline-block; */
  border-radius: 50%;
  border: 1px solid;
  width: 11px;
  height: 11px;
  margin-left: 3px;
}
.component-seckill .seckill-head .more div{
  max-width: 65px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.component-seckill .list-wrap{
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}
.component-seckill .list-wrap .item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 10px;
  width: 92px;
  background-color: white;
  border-radius: 10px 10px 0 0;
}
.component-seckill .list-wrap .item .img-wrap img{
  border-radius: 10px 10px 0 0 !important;
}
.component-seckill .list-wrap .item .good-name{
  /* text-align: center; */
  margin: 10px 0 0;
}
.component-seckill .list-wrap .item .tuan-info{
  display: flex;
}
.component-seckill .list-wrap .item .tuan-info div:first-child{
  height: 14px;
  line-height: 14px;
  background: linear-gradient( 
225deg, #FE5838, #FB331D);
  border-radius: 2px 0px 0px 2px;
  box-sizing: border-box;
  padding: 0 2px;
  font-size: 10px;
  font-weight: 500;
  color: #FFFFFF;
  display: inline-block;
  width: 30px;
}
.component-seckill .list-wrap .item .tuan-info div:last-child{
  height: 14px;
  line-height: 14px;
  background: #FFEFEF;
  border-radius: 0px 2px 2px 0px;
  font-size: 10px;
  font-weight: 500;
  padding: 0 1px;
  color: #F84346;
  display: inline-block;
}
.component-seckill .list-wrap .item .good-price-tip{
  border-radius: 4px;
  background: rgba(246, 93, 114, 0.1);
  font-size: 12px;
  font-weight: 400;
  color: rgba(246, 93, 114, 1);
  width: 50px;
  text-align: center;
  margin-top: 2px;
}
.component-seckill .list-wrap .item .good-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}
.component-seckill .list-wrap .item .good-price .good-price-left{
  font-size: 12px;
  color: #F84346;
  font-weight: bold;
}
.component-seckill .list-wrap .item .good-price .good-price-right{
  font-size: 14px;
  font-weight: bolder;
  color: white;
  border-radius: 30px;
  background: rgba(246, 93, 114, 1);
  padding: 0 5px;
  box-sizing: border-box;
}
.component-seckill .list-wrap .item .real-price {
  font-size: 12px;
  color: #898989;
  text-decoration: line-through;
}
.component-seckill .list-wrap .item .good-price span:first-child{
  font-size: 13px;
}
.component-seckill .list-wrap .item .good-price span:last-child {
  font-size: 18px;
}
.component-seckill .list-wrap .item .img-wrap img {
  width: 92px;
  height: 90px;
  padding: 0;
  margin: 0;
  border-radius: 10px;
}

.component-seckill .list-wrap .item .new-price {
  font-size: 14px;
  text-align: center;
  /* display: block; */
}
.component-seckill .list-wrap .item .old-price {
  font-size: 12px;
  text-align: center;
  color: #898989;
  text-decoration: line-through;
}
.component-seckill .list-wrap-vertical .item-vertical{
  margin-bottom: 10px;
}
.component-seckill .list-wrap-vertical .img-wrap-vertical img{
  width: 100%;
  height: 300px;
  border-radius: 8px 8px 0 0;
}
.component-seckill .list-wrap-vertical .good-name{
  color: rgba(56, 56, 56, 1);
  font-size: 16px;
  font-weight: 700;
  margin-top: 16px;
}
.component-seckill .list-wrap-vertical .good-subname{
  color: rgba(166, 166, 166, 1);
  font-size: 14px;
  font-weight: 400;
  margin-top: 6px;
}
.component-seckill .list-wrap-vertical .good-price-vertical{
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}
.component-seckill .list-wrap-vertical .good-price-vertical-left-sell{
  font-size: 20px;
  font-weight: 700;
  color: rgba(246, 93, 114, 1);
}

.component-seckill .list-wrap-vertical .good-price-vertical-left-out{
  font-size: 13px;
  font-weight: 400;
  text-decoration-line: line-through;
  color: rgba(166, 166, 166, 1);
}
.component-seckill .list-wrap-vertical .good-price-vertical-right{
  font-size: 14px;
  font-weight: 400;
  color: rgba(246, 93, 114, 1);
}
.component-seckill .list-wrap-vertical .seckil-info-vertical{
  display: flex;
  justify-content: space-between;
  height: 30px;
  background: rgba(246, 93, 114, 0.1);
  padding-left: 8px;
  box-sizing: border-box;
  border-radius: 8px;
  margin-top: 7px;
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-left{
  display: flex;
  align-items: center;
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-left .time-text{
  font-size: 16px;
  font-weight: 400;
  color: rgba(246, 93, 114, 1);
  margin-right: 6px;
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-left .time{
  display: flex;
  align-items: center;
  justify-content: center;
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-left .time .num{
  background-color: rgba(246, 93, 114, 1);
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  border-radius: 3px;
  font-size: 11px;
  text-align: center;

}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-left .time .colon{
  color: rgba(246, 93, 114, 1);
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-right{
  width: 70px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.component-seckill .list-wrap-vertical .seckil-info-vertical .seckil-info-vertical-right img{
  width: 24px;
  height: 27px;
}
.component-seckill  .list-wrap-vertical .more-vertical{
  font-size: 14px;
  text-align: center;
}

/* 编辑 */
.component-seckill h3 {
  font-size: 14px;
  font-weight: 600;
  padding: 5px 10px 10px 10px;
}