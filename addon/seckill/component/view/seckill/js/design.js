var infoHtml =
  '<div class="goods-list-edit"> '+
    '            <div class="layui-form-item">\n' +
    '                <label class="layui-form-label sm">选择模板：</label>\n' +
    '                <div class="layui-input-block">\n' +
    '                    <input type="radio" :name="`selectedTemplate_${index}`" value="1" :lay-filter="`seckill_edit_selectedTemplate_${index}`" title="模板1" :checked="data.selectedTemplate==1">\n' +
    '                    <input type="radio" :name="`selectedTemplate_${index}`" value="2" :lay-filter="`seckill_edit_selectedTemplate_${index}`" title="模板2" :checked="data.selectedTemplate==2">\n' +
    '                </div>\n' +
    '            </div>\n' +
    '<div class="layui-form-item" v-if="data.selectedTemplate == 1">' +
    '<label class="layui-form-label sm">标题</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.name" class="layui-input" />'+
    '</div>' +
    '</div>'+
    '<div class="layui-form-item">' +
    '<label class="layui-form-label sm">文本内容</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.moreText" class="layui-input" />'+
    '</div>' +
    '</div>'+
    '<div class="layui-form-item" v-if="data.selectedTemplate == 2">' +
    '<label class="layui-form-label sm">展示商品数</label>'+
    '<div class="layui-input-block">'+
    '<input type="number" name="displayGoodsCount" v-model="data.displayGoodsCount" class="layui-input" />'+
    '<div class="layui-form-mid">最多可展示{{data.maxDisplayGoodsCount}}个秒杀商品，超出跳列表页</div>'+
    '</div>' +
    '</div>'+
    '<color v-bind:data="{ field : `textColor`, label : `字体颜色:` }"></color>'+
    '<template v-if="data.selectedTemplate == 1"><color v-bind:data="{ field : `backgroundColor`, label : `背景颜色:` }"></color></template>'+
    '<div class="layui-form-item" v-if="data.selectedTemplate == 1"><label class="layui-form-label sm">背景图片：</label>\n' +
    '<div class="layui-input-block">\n' +
    '   <img-upload v-bind:data="{ data : data, field: \'backgroundImg\' }"></img-upload>\n' +
    '</div>' +
    '</div>\n' +
    '<div class="layui-form-item" v-if="false">' +
    '<label class="layui-form-label sm" >跳转地址</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" disabled v-model="data.moreUrl" class="layui-input" />'+
    '</div></div>'+
  '</div>'

Vue.component("seckill-list", {
  props : ['index'],
  template: infoHtml,
  data: function () {
  return {
    data: this.$parent.data,
  }
  },
  created:function() {
    // this.data.sources = "diy"
    // this.data.goodsCount = "3"
    
    this.$parent.data.verify = this.verify;//加载验证方法
  },
  mounted:async function (){
    var data = this.data
    var index = this.index
    layui.use(['form',],function () {
      var form = layui.form
      form.render();
      form.on('radio(seckill_edit_selectedTemplate_'+index+')', function(ele){
        data.selectedTemplate=parseInt(ele.value)
      })
    })
  },
  methods: {
    verify: function() {  
      var res = { code : true, message : "" };
      if(this.data.selectedTemplate ==1 && this.data.name == '') {
        res.code = false
        res.message = '请输入秒杀模块的标题'
        return res
      }
      if(this.data.moreText == '') {
        res.code = false
        res.message = '请输入秒杀模块的文本内容'
        return res
      }
      if(this.data.selectedTemplate ==2 && (parseInt(this.data.displayGoodsCount) < 1 || this.data.displayGoodsCount > this.data.maxDisplayGoodsCount)) {
        res.code = false
        res.message = '秒杀模块的展示商品数量需要在1到'+this.data.maxDisplayGoodsCount+'范围之内';
        return res
      }
      if(false && this.data.moreUrl == '') {
        res.code = false
        res.message = '请输入秒杀模块的跳转地址'
        return res
      }
      return res
    }
  }
});