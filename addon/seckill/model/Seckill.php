<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+

namespace addon\seckill\model;

use app\model\BaseModel;
use app\model\goods\GoodsModel;
use think\facade\Cache;
use think\facade\Db;

/**
 * 限时秒杀(时段)
 */
class Seckill extends BaseModel
{
	/**
	 * 添加秒杀时段
	 * @param array $data
	 */
	public function addSeckill($data)
	{
        if ($data['seckill_start_time'] >= $data['seckill_end_time']) {
            return $this->error('', '开始时间必须小于结束时间');
        }
		//时间段检测
		$seckill_count = model('promotion_seckill')->getCount([
			[ 'seckill_start_time|seckill_end_time', 'between', [ $data['seckill_start_time'], $data['seckill_end_time'] ] ]
		]);
		if ($seckill_count > 0) {
			return $this->error('', '秒杀时间段设置冲突');
		}
		$seckill_count = model('promotion_seckill')->getCount([
			[ 'seckill_start_time', '<=', $data['seckill_start_time'] ],
			[ 'seckill_end_time', '>=', $data['seckill_end_time'] ],
		]);
		if ($seckill_count > 0) {
			return $this->error('', '秒杀时间段设置冲突');
		}
		//添加数据
		$data['create_time'] = time();
		$seckill_id = model('promotion_seckill')->add($data);
		Cache::tag("promotion_seckill")->clear();
		return $this->success($seckill_id);
	}
	
	/**
	 * 修改秒杀时段
	 * @param unknown $data
	 * @return multitype:string
	 */
	public function editSeckill($data)
	{
        if ($data['seckill_start_time'] >= $data['seckill_end_time']) {
            return $this->error('', '开始时间必须小于结束时间');
        }
		//时间段检测
		$seckill_count = model('promotion_seckill')->getCount([
			[ 'seckill_start_time|seckill_end_time', 'between', [ $data['seckill_start_time'], $data['seckill_end_time'] ] ],
			[ 'seckill_id', '<>', $data['seckill_id'] ]
		]);
		if ($seckill_count > 0) {
			return $this->error('', '秒杀时间段设置冲突');
		}
		$seckill_count = model('promotion_seckill')->getCount([
			[ 'seckill_start_time', '<=', $data['seckill_start_time'] ],
			[ 'seckill_end_time', '>=', $data['seckill_end_time'] ],
			[ 'seckill_id', '<>', $data['seckill_id'] ]
		]);
		if ($seckill_count > 0) {
			return $this->error('', '秒杀时间段设置冲突');
		}
		//更新数据
		$data['modify_time'] = time();
		$res = model('promotion_seckill')->update($data, [ [ 'seckill_id', '=', $data['seckill_id'] ] ]);
		Cache::tag("promotion_seckill")->clear();
		return $this->success($res);
	}
	
	/**
	 * 删除秒杀时段
	 * @param unknown $seckill_id
	 */
	public function deleteSeckill($seckill_id)
	{
		$res = model('promotion_seckill')->delete([ [ 'seckill_id', '=', $seckill_id ] ]);
		if ($res) {
			model('promotion_seckill_goods')->delete([ [ 'seckill_id', '=', $seckill_id ] ]);
		}
		Cache::tag("promotion_seckill")->clear();
		return $this->success($res);
	}
	
	/**
	 * 获取秒杀时段信息
	 * @param array $condition
	 * @param string $field
	 */
	public function getSeckillInfo($condition, $field = '*')
	{
		$data = json_encode([ $condition, $field ]);
		$cache = Cache::get("promotion_seckill_getSeckillInfo_" . $data);
		if (!empty($cache)) {
			return $this->success($cache);
		}
		$res = model('promotion_seckill')->getInfo($condition, $field);
		Cache::tag("promotion_seckill")->set("promotion_seckill_getSeckillInfo_" . $data, $res);
		return $this->success($res);
	}
	
	/**
	 * 获取秒杀时段列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param string $limit
	 */
	public function getSeckillList($condition = [], $field = '*', $order = '', $limit = null)
	{
		$data = json_encode([ $condition, $field, $order, $limit ]);
		$cache = Cache::get("promotion_seckill_getSeckillList_" . $data);
		if (!empty($cache)) {
			return $this->success($cache);
		}
		$list = model('promotion_seckill')->getList($condition, $field, $order, '', '', '', $limit);
		Cache::tag("promotion_seckill")->set("promotion_seckill_getSeckillList_" . $data, $list);
		
		return $this->success($list);
	}

    /**
     * 获取秒杀时段列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param int $page
     * @param string $limit
     */
    public function getSeckillPageList($condition = [], $field = '*', $order = '', $page=1, $limit = PAGE_LIST_ROWS)
    {
        $list = model('promotion_seckill')->pageList($condition, $field, $order, $page, $limit, '', '','');
        return $this->success($list);
    }

	/**
	 * 转换秒杀时间
	 * @param $info
	 * @return mixed
	 */
	public function transformSeckillTime($info)
	{
        // 新模式采用时间戳，特地做转换兼容旧格式，大于10位数当作时间戳处理
        if (strlen($info['seckill_start_time']) >= 10 && strlen($info['seckill_end_time']) >= 10) {
            list($info['start_date'], $info['start_hour'], $info['start_minute'], $info['start_second']) = explode(' ', date('Y-m-d H i s', $info['seckill_start_time']));
            list($info['end_date'], $info['end_hour'], $info['end_minute'], $info['end_second']) = explode(' ', date('Y-m-d H i s', $info['seckill_end_time']));
        } else {
            $info['start_hour'] = floor($info['seckill_start_time'] / 3600);
            $info['start_minute'] = floor(($info['seckill_start_time'] % 3600) / 60);
            $info['start_second'] = $info['seckill_start_time'] % 60;

            $info['end_hour'] = floor($info['seckill_end_time'] / 3600);
            $info['end_minute'] = floor(($info['seckill_end_time'] % 3600) / 60);
            $info['end_second'] = $info['seckill_end_time'] % 60;

            if ($info['start_hour'] < 10) $info['start_hour'] = '0' . $info['start_hour'];
            if ($info['start_minute'] < 10) $info['start_minute'] = '0' . $info['start_minute'];
            if ($info['start_second'] < 10) $info['start_second'] = '0' . $info['start_second'];

            if ($info['end_hour'] < 10) $info['end_hour'] = '0' . $info['end_hour'];
            if ($info['end_minute'] < 10) $info['end_minute'] = '0' . $info['end_minute'];
            if ($info['end_second'] < 10) $info['end_second'] = '0' . $info['end_second'];
            $info['start_date'] = $info['end_date'] = '';
        }
		return $info;
	}
	/******************************************************秒杀商品*********************************************************************/
	/**
	 * 添加秒杀商品
	 * @param unknown $seckill_id
	 * @param unknown $site_id
	 * @param unknown $sku_ids
	 * @return multitype:string
	 */
	public function addSeckillGoods($seckill_id, $site_id, $sku_ids)
	{
		$where['sku_id'] = $sku_ids;
		$site_id && $where['site_id'] = $site_id;
		$sku_list = model("goods_sku")->getList($where, 'sku_id, price');
		
		$data = [];
		foreach ($sku_list as $val) {
			$goods_count = model("promotion_seckill_goods")->getCount([ 'seckill_id' => $seckill_id, 'sku_id' => $val['sku_id'] ]);
			if (empty($goods_count)) {
				$data[] = [
					'seckill_id' => $seckill_id,
					'site_id' => $site_id,
					'sku_id' => $val['sku_id'],
					'seckill_price' => $val['price']
				];
			}
		}
		model("promotion_seckill_goods")->addList($data);
		
		return $this->success();
	}

	/**
	 * [addSeckillGoods 由原来的skuid换成goodsid]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T16:49:12+0800
	 * @param    [type]                   $seckill_id [description]
	 * @param    [type]                   $goods_ids  [description]
	 */
	public function adminAddSeckillGoods($seckill_id, $goods_ids)
	{
		$where[] = ['goods_id','in',explode(',', $goods_ids)];
		$goods_list = model("goods")->getList($where, 'goods_id,price');
		
		$data = [];
		foreach ($goods_list as $val) {
			$goods_count = model("promotion_seckill_goods")->getCount([ 'seckill_id' => $seckill_id, 'sku_id' => $val['goods_id'] ]);
			if (empty($goods_count)) {
				$data[] = [
					'seckill_id' => $seckill_id,
					'sku_id' => $val['goods_id'],
					'seckill_price' => $val['price']
				];
			}
		}
		model("promotion_seckill_goods")->addList($data);
		
		return $this->success();
	}
	
	/**
	 * 修改秒杀商品
	 * @param unknown $seckill_id
	 * @param unknown $id
	 * @param unknown $price
	 * @return multitype:string
	 */
	public function editSeckillGoods($seckill_id, $id, $price=0,$rate=0,$publicizeImg=null)
	{
		$psgMl = model("promotion_seckill_goods");
		$where['id'] = $id;

		$info = $psgMl->getInfo($where,'sku_id,seckill_price as old_seckill_price');
		if (!$info) {
			return $this->error('','秒杀商品不存在');
		}
//		$seckStatus = model('promotion_seckill')->getValue(['seckill_id'=>$seckill_id], 'status');
//		if ($seckStatus!==0) {
//			return $this->error('','不能编辑正在进行中的活动商品');
//		}

		$goods = model('goods')->getInfo(['goods_id'=>$info['sku_id'],'goods_state'=>1],'cost_price,reward_shop_rate,price');
		if (!$goods) {
			return $this->error('','商品不存在或已下架');
		}

        $goodsMl = GoodsModel::find($info['sku_id'])->append(['max_cost_price','max_market_price']);
        $maxCostPrice = $goodsMl->max_cost_price;
        $maxSkuPrice = $goodsMl->max_market_price;
		//修改
		//$sale_price = currencyFormat( ($maxSkuPrice * $goods['reward_shop_rate'] * 0.01) + $maxSkuPrice);
        $sale_price = $maxSkuPrice;

		if ($price && $price<$maxCostPrice) {
			return $this->error('','秒杀价不得低于成本价');
		}

		if ($price && $price > $sale_price) {
			return $this->error('','秒杀价不得高于商品原价');
		}

		$data['seckill_id']    = $seckill_id;
		$price && $data['seckill_price'] = $price;
		$rate !== '' && $data['reward_shop_rate'] = $rate;
        $publicizeImg !== null && $data['publicize_img_path'] = $publicizeImg;

		$psgMl->update($data, $where);
		return $this->success(array_merge($where,$info,$goods,$data));
	}
	
	/**
	 * 删除秒杀商品
	 * @param unknown $seckill_id
	 * @param int $site_id
	 * @param unknown $sku_id
	 * @return multitype:string
	 */
	public function deleteSeckillGoods($seckill_id, $site_id, $sku_id)
	{
		model("promotion_seckill_goods")->delete([ [ 'seckill_id', '=', $seckill_id ], [ 'sku_id', '=', $sku_id ]]);
		return $this->success();
	}
	
	/**
	 * 获取秒杀商品详情
	 * @param int $id
	 * @return mixed
	 */
	public function getSeckillGoodsDetail($id)
	{
		$condition = [
			[ 'sg.id', '=', $id ]
		];
		$alias = 'sg';
		$join = [
			[ 'goods_sku sku', 'sg.sku_id = sku.sku_id', 'inner' ],
			[ 'promotion_seckill ps', 'ps.seckill_id = sg.seckill_id', 'inner' ],
		];
		$list = model('promotion_seckill_goods')->getInfo($condition, 'sku.goods_id,sku.sku_id,sku.sku_name,sku.sku_spec_format,sku.price,sku.promotion_type,sku.stock,sku.click_num,sku.sale_num,sku.collect_num,sku.sku_image,sku.sku_images,sku.site_id,sku.goods_content,sku.goods_state,sku.verify_state,sku.is_virtual,sku.is_free_shipping,sku.goods_spec_format,sku.goods_attr_format,sku.introduction,sku.unit,sku.video_url,sku.evaluate,sku.category_id,sku.category_id_1,sku.category_id_2,sku.category_id_3,sku.category_name,sg.seckill_id,sg.seckill_price,ps.seckill_start_time,ps.seckill_end_time,sg.id', $alias, $join);
		return $this->success($list);
	}
	
	/**
	 * 获取秒杀商品列表
	 * @param array $condition
	 * @param int $page
	 * @param int $page_size
	 * @param string $order
	 * @param string $field
	 * @return mixed
	 */
	public function getSeckillGoodsPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'npsg.id desc', $field = '')
	{
		if (empty($field)) {
			$field = ' ngs.sku_name, ngs.sku_id, ngs.sku_no, ngs.sku_spec_format, ngs.price, ngs.market_price,
	        ngs.cost_price, ngs.stock, ngs.weight, ngs.volume,
	        ngs.click_num, ngs.sale_num, ngs.collect_num, ngs.sku_image, ngs.sku_images,
	        ngs.goods_class, ngs.goods_id, ngs.goods_attr_class, ngs.goods_attr_name,
	        ngs.goods_name,ngs.site_id,ngs.site_name,npsg.id,npsg.seckill_id, npsg.seckill_price, npsg.seckill_id,
	        nps.seckill_start_time, nps.seckill_end_time, nps.name,npsg.status,npsg.stock,npsg.remain_stock,npsg.sale_num';
		}
		
		$alias = 'npsg';
		$join = [
			[ 'goods_sku ngs', 'npsg.sku_id = ngs.sku_id', 'inner' ],
			[ 'promotion_seckill nps', 'npsg.seckill_id = nps.seckill_id', 'inner' ],
		];
		$list = model('promotion_seckill_goods')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
		return $this->success($list);
	}

	/**
	 * [newSeckillGoodsPageList skuid换成goodsid，新方法]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T17:34:55+0800
	 * @param    array                    $condition [description]
	 * @param    integer                  $page      [description]
	 * @param    [type]                   $page_size [description]
	 * @param    string                   $order     [description]
	 * @param    string                   $field     [description]
	 * @return   [type]                              [description]
	 */
	public function newSeckillGoodsPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'npsg.id desc', $field = '')
	{
		if (empty($field)) {
			$field = 'g.price, g.market_price,g.cost_price, g.stock,g.reward_shop_rate,g.sale_num, g.collect_num, g.goods_image,
	        g.goods_class, g.goods_id, g.goods_attr_class, g.goods_attr_name,g.goods_name,g.site_id,g.site_name,
	        nps.seckill_start_time, nps.seckill_end_time, nps.name,
	        npsg.id,npsg.seckill_id, npsg.seckill_price, npsg.seckill_id,npsg.status,npsg.stock,npsg.remain_stock,npsg.sale_num';
		}
		
		$alias = 'npsg';
		$join = [
			[ 'goods g', 'npsg.sku_id = g.goods_id', 'inner' ],
			[ 'promotion_seckill nps', 'npsg.seckill_id = nps.seckill_id', 'inner' ],
		];
		$list = model('promotion_seckill_goods')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
		return $this->success($list);
	}

	/**
	 * [changeStatus 秒杀商品上下架]
	 * <AUTHOR>
	 * @DateTime 2020-10-14T13:15:33+0800
	 * @param    [type]                   $id     [description]
	 * @param    [type]                   $status [description]
	 * @return   [type]                           [description]
	 */
	public function changeStatus($id,$status){
		$psgMl = model("promotion_seckill_goods");
        $where = [];
        $data['status'] = $status;
        if(is_array($id)){
            $where[]  = ['id', 'in', $id];
        }else{
            $where[]  = ['id', '=', $id];
        }

//        $seckStatus = model('promotion_seckill')->getValue(['seckill_id'=>$res['seckill_id']], 'status');
//		if ($seckStatus==1) {
//			return $this->error('','不能编辑正在进行中的活动商品');
//		}

        $goods_info = $psgMl->getInfo(["id"=>$id],"sku_id,seckill_price");
        if(!empty($goods_info) && $status == 1){
            $goods_obj = GoodsModel::where(["goods_id"=>$goods_info["sku_id"],"goods_state"=>1])
                ->with(['skus'=>function ($query){
                    $query->where(["goods_state"=>1])->field("goods_id,max(cost_price) as cost_price,min(market_price) as market_price")->group('goods_id');
                }])
                ->field("goods_id")->find();
            if(empty($goods_obj)){
                return $this->error("","商品尚未上架，请上架商品后再配置上架到本秒杀活动");
            }else{
                if($goods_info['seckill_price'] < $goods_obj->skus[0]['cost_price']){
                    return  $this->error("","当前秒杀价低于商品最小成本价");
                }
                if($goods_info['seckill_price'] > $goods_obj->skus[0]['market_price']){
                    return  $this->error("","当前秒杀价高于商品最高原价（划线价）");
                }
            }
        }
		$psgMl->update($data, $where);
		return $this->success();
    }

    /**
     * [editSeckillStock 编辑秒杀库存]
     * <AUTHOR>
     * @DateTime 2020-10-14T14:23:45+0800
     * @return   [type]                   [description]
     */
    public function editSeckillStock($id, $stock = 0)
    {
        $psgMl = model("promotion_seckill_goods");
        $where['id'] = $id;
        $field = 'stock,remain_stock,seckill_id,sale_num,sku_id';
        $info = $psgMl->getInfo($where, $field);
        if (!$info) {
            return $this->error('', '秒杀商品不存在');
        }
        $goods = model('goods')->getInfo([
            'goods_id' => $info['sku_id']
        ], 'goods_stock');
        if (empty($goods)) {
            return $this->error('', '秒杀商品不存在');
        } elseif ($stock > $goods['goods_stock']) {
            return $this->error('', "库存不能大于商品总库存{$goods['goods_stock']}个");
        }

//    	$seckStatus = model('promotion_seckill')->getValue(['seckill_id'=>$info['seckill_id']], 'status');
//		if ($seckStatus==1) {
//			return $this->error('','不能编辑正在进行中的活动商品');
//		}

        // 未付款库存
        $unpaidStock = Db::name('order_goods')
            ->alias('og')
            ->leftJoin('order o','og.order_id=o.order_id')
            ->where(['pay_status' => 0, 'order_status' => 0, 'order_create_type' => 3, 'og.goods_id' => $info['sku_id'], 'og.refund_status'=>0])->sum('og.num');

        // 最小库存 = 销量 + 未付款库存
        $minStock = $info['sale_num'] + $unpaidStock;

        // 库存不能小于最小库存，否则会出现超买情况
        if ($minStock > $stock) {
            return $this->error('', '库存必须大于或等于已用库存' . $minStock);
        }

        // 剩余销量 = 库存 - 最小库存
        $data['remain_stock'] = $stock - $minStock;

        $data['stock'] = $stock;

        $psgMl->update($data, $where);
        return $this->success();
    }

    /**
     * [changeSeckillStatus 秒杀活动上下架]
     * <AUTHOR>
     * @DateTime 2020-10-14T14:58:45+0800
     * @return   [type]                   [description]
     */
    public function changeSeckillStatus($seckill_id,$status){
    	$seckillMl = model("promotion_seckill");
        $where['seckill_id']    = $seckill_id;
        $data['status'] = $status;

        $res = $seckillMl->getCount($where);
        if (!$res) {
        	return $this->error('','活动不存在');
        }
		$res = $seckillMl->update($data, $where);
		Cache::tag("promotion_seckill")->clear();
		return $this->success();
    }

    /**
     * 编辑秒杀商品排序
     * @param int $seckill_id   秒杀id
     * @param int $sort         排序0-99
     * @return array
     */
    public function editSeckillGoodsSort(int $seckill_id, int $sort)
    {
        $psgMl = model("promotion_seckill_goods");
        $where['id'] = $seckill_id;

        if ($sort < 0 || $sort > 999) {
            return $this->error('', '只支持0-999范围');
        }

        $info = $psgMl->getInfo($where, 'id,sort');
        if (!$info) {
            return $this->error('', '秒杀商品不存在');
        }

        $psgMl->update(['sort' => $sort], $where);
        return $this->success();
    }

    /**
     * 编辑秒杀商品限购数量
     * @param int $seckill_id   秒杀id
     * @param int $buyLimit        限购数量
     * @return array
     */
    public function editBuyLimit(array $seckill_ids, int $buyLimit)
    {
        $psgMl = model("promotion_seckill_goods");
        $where[] = ['id', 'in', $seckill_ids];

        if ($buyLimit < 1) {
            return $this->error('', '限购数量必须大于0');
        }

        $psgMl->update(['buy_limit' => $buyLimit], $where);
        return $this->success();
    }
}