<?php


namespace addon\multipleDiscount\model;


use addon\multipleDiscount\constant\MULTIPLE_DISCOUNT_STATUS;
use app\model\goods\GoodsSkuModel;

class MultipleDiscountRepository
{
    /**
     * 获取所有进行中的多件折扣活动
     * @param int $skuId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllStart()
    {
        return MultipleDiscountModel::where("status", MULTIPLE_DISCOUNT_STATUS::START)
            ->where("start_time", "<=", time())
            ->where("over_time", ">", time())
            ->order("start_time", "asc")
            ->order("create_time", "asc")
            ->select();
    }

    /**
     * 获取所有进行中的单品多件折扣活动
     * @param int $skuId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllSingleStart()
    {
        return MultipleDiscountModel::where("status", MULTIPLE_DISCOUNT_STATUS::START)
            ->where("start_time", "<=", time())
            ->where("over_time", ">", time())
            ->where("type", 1)
            ->order("start_time", "asc")
            ->select();
    }

    /**
     * 根据skuid获取所有参与的多件折扣活动
     * @param $skuId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getParticipateDiscountBuSkuId($skuId)
    {
        $allDiscount = $this->getAllStart();
        $mDiscountIds = [];

        foreach($allDiscount as $discountModel)
        {
            $discountAction = new MultipleDiscountAction($discountModel);

            if($discountAction->checkScenario($skuId) && $discountAction->checkGoodsTypeBySkuId($skuId))
            {
                //该SKU符合优惠范围的第一个优惠是多品多折的话则属于多品多折，否则属于单品多折
                if($discountModel->type == 2 && empty($mDiscountIds))
                {
                    $mDiscountIds[] = $discountModel->multiple_discount_id;
                    break;
                }
                elseif($discountModel->type == 1)
                    $mDiscountIds[] = $discountModel->multiple_discount_id;
            }
        }
        return $allDiscount->whereIn("multiple_discount_id", $mDiscountIds);
    }
}