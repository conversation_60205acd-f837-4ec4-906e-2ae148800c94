<?php

namespace addon\multipleDiscount\model;

use app\model\BaseModel;
use app\service\shop\ShopGoodsService;
use think\facade\Db;

class MultipleDiscountGoods extends BaseModel
{

    /**
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @param ...$otherParams
     * @return array
     * <AUTHOR>
     */
    public function multipleDiscountGoodsPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'mdg.id desc', $field = '', ...$otherParams): array
    {
        if (empty($field)) {
            $field = 'g.price, g.market_price, g.cost_price,g.stock, g.reward_shop_rate, g.sale_num, g.collect_num, g.goods_image,
	        g.goods_class, g.goods_id, g.goods_attr_class, g.goods_attr_name, g.goods_name, g.site_id, g.site_name,
	        md.end_time, md.multiple_discount_name,
	        mdg.id, mdg.multiple_discount_id, mdg.status';
        }

        $alias = 'mdg';
        $join = [
            [ 'goods g', 'mdg.goods_id = g.goods_id', 'inner' ],
            [ 'multiple_discount md', 'md.multiple_discount_id = mdg.multiple_discount_id', 'inner' ],
        ];
        $list = model('multiple_discount_goods')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
        $selectedGoods = Db::table('xm_multiple_discount_goods')->alias('mdg')
            ->leftJoin('multiple_discount md', 'md.multiple_discount_id = mdg.multiple_discount_id')
            ->where('md.over_time', '>', time())
            ->where('mdg.status', 1)
            // ->where('mdg.multiple_discount_id', $otherParams[0])
            ->column('mdg.goods_id');
        $list['selected_goods'] = $selectedGoods;
        return $this->success($list);
    }


    /**
     * @param array $data
     * @return array
     */
    public function addGoods(array $data)
    {
        return $this->success(model("multiple_discount_goods")->addList($data));
    }


    /**
     * @param array $data
     * @return array
     */
    public function udpateGoods(array $data, array $where)
    {
        return $this->success(model("multiple_discount_goods")->update($data, $where));
    }


    /**
     * 删除平台商品优惠券关联的商品
     * @param unknown $discount_id
     * @param unknown $sku_id
     * @param unknown $site_id
     */
    public function deleteMultipleDiscountGoods(int $multipleDiscountId, int $goodsId): array
    {
        $discount_info = model('multiple_discount')->getInfo([ [ 'multiple_discount_id', '=', $multipleDiscountId ] ], 'status');
        if (!empty($discount_info)) {
            $res = model('multiple_discount_goods')->update(['status' => -1], [ [ 'multiple_discount_id', '=', $multipleDiscountId ], [ 'goods_id', '=', $goodsId ] ]);
            return $this->success($res);
        } else {
            return $this->error('', '活动不存在');
        }
    }


    /**
     * @param int $memberId
     * @param int $multipleDiscountId
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @return array
     * <AUTHOR>
     */
    public function getMultipleDiscountGoods(int $memberId, int $multipleDiscountId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'gg.id desc'): array
    {
        $fields = 'gg.id as multiple_discount_goods_id, gg.multiple_discount_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        $condition[] = ['md.use_scenario', '=', 3];
        $condition[] = ['md.status', '=', 1];
        $condition[] = ['md.multiple_discount_id', '=', $multipleDiscountId];
        $condition[] = ['gg.status', '=', 1];
        $alias = 'md';
        $join = [
            [ 'multiple_discount_goods gg', 'md.multiple_discount_id = gg.multiple_discount_id', 'inner' ],
            [ 'goods g', 'gg.goods_id = g.goods_id', 'inner' ],
        ];

        $list = model('multiple_discount')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);

        return $this->success($list);
    }


    /**
     * @param int $memberId
     * @param int $multipleDiscountId
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @return array
     * <AUTHOR>
     */
    public function getMultipleDiscountCategoryGoodsList(int $memberId, int $multipleDiscountId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc'): array
    {
        $multipleDiscountInfo = model('multiple_discount')->getInfo(['multiple_discount_id' => $multipleDiscountId]);
        $categoryArr = explode(',', $multipleDiscountInfo['category_ids']);
        $categoryCondition = 'g.category_id_' . count($categoryArr);
        $fields = 'md.multiple_discount_name, md.multiple_discount_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        $condition[] = ['md.use_scenario', '=', 2];
        $condition[] = ['md.multiple_discount_id', '=', $multipleDiscountId];

        $alias = 'md';
        $join = [
            [ 'goods g', 'find_in_set(' . $categoryCondition . ', md.category_ids)', 'inner' ],
        ];

        $list = model('multiple_discount')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);
        return $this->success($list);
    }


    /**
     * @param int $memberId
     * @param int $multipleDiscountId
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @return array
     * <AUTHOR>
     */
    public function getMultipleDiscountExcludeGoodsList(int $memberId, int $multipleDiscountId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc'): array
    {
        $fields = 'gg.id as multiple_discount_goods_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        // $condition[] = ['gp.use_scenario', '=', 4];
        $condition[] = ['', 'exp', Db::raw(' gg.id is null')];
        // $condition[] = ['gp.goodscoupon_type_id', '=', $goodsCouponTypeId];
        $alias = 'g';
        $join = [
            [ 'multiple_discount_goods gg', 'gg.goods_id = g.goods_id and gg.status = 1 and gg.multiple_discount_id = ' . $multipleDiscountId, 'left' ],
        ];

        $list = model('goods')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);

        return $this->success($list);
    }


    /**
     * @param int $memberId
     * @param int $multipleDiscountId
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @return array
     * <AUTHOR>
     */
    public function getTopicGoodsList(int $memberId, int $multipleDiscountId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc'): array
    {
        $multipleDiscountInfo = model('multiple_discount')->getInfo(['multiple_discount_id' => $multipleDiscountId]);
        $topicArr = explode(',', $multipleDiscountInfo['category_ids']);
        $topicCondition = 'g.category_id_' . count($topicArr);
        $fields = 'g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        $condition[] = ['ptg.status', '=', 1];
        $condition[] = ['ptg.topic_id', 'in', $topicArr];

        $alias = 'ptg';
        $join = [
            [ 'goods_sku gs', 'gs.sku_id = ptg.sku_id','left'],
            [ 'goods g', 'g.goods_id = gs.goods_id', 'left' ]
        ];

        $list = model('promotion_topic_goods')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join, 'g.goods_id');

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);
        return $this->success($list);
    }
}