<?php


namespace addon\multipleDiscount\model;

use addon\multipleDiscount\constant\MULTIPLE_DISCOUNT_STATUS;
use addon\multipleDiscount\constant\MULTIPLE_DISCOUNT_USE_SCENARIO;
use app\model\goods\GoodsCategoryModel;
use app\model\goods\GoodsSkuModel;
use app\model\goods\TopicGoodsModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyGoodsPriceInfoObject;
use app\model\object\BuyInfoObject;
use app\model\order\OrderCreateNew;
use app\model\order\OrderCreateNew as OrderCreateModel;
use app\service\Activity\SeckillService;
use app\service\shop\ShopGoodsService;

class MultipleDiscountAction
{
    public $platformcoupon = null;

    /**
     * @var MultipleDiscountModel
     */
    public $mDiscount;

    public function __construct(MultipleDiscountModel $mDiscount)
    {
        $this->mDiscount = $mDiscount;
    }

    /**
     * 检测商品类型
     * @param BuyGoodsObject $goodsObject
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkGoodsType(BuyGoodsObject $goodsObject)
    {
        return $this->checkGoodsTypeBySkuId($goodsObject->skuId);
    }

    /**
     * 检查商品类型是否能参与活动，  因为拼团商品详情页入口不一样，没加拼团的判断
     * @param $skuId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkGoodsTypeBySkuId($skuId)
    {
        $goodsId = GoodsSkuModel::find($skuId)->goods_id;
        $maidou_tag = ShopGoodsService::getInstance()->getIsTagGoods('maidou',$goodsId);

        $seckill = SeckillService::getInstance()->getShopSeckillByGoodsId(0,[$goodsId]);
        $seckill_goods = array_column($seckill,'goods_id');
        $is_seckill   = in_array($goodsId,$seckill_goods)? 1: 0; // 是否秒杀商品

        if($maidou_tag || $is_seckill)
            return false;
        return  true;
    }


    /**
     * 计算折扣金额
     * @param float $goodsMoney 商品总金额
     * @param float $promotionMoney 供应商优惠金额
     */
    public function calculateDiscountMoney(float $goodsMoney, float $promotionMoney)
    {
        return ($goodsMoney - $promotionMoney) * (1 - $this->mDiscount->discount * 0.1);
    }

    public function checkScenario($skuId)
    {
        if ($this->mDiscount->use_scenario == MULTIPLE_DISCOUNT_USE_SCENARIO::ALL)
            return true;
        if ($this->mDiscount->use_scenario == MULTIPLE_DISCOUNT_USE_SCENARIO::CATEGORY)
        {
            $skuCategoryId = [];
            //$useCategory = explode(",",$this->mDiscount->category_ids);
            $useCategory = $this->mDiscount->category_ids;

            $skuModel = GoodsSkuModel::find($skuId);

            if($skuModel->category_id_3 > 0)
                $skuCategoryId[] = $skuModel->category_id_3;
            if ($skuModel->category_id_2 > 0)
                $skuCategoryId[] = $skuModel->category_id_2;
            if ($skuModel->category_id_1 > 0)
                $skuCategoryId[] = $skuModel->category_id_1;

            if(in_array($useCategory, $skuCategoryId))
                return  true;
        }
        if ($this->mDiscount->use_scenario == MULTIPLE_DISCOUNT_USE_SCENARIO::GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;

            $dGoods = $this->mDiscount->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", 0);
            $dSku = $this->mDiscount->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", $skuId);
            if($dGoods->count() > 0 || $dSku->count() > 0)
            {
                return true;
            }
        }
        if ($this->mDiscount->use_scenario == MULTIPLE_DISCOUNT_USE_SCENARIO::EXCLUDE_GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;
            $useGoodsIds = $this->mDiscount->useGoods->column("goods_id");
            $useSkuIds = $this->mDiscount->useGoods->column("sku_id");
            if(in_array($goodsModel->goods_id, $useGoodsIds) || in_array($skuId, $useSkuIds))
            {
                return false;
            }
            return true;
        }
        if ($this->mDiscount->use_scenario == MULTIPLE_DISCOUNT_USE_SCENARIO::TOPIC)
        {
            $topicIds = explode(",",$this->mDiscount->category_ids);
            $activityGoods = TopicGoodsModel::whereIn("topic_id", $topicIds)->where("sku_id", $skuId)->where("status", 1)->find();
            if($activityGoods)
                return true;
        }
        return false;
    }



    /**
     *
     * @param BuyGoodsObject[] $buyGoods
     * @return bool
     */
    public function checkAchieveLeast(array $buyGoods)
    {
        $price = (new BuyInfoObject($buyGoods))->calculateUseCouponTargetMoney();
        if($price >= $this->mDiscount->at_least)
            return true;
        return false;
    }

    public function end()
    {
        $this->mDiscount->status = MULTIPLE_DISCOUNT_STATUS::CLOSE;
        $this->mDiscount->over_time = time();
        $this->mDiscount->update_time = time();
        $this->mDiscount->save();
    }
}