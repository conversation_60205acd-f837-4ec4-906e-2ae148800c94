<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\multipleDiscount\model;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use app\model\BaseModel;
use think\facade\Db;

class OrderMultipleDiscount extends BaseModel
{
    public $table = 'order_multiple_discount';


    /**
     * Notes:
     * User: luoshiqiang
     * Date: 2021/11/29
     * Time: 10:17 上午
     * @param $condition
     * @param int $page
     * @param int $page_size
     * @param bool $isPage
     * @return array
     */
    public function getPageList($condition, $page = 1, $page_size = PAGE_LIST_ROWS, $isPage = true, $operateGroupId = 0): array
    {
        $field = 'md.multiple_discount_name, omd.multiple_discount_id, omd.id, o.member_id, md.at_least, md.discount, mem.username, 
        mem.nickname, mem.mobile, o.order_money, o.order_no, o.create_time, md.status, o.multiple_discount_money';

        $alias = 'omd';
        $join = [
            [
                'multiple_discount md',
                'md.multiple_discount_id = omd.multiple_discount_id',
                'left'
            ],
            [
                'order o',
                'o.order_id = omd.order_id',
                'left'
            ],
            [
                'member mem',
                'mem.member_id = o.member_id',
                'inner'
            ],
            [
                'operate_group_relation ogr',
                "ogr.relation_id = omd.multiple_discount_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT . "'",
                'left'
            ]
        ];

        if ($operateGroupId) {
            $condition[] = ['ogr.operate_group_id', '=', $operateGroupId];
        } else {
            $condition[] = ['', 'exp', Db::raw(' ogr.operate_group_relation_id is null ')];
        }

        if ($isPage) {
            $list = model("order_multiple_discount")->pageList($condition, $field, 'id desc', $page, $page_size, $alias, $join, 'omd.order_id');
        } else {
            $list = model("order_multiple_discount")->getList($condition, $field, 'id desc', $alias, $join, 'omd.order_id');
        }

        return $this->success($list);
    }


    /**
     * 后台列表统计
     * @param $data
     * @param $type
     * @return int|mixed
     */
    public function getMultipleDiscountStatistics($data, $type, $operateGroupId = 0)
    {
        $where = '1 and o.order_status in (-2, -3, 1, 3, 4, 10)';
        $onWhere = 'o.order_id = omd.order_id';
        if (isset($data['multiple_discount_id']) && $data['multiple_discount_id']) {
            $where .= " AND omd.multiple_discount_id = " . $data['multiple_discount_id'];
        }
        if (isset($data['multiple_discount_name']) && $data['multiple_discount_name']) {
            $where .= " AND md.multiple_discount_name like '%" . $data['multiple_discount_name'] . "'";
        }
        if (isset($data['username']) && $data['username']) {
            $where .= " AND (mem.username like '%" . $data['username'] . "' OR mem.nickname like '%" . $data['username'] . "%')";
        }
        if (isset($data['mobile']) && $data['mobile']) {
            $where .= " AND mem.mobile = " . $data['mobile'];
        }
        $field = '';

        if ($operateGroupId) {
            $where .= ' AND ogr.operate_group_id = ' . $operateGroupId;
        } else {
            $where .= ' AND ogr.operate_group_relation_id is null ';
        }

        switch ($type) {
            case 'discountOrderTotal':
                // if (isset($data['state']) && $data['state']) {
                //     $where .= " AND npc.state = " . $data['state'];
                // }

                $field .= 'count(distinct(omd.order_id)) as total';
//                $where .= ' group by omd.order_id';
                $sql = 'SELECT ' . $field . ' FROM xm_order_multiple_discount AS omd
                    LEFT JOIN xm_multiple_discount md ON md.multiple_discount_id = omd.multiple_discount_id
                    LEFT JOIN xm_order AS o ON ' . $onWhere . "
                    LEFT JOIN xm_member AS mem ON mem.member_id = o.member_id
                    LEFT JOIN xm_operate_group_relation AS ogr ON ogr.relation_id = omd.multiple_discount_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT . "'
                    where " . $where;
                break;
            case 'discountMoneyTotal':
                $field .= 'sum(multiple_discount_money) as total';
                $where .= ' group by omd.order_id';

                $sql = 'SELECT ' . $field . ' FROM ( SELECT o.multiple_discount_money FROM xm_order_multiple_discount AS omd
                    LEFT JOIN xm_multiple_discount md ON md.multiple_discount_id = omd.multiple_discount_id
                    LEFT JOIN xm_order AS o ON ' . $onWhere . "
                    LEFT JOIN xm_member AS mem ON mem.member_id = o.member_id
                    LEFT JOIN xm_operate_group_relation AS ogr ON ogr.relation_id = omd.multiple_discount_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT . "'
                    where " . $where  . ') tmp';
                break;
            case 'connectMoneyTotal':
                $field .= 'sum(order_money) as total';
                $where .= ' group by omd.order_id';

                $sql = 'SELECT ' . $field . ' FROM (SELECT o.order_money FROM xm_order_multiple_discount AS omd
                    LEFT JOIN xm_multiple_discount md ON md.multiple_discount_id = omd.multiple_discount_id
                    LEFT JOIN xm_order AS o ON ' . $onWhere . " 
                    LEFT JOIN xm_member AS mem ON mem.member_id = o.member_id
                    LEFT JOIN xm_operate_group_relation AS ogr ON ogr.relation_id = omd.multiple_discount_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT . "'
                    where " . $where . ') tmp';
                break;
            default:;
        }

        $result = Db::query($sql);

        return !is_null(empty($result) ? null : $result) ? (isset($result[0]['total']) ? $result[0]['total'] : 0) : 0;
    }
}