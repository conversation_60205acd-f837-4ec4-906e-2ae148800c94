<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\multipleDiscount\model;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 优惠券
 */
class MultipleDiscount extends BaseModel
{


	/**
	 * 获取编码
	 */
	public function getCode()
	{
		return random_keys(8);
	}
	
	/**
	 * 获取优惠券
	 * @param int $goodscoupon_type_id
	 * @param int $member_id
	 * @param int $get_type
	 * @return array
	 */
	public function receiveGoodscoupon($goodscoupon_type_id, $member_id, $get_type, $is_stock = 0, $is_limit = 1)
	{
		
		// 用户已领取数量
		if (empty($member_id)) {
			return $this->error('', '请先进行登录');
		}
		$goodscoupon_type_info = model('promotion_goodscoupon_type')->getInfo([ 'goodscoupon_type_id' => $goodscoupon_type_id ]);
		if (!empty($goodscoupon_type_info)) {
		    if ($goodscoupon_type_info['privacy_status'] == 0 && $get_type == 2) {
                return $this->error('', '内部券, 无法主动领取');
            }

			if ($goodscoupon_type_info['count'] > 0 && $goodscoupon_type_info['count'] == $goodscoupon_type_info['lead_count'] && $is_stock == 0) {
				return $this->error('', '来迟了该优惠券已被领取完了');
			}
			
			if ($goodscoupon_type_info['max_fetch'] != 0) {
				//限制领取
				$member_receive_num = model('promotion_goodscoupon')->getCount([
					'goodscoupon_type_id' => $goodscoupon_type_id,
					'member_id' => $member_id
				]);
				if ($member_receive_num >= $goodscoupon_type_info['max_fetch'] && $is_limit == 1) {
					return $this->error('', '该优惠券领取已达到上限');
				}
				
			}
			
			$data = [
				'goodscoupon_name' => $goodscoupon_type_info['goodscoupon_name'],
				'goodscoupon_type_id' => $goodscoupon_type_id,
				'goodscoupon_code' => $this->getCode(),
				'member_id' => $member_id,
				'at_least' => $goodscoupon_type_info['at_least'],
				'money' => $goodscoupon_type_info['money'],
				'state' => 1,
				'get_type' => $get_type,
				'fetch_time' => time(),
                'use_scenario' => $goodscoupon_type_info['use_scenario'],
                'is_shop_commission' => $goodscoupon_type_info['is_shop_commission'],
                'category_ids' => $goodscoupon_type_info['category_ids'],
                'over_time' => $goodscoupon_type_info['over_time'],
			];
			
			if ($goodscoupon_type_info['validity_type'] == 0) {
				$data['end_time'] = $goodscoupon_type_info['end_time'];
			} else {
				$data['end_time'] = (time() + $goodscoupon_type_info['fixed_term'] * 86400);
			}
			$res = model('promotion_goodscoupon')->add($data);
			if ($is_stock == 0) {
				model('promotion_goodscoupon_type')->setInc([ 'goodscoupon_type_id' => $goodscoupon_type_id ], 'lead_count');
			}
			return $this->success($res);
			
		} else {
			return $this->error('', '未查找到该优惠券');
		}
	}
	
	/**
	 * 使用优惠券
	 * @param $data
     * @return array
	 */
	public function useGoodscoupon($goodscoupon_id, $member_id, $use_order_id)
	{
		$data = array( 'use_order_id' => $use_order_id, 'use_time' => time(), 'state' => 2 );
		$condition = array(
			[ 'goodscoupon_id', '=', $goodscoupon_id ],
			[ 'member_id', '=', $member_id ],
			[ 'state', '=', 1 ]
		);
		$result = model("promotion_goodscoupon")->update($data, $condition);
		return $this->success($result);
	}
	
	/**
	 * 退还优惠券
	 * @param $goodscoupon_id
	 * @param $member_id
     * @return array
	 */
	public function refundGoodscoupon($goodscoupon_id, $member_id)
	{
		$result = model("promotion_goodscoupon")->update([ 'use_time' => 0, 'state' => 1, 'use_order_id'=>'' ], [ [ 'goodscoupon_id', '=', $goodscoupon_id ], [ 'member_id', '=', $member_id ], [ 'state', '=', 2 ] ]);
		return $this->success($result);
	}
	
	/**
	 * 获取优惠券信息
	 * @param unknown $goodscoupon_code 优惠券编码
	 * @param unknown $field
     * @return array
	 */
	public function getGoodscouponInfo($condition, $field = "*")
	{
		$info = model("promotion_goodscoupon")->getInfo($condition, $field);
		return $this->success($info);
	}
	
	/**
	 * 获取优惠券列表
	 * @param array $condition
	 * @param bool $field
	 * @param string $order
	 * @param null $limit
     * @return array
	 */
	public function getGoodscouponList($condition = [], $field = true, $order = '', $limit = null)
	{
		$list = model("promotion_goodscoupon")->getList($condition, $field, $order, '', '', '', $limit);
		return $this->success($list);
	}
	
	/**
	 * 获取优惠券列表
	 * @param array $condition
	 * @param number $page
	 * @param string $page_size
	 * @param string $order
	 * @param string $field
     * @return array
	 */
	public function getGoodscouponPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'fetch_time desc', $field = 'goodscoupon_id,goodscoupon_type_id,goodscoupon_code,member_id,use_order_id,at_least,money,state,get_type,fetch_time,use_time,end_time')
	{
		$list = model('promotion_goodscoupon')->pageList($condition, $field, $order, $page, $page_size);
		return $this->success($list);
	}
	
	/**
	 * 获取会员优惠券列表
	 * @param array $condition
	 * @param number $page
	 * @param number $page_size
     * @return array
	 */
	public function getMemberGoodscouponPageList($condition, $page = 1, $page_size = PAGE_LIST_ROWS, $isPage = true)
	{
		$field = 'npc.goodscoupon_name, npc.use_order_id, npc.goodscoupon_id, npc.goodscoupon_type_id, npc.goodscoupon_code, npc.member_id,
		npc.at_least, npc.money, npc.state, npc.get_type, npc.fetch_time, npc.use_time, npc.end_time,
		mem.username, mem.nickname, mem.mobile, npc.use_scenario, sum(o.order_money) order_money, GROUP_CONCAT(o.order_no) order_no';
		$alias = 'npc';
		$join = [
			[
				'member mem',
				'npc.member_id = mem.member_id',
				'inner'
			],
			[
				'order o',
				'o.order_id in (npc.use_order_id)',
				'left'
			]
		];

		if ($isPage) {
            $list = model("promotion_goodscoupon")->pageList($condition, $field, 'fetch_time desc', $page, $page_size, $alias, $join, 'npc.goodscoupon_id');
        } else {
            $list = model("promotion_goodscoupon")->getList($condition, $field, 'fetch_time desc', $alias, $join, 'npc.goodscoupon_id');
        }

		return $this->success($list);
	}
	
	/**
	 * 获取优惠券信息
	 * @param array $condition
	 * @param unknown $field
     * @return array
	 */
	public function getGoodscouponTypeInfo($condition, $field = 'multiple_discount_id,multiple_discount_name,discount,max_fetch,at_least,use_scenario,status,start_time,over_time,is_use_goodscoupon,type')
	{
		$info = model("multiple_discount")->getInfo($condition, $field);
		return $this->success($info);
	}
	
	/**
	 * 获取优惠券列表
	 * @param array $condition
	 * @param bool $field
	 * @param string $order
	 * @param null $limit
     * @return array
	 */
	public function getGoodscouponTypeList($condition = [], $field = true, $order = '', $limit = null)
	{
		$list = model("promotion_goodscoupon_type")->getList($condition, $field, $order, '', '', '', $limit);
		return $this->success($list);
	}
	
	/**
	 * 获取优惠券分页列表
	 * @param $condition
	 * @param int $page
	 * @param int $page_size
	 * @return array
	 */
	public function getGoodscouponTypePageList($condition, $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'goodscoupon_type_id desc', $field = 'goodscoupon_type_id,goodscoupon_name,money,max_fetch,at_least,end_time,image,validity_type,fixed_term,status,is_show,use_scenario')
	{
		$list = model("promotion_goodscoupon_type")->pageList($condition, $field, $order, $page, $page_size);
		return $this->success($list);
	}
	
	/**
	 * 获取会员已领取优惠券优惠券
	 * @param array $member_id
     * @return array
	 */
	public function getMemberGoodscouponList($member_id, $state, $money = 0, $order = "fetch_time desc")
	{
		$condition = array(
			[ "member_id", "=", $member_id ],
			[ "state", "=", $state ],
//            [ "end_time", ">", time()]
		);
		if ($money > 0) {
//            $condition[] = [ "at_least", "=", 0 ];
			$condition[] = [ "at_least", "<=", $money ];
		}
		$list = model("promotion_goodscoupon")->getList($condition, "*", $order, '', '', '', 0);
		return $this->success($list);
	}

    /**
     * @param $condition
     * @return array
     */
	public function getMemberGoodscouponCount($condition)
	{
		$list = model("promotion_goodscoupon")->getCount($condition);
		return $this->success($list);
	}
	
	/**
	 * 增加库存
	 * @param $param
     * @return array
	 */
	public function incStock($param)
	{
		$condition = array(
			[ "goodscoupon_type_id", "=", $param["goodscoupon_type_id"] ]
		);
		$num = $param["num"];
		$goodscoupon_info = model("promotion_goodscoupon_type")->getInfo($condition, "count,lead_count");
		if (empty($goodscoupon_info))
			return $this->error(-1, "");
		
		//编辑优惠券库存
		$result = model("promotion_goodscoupon_type")->setDec($condition, "lead_count", $num);
		return $this->success($result);
	}
	
	/**
	 * 减少库存
	 * @param $param
     * @return array
	 */
	public function decStock($param)
	{
		$condition = array(
			[ "goodscoupon_type_id", "=", $param["goodscoupon_type_id"] ]
		);
		$num = $param["num"];
		$goodscoupon_info = model("promotion_goodscoupon_type")->getInfo($condition, "count,lead_count");
		if (empty($goodscoupon_info))
			return $this->error(-1, "找不到可发放的优惠券");
		
		//编辑sku库存
		if (($goodscoupon_info["count"] - $goodscoupon_info["lead_count"]) < $num)
			return $this->error(-1, "库存不足");
		
		$result = model("promotion_goodscoupon_type")->setInc($condition, "lead_count", $num);
		if ($result === false)
			return $this->error();
		
		return $this->success($result);
	}
	
	
	/**
	 * 定时关闭
	 * @return int
	 */
	public function cronGoodscouponEnd()
	{
		$res = model("promotion_goodscoupon")->update([ 'state' => 3 ], [ [ 'state', '=', 1 ], [ 'end_time', '<=', time() ] ]);
		return $res;
	}


    /**
     * 后台列表统计
     * @param $data
     * @param $type
     * @return int|mixed
     */
    public function getMultipleDiscountStatistics($data, $type)
    {
        $where = '1';
        $onWhere = 'o.order_id = npc.use_order_id';
        if (isset($data['multiple_discount_id']) && $data['multiple_discount_id']) {
            $where .= " AND omd.multiple_discount_id = " . $data['multiple_discount_id'];
        }
        if (isset($data['multiple_discount_name']) && $data['multiple_discount_name']) {
            $where .= " AND omd.multiple_discount_name like '%" . $data['multiple_discount_name'] . "'";
        }
        if (isset($data['username']) && $data['username']) {
            $where .= " AND (mem.username like '%" . $data['username'] . "' OR mem.nickname like '%" . $data['username'] . "%')";
        }
        if (isset($data['mobile']) && $data['mobile']) {
            $where .= " AND mem.mobile = " . $data['mobile'];
        }
        $field = '';
        switch ($type) {
            case 'discountOrderTotal':
                // if (isset($data['state']) && $data['state']) {
                //     $where .= " AND npc.state = " . $data['state'];
                // }

                $field .= 'count(distinct(omd.id)) as total';
                $where .= ' group by omd.order_id';
                break;
            case 'discountMoneyTotal':
                if (isset($data['state']) && $data['state']) {
                    $where .= " AND npc.state = " . $data['state'];
                }
                $field .= 'sum(npc.money) as total';
                break;
            case 'connectMoneyTotal':
                // $onWhere = 'o.order_id in (npc.use_order_id)';
                // $where .= " AND npc.state = 2";
                $field .= 'sum(o.order_money) as total';
                break;
            default:;
        }

        $sql = 'SELECT ' . $field . ' FROM xm_order_multiple_discount AS omd 
            INNER JOIN xm_member AS mem ON mem.member_id = npc.member_id
            LEFT JOIN xm_order AS o ON ' . $onWhere . ' 
            where ' . $where;
        $result = Db::query($sql);

        return !is_null($result[0]['total']) ? $result[0]['total'] : 0;
	}


    public function getSendPageData(array $data, bool $isPage = true)
    {
        $fields = 'm.member_id, m.mobile, m.nickname, m.reg_time'; //mm.nickname as parent_mobile, mm.mobile as parent_mobile, s.site_name';
        $alias = 'm';
        $join = [
            [
                'cron_log cl',
                'm. member_id = cl.id',
                'left'
            ]
            // [
            //     'shop_member sm',
            //     'sm.member_id = m.member_id and (sm.expire_time = -1 or (sm.expire_time > ' . time() . ' and sm.unlock_time = 0))',
            //     'left'
            // ],
            // [
            //     'shop s',
            //     's.site_id = sm.site_id',
            //     'left'
            // ],
            // [
            //     'member_recommend mr',
            //     'mr.member_id = m.member_id',
            //     'left'
            // ],
            // [
            //     'member mm',
            //     'mm.member_id = mr.pid',
            //     'left'
            // ],
        ];

        $condition = [];
        $condition[] = ['m.status', '=', 1];
        if (isset($data['mobile']) && $data['mobile']){
            $mobileArr = explode(',', urldecode($data['mobile']));
            $condition[] = ['m.mobile', 'in', $mobileArr];
        }

        if (isset($data['start_time']) && $data['start_time']) {
            $condition[] = ['m.reg_time', '>=', strtotime($data['start_time'])];
        }

        if (isset($data['end_time']) && $data['end_time']) {
            $condition[] = ['m.reg_time', '<=', strtotime($data['end_time'])];
        }

        if (isset($data['member_type']) && $data['member_type']) {
            $join[] = [
                'yp_sync_users ysu',
                'ysu.yp_mid = m.member_id',
                'left'
            ];

            switch ($data['member_type']) {
                case 1:
                    $condition[] = [
                        '',
                        'exp',
                        Db::raw(' ysu.yp_uid = 0 and ysu.is_director = 0 and ysu.is_agent = 0 and ysu.is_trustee = 0 and ysu.is_group_manager = 0 ')
                    ];
                    break;
                case 2:
                    $condition[] = ['ysu.yp_uid', '>', 0];
                    break;
                case 3:
                    $condition[] = ['ysu.is_trustee', '=', 1];
                    break;
                case 4:
                    $condition[] = ['ysu.is_group_manager', '=', 1];
                    break;
                default:;
            }
        }

        if (isset($data['receive_status']) && $data['receive_status']) {
            $join[] = [
                'promotion_goodscoupon gc',
                'gc.member_id = m.member_id and gc.goodscoupon_type_id = ' . $data['goodscoupon_type_id'],
                'left'
            ];

            if ($data['receive_status'] == 1) {
                $condition[] = ['', 'exp', Db::raw(' gc.goodscoupon_id is null ')];
            } else {
                $condition[] = ['gc.goodscoupon_id', '>', 0];
            }
        }

        if (isset($data['near_days']) && isset($data['user_behavior']) && $data['near_days'] && $data['user_behavior']) {
            $timeAgo = strtotime('-' . $data['near_days'] . ' days');
            if ($data['user_behavior'] == 1 && $data['times']) {
                $condition[] = [
                    'm.member_id',
                    'in',
                    Db::raw('
                        select uid from (select uid, count(uid) as times from xm_user_behavior_record where uid > 0 and platform = 1 and created_at > ' . $timeAgo . ' group by uid having times >= ' . $data['times'] . ') as tmp
                        ')
                ];
            } elseif ($data['user_behavior'] == 2 && $data['times']) {
                $condition[] = [
                    'm.member_id',
                    'in',
                    Db::raw('
                        SELECT member_id FROM (
                            SELECT
                                count( member_id ) AS times,
                                member_id 
                            FROM
                                (
                                SELECT
                                    member_id,
                                    out_trade_no 
                                FROM
                                    xm_order 
                                WHERE
                                    order_status IN ( 3, 4, 10 ) 
                                    AND pay_time >= ' . $timeAgo . ' 
                                GROUP BY
                                    out_trade_no 
                                ORDER BY
                                    out_trade_no DESC 
                                ) tmp1 
                            GROUP BY
                                member_id 
                            ) tmp2 
                        WHERE
                            times >= ' . $data['times']
                    )
                ];
            } elseif ($data['user_behavior'] == 3) {
                $condition[] = [
                    'm.member_id',
                    'not in',
                    Db::raw('
                        SELECT
                            member_id 
                        FROM
                            (
                            SELECT
                                member_id,
                                out_trade_no 
                            FROM
                                xm_order 
                            WHERE
                                order_status IN ( 3, 4, 10 ) 
                                AND pay_time >= ' . $timeAgo . ' 
                            GROUP BY
                                out_trade_no 
                            ORDER BY
                                out_trade_no DESC 
                            ) tmp1 
                        GROUP BY
                            member_id
                    ')
                ];
            }
        }

        if ($isPage) {
            $res = model('member')->pageList($condition, $fields, 'm.reg_time desc', $data['page'] ?? 1, $data['page_size'] ?? PAGE_LIST_ROWS, $alias, $join, 'm.member_id');
        } else {
            $res = model('member')->getList($condition, 'm.member_id', 'm.reg_time desc', $alias, $join, 'm.member_id');
        }
        if (!empty($res['list'])) {
            foreach ($res['list'] as $k => $v) {
                $memberBindShopCondition = [
                    ['member_id', '=', $v['member_id']],
                    ['', 'exp', Db::raw(' (expire_time = -1 or (expire_time > ' . time() . ' and unlock_time = 0)) ')]
                ];
                $memberBindShopId = model('shop_member')->getValue($memberBindShopCondition, 'site_id');
                $res['list'][$k]['site_name'] = model('shop')->getValue(['site_id' => $memberBindShopId], 'site_name');
                $parentMemberId = model('member_recommend')->getValue(['member_id' => $v['member_id']], 'pid');
                $res['list'][$k]['parent_name'] = model('member')->getValue(['member_id' => $parentMemberId], 'nickname');
                $res['list'][$k]['reg_time'] = date('Y-m-d H:i:s', $v['reg_time']);
            }
        }

        $res['goodsCouponTypeInfo'] = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $data['goodscoupon_type_id']]);
        return $this->success($res);
	}


    public function delCoupon(array $condition)
    {
        return model('promotion_goodscoupon')->delete($condition);
	}
}