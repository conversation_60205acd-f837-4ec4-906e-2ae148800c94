<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\multipleDiscount\model;

use app\model\BaseModel;
/**
 * 优惠券
 */
class MemberGoodscoupon extends BaseModel
{

    /**
     * 获取会员已领取优惠券优惠券
     * @param array $member_id
     */
    public function getMemberGoodscouponList($member_id, $state, $order = "fetch_time desc"){
        $condition = array(
            ["member_id", "=", $member_id],
            ["state", "=", $state],
        );

        $list = model("promotion_goodscoupon")->getList($condition, "*", $order, '', '', '', 0);
        return $this->success($list);
    }

    /**
     * 使用优惠券
     * @param $goodscoupon_id
     */
    public function useMemberGoodscoupon($goodscoupon_id, $member_id, $order_id = 0){
        //优惠券处理方案
        $result = model('promotion_goodscoupon')->update(['use_order_id' => $order_id, 'state' => 2, 'use_time' => time()], [['goodscoupon_id', '=', $goodscoupon_id], ["member_id", "=", $member_id], ['state', '=', 1]]);
        if($result === false){
            return $this->error();
        }
        return $this->success();
    }

    
    /**
     * 获取会员已领取优惠券优惠券数量
     * @param unknown $member_id
     * @param unknown $state
     * @return multitype:number unknown
     */
    public function getMemberGoodscouponNum($member_id, $state)
    {
        $condition = array(
            [ "member_id", "=", $member_id ],
            [ "state", "=", $state ],
        );

        $num = model("promotion_goodscoupon")->getCount($condition);
        return $this->success($num);
    }
    
    /**
     * 会员是否可领取该优惠券
     */
    public function receivedNum($goodscoupon_type_id, $member_id){
        $goodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodscoupon_type_id]);
        if (!$goodsCouponTypeInfo || $goodsCouponTypeInfo['status'] != 1) {
            return $this->success(['can_receive' => 0, 'notice_msg' => '活动已结束']);
        }

        if ($goodsCouponTypeInfo['privacy_status'] == 0) {
            return $this->success(['can_receive' => 0, 'notice_msg' => '内部券, 无法主动领取']);
        }

        $receivedNum = model('promotion_goodscoupon')->getCount([ ['goodscoupon_type_id', '=', $goodscoupon_type_id], ['member_id', '=', $member_id] ]);
        if ($receivedNum) {
            if ($goodsCouponTypeInfo['max_fetch'] > 0 && $receivedNum >= $goodsCouponTypeInfo['max_fetch']) {
                return $this->success(['can_receive' => 0, 'notice_msg' => '已领取']);
            }
        }
        // else
        // {
        if ($goodsCouponTypeInfo['count'] > 0 && $goodsCouponTypeInfo['lead_count'] >= $goodsCouponTypeInfo['count']) {
            return $this->success(['can_receive' => 0, 'notice_msg' => '已到领取上限']);
        }
        // }

        return $this->success(['can_receive' => 1, 'notice_msg' => '']);
    }
}