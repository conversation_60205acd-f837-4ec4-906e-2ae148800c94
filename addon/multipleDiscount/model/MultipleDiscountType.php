<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\multipleDiscount\model;

use addon\multipleDiscount\constant\MultipleDiscountScenario;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\service\OperateGroupRelationService;
use app\model\BaseModel;
use app\model\system\Cron;
use think\facade\Db;

/**
 * 优惠券活动
 */
class MultipleDiscountType extends BaseModel
{
	//优惠券类型状态
	private $multipleDiscountStatus = [
		1 => '进行中',
		2 => '已结束',
		-1 => '已关闭',
	];

    /**
     * @return string[]
     * <AUTHOR>
     */
	public function getMultipleDiscountStatus()
	{
		return $this->multipleDiscountStatus;
	}


    /**
     * @param $data
     * @param int $operateGroupId
     * @return array
     * <AUTHOR>
     */
	public function addMultipleDiscount($data, int $operateGroupId = 0)
	{
		//只要创建了就是进行中
		$data['status'] = 1;
		$res = model("multiple_discount")->add($data);
        if ($operateGroupId && $res) {
            $operateGroupRelationService = new OperateGroupRelationService();
            $operateGroupRelationService->save($operateGroupId, OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT, $res);
        }
        $cron = new Cron();
        $cron->addCron(1, 1, '多件折扣定时结束', 'CronMultipleDiscountEnd', $data['over_time'], $res);
		$this->qrcode($res, 'all', 'create');
		return $this->success($res);
	}

    /**
     * 编辑活动
     * @param array $data
     * @param int $multipleDiscountId
     * @return array
     * <AUTHOR>
     */
	public function editMultipleDiscount(array $data, int $multipleDiscountId): array
	{
		$res = model("multiple_discount")->update($data, [ [ 'multiple_discount_id', '=', $multipleDiscountId ] ]);
		$cron = new Cron();
		$cron->deleteCron([ ['event', '=', 'CronMultipleDiscountEnd'], [ 'relate_id', '=', $multipleDiscountId ] ]);
        $cron = new Cron();
        $cron->addCron(1, 1, '多件折扣定时结束', 'CronMultipleDiscountEnd', $data['over_time'], $multipleDiscountId);
		return $this->success($res);
	}
	
	/**
	 * 关闭活动
	 * @param $multipleDiscountId
	 * @return array|\multitype
	 */
	public function closeMultipleDiscount(int $multipleDiscountId)
	{
		$res = model('multiple_discount')->update([ 'status' => -1 ], [ [ 'multiple_discount_id', '=', $multipleDiscountId ] ]);
		$cron = new Cron();
		$cron->deleteCron([ ['event', '=', 'CronMultipleDiscountEnd'], [ 'relate_id', '=', $multipleDiscountId ] ]);
		return $this->success($res);
	}

    /**
     * 关闭活动
     * @param int $goodscoupon_type_id
     * @return array
     * <AUTHOR>
     */
    public function shutDownMultipleDiscount(int $multipleDiscountId): array
    {
        $nowTime = time();
        $res = model('multiple_discount')->update(['status' => 2, 'over_time' => $nowTime], [ [ 'multiple_discount_id', '=', $multipleDiscountId ] ]);
        // model('promotion_goodscoupon')->update(['state' => 3, 'end_time' => $nowTime, 'over_time' => $nowTime], ['multiple_discount_id' => $multipleDiscountId, 'state' => 1]);
        $cron = new Cron();
        $cron->deleteCron([ ['event', '=', 'CronMultipleDiscountEnd'], [ 'relate_id', '=', $multipleDiscountId ] ]);
        return $this->success($res);
    }

    /**
     * 删除活动
     *
     * @param int $multipleDiscountId
     * @return array
     * <AUTHOR>
     */
	public function deleteMultipleDiscount(int $multipleDiscountId): array
	{
		$res = model("multiple_discount")->delete([ [ 'multiple_discount_id', '=', $multipleDiscountId ] ]);
		$cron = new Cron();
		$cron->deleteCron([ ['event', '=', 'CronMultipleDiscountEnd'], [ 'relate_id', '=', $multipleDiscountId ] ]);
		return $this->success($res);
	}

    /**
     * 获取活动详情
     *
     * @param int $multipleDiscountId
     * @return array
     * <AUTHOR>
     */
	public function getMultipleDiscountInfo(int $multipleDiscountId): array
	{
		$res = model('multiple_discount')->getInfo([ [ 'multiple_discount_id', '=', $multipleDiscountId ] ]);
        if ($res['use_scenario'] == MultipleDiscountScenario::TOPIC) {
            $topicNameArr = model('promotion_topic')->getColumn([ ['topic_id', 'in', explode(',', $res['category_ids'])] ], 'topic_name');
            $res['category_name'] = implode(',', $topicNameArr);
        }
		return $this->success($res);
	}


    /**
     * 获取活动详情
     *
     * @param array $condition
     * @param string $field
     * @return array
     * <AUTHOR>
     */
    public function getInfo(array $condition = [], string $field= '*'): array
    {
        $info = model('multiple_discount')->getInfo($condition, $field);
        return $this->success($info);
    }

	/**
	 * 获取 优惠券类型列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param string $limit
	 */
	public function getGoodscouponTypeList($condition = [], $field = '*', $order = '', $limit = null)
	{
		$res = model('promotion_goodscoupon_type')->getList($condition, $field, $order, '', '', '', $limit);
		return $this->success($res);
	}
	
	/**
	 * 获取优惠券活动分页列表
	 * @param array $condition
	 * @param number $page
	 * @param string $page_size
	 * @param string $order
	 * @param string $field
	 */
	public function getGoodscouponTypePageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*', $alias = '', $join = [])
	{
		$list = model('multiple_discount')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
		return $this->success($list);
	}
	
	/**
	 * 生成优惠券二维码
	 * @param $goodscoupon_type_id
	 * @param string $app_type all为全部
	 * @param string $type 类型 create创建 get获取
	 * @return mixed|array
	 */
	public function qrcode($goodscoupon_type_id, $app_type, $type)
	{
		$res = event('Qrcode', [
			'app_type' => $app_type,
			'type' => $type,
			'data' => [
				'goodscoupon_type_id' => $goodscoupon_type_id
			],
			'page' => '/otherpages/goods/goodscoupon_receive/goodscoupon_receive',
			'qrcode_path' => 'upload/qrcode/goodscoupon',
			'qrcode_name' => 'goodscoupon_type_code_' . $goodscoupon_type_id,
		], true);
		return $res;
	}
	
	/**
	 * 活动定时结束
	 * @param unknown $goodscoupon_type_id
	 */
	public function goodscouponCronEnd($goodscoupon_type_id){
		$res = model('promotion_goodscoupon_type')->update([ 'status' => 2 ], [ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		return $this->success($res);
	}


    /**
     * 获取专题活动列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param string $limit
     */
    public function getTopicList($condition = [], $field = '*', $order = '', $limit = null)
    {
        $list = model('promotion_topic')->getList($condition, $field, $order, '', '', '', $limit);
        return $this->success($list);
    }



    public function getMultipleDiscountSingleCount(int $multipleDiscountId): int
    {
        $res = model('order_multiple_discount')->query('select count(DISTINCT(order_id)) as total from xm_order_multiple_discount where multiple_discount_id = ' . $multipleDiscountId);
        return $res[0]['total'];
    }
}