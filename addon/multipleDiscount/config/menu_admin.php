<?php
// +----------------------------------------------------------------------
// | 平台端菜单设置
// +----------------------------------------------------------------------
return [
    [
        'name' => 'PROMOTION_MULTIPLE_DISCOUNT',
        'title' => '多件折扣',
        'url' => 'multipleDiscount://admin/multipleDiscount/lists',
        'parent' => 'PROMOTION_PLATFORM',
        'is_show' => 0,
        'is_control' => 0,
        'is_icon' => 0,
        'picture' => '',
        'picture_select' => '',
        'sort' => 100,
        'child_list' => [
            [
                'name' => 'MULTIPLE_DISCOUNT_DETAIL',
                'title' => '多件折扣详情',
                'url' => 'multipleDiscount://admin/multipleDiscount/detail',
                'sort'    => 1,
                'is_show' => 0
            ],
            [
                'name' => 'MULTIPLE_DISCOUNT_ADD',
                'title' => '添加多件折扣',
                'url' => 'multipleDiscount://admin/multipleDiscount/add',
                'sort'    => 1,
                'is_show' => 0
            ],
            [
                'name' => 'MULTIPLE_DISCOUNT_EDIT',
                'title' => '编辑多件折扣',
                'url' => 'multipleDiscount://admin/multipleDiscount/edit',
                'sort'    => 1,
                'is_show' => 0
            ],
            [
                'name' => 'MULTIPLE_DISCOUNT_CLOSE',
                'title' => '关闭多件折扣',
                'url' => 'multipleDiscount://admin/multipleDiscount/close',
                'sort'    => 1,
                'is_show' => 0
            ],
            [
                'name' => 'MULTIPLE_DISCOUNT_DELETE',
                'title' => '删除多件折扣',
                'url' => 'multipleDiscount://admin/multipleDiscount/delete',
                'sort'    => 1,
                'is_show' => 0
            ],
            [
                'name' => 'MULTIPLE_DISCOUNT_RECEIVE',
                'title' => '多件折扣优惠记录',
                'url' => 'multipleDiscount://admin/multipleDiscount/receive',
                'sort'    => 1,
                'is_show' => 0
            ],

        ]
    ],
];
