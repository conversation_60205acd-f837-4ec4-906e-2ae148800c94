<?php
// 事件定义文件
return [
    'bind'      => [ 
    ],

    'listen'    => [

        //展示活动
        'ShowPromotion' => [
            'addon\multipleDiscount\event\ShowPromotion',
        ],
        //优惠券自动关闭
        'CronMultipleDiscountEnd' => [
            'addon\multipleDiscount\event\CronMultipleDiscountEnd',
        ],
    	// 优惠券活动定时结束
    	'CronMultipleDiscountTypeEnd' => [
            'addon\multipleDiscount\event\CronMultipleDiscountTypeEnd',
    	],
        'OrderClose' => [
            'addon\multipleDiscount\event\OrderClose',
        ]
    ],

    'subscribe' => [
    ],
];
