<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+

namespace addon\multipleDiscount\admin\controller;

use addon\multipleDiscount\constant\MultipleDiscountScenario;
use addon\multipleDiscount\model\MultipleDiscountExport;
use addon\multipleDiscount\model\MultipleDiscountGoods;
use addon\multipleDiscount\model\MemberGoodscoupon;
use addon\multipleDiscount\model\OrderMultipleDiscount;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use app\admin\controller\BaseAdmin;
use addon\multipleDiscount\model\MultipleDiscountType as MultipleDiscountTypeModel;
use addon\multipleDiscount\model\Goodscoupon as GoodscouponModel;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlWeappNoticeRepository;
use app\Domain\Services\WeappNotice\SendGoodscouponNoticeService;
use app\model\goods\GoodsCategory as GoodsCategoryModel;
use app\model\shop\ShopGroup as ShopGroupModel;
use think\facade\Db;
use think\facade\Log;

/**
 * 多件折扣
 * <AUTHOR>
 *
 */
class MultipleDiscount extends BaseAdmin
{
    protected $multipleDiscountModel;

    protected $orderMultipleDiscountModel;

    protected $multipleDiscountGoodsModel;


    public function __construct()
    {
        parent::__construct();
        $this->multipleDiscountModel = new MultipleDiscountTypeModel();
        $this->orderMultipleDiscountModel = new OrderMultipleDiscount();
        $this->multipleDiscountGoodsModel = new MultipleDiscountGoods();
    }


    /**
     * 活动列表
     */
    public function lists()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $multipleDiscountName = input('multiple_discount_name', '');
            $status = input('status', '');
            $useScenario = input('use_scenario');
            $condition = [];

            # 状态
            if ($status !== "") {
                if ($status == 3) {
                    $condition[] = ['md.start_time', '>', time()];
                } else {
                    $condition[] = ['md.status', '=', $status];
                }
            }
            # 使用范围
            if ($useScenario) {
                $condition[] = ['md.use_scenario', '=', $useScenario];
            }

            $condition[] = ['md.multiple_discount_name', 'like', '%' . $multipleDiscountName . '%'];
            $order = 'md.create_time desc';
            $field = 'md.*';

            if ($this->operate_group_id) {
                $condition[] = ['ogr.operate_group_id', '=', $this->operate_group_id];
            } else {
                $condition[] = ['', 'exp', Db::raw(' ogr.operate_group_relation_id is null ')];
            }

            $alias = 'md';
            $join = [
                ['operate_group_relation ogr', "ogr.relation_id = md.multiple_discount_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::MULTIPLE_DISCOUNT . "'", 'left']
            ];

            $res = $this->multipleDiscountModel->getGoodscouponTypePageList($condition, $page, $page_size, $order, $field, $alias, $join);

            //获取优惠券状态
            $multipleDiscountStatusArr = $this->multipleDiscountModel->getMultipleDiscountStatus();
            foreach ($res['data']['list'] as $key => $val) {
                $res['data']['list'][$key]['status_name'] = $multipleDiscountStatusArr[$val['status']];
                if ($res['data']['list'][$key]['start_time'] > time()) {
                    $res['data']['list'][$key]['status_name'] = '未开始';
                }
                $res['data']['list'][$key]['count'] = $this->multipleDiscountModel->getMultipleDiscountSingleCount($val['multiple_discount_id']);
            }
            return $res;
        } else {
            //优惠券状态
            $multipleDiscountStatusArr = $this->multipleDiscountModel->getMultipleDiscountStatus();
            $this->assign('multiple_discount_status_arr', $multipleDiscountStatusArr);

            //优惠券路径
            $miniProgramPath = '/promotionpages/special_offers/special_offers';
            $h5Path = addon_url('/mini-h5/promotionpages/special_offers/special_offers');
            $this->assign('miniProgramPath', $miniProgramPath);
            $this->assign('h5Path', substr($h5Path, 0, strpos($h5Path, '.html')));

            //店铺等级
            $shop_group_model = new ShopGroupModel();
            $shop_group = $shop_group_model->getGroupList([],'*');
            $this->assign('group_list', $shop_group['data']);

            return $this->fetch("multipleDiscount/lists");
        }
    }

    /**
     * 添加活动
     */
    public function add()
    {
        if (request()->isAjax()) {
            $firstCategory = input('category_id_1', '');
            $secondCategory = input('category_id_2', '');
            $thirdCategory = input('category_id_3', '');
            $topicList = input('topic_list', '');
            $category = [];
            $useScenario = input('use_scenario', 2);
            $firstCategory && array_push($category, $firstCategory);
            $secondCategory && array_push($category, $secondCategory);
            $thirdCategory && array_push($category, $thirdCategory);
            $categoryIds = implode(',', $category);
            $data = [
                'multiple_discount_name' => input('multiple_discount_name', ''),
                'discount' => input('discount', ''),
                'max_fetch' => input('max_fetch', 0),
                'at_least' => input('at_least', ''),
                'use_scenario' => input('use_scenario', 1),
                'category_ids' => $useScenario == MultipleDiscountScenario::TOPIC ? $topicList : $categoryIds,
                'category_name' => input('category_name', ''),
                'is_use_goodscoupon' => input('is_use_goodscoupon', 0),
                'create_time' => time(),
                'start_time' => strtotime(input('start_time', '')),
                'over_time' => strtotime(input('over_time', '')),
                'type' => input('type', 1),
            ];

            return $this->multipleDiscountModel->addMultipleDiscount($data, $this->operate_group_id);
        } else {
            $goods_category_list = (new GoodsCategoryModel())->getCategoryList(['pid' => 0], 'category_id,category_name,level,commission_rate,reward_company_rate');
            $goods_category_list = $goods_category_list[ 'data' ];
            $topicWhere = [
                ['status', 'in', [1, 2]],
            ];
            $topicList = $this->multipleDiscountModel->getTopicList($topicWhere, 'topic_id, topic_name');
            $tmp = [];
            if (!empty($topicList['data'])) {
                foreach ($topicList['data'] as $k => $v) {
                    $tmp[$k]['value'] = $v['topic_id'];
                    $tmp[$k]['name'] = $v['topic_name'];
                }
            }
            $this->assign("goods_category_list", $goods_category_list);
            $this->assign('topicList', $tmp);
            return $this->fetch("multipleDiscount/add");
        }
    }

    /**
     * 编辑活动
     */
    public function edit()
    {
        // if (request()->isAjax()) {
        //     $firstCategory = input('category_id_1', '');
        //     $secondCategory = input('category_id_2', '');
        //     $thirdCategory = input('category_id_3', '');
        //     $category = [];
        //     $firstCategory && array_push($category, $firstCategory);
        //     $secondCategory && array_push($category, $secondCategory);
        //     $thirdCategory && array_push($category, $thirdCategory);
        //     $categoryIds = implode(',', $category);
        //     $data = [
        //         'goodscoupon_name' => input('goodscoupon_name', ''),//优惠券名称
        //         'money' => input('money', ''),//优惠券面额
        //         'count' => input('count', ''),//发放数量
        //         'max_fetch' => input('max_fetch', ''),//最大领取数量
        //         'at_least' => input('at_least', ''),//满多少元可以使用
        //         'end_time' => strtotime(input('over_time', '')),//活动结束时间
        //         'image' => input('image', ''),//优惠券图片
        //         'validity_type' => input('validity_type', ''),//有效期类型 0固定时间 1领取之日起
        //         'fixed_term' => input('fixed_term', ''),//领取之日起N天内有效
        //         'use_scenario' => input('use_scenario', 1),//使用场景
        //         'category_ids' => $categoryIds,
        //         'category_name' => input('category_name', ''),
        //         'is_show' => 1,//是否允许直接领取 1:是 0：否 允许直接领取，用户才可以在手机端和PC端进行领取，否则只能以活动的形式发放。
        //         'create_time' => time(),
        //         'over_time' => strtotime(input('over_time', '')),
        //         'is_shop_commission' => input('is_shop_commission', 0),
        //     ];
        //
        //     $goodscoupon_type_id = input('goodscoupon_type_id', 0);
        //     $goodscoupon_type_model = new GoodscouponTypeModel();
        //     return $goodscoupon_type_model->editGoodscouponType($data, $goodscoupon_type_id);
        // } else {
        //     $goodscoupon_type_id = input('goodscoupon_type_id', 0);
        //     $this->assign('goodscoupon_type_id', $goodscoupon_type_id);
        //
        //     $goodscoupon_type_model = new GoodscouponTypeModel();
        //     $goodscoupon_type_info = $goodscoupon_type_model->getGoodscouponTypeInfo($goodscoupon_type_id);
        //
        //     $categoryIdArr = explode(',', $goodscoupon_type_info['data']['category_ids']);
        //     $goodscoupon_type_info['data']['category_id_1'] = $categoryIdArr[0] ?? '';
        //     $goodscoupon_type_info['data']['category_id_2'] = $categoryIdArr[1] ?? '';
        //     $goodscoupon_type_info['data']['category_id_3'] = $categoryIdArr[2] ?? '';
        //
        //     $goodscoupon_type_info['data']['over_time'] = date('Y-m-d H:i:s', $goodscoupon_type_info['data']['over_time']);
        //     $this->assign('goodscoupon_type_info', $goodscoupon_type_info['data']);
        //
        //     $goods_category_list = (new GoodsCategoryModel())->getCategoryList(['pid' => 0], 'category_id,category_name,level,commission_rate,reward_company_rate');
        //     $goods_category_list = $goods_category_list[ 'data' ];
        //     $this->assign("goods_category_list", $goods_category_list);
        //
        //     return $this->fetch("goodscoupon/edit");
        // }
    }

    /**
     * 活动详情
     */
    public function detail()
    {
        $multipleDiscountId = input('multiple_discount_id', 0);
        $multipleDiscountInfo = $this->multipleDiscountModel->getMultipleDiscountInfo($multipleDiscountId);

        $multipleDiscountInfo['data']['start_time'] = date('Y-m-d H:i:s', $multipleDiscountInfo['data']['start_time']);
        $multipleDiscountInfo['data']['over_time'] = date('Y-m-d H:i:s', $multipleDiscountInfo['data']['over_time']);

        $tmp = explode('.', $multipleDiscountInfo['data']['discount']);
        $multipleDiscountInfo['data']['discount'] = $tmp[1] > 0 ? $multipleDiscountInfo['data']['discount'] : $tmp[0];

        $this->assign('multipleDiscountInfo', $multipleDiscountInfo['data']);

        return $this->fetch("multipleDiscount/detail");
    }


    /**
     * 关闭活动
     */
    public function close()
    {
        if (request()->isAjax()) {
            $multipleDiscountId = input('multiple_discount_id', 0);
            return $this->multipleDiscountModel->closeMultipleDiscount($multipleDiscountId);
        }
    }

    /**
     * 删除活动
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $multipleDiscountId = input('multiple_discount_id', 0);
            return $this->multipleDiscountModel->deleteMultipleDiscount($multipleDiscountId);
        }
    }

    /**
     * 活动优惠记录
     * */
    public function receive()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $data['multiple_discount_id'] = input('multiple_discount_id', 0);
            $data['multiple_discount_name'] = input('multiple_discount_name', 0);
            $data['username'] = input('username', '');
            $data['mobile'] = input('mobile', '');
            $condition = [];
            if ($data['multiple_discount_id']) {
                $condition[] = ['omd.multiple_discount_id', '=', $data['multiple_discount_id']];
            }
            if ($data['multiple_discount_name']) {
                $condition[] = ['md.multiple_discount_name', 'like', '%' .  $data['multiple_discount_name'] . '%'];
            }
            if ($data['username']) {
                $condition[] = ['', 'exp', Db::raw(" (mem.username like '%" . $data['username'] . "' or mem.nickname like '%" . $data['username'] . "') ")];
            }
            if ($data['mobile']) {
                $condition[] = ['mem.mobile', '=', $data['mobile']];
            }
            $condition[] = ['o.order_status', 'in', [-2, -3, 1, 3, 4, 10]];
            $res = $this->orderMultipleDiscountModel->getPageList($condition, $page, $page_size, true, $this->operate_group_id);
            $res['data']['discountOrderTotal'] = $this->orderMultipleDiscountModel->getMultipleDiscountStatistics($data, 'discountOrderTotal', $this->operate_group_id);
            $res['data']['discountMoneyTotal'] = $this->orderMultipleDiscountModel->getMultipleDiscountStatistics($data, 'discountMoneyTotal', $this->operate_group_id);
            $res['data']['connectMoneyTotal'] = $this->orderMultipleDiscountModel->getMultipleDiscountStatistics($data, 'connectMoneyTotal', $this->operate_group_id);

            if (!empty($res['data']['list'])) {
                $list = $res['data']['list'];
                foreach ($list as $k => $info) {
                    $list[$k]['order_no_btn'] = '<a href="/admin/order/detail.html?order_no=' . $info['order_no'] . '" target="_blank" class="ns-btn-hover" lay-event="order">' . $info['order_no']  . '</a>';
                }
                $res['data']['list'] = $list;
            }

            return $res;
        } else {
            if ($multipleDiscountId= input('multiple_discount_id', 0)) {
                $multipleDiscountName = model('multipleDiscount')->getValue(['multiple_discount_id' => $multipleDiscountId], 'multiple_discount_name');
                $this->assign('multiple_discount_id', $multipleDiscountId);
                $this->assign('multiple_discount_name', $multipleDiscountName);
            }

            return $this->fetch("multipleDiscount/receive");
        }
    }


    /**
     * 关联商品管理
     * @return \think\response\View
     */
    public function goods()
    {
        $multipleDiscountId = input('multiple_discount_id', 0);
        $multipleDiscountGoods = model('multiple_discount_goods')->getInfo(['multiple_discount_id' => $multipleDiscountId]);
        return View('multipleDiscount/goods_list', compact('multipleDiscountGoods', 'multipleDiscountId'));
    }


    /**
     * 获取商品列表数据
     *
     * @return array
     */
    public function goodsData()
    {
        $multipleDiscountId = input('multiple_discount_id', 0);
        $page = input('page', 1);
        $page_size = input('page_size', PAGE_LIST_ROWS);
        $goods_name  = input('goods_name', '');
        $searchType = input('search_type/d', 1);
        $searchKeys = input('search_keys', '');
        $status = input('status', 0);
        $condition = [];

        switch ($searchType) {
            case 1:
                $condition[] = [ 'g.goods_name', 'like', '%' . $searchKeys . '%' ];
                break;
            case 2:
                if ($searchKeys) {
                    $condition[] = [ 'mdg.goods_id', '=', $searchKeys];
                }
        }

        $condition[] = [ 'mdg.status', '=', 1];
        $condition[] = [ 'g.goods_name', 'like', '%' . $goods_name . '%' ];
        $multipleDiscountId && $condition[] = [ 'mdg.multiple_discount_id', '=', $multipleDiscountId ];
        $order = 'mdg.id desc';
        $field = 'mdg.*, g.goods_name, g.cost_price ,g.goods_id ,g.price, g.goods_image, g.site_name, md.over_time, g.reward_shop_rate';

        $res = $this->multipleDiscountGoodsModel->multipleDiscountGoodsPageList($condition, $page, $page_size, $order, $field);

        foreach ($res['data']['list'] as $key => $val) {
            $res['data']['list'][ $key ]['sale_price'] = currencyFormat(currencyFormat($val['price'] * $val['reward_shop_rate'] * 0.01) + $val['price']);
        }
        return $res;
    }



    /**
     * [addGoods 添加关联商品]
     * @DateTime 2020-10-14T09:45:17+0800
     */
    public function addGoods()
    {
        try {
            $multipleDiscountId = input('multiple_discount_id', 0);
            $goods_ids  = input('goods_ids', '');

            if (!$goods_ids) throw new \Exception('请选择商品');

            $where[] = ['goods_id', 'in', explode(',', $goods_ids)];
            $goods_list = model("goods")->getList($where, 'goods_id, price, sku_id');

            $data = [];
            foreach ($goods_list as $val) {
                $goods_count = model("multiple_discount_goods")->getCount([ 'multiple_discount_id' => $multipleDiscountId, 'goods_id' => $val['goods_id'] ]);
                if (empty($goods_count)) {
                    $data[] = [
                        'multiple_discount_id' => $multipleDiscountId,
                        'goods_id' => $val['goods_id'],
                        'add_time' => time(),
                        'sku_id' => $val['sku_id']
                    ];
                } else {
                    (new MultipleDiscountGoods())->udpateGoods(['status' => 1], [ 'multiple_discount_id' => $multipleDiscountId, 'goods_id' => $val['goods_id'] ]);
                    continue;
                }
            }

            return (new MultipleDiscountGoods())->addGoods($data);
        } catch (\Exception $e) {
            return (new MultipleDiscountGoods())->error('', $e->getMessage());
        }
    }


    /**
     * 取消关联商品
     * @return array
     */
    public function deleteGoods()
    {
        if (request()->isAjax()) {
            $multipleDiscountId = input('multiple_discount_id', 0);
            $goodsId = input('goods_id', '');
            return (new MultipleDiscountGoods())->deleteMultipleDiscountGoods($multipleDiscountId, $goodsId);
        }
    }


    /**
     * 提前结束活动
     * @return array|\multitype
     */
    public function shutDown()
    {
        if (request()->isAjax()) {
            $multipleDiscountId = input('multiple_discount_id', 0);
            return $this->multipleDiscountModel->shutDownMultipleDiscount($multipleDiscountId);
        }
    }


    /**
     * 主动发券页面
     */
    public function sendPage()
    {
        $goodsCouponTypeId = input('goodscoupon_type_id', 0);
        $goodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodsCouponTypeId]);
        return View('send', compact('goodsCouponTypeId', 'goodsCouponTypeInfo'));
    }


    public function sendPageData()
    {
        try {
            return (new \addon\goodscoupon\model\MultipleDiscount())->getSendPageData(input());
        } catch (\Exception $e) {
            return (new \addon\goodscoupon\model\MultipleDiscount())->error('', $e->getMessage());
        }
    }


    /**
     * 发送给选择的用户
     * <AUTHOR>
     */
    public function sendToSelectedMember()
    {
        try {
            $memberIdArr = input('member_id', []);
            $goodsCouponTypeId = input('goodscoupon_type_id', 0);

            $sendCount = 0;
            foreach($memberIdArr as $k => $memberId) {
                $receiveStatus = (new MemberGoodscoupon())->receivedNum($goodsCouponTypeId, $memberId)['data'];
                if (!$receiveStatus['can_receive']) continue;

                $receiveRes = (new \addon\goodscoupon\model\MultipleDiscount())->receiveGoodscoupon($goodsCouponTypeId, $memberId, 4);
                // 发送通知
                if ($receiveRes['code'] == 0) {
                    $sendCount++;
                    $service = new SendGoodscouponNoticeService(new MysqlWeappNoticeRepository());
                    $service->execute($memberId, $goodsCouponTypeId);
                }
            }

            return (new \addon\goodscoupon\model\MultipleDiscount())->success(['send_count' => $sendCount]);
        } catch (\Exception $e) {
            return (new \addon\goodscoupon\model\MultipleDiscount())->error('', $e->getMessage());
        }
    }


    /**
     * 发送给筛选出来的用户
     * <AUTHOR>
     */
    public function sendToSearchedMember()
    {
        try {
            $goodsCouponTypeId = input('goodscoupon_type_id', 0);
            $res = (new \addon\goodscoupon\model\MultipleDiscount())->getSendPageData(input(), false)['data'];
            unset($res['goodsCouponTypeInfo']);

            $sendCount = 0;
            if (!empty($res)) {
                foreach($res as $k => $memberId) {
                    $receiveStatus = (new MemberGoodscoupon())->receivedNum($goodsCouponTypeId, $memberId['member_id'])['data'];
                    if (!$receiveStatus['can_receive']) continue;

                    $receiveRes = (new \addon\goodscoupon\model\MultipleDiscount())->receiveGoodscoupon($goodsCouponTypeId, $memberId['member_id'], 4);
                    // 发送通知
                    if ($receiveRes['code'] == 0) {
                        $sendCount++;
                        $service = new SendGoodscouponNoticeService(new MysqlWeappNoticeRepository());
                        $service->execute($memberId['member_id'], $goodsCouponTypeId);
                    }
                }
            }

            return (new \addon\goodscoupon\model\MultipleDiscount())->success(['send_count' => $sendCount]);
        } catch (\Exception $e) {
            return (new \addon\goodscoupon\model\MultipleDiscount())->error('', $e->getMessage());
        }
    }


    /**
     * 导出
     */
    public function export(){
        $data['multiple_discount_id'] = input('multiple_discount_id', 0);
        $data['multiple_discount_name'] = input('multiple_discount_name', 0);
        $data['username'] = input('username', '');
        $data['mobile'] = input('mobile', '');
        $condition = [];
        if ($data['multiple_discount_id']) {
            $condition[] = ['md.multiple_discount_id', '=', $data['multiple_discount_id']];
        }
        if ($data['multiple_discount_name']) {
            $condition[] = ['md.multiple_discount_name', 'like', '%' .  urldecode($data['multiple_discount_name']) . '%'];
        }
        if ($data['username']) {
            $condition[] = ['', 'exp', Db::raw(" (mem.username like '%" . urldecode($data['username']) . "' or mem.nickname like '%" . urldecode($data['username']) . "') ")];
        }
        if ($data['mobile']) {
            $condition[] = ['mem.mobile', '=', $data['mobile']];
        }
        $condition[] = ['o.order_status', 'in', [-2, -3, 1, 3, 4, 10]];
        $res = $this->orderMultipleDiscountModel->getPageList($condition, 1, PAGE_LIST_ROWS, false, $this->operate_group_id);
        
        if (!empty($res['data'])) {
            foreach ($res['data'] as $k => $v) {
                $res['data'][$k]['username'] = $v['username'] ?: ($v['nickname'] ?: $v['mobile']);
                $res['data'][$k]['order_no'] = "\t" . $v['order_no'] . "\t";
            }
        }

        $goodsCouponExportModel = new MultipleDiscountExport();
        $goodsCouponExportModel->export($res, '领取记录');
    }


    public function deleteMemberCoupon()
    {
        $goodsCouponModel = new \addon\goodscoupon\model\MultipleDiscount();
        try {
            $couponId = input('coupon_id', 0);
            $couponInfo = $goodsCouponModel->getGoodscouponInfo(['goodscoupon_id' => $couponId]);
            if (empty($couponInfo['data'])) throw new \Exception('找不到优惠券信息');
            $goodsCouponModel->delCoupon(['goodscoupon_id' => $couponId]);
            model('promotion_goodscoupon_type')->update(['lead_count' => Db::raw('lead_count - 1')], ['goodscoupon_type_id' => $couponInfo['data']['goodscoupon_type_id']]);
            return (new \addon\goodscoupon\model\MultipleDiscount())->success([]);
        } catch (\Exception $e) {
            return (new \addon\goodscoupon\model\MultipleDiscount())->error('', $e->getMessage());
        }
    }
}