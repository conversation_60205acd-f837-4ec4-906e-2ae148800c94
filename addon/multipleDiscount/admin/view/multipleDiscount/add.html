{extend name="app/admin/view/base.html"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
<script src="__STATIC__/js/xm-select.js"></script>

<style>
    .ns-form {
        /*margin-left: 80px;*/
        width: 800px;
    }
    /*.ns-form .layui-form-label{width: 120px;}*/
    /*.ns-form .layui-input-block{margin-left: 120px;}*/
    /*.ns-form .layui-input-block.block-style2{*/
    /*    margin: 10px 0;*/
    /*    border: 1px solid #f1f1f1;*/
    /*    padding: 20px;*/
    /*}*/
    .ns-form .layui-input-block.block-style2 > div{
        line-height: 18px;
        margin: 5px 0;
    }
    .ns-form .layui-input-block.block-style2 .title{
        border-left: 5px solid #4685FD;
        padding-left: 6px;
        margin-left: -10px;
    }
    .ns-form .layui-input-block.block-style2 .title span{
        color: red;
    }
    .ns-form .layui-input-block.block-style2 .desc{
        color: #aaa;
    }
    .ns-form .layui-input-block.block-style2 .desc span{
        color: black;
    }
</style>
{/block}
{/block}
{block name="main"}

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>优惠活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="multiple_discount_name" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>优惠政策：</label>
        <div class="layui-input-block nc-len-mid">
            <div class="layui-input-inline">
                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="1" title="单个商品满" checked></div>
                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="2" title="多个商品满"></div>
                <input type="number" min="1" name="at_least" lay-verify="required|int|at_least" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
                <div class="layui-input-inline">件，享</div><input type="number" name="discount" lay-verify="required|number|gtzero|discount" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
            </div>
            <span class="layui-form-mid">折</span>
        </div>
        <div class="ns-word-aux">
            <p>件数必需是大于0的整数；折扣需大于1，小于10，支持一位小数</p>
        </div>
    </div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动优惠上限：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				<input type="number" name="max_fetch" min="0" lay-verify="required|number|int" autocomplete="off" class="layui-input ns-len-short">
			</div>
		</div>
		<div class="ns-word-aux">
			<p>数量不能小于0，且必须为整数，优惠订单数达上限时自动结束该活动；<br />设置为0时，视为无上限</p>
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>选择活动商品：</label>
        <div class="layui-input-block">
            <div>
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">注意：秒杀、拼团和迈豆专区商品不可同时参与使用折扣优惠</span>
            </div>
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="1" title="全场通用" checked>
                <!--<span style="color:#B2B2B2; font-size:12px; line-height:1.6;">秒杀及拼团商品除外</span>-->
            </div>
            <div>
                <div class="layui-input-inline">
                    <input  type="radio" name="use_scenario" lay-filter="use_scenario" value="2" title="指定类目可用">
                </div>
                <input type="text" readonly onfocus="selectedCategoryPopup()" name="category_name" autocomplete="off" placeholder="选择分类" class="layui-input ns-len-mid" />
                <input type="hidden" name="category_id" />
                <input type="hidden" name="category_id_1" />
                <input type="hidden" name="category_id_2" />
                <input type="hidden" name="category_id_3" />
            </div>
            <div>
                <div class="layui-input-inline">
                    <div class="layui-input-inline"><input  type="radio" name="use_scenario" lay-filter="use_scenario" value="5" title="指定专区可用"></div>
                    <div class="content layui-input-inline" style="width: 400px;">
                        <div id="topic_list"></div>
                    </div>
                </div>
            </div>
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="3" title="指定商品可用">
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">创建后在优惠券列表页对应活动的“管理商品”中添加参与优惠的商品</span>
            </div>
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="4" title="指定商品不可用">
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">除指定商品外，其他全部商品均可参与优惠活动</span>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>是否叠加优惠：</label>
        <div class="layui-input-block">
            <input type="checkbox" name="is_use_goodscoupon" value="1" lay-filter="filter" title="允许与优惠券叠加使用">
            <div>
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">勾选后，商品可同时享受折扣和优惠券优惠</span>
            </div>
        </div>
    </div>

	<!--<div class="layui-form-item">-->
	<!--	<label class="layui-form-label img-upload-lable"><span class="required">*</span>优惠券图片：</label>-->
	<!--	<div class="layui-input-block img-upload">-->
	<!--		<input type="hidden" class="layui-input" name="image" lay-verify="image"/>-->
	<!--		<div class="upload-img-block">-->
	<!--			<div class="upload-img-box" id="goodscouponImg">-->
	<!--				<div class="ns-upload-default">-->
	<!--					<img src="SHOP_IMG/upload_img.png" />-->
	<!--					<p>点击上传</p>-->
	<!--				</div>-->
	<!--			</div>-->
	<!--		</div>-->
	<!--	</div>-->
	<!--	<div class="ns-word-aux">-->
	<!--		<p>建议尺寸：702*410像素</p>-->
	<!--		<p>图片上传默认不限制大小</p>-->
	<!--	</div>-->
	<!--</div>-->
    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label"><span class="required">*</span>活动开始时间：</label>
        <div class="layui-input-block">
            <input type="text" name="start_time" lay-verify="startTime" id="start_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
        </div>
        <div class="ns-word-aux">
            <p>到时间活动自动开始</p>
        </div>
    </div>
	<div class="layui-form-item ns-end-time">
		<label class="layui-form-label"><span class="required">*</span>活动结束时间：</label>
		<div class="layui-input-block">
			<input type="text" name="over_time" lay-verify="overTime" id="over_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
		</div>
        <div class="ns-word-aux">
            <p>到期后活动自动结束</p>
        </div>
	</div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

<!--选择商品分类-->
<script type="text/html" id="selectedCategory">

    <div class="category-list">

        <div class="item">
            <!--后续做搜索-->
            <ul>
                {foreach name="$goods_category_list" item="vo"}
                {{# if(d.category_id_1 == '{$vo['category_id']}' ){ }}
                <li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-reward_company_rate="{$vo['reward_company_rate']}" data-level="{$vo['level']}" class="selected">
                    {{# }else{ }}
                <li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-reward_company_rate="{$vo['reward_company_rate']}" data-level="{$vo['level']}">
                    {{# } }}
                    <span class="category-name">{$vo['category_name']}</span>
                    <span class="right-arrow">&gt;</span>
                </li>
                {/foreach}
            </ul>
        </div>

        <div class="item" data-level="2">
            <!--后续做搜索-->
            <ul></ul>
        </div>

        <div class="item" data-level="3">
            <!--后续做搜索-->
            <ul></ul>
        </div>

    </div>

    <div class="selected-category-wrap">
        <label>您当前选择的是：</label>
        <span class="js-selected-category"></span>
    </div>
</script>
{/block}
{block name="script"}
<script>
    let topicList = {:json_encode($topicList)};

	layui.use(['laydate', 'form', 'upload'], function() {
		var form = layui.form,
			upload = layui.upload,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
			currentDate = new Date();  //当前时间
		form.render();

		currentDate.setDate(currentDate.getDate() + 10);  //10天后的日期

        // 时间模块
        laydate.render({
            elem: '#start_time', //指定元素
            type: 'datetime',
        });

		// 时间模块
		laydate.render({
			elem: '#over_time', //指定元素
			type: 'datetime',
		});


        let richang = xmSelect.render({
            el: '#topic_list',
            name:'topic_list',
            language: 'zn',
            theme: {
                color: '#4685FD',
            },
            data: topicList,
        })


        /**
		 * 表单监听提交
		 */
		form.on('submit(save)', function(data) {
			if (data.field.is_show == undefined) {
				data.field.is_show = 0;
			}

			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				url: ns.url("multipleDiscount://admin/multipleDiscount/add"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
                            closeBtn: false,
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("multipleDiscount://admin/multipleDiscount/lists")
							},
							btn2: function() {
								location.href = ns.url("multipleDiscount://admin/multipleDiscount/add")
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		// // 图片上传
		// var uploadInst = upload.render({
		// 	elem: '#multipleDiscountImg',
		// 	url: ns.url("admin/upload/upload"),
		// 	done: function(res) {
		// 		if (res.code >= 0) {
		// 			$("input[name='image']").val(res.data.pic_path);
		// 			$("#multipleDiscountImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
		// 		}
		// 		return layer.msg(res.message);
		// 	}
		// });

		form.verify({
			days: function(value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return '请输入整数';
				}
			},
			number: function (value) {
				if (parseFloat(value) < 0) {
					return '请输入大于或等于0的数!'
				}
			},
			int: function (value) {
				if (value%1 != 0) {
					return '请输入整数!'
				}
				if (value < 0) {
					return '请输入大于0的数!'
				}
			},
			money: function (value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位'
				}
			},
            startTime: function(value) {
                var now_time = (new Date()).getTime();
                var overTime = $('#over_time').val();
                var overTime = (new Date(overTime)).getTime();
                if (!value) {
                    return '开始时间不能为空!'
                }
                if (now_time > overTime) {
                    return '开始时间大于结束时间!'
                }
            },
			overTime: function(value) {
				var now_time = (new Date()).getTime();
				var end_time = (new Date(value)).getTime();
                if (!value) {
                    return '活动结束时间不能为空!'
                }
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
			},
			gtzero: function(value) {
				if (parseFloat(value) <= 0) {
					return '请输入大于0的数!'
				}
			},
            discount: function(value) {
                var arrMen = value.split(".");
                var val = 0;
                if (value < 1 || value > 10) {
                    return '折扣需大于1，小于10，支持一位小数';
                }
                if (arrMen.length == 2) {
                    val = arrMen[1];
                }
                if (val.length > 1) {
                    return '折扣只支持一位小数';
                }
            },
            at_least: function (value) {
                if (value%1 != 0) {
                    return '请输入整数!'
                }
                if (value < 1) {
                    return '请输入大于0的数!'
                }
            }
		});
	});

	function back() {
		location.href = ns.url("multipleDiscount://admin/multipleDiscount/lists");
	}
</script>
<script src="ADMIN_JS/goods_coupon_edit.js?v=1"></script>
{/block}
