<script src="ADMIN_JS/common.js"></script>
<script>
    var  form, table, laytpl,
        repeat_flag = false, //防重复标识
        selectedGoodsSkuId = [],
        multiple_discount_id =  $("#multiple_discount_id").val();

    layui.use(['form', 'laytpl'], function() {
        form = layui.form;
        laytpl = layui.laytpl;
        form.render();
        table = new Table({
            elem: '#good_list'
            , url: ns.url("multipleDiscount://admin/multipleDiscount/goodsData")
            , async : false
            , toolbar: '#toolbar'
            , where: {'multiple_discount_id': multiple_discount_id}
            , parseData: function(res) {
                console.log(res);
                selectedGoodsSkuId = [];
                for (var i in res.data.selected_goods) {
                    selectedGoodsSkuId.push(res.data.selected_goods[i]);
                }
                return {
                    "code": res.code,
                    "msg": res.message,
                    "count": res.data.count,
                    "data": res.data.list,
                };
            }
            , cols: [[
                {type: 'checkbox', align: 'center', unresize: 'false', width: '5%', align: 'center'},
                {field: 'id', title: 'ID', unresize: 'false', width: '10%', align: 'center'},
                {title: '商品', unresize: 'false', width: '35%', templet: '#goodIntro', align: 'center'},
                {field: 'price', title: '销售价格', unresize: 'false', width: '20%', align: 'center', templet: function(data) {
                        return '￥<span class="goods-price">'+ data.sale_price +'</span>'
                }},
                {field: 'cost_price', title: '成本价', unresize: 'false', width: '20%', align: 'center', templet: function(data) {
                        return '￥<span>'+ data.cost_price +'</span>'
                }},
                {title: '操作', toolbar: '#operation', unresize: 'false', width: '10%'}
            ]]
        });

        /**
         * 添加商品
         */
        goods = function (){
            adminGoodsSelect(function (res) {
                // selectedGoodsSkuId = [];
                var goods_id = [];
                for(var i=0;i<res.length;i++) {
                    goods_id.push(res[i].goods_id);
                }

                $.ajax({
                    type: 'POST',
                    async: false,
                    url: ns.url("multipleDiscount://admin/multipleDiscount/addGoods"),
                    data: {
                        'goods_ids': goods_id.toString(),
                        'multiple_discount_id': multiple_discount_id
                    },
                    dataType: 'JSON',
                    success: function(res) {
                        layer.msg(res.message);
                        table.reload();
                    }
                });

            }, selectedGoodsSkuId, {mode: "spu", disabled: 1});
        };

        /**
         * 搜索功能
         */
        form.on('submit(searchBtn)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    search_type: $('#search_type').val(),
                    search_keys: $('#search_keys').val(),
                    status: $("#divSearchBtns input:checkbox:checked").val()
                }
            });
        });


        /**
         * 状态搜索的操作
         */
        form.on('checkbox(oneChoose)', function (data) {
            if(data.elem.checked || data.elem.checked == 'true'){
                $("#divSearchBtns input:checkbox").prop("checked",false);
                $(this).prop("checked", true);
            } else {
                $("#divSearchBtns input:checkbox").prop("checked",false);
            }
            form.render('checkbox');
        });
        form.render('checkbox');


        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'delete': //删除
                    delGoods(data.goods_id);
                    break;
            }
        });

        /**
         * 删除
         */
        function delGoods(goods_id) {

            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要删除该商品吗?', function() {
                $.ajax({
                    url: ns.url("multipleDiscount://admin/multipleDiscount/deleteGoods"),
                    data: {
                        "goods_id": goods_id,
                        "multiple_discount_id": multiple_discount_id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function() {
                layer.close();
                repeat_flag = false;
            });
        }
    });
</script>