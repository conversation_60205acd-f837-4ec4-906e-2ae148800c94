{extend name="app/admin/view/base.html"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
{/block}
{block name="main"}

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>优惠券名称：</label>
		<div class="layui-input-block">
			<input type="text" name="goodscoupon_name" value="{$goodscoupon_type_info.goodscoupon_name}" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>优惠政策：</label>
		<div class="layui-input-block nc-len-mid">
			<div class="layui-input-inline">
                <div class="layui-input-inline"><input type="radio" checked></div>
                <div class="layui-input-inline">满</div><input type="number" name="at_least" value="{$goodscoupon_type_info.at_least}" lay-verify="required|number|money|gtzero" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
                <div class="layui-input-inline">减</div><input type="number" name="money" value="{$goodscoupon_type_info.money}" lay-verify="required|number|money|gtzero" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
			</div>
			<span class="layui-form-mid">元</span>
		</div>
		<div class="ns-word-aux">
			<p>价格不能小于0，可保留两位小数</p>
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>发放数量：</label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="number" name="count" value="{$goodscoupon_type_info.count}" min="0" lay-verify="required|number|int|gtzero" autocomplete="off" class="layui-input ns-len-short">
            </div>
        </div>
        <div class="ns-word-aux">
            <p>数量不能小于0，且必须为整数；设置为0时，可无限领取</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>领取数量限制：</label>
        <div class="layui-input-block">
            <span class="layui-input-inline">每个用户可最多可领取</span>
            <div class="layui-input-inline">
                <input type="number" name="max_fetch" value="{$goodscoupon_type_info.max_fetch}" min="0" value="1" lay-verify="required|number|int|max" autocomplete="off" class="layui-input ns-len-short">
            </div>
        </div>
        <div class="ns-word-aux">
            <p>数量不能小于0，且必须为整数；设置为0时，可无限领取</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">是否进行店主分佣：</label>
        <div class="layui-input-block">
            <input type="radio" name="is_shop_commission" lay-filter="is_shop_commission" value="0" title="否" {if $goodscoupon_type_info['is_shop_commission'] == 0} checked {/if}>
            <input type="radio" name="is_shop_commission" lay-filter="is_shop_commission" value="1" title="是" {if $goodscoupon_type_info['is_shop_commission'] == 1} checked {/if}>
        </div>
        <div class="ns-word-aux">
            <p>使用优惠券是否进行店主分佣</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>使用场景：</label>
        <div class="layui-input-block">
            <input type="radio" name="use_scenario" lay-filter="use_scenario" value="1" title="全场通用" {if $goodscoupon_type_info['use_scenario'] == 1 } checked {/if}>
            <input type="radio" name="use_scenario" lay-filter="use_scenario" value="2" title="按类目限制" {if $goodscoupon_type_info['use_scenario'] == 2 } checked {/if}>
            <input type="radio" name="use_scenario" lay-filter="use_scenario" value="3" title="按商品限制" {if $goodscoupon_type_info['use_scenario'] == 3 } checked {/if}>
        </div>
    </div>

    <div class="layui-form-item group_list" {if $goodscoupon_type_info.use_scenario != 2} style="display: none" {/if}>
        <label class="layui-form-label">商品分类：</label>
        <div class="layui-input-inline">
            <input type="text" readonly onfocus="selectedCategoryPopup()" name="category_name" value="{$goodscoupon_type_info.category_name}" autocomplete="off" class="layui-input ns-len-mid" />
            <input type="hidden" name="category_id" value="{$goodscoupon_type_info.category_id_3}" />
            <input type="hidden" name="category_id_1" value="{$goodscoupon_type_info.category_id_1}" />
            <input type="hidden" name="category_id_2" value="{$goodscoupon_type_info.category_id_2}" />
            <input type="hidden" name="category_id_3" value="{$goodscoupon_type_info.category_id_3}" />
        </div>
        <!--<button class="layui-btn layui-btn-primary" onclick="selectedCategoryPopup()">选择</button>-->
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">领取有效期：</label>
        <div><input type="radio" name="validity_type" value="0" lay-filter="filter" checked="checked" title="至活动结束时失效"></div>
        <div class="layui-input-inline">
            <div class="layui-input-inline">
                <input type="radio" name="validity_type" value="1" lay-filter="filter" title="领取之日起" class="layui-input-inline">
            </div>
            <input type="number" min="1" max="365" value="10" name="fixed_term" lay-verify="days" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
            <span class="layui-form-mid">天有效</span>
        </div>
    </div>
	
	<div class="layui-form-item">
		<label class="layui-form-label img-upload-lable">优惠券图片：</label>
		<div class="layui-input-inline img-upload">
			<input type="hidden" class="layui-input" name="image" value="{$goodscoupon_type_info.image}" />
			<div class="upload-img-block icon">
				<div class="upload-img-box" id="goodscouponImg">
					{if condition="$goodscoupon_type_info.image"}
					<img src="{:img($goodscoupon_type_info.image)}" />
					{else/}
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
					{/if}
				</div>
			</div>
		</div>
	</div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label">活动结束时间：</label>
        <div class="layui-input-block">
            <input type="text" name="over_time" value="{$goodscoupon_type_info.over_time}" lay-verify="time" id="over_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
        </div>
        <div class="ns-word-aux">
            <p>如果活动到期结束，已被领取的优惠券也同步过期</p>
        </div>
    </div>
	
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>

	<input type="hidden" name="goodscoupon_type_id" value="{$goodscoupon_type_info.goodscoupon_type_id}" />

    <!--选择商品分类-->
    <script type="text/html" id="selectedCategory">

        <div class="category-list">

            <div class="item">
                <!--后续做搜索-->
                <ul>
                    {foreach name="$goods_category_list" item="vo"}
                    {{# if(d.category_id_1 == '{$vo['category_id']}' ){ }}
                    <li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-reward_company_rate="{$vo['reward_company_rate']}" data-level="{$vo['level']}" class="selected">
                        {{# }else{ }}
                    <li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-reward_company_rate="{$vo['reward_company_rate']}" data-level="{$vo['level']}">
                        {{# } }}
                        <span class="category-name">{$vo['category_name']}</span>
                        <span class="right-arrow">&gt;</span>
                    </li>
                    {/foreach}
                </ul>
            </div>

            <div class="item" data-level="2">
                <!--后续做搜索-->
                <ul></ul>
            </div>

            <div class="item" data-level="3">
                <!--后续做搜索-->
                <ul></ul>
            </div>

        </div>

        <div class="selected-category-wrap">
            <label>您当前选择的是：</label>
            <span class="js-selected-category"></span>
        </div>
    </script>
</div>
{/block}
{block name="script"}
<script>
	layui.use(['laydate', 'form', 'upload'], function() {
		var form = layui.form,
			upload = layui.upload,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

		// 时间模块
		laydate.render({
			elem: '#over_time', //指定元素
			type: 'datetime'
		});

        //监听活动商品类型
        form.on('radio(use_scenario)', function(data){
            var value = data.value;
            if(value == 1 || value == 3){
                $(".group_list").hide();
            }
            if(value == 2){
                $(".group_list").show();
            }
        });

		/**
		 * 监听表单提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return false;
			var field = data.field;


			repeat_flag = true;
			
			$.ajax({
				url: ns.url("goodscoupon://admin/goodscoupon/edit"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(){
								location.href = ns.url("goodscoupon://admin/goodscoupon/lists")
							},
							btn2: function() {
								location.reload();
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		
		// 图片上传
		var uploadInst = upload.render({
			elem: '#goodscouponImg',
			url: ns.url("admin/upload/upload"),
			done: function(res) {
				if (res.code >= 0) {
					$("input[name='image']").val(res.data.pic_path);
					$("#goodscouponImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
				}
				return layer.msg(res.message);
			}
		});
		
		form.verify({
			days: function(value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return '请输入整数';
				}
			},
			number: function (value) {
				if (value < 0) {
					return '请输入不小于0的数!'
				}
			},
			int: function (value) {
				if (value%1 != 0) {
					return '请输入整数!'
				}
				if (value <= 0) {
					return '请输入大于0的数!'
				}
			},
			money: function (value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位'
				}
			},
			time: function(value) {
				var now_time = (new Date()).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
			},
			gtzero: function(value) {
				if (parseFloat(value) <= 0) {
					return '请输入大于0的数!'
				}
			},
			max: function(value) {
				var _count = $("input[name=count]").val();

				if (parseInt(value) > parseInt(_count)) {
					return '最大领取数量不能超过发放数量!';
				}
			}
		});
	});

	function back() {
		location.href = ns.url("goodscoupon://admin/goodscoupon/lists");
	}
</script>
<script src="ADMIN_JS/goods_coupon_edit.js?v=1"></script>
{/block}