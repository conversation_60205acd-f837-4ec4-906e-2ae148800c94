{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.layui-layer-page .layui-layer-content { padding: 20px 30px; }
    .layui-table-cell {
        margin: 0;
        font-size: 14px;
        padding: 0 5px;
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
        word-break: break-all;
    }

</style>
{/block}
{block name="main"}

<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" id="form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">活动ID：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="multiple_discount_id" placeholder="请输入活动ID" value="{$multiple_discount_id ? $multiple_discount_id : ''}" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">活动名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="multiple_discount_name" placeholder="请输入活动名称" value="{$multiple_discount_name ? $multiple_discount_name : ''}" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">活动类型：</label>
                    <div class="layui-input-inline">
                        <select name="state" lay-filter="state">
                            <option value="">全部</option>
                            <!--<option value="1">满减</option>-->
                            <option value="2">满折</option>
                            <!--<option value="3">满赠</option>-->
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">用户昵称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="username" placeholder="请输入用户昵称" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">用户手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="请输入用户手机号" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button class="layui-btn layui-btn-primary export" lay-submit lay-filter="export" >导出</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="goodscoupon_tab">
    <div class="layui-colla-item" style="border: 1px solid #f1f1f1; height: 40px; line-height: 40px;">
        <span style="padding-left: 20px;">已优惠订单数：<span id="discountOrderTotal"></span></span>
        <span style="padding-left: 20px;">优惠总额：<span id="discountMoneyTotal"></span></span>
        <span style="padding-left: 20px;">关联订单总额：<span id="connectMoneyTotal"></span></span>
    </div>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="orderMultipleDiscountList" lay-filter="orderMultipleDiscountList"></table>
	</div>
</div>

<!--领取时间-->
<script type="text/html" id="create_time">
	{{ ns.time_to_date(d.create_time) }}
</script>

<!--使用时间-->
<script type="text/html" id="use_time">
    {{ ns.time_to_date(d.use_time) }}
</script>

<input id="multiple_discount_id" type="hidden" value="{$multiple_discount_id ? $multiple_discount_id: 0}" />
{/block}
{block name="script"}
<script>
	layui.use(['form', 'laytpl', 'element'], function() {
		var table,
			form = layui.form,
			element = layui.element,
            multiple_discount_id = $('#multiple_discount_id').val(),
			laytpl = layui.laytpl;
		form.render();

		table = new Table({
			elem: '#orderMultipleDiscountList',
			url: ns.url("multipleDiscount://admin/multipleDiscount/receive"),
            method: 'post',
            where: {multiple_discount_id: multiple_discount_id, multiple_discount_name: $('input[name="multiple_discount_name"]').val()},
            parseData: function(data) {
                return {
                    "code": data.code,
                    "msg": data.message,
                    "count": data.data.count,
                    "data": data.data.list,
                    "discountOrderTotal": data.data.discountOrderTotal,
                    "discountMoneyTotal": data.data.discountMoneyTotal,
                    "connectMoneyTotal": data.data.connectMoneyTotal,
                };
            },
            cols: [
			    [
                    {
                        field: 'multiple_discount_id',
                        title: '活动ID',
                        unresize: 'false',
                        width: '8%',
                        align: 'center'
                    },
                    {
                        field: 'username',
                        title: '下单用户昵称',
                        unresize: 'false',
                        width: '10%',
                        align: 'center',
                        templet: function(data) {
                            return data.username ? data.username : (data.nickname ? data.nickname : data.mobile);
                        }
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        unresize: 'false',
                        width: '10%',
                        align: 'center'
                    },
                    {
                        field: 'multiple_discount_name',
                        title: '活动名称',
                        unresize: 'false',
                        width: '15%',
                        align: 'center',
                    },
                    {
                        field: 'multiple_discount_money',
                        title: '优惠金额',
                        unresize: 'false',
                        width: '10%',
                        align: 'center',
                    },
                    {
                        title: '活动类型',
                        unresize: 'false',
                        // templet: '#state',
                        templet: function(item) {
                            return '满折';
                        },
                        width: '10%',
                        align: 'center',
                    },
                    {
                        title: '下单时间',
                        unresize: 'false',
                        templet: '#create_time',
                        width: '10%',
                        align: 'center',
                    },
                    {
                        title: '关联订单金额',
                        field: 'order_money',
                        unresize: 'false',
                        // templet: '#use_time',
                        width: '10%',
                        align: 'center',
                    },
                    // {
                    //     field: 'order_money',
                    //     title: '关联订单金额',
                    //     unresize: 'false',
                    //     width: '10%',
                    //     align: 'center',
                    // },
                    {
                        field: 'order_no_btn',
                        title: '关联订单号',
                        width: '17%',
                        align: 'center',
                    },
                    // {
                    //     title: '操作',
                    //     width: '5%',
                    //     align: 'center',
                    //     templet: function(item) {
                    //         let btn = '';
                    //         if (item.state == 1) {
                    //             btn += '<a href="javascript:void();" lay-event="del">删除</a>'
                    //         }
                    //         return btn;
                    //     }
                    // }
                ]
			],
            callback: function(res, curr, count)
            {
                $('#discountOrderTotal').html(res.discountOrderTotal);
                $('#discountMoneyTotal').html(res.discountMoneyTotal);
                $('#connectMoneyTotal').html(res.connectMoneyTotal);
            }
		});
		
		// 搜索
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});


        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.href = ns.url("multipleDiscount://admin/multipleDiscount/edit", {"multiple_discount_id": data.multiple_discount_id});
                    break;
                case 'del': //关闭
                    layer.confirm('确定要删除吗?', function() {
                        if (repeat_flag) return false;
                        repeat_flag = true;

                        $.ajax({
                            url: ns.url("multipleDiscount://admin/multipleDiscount/deleteMemberCoupon", {"coupon_id": data.goodscoupon_id}),
                            data: data,
                            dataType: 'JSON',
                            type: 'POST',
                            success: function(res) {
                                layer.msg(res.message);
                                repeat_flag = false;

                                if (res.code == 0) {
                                    table.reload();
                                }
                            }
                        });
                    }, function() {
                        layer.close();
                        repeat_flag = false;
                    });
                    break;
            }
        });
	});

    $("body").on("click", ".export", function (data){
        //导出
        location.href = ns.url("multipleDiscount://admin/multipleDiscount/export", $('#form').serialize());
        return false;
        // // console.log(ns.url("goodscoupon://admin/goodscoupon/export", ns.urlReplace(location.hash.replace('#!', ''))));
        // // return false;
        // layer.msg('导出中，请稍后...', {icon: 16, shade: 0.5, time: 0});
        // $.ajax({
        //     url : ns.url("goodscoupon://admin/goodscoupon/export", ns.urlReplace(location.hash.replace('#!', ''))),
        //     type : 'GET',
        //     data: getUrlVars($('#form').serialize()),
        //     dataType : 'JSON',
        //     success : function (res){
        //         if(res.code == 0)
        //         {
        //             location.href = res.data;
        //             layer.msg('操作成功')
        //         }
        //         else
        //         {
        //             layer.msg(res.message)
        //             return false;
        //         }
        //     }
        // });
        // return false;
    });


    function getUrlVars(url) {
        var hash;
        var myJson = {};
        var hashes = url.slice(url.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            myJson[hash[0]] = hash[1];
        }
        return myJson;
    }
</script>
{/block}