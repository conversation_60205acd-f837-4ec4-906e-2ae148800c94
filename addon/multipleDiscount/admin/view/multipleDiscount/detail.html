{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .ns-discount { display: flex; justify-content: space-between; height: 34px; line-height: 34px; padding: 5px 15px; background-color: #F6FBFD; border: 1px dashed #BCE8F1; }
    .layui-table-view {
        margin-top: 0;
    }
</style>
{/block}
{block name="main"}
<div class="layui-form">
    <div class="layui-form-item">
        <label class="layui-form-label">优惠活动名称：</label>
        <div class="layui-input-inline">{$multipleDiscountInfo.multiple_discount_name}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">优惠政策：</label>
        <div class="layui-input-inline">
            {if $multipleDiscountInfo.type == 1}
                单商品
            {elseif $multipleDiscountInfo.type == 2}
                多商品
            {/if}
            满{$multipleDiscountInfo.at_least}件享{$multipleDiscountInfo.discount}折
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活动优惠上限：</label>
        <div class="layui-input-inline">{if $multipleDiscountInfo.max_fetch == 0} 不限 {else/} {$multipleDiscountInfo.max_fetch} {/if}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">优惠范围：</label>
        <div class="layui-input-inline">
            {if $multipleDiscountInfo.use_scenario == 1}
            全场参与
            {elseif $multipleDiscountInfo.use_scenario == 2}
            指定类目参与 ({$multipleDiscountInfo.category_name})
            {elseif $multipleDiscountInfo.use_scenario == 3}
            指定商品参与
            {elseif $multipleDiscountInfo.use_scenario == 4}
            指定商品不参与
            {else/}
            指定专区参与 ({$multipleDiscountInfo.category_name})
            {/if}
        </div>
    </div>

    {if $multipleDiscountInfo.use_scenario == 2}
    <div class="layui-form-item">
        <label class="layui-form-label">类目：</label>
        <div class="layui-input-inline">{$multipleDiscountInfo.category_name}</div>
    </div>
    {/if}

    <div class="layui-form-item">
        <label class="layui-form-label">是否叠加优惠：</label>
        <div class="layui-input-inline">{if $multipleDiscountInfo.is_use_goodscoupon == 1} 是 {else/} 否 {/if}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活动开始时间：</label>
        <div class="layui-input-inline">{$multipleDiscountInfo.start_time}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活动结束时间：</label>
        <div class="layui-input-inline">{$multipleDiscountInfo.over_time}</div>
    </div>

    <div class="ns-form-row">
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>

</div>
{/block}
{block name="script"}
<script>
    function back() {
        location.href = ns.url("multipleDiscount://admin/multipleDiscount/lists");
    }
</script>
{/block}