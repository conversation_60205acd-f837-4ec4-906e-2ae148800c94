<?php


namespace addon\multipleDiscount\domainService;


use addon\multipleDiscount\model\MultipleDiscountAction;
use addon\multipleDiscount\model\MultipleDiscountModel;
use addon\multipleDiscount\model\MultipleDiscountRepository;
use app\model\object\BuyGoodsObject;

class CheckMultipleDiscountService
{
    public $multipleDiscountRepository;
    public function __construct(MultipleDiscountRepository $multipleDiscountRepository)
    {
        $this->multipleDiscountRepository = $multipleDiscountRepository;
    }

    /**
     * @param $skuId
     * @param $buyNums
     * @param $goodsMoney
     * @param $promotionMoney
     * @return MultipleDiscountAction|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function execute($skuId, $buyNums, array $manyDiscountBuyInfo = [])
    {
        if(!empty($manyDiscountBuyInfo) && isset($manyDiscountBuyInfo[$skuId]))
            $discountModels = (new MultipleDiscountRepository())->getParticipateDiscountBuSkuId($skuId);
        else
            $discountModels = $this->multipleDiscountRepository->getAllSingleStart();

        if($discountModels->count() <= 0)
            return null;

        $useDiscount = null;
        foreach($discountModels as $discount)
        {
            $discountAction = new MultipleDiscountAction($discount);
            //检查使用场景
            if(!$discountAction->checkScenario($skuId))
                continue;
            //检查是否拼团秒杀
            if(!$discountAction->checkGoodsTypeBySkuId($skuId))
                continue;
            //检查单品满多少件享受
            if($discount->type == 1 && $buyNums < $discount->at_least)
                continue;
            if($discount->type == 2 && array_sum(array_values($manyDiscountBuyInfo)) < $discount->at_least)
                continue;
            //使用折扣
            if($useDiscount == null)
                $useDiscount = $discountAction;
            //默认使用更优惠的折扣  则扣相同则使用结束
            if($discountAction->mDiscount->discount < $useDiscount->mDiscount->discount ||
                ($discountAction->mDiscount->discount == $useDiscount->mDiscount->discount && $discountAction->mDiscount->over_time < $useDiscount->mDiscount->over_time))
            {
                $useDiscount = $discountAction;
            }
        }
        return $useDiscount;
    }
}