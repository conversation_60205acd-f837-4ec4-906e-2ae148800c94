var infoHtml = '<div class="goods-list-edit layui-form">'+
  '<div class="goods-list-edit layui-form"> '+
    '<label class="layui-form-label sm">标题</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.name" class="layui-input" />'+
    '</div>'+
    '<label class="layui-form-label sm">文本内容</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" v-model="data.moreText" class="layui-input" />'+
    '</div>'+
    '<label class="layui-form-label sm">跳转地址</label>'+
    '<div class="layui-input-block">'+
      '<input type="text" name="title" disabled v-model="data.moreUrl" class="layui-input" />'+
    '</div>'+
  '</div>'+
'</div>'

Vue.component("maidou-list", {
  template: infoHtml,
  data: function () {
  return {
    data: this.$parent.data,
  }
  },
  created:function() {
    // this.data.sources = "diy"
    // this.data.goodsCount = "3"
    
    this.$parent.data.verify = this.verify;//加载验证方法
  },
  methods: {
    verify: function() {
      var res = { code : true, message : "" };
      if(this.data.name == '') {
        res.code = false
        res.message = '请输入迈豆模块的标题'
        return res
      }
      if(this.data.moreText == '') {
        res.code = false
        res.message = '请输入迈豆模块的文本内容'
        return res
      }
      if(this.data.moreUrl == '') {
        res.code = false
        res.message = '请输入迈豆模块的跳转地址'
        return res
      }
      return res
    }
  }
});