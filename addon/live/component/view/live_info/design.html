<nc-component v-bind:data="data[index]" class="component-live-info">
	
	<!-- 预览 -->
	<template v-slot:preview="slotProps">
		<div class="live-wrap" v-bind:style="`background-color:${data[index].backgroundColor};background-image:url('${data[index].backgroundImg}');`">
			<div class="live-wrap-left">
				<img :src="data[index].coverImg" class="live-wrap-left-img" v-if="data[index].coverImg">
				<img src="{$resource_path}/live_info/img/playback.png" class="live-wrap-left-live" v-if="slotProps.liveTestData.live_status==103">
				<img src="{$resource_path}/live_info/img/subscribe.png" class="live-wrap-left-live" v-else-if="slotProps.liveTestData.live_status==102">
				<img src="{$resource_path}/live_info/img/live.png" class="live-wrap-left-live" v-else>
			</div>
			<div class="live-wrap-right">
				<p class="live-wrap-right-name" v-bind:style="`color: ${data[index].textColor};`">{{data[index].title}}</p>
				<p class="live-wrap-right-desc" v-bind:style="`color: ${data[index].textColor};`">主播：{{slotProps.liveTestData.anchor_name}}</p>
				<div class="live-wrap-right-list">
					<img src="{$resource_path}/live_info/img/product.png" class="live-wrap-right-list-one">
					<img src="{$resource_path}/live_info/img/product.png" class="live-wrap-right-list-one">
					<img src="{$resource_path}/live_info/img/product.png" class="live-wrap-right-list-one">
				</div>
				<div class="live-wrap-right-op">
					<div class="live-wrap-right-op-share"><img src="{$resource_path}/live_info/img/share.png" alt="">分享</div>
					<div class="live-wrap-right-op-play" v-if="slotProps.liveTestData.live_status==103"><img src="{$resource_path}/live_info/img/play.png" alt="">回放</div>
					<div class="live-wrap-right-op-play" v-else-if="slotProps.liveTestData.live_status==103"><img src="{$resource_path}/live_info/img/subscribe-icon.png" alt="">预约</div>
					<div class="live-wrap-right-op-play" v-else><img src="{$resource_path}/live_info/img/play.png" alt="">观看</div>
				</div>
			</div>
		</div>
<!--			<div class="room-info" v-if="nc.isShowAnchorInfo || nc.isShowLiveGood">-->
<!--				<template v-if="nc.isShowAnchorInfo">-->
<!--					<img src="{$resource_path}/live_info/img/default_headimg.png" class="anchor-img">-->
<!--					<span class="anchor-name">主播：主播昵称</span>-->
<!--				</template>-->
<!--				<template v-if="nc.isShowAnchorInfo && nc.isShowLiveGood">-->
<!--					<span class="separate">|</span>-->
<!--				</template>-->
<!--				<template v-if="nc.isShowLiveGood">-->
<!--					<span class="goods-text">直播商品：1</span>-->
<!--				</template>-->
<!--			</div>-->

	</template>
	
	<!-- 编辑 -->
	<template slot="edit">
<!--		<slide v-bind:data="{ field : 'paddingUpDown', label : '上下边距' }"></slide>-->
		<template v-if="nc.lazyLoad">
			<live-show-content :index="index"></live-show-content>
		</template>
	</template>
	
	<!-- 资源 -->
	<template slot="resource">
		
		<css src="{$resource_path}/live_info/css/design.css"></css>
		<js src="{$resource_path}/live_info/js/design.js"></js>
		
	</template>

</nc-component>