<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\wholesale\shop\controller;

use app\shop\controller\BaseShop;
use addon\wholesale\model\Wholesale as WholesaleModel;

/**
 * 批发控制器
 */
class Wholesale extends BaseShop
{
	
	/*
	 *  批发活动列表
	 */
	public function lists()
	{
		$model = new WholesaleModel();
		$goods_name = input('goods_name', '');
		
		$condition[] = [ 'g.site_id', '=', $this->site_id ];
		if (request()->isAjax()) {
            $condition[] = [ 'wg.wholesale_goods_id', '>', 0 ];
			$condition[] = [ 'g.goods_name', 'like', '%' . $goods_name . '%' ];
            $page = input('page', 1);
			$page_size = input('page_size', PAGE_LIST_ROWS);
			$list = $model->getWholesaleGoodsViewPageList($condition, $page, $page_size, 'g.create_time desc');
			return $list;
		} else {
            return $this->fetch("wholesale/lists");
		}

	}



    /**
     * 添加批发商品
     */
    public function add()
    {
        if (request()->isAjax()) {
            $wholesale_data = [
                'site_id' => $this->site_id,
                'site_name' => $this->shop_info['site_name'],
                'price_json' => input('price_json', ''),
                'goods_id' => input('goods_id', ''),
            ];

            $wholesale_model = new WholesaleModel();
            return $wholesale_model->addGoodsWholesale($wholesale_data);
        } else {

            return $this->fetch("wholesale/add");
        }
    }

    /**
     * 编辑批发商品
     */
    public function edit()
    {
        $goods_id = input('goods_id', 0);
        $wholesale_model = new WholesaleModel();
        $condition = array(
            ['site_id', '=', $this->site_id],
            ['goods_id', '=', $goods_id],
        );
        if (request()->isAjax()) {
            $wholesale_data = [
                'site_id' => $this->site_id,
                'site_name' => $this->shop_info['site_name'],
                'price_json' => input('price_json', ''),
                'goods_id' => $goods_id,
            ];
            return $wholesale_model->editGoodsWholesale($wholesale_data, $condition);
        } else {
            $this->assign('goods_id', $goods_id);

            $info_result = $wholesale_model->getWholesaleGoodsDetail($condition);
            $this->assign('info', $info_result['data'] ?? []);
            return $this->fetch("wholesale/edit");
        }
    }


    /**
     * 删除批发商品
     */
	public function delete(){
        if (request()->isAjax()) {
            $goods_id = input('goods_id', 0);
            $wholesale_model = new WholesaleModel();
            $condition = array(
                ['goods_id', '=', $goods_id],
                ['site_id', '=', $this->site_id]
            );
            $result = $wholesale_model->delete($condition);
            return $result;
        }
    }


}