{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>批发活动列表展示商品的批发相关信息</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="goods_name" placeholder="请输入商品名称" class="layui-input" autocomplete="off">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
			  <i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="wholesale_list" lay-filter="wholesale_list"></table>

<!-- 商品 -->
<script type="text/html" id="goods">

	<div class="ns-table-tuwen-box">
		<div class="ns-img-box">
			{{#  if(d.goods_image){  }}
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
			{{#  }  }}
		</div>
		<div class="ns-font-box">
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 商品状态 -->
<script type="text/html" id="goods_status">
	{{#  if(d.goods_state == 1){  }}
	销售中
	{{#  }else if(d.goods_state == 0){  }}
	仓库中
	{{#  }  }}
</script>


<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="detail">详情</a>
		<a class="layui-btn" lay-event="del">删除</a>
	</div>
</script>
{/block}

{block name="script"}
<script>
	layui.use(['form', 'element', 'laytpl'], function() {
		var table,
				form = layui.form,
				element = layui.element,
				laytpl = layui.laytpl; //防重复标识
		form.render();


		table = new Table({
			elem: '#wholesale_list',
			url: ns.url("wholesale://admin/wholesale/lists"),
			cols: [
				[{
					title: '商品信息',
					unresize: 'false',
					width: '30%',
					templet: '#goods'
				}, {
					field: 'site_name',
					title: '店铺名称',
				}, {
					field: 'price',
					title: '商品原价',
					unresize: 'false',
					width: '8%',
					templet: function (data) {
						return '￥' + data.price;
					}
				}, {
					field: 'goods_stock',
					title: '商品库存',
					unresize: 'false',
					width: '8%',
				}, {
					field: 'price',
					title: '商品状态',
					unresize: 'false',
					width: '10%',
					templet: "#goods_status"
				}, {
					field: 'wholesale_price',
					title: '批发价格区间',
					unresize: 'false',
					width: '15%',
					templet: function (data) {
						var html = '';
						if (data.wholesale_goods_id > 0) {
							if (data.min_price == data.max_price) {
								html = '<span style="padding-right: 15px;">￥' + data.min_price + '</span>';
							} else {
								html = '<span style="padding-right: 15px;">￥' + data.min_price + '~' + data.max_price + '</span>';
							}
						}
						return html;
					}
				}, {
					field: 'min_num',
					title: '起批量',
					unresize: 'false',
					width: '8%'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '12%'
				}]
			],

		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function (data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		//监听Tab切换
		element.on('tab(status)', function (data) {
			var status = $(this).attr("data-status");
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': status
				}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function (obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'detail': //取消参与
					location.href = ns.url("wholesale://admin/wholesale/detail", {"goods_id": data.goods_id});
					break;
				case 'del': //取消参与
					del(data.goods_id);
					break;
			}
		});


		var del_flag = false;

		/**
		 * 删除
		 */
		function del(goods_id) {
			layer.confirm('确定要删除批发商品吗?', function () {
				if (del_flag) return;
				del_flag = true;

				$.ajax({
					url: ns.url("wholesale://admin/wholesale/delete"),
					data: {
						goods_id: goods_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						del_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				del_flag = false;
			});
		}

	})



</script>
{/block}