<?php


namespace addon\questionnaireActivity\event;


use addon\questionnaireActivity\domainModel\Questionnaire;
use addon\questionnaireActivity\domainModel\QuestionnaireShareMember;
use app\model\member\MemberModel;

class QuestionnaireAccessEvent
{
    /**
     * @var Questionnaire
     */
    public $questionnaire;

    /**
     * @var MemberModel
     */
    public $accessMember;

    /**
     * @var QuestionnaireShareMember
     */
    public $shareMember;

    public function __construct(Questionnaire $questionnaire, MemberModel $accessMember, QuestionnaireShareMember $shareMember=null)
    {
        $this->questionnaire = $questionnaire;
        $this->accessMember = $accessMember;
        $this->shareMember = $shareMember;
    }
}