{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .daterange-input-wrap{
        display: flex;
        width: 100%;
    }
    .daterange-input{

    }
    .upload-img-block-small{
        width: 200px;
        height: 200px;
    }
    #options_lists{
        display: none;
    }
    #field_add_option{
        display: none;
    }
    .one-options{
       display: flex;
    }
    .one-options:not(:first-child){
        margin-top: 10px;
    }
    .one-options-img{
        width: 34px;
        height: 34px;
        margin-right: 15px;
        display: none;
    }
    .one-options-img-fill{
        border: 1px dashed #ddd;
        display: flex;
        width: 34px;
        height: 34px;
        margin-right: 15px;
    }
    .one-options-upload{
        margin-left: 15px;
        font-size: 12px;
    }
    .one-options-op{
        margin-left: 15px;
        font-size: 12px;
    }
    .layui-layer-content{
        overflow: auto!important;
    }
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form" lay-filter="activity_form">
    <div class="layui-form-item">
        <div class="layui-input-inline">
            <label class="layui-form-label">活动名称：</label>
            <div class="layui-input-block">
                <input type="text" name="name" value=""  autocomplete="off" class="layui-input ns-len-long" lay-verify="required">
            </div>
        </div>
<!--        <div class="layui-input-inline">-->
<!--            <span class="ns-text-color-dark-gray">仅作后台管理查看，不对外展示</span>-->
<!--        </div>-->
    </div>
    <div class="layui-form-item">
        <div class="layui-input-inline">
            <label class="layui-form-label">分享语：</label>
            <div class="layui-input-block">
                <input type="text" name="share_title" value=""  autocomplete="off" class="layui-input ns-len-long" lay-verify="required">
            </div>
        </div>
        <div class="layui-input-inline">
            <span class="ns-text-color-dark-gray">分享表单卡片标题文案</span>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-inline">
            <label class="layui-form-label ">开始时间：</label>
            <div class="layui-input-block">
                <div class="daterange-input-wrap">
                    <input type="text" class="layui-input daterange-input ns-len-mid" name="start_time" id="daterangeStart" placeholder="" autocomplete="off" lay-verify="required">
                    <span style="margin: 0 10px;">-</span>
                    <input type="text" class="layui-input daterange-input ns-len-mid" name="end_time" id="daterangeEnd" placeholder="" autocomplete="off" lay-verify="required">
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">参与限制：</label>
        <div class="layui-input-block">
            <div class="">
                <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="not_limit" title="不限制" checked>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="tag" title='仅指定标签的用户可参与活动'>
            </div>
            {include file="app/admin/view/member/member_tag_component.html"}
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="realname" title='用户必需先完成实名校验'>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="gender" title='指定用户性别为'>
            </div>
            <div class="layui-input-inline">
                <select name="gender">
                    <option value="1">男</option>
                    <option value="2">女</option>
                </select>
            </div>
            <span class="layui-form-mid ns-text-color-dark-gray">选择该项时，用户必需先完成实名验证</span>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
                <input type="checkbox" lay-skin="primary" name="join_limit" lay-filter="join_limit" value="age" title='指定用户年龄段为'>
            </div>
            <div class="layui-input-inline">
                <input type="number" class="layui-input ns-len-short" name="age_start" value="18">
            </div>
            <span class="layui-form-mid ns-text-color-dark-gray">至</span>
            <div class="layui-input-inline">
                <input type="number" class="layui-input ns-len-short" name="age_end" value="25">
            </div>
            <span class="layui-form-mid ns-text-color-dark-gray">之间（含）选择该项时，用户必需先完成实名验证</span>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">提交限制：</label>
        <div class="layui-input-block">
            <div >
                <input type="radio" name="submit_limit" value="1" title="同一用户仅能提交一次" checked>
            </div>
            <div >
                <input type="radio" name="submit_limit" value="0" title="允许提交多次问卷">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">修改限制：</label>
        <div class="layui-input-block">
            <div >
                <input type="radio" name="edit_limit" value="0" title="提交后不可修改" checked>
            </div>
            <div >
                <input type="radio" name="edit_limit" value="1" title="提交后，活动结束前可多次修改">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label img-upload-lable">头部宣传图：</label>
        <div class="layui-input-block img-upload">
            <input type="hidden" class="layui-input" name="image" lay-verify="required" />
            <div class="upload-img-block upload-img-block-small">
                <div class="upload-img-box" id="uploadImage">
                    <div class="ns-upload-default">
                        <img src="SHOP_IMG/upload_img.png" />
                        <p>点击上传</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="ns-word-aux">
            <p>表单页顶部图片，点击跳转活动规则页，建议图片小于300kb</p>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label img-upload-lable">尾部广告图：</label>
        <div class="layui-input-block img-upload">
            <input type="hidden" class="layui-input" name="bottom_image" />
            <div class="upload-img-block upload-img-block-small">
                <div class="upload-img-box" id="uploadFooterImage">
                    <div class="ns-upload-default">
                        <img src="SHOP_IMG/upload_img.png" />
                        <p>点击上传</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="ns-word-aux">
            <p>表单页尾部宣传图，支持长按识别图片二维码，建议图片小于300kb</p>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">活动说明：</label>
        <div class="layui-input-block">
            <script id="content_editor" type="text/plain" class="ns-special-length" style="height:300px;"></script>
            <div class="layui-form-mid ns-text-color-dark-gray">
                <p>选填，活动说明文案默认点击宣传图进入查看，可以在宣传图片中增加点击按钮，提示用户点击查看</p>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">表单字段：</label>
        <div class="layui-input-block">
            <span class="layui-form-mid ns-text-color-dark-gray" style="float: none;">最多可添加50个字段，活动开始后将不能再修改字段配置</span>
            <table id="fields_table" lay-filter="fields_table"></table>
            <div class="ns-table-btn">
                <a class="layui-btn" id="add_field">增加表单内容</a>
            </div>
        </div>
    </div>

    <div class="ns-form-row">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>

</div>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <!--        {{# if(d.status==0) { }}-->
        <!--        <a class="layui-btn" lay-event="edit">编辑</a>-->
        <!--        {{# } }}-->
        <a class="layui-btn" lay-event="edit">修改</a>
        <!--<a class="layui-btn" lay-event="remove">删除</a>-->
    </div>
</script>

<!--添加表单字段配置-->
<script type="text/html" id="addFieldPopup">
    <div class="layui-form" lay-filter="add_field_form">
        <div class="layui-form-item">
            <label class="layui-form-label sm">字段名称：</label>
            <div class="layui-input-inline">
                <input type="text" name="name" placeholder="" autocomplete="off" class="layui-input ns-len-mid" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label sm">字段说明：</label>
            <div class="layui-input-inline">
                <input type="text" name="describe" placeholder="" autocomplete="off" class="layui-input ns-len-mid">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label sm">选/必填</label>
            <div class="layui-input-block">
                <input type="radio" name="is_require" value="1" title="必填" checked>
                <input type="radio" name="is_require" value="0" title="选填">
            </div>
        </div>
        <div class="layui-form-item" id="select_type">
            <label class="layui-form-label sm">选/必填</label>
            <div class="layui-input-block">
                <select name="type" id="field_type" lay-filter="field-type-filter">
                    <option value="text">文本</option>
                    <option value="number">数字</option>
                    <option value="date">日期</option>
                    <option value="datetime">时间</option>
                    <option value="radio">单选项</option>
                    <option value="checkbox">复选项</option>
                    <option value="textarea">文本域</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="options_lists">
            <label class="layui-form-label sm">选项</label>
            <div class="layui-input-block">
<!--                <div class="layui-input-inline ns-len-long one-options">-->
<!--                    <input type="text" hidden="hidden" name="option-image">-->
<!--                    <span class="one-options-img-fill"></span>-->
<!--                    <img src="" alt="" class="one-options-img">-->
<!--                    <input type="text" class="layui-input ns-len-mid">-->
<!--                    <a href="#" class="ns-text-color one-options-upload">添加选项图</a>-->
<!--                    <a href="#" class="ns-text-color one-options-op">删除</a>-->
<!--                </div>-->
            </div>
        </div>
        <a class="ns-text-color" id="field_add_option" style="margin-left: 80px;cursor: pointer;">增加选项</a>
    </div>
</script>
<button id="optionUploadAgent" style="display: none;"></button>

{/block}
{block name="script"}
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="application/javascript">
    var content_editor = UE.getEditor('content_editor');
    var fields_table_list = [];
    var optionUploadButEle;
    var option_field_length_max = 50; // 选项字段条数最大数量
    var one_option_field_dict = {};
    var one_option_field_index = null; //表格第几行
    var questionnaire_id = "{$questionnaire_id}";
    var questionnaire_info = {$questionnaire_info|raw};
    layui.use(['form', 'laytpl','laydate', 'element','upload'], function() {
        var laydate = layui.laydate;
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            upload = layui.upload,
            element = layui.element;
        form.render();
        //日期范围
        laydate.render({
            elem: '#daterangeStart'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        laydate.render({
            elem: '#daterangeEnd'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        table = new Table({
            elem: '#fields_table',
            cols: [
                [
                    {
                        field: 'name',
                        title: '字段名称',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'type',
                        title: '类型',
                        unresize: 'false',
                        align: 'center',
                        templet:function (data){
                            var type_dict = {"text":"文本","number":"数字","date":"日期","datetime":"时间","radio":"单选项","checkbox":"复选项","textarea":"文本域"};
                            return type_dict[data.type];
                        }
                    },
                    {
                        field: 'is_require',
                        title: '选/必填',
                        unresize: 'false',
                        align: 'center',
                        templet:function (data) {
                            return data.is_require == 1 ? '必填' : '选填';
                        }
                    },
                    {
                        field: 'describe',
                        title: '字段说明',
                        unresize: 'false',
                        align: 'center',
                    },
                    {
                        title: '操作',
                        align: 'left',
                        toolbar: '#operation',
                        unresize: 'false',
                    }
                ]
            ],
            data:fields_table_list
        });
        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            //获取当前行的索引
            one_option_field_index = $(obj.tr).attr("data-index");
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    one_option_field_dict = data;
                    $('#add_field').click();

                    $("#field_type").attr("disabled","disabled");
                    form.render('select');

                    break;
                case 'remove': //提前结束
                    layer.confirm('确认删除字段？',function (index) {
                        fields_table_list.splice(one_option_field_index,1);
                        renderTable();
                        layer.close(index);
                    },function () {
                        layer.close(index);
                    })
                    break;
            }
        });

        function renderTable(){
            one_option_field_dict = {};
            one_option_field_index = null;
            table.reload({
                data:fields_table_list
            });
        }
        //图片上传
        var uploadImage = upload.render({
            elem: '#uploadImage',
            url: ns.url("admin/upload/upload"),
            done: function(res) {
                if (res.code >= 0) {
                    $("input[name='image']").val(res.data.pic_path);
                    $("#uploadImage").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });
        upload.render({
            elem: '#uploadFooterImage',
            url: ns.url("admin/upload/upload"),
            done: function(res) {
                if (res.code >= 0) {
                    $("input[name='bottom_image']").val(res.data.pic_path);
                    $("#uploadFooterImage").html("<img src=" + ns.img(res.data.pic_path) + " >");
                }
                return layer.msg(res.message);
            }
        });

        // 图片选项图片上传
        upload.render({
            elem: '#optionUploadAgent',
            url: ns.url("admin/upload/upload"),
            done: function(res) {
                if (res.code >= 0) {
                    optionUploadButEle.siblings('input[name=option-image]').val(res.data.pic_path);
                    optionUploadButEle.siblings('.one-options-img').attr('src',res.data.pic_path);
                    optionUploadButEle.siblings('.one-options-img-fill').hide();
                    optionUploadButEle.siblings('.one-options-img').show();
                }
                layer.msg(res.message);
            }
        });

        // 字段编辑表单渲染
        function renderFieldForm(data = {}){
            if(Object.keys(data).length){
                form.val('add_field_form', {
                    'name': data.name,
                    'type': data.type,
                    'is_require': data.is_require,
                    'describe': data.describe,
                });
                if(["radio","checkbox"].includes(data.type)){
                    renderOptionsElement(data.select_data)
                }
                form.render();
            }
        }

        function renderOptionsElement(data = []) {
            if(data.length>0){
                for (let i = 0; i < data.length; i++) {
                    var option_html = '<div class="layui-input-inline ns-len-long one-options">\n' +
                        '                    <input type="text" hidden="hidden" name="option-image" value="'+data[i].image+'">\n' +
                        '                    <span class="one-options-img-fill" style="'+(data[i].image ? "display: none;" : "display:block;")+'"></span>\n' +
                        '                    <img src="'+data[i].image+'" alt="" class="one-options-img" style="'+(data[i].image ? "display: block;" : "display: none;")+'">\n' +
                        '                    <input type="text" class="layui-input ns-len-mid" name="option-value" value="'+data[i].value+'">\n' +
                        '                    <a href="#" class="ns-text-color one-options-upload">添加选项图</a>\n' +
                        '                    <a href="#" class="ns-text-color one-options-op">删除</a>\n' +
                        '                </div>'
                    $('#options_lists .layui-input-block').append(option_html);
                    $('#options_lists').show();
                    $('#field_add_option').show();
                }
            }else{
                var option_html = '<div class="layui-input-inline ns-len-long one-options">\n' +
                    '                    <input type="text" hidden="hidden" name="option-image">\n' +
                    '                    <span class="one-options-img-fill"></span>\n' +
                    '                    <img src="" alt="" class="one-options-img">\n' +
                    '                    <input type="text" class="layui-input ns-len-mid" name="option-value">\n' +
                    '                    <a href="#" class="ns-text-color one-options-upload">添加选项图</a>\n' +
                    '                    <a href="#" class="ns-text-color one-options-op">删除</a>\n' +
                    '                </div>'
                $('#options_lists .layui-input-block').append(option_html);
            }
        }

        $('#add_field').on('click',function () {
            var layer_one = layer.open({
                type: 1,
                area: ['600px', '700px'],
                title: '表单字段配置说明',
                content: $('#addFieldPopup').html(),
                btn: ['提交', '取消'], //按钮
                success:function (){
                    $("#field_type").removeAttr("disabled");
                    renderFieldForm(one_option_field_dict);
                    form.render();
                    form.on('select(field-type-filter)', function(data){
                        var elem = data.elem; // 获得 select 原始 DOM 对象
                        var value = data.value; // 获得被选中的值
                        var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
                        var show_options_list_field = ['radio','checkbox'];
                        if(show_options_list_field.includes(value)){
                            $('#options_lists').show();
                            $('#field_add_option').show();
                        }else{
                            $('#options_lists').hide();
                            $('#field_add_option').hide();
                        }
                    });
                    $('#field_add_option').on('click',function () {
                        renderOptionsElement();
                        form.render();
                    });
                    $('#options_lists').off('click','.one-options-upload');
                    $('#options_lists').on('click','.one-options-upload',function () {
                        optionUploadButEle = $(this);
                        $('#optionUploadAgent').click();
                    });
                    $('#options_lists').off('click','.one-options-op');
                    $('#options_lists').on('click','.one-options-op', function (event){
                        $(this).parent().remove();
                    });
                },
                yes: function (res) {
                    if(fields_table_list.length>=option_field_length_max){
                        layer.msg(`字段条数数量不得超过${option_field_length_max}条`);
                        return;
                    }
                    // 获取表单数据事件
                    let form_data = form.val(`add_field_form`);
                    if(!form_data.name){
                        layer.msg('字段名字是必填项!');
                        return
                    }

                    if(["radio","checkbox"].includes(form_data.type)){
                        var select_data = []
                        // 获取选项数据
                        $('#options_lists .one-options').each(function (index,ele) {
                            let option_value = $(ele).find('input[name=option-value]').val();
                            let option_image = $(ele).find('input[name=option-image]').val();
                            select_data.push({
                                value: option_value,
                                image: option_image
                            });
                        });
                        if(select_data.length<1){
                            layer.msg('最少增加一个选项!');
                            return;
                        }
                        form_data.select_data = select_data;
                    }
                    if(one_option_field_dict && Object.keys(one_option_field_dict).length){
                        fields_table_list[one_option_field_index] = form_data;
                    }else{
                        fields_table_list.push(form_data);
                    }
                    layer.close(layer_one);
                },
                end:function () {
                    renderTable()
                }
            })
            return false;
        })

        // 选择不限制，清除其他选项值
        function resetJoinLimit(){
            $('#tag_ids').val('');
            $('#tag_status').val('0');
            $('.select-tag-box').html('<div class="placeholder select-tag" style="width: 100%;">请选择标签</div>');
            $('select[name=sex]').val(1);
            $('input[name=age_start]').val(18);
            $('input[name=age_end]').val(25);
        }

        form.on('checkbox(join_limit)',function (data) {
            var elem = data.elem; // 获得 checkbox 原始 DOM 对象
            var checked = elem.checked; // 获得 checkbox 选中状态
            var value = elem.value; // 获得 checkbox 值
            if(value=='not_limit' && checked){
                $("input[name=join_limit]").prop('checked',false);
                $("input[name=join_limit][value=not_limit]").prop('checked',true);
                resetJoinLimit()
                form.render();
            }
            if(value!='not_limit' && checked){
                $("input[name=join_limit][value=not_limit]").prop('checked',false);
                if(value == 'gender' || value=='age'){
                    $("input[name=join_limit][value=realname]").prop('checked',true);
                }
                form.render();
            }
        })
        function isInteger(obj) {
            return obj%1 === 0
        }

        function submitDataCheck(params){
            if(!isInteger(params.age_start) || !isInteger(params.age_end)) {
               layer.msg('年龄需要是整数');
                return false;
            }
            if(parseInt(params.age_start) > parseInt(params.age_end)){
                layer.msg('年龄范围不正确');
                return false;
            }
            return true;
        }

        // 提交数据构建
        function submitDataConstruct(params) {
            var join_limit = {
                "tag":{"tag_ids":"","tag_status":"0","enable":"0"},
                "gender":{"value":"1","enable":"0"},
                "age":{"value":"18,25","enable":"0"},
                "realname":{"enable":"0"},
                "not_limit":{"enable":"0"}
            };
            $("input[name=join_limit]:checked").each(function (index, ele) {
                var join_limit_value = $(this).val();
                switch (join_limit_value) {
                    case 'not_limit':
                        join_limit.not_limit = {"enable":1}
                        break;
                    case 'tag':
                        join_limit.tag = {
                            "tag_ids": params.tag_ids,
                            "tag_status": params.tag_status,
                            "enable": 1
                        }
                        break;
                    case 'gender':
                        join_limit.gender = {"value":params.gender,"enable":"1"}
                        break;
                    case 'age':
                        join_limit.age = {"value":`${params.age_start},${params.age_end}`,"enable":"1"}
                        break;
                    case 'realname':
                        join_limit.realname = {"enable":"1"}
                        break;
                }
            })
            params.join_limit = join_limit;
            params.content = content_editor.getContent();
            params.column_data = fields_table_list
            if(questionnaire_id){
                params.questionnaire_id = questionnaire_id;
            }
            return params;
        }


        form.on('submit(save)',function (data){
            var field = data.field; // 获取表单字段值
            var check_reslut = submitDataCheck(field);
            if(!check_reslut){
                return;
            }
            layer.confirm('确认保存？', {
                btn: ['确定', '取消'] //按钮
            }, function(){
                var params = submitDataConstruct(field);
                var url = questionnaire_id ?　ns.url("questionnaireActivity/admin/questionnaire/edit") : ns.url("questionnaireActivity/admin/questionnaire/add");
                var loadIndex = layer.msg('保存中', {
                    icon: 16,
                    shade: 0.01,
                    time:0
                });
                $.ajax({
                    type: "post",
                    url: url,
                    dataType: 'json',
                    data:params,
                    async: false,
                    success: function (res) {
                        layer.close(loadIndex);
                        if(res.code == 0){
                            layer.msg(res.message,{icon: 1},function () {
                                back();
                            })
                        }else{
                            layer.msg(res.message,{icon: 2})
                        }
                    }
                })
            }, function(){

            });

        })

        function renderActivityForm(){
            form.val('activity_form',questionnaire_info);
            if(Object.keys(questionnaire_info).length){
                var join_limit_key = Object.keys(questionnaire_info.join_limit)
                for (let i = 0; i < join_limit_key.length; i++) {
                    if(questionnaire_info.join_limit[join_limit_key[i]]['enable']==1){
                        $("input[name=join_limit][value="+join_limit_key[i]+"]").prop('checked',true);
                    }else{
                        $("input[name=join_limit][value="+join_limit_key[i]+"]").prop('checked',false);
                    }
                    if(join_limit_key[i]=='age'){
                        if(questionnaire_info.join_limit[join_limit_key[i]]['value']){
                            var age_list = questionnaire_info.join_limit[join_limit_key[i]]['value'].split(',');
                            if(age_list.length>1){
                                $('input[name=age_start]').val(age_list[0]);
                                $('input[name=age_end]').val(age_list[1]);
                            }
                        }
                    }else if(join_limit_key[i] == 'gender'){
                        $('select[name=gender]').val(questionnaire_info.join_limit[join_limit_key[i]]['value'])
                    }
                }

                if(questionnaire_info.image){
                    $("input[name='image']").val(questionnaire_info.image);
                    $("#uploadImage").html("<img src=" + ns.img(questionnaire_info.image) + " >");
                }
                if(questionnaire_info.bottom_image){
                    $("input[name='bottom_image']").val(questionnaire_info.bottom_image);
                    $("#uploadFooterImage").html("<img src=" + ns.img(questionnaire_info.bottom_image) + " >");
                }
                if(questionnaire_info.content){
                    content_editor.ready(function() {
                        content_editor.setContent(questionnaire_info.content);
                    });
                }
                if(questionnaire_info.column_data && questionnaire_info.column_data.length){
                    fields_table_list = questionnaire_info.column_data
                    renderTable()
                }
            }
            form.render();
        }
        renderActivityForm();
    })
    function back() {
        window.location.href=ns.url('questionnaireActivity://admin/questionnaire/lists');
    }
</script>
{/block}