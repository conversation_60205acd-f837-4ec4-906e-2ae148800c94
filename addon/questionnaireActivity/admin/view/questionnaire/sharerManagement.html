{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .daterange-input{
        display: inline-block;
    }
</style>
{/block}
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">分享人手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">统计时间：</label>
                    <div class="layui-input-inline daterange-input-wrap">
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="statistics_start_time" id="daterangeStart" placeholder="" autocomplete="off">
                        <span>到</span>
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="statistics_end_time" id="daterangeEnd" placeholder="" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="submit">搜索</button>
                <button class="layui-btn layui-btn-primary" id="add_user">添加分享人</button>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<table id="sharer_list" lay-filter="sharer_list"></table>

<!--添加分享人员-->
<script type="text/html" id="addUserPopup">
    <div class="layui-form">
        <p style="margin-bottom: 15px;">请输入需添加为分享人的用户手机号，多个手机号以换行分隔</p>
        <textarea placeholder="请输入手机号码" class="layui-textarea" style="min-height: 250px;" name="add_user_text"></textarea>
    </div>
</script>

<!--新分享人员任务确认-->
<script type="text/html" id="userTaskPopup">
    <div class="layui-form">
        <p style="margin-bottom: 15px;">已添加{{d.user_count}}个分享人员，确定要添加到问卷活动参与名单？</p>
        <div class="layui-form-item">
            <!--            <input type="checkbox" name="task_one_permission" title="开放任务一权限" lay-skin="primary" checked disabled>-->
            <!--            <input type="checkbox" name="task_one_permission" title="开放任务二权限">-->
        </div>
    </div>
</script>
{/block}
{block name="script"}
<script type="application/javascript">
    var questionnaire_id = '{$questionnaire_id}';
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var laydate = layui.laydate;
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        form.render();
        //日期范围
        laydate.render({
            elem: '#daterangeStart'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        laydate.render({
            elem: '#daterangeEnd'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        table = new Table({
            elem: '#sharer_list',
            url: ns.url("questionnaireActivity/admin/questionnaire/sharerManagement"),
            where:{
                questionnaire_id:questionnaire_id
            },
            cols: [
                [
                    {
                        field: 'id',
                        title: 'ID',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'mobile',
                        title: '分享人手机号',
                        unresize: 'false',
                        align: 'center',
                    },
                    {
                        field: 'pv',
                        title: '分享浏览次数',
                        unresize: 'false',
                        align: 'center',
                    },
                    {
                        field: 'uv',
                        title: '分享访问人数',
                        unresize: 'false',
                        align: 'center',
                    },
                    {
                        field: 'sv',
                        title: '提交数据量',
                        align: 'center',
                        unresize: 'false',
                        templet: function (data) {
                            return data.sv + ' <a href="' + ns.url("questionnaireActivity/admin/questionnaire/submitDataDetails",
                                {'questionnaire_id': data.questionnaire_id,'share_member_id':data.share_member_id}) + '" style="color: #0d73f9">查看</a>'
                        }
                    },
                    {
                        field: 'status_text',
                        title: '分享权限',
                        align: 'center',
                        unresize: 'false',
                        templet:function (data) {
                            return '<input type="checkbox" lay-skin="switch" lay-filter="switchPermissions" lay-text="已开启|已关闭" '+(data.enable ? 'checked' : '')+' data-member_id="'+data.member_id+'">'
                        }
                    },
                    {
                        field: 'create_time',
                        title: '添加时间',
                        align: 'center',
                        unresize: 'false',
                    },
                ]
            ],
        });
        form.on('submit(submit)', function(data){
            table.reload({
                page: {curr: 1},
                where: {
                    ...data.field,
                    questionnaire_id:questionnaire_id
                },
            })
            return false;
        })
        form.on('switch(switchPermissions)',function (data) {
            var checked = data.elem.checked;
            var member_id = data.elem.dataset.member_id;
            var msg = !checked ? "关闭权限后，该分享人原分享链接将失效，无法打开并提交表单，确认要关闭？" : '确定要打开权限？'
            layer.confirm(msg,{
                btn: ['确定', '取消']
            },function (index) {
                data.elem.checked = checked;
                form.render();
                layer.close(index);
                var load=layer.load(1);
                $.ajax({
                    type: "post",
                    url: ns.url("questionnaireActivity/admin/questionnaire/shareManagementEnable"),
                    dataType: 'json',
                    async: false,
                    data: {member_id,enable:checked ? 1 : 0,questionnaire_id:questionnaire_id},
                    success: function (res) {
                        layer.close(load);
                        if(res.code == 0){
                            layer.close(index);
                        }else{
                            layer.msg(res.msg);
                            data.elem.checked = !checked;
                            form.render();
                            layer.close(index);
                        }
                    }
                })
            },function (index) {
                data.elem.checked = !checked;
                form.render();
                layer.close(index);
            })
        })

        $('#add_user').on('click',function () {
            var layer_one = layer.open({
                type: 1,
                area: ['500px', '500px'],
                title: '添加分享人员',
                content: $('#addUserPopup').html(),
                btn: ['提交', '取消'], //按钮
                yes: function(res) {
                    var add_user_text = $('textarea[name=add_user_text]').val();
                    if(!add_user_text){
                        layer.msg('请输入用户手机号');
                        return;
                    }
                    laytpl($('#userTaskPopup').html()).render({user_count:add_user_text.split(/\r?\n/).length},function(html){
                        var layer_two = layer.open({
                            type: 1,
                            area: ['350px', '200px'],
                            title: '新用户任务',
                            content: html,
                            btn: ['确定', '取消'], //按钮
                            success: function () {
                                form.render();
                            },
                            yes: function(res2) {
                                var load=layer.load(1);
                                $.ajax({
                                    type: "post",
                                    url: ns.url("questionnaireActivity/admin/questionnaire/sharerManagementAdd"),
                                    dataType: 'json',
                                    async: false,
                                    data: {mobiles: add_user_text,questionnaire_id:questionnaire_id},
                                    success: function (res) {
                                        layer.close(load);
                                        if(res.code == 0){
                                            layer.close(layer_two);
                                            layer.close(layer_one);

                                            var tip_html = '<p>提交用户手机号'+res.data.all_nums+'个，已添加进活动名单的用户'+res.data.add_nums+'个，剩余'+res.data.no_reg_nums+'个未在小程序注册，具体名单如下：</p>';
                                            if(res.data.no_reg_mobile && res.data.no_reg_mobile.length>0){
                                                for (let i = 0; i < res.data.no_reg_mobile.length; i++) {
                                                    tip_html = tip_html + '<p>'+res.data.no_reg_mobile[i]+'</p>';
                                                }
                                            }else{
                                                tip_html = tip_html + '<p>无</p>';
                                            }
                                            var add_result_tip = layer.open({
                                                type: 1,
                                                area: ['350px', 'auto'],
                                                title: '新分享人添加结果',
                                                content: tip_html ,
                                                btn: ['返回'], //按钮
                                                yes: function () {
                                                    layer.close(add_result_tip)
                                                }
                                            })
                                            table.reload({
                                                page: {curr: 1}
                                            })
                                        }else {
                                            layer.msg(res.msg);
                                        }
                                    }
                                })
                            }
                        })
                    })
                },
                but2: function (index) {
                    layer.close(layer_one);
                }
            })
            return false;
        })
    })
</script>
{/block}