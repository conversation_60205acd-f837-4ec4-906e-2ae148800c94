{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .daterange-input{
        display: inline-block;
    }
</style>
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">用户手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">分享人手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="share_member_mobile" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">提交时间：</label>
                    <div class="layui-input-inline daterange-input-wrap">
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="submit_time_start" id="daterangeStart" placeholder="" autocomplete="off">
                        <span>到</span>
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="submit_time_end" id="daterangeEnd" placeholder="" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="submit">搜索</button>
                <button class="layui-btn layui-btn-primary" id="export">导出数据</button>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<table id="data_list" lay-filter="data_list"></table>

{/block}
{block name="script"}
<script type="application/javascript">
    var questionnaire_id = '{$questionnaire_id}';
    var share_member_id = '{$share_member_id}';
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var laydate = layui.laydate;
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        form.render();
        //日期范围
        laydate.render({
            elem: '#daterangeStart'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        laydate.render({
            elem: '#daterangeEnd'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        function escapeHtml(str) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#39;',
                '/': '&#x2F;',
            };
            return str.replace(/[&<>"'/]/g, function(m) { return map[m]; });
        }
        function getData(callback){
            $.ajax({
                type: "post",
                url: ns.url("questionnaireActivity/admin/questionnaire/submitDataDetails"),
                dataType: 'json',
                data: {questionnaire_id:questionnaire_id,share_member_id:share_member_id},
                async: false,
                success: function (res) {
                    if(res.code == 0){
                        var column_list =[];
                        if(res.data.list.length>0){
                           column_list = res.data.list[0].data.map((item, index)=> {
                               return {
                                   field: item.name,
                                   title: item.name,
                                   align: 'left',
                                   unresize: 'false',
                                   templet: function (data) {
                                      //var data_list = data.data.filter(one=>one.name==item.name)
                                       var str = data.data.length > index ? data.data[index].value || '' : '';
                                       const pattern = /<\/?[\w\s="/.':;#-\/\?]+>/gi;
                                       if(pattern.test(str)){   //判断字符串是否需要转义显示
                                           str = escapeHtml(str)
                                       }

                                       return str;
                                   }
                               };
                           })
                        }
                        if(callback && typeof callback == 'function'){
                            callback(column_list)
                        }
                    }
                }
            })
        }
        getData(function (column_list) {
            table = new Table({
                elem: '#data_list',
                url: ns.url("questionnaireActivity/admin/questionnaire/submitDataDetails"),
                where:{
                    questionnaire_id:questionnaire_id,
                    share_member_id:share_member_id
                },
                cols: [
                    [
                        {
                            field: 'mobile',
                            title: '提交用户',
                            align: 'left',
                            unresize: 'false',
                        },
                        {
                            field: 'share_member_mobile',
                            title: '分享人',
                            align: 'left',
                            unresize: 'false',
                        },
                        ...column_list,
                        {
                            field: 'last_update_time',
                            title: '最后提交时间',
                            align: 'center',
                            unresize: 'false',
                        },
                    ]
                ],
            });
        })
        form.on('submit(submit)', function(data){
            table.reload({
                page: {curr: 1},
                where: {
                    ...data.field,
                    questionnaire_id:questionnaire_id,
                    share_member_id:share_member_id
                },
            })
            return false;
        })
        $('#export').on('click',function () {
            let data={
                mobile:$('[name=mobile]').val(),
                share_member_mobile:$('[name=share_member_mobile]').val(),
                submit_time_start:$('[name=submit_time_start]').val(),
                submit_time_end:$('[name=submit_time_end]').val(),
                questionnaire_id:questionnaire_id,
                share_member_id:share_member_id
            }
            var index=layer.load(1)
            $.ajax({
                type: "post",
                url: ns.url("questionnaireActivity/admin/questionnaire/submitDataDetailsExport"),
                dataType: 'json',
                async: false,
                data,
                success: function (res) {
                    if (res.code == 0) {
                        layer.close(index);
                        window.location.href=res.data.path;
                    }
                }
            })
            return false;
        })
    })
</script>
{/block}