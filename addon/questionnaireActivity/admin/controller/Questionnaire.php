<?php

namespace addon\questionnaireActivity\admin\controller;

use addon\questionnaireActivity\domainModel\QuestionnaireStatistics;
use addon\questionnaireActivity\doMainService\QuestionnaireService;
use addon\questionnaireActivity\facade\QuestionnaireRepository;
use addon\questionnaireActivity\model\QuestionnaireAccessModel;
use addon\questionnaireActivity\model\QuestionnaireModel;
use addon\questionnaireActivity\model\QuestionnaireShareMemberModel;
use addon\questionnaireActivity\model\QuestionnaireSubmitModel;
use app\admin\controller\BaseAdmin;
use app\Domain\Models\Excel\ExportExcel;
use app\Request;
use app\service\member\MemberService;
use think\facade\Db;

class Questionnaire extends BaseAdmin
{
    // 问卷活动列表
    public function lists(Request $request)
    {
        if($request->isAjax())
        {
            $name = input('name', '');
            $status = input('status', '');
            $createTimeStart = input('create_time_start', '');
            $createTimeEnd = input('create_time_end', '');
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);

            $where = [];
            if($name)
                $where[] = ['name', 'like', "%{$name}%"];
            if(is_numeric($status))
                $where[] = ['status', '=', $status];
            if($createTimeStart && $createTimeEnd)
                $where[] = ['create_time', 'between', [$createTimeStart,$createTimeEnd]];

            $ret = QuestionnaireModel::where($where)->field("questionnaire_id,name,start_time,end_time,status,create_time")->order("questionnaire_id", "desc")->paginate($page_size);
            $ret = model_to_api_page($ret);

            foreach ($ret['list'] as $i=>$v)
            {
                $ret['list'][$i]['submit_nums'] = QuestionnaireStatistics::submitNums($v['questionnaire_id']);
                $ret['list'][$i]['share_member_nums'] = QuestionnaireStatistics::shareMemberNums($v['questionnaire_id']);
                $ret['list'][$i]['status_content'] = lang("questionnaire.status")[$v['status']];
            }

            return json(success(0, '操作成功', $ret));

        }

        return $this->fetch('questionnaire/list');
    }

    // 获取单个活动的推广路径，二维码
    public function promote(Request $request)
    {
        $questionnaire_id = $request->param('questionnaire_id', null);
        $path = "/promotionpages/questionnaire/qform/qform?questionnaire_id={$questionnaire_id}";
        $h5_path = $request->domain().'/mini-h5'.$path;
        $qrcode_save_path = root_path().DIRECTORY_SEPARATOR.'upload'.DIRECTORY_SEPARATOR.'qrcode'.DIRECTORY_SEPARATOR.'questionnaire';
        // 获取包含毫秒的时间戳
        $timestamp = microtime(true);
        // 将时间戳转换为毫秒级别的整数
        $milliseconds = (int)($timestamp * 1000);
        // 将时间戳转换为字符串
        $timestampString = (string)$milliseconds;

        $data = [
            "mini_path" => $path,
            "h5_path" => $h5_path,
            'h5_qrcode' => imgToBase64(qrcode($h5_path,$qrcode_save_path,$timestampString))
        ];
        return json(success(0, '操作成功', $data));
    }

    // 活动表单增加
    public function add(Request $request)
    {
        if($request->isAjax())
        {
            $rules = [
                'name' => 'require',
                'share_title' => 'require',
                'start_time' => 'require|date',
                'end_time' => 'require|date',
                'join_limit' => 'require',
                'submit_limit' => 'require|number',
                'edit_limit' => 'require|number',
                'image' => 'require|url',
                'content' => 'require',
                'column_data' => 'require',
                'bottom_image'=>'regex:^[\s\S]*$'
            ];
            $data = $request->only(array_keys($rules));
            validate($rules)->check($data);

            $ret = QuestionnaireModel::create($data);
            return json(success(0, '操作成功', ['questionnaire_id'=>$ret->questionnaire_id]));
        }

       return $this->fetch('questionnaire/add');
    }

    // 活动表单编辑
    public function edit(Request $request)
    {
        if($request->isAjax())
        {
            $rules = [
                'questionnaire_id' => 'require',
                'name' => 'require',
                'share_title' => 'require',
                'start_time' => 'require|date',
                'end_time' => 'require|date',
                'join_limit' => 'require',
                'submit_limit' => 'require|number',
                'edit_limit' => 'require|number',
                'image' => 'require|url',
                'content' => 'require',
                'column_data' => 'require',
                'bottom_image'=>'regex:^[\s\S]*$'
            ];
            $data = $request->only(array_keys($rules));
            validate($rules)->check($data);

            $qId = $data['questionnaire_id'];
            unset($data['questionnaire_id']);

            $ret = QuestionnaireModel::where('questionnaire_id', $qId)->update($data);
            return json(success(0, '操作成功', ['questionnaire_id'=>$qId]));
        }

        $questionnaire_id = $request->param('questionnaire_id','');
        $questionnaire_info = QuestionnaireModel::where("questionnaire_id",$questionnaire_id)->find();
        if(!$questionnaire_info){
            $questionnaire_info = json_encode(new \stdClass());
            $tag_ids = '';
            $tag_status = 0;
        }else{
            $tag = $questionnaire_info->join_limit->tag;
            $tag_ids = $tag["tag_ids"];
            $tag_status = $tag["tag_status"];
        }
        return $this->fetch('questionnaire/edit',["questionnaire_info"=>$questionnaire_info,
            'questionnaire_id'=>$questionnaire_id,
            'tag_ids'=>$tag_ids,
            'tag_status'=>$tag_status
        ]);
    }

    /**
     * 问卷详情
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        if($request->isAjax())
        {
            $rules = [
                'questionnaire_id' => 'require',
            ];
            $data = $request->only(array_keys($rules));
            validate($rules)->check($data);

            $qId = $data['questionnaire_id'];

            $ret = QuestionnaireModel::find($qId);
            if(!$ret)
                return json(error(-1, "问卷不存在"));
            return json(success(0, '操作成功', $ret->toArray()));
        }
    }

    // 提交数据明细
    public function submitDataDetails(Request $request)
    {
        if($request->isAjax())
        {
            $questionnaireId = input('questionnaire_id', '');
            $share_member_id = input('share_member_id', '');
            $mobile = input('mobile', '');
            $shareMemberMobile = input('share_member_mobile', '');
            $submitTimeStart = input('submit_time_start', '');
            $submitTimeEnd = input('submit_time_end', '');
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);

            $where = [];
            if($mobile)
                $where[] = ['m1.mobile', '=', $mobile];
            if($shareMemberMobile)
                $where[] = ['m2.mobile', '=', $shareMemberMobile];
            if($submitTimeStart && $submitTimeEnd)
                $where[] = ['qs.last_update_time', 'between', [$submitTimeStart, $submitTimeEnd]];
            if($share_member_id)
                $where[] = ['qs.share_member_id', '=', $share_member_id];

            $ret = Db::name("questionnaire_submit")
                ->alias("qs")
                ->join("member m1", "qs.member_id=m1.member_id")
                ->leftJoin("member m2", "qs.share_member_id=m2.member_id")
                ->where("qs.questionnaire_id", $questionnaireId)
                ->where($where)
                ->field("id as submit_id,m1.mobile,m2.mobile as share_member_mobile,data,last_update_time")
                ->order('last_update_time desc')
                ->paginate($page_size);

            $ret = model_to_api_page($ret);

            foreach ($ret['list'] as $i=>$v)
            {
                $ret['list'][$i]['data'] = json_decode($v['data'], true);
            }
            return json(success(0, '操作成功', $ret));
        }

        return $this->fetch('questionnaire/submitDataDetails',["questionnaire_id"=>$request->param('questionnaire_id', ''),
            "share_member_id"=>$request->param('share_member_id', '')]);
    }

    // 提交数据明细,导出数据
    public function submitDataDetailsExport(Request $request)
    {
        $questionnaireId = input('questionnaire_id', '');
        $share_member_id = input('share_member_id', '');
        $mobile = input('mobile', '');
        $shareMemberMobile = input('share_member_mobile', '');
        $submitTimeStart = input('submit_time_start', '');
        $submitTimeEnd = input('submit_time_end', '');

        $where = [];
        if($mobile)
            $where[] = ['m1.mobile', '=', $mobile];
        if($shareMemberMobile)
            $where[] = ['m2.mobile', '=', $shareMemberMobile];
        if($submitTimeStart && $submitTimeEnd)
            $where[] = ['qs.last_update_time', 'between', [$submitTimeStart, $submitTimeEnd]];
        if($share_member_id)
            $where[] = ['qs.share_member_id', '=', $share_member_id];

        $userList = Db::name("questionnaire_submit")
            ->alias("qs")
            ->join("member m1", "qs.member_id=m1.member_id")
            ->leftJoin("member m2", "qs.share_member_id=m2.member_id")
            ->where("qs.questionnaire_id", $questionnaireId)
            ->where($where)
            ->field("id as submit_id,m1.mobile,m2.mobile as share_member_mobile,data,last_update_time")
            ->order('last_update_time desc')
            ->select()->toArray();

        $custom_fields = [];
        foreach ($userList as $key=>&$item){
            $json_list = [];
            if($item['data']){
                $json_list = json_decode($item['data'],true);
            }
            foreach ($json_list as $index=>$value){
              if(!in_array("{$value['name']}|{$value['name']}",$custom_fields)){
                  array_push($custom_fields,"{$value['name']}|{$value['name']}");
              }
              $item[$value['name']] = $value['value'] ?? '';
            }
            unset($item['data']);
        }
        array_unshift($custom_fields,"mobile|提交用户","share_member_mobile|分享人");
        array_push($custom_fields,"last_update_time|最后提交时间");

        $exportExcel = new ExportExcel();
        $file = $exportExcel->field($custom_fields)->folder("questionnaire_activity")->data($userList)->save('问卷活动-提交数据明细');

        $file = str_replace("\\", "/", $file);
        return json(success(0, '导出成功', ['path' => $request->domain() . '/' . $file]));
    }

    // 分享人管理
    public function sharerManagement(Request $request)
    {
        if($request->isAjax()){
            $questionnaire_id = $request->param("questionnaire_id") ?? "";
            $mobile = $request->param("mobile") ?? "";
            $statisticsStartTime = $request->param('statistics_start_time') ?? '';
            $statisticsEndTime = $request->param('statistics_end_time') ?? '';
            $pageSize = $request->param("page_size") ?? 20;
            $statisticsBetweenTime = [];
            if($statisticsStartTime && $statisticsEndTime){
                $statisticsBetweenTime = [$statisticsStartTime, $statisticsEndTime];
            }

            $where = [
                ['q.questionnaire_id','=',$questionnaire_id]
            ];
            if($mobile){
                $where[] = ['m.mobile','like',"%{$mobile}%"];
            }
            $fields = ['q.id','q.questionnaire_id','m.member_id','m.mobile','q.member_id as share_member_id','q.enable','q.create_time'];
            $dataList =  QuestionnaireShareMemberModel::alias('q')->join('xm_member m', 'm.member_id = q.member_id','left')->field($fields)->where($where)->order('create_time desc')->paginate($pageSize);
            $dataList = model_to_api_page($dataList);
            foreach ($dataList['list'] as $key=>&$value){
                $value['pv'] = QuestionnaireStatistics::shareViews($value['questionnaire_id'],$value['member_id'],$statisticsBetweenTime);
                $value['uv'] = QuestionnaireStatistics::ShareVisitors($value['questionnaire_id'],$value['member_id'],$statisticsBetweenTime);
                $value['sv'] = QuestionnaireStatistics::shareSubmitNums($value['questionnaire_id'],$value['member_id'],$statisticsBetweenTime);
            }
            return json(success(0,"", $dataList));
        }
        return $this->fetch('questionnaire/sharerManagement',["questionnaire_id"=>$request->param('questionnaire_id', '')]);
    }

    // 添加分享员
    public function sharerManagementAdd(Request $request, QuestionnaireService $service)
    {
        if($request->isAjax())
        {
            $rules = [
                'questionnaire_id' => 'require',
                'mobiles' => 'require',
            ];
            $data = $request->only(array_keys($rules));
            validate($rules)->check($data);

            $mobiles = trim($data['mobiles']);
            $siftData = (new MemberService())->siftMobiles($mobiles);

            $addNums = 0;
            if(count($siftData['reg']) > 0)
            {
                foreach ($siftData['reg'] as $mobile)
                {
                    $ret = $service->addShareMemberByMobile($data['questionnaire_id'], $mobile);
                    if($ret)
                        $addNums++;
                }
            }
            return json(success(0, '操作成功', ['all_nums'=>count($siftData['no_reg'])+count($siftData['reg']),'add_nums'=>$addNums, 'no_reg_nums'=>count($siftData['no_reg']), 'no_reg_mobile'=>$siftData['no_reg']]));
        }

        return $this->fetch('questionnaire/sharerManagementAdd');
    }

    /**
     * 更改分享人权限
     * @param Request $request
     * @param QuestionnaireService $service
     * @return \think\response\Json
     */
    public function shareManagementEnable(Request $request, QuestionnaireService $service)
    {
        $rules = [
            'questionnaire_id' => 'require',
            'member_id' => 'require',
            'enable' => 'require|boolean',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $ret = $service->changeShareMemberEnable($data['questionnaire_id'], $data['member_id'], $data['enable']);
        if($ret)
            return json(success(0, '操作成功', []));
        else
            return json(error(-1, '操作失败', []));
    }

    /**
     * 提前结束
     * @param Request $request
     * @param QuestionnaireService $service
     * @return \think\response\Json
     */
    public function advanceEnd(Request $request, QuestionnaireService $service)
    {
        $rules = [
            'questionnaire_id' => 'require',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $ret = $service->advanceEnd($data['questionnaire_id']);
        if($ret)
            return json(success(0, '操作成功', []));
        else
            return json(error(-1, '操作失败', []));
    }
}