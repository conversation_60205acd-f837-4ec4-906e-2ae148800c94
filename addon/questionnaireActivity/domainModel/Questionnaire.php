<?php


namespace addon\questionnaireActivity\domainModel;


use addon\goodscoupon\model\GoodsCouponRules;
use addon\questionnaireActivity\dataType\QUESTIONNAIRE_ACTIVITY_STATUS;
use addon\questionnaireActivity\model\QuestionnaireAccessModel;
use addon\questionnaireActivity\model\QuestionnaireShareMemberModel;
use addon\questionnaireActivity\model\QuestionnaireSubmitModel;
use app\Domain\DataTransformTrait;
use app\Exceptions\ClientException;
use app\model\member\MemberAuth;
use app\model\member\MemberAuthBySmModel;
use app\model\member\MemberAuthModel;
use app\model\member\MemberModel;
use app\model\member\MemberTag;
use app\service\member\MemberService;
use app\service\operateGroup\OperateService;
use Carbon\Carbon;
use think\facade\Log;

class Questionnaire extends \app\Domain\Models\DomainModel
{
    use DataTransformTrait;

    protected $questionnaireId = 0;
    protected $name = "";
    protected $shareTitle = "";
    protected $startTime = "";
    protected $endTime = "";
    protected $status = 0;
    protected $joinLimit = [];
    protected $submitLimit = 0;
    protected $editLimit = 0;
    protected $image = "";
    protected $bottomImage = "";
    protected $content = "";
    protected $columnData = [];

    protected $primary_key = 'questionnaire_id';
    /**
     * @return int
     */
    public function getQuestionnaireId(): int
    {
        return $this->questionnaireId;
    }

    /**
     * @param int $questionnaireId
     */
    public function setQuestionnaireId(int $questionnaireId): void
    {
        $this->questionnaireId = $questionnaireId;
        $this->addUpdateData("questionnaire_id", $questionnaireId);

    }


    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
        $this->addUpdateData("name", $name);
    }

    /**
     * @return string
     */
    public function getShareTitle(): string
    {
        return $this->shareTitle;
    }

    /**
     * @param string $shareTitle
     */
    public function setShareTitle(string $shareTitle): void
    {
        $this->shareTitle = $shareTitle;
        $this->addUpdateData("share_title", $shareTitle);
    }

    /**
     * @return string
     */
    public function getStartTime(): string
    {
        return $this->startTime;
    }

    /**
     * @param string $startTime
     */
    public function setStartTime(string $startTime): void
    {
        $this->startTime = $startTime;
        $this->addUpdateData("start_time", $startTime);
    }

    /**
     * @return string
     */
    public function getEndTime(): string
    {
        return $this->endTime;
    }

    /**
     * @param string $endTime
     */
    public function setEndTime(string $endTime): void
    {
        $this->endTime = $endTime;
        $this->addUpdateData("end_time", $endTime);
    }

    /**
     * @return int
     */
    public function getStatus(): int
    {
        return $this->status;
    }

    /**
     * @param int $status
     */
    public function setStatus(int $status): void
    {
        $this->status = $status;
        $this->addUpdateData("status", $status);

    }

    /**
     * @return array
     */
    public function getJoinLimit(): array
    {
        return $this->joinLimit;
    }

    /**
     * @param array $joinLimit
     */
    public function setJoinLimit(array $joinLimit): void
    {
        $this->joinLimit = $joinLimit;
        $this->addUpdateData("join_limit", $joinLimit);
    }

    /**
     * @return int
     */
    public function getSubmitLimit(): int
    {
        return $this->submitLimit;
    }

    /**
     * @param int $submitLimit
     */
    public function setSubmitLimit(int $submitLimit): void
    {
        $this->submitLimit = $submitLimit;
        $this->addUpdateData("submit_limit", $submitLimit);
    }

    /**
     * @return int
     */
    public function getEditLimit(): int
    {
        return $this->editLimit;
    }

    /**
     * @param int $editLimit
     */
    public function setEditLimit(int $editLimit): void
    {
        $this->editLimit = $editLimit;
        $this->addUpdateData("edit_limit", $editLimit);

    }

    /**
     * @return string
     */
    public function getImage(): string
    {
        return $this->image;
    }

    /**
     * @param string $image
     */
    public function setImage(string $image): void
    {
        $this->image = $image;
        $this->addUpdateData("image", $image);

    }

    /**
     * @return string
     */
    public function getBottomImage(): string
    {
        return $this->bottomImage;
    }

    /**
     * @param string $bottomImage
     */
    public function setBottomImage(string $bottomImage): void
    {
        $this->bottomImage = $bottomImage;
        $this->addUpdateData("bottom_image", $bottomImage);

    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @param string $content
     */
    public function setContent(string $content): void
    {
        $this->content = $content;
        $this->addUpdateData("content", $content);

    }

    /**
     * @return array
     */
    public function getColumnData(): array
    {
        return $this->columnData;
    }

    /**
     * @param array $columnData
     */
    public function setColumnData(array $columnData): void
    {
        $this->columnData = $columnData;
        $this->addUpdateData("column_data", $columnData);
    }

    /**
     * 查找分享用户
     * @param $memberId
     * @return QuestionnaireShareMember|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function findShareMember($memberId):?QuestionnaireShareMember
    {
        if($ml = QuestionnaireShareMemberModel::where("member_id", $memberId)->where("questionnaire_id", $this->getQuestionnaireId())->find())
        {
            $shareMember = new QuestionnaireShareMember();
            $this->DomainModelInitDbData($shareMember, $ml);
            return $shareMember;
        }
        return null;
    }

    public function addShareMember(MemberModel $member)
    {
        if(!$this->findShareMember($member->member_id))
        {
            $data['questionnaire_id'] = $this->getQuestionnaireId();
            $data['member_id'] = $member->member_id;
            $data['enable'] = 1;
            $ret =  QuestionnaireShareMemberModel::create($data);
            if($ret)
                return true;
        }
        return false;
    }

    public function changeShareMemberEnable(MemberModel $member, $enable)
    {
        $shareMember = $this->findShareMember($member->member_id);
        if(!$shareMember)
            throw new \Exception("该用户未加入分享人");

        $shareMember->setEnable($enable);
        return $this->updateShareMember($shareMember);
    }

    public function updateShareMember(QuestionnaireShareMember $shareMember)
    {
        return QuestionnaireShareMemberModel::where("questionnaire_id", $this->getQuestionnaireId())->where("member_id", $shareMember->getMemberId())->update($shareMember->updateData);
    }

    public function start()
    {
        $this->setStatus(QUESTIONNAIRE_ACTIVITY_STATUS::$STARTED);
        \addon\questionnaireActivity\facade\QuestionnaireRepository::updateModel($this);
    }

    public function advanceEnd()
    {
        $this->setStatus(QUESTIONNAIRE_ACTIVITY_STATUS::$END);
        $this->setEndTime(Carbon::now()->toDateTimeString());
        \addon\questionnaireActivity\facade\QuestionnaireRepository::updateModel($this);
    }

    public function end()
    {
        $this->setStatus(QUESTIONNAIRE_ACTIVITY_STATUS::$END);
        \addon\questionnaireActivity\facade\QuestionnaireRepository::updateModel($this);
    }

    public function isNeedRealName()
    {
        $limitData = $this->getJoinLimit();
        if(isset($limitData['realname']) && $limitData['realname']['enable'] == 1)
            return true;
        if(isset($limitData['gender']) && $limitData['gender']['enable'] == 1)
            return true;
        if(isset($limitData['age']) && $limitData['age']['enable'] == 1)
            return true;
        return false;
    }

    public function checkJoinLimit($memberId, &$failReason=[])
    {
        $member = MemberModel::find($memberId);
        if(!$member)
        {
            $failReason[] = "用户不存在";
            return false;
        }
        //是否有参与限制
        if($this->isJoinLimit())
        {
            //是否需要实名
            if($this->isNeedRealName())
            {
                if(!$this->checkRealname($member))
                {
                    $failReason[] = "实名验证不通过";
                    return false;
                }
                $memberAuth = $member->auth ?: $member->xmAuth;
                $idCard = $memberAuth->auth_card_no;
                if(!$this->checkGender($idCard))
                {
                    $failReason[] = "性别验证不通过";
                    return false;
                }
                if(!$this->checkAge($idCard))
                {
                    $failReason[] = "年龄验证不通过";
                    return false;
                }
            }
            if(!$this->checkTag($member->member_id))
            {
                $failReason[] = "标签验证不通过";
                return false;
            }
        }
        return true;
    }

    /**
     * 检测实名信息
     * @param MemberModel|MemberAuthBySmModel $member
     * @return bool
     */
    public function checkRealname(MemberModel $member)
    {
        $memberAuth = $member->auth;
        $memberService = new MemberService();
        if(!$memberAuth)
        {
            $memberAuth = $memberService->syncXmMemberAuth($member);
            if($memberAuth)
            {
                $memberService->updateByAuth($memberAuth);
            }
        }
        //这里不需要请求接口验证
        return (bool)$memberAuth;
    }

    /**
     * 检测性别限制
     * @param MemberAuthModel $memberAuth
     * @return bool
     */
    public function checkGender($idCard)
    {
        $limitData = $this->getJoinLimit();
        if(isset($limitData['gender']) && $limitData['gender']['enable'] == 1)
        {
            $genderLimit = $limitData['gender']['value'];
            //$idCard = $memberAuth->auth_card_no;
            $gender = getGenderForIdCard($idCard);
            if($gender == '男')
                $gender = 1;
            elseif($gender == '女')
                $gender = 2;
            else
                $gender = 0;

            if($genderLimit != $gender)
                return false;
            else
                return true;
        }
        return true;
    }

    /**
     * 检测年龄限制
     * @param MemberAuthModel $memberAuth
     * @return bool
     */
    public function checkAge($idCard)
    {
        $limitData = $this->getJoinLimit();
        if(isset($limitData['age']) && $limitData['age']['enable'] == 1)
        {
            $ageLimit = $limitData['age']['value'];
            $ageLimit = explode(",", $ageLimit);

            //$idCard = $memberAuth->auth_card_no;
            $age = getAgeForIdCard($idCard);
            if($age >= $ageLimit[0] && $age <= $ageLimit[1])
                return true;
            else
                return false;
        }
        return true;
    }

    public function checkTag($memberId)
    {
        $limitData = $this->getJoinLimit();
        if(isset($limitData['tag']) && $limitData['tag']['enable'] == 1)
        {
            $tagIds = explode(",", $limitData['tag']['tag_ids']);
            $tagStatus = $limitData['tag']['tag_status'];
            if($tagStatus == 0) //符合任一标签
                return MemberTag::isMemberHasOneTags($memberId, $tagIds);
            elseif($tagStatus == 1) //同时符合所有标签
                return MemberTag::isMemberHasAllTags($memberId, $tagIds);
            elseif($tagStatus == 2) //无任何标签
                return !MemberTag::isMemberHasTags($memberId);
        }
        return true;
    }

    /**
     * 是否有参与限制
     * @return bool
     */
    public function isJoinLimit()
    {
        $limitData = $this->getJoinLimit();

        if(isset($limitData['not_limit']) && $limitData['not_limit']['enable'] == 1)
            return false;
        return true;
    }

    public function isEnd()
    {
        return $this->status == QUESTIONNAIRE_ACTIVITY_STATUS::$END || Carbon::now()->diffInSeconds($this->endTime) < 0;
    }

    public function isStart()
    {
        return $this->status == QUESTIONNAIRE_ACTIVITY_STATUS::$STARTED || strtotime($this->startTime) <= time() && strtotime($this->endTime) > time();
    }

    public function submit(MemberModel $submitMember, array $data, QuestionnaireShareMember $shareMember=null)
    {
        if($this->getSubmitLimit() > 0)
        {
            $submitNums = QuestionnaireStatistics::MemberSubmitNums($this->getQuestionnaireId(), $submitMember->member_id);
            if($submitNums >= $this->getSubmitLimit())
                throw new ClientException("超出提交限制");
        }
        $this->checkColumnRequire($data);
        $data = $this->formatSubmitData($data);
        $shareMemberId = $shareMember && $shareMember->isEnable() ? $shareMember->getMemberId() : 0;

        $addData['questionnaire_id'] = $this->getQuestionnaireId();
        $addData['member_id'] = $submitMember->member_id;
        $addData['data'] = $data;
        $addData['last_update_time'] = Carbon::now()->toDateTimeString();
        $addData['share_member_id'] = $shareMemberId;
        return QuestionnaireSubmitModel::create($addData);
    }

    /**
     * 格式化提交数据
     * @param array $submitData
     * @return array
     */
    public function formatSubmitData(array $submitData)
    {
        $cData = $this->getColumnData();
        $columnData = [];
        foreach ($cData as $c)
        {
            $columnData[$c['name']] = $c;
        }

        $retData = [];
        foreach ($submitData as $k=>$sData)
        {
            if(isset($columnData[$sData['name']]))
            {
                $sData['type'] = $columnData[$sData['name']]['type'];
                $retData[] = $sData;
            }
        }

        return $retData;
    }

    public function checkColumnRequire(array $data)
    {
        $requireColumn = [];
        $columnData = $this->getColumnData();
        foreach ($columnData as $c)
        {
            if($c['is_require'] == 1)
                $requireColumn[] = $c['name'];
        }
        $submitColumns = array_column($data, 'name');

        $loseColumn = array_diff($requireColumn, $submitColumns);
        if($loseColumn)
            throw new ClientException(implode(",", $loseColumn)."必填");
        return true;
    }

    public function access(MemberModel $accessMember, QuestionnaireShareMember $shareMember=null)
    {
        $data['questionnaire_id'] = $this->getQuestionnaireId();
        $data['questionnaire_url'] = request()->url();
        $data['share_member_id'] = $shareMember ? $shareMember->getMemberId() : 0;
        $data['access_member_id'] = $accessMember->member_id;
        $data['access_time'] = Carbon::now()->toDateTimeString();
        QuestionnaireAccessModel::create($data);
    }
}