<?php


namespace addon\questionnaireActivity\domainModel;

use app\Domain\DataTransformTrait;
use app\Domain\Models\DomainModel;
use app\Exceptions\ClientException;
use Carbon\Carbon;

class QuestionnaireSubmit extends DomainModel
{
    protected $primary_key = "id";
    protected $questionnaireId = 0;
    protected $memberId = 0;
    protected $data = [];
    protected $lastUpdateTime = "";
    protected $shareMemberId = 0;
    protected $belongQuestionnaire = null;


    /**
     * @return int
     */
    public function getQuestionnaireId(): int
    {
        return $this->questionnaireId;
    }

    /**
     * @param int $questionnaireId
     */
    public function setQuestionnaireId(int $questionnaireId): void
    {
        $this->questionnaireId = $questionnaireId;
    }

    /**
     * @return int
     */
    public function getMemberId(): int
    {
        return $this->memberId;
    }

    /**
     * @param int $memberId
     */
    public function setMemberId(int $memberId): void
    {
        $this->memberId = $memberId;
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @param array $data
     */
    public function setData(array $data): void
    {
        $this->data = $data;
        $this->addUpdateData("data", $data);
    }

    /**
     * @return string
     */
    public function getLastUpdateTime(): string
    {
        return $this->lastUpdateTime;
    }

    /**
     * @param string $lastUpdateTime
     */
    public function setLastUpdateTime(string $lastUpdateTime): void
    {
        $this->lastUpdateTime = $lastUpdateTime;
        $this->addUpdateData("last_update_time", $lastUpdateTime);

    }

    /**
     * @return int
     */
    public function getShareMemberId(): int
    {
        return $this->shareMemberId;
    }

    /**
     * @param int $shareMemberId
     */
    public function setShareMemberId(int $shareMemberId): void
    {
        $this->shareMemberId = $shareMemberId;
    }

    /**
     * @return Questionnaire|null
     */
    public function belongQuestionnaire():?Questionnaire
    {
        if($this->questionnaireId && !$this->belongQuestionnaire)
        {
            return \addon\questionnaireActivity\facade\QuestionnaireRepository::find($this->questionnaireId);
        }
        return $this->belongQuestionnaire;
    }

    public function edit(array $data)
    {
        $questionnaire = $this->belongQuestionnaire();
        if(!$questionnaire)
            throw new \Exception('问卷不存在');
        if($questionnaire->isEnd())
            throw new ClientException('问卷活动已结束');
        if(!$questionnaire->getEditLimit())
            throw new ClientException('问卷不可修改');

        $questionnaire->checkColumnRequire($data);
        $data = $questionnaire->formatSubmitData($data);

        $this->setData($data);
        $this->setLastUpdateTime(Carbon::now()->toDateTimeString());
        \addon\questionnaireActivity\facade\QuestionnaireSubmitRepository::updateModel($this);
    }
}