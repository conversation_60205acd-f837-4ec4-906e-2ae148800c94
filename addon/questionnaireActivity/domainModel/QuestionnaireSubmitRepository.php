<?php


namespace addon\questionnaireActivity\domainModel;


use addon\questionnaireActivity\model\QuestionnaireSubmitModel;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlRepository;
use think\Model;

class QuestionnaireSubmitRepository extends MysqlRepository
{
    public function __construct()
    {
        $this->dbModel = new QuestionnaireSubmitModel();
    }

    /**
     * @param int $submitId
     * @return QuestionnaireSubmit|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function find(int $submitId)
    {
        $questionnaire = null;
        $ml = $this->dbModel->where("id", $submitId)->find();
        if($ml)
        {
            $questionnaire = new QuestionnaireSubmit();
            $this->DomainModelInitDbData($questionnaire, $ml);
        }
        return $questionnaire;
    }

    /**
     * @param $submitId
     * @param $memberId
     * @return QuestionnaireSubmit|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function findMemberSubmit($submitId, $memberId)
    {
        $questionnaire = null;
        $ml = $this->dbModel->where("id", $submitId)->where("member_id", $memberId)->find();
        if($ml)
        {
            $questionnaire = new QuestionnaireSubmit();
            $this->DomainModelInitDbData($questionnaire, $ml);
        }
        return $questionnaire;
    }

}