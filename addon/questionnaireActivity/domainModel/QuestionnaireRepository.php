<?php


namespace addon\questionnaireActivity\domainModel;


use addon\questionnaireActivity\dataType\QUESTIONNAIRE_ACTIVITY_STATUS;
use addon\questionnaireActivity\model\QuestionnaireModel;
use app\Domain\DataTransformTrait;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlRepository;
use Carbon\Carbon;

class QuestionnaireRepository extends MysqlRepository
{
    public function __construct()
    {
        $this->dbModel = new QuestionnaireModel();
    }

    public function find(int $questionnaireId)
    {
        $questionnaire = null;
        $ml = $this->dbModel->where("questionnaire_id", $questionnaireId)->find();
        if($ml)
        {
            $questionnaire = new Questionnaire();
            $this->DomainModelInitDbData($questionnaire, $ml);
        }
        return $questionnaire;
    }

    public function getForNoStart()
    {
        $questionnaireList = [];
        $mls = $this->dbModel->where("status", QUESTIONNAIRE_ACTIVITY_STATUS::$WAIT)->where("start_time", "<=", Carbon::now()->toDateTimeString())->where("end_time", ">", Carbon::now()->toDateTimeString())->select();

        foreach ($mls as $ml)
        {
            $questionnaire = new Questionnaire();
            $this->DomainModelInitDbData($questionnaire, $ml);
            $questionnaireList[] = $questionnaire;
        }
        return $questionnaireList;
    }

    public function getForNoEnd()
    {
        $questionnaireList = [];
        $mls = $this->dbModel->where("status", "<>",QUESTIONNAIRE_ACTIVITY_STATUS::$END)->where("end_time", "<", Carbon::now()->toDateTimeString())->select();
        foreach ($mls as $ml)
        {
            $questionnaire = new Questionnaire();
            $this->DomainModelInitDbData($questionnaire, $ml);
            $questionnaireList[] = $questionnaire;
        }
        return $questionnaireList;
    }
}