<?php


namespace addon\questionnaireActivity\domainModel;


use addon\questionnaireActivity\model\QuestionnaireAccessModel;
use addon\questionnaireActivity\model\QuestionnaireShareMemberModel;
use addon\questionnaireActivity\model\QuestionnaireSubmitModel;

class QuestionnaireStatistics
{
    /**
     * 问卷活动用户提交数
     * @param $questionnaireId
     * @param $memberId
     * @return int
     */
    public static function MemberSubmitNums($questionnaireId, $memberId)
    {
        return QuestionnaireSubmitModel::where("questionnaire_id", $questionnaireId)
            ->where("member_id", $memberId)
            ->count();
    }

    /**
     * 问卷活动提交数
     * @param $questionnaireId
     * @return int
     */
    public static function submitNums($questionnaireId)
    {
        return QuestionnaireSubmitModel::where("questionnaire_id", $questionnaireId)
            ->count();
    }

    /**
     * 问卷活动分享人数
     * @param $questionnaireId
     * @return int
     */
    public static function shareMemberNums($questionnaireId)
    {
        return QuestionnaireShareMemberModel::where("questionnaire_id", $questionnaireId)
            ->count();
    }

    /**
     * 分享浏览次数
     * @param $questionnaireId
     * @param $shareMemberId   //分享人memeber_id
     * @param $statisticsBetweenTime  //统计时间段
     * @return int
     */
    public static function shareViews($questionnaireId,$shareMemberId,$statisticsBetweenTime): int
    {
        $where = [
            ['questionnaire_id','=',$questionnaireId],
            ['share_member_id','=',$shareMemberId]
        ];
        if(is_array($statisticsBetweenTime) && count($statisticsBetweenTime)==2){
            $where[] = ['create_time', 'between time', $statisticsBetweenTime];
        }
        return QuestionnaireAccessModel::where($where)->count();
    }

    /**
     * 分享访问人数
     * @param $questionnaireId
     * @param $shareMemberId //分享人memeber_id
     * @param $statisticsBetweenTime //统计时间段
     * @return int
     */
    public static function ShareVisitors($questionnaireId,$shareMemberId,$statisticsBetweenTime): int
    {
        $where = [
            ['questionnaire_id','=',$questionnaireId],
            ['share_member_id','=',$shareMemberId]
        ];
        if(is_array($statisticsBetweenTime) && count($statisticsBetweenTime)==2){
            $where[] = ['create_time', 'between time', $statisticsBetweenTime];
        }
        return QuestionnaireAccessModel::where($where)->count('distinct access_member_id');
    }

    /**
     * 分享人分享出来的问卷的答题数量
     * @param $questionnaireId
     * @param $shareMemberId
     * @param $statisticsBetweenTime
     * @return int
     */
    public static function shareSubmitNums($questionnaireId,$shareMemberId,$statisticsBetweenTime) :int
    {
        $where = [
            ['questionnaire_id','=',$questionnaireId],
            ['share_member_id','=',$shareMemberId]
        ];
        if(is_array($statisticsBetweenTime) && count($statisticsBetweenTime)==2){
            $where[] = ['create_time', 'between time', $statisticsBetweenTime];
        }
        return QuestionnaireSubmitModel::where($where)->count();
    }
}