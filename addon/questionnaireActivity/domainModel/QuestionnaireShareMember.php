<?php


namespace addon\questionnaireActivity\domainModel;

use app\Domain\DataTransformTrait;

class QuestionnaireShareMember extends \app\Domain\Models\DomainModel
{
    use DataTransformTrait;

    protected $questionnaireId = 0;

    protected $memberId = 0;

    protected $enable = false;

    protected $belongQuestionnaire = null;

    /**
     * @return int
     */
    public function getQuestionnaireId(): int
    {
        return $this->questionnaireId;
    }

    /**
     * @param int $questionnaireId
     */
    public function setQuestionnaireId(int $questionnaireId): void
    {
        $this->questionnaireId = $questionnaireId;
    }

    /**
     * @return bool
     */
    public function isEnable(): bool
    {
        return $this->enable;
    }

    /**
     * @param bool $enable
     */
    public function setEnable(bool $enable): void
    {
        $this->enable = $enable;
        $this->addUpdateData('enable', $enable);

    }

    /**
     * @return int
     */
    public function getMemberId(): int
    {
        return $this->memberId;
    }

    /**
     * @param int $memberId
     */
    public function setMemberId(int $memberId): void
    {
        $this->memberId = $memberId;
    }


    /**
     * @return Questionnaire
     */
    public function belongQuestionnaire():?Questionnaire
    {
        if($this->questionnaireId > 0 && !$this->belongQuestionnaire)
        {
            return \addon\questionnaireActivity\facade\QuestionnaireRepository::find($this->questionnaireId);
        }
        return $this->belongQuestionnaire;
    }


}