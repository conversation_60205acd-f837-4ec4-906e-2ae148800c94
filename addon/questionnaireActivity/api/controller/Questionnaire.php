<?php


namespace addon\questionnaireActivity\api\controller;

use addon\questionnaireActivity\domainModel\QuestionnaireRepository;
use addon\questionnaireActivity\doMainService\QuestionnaireService;
use addon\questionnaireActivity\doMainService\QuestionnaireSubmitService;
use addon\questionnaireActivity\event\QuestionnaireAccessEvent;
use addon\questionnaireActivity\model\QuestionnaireModel;
use addon\questionnaireActivity\model\QuestionnaireSubmitModel;
use app\api\controller\BaseApi;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\Request;
use PHPUnit\Exception;

class Questionnaire extends BaseApi
{
    public function index(Request $request, QuestionnaireRepository $questionnaireRepository)
    {
        $token = $this->checkToken();

        $rules = [
            'questionnaire_id' => 'require|number',
            'share_member_id' => 'number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $questionnaire = $questionnaireRepository->find($data['questionnaire_id']);
        $shareMember = isset($data['share_member_id']) && $data['share_member_id'] > 0 ? $questionnaire->findShareMember($data['share_member_id']) : null;

        if(!$questionnaire)
            return $this->response($this->error('', '活动不存在'));
        if($questionnaire->isEnd())
            return $this->response($this->error('', 'URL_TIME_OUT'));
        if(!$questionnaire->isStart())
            return $this->response($this->error('', '活动尚未开始'));

        if($this->member_id && !$questionnaire->checkJoinLimit($this->member_id, $failReason))
        {
            if(in_array("实名验证不通过", $failReason))
                return $this->response($this->error('', 'REALNAME_AUTH_ERROR'));
            else
                return $this->response($this->error('', 'JOIN_LIMIT'));
        }

        $ret = QuestionnaireModel::field("name,share_title,status,image,content,column_data,bottom_image")->find($data['questionnaire_id'])->toArray();
        if($this->member_id){
            event(new QuestionnaireAccessEvent($questionnaire, MemberModel::find($this->member_id), $shareMember));
        }
        return $this->response($this->success($ret));
    }

    /**
     * 问卷提交
     * @param Request $request
     * @param QuestionnaireService $service
     * @return false|string
     */
    public function submit(Request $request, QuestionnaireService $service)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'questionnaire_id' => 'require|number',
            'share_member_id' => 'number',
            'data' => 'require',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);
        if($data['data']){
            try {
                $data['data'] = json_decode($data['data'],true);
            }catch (Exception $exception){

            }
        }
        $shareMemberId = $data['share_member_id'] ?? 0;
        $ret = $service->submit($data['questionnaire_id'], $this->member_id, $data['data'], $shareMemberId);
        if($ret)
            return $this->response($this->success(['submit_id'=>$ret, 'qr_code'=>QuestionnaireModel::find($data['questionnaire_id'])->bottom_image]));
        else
            return $this->response($this->error('', $service->getFailReason() ?: '提交失败'));
    }

    public function lists(Request $request)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);
        $rules = [
            'questionnaire_id' => 'require|number',
            'submit_start_date' => 'date',
            'submit_end_date' => 'date'
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $page = input('page', 1);
        $page_size = input('page_size', PAGE_LIST_ROWS);

        $where = [
            ["qs.member_id", '=',$this->member_id]
        ];
        if(isset($data['questionnaire_id']) && $data['questionnaire_id']>0){
            $where[] = ["qs.questionnaire_id", '=', $data['questionnaire_id']];
        }
        if(isset($data['submit_start_date']) && isset($data['submit_end_date'])){
            $where[] = ['qs.last_update_time','between time',[strtotime($data['submit_start_date']),strtotime($data['submit_end_date'])]];
        }


        $ret = QuestionnaireSubmitModel::alias('qs')->join('xm_questionnaire q','qs.questionnaire_id=q.questionnaire_id','left')
            ->where($where)
            ->field("qs.id as submit_id,qs.member_id,qs.last_update_time,q.name")
            ->order("id", "desc")
            ->paginate($page_size);

        foreach ($ret as $i=>$r)
        {
            $mobile = MemberModel::where("member_id", $r['member_id'])->value('mobile');
            $ret[$i]['mobile'] = formatPhone($mobile);
        }

        $ret = model_to_api_page($ret);
        $join_activity_list = QuestionnaireSubmitModel::alias('qs')->join('xm_questionnaire q','qs.questionnaire_id=q.questionnaire_id','left')
            ->field('q.questionnaire_id,q.name')->where("member_id", $this->member_id)->group('q.questionnaire_id')->select()->toArray();
        $ret['join_activity_list'] = array_merge([['questionnaire_id'=>0,'name'=>'全部问卷']],$join_activity_list);
        return $this->response($this->success($ret));
    }

    public function childLists(Request $request)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'questionnaire_id' => 'require|number',
            'submit_start_date' => 'date',
            'submit_end_date' => 'date',
            'mobile' => 'max:100'
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $page = input('page', 1);
        $page_size = input('page_size', PAGE_LIST_ROWS);

        $where = [
            ["qs.member_id", "<>", $this->member_id],
            ["qs.share_member_id", '=', $this->member_id]
        ];

        if(isset($data['questionnaire_id']) && $data['questionnaire_id']>0){
            $where[] = ["qs.questionnaire_id", '=', $data['questionnaire_id']];
        }
        if(isset($data['submit_start_date']) && isset($data['submit_end_date'])){
            $where[] = ['qs.last_update_time','between time',[strtotime($data['submit_start_date']),strtotime($data['submit_end_date'])]];
        }
        if(isset($data['mobile'])){
            $where[] = ['xm.mobile','like',"%{$data['mobile']}"];
        }

        $ret = QuestionnaireSubmitModel::alias('qs')
            ->join('xm_questionnaire q','qs.questionnaire_id=q.questionnaire_id','left')
            ->join('xm_member xm','qs.member_id=xm.member_id','left')
            ->where($where)
            ->field("qs.id as submit_id,qs.member_id,qs.last_update_time,xm.mobile,q.name")
            ->order("id", "desc")
            ->paginate($page_size);

        foreach ($ret as $i=>$r)
        {
            $ret[$i]['mobile'] = formatPhone($r["mobile"]);
        }

        $ret = model_to_api_page($ret);
        $join_activity_list = QuestionnaireSubmitModel::alias('qs')->join('xm_questionnaire q','qs.questionnaire_id=q.questionnaire_id','left')
            ->field('q.questionnaire_id,q.name')
            ->where("member_id", "<>", $this->member_id)
            ->where("share_member_id", $this->member_id)->group('q.questionnaire_id')->select()->toArray();
        $ret['join_activity_list'] = array_merge([['questionnaire_id'=>0,'name'=>'全部问卷']],$join_activity_list);
        return $this->response($this->success($ret));
    }

    /**
     * 问卷提交记录详情
     * @param Request $request
     * @return false|string
     */
    public function detail(Request $request, QuestionnaireRepository $questionnaireRepository)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'submit_id' => 'require|number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);
        $submit = QuestionnaireSubmitModel::where("id", $data['submit_id'])->find();

        if(!$submit || ($submit->member_id != $this->member_id && $submit->share_member_id != $this->member_id))
        {
            return $this->response($this->error('', '提交记录不存在'));
        }
        $submitData['column_data'] = $submit->data;
        $questionnaire = $questionnaireRepository->find($submit->questionnaire_id);
        $submitData['is_edit'] = $questionnaire->getEditLimit() && $this->member_id == $submit->member_id;
        return $this->response($this->success($submitData));
    }

    /**
     * 问卷提交记录编辑详情
     * @param Request $request
     * @return false|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function editDetail(Request $request)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'submit_id' => 'require|number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $submit = QuestionnaireSubmitModel::where("id", $data['submit_id'])->where("member_id", $this->member_id)->find();
        if(!$submit)
            return $this->response($this->error('', '提交记录不存在'));
        $sData = $submit->data;
        $submitData = [];
        foreach ($sData as $s)
            $submitData[$s['name']] = $s;

        $questionnaire = QuestionnaireModel::find($submit->questionnaire_id)->toArray();
        $columnData = $questionnaire['column_data'];
        foreach ($columnData as $i=>&$s)
        {
            $columnData[$i]['value'] = $submitData[$s['name']]['value'] ?: "";
            if($s['type'] == 'radio')
            {
                foreach ($s['select_data'] as &$selectData)
                {
                    if($columnData[$i]['value'] == $selectData['value'])
                        $selectData['is_select'] = true;
                    else
                        $selectData['is_select'] = false;
                }
            }
            elseif($s['type'] == 'checkbox')
            {
                foreach ($s['select_data'] as &$selectData)
                {
                    $columnValues = explode("/", $columnData[$i]['value']);
                    if(in_array($selectData['value'], $columnValues))
                        $selectData['is_select'] = true;
                    else
                        $selectData['is_select'] = false;
                }
            }
        }
        $questionnaire['column_data'] = $columnData;
        return $this->response($this->success($questionnaire));
    }

    public function edit(Request $request, QuestionnaireSubmitService $submitService)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $rules = [
            'submit_id' => 'require|number',
            'data' => 'require',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);
        if($data['data']){
            try {
                $data['data'] = json_decode($data['data'],true);
            }catch (Exception $exception){

            }
        }

        $ret = $submitService->edit($data['submit_id'], $this->member_id, $data['data']);
        if($ret)
            return $this->response($this->success($ret));
        else
            return $this->response($this->error('', $submitService->getFailReason() ?: '编辑失败'));
    }
}