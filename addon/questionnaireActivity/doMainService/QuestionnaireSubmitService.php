<?php


namespace addon\questionnaireActivity\doMainService;


use addon\questionnaireActivity\domainModel\QuestionnaireSubmitRepository;
use app\Exceptions\ClientException;
use think\facade\Log;

class QuestionnaireSubmitService extends DomainService
{
    protected $submitRepository;

    public function __construct(QuestionnaireSubmitRepository $submitRepository)
    {
        $this->submitRepository = $submitRepository;
    }

    public function edit($submitId, $memberId, array $data)
    {
        try
        {
            $submit = $this->submitRepository->findMemberSubmit($submitId, $memberId);
            if(!$submit)
                throw new \Exception('问卷提交记录不存在');

            $submit->edit($data);
            return true;
        }
        catch (ClientException $e)
        {
            $this->failReason = $e->getMessage();
            return false;
        }
        catch (\Exception $e)
        {
            Log::error("问卷记录编辑失败:".$e->getMessage());
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return false;
        }
    }
}