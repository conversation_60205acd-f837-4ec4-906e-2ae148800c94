<?php


namespace addon\questionnaireActivity\doMainService;



use addon\questionnaireActivity\domainModel\Questionnaire;
use addon\questionnaireActivity\domainModel\QuestionnaireRepository;
use addon\questionnaireActivity\domainModel\QuestionnaireSubmitRepository;
use app\Exceptions\ClientException;
use app\model\member\MemberModel;
use think\facade\Log;

/**
 * 问卷服务
 * Class QuestionnaireService
 * @package addon\questionnaireActivity\doMainService
 */
class QuestionnaireService extends DomainService
{
    /**
     * @var QuestionnaireRepository
     */
    protected $questionnaireRepository;
    public function __construct(QuestionnaireRepository $questionnaireRepository)
    {
        $this->questionnaireRepository = $questionnaireRepository;

    }

    /**
     * 根据手机号添加分享人
     * @param $questionnaireId
     * @param $mobile
     * @return bool
     */
    public function addShareMemberByMobile($questionnaireId, $mobile)
    {
        try
        {
            $questionnaire = $this->questionnaireRepository->find($questionnaireId);
            if(!$questionnaire)
                throw new \Exception('问卷不存在');

            $member = MemberModel::where("status", 1)->where("mobile", $mobile)->find();
            if(!$member)
                throw new \Exception('用户不存在或已注销');
            return $questionnaire->addShareMember($member);
        }
        catch (\Exception $e)
        {
            Log::error("添加分享人失败:".$e->getMessage());
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 变更分享人权限
     * @param $questionnaireId
     * @param $memberId
     * @return bool
     */
    public function changeShareMemberEnable($questionnaireId, $memberId, $enable)
    {
        try
        {
            $questionnaire = $this->questionnaireRepository->find($questionnaireId);
            if(!$questionnaire)
                throw new \Exception('问卷不存在');

            $member = MemberModel::find($memberId);
            if(!$member)
                throw new \Exception('用户不存在');
            $questionnaire->changeShareMemberEnable($member, $enable);
            return true;
        }
        catch (\Exception $e)
        {
            Log::error("变更分享人权限失败:".$e->getMessage());
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 问卷提交
     * @param $questionnaireId
     * @param $memberId
     * @param $data
     * @param int $shareMemberId
     * @return int|mixed
     */
    public function submit($questionnaireId, $memberId, $data, $shareMemberId=0)
    {
        try
        {
            $questionnaire = $this->questionnaireRepository->find($questionnaireId);
            if(!$questionnaire)
                throw new \Exception('问卷不存在');
            if($questionnaire->isEnd())
                throw new ClientException('链接已过期');
            if(!$questionnaire->isStart())
                throw new ClientException('问卷活动尚未开始');

            $member = MemberModel::find($memberId);
            if(!$member)
                throw new \Exception('用户不存在');

            $shareMember = $questionnaire->findShareMember($shareMemberId);
            $ret = $questionnaire->submit($member, $data, $shareMember);
            return $ret->id;
        }
        catch (ClientException $e)
        {
            $this->failReason = $e->getMessage();
            return 0;
        }
        catch (\Exception $e)
        {
            Log::error("问卷提交失败:".$e->getMessage());
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return 0;
        }
    }

    public function advanceEnd($questionnaireId)
    {
        try
        {
            $questionnaire = $this->questionnaireRepository->find($questionnaireId);
            if(!$questionnaire)
                throw new \Exception('问卷不存在');
            if(!$questionnaire->isStart())
                throw new \Exception('状态异常');
            $questionnaire->advanceEnd();
            return true;
        }
        catch (\Exception $e)
        {
            Log::error("问卷结束失败:".$e->getMessage());
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return false;
        }
    }
}