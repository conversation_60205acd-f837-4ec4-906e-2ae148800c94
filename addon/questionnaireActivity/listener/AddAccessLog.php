<?php


namespace addon\questionnaireActivity\listener;


use addon\questionnaireActivity\event\QuestionnaireAccessEvent;
use think\facade\Log;

class AddAccessLog
{
    public function handle(QuestionnaireAccessEvent $event)
    {
        try
        {
            $questionnaire = $event->questionnaire;
            $questionnaire->access($event->accessMember, $event->shareMember);
        }
        catch (\Exception $e)
        {
            Log::error("写入访问日志失败");
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
        }
    }
}