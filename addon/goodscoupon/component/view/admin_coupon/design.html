<nc-component v-bind:data="data[index]" class="admin-coupon">

	<!-- 预览 -->
	<template slot="preview">
		
		<div class="coupon ns-bg-color">
			<div class="price">
				<span>满10元可用</span>
				<span>￥<span>100</span></span>
			</div>
			<span class="operation">领取</span>
		</div>

		<div class="coupon ns-bg-color">
			<div class="price">
				<span>满10元可用</span>
				<span>￥<span>100</span></span>
			</div>
			<span class="operation">领取</span>
		</div>

		<!--<div class="left-img"><img v-bind:src="nc.left_img_url ? changeImgUrl(nc.left_img_url) : '{$resource_path}/search/img/category.png'" class="self-adaption"/></div>-->
		<!--<div class="top-search-form">-->
			<!--<div class="top-search-box">-->
				<!--<input type="text" placeholder="搜索"/>-->
				<!--<span class="top-search-icon"><img src="{$resource_path}/search/img/icon_search.png" class="self-adaption"/></span>-->
			<!--</div>-->
		<!--</div>-->
		<!--<div class="right-img"><img v-bind:src="nc.right_img_url ? changeImgUrl(nc.right_img_url) : '{$resource_path}/search/img/user.png'" class="self-adaption"/></div>-->

	</template>

	<!-- 编辑 -->
	<template slot="edit">
		
		<template v-if="nc.lazyLoad">
			<admin-coupon></admin-coupon>
		</template>
	
	</template>
	
	<!-- 资源 -->
	<template slot="resource">

		<css src="{$resource_path}/admin_coupon/css/design.css"></css>
		<js src="{$resource_path}/admin_coupon/js/design.js"></js>
		
	</template>

</nc-component>