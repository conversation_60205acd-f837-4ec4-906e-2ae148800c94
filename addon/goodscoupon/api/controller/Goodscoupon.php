<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\goodscoupon\api\controller;

use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\goodscoupon\model\GoodscouponGoods;
use app\api\controller\BaseApi;
use addon\goodscoupon\model\Goodscoupon as GoodscouponModel;
use addon\goodscoupon\model\GoodscouponType as GoodscouponTypeModel;
use addon\goodscoupon\model\MemberGoodscoupon;
use think\facade\Db;

/**
 * 优惠券
 */
class Goodscoupon extends BaseApi
{

    /**
     * 优惠券类型信息
     */
    public function typeinfo()
    {
        try {
            $goodsCouponTypeId = $this->params['goodscoupon_type_id'] ?? 0;
            if (empty($goodsCouponTypeId)) {
                throw new \Exception('REQUEST_GOODSCOUPON_TYPE_ID');
            }

            $goodsCouponModel = new GoodscouponModel();
            $condition = [
                [ 'goodscoupon_type_id', '=', $goodsCouponTypeId ],
                [ 'is_show', '=', 1 ]
            ];

            $info = $goodsCouponModel->getGoodscouponTypeInfo($condition);
            $info = $goodsCouponModel->goodscouponTypeInfoFormat($info);

            return $this->response($info);
        } catch (\Exception $e) {
            return $this->response($this->error('', $e->getMessage()));
        }

    }

    /**
     * 列表信息
     */
    public function memberpage()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);
        // $token['data']['member_id'] = 383;
        $page = $this->params['page'] ?? 1;
        $page_size = $this->params['page_size'] ?? PAGE_LIST_ROWS;
        $state = $this->params['state'] ?? 1;                                   //优惠券状态 1已领用（未使用） 2已使用 3已过期
        $is_own = $this->params['is_own'] ?? '';                                //是否自营

        $goodscoupon_model = new GoodscouponModel();
        $condition = [
            [ 'npc.member_id', '=', $token['data']['member_id'] ],
            [ 'npc.state', '=', $state ]
        ];
        $list = $goodscoupon_model->getMemberGoodscouponPageList($condition, $page, $page_size);

        if (!empty($list['data']['list'])) {
            foreach ($list['data']['list'] as $k => $v) {
                $list['data']['list'][$k]['desc'] = $v['use_scenario'] == 1 ? '全场商品' : ($v['use_scenario'] == 2 ? '指定分类商品' : '指定商品');
                $list['data']['list'][$k]['desc'] .= '满' . round($v['at_least'], 2) . '元可用';
            }
        }

        return $this->response($list);
    }

    /**
     * 优惠券类型列表
     */
    public function typelists()
    {

        $goodscoupon_model = new GoodscouponModel();
        $condition = [
            [ 'status', '=', 1 ],
            [ 'is_show', '=', 1 ],
        ];

        $list = $goodscoupon_model->getGoodscouponTypeList($condition, "goodscoupon_type_id,goodscoupon_name,money,max_fetch,at_least,end_time,validity_type,fixed_term,use_scenario", "money desc", "");
        return $this->response($list);
    }

    /**
     * 优惠券类型分页列表
     */
    public function typepagelists()
    {
        $page = $this->params['page'] ?? 1;
        $page_size = $this->params['page_size'] ?? PAGE_LIST_ROWS;

        $goodscoupon_model = new GoodscouponModel();
        $condition = [
            [ 'status', '=', 1 ],
            [ 'is_show', '=', 1 ],
        ];

        $list = $goodscoupon_model->getGoodscouponTypePageList($condition, $page, $page_size);
        return $this->response($list);
    }

    /**
     * 获取优惠券
     * @return false|string
     */
    public function receive()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $goodscoupon_type_id = $this->params['goodscoupon_type_id'] ?? 0;
        $get_type = $this->params['get_type'] ?? 2;//获取方式:1订单2.直接领取3.活动领取

        if (empty($goodscoupon_type_id)) {
            return $this->response($this->error('', 'REQUEST_COUPON_TYPE_ID'));
        }

        $goodscoupon_model = new GoodscouponModel();
        $res = $goodscoupon_model->receiveGoodscoupon($goodscoupon_type_id, $token['data']['member_id'], $get_type);

        $res['data'] = [];
        //判断一下用户是否拥有当前优惠券
        $coupon_result = $goodscoupon_model->getGoodscouponInfo([['goodscoupon_type_id', '=', $goodscoupon_type_id], ['member_id', '=', $token['data']['member_id']]], 'goodscoupon_type_id');
        $coupon = $coupon_result['data'];
        $res['data']['is_exist'] = empty($coupon) ? 0 : 1;

        return $this->response($res);
    }

    /**
     * 会员优惠券数量
     * @return string
     */
    public function num()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $state = $this->params['state'] ?? 1;
        $goodscoupon_model = new MemberGoodscoupon();

        $count = $goodscoupon_model->getMemberGoodscouponNum($token['data']['member_id'], $state);
        return $this->response($count);
    }

    /**
     * 会员优惠券数量
     * @return string
     */
    public function num_info()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $goodscoupon_model = new MemberGoodscoupon();
        $data = $goodscoupon_model->getMemberGoodscouponNum($token['data']['member_id'], 1);
        $retData['receipt_count'] = $data['code'] == 0 ? $data['data'] : 0;
        $data = $goodscoupon_model->getMemberGoodscouponNum($token['data']['member_id'], 2);
        $retData['used_count'] = $data['code'] == 0 ? $data['data'] : 0;
        return $this->response($this->success($retData));
    }

    /**
     * 会员优惠券数量
     * @return string
     */


    /**
     * 是否可以领取
     */
    public function receivedNum()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $goodscoupon_type_id = $this->params['goodscoupon_type_id'] ?? 0;

        $goodscoupon_model = new MemberGoodscoupon();
        $res = $goodscoupon_model->receivedNum($goodscoupon_type_id, $this->member_id, 2);
        return $this->response($res);
    }


    public function goodsList()
    {
        try {
            $this->checkToken();
            $page = $this->params['page'] ?? 1;
            $page_size = $this->params['page_size'] ?? PAGE_LIST_ROWS;
            $goodsCouponTypeId = $this->params['goodscoupon_type_id'] ?? 0;
            if (empty($goodsCouponTypeId)) {
                throw new \Exception('REQUEST_GOODSCOUPON_TYPE_ID');
            }

            $goodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodsCouponTypeId]);

            $goodsCouponGoods = new GoodscouponGoods();
            $res = $this->success(['count' => 0, 'page_count' => 0, 'list' => []]);
            if ($goodsCouponTypeInfo['use_scenario'] == GOODSCOUPON_SCENARIO::GOODS) {
                $res = $goodsCouponGoods->getGoodsCouponGoodsList($this->member_id ?? 0, $goodsCouponTypeId, $page, $page_size);
            } elseif ($goodsCouponTypeInfo['use_scenario'] == GOODSCOUPON_SCENARIO::CATEGORY) {
                $res = $goodsCouponGoods->getGoodsCouponCategoryGoodsList($this->member_id ?? 0, $goodsCouponTypeId, $page, $page_size);
            } elseif ($goodsCouponTypeInfo['use_scenario'] == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS) {
                $res = $goodsCouponGoods->getGoodsCouponExcludeGoodsList($this->member_id ?? 0, $goodsCouponTypeId, $page, $page_size);
            } elseif ($goodsCouponTypeInfo['use_scenario'] == GOODSCOUPON_SCENARIO::TOPIC) {
                $res = $goodsCouponGoods->getTopicGoodsList($this->member_id ?? 0, $goodsCouponTypeId, $page, $page_size);
            }elseif ($goodsCouponTypeInfo['use_scenario'] == GOODSCOUPON_SCENARIO::ALL){
                $res = $goodsCouponGoods->getALlGoodsList($this->member_id ?? 0, $goodsCouponTypeId, $page, $page_size);
            }

            return $this->response($res);
        } catch (\Exception $e) {
            return $this->response($this->error('', $e->getMessage()));
        }
    }

    public function use_remind()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->response($token);

        $memberId = $this->member_id;
        $res = (new GoodscouponModel())->getNoUseRemind($memberId);
        return $this->response($res);

    }
}