<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */
namespace addon\goodscoupon\model;

use app\model\BaseModel;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\order\OrderExport;

/**
 * 订单导出
 * <AUTHOR>
 */

class GoodsCouponExport extends BaseModel
{
    public $fields = [
        'goodscoupon_type_id' => '活动ID',
        'username' => '领取用户名称',
        'mobile' => '用户手机号',
        'goodscoupon_name' => '活动名称',
        'privacy_status' => '券类型',
        'money' => '优惠金额',
        'state' => '优惠券状态',
        'fetch_time' => '领取时间',
        'get_type' => '获取方式',
        'use_time' => '使用时间',
        'order_money' => '关联订单金额',
        'order_no' => '关联订单号',
    ];


    public $define_data = [
        'state' => ['type' => 2, 'data' => [1 => '已领取', 2 => '已使用', 3 => '已过期']],
        'fetch_time' => ['type' => 1],
        'use_time' => ['type' => 1],
        'privacy_status' => ['type' => 2, 'data' => [0 => '内部券', 1 => '公开券']],
        'get_type' => ['type' => 2, 'data' => [1 => '订单', 2 => '直接领取', 3 => '活动领取', 4 => '后台发放', 5 => '规则自动派发']],
    ];


    /**
     *  数据处理
     * @param $data
     * @param $field
     * @return array
     */
    public function handleData($data, $field): array
    {
        $define_data = $this->define_data;
        foreach ($data as $k => $v) {
            //获取键
            $keys = array_keys($v);

            foreach ($keys as $key) {

                if (in_array($key, $field)) {

                    if (array_key_exists($key, $define_data)) {

                        $type = $define_data[$key]['type'];

                        switch ($type) {

                            case 1:
                                $data[$k][$key] = time_to_date($v[$key]);
                                break;
                            case 2:
                                $define_data_data = $define_data[$key]['data'];
                                if ($key == 'privacy_status') {
                                    if (is_null($v[$key])) {
                                        $data[$k][$key] = $define_data_data[0];
                                    } else {
                                        $data[$k][$key] = $define_data_data[$v[$key]];
                                    }
                                } else {
                                    $data[$k][$key] = !empty($v[$key]) ? $define_data_data[$v[$key]] : '';
                                }
                        }

                    }
                }
            }

        }
        return $data;
    }


    /**
     * 导出
     */
    public function export($data, $title){
        $input_field = implode(',', array_keys($this->fields));
        $header_arr = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );
        $input_field = explode(',', $input_field);
        //处理数据
        if (!empty($data['data'])) {
            $list = $this->handleData($data['data'], $input_field);
        }
        $count = count($input_field);
        // 实例化excel
        $phpExcel = new \PHPExcel();
        $phpExcel->getProperties()->setTitle($title);
        $phpExcel->getProperties()->setSubject($title);
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $this->fields[$input_field[$i]]);
        }

        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {
                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle($title);
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file = date('Y年m月d日His-' . $title, time()) . '.xlsx';
        $objWriter->save($file);
        header("Content-type:application/octet-stream");
        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }
}