<?php

namespace addon\goodscoupon\model;

use app\model\BaseModel;

/**
 * 商品用券数据导出
 */
class GoodsUseCouponExport extends BaseModel
{
    public $fields = [
        'goods_id' => '商品ID',
        'goods_name' => '商品名字',
        'num' => '优惠下单件数',
        'goodscoupon_money' => '优惠总金额',
        'total_amount_paid' => '实付总金额',
        'cost_money' => '商品总成本',
        'total_profit' => '总利润',
    ];

    public function export($data, $title){
        $input_field = implode(',', array_keys($this->fields));
        $header_arr = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );
        $input_field = explode(',', $input_field);
        $list = $data['data'];
        $count = count($input_field);
        // 实例化excel
        $phpExcel = new \PHPExcel();
        $phpExcel->getProperties()->setTitle($title);
        $phpExcel->getProperties()->setSubject($title);
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);
        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $this->fields[$input_field[$i]]);
        }

        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {
                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle($title);
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file = date('Y年m月d日His-' . $title, time()) . '.xlsx';
        $objWriter->save($file);
        header("Content-type:application/octet-stream");
        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;
    }
}