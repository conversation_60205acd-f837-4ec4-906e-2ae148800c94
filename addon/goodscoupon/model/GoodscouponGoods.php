<?php

namespace addon\goodscoupon\model;

use app\model\BaseModel;
use app\service\shop\ShopGoodsService;
use think\facade\Db;

class GoodscouponGoods extends BaseModel
{
    public function goodscouponGoodsPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'pgg.id desc', $field = '', ...$otherParams)
    {
        if (empty($field)) {
            $field = 'g.price, g.market_price, g.cost_price,g.stock, g.reward_shop_rate, g.sale_num, g.collect_num, g.goods_image,
	        g.goods_class, g.goods_id, g.goods_attr_class, g.goods_attr_name, g.goods_name, g.site_id, g.site_name,
	        pgy.end_time, pgy.goodscoupon_type_name,
	        pgg.id, pgg.goodscoupon_type_id, pgg.status';
        }


        $alias = 'pgg';
        $join = [
            [ 'goods g', 'pgg.goods_id = g.goods_id', 'inner' ],
            [ 'promotion_goodscoupon_type pgy', 'pgy.goodscoupon_type_id = pgg.goodscoupon_type_id', 'inner' ],
        ];
        $list = model('promotion_goodscoupon_goods')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
        $selectedGoods = Db::table('xm_promotion_goodscoupon_goods')->alias('pgg')
            ->leftJoin('promotion_goodscoupon_type pgy', 'pgy.goodscoupon_type_id = pgg.goodscoupon_type_id')
            ->where('pgy.over_time', '>', time())
            ->where('pgg.status', 1)
            ->where('pgg.goodscoupon_type_id', $otherParams[0])
            ->column('pgg.goods_id');
        $list['selected_goods'] = $selectedGoods;
        return $this->success($list);
    }


    /**
     * @param array $data
     * @return array
     */
    public function addGoods(array $data)
    {
        return $this->success(model("promotion_goodscoupon_goods")->addList($data));
    }


    /**
     * @param array $data
     * @return array
     */
    public function udpateGoods(array $data, array $where)
    {
        return $this->success(model("promotion_goodscoupon_goods")->update($data, $where));
    }


    /**
     * 删除平台商品优惠券关联的商品
     * @param unknown $discount_id
     * @param unknown $sku_id
     * @param unknown $site_id
     */
    public function deleteGoodsCouponGoods($goodsCoupinTypeId, $goodsId)
    {
        $discount_info = model('promotion_goodscoupon_type')->getInfo([ [ 'goodscoupon_type_id', '=', $goodsCoupinTypeId ] ], 'status');
        if (!empty($discount_info)) {
            $res = model('promotion_goodscoupon_goods')->update(['status' => -1], [ [ 'goodscoupon_type_id', '=', $goodsCoupinTypeId ], [ 'goods_id', '=', $goodsId ] ]);
            return $this->success($res);
        } else {
            return $this->error('', '优惠券不存在');
        }
    }


    public function getGoodsCouponGoodsList(int $memberId, int $goodsCouponTypeId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'gg.id desc')
    {
        
        $fields = 'gp.goodscoupon_name, gg.id as goodscoupon_goods_id, gg.goodscoupon_type_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate, g.goods_stock';

        $condition = [];
        $condition[] = ['gp.use_scenario', '=', 3];
        $condition[] = ['gg.status', '=', 1];
        $condition[] = ['gp.goodscoupon_type_id', '=', $goodsCouponTypeId];
        $alias = 'gp';
        $join = [
            [ 'promotion_goodscoupon_goods gg', 'gp.goodscoupon_type_id = gg.goodscoupon_type_id', 'inner' ],
            [ 'goods g', 'gg.goods_id = g.goods_id', 'inner' ],
        ];

        $list = model('promotion_goodscoupon_type')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);

        return $this->success($list);
    }


    public function getGoodsCouponCategoryGoodsList(int $memberId, int $goodsCouponTypeId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc')
    {
        $goodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodsCouponTypeId]);
        $fields = 'gp.goodscoupon_name, gp.goodscoupon_type_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        $condition[] = ['gp.use_scenario', '=', 2];
        $condition[] = ['gp.goodscoupon_type_id', '=', $goodsCouponTypeId];

        $alias = 'gp';
        $join = [
            [ 'goods g', 'find_in_set(g.category_id_1, gp.category_ids) or find_in_set(g.category_id_2, gp.category_ids) or find_in_set(g.category_id_3, gp.category_ids)', 'inner' ],
        ];

        $list = model('promotion_goodscoupon_type')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);
        return $this->success($list);
    }



    public function getGoodsCouponExcludeGoodsList(int $memberId, int $goodsCouponTypeId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc')
    {
        $fields = 'gg.id as goodscoupon_goods_id, g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        // $condition[] = ['gp.use_scenario', '=', 4];
        $condition[] = ['', 'exp', Db::raw(' gg.id is null')];
        // $condition[] = ['gp.goodscoupon_type_id', '=', $goodsCouponTypeId];
        $alias = 'g';
        $join = [
            [ 'promotion_goodscoupon_goods gg', 'gg.goods_id = g.goods_id and gg.status = 1 and goodscoupon_type_id = ' . $goodsCouponTypeId, 'left' ],
        ];

        $list = model('goods')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join);

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);

        return $this->success($list);
    }


    public function getTopicGoodsList(int $memberId, int $goodsCouponTypeId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id desc')
    {
        $goodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodsCouponTypeId]);
        $topicArr = explode(',', $goodsCouponTypeInfo['category_ids']);
        $fields = 'g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [];
        $condition[] = ['ptg.status', '=', 1];
        $condition[] = ['ptg.topic_id', 'in', $topicArr];

        $alias = 'ptg';
        $join = [
            [ 'goods_sku gs', 'gs.sku_id = ptg.sku_id','left'],
            [ 'goods g', 'g.goods_id = gs.goods_id', 'left' ]
        ];

        $list = model('promotion_topic_goods')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join, 'g.goods_id');

        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);
        return $this->success($list);
    }

    /**
     * 获取所有商品
     * @param int $memberId
     * @param int $goodsCouponTypeId
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @return array
     */
    public function getALlGoodsList(int $memberId, int $goodsCouponTypeId, int $page = 1, int $page_size = PAGE_LIST_ROWS, string $order = 'g.goods_id asc'): array
    {
        $fields = 'g.goods_id, g.goods_name, g.sale_num, g.sku_id, 
        g.price, g.goods_image, g.market_price, g.price as discount_price, g.sale_num + g.virtual_sale_num as new_sale_num, g.reward_shop_rate';

        $condition = [
            ['g.goods_state', '=', 1],
            ['g.is_delete', '=', 0]
        ];

        $alias = 'g';
        $join = [];

        $list = model('goods')->pageList($condition, $fields, $order, $page, $page_size, $alias, $join, 'g.goods_id');
        $list['list'] = (new ShopGoodsService())->dealData($list['list'], [], $memberId);
        foreach ($list['list'] as $key => $value) {
            $list['list'][$key]['goods_image'] = explode(",", $value['goods_image'])[0];
        }

        return $this->success($list);
    }
}