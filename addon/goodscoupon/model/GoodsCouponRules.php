<?php
/**
 * Created By luoshiqiang
 * User: luoshiqiang
 * Date: 2021/12/20
 * Time: 4:59 下午
 */

namespace addon\goodscoupon\model;


use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\goodscoupon\constant\GoodsCouponRuleType;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\service\OperateGroupRelationService;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlWeappNoticeRepository;
use app\Domain\Services\WeappNotice\SendGoodscouponNoticeService;
use app\job\ProcessSendGoodsCouponByTagRule;
use app\model\BaseModel;
use app\service\operateGroup\OperateService;
use think\facade\Log;
use think\facade\Db;
use think\facade\Queue;

class GoodsCouponRules extends BaseModel
{
    public $goodsCouponStatusArr = [
        '1' => '未开始',
        '2' => '进行中',
        '3' => '已停止'
    ];

    public function getPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*', $alias = '', $join = [])
    {
        $list = model('promotion_goods_coupon_rules')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
        return $this->success($list);
    }


    public function addGoodsCouponRule(array $data, int $operateGroupId = 0, array $tagIds=[]): array
    {
        $res = model("promotion_goods_coupon_rules")->add($data);

        if($data['type'] == GoodsCouponRuleType::ADD_TAG_TO_MEMBER && !empty($tagIds))
        {
            foreach($tagIds as $tagId)
                GoodsCouponRulesTagRelationModel::create(["rule_id"=>$res,"tag_id"=>$tagId,"created_at"=>time(),"updated_at"=>time()]);
        }

        if ($operateGroupId && $res) {
            $operateGroupRelationService = new OperateGroupRelationService();
            $operateGroupRelationService->save($operateGroupId, OPERATE_GROUP_RELATION_TYPE::GOODS_COUPON_RULE, $res);
        }

        if($data['type'] == GoodsCouponRuleType::ADD_TAG_TO_MEMBER && !empty($tagIds))
            Queue::push(ProcessSendGoodsCouponByTagRule::class);
        return $this->success($res);
    }


    /**
     * 获取自动派券规则详情
     * @param int $ruleId
     * @return multitype:string mixed
     */
    public function getGoodsCouponRuleInfo(int $ruleId = 0)
    {
        return model('promotion_goods_coupon_rules')->getInfo([ [ 'rule_id', '=', $ruleId ] ]);
    }

    /**
     * 停止规则
     * @param $ruleId
     * @return array|\multitype
     */
    public function stopRule(int $ruleId = 0): array
    {
        $nowDateTime = date('Y-m-d H:i:s', time());
        $res = model('promotion_goods_coupon_rules')->update(['status' => 3, 'stop_time' => $nowDateTime], [ [ 'rule_id', '=', $ruleId ] ]);
        return $this->success($res);
    }


    /**
     * Notes: 根据规则自动派发优惠券
     * User: luoshiqiang
     * Date: 2021/12/24
     * Time: 1:54 下午
     * @param int $operateGroupId
     * @param int $memberId
     * @param int $type
     * @param string $outTradeNo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendRuleGoodsCoupon(int $operateGroupId = 0, int $memberId = 0, int $type = 1, string $outTradeNo = '')
    {
        //        if (!$operateGroupId) return;

        $condition = [];

        if ($operateGroupId) {
            $condition[] = ['ogr.operate_group_id', '=', $operateGroupId];
        } else {
            $condition[] = ['', 'exp', Db::raw(' ogr.operate_group_relation_id is null ')];
        }

        $condition[] = ['pgcr.status', '=', 2];
        $condition[] = ['pgcr.type', '=', $type];

        if ($type == GoodsCouponRuleType::FIRST_BUY) {
            $orderMoney = model('order')->getSum(['out_trade_no' => $outTradeNo], 'order_money');
            $condition[] = ['pgcr.money', '<=', $orderMoney];
        }

        $alias = 'pgcr';
        $join = [
            ['operate_group_relation ogr', "ogr.relation_id = pgcr.rule_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::GOODS_COUPON_RULE . "'", 'left']
        ];

        $ruleList = model('promotion_goods_coupon_rules')->getList($condition, 'pgcr.*', '', $alias, $join);
//        dd($list);
//        $operateGroupRelationService = new OperateGroupRelationService();
//        $operateGroupRelations = $operateGroupRelationService->selectByOperateGroupId($operateGroupId, OPERATE_GROUP_RELATION_TYPE::GOODS_COUPON_RULE)->toArray();
//        if (empty($operateGroupRelations)) return;
//
//        $condition = [];
//        $ruleIdsArray = array_column($operateGroupRelations, 'relation_id');
//        $condition[] = ['rule_id', 'in', $ruleIdsArray];
//        $condition[] = ['status', '=', 2];
//        $condition[] = ['type', '=', $type];

//        if ($type == GoodsCouponRuleType::REGISTER) {
//            $ruleList = model('promotion_goods_coupon_rules')->getList($condition);
//        } else if ($type == GoodsCouponRuleType::FIRST_BUY) {
//            $payInfo = model('pay')->getInfo(['out_trade_no' => $outTradeNo]);
//            $condition[] = ['money', '<=', $payInfo['pay_money']];
//            $ruleList = model('promotion_goods_coupon_rules')->getList($condition);
//        }

        if (empty($ruleList)) return;

        $goodsCouponModel = new Goodscoupon();
        foreach ($ruleList as $k => $rule) {
            $goodsCouponTypeConfigList = json_decode($rule['relation_config'], true);
            foreach ($goodsCouponTypeConfigList as $m => $goodsCouponType) {
                for ($i = 1; $i <= $goodsCouponType['single_count']; $i++) {
                    $sendRes = $goodsCouponModel->receiveGoodscoupon(
                        $goodsCouponType['goodscoupon_type_id'],
                        $memberId,
                        5,
                        0,
                        1,
                        $rule['rule_id']
                    );

                    if (isset($sendRes['code']) && $sendRes['code'] == 0) {
                        model('promotion_goods_coupon_rules')->setInc([ ['rule_id', '=', $rule['rule_id']] ], 'send_count');
                        // 发送通知
                        $service = new SendGoodscouponNoticeService(new MysqlWeappNoticeRepository());
                        $service->execute($memberId, $goodsCouponType['goodscoupon_type_id']);
                    }
                }
//                $memberReceivedCount = model('promotion_goodscoupon')->getCount(['member_id' => $memberId, 'goodscoupon_type_id' => $goodsCouponType['goodscoupon_type_id']]);
//                if ($memberReceivedCount >= $goodsCouponType['single_count']) continue;
            }
        }
    }
}