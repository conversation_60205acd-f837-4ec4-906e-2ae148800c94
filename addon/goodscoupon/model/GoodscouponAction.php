<?php


namespace addon\goodscoupon\model;


use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\goodscoupon\constant\GOODSCOUPON_STATUS;
use app\model\goods\GoodsCategoryModel;
use app\model\goods\GoodsModel;
use app\model\goods\GoodsSkuModel;
use app\model\goods\TopicGoodsModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyGoodsPriceInfoObject;
use app\model\object\BuyInfoObject;
use app\model\order\OrderCreateNew;
use app\model\order\OrderCreateNew as OrderCreateModel;
use app\service\Activity\SeckillService;
use app\service\shop\ShopGoodsService;

class GoodscouponAction
{
    public $platformcoupon = null;

    /**
     * @var BuyGoodsObject[]
     */
    public $useBuyGoodsObject = [];
    /**
     * @var GoodscouponModel
     */
    public $gCouponModel;

    public function __construct(GoodscouponModel $gCouponModel)
    {
        $this->gCouponModel = $gCouponModel;
    }

    public function checkStatus()
    {
        if($this->gCouponModel->state != GOODSCOUPON_STATUS::NO_USE)
        {
            return false;
        }
        return true;
    }

    public function checkUse()
    {
        if($this->gCouponModel->state == GOODSCOUPON_STATUS::USED)
            return true;

        return false;
    }

    public function checkTimeOut()
    {
        if($this->gCouponModel->state == GOODSCOUPON_STATUS::TIME_OUT)
            return true;
        if($this->gCouponModel->end_time < time())
            return true;
        return false;
    }

    /**
     * 获取符合优惠券使用场景的所有购物商品对象
     * @param BuyInfoObject $buyInfoObject
     * @return BuyGoodsObject[]
     */
    public function getUseBuyGoodsObject(BuyInfoObject $buyInfoObject)
    {
        if(empty($this->useBuyGoodsObject))
        {
            $useBuyGoodsObject = [];
            foreach($buyInfoObject->buyGoodsObject as $buyGoodsObject)
            {
                if($this->checkScenario($buyGoodsObject->skuId) && $this->checkGoodsType($buyGoodsObject->skuId))
                    $useBuyGoodsObject[] = $buyGoodsObject;
            }
            $this->useBuyGoodsObject = $useBuyGoodsObject;
        }
        return $this->useBuyGoodsObject;
    }



    /**
     * @param $skuId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkGoodsType($skuId)
    {
        $goodsId = GoodsSkuModel::find($skuId)->goods_id;
        $maidou_tag = ShopGoodsService::getInstance()->getIsTagGoods('maidou',$goodsId);

        $seckill = SeckillService::getInstance()->getShopSeckillByGoodsId(0,[$goodsId]);
        $seckill_goods = array_column($seckill,'goods_id');
        $is_seckill   = in_array($goodsId,$seckill_goods)? 1: 0; // 是否秒杀商品

        if($maidou_tag || $is_seckill)
            return false;
        return  true;
    }

    public function checkScenario(int $skuId)
    {
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::ALL)
            return true;
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::CATEGORY)
        {
            $couponUseCategory = explode(",",$this->gCouponModel->category_ids);

            $skuModel = GoodsSkuModel::find($skuId);

            $skuCategoryId = strval($skuModel->category_id);
            $category_id_1 = strval($skuModel->category_id_1);
            $category_id_2 = strval($skuModel->category_id_2);
            $category_id_3 = strval($skuModel->category_id_3);
            if(in_array($skuCategoryId,$couponUseCategory) ||
                in_array($category_id_1,$couponUseCategory) ||
                in_array($category_id_2,$couponUseCategory) ||
                in_array($category_id_3,$couponUseCategory)
            ){
                return  true;
            }
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;
            $couponUseGoodsIds = $this->gCouponModel->belongGoodscouponType->useGoods->column("goods_id");
            $couponUseSkuIds = $this->gCouponModel->belongGoodscouponType->useGoods->column("sku_id");
            if(in_array($goodsModel->goods_id, $couponUseGoodsIds) || in_array($skuId, $couponUseSkuIds))
            {
                return true;
            }
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;

            $dGoods = $this->gCouponModel->belongGoodscouponType->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", 0);
            $dSku = $this->gCouponModel->belongGoodscouponType->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", $skuId);
            if($dGoods->count() > 0 || $dSku->count() > 0)
            {
                return false;
            }
            return true;
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::TOPIC)
        {
            $topicIds = explode(",",$this->gCouponModel->category_ids);
            $activityGoods = TopicGoodsModel::whereIn("topic_id", $topicIds)->where("sku_id", $skuId)->where("status", 1)->find();
            if($activityGoods)
                return true;
        }
        return false;
    }



    /**
     *
     * @param BuyGoodsObject[] $buyGoods
     * @return bool
     */
    public function checkAchieveLeast(array $buyGoods)
    {
        $price = (new BuyInfoObject($buyGoods))->calculateUseCouponTargetMoney();
        if($price >= $this->gCouponModel->at_least)
            return true;
        return false;
    }

    public function getUseGoodsList($limit=30)
    {
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::ALL)
        {
            return GoodsModel::where("goods_state", 1)->where("is_delete", 0)->select();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::GOODS)
        {
            $couponUseGoodsIds = $this->gCouponModel->belongGoodscouponType->useGoods->column("goods_id");
            return GoodsModel::whereIn("goods_id", $couponUseGoodsIds)->where("goods_state", 1)->where("is_delete", 0)->limit($limit)->select();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS)
        {
            $couponUseGoodsIds = $this->gCouponModel->belongGoodscouponType->useGoods->column("goods_id");
            return GoodsModel::whereNotIn("goods_id", $couponUseGoodsIds)->where("goods_state", 1)->where("is_delete", 0)->limit($limit)->select();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::CATEGORY)
        {
            $couponUseCategory = explode(",",$this->gCouponModel->category_ids);
            return GoodsModel::whereIn("category_id_1|category_id_2|category_id_3", $couponUseCategory)->where("goods_state", 1)->where("is_delete", 0)->limit($limit)->select();
        }
        return [];
    }
}