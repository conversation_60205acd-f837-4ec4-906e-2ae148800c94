<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\goodscoupon\model;

use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\model\OperateGroupRelation;
use addon\operateGroup\service\OperateGroupRelationService;
use app\model\BaseModel;
use app\model\system\Cron;

/**
 * 优惠券活动
 */
class GoodscouponType extends BaseModel
{
	//优惠券类型状态
	private $goodscoupon_type_status = [
		1 => '进行中',
		2 => '已结束',
        3 => '未开始',
		-1 => '已关闭',
	];
	
	public function getGoodscouponTypeStatus()
	{
		return $this->goodscoupon_type_status;
	}

    /**
     * Notes:
     * User: luoshiqiang
     * Date: 2021/12/16
     * Time: 5:20 下午
     * @param $data
     * @param int $operateGroupId
     * @return array
     */
	public function addGoodscouponType($data, int $operateGroupId = 0, array $tagIds = []): array
	{
		//只要创建了就是进行中
		$data['status'] = 1;
		$res = model("promotion_goodscoupon_type")->add($data);

        if(isset($data['relation_tag_status']) && !empty($tagIds))
        {
            foreach($tagIds as $tagId)
                GoodsCouponTypeTagRelationModel::create(["goodscoupon_type_id"=>GoodscouponTypeModel::find($res)->goodscoupon_type_id,"tag_id"=>$tagId,"created_at"=>time(),"updated_at"=>time()]);
        }

        if ($operateGroupId && $res) {
            $operateGroupRelationService = new OperateGroupRelationService();
            $operateGroupRelationService->save($operateGroupId, OPERATE_GROUP_RELATION_TYPE::GOODSCOUPON, $res);
        }

		$this->qrcode($res, 'all', 'create');
		return $this->success($res);
	}
	
	/**
	 * 编辑优惠券活动
	 * @param unknown $data
	 * @param unknown $goodscoupon_type_id
	 * @return multitype:string
	 */
	public function editGoodscouponType($data, $goodscoupon_type_id)
	{
		$res = model("promotion_goodscoupon_type")->update($data, [ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		return $this->success($res);
	}
	
	/**
	 * 关闭优惠券
	 * @param $goodscoupon_type_id
	 * @return array|\multitype
	 */
	public function closeGoodscouponType($goodscoupon_type_id)
	{
		$res = model('promotion_goodscoupon_type')->update([ 'status' => -1 ], [ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		return $this->success($res);
	}

    /**
     * 关闭优惠券
     * @param $goodscoupon_type_id
     * @return array|\multitype
     */
    public function shutDownGoodscouponType($goodscoupon_type_id)
    {
        $nowTime = time();
        $res = model('promotion_goodscoupon_type')->update(['status' => 2, 'end_time' => $nowTime, 'over_time' => $nowTime], [ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
        model('promotion_goodscoupon')->update(['state' => 3, 'end_time' => $nowTime, 'over_time' => $nowTime], ['goodscoupon_type_id' => $goodscoupon_type_id, 'state' => 1]);
        return $this->success($res);
    }
	
	/**
	 * 删除优惠券活动
	 * @param unknown $goodscoupon_type_id
	 * @return multitype:string
	 */
	public function deleteGoodscouponType($goodscoupon_type_id)
	{
		$res = model("promotion_goodscoupon_type")->delete([ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		if ($res) {
			model("promotion_goodscoupon")->delete([ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		}
		
		return $this->success($res);
	}
	
	/**
	 * 获取优惠券活动详情
	 * @param int $discount_id
	 * @return multitype:string mixed
	 */
	public function getGoodscouponTypeInfo($goodscoupon_type_id)
	{
		$res = model('promotion_goodscoupon_type')->getInfo([ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		if ($res['use_scenario'] == GOODSCOUPON_SCENARIO::TOPIC) {
		    $topicNameArr = model('promotion_topic')->getColumn([ ['topic_id', 'in', explode(',', $res['category_ids'])] ], 'topic_name');
            $res['topic_name'] = implode(',', $topicNameArr);
        }
		return $this->success($res);
	}

    /**
     * 获取优惠券活动详情
     * @param int $discount_id
     * @return multitype:string mixed
     */
    public function getInfo($condition = [], $field= '*')
    {
        $info = model('promotion_goodscoupon_type')->getInfo($condition, $field);
        return $this->success($info);
    }

	/**
	 * 获取 优惠券类型列表
	 * @param array $condition
	 * @param string $field
	 * @param string $order
	 * @param string $limit
	 */
	public function getGoodscouponTypeList($condition = [], $field = '*', $order = '', $limit = null)
	{
		$res = model('promotion_goodscoupon_type')->getList($condition, $field, $order, '', '', '', $limit);
		return $this->success($res);
	}
	
	/**
	 * 获取优惠券活动分页列表
	 * @param array $condition
	 * @param number $page
	 * @param string $page_size
	 * @param string $order
	 * @param string $field
	 * @param string $alias
	 * @param array $join
	 */
	public function getGoodscouponTypePageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*', $alias = '', $join = [])
	{
		$list = model('promotion_goodscoupon_type')->pageList($condition, $field, $order, $page, $page_size, $alias, $join);
		return $this->success($list);
	}
	
	/**
	 * 生成优惠券二维码
	 * @param $goodscoupon_type_id
	 * @param string $app_type all为全部
	 * @param string $type 类型 create创建 get获取
	 * @return mixed|array
	 */
	public function qrcode($goodscoupon_type_id, $app_type, $type)
	{
		$res = event('Qrcode', [
			'app_type' => $app_type,
			'type' => $type,
			'data' => [
				'goodscoupon_type_id' => $goodscoupon_type_id
			],
			'page' => '/otherpages/goods/goodscoupon_receive/goodscoupon_receive',
			'qrcode_path' => 'upload/qrcode/goodscoupon',
			'qrcode_name' => 'goodscoupon_type_code_' . $goodscoupon_type_id,
		], true);
		return $res;
	}
	
	/**
	 * 优惠券定时结束
	 * @param unknown $goodscoupon_type_id
	 */
	public function goodscouponCronEnd($goodscoupon_type_id){
		$res = model('promotion_goodscoupon_type')->update([ 'status' => 2 ], [ [ 'goodscoupon_type_id', '=', $goodscoupon_type_id ] ]);
		return $this->success($res);
	}


    /**
     * 获取专题活动列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param string $limit
     */
    public function getTopicList($condition = [], $field = '*', $order = '', $limit = null)
    {
        $list = model('promotion_topic')->getList($condition, $field, $order, '', '', '', $limit);
        return $this->success($list);
    }

    /**
     * 获取优惠券小程序地址
     * @param $goodscoupon_type_id
     * @return string
     */
    public function getWxUrl($goodscoupon_type_id)
    {
        return '/otherpages/goods/coupon_goods_list/coupon_goods_list?goodscoupon_type_id='.$goodscoupon_type_id;
    }
}