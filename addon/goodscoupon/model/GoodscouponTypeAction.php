<?php


namespace addon\goodscoupon\model;


use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use app\model\goods\GoodsModel;
use app\model\goods\GoodsSkuModel;
use app\model\goods\TopicGoodsModel;
use app\service\Activity\SeckillService;
use app\service\shop\ShopGoodsService;
use think\facade\Db;

class GoodscouponTypeAction
{
    /**
     * @var GoodscouponModel
     */
    public $gCouponModel;

    public function __construct(GoodscouponTypeModel $gCouponModel)
    {
        $this->gCouponModel = $gCouponModel;
    }

    /**
     * @param $skuId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkGoodsType($skuId)
    {
        $goodsId = GoodsSkuModel::find($skuId)->goods_id;
        $maidou_tag = ShopGoodsService::getInstance()->getIsTagGoods('maidou',$goodsId);

        $seckill = SeckillService::getInstance()->getShopSeckillByGoodsId(0,[$goodsId]);
        $seckill_goods = array_column($seckill,'goods_id');
        $is_seckill   = in_array($goodsId,$seckill_goods)? 1: 0; // 是否秒杀商品

        if($maidou_tag || $is_seckill)
            return false;
        return  true;
    }

    public function checkScenario(int $skuId)
    {
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::ALL)
            return true;
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::CATEGORY)
        {
            $couponUseCategory = explode(",",$this->gCouponModel->category_ids);

            $skuModel = GoodsSkuModel::find($skuId);
            $skuCategoryId = strval($skuModel->category_id);
            $category_id_1 = strval($skuModel->category_id_1);
            $category_id_2 = strval($skuModel->category_id_2);
            $category_id_3 = strval($skuModel->category_id_3);
            if(in_array($skuCategoryId,$couponUseCategory) ||
                in_array($category_id_1,$couponUseCategory) ||
                in_array($category_id_2,$couponUseCategory) ||
                in_array($category_id_3,$couponUseCategory)
            )
                return  true;
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;
            
            $dGoods = $this->gCouponModel->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", 0);
            $dSku = $this->gCouponModel->useGoods->where("goods_id", $goodsModel->goods_id)->where("sku_id", $skuId);
            if($dGoods->count() > 0 || $dSku->count() > 0)
            {
                return true;
            }
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS)
        {
            $skuModel = GoodsSkuModel::find($skuId);
            $goodsModel = $skuModel->belongGoods;
            $couponUseGoodsIds = $this->gCouponModel->useGoods->column("goods_id");
            $couponUseSkuIds = $this->gCouponModel->useGoods->column("sku_id");
            if(in_array($goodsModel->goods_id, $couponUseGoodsIds) || in_array($skuId, $couponUseSkuIds))
            {
                return false;
            }
            return true;
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::TOPIC)
        {
            $topicIds = explode(",",$this->gCouponModel->category_ids);
            $activityGoods = TopicGoodsModel::whereIn("topic_id", $topicIds)->where("sku_id", $skuId)->where("status", 1)->find();
            if($activityGoods)
                return true;
        }
        return false;
    }

    public function getUseGoodsList($field = "goods_id,goods_name,goods_image,sku_id,market_price,price,introduction", $limit=30)
    {

        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::ALL)
        {
            return GoodsModel::where("goods_state", 1)->where("is_delete", 0)->field($field)->limit($limit)->select();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::GOODS)
        {
            return Db::name("promotion_goodscoupon_goods")
                ->alias("pgg")
                ->join("xm_goods g", "pgg.goods_id=g.goods_id")
                ->where("pgg.goodscoupon_type_id", $this->gCouponModel->goodscoupon_type_id)
                ->where("pgg.status", 1)
                ->where("g.goods_state", 1)
                ->order("pgg.sort", "desc")
                ->limit($limit)
                ->field("g.goods_id,g.goods_name,g.goods_image,g.sku_id,g.market_price,g.price,g.introduction")
                ->select()->toArray();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS)
        {
            $couponUseGoodsIds = $this->gCouponModel->useGoods->column("goods_id");
            return GoodsModel::whereNotIn("goods_id", $couponUseGoodsIds)->where("goods_state", 1)->where("is_delete", 0)->field($field)->order("goods_id", "desc")->limit($limit)->select();
        }
        if ($this->gCouponModel->use_scenario == GOODSCOUPON_SCENARIO::CATEGORY)
        {
            $couponUseCategory = explode(",",$this->gCouponModel->category_ids);
            return GoodsModel::whereIn("category_id_1|category_id_2|category_id_3", $couponUseCategory)->where("goods_state", 1)->field($field)->where("is_delete", 0)->limit($limit)->select();
        }
        return [];
    }
}