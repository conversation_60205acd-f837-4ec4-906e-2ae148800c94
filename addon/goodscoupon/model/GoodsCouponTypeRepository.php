<?php


namespace addon\goodscoupon\model;


use addon\goodscoupon\constant\GOODSCOUPON_STATUS;
use addon\goodscoupon\model\Goodscoupon as Goodscoupon;

class GoodsCouponTypeRepository
{
    /**
     * 获取所有公开进行中的优惠券活动
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAllPrivacyStart()
    {
        return GoodscouponTypeModel::where("status", 1)
            ->where("over_time", ">", time())
            ->where("privacy_status", 1)
            ->order("create_time", "desc")
            ->select();
    }

    /**
     * 根据skuid获取所有参与的满减活动
     * @param $skuId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getParticipateCouponBySkuId($skuId)
    {
        $allCoupons = $this->getAllPrivacyStart();
        $couponsTypeIds = [];

        foreach($allCoupons as $couponsModel)
        {
            $couponAction = new GoodscouponTypeAction($couponsModel);
            if($couponAction->checkScenario($skuId) && $couponAction->checkGoodsType($skuId) && ($couponsModel->count == 0 || $this->getReceiveNums($couponsModel->goodscoupon_type_id) < $couponsModel->count))
                $couponsTypeIds[] = $couponsModel->goodscoupon_type_id;
        }
        return $allCoupons->whereIn("goodscoupon_type_id", $couponsTypeIds);
    }

    /**
     * 根据skuid获取该用户所有参与的满减活动
     * @param $memberId
     * @param $skuId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getParticipateCouponByMemberSkuId($memberId, $skuId)
    {
        $allCoupons = $this->getAllPrivacyStart();
        $couponsTypeIds = [];

        foreach($allCoupons as $couponsModel)
        {
            $couponAction = new GoodscouponTypeAction($couponsModel);
            if($couponAction->checkScenario($skuId) && $couponAction->checkGoodsType($skuId) && ($couponsModel->count == 0 || $this->getReceiveNums($couponsModel->goodscoupon_type_id) < $couponsModel->count || $this->getMemberNoUseNums($couponsModel->goodscoupon_type_id, $memberId) > 0))
                $couponsTypeIds[] = $couponsModel->goodscoupon_type_id;
        }
        return $allCoupons->whereIn("goodscoupon_type_id", $couponsTypeIds);
    }

    public function getReceiveNums($goodscoupon_type_id)
    {
        $data['goodscoupon_type_id'] = $goodscoupon_type_id;
        return (new Goodscoupon())->getGoodsCouponStatistics($data, 'sent');
    }

    public function getMemberNoUseNums($goodscouponTypeId, $memberId)
    {
        return GoodscouponModel::where("goodscoupon_type_id", $goodscouponTypeId)->where("member_id", $memberId)->where("state", GOODSCOUPON_STATUS::NO_USE)->count();
    }
}