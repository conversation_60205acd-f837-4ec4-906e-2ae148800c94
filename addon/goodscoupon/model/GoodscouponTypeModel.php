<?php


namespace addon\goodscoupon\model;

use think\Model;
class GoodscouponTypeModel extends Model
{
    protected $table = 'xm_promotion_goodscoupon_type';
    protected $pk = 'goodscoupon_type_id';

    public function useGoods()
    {
        $ret = $this->hasMany(GoodscouponGoodsModel::class, "goodscoupon_type_id", "goodscoupon_type_id");
        $ret->where("status", 1);
        return $ret;
    }

    public function getMoneyAttr($value)
    {
        return moneyRemoveZero($value);
    }

    public function getAtLeastAttr($value)
    {
        return moneyRemoveZero($value);
    }

    public function tagRelation()
    {
        return $this->hasMany(GoodsCouponTypeTagRelationModel::class, "goodscoupon_type_id", "goodscoupon_type_id");
    }
}