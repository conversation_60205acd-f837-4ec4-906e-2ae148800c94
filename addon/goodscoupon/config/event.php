<?php
// 事件定义文件
return [
    'bind'      => [ 
    ],

    'listen'    => [

        //展示活动
        'ShowPromotion' => [
            'addon\goodscoupon\event\ShowPromotion',
        ],
        //优惠券自动关闭
        'CronGoodscouponEnd' => [
            'addon\goodscoupon\event\CronGoodscouponEnd',
        ],
    	// 优惠券活动定时结束
    	'CronGoodscouponTypeEnd' => [
    		'addon\goodscoupon\event\CronGoodscouponTypeEnd',
    	],
        'OrderClose' => [
            'addon\goodscoupon\event\OrderClose',
        ]
    ],

    'subscribe' => [
    ],
];
