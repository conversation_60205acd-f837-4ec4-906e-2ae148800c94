<?php


namespace addon\goodscoupon\domainService;


use addon\goodscoupon\model\GoodscouponAction;
use addon\goodscoupon\model\GoodscouponModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyGoodsPriceInfoObject;
use app\model\object\BuyInfoObject;
use app\model\order\OrderModel;

class DistributionGoodsCouponMoneyService
{
    public static function execute($out_trade_no)
    {
        $orders = OrderModel::where("out_trade_no", $out_trade_no)->where("goodscoupon_id", ">", 0)->select();

        if($orders->count() == 0)
            return;

        //总支付金额
        $allOrderPayMoney = 0;
        $buyGoodsObjects = [];
        foreach($orders as $order)
        {
            if($order->pay_money > 0)
                $allOrderPayMoney += $order->pay_money;
            elseif($order->balance_money > 0)
                $allOrderPayMoney += $order->balance_money;
        }
        //使用的优惠券金额
        $goodscouponMoney = $orders[0]->goodscoupon_money;
        $goodscoupon = new GoodscouponAction(GoodscouponModel::find($orders[0]->goodscoupon_id));

        foreach($orders as $order)
        {
            $order->goodscoupon_money = 0;
            foreach($order->goods as $orderGoods)
            {
                $orderGoods->goodscoupon_money = GetBuyGoodsDistributionCouponMoneyService::execute($goodscoupon, BuyInfoObject::createByOutTradeNo($out_trade_no), $goodscouponMoney, $orderGoods->sku_id);
                $order->goodscoupon_money = $order->goodscoupon_money + $orderGoods->goodscoupon_money;
                $orderGoods->save();
            }
            $order->order_money -= $order->goodscoupon_money;
            if($order->pay_money > 0)
                $order->pay_money -= $order->goodscoupon_money;
            elseif($order->balance_money > 0)
                $order->balance_money -= $order->goodscoupon_money;
            $order->save();
        }
    }
}