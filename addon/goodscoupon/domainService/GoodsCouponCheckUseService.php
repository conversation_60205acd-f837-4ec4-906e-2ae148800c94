<?php


namespace addon\goodscoupon\domainService;


use addon\goodscoupon\model\GoodscouponAction;
use addon\goodscoupon\model\GoodscouponModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyGoodsPriceInfoObject;
use app\model\object\BuyInfoObject;

/**
 * 优惠券可用性检测服务
 * Class GoodsCouponCheckUseService
 * @package addon\goodscoupon\domainService
 */
class GoodsCouponCheckUseService
{
    protected static $disabledReason = [];


    public static function executeByCalculateData(array $calculateData)
    {
        $goodscouponAction = new GoodscouponAction(GoodscouponModel::find($calculateData['goodscoupon_id']));
        if(isset($calculateData['cart_ids']) && !empty($calculateData['cart_ids']))
            $buyInfoObject = BuyInfoObject::createByCartIdsStr($calculateData['cart_ids']);
        else
            $buyInfoObject = BuyInfoObject::createBySingleSku($calculateData['sku_id'], $calculateData['num']);
        foreach($buyInfoObject->buyGoodsObject as $buyGoodsObject)
        {
            $buyGoodsObject->setPriceInfoObjectByCalculateData($calculateData);
        }
        return self::check($goodscouponAction, $buyInfoObject);
    }

    /**
     * 优惠券可用性检测
     * @param GoodscouponAction $goodscouponAction 使用的优惠券对象
     * @param BuyInfoObject $useCouponBuyGoods 购买商品价格信息对象
     * @return bool 检测结果 true可用  false不可用
     */
    public static function executeByPriceInfoObject(GoodscouponAction $goodscouponAction, BuyInfoObject $buyInfoObject)
    {
        return self::check($goodscouponAction, $buyInfoObject);
    }

    /**
     * @param GoodscouponAction $goodscouponAction 使用的优惠券对象
     * @param BuyInfoObject $buyInfoObject 购买商品信息对象
     * @return bool 检测结果 true可用  false不可用
     */
    protected static function check(GoodscouponAction $goodscouponAction, BuyInfoObject $buyInfoObject)
    {
        self::$disabledReason = [];
        if($goodscouponAction->checkUse())
        {
            self::$disabledReason[] =  "优惠券已使用";
            return false;
        }

        if($goodscouponAction->checkTimeOut())
        {
            self::$disabledReason[] = "优惠券已过期";
            return false;
        }

        if(!$goodscouponAction->checkStatus())
        {
            self::$disabledReason[] = "优惠券状态异常";
            return false;
        }

        $useBuyGoodsObject = $goodscouponAction->getUseBuyGoodsObject($buyInfoObject);
        if(empty($useBuyGoodsObject))
        {
            self::$disabledReason[] = "无可使用优惠券的商品";
            return false;
        }

        if(!$goodscouponAction->checkAchieveLeast($useBuyGoodsObject))
        {
            self::$disabledReason[] = "不满足使用条件";
            return false;
        }

        return  true;
    }

    /**
     * 获取不可用原因
     * @return array
     */
    public static function getDisabledReason()
    {
        return self::$disabledReason;
    }
}