<?php


namespace addon\goodscoupon\domainService;


use addon\goodscoupon\model\GoodscouponAction;
use addon\goodscoupon\model\GoodscouponModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyInfoObject;
use think\facade\Log;

class GetBuyGoodsDistributionCouponMoneyService
{
    public static function execute(GoodscouponAction $goodscoupon, BuyInfoObject $buyInfo, $goodsCouponMoney, $skuId)
    {
        $useCouponGoodsObjects = $goodscoupon->getUseBuyGoodsObject($buyInfo);

        if(empty($useCouponGoodsObjects))
            return 0;

        $useCouponBuyInfo = new BuyInfoObject($useCouponGoodsObjects);
        //目标金额
        $targetMoney = $useCouponBuyInfo->calculateUseCouponTargetMoney();
        //剩余优惠金额
        $surplusCouponMoney = $goodsCouponMoney;
        //分配优惠金额
        $spiltCouponMoney = 0;
        foreach($useCouponGoodsObjects as $i=>$goodsObject)
        {
            //判断是否数组的最后一个sku商品
            if($i != count($useCouponGoodsObjects) - 1)
            {
                //不是数组最后一个则按比例算优惠金额
                $skuUseCouponMoney = (new BuyInfoObject([$goodsObject]))->calculateUseCouponTargetMoney();
                $goodscouponMoney = round($goodsCouponMoney * $skuUseCouponMoney / $targetMoney, 2);
                $surplusCouponMoney -= $goodscouponMoney;
            }
            //数组最后一个直接去剩余的优惠金额
            else
                $goodscouponMoney = $surplusCouponMoney;

            if($goodsObject->skuId == $skuId)
            {
                $spiltCouponMoney = $goodscouponMoney;
                break;
            }

        }
        return  round($spiltCouponMoney, 2);
    }

    public function executeByCartBuy(GoodscouponAction $goodscoupon, array $cartIds)
    {
        $useCouponGoodsObject = $goodscoupon->getUseBuyGoodsObject(BuyInfoObject::createByCartIds($cartIds));

    }

    /**
     * @param GoodscouponAction $goodscoupon
     * @param array $calculateData
     * @param $skuId
     * @param $buyNums
     * @return float
     */
    public static function executeByCalculateData(GoodscouponAction $goodscoupon, array $calculateData, $skuId)
    {
        try
        {
            $self = new self();
            if(isset($calculateData['cart_ids']) && !empty($calculateData['cart_ids']))
                $buyInfo = BuyInfoObject::createByCartIdsStr($calculateData['cart_ids']);
            elseif(isset($calculateData['sku_id']) && isset($calculateData['num']))
                $buyInfo = BuyInfoObject::createBySingleSku($calculateData['sku_id'], $calculateData['num']);
            else
                throw new \Exception("提交参数错误");

            foreach($buyInfo->buyGoodsObject as $i=>$goodsObject)
            {
                $goodsObject->setPriceInfoObjectByCalculateData($calculateData);
            }

            return self::execute($goodscoupon, $buyInfo, $calculateData['goodscoupon_money'], $skuId);
        }
        catch(\Throwable $e)
        {
            Log::error("优惠券分配失败");
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return 0;
        }
    }
}