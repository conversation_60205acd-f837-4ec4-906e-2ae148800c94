<?php


namespace addon\goodscoupon\domainService;


use addon\goodscoupon\constant\GoodsCouponRuleStatus;
use addon\goodscoupon\constant\GoodsCouponRuleType;
use addon\goodscoupon\model\GoodscouponModel;
use addon\goodscoupon\model\GoodsCouponRulesModel;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlWeappNoticeRepository;
use app\Domain\Services\WeappNotice\SendGoodscouponNoticeService;
use app\model\enterpriseWx\MemberEnterpriseTagModel;

class SendGoodsCouponByTagRuleService
{
    public static function execute()
    {
        $rules = GoodsCouponRulesModel
            ::where('type', GoodsCouponRuleType::ADD_TAG_TO_MEMBER)
            ->where('status', GoodsCouponRuleStatus::RUNNING)
            ->select();

        foreach($rules as $rule)
        {
            $tagIds = $rule->tagRelation->column("tag_id");
            if($tagIds)
            {
                if($rule->relation_tag_status == 0)
                    $memberIds = self::getMemberIdsOneTag($tagIds);
                else
                    $memberIds = self::getMemberMoreTag($tagIds);

                $config = json_decode($rule['relation_config'], true);
                $sendMemberIds = [];
                foreach($config as $c)
                {
                    $single_count = $c['single_count'] ?? 0;

                    $sendEndMemberIds = GoodscouponModel
                        ::where("goodscoupon_type_id", $c['goodscoupon_type_id'])
                        ->where("get_type_relation_id", $rule['rule_id'])
                        ->whereIn("member_id", $memberIds)
                        ->group('member_id')
                        ->having("count(*)>={$single_count}")
                        ->column("member_id");
                    $sendMemberIds = array_values(array_diff($memberIds, $sendEndMemberIds));
                    foreach ($sendMemberIds as $memberId)
                    {
                        $receiveRes = (new \addon\goodscoupon\model\Goodscoupon())
                            ->receiveGoodscoupon($c['goodscoupon_type_id'], $memberId, 5, 0, 1, $rule->rule_id);
                        // 发送通知
                        if ($receiveRes['code'] == 0) {
                            model('promotion_goods_coupon_rules')->setInc([ ['rule_id', '=', $rule->rule_id] ], 'send_count');
                            $service = new SendGoodscouponNoticeService(new MysqlWeappNoticeRepository());
                            $service->execute($memberId, $c['goodscoupon_type_id']);
                        }
                    }
                }
            }
        }
    }

    public static function getMemberIdsOneTag(array $tag_ids)
    {
        return MemberEnterpriseTagModel::whereIn("tag_id", $tag_ids)->group("member_id")->column("member_id");
    }

    public static function getMemberMoreTag(array $tag_ids)
    {
        $sql = [];
        foreach($tag_ids as $i=>$tag_id)
            $sql[] = "EXISTS (SELECT * FROM xm_member_enterprise_tag where member_id=t.member_id and tag_id={$tag_id})";
        $sqlStr = implode(" AND ", $sql);
        return MemberEnterpriseTagModel::alias("t")->whereRaw($sqlStr)->group("member_id")->column("member_id");
    }
}