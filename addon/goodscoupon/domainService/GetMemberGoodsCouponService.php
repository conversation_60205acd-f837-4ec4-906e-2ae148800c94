<?php


namespace addon\goodscoupon\domainService;


use addon\combo\model\ComboRepository;
use addon\goodscoupon\constant\GOODSCOUPON_STATUS;
use addon\goodscoupon\model\GoodscouponAction;
use addon\goodscoupon\model\GoodscouponModel;
use app\model\goods\GoodsSkuModel;
use app\model\object\BuyGoodsObject;
use app\model\object\BuyGoodsPriceInfoObject;
use app\model\object\BuyInfoObject;
use app\model\object\MemberGoodsCouponCheckObject;
use League\Flysystem\Exception;

/**
 * 获取用户优惠券服务
 * Class GetMemberGoodsCouponService
 * @package addon\goodscoupon\domainService
 */
class GetMemberGoodsCouponService
{
    /**
     * @param $memberId
     * @param BuyInfoObject $buyInfo
     * @return MemberGoodsCouponxCheckObject[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function execute($memberId, BuyInfoObject $buyInfo)
    {
        /**
         * 优惠券检测结果对象
         */
        $checkResult = [];
        $coupons = GoodscouponModel::where("member_id", $memberId)->where("state", GOODSCOUPON_STATUS::NO_USE)->order(['money'=>'desc','end_time'=>'asc'])->select();

        foreach($coupons as $coupon)
        {
            $goodsCouponAction = new GoodscouponAction($coupon);
            //检测结果
            $ret = GoodsCouponCheckUseService::executeByPriceInfoObject($goodsCouponAction, $buyInfo);
            $checkResult[] = new MemberGoodsCouponCheckObject($buyInfo, $goodsCouponAction, $ret, GoodsCouponCheckUseService::getDisabledReason());
        }
        return $checkResult;
    }

    public static function executeByCartBuy($memberId, array $cart_ids)
    {
        $self = new self();
        $buyInfo = BuyInfoObject::createByCartIdsStr($cart_ids);
        return $self->execute($memberId, $buyInfo);
    }

    public static function executeByAloneBuy($memberId, $skuId, $buyNums)
    {
        $self = new self();
        $buyInfo = BuyInfoObject::createBySingleSku($skuId, $buyNums);
        return $self->execute($memberId, $buyInfo);
    }

    /**
     * 根据确认订单页面的数据执行服务
     * @param array $calculateData
     * @return MemberGoodsCouponCheckObject[]
     */
    public static function executeByCalculateData(array $calculateData)
    {

        $self = new self();
        if(isset($calculateData['combo_id']) && !empty($calculateData['combo_id']) && isset($calculateData['combo_sku_ids']))
        {
            $combo = ComboRepository::find($calculateData['combo_id']);
            foreach ($calculateData['combo_sku_ids'] as $sku_id)
            {
                $goodsId = GoodsSkuModel::find($sku_id)->goods_id;
                $combo->findGoods($goodsId)->selectBuySku($sku_id);
            }
            $buyInfo = BuyInfoObject::createByCombo($combo);
        }
        elseif(isset($calculateData['cart_ids']) && !empty($calculateData['cart_ids']))
            $buyInfo = BuyInfoObject::createByCartIdsStr($calculateData['cart_ids']);
        elseif(isset($calculateData['sku_id']) && isset($calculateData['num']))
            $buyInfo = BuyInfoObject::createBySingleSku($calculateData['sku_id'], $calculateData['num']);
        else
            return [];
        foreach($buyInfo->buyGoodsObject as $buyGoodsObject)
        {
            $buyGoodsObject->setPriceInfoObjectByCalculateData($calculateData);
        }
        return $self->execute($calculateData['member_id'], $buyInfo);
    }
}