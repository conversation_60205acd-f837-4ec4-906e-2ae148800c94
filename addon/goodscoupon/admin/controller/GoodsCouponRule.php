<?php
/**
 * Created By luoshiqiang
 * User: luoshiqiang
 * Date: 2021/12/20
 * Time: 2:36 下午
 */

namespace addon\goodscoupon\admin\controller;


use addon\goodscoupon\model\GoodsCouponRules;
use addon\goodscoupon\model\GoodsCouponRulesTagRelationModel;
use addon\goodscoupon\model\GoodscouponType as GoodscouponTypeModel;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\model\OperateGroup;
use app\admin\controller\BaseAdmin;
use addon\goodscoupon\constant\GoodsCouponRuleType;
use app\model\enterpriseWx\EnterpriseTagModel;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

class GoodsCouponRule extends BaseAdmin
{
    protected $goodsCouponRuleModel;

    public function __construct()
    {
        parent::__construct();
        $this->goodsCouponRuleModel = new GoodsCouponRules();
    }


    /**
     * 活动列表
     */
    public function list()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $ruleId = input('rule_id', '');
            $ruleName = input('rule_name', '');
            $status = input('status', '');
            $condition = [];

            # 规则ID
            if ($ruleId !== "") {
                $condition[] = ['pgcr.rule_id', '=', $ruleId];
            }
            # 规则名称
            if ($ruleName !== "") {
                $condition[] = ['pgcr.rule_name', 'like', '%' . $ruleName . '%'];
            }
            # 状态
            if ($status !== "") {
                $condition[] = ['pgcr.status', '=', $status];
            }

            if ($this->operate_group_id) {
                $condition[] = ['ogr.operate_group_id', '=', $this->operate_group_id];
            } else {
                $condition[] = ['', 'exp', Db::raw(' ogr.operate_group_relation_id is null ')];
            }

            $order = 'pgcr.created_at desc';
            $field = '*';

            $alias = 'pgcr';
            $join = [
                ['operate_group_relation ogr', "ogr.relation_id = pgcr.rule_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::GOODS_COUPON_RULE . "'", 'left']
            ];

            $res = $this->goodsCouponRuleModel->getPageList($condition, $page, $page_size, $order, $field, $alias, $join);

            foreach ($res['data']['list'] as $key => $val) {
                $res['data']['list'][$key]['status_name'] = $this->goodsCouponRuleModel->goodsCouponStatusArr[$val['status']];
            }
            return $res;
        } else {
            return $this->fetch("goodscoupon/rule/list");
        }
    }


    public function add()
    {
        if (request()->isAjax()) {
            $token = input('token', '');
            if (!$token) return $this->goodsCouponRuleModel->error('系统出错了');

            $type = input('type', 1);
            $money = input('money', 0);
            $relationIds = '';
            $relationConfig = '';
            $temp = Cache::get($token);
            $selectedGoodsCoupon = json_decode($temp, true);
            if (!empty($selectedGoodsCoupon)) {
                $relationConfig = $temp;
                $idsArr = array_column($selectedGoodsCoupon, 'goodscoupon_type_id');
                $relationIds = implode(',', $idsArr);
            } else {
                return $this->goodsCouponRuleModel->error('', '关联优惠券不能为空');
            }

            $data = [
                'rule_name' => input('rule_name', ''),
                'start_time' => input('start_time', ''),
                'stop_time' => input('stop_time', ''),
                'relation_ids' => $relationIds,
                'relation_config' => $relationConfig,
                'type' => $type,
                'money' => GoodsCouponRuleType::FIRST_BUY ? $money : 0,
                'status' => 1,
                'relation_tag_status' => input('tag_status', '0'),
            ];

            strtotime($data['start_time']) <= time() && $data['status'] = 2;
            $tag_ids =  input('tag_ids', "");
            return $this->goodsCouponRuleModel->addGoodsCouponRule($data, $this->operate_group_id, explode(",", $tag_ids));
        } else {
            $token = md5(unique_random(32));

            $this->assign("tag_ids", "");
            $this->assign("tag_status", "");
            $this->assign('token', $token);
            return $this->fetch("goodscoupon/rule/add");
        }
    }


    public function tokenGetTempList()
    {
        $token = input('token', '');
        $action = input('action', 'getData');
        $goodsCouponTypeId = input('goodscoupon_type_id', 0);
        $singleCount = input('single_count', 0);
        $res = ['list' => []];
        $temp = Cache::get($token);
        switch ($action) {
            case 'getData':
                if ($temp) $res['list'] = json_decode($temp, true);
                break;
            case 'add':
                if ($temp) {
                    $temp = json_decode($temp, true);
                } else {
                    $temp = [];
                }

                $goodsGoodsCouponTypeInfo = model('promotion_goodscoupon_type')->getInfo(['goodscoupon_type_id' => $goodsCouponTypeId], 'goodscoupon_type_id, goodscoupon_name');
                $goodsGoodsCouponTypeInfo['single_count'] = 0;
                array_push($temp, $goodsGoodsCouponTypeInfo);

                $res = $temp;
                Log::info($temp);
                Log::info(Cache::set($token, json_encode($temp)));
                break;
            case 'del':
                $temp = json_decode($temp, true);
                foreach ($temp as $k => $v) {
                    if ($v['goodscoupon_type_id'] == $goodsCouponTypeId) {
                        unset($temp[$k]);
                        break;
                    }
                }
                Cache::set($token, json_encode($temp));
                $res = $temp;
                break;
            case 'editSingleCount':
                $temp = json_decode($temp, true);
                foreach ($temp as $k => $v) {
                    if ($v['goodscoupon_type_id'] == $goodsCouponTypeId) {
                        $temp[$k]['single_count'] = $singleCount;
                        break;
                    }
                }
                Cache::set($token, json_encode($temp));
                $res = $temp;
                break;
        }
        return $this->goodsCouponRuleModel->success($res);
    }


    public function selectGoodsCoupon()
    {
        $token = input('token', '');
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $privacyStatus = input('privacy_status', '');
            $condition = [];

            if ($goodsCouponName = input('goods_coupon_name', '')) {
                $condition[] = ['gpt.goodscoupon_name', 'like', '%' . $goodsCouponName . '%'];
            }

            if (is_numeric($privacyStatus)) {
                $condition[] = ['gpt.privacy_status', '=' ,$privacyStatus];
            }

            $condition[] = ['gpt.status', '=', 1];
            $condition[] = ['', 'exp', Db::raw(' (gpt.count = 0 or gpt.count > gpt.lead_count)')];

            $order = 'gpt.create_time desc';
            $field = '*';

            if ($this->operate_group_id) {
                $condition[] = ['ogr.operate_group_id', '=', $this->operate_group_id];
            } else {
                $condition[] = ['', 'exp', Db::raw(' ogr.operate_group_relation_id is null ')];
            }

            $alias = 'gpt';
            $join = [
                ['operate_group_relation ogr', "ogr.relation_id = gpt.goodscoupon_type_id and ogr.relation_type = '" . OPERATE_GROUP_RELATION_TYPE::GOODSCOUPON . "'", 'left']
            ];

            $goodscoupon_type_model = new GoodscouponTypeModel();
            $res = $goodscoupon_type_model->getGoodscouponTypePageList($condition, $page, $page_size, $order, $field, $alias, $join);

            $temp = Cache::get($token);
            $selectedCouponIds = [];
            if ($temp) {
                $selectedCoupon = json_decode($temp, true);
                $selectedCouponIds = array_column($selectedCoupon, 'goodscoupon_type_id');
            }

            if (!empty($res['data']['list'])) {
                foreach ($res['data']['list'] as $k => $v) {
                    $res['data']['list'][$k]['over_time'] = date('Y-m-d H:i:s', $v['over_time']);
                    $res['data']['list'][$k]['selected'] = 0;
                    $v['count'] == 0 && $res['data']['list'][$k]['count'] = '无限';
                    $v['count'] > 0 && $res['data']['list'][$k]['count'] = $v['count'] - $v['lead_count'];
                    if (in_array($v['goodscoupon_type_id'], $selectedCouponIds)) {
                        $res['data']['list'][$k]['selected'] = 1;
                    }
                }
            }

            return $res;
        } else {

            $this->assign('token', $token);
            return $this->fetch("goodscoupon/rule/selectGoodsCoupon");
        }
    }


    public function detail()
    {
        $ruleId = input('rule_id', 0);

        $ruleInfo = $this->goodsCouponRuleModel->getGoodsCouponRuleInfo($ruleId);
        $ruleInfo['status'] = $this->goodsCouponRuleModel->goodsCouponStatusArr[$ruleInfo['status']];

        if($ruleInfo['type'] == GoodsCouponRuleType::ADD_TAG_TO_MEMBER)
        {
            $tag_ids = GoodsCouponRulesTagRelationModel::where("rule_id", $ruleId)->column("tag_id");
            $tag_names = EnterpriseTagModel::whereIn("tag_id", $tag_ids)->column("tag_name");
            $tag_rule = $ruleInfo['relation_tag_status'] == 0 ? "符合任一标签" : "同时符合标签";
            $tag_rule.= " (".implode(" ， ", $tag_names).")";
            $this->assign('tag_rule', $tag_rule);
        }
        $this->assign('rule', $ruleInfo);
        return $this->fetch("goodscoupon/rule/detail");
    }


    public function detailList()
    {
        $ruleId = input('rule_id', 0);

        $ruleInfo = $this->goodsCouponRuleModel->getGoodsCouponRuleInfo($ruleId);
        $couponList = json_decode($ruleInfo['relation_config'], true);

        return $this->goodsCouponRuleModel->success(['list' => $couponList]);
    }


    public function stopped()
    {
        if (request()->isAjax()) {
            $ruleId = input('rule_id', 0);
            return $this->goodsCouponRuleModel->stopRule($ruleId);
        }
    }
}