{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    input[name=sort]{
        width: 100px!important;
    }
</style>
{/block}
{block name="main"}

<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <div class="layui-form">
            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <label class="layui-form-label">商品搜索：</label>
                    <div class="layui-input-inline ns-len-short" style="margin-right: 0px;">
                        <select name="search_type" id="search_type">
                            <option value="1">按商品名称</option>
                            <option value="2">按商品ID</option>
                        </select>

                    </div>
                    <div class="layui-input-inline">
                        <input type="text" id="search_keys" name="search_keys" placeholder="" class="layui-input" autocomplete="off">
                    </div>
                </div>
                <!--<div class="layui-input-inline" id="divSearchBtns">-->
                <!--    <label class="layui-form-label">状态：</label>-->
                <!--    <input type="checkbox" name="status" value="1" title="上架中" lay-filter="oneChoose">-->
                <!--    <input type="checkbox" name="status" value="2" title="已下架" lay-filter="oneChoose">-->
                <!--    <input type="checkbox" name="status" value="3" title="已售罄" lay-filter="oneChoose">-->
                <!--</div>-->
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-inline">
                    <button class="layui-btn ns-bg-color" lay-filter="searchBtn" lay-submit>搜索</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 每个秒杀活动的ID -->
<input type="hidden" value="{$goodsCouponTypeId}" name="goodscoupon_type_id" id="goodscoupon_type_id" />
<input type="hidden" value="" name="selected_goods" id="selected_goods" />

<!-- 列表 -->
<table id="good_list" lay-filter="good_list"></table>

<script type="text/html" id="toolbar">
    <div class="layui-btn-group ">
        <a class="layui-btn ns-bg-color" onclick="goods()" data-num="{$goodsCouponTypeId}">添加商品</a>
    </div>
</script>

<script type="text/html" id="editSort">
    <input name="sort" type="number" min="0" max="999" onchange="editSort({{d.goodscoupon_type_id}},{{d.goods_id}}, this)" data-sort="{{d.sort}}" value="{{d.sort}}" placeholder="请输入排序" class="layui-input" autocomplete="off">
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="delete">取消</a>
    </div>
</script>

<!-- 商品 -->
<script type="text/html" id="goodIntro">
    <div class="ns-table-title">
        <div class="ns-title-pic">
            {{#  if(d.goods_image){  }}
            <img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
            <!-- <img layer-src src="{{ns.img(d.goods_image[0])}}"/> -->
            {{#  }  }}
        </div>
        <div class="ns-title-content">
            <a href="javascript:;" class="ns-multi-line-hiding ns-text-color">{{d.goods_name}}</a>
        </div>
    </div>
</script>

{/block}
{block name="script"}
{include file="addon/goodscoupon/admin/view/goodscoupon/_js.goods_list.html"}
{/block}