{extend name="app/admin/view/base.html"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
<script src="__STATIC__/js/xm-select.js"></script>

<style>
    .ns-form {
        /*margin-left: 80px;*/
        width: 800px;
    }
    /*.ns-form .layui-form-label{width: 120px;}*/
    /*.ns-form .layui-input-block{margin-left: 120px;}*/
    /*.ns-form .layui-input-block.block-style2{*/
    /*    margin: 10px 0;*/
    /*    border: 1px solid #f1f1f1;*/
    /*    padding: 20px;*/
    /*}*/
    .ns-form .layui-input-block.block-style2 > div{
        line-height: 18px;
        margin: 5px 0;
    }
    .ns-form .layui-input-block.block-style2 .title{
        border-left: 5px solid #4685FD;
        padding-left: 6px;
        margin-left: -10px;
    }
    .ns-form .layui-input-block.block-style2 .title span{
        color: red;
    }
    .ns-form .layui-input-block.block-style2 .desc{
        color: #aaa;
    }
    .ns-form .layui-input-block.block-style2 .desc span{
        color: black;
    }
</style>
{/block}
{/block}
{block name="main"}

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label">规则名称：</label>
		<div class="layui-input-block">
            {$rule.rule_name}
		</div>
	</div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label">已派发总数量：</label>
        <div class="layui-input-block">
            {$rule.send_count}
        </div>
    </div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label">开始执行时间：</label>
        <div class="layui-input-block">
            {$rule.start_time}
        </div>
    </div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label">结束执行时间：</label>
        <div class="layui-input-block">
            {$rule.stop_time}
        </div>
    </div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label">状态：</label>
        <div class="layui-input-block">
            {$rule.status}
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">执行规则：</label>
        <div class="layui-input-block">
            {if($rule.type == 1)}
            <div>
                新用户手机号注册后发放（无订单记录）
<!--                <input class="layui-input-inline" type="radio" name="type" lay-filter="type" value="1" checked title="新用户手机号注册后发放（无订单记录）">-->
            </div>
            {elseif($rule.type == 3)}
            <div>
                <div class="layui-input-inline">{$tag_rule}</div>
            </div>
            {elseif($rule.type == 4)}
            <div>
                用户完成小程序实名验证后发放
            </div>
            {else}
            <div>
                <div class="layui-input-inline">用户首单支付成功后发放，支付金额需满</div>
                <div class="layui-input-inline"><input style="text-align: center;" type="text" name="money" lay-verify="number" value="{$rule.money}" disabled></div>
                <div class="layui-input-inline">元</div>
<!--                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="2" title="用户首单支付成功后发放，支付金额需满"></div>-->
<!--                <div class="layui-input-inline"><input style="text-align: center;" type="text" name="money" lay-verify="number" value="0"></div>-->
            </div>
            {/if}
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">派发优惠券：</label>
        <div class="layui-input-block">
            <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">当用户触发执行规则时，系统自动向该用户派发以下优惠券</span>
        </div>
        <div class="layui-input-block">
            <table class="layui-input-block" id="relationGoodsCouponTypeTables" lay-filter="relationGoodsCouponTypeTables"></table>
        </div>

    </div>
	<div class="ns-form-row">
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

<input type="hidden" name="rule_id" value="{$rule.rule_id}" id="rule_id">

{/block}
{block name="script"}
<script>
	layui.use(['laydate', 'form', 'upload', 'table'], function() {
		var form = layui.form,
			upload = layui.upload,
			laydate = layui.laydate,
			repeat_flag = false, //防重复标识
			currentDate = new Date(), //当前时间
            table = layui.table;
		form.render();

		var createTable = function() {
            table.init('relationGoodsCouponTypeTables');
        }

        // 时间模块
		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime',
		});
        laydate.render({
            elem: '#stop_time', //指定元素
            type: 'datetime',
        });


        table = new Table({
            elem: '#relationGoodsCouponTypeTables',
            url: ns.url("goodscoupon://admin/goodsCouponRule/detailList"),
            page: false,
            where: {rule_id: $('#rule_id').val()},
            cols: [
                [
                    {
                        field: 'goodscoupon_type_id',
                        title: '券ID',
                        unresize: 'false',
                        align: 'center',
                        width: '20%'
                    },
                    {
                        field: 'goodscoupon_name',
                        title: '优惠券名称',
                        align: 'center',
                        unresize: 'false',
                        width: '40%'
                    },
                    {
                        field: 'single_count',
                        title: '每个用户派发张数',
                        unresize: 'false',
                        align: 'center',
                        width: '40%',
                        templet: function(data) {
                            return '<input name="single_count" disabled style="text-align: center;" type="number" onchange="editSingleCount(' + data.goodscoupon_type_id + ', this)" value="' + data.single_count + '" placeholder="" className="layui-input edit-sort ns-len-short" autoComplete="off">';
                        }
                    },
                ]
            ],
        });
	});

	function back() {
		location.href = ns.url("goodscoupon://admin/goodsCouponRule/list");
	}
</script>
<script src="ADMIN_JS/goods_coupon_edit.js?v=1"></script>
{/block}
