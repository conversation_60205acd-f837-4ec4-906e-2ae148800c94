{extend name="app/admin/view/base.html"/}
{block name="resources"}
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />
<script src="__STATIC__/js/xm-select.js"></script>

<style>
    .ns-form {
        /*margin-left: 80px;*/
        width: 800px;
    }
    /*.ns-form .layui-form-label{width: 120px;}*/
    /*.ns-form .layui-input-block{margin-left: 120px;}*/
    /*.ns-form .layui-input-block.block-style2{*/
    /*    margin: 10px 0;*/
    /*    border: 1px solid #f1f1f1;*/
    /*    padding: 20px;*/
    /*}*/
    .ns-form .layui-input-block.block-style2 > div{
        line-height: 18px;
        margin: 5px 0;
    }
    .ns-form .layui-input-block.block-style2 .title{
        border-left: 5px solid #4685FD;
        padding-left: 6px;
        margin-left: -10px;
    }
    .ns-form .layui-input-block.block-style2 .title span{
        color: red;
    }
    .ns-form .layui-input-block.block-style2 .desc{
        color: #aaa;
    }
    .ns-form .layui-input-block.block-style2 .desc span{
        color: black;
    }
</style>
{/block}
{/block}
{block name="main"}

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>规则名称：</label>
		<div class="layui-input-block">
			<input type="text" name="rule_name" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
        <div class="ns-word-aux">
            <p>名称不在前端展示，仅用于后台管理查看</p>
        </div>
	</div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label"><span class="required">*</span>开始执行时间：</label>
        <div class="layui-input-block">
            <input type="text" name="start_time" id="start_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
        </div>
        <div class="ns-word-aux">
            <p>到设定时间后，系统开始按规则执行派券任务</p>
        </div>
    </div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label"><span class="required">*</span>结束执行时间：</label>
        <div class="layui-input-block">
            <input type="text" name="stop_time" lay-verify="time" id="stop_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
        </div>
        <div class="ns-word-aux">
            <p>到设定时间后，系统结束按规则执行派券任务</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>执行规则：</label>
        <div class="layui-input-block">
            <div>
                <input class="layui-input-inline" type="radio" name="type" lay-filter="type" value="1" checked title="新用户手机号注册后发放（无订单记录）">
            </div>
            <div>
                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="2" title="用户首单支付成功后发放，支付金额需满"></div>
                <div class="layui-input-inline"><input style="text-align: center;" type="text" name="money" lay-verify="number" value="0"></div>
                <div class="layui-input-inline">元</div>
                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="3" title="指定标签用户发放"></div>
                {include file="app/admin/view/member/member_tag_component.html"}
                <div class="layui-input-inline"><input type="radio" name="type" lay-filter="type" value="4" title="用户完成小程序实名验证后发放"></div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>派发优惠券：</label>
        <div class="layui-input-block">
            <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">当用户触发执行规则时，系统自动向该用户派发以下优惠券</span>
        </div>
        <div class="layui-input-block">
            <table class="layui-input-block" id="relationGoodsCouponTypeTables" lay-filter="relationGoodsCouponTypeTables"></table>
        </div>

    </div>
    <input type="hidden" name="token" value="{$token}" id="token">
	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>



{/block}
{block name="script"}
<script>
	layui.use(['laydate', 'form', 'upload', 'table'], function() {
		var form = layui.form,
			upload = layui.upload,
			laydate = layui.laydate,
			repeat_flag = false, //防重复标识
			currentDate = new Date(), //当前时间
            table = layui.table;
		form.render();

		var createTable = function() {
            table.init('relationGoodsCouponTypeTables');
        }

        // 时间模块
		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime',
		});
        laydate.render({
            elem: '#stop_time', //指定元素
            type: 'datetime',
        });


        table = new Table({
            elem: '#relationGoodsCouponTypeTables',
            url: ns.url("goodscoupon://admin/goodsCouponRule/tokenGetTempList"),
            page: false,
            cols: [
                [
                    {
                        field: 'goodscoupon_type_id',
                        title: '券ID',
                        unresize: 'false',
                        align: 'center',
                        width: '20%'
                    },
                    {
                        field: 'goodscoupon_name',
                        title: '优惠券名称',
                        align: 'center',
                        unresize: 'false',
                        width: '30%'
                    },
                    {
                        field: 'single_count',
                        title: '每个用户派发张数',
                        unresize: 'false',
                        align: 'center',
                        width: '30%',
                        templet: function(data) {
                            return '<input name="single_count" style="text-align: center;" type="number" lay-verify="int" onchange="editSingleCount(' + data.goodscoupon_type_id + ', this)" value="' + data.single_count + '" placeholder="" className="layui-input edit-sort ns-len-short" autoComplete="off">';
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        unresize: 'false',
                        width: '20%',
                        templet: function(data) {
                            return '<div className="ns-table-btn">' +
                                '<a className="layui-btn" lay-event="del">删除</a>' +
                            '</div>';
                        }
                    }
                ]
            ],
            callback: function(res, curr, count) {
                var html = '<tr style="text-align: center;">' +
                    '<td></td>' +
                    '<td><button class="layui-btn ns-bg-color" lay-event="selectGoodsCoupon">－请选择派发的优惠券－</button></td>' +
                    '<td></td>' +
                    '<td></td>' +
                    '</tr>';
                if (res.data.length > 0) {
                    $(".layui-table-main tbody").append(html);
                } else {
                    $(".layui-table-main tbody").html(html);
                }
            }
        });


        table.tool(function (obj) {
            var data = obj.data;
            let token = $('#token').val();
            switch (obj.event) {
                case 'selectGoodsCoupon': //编辑
                    layer.open({
                        type: 2,
                        title: '选择优惠券',
                        area: ['800px', '600px'],
                        content: ns.url("goodscoupon://admin/goodsCouponRule/selectGoodsCoupon", {"token": token}),
                        end: function() {
                            table.reload({
                                where: {token: token}
                            });
                        }
                    });
                    break;
                case 'del':
                    if (repeat_flag) return false;
                    repeat_flag = true;
                    $.ajax({
                        url: ns.url("goodscoupon://admin/goodsCouponRule/tokenGetTempList"),
                        data: {token: token, goodscoupon_type_id: data.goodscoupon_type_id, action: 'del'},
                        dataType: 'JSON',
                        type: 'POST',
                        success: function(res) {
                            repeat_flag = false;
                            if (res.code == 0) {
                                table.reload({
                                    where: {token: token}
                                });
                            }else{
                                layer.msg(res.message);
                            }
                        }
                    });
            }
        });

        /**
		 * 表单监听提交
		 */
		form.on('submit(save)', function(data) {
			if (data.field.is_show == undefined) {
				data.field.is_show = 0;
			}

			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				url: ns.url("goodscoupon://admin/goodsCouponRule/add"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
                            closeBtn: false,
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("goodscoupon://admin/goodsCouponRule/list")
							},
							btn2: function() {
								location.href = ns.url("goodscoupon://admin/goodsCouponRule/add")
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		form.verify({
			number: function (value) {
				if (parseFloat(value) < 0) {
					return '请输入大于或等于0的数!'
				}
			},
			int: function (value) {
				if (value%1 != 0) {
					return '请输入整数!'
				}
				if (value <= 0) {
					return '请输入大于0的数!'
				}
			},
			money: function (value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位'
				}
			},
			time: function(value) {
				var now_time = (new Date()).getTime();
				var end_time = (new Date(value)).getTime();
				var start_time = $('#start_time').val();
                startTime = (new Date(start_time)).getTime();
                if (!value) {
                    return '活动结束时间不能为空!'
                }
                if (startTime >= end_time) {
                    return '开始时间不能大于结束时间!'
                }
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
			},
			gtzero: function(value) {
				if (parseFloat(value) <= 0) {
					return '请输入大于0的数!'
				}
			},
		});
	});

	function back() {
		location.href = ns.url("goodscoupon://admin/goodsCouponRule/list");
	}

    function editSingleCount(id, event){
        var data = $(event).val(),
            token = $('#token').val();
        $.ajax({
            url: ns.url("goodscoupon://admin/goodsCouponRule/tokenGetTempList"),
            data: {goodscoupon_type_id: id, single_count: data, action: 'editSingleCount', token: token},
            type: 'post',
            dataType: 'json',
        })
    }
</script>
<script src="ADMIN_JS/goods_coupon_edit.js?v=1"></script>
{/block}
