{extend name="app/admin/view/popup_base.html"/}
{block name="resources"}
<style>
    .layui-layer-page .layui-layer-content { padding: 20px 30px; }
</style>
{/block}
{block name="main"}


<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">优惠券名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goods_coupon_name" placeholder="请输入优惠券名称搜索" autocomplete="off" class="layui-input">
                    </div>
                </div>

            </div>
            <input type="hidden" name="token" value="{$token}" id="token">
            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab ns-table-tab"  lay-filter="goodscoupon_tab">
    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="goodsCouponList" lay-filter="goodsCouponList"></table>
    </div>
</div>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var table,
            form = layui.form,
            element = layui.element,
            repeat_flag = false; //防重复标识
        form.render();

        table = new Table({
            elem: '#goodsCouponList',
            url: ns.url("goodscoupon://admin/goodsCouponRule/selectGoodsCoupon"),
            where: {token: $('#token').val()},
            cols: [
                [
                    {
                        field: 'goodscoupon_type_id',
                        title: '券ID',
                        unresize: 'false',
                        align: 'center',
                        width: '10%'
                    },
                    {
                        field: 'goodscoupon_name',
                        title: '优惠券名称',
                        align: 'center',
                        unresize: 'false',
                        width: '30%'
                    },
                    {
                        field: 'count',
                        title: '剩余券数量',
                        unresize: 'false',
                        align: 'center',
                        width: '20%',
                    },
                    {
                        field: 'over_time',
                        title: '活动结束',
                        align: 'center',
                        unresize: 'false',
                        width: '30%',
                    },
                    {
                        title: '操作',
                        align: 'center',
                        unresize: 'false',
                        width: '10%',
                        templet: function(data) {
                            let btn = '<div className="ns-table-btn">' +
                                '<a className="layui-btn" lay-event="select">选择</a>' +
                                '</div>';
                            if (data.selected == 0) {
                                return btn;
                            }
                            return '已选';
                        }
                    }
                ]
            ],
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data,
                token = $('#token').val();
            switch (obj.event) {
                case 'select': //编辑
                    if (repeat_flag) return false;
                    repeat_flag = true;
                    $.ajax({
                        url: ns.url("goodscoupon://admin/goodsCouponRule/tokenGetTempList"),
                        type: 'post',
                        dataType: 'json',
                        data: {'action': 'add', 'goodscoupon_type_id': data.goodscoupon_type_id, 'token': token},
                        success: function() {
                            repeat_flag = false;
                            table.reload();
                        }
                    })
                    break;
            }
        });


        // 搜索
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
            return false;
        });
    });
</script>
{/block}