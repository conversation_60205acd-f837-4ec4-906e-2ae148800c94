{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.layui-layer-page .layui-layer-content { padding: 20px 30px; }
</style>
{/block}
{block name="main"}

<!-- 按钮容器 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加优惠券</button>
</div>

<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title"></h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">优惠券名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="goodscoupon_name" placeholder="请输入优惠券名称" autocomplete="off" class="layui-input">
					</div>
				</div>

                <div class="layui-inline">
                    <label class="layui-form-label">活动状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-filter="status">
                            <option value="">全部</option>
							<option value="3">未开始</option>
                            <option value="1">进行中</option>
                            <option value="2">已结束</option>
                        </select>
                    </div>
                </div>
			</div>
			
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">使用范围：</label>
					<div class="layui-input-inline">
						<select name="use_scenario" lay-filter="use_scenario">
							<option value="">全部</option>
							<option value="1">全场通用</option>
							<option value="2">指定类目可用</option>
							<option value="3">指定商品可用</option>
							<option value="4">指定商品不可用</option>
						</select>
					</div>
				</div>

				{if !$operate_group_id}
<!--				<div class="layui-inline">-->
<!--					<label class="layui-form-label">运营组：</label>-->
<!--					<div class="layui-input-inline">-->
<!--						<select name="operate_group_id" lay-filter="operate_group_id">-->
<!--							<option value="">全部</option>-->
<!--							<option value="-1">非运营组</option>-->
<!--							{foreach($operate_group_list as $k => $v)}-->
<!--							<option value="{$v['operate_group_id']}">{$v['operate_group_name']}</option>-->
<!--							{/foreach}-->
<!--						</select>-->
<!--					</div>-->
<!--				</div>-->
				{/if}
			</div>

			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				<a class="layui-btn layui-btn-primary" onclick="allReceive();">全部领取记录</a>
				<a class="layui-btn layui-btn-primary" onclick="goodsCouponRule();">自动派券规则</a>
			</div>
		</form>
	</div>
</div>

<input type="hidden" id="miniProgramPath" value="{$miniProgramPath}">
<input type="hidden" id="h5Path" value="{$h5Path}">

<div class="layui-tab ns-table-tab"  lay-filter="goodscoupon_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">全部</li>
		<li lay-id="3">未开始</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="goodscoupon_list" lay-filter="goodscoupon_list"></table>
	</div>
</div>

<script type="text/html" id="validity">
	{{#  if(d.validity_type == 0){  }}
	失效期：{{ ns.time_to_date(d.end_time) }}
	{{#  }else{  }}
	领取后，{{ d.fixed_term }}天有效
	{{#  }  }}
</script>

<script type="text/html" id="overTime">
    {{ ns.time_to_date(d.over_time) }}
</script>

<script type="text/html" id="startTime">
	{{ ns.time_to_date(d.start_time) }}
</script>

<!-- 操作 -->
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js"></script>
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="send">主动发券</a>
        <!-- 进行中 -->
        {{#  if(d.status == 1){ }}
        <a class="layui-btn" lay-event="detail">详情</a>
        <!--<a class="layui-btn" lay-event="edit">编辑</a>-->
        {{#  } }}

        <!-- 已结束 -->
        {{#  if(d.status == 2){ }}
        <a class="layui-btn" lay-event="detail">详情</a>
        {{#  } }}

        <a class="layui-btn" lay-event="copyPath">复制路径</a>

        {{# if(d.use_scenario == 3 || d.use_scenario == 4) { }}
        <a class="layui-btn" lay-event="goods">管理商品</a>
        {{# } }}

        <a class="layui-btn" lay-event="receive">领取记录</a>
        {{#  if(d.status == 1){ }}
        <a class="layui-btn" lay-event="shutDown">提前结束</a>
        {{#  } }}
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'laytpl','laydate', 'element'], function() {
		var table,
			form = layui.form,
			laytpl = layui.laytpl,
			element = layui.element,
			laydate = layui.laydate,
			validityType = 0,
			repeat_flag = false; //防重复标识
		form.render();

		//监听Tab切换，以改变地址hash值
		element.on('tab(goodscoupon_tab)', function(){
			table.reload({
                page: {curr: 1},
                where: {'status': this.getAttribute('lay-id')},
            })
		});

        table = new Table({
            elem: '#goodscoupon_list',
            url: ns.url("goodscoupon://admin/goodscoupon/lists"),
            cols: [
                [
                    {
                        field: 'goodscoupon_type_id',
                        title: 'ID',
                        unresize: 'false',
                        align: 'center',
                        width: '5%'
                    },
                    {
                        field: 'goodscoupon_name',
                        title: '优惠券名称',
                        align: 'center',
                        unresize: 'false',
                        width: '10%'
                    },
                    {
                        field: 'money',
                        title: '优惠政策',
                        unresize: 'false',
                        align: 'center',
                        width: '10%',
                        templet: function(data) {
                            return '满' + data.at_least + '减' + data.money;
                        }
                    },
                    {
                        field: 'count',
                        title: '发放总数量',
                        align: 'center',
                        unresize: 'false',
                        width: '6%',
                        templet: function(data){
                            return data.count == 0 ? '不限' : data.count;
                        }
                    },
                    {
                        title: '领取上限',
                        align: 'center',
                        unresize: 'false',
                        width: '6%',
                        templet: function(data){
                            return data.max_fetch == 0 ? '不限' : data.max_fetch + '张/人';
                        }
                    },
                    {
                        title: '领取有效期',
                        align: 'center',
                        unresize: 'false',
                        templet: '#validity',
                        width: '15%'
                    },
					{
						title: '活动开始时间',
						align: 'center',
						unresize: 'false',
						templet: '#startTime',
						width: '10%'
					},
                    {
                        title: '活动结束时间',
                        align: 'center',
                        unresize: 'false',
                        templet: '#overTime',
                        width: '10%'
                    },
                    {
                        title: '使用范围',
                        align: 'center',
                        unresize: 'false',
                        templet: function(data) {
                            let useScenarioCn = {1: '全场通用', 2: '指定类目可用', 3: '指定商品可用', 4: '指定商品不可用', 5: '指定专区可用'}
                            return useScenarioCn[data.use_scenario];
                        },
                        width: '8%'
                    },
                    {
                        field: 'status_name',
                        title: '状态',
                        align: 'center',
                        unresize: 'false',
                        width: '5%'
                    },
                    {
                        field: 'privacy_status',
                        title: '公开状态',
                        align: 'center',
                        unresize: 'false',
                        width: '6%',
                        templet: function(item) {
                            let privacyStatusCn = {0: '内部券', 1: '公开券'};
                            return privacyStatusCn[item.privacy_status];
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        toolbar: '#operation',
                        unresize: 'false',
                        width: '12%'
                    }
                ]
            ],
        });
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("goodscoupon://admin/goodscoupon/edit", {"goodscoupon_type_id": data.goodscoupon_type_id});
					break;
				case 'close': //关闭
					layer.confirm('确定要关闭吗?', function() {
						if (repeat_flag) return false;
						repeat_flag = true;
						
						$.ajax({
							url: ns.url("goodscoupon://admin/goodscoupon/close", {"goodscoupon_type_id": data.goodscoupon_type_id}),
							data: data,
							dataType: 'JSON',
							type: 'POST',
							success: function(res) {
								layer.msg(res.message);
								repeat_flag = false;

								if (res.code == 0) {
									table.reload();
								}
							}
						});
					}, function() {
						layer.close();
						repeat_flag = false;
					});
					break;
				case 'detail': //详情
                    location.href = ns.url("goodscoupon://admin/goodscoupon/detail", {"goodscoupon_type_id": data.goodscoupon_type_id});
					break;
                case 'receive': //领取记录
                    location.href = ns.url("goodscoupon://admin/goodscoupon/receive", {"goodscoupon_type_id": data.goodscoupon_type_id});
                    break;
                case 'goods':
                    location.href = ns.url("goodscoupon://admin/goodscoupon/goods", {"goodscoupon_type_id": data.goodscoupon_type_id});
                    break;
                case 'shutDown':
                    layer.confirm('提前结束活动后，用户将无法再领取该活动优惠券，已被领取的优惠券也自动过期，无法使用，请确定是否提前结束。', function() {
                        if (repeat_flag) return false;
                        repeat_flag = true;

                        $.ajax({
                            url: ns.url("goodscoupon://admin/goodscoupon/shutDown", {"goodscoupon_type_id": data.goodscoupon_type_id}),
                            data: data,
                            dataType: 'JSON',
                            type: 'POST',
                            success: function(res) {
                                layer.msg(res.message);
                                repeat_flag = false;

                                if (res.code == 0) {
                                    table.reload();
                                }
                            }
                        });
                    }, function() {
                        layer.close();
                        repeat_flag = false;
                    });
                    break;
                case 'copyPath':
                    let miniProgramPath = $('#miniProgramPath').val();
                    let h5Path = $('#h5Path').val();

                    layer.open({
                        type: 1,
                        area: ['auto', '200px'],
                        content: "小程序路径: " + miniProgramPath + '?goodscoupon_type_id=' + data.goodscoupon_type_id + "&nbsp;&nbsp;&nbsp;<span class='layui-btn layui-btn-primary' style='padding: 2px 2px; display: inline;' data-clipboard-text='" +
                            miniProgramPath + '?goodscoupon_type_id=' + data.goodscoupon_type_id + "' id='copyMiniProgramPath' onclick='copyMiniProgramPath();'>复制</span>" +
                            "<br /><br />h5路径: " + h5Path + '?goodscoupon_type_id=' + data.goodscoupon_type_id + "&nbsp;&nbsp;&nbsp;<span class='layui-btn layui-btn-primary' style='padding: 2px 2px; display: inline;' data-clipboard-text='" +
                            h5Path + '?goodscoupon_type_id=' + data.goodscoupon_type_id + "' id='copyH5Path' onclick='copyH5Path();'>复制</span>",
                        cancel: function(){
                            //右上角关闭回调

                            //return false 开启该代码可禁止点击该按钮关闭
                        }
                    });
                    break;
                case 'send':
                    location.href = ns.url("goodscoupon://admin/goodscoupon/sendPage", {"goodscoupon_type_id": data.goodscoupon_type_id});
                    break;
            }
		});

		//详情
		function goodscouponDetail(data) {
			var detailHtml = $("#goodscouponDetail").html();
			laytpl(detailHtml).render(data, function(html) {
				layer.open({
					type: 1,
					title: '优惠券详情',
					area: ['500px'],
					content: html

				});
			})
		}



		// 搜索
		form.on('submit(search)', function(data) {
			if(validityType == 2){
				data.field.start_time = $("#start_day").val();
				data.field.end_time = $("#end_day").val();
			}
			
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
		
		form.on('select(validity_type)', function(data){
			switch (data.value) {
				case '':
					$(".relative-time").addClass("layui-hide");
					$(".fixed-time").addClass("layui-hide");
					break;
				case '1':
					laydate.render({
						elem: '#start_date', //指定元素
						type: 'datetime',
						done: function(value, date, endDate){
							$("input[name='start_time']").val(ns.date_to_time(value));
						}
					});
					laydate.render({
						elem: '#end_date', //指定元素
						type: 'datetime',
						done: function(value, date, endDate){
							$("input[name='end_time']").val(ns.date_to_time(value));
						}
					});
					$(".relative-time").addClass("layui-hide");
					$(".fixed-time").removeClass("layui-hide");
					break;
				case '2':
					validityType = 2;
					$(".relative-time").removeClass("layui-hide");
					$(".fixed-time").addClass("layui-hide");
					break;

			}
		});
		
		form.verify({
			int: function(value) {
				if (value < 0) {
					return '发券天数不能小于0！';
				}
			}
		})
	});
	
	function add() {
		location.href = ns.url("goodscoupon://admin/goodscoupon/add");
	}


	function allReceive() {
        location.href = ns.url("goodscoupon://admin/goodscoupon/receive");
    }

    function goodsCouponRule() {
		location.href = ns.url("goodscoupon://admin/goodsCouponRule/list");
	}

    function copyMiniProgramPath()
    {
        let content = $("#copyMiniProgramPath").attr('data-clipboard-text');
        let clipboard = new Clipboard('#copyMiniProgramPath', {
            text: function() {
                return content;
            }
        });
        clipboard.on('success', function(e) {
            alert("复制成功");
            clipboard.destroy();  //解决多次弹窗
        });

        clipboard.on('error', function(e) {
            console.log(e);
        });
    }

    function copyH5Path()
    {
        let content = $("#copyH5Path").attr('data-clipboard-text');
        let clipboard = new Clipboard('#copyH5Path', {
            text: function() {
                return content;
            }
        });
        clipboard.on('success', function(e) {
            alert("复制成功")
            clipboard.destroy();  //解决多次弹窗;
        });

        clipboard.on('error', function(e) {
            console.log(e);
        });
    }
</script>
{/block}