{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.layui-layer-page .layui-layer-content { padding: 20px 30px; }
    .layui-table-cell {
        margin: 0;
        font-size: 14px;
        padding: 0 5px;
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
        word-break: break-all;
    }

</style>
{/block}
{block name="main"}

<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" id="form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">活动ID：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goodscoupon_type_id" placeholder="请输入活动ID" value="{$goodscoupon_type_id ? $goodscoupon_type_id : ''}" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">活动名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goodscoupon_name" placeholder="请输入活动名称" value="{$goodscoupon_name ? $goodscoupon_name : ''}" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">优惠券状态：</label>
                    <div class="layui-input-inline">
                        <select name="state" lay-filter="state">
                            <option value="">全部</option>
                            <option value="1">已领取</option>
                            <option value="2">已使用</option>
                            <option value="3">已过期</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">领取用户名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="username" placeholder="请输入用户昵称" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">用户手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="请输入用户手机号" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">优惠券类型：</label>
                    <div class="layui-input-inline">
                        <select name="privacy_status" lay-filter="privacy_status">
                            <option value="">全部</option>
                            <option value="0">内部券</option>
                            <option value="1">公开券</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">领取时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_fetch_time" placeholder="开始时间" id="start_fetch_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_fetch_time" placeholder="结束时间" id="end_fetch_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">使用时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_use_time" placeholder="开始时间" id="start_use_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_use_time" placeholder="结束时间" id="end_use_time" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>
            </div>

            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button class="layui-btn layui-btn-primary export" lay-submit lay-filter="export" >导出用券数据</button>
                <button class="layui-btn layui-btn-primary useCouponExport" lay-submit lay-filter="useCouponExport" >导出商品用券数据</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="goodscoupon_tab">
    <div class="layui-colla-item" style="border: 1px solid #f1f1f1; height: 40px; line-height: 40px;">
        <span style="padding-left: 20px;">已发放优惠券：<span id="sentTotal"></span></span>
        <span style="padding-left: 20px;">预计优惠总额：<span id="expectDiscountTotal"></span></span>
        <span style="padding-left: 20px;">已使用：<span id="usedTotal"></span></span>
        <span style="padding-left: 20px;">实际优惠总额：<span id="actualDiscountTotal"></span></span>
        <span style="padding-left: 20px;">关联订单总额：<span id="connectTotal"></span></span>
    </div>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="goodscoupon_list" lay-filter="goodscoupon_list"></table>
	</div>
</div>
<!--获取方式-->
<script type="text/html" id="get_type">
    {{# if(d.get_type == 1){ }}
    <div class="layui-elip">订单领取</div>
    {{# }else if(d.get_type == 2){ }}
    <div class="layui-elip">直接领取</div>
    {{# }else if(d.get_type == 3){ }}
    <div class="layui-elip">获取领取</div>
    {{# } }}
</script>

<!--状态-->
<script type="text/html" id="state">
    {{# if(d.state == 1){ }}
    <div class="layui-elip">已领取</div>
    {{# }else if(d.state == 2){ }}
    <div class="layui-elip">已使用</div>
    {{# }else if(d.state == 3){ }}
    <div class="layui-elip">已过期</div>
    {{# } }}
</script>

<!--领取时间-->
<script type="text/html" id="fetch_time">
	{{ ns.time_to_date(d.fetch_time) }}
</script>

<!--使用时间-->
<script type="text/html" id="use_time">
    {{ ns.time_to_date(d.use_time) }}
</script>

<input id="goodscoupon_type_id" type="hidden" value="{$goodscoupon_type_id ? $goodscoupon_type_id: 0}" />
<input id="rule_id" type="hidden" value="{$rule_id ? $rule_id: 0}" />
{/block}
{block name="script"}
<script>
	layui.use(['form', 'laytpl', 'element', 'laydate'], function() {
		var table,
			form = layui.form,
			element = layui.element,
            goodscoupon_type_id = $('#goodscoupon_type_id').val(),
            rule_id = $('#rule_id').val(),
            laydate = layui.laydate,
			laytpl = layui.laytpl;

        //开始时间
        laydate.render({
            elem: '#start_fetch_time' //指定元素
            ,done: function(value, date, endDate){
                start_time = ns.date_to_time(value);

            }
        });
        //结束时间
        laydate.render({
            elem: '#end_fetch_time' //指定元素
            ,done: function(value, date, endDate){
                end_time = ns.date_to_time(value);
            }
        });

        //开始时间
        laydate.render({
            elem: '#start_use_time' //指定元素
            ,done: function(value, date, endDate){
                start_time = ns.date_to_time(value);

            }
        });
        //结束时间
        laydate.render({
            elem: '#end_use_time' //指定元素
            ,done: function(value, date, endDate){
                end_time = ns.date_to_time(value);
            }
        });

		form.render();

		table = new Table({
			elem: '#goodscoupon_list',
			url: ns.url("goodscoupon://admin/goodscoupon/receive"),
            method: 'post',
            where: {goodscoupon_type_id: goodscoupon_type_id, goodscoupon_name: $('input[name="goodscoupon_name"]').val(), rule_id: rule_id},
            parseData: function(data) {
                return {
                    "code": data.code,
                    "msg": data.message,
                    "count": data.data.count,
                    "data": data.data.list,
                    "sentTotal": data.data.sentTotal,
                    "expectDiscountTotal": data.data.expectDiscountTotal,
                    "usedTotal": data.data.usedTotal,
                    "actualDiscountTotal": data.data.actualDiscountTotal,
                    "connectTotal": data.data.connectTotal,
                };
            },
            cols: [
			    [
                    {
                        field: 'goodscoupon_type_id',
                        title: '活动ID',
                        unresize: 'false',
                        width: '5%',
                        align: 'center'
                    },
                    {
                        field: 'username',
                        title: '领取用户名称',
                        unresize: 'false',
                        width: '8%',
                        align: 'center',
                        templet: function(data) {
                            return data.username ? data.username : (data.nickname ? data.nickname : data.mobile);
                        }
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        unresize: 'false',
                        width: '8%',
                        align: 'center'
                    },
                    {
                        field: 'goodscoupon_name',
                        title: '活动名称',
                        unresize: 'false',
                        width: '8%',
                        align: 'center',
                    },
                    {
                        field: 'privacy_status',
                        title: '券类型',
                        unresize: 'false',
                        width: '5%',
                        align: 'center',
                        templet: function(data){
                            let getTypeCn = {0: '内部券', 1: '公开券'};
                            return getTypeCn[data.privacy_status];
                        }
                    },
                    {
                        field: 'money',
                        title: '优惠金额',
                        unresize: 'false',
                        width: '7%',
                        align: 'center',
                    },
                    {
                        title: '优惠券状态',
                        unresize: 'false',
                        templet: '#state',
                        width: '8%',
                        align: 'center',
                    },
                    {
                        title: '领取时间',
                        unresize: 'false',
                        templet: '#fetch_time',
                        width: '10%',
                        align: 'center',
                    },
                    {
                        title: '获取方式',
                        unresize: 'false',
                        templet: '#get_type',
                        width: '8%',
                        align: 'center',
                        templet: function(data) {
                            let getTypeCn = {1: '订单', 2: '直接领取', 3: '活动领取', 4: '后台发放', 5: '规则自动派发'};
                            return getTypeCn[data.get_type];
                        }
                    },
                    {
                        title: '使用时间',
                        unresize: 'false',
                        templet: '#use_time',
                        width: '10%',
                        align: 'center',
                    },
                    {
                        field: 'order_money',
                        title: '关联订单金额',
                        unresize: 'false',
                        width: '8%',
                        align: 'center',
                    },
                    {
                        field: 'order_no_btn',
                        title: '关联订单号',
                        width: '12%',
                        align: 'center',
                    },
                    {
                        title: '操作',
                        width: '5%',
                        align: 'center',
                        templet: function(item) {
                            let btn = '';
                            if (item.state == 1) {
                                btn += '<a href="javascript:void();" lay-event="del">删除</a>'
                            }
                            return btn;
                        }
                    }
                ]
			],
            callback: function(res, curr, count)
            {
                $('#sentTotal').html(res.sentTotal);
                $('#expectDiscountTotal').html(res.expectDiscountTotal);
                $('#usedTotal').html(res.usedTotal);
                $('#actualDiscountTotal').html(res.actualDiscountTotal);
                console.log(res);
                $('#connectTotal').html(res.connectTotal);
            }
		});
		
		// 搜索
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});


        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.href = ns.url("goodscoupon://admin/goodscoupon/edit", {"goodscoupon_type_id": data.goodscoupon_type_id});
                    break;
                case 'del': //关闭
                    layer.confirm('确定要删除吗?', function() {
                        if (repeat_flag) return false;
                        repeat_flag = true;

                        $.ajax({
                            url: ns.url("goodscoupon://admin/goodscoupon/deleteMemberCoupon", {"coupon_id": data.goodscoupon_id}),
                            data: data,
                            dataType: 'JSON',
                            type: 'POST',
                            success: function(res) {
                                layer.msg(res.message);
                                repeat_flag = false;

                                if (res.code == 0) {
                                    table.reload();
                                }
                            }
                        });
                    }, function() {
                        layer.close();
                        repeat_flag = false;
                    });
                    break;
            }
        });
	});

    $("body").on("click", ".export", function (data){
        //导出
        location.href = ns.url("goodscoupon://admin/goodscoupon/export", $('#form').serialize());
        return false;
        // // console.log(ns.url("goodscoupon://admin/goodscoupon/export", ns.urlReplace(location.hash.replace('#!', ''))));
        // // return false;
        // layer.msg('导出中，请稍后...', {icon: 16, shade: 0.5, time: 0});
        // $.ajax({
        //     url : ns.url("goodscoupon://admin/goodscoupon/export", ns.urlReplace(location.hash.replace('#!', ''))),
        //     type : 'GET',
        //     data: getUrlVars($('#form').serialize()),
        //     dataType : 'JSON',
        //     success : function (res){
        //         if(res.code == 0)
        //         {
        //             location.href = res.data;
        //             layer.msg('操作成功')
        //         }
        //         else
        //         {
        //             layer.msg(res.message)
        //             return false;
        //         }
        //     }
        // });
        // return false;
    });
    $("body").on("click", ".useCouponExport", function (data){
        location.href = ns.url("goodscoupon://admin/goodscoupon/goodsUsecouponExport", $('#form').serialize());
        return false;
    })


    function getUrlVars(url) {
        var hash;
        var myJson = {};
        var hashes = url.slice(url.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            myJson[hash[0]] = hash[1];
        }
        return myJson;
    }
</script>
{/block}