{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .ns-discount { display: flex; justify-content: space-between; height: 34px; line-height: 34px; padding: 5px 15px; background-color: #F6FBFD; border: 1px dashed #BCE8F1; }
    .layui-table-view {
        margin-top: 0;
    }
    .layui-badge{
        margin-right: 5px;
    }
</style>
{/block}
{block name="main"}
<div class="layui-form">
    <div class="layui-form-item">
        <label class="layui-form-label">优惠券名称：</label>
        <div class="layui-input-inline">{$goodscoupon_type_info.goodscoupon_name}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">优惠政策：</label>
        <div class="layui-input-inline">满￥{$goodscoupon_type_info.at_least}减￥{$goodscoupon_type_info.money}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">是否公开：</label>
        <div class="layui-input-inline">{if $goodscoupon_type_info.privacy_status == 0} 内部券 {else/} 公开券 {/if}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">发放总数量：</label>
        <div class="layui-input-inline">{if $goodscoupon_type_info.count == 0} 不限 {else/} {$goodscoupon_type_info.count} 张 {/if}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">用户领取限制：</label>
        <div class="layui-input-inline">
            {if $tag_rule != ""}
                {$tag_rule}， 领取次数
            {/if}
            {if $goodscoupon_type_info.max_fetch == 0}
                不限
            {else/}
                {$goodscoupon_type_info.max_fetch} 张
            {/if}
        </div>
    </div>

    <!--<div class="layui-form-item">-->
    <!--    <label class="layui-form-label">是否进行店主分佣：</label>-->
    <!--    <div class="layui-input-inline">{if $goodscoupon_type_info.use_scenario == 0} 否，任何用户使用该券购买后，均不产生店主佣金或享受分销商价 {else/}  是，分销商购买可享受分销商价，普通用户购买店主有佣金 {/if}</div>-->
    <!--</div>-->

    <div class="layui-form-item">
        <label class="layui-form-label">使用范围：</label>
        <div class="layui-input-inline">
            {if $goodscoupon_type_info.use_scenario == 1}
            全场通用
            {elseif $goodscoupon_type_info.use_scenario == 2}
            指定类目可用
            {elseif $goodscoupon_type_info.use_scenario == 3}
            指定商品可用
            {elseif $goodscoupon_type_info.use_scenario == 4}
            指定商品不可用
            {elseif $goodscoupon_type_info.use_scenario == 5}
            指定专区可用 ({$goodscoupon_type_info.topic_name})
            {/if}
        </div>
    </div>

    {if $goodscoupon_type_info.use_scenario == 2}
    <div class="layui-form-item">
        <label class="layui-form-label">类目：</label>
        <div class="layui-input-inline">
            {foreach $goodscoupon_type_info.category_name as $key=>$value}
            <span class="layui-badge layui-bg-blue">{$value}</span>
            {/foreach}
        </div>
    </div>
    {/if}

    <div class="layui-form-item">
        <label class="layui-form-label">领取有效期：</label>
        <div><input type="radio" name="validity_type" value="0" lay-filter="filter" disabled {if $goodscoupon_type_info.validity_type == 0} checked="checked" {/if} title="至活动结束时失效"></div>
        <div class="layui-input-inline">
            <div class="layui-input-inline">
                <input type="radio" name="validity_type" value="1" {if $goodscoupon_type_info.validity_type == 1} checked="checked" {/if} lay-filter="filter" disabled title="领取之日起" class="layui-input-inline">
            </div>
            <input type="number" disabled min="1" max="365" value="{$goodscoupon_type_info.fixed_term}" name="fixed_term" lay-verify="days" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
            <span class="layui-form-mid">天有效</span>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label img-upload-lable">优惠券图片：</label>
        <div class="layui-input-inline img-upload">
            <input type="hidden" class="layui-input" name="image" value="{$goodscoupon_type_info.image}" />
            <div class="upload-img-block icon">
                <div class="upload-img-box" id="goodscouponImg">
                    {if condition="$goodscoupon_type_info.image"}
                    <img src="{:img($goodscoupon_type_info.image)}" />
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活动开始时间：</label>
        <div class="layui-input-inline">{$goodscoupon_type_info.start_time}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">活动结束时间：</label>
        <div class="layui-input-inline">{$goodscoupon_type_info.over_time}</div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">用券弹窗提醒：</label>
        <div class="layui-input-inline">{if $goodscoupon_type_info.use_remind == 0} 关闭 {else/} 开启 {/if}</div>
    </div>

    <div class="ns-form-row">
        <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var goodscoupon_type_id = {$goodscoupon_type_info['goodscoupon_type_id']};
    console.log(goodscoupon_type_id,'sdfsd');
    layui.use(['laydate', 'form', 'upload'], function() {
        var form = layui.form,
            upload = layui.upload;

        // 图片上传
        var uploadInst = upload.render({
            elem: '#goodscouponImg',
            url: ns.url("admin/upload/upload"),
            done: function(res) {
                if (res.code >= 0) {
                    save(res.data.pic_path);
                }else{
                    return layer.msg(res.message);
                }
            }
        });

        function save(image_url){

            $.ajax({
                url: ns.url("goodscoupon://admin/goodscoupon/editImg"),
                data: {goodscoupon_type_id:goodscoupon_type_id,image:image_url},
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    if (res.code == 0) {
                        $("input[name='image']").val(image_url);
                        $("#goodscouponImg").html("<img src=" + ns.img(image_url) + " >");
                    }
                    layer.msg(res.message);
                }
            });
        };
    });
    function back() {
        location.href = ns.url("goodscoupon://admin/goodscoupon/lists");
    }
</script>
{/block}