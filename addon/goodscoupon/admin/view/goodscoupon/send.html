{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .layui-layer-page .layui-layer-content { padding: 20px 30px; }
</style>
{/block}
{block name="main"}


<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show" id="formData">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="请输入手机号" id="mobile" autocomplete="off" class="layui-input">
                    </div>
                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="batchSearch">多用户搜索</button>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">注册时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_time"  id="start_time" autocomplete="off" placeholder="" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_time" id="end_time" autocomplete="off" placeholder="" readonly>
                        <i class="ns-calendar"></i>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">用户类型：</label>
                    <div class="layui-input-inline">
                        <select name="member_type" lay-filter="member_type">
                            <option value="">全部</option>
                            <option value="1">普通用户</option>
                            <option value="2">平台店主(VIP)</option>
                            <option value="3">平台董事</option>
                            <option value="4">平台经理</option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">是否已领：</label>
                    <div class="layui-input-inline">
                        <select name="receive_status" lay-filter="">
                            <option value="">全部</option>
                            <option value="1">未领券</option>
                            <option value="2">已领券</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">用户在近  </label>
                <input type="text" name="near_days" autocomplete="off" class="layui-input-inline layui-input align-center" style="padding: 0; text-align: center;">
                <div class="layui-input-inline">天内，</div>
                <div class="layui-input-inline">
                    <select name="user_behavior" lay-filter="user_behavior">
                        <option value="">请选择用户行为</option>
                        <option value="1">打开小程序</option>
                        <option value="2">购买商品</option>
                        <option value="3">无购买商品</option>
                    </select>
                </div>
                <input type="text" id="times" name="times" autocomplete="off" class="layui-input-inline layui-input align-center" style="padding: 0; text-align: center;">
                <div class="layui-input-inline">次</div>
            </div>

            <div class="ns-form-row">
                <input type="hidden" name="goodscoupon_type_id" id="goodsCouponTypeId" value="{$goodsCouponTypeId}">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">搜索</button>
            </div>
        </form>
    </div>
</div>

<br />
<div>
    <button class="layui-btn ns-bg-color" id="sendToSelected">向选中用户发券</button> &nbsp;
    <div class="layui-inline" style="color: red;">
        当前可发券数量 <span id="canSendCount_1">{if $goodsCouponTypeInfo['count'] == 0}不限{else/}{$goodsCouponTypeInfo['count']-$goodsCouponTypeInfo['lead_count']}{/if}</span>, 已选中待发券用户
        <span id="bottomSelectedCount">0</span> 人
    </div>
</div>

<br />
<div>
    <button class="layui-btn ns-bg-color" id="sendToSearched">向筛选用户发券</button> &nbsp;
    <div class="layui-inline" style="color: red;">
        当前可发券数量 <span id="canSendCount_2">{if $goodsCouponTypeInfo['count'] == 0}不限{else/}{$goodsCouponTypeInfo['count']-$goodsCouponTypeInfo['lead_count']}{/if}</span>, 筛选待发券用户 <span id="bottomSearchedCount">0</span> 人
    </div>
</div>

<input type="hidden" name="goodsCouponTypeId" value="{$goodsCouponTypeId}">

<div class="layui-tab ns-table-tab"  lay-filter="goodscoupon_tab">
    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="goodscoupon_list" lay-filter="goodscoupon_list"></table>
    </div>
</div>

<script type="text/html" id="selectToolBar">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="select">全部选中</a>
        <a class="layui-btn" lay-event="unSelect">全部取消 (<span id="selectedCount">0</span>)</a>
    </div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="receive">选择</a>
    </div>
</script>

<script type="text/html" id="batchMobileContent">
    <div>
        <span style="color: red;">可输入多个手机号搜索，每个手机号以换行分隔。如手机号非平台用户将无法搜索到；如果重复手机号则视为一个用户</span>
        <textarea name="batchMobile" id="batchMobile" cols="130" rows="28" style="resize: none;"></textarea>
    </div>
</script>
{/block}
{block name="script"}
<script>
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element,
            laydate = layui.laydate,
            validityType = 0,
            selectedBox = [],
            currentPageMemberId = [],
            currentPage = 1,
            sendToSelectedRepeatFlag = false,
            sendToSearchedRepeatFlag = false,
            sendToSearchedBtnOpenStatus = false,
            batchMobile = '',
            repeat_flag = false; //防重复标识

        form.render();

        //渲染时间
        laydate.render({
            elem: '#start_time',
            type: 'date'
        });

        laydate.render({
            elem: '#end_time',
            type: 'date'
        });

        //监听Tab切换，以改变地址hash值
        element.on('tab(goodscoupon_tab)', function(){
            table.reload({
                page: {curr: 1},
                where: {'status': this.getAttribute('lay-id')},
            })
        });

        table = new Table({
            elem: '#goodscoupon_list',
            // url: ns.url("goodscoupon://admin/goodscoupon/lists"),
            data:[],
            toolbar: "#selectToolBar",
            cols: [
                [
                    {
                        field: 'member_id',
                        title: 'ID',
                        unresize: 'false',
                        align: 'center',
                        width: '10%',
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        align: 'center',
                        unresize: 'false',
                        width: '15%',
                    },
                    {
                        field: 'site_name',
                        title: '当前锁定店铺',
                        unresize: 'false',
                        align: 'center',
                        width: '15%',
                    },
                    {
                        field: 'parent_name',
                        title: '注册推荐人',
                        align: 'center',
                        unresize: 'false',
                        width: '20%',
                    },
                    {
                        field: 'reg_time',
                        title: '注册时间',
                        align: 'center',
                        unresize: 'false',
                        width: '20%',
                    },
                    {
                        title: '操作',
                        unresize: 'false',
                        width: '20%',
                        align: 'center',
                        templet: function(data) {
                            let index = selectedBox.indexOf(data.member_id);
                            if (index === -1) {
                                var btn = "<a class='a-class' lay-event='selectSingle'>选择</a>";
                            } else {
                                var btn = "<a class='a-class' lay-event='unSelectSingle'>取消选择</a>";
                            }
                            return btn;
                        }
                    }
                ]
            ],
            parseData: function(data){
                sendToSearchedBtnOpenStatus = true;
                return {
                    "code": data.code,
                    "msg": data.message,
                    "count": data.data.count,
                    "data": data.data.list,
                    'goodsCouponTypeInfo': data.data.goodsCouponTypeInfo,
                };
            },
            callback: function(res, curr, count)
            {
                for (let i = 0; i < res.data.length; i++) {
                    currentPageMemberId.push(res.data[i].member_id);
                }
                currentPage = curr;
                $('#selectedCount').html(selectedBox.length);
                $('#bottomSelectedCount').html(selectedBox.length);
                $('#bottomSearchedCount').html(count);
                let sendCountStr = 0;
                if (res.goodsCouponTypeInfo != undefined) {
                    if (res.goodsCouponTypeInfo.count == 0) {
                        sendCountStr = '不限';
                    } else {
                        sendCountStr = res.goodsCouponTypeInfo['count'] - res.goodsCouponTypeInfo.lead_count;
                    }
                    $('#canSendCount_1, #canSendCount_2').html(sendCountStr);
                }
            }
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'selectSingle':
                    selectedBox.push(obj.data.member_id);
                    break;
                case 'unSelectSingle':
                    let index = selectedBox.indexOf(obj.data.member_id);
                    selectedBox.splice(index, 1);
                    break;
            }
            tableReload();
        });

        table.toolbar(function(obj) {
            // var id_array = new Array();
            // for (i in obj.data) id_array.push(obj.data[i].goods_id);
            switch (obj.event) {
                case 'select':
                    if (currentPageMemberId.length === 0) return false;
                    currentPageMemberId.forEach(function(value) {
                        let index = selectedBox.indexOf(value);
                        if (index === -1) {
                            selectedBox.push(value);
                        }
                    });
                    break;
                case 'unSelect':
                    if (selectedBox.length === 0) return false;
                    selectedBox = [];
                    break;
            }

            tableReload();
        });

        // 搜索
        form.on('submit(search)', function(data) {
            currentPageMemberId = [];
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field,
                url: ns.url("goodscoupon://admin/goodscoupon/sendPageData"),
            });
            selectedBox = [];
            return false;
        });

        function tableReload()
        {
            table.reload({
                page: {
                    curr: currentPage
                },
                where: getUrlVars($('#formData').serialize()),
                url: ns.url("goodscoupon://admin/goodscoupon/sendPageData"),
            });
        }


        $('#sendToSelected').on('click', function() {
            if (selectedBox.length >= 5000) {
                alert('超过最大限制发送数量: 5000!');
                return false;
            }

            if (sendToSelectedRepeatFlag) return false;
            sendToSelectedRepeatFlag = true;

            if (selectedBox.length <= 0) {
                alert('请选择用户!');
                sendToSelectedRepeatFlag = false;
                return false;
            }

            let goodsCouponTypeId = $('#goodsCouponTypeId').val();

            var loading = layer.load(1, {
                shade: [0.2, '#fff'],
                content: '发送中, 请等待',
                success: function (layerContentStyle) {
                    layerContentStyle.find('.layui-layer-content').css({
                        'padding-top': '65px',
                        'text-align': 'center',
                        'width': '120px'
                    });
                    layerContentStyle.find('.layui-layer-loading1').css({
                        'background': 'url(/public/static/ext/layui/css/modules/layer/default/loading-1.gif) no-repeat center',
                    });
                }
            });

            $.ajax({
                url: ns.url("goodscoupon://admin/goodscoupon/sendToSelectedMember"),
                type: 'post',
                dataType: 'json',
                data: {member_id: selectedBox, goodscoupon_type_id: goodsCouponTypeId},
                success: function(res) {
                    if (res.code < 0) {
                        console.log('发送失败, ' + res.message);
                        return false;
                    }
                },
                complete: function(res) {
                    sendToSelectedRepeatFlag = false;
                    layer.close(loading);
                    layer.msg(res.responseJSON.message + '<br />已发送: ' + res.responseJSON.data.send_count + ' 个用户', {time: 2000}, function () {
                        selectedBox = [];
                        tableReload();
                    })
                }
            });
        })


        $('#sendToSearched').on('click', function() {
            if (!sendToSearchedBtnOpenStatus) {
                alert('请先进行筛选!');
                return false;
            }

            if ($('#bottomSearchedCount').html() >= 5000) {
                alert('超过最大限制发送数量: 5000!');
                return false;
            }

            if (sendToSearchedRepeatFlag) return false;
            sendToSearchedRepeatFlag = true;

            var loading = layer.load(1, {
                shade: [0.2, '#fff'],
                content: '发送中, 请等待',
                success: function (layerContentStyle) {
                    layerContentStyle.find('.layui-layer-content').css({
                        'padding-top': '65px',
                        'text-align': 'center',
                        'width': '120px'
                    });
                    layerContentStyle.find('.layui-layer-loading1').css({
                        'background': 'url(/public/static/ext/layui/css/modules/layer/default/loading-1.gif) no-repeat center',
                    });
                }
            });

            $.ajax({
                url: ns.url("goodscoupon://admin/goodscoupon/sendToSearchedMember"),
                type: 'post',
                dataType: 'json',
                data: getUrlVars($('#formData').serialize()),
                success: function(res) {
                    if (res.code < 0) {
                        console.log('发送失败, ' + res.message);
                        return false;
                    }
                },
                complete: function(res) {
                    sendToSearchedRepeatFlag = false;
                    layer.close(loading);
                    layer.msg(res.responseJSON.message + '<br />已发送: ' + res.responseJSON.data.send_count + ' 个用户', {time: 2000}, function () {
                        selectedBox = [];
                        tableReload();
                    })
                }
            });
        })

        form.on('select(user_behavior)', function(data){
            if (data.value == 3) {
                $('#times').val('');
                $('#times').attr("disabled", true);
            } else {
                $('#times').attr("disabled", false);
            }
        });


        form.on('submit(batchSearch)', function() {
            let batchMobileTemplate = layer.open({
                title: "多用户搜索",
                type: 1,
                area: ['1000px', '600px'],
                fixed: false, //不固定
                btn: ['保存', '返回'],
                content: $('#batchMobileContent').html(),
                success: function() {
                    if ($('#mobile').val()) {
                        $('#batchMobile').val($('#mobile').val().replace(/,/g,"\r"));
                    }
                },
                yes: function() {
                    if (!$('#batchMobile').val()) {
                        $('#mobile').attr("readonly", false);
                        $('#mobile').val('');
                        layer.close(batchMobileTemplate);
                        return false;
                    }

                    let batchMobileVal = $('#batchMobile').val();
                    batchMobileVal = batchMobileVal.replace(/\ +/g,"");
                    batchMobileVal = batchMobileVal.replace(/[\r\n]/g,",");

                    $('#mobile').val(batchMobileVal);
                    $('#mobile').attr("readonly", true);
                    layer.close(batchMobileTemplate);
                }
            });

            return false;
        });

    });

    function getUrlVars(url) {
        var hash;
        var myJson = {};
        var hashes = url.slice(url.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            myJson[hash[0]] = hash[1];
        }
        return myJson;
    }
</script>
{/block}