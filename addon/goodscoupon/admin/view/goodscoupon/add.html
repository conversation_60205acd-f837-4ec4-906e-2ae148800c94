{extend name="app/admin/view/base.html"/}
{block name="resources"}
<!--<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />-->
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/lay/modules/dtree/dtree.css">
<link rel="stylesheet" href="__STATIC__/ext/layui/lay/modules/dtree/font/dtreefont.css">
<script src="__STATIC__/js/xm-select.js"></script>

<style>
    .ns-form {
        /*margin-left: 80px;*/
        width: 800px;
    }
    /*.ns-form .layui-form-label{width: 120px;}*/
    /*.ns-form .layui-input-block{margin-left: 120px;}*/
    /*.ns-form .layui-input-block.block-style2{*/
    /*    margin: 10px 0;*/
    /*    border: 1px solid #f1f1f1;*/
    /*    padding: 20px;*/
    /*}*/
    .ns-form .layui-input-block.block-style2 > div{
        line-height: 18px;
        margin: 5px 0;
    }
    .ns-form .layui-input-block.block-style2 .title{
        border-left: 5px solid #4685FD;
        padding-left: 6px;
        margin-left: -10px;
    }
    .ns-form .layui-input-block.block-style2 .title span{
        color: red;
    }
    .ns-form .layui-input-block.block-style2 .desc{
        color: #aaa;
    }
    .ns-form .layui-input-block.block-style2 .desc span{
        color: black;
    }
    .layui-layer-content{
        overflow: auto!important;
    }
    #chooseCategory{
        min-height: 36px;
        line-height: 36px;
        order: 1px solid #E6E6E6;
        border-radius: 2px;
        margin-left: 134px;
        cursor: pointer;
    }
    .layui-badge{
        margin-right: 5px;
    }
</style>
{/block}
{/block}
{block name="main"}

<div class="layui-form ns-form">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>优惠券名称：</label>
		<div class="layui-input-block">
			<input type="text" name="goodscoupon_name" lay-verify="required" autocomplete="off" class="layui-input ns-len-long">
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>优惠政策：</label>
        <div class="layui-input-block nc-len-mid">
            <div class="layui-input-inline">
                <div class="layui-input-inline"><input type="radio" checked></div>
                <div class="layui-input-inline">满</div><input type="number" name="at_least" lay-verify="required|number|money|gtzero" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
                <div class="layui-input-inline">减</div><input type="number" name="money" lay-verify="required|number|money|gtzero" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
            </div>
            <span class="layui-form-mid">元</span>
        </div>
        <div class="ns-word-aux">
            <p>价格不能小于0，可保留两位小数</p>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>是否公开：</label>
        <div class="layui-input-block nc-len-mid">
            <input type="radio" name="privacy_status" value="1" lay-filter="filter" checked="checked" title="公开券">
            <input type="radio" name="privacy_status" value="0" lay-filter="filter" title="内部券" class="layui-input-inline">
        </div>
        <div class="ns-word-aux">
            <p>选择内部券后, 不支持用户自主领券, 只能后台主动发券</p>
        </div>
    </div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>发放总数量：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				<input type="number" name="count" min="0" lay-verify="required|number" autocomplete="off" class="layui-input ns-len-short">
			</div>
		</div>
		<div class="ns-word-aux">
			<p>设置为0时，可无限领取</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>用户领取限制：</label>
		<div class="layui-input-block">
            <span class="layui-input-inline">每个用户可最多可领取</span>
			<div class="layui-input-inline">
				<input type="number" name="max_fetch" min="0" value="" lay-verify="required|number|max" autocomplete="off" class="layui-input ns-len-short">
			</div>
		</div>
		<div class="ns-word-aux">
			<p>数量不能小于0，且必须为整数；设置为0时，可无限领取</p>
		</div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <span class="layui-input-inline">仅指定标签用户可领取</span>
            {include file="app/admin/view/member/member_tag_component.html"}
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>使用范围：</label>
        <div class="layui-input-block">
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="1" title="全场通用" checked>
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">秒杀及拼团商品除外</span>
            </div>
            <div>
                <div class="layui-input-inline">
                    <input  type="radio" name="use_scenario" lay-filter="use_scenario" value="2" title="指定类目可用">
                </div>
                <p id="chooseCategory" class="layui-input">

                </p>
                <input type="hidden"  readonly name="category_name" autocomplete="off" placeholder="选择分类" class="layui-input ns-len-mid" />
                <input type="hidden" name="category_id" />
            </div>
            <div>
                <div class="layui-input-inline">
                    <div class="layui-input-inline"><input  type="radio" name="use_scenario" lay-filter="use_scenario" value="5" title="指定专区可用"></div>
                    <div class="content layui-input-inline" style="width: 400px;">
                        <div id="topic_list"></div>
                    </div>
                </div>
            </div>
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="3" title="指定商品可用">
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">创建后在优惠券列表页对应活动的“管理商品”中添加参与优惠的商品</span>
            </div>
            <div>
                <input class="layui-input-inline" type="radio" name="use_scenario" lay-filter="use_scenario" value="4" title="指定商品不可用">
                <span style="color:#B2B2B2; font-size:12px; line-height:1.6;">创建后在优惠券列表页对应活动的“管理商品”中添加不参与优惠的商品</span>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>领取有效期：</label>
        <div><input type="radio" name="validity_type" value="0" lay-filter="filter" checked="checked" title="至活动结束时失效"></div>
        <div class="layui-input-inline">
            <div class="layui-input-inline">
                <input type="radio" name="validity_type" value="1" lay-filter="filter" title="领取之日起" class="layui-input-inline">
            </div>
            <input type="number" min="1" max="365" value="" name="fixed_term" lay-verify="validityDays" autocomplete="off" class="layui-input-inline layui-input ns-len-short">
            <span class="layui-form-mid">天有效</span>
        </div>
    </div>

	<div class="layui-form-item">
		<label class="layui-form-label img-upload-lable"><span class="required">*</span>优惠券图片：</label>
		<div class="layui-input-block img-upload">
			<input type="hidden" class="layui-input" name="image" lay-verify="image"/>
			<div class="upload-img-block">
				<div class="upload-img-box" id="goodscouponImg">
					<div class="ns-upload-default">
						<img src="SHOP_IMG/upload_img.png" />
						<p>点击上传</p>
					</div>
				</div>
			</div>
		</div>
		<div class="ns-word-aux">
			<p>建议尺寸：702*410像素</p>
			<p>图片上传默认不限制大小</p>
		</div>
	</div>

    <div class="layui-form-item ns-end-time">
        <label class="layui-form-label"><span class="required">*</span>活动开始时间：</label>
        <div class="layui-input-block">
            <input type="text" name="start_time" id="start_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
        </div>
        <div class="ns-word-aux">
            <p>活动开始后，用户才能领取优惠券</p>
        </div>
    </div>

	<div class="layui-form-item ns-end-time">
		<label class="layui-form-label"><span class="required">*</span>活动结束时间：</label>
		<div class="layui-input-block">
			<input type="text" name="over_time" lay-verify="overTime" id="over_time" class="layui-input ns-len-mid" autocomplete="off" readonly>
		</div>
        <div class="ns-word-aux">
            <p>如果活动到期结束，已被领取的优惠券也同步过期</p>
        </div>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>用券弹窗提醒：</label>
        <div class="layui-input-block nc-len-mid">
            <input type="radio" name="use_remind" value="0" lay-filter="filter" checked="checked" title="关闭">
            <input type="radio" name="use_remind" value="1" lay-filter="filter" title="开启" class="layui-input-inline">
        </div>
        <div class="ns-word-aux">
            <p>开启提醒功能，用户领取该券后，如未到期且未使用，每次进入小程序时，系统均会弹窗提示使用该券。</p>
        </div>
    </div>

	<div class="ns-form-row">
		<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
    let topicList = {:json_encode($topicList)};
    let choosedCategoryList = [];
    var category_id_name_dict = {};
    var tmp_param_list= [];
    layui.extend({"dtree":"__STATIC__/ext/layui/lay/modules/dtree/dtree"}).use(['dtree','laydate', 'form', 'upload'], function() {
		var form = layui.form,
			upload = layui.upload,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
			currentDate = new Date();  //当前时间
		form.render();
        var dtree = layui.dtree;

		currentDate.setDate(currentDate.getDate() + 10);  //10天后的日期

		// 时间模块
		laydate.render({
			elem: '#over_time', //指定元素
			type: 'datetime',
		});

        // 时间模块
        laydate.render({
            elem: '#start_time', //指定元素
            type: 'datetime',
        });


        let richang = xmSelect.render({
            el: '#topic_list',
            name:'topic_list',
            language: 'zn',
            theme: {
                color: '#4685FD',
            },
            data: topicList,
        })


        /**
		 * 表单监听提交
		 */
		form.on('submit(save)', function(data) {
			if (data.field.is_show == undefined) {
				data.field.is_show = 0;
			}

			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				url: ns.url("goodscoupon://admin/goodscoupon/add"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
                            closeBtn: false,
							btn: ['返回列表', '继续添加'],
							yes: function(){
								location.href = ns.url("goodscoupon://admin/goodscoupon/lists")
							},
							btn2: function() {
								location.href = ns.url("goodscoupon://admin/goodscoupon/add")
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		// 图片上传
		var uploadInst = upload.render({
			elem: '#goodscouponImg',
			url: ns.url("admin/upload/upload"),
			done: function(res) {
				if (res.code >= 0) {
					$("input[name='image']").val(res.data.pic_path);
					$("#goodscouponImg").html("<img src=" + ns.img(res.data.pic_path) + " >");
				}
				return layer.msg(res.message);
			}
		});


		form.verify({
			days: function(value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return '请输入整数';
				}
			},
			number: function (value) {
				if (parseFloat(value) < 0) {
					return '请输入大于或等于0的数!'
				}
			},
			int: function (value) {
				if (value%1 != 0) {
					return '请输入整数!'
				}
				if (value < 0) {
					return '请输入大于0的数!'
				}
			},
			money: function (value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位'
				}
			},
			overTime: function(value) {
                var now_time = (new Date()).getTime();
                var end_time = (new Date(value)).getTime();
                var start_time = $('#start_time').val();
                startTime = (new Date(start_time)).getTime();
                if (!value) {
                    return '活动结束时间不能为空!'
                }
                if (startTime >= end_time) {
                    return '开始时间不能大于结束时间!'
                }
                if (now_time > end_time) {
                    return '结束时间不能小于当前时间!'
                }

				// var now_time = (new Date()).getTime();
				// var end_time = (new Date(value)).getTime();
                // if (!value) {
                //     return '活动结束时间不能为空!'
                // }
				// if (now_time > end_time) {
				// 	return '结束时间不能小于当前时间!'
				// }
			},
            // startTime: function(value) {
            //     var now_time = (new Date()).getTime();
            //     var end_time = (new Date(value)).getTime();
            //     if (!value) {
            //         return '活动结束时间不能为空!'
            //     }
            //     if (now_time > end_time) {
            //         return '结束时间不能小于当前时间!'
            //     }
            // },
			gtzero: function(value) {
				if (parseFloat(value) <= 0) {
					return '请输入大于0的数!'
				}
			},
            image: function(value) {
                if (!value) {
                    return '请上传图片!';
                }
            },
            validityDays: function(value) {
			    var validityType = $('input[name="validity_type"]:checked').val();
                if (validityType == 1) {
                    if (!value || value < 1) {
                        return '请输入有效天数(不能为0)!';
                    }
                }
            },
		});

        function handleCategoryList(categoryTreeList,full_category_name) {
            let node_list = []
            for (let i = 0; i < categoryTreeList.length; i++) {
                let node = {"children":[]};
                node.id = categoryTreeList[i].category_id.toString();
                node.title = categoryTreeList[i].category_name;
                tmp_category_name = full_category_name ? full_category_name+'/'+categoryTreeList[i].category_name : categoryTreeList[i].category_name
                category_id_name_dict[categoryTreeList[i].category_id.toString()] = tmp_category_name;
                node.parentId = categoryTreeList[i].pid.toString();
                node.checkArr = {"type": "0", "checked": "0"};
                if(categoryTreeList[i].child_list && categoryTreeList[i].child_list.length>0){
                    node.children = handleCategoryList(categoryTreeList[i].child_list,tmp_category_name);
                }
                node_list.push(node);
            }
            return node_list;
        }

        function renderCategoryInput(){
            $("input[name='category_id']").val(choosedCategoryList.map(function (item) {
                return item.category_id;
            }).join(','));
            $("input[name='category_name']").val(choosedCategoryList.map(function (item) {
                return item.category_name;
            }).join(','));
            if(choosedCategoryList.length>0){
                let category_name_html ='';
                for (let i = 0; i < choosedCategoryList.length; i++) {
                    category_name_html+='<span class="layui-badge layui-bg-blue">'+choosedCategoryList[i].category_name+'</span>'
                }
                $('#chooseCategory').html(category_name_html);
            }else{
                $('#chooseCategory').html("请选择分类")
            }
        }
        renderCategoryInput();

        $("#chooseCategory").on('click',function(){
            $.ajax({
                url: ns.url("goodscoupon/admin/goodscoupon/getAllCatgory"),
                data: {},
                dataType: 'json',
                type: 'post',
                success: function (res) {
                    if(res.code==0){
                        let category_list = handleCategoryList(res.data);
                        console.log('category_list',category_list)
                        layer.open({
                            type: 1,  //type:0 也行
                            title: "选择商品分类",
                            area: ["400px", "500px"],
                            content: '<ul id="categoryTree" class="dtree" data-id="0"></ul>',
                            btn: ['确认选择'],
                            success: function(layero, index){
                                var DTree = dtree.render({
                                    elem: "#categoryTree",
                                    initLevel:1,
                                    data:category_list,
                                    checkbar: true,
                                    checkbarData: "choose", // 记录选中（默认）， "change"：记录变更， "all"：记录全部， "halfChoose"："记录选中和半选（V2.5.0新增）"
                                    checkbarType: "no-all", // 默认就是all，其他的值为： no-all  p-casc   self  only
                                    done: function(res, $ul, first){
                                        if(first && tmp_param_list.length>0) {
                                            // 初始化选中
                                            dtree.chooseDataInit("categoryTree",tmp_param_list.map(function (item) {
                                                return item.nodeId;
                                            }).join(','));
                                            // 反选半选状态
                                            dtree.initNoAllCheck("categoryTree");
                                        }
                                    }
                                });
                            },
                            yes: function(index, layero) {
                                tmp_param_list = dtree.getCheckbarNodesParam("categoryTree");
                                console.log('tmp_param_list', tmp_param_list)
                                var choose_list = tmp_param_list.map(function (item) {
                                    return item.nodeId
                                })
                                choose_list = Array.from(new Set(choose_list))
                                choosedCategoryList = tmp_param_list.filter(function (item) {
                                    if(parseInt(item.level)>1){
                                        return !choose_list.includes(item.parentId)
                                    }
                                    return true
                                }).map(function (item) {
                                    item.category_id = item.nodeId;
                                    item.category_name = category_id_name_dict[item.nodeId];
                                    return item;
                                })
                                renderCategoryInput()
                                layer.close(index);
                            }
                        });
                    }
                }
            })


        });
	});

	function back() {
		location.href = ns.url("goodscoupon://admin/goodscoupon/lists");
	}
</script>
<!--<script src="ADMIN_JS/goods_coupon_edit.js?v=1"></script>-->
{/block}
