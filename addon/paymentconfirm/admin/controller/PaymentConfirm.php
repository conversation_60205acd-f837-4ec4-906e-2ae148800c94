<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\paymentconfirm\admin\controller;

use app\admin\controller\BaseAdmin;
use addon\paymentconfirm\model\PaymentConfirm AS PaymentConfirmModel;
use think\facade\Db;
use think\facade\Log;

/**
 * 支付确认 控制器
 */
class PaymentConfirm extends BaseAdmin
{
    public $paymentContirmModel;

    public function __construct()
    {
        parent::__construct();
        $this->paymentContirmModel = new PaymentConfirmModel();
    }

//    public function list()
//    {
//        $outTradeNo = input("out_trade_no",'');
//        $status = input('status', 0);
//        $startTime = input("start_time", '');
//        $endTime = input("end_time", '');
//        $orderNo = input("order_no", '');
//        if (request()->isAjax()) {
//            $page = input('page', 1);
//            $page_size = input('page_size', PAGE_LIST_ROWS);
//            $join = [];
//            $where = [];
//
//            if ($outTradeNo) {
//                $where[] = ['p.out_trade_no', '=', $outTradeNo];
//            }
//
//            if ($status == 0) {
//                $where[] = Db::raw('p.pay_confirm_obj is null');
//            } else {
//                $where[] = Db::raw('p.pay_confirm_obj is not null');
//            }
//
//            if ($startTime) {
//                $where[] = ['p.pay_time', '>=', date_to_time($startTime)];
//            }
//
//            if ($endTime) {
//                $where[] = ['p.pay_time', '<=', date_to_time($endTime)];
//            }
//
//            $join[] = [
//                'pay_refund pr',
//                'pr.out_trade_no = p.out_trade_no',
//                'left'
//            ];
//
//            if ($orderNo) {
//                $join[] = [
//                    'order o',
//                    'o.out_trade_no = p.out_trade_no',
//                    'left'
//                ];
//                $where[] = ['o.order_no', '=', $orderNo];
//            }
//
//            $list = $this->paymentContirmModel->getList($where, $page, $page_size, $join);
//            return $list;
//        } else {
//            $this->assign('out_trade_no', $outTradeNo);
//            return $this->fetch("payment/list");
//        }
//
//    }




    public function list()
    {
        $outTradeNo = input("out_trade_no",'');
        $status = input('status', 0);
        $startTime = input("start_time", '');
        $endTime = input("end_time", '');
        $orderNo = input("order_no", '');
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $join = [];
            $where = [];

            if ($outTradeNo) {
                $where[] = ['p.out_trade_no', '=', $outTradeNo];
            }

            if ($status == 0) {
                $where[] = Db::raw('p.pay_confirm_obj is null');
            } else {
                $where[] = Db::raw('p.pay_confirm_obj is not null');
            }

            if ($startTime) {
                $where[] = ['p.pay_time', '>=', date_to_time($startTime)];
            }

            if ($endTime) {
                $where[] = ['p.pay_time', '<=', date_to_time($endTime)];
            }

            if ($orderNo) {
                $where[] = ['o.order_no', '=', $orderNo];
            }

            if ($status == 0) {
                $list = $this->paymentContirmModel->getList($where, $page, $page_size, $join);
            } else {
                $join[] = [
                    'pay_refund pr',
                    'pr.out_trade_no = p.out_trade_no',
                    'left'
                ];
                $join[] = [
                    'order o',
                    'o.out_trade_no = p.out_trade_no',
                    'left'
                ];
                $list = $this->paymentContirmModel->getConfirmList($where, $page, $page_size, $join);
            }

            return $list;
        } else {
            $this->assign('out_trade_no', $outTradeNo);
            return $this->fetch("payment/list");
        }

    }


    public function confirm() {
        $paymentIds = input('payment_ids', []);
        if (empty($paymentIds)) throw new \Exception('请选择要确认的支付数据');
        foreach ($paymentIds as $paymentId) {
            $outTradeNo = model('pay')->getValue(['id' => $paymentId], 'out_trade_no');
            try {
                $this->paymentContirmModel->getOrCreatePaymentConfirmObj($outTradeNo);
            } catch (\Exception $e) {
                Log::error($e->getCode());
                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());
                return json(['code' => '-1', 'message' => '操作失败 outTradeNo='.$outTradeNo]);
            }
        }
        return json(['code' => '0', 'message' => '确认成功']);
    }
}
