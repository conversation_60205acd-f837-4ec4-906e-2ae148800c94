{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<div class="layui-form">
<!--		<div class="layui-input-inline">-->
<!--            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>-->
<!--                <i class="layui-icon">&#xe615;</i>-->
<!--            </button>-->
<!--			<input type="text" name="order_no" id="order_no" placeholder="订单号" value="{$orderNo ?? ''}" class="layui-input" autocomplete="off">-->
<!--		</div>-->
		<div class="layui-input-inline">
			<input type="text" name="out_trade_no" id="out_trade_no" value="{$out_trade_no ?? ''}" placeholder="流水号" class="layui-input" autocomplete="off">
			<i class="ns-calendar"></i>
		</div>
		<div class="layui-input-inline">
			<input type="text" name="start_time" id="start_time" placeholder="开始时间" class="layui-input" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>
		<div class="layui-input-inline end-time">
			<input type="text" name="end_time" id="end_time" placeholder="结束时间" class="layui-input" autocomplete="off" readonly>
			<i class="ns-calendar"></i>
		</div>

		<div class="layui-input-inline">
            <input type="text" name="order_no" id="order_no" placeholder="订单号" value="{$orderNo ?? ''}" class="layui-input" autocomplete="off">
<!--			<input type="text" name="bargain_name" placeholder="请输入活动名称" class="layui-input" autocomplete="off">-->
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>





<div class="layui-tab ns-table-tab" lay-filter="payment_tab">
	<div class="layui-tab-content">
		<ul class="layui-tab-title">
			<li class="layui-this" data-status="0">未确认</li>
			<li data-status="1">已确认</li>
		</ul>
		<table id="payment_list" lay-filter="payment_list"></table>
	</div>
</div>

<script type="text/html" id="operation">
	<div class="ns-table-btn">
		{{# if(!d.pay_confirm_obj) { }}
		<a class="layui-btn" lay-event="confirm">确认</a>
		{{# } }}
	</div>
</script>



<script type="text/html" id="topToolbar">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-sm" lay-event="confirmAll">确认已勾选</button>
	</div>
</script>

{/block}

{block name="script"}
<script>
	var outTradeNo = '{$out_trade_no}';
	layui.use(['form', 'element', 'laytpl', 'laydate'], function() {
		var table,
			form = layui.form,
			element = layui.element,
            laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

		//监听Tab切换，以改变地址hash值
		element.on('tab(payment_tab)', function() {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('data-status')
				}
			});
		});

        //开始时间
        laydate.render({
            elem: '#start_time' //指定元素
            ,done: function(value, date, endDate){
                start_time = ns.date_to_time(value);

            }
        });
        //结束时间
        laydate.render({
            elem: '#end_time' //指定元素
            ,done: function(value, date, endDate){
                end_time = ns.date_to_time(value);
            }
        });

		table = new Table({
			elem: '#payment_list',
			url: ns.url("paymentconfirm://admin/PaymentConfirm/list"),
			toolbar:'#topToolbar',
			where: {out_trade_no: outTradeNo},
			cols: [[
				{
					checkbox: true,
					width: '5%',
				}
				,{
                    field: 'out_trade_no',
					title: '支付流水号',
					width: '20%',
					templet: function(data) {
                    	return '<a target="_blank" href="'+ ns.url("admin/order/lists") +'#!out_trade_no='+ data.out_trade_no +'&page=1">'+ data.out_trade_no +'</a>' + (data.refund_status_name ? '<br />' + data.refund_status_name : '') ;
					}
				}
				,{
					field: 'pay_type',
					title: '支付方式',
					width: '10%',
					templet: function(data) {
						var typeCn = {'adapay': '汇付天下'};
						return typeCn[data.pay_type];
					}
				}
				,{
                    field: 'trade_no',
                    title: '第三方流水号',
					width: '30%',
                }
                ,{
			    	field:'pay_money',
                    title: '金额',
					sort: true,
					width: '10%'
                }
                ,{
					field:'pay_timestamp',
					title: '支付时间',
					sort: true,
					width: '20%',
				}
				,{
					title: '操作',
					toolbar: '#operation',
					width: '10%'
				}
			]],
			done:function(res){

				tdTitle();

			}
		});

		/**
		 * 监听table行工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
                case 'detail': //详情
                    location.href = ns.url("bargain://admin/bargain/detail", {"bargain_id": data.bargain_id});
                    break;
				case 'confirm': //删除
					confirmSingle(data.id);
					break;
			}
		});


		/**
		 * 监听table头部工具栏
		 */
		table.on('toolbar', function(obj) {
			var checkStatus = table.checkStatus(obj.config.id);
			switch (obj.event) {
				case 'confirmAll':
					confirmAll();
					break;
			}
		});


        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

		/**
		 * 删除
		 */
		function confirmSingle(paymentId) {
			console.log($(".layui-laypage-em").next());
			layer.confirm('确定要进行此支付确认吗?', function() {
				var loading = layer.load(2);

				$.ajax({
					url: ns.url("paymentconfirm://admin/PaymentConfirm/confirm"),
					data: {
						payment_ids: [paymentId],
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						layer.close(loading);
						if (res.code == 0) {
							layui.table.reload('payment_list',{page:{curr:$(".layui-laypage-em").next().html()}})   //这行时在当前页刷新表格的写法
						}
					},
					error: function (res) {
						layer.close(loading);
						layer.msg(res.message);
						layui.table.reload('payment_list',{page:{curr:$(".layui-laypage-em").next().html()}})   //这行时在当前页刷新表格的写法
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		function confirmAll() {
			layer.confirm('确定要进行此操作吗?', function() {
				var checkData = layui.table.checkStatus('payment_list').data;
				if (checkData.length == 0) {
					layer.close();
					layer.msg('请先选择要确认的支付数据');
					return;
				}

				var loading = layer.load(2);

				var paymentIds = [];
				checkData.forEach(function (item) {
					paymentIds.push(item.id);
				});

				$.ajax({
					url: ns.url('paymentconfirm://admin/PaymentConfirm/confirm'),
					data: {
						payment_ids: paymentIds,
					},
					dataType: 'JSON',
					'type': 'POST',
					success: function (res) {
						layer.msg(res.message);
						layer.close(loading);
						layui.table.reload('payment_list',{page:{curr:$(".layui-laypage-em").next().html()}})   //这行时在当前页刷新表格的写法
					},
					error: function (res) {
						layer.close(loading);
						layer.msg(res.message);
						table.reload();
					}
				})
			}, function() {
				layer.close();
			});
		}


		function tdTitle(){

			$('th').each(function(index,element){

				$(element).attr('title',$(element).text());

			});

			$('td').each(function(index,element){

				$(element).attr('title',$(element).text());

			});

		};
	});
</script>
{/block}