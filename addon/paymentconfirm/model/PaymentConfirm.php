<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+

namespace addon\paymentconfirm\model;

use app\model\BaseModel;
use think\facade\Db;
use addon\adapay\model\Pay AS AdaPay;
use think\facade\Log;

/**
 * 支付确认
 */
class PaymentConfirm extends BaseModel
{

    /**
     * 获取pay表已确认列表
     * @param array $where
     * @param int $page
     * @param int $page_size
     * @param array $join
     * @return array
     */
    public function getConfirmList(array $where = [], $page = 1, $page_size = PAGE_LIST_ROWS, $join = [])
    {
        $condition = [
            ['p.pay_type', '=', 'adapay'],
            ['p.pay_status', '=', 2],
            ['p.order_type', '=', 0],
        ];
        $condition = array_merge($condition, $where);

        $list = model('pay')->pageList($condition, "p.*, ". Db::raw('sum(IFNULL(pr.refund_fee, 0)) as total'), 'p.id desc', $page, $page_size, 'p', $join, 'p.out_trade_no');
        if(!empty($list['list'])) {
            foreach($list['list'] as $k => $v) {
                $list['list'][$k]['pay_timestamp'] = date('Y-m-d H:i:s', $v['pay_time']);
                $list['list'][$k]['refund_status'] = 0;
                $list['list'][$k]['refund_status_name'] = '';
                if (($refundMoney = $this->checkOrderGoodsRefund($v['out_trade_no'])) > 0) {
                    $list['list'][$k]['refund_status'] = 1;
                    if ($refundMoney == $v['pay_money']) $list['list'][$k]['refund_status_name'] = '已退款';
                    if ($refundMoney < $v['pay_money']) $list['list'][$k]['refund_status_name'] = "部分退款({$refundMoney})";
                }
            }
        }

        return $this->success($list);
    }


    /**
     * 获取pay表未确认列表
     * @param array $where
     * @param int $page
     * @param int $page_size
     * @param array $join
     * @return array
     */
    public function getList(array $where = [], $page = 1, $page_size = PAGE_LIST_ROWS, $join = [])
    {
        $condition = [
            ['p.pay_type', '=', 'adapay'],
            ['p.pay_status', '=', 2],
            ['p.order_type', '=', 0],
        ];

        $condition = array_merge($condition, $where);

        $fields = 'p.*,' . Db::raw('sum(IFNULL(pr.refund_fee, 0)) as total');

        $sql = Db::table('xm_pay p')
            ->where($condition)
            ->leftJoin('xm_pay_refund pr', 'pr.out_trade_no = p.out_trade_no')
            ->leftJoin('xm_order o', 'o.out_trade_no = p.out_trade_no')
            ->field($fields)
            ->group('p.out_trade_no')
            ->having('pay_money > total');
        $count = $sql->count();

        $list = $sql->limit($page_size)->page($page)->order('p.id desc')->select()->toArray();

        if(!empty($list)) {
            foreach($list as $k => $v) {
                $list[$k]['pay_timestamp'] = date('Y-m-d H:i:s', $v['pay_time']);
                $list[$k]['refund_status'] = 0;
                $list[$k]['refund_status_name'] = '';
                if (($refundMoney = $this->checkOrderGoodsRefund($v['out_trade_no'])) > 0) {
                    $list[$k]['refund_status'] = 1;
                    if ($refundMoney == $v['pay_money']) $list[$k]['refund_status_name'] = '已退款';
                    if ($refundMoney < $v['pay_money']) $list[$k]['refund_status_name'] = "部分退款({$refundMoney})";
                }
            }
        }

        $result = [
            'page_count' => $page_size == 0 ? 1 : ceil(ceil($count/$page_size)),
            'count' => $count,
            'list' => $list
        ];

        return $this->success($result);
    }


    /**
     * 获取支付确认对象, 不存在则创建
     * @param string json paymentConfirmObj
     * @throws \Exception
     */
    public function getOrCreatePaymentConfirmObj(string $outTradeNo = '')
    {
        if (!$outTradeNo) return;
        $payInfo = model('pay')->getInfo(['out_trade_no' => $outTradeNo]);
        $objStr = $payInfo['pay_confirm_obj'];
        if (is_null($objStr) && $payInfo['pay_status'] == 2) {
            $payInfo['order_good_no'] = $outTradeNo;
            $refundMoney = $this->checkOrderGoodsRefund($outTradeNo);
            $payInfo['pay_money'] -= $refundMoney;
            $createObjRes = (new Adapay)->createConfirmPayment($payInfo);
            if($createObjRes['code'] < 0 ){
                Log::error('创建支付对象错误' . json_encode($createObjRes));
                throw new \Exception('创建支付对象错误', $createObjRes['data']);
            }
            $objStr = json_encode($createObjRes['data']);
            model('pay')->update(['pay_confirm_obj' => $objStr], ['out_trade_no' => $outTradeNo]);
        }

        return $objStr;
    }



    public function checkOrderGoodsRefund(string $outTradeNo = '')
    {
        $refundMoney = 0;
        if(!$outTradeNo) return $refundMoney;

//        $fields = 'o.order_id, o.order_no, og.order_goods_id, og.refund_status, og.refund_apply_money';
//        $join = [
//            [
//                'order_goods og',
//                'og.order_id = o.order_id',
//                'left'
//            ]
//        ];
//        $orders = model('order')->getList(['o.out_trade_no' => $outTradeNo], $fields, '', 'o', $join);
//        foreach ($orders as $k => $goods) {
//            if (in_array($goods['refund_status'], [2, 3])) {
//                $refundMoney += $goods['refund_apply_money'];
//            }
//        }
        $refundMoney = model('pay_refund')->getSum(['out_trade_no' => $outTradeNo], 'refund_fee');
        return $refundMoney;
    }
}