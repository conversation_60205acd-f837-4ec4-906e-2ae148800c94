<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\LEAGUE_TASK_POINT_LOG_STATUS;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\model\member\MemberModel;
use Carbon\Carbon;

abstract class PointReward implements PointChangeWay
{
    protected $completeMember = null;

    protected $receiveMember = null;

    public function completeMember()
    {
        return $this->completeMember;
    }

    public function receiveMember():?MemberModel
    {
        return $this->receiveMember;
    }

    /**
     * 检查某个奖励是否已发放
     * @param $leagueTaskKey
     * @param $relationValue
     * @return bool
     * @throws \Exception
     */
    public function belongLog($leagueTaskKey)
    {
        //$timeStart = Carbon::parse(date("Y-m", time()))->toDateTimeString();
        //$timeEnd = Carbon::now()->toDateTimeString();
        $logs = MemberLeagueTaskPointLogModel::where("member_id", $this->receiveMember()->member_id)
            ->where("league_task_key", $leagueTaskKey)
            ->where("point_type", $this->getType())
            ->where("relation_key", $this->getRelationKey())
            ->select();

        foreach ($logs as $log)
        {
            $valueIds = explode(",", $log->relation_value);
            if(in_array($this->getRelationValue(), $valueIds))
                return $log;
        }
        return null;
    }

    /**
     * 奖励是否已发
     * @param $leagueTaskKey
     * @return bool
     * @throws \Exception
     */
    public function isSend($leagueTaskKey)
    {
        if($this->belongLog($leagueTaskKey))
            return true;
        return false;
    }

    public function checkCustomRule(&$failReason = '')
    {
        return true;
    }
}