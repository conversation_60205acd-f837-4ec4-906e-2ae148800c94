<?php

namespace addon\leaguePoints\domainModel;

use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\model\LeagueTaskMemberGoodsTask;
use app\model\member\MemberModel;

/**
 * 商品任务推广积分奖励
 */
class GoodsTaskPointReward extends PointReward
{
    /**
     * @var LeagueTaskMemberGoodsTask 会员商品任务
     */
    protected $memberGoodsTask = null;
    
    /**
     * 构造函数
     * 
     * @param LeagueTaskMemberGoodsTask $memberGoodsTask 会员商品任务
     */
    public function __construct(LeagueTaskMemberGoodsTask $memberGoodsTask)
    {
        $this->memberGoodsTask = $memberGoodsTask;
        
        // 设置接收奖励的会员
        $this->receiveMember = MemberModel::where('status', 1)
            ->where('member_id', $memberGoodsTask->member_id)
            ->find();
            
        // 设置完成任务的会员（同一个人）
        $this->completeMember = $this->receiveMember;
    }
    
    /**
     * 获取奖励类型
     * 
     * @return string
     */
    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$GOODS_TASK_COMPLETE;
    }
    
    /**
     * 获取关联键
     * 
     * @return string
     */
    public function getRelationKey()
    {
        return 'member_goods_task';
    }
    
    /**
     * 获取关联值
     * 
     * @return int
     */
    public function getRelationValue()
    {
        return $this->memberGoodsTask->member_goods_task_id;
    }

    /**
     * 获取奖励金额
     *
     * @return int
     */
    public function getRewardPoints()
    {
        return $this->memberGoodsTask->reward_points;
    }

} 