<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\model\LeagueExchangeGoodsModel;

class ExchangeDeduction implements PointDeduction
{
    protected $exchangeGoods = null;
    public function __construct(LeagueExchangeGoodsModel $exchangeGoods)
    {
        $this->exchangeGoods = $exchangeGoods;
    }

    public function getType()
    {
        return 'exchange';
    }

    public function getRelationKey()
    {
        return 'league_exchange_goods';
    }

    public function getRelationValue()
    {
        return $this->exchangeGoods->id;
    }

    public function getDeductionPoints()
    {
        return $this->exchangeGoods->points;
    }

    public function getRemark()
    {
        $remark = '';
        $exchangeGoodsName = $this->exchangeGoods->name;
        if($this->exchangeGoods->type == LeagueExchangeGoodsModel::$TYPE_GOODSCOUPON)
        {

            $remark = "优惠券【{$exchangeGoodsName}】";
        }
        else
        {
            $remark = $exchangeGoodsName;
        }
        return $remark;
    }


}