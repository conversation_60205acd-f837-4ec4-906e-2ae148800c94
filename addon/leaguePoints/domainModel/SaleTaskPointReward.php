<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\model\LeagueTaskMemberSaleTaskModel;
use app\model\goods\GoodsModel;
use app\model\member\MemberModel;
use Carbon\Carbon;

class SaleTaskPointReward extends PointReward
{
    public $memberSaleTask = null;

    public $receiveMember = null;

    public $noSendReason = [];

    public $remark = '';

    public function __construct(LeagueTaskMemberSaleTaskModel $memberSaleTask)
    {
        $this->memberSaleTask = $memberSaleTask;
        $this->receiveMember = $this->completeMember = MemberModel::find($memberSaleTask->member_id);

        $goods = GoodsModel::find($memberSaleTask->goods_id);
        $goodsName = $goods ? $goods->goods_name : '';
        $this->remark = $goodsName.'-'.Carbon::parse($memberSaleTask->month)->format("Y年m月")."销售达标奖励";
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$SALE_TASK_COMPLETE;
    }

    public function getRelationKey()
    {
        return 'member_sale_task';
    }

    /**
     * 获取奖励金额
     *
     * @return int
     */
    public function getRewardPoints()
    {
        return $this->memberSaleTask->reward_points;
    }

    public function getRelationValue()
    {
        return $this->memberSaleTask->member_sale_task_id;
    }

}