<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use app\model\member\MemberModel;

class OtherPointReward extends PointReward
{
    public $amount = 0;
    public $remark = '';
    public $adminUser = '';
    public function __construct(MemberModel $member, $amount, $adminUser, $remark='')
    {
        $this->amount = $amount;
        $this->remark = $remark;
        $this->adminUser = $adminUser;
        $this->receiveMember = $member;
        $this->completeMember = $member;
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$OTHER;
    }

    public function getRelationKey()
    {
        return '';
    }

    public function getRelationValue()
    {
        return 0;
    }


}