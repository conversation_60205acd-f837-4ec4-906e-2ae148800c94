<?php


namespace addon\leaguePoints\domainModel;


use app\Domain\Models\DomainModel;

class LeagueTaskPointActivityRule extends DomainModel
{
    /**
     * @var string
     */
    protected $ruleKey = "";
    /**
     * @var string
     */
    protected $ruleNums = 0;
    /**
     * @var string
     */
    protected $ruleVal = 0;
    /**
     * @var string
     */
    protected $enable = 0;
    /**
     * @var string
     */
    protected $ruleNumsMax = 0;

    /**
     * @return string
     */
    public function getRuleKey(): string
    {
        return $this->ruleKey;
    }

    /**
     * @param string $ruleKey
     */
    public function setRuleKey(string $ruleKey): void
    {
        $this->ruleKey = $ruleKey;
    }

    /**
     * @return string
     */
    public function getRuleNums()
    {
        return $this->ruleNums;
    }

    /**
     * @param string $ruleNums
     */
    public function setRuleNums($ruleNums): void
    {
        $this->ruleNums = $ruleNums;
    }

    /**
     * @return string
     */
    public function getRuleVal()
    {
        return $this->ruleVal;
    }

    /**
     * @param string $ruleVal
     */
    public function setRuleVal($ruleVal): void
    {
        $this->ruleVal = $ruleVal;
    }

    /**
     * @return string
     */
    public function getEnable()
    {
        return $this->enable;
    }

    /**
     * @param string $enable
     */
    public function setEnable($enable): void
    {
        $this->enable = $enable;
    }

    /**
     * @return string
     */
    public function getRuleNumsMax()
    {
        return $this->ruleNumsMax;
    }

    /**
     * @param string $ruleNumsMax
     */
    public function setRuleNumsMax($ruleNumsMax): void
    {
        $this->ruleNumsMax = $ruleNumsMax;
    }


}