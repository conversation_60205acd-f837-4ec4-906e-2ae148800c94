<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\model\LeagueTaskPointConfigModel;

class LeagueTaskPointConfig
{
    /**
     * 获取所有加盟任务键
     */
    public static function getAllLeagueTaskKey()
    {
        return LeagueTaskPointConfigModel::where("cid", 0)->distinct("league_task_key")->order("league_task_key", "asc")->column("league_task_key");
    }

    /**
     * 获取所有加盟任务键
     */
    public static function getDefaultLeagueTask()
    {
        return LeagueTaskPointConfigModel::where("cid", 0)->order("league_task_key", "asc")->select();
    }
}