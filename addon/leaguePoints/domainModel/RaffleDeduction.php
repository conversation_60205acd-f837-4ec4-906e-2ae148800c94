<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\model\LeagueRaffleRecordModel;

class RaffleDeduction implements PointDeduction
{
    protected $record = null;
    public function __construct(LeagueRaffleRecordModel $record)
    {
        $this->record = $record;
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$RAFFLE;
    }

    public function getRelationKey()
    {
        return 'league_raffle_record';
    }

    public function getRelationValue()
    {
        return $this->record->record_id;
    }

    public function getDeductionPoints()
    {
        return $this->record->points;
    }

    public function getRemark()
    {
        return '';
    }

}