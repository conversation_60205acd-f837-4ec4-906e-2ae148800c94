<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use app\model\goods\GoodsBrowseModelLog;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;

class RecommendBrowsePointReward extends PointReward
{
    protected $goodsBrowseLog = null;

    protected $noSendReason = [];

    public function __construct(GoodsBrowseModelLog $goodsBrowseLog)
    {
        $this->goodsBrowseLog = $goodsBrowseLog;

        $this->completeMember = MemberModel::find($goodsBrowseLog->member_id);

        $parentMemberId = MemberRecommendModel::where("member_id", $goodsBrowseLog->member_id)->value('pid');
        if($parentMemberId)
            $this->receiveMember = MemberModel::where("status", 1)->where("member_id", $parentMemberId)->find();
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$RECOMMEND_BROWSE;
    }

    public function getRelationKey()
    {
        return 'goods_browse_log';
    }

    public function getRelationValue()
    {
        return $this->goodsBrowseLog->id;
    }

    public function getNoSendReason(): array
    {
        return $this->noSendReason;
    }

}