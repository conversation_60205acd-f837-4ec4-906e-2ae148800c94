<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;

class PointsEmptyDeduction implements PointDeduction
{
    protected $month = '';
    public function __construct($month)
    {
        $this->month = $month;
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$MONTH_EMPTY;
    }

    public function getRelationKey()
    {
        return 'month';
    }

    public function getRelationValue()
    {
        return $this->month;
    }

    public function getDeductionPoints()
    {
        // TODO: Implement getDeductionPoints() method.
    }

    public function getRemark()
    {
        return '';
    }


}