<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use app\Domain\DataTransformTrait;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlRepository;

class MemberLeagueTaskPointRepository extends MysqlRepository
{
    public function __construct(LeagueTaskPointJoinMemberModel $model)
    {
        $this->dbModel = $model;
    }

    public function findTask($memberId, $leagueTaskKey)
    {
        $memberTask = null;
        $task = $this->dbModel->where("member_id", $memberId)->where("league_task_key", $leagueTaskKey)->find();
        if($task)
        {
            $memberTask = new MemberLeagueTaskPoint();
            $this->DomainModelInitDbData($memberTask, $task);
        }
        return $memberTask;
    }

}