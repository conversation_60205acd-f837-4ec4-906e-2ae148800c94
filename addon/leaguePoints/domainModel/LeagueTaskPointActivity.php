<?php


namespace addon\leaguePoints\domainModel;

use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use app\Domain\DataTransformTrait;
use app\model\member\MemberModel;

class LeagueTaskPointActivity extends \app\Domain\Models\DomainModel
{
    use DataTransformTrait;

    protected $cid = 0;
    protected $leagueTaskKey = "";
    protected $taskUsePoint = 0;
    protected $rules = [];
    protected $ruleContent = "";
    protected $goodsRewardPoints = false;
    protected $taskCompleteMax = 0;
    protected $saleTaskEnable = false;
    protected $saleTaskMax = 0;

    /**
     * @return int
     */
    public function getCid(): int
    {
        return $this->cid;
    }

    /**
     * @param int $cid
     */
    public function setCid(int $cid): void
    {
        $this->cid = $cid;
    }

    /**
     * @return string
     */
    public function getLeagueTaskKey(): string
    {
        return $this->leagueTaskKey;
    }

    /**
     * @param string $leagueTaskKey
     */
    public function setLeagueTaskKey(string $leagueTaskKey): void
    {
        $this->leagueTaskKey = $leagueTaskKey;
    }

    /**
     * @return int
     */
    public function getTaskUsePoint(): int
    {
        return $this->taskUsePoint;
    }

    /**
     * @param int $taskUsePoint
     */
    public function setTaskUsePoint(int $taskUsePoint): void
    {
        $this->taskUsePoint = $taskUsePoint;
    }

    /**
     * @return array
     */
    public function getRules(): array
    {
        return $this->rules;
    }

    /**
     * @param array $rules
     */
    public function setRules(array $rules): void
    {
        $this->rules = $rules;
    }

    /**
     * @return bool
     */
    public function isGoodsRewardPoints(): bool
    {
        return $this->goodsRewardPoints;
    }

    /**
     * @param bool $goodsRewardPoints
     */
    public function setGoodsRewardPoints(bool $goodsRewardPoints): void
    {
        $this->goodsRewardPoints = $goodsRewardPoints;
    }

    /**
     * @return int
     */
    public function getTaskCompleteMax(): int
    {
        return $this->taskCompleteMax;
    }

    /**
     * @param int $taskCompleteMax
     */
    public function setTaskCompleteMax(int $taskCompleteMax): void
    {
        $this->taskCompleteMax = $taskCompleteMax;
    }

    /**
     * @return string
     */
    public function getRuleContent(): string
    {
        return $this->ruleContent;
    }

    /**
     * @param string $ruleContent
     */
    public function setRuleContent(string $ruleContent): void
    {
        $this->ruleContent = $ruleContent;
    }

    /**
     * @return bool
     */
    public function isSaleTaskEnable(): bool
    {
        return $this->saleTaskEnable;
    }

    /**
     * @param bool $saleTaskEnable
     */
    public function setSaleTaskEnable(bool $saleTaskEnable): void
    {
        $this->saleTaskEnable = $saleTaskEnable;
    }

    /**
     * @return int
     */
    public function getSaleTaskMax(): int
    {
        return $this->saleTaskMax;
    }

    /**
     * @param int $saleTaskMax
     */
    public function setSaleTaskMax(int $saleTaskMax): void
    {
        $this->saleTaskMax = $saleTaskMax;
    }


    public function addMemberByMobiles($mobiles)
    {
        $oldMobiles = MemberModel::whereIn("member_id", function  ($query) {
            $query->table('xm_league_task_point_join_member')->distinct("member_id")->where("league_task_key", $this->getLeagueTaskKey())->field('member_id');
        })->column("mobile");
        $addMobiles = array_diff($mobiles, $oldMobiles);
        $addMemberIds = MemberModel::where("status", 1)->whereIn("mobile", $addMobiles)->column("member_id");
        $this->addMembers($addMemberIds);
    }

    public function addMembers($memberIds)
    {
        foreach ($memberIds as $memberId)
        {
           $data['member_id'] = $memberId;
           $data['league_task_key'] = $this->getLeagueTaskKey();
           $data['enable'] = 1;
           LeagueTaskPointJoinMemberModel::create($data);
        }
    }


}