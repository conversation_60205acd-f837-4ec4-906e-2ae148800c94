<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\model\goods\GoodsBrowseModelLog;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\model\shop\ShopMemberModel;
use app\model\shop\ShopModel;

class ShopFansBrowsePointReward extends PointReward
{
    protected $goodsBrowseLog = null;

    protected $receiveMember = null;

    protected $noSendReason = [];

    public function __construct(GoodsBrowseModelLog $goodsBrowseLog)
    {
        $this->goodsBrowseLog = $goodsBrowseLog;

        $this->completeMember = MemberModel::find($goodsBrowseLog->member_id);

        $shopId = ShopMemberModel::where("member_id", $goodsBrowseLog->member_id)->where(function ($query){
            $query->where("expire_time", -1)->whereOr("expire_time", ">", time());
        })->where("unlock_time", 0)->value('site_id');

        if($shopId) {
            $mobile = ShopModel::where("site_id", $shopId)->value('username');
            $this->receiveMember = MemberModel::where("mobile", $mobile)->where("status", 1)->find();
        }
    }

    public function isSend($leagueTaskKey):bool
    {
        if(!parent::isSend($leagueTaskKey))
        {
            //排除直推用户新增的店铺粉丝浏览数据, 因为在直推好友规则已经发了
            $parentMemberId = MemberRecommendModel::where("member_id", $this->completeMember->member_id)->value('pid');
            if($parentMemberId == $this->receiveMember()->member_id)
            {
                //$this->noSendReason[] = "直推用户新增的浏览数据不计算";
                return true;
            }
            return false;
        }
        else
            return true;
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$SHOP_FANS_BROWSE;
    }

    public function getRelationKey()
    {
        return 'goods_browse_log';
    }

    public function getRelationValue()
    {
        return $this->goodsBrowseLog->id;
    }
}