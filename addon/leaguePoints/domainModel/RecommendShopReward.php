<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\model\shop\ShopModel;
use app\service\init\CommonService;

class RecommendShopReward extends PointReward
{
    protected $shop = null;
    protected $leagueId = 0;
    protected $xmUid = 0;

    public function __construct(ShopModel $shop, $xmUid, $leagueId)
    {
        $this->shop = $shop;
        $this->leagueId = $leagueId;
        $this->xmUid = $xmUid;

        $openMemberId = MemberModel::where("mobile", $shop->username)->where("status", 1)->value('member_id');
        if(!$openMemberId)
            throw new \Exception("开店人不存在或已注销");

        if($openMemberId && $parentMemberId = MemberRecommendModel::where("member_id", $openMemberId)->value('pid'))
        {
            $this->receiveMember = MemberModel::where("status", 1)->where("member_id", $parentMemberId)->find();
        }
    }
    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$RECOMMEND_SHOP;
    }

    public function getRelationKey()
    {
        return "shop";
    }

    public function getRelationValue()
    {
        return $this->shop->site_id;
    }

    public function checkCustomRule(&$failReason = '')
    {
        if(empty($this->leagueId))
        {
            $failReason = '加盟ID为空';
            return false;
        }

        $res = (new \app\model\league\LeagueTask())->isFristAddLeague($this->xmUid,$this->leagueId);
        if(!$res)
        {
            $failReason = '该加盟不是付费加盟';
            return false;
        }
        return true;
    }
}