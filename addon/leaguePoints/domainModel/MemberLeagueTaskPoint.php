<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\LEAGUE_TASK_POINT_LOG_STATUS;
use addon\leaguePoints\dataType\LEAGUE_TASK_RULE_KEY;
use addon\leaguePoints\model\LeagueExchangeGoodsModel;
use addon\leaguePoints\model\LeagueRaffleRecordModel;
use addon\leaguePoints\model\LeagueTaskMemberGoodsTask;
use addon\leaguePoints\model\LeagueTaskPointCompleteRecord;
use addon\leaguePoints\model\LeagueTaskPointConfigModel;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\Domain\DataTransformTrait;
use app\Domain\Models\DomainModel;
use app\model\goods\GoodsBrowseModelLog;
use app\model\member\MemberModel;
use app\service\init\CommonService;
use Carbon\Carbon;
use think\facade\Log;
use think\Model;

class MemberLeagueTaskPoint extends DomainModel
{
    use DataTransformTrait;
    /**
     * @var int
     */
    protected $memberId = 0;
    /**
     * @var MemberModel
     */
    protected $member = null;
    /**
     * @var int
     */
    protected $point = 0;
    /**
     * @var string
     */
    protected $leagueTaskKey = "";
    /**
     * @var int
     */
    protected $enable = 0;

    /**
     * @var LeagueTaskPointActivity
     */
    protected $belongActivity = null;

    /**
     * @return MemberModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMember()
    {
        if($this->memberId > 0 && !$this->member)
        {
            $this->member = MemberModel::where("status", 1)->find($this->memberId);
        }
        return $this->member;
    }

    /**
     * @return int
     */
    public function getPoint(): int
    {
        return $this->point;
    }

    /**
     * @param int $point
     */
    public function setPoint(int $point): void
    {
        $this->point = $point;
        $this->addUpdateData("point", $point);
    }

    /**
     * @return int
     */
    public function getMemberId(): int
    {
        return $this->memberId;
    }

    /**
     * @param int $memberId
     */
    public function setMemberId(int $memberId): void
    {
        $this->memberId = $memberId;
    }

    /**
     * @return string
     */
    public function getLeagueTaskKey(): string
    {
        return $this->leagueTaskKey;
    }

    /**
     * @param string $leagueTaskKey
     */
    public function setLeagueTaskKey(string $leagueTaskKey): void
    {
        $this->leagueTaskKey = $leagueTaskKey;
    }

    /**
     * @return int
     */
    public function getEnable(): int
    {
        return $this->enable;
    }

    /**
     * @param int $enable
     */
    public function setEnable(int $enable): void
    {
        $this->enable = $enable;
        $this->addUpdateData("enable", $enable);
    }



    /**
     * @return LeagueTaskPointActivity
     */
    public function getBelongActivity($cid = 0)
    {
        if(!$this->belongActivity)
        {
            $activityRepository = (new LeagueTaskPointActivityRepository());
            $activity = $activityRepository->findTaskByCid($cid, $this->getLeagueTaskKey());
            //使用默认配置
            if($cid > 0 && !$activity)
            {
                $activity = $activityRepository->findTaskByCid(0, $this->getLeagueTaskKey());
            }

            $this->belongActivity = $activity;
        }

        return $this->belongActivity;
    }

    public function getCompleteNumsNowMonth()
    {
        $timeStart = Carbon::parse(date("Y-m", time()))->toDateTimeString();
        $timeEnd = Carbon::now()->toDateTimeString();
        return $this->getCompleteNumsTimeRange($timeStart, $timeEnd);
    }

    public function getCompleteNumsTimeRange($timeStart, $timeEnd)
    {
        return LeagueTaskPointCompleteRecord::where("member_id", $this->memberId)
            ->where("league_task_key", $this->getLeagueTaskKey())
            ->whereBetween('complete_time', [$timeStart, $timeEnd])
            ->count();
    }

    public function getSentPointsMonthByType($pointType)
    {
        $timeStart = Carbon::parse(date("Y-m", time()))->toDateTimeString();
        $timeEnd = Carbon::now()->toDateTimeString();
        return $this->getSentPointsTimeRangeByType($pointType, $timeStart, $timeEnd);
    }

    public function getSentPointsTimeRangeByType($pointType, $timeStart, $timeEnd)
    {
        return MemberLeagueTaskPointLogModel::whereBetween("create_time", [$timeStart, $timeEnd])
            ->where("member_id", $this->memberId)
            ->where("point_type", $pointType)
            ->where("league_task_key", $this->getLeagueTaskKey())
            ->sum('point');
    }

    /**
     * 获取规则（已启用）
     * @param string $cid
     * @return array
     */
    public function getRules()
    {
        $rules = [];
        if($this->getEnable())
        {
            $activity = $this->getBelongActivity(0);
            $rulesConfig = $activity->getRules();
            foreach ($rulesConfig as $c)
                if($c['enable'] == 1)
                    $rules[] = $c;
        }
        return $rules;
    }

    /**
     * @return LeagueTaskPointActivityRule[]
     */
    public function getActivityRules(): array
    {
        $ret = [];
        $rules = $this->getRules();
        foreach ($rules as $rule)
        {
            $activityRule = new LeagueTaskPointActivityRule();
            $this->DomainModelInitData($activityRule, $rule);
            $ret[] = $activityRule;
        }
        return $ret;
    }

    public function getCompletePoints($cid)
    {
        $activity = $this->getBelongActivity($cid);
        return $activity->getTaskUsePoint();
    }

    public function getSMLeagueTasks()
    {
        $taskNum = explode("_", $this->getLeagueTaskKey())[1];

        $service = new CommonService();
        //兼容已注销用户卡住发放队列
        $member = $this->getMember();
        if(!$member)
            return [];

        $xmTasks = $service->getUserLeagueTask($member->mobile);

        $smNoCompleteTasks = [];
        if($xmTasks && $xmTasks[$taskNum])
        {
            $taskList = $xmTasks[$taskNum];
            foreach ($taskList as $task)
                if($task['is_task_com'] == 0 && $task['status'] == 0)       //当前奖励未发放且任务未完成
                    $smNoCompleteTasks[] = $task;
            if($smNoCompleteTasks)
            {
                $smNoCompleteTasks = array_values($smNoCompleteTasks);
                $endTimeArr = array_column($smNoCompleteTasks, 'end_time');
                array_multisort($endTimeArr, SORT_ASC, SORT_LOCALE_STRING, $smNoCompleteTasks);

                foreach ($smNoCompleteTasks as $i=>&$task)
                {
                    $task['surplus_seconds'] = Carbon::now()->diffInSeconds(Carbon::parse($task['end_time']));
                    $task['complete_points'] = $this->getCompletePoints($task['cate_id']);
                    if($i == 0)
                        $task['use_points'] = $this->getPoint() > $task['complete_points'] ? $task['complete_points'] : $this->getPoint();
                    else
                        $task['use_points'] = 0;
                }
                return $smNoCompleteTasks;
            }
        }

        return [];
    }

    /**
     * 根据键查找对应积分获取配置
     * @param $key
     * @return LeagueTaskPointActivityRule|null
     */
    public function findPointRuleByKey($key)
    {
        $rules = $this->getActivityRules();
        foreach ($rules as $rule)
            if($rule->getRuleKey() == $key)
                return $rule;
        return null;
    }

    /**
     * 完成奖励规则任务
     * @param PointReward $pointReward
     */
    public function sendReward(PointReward $pointReward, &$failReason=[])
    {
        //规则是否开启
        $rule = $this->findPointRuleByKey($pointReward->getType());

        if(!$this->getEnable())
        {
            $failReason[] = $pointReward->getType()."用户权限已禁用";
            return false;
        }

        if(!$rule || !$rule->getEnable())
        {
            $failReason[] = $pointReward->getType()."规则未开启或不存在";
            return false;
        }

        $CustomFailReason = '';
        if(!$pointReward->checkCustomRule($CustomFailReason))
        {
            $failReason[] = $CustomFailReason;
            return false;
        }

        if($pointReward->isSend($this->getLeagueTaskKey()))
        {
            $failReason[] = $pointReward->getType()."奖励已发";
            return false;
        }
        if($pointReward instanceof RecommendBrowsePointReward || $pointReward instanceof ShopFansBrowsePointReward)
        {
            //当月的数据按浏览人次计数, 查找是否有未完成的记录
            $browseIds = $this->getBrowseIdsForMonth($pointReward->getType());
            if($browseIds)
            {
                //获取计数内的所有用户id
                $browseMemberIds = GoodsBrowseModelLog::whereIn("id", $browseIds)->column("member_id");
                //存在表示该用户的浏览已经算过
                if(in_array($pointReward->completeMember()->member_id, $browseMemberIds))
                {
                    $failReason[] = "本月该浏览用户已计算人次";
                    return false;
                }
            }
        }

        $rewardPoint = $rule->getRuleVal();
        $sendPoints = $this->getSentPointsMonthByType($pointReward->getType());

        //达到上限不进行计数增加
        if($rule->getRuleNumsMax() > 0 && $rewardPoint > 0 && $rewardPoint + $sendPoints <= $rule->getRuleNumsMax())
        {
            //写入积分记录
            $log = $this->writePointLog($pointReward);
            //积分变更
            $this->changePoints($log);
            return true;
        }
        else
        {
            $failReason[] = $pointReward->getType()."规则达到上限不进行不再计算";
            return false;
        }
    }

    public function sendGoodsTaskPointReward(GoodsTaskPointReward $pointReward, &$failReason = '')
    {
        if(!$this->getEnable())
        {
            $failReason = "用户权限已禁用";
            return false;
        }

        if($pointReward->isSend($this->getLeagueTaskKey()))
        {
            $failReason = "奖励已发";
            return false;
        }

        // 检查配置是否开启
        $task = LeagueTaskMemberGoodsTask::find($pointReward->getRelationValue());
        $configModel = new LeagueTaskPointConfigModel();
        if (!$configModel->isGoodsRewardPointsEnabled($task['league_task_key'])) {
            $failReason = "该任务类型未开启商品推广积分";
            return false;
        }

        // 获取奖励的积分 - 直接从会员商品任务中获取reward_points字段
        $memberGoodsTaskId = $pointReward->getRelationValue();
        $memberGoodsTask = \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::where('member_goods_task_id', $memberGoodsTaskId)->find();
        if (!$memberGoodsTask) {
            $failReason = "任务不存在，无法获取奖励积分";
            return false;
        }
        
        $rewardPoint = $memberGoodsTask['reward_points'];
        if (!is_numeric($rewardPoint) || $rewardPoint <= 0) {
            $failReason = "任务奖励积分无效";
            return false;
        }


        $sendPoints = $this->getSentPointsMonthByType($pointReward->getType());

        $activity = $this->getBelongActivity(0);
        //达到上限不进行计数增加
        if($activity->getTaskCompleteMax() > 0 && $pointReward->getRewardPoints() + $sendPoints <= $activity->getTaskCompleteMax())
        {
            // 使用自定义方法记录积分日志
            $log = $this->writeGoodsTaskPointLog($pointReward, $rewardPoint);

            // 积分变更
            $this->changePoints($log);
            return true;
        }
        else
        {
            $failReason = "已达到本月推广任务获得贡献值上限";
            return false;
        }
    }

    public function sendSaleTaskPointReward(SaleTaskPointReward $pointReward, &$failReason='')
    {
        if(!$this->getEnable())
        {
            $failReason = $pointReward->getType()."用户权限已禁用";
            return false;
        }

        if($pointReward->isSend($this->getLeagueTaskKey()))
        {
            $failReason = $pointReward->getType()."奖励已发";
            return false;
        }

        // 获取奖励的积分 - 直接从会员商品任务中获取reward_points字段
        $memberTaskId = $pointReward->getRelationValue();
        $memberTask = \addon\leaguePoints\model\LeagueTaskMemberSaleTaskModel::where('member_sale_task_id', $memberTaskId)->find();
        if (!$memberTask) {
            $failReason = "任务不存在，无法获取奖励积分";
            return false;
        }

        $rewardPoint = $memberTask['reward_points'];
        if (!is_numeric($rewardPoint) || $rewardPoint <= 0) {
            $failReason = "任务奖励积分无效";
            return false;
        }

        $month = $pointReward->memberSaleTask->month;
        $timeStart = Carbon::parse($month)->toDateTimeString();
        $timeEnd = Carbon::parse($timeStart)->addMonth()->toDateTimeString();
        $sendPoints =  $this->getSentPointsTimeRangeByType($pointReward->getType(), $timeStart, $timeEnd);

        $activity = $this->getBelongActivity(0);
        //达到上限不进行计数增加
        if($activity->getSaleTaskMax() > 0 && $pointReward->getRewardPoints() + $sendPoints <= $activity->getSaleTaskMax())
        {
            // 使用自定义方法记录积分日志
            $log = $this->writeSaleTaskPointLog($pointReward, $rewardPoint);

            // 积分变更
            $this->changePoints($log);
            return true;
        }
        else
        {
            $failReason = $pointReward->getType()."规则达到上限不进行不再计算";
            return false;
        }
    }

    public function sendOtherPointReward(OtherPointReward $pointReward, &$failReason='')
    {
        if(!$this->getEnable())
        {
            $failReason = $pointReward->getType()."用户权限已禁用";
            return false;
        }

        // 使用自定义方法记录积分日志
        $logData = [
            'member_id' => $this->memberId,
            'league_task_key' => $this->leagueTaskKey,
            'point_type' => $pointReward->getType(),
            'point' => $pointReward->amount,
            'relation_key' => $pointReward->getRelationKey(),
            'relation_value' => $pointReward->getRelationValue(),
            'surplus_point' => $this->point,
            'status' => \addon\leaguePoints\dataType\LEAGUE_TASK_POINT_LOG_STATUS::$complete,
            'remark' => $pointReward->remark,
            'admin_user' => $pointReward->adminUser,
        ];

        $log = MemberLeagueTaskPointLogModel::create($logData);

        // 积分变更
        $this->changePoints($log);
        return true;
    }

    /**
     * 为商品任务写入积分记录
     * @param GoodsTaskPointReward $pointReward
     * @param int $rewardPoint 奖励积分
     * @return \addon\leaguePoints\model\MemberLeagueTaskPointLogModel
     */
    public function writeGoodsTaskPointLog(GoodsTaskPointReward $pointReward, $rewardPoint)
    {
        $logData = [
            'member_id' => $this->memberId,
            'league_task_key' => $this->leagueTaskKey,
            'point_type' => $pointReward->getType(),
            'point' => $rewardPoint,
            'relation_key' => $pointReward->getRelationKey(),
            'relation_value' => $pointReward->getRelationValue(),
            'surplus_point' => $this->point,
            'status' => \addon\leaguePoints\dataType\LEAGUE_TASK_POINT_LOG_STATUS::$complete
        ];
        
        return MemberLeagueTaskPointLogModel::create($logData);
    }

    /**
     * 为商品任务写入积分记录
     * @param SaleTaskPointReward $pointReward
     * @param int $rewardPoint 奖励积分
     * @return \addon\leaguePoints\model\MemberLeagueTaskPointLogModel
     */
    public function writeSaleTaskPointLog(SaleTaskPointReward $pointReward, $rewardPoint)
    {
        $logData = [
            'member_id' => $this->memberId,
            'league_task_key' => $this->leagueTaskKey,
            'point_type' => $pointReward->getType(),
            'point' => $rewardPoint,
            'relation_key' => $pointReward->getRelationKey(),
            'relation_value' => $pointReward->getRelationValue(),
            'surplus_point' => $this->point,
            'remark' => $pointReward->remark,
            'status' => \addon\leaguePoints\dataType\LEAGUE_TASK_POINT_LOG_STATUS::$complete
        ];

        return MemberLeagueTaskPointLogModel::create($logData);
    }

    /**
     * 积分变更
     * @param MemberLeagueTaskPointLogModel $log
     */
    public function changePoints(MemberLeagueTaskPointLogModel $log)
    {
        if($log->status == LEAGUE_TASK_POINT_LOG_STATUS::$complete)
        {
            //积分变动
            $this->setPoint($this->point + $log->point);
            $repository = new MemberLeagueTaskPointRepository(new LeagueTaskPointJoinMemberModel());
            $repository->updateModel($this);
            return true;
        }
        return false;
    }

    /**
     * 写入积分记录
     * @param PointReward $pointReward
     * @return MemberLeagueTaskPointLogModel|array|mixed|Model
     */
    public function writePointLog(PointReward $pointReward)
    {
        $pointType = $pointReward->getType();
        $noCompleteLog = $this->findNoCompleteLogForType($pointType);
        $rule = $this->findPointRuleByKey($pointType);
        $rewardPoint = $rule->getRuleVal();
        //更新未完成记录
        if($noCompleteLog)
        {
            $values = [];
            if($noCompleteLog->relation_value)
            {
                $values = explode(",", $noCompleteLog->relation_value);
            }
            $values[] = $pointReward->getRelationValue();
            $noCompleteLog->relation_value = implode(",", $values);
            if(count($values) >= $rule->getRuleNums())
            {
                $noCompleteLog->point = $rewardPoint;
                $noCompleteLog->status = LEAGUE_TASK_POINT_LOG_STATUS::$complete;
            }
            $noCompleteLog->save();
            return $noCompleteLog;
        }
        else
        {
            $logData['member_id'] = $this->memberId;
            $logData['league_task_key'] = $this->leagueTaskKey;
            $logData['point_type'] = $pointReward->getType();
            $logData['point'] = 0;
            $logData['relation_key'] = $pointReward->getRelationKey();
            $logData['relation_value'] = $pointReward->getRelationValue();
            $logData['surplus_point'] = $this->point;
            if($rule->getRuleNums() == 1)
            {
                $logData['point'] = $rewardPoint;
                $logData['status'] = 1;
            }
            else
                $logData['status'] = 0;
            return MemberLeagueTaskPointLogModel::create($logData);
        }
    }

    public function writeDeductionLog($point, PointDeduction $deduction)
    {
        $logData['member_id'] = $this->memberId;
        $logData['league_task_key'] = $this->leagueTaskKey;
        $logData['point_type'] = $deduction->getType();
        $logData['point'] = -1 * $point;
        $logData['relation_key'] = $deduction->getRelationKey();
        $logData['relation_value'] = $deduction->getRelationValue();
        $logData['surplus_point'] = $this->point;
        $logData['status'] = 1;
        return MemberLeagueTaskPointLogModel::create($logData);
    }

    public function writeDeductionLogNew(PointDeduction $deduction)
    {
        $logData['member_id'] = $this->memberId;
        $logData['league_task_key'] = $this->leagueTaskKey;
        $logData['point_type'] = $deduction->getType();
        $logData['point'] = -1 * $deduction->getDeductionPoints();
        $logData['relation_key'] = $deduction->getRelationKey();
        $logData['relation_value'] = $deduction->getRelationValue();
        $logData['surplus_point'] = $this->point;
        $logData['status'] = 1;
        $logData['remark'] = $deduction->getRemark();
        return MemberLeagueTaskPointLogModel::create($logData);
    }

    public function findNoCompleteLogForType($pointType)
    {
        $timeStart = Carbon::parse(date("Y-m", time()))->toDateTimeString();
        $timeEnd = Carbon::now()->toDateTimeString();

        return MemberLeagueTaskPointLogModel::whereBetween("create_time", [$timeStart, $timeEnd])
            ->where("member_id", $this->memberId)
            ->where("league_task_key", $this->getLeagueTaskKey())
            ->where("status", LEAGUE_TASK_POINT_LOG_STATUS::$incomplete)
            ->where("point_type", $pointType)
            ->find();
    }

    public function getBrowseIdsForMonth($pointType)
    {
        $timeStart = Carbon::parse(date("Y-m", time()))->toDateTimeString();
        $timeEnd = Carbon::now()->toDateTimeString();
        $logIdsArr = MemberLeagueTaskPointLogModel::whereBetween("create_time", [$timeStart, $timeEnd])
            ->where("member_id", $this->memberId)
            ->where("league_task_key", $this->getLeagueTaskKey())
            ->where("point_type", $pointType)
            ->column("relation_value");

        $goodsBrowseLogIds = [];

        foreach ($logIdsArr as $v)
        {
            $goodsBrowseLogIds = array_merge($goodsBrowseLogIds, explode(",", $v));
        }
        return $goodsBrowseLogIds;
    }

    public function autoCompleteLeagueTask()
    {
        $tasks = $this->getSMLeagueTasks();
        if(count($tasks) > 0)
        {
            $task = $tasks[0];
            if($this->getPoint() >= $task['complete_points'])
            {
                $completeLog = $this->writeLeagueTaskCompleteLog($task['complete_points'], $task);
                //先迈返回成功
                if($this->completeToSM($completeLog))
                {
                    //写入积分记录
                    $log = $this->writeDeductionLog($task['complete_points'], new LeagueTaskCompleteDeduction($completeLog->id));
                    //积分变更
                    $this->changePoints($log);
                    //变更完成记录状态
                    $completeLog->status = 1;
                    $completeLog->save();
                }
            }
        }

    }

    public function getPoints()
    {
        return $this->point;
    }

    public function exchange(LeagueExchangeGoodsModel $exchangeGoods)
    {
        if($exchangeGoods->status != LeagueExchangeGoodsModel::$STATUS_UP)
            return false;

        return $this->pointsDeduction(new ExchangeDeduction($exchangeGoods));
        //变更完成记录状态
    }

    public function raffle(LeagueRaffleRecordModel $record)
    {
        return $this->pointsDeduction(new RaffleDeduction($record));
    }

    public function writeLeagueTaskCompleteLog($point, array $leagueData)
    {
        $record = LeagueTaskPointCompleteRecord::where("user_league_id", $leagueData['user_league_id'])->where("league_task_key", $this->getLeagueTaskKey())->where("status", 0)->find();
        if(!$record)
        {
            $data['member_id'] = $this->memberId;
            $data['user_league_id'] = $leagueData['user_league_id'];
            $data['league_name'] = $leagueData['league_name'];
            $data['league_task_key'] = $this->getLeagueTaskKey();
            $data['status'] = 0;
            $data['league_data'] = $leagueData;
            $data['point'] = $point;
            $data['complete_time'] = Carbon::now()->toDateTimeString();
            $record = LeagueTaskPointCompleteRecord::create($data);
        }

        return $record;
    }

    /**
     * 请求先迈积分完成加盟任务
     * @param LeagueTaskPointCompleteRecord $record
     * @return mixed
     * @throws \app\Exceptions\XMException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function completeToSM(LeagueTaskPointCompleteRecord $record)
    {
        $leagueData = (array)$record->league_data;

        $service = new CommonService();
        $postData['user_league_id'] = $record->user_league_id;
        $postData['mobile'] = MemberModel::find($record->member_id)->mobile;
        $postData['order_point'] = $record->point;
        $postData['complete_time'] = $record->complete_time;
        $postData['task_type'] = $leagueData['task_type'];
        $postData['task_id'] = $leagueData['task_id'];
        $postData['task_num'] = $leagueData['task_num'];
        $postData['league_pay_id'] = $leagueData['league_pay_id'];
        $postData['point_recode_id'] = $record->id;

        $ret = $service->pointCompleteLeagueTask($postData);
        if($ret['data']['is_success'] > 0)
            return true;
        else
        {
            Log::error("加盟单自动完成失败：".json_encode($ret));
            return false;
        }
    }

    public function pointsEmpty()
    {
        //写入积分记录
        $log = $this->writeDeductionLog($this->point, new PointsEmptyDeduction(Carbon::now()->subMonth()->format("Y-m")));
        //积分变更
        $this->changePoints($log);
    }

    public function pointsDeduction(PointDeduction $deduction)
    {
        if($this->getPoint() < $deduction->getDeductionPoints())
            return false;

        //写入积分记录
        $log = $this->writeDeductionLogNew($deduction);
        //积分变更
        return $this->changePoints($log);
    }
}