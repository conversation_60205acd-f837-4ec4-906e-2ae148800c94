<?php


namespace addon\leaguePoints\domainModel;

use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;


/**
 * 积分抵扣
 * Class LeagueTaskCompleteDeduction
 * @package addon\leaguePoints\domainModel
 */
class LeagueTaskCompleteDeduction implements PointDeduction
{
    protected $recordId = 0;
    public function __construct($recordId)
    {
        $this->recordId = $recordId;
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$TASK_COMPLETE;
    }

    public function getRelationKey()
    {
        return 'league_task_point_complete_record';
    }

    public function getRelationValue()
    {
        return $this->recordId;
    }

    public function getDeductionPoints()
    {
        // TODO: Implement getDeductionPoints() method.
    }

    public function getRemark()
    {
        return '';
    }


}