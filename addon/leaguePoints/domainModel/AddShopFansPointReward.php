<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\model\shop\ShopMemberModel;
use app\model\shop\ShopModel;

class AddShopFansPointReward extends PointReward
{
    protected $fansMember = null;

    protected $shopMember = null;

    protected $noSendReason = [];

    public function __construct(ShopMemberModel $shopMember)
    {
        $this->shopMember = $shopMember;
        $this->fansMember = MemberModel::find($shopMember->member_id);
        $this->completeMember = $this->fansMember;

        $mobile = ShopModel::where("site_id", $shopMember->site_id)->where("shop_status", 1)->value('username');
        if($mobile)
            $this->receiveMember = MemberModel::where("mobile", $mobile)->where("status", 1)->find();

    }

    public function isSend($leagueTaskKey):bool
    {
        //本身没发奖并且同款直推奖励没法才能发(暂时不需要,下面已经判断了)
        //if(!parent::isSend($leagueTaskKey) && !$this->isRecommendRegisterSend($leagueTaskKey))
        if(!parent::isSend($leagueTaskKey))
        {
            //排除直推用户新增的店铺粉丝数据, 因为在直推好友规则已经发了
            $parentMemberId = MemberRecommendModel::where("member_id", $this->fansMember->member_id)->value('pid');

            if($parentMemberId == $this->receiveMember()->member_id)
            {
                //$this->noSendReason[] = "直推用户新增的店铺粉丝数据不计算";
                return true;
            }
            return false;
        }
        else
            return true;
    }

    /**
     * 检查同款直推奖励是否已发放
     * @param $leagueTaskKey
     * @param $relationValue
     * @return bool
     * @throws \Exception
     */
    public function isRecommendRegisterSend($leagueTaskKey)
    {
        $recommendRegisterReward = new RecommendRegisterPointReward($this->receiveMember()->member_id, $this->completeMember->member_id);
        return $recommendRegisterReward->isSend($leagueTaskKey);
    }


    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$ADD_SHOP_FANS;
    }

    public function getRelationKey()
    {
        return "shop_member";
    }

    public function getRelationValue()
    {
        return $this->shopMember->id;
    }

}