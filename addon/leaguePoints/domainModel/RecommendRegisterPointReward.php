<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;

class RecommendRegisterPointReward extends PointReward
{
    protected $memberRecommend = null;

    protected $noSendReason = [];

    protected $childMemberId = 0;

    public function __construct($memberId, $childMemberId)
    {
        $this->childMemberId = $childMemberId;

        $this->receiveMember = MemberModel::where("status", 1)->where("member_id", $memberId)->find();
    }

    public function getType()
    {
        return MEMBER_LEAGUE_POINT_TYPE::$RECOMMEND_REGISTER;
    }

    public function getRelationKey()
    {
        return "member";
    }

    public function getRelationValue()
    {
        return $this->childMemberId;
    }

    public function check(): bool
    {
        if(!$this->receiveMember)
        {
            $this->noSendReason[] = "领取人已注销或店铺已关闭";
            return false;
        }
        return true;
    }

    public function getNoSendReason(): array
    {
        return $this->noSendReason;
    }

}