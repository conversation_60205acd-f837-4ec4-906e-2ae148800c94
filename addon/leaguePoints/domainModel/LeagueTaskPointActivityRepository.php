<?php


namespace addon\leaguePoints\domainModel;


use addon\leaguePoints\model\LeagueTaskPointConfigModel;
use app\Domain\DataTransformTrait;

class LeagueTaskPointActivityRepository
{
    use DataTransformTrait;

    /**
     * @param $cid
     * @param $leagueTaskKey
     * @return LeagueTaskPointActivity
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function findTaskByCid($cid, $leagueTaskKey)
    {
        $activity = null;
        $ml = LeagueTaskPointConfigModel::where("cid", $cid)->where("league_task_key", $leagueTaskKey)->find();
        if($ml)
        {
            $activity = new LeagueTaskPointActivity();
            $this->DomainModelInitDbData($activity, $ml);
        }
        return $activity;
    }
}