{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .primary_color{
        color: #4685FD;
        cursor: pointer;
        font-size: 14px;
    }
    .layui-table-cell {
        overflow: visible !important;
    }
    td .layui-form-select {
        margin-top: -10px;
        margin-left: -15px;
        margin-right: -15px;
    }
    .layui-table-view .layui-input{
        width: 100%;
    }
    .ns-table-btn {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
    }
    .ns-table-btn .layui-btn {
        margin: 2px 5px;
    }
    .ns-table-btn .layui-btn:first-child {
        margin-left: 0;
    }
    .action-btns {
        padding: 10px 0;
        margin-bottom: 15px;
    }
    /* 弹窗样式 */
    .exchange-add-form {
        padding: 20px;
    }
    .exchange-add-form .form-item {
        margin-bottom: 15px;
        display: flex;
        line-height: 38px;
    }
    .exchange-add-form .form-label {
        width: 110px;
        text-align: right;
        padding-right: 10px;
    }
    .exchange-add-form .form-content {
        flex: 1;
    }
    .exchange-add-form .btn-group {
        margin-top: 30px;
        text-align: center;
    }
    .exchange-add-form .btn-group button {
        margin: 0 10px;
        padding: 0 30px;
    }
    .exchange-add-form .btn-cancel {
        background: #fff;
        color: #333;
        border: 1px solid #ddd;
    }
</style>
{/block}
{block name="main"}
<div class="action-btns">
    <button class="layui-btn layui-btn-normal" id="addCoupon">添加优惠券</button>
</div>

<table id="exchangeList" lay-filter="exchangeList"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        {{# if(d.status == 1){ }}
        <a class="layui-btn" lay-event="offline">下架</a>
        {{# } else { }}
        <a class="layui-btn" lay-event="online">上架</a>
        {{# } }}
    </div>
</script>

<!-- 状态 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 1){ }}
    <span style="color: green;">正常</span>
    {{# } else { }}
    <span style="color: red;">下架</span>
    {{# } }}
</script>

<!-- 添加兑换物弹窗 -->
<script type="text/html" id="addExchangeItemTpl">
    <div class="exchange-add-form">
        <div class="layui-form" lay-filter="addExchangeForm">
            <div class="form-item">
                <div class="form-label">物品类型：</div>
                <div class="form-content">优惠券</div>
            </div>
            <div class="form-item">
                <div class="form-label">物品名称：</div>
                <div class="form-content">{{ d.goodscoupon_name || '' }}</div>
            </div>
            <div class="form-item">
                <div class="form-label">兑换所需积分：</div>
                <div class="form-content">
                    <input type="number" name="points" lay-verify="required|number" placeholder="请输入积分值" autocomplete="off" class="layui-input" value="{{ d.points || '' }}">
                </div>
            </div>
            <div class="btn-group">
                <button type="button" class="layui-btn btn-cancel cancel">取消</button>
                <button type="button" class="layui-btn layui-btn-normal save">保存</button>
            </div>
        </div>
    </div>
</script>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'layer', 'table', 'laytpl'], function(){
        var form = layui.form,
            layer = layui.layer,
            table = layui.table,
            laytpl = layui.laytpl,
            $ = layui.$;
        
        var selectedCoupon = {}; // 选中的优惠券数据
            
        // 渲染表格
        table.render({
            elem: '#exchangeList',
            url: ns.url("leaguePoints://admin/exchangeManagement/lists"),
            cols: [[
                {field: 'name', title: '兑换物名称', width: 550, unresize: 'false'},
                {field: 'points', title: '花费积分值', width: 150, unresize: 'false'},
                {field: 'exchange_count', title: '兑换次数', width: 150, unresize: 'false'},
                {field: 'total_points', title: '累计消耗积分值', width: 200, unresize: 'false'},
                {field: 'status', title: '状态', width: 150, templet: '#statusTpl', unresize: 'false'},
                {field: 'operation', title: '操作', toolbar: '#operation', unresize: 'false', align: 'left'}
            ]],
            page: true,
            limit: 10,
            parseData: function(res){
                return {
                    "code": res.code == 0 ? 0 : 1,
                    "msg": res.message,
                    "count": res.data.count,
                    "data": res.data.list
                };
            }
        });
        
        // 监听工具条
        table.on('tool(exchangeList)', function(obj){
            var data = obj.data;
            if(obj.event === 'edit'){
                // 编辑操作
                showEditExchangeForm(data);
            } else if(obj.event === 'offline'){
                // 下架操作
                layer.confirm('确定要下架该优惠券吗？下架后兑换商城列表不可见，不支持兑换，已兑换的优惠券可正常使用', function(index){
                    $.ajax({
                        url: ns.url("leaguePoints://admin/exchangeManagement/modifyStatus"),
                        data: {
                            id: data.id,
                            status: 0
                        },
                        dataType: 'json',
                        type: 'post',
                        success: function(res) {
                            layer.msg(res.message);
                            if (res.code == 0) {
                                table.reload('exchangeList');
                            }
                        }
                    });
                    layer.close(index);
                });
            } else if(obj.event === 'online'){
                // 上架操作
                layer.confirm('确定要上架该优惠券吗？', function(index){
                    $.ajax({
                        url: ns.url("leaguePoints://admin/exchangeManagement/modifyStatus"),
                        data: {
                            id: data.id,
                            status: 1
                        },
                        dataType: 'json',
                        type: 'post',
                        success: function(res) {
                            layer.msg(res.message);
                            if (res.code == 0) {
                                table.reload('exchangeList');
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });
        
        // 添加优惠券按钮
        $('#addCoupon').on('click', function(){
            layer.open({
                type: 2,
                title: '选择优惠券',
                area: ['800px', '600px'],
                content: ns.url("membersignin://admin/signIn/selectGoodsCoupon"),
                btn: ['确认', '取消'],
                yes: function(index, layero){
                    var iframeWin = window[layero.find('iframe')[0]['name']]; // 【核心】
                    selectedCoupon = iframeWin.dataToParent(); // 调用子页面的方法
                    if(Object.keys(selectedCoupon).length){
                        layer.close(index);
                        
                        // 打开添加兑换物弹窗
                        showAddExchangeForm(selectedCoupon);
                    } else {
                        layer.msg('请选择优惠券');
                    }
                }
            });
        });
        
        // 显示添加兑换物弹窗
        function showAddExchangeForm(couponData) {
            laytpl($('#addExchangeItemTpl').html()).render(couponData, function(html){
                var addLayer = layer.open({
                    type: 1,
                    title: '添加兑换物',
                    area: ['500px', '350px'],
                    content: html,
                    success: function(layero, index){
                        // 点击取消按钮
                        layero.find('.cancel').click(function(){
                            layer.close(index);
                        });
                        
                        // 点击保存按钮
                        layero.find('.save').click(function(){
                            var points = layero.find('input[name="points"]').val();
                            
                            if(!points || isNaN(points) || points <= 0){
                                layer.msg('请输入正确的积分值');
                                return;
                            }
                            
                            // 添加兑换物品
                            var postData = {
                                name: couponData.goodscoupon_name,
                                type: 1, // 优惠券类型
                                relation_id: couponData.goodscoupon_type_id,
                                points: parseInt(points)
                            };
                            
                            $.ajax({
                                url: ns.url("leaguePoints://admin/exchangeManagement/add"),
                                data: postData,
                                dataType: 'json',
                                type: 'post',
                                success: function(res) {
                                    layer.msg(res.message);
                                    if (res.code == 0) {
                                        // 重载表格
                                        table.reload('exchangeList');
                                        layer.close(index);
                                    }
                                }
                            });
                        });
                    }
                });
            });
        }
        
        // 显示编辑兑换物弹窗
        function showEditExchangeForm(data) {
            // 先获取详情
            $.ajax({
                url: ns.url("leaguePoints://admin/exchangeManagement/detail"),
                data: {id: data.id},
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    if (res.code == 0) {
                        var detail = res.data;
                        
                        // 准备模板数据
                        var templateData = {
                            goodscoupon_name: detail.name,
                            points: detail.points,
                            id: detail.id
                        };
                        
                        laytpl($('#addExchangeItemTpl').html()).render(templateData, function(html){
                            var editLayer = layer.open({
                                type: 1,
                                title: '编辑兑换物',
                                area: ['500px', '350px'],
                                content: html,
                                success: function(layero, index){
                                    // 点击取消按钮
                                    layero.find('.cancel').click(function(){
                                        layer.close(index);
                                    });
                                    
                                    // 点击保存按钮
                                    layero.find('.save').click(function(){
                                        var points = layero.find('input[name="points"]').val();
                                        
                                        if(!points || isNaN(points) || points <= 0){
                                            layer.msg('请输入正确的积分值');
                                            return;
                                        }
                                        
                                        // 编辑兑换物品
                                        $.ajax({
                                            url: ns.url("leaguePoints://admin/exchangeManagement/edit"),
                                            data: {
                                                id: detail.id,
                                                points: parseInt(points)
                                            },
                                            dataType: 'json',
                                            type: 'post',
                                            success: function(res) {
                                                layer.msg(res.message);
                                                if (res.code == 0) {
                                                    // 重载表格
                                                    table.reload('exchangeList');
                                                    layer.close(index);
                                                }
                                            }
                                        });
                                    });
                                }
                            });
                        });
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        }
    });
</script>
{/block}