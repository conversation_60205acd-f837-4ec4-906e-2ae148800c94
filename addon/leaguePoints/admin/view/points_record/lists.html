{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .daterange-input{
        display: inline-block;
    }
    /* 贡献值发放弹窗样式 */
    .points-reward-popup .layui-form-label {
        width: 100px;
    }
    .points-reward-popup .layui-input-block {
        margin-left: 130px;
    }
    .points-batch-popup .batch-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }
    .points-batch-popup .batch-table th,
    .points-batch-popup .batch-table td {
        border: 1px solid #e6e6e6;
        padding: 10px;
        text-align: center;
    }
    .points-batch-popup .batch-table th {
        background-color: #f2f2f2;
    }
    .points-batch-popup .batch-footer {
        text-align: right;
        margin: 15px 0;
    }
    .points-batch-popup .batch-actions {
        text-align: center;
        margin-top: 20px;
    }
    .download-template {
        float: right;
        color: #3E98FF;
        cursor: pointer;
    }
    /* 批量发放弹窗增强样式 */
    .points-batch-popup {
        padding: 15px;
    }
    .points-batch-popup .batch-table {
        table-layout: fixed;
    }
    .points-batch-popup .batch-table th:nth-child(1),
    .points-batch-popup .batch-table td:nth-child(1) {
        width: 30%;
    }
    .points-batch-popup .batch-table th:nth-child(2),
    .points-batch-popup .batch-table td:nth-child(2) {
        width: 20%;
    }
    .points-batch-popup .batch-table th:nth-child(3),
    .points-batch-popup .batch-table td:nth-child(3) {
        width: 50%;
    }
    .batch-pagination {
        margin: 20px 0;
    }
    .layui-laypage-curr .layui-laypage-em {
        background-color: #3E98FF;
    }
    /* 确认弹框样式 */
    .confirm-dialog {
        padding: 30px 20px;
        text-align: center;
        font-size: 16px;
        line-height: 1.8;
    }
    .confirm-dialog .highlight {
        color: #FF5722;
        font-weight: bold;
        font-size: 18px;
        padding: 0 5px;
    }
    /* 覆盖默认按钮样式，确保暂不发放按钮没有背景色 */
    .layui-layer-btn a.layui-layer-btn0 {
        border-color: #E2E2E2 !important;
        background-color: transparent !important;
        color: #333 !important;
    }
    .layui-layer-btn a.layui-layer-btn1 {
        border-color: #4685FD !important;
        background-color: #4685FD !important;
        color: #fff !important;
    }
    /* 发放结果弹窗样式 */
    .points-result-popup {
        padding: 0;
        position: relative;
    }
    .result-header {
        background-color: #f8f8f8;
        padding: 15px 20px;
        border-bottom: 1px solid #e6e6e6;
        position: relative;
    }
    .result-header h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
    }
    .result-close {
        position: absolute;
        right: 15px;
        top: 12px;
        font-size: 20px;
        color: #999;
        cursor: pointer;
    }
    .result-content {
        padding: 20px;
    }
    .result-summary {
        font-size: 15px;
        line-height: 1.8;
        margin-bottom: 20px;
    }
    .result-detail {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e6e6e6;
    }
    .result-table {
        width: 100%;
        border-collapse: collapse;
    }
    .result-table th,
    .result-table td {
        padding: 10px;
        text-align: center;
        border: 1px solid #e6e6e6;
    }
    .result-table th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    .result-actions {
        text-align: center;
        margin-top: 20px;
        padding: 0 20px 20px;
    }
    .highlight {
        color: #FF5722;
        font-weight: bold;
        font-size: 16px;
        padding: 0 3px;
    }
    /* 确认按钮样式 */
    #confirmResultBtn {
        background-color: #4685FD;
        border-color: #4685FD;
    }
</style>
{/block}
{block name="main"}
<div class="layui-tab ns-table-tab"  lay-filter="points_detail_tab">
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="0">任务一</li>
<!--        <li lay-id="1">任务二</li>-->
    </ul>
    <div class="layui-tab-content">
        <!-- 筛选面板 -->
        <div class="ns-screen layui-collapse" lay-filter="selection_panel">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title"></h2>
                <form class="layui-colla-content layui-form layui-show">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">手机号：</label>
                            <div class="layui-input-inline">
                                <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">类型：</label>
                            <div class="layui-input-inline">
                                <select name="point_type" lay-filter="task_one_permission">
                                    <option value="" selected>全部</option>
                                    {foreach $type_list as $key => $value}
                                    <option value="{$key}">{$value}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">记录时间：</label>
                            <div class="layui-input-inline daterange-input-wrap">
                                <input type="text" class="layui-input daterange-input nc-len-mid" name="record_start_time" id="daterangeStart" placeholder="" autocomplete="off">
                                <span>到</span>
                                <input type="text" class="layui-input daterange-input nc-len-mid" name="record_end_time" id="daterangeEnd" placeholder="" autocomplete="off">
                            </div>
                        </div>
                    </div>



                    <div class="ns-form-row">
                        <button class="layui-btn ns-bg-color" lay-submit lay-filter="submit">搜索</button>
                        <button class="layui-btn layui-btn-primary" id="export">导出</button>
                        <button class="layui-btn layui-btn-primary" id="send_reward">手动发放贡献值</button>
                    </div>
                </form>
            </div>
        </div>
        <!-- 列表 -->
        <table id="points_record_details" lay-filter="points_record_details"></table>
    </div>
</div>

<!-- 单个发放贡献值弹窗 -->
<script type="text/html" id="singleRewardPopup">
    <div class="points-reward-popup">
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">用户手机号：</label>
                <div class="layui-input-block">
                    <input type="text" name="mobile" placeholder="请输入手机号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">发放贡献值：</label>
                <div class="layui-input-block">
                    <input type="text" name="points" placeholder="请输入贡献值" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备注：</label>
                <div class="layui-input-block">
                    <input type="text" name="remark" placeholder="请输入备注信息" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item" style="margin-top: 30px; text-align: center;">
                <button type="button" class="layui-btn layui-btn-primary" id="batchRewardBtn">批量发放</button>
                <button type="button" class="layui-btn ns-bg-color" id="confirmSingleReward">确认发放</button>
            </div>
        </form>
    </div>
</script>

<!-- 批量发放贡献值弹窗 -->
<script type="text/html" id="batchRewardPopup">
    <div class="points-batch-popup">
        <div class="layui-form-item">
            <label class="layui-form-label">发放名单：</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-primary" id="importBtn">导入</button>
                <a class="download-template" href="/public/template/batch_send_point_reward.xls">下载导入模板</a>
                <input type="file" id="fileInput" style="display:none;" accept=".xls,.xlsx">
            </div>
        </div>
        
        <table class="batch-table">
            <thead>
                <tr>
                    <th>手机号</th>
                    <th>发放数量</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody id="batch-table-content">
                <tr>
                    <td colspan="3" style="text-align: center;">请导入发放名单</td>
                </tr>
            </tbody>
        </table>
        
        <!-- 分页控制 -->
        <div class="batch-pagination" style="text-align: center; margin: 10px 0;">
            <div id="batch-page-control"></div>
        </div>
        
        <div class="batch-footer">
            合计发放 <span id="total-users" style="color: #FF5722;">0</span> 人，共 <span id="total-points" style="color: #FF5722;">0</span> 贡献值
        </div>
        
        <div class="batch-actions">
            <button type="button" class="layui-btn ns-bg-color" id="confirmBatchReward">确认发放</button>
        </div>
    </div>
</script>

<!-- 批量发放结果弹窗 -->
<script type="text/html" id="batchResultPopup">
    <div class="points-result-popup">
        <div class="result-header">
            <h3>手动发放结果</h3>
            <span class="result-close" id="closeResultBtn">×</span>
        </div>
        
        <div class="result-content">
            <div class="result-summary">
                发放成功 <span class="success-count highlight">{{d.successNums}}</span> 人，共 <span class="success-points highlight">{{d.successPoints}}</span> 贡献值；
                发放失败 <span class="fail-count highlight">{{d.failNums}}</span> 人，共 <span class="fail-points highlight">{{d.failPoints}}</span> 贡献值未发放
            </div>
            
            <div class="result-detail">
                <table class="result-table">
                    <thead>
                        <tr>
                            <th>手机号</th>
                            <th>发放数量</th>
                            <th>失败原因</th>
                        </tr>
                    </thead>
                    <tbody id="result-table-content">
                        {{# if(d.failList && d.failList.length === 0){ }}
                        <tr>
                            <td colspan="3" style="text-align: center;">无失败记录</td>
                        </tr>
                        {{# } else if(d.failList && d.failList.length > 0) { }}
                        {{# layui.each(d.failList, function(index, item){ }}
                        <tr>
                            <td>{{item.mobile}}</td>
                            <td>{{item.points}}</td>
                            <td>{{item.fail_reason || '-'}}</td>
                        </tr>
                        {{# }); }}
                        {{# } else { }}
                        <tr>
                            <td colspan="3" style="text-align: center;">无失败记录</td>
                        </tr>
                        {{# } }}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="result-actions">
            {{# if(d.fileUrl){ }}
            <a href="{{d.fileUrl}}" class="layui-btn layui-btn-primary" target="_blank">导出发放结果</a>
            {{# } }}
            <button type="button" class="layui-btn" id="confirmResultBtn">确认</button>
        </div>
    </div>
</script>
{/block}
{block name="script"}
<script type="application/javascript">
    layui.use(['form', 'laytpl','laydate', 'element', 'layer'], function() {
        var table,
            laydate = layui.laydate;
        var form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element,
            layer = layui.layer;
        form.render();
        //日期范围
        laydate.render({
            elem: '#daterangeStart'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        laydate.render({
            elem: '#daterangeEnd'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        table = new Table({
            elem: '#points_record_details',
            url: ns.url("leaguePoints/admin/PointsRecord/lists"),
            cols: [
                [
                    {
                        field: 'mobile',
                        title: '手机号',
                        align: 'left',
                        unresize: 'false',
                        width: '10%'
                    },
                    {
                        field: 'point_type_text',
                        title: '积分类型',
                        align: 'left',
                        unresize: 'false',
                        width: '10%'
                    },
                    {
                        field: 'point',
                        title: '积分变动',
                        align: 'left',
                        unresize: 'false',
                        width: '10%'
                    },
                    {
                        field: 'remark',
                        title: '备注',
                        align: 'left',
                        unresize: 'false',
                        width: '15%'
                    },
                    {
                        field: 'admin_user',
                        title: '操作人',
                        align: 'left',
                        unresize: 'false',
                        width: '10%'
                    },
                    {
                        field: 'create_time',
                        title: '记录时间',
                        align: 'left',
                        unresize: 'false',
                        width: '10%'
                    }
                ]
            ]
        });
        //监听Tab切换，以改变地址hash值
        element.on('tab(points_detail_tab)', function(){
            var status=this.getAttribute('lay-id');
            $('.layui-form')[0].reset();
            form.render();
            table.reload({
                page: {curr: 1},
                where:{status},
            })
        });
        form.on('submit(submit)', function(data){
            table.reload({
                page: {curr: 1},
                where: data.field,
            })
            return false;
        })

        $('#export').on('click',function () {
            let data={
                mobile:$('[name=mobile]').val(),
                record_start_time:$('[name=record_start_time]').val(),
                record_end_time:$('[name=record_end_time]').val(),
                point_type:$('[name=point_type]').val(),
            }
            var index=layer.load(1)
            $.ajax({
                type: "post",
                url: ns.url("leaguePoints/admin/PointsRecord/export"),
                dataType: 'json',
                async: false,
                data,
                success: function (res) {
                    if (res.code == 0) {
                        layer.close(index);
                        window.location.href=res.data.path;
                    }
                }
            })
            return false;
        })
        
        // 手动发放贡献值按钮点击事件
        $('#send_reward').on('click', function() {
            layer.open({
                type: 1,
                title: '手动发放贡献值',
                area: ['500px', '400px'],
                content: $('#singleRewardPopup').html(),
                success: function(layero, index) {
                    // 批量发放按钮点击事件
                    $(layero).find('#batchRewardBtn').on('click', function() {
                        layer.close(index);
                        openBatchRewardPopup();
                    });
                    
                    // 确认发放按钮点击事件
                    $(layero).find('#confirmSingleReward').on('click', function() {
                        var mobile = $(layero).find('input[name="mobile"]').val();
                        var points = $(layero).find('input[name="points"]').val();
                        var remark = $(layero).find('input[name="remark"]').val();
                        
                        if (!mobile) {
                            layer.msg('请输入手机号', {icon: 2});
                            return;
                        }
                        if (!points) {
                            layer.msg('请输入贡献值', {icon: 2});
                            return;
                        }
                        
                        var loadingIndex = layer.load(1);
                        $.ajax({
                            type: "post",
                            url: ns.url("leaguePoints/admin/PointsRecord/sendSingle"),
                            dataType: 'json',
                            data: {
                                mobile: mobile,
                                points: points,
                                remark: remark || '手动发放贡献值'
                            },
                            success: function (res) {
                                layer.close(loadingIndex);
                                if (res.code == 0) {
                                    layer.msg('发放成功', {icon: 1});
                                    layer.close(index);
                                    table.reload();
                                } else {
                                    layer.msg(res.message || '发放失败', {icon: 2});
                                }
                            },
                            error: function() {
                                layer.close(loadingIndex);
                                layer.msg('网络错误，请重试', {icon: 2});
                            }
                        });
                    });
                }
            });
            return false;
        });
        
        // 打开批量发放弹窗
        function openBatchRewardPopup() {
            layer.open({
                type: 1,
                title: '手动发放贡献值',
                area: ['800px', '750px'],
                content: $('#batchRewardPopup').html(),
                success: function(layero, index) {
                    // 全局变量保存批量数据
                    var batchData = [];
                    
                    // 导入按钮点击事件
                    $(layero).find('#importBtn').on('click', function() {
                        $(layero).find('#fileInput').click();
                    });
                    
                    // 文件选择事件
                    $(layero).find('#fileInput').on('change', function(e) {
                        var files = e.target.files;
                        if (files.length === 0) {
                            return;
                        }
                        
                        var formData = new FormData();
                        formData.append('file', files[0]);
                        
                        var loadingIndex = layer.load(1, {shade: [0.3, '#fff']});
                        
                        // 上传文件
                        $.ajax({
                            url: ns.url("leaguePoints/admin/PointsRecord/importSend"),
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(res) {
                                layer.close(loadingIndex);
                                if (res.code === 0) {
                                    // 显示错误链接
                                    if (res.data.error_url) {
                                        layer.msg('部分数据无效，<a href="' + res.data.error_url + '" class="layui-font-blue" target="_blank">点击下载错误详情</a>', {icon: 1});
                                    } else {
                                        layer.msg(res.message, {icon: 1});
                                    }
                                    
                                    // 更新全局变量
                                    batchData = res.data.data;
                                    
                                    // 更新表格和统计信息
                                    refreshBatchTable(layero, batchData);
                                } else {
                                    layer.msg(res.message, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.close(loadingIndex);
                                layer.msg('网络错误，请重试', {icon: 2});
                            }
                        });
                    });
                    
                    // 确认发放按钮点击事件
                    $(layero).find('#confirmBatchReward').on('click', function() {
                        if (batchData.length === 0) {
                            layer.msg('请先导入发放名单', {icon: 2});
                            return;
                        }
                        
                        // 计算总贡献值
                        var totalPoints = 0;
                        for (var i = 0; i < batchData.length; i++) {
                            totalPoints += parseFloat(batchData[i].points);
                        }
                        
                        // 添加确认弹框
                        layer.confirm('<div class="confirm-dialog">将要向 <span class="highlight">' + batchData.length + '</span> 人发放共 <span class="highlight">' + totalPoints + '</span> 贡献值，请确定发放</div>', {
                            btn: ['暂不发放', '确认发放'],
                            title: '确认操作',
                            area: ['400px', 'auto'],
                            closeBtn: 0,
                            shadeClose: true,
                            btn2: function(index) {
                                // 点击确认发放按钮时的处理
                                layer.close(index);
                                
                                var loadingIndex = layer.load(1);
                                $.ajax({
                                    type: "post",
                                    url: ns.url("leaguePoints/admin/PointsRecord/sendBatch"),
                                    dataType: 'json',
                                    data: {
                                        data: JSON.stringify(batchData)
                                    },
                                    success: function (res) {
                                        layer.close(loadingIndex);
                                        if (res.code == 0 || res.code == 1) {
                                            // 显示发放结果弹窗
                                            showBatchResult(res.data);
                                            layer.close(index);
                                        } else {
                                            layer.msg(res.message || '批量发放失败', {icon: 2});
                                        }
                                    },
                                    error: function() {
                                        layer.close(loadingIndex);
                                        layer.msg('网络错误，请重试', {icon: 2});
                                    }
                                });
                                
                                return false;
                            }
                        }, function(index) {
                            // 点击暂不发放按钮时的处理
                            layer.close(index);
                            return false;
                        });
                    });
                }
            });
        }
        
        // 刷新批量表格数据
        function refreshBatchTable(layero, data) {
            // 全局数据
            window.batchTableData = data;
            
            // 如果数据为空
            if (data.length === 0) {
                $(layero).find('#batch-table-content').html('<tr><td colspan="3" style="text-align: center;">请导入发放名单</td></tr>');
                $(layero).find('#total-users').text(0);
                $(layero).find('#total-points').text(0);
                $(layero).find('#batch-page-control').empty();
                return;
            }
            
            // 计算总积分
            var totalPoints = 0;
            for (var i = 0; i < data.length; i++) {
                totalPoints += parseFloat(data[i].points);
            }
            
            // 更新统计信息
            $(layero).find('#total-users').text(data.length);
            $(layero).find('#total-points').text(totalPoints);
            
            // 初始化分页
            renderTablePage(layero, 1);
        }
        
        // 渲染分页表格
        function renderTablePage(layero, currentPage) {
            var data = window.batchTableData || [];
            var pageSize = 10; // 每页显示10条
            var totalPages = Math.ceil(data.length / pageSize);
            
            // 计算当前页的数据
            var startIndex = (currentPage - 1) * pageSize;
            var endIndex = Math.min(startIndex + pageSize, data.length);
            var pageData = data.slice(startIndex, endIndex);
            
            // 渲染表格内容
            var html = '';
            if (pageData.length === 0) {
                html = '<tr><td colspan="3" style="text-align: center;">当前页无数据</td></tr>';
            } else {
                for (var i = 0; i < pageData.length; i++) {
                    var item = pageData[i];
                    html += '<tr>' +
                        '<td>' + item.mobile + '</td>' +
                        '<td>' + item.points + '</td>' +
                        '<td>' + item.remark + '</td>' +
                        '</tr>';
                }
            }
            
            $(layero).find('#batch-table-content').html(html);
            
            // 渲染分页控件
            if (totalPages > 1) {
                var pageHtml = '<div class="layui-box layui-laypage layui-laypage-default">';
                
                // 上一页
                if (currentPage > 1) {
                    pageHtml += '<a href="javascript:;" class="layui-laypage-prev" data-page="' + (currentPage - 1) + '">上一页</a>';
                } else {
                    pageHtml += '<a href="javascript:;" class="layui-laypage-prev layui-disabled">上一页</a>';
                }
                
                // 页码
                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(totalPages, startPage + 4);
                
                if (startPage > 1) {
                    pageHtml += '<a href="javascript:;" data-page="1">1</a>';
                    if (startPage > 2) {
                        pageHtml += '<span class="layui-laypage-spr">…</span>';
                    }
                }
                
                for (var i = startPage; i <= endPage; i++) {
                    if (i === currentPage) {
                        pageHtml += '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' + i + '</em></span>';
                    } else {
                        pageHtml += '<a href="javascript:;" data-page="' + i + '">' + i + '</a>';
                    }
                }
                
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        pageHtml += '<span class="layui-laypage-spr">…</span>';
                    }
                    pageHtml += '<a href="javascript:;" data-page="' + totalPages + '">' + totalPages + '</a>';
                }
                
                // 下一页
                if (currentPage < totalPages) {
                    pageHtml += '<a href="javascript:;" class="layui-laypage-next" data-page="' + (currentPage + 1) + '">下一页</a>';
                } else {
                    pageHtml += '<a href="javascript:;" class="layui-laypage-next layui-disabled">下一页</a>';
                }
                
                pageHtml += '</div>';
                
                $(layero).find('#batch-page-control').html(pageHtml);
                
                // 绑定分页事件
                $(layero).find('#batch-page-control a[data-page]').on('click', function() {
                    var page = $(this).data('page');
                    renderTablePage(layero, page);
                });
            } else {
                $(layero).find('#batch-page-control').empty();
            }
        }

        // 显示批量发放结果
        function showBatchResult(data) {
            // 准备失败列表数据
            var failList = [];
            if (data.failNums > 0 && data.failList) {
                failList = data.failList;
            }
            
            // 使用layui模板引擎渲染弹窗内容
            var getTpl = $('#batchResultPopup').html();
            var renderData = {
                successNums: data.successNums || 0,
                successPoints: data.successPoints || 0,
                failNums: data.failNums || 0,
                failPoints: data.failPoints || 0,
                fileUrl: data.fileUrl || '',
                failList: failList
            };
            
            laytpl(getTpl).render(renderData, function(html) {
                // 打开弹窗
                var resultIndex = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 0,
                    area: ['700px', '550px'],
                    content: html,
                    success: function(layero, index) {
                        // 关闭按钮点击事件
                        $(layero).find('#closeResultBtn').on('click', function() {
                            layer.close(index);
                            table.reload(); // 刷新表格
                        });
                        
                        // 确认按钮点击事件
                        $(layero).find('#confirmResultBtn').on('click', function() {
                            layer.close(index);
                            table.reload(); // 刷新表格
                        });
                    }
                });
            });
        }
    })
</script>
{/block}