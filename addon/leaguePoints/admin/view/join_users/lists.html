{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .daterange-input{
        display: inline-block;
    }
</style>
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title"></h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">手机号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">任务一权限：</label>
                    <div class="layui-input-inline">
                        <select name="task_one_permission" lay-filter="task_one_permission">
                            <option value="" selected>全部</option>
                            <option value="1">已开放</option>
                            <option value="0">未开放</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 124px;">贡献值统计时段：</label>
                    <div class="layui-input-inline daterange-input-wrap">
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="complete_start_time" id="daterangeStart" placeholder="" autocomplete="off">
                        <span>到</span>
                        <input type="text" class="layui-input daterange-input nc-len-mid" name="complete_end_time" id="daterangeEnd" placeholder="" autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">标签：</label>
<!--                    <div class="layui-input-inline">-->
<!--                        <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">-->
<!--                    </div>-->
                    {include file="app/admin/view/member/member_tag_component.html"}
                </div>
            </div>



            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="submit">搜索</button>
                <button class="layui-btn layui-btn-primary" id="export">导出</button>
                <button class="layui-btn layui-btn-primary" id="add_user">添加新用户</button>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<div class="join_users_list_father">
    <table id="join_users_list" lay-filter="join_users_list"></table>
</div>

<script type="text/html" id="get_point_show_html">
    <a href="#" style="color: #0d73f9" class="user_point_show" lay-event="user_point_show">{{ d.get_point}}</a>
</script>

<script type="text/html" id="user_point_show_html">
    <a href="#" style="color: #0d73f9" class="user_point_show" lay-event="user_point_show">{{ d.use_point}}</a>
</script>

<!--添加新用户-->
<script type="text/html" id="addUserPopup">
    <div class="layui-form">
        <p style="margin-bottom: 15px;">请填写加入活动的用户手机号，输入多个用户以换行分隔</p>
        <textarea placeholder="请输入手机号码" class="layui-textarea" style="min-height: 250px;" name="add_user_text"></textarea>
    </div>
</script>

<!--新用户任务确认-->
<script type="text/html" id="userTaskPopup">
    <div class="layui-form">
        <p style="margin-bottom: 15px;">已添加{{d.user_count}}个用户，确定要添加到贡献值活动参与名单？</p>
        <div class="layui-form-item">
<!--            <input type="checkbox" name="task_one_permission" title="开放任务一权限" lay-skin="primary" checked disabled>-->
<!--            <input type="checkbox" name="task_one_permission" title="开放任务二权限">-->
        </div>
    </div>
</script>

<!--用户积分明细-->
<script type="text/html" id="userPointsDetails">
    <table id="points_record_list" lay-filter="points_record_list"></table>
</script>

<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
    <button class="layui-btn layui-btn-primary" lay-event="off_tasks">批量关闭权限</button>
    <button class="layui-btn layui-btn-primary" lay-event="on_tasks">批量开启权限</button>
</script>

{/block}
{block name="script"}
<script type="application/javascript">
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var table,
            laydate = layui.laydate;
        var form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        form.render();

        var end_time = new Date();
        var start_time = new Date();
        start_time = start_time.setMonth(start_time.getMonth()-1);
        start_time = new Date(start_time);
        //日期范围
        laydate.render({
            elem: '#daterangeStart'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            ,value: start_time
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        laydate.render({
            elem: '#daterangeEnd'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , type: 'datetime'
            , value: end_time
            // ,value:daterange //必须遵循format参数设定的格式
            , done: function (value, date, endDate) {

            }
        });
        var points_record_list_table;
        let bottomToolbar = __operateGroupBottomToolbar("#batchOperation");
        table = new Table({
            elem: '#join_users_list',
            url: ns.url("/leaguePoints/admin/JoinUsers/lists"),
            bottomToolbar: bottomToolbar,
            cols: [
                [
                    {
                        type: 'checkbox',
                        unresize: 'false',
                    },
                    {
                        field: 'nickname',
                        title: '姓名',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'order_count',
                        title: '当前在加盟订单数',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'get_point',
                        title: '获取贡献值',
                        unresize: 'false',
                        align: 'left',
                        templet:'#get_point_show_html'
                    },
                    {
                        field: 'use_point',
                        title: '已使用贡献值',
                        align: 'left',
                        unresize: 'false',
                        templet:'#user_point_show_html'
                    },
                    {
                        field: 'sum_count',
                        title: '贡献值完成任务一',
                        unresize: 'false',
                        align: 'left',
                    },
                    {
                        field: 'enable',
                        title: '任务一权限',
                        align: 'left',
                        unresize: 'false',
                        templet:function (data) {
                            return '<input type="checkbox" lay-skin="switch" lay-filter="switchOne" lay-text="已开放|未开放" '+(data.enable ? 'checked' : '')+' data-id="'+data.id+'">'
                        }
                    },
                    {
                        field: 'create_time',
                        title: '加入时间',
                        align: 'left',
                        unresize: 'false',
                    }
                ]
            ]
        });
        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            var member_id = obj.data.member_id;
            switch (obj.event) {
                case 'user_point_show':
                    laytpl($('#userPointsDetails').html()).render(data,function (html) {
                        layer.open({
                            type: 1,
                            area: ['1000px', '800px'],
                            title: '积分记录明细',
                            content: html,
                            success:function () {
                                points_record_list_table = new Table({
                                    elem: '#points_record_list',
                                    height: '620px',
                                    url: ns.url("leaguePoints/admin/PointsRecord/lists"),
                                    where:{member_id},
                                    cols: [
                                        [
                                            {
                                                field: 'mobile',
                                                title: '手机号',
                                                align: 'left',
                                                unresize: 'false',
                                            },
                                            {
                                                field: 'point_type_text',
                                                title: '积分类型',
                                                align: 'left',
                                                unresize: 'false',
                                            },
                                            {
                                                field: 'point',
                                                title: '积分变动',
                                                align: 'left',
                                                unresize: 'false',
                                            },
                                            {
                                                field: 'create_time',
                                                title: '记录时间',
                                                align: 'left',
                                                unresize: 'false',
                                            }
                                        ]
                                    ]
                                });
                            }
                        })
                    })
                    break;
            }
        })
        // 批量操作
        table.bottomToolbar(function (obj) {
            if (obj.data.length < 1) {
                layer.msg('请选择要操作的数据');
                return;
            }

            // var id_array = new Array();
            // for (i in obj.data) id_array.push(obj.data[i].goods_id);
            switch (obj.event) {
                case 'off_tasks':
                    //关闭
                    change_status(obj.data,false,function () {
                        table.reload({
                            page: {curr: 1},
                        })
                    },function () {

                    })
                    break;
                case 'on_tasks':
                    //开启
                    change_status(obj.data,true,function () {
                        table.reload({
                            page: {curr: 1},
                        })
                    },function () {

                    })
                    break;
            }
        });

        form.on('submit(submit)', function(data){
            table.reload({
                page: {curr: 1},
                where: data.field,
            })
            return false;
        })
        form.on('switch(switchOne)',function (data) {
            var checked = data.elem.checked;
            var id = data.elem.dataset.id;
            var data_list = table._table.cache.join_users_list.filter(item=>item.id==id)
            var nickname = data_list.length ? data_list[0].nickname : '';
            change_status([{id,nickname}],checked,function () {

            },function () {
                data.elem.checked = !checked;
                form.render();
            })
        })
        function change_status(ids_data = [], status,successCallback,errorCallback){
            var ids_list = [];
            var nickname_list = [];
            for (let i = 0; i < ids_data.length; i++) {
                ids_list.push(ids_data[i].id);
                nickname_list.push("【"+ids_data[i].nickname+"】");
            }
            var msg = !status ? "确定取消"+nickname_list.join(' ')+"参与贡献值活动的权限？取消后用户将无法通过规则生产贡献值，但用户当月剩余贡献值仍可以完成任务。" : '确定要给'+nickname_list.join(' ')+'开放任务一的权限？'
            layer.confirm(msg,{
                btn: ['确定', '取消']
            },function (index) {
                var load=layer.load(1);
                $.ajax({
                    type: "post",
                    url: ns.url("/leaguePoints/admin/JoinUsers/changeStatus"),
                    dataType: 'json',
                    async: false,
                    data: {id:ids_list.join(','),enable:status ? 1 : 0},
                    success: function (res) {
                        layer.close(load);
                        if(res.code == 0){
                            layer.close(index);
                            layer.msg(res.message,function () {
                                if(successCallback && typeof successCallback=='function'){
                                    successCallback()
                                }
                            })
                        }else{
                            layer.close(index);
                            layer.msg(res.message,function () {
                                if(errorCallback && typeof errorCallback == 'function'){
                                    errorCallback()
                                }
                            });
                        }
                    }
                })
            },function (index) {
                layer.close(index);
                if(errorCallback && typeof errorCallback == 'function'){
                    errorCallback()
                }
            })
        }
        $('#add_user').on('click',function () {
            var layer_one = layer.open({
                type: 1,
                area: ['500px', '500px'],
                title: '添加新用户',
                content: $('#addUserPopup').html(),
                btn: ['提交', '取消'], //按钮
                yes: function(res) {
                    var add_user_text = $('textarea[name=add_user_text]').val();
                    if(!add_user_text){
                        layer.msg('请输入用户手机号');
                        return;
                    }
                    laytpl($('#userTaskPopup').html()).render({user_count:add_user_text.split(/\r?\n/).length},function(html){
                        var layer_two = layer.open({
                            type: 1,
                            area: ['350px', '200px'],
                            title: '新用户任务',
                            content: html,
                            btn: ['确定', '取消'], //按钮
                            success: function () {
                              form.render();
                            },
                            yes: function(res2) {
                                var load=layer.load(1);
                                $.ajax({
                                    type: "post",
                                    url: ns.url("/leaguePoints/admin/PointsRules/addMembers"),
                                    dataType: 'json',
                                    async: false,
                                    data: {mobiles: add_user_text},
                                    success: function (res) {
                                        layer.close(load);
                                        if(res.code == 0){
                                            layer.close(layer_two);
                                            layer.close(layer_one);

                                            var tip_html = '<p>提交用户手机号'+res.data.all_nums+'个，已添加进活动名单的用户'+res.data.add_nums+'个，剩余'+res.data.no_register_nums+'个未在小程序注册，具体名单如下：</p>';
                                            if(res.data.no_register_mobiles && res.data.no_register_mobiles.length>0){
                                                for (let i = 0; i < res.data.no_register_mobiles.length; i++) {
                                                    tip_html = tip_html + '<p>'+res.data.no_register_mobiles[i]+'</p>';
                                                }
                                            }else{
                                                tip_html = tip_html + '<p>无</p>';
                                            }
                                            var add_result_tip = layer.open({
                                                type: 1,
                                                area: ['350px', 'auto'],
                                                title: '新用户添加结果',
                                                content: tip_html ,
                                                btn: ['返回'], //按钮
                                                yes: function () {
                                                    layer.close(add_result_tip)
                                                }
                                            })
                                            table.reload({
                                                page: {curr: 1}
                                            })
                                        }else {
                                            layer.msg(res.msg);
                                        }
                                    }
                                })
                            }
                        })
                    })
                },
                but2: function (index) {
                    layer.close(layer_one);
                }
            })
            return false;
        })

        $('#export').on('click',function () {
            let data={
                mobile:$('[name=mobile]').val(),
                task_one_permission:$('[name=task_one_permission]').val(),
                complete_start_time:$('[name=complete_start_time]').val(),
                complete_end_time:$('[name=complete_end_time]').val(),
                tag_ids:$('#tag_ids').val(),
                tag_status:$(':radio[name="tag_status"]:checked').val(),
            }
            var index=layer.load(1)
            $.ajax({
                type: "post",
                url: ns.url("leaguePoints/admin/JoinUsers/export"),
                dataType: 'json',
                async: false,
                data,
                success: function (res) {
                    if (res.code == 0) {
                        layer.close(index);
                        window.location.href=res.data.path;
                    }
                }
            })
            return false;
        })
    })
</script>
{/block}