{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .layui-tab-item{
        padding-top: 20px;
        box-sizing: border-box;
    }
    .layui-form-checkbox{
        margin-top: 0!important;
    }
</style>
{/block}
{block name="main"}
<div class="layui-tab ns-table-tab"  lay-filter="config_tab">
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="league_1">任务一</li>
<!--        <li lay-id="league_2">任务二</li>-->
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show league_1">
            <form class="layui-form" lay-filter="league_form">
                <div class="layui-form-item">
                    <label class="layui-form-label">每个任务消耗贡献值：</label>
                    <div class="layui-input-inline">
                        <input type="number" name="task_use_point" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                    <p class="layui-form-mid">全部加盟类目任务一均默认该值，也可在类目贡献值管理中单独配置。</p>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产生贡献值规则：</label>
                    <p class="layui-form-mid">规则配置可随时修改，但仅影响后续用户行为，已产生的贡献值不影响。</p>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" name="recommend_register_enable">
                        <span class="layui-form-mid">新增直接推荐的用户，每增加一个加</span>
                        <input type="number" name="recommend_register_rule_val" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">分，每月得分上限</span>
                        <input type="number" name="recommend_register_rule_nums_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" name="add_shop_fans_enable">
                        <span class="layui-form-mid">新增店铺粉丝，每增加一个加</span>
                        <input type="number" name="add_shop_fans_rule_val" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">分，每月得分上限</span>
                        <input type="number" name="add_shop_fans_rule_nums_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" name="recommend_browse_enable">
                        <span class="layui-form-mid">直推好友浏览数，每增加</span>
                        <input type="number" name="recommend_browse_rule_nums" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">人浏览加</span>
                        <input type="number" name="recommend_browse_rule_val" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">分，每月得分上限</span>
                        <input type="number" name="recommend_browse_rule_nums_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" name="shop_fans_browse_enable">
                        <span class="layui-form-mid">店铺粉丝浏览数，每增加</span>
                        <input type="number" name="shop_fans_browse_rule_nums" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">人浏览加</span>
                        <input type="number" name="shop_fans_browse_rule_val" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">分，每月得分上限</span>
                        <input type="number" name="shop_fans_browse_rule_nums_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" name="recommend_shop_enable">
                        <span class="layui-form-mid">直推用户首笔付费加盟，每增加一个加</span>
                        <input type="number" name="recommend_shop_rule_val" value="0"  autocomplete="off" class="layui-input ns-len-short">
                        <span class="layui-form-mid">分，每月得分上限</span>
                        <input type="number" name="recommend_shop_rule_nums_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" lay-filter="promote_task_filter" name="promote_task_enable">
                        <span class="layui-form-mid">完成指定商品推广任务可获得对应贡献值</span>
                        <span class="layui-form-mid" style="color: #4685FD;cursor: pointer;" onclick="location.href = ns.url('leaguePoints://admin/PromoteProductManagement/lists', {league_task_key: $('.layui-tab-title .layui-this').attr('lay-id')})">管理推广任务商品</span>
                        <span class="layui-form-mid">，每月得分上限</span>
                        <input type="number" name="task_complete_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block" style="display: flex;align-items: center;">
                        <input type="checkbox" lay-skin="primary" lay-filter="promote_task_filter" name="sale_task_enable">
                        <span class="layui-form-mid">店铺销售指定商品达标可获得对应贡献值</span>
                        <span class="layui-form-mid" style="color: #4685FD;cursor: pointer;" onclick="location.href = ns.url('leaguePoints://admin/SaleTaskManagement/lists', {league_task_key: $('.layui-tab-title .layui-this').attr('lay-id')})">管理销售任务商品</span>
                        <span class="layui-form-mid">，每月得分上限</span>
                        <input type="number" name="sale_task_max" value="0"  autocomplete="off" class="layui-input ns-len-short">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">活动规则说明：</label>
                    <div class="layui-input-block">
                        <script id="league_1_editor" type="text/plain" class="ns-special-length" style="height:300px;"></script>
                    </div>
                </div>
            </form>
            <div class="ns-form-row">
                <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary" onclick="back()">返回</button>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="application/javascript">
    var league_1_ue = UE.getEditor('league_1_editor');
    var league_data = {};
    layui.use(['form', 'laytpl','laydate', 'element'], function() {
        var laydate = layui.laydate;
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        form.render();
        function getData() {
            var loadIndex = layer.msg('加载中', {
                icon: 16,
                shade: 0.01,
                time:0
            });
            $.ajax({
                type: "post",
                url: ns.url("/leaguePoints/admin/PointsRules/config"),
                dataType: 'json',
                async: false,
                success: function (res) {
                    layer.close(loadIndex);
                    if(res.code == 0){
                        league_data = res.data;
                        var league_1 = res.data.league_1;
                        if(league_1){
                            console.log("goods_reward_points值:", league_1.goods_reward_points);
                            console.log("是否等于1:", league_1.goods_reward_points == 1);
                            console.log("复选框元素存在:", $('.league_1 input[name=promote_task_enable]').length);
                            
                            $('.league_1 input[name=task_use_point]').val(league_1.task_use_point);
                            $('.league_1 input[name=task_complete_max]').val(league_1.task_complete_max);
                            $('.league_1 input[name=sale_task_max]').val(league_1.sale_task_max);
                            
                            // 使用layui表单赋值方法处理复选框
                            form.val('league_form', {
                                'promote_task_enable': parseInt(league_1.goods_reward_points) === 1,
                                'sale_task_enable': parseInt(league_1.sale_task_enable) === 1
                            });
                            
                            console.log("设置后复选框状态:", $('.league_1 input[name=promote_task_enable]').prop('checked'));
                            form.render('checkbox');
                            
                            for (const key in league_1.rules) {
                                var key_list = Object.keys(league_1.rules[key]);
                                var rule_key = league_1.rules[key]['rule_key'];
                                key_list = key_list.filter(function (item) {
                                    if(item == rule_key){
                                        return false;
                                    }else{
                                        return true;
                                    }
                                })
                                key_list.map(function (item) {
                                    var input_name = rule_key+ '_' + item;
                                    if(item=='enable'){
                                        $('.league_1 input[name='+input_name+']').prop('checked',league_1.rules[key][item]==1);
                                    }else{
                                        $('.league_1 input[name='+input_name+']').val(league_1.rules[key][item]);
                                    }
                                })
                            }
                            form.render();
                            league_1_ue.ready(function() {
                                league_1_ue.setContent(league_1.rule_content);
                            });
                        }
                    }
                }
            })
        }
        getData();

        form.on('submit(save)',function (){
            layer.confirm('确认保存配置？', {
                btn: ['确定', '取消'] //按钮
            }, function(){
                var tab_id = $('.layui-tab-title .layui-this').attr('lay-id');
                var league_row = league_data[tab_id];
                var params = {
                    config_id : league_row.config_id,
                    league_task_key: league_row.league_task_key,
                    task_use_point: $('.'+tab_id+' input[name=task_use_point]').val(),
                    goods_reward_points: $('.'+tab_id+' input[name=promote_task_enable]').prop('checked') ? 1 : 0,
                    task_complete_max: $('.'+tab_id+' input[name=task_complete_max]').val(),
                    sale_task_enable: $('.'+tab_id+' input[name=sale_task_enable]').prop('checked') ? 1 : 0,
                    sale_task_max: $('.'+tab_id+' input[name=sale_task_max]').val(),
                }
                if(tab_id == 'league_1'){
                    params.rule_content = league_1_ue.getContent();
                }
                var rules = {};
                $('.'+tab_id+' input').each(function(){
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if($(this).attr('type')=='checkbox'){
                        value = $(this).prop('checked')?1:0;
                    }
                    var rule_key = '';
                    for (const key in league_row.rules) {
                        if(name.indexOf(league_row.rules[key].rule_key)!=-1){
                            rule_key = league_row.rules[key].rule_key;
                            break;
                        }
                    }
                    var val_key = name.substring(rule_key.length+1, name.length);
                    if(rule_key && val_key){
                        if (rules[rule_key]){
                            rules[rule_key][val_key] = value;
                        }else{
                            var tmp = {};
                            tmp[val_key] = value;
                            rules[rule_key] =tmp;
                        }
                        rules[rule_key].rule_key = rule_key;
                    }
                })
                for (const rulesKey in rules) {
                    if(!Object.hasOwn(rules[rulesKey],'rule_nums')){
                        rules[rulesKey].rule_nums = '1';
                    }
                }
                params.rules = Object.values(rules);

                var loadIndex = layer.msg('保存中', {
                    icon: 16,
                    shade: 0.01,
                    time:0
                });
                $.ajax({
                    type: "post",
                    url: ns.url("leaguePoints/admin/PointsRules/edit"),
                    dataType: 'json',
                    data:params,
                    async: false,
                    success: function (res) {
                        layer.close(loadIndex);
                        if(res.code == 0){
                            layer.msg(res.message,{icon: 1})
                        }else{
                            layer.msg(res.message,{icon: 2})
                        }
                    }
                })
            }, function(){

            });

        })
    })
    function back() {
        history.back();
    }
</script>
{/block}