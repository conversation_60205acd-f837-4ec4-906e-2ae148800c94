{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
  .daterange-input{
    display: inline-block;
  }
  .task-review-img{
    width: 150px;
    height: auto;
  }
  /* 添加长文本样式控制 */
  .remark-content {
    max-height: 200px;
    overflow-y: auto;
    word-break: break-all;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    text-align: left;
  }
  /* 修复弹窗样式 */
  .task-review .layui-form-item .layui-input-block {
    max-width: 450px;
    overflow: hidden;
  }
  /* 添加弹窗内容区域滚动条 */
  .layui-layer-content {
    overflow-y: auto !important;
  }
  .task-review {
    padding: 15px 0;
  }
</style>
{/block}
{block name="main"}
<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
  <div class="layui-colla-item">
    <h2 class="layui-colla-title"></h2>
    <form class="layui-colla-content layui-form layui-show">
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">手机号：</label>
          <div class="layui-input-inline">
            <input type="text" name="mobile" placeholder="" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-inline">
          <label class="layui-form-label" style="width: 124px;">提交时间：</label>
          <div class="layui-input-inline daterange-input-wrap">
            <input type="text" class="layui-input daterange-input nc-len-mid" name="complete_start_time" id="daterangeStart" placeholder="" autocomplete="off">
            <span>到</span>
            <input type="text" class="layui-input daterange-input nc-len-mid" name="complete_end_time" id="daterangeEnd" placeholder="" autocomplete="off">
          </div>
        </div>
        <div class="layui-inline">
          <label class="layui-form-label">审核状态：</label>
          <div class="layui-input-inline">
            <select name="status" lay-filter="">
              <option value="">全部</option>
              <option value="10">待审核</option>
              <option value="100">审核通过</option>
              <option value="-1">审核拒绝</option>
            </select>
          </div>
        </div>
      </div>

      <div class="ns-form-row">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="submit">搜索</button>
        <button class="layui-btn layui-btn-primary">导出</button>
      </div>
    </form>
  </div>
</div>
<!-- 列表 -->
<table id="promote_task_list" lay-filter="promote_task_list"></table>
<!-- 操作 -->
<script type="text/html" id="operation">
  <div class="ns-table-btn">
    {{# if(d.allow_edit) { }}
    <a class="layui-btn" lay-event="edit">编辑</a>
    {{# } }}
    {{# if(d.allow_show) { }}
    <a class="layui-btn" lay-event="detail">查看</a>
    {{# } }}
    {{# if(d.allow_close) { }}
    <a class="layui-btn" lay-event="end">提前结束</a>
    {{# } }}
    {{# if(d.allow_delete) { }}
    <a class="layui-btn" lay-event="remove">删除</a>
    {{# } }}
  </div>
</script>
<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
  <button class="layui-btn layui-btn-primary" lay-event="off_tasks">批量驳回</button>
  <button class="layui-btn layui-btn-primary" lay-event="on_tasks">批量通过</button>
</script>

<script type="text/html" id="taskReview">
  <div class="layui-form task-review">
    <div class="layui-form-item">
      <label class="layui-form-label sm">提交用户：</label>
      <div class="layui-input-block">
        <p>{{ d.member_display || d.member_mobile || d.member_id || '未知' }}</p>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label sm">商品名称：</label>
      <div class="layui-input-block">
        <p>{{ d.goods_name || d.goods_id || '未知' }}</p>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label sm">订单号：</label>
      <div class="layui-input-block">
        <p>{{ d.order_no || '无' }}</p>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label sm">截图：</label>
      <div class="layui-input-block">
        {{# if(d.images){ }}
        {{# var imgs = d.images.split(','); }}
        {{# layui.each(imgs, function(index, url){ }}
        <img src="{{ url }}" alt="任务截图" class="task-review-img">
        {{# }); }}
        {{# } else { }}
        <p>无截图</p>
        {{# } }}
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label sm">补充说明：</label>
      <div class="layui-input-block">
        {{# if(d.remark){ }}
        <div class="remark-content">{{ d.remark }}</div>
        {{# } else { }}
        <p>无</p>
        {{# } }}
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label sm">审核意见：</label>
      <div class="layui-input-block">
        <input type="radio" name="status" placeholder="" autocomplete="off" class="layui-input" value="100" title="通过" checked>
        <input type="radio" name="status" placeholder="" autocomplete="off" class="layui-input" value="-1" title="驳回" >
      </div>
      <div class="layui-input-block" style="margin-left: 80px;">
        <textarea name="remark" placeholder="请输入审核备注" class="layui-textarea" maxlength="100"></textarea>
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
        <input type="hidden" name="member_goods_task_id" value="{{ d.member_goods_task_id }}">
        <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">确认审核</button>
      </div>
    </div>
  </div>
</script>
{/block}
{block name="script"}
<script type="application/javascript">
  // 获取URL参数函数
  function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
  }
  
  // 当前任务类型
  var currentLeagueTaskKey = getUrlParam('league_task_key') || 'league_1';
  // 获取状态参数
  var urlStatus = getUrlParam('status') || '';

  layui.use(['form', 'laytpl','laydate', 'element'], function() {
    var table,
            laydate = layui.laydate;
    var form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
    form.render();
    // 根据URL参数设置筛选状态
    if(urlStatus){
      $("select[name='status']").val(urlStatus);
      form.render('select');
    }
    //日期范围
    laydate.render({
      elem: '#daterangeStart'
      , format: 'yyyy-MM-dd HH:mm:ss'
      , type: 'datetime'
      // ,value:daterange //必须遵循format参数设定的格式
      , done: function (value, date, endDate) {

      }
    });
    laydate.render({
      elem: '#daterangeEnd'
      , format: 'yyyy-MM-dd HH:mm:ss'
      , type: 'datetime'
      // ,value:daterange //必须遵循format参数设定的格式
      , done: function (value, date, endDate) {

      }
    });
    let bottomToolbar = __operateGroupBottomToolbar("#batchOperation");
    table = new Table({
      elem: '#promote_task_list',
      url: ns.url('leaguePoints://admin/PromoteProductManagement/auditList'),
      where: {
        league_task_key: currentLeagueTaskKey,
        status: urlStatus
      },
      bottomToolbar: bottomToolbar,
      height: '600px',
      cols: [
        [
          {
            type: 'checkbox',
            unresize: 'false',
          },
          {
            field: 'member_display',
            title: '提交用户',
            align: 'left',
            unresize: 'false',
          },
          {
            field: 'goods_name',
            title: '商品名称/订单号',
            unresize: 'false',
            align: 'center',
            templet: function(d){
              var html = d.goods_name || d.goods_id || '未知';
              if(d.order_no) {
                html += '<br>订单号：' + d.order_no;
              }
              return html;
            }
          },
          {
            field: 'reward_points',
            title: '奖励贡献值',
            align: 'center',
            unresize: 'false',
          },
          {
            field: 'status_text',
            title: '任务状态',
            align: 'center',
            unresize: 'false',
          },
          {
            field: 'images',
            title: '截图',
            align: 'center',
            unresize: 'false',
            templet: function(d){
              if(d.images){
                var imgs = d.images.split(',');
                return imgs.map(function(url){
                  return '<img src="'+url+'" class="task-review-img" />';
                }).join('');
              }
              return '';
            }
          },
          {
            field: 'remark',
            title: '补充说明',
            align: 'center',
            unresize: 'false',
            templet: function(d){
              if(d.remark && d.remark.length > 0){
                if(d.remark.length > 30){
                  return '<div class="layui-elip" title="'+d.remark+'">'+d.remark.substring(0,30)+'...</div>';
                }
                return d.remark;
              }
              return '';
            }
          },
          {
            field: 'submit_time',
            title: '提交时间',
            align: 'center',
            unresize: 'false',
          },
          {
            title: '操作',
            align: 'center',
            toolbar: '#operation',
            unresize: 'false',
          }
        ]
      ],
      parseData: function(res) {
        console.log('审核列表接口返回：', res);
        return {
          "code": res.code,
          "msg": res.message,
          "count": res.data && res.data.count ? res.data.count : 0,
          "data": res.data && res.data.list ? res.data.list : []
        };
      }
    });

    /**
     * 监听工具栏操作
     */
    table.tool(function(obj) {
      var data = obj.data;
      switch (obj.event) {
        case 'detail': //详情
          showTaskReview(data)
          break;
      }
    });
    // 批量操作
    table.bottomToolbar(function (obj) {
      if (obj.data.length < 1) {
        layer.msg('请选择要操作的数据');
        return;
      }

      var id_array = new Array();
      for (i in obj.data) id_array.push(obj.data[i].member_goods_task_id);
      console.log("选中ID:", id_array);
      
      switch (obj.event) {
        case 'off_tasks':
          layer.confirm('确定要批量驳回选中的任务吗?', function(index) {
            layer.close(index);
            layer.prompt({title: '请输入驳回原因', formType: 2}, function(text, index){
              layer.close(index);
              batchAudit(id_array, -1, text);
            });
          });
          break;
        case 'on_tasks':
          layer.confirm('确定要批量通过选中的任务吗?', function(index) {
            layer.close(index);
            batchAudit(id_array, 100, '');
          });
          break;
      }
    });
    
    // 批量审核
    function batchAudit(ids, status, remark) {
      var loadIndex = layer.load(1, {shade: [0.1,'#fff']});
      $.ajax({
        url: ns.url('leaguePoints://admin/PromoteProductManagement/auditTask'),
        data: {
          member_goods_task_ids: ids.join(','),
          status: status,
          remark: remark,
          fail_reason: status == -1 ? remark : ''
        },
        type: 'POST',
        dataType: 'json',
        success: function(res) {
          layer.close(loadIndex);
          if(res.code === 0) {
            layer.msg(res.message || '操作成功');
            table.reload(); // 刷新列表
          } else {
            layer.msg(res.message || '操作失败');
          }
        },
        error: function() {
          layer.close(loadIndex);
          layer.msg('操作失败，请重试');
        }
      });
    }
    
    // 查看/审核弹窗
    function showTaskReview(data){
      // 预处理数据
      var taskData = data || {};
      console.log("查看任务详情:", taskData);
      
      // 动态渲染模板
      var getTpl = $('#taskReview').html();
      laytpl(getTpl).render(taskData, function(html){
        var reviewLayer = layer.open({
          type: 1,
          title: '任务审核',
          area: ['650px', '80%'],
          content: html,
          scrollbar: true,
          success: function () {
            // 表单渲染和事件绑定
            form.render();
            
            // 监听审核表单提交
            form.on('submit(save)', function(formData){
              var field = formData.field;
              console.log("提交审核:", field);
              
              // 调用审核接口
              $.ajax({
                url: ns.url('leaguePoints://admin/PromoteProductManagement/auditTask'),
                data: {
                  member_goods_task_id: taskData.member_goods_task_id,
                  status: field.status,
                  remark: field.remark,
                  fail_reason: field.status == -1 ? field.remark : ''
                },
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                  if(res.code === 0) {
                    layer.msg(res.message || '审核成功');
                    layer.close(reviewLayer);
                    table.reload(); // 刷新列表
                  } else {
                    layer.msg(res.message || '审核失败');
                  }
                }
              });
              return false; // 阻止默认表单提交
            });
          }
        });
      });
    }
    
    // 监听搜索
    form.on('submit(submit)', function(data){
      console.log("搜索条件:", data.field);
      table.reload({
        page: {
          curr: 1
        },
        where: data.field
      });
      return false;
    });
    
    // 页面加载后显示当前任务类型
    $(function(){
      var taskTypeText = currentLeagueTaskKey === 'league_1' ? '任务一' : '任务二';
      $('.layui-colla-title').text('当前任务类型：' + taskTypeText);
    });
  })
</script>
{/block}