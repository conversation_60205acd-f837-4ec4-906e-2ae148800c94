{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .edit-task{
        padding: 0!important;
    }
</style>
{/block}
{block name="main"}
<div class="layui-form" style="margin-top: 20px;">
    <button class="layui-btn ns-bg-color" lay-submit lay-filter="addPromoteGoods">添加推广商品</button>
    <button class="layui-btn ns-bg-color" id="auditTaskBtn">任务审核（<span id="auditTaskCount">0</span>）</button>
    <span style="margin-left: 20px;">当前任务类型：<span id="currentTaskType">-</span></span>
</div>
<!-- 列表 -->
<table id="promote_goods_list" lay-filter="promote_goods_list"></table>
<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="remove">删除</a>
    </div>
</script>
<script type="text/html" id="editTask">
    <div class="edit-task layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label sm">任务商品</label>
            <div class="layui-input-block">
                <p id="selected_goods_name">{{ d.goods_name || '' }}</p>
                <input type="hidden" name="goods_id" value="{{ d.goods_id || '' }}">
                <input type="hidden" name="id" value="{{ d.goods_task_id || '' }}">
                <input type="hidden" name="league_task_key" value="{{ d.league_task_key || currentLeagueTaskKey }}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label sm">任务奖励</label>
            <div class="layui-input-inline">
                <input type="number" name="task_reward" lay-verify="required|number" value="{{ d.reward_points || '' }}" placeholder="请输入任务奖励" autocomplete="off" class="layui-input ns-len-mid">
            </div>
            <span class="layui-form-mid">贡献值</span>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label sm">任务说明</label>
            <div class="layui-input-block">
                <textarea name="task_desc" placeholder="请输入任务说明" class="layui-textarea ns-len-long">{{ d.rules || '' }}</textarea>
            </div>
        </div>
        <div class="ns-form-row">
            <button class="layui-btn layui-btn-primary" onclick="closeEditLayer()">取消</button>
            <button class="layui-btn ns-bg-color" lay-submit lay-filter="editTaskSave">保存</button>
        </div>
    </div>
</script>
{/block}
{block name="script"}
<script src="ADMIN_JS/common.js"></script>
<script type="application/javascript">
    var editLayer, editData = {};
    // 获取当前league_task_key参数
    var currentLeagueTaskKey = getUrlParam('league_task_key') || 'league_1';
    
    // 获取URL参数函数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
    
    layui.use(['form', 'laytpl', 'laydate', 'element'], function() {
        var laydate = layui.laydate;
        var table,
            form = layui.form,
            laytpl = layui.laytpl,
            element = layui.element;
        form.render();
        
        // 显示当前任务类型
        $('#currentTaskType').text(currentLeagueTaskKey === 'league_1' ? '任务一' : '任务二');

        table = new Table({
            elem: '#promote_goods_list',
            url: ns.url("leaguePoints://admin/PromoteProductManagement/lists"),
            where: {
                league_task_key: currentLeagueTaskKey
            },
            cols: [
                [
                    {
                        field: 'goods_name',
                        title: '商品名称',
                        align: 'left',
                        unresize: 'false',
                    },
                    {
                        field: 'reward_points',
                        title: '奖励贡献值',
                        unresize: 'false',
                        align: 'center',
                    },
                    {
                        field: 'purchase_count',
                        title: '领取任务人数',
                        align: 'center',
                        unresize: 'false',
                        templet: function(data) {
                            return data.purchase_count || 0;
                        }
                    },
                    {
                        field: 'task_submit_count',
                        title: '提交任务数',
                        align: 'center',
                        unresize: 'false',
                        templet: function(data) {
                            return data.task_submit_count || 0;
                        }
                    },
                    {
                        field: 'total_reward',
                        title: '累计发放贡献值',
                        align: 'center',
                        unresize: 'false',
                        templet: function(data) {
                            return data.total_reward || 0;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        toolbar: '#operation',
                        unresize: 'false',
                    }
                ]
            ],
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.message,
                    "count": res.data && res.data.count ? res.data.count : 0,
                    "data": res.data && res.data.list ? res.data.list : []
                };
            }
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': // 编辑
                    editData = data;
                    showEditTask(data);
                    break;
                case 'remove': // 删除
                    layer.confirm('确定要删除该推广商品吗?', function(index) {
                        layer.close(index);
                        $.ajax({
                            type: 'POST',
                            async: false,
                            url: ns.url('leaguePoints://admin/PromoteProductManagement/delete'),
                            data: {id: data.goods_task_id},
                            dataType: 'JSON',
                            success: function(res) {
                                layer.msg(res.message);
                                if(res.code == 0){
                                    table.reload();
                                }
                            }
                        });
                    });
                    break;
            }
        });
        
        // 添加推广商品
        form.on('submit(addPromoteGoods)', function() {
            adminGoodsSelect(function(res) {
                if (res.length > 0) {
                    // 批量处理选中的商品
                    var batchData = [];
                    for (var i = 0; i < res.length; i++) {
                        var selectedGoods = res[i];
                        batchData.push({
                            goods_id: selectedGoods.goods_id,
                            goods_name: selectedGoods.goods_name
                        });
                    }
                    batchAddPromoteGoods(batchData);
                }
            }, [], {mode: "spu", disabled: 1, is_show_all_goods: 0, select_num: 0}); // select_num: 0 表示不限制选择数量
            return false;
        });
        
        // 批量添加推广商品
        function batchAddPromoteGoods(goodsList) {
            if (goodsList.length === 1) {
                // 单个商品直接打开编辑窗口
                editData = goodsList[0];
                showEditTask(editData);
            } else {
                // 多个商品弹出批量设置窗口
                showBatchEditTask(goodsList);
            }
        }
        
        // 显示批量编辑任务弹窗
        function showBatchEditTask(goodsList) {
            var html = '<div class="edit-task layui-form">' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label sm">已选商品</label>' +
                '<div class="layui-input-block">' +
                '<p>' + goodsList.length + '个商品</p>' +
                '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label sm">任务奖励</label>' +
                '<div class="layui-input-inline">' +
                '<input type="number" name="batch_task_reward" lay-verify="required|number" value="" placeholder="请输入任务奖励" autocomplete="off" class="layui-input ns-len-mid">' +
                '</div>' +
                '<span class="layui-form-mid">贡献值</span>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label sm">任务说明</label>' +
                '<div class="layui-input-block">' +
                '<textarea name="batch_task_desc" placeholder="请输入任务说明" class="layui-textarea ns-len-long"></textarea>' +
                '</div>' +
                '</div>' +
                '<div class="ns-form-row">' +
                '<button class="layui-btn layui-btn-primary" onclick="closeEditLayer()">取消</button>' +
                '<button class="layui-btn ns-bg-color" lay-submit lay-filter="batchTaskSave">保存</button>' +
                '</div>' +
                '</div>';

            editLayer = layer.open({
                type: 1,
                title: '批量添加推广任务商品',
                area: ['600px'],
                content: html,
                success: function() {
                    layui.form.render();
                    
                    // 批量保存处理
                    layui.form.on('submit(batchTaskSave)', function(data) {
                        var reward_points = data.field.batch_task_reward;
                        var rules = data.field.batch_task_desc;
                        
                        if (reward_points <= 0) {
                            layer.msg('奖励贡献值必须大于0');
                            return false;
                        }
                        
                        // 显示加载
                        var loadIndex = layer.msg('正在添加...', {
                            icon: 16,
                            shade: 0.01,
                            time: 0
                        });
                        
                        // 记录成功和失败数量
                        var successCount = 0;
                        var failCount = 0;
                        var totalCount = goodsList.length;
                        var processedCount = 0;
                        
                        // 逐个添加商品
                        for (var i = 0; i < goodsList.length; i++) {
                            (function(index) {
                                var goodsItem = goodsList[index];
                                $.ajax({
                                    type: 'POST',
                                    url: ns.url('leaguePoints://admin/PromoteProductManagement/add'),
                                    data: {
                                        goods_id: goodsItem.goods_id,
                                        task_reward: reward_points,
                                        task_desc: rules,
                                        league_task_key: currentLeagueTaskKey
                                    },
                                    dataType: 'JSON',
                                    success: function(res) {
                                        processedCount++;
                                        if (res.code == 0) {
                                            successCount++;
                                        } else {
                                            failCount++;
                                        }
                                        
                                        // 所有请求处理完毕
                                        if (processedCount >= totalCount) {
                                            layer.close(loadIndex);
                                            layer.msg('添加完成：成功' + successCount + '个，失败' + failCount + '个');
                                            closeEditLayer();
                                            table.reload();
                                        }
                                    },
                                    error: function() {
                                        processedCount++;
                                        failCount++;
                                        
                                        // 所有请求处理完毕
                                        if (processedCount >= totalCount) {
                                            layer.close(loadIndex);
                                            layer.msg('添加完成：成功' + successCount + '个，失败' + failCount + '个');
                                            closeEditLayer();
                                            table.reload();
                                        }
                                    }
                                });
                            })(i);
                        }
                        
                        return false;
                    });
                }
            });
        }
        
        // 保存推广任务
        form.on('submit(editTaskSave)', function(data) {
            var field = data.field;
            var url = 'leaguePoints://admin/PromoteProductManagement/add';
            if (field.id) {
                url = 'leaguePoints://admin/PromoteProductManagement/edit';
            }
            
            $.ajax({
                type: 'POST',
                url: ns.url(url),
                data: field,
                dataType: 'JSON',
                success: function(res) {
                    layer.msg(res.message);
                    if (res.code == 0) {
                        closeEditLayer();
                        table.reload();
                    }
                }
            });
            
            return false;
        });
    });
    
    // 显示编辑任务弹窗
    function showEditTask(data) {
        var editHtml = $('#editTask').html();
        laytpl = layui.laytpl;
        
        var getTpl = editHtml;
        var view = document.createElement('div');
        laytpl(getTpl).render(data || {}, function(html) {
            view.innerHTML = html;
        });
        
        editLayer = layer.open({
            type: 1,
            title: data.goods_task_id ? '编辑推广任务商品' : '添加推广任务商品',
            area: ['600px'],
            content: view.innerHTML,
            success: function() {
                layui.form.render();
            }
        });
    }
    
    // 关闭编辑弹窗
    function closeEditLayer() {
        if (editLayer) {
            layer.close(editLayer);
        }
    }
    
    // 动态获取待审核数量并渲染
    function refreshAuditTaskCount() {
        $.ajax({
            url: ns.url('leaguePoints://admin/PromoteProductManagement/getAuditCount'),
            data: {league_task_key: currentLeagueTaskKey},
            dataType: 'json',
            success: function(res) {
                if(res.code === 0 && res.data && typeof res.data.count !== 'undefined') {
                    $('#auditTaskCount').text(res.data.count);
                } else {
                    $('#auditTaskCount').text('0');
                }
            },
            error: function() {
                $('#auditTaskCount').text('0');
            }
        });
    }
    // 页面加载时刷新
    $(function(){
        refreshAuditTaskCount();
    });
    // 切换任务类型时也可调用refreshAuditTaskCount()
    // 审核按钮点击跳转
    $('#auditTaskBtn').on('click', function(){
        location.href = ns.url('leaguePoints://admin/PromoteProductManagement/audits') + '?league_task_key=' + currentLeagueTaskKey + '&status=10';
    });
</script>
{/block}