{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .ns-card {
        margin-top: 10px;
        padding: 20px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .ns-tabs {
        border-bottom: 1px solid #e5e5e5;
        margin-bottom: 20px;
    }
    .ns-tabs .tab-item {
        display: inline-block;
        padding: 0 20px 10px;
        font-size: 14px;
        position: relative;
        cursor: pointer;
    }
    .ns-tabs .tab-item.active {
        color: #4685FD;
        font-weight: bold;
    }
    .ns-tabs .tab-item.active:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -1px;
        width: 100%;
        height: 2px;
        background-color: #4685FD;
    }
    .ns-form-row {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        margin-left: 0 !important;
    }
    .ns-form-row .label {
        width: 120px;
        text-align: right;
        padding-right: 10px;
    }
    .ns-form-row .input-block {
        flex: 1;
    }
    .ns-form-row .ns-input {
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        border: 1px solid #e5e5e5;
        border-radius: 3px;
        width: 200px;
    }
    .ns-form-row .ns-radio {
        margin-right: 10px;
    }
    .ns-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    .ns-table th, 
    .ns-table td {
        padding: 10px;
        text-align: center;
        border: 1px solid #e5e5e5;
    }
    .ns-table th {
        background-color: #f7f7f7;
    }
    .ns-btn {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        padding: 0 15px;
        background-color: #4685FD;
        color: #fff;
        border-radius: 3px;
        cursor: pointer;
        border: none;
        font-size: 14px;
    }
    .ns-btn-delete {
        color: #4685FD;
        cursor: pointer;
    }
    .ns-rules {
        margin-top: 20px;
        color: red;
        line-height: 1.6;
    }
    .reward-total {
        margin-top: 10px;
        color: #666;
    }
</style>
{/block}
{block name="main"}
<div class="ns-card">
    <div class="ns-tabs">
        <div class="tab-item active">活动配置</div>
        <div class="tab-item" onclick="location.href='{:addon_url(\'leaguePoints://admin/RaffleManagement/record\')}'">抽奖记录</div>
    </div>
    
    <div class="tab-content">
        <div class="tab-pane active" id="config">
            <div class="ns-form">
                <div class="ns-form-row">
                    <div class="label">活动状态</div>
                    <div class="input-block">
                        <input type="radio" name="status" value="0" class="ns-radio" id="status-off"><label for="status-off">关闭</label>
                        <input type="radio" name="status" value="1" class="ns-radio" id="status-on"><label for="status-on">开启</label>
                        <span style="margin-left: 10px;color: #999;">关闭抽奖时抽奖入口不显示，也无法参与抽奖</span>
                    </div>
                </div>
                
                <div class="ns-form-row">
                    <div class="label">消耗积分</div>
                    <div class="input-block">
                        <input type="number" class="ns-input" id="points" min="1">
                        <span style="margin-left: 10px;color: #999;">配置每次抽奖消耗的贡献值数量</span>
                    </div>
                </div>
                
                <div class="ns-form-row">
                    <div class="label">配置奖项</div>
                    <div class="input-block">
                        <button class="ns-btn" id="addReward" style="margin-right: 10px;">优惠券奖励</button>
                        <button class="ns-btn" id="addPhoneReward">话费奖励</button>
                    </div>
                </div>
                
                <div class="ns-form-row" style="align-items: flex-start;">
                    <div class="label"></div>
                    <div class="input-block">
                        <table class="ns-table" id="rewardTable">
                            <thead>
                                <tr>
                                    <th width="35%">奖励内容</th>
                                    <th width="25%">抽中概率 %</th>
                                    <th width="25%">中奖数量上限</th>
                                    <th width="15%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 奖项数据会在JS中动态加载 -->
                            </tbody>
                        </table>
                        
                        <div class="ns-rules">
                            <div>中奖数量上限设置-1时表示不限制。</div>
                        </div>
                    </div>
                </div>
                
                <!-- 添加保存按钮 -->
                <div class="ns-form-row" style="margin-top: 20px; justify-content: center;">
                    <button class="ns-btn" id="saveAllConfig">保存所有设置</button>
                </div>
            </div>
        </div>
        
        <div class="tab-pane" id="records" style="display: none;">
            <!-- 抽奖记录内容 -->
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'layer', 'table', 'laytpl'], function(){
        var form = layui.form,
            layer = layui.layer,
            table = layui.table,
            laytpl = layui.laytpl,
            $ = layui.$;
        
        var selectedCoupon = {}; // 选中的优惠券数据
        
        // 加载配置数据
        loadConfigData();
        
        // 标签页切换
        $('.ns-tabs .tab-item').on('click', function() {
            var index = $(this).index();
            if(index === 1) {
                location.href = ns.url("leaguePoints://admin/RaffleManagement/record");
                return;
            }
            $('.ns-tabs .tab-item').removeClass('active');
            $(this).addClass('active');
            $('.tab-pane').hide();
            $('.tab-pane').eq(index).show();
        });
        
        // 抽中概率输入监听
        $(document).on('input', '#rewardTable input[type="number"]', function() {
            calculateProbability();
        });

        // 活动状态切换事件 - 已移除自动保存
        $(document).on('change', 'input[name="status"]', function() {
            // 仅切换状态，不自动保存
        });
        
        // 消耗积分输入框事件 - 已移除自动保存
        $('#points').on('blur', function() {
            // 仅处理输入，不自动保存
        });
        
        // 更新配置
        function updateConfig(data) {
            // 显示加载提示
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/saveConfig"),
                data: data,
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    
                    if (res.code === 0) {
                        layer.msg('更新成功');
                    } else {
                        layer.msg(res.message || '更新失败');
                    }
                },
                error: function() {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    layer.msg('请求失败，请重试');
                }
            });
        }
        
        // 优惠券奖励按钮
        $('#addReward').on('click', function(){
            layer.open({
                type: 2,
                title: '选择优惠券',
                area: ['800px', '600px'],
                content: ns.url("membersignin://admin/signIn/selectGoodsCoupon"),
                btn: ['确认', '取消'],
                yes: function(index, layero){
                    var iframeWin = window[layero.find('iframe')[0]['name']]; // 【核心】
                    selectedCoupon = iframeWin.dataToParent(); // 调用子页面的方法
                    if(Object.keys(selectedCoupon).length){
                        layer.close(index);
                        
                        // 添加优惠券奖项到数据库
                        addRewardToDatabase(selectedCoupon);
                    } else {
                        layer.msg('请选择优惠券');
                    }
                }
            });
        });
        
        // 话费奖励按钮
        $('#addPhoneReward').on('click', function(){
            // 打开话费奖励输入对话框
            layer.open({
                type: 1,
                title: '添加话费奖励',
                area: ['400px', '230px'],
                skin: 'phone-reward-dialog',
                content: '<div style="padding: 20px 20px 10px;">' +
                         '<h3 style="font-size: 16px; margin-bottom: 20px; font-weight: normal;">充值话费金额</h3>' +
                         '<div style="display: flex; align-items: center;">' +
                         '<input type="number" id="phoneRewardAmount" class="layui-input" style="width: 180px;">' +
                         '<span style="margin-left: 10px;">元</span>' +
                         '</div>' +
                         '</div>',
                btn: ['确认添加', '取消'],
                btnAlign: 'c',
                btn1: function(index, layero){
                    var amount = $('#phoneRewardAmount').val();
                    
                    if(!amount || amount <= 0){
                        layer.msg('请输入有效的话费金额');
                        return;
                    }
                    
                    // 自动生成奖励名称
                    var rewardName = '话费充值【' + amount + '元】';
                    
                    // 构造话费奖励数据
                    var phoneRewardData = {
                        reward_type: 2, // 话费奖励类型
                        relation_id: 0, // 关联ID设为0
                        relation_value: amount, // 使用relation_value存储话费金额
                        reward_name: rewardName,
                        probability: 0, // 默认概率为0
                        reward_limit: 0 // 默认数量上限为0
                    };
                    
                    // 添加奖项到数据库
                    addPhoneRewardToDatabase(phoneRewardData);
                    
                    layer.close(index);
                }
            });
            
            // 添加自定义样式到按钮
            setTimeout(function(){
                $('.phone-reward-dialog .layui-layer-btn0').css({
                    'background-color': '#00a0e9',
                    'color': '#fff',
                    'border-color': '#00a0e9',
                    'padding': '0 25px'
                });
                $('.phone-reward-dialog .layui-layer-btn1').css({
                    'background-color': '#fff',
                    'color': '#333',
                    'border-color': '#e6e6e6',
                    'padding': '0 25px'
                });
            }, 10);
        });
        
        // 添加话费奖励到数据库
        function addPhoneRewardToDatabase(phoneRewardData) {
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/addReward"),
                data: phoneRewardData,
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg('添加成功');
                        // 重新加载奖项列表
                        loadRewardList();
                    } else {
                        layer.msg(res.message || '添加失败');
                    }
                }
            });
        }
        
        // 从数据库加载配置
        function loadConfigData() {
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/getConfig"),
                dataType: 'json',
                type: 'get',
                success: function(res) {
                    if (res.code === 0 && res.data) {
                        // 设置活动状态
                        var status = res.data.status !== undefined ? parseInt(res.data.status) : 1;
                        $('input[name="status"][value="'+status+'"]').prop('checked', true);
                        
                        // 设置消耗积分
                        $('#points').val(res.data.points || 20);
                        
                        // 加载奖项列表
                        loadRewardList();
                        
                        // 重新渲染表单
                        form.render();
                    }
                }
            });
        }
        
        // 从数据库加载奖项列表
        function loadRewardList() {
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/config"),
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    if (res.code === 0 && res.data && res.data.list) {
                        // 清空表格
                        $('#rewardTable tbody').empty();
                        
                        // 按reward_id正序排列奖项
                        var sortedList = res.data.list.sort(function(a, b) {
                            return a.reward_id - b.reward_id;
                        });
                        
                        // 添加奖项
                        sortedList.forEach(function(item) {
                            appendRewardToTable(item);
                        });
                        
                        // 重新计算概率
                        calculateProbability();
                    }
                }
            });
        }
        
        // 添加奖项到数据库
        function addRewardToDatabase(couponData) {
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/addReward"),
                data: {
                    reward_type: 1, // 优惠券类型
                    goodscoupon_type_id: couponData.goodscoupon_type_id, // 前端仍使用goodscoupon_type_id参数名
                    reward_name: couponData.goodscoupon_name,
                    probability: 0, // 默认概率为0
                    reward_limit: 0 // 默认数量上限为0
                },
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg('添加成功');
                        // 重新加载奖项列表
                        loadRewardList();
                    } else {
                        layer.msg(res.message || '添加失败');
                    }
                }
            });
        }
        
        // 更新奖项
        function updateReward(reward_id, data) {
            // 显示加载提示
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            // 获取基本配置，保持现有设置不变
            var status = $('input[name="status"]:checked').val() || 0;
            var points = $('#points').val() || 20;
            
            // 创建一个仅包含当前奖项的rewards数组
            var rewards = [{
                reward_id: reward_id,
                probability: data.probability !== undefined ? data.probability : 0,
                reward_limit: data.reward_limit !== undefined ? data.reward_limit : 0,
                relation_value: data.relation_value
            }];
            
            // 使用新的saveReward接口更新奖项
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/saveReward"),
                data: {
                    points: points,
                    status: status,
                    rewards: rewards
                },
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    
                    if (res.code === 0) {
                        layer.msg('更新成功');
                        // 重新加载奖项列表以确保数据同步
                        loadRewardList();
                    } else {
                        layer.msg(res.message || '更新失败');
                        // 重新加载列表以恢复正确的数据
                        loadRewardList();
                    }
                },
                error: function() {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    layer.msg('请求失败，请重试');
                    // 重新加载列表以恢复正确的数据
                    loadRewardList();
                }
            });
        }
        
        // 将奖项添加到表格
        function appendRewardToTable(item) {
            var tr = $('<tr data-id="'+ item.reward_id +'"></tr>');
            
            // 谢谢参与特殊处理
            if (item.reward_type == 0) {
                tr.attr('id', 'thanksReward');
                tr.append('<td>' + item.reward_name + '</td>');
                tr.append('<td><input type="number" class="ns-input" value="' + item.probability + '" min="0" max="100" id="thanksProbability" readonly></td>');
                tr.append('<td>不限</td>');
                tr.append('<td>-</td>');
            } else {
                tr.append('<td>' + item.reward_name + '</td>');
                tr.append('<td><input type="number" class="ns-input probability-input" value="' + item.probability + '" min="0" max="100" data-id="' + item.reward_id + '" data-original="' + item.probability + '"></td>');
                var limitCell = $('<td></td>');
                var limitInput = $('<input type="number" class="ns-input limit-input" value="' + (item.reward_limit == -1 ? "-1" : item.reward_limit) + '" min="-1" data-id="' + item.reward_id + '" data-original="' + item.reward_limit + '">');
                limitCell.append(limitInput);
                // 如果是-1，添加(不限)提示
                if(item.reward_limit == -1) {
                    limitCell.append('<span class="limit-tip" style="margin-left:5px;color:#ff0000;">(不限)</span>');
                }
                tr.append(limitCell);
                tr.append('<td><a class="ns-btn-delete" onclick="deleteReward(' + item.reward_id + ')">删除</a></td>');
            }
            
            $('#rewardTable tbody').append(tr);
            
            // 添加输入和失去焦点事件
            tr.find('.probability-input').on('input', function() {
                // 实时计算和预览概率变化
                calculateProbability();
            });
            
            tr.find('.limit-input').on('input', function() {
                // 添加输入事件，提示-1表示不限制
                if($(this).val() === "-1") {
                    // 如果输入框后面没有提示，添加提示
                    if($(this).next('.limit-tip').length === 0) {
                        $(this).after('<span class="limit-tip" style="margin-left:5px;color:#ff0000;">(不限)</span>');
                    }
                } else {
                    // 如果有提示，移除提示
                    $(this).next('.limit-tip').remove();
                }
            }).on('blur', function() {
                // 只处理显示一致性，不自动保存
                var inputValue = $(this).val();
                var newValue = inputValue === "" ? 0 : parseInt(inputValue);
                
                // 确保显示一致性
                if(newValue === -1 && $(this).next('.limit-tip').length === 0) {
                    $(this).after('<span class="limit-tip" style="margin-left:5px;color:#ff0000;">(不限)</span>');
                }
            });
        }
        
        // 计算抽中概率
        window.calculateProbability = function() {
            var totalProbability = 0;
            var inputs = $('#rewardTable tbody tr:not(#thanksReward) .probability-input');
            
            inputs.each(function() {
                var val = parseFloat($(this).val()) || 0;
                totalProbability += val;
            });
            
            // 确保概率不超过100%
            if (totalProbability > 100) {
                layer.msg('所有奖项概率总和不能超过100%');
                $(inputs[inputs.length - 1]).val(Math.max(0, 100 - totalProbability + parseFloat($(inputs[inputs.length - 1]).val())));
                totalProbability = 100;
            }
            
            // 设置"谢谢参与"概率 - 仅用于显示
            var thanksProbability = 100 - totalProbability;
            $('#thanksProbability').val(thanksProbability);
            
            // 注意：此处只计算显示，不保存到数据库
            // 实际保存会在blur事件中触发
        }
        
        // 删除奖品
        window.deleteReward = function(reward_id) {
            layer.confirm('确定要删除该奖品吗？', function(index){
                $.ajax({
                    url: ns.url("leaguePoints://admin/RaffleManagement/deleteReward"),
                    data: {
                        reward_id: reward_id
                    },
                    dataType: 'json',
                    type: 'post',
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('删除成功');
                            // 移除表格行
                            $('#rewardTable tbody tr[data-id="'+reward_id+'"]').remove();
                            // 重新计算概率
                            calculateProbability();
                        } else {
                            layer.msg(res.message || '删除失败');
                        }
                        layer.close(index);
                    }
                });
            });
        }
        
        // 统一保存所有配置
        function saveAll() {
            // 显示加载提示
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            // 直接调用saveAllRewards保存所有配置
            saveAllRewards(loadingIndex);
        }
        
        // 保存所有奖项配置
        function saveAllRewards(loadingIndex) {
            // 获取所有奖项数据
            var rewards = [];
            $('#rewardTable tbody tr').each(function() {
                var reward_id = $(this).data('id');
                if (!reward_id) return; // 跳过没有ID的行
                
                var probability = parseFloat($(this).find('.probability-input').val()) || 0;
                
                // 处理奖励上限
                var limitValue = $(this).find('.limit-input').val();
                var reward_limit = limitValue === "" ? 0 : parseInt(limitValue);
                
                rewards.push({
                    reward_id: reward_id,
                    probability: probability,
                    reward_limit: reward_limit
                });
            });
            
            // 如果没有奖项，直接提示保存成功
            if (rewards.length === 0) {
                layer.close(loadingIndex);
                layer.msg('保存成功');
                return;
            }
            
            // 获取基本配置
            var status = $('input[name="status"]:checked').val() || 0;
            var points = $('#points').val() || 20;
            
            // 使用新的saveReward接口一次性保存所有配置
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/saveReward"),
                data: {
                    points: points,
                    status: status,
                    rewards: rewards
                },
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    layer.close(loadingIndex);
                    
                    if (res.code === 0) {
                        layer.msg('保存成功');
                        // 重新加载奖项列表以确保数据同步
                        loadRewardList();
                    } else {
                        layer.msg(res.message || '保存失败');
                        // 重新加载列表以恢复正确的数据
                        loadRewardList();
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('请求失败，请重试');
                    // 重新加载列表以恢复正确的数据
                    loadRewardList();
                }
            });
        }
        
        // 保存按钮点击事件
        $('#saveAllConfig').on('click', function() {
            saveAll();
        });
    });
</script>
{/block}