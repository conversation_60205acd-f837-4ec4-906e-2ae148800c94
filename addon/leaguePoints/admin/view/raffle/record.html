{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style type="text/css">
    .ns-card {
        margin-top: 10px;
        padding: 20px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .ns-tabs {
        border-bottom: 1px solid #e5e5e5;
        margin-bottom: 20px;
    }
    .ns-tabs .tab-item {
        display: inline-block;
        padding: 0 20px 10px;
        font-size: 14px;
        position: relative;
        cursor: pointer;
    }
    .ns-tabs .tab-item.active {
        color: #4685FD;
        font-weight: bold;
    }
    .ns-tabs .tab-item.active:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -1px;
        width: 100%;
        height: 2px;
        background-color: #4685FD;
    }
    .search-wrap {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 20px;
    }
    .search-wrap .search-item {
        margin-right: 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    .search-wrap .search-item .search-label {
        margin-right: 5px;
    }
    .search-wrap .search-item .layui-input {
        width: 180px;
        height: 32px;
        line-height: 32px;
    }
    .search-wrap .search-item .layui-btn {
        height: 32px;
        line-height: 32px;
        padding: 0 15px;
    }
    .search-btn {
        background-color: #4685FD;
    }
    .export-btn {
        background-color: #fff;
        color: #5FB878;
        border: 1px solid #5FB878;
    }
    .batch-btn {
        background-color: #fff;
        color: #FF5722;
        border: 1px solid #FF5722;
    }
    .ns-table {
        width: 100%;
        border-collapse: collapse;
    }
    .ns-table th, 
    .ns-table td {
        padding: 12px 10px;
        text-align: center;
        border: 1px solid #e5e5e5;
    }
    .ns-table th {
        background-color: #f7f7f7;
        font-weight: normal;
    }
    .ns-table tbody tr:hover {
        background-color: #f9f9f9;
    }
    .ns-table .layui-text-blue {
        color: #4685FD;
    }
    .change-btn {
        padding: 3px 8px;
        background-color: #1E9FFF;
        color: #fff;
        border-radius: 2px;
        font-size: 12px;
        cursor: pointer;
        display: inline-block;
    }
    /* 对话框样式 */
    .change-mobile-dialog {
        padding: 20px;
    }
    .dialog-row {
        margin-bottom: 15px;
        display: flex;
    }
    .dialog-label {
        width: 100px;
        text-align: right;
        padding-right: 15px;
        line-height: 38px;
    }
    .dialog-content {
        flex: 1;
        line-height: 38px;
    }
    .dialog-input {
        width: 100%;
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        border: 1px solid #e6e6e6;
        border-radius: 2px;
    }
</style>
{/block}

{block name="main"}
<div class="ns-card">
    <div class="ns-tabs">
        <div class="tab-item" onclick="location.href='{:addon_url(\'leaguePoints://admin/RaffleManagement/config\')}'">活动配置</div>
        <div class="tab-item active">抽奖记录</div>
    </div>
    
    <div class="search-wrap">
        <div class="search-item">
            <span class="search-label">抽奖用户</span>
            <input type="text" placeholder="用户手机号" class="layui-input" id="searchMobile">
        </div>
        <div class="search-item">
            <span class="search-label">选择起止日期</span>
            <input type="text" id="dateRange" placeholder="选择起止日期" class="layui-input">
        </div>
        <div class="search-item">
            <span class="search-label">奖项类型</span>
            <select id="rewardType" class="layui-input">
                <option value="">全部</option>
                <option value="0">谢谢参与</option>
                <option value="1">优惠券</option>
                <option value="2">话费</option>
            </select>
        </div>
        <div class="search-item">
            <span class="search-label">发放状态</span>
            <select id="statusType" class="layui-input">
                <option value="">全部</option>
                <option value="-1">发放失败</option>
                <option value="0">待发放</option>
                <option value="100">已发放</option>
            </select>
        </div>
        <div class="search-item">
            <button type="button" class="layui-btn search-btn" id="searchBtn">搜索</button>
        </div>
        <div class="search-item">
            <button type="button" class="layui-btn layui-btn-primary" id="exportBtn">导出</button>
        </div>
        <div class="search-item">
            <button type="button" class="layui-btn layui-btn-primary" id="batchChangeStatusBtn">批量变更发放状态</button>
        </div>
    </div>
    
    <table id="recordTable" lay-filter="recordTable"></table>
</div>
{/block}

{block name="script"}
<script type="text/html" id="rechargeMobileTpl">
    {{# if(d.recharge_mobile){ }}
        {{ d.recharge_mobile }}
        {{# if(d.reward_type == 2){ }}
            <span class="change-btn" data-id="{{ d.record_id }}">变更</span>
        {{# } }}
    {{# } }}
</script>

<script>
    layui.use(['form', 'laydate', 'table', 'layer'], function(){
        var form = layui.form,
            laydate = layui.laydate,
            table = layui.table,
            layer = layui.layer,
            $ = layui.$;
        
        // 初始化日期范围选择器
        laydate.render({
            elem: '#dateRange',
            range: true
        });
        
        // 渲染表格
        var tableInstance = table.render({
            elem: '#recordTable',
            url: ns.url("leaguePoints://admin/RaffleManagement/record"),
            cols: [[
                {type: 'checkbox', width: '5%', unresize: 'false', templet: function(d){
                    // 只有话费充值类型且待发放状态才显示多选框
                    if(d.reward_type == 2 && d.status == 0) {
                        return '<input type="checkbox" name="layTableCheckbox" lay-skin="primary" lay-filter="layTableAllChoose">';
                    } else {
                        return '';
                    }
                }},
                {field: 'member_mobile', title: '抽奖用户', width: '15%', unresize: 'false'},
                {field: 'points', title: '当次消耗贡献值', width: '10%', unresize: 'false'},
                {field: 'reward_name', title: '抽中奖项', width: '15%', unresize: 'false'},
                {field: 'recharge_mobile', title: '充值手机号', width: '15%', unresize: 'false', templet: '#rechargeMobileTpl'},
                {field: 'status_text', title: '发放状态', width: '10%', unresize: 'false', templet: function(d){
                    if(d.status == -1) {
                        return '<span style="color: red;">发放失败</span>';
                    } else if(d.status == 0) {
                        return '<span style="color: #FFB800;">待发放</span>';
                    } else if(d.status == 100) {
                        return '<span style="color: #009688;">已发放</span>';
                    } else {
                        return '<span>未知状态</span>';
                    }
                }},
                {field: 'create_time', title: '抽奖时间', width: '10%', unresize: 'false', templet: function(d){
                    return d.create_time ? (new Date(d.create_time * 1000).toLocaleString()) : '';
                }},
                {field: 'operate', title: '操作', width: '10%', unresize: 'false', templet: function(d){
                    // 只有话费充值类型且待发放状态才显示"变更发放状态"按钮
                    if(d.reward_type == 2 && d.status == 0) {
                        return '<span class="change-status-btn layui-btn layui-btn-xs" data-id="' + d.record_id + '">变更发放状态</span>';
                    } else {
                        return '';
                    }
                }}
            ]],
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            parseData: function(res){
                return {
                    "code": res.code == 0 ? 0 : 1,
                    "msg": res.message,
                    "count": res.data.count,
                    "data": res.data.list
                };
            }
        });
        
        // 搜索按钮点击事件
        $('#searchBtn').on('click', function(){
            search();
        });
        
        // 导出按钮点击事件
        $('#exportBtn').on('click', function(){
            var mobile = $('#searchMobile').val();
            var dateRange = $('#dateRange').val();
            var reward_type = $('#rewardType').val();
            var status = $('#statusType').val();
            
            var start_time = 0;
            var end_time = 0;
            
            if (dateRange) {
                var dates = dateRange.split(' - ');
                if (dates.length == 2) {
                    start_time = new Date(dates[0]).getTime() / 1000;
                    end_time = new Date(dates[1]).getTime() / 1000 + 86399; // 加上一天减1秒，包含整个结束日
                }
            }
            
            // 显示加载提示
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            // 使用AJAX请求导出Excel
            $.ajax({
                url: ns.url("leaguePoints://admin/RaffleManagement/exportRecordExcel"),
                data: {
                    mobile: mobile,
                    start_time: start_time,
                    end_time: end_time,
                    reward_type: reward_type, // 始终传递reward_type，即使为空字符串
                    status: status // 始终传递status，即使为空字符串
                },
                dataType: 'json',
                type: 'post',
                success: function(res) {
                    layer.close(loadingIndex);
                    
                    if (res.code === 0 && res.data && res.data.path) {
                        layer.msg('导出成功，正在下载...');
                        window.location.href = res.data.path;
                    } else {
                        layer.msg(res.message || '导出失败');
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('导出请求失败，请重试');
                }
            });
        });
        
        // 批量变更发放状态按钮点击事件
        $('#batchChangeStatusBtn').on('click', function(){
            // 获取所有选中的行数据
            var checkStatus = table.checkStatus('recordTable');
            var data = checkStatus.data;
            
            if (data.length === 0) {
                layer.msg('请选择需要变更状态的记录');
                return;
            }
            
            // 验证所有记录是否符合条件（话费充值类型且待发放状态）
            for (var i = 0; i < data.length; i++) {
                if (data[i].reward_type != 2 || data[i].status != 0) {
                    layer.msg('只能批量变更话费充值类型且待发放状态的记录');
                    return;
                }
            }
            
            // 收集所有选中记录的ID
            var recordIds = [];
            for (var i = 0; i < data.length; i++) {
                recordIds.push(data[i].record_id);
            }
            
            // 确认对话框
            layer.confirm('确定将选中的 ' + data.length + ' 条记录状态修改为"已发放"吗？', {
                btn: ['取消', '确认变更'],
                btn2: function(index, layero){
                    // 显示加载提示
                    var loadingIndex = layer.load(1, {
                        shade: [0.1, '#fff']
                    });
                    
                    // 发送AJAX请求批量更新状态，固定状态为"已发放"
                    $.ajax({
                        url: ns.url('leaguePoints://admin/RaffleManagement/batchChangeStatus'),
                        data: {
                            record_ids: recordIds.join(','),
                            status: 100  // 固定状态为"已发放"
                        },
                        type: 'post',
                        dataType: 'json',
                        success: function(res) {
                            layer.close(loadingIndex);
                            
                            if (res.code === 0) {
                                layer.msg('状态更新成功，共更新 ' + res.data.affected_rows + ' 条记录');
                                layer.close(index);
                                
                                // 刷新表格
                                tableInstance.reload();
                            } else {
                                layer.msg(res.message || '状态更新失败');
                            }
                        },
                        error: function() {
                            layer.close(loadingIndex);
                            layer.msg('请求失败，请重试');
                        }
                    });
                    
                    return false;
                },
                success: function(layero, index) {
                    // 修改按钮顺序和样式（默认按钮是相反的）
                    var $btns = $(layero).find('.layui-layer-btn').children();
                    $btns.eq(0).removeClass('layui-layer-btn0').addClass('layui-layer-btn1');
                    $btns.eq(1).removeClass('layui-layer-btn1').addClass('layui-layer-btn0');
                }
            });
        });
        
        // 表格中"变更"按钮点击事件
        $(document).on('click', '.change-btn', function(e){
            // 阻止事件冒泡
            e.stopPropagation();
            
            // 获取当前行数据
            var recordId = $(this).data('id');
            var currentData = null;
            
            // 获取表格当前页的所有数据
            var tableData = table.cache.recordTable || [];
            
            // 查找当前记录
            for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].record_id == recordId) {
                    currentData = tableData[i];
                    break;
                }
            }
            
            if (!currentData) {
                layer.msg('获取记录信息失败');
                return;
            }
            
            // 格式化时间
            var createTime = currentData.create_time ? (new Date(currentData.create_time * 1000).toLocaleString()) : '';
            
            // 构建对话框内容
            var dialogContent = 
                '<div class="change-mobile-dialog">' +
                '    <div class="dialog-row">' +
                '        <div class="dialog-label">抽奖用户：</div>' +
                '        <div class="dialog-content">' + currentData.member_mobile + '</div>' +
                '    </div>' +
                '    <div class="dialog-row">' +
                '        <div class="dialog-label">抽中奖品：</div>' +
                '        <div class="dialog-content">话费充值 ' + currentData.reward_name + '</div>' +
                '    </div>' +
                '    <div class="dialog-row">' +
                '        <div class="dialog-label">抽奖时间：</div>' +
                '        <div class="dialog-content">' + createTime + '</div>' +
                '    </div>' +
                '    <div class="dialog-row">' +
                '        <div class="dialog-label">原号码：</div>' +
                '        <div class="dialog-content">' + currentData.recharge_mobile + '</div>' +
                '    </div>' +
                '    <div class="dialog-row">' +
                '        <div class="dialog-label">修改为：</div>' +
                '        <div class="dialog-content">' +
                '            <input type="text" id="newMobile" class="dialog-input" placeholder="请输入新的手机号" value="">' +
                '        </div>' +
                '    </div>' +
                '</div>';
            
            // 打开对话框
            layer.open({
                type: 1,
                title: '变更充值手机号',
                content: dialogContent,
                area: ['500px', 'auto'],
                btn: ['取消', '确认变更'],
                btn2: function(index, layero){
                    // 获取新手机号
                    var newMobile = $('#newMobile').val().trim();
                    
                    // 验证手机号
                    if (!newMobile) {
                        layer.msg('请输入新的手机号');
                        return false;
                    }
                    
                    // 验证手机号格式（中国大陆手机号）
                    if (!/^1[3-9]\d{9}$/.test(newMobile)) {
                        layer.msg('请输入正确的手机号格式');
                        return false;
                    }
                    
                    // 显示加载提示
                    var loadingIndex = layer.load(1, {
                        shade: [0.1, '#fff']
                    });
                    
                    // 发送AJAX请求更新手机号
                    $.ajax({
                        url: ns.url('leaguePoints://admin/RaffleManagement/updateRechargeMobile'),
                        data: {
                            record_id: recordId,
                            mobile: newMobile
                        },
                        type: 'post',
                        dataType: 'json',
                        success: function(res) {
                            layer.close(loadingIndex);
                            
                            if (res.code === 0) {
                                layer.msg('手机号更新成功');
                                layer.close(index);
                                
                                // 刷新表格
                                tableInstance.reload();
                            } else {
                                layer.msg(res.message || '手机号更新失败');
                            }
                        },
                        error: function() {
                            layer.close(loadingIndex);
                            layer.msg('请求失败，请重试');
                        }
                    });
                    
                    return false;
                },
                success: function(layero, index) {
                    // 修改按钮顺序和样式（默认按钮是相反的）
                    var $btns = $(layero).find('.layui-layer-btn').children();
                    $btns.eq(0).removeClass('layui-layer-btn0').addClass('layui-layer-btn1');
                    $btns.eq(1).removeClass('layui-layer-btn1').addClass('layui-layer-btn0');
                }
            });
        });
        
        // 搜索功能
        function search() {
            var mobile = $('#searchMobile').val();
            var dateRange = $('#dateRange').val();
            var reward_type = $('#rewardType').val();
            var status = $('#statusType').val();
            
            var searchData = {};
            
            if (mobile) {
                searchData.mobile = mobile;
            }
            
            if (dateRange) {
                var dates = dateRange.split(' - ');
                if (dates.length == 2) {
                    searchData.start_time = new Date(dates[0]).getTime() / 1000;
                    searchData.end_time = new Date(dates[1]).getTime() / 1000 + 86399; // 加上一天减1秒，包含整个结束日
                }
            }
            
            // 始终添加reward_type参数，即使为空字符串，确保能清除之前的筛选条件
            searchData.reward_type = reward_type;
            
            // 始终添加status参数，即使为空字符串，确保能清除之前的筛选条件
            searchData.status = status;
            
            // 重载表格
            tableInstance.reload({
                where: searchData,
                page: {
                    curr: 1
                }
            });
        }
        
        // 标签页切换
        $('.ns-tabs .tab-item').on('click', function() {
            if(!$(this).hasClass('active')){
                var index = $(this).index();
                $('.ns-tabs .tab-item').removeClass('active');
                $(this).addClass('active');
                
                // 如果是第一个标签，跳转到配置页面
                if(index === 0){
                    location.href = ns.url("leaguePoints://admin/RaffleManagement/config");
                }
            }
        });

        // 表格中"变更发放状态"按钮点击事件
        $(document).on('click', '.change-status-btn', function(e){
            // 阻止事件冒泡
            e.stopPropagation();
            
            // 获取记录ID
            var recordId = $(this).data('id');
            
            // 确认对话框
            layer.confirm('确定将此记录状态修改为"已发放"吗？', {
                btn: ['取消', '确认变更'],
                btn2: function(index, layero){
                    // 显示加载提示
                    var loadingIndex = layer.load(1, {
                        shade: [0.1, '#fff']
                    });
                    
                    // 发送AJAX请求更新状态
                    $.ajax({
                        url: ns.url('leaguePoints://admin/RaffleManagement/batchChangeStatus'),
                        data: {
                            record_ids: recordId,
                            status: 100  // 固定状态为"已发放"
                        },
                        type: 'post',
                        dataType: 'json',
                        success: function(res) {
                            layer.close(loadingIndex);
                            
                            if (res.code === 0) {
                                layer.msg('状态更新成功');
                                layer.close(index);
                                
                                // 刷新表格
                                tableInstance.reload();
                            } else {
                                layer.msg(res.message || '状态更新失败');
                            }
                        },
                        error: function() {
                            layer.close(loadingIndex);
                            layer.msg('请求失败，请重试');
                        }
                    });
                    
                    return false;
                },
                success: function(layero, index) {
                    // 修改按钮顺序和样式（默认按钮是相反的）
                    var $btns = $(layero).find('.layui-layer-btn').children();
                    $btns.eq(0).removeClass('layui-layer-btn0').addClass('layui-layer-btn1');
                    $btns.eq(1).removeClass('layui-layer-btn1').addClass('layui-layer-btn0');
                }
            });
        });
    });
</script>
{/block}