<?php

namespace addon\leaguePoints\admin\controller;

use app\admin\controller\BaseAdmin;
use app\model\goods\Goods as GoodsModel;
use addon\leaguePoints\model\LeagueTaskGoodsTaskModel;
use app\model\member\MemberModel;

class PromoteProductManagement extends BaseAdmin
{
    /**
     * 推广商品任务列表
     */
    public function lists()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            
            // 添加league_task_key筛选条件
            $league_task_key = input('league_task_key', 'league_1');
            $condition = [['league_task_key', '=', $league_task_key]];
            
            $model = new LeagueTaskGoodsTaskModel();
            $list = $model->where($condition)
                ->append(['goods_name', 'purchase_count', 'task_submit_count', 'total_reward'])
                ->paginate($page_size);

            $result = model_to_api_page($list);
            return success(0, '获取成功', $result);
        } else {
            return $this->fetch('promote_product_management/lists');
        }
    }
    
    /**
     * 添加推广商品
     */
    public function add()
    {
        if (request()->isAjax()) {
            $goods_id = input('goods_id', 0);
            $reward_points = input('task_reward', 0);
            $rules = input('task_desc', '');
            $league_task_key = input('league_task_key', 'league_1');
            
            // 验证数据
            if (empty($goods_id)) {
                return error(-1, '请选择商品');
            }
            if (empty($reward_points) || $reward_points <= 0) {
                return error(-1, '奖励贡献值必须大于0');
            }
            
            // 检查商品是否存在
            $goods_info = (new GoodsModel())->getGoodsInfo([['goods_id', '=', $goods_id]], 'goods_id, goods_name');
            if (empty($goods_info['data'])) {
                return error(-1, '商品不存在');
            }
            
            // 检查商品是否已添加到同一任务中
            $model = new LeagueTaskGoodsTaskModel();
            $exist = $model->where([
                ['goods_id', '=', $goods_id],
                ['league_task_key', '=', $league_task_key]
            ])->find();
            
            if (!empty($exist)) {
                return error(-1, '该商品已添加为该类型的推广任务');
            }
            
            // 添加记录，使用事务确保数据一致性
            $model->startTrans();
            try {
                $data = [
                    'league_task_key' => $league_task_key,
                    'goods_id' => $goods_id,
                    'reward_points' => $reward_points,
                    'rules' => $rules
                ];
                
                $result = $model->save($data);
                
                if ($result) {
                    $model->commit();
                    return success(0, '添加成功');
                } else {
                    $model->rollback();
                    return error(-1, '添加失败');
                }
            } catch (\Exception $e) {
                $model->rollback();
                return error(-1, '添加失败：' . $e->getMessage());
            }
        }
    }
    
    /**
     * 编辑推广商品
     */
    public function edit()
    {
        if (request()->isAjax()) {
            $goods_task_id = input('id', 0);
            $reward_points = input('task_reward', 0);
            $rules = input('task_desc', '');
            $league_task_key = input('league_task_key', 'league_1');
            
            // 验证数据
            if (empty($goods_task_id)) {
                return error(-1, '参数错误');
            }
            if (empty($reward_points) || $reward_points <= 0) {
                return error(-1, '奖励贡献值必须大于0');
            }
            
            // 检查记录是否存在
            $model = new LeagueTaskGoodsTaskModel();
            $exist = $model->where([['goods_task_id', '=', $goods_task_id]])->find();
            if (empty($exist)) {
                return error(-1, '推广任务不存在');
            }
            
            // 更新记录
            $data = [
                'league_task_key' => $league_task_key,
                'reward_points' => $reward_points,
                'rules' => $rules
            ];
            
            $result = $model->where([['goods_task_id', '=', $goods_task_id]])->update($data);
            
            if ($result !== false) {
                return success(0, '编辑成功');
            } else {
                return error(-1, '编辑失败');
            }
        }
    }
    
    /**
     * 删除推广商品
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $goods_task_id = input('id', 0);
            
            // 验证数据
            if (empty($goods_task_id)) {
                return error(-1, '参数错误');
            }
            
            // 检查记录是否存在
            $model = new LeagueTaskGoodsTaskModel();
            $exist = $model->where([['goods_task_id', '=', $goods_task_id]])->find();
            if (empty($exist)) {
                return error(-1, '推广任务不存在');
            }
            
            // 删除记录
            $result = $model->where([['goods_task_id', '=', $goods_task_id]])->delete();
            
            if ($result) {
                return success(0, '删除成功');
            } else {
                return error(-1, '删除失败');
            }
        }
    }
    
    /**
     * 获取商品信息
     */
    public function getGoodsInfo()
    {
        if (request()->isAjax()) {
            $goods_id = input('goods_id', 0);
            
            if (empty($goods_id)) {
                return error(-1, '参数错误');
            }
            
            $goods_info = (new GoodsModel())->getGoodsInfo([['goods_id', '=', $goods_id]], 'goods_id, goods_name');
            
            if (!empty($goods_info['data'])) {
                return success(0, '获取成功', $goods_info['data']);
            } else {
                return error(-1, '商品不存在');
            }
        }
    }

    public function audits()
    {
        return $this->fetch('promote_product_management/audits');
    }

    /**
     * 推广任务详情
     */
    public function taskDetail()
    {
        $member_goods_task_id = input('member_goods_task_id', 0);
        if (empty($member_goods_task_id)) {
            return error(-1, '参数错误');
        }
        $task = \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::where('member_goods_task_id', $member_goods_task_id)->find();
        if (!$task) {
            return error(-1, '任务不存在');
        }
        // 可扩展：联查商品、会员等信息
        $goods = null;
        if ($task['goods_id']) {
            $goods = (new \app\model\goods\Goods())->getGoodsInfo([['goods_id', '=', $task['goods_id']]]);
        }
        $task = $task->toArray();
        $task['goods_info'] = $goods['data'] ?? [];
        return success(0, '获取成功', $task);
    }

    /**
     * 推广任务审核
     */
    public function auditTask()
    {
        if (request()->isAjax()) {
            $member_goods_task_id = input('member_goods_task_id', 0);
            $member_goods_task_ids = input('member_goods_task_ids', '');
            $status = input('status', 0); // 100=通过，-1=驳回
            $fail_reason = input('fail_reason', '');

            if (empty($member_goods_task_id) && empty($member_goods_task_ids)) {
                return error(-1, '参数错误');
            }
            if (!in_array($status, [100, -1])) {
                return error(-1, '参数错误');
            }

            $taskModel = new \addon\leaguePoints\model\LeagueTaskMemberGoodsTask();
            
            // 判断是单个审核还是批量审核
            if (!empty($member_goods_task_ids)) {
                // 批量审核
                $ids = explode(',', $member_goods_task_ids);
                if (empty($ids)) {
                    return error(-1, '参数错误');
                }
                
                $count = 0;
                foreach ($ids as $id) {
                    $task = $taskModel->where('member_goods_task_id', $id)->find();
                    if (!$task || $task['status'] != 10) {
                        continue; // 跳过不存在或非待审核的任务
                    }
                    
                    $update = [
                        'status' => $status,
                        'fail_reason' => $status == -1 ? $fail_reason : '',
                        'update_time' => date('Y-m-d H:i:s')
                    ];
                    $res = $taskModel->where('member_goods_task_id', $id)->update($update);
                    if ($res) {
                        $count++;

                        // 审核通过，发放积分
                        if ($status == 100) {
                            $goodsTaskService = new \addon\leaguePoints\service\MemberGoodsTask();
                            $ret = $goodsTaskService->handleTaskApproved($id);

                        }
                    }
                }
                
                if ($count > 0) {
                    return success(0, '成功审核' . $count . '条任务');
                } else {
                    return error(-1, '未找到可审核的任务');
                }
            } else {
                // 单个审核
                $task = $taskModel->where('member_goods_task_id', $member_goods_task_id)->find();
                if (!$task) {
                    return error(-1, '任务不存在');
                }
                if ($task['status'] != 10) { // 仅待审核可操作
                    return error(-1, '当前状态不可审核');
                }

                $update = [
                    'status' => $status,
                    'fail_reason' => $status == -1 ? $fail_reason : '',
                    'update_time' => date('Y-m-d H:i:s')
                ];
                $res = $taskModel->where('member_goods_task_id', $member_goods_task_id)->update($update);
                if ($res !== false) {
                    // 审核通过，发放积分
                    if ($status == 100) {
                        $goodsTaskService = new \addon\leaguePoints\service\MemberGoodsTask();
                        $ret = $goodsTaskService->handleTaskApproved($member_goods_task_id);
                    }
                    
                    return success(0, '审核成功');
                } else {
                    return error(-1, '审核失败');
                }
            }
        }
    }

    /**
     * 获取待审核任务数量
     */
    public function getAuditCount()
    {
        $league_task_key = input('league_task_key', 'league_1');
        $count = \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::where([
            ['status', '=', 10],
            ['league_task_key', '=', $league_task_key]
        ])->count();
        return success(0, '获取成功', ['count' => $count]);
    }

    /**
     * 审核任务分页列表
     */
    public function auditList()
    {
        $page = input('page', 1);
        $page_size = input('limit', PAGE_LIST_ROWS);
        $league_task_key = input('league_task_key', 'league_1');
        $status = input('status', '');
        $start_time = input('complete_start_time', '');
        $end_time = input('complete_end_time', '');
        $mobile = input('mobile', '');
        $where = [
            ['league_task_key', '=', $league_task_key]
        ];
        
        // 状态筛选
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }

        // 时间段筛选
        if ($start_time !== '' && $end_time !== '') {
            $where[] = ['submit_time', 'between', [$start_time,$end_time]];
        }

        // 手机筛选
        if ($mobile !== '') {
           $memberId = MemberModel::where("mobile", $mobile)->value('member_id');
            $where[] = ['member_id', '=', $memberId];
        }
        
        // 查询待审核任务
        $list = \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::where($where)
            ->order('submit_time', 'desc')
            ->paginate(['list_rows' => $page_size, 'page' => $page]);
        
        $result = model_to_api_page($list);

        // 补充商品和会员信息
        if (!empty($result['list'])) {
            foreach ($result['list'] as $key => &$item) {
                // 获取商品信息
                if ($item['goods_id']) {
                    $goods_info = (new \app\model\goods\Goods())->getGoodsInfo([['goods_id', '=', $item['goods_id']]], 'goods_id,goods_name,goods_image');
                    $item['goods_name'] = $goods_info['data']['goods_name'] ?? '';
                    $item['goods_image'] = $goods_info['data']['goods_image'] ?? '';
                }
                
                // 获取会员信息
                if ($item['member_id']) {
                    $member_info = (new \app\model\member\Member())->getMemberInfo([['member_id', '=', $item['member_id']]], 'member_id,nickname,mobile');
                    $item['member_nickname'] = $member_info['data']['nickname'] ?? '';
                    $item['member_mobile'] = $member_info['data']['mobile'] ?? '';
                    $item['member_display'] = $member_info['data']['mobile'] ?: ($member_info['data']['nickname'] ?: $item['member_id']); // 优先显示手机号
                }
                
                // 获取订单号
                if ($item['order_id']) {
                    $order_info = \think\facade\Db::name('order')->where('order_id', $item['order_id'])->field('order_no')->find();
                    $item['order_no'] = $order_info['order_no'] ?? '';
                } else {
                    $item['order_no'] = '';
                }
                
                // 状态文字
                $status_texts = [
                    0 => '进行中',
                    10 => '待审核',
                    100 => '已通过',
                    -1 => '已驳回'
                ];
                $item['status_text'] = $status_texts[$item['status']] ?? '未知';
                
                // 给每一行增加操作按钮显示标记
                $item['allow_show'] = true; // 允许查看详情
            }
        }
        
        return success(0, '获取成功', $result);
    }
}