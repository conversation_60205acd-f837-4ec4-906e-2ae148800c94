<?php

namespace addon\leaguePoints\admin\controller;

use addon\leaguePoints\model\LeagueRaffleRewardModel;
use addon\leaguePoints\model\LeagueRaffleRecordModel;
use addon\leaguePoints\service\RaffleService;
use app\admin\controller\BaseAdmin;
use app\Domain\Models\Excel\ExportExcel;
use think\facade\Db;
use think\Request;

class RaffleManagement extends BaseAdmin
{
    /**
     * 抽奖配置页面
     */
    public function config()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('limit', PAGE_LIST_ROWS);
            
            // 强制只显示status=1的奖项（未被删除的奖项）
            $condition = [['status', '=', 1]];
            
                        $reward_model = new LeagueRaffleRewardModel();
            $list = $reward_model->getRewardPageList($condition, $page, $page_size);
            
            return json(success(0, "", $list));
        } else {
            // 获取配置信息
            $raffle_service = new RaffleService();
            $config = $raffle_service->getRaffleConfig();
            
            $this->assign('config', $config['data']);
            return $this->fetch('raffle/config');
        }
    }

    /**
     * 获取抽奖配置
     */
    public function getConfig()
    {
        $raffle_service = new RaffleService();
        $config = $raffle_service->getRaffleConfig();
        if ($config['code'] == 0) {
            return json(success(0, "", $config['data']));
        } else {
            return json(error(1, $config['message'] ?? '获取配置失败'));
        }
    }
    
    /**
     * 保存抽奖配置
     */
    public function saveConfig()
    {
        if (request()->isAjax()) {
            $points = input('points', 20);
            $status = input('status', 1); // 获取活动状态
            
            $data = [
                'points' => intval($points),
                'status' => intval($status)
            ];
            
            $raffle_service = new RaffleService();
            $result = $raffle_service->saveRaffleConfig($data);
            
            if ($result['code'] == 0) {
                return json(success(0, "保存成功", $result['data']));
            } else {
                return json(error(1, $result['message'] ?? '保存失败'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 添加奖项
     */
    public function addReward()
    {
        if (request()->isAjax()) {
            $data = [
                'reward_type' => input('reward_type', 1),
                'relation_id' => input('goodscoupon_type_id', 0),
                'relation_value' => input('relation_value', ''),
                'reward_name' => input('reward_name', ''),
                'probability' => input('probability', 0),
                'reward_limit' => input('reward_limit', -1)
            ];
            
            $raffle_service = new RaffleService();
            $result = $raffle_service->addReward($data);

            if ($result['code'] == 0) {
                return json(success(0, "添加成功", $result['data']));
            } else {
                return json(error(1, $result['message'] ?? '添加失败'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 编辑奖项
     */
    public function editReward()
    {
        if (request()->isAjax()) {
            $reward_id = input('reward_id', 0);
            $data = [
                'probability' => input('probability', 0),
                'reward_limit' => input('reward_limit', -1)
            ];
            
            // 如果提供了relation_value，也更新它
            if (input('?relation_value')) {
                $data['relation_value'] = input('relation_value', '');
            }
            
            $raffle_service = new RaffleService();
            $result = $raffle_service->editReward($data, $reward_id);
            
            if ($result['code'] == 0) {
                return json(success(0, "编辑成功", $result['data']));
            } else {
                return json(error(1, $result['message'] ?? '编辑失败'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 删除奖项
     */
    public function deleteReward()
    {
        if (request()->isAjax()) {
            $reward_id = input('reward_id', 0);
            
            $raffle_service = new RaffleService();
            $result = $raffle_service->deleteReward($reward_id);
            
            if ($result['code'] == 0) {
                return json(success(0, "删除成功", $result['data']));
            } else {
                return json(error(1, $result['message'] ?? '删除失败'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 抽奖记录页面
     */
    public function record()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('limit', PAGE_LIST_ROWS);
            $member_id = input('member_id', 0);
            $mobile = input('mobile', '');
            $start_time = input('start_time', 0);
            $end_time = input('end_time', 0);
            $reward_type = input('reward_type', '');
            $status = input('status', '');
            
            $condition = [];
            
            if (!empty($mobile)) {
                // 通过手机号查找用户
                $member_info = Db::name('member')->where('mobile', $mobile)->find();
                if (!empty($member_info)) {
                    $condition[] = ['member_id', '=', $member_info['member_id']];
                } else {
                    // 未找到该手机号的会员，返回空
                    return json(success(0, "", [
                        'list' => [],
                        'count' => 0,
                        'page' => $page,
                        'page_size' => $page_size
                    ]));
                }
            }
            
            if (!empty($member_id)) {
                $condition[] = ['member_id', '=', $member_id];
            }
            
            if (!empty($start_time)) {
                $condition[] = ['create_time', '>=', $start_time];
            }
            
            if (!empty($end_time)) {
                $condition[] = ['create_time', '<=', $end_time];
            }
            
            // 增加奖项类型筛选
            if ($reward_type !== '') {
                $condition[] = ['reward_type', '=', $reward_type];
            }
            
            // 增加发放状态筛选
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            $record_model = new LeagueRaffleRecordModel();
            $list = $record_model->getRecordPageList($condition, $page, $page_size);
            
            // 添加发放状态描述
            if (!empty($list['list'])) {
                foreach ($list['list'] as &$item) {
                    if ($item['status'] == -1) {
                        $item['status_text'] = '发放失败';
                    } elseif ($item['status'] == 0) {
                        $item['status_text'] = '待发放';
                    } elseif ($item['status'] == 100) {
                        $item['status_text'] = '已发放';
                    } else {
                        $item['status_text'] = '未知状态';
                    }
                    
                    // 移除设置空充值手机号的代码，保留数据库中的原始值
                    // $item['recharge_mobile'] = '';
                }
            }
            
            return json(success(0, "", $list));
        } else {
            return $this->fetch('raffle/record');
        }
    }
    
    /**
     * 导出抽奖记录
     */
    public function exportRecord()
    {
        $member_id = input('member_id', 0);
        $mobile = input('mobile', '');
        $start_time = input('start_time', 0);
        $end_time = input('end_time', 0);
        $reward_type = input('reward_type', '');
        $status = input('status', '');
        
        $condition = [];
        
        if (!empty($mobile)) {
            // 通过手机号查找用户
            $member_info = Db::name('member')->where('mobile', $mobile)->find();
            if (!empty($member_info)) {
                $condition[] = ['member_id', '=', $member_info['member_id']];
            }
        }
        
        if (!empty($member_id)) {
            $condition[] = ['member_id', '=', $member_id];
        }
        
        if (!empty($start_time)) {
            $condition[] = ['create_time', '>=', $start_time];
        }
        
        if (!empty($end_time)) {
            $condition[] = ['create_time', '<=', $end_time];
        }
        
        // 增加奖项类型筛选
        if ($reward_type !== '') {
            $condition[] = ['reward_type', '=', $reward_type];
        }
        
        // 增加发放状态筛选
        if ($status !== '') {
            $condition[] = ['status', '=', $status];
        }
        
        $record_model = new LeagueRaffleRecordModel();
        $list = $record_model->getRecordList($condition, '*', 'create_time desc');
        
        // 获取会员信息
        $member_model = new \app\model\member\Member();
        foreach ($list as $key => $item) {
            $member_info = $member_model->getMemberInfo([['member_id', '=', $item['member_id']]], 'nickname,mobile,headimg');
            $list[$key]['member_nickname'] = $member_info['data']['nickname'] ?? '';
            $list[$key]['member_mobile'] = $member_info['data']['mobile'] ?? '';
            $list[$key]['member_headimg'] = $member_info['data']['headimg'] ?? '';
            $list[$key]['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            
            // 添加发放状态
            if ($item['status'] == -1) {
                $list[$key]['status_text'] = '发放失败';
            } elseif ($item['status'] == 0) {
                $list[$key]['status_text'] = '待发放';
            } elseif ($item['status'] == 100) {
                $list[$key]['status_text'] = '已发放';
            } else {
                $list[$key]['status_text'] = '未知状态';
            }
            
            // 移除设置空充值手机号的代码，保留数据库中的原始值
            // $list[$key]['recharge_mobile'] = '';
        }
        
        // 表头
        $header = [
            'member_mobile' => '抽奖用户',
            'points' => '当次消耗贡献值',
            'reward_name' => '抽中奖项',
            'recharge_mobile' => '充值手机号',
            'status_text' => '发放状态',
            'create_time' => '抽奖时间'
        ];
        
        // 调用导出csv方法
        $this->exportToCsv('抽奖记录', $header, $list);
    }
    
    /**
     * 导出CSV
     * @param string $file_name 导出文件名称
     * @param array $header 表头
     * @param array $data 数据
     */
    protected function exportToCsv($file_name, $header, $data)
    {
        // CSV头部
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename=' . $file_name . '.csv');
        header('Cache-Control: max-age=0');
        
        // 打开PHP文件句柄，php://output代表直接输出到浏览器
        $fp = fopen('php://output', 'a');
        
        // 输出Excel列头信息
        fputcsv($fp, array_values($header));
        
        // 逐行输出数据
        foreach ($data as $row) {
            $export_data = [];
            foreach ($header as $key => $value) {
                $export_data[] = $row[$key] ?? '';
            }
            fputcsv($fp, $export_data);
        }
        
        fclose($fp);
        exit;
    }
    
    /**
     * 导出抽奖记录Excel
     * @param Request $request
     * @return \think\response\Json
     */
    public function exportRecordExcel(Request $request)
    {
        try {
            $member_id = $request->param('member_id', 0);
            $mobile = $request->param('mobile', '');
            $start_time = $request->param('start_time', 0);
            $end_time = $request->param('end_time', 0);
            $reward_type = $request->param('reward_type', '');
            $status = $request->param('status', '');
            
            $condition = [];
            
            if (!empty($mobile)) {
                // 通过手机号查找用户
                $member_info = Db::name('member')->where('mobile', $mobile)->find();
                if (!empty($member_info)) {
                    $condition[] = ['member_id', '=', $member_info['member_id']];
                }
            }
            
            if (!empty($member_id)) {
                $condition[] = ['member_id', '=', $member_id];
            }
            
            if (!empty($start_time)) {
                $condition[] = ['create_time', '>=', $start_time];
            }
            
            if (!empty($end_time)) {
                $condition[] = ['create_time', '<=', $end_time];
            }
            
            // 增加奖项类型筛选
            if ($reward_type !== '') {
                $condition[] = ['reward_type', '=', $reward_type];
            }
            
            // 增加发放状态筛选
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            $record_model = new LeagueRaffleRecordModel();
            $list = $record_model->getRecordList($condition, '*', 'create_time desc');
            
            // 获取会员信息并格式化数据
            $member_model = new \app\model\member\Member();
            $data = [];
            foreach ($list as $key => $item) {
                $member_info = $member_model->getMemberInfo([['member_id', '=', $item['member_id']]], 'nickname,mobile,headimg');
                
                // 处理发放状态
                $status_text = '未知状态';
                if ($item['status'] == -1) {
                    $status_text = '发放失败';
                } elseif ($item['status'] == 0) {
                    $status_text = '待发放';
                } elseif ($item['status'] == 100) {
                    $status_text = '已发放';
                }
                
                $data[] = [
                    'record_id' => $item['record_id'],
                    'member_id' => $item['member_id'],
                    'member_nickname' => $member_info['data']['nickname'] ?? '',
                    'member_mobile' => $member_info['data']['mobile'] ?? '',
                    'points' => $item['points'],
                    'reward_id' => $item['reward_id'],
                    'reward_name' => $item['reward_name'],
                    'reward_type' => $item['reward_type'],
                    'reward_type_text' => $item['reward_type'] == 0 ? '谢谢参与' : ($item['reward_type'] == 1 ? '优惠券' : '话费'),
                    'recharge_mobile' => $item['recharge_mobile'], // 使用数据库中的原始值
                    'status' => $item['status'],
                    'status_text' => $status_text,
                    'create_time' => date('Y-m-d H:i:s', $item['create_time'])
                ];
            }
            
            // 使用ExportExcel类导出数据
            $exportExcel = new ExportExcel();
            $file = $exportExcel->field([
                "record_id|记录ID",
                "member_mobile|用户手机号",
                "member_nickname|用户昵称",
                "points|消耗贡献值",
                "reward_name|抽中奖品",
                "reward_type_text|奖品类型",
                "recharge_mobile|充值手机号",
                "status_text|发放状态",
                "create_time|抽奖时间"
            ])->folder("raffle_records")->data($data)->save('抽奖记录');
            
            return json(success(0, '导出成功', ['path' => $request->domain() . '/' . $exportExcel->zip($file)]));
        } catch (\Throwable $exception) {
            return json(error(-1, $exception->getMessage()));
        }
    }

    /**
     * 变更充值手机号
     * 仅在话费充值类型且待发放状态下可以修改
     */
    public function updateRechargeMobile()
    {
        if (request()->isAjax()) {
            // 获取参数
            $record_id = input('record_id', 0, 'intval');
            $mobile = input('mobile', '', 'trim');
            
            // 参数初步验证
            if (empty($record_id)) {
                return json(error(1, '记录ID不能为空'));
            }
            
            if (empty($mobile)) {
                return json(error(1, '手机号不能为空'));
            }
            
            // 使用RaffleService处理业务逻辑
            $raffle_service = new RaffleService();
            $result = $raffle_service->updateRechargeMobile($record_id, $mobile, 0); // 管理员操作，不需要会员ID验证
            
            if ($result['code'] == 0) {
                return json(success(0, "手机号更新成功"));
            } else {
                return json(error(1, $result['message'] ?? '手机号更新失败，请重试'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 批量变更记录发放状态
     */
    public function batchChangeStatus()
    {
        if (request()->isAjax()) {
            // 获取参数
            $record_ids = input('record_ids', '');
            $status = input('status', '', 'intval');
            
            // 参数初步验证
            if (empty($record_ids)) {
                return json(error(1, '请选择要更新的记录'));
            }
            
            // 将记录ID字符串转换为数组
            if (is_string($record_ids)) {
                $record_ids = explode(',', $record_ids);
            }
            
            // 验证状态值
            if (!in_array($status, [-1, 0, 100])) {
                return json(error(1, '无效的状态值'));
            }
            
            // 使用RaffleService处理业务逻辑
            $raffle_service = new RaffleService();
            $result = $raffle_service->batchUpdateRecordStatus($record_ids, $status);
            
            if ($result['code'] == 0) {
                return json(success(0, "批量更新状态成功", [
                    'affected_rows' => $result['data']['affected_rows']
                ]));
            } else {
                return json(error(1, $result['message'] ?? '批量更新状态失败，请重试'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 保存所有奖项设置
     * 一次性保存基本配置和所有奖项配置
     */
    public function saveReward()
    {
        if (request()->isAjax()) {
            // 获取基本配置
            $points = input('points', 20);
            $status = input('status', 1);
            
            // 获取奖项配置
            $rewards = input('rewards', []);
            
            // 准备数据
            $data = [
                'base_config' => [
                    'points' => intval($points),
                    'status' => intval($status)
                ],
                'rewards' => $rewards
            ];
            
            // 调用服务层方法
            $raffle_service = new RaffleService();
            $result = $raffle_service->saveAllRewards($data);
            
            if ($result['code'] == 0) {
                return json(success(0, "保存成功", $result['data']));
            } else {
                return json(error(1, $result['message'] ?? '保存失败'));
            }
        }
        
        return json(error(1, '非法请求'));
    }
}