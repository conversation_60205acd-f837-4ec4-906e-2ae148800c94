<?php

namespace addon\leaguePoints\admin\controller;

use addon\leaguePoints\domainModel\LeagueTaskPointActivityRepository;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\model\LeagueTaskPointConfigModel;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use app\admin\controller\BaseAdmin;
use app\model\member\MemberModel;
use app\Request;
use think\facade\Db;

class PointsRules extends BaseAdmin
{
    public function config(Request $request)
    {
        if($request->isAjax())
        {
            $ret = [];
            $config = LeagueTaskPointConfigModel::where("cid", 0)
                      ->field('*')
                      ->order("league_task_key","asc")
                      ->select();
                      
            foreach ($config as $c) {
                $c['goods_reward_points'] = intval($c['goods_reward_points']);
                $ret[$c['league_task_key']] = $c;
            }

            return json(success(0, '操作成功', $ret));
        }

        $this->assign('title', '贡献值规则配置');
        return $this->fetch('points_rules/config');
    }

    public function add(Request $request)
    {
        $rules = [
            'league_task_key' => 'require|in:league_1,league_2',
            'task_use_point' => 'require|number',
            'rules' => 'require|array',
            'rule_content' => 'require',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        //rules结构[{"rule_key":"recommend_register","rule_nums":"1","rule_val":"20","enable":"1"}]
        $ret = LeagueTaskPointConfigModel::create($data);
        return json(success(0, '操作成功', ['config_id'=>$ret->config_id]));
    }

    public function edit(Request $request)
    {
        $rules = [
            'config_id' => 'require|number',
            'league_task_key' => 'require|in:league_1,league_2',
            'task_use_point' => 'require|number',
            'rules' => 'require|array',
            'rule_content' => 'require',
            'goods_reward_points' => 'number|in:0,1',
            'task_complete_max' => 'number',
            'sale_task_enable' => 'number|in:0,1',
            'sale_task_max' => 'number',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $configId = $data['config_id'];
        unset($data['config_id']);

        LeagueTaskPointConfigModel::where("config_id", $configId)->update($data);
        return json(error(0, '操作成功', ['config_id'=>$configId]));
    }

    public function addMembers(Request $request)
    {
        $rules = [
            'mobiles' => 'require',
        ];
        $data = $request->only(array_keys($rules));
        validate($rules)->check($data);

        $mobiles = trim($data['mobiles']);
        //转成数组
        $mobiles = str_replace("\r\n", ",", $mobiles);
        $mobiles = str_replace("\n", ",", $mobiles);
        $mobiles = explode(",", $mobiles);


        $registerMobiles = \app\model\member\MemberModel::whereIn("mobile", $mobiles)->where("status", 1)->column("mobile");
        $noRegisterMobiles = array_diff($mobiles, $registerMobiles);

        if(count($registerMobiles) > 0)
        {
            $activityRepository = new LeagueTaskPointActivityRepository();
            //获取正在进行的所有加盟任务
            $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();
            foreach ($taskKeys as $taskKey)
            {
                $activity = $activityRepository->findTaskByCid(0, $taskKey);
                $activity->addMemberByMobiles($registerMobiles);
            }
        }

        return json(error(0, '操作成功', ['all_nums'=>count($mobiles),'add_nums'=>count($registerMobiles),'register_nums'=>count($registerMobiles), 'no_register_nums'=>count($noRegisterMobiles), 'no_register_mobiles'=>$noRegisterMobiles]));
    }
}