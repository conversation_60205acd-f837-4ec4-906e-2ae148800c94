<?php

namespace addon\leaguePoints\admin\controller;

use addon\leaguePoints\model\LeagueTaskPointCompleteRecord;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\model\user\SyncUsersModel;
use app\admin\controller\BaseAdmin;
use app\Domain\Models\Excel\ExportExcel;
use think\Request;

class JoinUsers extends BaseAdmin
{
    public function lists(Request $request)
    {
        if($request->isAjax()){
            $mobile = $request->param("mobile") ?? "";
            $task_one_permission = $request->param("task_one_permission") ?? "";
            $complete_start_time = $request->param("complete_start_time") ?? "";
            $complete_end_time = $request->param("complete_end_time") ?? "";
            $tag_ids = $request->param("tag_ids") ?? "";
            $tag_status = $request->param("tag_status") ?? "0";
            $pageSize = $request->param("page_size") ?? 20;

            $where = [
                ['league_task_key','=', 'league_1']
            ];
            $fields =['s.id', 'm.nickname', 'm.mobile', 's.member_id','s.league_task_key','s.point','s.enable','s.create_time','et.tag_id'];
            if($mobile){
                $where[] = ['mobile', '=', $mobile];
            }
            if($task_one_permission != ''){
                $where[] = ['enable', '=', $task_one_permission];
            }

            $userList = LeagueTaskPointJoinMemberModel::alias('s')
                ->join('xm_member m', 'm.member_id = s.member_id','left')
                ->join('xm_member_enterprise_tag et', 'et.member_id = s.member_id','left')
                ->field($fields)->where($where);
            if($tag_ids){
                if($tag_status == 0){
                   $userList->where('et.tag_id', 'in', explode(',',$tag_ids))->group('s.member_id,s.league_task_key');
                }
                if($tag_status == 1){
                    $userList->where('et.tag_id', 'in', explode(',',$tag_ids))->group('s.member_id,s.league_task_key')->having("count(DISTINCT et.tag_id) = ".count(explode(',',$tag_ids)));
                }
            }else{
                $userList->group('s.member_id,s.league_task_key');
            }
            $userList = $userList->order('create_time desc')->append(['league_task_name'])->paginate($pageSize);
            $userList = model_to_api_page($userList);
            foreach ($userList['list'] as $key => $value){
                $complete_where = [
                    ['member_id', '=', $value['member_id']],
                    ['status', '=', 1],
                    ['league_task_key', '=', $value['league_task_key']]
                ];
                if($complete_start_time && $complete_end_time){
                    $complete_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
                }
                // 计算任务完成数量
                $complete_data = LeagueTaskPointCompleteRecord::where($complete_where)
                    ->field(['count(*) as sum_count'])->find();
                if($complete_data){
                    $userList['list'][$key]["sum_count"] = $complete_data->sum_count;
                }else{
                    $userList['list'][$key]["sum_count"] = 0;
                }

                $task_complete_log_where = [
                    ['member_id', '=', $value['member_id']],
                    ['league_task_key', '=', $value['league_task_key']],
                    ['point_type','=', 'task_complete'],
                ];
                if($complete_start_time && $complete_end_time){
                    $task_complete_log_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
                }
                // 计算已使用贡献值
                $task_complete_log_data = MemberLeagueTaskPointLogModel::where($task_complete_log_where)->field(['sum(abs(point)) as use_point'])->find();
                $userList['list'][$key]["use_point"] = $task_complete_log_data->use_point ?? 0;

                $task_get_where = [
                    ['member_id', '=', $value['member_id']],
                    ['league_task_key', '=', $value['league_task_key']],
                    ['point','>', 0]
                ];
                if($complete_start_time && $complete_end_time){
                    $task_get_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
                }
                // 计算获取的贡献值
                $task_get_data = MemberLeagueTaskPointLogModel::where($task_get_where)->field(['sum(point) as get_point'])->find();
                $userList['list'][$key]["get_point"] = $task_get_data->get_point ?? 0;


                // 计算当前在加盟订单数
                if($value['member_id']){
                    $order_where = [
                        ['u.yp_mid','=',$value['member_id']],
                        ["t.type", "=" ,1],
                        ['t.state', '=', 0],
                        ['t.end_time', '>', time()],
                        ['t.early_exit_time','null',null]
                    ];
                    $userList['list'][$key]["order_count"] = SyncUsersModel::alias('u')->join('xm_league_task t','u.xm_uid = t.xm_uid','left')
                        ->where($order_where)->count();
                }else{
                    $userList['list'][$key]["order_count"] = 0;
                }
            }
            return json(success(0,"", $userList));
        }
        return $this->fetch('join_users/lists');
    }

    public function changeStatus(Request $request)
    {
        $params = $request->only(['id','enable']);
        $rule = [
            'id' => 'require',
            'enable' => 'require|in:0,1',
        ];
        validate($rule)->check($params);

        $result = LeagueTaskPointJoinMemberModel::whereIn('id', explode(',',$params['id']))->update(['enable' => $params['enable']]);
        if($result){
            return json(success(0, '操作成功'));
        }else{
            return json(error(1, '操作失败'));
        }
    }

    public function export(Request $request)
    {
        $mobile = $request->param("mobile") ?? "";
        $task_one_permission = $request->param("task_one_permission") ?? "";
        $complete_start_time = $request->param("complete_start_time") ?? "";
        $complete_end_time = $request->param("complete_end_time") ?? "";
        $tag_ids = $request->param("tag_ids") ?? "";
        $tag_status = $request->param("tag_status") ?? "0";

        $where = [
            ['league_task_key','=', 'league_1']
        ];

        $fields =['s.id', 'm.nickname', 'm.mobile', 's.member_id','s.league_task_key','s.point','s.enable','s.create_time'];
        if($mobile){
            $where[] = ['mobile', '=', $mobile];
        }
        if($task_one_permission != ''){
            $where[] = ['enable', '=', $task_one_permission];
        }

        $userList = LeagueTaskPointJoinMemberModel::alias('s')
            ->join('xm_member m', 'm.member_id = s.member_id','left')
            ->field($fields)->where($where)->order('create_time desc')->append(['league_task_name']);

        if($tag_ids){
            $userList->join('xm_member_enterprise_tag et', 'et.member_id = s.member_id','left');
            if($tag_status == 0){
                $userList->where('et.tag_id', 'in', explode(',',$tag_ids))->group('s.member_id,s.league_task_key');
            }
            if($tag_status == 1){
                $userList->where('et.tag_id', 'in', explode(',',$tag_ids))->group('s.member_id,s.league_task_key')->having("count(DISTINCT et.tag_id) = ".count(explode(',',$tag_ids)));
            }
        }

        $userList = $userList->select()->toArray();

        foreach ($userList as $key => $value) {
            $userList[$key]['enable_text'] = $value['enable'] == 1 ? '已开放' : '未开放';
            $complete_where = [
                ['member_id', '=', $value['member_id']],
                ['status', '=', 1],
                ['league_task_key', '=', $value['league_task_key']]
            ];
            if ($complete_start_time && $complete_end_time) {
                $complete_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
            }
            // 计算任务完成数量
            $complete_data = LeagueTaskPointCompleteRecord::where($complete_where)
                ->field(['count(*) as sum_count'])->find();
            if ($complete_data) {
                $userList[$key]["sum_count"] = $complete_data->sum_count;
            } else {
                $userList[$key]["sum_count"] = 0;
            }

            $task_complete_log_where = [
                ['member_id', '=', $value['member_id']],
                ['league_task_key', '=', $value['league_task_key']],
                ['point_type', '=', 'task_complete'],
            ];
            if ($complete_start_time && $complete_end_time) {
                $task_complete_log_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
            }
            // 计算已使用贡献值
            $task_complete_log_data = MemberLeagueTaskPointLogModel::where($task_complete_log_where)->field(['sum(abs(point)) as use_point'])->find();
            $userList[$key]["use_point"] = $task_complete_log_data->use_point ?? 0;

            $task_get_where = [
                ['member_id', '=', $value['member_id']],
                ['league_task_key', '=', $value['league_task_key']],
                ['point', '>', 0]
            ];
            if ($complete_start_time && $complete_end_time) {
                $task_get_where[] = ['create_time', 'between time', [$complete_start_time, $complete_end_time]];
            }
            // 计算获取的贡献值
            $task_get_data = MemberLeagueTaskPointLogModel::where($task_get_where)->field(['sum(point) as get_point'])->find();
            $userList[$key]["get_point"] = $task_get_data->get_point ?? 0;

            // 计算当前在加盟订单数
            if($value['member_id']){
                $order_where = [
                    ['u.yp_mid','=',$value['member_id']],
                    ["t.type", "=" ,1],
                    ['t.state', '=', 0],
                    ['t.end_time', '>', time()],
                    ['t.early_exit_time','null',null]
                ];
                $userList[$key]["order_count"] = SyncUsersModel::alias('u')->join('xm_league_task t','u.xm_uid = t.xm_uid','left')
                    ->where($order_where)->count();
            }else{
                $userList[$key]["order_count"] = 0;
            }
        }
        $exportExcel = new ExportExcel();
        $file = $exportExcel->field([
            "nickname|昵称",
            "mobile|手机号",
            "order_count|当前在加盟订单数",
            "get_point|获取贡献值",
            "use_point|已使用贡献值",
            "sum_count|贡献值完成任务一",
            "enable_text|任务一权限",
            "create_time|加入时间",
        ])->folder("league_points")->data($userList)->save('加盟贡献值活动-活动用户列表');

        $file = str_replace("\\", "/", $file);
        return json(success(0, '导出成功', ['path' => $request->domain() . '/' . $file]));
    }
}