<?php

namespace addon\leaguePoints\admin\controller;

use addon\leaguePoints\domainModel\LeagueTaskPointActivityRepository;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\service\GoodsSaleTask;
use app\admin\controller\BaseAdmin;
use app\model\goods\Goods as GoodsModel;
use addon\leaguePoints\model\LeagueTaskSaleTaskGoodsModel;
use app\model\order\OrderModel;
use Carbon\Carbon;
use think\facade\Db;

class SaleTaskManagement extends BaseAdmin
{
    /**
     * 销售任务商品列表
     */
    public function lists()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('limit', PAGE_LIST_ROWS);
            
            // 添加league_task_key筛选条件
            $league_task_key = input('league_task_key', 'league_1');
            $condition = [['league_task_key', '=', $league_task_key]];
            
            $model = new LeagueTaskSaleTaskGoodsModel();
            $list = $model->where($condition)
                ->append(['goods_name', 'total_people', 'total_reward', 'status_text'])
                ->paginate([
                    'list_rows' => $page_size,
                    'page' => $page
                ]);

            $result = model_to_api_page($list);
            return success(0, '获取成功', $result);
        } else {
            return $this->fetch('sale_task/lists');
        }
    }
    
    /**
     * 添加销售任务商品
     */
    public function add()
    {
        if (request()->isAjax()) {
            $goods_id = input('goods_id', 0);
            $reward_points = input('reward_points', 0);
            $month_sales = input('month_sales', 0);
            $league_task_key = input('league_task_key', 'league_1');
            
            // 验证数据
            if (empty($goods_id)) {
                return error(-1, '请选择商品');
            }
            if (empty($reward_points) || $reward_points <= 0) {
                return error(-1, '奖励贡献值必须大于0');
            }
            if (empty($month_sales) || $month_sales <= 0) {
                return error(-1, '月度销售指标必须大于0');
            }
            
            // 检查商品是否存在
            $goods_info = (new GoodsModel())->getGoodsInfo([['goods_id', '=', $goods_id]], 'goods_id, goods_name');
            if (empty($goods_info['data'])) {
                return error(-1, '商品不存在');
            }
            
            // 检查商品是否已添加到同一任务中
            $model = new LeagueTaskSaleTaskGoodsModel();
            $exist = $model->where([
                ['goods_id', '=', $goods_id],
                ['league_task_key', '=', $league_task_key]
            ])->find();
            
            if (!empty($exist)) {
                return error(-1, '该商品已添加为销售任务');
            }
            
            // 添加记录，使用事务确保数据一致性
            $model->startTrans();
            try {
                $data = [
                    'league_task_key' => $league_task_key,
                    'goods_id' => $goods_id,
                    'reward_points' => $reward_points,
                    'month_sales' => $month_sales,
                    'status' => LeagueTaskSaleTaskGoodsModel::STATUS_ENABLED,  // 默认启用
                    'disable_start_time' => 0
                ];
                
                $result = $model->save($data);
                
                if ($result) {
                    $this->createSaleTask($goods_id);

                    $model->commit();
                    return success(0, '添加成功');
                } else {
                    $model->rollback();
                    return error(-1, '添加失败');
                }
            } catch (\Exception $e) {
                $model->rollback();
                return error(-1, '添加失败：' . $e->getMessage());
            }
        }
    }

    protected function createSaleTask($goodsId)
    {
        $monthStart = Carbon::parse(Carbon::now()->format("Y-m"))->timestamp;
        $monthEnd = Carbon::createFromTimestamp($monthStart)->addMonth()->timestamp - 1;
        $orderIds = Db::name("order")
            ->alias('o')
            ->join('order_goods og', 'o.order_id=og.order_id')
            ->whereBetween('o.create_time', [$monthStart, $monthEnd])
            ->where('o.pay_status', 1)
            ->where('o.pay_time', ">", 0)
            ->where('og.goods_id', $goodsId)
            ->column("o.order_id");


        $service = new GoodsSaleTask();

        $activityRepository = new LeagueTaskPointActivityRepository();
        $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();
        foreach ($taskKeys as $taskKey)
        {
            $activity = $activityRepository->findTaskByCid(0, $taskKey);
            if($activity->isSaleTaskEnable())
            {
                $orders = OrderModel::whereIn("order_id", $orderIds)->select();
                foreach ($orders as $order)
                    $service->createTask($order);
            }
        }
    }
    
    /**
     * 编辑销售任务商品
     */
    public function edit()
    {
        if (request()->isAjax()) {
            $sale_task_goods_id = input('id', 0);
            $reward_points = input('reward_points', 0);
            $month_sales = input('month_sales', 0);
            $league_task_key = input('league_task_key', 'league_1');
            
            // 验证数据
            if (empty($sale_task_goods_id)) {
                return error(-1, '参数错误');
            }
            if (empty($reward_points) || $reward_points <= 0) {
                return error(-1, '奖励贡献值必须大于0');
            }
            if (empty($month_sales) || $month_sales <= 0) {
                return error(-1, '月度销售指标必须大于0');
            }
            
            // 检查记录是否存在
            $model = new LeagueTaskSaleTaskGoodsModel();
            $exist = $model->where([['sale_task_goods_id', '=', $sale_task_goods_id]])->find();
            if (empty($exist)) {
                return error(-1, '销售任务不存在');
            }
            
            // 更新记录
            $data = [
                'league_task_key' => $league_task_key,
                'reward_points' => $reward_points,
                'month_sales' => $month_sales
            ];
            
            $result = $model->where([['sale_task_goods_id', '=', $sale_task_goods_id]])->update($data);
            
            if ($result !== false) {
                return success(0, '编辑成功');
            } else {
                return error(-1, '编辑失败');
            }
        }
    }
    
    /**
     * 删除销售任务商品
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $sale_task_goods_id = input('id', 0);
            
            // 验证数据
            if (empty($sale_task_goods_id)) {
                return error(-1, '参数错误');
            }
            
            // 检查记录是否存在
            $model = new LeagueTaskSaleTaskGoodsModel();
            $exist = $model->where([['sale_task_goods_id', '=', $sale_task_goods_id]])->find();
            if (empty($exist)) {
                return error(-1, '销售任务不存在');
            }
            
            // 删除记录
            $result = $model->where([['sale_task_goods_id', '=', $sale_task_goods_id]])->delete();
            
            if ($result) {
                return success(0, '删除成功');
            } else {
                return error(-1, '删除失败');
            }
        }
    }
    
    /**
     * 获取商品信息
     */
    public function getGoodsInfo()
    {
        if (request()->isAjax()) {
            $goods_id = input('goods_id', 0);
            
            if (empty($goods_id)) {
                return error(-1, '参数错误');
            }
            
            $goods_info = (new GoodsModel())->getGoodsInfo([['goods_id', '=', $goods_id]], 'goods_id, goods_name');
            
            if (!empty($goods_info['data'])) {
                return success(0, '获取成功', $goods_info['data']);
            } else {
                return error(-1, '商品不存在');
            }
        }
    }
    
    /**
     * 启用任务
     */
    public function enableTask()
    {
        if (request()->isAjax()) {
            $sale_task_goods_id = input('id', 0);
            
            if (empty($sale_task_goods_id)) {
                return error(-1, '参数错误');
            }
            
            $model = new LeagueTaskSaleTaskGoodsModel();
            $result = $model->enableTask($sale_task_goods_id);
            
            if ($result['code'] == 0) {
                return success(0, $result['message']);
            } else {
                return error(-1, $result['message']);
            }
        }
    }
    
    /**
     * 停用任务
     */
    public function disableTask()
    {
        if (request()->isAjax()) {
            $sale_task_goods_id = input('id', 0);
            
            if (empty($sale_task_goods_id)) {
                return error(-1, '参数错误');
            }
            
            $model = new LeagueTaskSaleTaskGoodsModel();
            $result = $model->disableTask($sale_task_goods_id);
            
            if ($result['code'] == 0) {
                return success(0, $result['message']);
            } else {
                return error(-1, $result['message']);
            }
        }
    }
}