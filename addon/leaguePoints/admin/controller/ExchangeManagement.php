<?php


namespace addon\leaguePoints\admin\controller;


use app\admin\controller\BaseAdmin;
use addon\leaguePoints\service\LeagueExchangeService;

class ExchangeManagement extends BaseAdmin
{
    /**
     * 兑换商品列表
     */
    public function lists()
    {
        if (request()->isAjax())
        {
            $page = input('page', 1);
            $page_size = input('page_size', 10);
            $search = input('search', '');

            $condition = [];
            if ($search) {
                $condition[] = ['name', 'like', '%' . $search . '%'];
            }

            $service = new LeagueExchangeService();
            $res = $service->getExchangeGoodsList($condition, $page, $page_size);
            return $res;
        }
        else {
            return $this->fetch('exchange/lists');
        }
    }

    /**
     * 添加兑换物品
     */
    public function add()
    {
        if (request()->isAjax())
        {
            $data = [
                'name' => input('name', ''),
                'type' => input('type', 1), // 默认为优惠券类型
                'relation_id' => input('relation_id', 0),
                'points' => input('points', 0),
                'status' => 1 // 默认上架状态
            ];

            // 数据验证
            if (empty($data['name'])) {
                return error(-1, '兑换物名称不能为空');
            }
            if (empty($data['relation_id'])) {
                return error(-1, '请选择关联优惠券');
            }
            if ($data['points'] <= 0) {
                return error(-1, '兑换所需积分必须大于0');
            }

            $service = new LeagueExchangeService();
            $res = $service->addExchangeGoods($data);
            return $res;
        }
    }

    /**
     * 编辑兑换物品
     */
    public function edit()
    {
        if (request()->isAjax())
        {
            $id = input('id', 0);
            if ($id <= 0) {
                return error(-1, '参数错误');
            }

            $data = [
                'points' => input('points', 0),
            ];

            // 数据验证
            if ($data['points'] <= 0) {
                return error(-1, '兑换所需积分必须大于0');
            }

            $service = new LeagueExchangeService();
            $res = $service->editExchangeGoods($data, $id);
            return $res;
        }
    }

    /**
     * 获取兑换物品详情
     */
    public function detail()
    {
        if (request()->isAjax())
        {
            $id = input('id', 0);
            if ($id <= 0) {
                return error(-1, '参数错误');
            }

            $service = new LeagueExchangeService();
            $res = $service->getExchangeGoodsInfo($id);
            return $res;
        }
    }

    /**
     * 修改状态 - 上下架
     */
    public function modifyStatus()
    {
        if (request()->isAjax())
        {
            $id = input('id', 0);
            $status = input('status', 0);

            if ($id <= 0) {
                return error(-1, '参数错误');
            }

            $service = new LeagueExchangeService();
            $res = $service->modifyStatus($id, $status);
            return $res;
        }
    }
}