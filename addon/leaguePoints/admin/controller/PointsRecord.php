<?php

namespace addon\leaguePoints\admin\controller;

use addon\leaguePoints\dataType\LEAGUE_TASK_RULE_KEY;
use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\domainModel\OtherPointReward;
use addon\leaguePoints\facade\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use app\admin\controller\BaseAdmin;
use app\Domain\Models\Excel\CustomPHPExcel;
use app\Domain\Models\Excel\ExportExcel;
use think\Request;
use addon\leaguePoints\service\SendLeaguePointService;
use app\model\member\MemberModel;
use think\facade\Db;

class PointsRecord extends BaseAdmin
{
    public function lists(Request $request)
    {
        if($request->isAjax()){
            $mobile = $request->param("mobile") ?? "";
            $record_start_time = $request->param("record_start_time") ?? "";
            $record_end_time = $request->param("record_end_time") ?? "";
            $point_type = $request->param("point_type") ?? "";
            $member_id = $request->param('member_id') ?? "";
            $league_task_key = $request->param('league_task_key') ?? "league_1";
            $pageSize = $request->param("page_size") ?? 20;

            $model = new MemberLeagueTaskPointLogModel();
            $where = [
                ['league_task_key', '=', $league_task_key]
            ];
            if($mobile){
                $where[] = ['m.mobile', '=', $mobile];
            }
            if($point_type){
                $where[] = ['s.point_type', '=', $point_type];
            }
            if($record_start_time && $record_end_time){
                $where[] = ['s.create_time', 'between time', [$record_start_time, $record_end_time]];
            }
            if($member_id){
                $where[] = ['s.member_id', '=', $member_id];
            }
            $fields =['s.id', 'm.mobile', 's.point_type','s.point', 's.create_time', 's.remark', 's.admin_user'];
            $list = $model->alias('s')->join('xm_member m', 'm.member_id = s.member_id','left')
                ->field($fields)->where($where)->order('create_time desc')->append(['point_type_text'])->paginate($pageSize);
            $list = model_to_api_page($list);
            return json(success(0,"", $list));
        }
        $type_list = [];
        $tmp_list = MEMBER_LEAGUE_POINT_TYPE::toArray();
        array_walk($tmp_list, function($value, $key) use (&$type_list){
            $type_list[$value] = lang('points_record.'.$value);
        });
        return $this->fetch('points_record/lists',['type_list'=>$type_list]);
    }

    public function export(Request $request){
        $mobile = $request->param("mobile") ?? "";
        $record_start_time = $request->param("record_start_time") ?? "";
        $record_end_time = $request->param("record_end_time") ?? "";
        $point_type = $request->param("point_type") ?? "";

        $model = new MemberLeagueTaskPointLogModel();
        $where = [];
        if($mobile){
            $where[] = ['m.mobile', '=', $mobile];
        }
        if($point_type){
            $where[] = ['s.point_type', '=', $point_type];
        }
        if($record_start_time && $record_end_time){
            $where[] = ['s.create_time', 'between time', [$record_start_time, $record_end_time]];
        }
        $fields =['s.id', 'm.mobile', 's.point_type','s.point', 's.create_time', 's.remark', 's.admin_user'];
        $list = $model->alias('s')->join('xm_member m', 'm.member_id = s.member_id','left')
            ->field($fields)->where($where)->order('create_time desc')->append(['point_type_text'])->select()->toArray();

        $exportExcel = new ExportExcel();
        $file = $exportExcel->field([
            "mobile|手机号",
            "point_type_text|积分类型",
            "point|积分变动",
            "remark|备注",
            "admin_user|操作人",
            "create_time|记录时间",
        ])->folder("league_points")->data($list)->save('加盟贡献值活动-贡献值记录明细');

        $file = str_replace("\\", "/", $file);
        return json(success(0, '导出成功', ['path' => $request->domain() . '/' . $file]));
    }

    /**
     * 单个发放贡献值
     */
    public function sendSingle()
    {
        $adminUser = $this->user_info['username'] ?? '';
        if (request()->isPost()) {
            $mobile = input('mobile', '');
            $points = input('points', 0);
            $remark = input('remark', '手动发放贡献值');
            
            if (empty($mobile)) {
                return json(error(1, '手机号不能为空'));
            }

            if (empty($adminUser)) {
                return json(error(1, '管理员不存在'));
            }
            
            if (empty($points) || !is_numeric($points)) {
                return json(error(1, '贡献值必须是数字且不能为空'));
            }
            
            // 查询会员信息
            $member = MemberModel::where('mobile', $mobile)->where("status", 1)->find();
            if (empty($member)) {
                return json(error(1, '未找到该手机号对应的用户'));
            }

            $pointReward = new OtherPointReward($member, $points, $adminUser, $remark);
            $receiveMember = $pointReward->receiveMember();
            $failReason = '';
            $taskKey = 'league_1';
            $memberTask = MemberLeagueTaskPointRepository::findTask($receiveMember->member_id, $taskKey);

            if (empty($memberTask)) {
                return json(error(1, '该用户无贡献值权限'));
            }

            $ret = $memberTask->sendOtherPointReward($pointReward, $failReason);

            if($ret)
                return json(success(0, '发放成功'));
            else
                return json(error(1, '发放失败:'.$failReason));
        }
        
        return json(error(1, '非法请求'));
    }
    
    /**
     * 批量发放贡献值
     */
    public function sendBatch(Request $request)
    {
        if (request()->isPost()) {
            $data = input('data', '');
            if (empty($data)) {
                return json(error(1, '发放数据不能为空'));
            }
            
            $data = json_decode($data, true);
            if (!is_array($data) || count($data) == 0) {
                return json(error(1, '发放数据格式错误'));
            }
            
            $adminUser = $this->user_info['username'] ?? '';
            if (empty($adminUser)) {
                return json(error(1, '管理员不存在'));
            }

            $resultList = [];
            Db::startTrans();
            try {
                foreach ($data as $i=>$item) {
                    $resultList[$i] = $item;
                    if (empty($item['mobile']) || empty($item['points'])) {

                        $resultList[$i]['result'] = false;
                        $resultList[$i]['fail_reason'] = '手机号或发放贡献值为空';
                        continue;
                    }
                    
                    // 查询会员信息
                    $member = MemberModel::where('mobile', $item['mobile'])->where("status", 1)->find();
                    if (empty($member)) {

                        $resultList[$i]['result'] = false;
                        $resultList[$i]['fail_reason'] = '会员不存在或已禁用';
                        continue;
                    }
                    
                    // 发放贡献值 - 不使用返回值，避免类型转换错误
                    $remark = $item['remark'] ?: '';
                    try {

                        $pointReward = new OtherPointReward($member, $item['points'], $adminUser, $remark);
                        $receiveMember = $pointReward->receiveMember();
                        $failReason = '';
                        $taskKey = 'league_1';
                        $memberTask = MemberLeagueTaskPointRepository::findTask($receiveMember->member_id, $taskKey);
                        if(!$memberTask)
                        {
                            $resultList[$i]['result'] = false;
                            $resultList[$i]['fail_reason'] = '该用户无贡献值权限';
                            continue;
                        }
                        if($memberTask->sendOtherPointReward($pointReward, $failReason))
                        {
                            $resultList[$i]['result'] = true;
                            $resultList[$i]['fail_reason'] = '';
                        }
                        else
                        {
                            $resultList[$i]['result'] = false;
                            $resultList[$i]['fail_reason'] = $failReason;
                            continue;
                        }


                    } catch (\Exception $e) {
                        // 记录单条发放失败，但不影响其他数据发放
                        $resultList[$i]['result'] = false;
                        $resultList[$i]['fail_reason'] = $e->getMessage();
                        continue;
                    }
                }

                $ret['successNums'] = 0;
                $ret['successPoints'] = 0;
                $ret['failNums'] = 0;
                $ret['failPoints'] = 0;
                $ret['fileUrl'] = '';
                $ret['failList'] = [];
                foreach ($resultList as &$r)
                {
                    if($r['result'])
                    {
                        $ret['successNums'] ++;
                        $ret['successPoints'] += $r['points'];
                    }
                    else
                    {
                        $ret['failNums'] ++;
                        $ret['failPoints'] += $r['points'];
                        $ret['failList'][] = $r;
                    }
                    $r['result'] = $r['result'] ? "发放成功" : "发放失败";
                }

                if($resultList)
                {
                    $exportExcel = new ExportExcel();
                    $file = $exportExcel->field([
                        "mobile|手机号",
                        "points|发放数量",
                        "remark|备注",
                        "result|发放结果",
                        "fail_reason|失败原因",
                    ])->folder("league_points")->data($resultList)->save('批量发放贡献值');
                    $ret['fileUrl'] = $request->domain() . '/' . $exportExcel->zip($file);
                }
                Db::commit();
                return json(success(1, '批量发放成功', $ret));
            } catch (\Exception $e) {
                Db::rollback();
                return json(error(1, '批量发放失败：' . $e->getMessage()));
            }
        }
        
        return json(error(1, '非法请求'));
    }

    public function importSend(\app\Request $request)
    {
        $post = input();
        $file = request()->file('file');
        if (empty($file)) {
            return json(error(1, '请选择上传文件'));
        }

        $file_name = $file->getPathname();
        $customPHPExcel = new CustomPHPExcel($file_name);
        $exportExcel = new \PHPExcel();

        $flagTure = 0;
        $flagFalse = 0;
        $data = [];
        $msg = [];
        
        $customPHPExcel->batchRead(function ($objPHPExcel, $startRow, $endRow) use(&$msg, &$flagTure, &$flagFalse, &$data, $customPHPExcel, $exportExcel){
            $sheetSelected = 0;
            $objPHPExcel->setActiveSheetIndex($sheetSelected);
            $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow(); // 获取表格行数
            
            // 循环读取每个单元格的数据
            if($startRow == 1){
                $startRow = 2; // 跳过表头
            }
            
            for ($row = $startRow; $row <= $rowCount; $row++) {
                $mobile = trim((string)$objPHPExcel->getActiveSheet()->getCell('A' . $row)->getValue());
                if (empty($mobile)) {
                    $flagFalse++;
                    $msg[$row] = '手机号不能为空';
                    continue;
                }
                
                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
                    $flagFalse++;
                    $msg[$row] = '手机号格式不正确';
                    continue;
                }
                
                $points = trim((string)$objPHPExcel->getActiveSheet()->getCell('B' . $row)->getValue());
                if (empty($points)) {
                    $flagFalse++;
                    $msg[$row] = '发放数量不能为空';
                    continue;
                }
                
                if (!is_numeric($points) || floatval($points) <= 0) {
                    $flagFalse++;
                    $msg[$row] = '发放数量必须为大于0的数字';
                    continue;
                }
                
                $remark = trim((string)$objPHPExcel->getActiveSheet()->getCell('C' . $row)->getValue());
                
                // 添加有效数据
                $data[] = [
                    'mobile' => $mobile,
                    'points' => $points,
                    'remark' => $remark ?: '批量发放贡献值'
                ];
                
                $flagTure++;
            }
            
            $customPHPExcel->importCreateErrFile($objPHPExcel, $msg, $exportExcel);
        });
        
        $url = $flagFalse > 0 ? $customPHPExcel->saveErrFile($exportExcel, "导入贡献值") : "";
        
        // 删除临时文件
        @unlink($file_name);
        
        $total_points = 0;
        foreach ($data as $item) {
            $total_points += floatval($item['points']);
        }
        
        return json(success(0, '解析完成，共'.$flagTure.'条有效数据，'.$flagFalse.'条无效数据', [
            'data' => $data,
            'total_users' => $flagTure,
            'total_points' => $total_points,
            'error_url' => $url
        ]));
    }
}