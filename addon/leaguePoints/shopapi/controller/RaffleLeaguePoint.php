<?php


namespace addon\leaguePoints\shopapi\controller;


use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\goodscoupon\model\GoodscouponGoods;
use addon\goodscoupon\model\GoodscouponType;
use addon\leaguePoints\model\LeagueRaffleRewardModel;
use addon\leaguePoints\service\LeagueExchangeService;
use addon\leaguePoints\service\MemberLeaguePoints;
use addon\leaguePoints\service\RaffleService;
use app\Request;
use app\shopapi\controller\BaseApi;
use think\facade\Db;

class RaffleLeaguePoint extends BaseApi
{
    public function index()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        // 初始化空数组用于存储所有奖项
        $rewardList = [
            'list' => [],
            'member_points' => 0,
            'raffle_points' => 0
        ];
        
        // 查询所有有效的奖项基本信息
        $basicRewards = Db::name('league_raffle_reward')
            ->where('status = 1')
            ->field('reward_id, reward_type, relation_id, relation_value, reward_name')
            ->order('reward_id asc')
            ->select()->toArray();
        
        // 分类存储各类型奖项ID
        $couponRewardIds = [];
        $phoneRewardIds = [];
        
        foreach ($basicRewards as $reward) {
            if ($reward['reward_type'] == 1) { // 优惠券类型
                $couponRewardIds[] = $reward['reward_id'];
            } else if ($reward['reward_type'] == 2) { // 话费充值类型
                $phoneRewardIds[] = $reward['reward_id'];
            }
        }
        
        // 处理优惠券类型奖项
        if (!empty($couponRewardIds)) {
            $couponRewards = Db::name('league_raffle_reward')
                ->alias('rr')
                ->join('promotion_goodscoupon_type pgt', 'pgt.goodscoupon_type_id=rr.relation_id')
                ->where('rr.status = 1')
                ->where('rr.reward_type = 1') // 奖项类型为优惠券
                ->where('pgt.status = 1')
                ->whereIn('rr.reward_id', $couponRewardIds)
                ->field('rr.reward_id, rr.reward_type, rr.reward_name,
                        pgt.goodscoupon_type_id, pgt.money, pgt.at_least, pgt.use_scenario')
                ->order('rr.reward_id asc')
                ->select()->toArray();
            
            $goodsCouponGoods = new GoodscouponGoods();
            foreach ($couponRewards as $i => $reward) {
                $res = [];
                if ($reward['use_scenario'] == GOODSCOUPON_SCENARIO::GOODS) {
                    $res = $goodsCouponGoods->getGoodsCouponGoodsList($this->member_id ?? 0, $reward['goodscoupon_type_id'], 1, 3);
                } elseif ($reward['use_scenario'] == GOODSCOUPON_SCENARIO::CATEGORY) {
                    $res = $goodsCouponGoods->getGoodsCouponCategoryGoodsList($this->member_id ?? 0, $reward['goodscoupon_type_id'], 1, 3);
                } elseif ($reward['use_scenario'] == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS) {
                    $res = $goodsCouponGoods->getGoodsCouponExcludeGoodsList($this->member_id ?? 0, $reward['goodscoupon_type_id'], 1, 3);
                } elseif ($reward['use_scenario'] == GOODSCOUPON_SCENARIO::TOPIC) {
                    $res = $goodsCouponGoods->getTopicGoodsList($this->member_id ?? 0, $reward['goodscoupon_type_id'], 1, 3);
                } elseif ($reward['use_scenario'] == GOODSCOUPON_SCENARIO::ALL) {
                    $res = $goodsCouponGoods->getALlGoodsList($this->member_id ?? 0, $reward['goodscoupon_type_id'], 1, 3);
                }
                $couponRewards[$i]['use_goods'] = $res ? $res['data']['list'] : [];
                $couponRewards[$i]['use_goods_nums'] = $res ? $res['data']['count'] : 0;
                $couponRewards[$i]['use_scenario_text'] = $reward['use_scenario'] == 1 ? '全场通用券' : ($reward['use_scenario'] == 2 ? '指定分类券' : '指定商品券');
                $couponRewards[$i]['wx_url'] = (new GoodscouponType())->getWxUrl($reward['goodscoupon_type_id']);
                $couponRewards[$i]['money'] = (float)$reward['money'];
                $couponRewards[$i]['at_least'] = (float)$reward['at_least'];
            }
            
            // 将优惠券类型奖项添加到结果列表
            $rewardList['list'] = array_merge($rewardList['list'], $couponRewards);
        }
        
        // 处理话费充值类型奖项
        if (!empty($phoneRewardIds)) {
            $phoneRewards = Db::name('league_raffle_reward')
                ->where('status = 1')
                ->where('reward_type = 2') // 奖项类型为话费充值
                ->whereIn('reward_id', $phoneRewardIds)
                ->field('reward_id, reward_type, relation_id, relation_value, reward_name')
                ->order('reward_id asc')
                ->select()->toArray();
            
            foreach ($phoneRewards as $i => $reward) {
                // 添加话费充值类型特有的字段
                $phoneRewards[$i]['money'] = (float)$reward['relation_value']; // 使用relation_value作为充值金额
                $phoneRewards[$i]['at_least'] = 0; // 话费充值没有最低消费限制
                $phoneRewards[$i]['use_goods'] = []; // 话费充值没有关联商品
                $phoneRewards[$i]['use_goods_nums'] = 0;
                $phoneRewards[$i]['use_scenario_text'] = '默认充值到登陆手机号';
                $phoneRewards[$i]['wx_url'] = ''; // 话费充值没有微信URL
            }
            
            // 将话费充值类型奖项添加到结果列表
            $rewardList['list'] = array_merge($rewardList['list'], $phoneRewards);
        }
        
        // 获取会员积分
        $rewardList['member_points'] = MemberLeaguePoints::getPoints($this->member_id);
        
        // 获取抽奖所需积分
        $service = new RaffleService();
        $rewardList['raffle_points'] = $service->getRafflePoints();

        return $this->resJson($this->success($rewardList));
    }

    public function doRaffle()
    {

        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $mobile = $this->user_info['username'];
        $service = new RaffleService();
        $res = $service->doRaffle($this->member_id, $mobile);
        return $this->resJson($res);
    }

    /**
     * 获取中奖记录
     * 根据设计图返回非谢谢参与的中奖记录数据
     */
    public function records()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $page_size = input('page_size', PAGE_LIST_ROWS);
        // 初始化结果数组
        $result = [
            'list' => []
        ];

        // 查询中奖记录，不包括谢谢参与的记录（reward_type != 0）
        $records = Db::name('league_raffle_record')
            ->where('member_id', $this->member_id)
            ->where('reward_type', '<>', 0) // 排除谢谢参与
            ->order('create_time desc')
            ->paginate($page_size);
        $records = model_to_api_page($records);

        if (!empty($records['list'])) {
            // 收集所有优惠券类型的奖品ID
            $couponIds = [];
            foreach ($records['list'] as $record) {
                if ($record['reward_type'] == 1) { // 优惠券类型
                    $couponIds[] = $record['relation_id'];
                }
            }

            // 查询优惠券信息
            $couponInfo = [];
            if (!empty($couponIds)) {
                $coupons = Db::name('promotion_goodscoupon_type')
                    ->whereIn('goodscoupon_type_id', $couponIds)
                    ->column('*', 'goodscoupon_type_id');
                $couponInfo = $coupons ?: [];
            }

            // 处理每条记录的数据格式
            foreach ($records['list'] as $i=>$record) {
                $item = [
                    'record_id' => $record['record_id'],
                    'reward_name' => $record['reward_name'],
                    'reward_type' => $record['reward_type'],
                    'create_time' => date('Y-m-d H:i:s', $record['create_time']),
                    'status' => $record['status'],
                    'status_text' => $record['status'] == 100 ? '已发放' : '待发放'
                ];

                // 根据不同奖励类型处理额外信息
                if ($record['reward_type'] == 1) { // 优惠券类型
                    if (isset($couponInfo[$record['relation_id']])) {
                        $coupon = $couponInfo[$record['relation_id']];
                        $item['money'] = (float)$coupon['money'];
                        $item['at_least'] = (float)$coupon['at_least'];
                    }
                } elseif ($record['reward_type'] == 2) { // 话费充值类型
                    $item['mobile'] = $record['recharge_mobile'];
                    if($record['status'] == 0 && time() - $record['create_time'] < 3600 * 48)
                    {
                        $item['change_mobile'] = 1 ;
                    }
                    else
                    {
                        $item['change_mobile'] = 0 ;
                    }
                }

                $records['list'][$i] = $item;
            }
        }

        return $this->resJson($this->success($records));
    }
    
    /**
     * 更新话费充值手机号
     * 仅在话费充值类型且待发放状态下可以修改
     */
    public function updateMobile(Request $request)
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));
        // 获取参数
        $record_id = $request->post('record_id', 0, 'intval');
        $mobile = $request->post('mobile', '', 'trim');
        
        // 调用服务层方法处理业务逻辑
        $service = new RaffleService();
        $result = $service->updateRechargeMobile($record_id, $mobile, $this->member_id);
        
        return $this->resJson($result);
    }
}