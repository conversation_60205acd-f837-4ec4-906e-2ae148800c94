<?php


namespace addon\leaguePoints\shopapi\controller;

use addon\goodscoupon\constant\GOODSCOUPON_SCENARIO;
use addon\goodscoupon\model\Goodscoupon as GoodscouponModel;
use addon\goodscoupon\model\GoodscouponGoods;
use addon\goodscoupon\model\GoodscouponType;
use addon\leaguePoints\model\LeagueExchangeGoodsModel;
use addon\leaguePoints\service\LeagueExchangeService;
use addon\leaguePoints\service\MemberLeaguePoints;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlWeappNoticeRepository;
use app\Domain\Services\WeappNotice\SendGoodscouponNoticeService;
use app\shopapi\controller\BaseApi;
use think\facade\Db;

class ExchangeLeaguePoint extends BaseApi
{
    public function exchangeList()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $page_size = input('page_size', PAGE_LIST_ROWS);

        $exchangeList = Db::name('league_exchange_goods')
            ->alias('eg')
            ->join('promotion_goodscoupon_type pgt', 'pgt.goodscoupon_type_id=eg.relation_id and eg.type=1')
            ->where('eg.status = 1')
            ->where('pgt.status = 1')
            ->field('eg.id as exchange_goods_id,goodscoupon_type_id,goodscoupon_name,points,money,use_scenario,at_least')
            ->paginate($page_size);
        $exchangeList = model_to_api_page($exchangeList);
        $exchangeList['member_points'] = MemberLeaguePoints::getPoints($this->member_id);
        $goodsCouponGoods = new GoodscouponGoods();
        foreach ($exchangeList['list'] as $i=>$ex)
        {
            $res = [];
            if ($ex['use_scenario'] == GOODSCOUPON_SCENARIO::GOODS) {
                $res = $goodsCouponGoods->getGoodsCouponGoodsList($this->member_id ?? 0, $ex['goodscoupon_type_id'], 1, 3);
            } elseif ($ex['use_scenario'] == GOODSCOUPON_SCENARIO::CATEGORY) {
                $res = $goodsCouponGoods->getGoodsCouponCategoryGoodsList($this->member_id ?? 0, $ex['goodscoupon_type_id'], 1, 3);
            } elseif ($ex['use_scenario'] == GOODSCOUPON_SCENARIO::EXCLUDE_GOODS) {
                $res = $goodsCouponGoods->getGoodsCouponExcludeGoodsList($this->member_id ?? 0, $ex['goodscoupon_type_id'], 1, 3);
            } elseif ($ex['use_scenario'] == GOODSCOUPON_SCENARIO::TOPIC) {
                $res = $goodsCouponGoods->getTopicGoodsList($this->member_id ?? 0, $ex['goodscoupon_type_id'], 1, 3);
            }elseif ($ex['use_scenario'] == GOODSCOUPON_SCENARIO::ALL){
                $res = $goodsCouponGoods->getALlGoodsList($this->member_id ?? 0, $ex['goodscoupon_type_id'], 1, 3);
            }
            $exchangeList['list'][$i]['use_goods'] = $res ? $res['data']['list'] : [];
            $exchangeList['list'][$i]['use_goods_nums'] = $res ? $res['data']['count'] : 0;
            $exchangeList['list'][$i]['use_scenario_text'] = $ex['use_scenario'] == 1 ? '全场通用券' : ($ex['use_scenario'] == 2 ? '指定分类券' : '指定商品券');
            $exchangeList['list'][$i]['wx_url'] = (new GoodscouponType())->getWxUrl($ex['goodscoupon_type_id']);
        }
        return $this->resJson($this->success($exchangeList));
    }

    public function exchange()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));
        //$this->member_id = 777;

        $exchange_goods_id = $this->params['exchange_goods_id'] ?? 0;

        if (empty($exchange_goods_id)) {
            return $this->resJson($this->error([],'参数错误'));
        }

        $service = new LeagueExchangeService();
        $exchange_goods = LeagueExchangeGoodsModel::find($exchange_goods_id);
        if(!$exchange_goods)
            return $this->resJson($this->error([],'兑换品不存在'));

        $res = $service->exchange($this->member_id, $exchange_goods);
        return $this->resJson($res);
    }
}