<?php


namespace addon\leaguePoints\shopapi\controller;


use addon\goodscoupon\model\GoodsCouponTypeRepository;
use addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\domainModel\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\LeagueTaskGoodsTaskModel;
use addon\leaguePoints\model\LeagueTaskPointCompleteRecord;
use addon\leaguePoints\model\LeagueTaskPointConfigModel;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use addon\leaguePoints\model\MemberLeagueTaskPointLogModel;
use addon\leaguePoints\service\RaffleService;
use addon\multipleDiscount\model\MultipleDiscountRepository;
use app\model\app\App;
use app\model\goods\GoodsModel;
use app\model\shop\Shop;
use app\service\shop\ShopGoodsService;
use app\shopapi\controller\BaseApi;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Request;
use think\Model;

class MemberLeaguePoint extends BaseApi
{
    public function index()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));
        //获取正在进行的所有加盟任务
        $taskRepository = new MemberLeagueTaskPointRepository(new LeagueTaskPointJoinMemberModel());
        $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();
        $ret = [];

        $diyview = (new Shop())->getDefaultShareInfo($this->site_id);
        foreach ($taskKeys as $taskKey)
        {
            $task = $taskRepository->findTask($this->member_id, $taskKey);
            if(!$task)
                return $this->response($this->error('用户未参与活动'));
            $ret[$taskKey]['point'] = $task->getPoint();
            $ret[$taskKey]['auto_complete_nums'] = $task->getCompleteNumsNowMonth();
            $ret[$taskKey]['wait_complete_task'] = $task->getSMLeagueTasks();
            $ret[$taskKey]['wait_complete_nums'] = count($ret[$taskKey]['wait_complete_task']);
            $ret[$taskKey]['rules'] = $task->getRules();
            $ret[$taskKey]['rule_content'] = $task->getBelongActivity()->getRuleContent();
            $ret[$taskKey]['enable'] = $task->getEnable();
            $ret[$taskKey]['diyview_info'] = $diyview['code'] == 0 ? $diyview['data'] : [];
            $ret[$taskKey]['path'] = "/otherpages/shop/home/<USER>";
            $ret[$taskKey]['is_goods_reward_points'] = $task->getBelongActivity()->isGoodsRewardPoints();
            $ret[$taskKey]['sale_task_enable'] = $task->getBelongActivity()->isSaleTaskEnable();
            $ret[$taskKey]['raffle_enable'] = (new RaffleService())->isEnable();
        }
        return $this->resJson($this->success($ret));
    }

    /**
     * 用户贡献值进出记录
     * @return false|string|\think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function inOutRecords()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $time_period_type = $this->params['time_period_type'] ?? 'current_month';
        $custom_start_date = $this->params['custom_start_date'] ?? '';
        $custom_end_date = $this->params['custom_end_date'] ?? '';
        $point_type_list = $this->params['point_type'] ?? '';
        $league_task_key = $this->params['league_task_key'] ?? 'league_1';
        $page_size =  $this->params['page_size'] ?? 10;


        $time_period_type_list = ['current_month','last_month','current_year','custom'];
        if(!in_array($time_period_type,$time_period_type_list)){
            $time_period_type = $time_period_type_list[0];
        }
        if($point_type_list){
            $point_type_list = json_decode($point_type_list);
            if(in_array('',$point_type_list)){
                $point_type_list = [];
            }
        }else{
            $point_type_list = [];
        }
        $where = [
            ['status', '=', 1],
            ['league_task_key', '=', $league_task_key],
            ['member_id', '=', $this->member_id]
        ];
        if($time_period_type=='current_month'){
            $where[] = ['create_time','between time',[date('Y-m-01',time()), date('Y-m-t',time())]];
        }else if($time_period_type == 'last_month'){
            $where[] = ['create_time','between time',[date('Y-m-01',strtotime('-1 month')), date('Y-m-t',strtotime('-1 month'))]];
        }else if($time_period_type == 'current_year'){
            $where[] = ['create_time','between time',[date('Y-01-01',time()), date('Y-m-t',time())]];
        }else if($time_period_type == 'custom'){
            $where[] = ['create_time','between time',[$custom_start_date, $custom_end_date]];
        }
        if(count($point_type_list)>0){
            $where[] = ['point_type', 'in', $point_type_list];
        }

        $record_list = MemberLeagueTaskPointLogModel::where($where)->append(['point_type_text'])->order('create_time desc')->paginate($page_size);
        Log::info("record_list_sql:.".Db::getLastSql());
        $record_list = model_to_api_page($record_list);
        foreach ($record_list['list'] as $key=>&$value){
            if($value['point_type']==MEMBER_LEAGUE_POINT_TYPE::$TASK_COMPLETE && $value['relation_key'] && $value['relation_value']){
                $table = $value['relation_key'];
                // 查询xm_league_task_point_complete_record表
                $complete_data = \model($table)->getInfo([
                    ['id','=',$value['relation_value']]
                ],['league_name']);
                if($complete_data){
                    $value['league_name'] = $complete_data['league_name'];
                }else{
                    $value['league_name'] = '';
                }
            }
            if($value['point_type']==MEMBER_LEAGUE_POINT_TYPE::$MONTH_EMPTY){
                $value['point_type_text'] = '清空'.date('Y年m月',strtotime($value['create_time'])).'过期贡献值';
            }
        }
        $record_list['point_get'] = MemberLeagueTaskPointLogModel::where($where)->where('point','>',0)->field('sum(point) as point_get')->find()->point_get ?? 0;
        $record_list['point_use'] = MemberLeagueTaskPointLogModel::where($where)->where('point','<',0)->where('point_type', '<>', MEMBER_LEAGUE_POINT_TYPE::$MONTH_EMPTY)->field('sum(abs(point)) as point_use')->find()->point_use ?? 0;
        return $this->resJson($this->success($record_list));
    }

    /**
     * 贡献值自动完成任务记录
     * @return false|string|\think\response\Json
     * @throws \think\db\exception\DbException
     */
    public function autoCompleteTaskRecords()
    {
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $time_period_type = $this->params['time_period_type'] ?? 'current_month';
        $custom_start_date = $this->params['custom_start_date'] ?? '';
        $custom_end_date = $this->params['custom_end_date'] ?? '';
        $league_task_key = $this->params['league_task_key'] ?? 'league_1';
        $page_size =  $this->params['page_size'] ?? 10;

        $time_period_type_list = ['current_month','last_month','current_year','custom'];
        if(!in_array($time_period_type,$time_period_type_list)){
            $time_period_type = $time_period_type_list[0];
        }
        $where = [
            ['status', '=', 1],
            ['league_task_key', '=', $league_task_key],
            ['member_id', '=', $this->member_id]
        ];
        if($time_period_type == 'current_month'){
            $where[] = ['create_time','between time',[date('Y-m-01',time()), date('Y-m-t',time())]];
        }else if($time_period_type == 'last_month'){
            $where[] = ['create_time','between time',[date('Y-m-01',strtotime('-1 month')), date('Y-m-t',strtotime('-1 month'))]];
        }else if($time_period_type == 'current_year'){
            $where[] = ['create_time','between time',[date('Y-01-01',time()), date('Y-m-t',time())]];
        }else if($time_period_type == 'custom'){
            $where[] = ['create_time','between time',[$custom_start_date, $custom_end_date]];
        }

        $record_list = LeagueTaskPointCompleteRecord::where($where)->withAttr('point',function ($value,$data){
            return '-'.$value;
        })->order('create_time desc')->paginate($page_size);
        $record_list = model_to_api_page($record_list);
        foreach ($record_list['list'] as $key=>&$value){
            $value['join_time_period'] = '';
            if($value['league_data']){
                $league_data = (array)($value['league_data']);
                $value['join_time_period'] = date('d/m',strtotime($league_data['start_time'])).'-'.date('d/m',strtotime($league_data['end_time']));
            }
            unset($value['league_data']);
        }
        return $this->resJson($this->success($record_list));
    }


    /**
     * 获取我的推广任务列表
     */
    public function taskList()
    {
        // 用户验权
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        $page_size = input('page_size', PAGE_LIST_ROWS);
        // 获取status参数，用于筛选任务状态
        $status = isset($this->params['status']) ? intval($this->params['status']) : null;

        // 使用DB::name()方式连表查询
        $task_alias = 'mt';
        $goods_alias = 'g';
        $order_alias = 'o';
        $order_goods_alias = 'og';
        $goods_task_alias = 'gt';

        // 构建查询条件
        $condition = [
            [$task_alias . '.member_id', '=', $this->member_id]
        ];

        // 如果有status参数，添加到筛选条件中
        if ($status !== null) {
            if($status == 10)
                $condition[] = [$task_alias . '.status', 'in', [0,10,-1]];
            else
                $condition[] = [$task_alias . '.status', '=', $status];
        }

        // 使用DB进行连表查询
        $query = Db::name('league_task_member_goods_task')->alias($task_alias)
            ->join('goods ' . $goods_alias, $goods_alias . '.goods_id = ' . $task_alias . '.goods_id', 'left')
            ->join('order ' . $order_alias, $order_alias . '.order_id = ' . $task_alias . '.order_id', 'left')
            ->join('order_goods ' . $order_goods_alias, $order_goods_alias . '.order_id = ' . $task_alias . '.order_id AND ' . $order_goods_alias . '.goods_id = ' . $task_alias . '.goods_id', 'left')
            ->join('league_task_goods_task ' . $goods_task_alias, $task_alias . '.goods_task_id = ' . $goods_task_alias . '.goods_task_id', 'left')
            ->where($condition)
            ->field([
                $task_alias . '.member_goods_task_id',
                $task_alias . '.goods_id',
                $task_alias . '.reward_points',
                $task_alias . '.status',
                $task_alias . '.remark',
                $task_alias . '.goods_task_id',
                $task_alias . '.order_id',
                $task_alias . '.league_task_key',
                $task_alias . '.fail_reason',
                $task_alias . '.reward_status',
                $goods_alias . '.goods_name',
                $order_goods_alias . '.sku_image as goods_image',
                $goods_alias . '.introduction',
                $order_alias . '.create_time',
                $goods_task_alias . '.rules'
            ])
            ->order($task_alias . '.create_time', 'desc');

        $list = $query->paginate($page_size);
        $list = model_to_api_page($list);
        // 组装返回数据
        foreach ($list['list'] as $key => &$item) {
            // 格式化下单时间 - 使用订单的创建时间
            $item['create_time'] = $item['create_time'] ? date('Y-m-d', $item['create_time']) : '';

            $item['status_name'] = lang('member_goods_task')[$item['status']] ?? '';
            $item['applet_url'] = '/pages/goods/detail/detail?sku_id='.GoodsModel::find($item['goods_id'])->sku_id;
            $item['goods_image'] = img($item['goods_image'] ?? '');
        }
        return $this->resJson($this->success($list));
    }

    public function taskGoodsList()
    {
        $page                     = Request::param('page', 1, 'intval');
        $page_size				  = Request::param('page_size', PAGE_LIST_ROWS, 'intval');
        $params['shop_id']        = 0;

        $openTasks = LeagueTaskPointConfigModel::where("goods_reward_points", 1)->column("league_task_key");
        if(!$openTasks)
            return $this->response($this->success(['page_count'=>0, 'count'=>0, 'list'=>[]]));

        $goodsConfigs = LeagueTaskGoodsTaskModel::whereIn("league_task_key", $openTasks)->select();
        $goodsConfigArr = [];
        foreach ($goodsConfigs as $c)
        {
            $goodsConfigArr[$c->goods_id]['reward_points'] = $c->reward_points;
            $goodsConfigArr[$c->goods_id]['rules'] = $c->rules;
        }
        $goodsIds = $goodsConfigs->column('goods_id');

        if(!$goodsIds)
            return $this->response($this->success(['page_count'=>0, 'count'=>0, 'list'=>[]]));

        $params['goods_ids'] = $goodsIds;
        $data = ShopGoodsService::getInstance()->getShopGoods($params, $page, $page_size, true, null, false);
        //列表追加优惠券信息
        foreach ($data['data']['list'] as $i=>$d)
        {
            $gCoupons = (new GoodsCouponTypeRepository())->getParticipateCouponByMemberSkuId($this->member_id, $d['sku_id']);
            $this->logAction('getParticipateCouponByMemberSkuId-sku_id='.$d['sku_id']);
            $maxCoupon = null;
            foreach ($gCoupons as $c)
            {
                if(empty($maxCoupon) || $maxCoupon['money'] < $c['money'])
                    $maxCoupon = ['money'=>$c['money']];
            }
            $data['data']['list'][$i]['goods_coupon'] = $maxCoupon;

            $cache_key_discounts = "getParticipateDiscountBuSkuId_{$d['sku_id']}";
            if (Cache::has($cache_key_discounts)) {
                $discounts = Cache::get($cache_key_discounts);
            } else {
                $discounts = (new MultipleDiscountRepository())->getParticipateDiscountBuSkuId($d['sku_id']);
                Cache::set($cache_key_discounts, $discounts, 600);
            }
            $this->logAction('getParticipateDiscountBuSkuId-sku_id='.$d['sku_id']);
            $minDiscount = null;
            foreach ($discounts as $c)
            {
                if(empty($minDiscount) || $minDiscount['discount'] > $c['discount'])
                    $minDiscount = ['discount'=>$c['discount'], 'at_least'=>$c['at_least']];
            }
            $data['data']['list'][$i]['reward_points'] = $goodsConfigArr[$d['goods_id']]['reward_points'];
            $data['data']['list'][$i]['rules'] = $goodsConfigArr[$d['goods_id']]['rules'];;
        }
        //列表追加多件折扣信息
        return $this->resJson($data);
    }

    /**
     * 用户提交商品推广任务
     * @return \think\response\Json
     */
    public function submitTask()
    {
        // 用户验权
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));
        // 获取参数
        $member_goods_task_id = $this->params['member_goods_task_id'] ?? null;
        $images = $this->params['images'] ?? null;
        $remark = $this->params['remark'] ?? '';
        // 调用service
        $service = new \addon\leaguePoints\service\MemberGoodsTask();
        $result = $service->submitTask($member_goods_task_id, $this->member_id, $images,$remark);
        if ($result['code'] === 0) {
            return $this->resJson($this->success([], $result['message']));
        } else {
            return $this->resJson($this->error([], $result['message']));
        }
    }

    /**
     * 获取任务详情
     * @return \think\response\Json
     */
    public function taskDetail()
    {
        // 用户验权
        $token = $this->checkToken();
        if ($token['code'] < 0) return $this->resJson($token);
        if (empty($this->member())) return $this->resJson($this->error([],'LEAGUE_POINTS_AUTH_ERROR'));

        // 获取参数
        $member_goods_task_id = $this->params['member_goods_task_id'] ?? 0;
        if (empty($member_goods_task_id)) {
            return $this->resJson($this->error([],'参数错误'));
        }

        // 查询任务详情
        $task = \think\facade\Db::name('league_task_member_goods_task')
            ->where([
                ['member_goods_task_id', '=', $member_goods_task_id],
                ['member_id', '=', $this->member_id] // 确保只能查看自己的任务
            ])
            ->field('member_goods_task_id, images, remark, fail_reason, status')
            ->find();

        if (!$task) {
            return $this->resJson($this->error([],'任务不存在'));
        }

        // 处理图片字段，转为数组
        if (!empty($task['images'])) {
            $task['images'] = explode(',', $task['images']);
        } else {
            $task['images'] = [];
        }

        return $this->resJson($this->success($task));
    }


}