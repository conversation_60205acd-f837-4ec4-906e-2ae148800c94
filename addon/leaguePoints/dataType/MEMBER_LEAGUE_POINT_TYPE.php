<?php


namespace addon\leaguePoints\dataType;

/**
 * 用户加盟积分记录类型
 * Class MEMBER_LEAGUE_POINT_TYPE
 * @package addon\leaguePoints\dataType
 */
class MEMBER_LEAGUE_POINT_TYPE extends LEAGUE_TASK_RULE_KEY
{
    /**
     * @var string 完成任务
     */
    public static $TASK_COMPLETE = "task_complete";
    /**
     * @var string 月末清零
     */
    public static $MONTH_EMPTY = "month_empty";
    
    /**
     * @var string 商品任务完成
     */
    public static $GOODS_TASK_COMPLETE = "goods_task_complete";

    /**
     * @var string 月度销售指标任务完成
     */
    public static $SALE_TASK_COMPLETE = "sale_task_complete";

    /**
     * @var string 兑换
     */
    public static $EXCHANGE = "exchange";

    /**
     * @var string 抽奖
     */
    public static $RAFFLE = "raffle";

    public static function toArray()
    {
        return array_merge(parent::toArray(), [
            self::$TASK_COMPLETE, 
            self::$MONTH_EMPTY,
            self::$GOODS_TASK_COMPLETE,
            self::$SALE_TASK_COMPLETE,
            self::$EXCHANGE,
            self::$RAFFLE,
        ]);
    }
}