<?php


namespace addon\leaguePoints\dataType;

/**
 * 用户获取加盟积分方式
 * Class LEAGUE_TASK_RULE_KEY
 * @package addon\leaguePoints\dataType
 */
class LEAGUE_TASK_RULE_KEY
{
    /**
     * @var string 直接推荐注册
     */
    public static $RECOMMEND_REGISTER = "recommend_register";
    /**
     * @var string 新增店铺粉丝
     */
    public static $ADD_SHOP_FANS = "add_shop_fans";
    /**
     * @var string 直推好友浏览数
     */
    public static $RECOMMEND_BROWSE = "recommend_browse";
    /**
     * @var string 店铺粉丝浏览
     */
    public static $SHOP_FANS_BROWSE = "shop_fans_browse";
    /**
     * @var string 直推用户升级店主
     */
    public static $RECOMMEND_SHOP = "recommend_shop";
    /**
     * @var string 其他
     */
    public static $OTHER = "other";

    public static function toArray()
    {
        return [self::$RECOMMEND_REGISTER,self::$ADD_SHOP_FANS,self::$RECOMMEND_BROWSE,self::$SHOP_FANS_BROWSE,self::$RECOMMEND_SHOP,self::$OTHER];
    }
}