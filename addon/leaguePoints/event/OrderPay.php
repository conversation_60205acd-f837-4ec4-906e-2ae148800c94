<?php


namespace addon\leaguePoints\event;


use addon\leaguePoints\domainModel\LeagueTaskPointActivityRepository;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\service\GoodsSaleTask;
use app\model\order\OrderGoodsModel;
use app\model\order\OrderModel;
use Carbon\Carbon;
use think\facade\Log;

class OrderPay
{
    public function handle($param)
    {
        if (empty($param['order_id'])) {
            return;
        }

        try
        {
            $order = OrderModel::find($param['order_id']);
            $month = Carbon::createFromTimestamp($order->create_time)->format("Y-m");
            $service = new GoodsSaleTask();

            $activityRepository = new LeagueTaskPointActivityRepository();
            $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();
            foreach ($taskKeys as $taskKey)
            {
                $activity = $activityRepository->findTaskByCid(0, $taskKey);
                if($activity->isSaleTaskEnable())
                {
                    $service->createTask($order);
                }
            }
        }
        catch (\Exception $e)
        {
            Log::error("创建月度销售任务失败");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
        }
    }


}