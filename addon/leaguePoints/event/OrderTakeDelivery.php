<?php
namespace addon\leaguePoints\event;

use addon\leaguePoints\service\MemberGoodsTask;

/**
 * 订单完成事件
 */
class OrderTakeDelivery
{
    /**
     * 事件处理方法（ThinkPHP默认处理方法名）
     *
     * @param array $param
     * @return void
     */
    public function handle($param)
    {
        if (empty($param['order_id'])) {
            return;
        }
        
        try {
            // 获取订单完整信息
            $orderModel = new \app\model\order\Order();
            $orderInfo = $orderModel->getOrderInfo([['order_id', '=', $param['order_id']]], 'order_id,member_id,order_status,order_type');
            
            if (empty($orderInfo['data'])) {
                return;
            }

            $orderInfo = $orderInfo['data'];
            
            // 获取订单商品信息
            $orderGoodsModel = model('order_goods');
            $orderGoods = $orderGoodsModel->getList([['order_id', '=', $param['order_id']]], 'order_goods_id,goods_id,sku_id,num,price');
            $orderInfo['order_goods'] = $orderGoods;

            // 处理商品任务
            $memberGoodsTaskService = new MemberGoodsTask();
            $ret = $memberGoodsTaskService->handleOrderReceived($orderInfo);
            // 记录日志而不是中断执行
            trace("处理订单完成任务结果：" . json_encode($ret), 'info');
            
        } catch (\Exception $e) {
            // 记录日志
            trace("处理订单完成任务异常：" . $e->getMessage(), 'error');
        }
    }
} 