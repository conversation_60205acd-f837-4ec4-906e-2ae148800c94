<?php

namespace addon\leaguePoints\model;

use think\Model;

class LeagueTaskPointCompleteRecord extends Model
{
    protected $table = 'xm_league_task_point_complete_record';
    protected $pk = 'id';

    protected $json = ['league_data'];

    public function getLeagueTaskNameAttr($value,$data)
    {
        $names = [
            'league_1'=>'任务一',
            'league_2'=>'任务二',
        ];
        return $names[$data['league_task_key']] ?? '';
    }

}