<?php
namespace addon\leaguePoints\model;

use addon\goodscoupon\model\GoodscouponTypeModel;
use app\model\BaseModel;
use app\model\Model;
use think\facade\Db;
use think\model\relation\BelongsTo;

/**
 * 抽奖奖项模型
 */
class LeagueRaffleRewardModel extends \think\Model
{
    protected $table = 'xm_league_raffle_reward';
    protected $pk = 'reward_id';

    public function belongGoodscouponType()
    {
        return $this->belongsTo(GoodscouponTypeModel::class, "relation_id", "goodscoupon_type_id");
    }

    /**
     * 获取奖项列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param null $limit
     * @return array
     */
    public function getRewardList($condition = [], $field = '*', $order = 'probability desc', $limit = null)
    {
        $list = model('league_raffle_reward')->getList($condition, $field, $order, '', '', '', $limit);
        return $list;
    }
    
    /**
     * 获取奖项分页列表
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @return array
     */
    public function getRewardPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'probability desc', $field = '*')
    {
        $list = model('league_raffle_reward')->pageList($condition, $field, $order, $page, $page_size);
        return $list;
    }
    
    /**
     * 获取奖项信息
     * @param array $condition
     * @param string $field
     * @return array
     */
    public function getRewardInfo($condition, $field = '*')
    {
        $info = model('league_raffle_reward')->getInfo($condition, $field);
        return $info;
    }

    /**
     * 添加奖项
     * @param $data
     * @return int|string
     */
    public function addReward($data)
    {
        $data['create_time'] = time();
        $data['update_time'] = time();
        
        $reward_id = model('league_raffle_reward')->add($data);
        return $reward_id;
    }

    /**
     * 编辑奖项
     * @param $data
     * @param $condition
     * @return int
     */
    public function editReward($data, $condition)
    {
        $data['update_time'] = time();
        $res = model('league_raffle_reward')->update($data, $condition);
        return $res;
    }

    /**
     * deleteReward
     * @param $condition
     * @return int
     */
    public function deleteReward($condition)
    {
        $data = [
            'status' => 0,
            'delete_time' => time(),
            'update_time' => time()
        ];
        $res = model('league_raffle_reward')->update($data, $condition);
        return $res;
    }

    /**
     * 更新奖项数量
     * @param $reward_id
     * @param int $num
     * @return int
     */
    public function addRewardCount($reward_id, $num = 1)
    {
        $res = model('league_raffle_reward')->setInc([['reward_id', '=', $reward_id]], 'reward_count', $num);
        return $res;
    }

    /**
     * 获取所有有效的奖项
     * @return mixed
     */
    public function getValidRewards()
    {
        $condition = [
            ['status', '=', 1],
        ];
        
        /*$rewards = LeagueRaffleRewardModel::where("status", 1)->where(function ($query){
            $query->where("reward_limit", ">", 0)->whereOr("reward_limit", -1);
        })->select()->toArray();*/
        $rewards = LeagueRaffleRewardModel::where("status", 1)->select()->toArray();
        return array_values($rewards);
    }
} 