<?php


namespace addon\leaguePoints\model;


use think\Model;

class LeagueExchangeGoodsModel extends Model
{
    protected $table = 'xm_league_exchange_goods';
    protected $pk = 'id';

    public static $STATUS_UP = 1;   //上架
    public static $STATUS_DOWN = 0; //下架

    public static $TYPE_GOODSCOUPON = 1;   //优惠券


    /**
     * 获取列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getList($condition = [], $field = '*', $order = 'id desc', $page = 1, $page_size = 10)
    {
        $data = $this->where($condition)
            ->field($field)
            ->order($order)
            ->page($page, $page_size)
            ->select()
            ->toArray();
        
        $count = $this->where($condition)->count();

        return ['list' => $data, 'count' => $count];
    }

    /**
     * 获取单条信息
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @return array
     */
    public function getInfo($condition, $field = '*')
    {
        $info = $this->where($condition)->field($field)->find();
        return $info ? $info->toArray() : [];
    }

    /**
     * 添加兑换物品
     * @param array $data 添加数据
     * @return bool|int
     */
    public function add($data)
    {
        $data['create_time'] = time();
        $data['update_time'] = time();
        return $this->save($data) ? $this->id : 0;
    }

    /**
     * 编辑兑换物品
     * @param array $data 编辑数据
     * @param array $condition 条件
     * @return bool
     */
    public function edit($data, $condition)
    {
        $data['update_time'] = time();
        return $this->where($condition)->update($data);
    }

    /**
     * 修改状态
     * @param int $status 状态（0下架 1上架）
     * @param array $condition 条件
     * @return bool
     */
    public function modifyStatus($status, $condition)
    {
        return $this->where($condition)->update(['status' => $status, 'update_time' => time()]);
    }

    /**
     * 更新兑换次数和积分
     * @param int $goods_id 兑换物品ID
     * @param int $points 本次积分
     * @return bool
     */
    public function updateExchangeCount($goods_id, $points)
    {
        return $this->where([['id', '=', $goods_id]])->inc('exchange_count')->inc('total_points', $points)->update();
    }
} 