<?php

namespace addon\leaguePoints\model;

use app\model\member\MemberModel;
use think\Model;


class LeagueTaskPointJoinMemberModel extends Model
{
    protected $table = 'xm_league_task_point_join_member';
    protected $pk = 'id';

    public function getLeagueTaskNameAttr($value,$data)
    {
        $names = [
            'league_1'=>'任务一',
            'league_2'=>'任务二',
        ];
        return $names[$data['league_task_key']] ?? '';
    }
    
    /**
     * 检查会员是否参与联盟任务
     * 
     * @param int $memberId 会员ID
     * @return bool
     */
    public function isMemberJoined($memberId)
    {
        return $this->where([
            ['member_id', '=', $memberId],
            ['enable', '=', 1], // 开放状态为1表示有效
        ])->count() > 0;
    }
    
    /**
     * 获取参与会员列表
     * 
     * @return array
     */
    public function getJoinedMemberIds()
    {
        return $this->where([
            ['enable', '=', 1]
        ])->column('member_id');
    }
}