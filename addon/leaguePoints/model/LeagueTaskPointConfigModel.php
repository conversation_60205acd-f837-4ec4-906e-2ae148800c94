<?php


namespace addon\leaguePoints\model;


use think\Model;

class LeagueTaskPointConfigModel extends Model
{
    protected $table = 'xm_league_task_point_config';
    protected $pk = 'config_id';
    protected $json = ['rules'];
    
    /**
     * 检查商品推广任务获贡献值功能是否开启
     * 
     * @param string $leagueTaskKey 加盟任务唯一标识
     * @return bool
     */
    public function isGoodsRewardPointsEnabled($leagueTaskKey = '')
    {
        $query = $this;
        
        if (!empty($leagueTaskKey)) {
            $query = $query->where('league_task_key', '=', $leagueTaskKey);
        }
        
        $config = $query->order('config_id', 'desc')->find();
        return $config && $config['goods_reward_points'] == 1;
    }
    
    /**
     * 获取配置信息
     * 
     * @param string $leagueTaskKey 加盟任务唯一标识
     * @return array
     */
    public function getConfig($leagueTaskKey = '')
    {
        $query = $this;
        
        if (!empty($leagueTaskKey)) {
            $query = $query->where('league_task_key', '=', $leagueTaskKey);
        }
        
        return $query->order('config_id', 'desc')->find() ?: [];
    }
}