<?php
namespace addon\leaguePoints\model;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 抽奖记录模型
 */
class LeagueRaffleRecordModel extends  \think\Model
{
    protected $table = 'xm_league_raffle_record';
    protected $pk = 'record_id';

    protected $type = [
        'raffle_setting' => 'array'
    ];

    /**
     * 获取抽奖记录列表
     * @param array $condition
     * @param string $field
     * @param string $order
     * @param null $limit
     * @return array
     */
    public function getRecordList($condition = [], $field = '*', $order = 'create_time desc', $limit = null)
    {
        $list = model('league_raffle_record')->getList($condition, $field, $order, '', '', '', $limit);
        return $list;
    }
    
    /**
     * 获取抽奖记录分页列表
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @return array
     */
    public function getRecordPageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = 'create_time desc', $field = '*')
    {
        $list = model('league_raffle_record')->pageList($condition, $field, $order, $page, $page_size);
        
        if (!empty($list['list'])) {
            // 获取用户信息
            $member_model = new \app\model\member\Member();
            foreach ($list['list'] as $key => $item) {
                $member_info = $member_model->getMemberInfo([['member_id', '=', $item['member_id']]], 'nickname,mobile,headimg');
                $list['list'][$key]['member_nickname'] = $member_info['data']['nickname'] ?? '';
                $list['list'][$key]['member_mobile'] = $member_info['data']['mobile'] ?? '';
                $list['list'][$key]['member_headimg'] = $member_info['data']['headimg'] ?? '';
            }
        }
        
        return $list;
    }
    
    /**
     * 获取抽奖记录信息
     * @param array $condition
     * @param string $field
     * @return array
     */
    public function getRecordInfo($condition, $field = '*')
    {
        $info = model('league_raffle_record')->getInfo($condition, $field);
        return $info;
    }

    /**
     * 添加抽奖记录
     * @param $data
     * @return mixed
     */
    public function addRecord($data)
    {
        $data['create_time'] = time();
        $record = LeagueRaffleRecordModel::create($data);
        return $record;
    }

    /**
     * 编辑抽奖记录
     * @param $data
     * @param $condition
     * @return int
     */
    public function editRecord($data, $condition)
    {
        $res = model('league_raffle_record')->update($data, $condition);
        return $res;
    }
    
    /**
     * 获取会员的抽奖统计
     * @param int $member_id
     * @return array
     */
    public function getMemberRaffleStatistics($member_id)
    {
        $condition = [
            ['member_id', '=', $member_id]
        ];
        
        // 总抽奖次数
        $raffle_count = model('league_raffle_record')->getCount($condition);
        
        // 中奖次数（不含谢谢参与）
        $win_condition = array_merge($condition, [['reward_type', '<>', 0]]);
        $win_count = model('league_raffle_record')->getCount($win_condition);
        
        // 消耗积分
        $points_sum = model('league_raffle_record')->getSum($condition, 'points');
        
        return [
            'raffle_count' => $raffle_count,
            'win_count' => $win_count,
            'points_sum' => $points_sum
        ];
    }

    /**
     * 获取奖品发放统计
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRewardStatistics()
    {
        $statistics = Db::name('league_raffle_record')
            ->field('reward_id, reward_name, reward_type, count(*) as count')
            ->group('reward_id')
            ->select()
            ->toArray();
            
        return $statistics;
    }
} 