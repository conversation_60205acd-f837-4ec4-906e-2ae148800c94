<?php


namespace addon\leaguePoints\model;


use think\Model;

class LeagueExchangeRecordModel extends Model
{
    protected $table = 'xm_league_exchange_record';
    protected $pk = 'id';

    /**
     * 获取列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getList($condition = [], $field = '*', $order = 'id desc', $page = 1, $page_size = 10)
    {
        $data = $this->where($condition)
            ->field($field)
            ->order($order)
            ->page($page, $page_size)
            ->select()
            ->toArray();
        
        $count = $this->where($condition)->count();

        return ['list' => $data, 'count' => $count];
    }

    /**
     * 获取单条信息
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @return array
     */
    public function getInfo($condition, $field = '*')
    {
        $info = $this->where($condition)->field($field)->find();
        return $info ? $info->toArray() : [];
    }

    /**
     * 添加兑换记录
     * @param array $data 添加数据
     * @return bool|int
     */
    public function add($data)
    {
        $data['create_time'] = time();
        return $this->save($data) ? $this->id : 0;
    }

    /**
     * 统计会员兑换次数
     * @param int $member_id 会员ID
     * @param int $goods_id 兑换物品ID
     * @return int
     */
    public function getMemberExchangeCount($member_id, $goods_id = 0)
    {
        $condition = [['member_id', '=', $member_id]];
        if ($goods_id > 0) {
            $condition[] = ['goods_id', '=', $goods_id];
        }
        return $this->where($condition)->count();
    }
} 