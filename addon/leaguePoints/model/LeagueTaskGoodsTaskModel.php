<?php

namespace addon\leaguePoints\model;

use think\Model;
use app\model\goods\Goods as GoodsModel;
use think\facade\Db;

class LeagueTaskGoodsTaskModel extends Model
{
    protected $table = 'xm_league_task_goods_task';
    protected $pk = 'goods_task_id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 追加字段，方便前端使用
    protected $append = ['allow_edit', 'allow_delete', 'purchase_count', 'task_submit_count', 'total_reward'];
    
    // 获取商品信息
    public function getGoodsInfoAttr($value, $data)
    {
        if (isset($data['goods_id']) && !empty($data['goods_id'])) {
            $goods_model = new GoodsModel();
            $goods_info = $goods_model->getGoodsInfo([['goods_id', '=', $data['goods_id']]], 'goods_id, goods_name');
            return $goods_info['data'] ?? [];
        }
        return [];
    }
    
    // 获取商品名称
    public function getGoodsNameAttr($value, $data)
    {
        if (isset($data['goods_id']) && !empty($data['goods_id'])) {
            $goods_model = new GoodsModel();
            $goods_info = $goods_model->getGoodsInfo([['goods_id', '=', $data['goods_id']]], 'goods_name');
            return $goods_info['data']['goods_name'] ?? '';
        }
        return '';
    }
    
    // 获取器：允许编辑
    public function getAllowEditAttr()
    {
        return true;
    }
    
    // 获取器：允许删除
    public function getAllowDeleteAttr()
    {
        return true;
    }
    
    // 获取器：购买人数
    public function getPurchaseCountAttr($value, $data)
    {
        if (!isset($data['goods_id']) || empty($data['goods_id'])) {
            return 0;
        }
        
        // 统计关联此商品的唯一会员数量
        return Db::name('league_task_member_goods_task')
            ->where([
                ['goods_id', '=', $data['goods_id']],
                ['league_task_key', '=', $data['league_task_key'] ?? 'league_1']
            ])
            ->group('member_id')
            ->count();
    }
    
    // 获取器：提交任务数
    public function getTaskSubmitCountAttr($value, $data)
    {
        if (!isset($data['goods_id']) || empty($data['goods_id'])) {
            return 0;
        }
        
        // 统计已提交审核的任务数量
        return Db::name('league_task_member_goods_task')
            ->where([
                ['goods_id', '=', $data['goods_id']],
                ['league_task_key', '=', $data['league_task_key'] ?? 'league_1'],
                ['status', 'in', [10, 100]] // 10=待审核, 100=已通过
            ])
            ->count();
    }
    
    // 获取器：累计发放贡献值
    public function getTotalRewardAttr($value, $data)
    {
        if (!isset($data['goods_id']) || empty($data['goods_id'])) {
            return 0;
        }
        
        // 统计已通过审核的任务累计奖励积分
        return Db::name('league_task_member_goods_task')
            ->where([
                ['goods_id', '=', $data['goods_id']],
                ['league_task_key', '=', $data['league_task_key'] ?? 'league_1'],
                ['status', '=', 100] // 100=已通过
            ])
            ->sum('reward_points');
    }
    
    /**
     * 获取商品任务信息
     * 
     * @param int $goodsId 商品ID
     * @param string $leagueTaskKey 任务Key
     * @return array|null
     */
    public function getGoodsTask($goodsId, $leagueTaskKey = 'league_1')
    {
        return $this->where([
            ['goods_id', '=', $goodsId],
            ['league_task_key', '=', $leagueTaskKey]
        ])->find();
    }
    
    /**
     * 检查商品是否为任务商品
     * 
     * @param int $goodsId 商品ID
     * @return bool
     */
    public function isTaskGoods($goodsId)
    {
        return $this->where('goods_id', '=', $goodsId)->count() > 0;
    }
    
    /**
     * 获取商品任务列表
     * 
     * @param array $goodsIds 商品ID数组
     * @return array
     */
    public function getTasksByGoodsIds(array $goodsIds)
    {
        if (empty($goodsIds)) {
            return [];
        }
        
        return $this->where('goods_id', 'in', $goodsIds)
            ->select()
            ->toArray();
    }
} 