<?php

namespace addon\leaguePoints\model;

use think\Model;
use app\model\goods\Goods as GoodsModel;
use think\facade\Db;

class LeagueTaskSaleTaskGoodsModel extends Model
{
    protected $table = 'xm_league_task_sale_task_goods';
    protected $pk = 'sale_task_goods_id';
    
    // 状态常量
    const STATUS_DISABLED = 0; // 停用
    const STATUS_ENABLED = 1; // 启用
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 追加字段，方便前端使用
    protected $append = ['allow_edit', 'allow_delete', 'total_people', 'total_reward', 'status_text'];
    
    // 获取商品信息
    public function getGoodsInfoAttr($value, $data)
    {
        if (isset($data['goods_id']) && !empty($data['goods_id'])) {
            $goods_model = new GoodsModel();
            $goods_info = $goods_model->getGoodsInfo([['goods_id', '=', $data['goods_id']]], 'goods_id, goods_name');
            return $goods_info['data'] ?? [];
        }
        return [];
    }
    
    // 获取商品名称
    public function getGoodsNameAttr($value, $data)
    {
        if (isset($data['goods_id']) && !empty($data['goods_id'])) {
            $goods_model = new GoodsModel();
            $goods_info = $goods_model->getGoodsInfo([['goods_id', '=', $data['goods_id']]], 'goods_name');
            return $goods_info['data']['goods_name'] ?? '';
        }
        return '';
    }
    
    // 获取器：允许编辑
    public function getAllowEditAttr()
    {
        return true;
    }
    
    // 获取器：允许删除
    public function getAllowDeleteAttr()
    {
        return true;
    }
    
    // 获取器：达标人次（已完成且奖励已发放的member数）
    public function getTotalPeopleAttr($value, $data)
    {
        if (!isset($data['goods_id']) || empty($data['goods_id']) || !isset($data['league_task_key'])) {
            return 0;
        }
        // 统计xm_league_task_member_sale_task表，goods_id+league_task_key，status=100，reward_status=1
        return Db::name('league_task_member_sale_task')
            ->where([
                ['goods_id', '=', $data['goods_id']],
                ['league_task_key', '=', $data['league_task_key']],
                ['status', '=', 100],
                ['reward_status', '=', 1]
            ])->count();
    }
    
    // 获取器：累计发放贡献值（sum(reward_points)，条件同上）
    public function getTotalRewardAttr($value, $data)
    {
        if (!isset($data['goods_id']) || empty($data['goods_id']) || !isset($data['league_task_key'])) {
            return 0;
        }
        return Db::name('league_task_member_sale_task')
            ->where([
                ['goods_id', '=', $data['goods_id']],
                ['league_task_key', '=', $data['league_task_key']],
                ['status', '=', 100],
                ['reward_status', '=', 1]
            ])->sum('reward_points');
    }

    // 获取器：状态文本
    public function getStatusTextAttr($value, $data)
    {
        if (!isset($data['status'])) {
            return '未知';
        }
        
        // 如果是启用状态
        if ($data['status'] == self::STATUS_ENABLED) {
            return '已启用';
        }
        
        // 如果是停用状态且有停用时间
        if ($data['status'] == self::STATUS_DISABLED && isset($data['disable_start_time']) && $data['disable_start_time'] > 0) {
            $date = date('Y年n月', $data['disable_start_time']);
            return $date . '起停用';
        }
        
        return '已停用';
    }
    
    /**
     * 获取商品销售任务信息
     * 
     * @param int $goodsId 商品ID
     * @param string $leagueTaskKey 任务Key
     * @return array|null
     */
    public function getSaleTaskGoods($goodsId, $leagueTaskKey = 'league_1')
    {
        return $this->where([
            ['goods_id', '=', $goodsId],
            ['league_task_key', '=', $leagueTaskKey]
        ])->find();
    }
    
    /**
     * 检查商品是否为销售任务商品
     * 
     * @param int $goodsId 商品ID
     * @return bool
     */
    public function isSaleTaskGoods($goodsId)
    {
        return $this->where('goods_id', '=', $goodsId)->count() > 0;
    }
    
    /**
     * 获取商品销售任务列表
     * 
     * @param array $goodsIds 商品ID数组
     * @return array
     */
    public function getTasksByGoodsIds(array $goodsIds)
    {
        if (empty($goodsIds)) {
            return [];
        }
        
        return $this->where('goods_id', 'in', $goodsIds)
            ->select()
            ->toArray();
    }
    
    /**
     * 启用任务
     * 
     * @param int $saleTaskGoodsId 任务ID
     * @return boolean|array
     */
    public function enableTask($saleTaskGoodsId)
    {
        $task = $this->where('sale_task_goods_id', '=', $saleTaskGoodsId)->find();
        if (empty($task)) {
            return ['code' => -1, 'message' => '任务不存在'];
        }
        
        $result = $this->where('sale_task_goods_id', '=', $saleTaskGoodsId)->update([
            'status' => self::STATUS_ENABLED,
            'disable_start_time' => 0
        ]);
        
        return $result ? ['code' => 0, 'message' => '启用成功'] : ['code' => -1, 'message' => '启用失败'];
    }
    
    /**
     * 停用任务
     * 
     * @param int $saleTaskGoodsId 任务ID
     * @return boolean|array
     */
    public function disableTask($saleTaskGoodsId)
    {
        $task = $this->where('sale_task_goods_id', '=', $saleTaskGoodsId)->find();
        if (empty($task)) {
            return ['code' => -1, 'message' => '任务不存在'];
        }
        
        // 计算下个月1号0点的时间戳
        $nextMonth = strtotime('first day of next month');
        
        $result = $this->where('sale_task_goods_id', '=', $saleTaskGoodsId)->update([
            'status' => self::STATUS_DISABLED,
            'disable_start_time' => $nextMonth
        ]);
        
        return $result ? ['code' => 0, 'message' => '停用成功'] : ['code' => -1, 'message' => '停用失败'];
    }
} 