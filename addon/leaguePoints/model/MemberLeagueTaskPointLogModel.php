<?php

namespace addon\leaguePoints\model;

use think\Model;

class MemberLeagueTaskPointLogModel extends Model
{
    protected $table = 'xm_member_league_task_point_log';
    protected $pk = 'id';

    public function getPointTypeTextAttr($value,$data)
    {
        $has = in_array($data['point_type'], \addon\leaguePoints\dataType\MEMBER_LEAGUE_POINT_TYPE::toArray());
        if($has){
            return lang('points_record.'.$data['point_type']);
        }else{
            return $data['point_type'];
        }
    }
}