<?php


namespace addon\leaguePoints\model;


use think\Model;

class LeagueTaskMemberSaleTaskModel extends Model
{
    protected $table = 'xm_league_task_member_sale_task';
    protected $pk = 'member_sale_task_id';

    protected $type = [
        'complete_order_ids' => 'array'
    ];

    public function belongTaskGoods()
    {
        return $this->belongsTo(LeagueTaskSaleTaskGoodsModel::class, "goods_id", "goods_id");
    }
}