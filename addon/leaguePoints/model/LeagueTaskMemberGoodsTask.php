<?php
namespace addon\leaguePoints\model;

use Carbon\Carbon;
use think\Model;

/**
 * 会员商品任务记录Model
 */
class LeagueTaskMemberGoodsTask extends Model
{
    protected $table = 'xm_league_task_member_goods_task';
    protected $pk = 'member_goods_task_id';

    protected $autoWriteTimestamp = true;

    // 状态常量
    const STATUS_PROCESSING = 0;    // 进行中
    const STATUS_PENDING = 10;      // 待审核
    const STATUS_APPROVED = 100;    // 已通过
    const STATUS_REJECTED = -1;     // 已驳回
    
    /**
     * 创建会员商品任务记录
     * 
     * @param int $memberId 会员ID
     * @param int $goodsId 商品ID
     * @param int $orderId 订单ID
     * @param int $goodsTaskId 商品任务ID
     * @param string $leagueTaskKey 任务Key
     * @return array
     */
    public function createMemberGoodsTask($memberId, $goodsId, $orderId, $goodsTaskId, $rewardPoints, $leagueTaskKey = 'league_1')
    {
        try {
            // 检查是否已存在相同记录
            $exists = $this->where([
                ['member_id', '=', $memberId],
                ['goods_id', '=', $goodsId],
                ['order_id', '=', $orderId]
            ])->find();
            
            if ($exists) {
                return ['code' => -1, 'message' => '该订单已存在任务记录'];
            }
            
            $data = [
                'member_id' => $memberId,
                'goods_id' => $goodsId,
                'order_id' => $orderId,
                'goods_task_id' => $goodsTaskId,
                'league_task_key' => $leagueTaskKey,
                'status' => self::STATUS_PROCESSING,
                'create_time' => Carbon::now()->toDateTimeString(),
                'reward_points' => $rewardPoints
            ];
            
            $result = $this->create($data);

            if ($result) {
                return ['code' => 0, 'message' => '创建任务成功', 'data' => $result['member_goods_task_id']];
            } else {
                return ['code' => -1, 'message' => '创建任务失败'];
            }
        } catch (\Exception $e) {
            return ['code' => -1, 'message' => '创建任务异常: ' . $e->getMessage()];
        }
    }
    
    /**
     * 获取会员指定商品的任务
     * 
     * @param int $memberId 会员ID
     * @param int $goodsId 商品ID
     * @return array
     */
    public function getMemberGoodsTasks($memberId, $goodsId)
    {
        return $this->where([
            ['member_id', '=', $memberId],
            ['goods_id', '=', $goodsId]
        ])->select()->toArray();
    }
} 