<?php
namespace addon\leaguePoints\service;

use addon\leaguePoints\model\LeagueTaskPointConfigModel;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use addon\leaguePoints\model\LeagueTaskGoodsTaskModel;
use addon\leaguePoints\model\LeagueTaskMemberGoodsTask;
use addon\leaguePoints\domainModel\GoodsTaskPointReward;
use addon\leaguePoints\facade\MemberLeagueTaskPointRepository;
use app\model\member\MemberModel;
use think\facade\Db;
use think\facade\Log;
use addon\leaguePoints\service\SendLeaguePointService;

/**
 * 会员商品任务服务类
 */
class MemberGoodsTask
{
    /**
     * 处理订单确认收货后的任务创建
     * 
     * @param array $orderInfo 订单信息
     * @return array
     */
    public function handleOrderReceived($orderInfo)
    {
        // 初始化返回结果
        $result = ['code' => 0, 'message' => '处理成功', 'data' => []];
        
        try {
            // 获取订单商品
            $orderGoods = $orderInfo['order_goods'] ?? [];
            if (empty($orderGoods)) {
                return ['code' => -1, 'message' => '订单商品为空'];
            }

            // 提取商品ID
            $goodsIds = array_column($orderGoods, 'goods_id');

            // 获取任务商品
            $goodsTaskModel = new LeagueTaskGoodsTaskModel();
            $taskGoods = $goodsTaskModel->getTasksByGoodsIds($goodsIds);

            if (empty($taskGoods)) {
                return ['code' => -1, 'message' => '订单中无任务商品'];
            }
            
            // 检查会员是否参与任务
            $memberId = $orderInfo['member_id'] ?? 0;
            $memberModel = new LeagueTaskPointJoinMemberModel();
            if (!$memberModel->isMemberJoined($memberId)) {
                return ['code' => -1, 'message' => '会员未参与任务'];
            }
            
            // 映射商品ID到任务
            $goodsTaskMap = [];
            $configModel = new LeagueTaskPointConfigModel();
            foreach ($taskGoods as $task) {
                // 检查每个商品对应的league_task_key的配置是否开启
                if (!$configModel->isGoodsRewardPointsEnabled($task['league_task_key'])) {
                    continue; // 如果该任务未开启，则跳过
                }
                $goodsTaskMap[$task['goods_id']] = $task;
            }
            
            if (empty($goodsTaskMap)) {
                return ['code' => -1, 'message' => '订单中无可用任务商品'];
            }
            
            // 创建会员商品任务
            $taskModel = new LeagueTaskMemberGoodsTask();
            $createdTasks = [];
            
            foreach ($orderGoods as $goods) {
                $goodsId = $goods['goods_id'];
                
                // 检查是否为任务商品
                if (!isset($goodsTaskMap[$goodsId])) {
                    continue;
                }
                
                $taskInfo = $goodsTaskMap[$goodsId];
                $taskResult = $taskModel->createMemberGoodsTask(
                    $memberId,
                    $goodsId,
                    $orderInfo['order_id'],
                    $taskInfo['goods_task_id'],
                    $taskInfo['reward_points'],
                    $taskInfo['league_task_key'] ?? 'league_1'
                );
                
                if ($taskResult['code'] == 0) {
                    $createdTasks[] = $taskResult['data'];
                }
            }
            
            $result['data'] = $createdTasks;
            $result['message'] = count($createdTasks) > 0 ? '成功创建' . count($createdTasks) . '个任务' : '没有创建任务';
            
        } catch (\Exception $e) {
            $result = ['code' => -1, 'message' => '处理异常：' . $e->getMessage().$e->getTraceAsString()];
        }
        
        return $result;
    }

    /**
     * 用户提交商品推广任务
     * @param int $member_goods_task_id
     * @param int $member_id
     * @param array $images
     * @return array
     */
    public function submitTask($member_goods_task_id, $member_id, $images, $remark='')
    {
        if (empty($member_goods_task_id) || empty($images) || !is_array($images)) {
            return ['code' => -1, 'message' => '参数错误'];
        }
        $taskModel = new \addon\leaguePoints\model\LeagueTaskMemberGoodsTask();
        $task = $taskModel->where([
            ['member_goods_task_id', '=', $member_goods_task_id],
            ['member_id', '=', $member_id]
        ])->find();
        if (!$task) {
            return ['code' => -1, 'message' => '任务不存在'];
        }
        if (!in_array($task['status'], [
            \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::STATUS_PROCESSING,
            \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::STATUS_REJECTED
        ])) {
            return ['code' => -1, 'message' => '当前状态不可提交'];
        }
        $configModel = new \addon\leaguePoints\model\LeagueTaskPointConfigModel();
        if (!$configModel->isGoodsRewardPointsEnabled($task['league_task_key'])) {
            return ['code' => -1, 'message' => '该任务不支持商品推广积分'];
        }
        $images_str = implode(',', $images);
        $task->images = $images_str;
        $task->status = \addon\leaguePoints\model\LeagueTaskMemberGoodsTask::STATUS_PENDING;
        $task->remark = $remark;
        $task->submit_time = date('Y-m-d H:i:s');
        $task->update_time = date('Y-m-d H:i:s');
        if ($task->save() === false) {
            return ['code' => -1, 'message' => '提交失败，请重试'];
        }
        return ['code' => 0, 'message' => '提交成功，待审核'];
    }
    
    /**
     * 处理任务审核通过后的积分发放
     * 
     * @param int $member_goods_task_id 会员商品任务ID
     * @return array 处理结果
     */
    public function handleTaskApproved($member_goods_task_id)
    {

        try {
            // 查找任务信息
            $taskModel = new LeagueTaskMemberGoodsTask();
            $task = $taskModel->where('member_goods_task_id', '=', $member_goods_task_id)->find();
            // 调用积分发放服务
            $success = SendLeaguePointService::sendGoodsTaskComplete($task, $fail_reason);
            if (!$success) {
                $task->reward_status = -1;
                $task->fail_reason = $fail_reason;
            }
            else
                $task->reward_status = 100;
            $task->save();

        } catch (\Exception $e) {
            Log::error("商品推广任务贡献值发放失败");
            Log::error( $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        }
    }
} 