<?php


namespace addon\leaguePoints\service;


use addon\leaguePoints\domainModel\AddShopFansPointReward;
use addon\leaguePoints\domainModel\GoodsTaskPointReward;
use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\domainModel\OtherPointReward;
use addon\leaguePoints\domainModel\PointReward;
use addon\leaguePoints\domainModel\RecommendBrowsePointReward;
use addon\leaguePoints\domainModel\RecommendRegisterPointReward;
use addon\leaguePoints\domainModel\RecommendShopReward;
use addon\leaguePoints\domainModel\SaleTaskPointReward;
use addon\leaguePoints\domainModel\ShopFansBrowsePointReward;
use addon\leaguePoints\facade\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\LeagueTaskMemberGoodsTask;
use addon\leaguePoints\model\LeagueTaskMemberSaleTaskModel;
use app\model\goods\GoodsBrowseModelLog;
use app\model\member\MemberModel;
use app\model\member\MemberRecommendModel;
use app\model\shop\ShopMemberModel;
use app\model\shop\ShopModel;
use app\model\user\SyncUsersModel;
use think\facade\Db;
use think\facade\Log;

class SendLeaguePointService
{

    protected static function sendAllLeagueTask(MemberModel $receiveMember, PointReward $pointReward)
    {
        //获取正在进行的所有加盟任务
        $success = 0;
        $failReason = [];
        $taskKeys = LeagueTaskPointConfig::getAllLeagueTaskKey();

        foreach ($taskKeys as $taskKey)
        {
            if($memberTask = MemberLeagueTaskPointRepository::findTask($receiveMember->member_id, $taskKey))
            {
                $ret = $memberTask->sendReward($pointReward, $failReason);
                if($ret)
                {
                    $success ++;
                }
            }
        }
        return ['success'=>$success, 'fail_reason'=>$failReason];
    }

    /**
     * 发放直推拉新奖励
     * @param $memberId
     * @param $childMemberId
     */
    public static function sendRecommendRegister($memberId, $childMemberId)
    {
        try
        {
            Db::startTrans();
            $pointReward = new RecommendRegisterPointReward($memberId, $childMemberId);
            $receiveMember = $pointReward->receiveMember();
            if($receiveMember)
                self::sendAllLeagueTask($receiveMember, $pointReward);
            Db::commit();
        }
        catch (\Exception $e)
        {
            Log::error("邀请新好友注册积分奖励发放失败");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
        }
    }

    public static function sendAddShopFans(ShopMemberModel $shopMember)
    {
        try
        {
            Db::startTrans();
            $pointReward = new AddShopFansPointReward($shopMember);
            $receiveMember = $pointReward->receiveMember();
            if($receiveMember)
                self::sendAllLeagueTask($receiveMember, $pointReward);
            Db::commit();
        }
        catch (\Exception $e)
        {
            Log::error("店铺绑定粉丝增长加盟积分奖励发放异常");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
        }
    }

    public static function sendRecommendBrowse(GoodsBrowseModelLog $goodsBrowseLog)
    {
        try
        {
            Db::startTrans();
            $pointReward = new RecommendBrowsePointReward($goodsBrowseLog);
            $receiveMember = $pointReward->receiveMember();
            if($receiveMember)
                self::sendAllLeagueTask($receiveMember, $pointReward);
            Db::commit();
        }
        catch (\Exception $e)
        {
            Log::error("好友商品浏览数增长加盟积分奖励发放失败");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
        }
    }

    public static function sendShopFansBrowse(GoodsBrowseModelLog $goodsBrowseLog)
    {
        try
        {
            Db::startTrans();

            $pointReward = new ShopFansBrowsePointReward($goodsBrowseLog);
            $receiveMember = $pointReward->receiveMember();
            if($receiveMember)
                self::sendAllLeagueTask($receiveMember, $pointReward);
            Db::commit();
        }
        catch (\Exception $e)
        {
            Log::error("粉丝商品浏览数增长加盟积分奖励发放失败");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
        }
    }

    public static function sendRecommendShop(ShopModel $shop, $xmUid, $leagueId)
    {
        try
        {
            //Db::startTrans();d

            if($xmUid)
            {
                $pointReward = new RecommendShopReward($shop, $xmUid, $leagueId);
                $receiveMember = $pointReward->receiveMember();

                if($receiveMember)
                    self::sendAllLeagueTask($receiveMember, $pointReward);
            }
            //Db::commit();
        }
        catch (\Exception $e)
        {

            Log::error("好友升级店主加盟积分奖励发放失败");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            //Db::rollback();
        }
    }
    
    /**
     * 发放商品任务完成积分奖励
     *
     * @param LeagueTaskMemberGoodsTask $memberGoodsTask 会员商品任务
     * @return bool 是否发放成功
     */
    public static function sendGoodsTaskComplete(LeagueTaskMemberGoodsTask $memberGoodsTask, &$failReason = '')
    {
        try
        {
            Db::startTrans();
            $pointReward = new GoodsTaskPointReward($memberGoodsTask);
            $receiveMember = $pointReward->receiveMember();

            $memberTask = MemberLeagueTaskPointRepository::findTask($receiveMember->member_id, $memberGoodsTask->league_task_key);
            $ret = $memberTask->sendGoodsTaskPointReward($pointReward, $failReason);

            Db::commit();
            return $ret;
        }
        catch (\Exception $e)
        {
            Log::error("商品任务完成积分奖励发放异常，任务ID: " . $memberGoodsTask->member_goods_task_id);
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
            return false;
        }
    }

    public static function sendSaleTaskComplete(LeagueTaskMemberSaleTaskModel $memberSaleTask, &$failReason='')
    {
        try
        {
            Db::startTrans();
            $pointReward = new SaleTaskPointReward($memberSaleTask);
            $receiveMember = $pointReward->receiveMember();

            $memberTask = MemberLeagueTaskPointRepository::findTask($receiveMember->member_id, $memberSaleTask->league_task_key);
            $ret = $memberTask->sendSaleTaskPointReward($pointReward, $failReason);
            Db::commit();
            return $ret;
        }
        catch (\Exception $e)
        {
            Log::error("月度销售任务完成积分奖励发放失败，任务ID: " . $memberSaleTask->member_sale_task_id);
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
            return false;
        }
    }


    /**
     * 发放其他类型奖励
     * @param MemberModel $member 发放用户
     * @param float $amount 发放积分
     * @param string $remark 备注
     * @return bool
     */
    public static function sendOther(MemberModel $member, float $amount, $adminUser, $remark='')
    {
        try
        {
            Db::startTrans();
            $pointReward = new OtherPointReward($member, $amount, $adminUser, $remark);
            $receiveMember = $pointReward->receiveMember();
            self::sendAllLeagueTask($receiveMember, $pointReward);
            Db::commit();
            return true;
        }
        catch (\Exception $e)
        {
            Log::error("其他类型积分奖励发放失败: ");
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            Db::rollback();
            return false;
        }
    }
}
