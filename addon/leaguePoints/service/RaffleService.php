<?php
namespace addon\leaguePoints\service;

use addon\leaguePoints\facade\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\LeagueRaffleRewardModel;
use addon\leaguePoints\model\LeagueRaffleRecordModel;
use app\model\system\Config;
use app\service\BaseService;
use Carbon\Carbon;
use think\facade\Cache;
use think\facade\Db;

/**
 * 抽奖业务逻辑
 */
class RaffleService extends BaseService
{
    /**
     * 获取抽奖活动配置
     * @return array
     */
    public function getRaffleConfig()
    {
        $config_model = new Config();
        $config = $config_model->getConfig([
            ['site_id', '=', 0],
            ['app_module', '=', 'admin'],
            ['config_key', '=', 'leaguepoints_raffle_config']
        ]);

        $result['points'] = intval($config['data']['value']['points']);
        
        // 活动状态
        $result['status'] = isset($config['data']['is_use']) ? intval($config['data']['is_use']) : 0;

        // 获取奖项列表
        $reward_model = new LeagueRaffleRewardModel();
        $rewards = $reward_model->getRewardList([['status', '=', 1]], '*', 'reward_id asc');
        $result['rewards'] = $rewards ?? [];
        
        return $this->success($result);
    }

    /**
     * 获取抽奖所需贡献值
     * @return int
     */
    public function getRafflePoints()
    {
        $config_model = new Config();
        $config = $config_model->getConfig([
            ['site_id', '=', 0],
            ['app_module', '=', 'admin'],
            ['config_key', '=', 'leaguepoints_raffle_config']
        ]);

        $result['points'] = intval($config['data']['value']['points']);
        return $result['points'];
    }

    /**
     * 获取抽奖所需贡献值
     * @return int
     */
    public function isEnable()
    {
        $config_model = new Config();
        $config = $config_model->getConfig([
            ['site_id', '=', 0],
            ['app_module', '=', 'admin'],
            ['config_key', '=', 'leaguepoints_raffle_config']
        ]);

        return isset($config['data']['is_use']) ? intval($config['data']['is_use']) : 0;
    }

    /**
     * 保存抽奖活动配置
     * @param array $data
     * @return array
     */
    public function saveRaffleConfig($data)
    {
        $config_model = new Config();
        
        $points = isset($data['points']) ? intval($data['points']) : 20;
        $status = isset($data['status']) ? intval($data['status']) : 1;
        
        // 保存积分配置到value字段
        $config_value = [
            'points' => $points
        ];
        
        // 参考中金支付Config.php，setConfig只用4个参数
        return $config_model->setConfig($config_value, '抽奖活动基础配置', $status, [
            [ 'site_id', '=', 0 ],
            ['app_module', '=', 'admin'],
            ['config_key', '=', 'leaguepoints_raffle_config']
        ]);
    }
    
    /**
     * 添加奖项
     * @param array $data
     * @return array
     */
    public function addReward($data)
    {
        $reward_model = new LeagueRaffleRewardModel();
        
        // 检查数据
        if (empty($data['reward_name'])) {
            return $this->error('', '奖项名称不能为空');
        }
        
        if (!isset($data['probability'])) {
            $data['probability'] = 0; // 默认概率为0
        }
        
        if (!isset($data['reward_limit'])) {
            $data['reward_limit'] = 0; // 默认数量上限为0
        }
        
        // 检查总概率是否超过100%
        $total_probability = $this->getTotalProbability();
        if ($total_probability + $data['probability'] > 100) {
            // 如果超过100%，自动将新加项的概率调整为剩余可用概率
            $data['probability'] = max(0, 100 - $total_probability);
        }
        
        // 添加奖项
        $res = $reward_model->addReward($data);
        if (!$res) {
            return $this->error('', '添加奖项失败');
        }
        
        // 调整谢谢参与奖项的概率
        $this->adjustThanksRewardProbability();
        
        return $this->success($res, '添加奖项成功');
    }
    
    /**
     * 编辑奖项
     * @param array $data
     * @param int $reward_id
     * @return array
     */
    public function editReward($data, $reward_id)
    {
        $reward_model = new LeagueRaffleRewardModel();
        
        // 获取原奖项信息
        $reward_info = $reward_model->getRewardInfo([['reward_id', '=', $reward_id]]);
        if (empty($reward_info)) {
            return $this->error('', '奖项不存在');
        }
        
        // 谢谢参与奖项不可修改类型
        if ($reward_info['reward_type'] == 0 && isset($data['reward_type']) && $data['reward_type'] != 0) {
            return $this->error('', '谢谢参与奖项类型不可修改');
        }

        // 是否更新概率
        $is_update_probability = isset($data['probability']) && $data['probability'] != $reward_info['probability'];
        
        // 非谢谢参与奖项且更新概率时，检查总概率
        if ($reward_info['reward_type'] != 0 && $is_update_probability) {
            // 获取所有非谢谢参与奖项（不包括当前编辑的奖项）
            $other_rewards = $reward_model->getRewardList([
                ['reward_type', '<>', 0], 
                ['status', '=', 1],
                ['reward_id', '<>', $reward_id]
            ]);
            
            // 计算其他奖项概率总和
            $other_total_probability = 0;
            foreach ($other_rewards as $reward) {
                $other_total_probability += $reward['probability'];
            }
            
            // 检查：当前修改的概率 + 其他奖项概率总和 是否超过100%
            if ($other_total_probability + floatval($data['probability']) > 100) {
                return $this->error('', '所有奖项概率总和不能超过100%');
            }
        }

        // 执行编辑
        $data['update_time'] = time();
        $res = $reward_model->editReward($data, [['reward_id', '=', $reward_id]]);
        
        if (!$res) {
            return $this->error('', '编辑奖项失败');
        }
        
        // 更新谢谢参与奖项概率（100% - 所有非谢谢参与奖项概率总和）
        $this->adjustThanksRewardProbability();

        return $this->success($res, '编辑奖项成功');
    }
    
    /**
     * 删除奖项
     * @param int $reward_id
     * @return array
     */
    public function deleteReward($reward_id)
    {
        $reward_model = new LeagueRaffleRewardModel();
        
        // 获取奖项信息
        $reward_info = $reward_model->getRewardInfo([['reward_id', '=', $reward_id]]);
        if (empty($reward_info)) {
            return $this->error('', '奖项不存在');
        }
        
        // 谢谢参与奖项不允许删除
        if ($reward_info['reward_type'] == 0) {
            return $this->error('', '谢谢参与奖项不允许删除');
        }
        
        // 删除奖项
        $res = $reward_model->deleteReward([['reward_id', '=', $reward_id]]);
        
        // 调整谢谢参与奖项的概率
        $this->adjustThanksRewardProbability();

        return $this->success($res, '删除奖项成功');
    }
    
    /**
     * 调整谢谢参与奖项的概率
     */
    private function adjustThanksRewardProbability()
    {
        $reward_model = new LeagueRaffleRewardModel();
        
        // 获取谢谢参与奖项
        $thanks_reward = $reward_model->getRewardInfo([
            ['reward_type', '=', 0], 
            ['status', '=', 1]
        ]);
        
        if (empty($thanks_reward)) {
            return $this->error('', '谢谢参与奖项不存在');
        }
        
        // 获取所有非谢谢参与奖项的总概率
        $non_thanks_probability = $this->getTotalProbability($thanks_reward['reward_id'], true);
        
        // 计算谢谢参与概率 - 确保总和为100%
        $thanks_probability = 100 - $non_thanks_probability;
        if ($thanks_probability < 0) {
            // 如果非谢谢参与奖项总概率已经超过100%，需要调整其他奖项
            $this->adjustOtherRewardsProbability($non_thanks_probability);
            $thanks_probability = 0;
        }
        
        // 更新谢谢参与的概率
        $reward_model->editReward([
            'probability' => $thanks_probability
        ], [['reward_id', '=', $thanks_reward['reward_id']]]);
        
        return $this->success();
    }
    
    /**
     * 按比例调整其他奖项的概率，确保总和不超过100%
     * @param float $total_probability 当前总概率
     * @return array
     */
    private function adjustOtherRewardsProbability($total_probability)
    {
        if ($total_probability <= 100) {
            return $this->success(); // 无需调整
        }
        
        $reward_model = new LeagueRaffleRewardModel();
        
        // 获取谢谢参与以外的所有奖项
        $rewards = $reward_model->getRewardList([
            ['reward_type', '<>', 0],
            ['status', '=', 1]
        ]);
        
        if (empty($rewards)) {
            return $this->error('', '没有可调整的奖项');
        }
        
        // 计算调整比例
        $ratio = 100 / $total_probability;
        
        // 按比例调整每个奖项的概率
        foreach ($rewards as $reward) {
            $new_probability = round($reward['probability'] * $ratio, 2);
            $reward_model->editReward([
                'probability' => $new_probability
            ], [['reward_id', '=', $reward['reward_id']]]);
        }
        
        return $this->success();
    }
    
    /**
     * 获取奖项总概率（不包括指定奖项）
     * @param int $exclude_reward_id 排除的奖项ID
     * @param bool $exclude_thanks 是否排除谢谢参与奖项，默认不排除
     * @return float
     */
    private function getTotalProbability($exclude_reward_id = 0, $exclude_thanks = false)
    {
        $condition = [['status', '=', 1]];
        if ($exclude_reward_id > 0) {
            $condition[] = ['reward_id', '<>', $exclude_reward_id];
        }
        
        // 如果需要排除谢谢参与奖项
        if ($exclude_thanks) {
            $condition[] = ['reward_type', '<>', 0];
        }
        
        $reward_model = new LeagueRaffleRewardModel();
        $reward_list = $reward_model->getRewardList($condition, 'reward_id, probability');
        
        $total_probability = 0;
        if (!empty($reward_list)) {
            foreach ($reward_list as $reward) {
                $total_probability += $reward['probability'];
            }
        }
        
        return $total_probability;
    }
    
    /**
     * 执行抽奖
     * @param int $member_id 会员ID
     * @return array
     */
    public function doRaffle($member_id, $mobile, $league_task_key = 'league_1')
    {
        // 获取配置
        $config = $this->getRaffleConfig();
        if (!$config['data']) {
            return $this->error('', '抽奖活动未配置');
        }
        
        $points = intval($config['data']['points']);

        $memberTask = MemberLeagueTaskPointRepository::findTask($member_id, $league_task_key);
        // 检查用户积分
        if ($memberTask->getPoint() < $points) {
            return $this->error('', '贡献值不足，无法参与抽奖');
        }
        
        // 获取有效奖项
        $reward_model = new LeagueRaffleRewardModel();
        $valid_rewards = $reward_model->getValidRewards();
        if (empty($valid_rewards)) {
            return $this->error('', '暂无可用奖项');
        }

        // 根据概率随机抽取奖项
        $result = $this->randomReward($valid_rewards, $roll);

        if (empty($result)) {
            return $this->error('', '未能抽取奖项');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 记录抽奖
            $record_model = new LeagueRaffleRecordModel();
            $record_data = [
                'member_id' => $member_id,
                'points' => $points,
                'reward_id' => $result['reward_id'],
                'reward_name' => $result['reward_name'],
                'reward_type' => $result['reward_type'],
                'relation_id' => $result['relation_id'] ?? 0,
                'status' => $result['reward_type'] == 0 ? 100 : 0,      //谢谢参与直接已发放
                'roll' => $roll,
                'raffle_setting' => $valid_rewards,
                'create_time' => time()
            ];

            $record_data['recharge_mobile'] = $result['reward_type'] == 2 ? $mobile : '';
            $record = $record_model->addRecord($record_data);
            if (!$record) {
                Db::rollback();
                return $this->error('', '抽奖记录添加失败');
            }

            // 扣除积分
            // TODO: 调用贡献值服务扣减贡献值
            if(!$memberTask->raffle($record))
            {
                Db::rollback();
                return ['code' => -1, 'message' => '贡献值扣减失败'];
            }

            // 发放奖励(优惠券)
            if ($result['reward_type'] == 1 && !empty($result['relation_id'])) {

                $receiveRes = (new \addon\goodscoupon\model\Goodscoupon())
                    ->receiveGoodscoupon($result['relation_id'], $member_id, 7, 1, 1);
                if ($receiveRes['code'] != 0) {
                    Db::rollback();
                    return ['code' => -1, 'message' => '兑换失败:'.$receiveRes['message']];
                }
                $goodscoupon_id = $receiveRes['data'];

                // 更新抽奖记录
                $record_model->editRecord([
                    'status' => 100,
                    'reward_relation_id' => $goodscoupon_id
                ], [['record_id', '=', $record->record_id]]);
            }
            
            // 更新奖项统计
            $reward_model->addRewardCount($result['reward_id']);

            Db::commit();

            $retReward['reward_id'] = $result['reward_id'];
            $retReward['reward_type'] = $result['reward_type'];
            $retReward['relation_id'] = $result['relation_id'];
            $retReward['reward_name'] = $result['reward_name'];
            // 返回结果
            return $this->success([
                'record_id' => $record->record_id,
                'reward' => $retReward
            ]);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', $e->getMessage());
        }
    }
    
    /**
     * 根据概率随机抽取奖项
     * @param array $rewards 奖项列表
     * @return array|null 抽中的奖项
     */
    private function randomReward($rewards, &$roll = 0)
    {
        $defaultRewards = null;

        foreach ($rewards as $reward) {
            if($reward['reward_type'] == 0)
                $defaultRewards = $reward;
        }

        if (empty($rewards) || empty($defaultRewards)) {
            return null;
        }

        // 计算总权重
        /*$total_weight = 0;
        foreach ($rewards as $reward) {
            $total_weight += $reward['probability'];
        }
        
        if ($total_weight <= 0) {
            return null;
        }*/
        
        // 生成随机数
        $random = mt_rand(1, 100);
        $roll = $random;
        // 根据随机数选择奖项
        $current_weight = 0;

        foreach ($rewards as $reward) {
            $current_weight += $reward['probability'];
            if ($random <= $current_weight) {
                // 如果有数量限制且已达到上限，返回默认谢谢参与
                if ($reward['reward_limit'] != -1 && $reward['reward_count'] >= $reward['reward_limit']) {
                    return $defaultRewards;
                }
                return $reward;
            }
        }
        
        // 返回默认谢谢参与
        return $defaultRewards;
    }
    
    /**
     * 更新话费充值手机号
     * 仅在话费充值类型且待发放状态下可以修改
     * 
     * @param int $record_id 记录ID
     * @param string $mobile 手机号
     * @param int $member_id 会员ID（0表示管理员操作，不检查会员ID）
     * @return array
     */
    public function updateRechargeMobile($record_id, $mobile, $member_id = 0)
    {
        // 参数验证
        if (empty($record_id)) {
            return $this->error('', '记录ID不能为空');
        }
        
        if (empty($mobile)) {
            return $this->error('', '手机号不能为空');
        }
        
        // 验证手机号格式（中国大陆手机号）
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            return $this->error('', '手机号格式不正确');
        }
        
        // 查询记录是否存在且符合条件
        $query = Db::name('league_raffle_record')
            ->where('record_id', $record_id);
            
        // 如果是会员操作，需要验证会员ID
        if ($member_id > 0) {
            $query = $query->where('member_id', $member_id);
        }
        
        $record = $query->find();
        
        if (empty($record)) {
            return $this->error('', '记录不存在或无权操作');
        }
        
        // 验证是否为话费充值类型且待发放状态
        if ($record['reward_type'] != 2) {
            return $this->error('', '该记录不是话费充值类型，无法修改手机号');
        }
        
        if ($record['status'] != 0) {
            return $this->error('', '该记录已发放，无法修改手机号');
        }

        // 会员操作需要检查时间限制，管理员操作不需要
        if ($member_id > 0 && time() - $record['create_time'] > 3600 * 48) {
            return $this->error('', '已超出可更换手机号时间');
        }
        
        // 更新手机号
        $result = Db::name('league_raffle_record')
            ->where('record_id', $record_id)
            ->update(['recharge_mobile' => $mobile]);
        
        return $this->success($result);
    }
    
    /**
     * 批量更新抽奖记录状态
     * 
     * @param array $record_ids 记录ID数组
     * @param int $status 目标状态：-1(发放失败)、0(待发放)、100(已发放)
     * @return array
     */
    public function batchUpdateRecordStatus($record_ids, $status)
    {
        // 参数验证
        if (empty($record_ids) || !is_array($record_ids)) {
            return $this->error('', '记录ID列表不能为空');
        }
        
        // 验证状态值是否合法
        $valid_status = [-1, 0, 100]; // 发放失败、待发放、已发放
        if (!in_array($status, $valid_status)) {
            return $this->error('', '无效的状态值');
        }
        
        // 查询所有记录是否存在
        $records = Db::name('league_raffle_record')
            ->whereIn('record_id', $record_ids)
            ->select()
            ->toArray();
            
        if (count($records) != count($record_ids)) {
            return $this->error('', '部分记录不存在');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 批量更新记录状态
            $result = Db::name('league_raffle_record')
                ->whereIn('record_id', $record_ids)
                ->update(['status' => $status, 'update_time' => time()]);
                
            if ($result === false) {
                Db::rollback();
                return $this->error('', '更新状态失败');
            }
            
            // 如果设置为已发放状态，可能需要处理额外逻辑
            if ($status == 100) {
                // 这里可以添加额外的处理逻辑，例如记录发放日志等
            }
            
            Db::commit();
            return $this->success([
                'affected_rows' => $result,
                'record_ids' => $record_ids
            ], '批量更新状态成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '批量更新状态失败：' . $e->getMessage());
        }
    }
    
    /**
     * 批量保存所有奖项配置和基本配置
     * @param array $data 包含基本配置和奖项配置的数组
     * @return array
     */
    public function saveAllRewards($data)
    {
        // 参数验证
        if (!isset($data['base_config']) || !isset($data['rewards'])) {
            return $this->error('', '参数错误');
        }
        
        $base_config = $data['base_config'];
        $rewards = $data['rewards'];
        
        // 开启事务
        Db::startTrans();
        try {
            // 1. 保存基本配置
            $points = isset($base_config['points']) ? intval($base_config['points']) : 20;
            $status = isset($base_config['status']) ? intval($base_config['status']) : 1;
            
            $config_model = new Config();
            $config_value = [
                'points' => $points
            ];
            
            $base_result = $config_model->setConfig($config_value, '抽奖活动基础配置', $status, [
                [ 'site_id', '=', 0 ],
                ['app_module', '=', 'admin'],
                ['config_key', '=', 'leaguepoints_raffle_config']
            ]);
            
            if ($base_result['code'] != 0) {
                Db::rollback();
                return $this->error('', '保存基本配置失败：' . ($base_result['message'] ?? '未知错误'));
            }
            
            // 2. 处理奖项配置
            if (!empty($rewards)) {
                $reward_model = new LeagueRaffleRewardModel();
                $non_thanks_probability = 0; // 非谢谢参与奖项的总概率
                
                // 找到谢谢参与奖项ID
                $thanks_reward = $reward_model->getRewardInfo([
                    ['reward_type', '=', 0], 
                    ['status', '=', 1]
                ]);
                
                if (empty($thanks_reward)) {
                    Db::rollback();
                    return $this->error('', '谢谢参与奖项不存在');
                }
                
                $thanks_reward_id = $thanks_reward['reward_id'];
                
                // 保存非"谢谢参与"奖项
                foreach ($rewards as $reward) {
                    // 跳过谢谢参与奖项，稍后单独处理
                    if (isset($reward['reward_id']) && $reward['reward_id'] == $thanks_reward_id) {
                        continue;
                    }
                    
                    // 验证必要字段
                    if (!isset($reward['reward_id']) || !isset($reward['probability']) || !isset($reward['reward_limit'])) {
                        continue;
                    }
                    
                    $reward_id = $reward['reward_id'];
                    $probability = floatval($reward['probability']);
                    $reward_limit = intval($reward['reward_limit']);
                    
                    // 累加非谢谢参与奖项的概率
                    $non_thanks_probability += $probability;
                    
                    // 更新奖项
                    $update_data = [
                        'probability' => $probability,
                        'reward_limit' => $reward_limit,
                        'update_time' => time()
                    ];
                    
                    // 如果提供了relation_value，也更新它（用于话费充值奖励）
                    if (isset($reward['relation_value'])) {
                        $update_data['relation_value'] = $reward['relation_value'];
                    }
                    
                    $result = $reward_model->editReward($update_data, [['reward_id', '=', $reward_id]]);
                    
                    if (!$result) {
                        Db::rollback();
                        return $this->error('', '更新奖项失败：奖项ID=' . $reward_id);
                    }
                }
                
                // 确保总概率不超过100%
                if ($non_thanks_probability > 100) {
                    Db::rollback();
                    return $this->error('', '所有奖项概率总和不能超过100%');
                }
                
                // 3. 自动计算并更新"谢谢参与"奖项的概率
                $thanks_probability = 100 - $non_thanks_probability;
                $result = $reward_model->editReward([
                    'probability' => $thanks_probability,
                    'update_time' => time()
                ], [['reward_id', '=', $thanks_reward_id]]);
                
                if (!$result) {
                    Db::rollback();
                    return $this->error('', '更新谢谢参与奖项概率失败');
                }
            }
            
            // 提交事务
            Db::commit();
            
            return $this->success('', '保存成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('', '保存失败：' . $e->getMessage());
        }
    }
} 