<?php


namespace addon\leaguePoints\service;


use addon\leaguePoints\model\LeagueTaskMemberSaleTaskModel;
use addon\leaguePoints\model\LeagueTaskSaleTaskGoodsModel;
use app\model\member\MemberModel;
use app\model\order\OrderModel;
use app\model\shop\ShopModel;
use app\service\shop\ShopOrderService;
use Carbon\Carbon;
use think\facade\Db;

class GoodsSaleTask
{
    protected $leagueTaskKey = 'league_1';
    public function __construct($leagueTaskKey='league_1')
    {
        $this->leagueTaskKey = $leagueTaskKey;
    }

    /**
     * 判断是否活动商品
     * @param int $goodsId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function isTaskGoods(int $goodsId)
    {
        $nowTime = Carbon::now()->timestamp;
        return (bool)LeagueTaskSaleTaskGoodsModel::where("league_task_key", $this->leagueTaskKey)->where(function ($query) use ($nowTime){
            $query->where("status", 1)->whereOr("disable_start_time", ">", $nowTime);
        })->where('goods_id', $goodsId)->find();
    }

    /**
     * 是否已领取月度销售指标任务
     * @param int $siteId
     * @param string $month
     * @param int $goodsId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function isReceiveTask(int $memberId, string $month, int $goodsId)
    {
        return (bool)LeagueTaskMemberSaleTaskModel::where("member_id", $memberId)->where("goods_id", $goodsId)->where("month", $month)->where("league_task_key", $this->leagueTaskKey)->find();
    }

    /**
     * 获取开启的商品ids
     * @return array
     */
    public function getEnableGoodsIds()
    {
        $nowTime = Carbon::now()->timestamp;
        return LeagueTaskSaleTaskGoodsModel::where("league_task_key", $this->leagueTaskKey)->where(function ($query) use ($nowTime){
            $query->where("status", 1)->whereOr("disable_start_time", ">", $nowTime);
        })->column('goods_id');
    }


    public function createTask(OrderModel $order)
    {
        $mobile = ShopModel::find($order->site_id)->username;
        $member = MemberModel::where("mobile", $mobile)->where('status', 1)->find();

        $retTasks = [];
        if($order->pay_status == 1 && !empty($order->out_trade_no) && !empty($order->pay_time) && $order->site_id != 110)
        {
            $taskGoodsIds = $this->getEnableGoodsIds();
            $month = Carbon::parse($order->create_time)->format("Y-m");
            foreach ($order->goods as $orderGoods)
            {
                if($this->isTaskGoods($orderGoods->goods_id) && !$this->isReceiveTask($member->member_id, $month, $orderGoods->goods_id))
                {
                    $addData['league_task_key'] = $this->leagueTaskKey;
                    $addData['site_id'] = $order->site_id;
                    $addData['member_id'] = $member->member_id;
                    $addData['goods_id'] = $orderGoods->goods_id;
                    $addData['month'] = $month;
                    LeagueTaskMemberSaleTaskModel::create($addData);
                }
            }
        }
    }

    public function isComplete(LeagueTaskMemberSaleTaskModel $memberTask)
    {
        $siteId = $memberTask->site_id;
        //开启或还没到停用的下个月1号
        if($memberTask->status == 1 || $memberTask->disable_start_time > Carbon::now()->timestamp)
        {
            $completeNums = OrderModel::where("site_id", $siteId)->where("order_status", 10)->count();
            if($completeNums >= $memberTask->month_sales)
                return true;
        }
        return false;
    }

    public function close(LeagueTaskMemberSaleTaskModel $memberTask)
    {
        $memberTask->status = -1;
        $memberTask->save();
    }

    public function complete(LeagueTaskMemberSaleTaskModel $memberTask)
    {
        $taskGoods = $memberTask->belongTaskGoods;

        $monthStart = Carbon::parse($memberTask->month)->timestamp;
        $monthEnd = Carbon::parse($memberTask->month)->addMonth()->timestamp - 1;
        $orderIds = Db::name("order")
            ->alias('o')
            ->join('order_goods og', 'o.order_id=og.order_id')
            ->where("o.site_id", $memberTask->site_id)
            ->whereBetween('o.create_time', [$monthStart, $monthEnd])
            ->where('o.pay_status', 1)
            ->where('o.pay_time', ">", 0)
            ->where('o.order_status', 10)
            ->where('og.goods_id', $taskGoods->goods_id)
            ->column("o.order_id");

        $memberTask->status = 100;
        $memberTask->month_sales = $taskGoods->month_sales;
        $memberTask->reward_points = $taskGoods->reward_points;
        $memberTask->complete_order_ids = $orderIds;
        $memberTask->save();
    }

    /**
     * 检测是否已完成月度指标
     * @param LeagueTaskMemberSaleTaskModel $memberTask
     * @return bool
     */
    public function checkComplete(LeagueTaskMemberSaleTaskModel $memberTask)
    {
        $soService = new ShopOrderService();
        $taskGoods = $memberTask->belongTaskGoods;
        //创建了任务就不需要判断是否开启，完成指标取最新配置
        if($taskGoods)
        {
            $monthSalesNums = $taskGoods->month_sales;
            if($soService->getMonthCompleteNums($memberTask->month, $memberTask->site_id, $memberTask->goods_id) >= $monthSalesNums)
            {
                $this->complete($memberTask);
                return true;
            }
        }
        return false;
    }

    public function sendReward(LeagueTaskMemberSaleTaskModel $memberTask)
    {
        $failReason = '';
        // 调用积分发放服务
        $success = SendLeaguePointService::sendSaleTaskComplete($memberTask, $failReason);
        if($success)
        {
            $memberTask->reward_status = 1;
        }
        else
        {
            $memberTask->reward_status = -1;
            $memberTask->fail_reason = $failReason;
        }
        $memberTask->save();
    }
}