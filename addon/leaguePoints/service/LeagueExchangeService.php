<?php


namespace addon\leaguePoints\service;


use addon\leaguePoints\facade\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\LeagueExchangeGoodsModel;
use addon\leaguePoints\model\LeagueExchangeRecordModel;
use app\Domain\Infrastructure\Persistence\Mysql\MysqlWeappNoticeRepository;
use app\Domain\Services\WeappNotice\SendGoodscouponNoticeService;
use think\facade\Db;
use think\facade\Log;
use think\Model;

class LeagueExchangeService
{
    /**
     * 添加兑换物品
     * @param array $data 兑换物品数据
     * @return array
     */
    public function addExchangeGoods($data)
    {
        $model = new LeagueExchangeGoodsModel();
        $goods_id = $model->add($data);
        if ($goods_id) {
            return ['code' => 0, 'message' => '添加成功', 'data' => $goods_id];
        } else {
            return ['code' => -1, 'message' => '添加兑换物品失败'];
        }
    }

    /**
     * 编辑兑换物品
     * @param array $data 兑换物品数据
     * @param int $id 兑换物品ID
     * @return array
     */
    public function editExchangeGoods($data, $id)
    {
        $model = new LeagueExchangeGoodsModel();
        $res = $model->edit($data, [['id', '=', $id]]);
        if ($res !== false) {
            return ['code' => 0, 'message' => '编辑成功', 'data' => $res];
        } else {
            return ['code' => -1, 'message' => '编辑兑换物品失败'];
        }
    }

    /**
     * 修改兑换物品状态
     * @param int $id 兑换物品ID
     * @param int $status 状态 0:下架 1:上架
     * @return array
     */
    public function modifyStatus($id, $status)
    {
        $model = new LeagueExchangeGoodsModel();
        $res = $model->modifyStatus($status, [['id', '=', $id]]);
        if ($res !== false) {
            $statusText = $status == 1 ? '上架' : '下架';
            return ['code' => 0, 'message' => $statusText . '成功', 'data' => $res];
        } else {
            return ['code' => -1, 'message' => '修改状态失败'];
        }
    }

    /**
     * 获取兑换物品列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getExchangeGoodsList($condition = [], $page = 1, $page_size = 10)
    {
        $model = new LeagueExchangeGoodsModel();
        $list = $model->getList($condition, '*', 'id desc', $page, $page_size);
        return ['code' => 0, 'message' => '获取成功', 'data' => $list];
    }

    /**
     * 获取兑换物品详情
     * @param int $id 兑换物品ID
     * @return array
     */
    public function getExchangeGoodsInfo($id)
    {
        $model = new LeagueExchangeGoodsModel();
        $info = $model->getInfo([['id', '=', $id]]);
        return ['code' => 0, 'message' => '获取成功', 'data' => $info];
    }

    /**
     * 兑换业务
     * @param $member_id 用户id
     * @param LeagueExchangeGoodsModel $exchangeGoods 兑换品模型
     * @param string $league_task_key 任务类型
     * @return array
     */
    public function exchange($member_id, LeagueExchangeGoodsModel $exchangeGoods, $league_task_key = 'league_1')
    {
        try
        {
            Db::startTrans();
            $memberTask = MemberLeagueTaskPointRepository::findTask($member_id, $league_task_key);
            // 查询积分是否足够
            if($memberTask->getPoints() < $exchangeGoods->points)
            {
                Db::rollback();
                return ['code' => -1, 'message' => '贡献值不足'];
            }
            // 查询是否上架
            if($exchangeGoods->status != LeagueExchangeGoodsModel::$STATUS_UP)
            {
                Db::rollback();
                return ['code' => -1, 'message' => '兑换品已下架'];
            }

            // 更新兑换次数和总积分
            $goods_model = new LeagueExchangeGoodsModel();
            $goods_model->updateExchangeCount($exchangeGoods->id, $exchangeGoods->points);

            // TODO: 调用贡献值服务扣减贡献值
            if(!$memberTask->exchange($exchangeGoods))
            {
                Db::rollback();
                return ['code' => -1, 'message' => '贡献值扣减失败'];
            }

            // TODO: 调用对应类型的服务发放奖励（如：优惠券）
            $receiveRes = (new \addon\goodscoupon\model\Goodscoupon())
                ->receiveGoodscoupon($exchangeGoods->relation_id, $member_id, 6, 1, 1);
            if ($receiveRes['code'] != 0) {
                Db::rollback();
                return ['code' => -1, 'message' => '兑换失败:'.$receiveRes['message']];
            }
            $goodscoupon_id = $receiveRes['data'];
            // 添加兑换记录
            $record_model = new LeagueExchangeRecordModel();
            $record_data = [
                'goods_id' => $exchangeGoods->id,
                'member_id' => $member_id,
                'points' => $exchangeGoods->points,
                'relation_id' => $goodscoupon_id,
                'type' => $exchangeGoods->type
            ];

            $record_id = $record_model->add($record_data);

            if (!$record_id) {
                Db::rollback();
                return ['code' => -1, 'message' => '兑换记录添加失败'];
            }
            Db::commit();
        }
        catch (\Exception $e)
        {
            Db::rollback();
            Log::error('兑换失败');
            Log::error($e->getMessage().PHP_EOL.$e->getTraceAsString());
            return ['code' => -1, 'message' => '兑换失败'];
        }
        // 发送通知
        $service = new SendGoodscouponNoticeService(new MysqlWeappNoticeRepository());
        $service->execute($member_id, $exchangeGoods->relation_id);

        return ['code' => 0, 'message' => '兑换成功', 'data' => $record_id];
    }

    /**
     * 获取兑换记录列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getExchangeRecordList($condition = [], $page = 1, $page_size = 10)
    {
        $model = new LeagueExchangeRecordModel();
        $list = $model->getList($condition, '*', 'id desc', $page, $page_size);
        return ['code' => 0, 'message' => '获取成功', 'data' => $list];
    }

} 