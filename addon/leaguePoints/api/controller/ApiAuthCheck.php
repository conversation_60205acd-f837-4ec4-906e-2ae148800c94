<?php


namespace addon\leaguePoints\api\controller;

use app\api\controller\BaseApi;
use app\model\shop\ShopModel;

trait ApiAuthCheck
{
    public $member_id;
    public $user_info;
    public $baseApi;
    public $params;
    public $default_shop = [];
    public $shopId = 0;
    public function __construct()
    {
        $this->baseApi = new BaseApi();

        $this->params = $this->baseApi->params;
        $this->default_shop = $this->baseApi->default_shop;
        $this->shopId = $this->baseApi->shopId;
    }

    public function checkToken(): array
    {
        $res = $this->baseApi->checkToken();
        $this->member_id = $this->baseApi->member_id;
        $this->user_info['username'] = ShopModel::where("site_id", $this->baseApi->shopId)->value('username');
        return $res;
    }
}