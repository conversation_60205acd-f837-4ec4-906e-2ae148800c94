<?php


namespace addon\leaguePoints\api\controller;

use addon\leaguePoints\domainModel\LeagueTaskPointConfig;
use addon\leaguePoints\domainModel\MemberLeagueTaskPointRepository;
use addon\leaguePoints\model\LeagueTaskMemberGoodsTask;
use addon\leaguePoints\model\LeagueTaskPointJoinMemberModel;
use app\model\goods\Goods as GoodsModel;
use app\api\controller\BaseApi;
use think\facade\Db;
use think\facade\Lang;

class MemberLeaguePoint extends \addon\leaguePoints\shopapi\controller\MemberLeaguePoint
{
    use ApiAuthCheck;
}