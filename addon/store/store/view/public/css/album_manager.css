#album {
	padding: 10px 20px;
}

.album-box {
	display: flex;
}

.album-list {
	margin-right: 25px;
	width: 180px;
	min-height: 400px;
	background-color: #F7F7F7;
}

.album-list li {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 25px;
	height: 50px;
	cursor: pointer;
}

.album-list li.active {
	background-color: #E8E8E8;
}

.album-content {
	flex: 1;
}

.album-img {
	display: flex;
	flex-wrap: wrap;
}

.album-img li {
	width: 125px;
	height: 155px;
	margin: 0 15px 15px 0;
}

.album-img li div {
	position: relative;
	width: 100%;
	height: 125px;
	line-height: 125px;
	text-align: center;
}

.album-img li div .image-box-active {
	position: absolute;
	z-index: 1;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	border: 2px solid;
}

.album-img li div .image-box-active:after {
	content: '';
	display: block;
	position: absolute;
	top: 0px;
	right: 0;
	border: 15px solid;
	border-color: transparent;
	border-top-color: #12b7f5;
	border-right-color: #12b7f5;
}

.album-img li div .image-box-active i {
	position: absolute;
	top: 2px;
	right: 2px;
	color: #fff;
	z-index: 2;
	font-style: normal;
	line-height: 1;
}

.album-img li img {
	max-height: 100%;
	max-width: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.album-img li span {
	display: block;
	margin-top: 10px;
	height: 20px;
	line-height: 20px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.empty-data {
	margin-top: 10px;
	width: 100%;
	height: 30px;
	line-height: 30px;
	text-align: center;
}

/* 本地图片上传*/
.multuple-list {
	display: flex;
	flex-wrap: wrap;
	overflow-y: auto;
	height: 300px;
	padding-top: 10px;
	box-sizing: border-box;
}

.multuple-list-img {
	border-color: #DDDDDD!important;
}

.multuple-list li {
	position: relative;
	display: flex;
	margin-bottom: 10px;
	margin-right: 10px;
	flex-direction: column;
	justify-content: center;
	flex-wrap: wrap;
	align-items: center;
	width: 120px;
	height: 120px;
	border: 1px solid;
}

.multuple-list li img {
	max-width: 100%;
	max-height: 100%;
}

.upload-close-modal {
	display: none;
	position: absolute;
	top: -10px;
	right: -10px;
	height: 20px;
	width: 20px;
	font-size: 18px;
	line-height: 20px;
	text-align: center;
	color: #fff;
	background-color: rgba(0, 0, 0, .5);
	border-radius: 50%;
	z-index: 1;
}

.upload-image-curtain {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	text-align: center;
	line-height: 122px;
	font-size: 18px;
	color: #fff;
	background-color: rgba(0, 0, 0, .5);
	z-index: 1;
}

.multuple-list li:hover .upload-close-modal {
	display: block;
}

.multuple-list li span {
	cursor: pointer;
	width: 100%;
	text-align: center;
}

.multuple-list li span:first-of-type {
	width: 30px;
	height: 30px;
	font-size: 18px;
	margin-bottom: 10px;
	line-height: 30px;
	border-radius: 50%;
	color: #fff;
}
