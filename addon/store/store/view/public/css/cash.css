.ns-basic-color {
	color: #FF3D17 !important;
}

.ns-basic-border-color {
	border-color: #FF3D17 !important;
}

.layui-layout-admin .layui-body {
	left: 0;
}

.layui-layout-admin .ns-crumbs {
	left: 0;
}

.layui-layout-admin .layui-body .ns-body-content {
	height: 100%;
	box-sizing: border-box;
}

.ns-align-right {
	text-align: right!important;
}

.layui-btn {
	font-size: 16px;
	width: 102px;
	height: 48px !important;
	line-height: 48px !important;
}

.ns-text-color-light-gray {
	color: #999999;
}

.ns-cash {
	min-width: 1230px;
	height: 100%;
	position: relative;
}

.ns-money {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	box-sizing: border-box;
	padding: 15px;
}

.ns-money-left {
	width: 400px;
	flex-shrink: 0;
}

.ns-money-middle {
	width: 20px;
	flex-shrink: 0;
}

.ns-money-right {
	flex: 1;
}

.ns-money-box {
	box-shadow: 0 0 4px #e5e5e5;
	height: 100%;
}

/* 左侧 */
.layui-card {
	margin-top: 0;
}

.ns-card-common .ns-card-title {
	font-weight: 500;
}

.ns-card-common .layui-card-header {
	padding: 5px 15px;
	border-bottom: 1px solid #E1E1E1;
}

.ns-money-left .layui-card-body {
	padding: 15px;
	height: calc(100% - 194px);
	position: relative;
	box-sizing: border-box;
	overflow: auto;
}

.ns-money-left .layui-card-body::-webkit-scrollbar {display:none}

/* 无商品 */
.ns-null-good {
	text-align: center;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.ns-null-good img {
	width: 80px;
	margin-bottom: 15px;
}

.ns-null-font {
	color: #999999;
	font-size: 12px;
}

/* 账单列表 */
.ns-good-seleted {
	border-bottom: 1px solid #F1F1F1;
	padding: 5px;
}

.ns-good-seleted-name, .ns-good-seleted-info {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	line-height: 35px;
}

.ns-good-seleted-name p {
	width: 80%;
}

.layui-card-bottom {
	padding: 0 15px;
	border-top: 1px solid #F1F1F1;
}

.layui-card-bottom p {
	display: flex;
	justify-content: space-between;
}

.layui-card-bottom span {
	line-height: 30px;
}

.ns-total-sum {
	font-size: 18px;
}

.ns-card-common {
	position: relative;
	height: 100%;
}

.ns-card-bottom {
	position: absolute;
	width: 100%;
	bottom: 0;
}

.ns-card-bottom .ns-form-row {
	width: 100%;
	margin-left: 0;
	margin-top: 0;
	padding: 15px;
	box-sizing: border-box;
	border-top: 1px solid #F1F1F1;
	display: flex;
	justify-content: space-between;
}

/* 购买的数量 */
.ns-selected-num input {
	display: inline-block;
	width: 36px;
	height: 20px;
	line-height: 20px;
	text-align: center;
	padding: 0;
	font-size: 12px;
	border-radius: 0;
	float: right;
}

.ns-selected-num span {
	display: inline-block;
	width: 20px;
	height: 20px;
	text-align: center;
	line-height: 20px;
	font-size: 15px;
	border: 1px solid #E6E6E6;
	box-sizing: border-box;
	background-color: #FFFFFF;
	color: #999999;
	cursor: pointer;
	vertical-align: top;
	float: right;
}

.ns-selected-num span:first-child {
	border-left: 0;
}

.ns-selected-num span:last-child {
	border-right: 0;
}

/* 删除一条结账信息 */
.ns-del-account {
	cursor: pointer;
}

/* 商品 */
.ns-goods-block {
	width: 100%;
	height: 100%;
}

.ns-table-tab .layui-tab-content {
	height: calc(100% - 122px);
}

/* 左侧商品 */
.ns-good-left {
	float: left;
	width: calc(100% - 201px);
	height: 100%;
}

.ns-good-left .layui-input-block {
	margin-left: 0;
}

.ns-table-tab {
	margin: 0;
	padding-top: 10px;
	height: 100%;
	box-sizing: border-box;
	position: relative;
}

.ns-table-tab .layui-tab-title li:first-child {
	margin-left: 20px;
}

.ns-good-left .layui-input {
	height: 40px;
	line-height: 40px;
}

.ns-good-left .layui-btn {
	width: 50px;
	height: 38px !important;
	line-height: 38px !important;
}

.ns-single-filter-box {
	padding: 15px;
}

.ns-goods-list {
	padding: 0 15px;
	height: calc(100% - 70px);
	overflow: auto;
	position: relative;
}

.ns-goods-list .ns-null-good img {
	width: 100px;
}

.ns-goods-list::-webkit-scrollbar {
	width: 3px;
}

.ns-goods-list::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, .2);
	border-radius: 3px;
}

.ns-good-box {
	display: inline-block;
	height: 130px;
	border: 1px solid #E1E1E1;
	padding: 8px;
	margin-bottom: 16px;
	box-sizing: border-box;
	position: relative;
	cursor: pointer;
	vertical-align: top;
	border-radius: 5px;
	float: left;
}

/* 一行4个商品时的排布 */
@media screen and (min-width: 1840px) {
	.ns-good-box {
		width: 24.25%;
		margin-right: 1%;
	}
	
	.ns-good-box:nth-child(4n) {
		margin-right: 0;
	}
}

/* 一行3个商品时的排布 */
@media screen and (min-width: 1500px) and (max-width: 1839px) {
	.ns-good-box {
		width: 32%;
		margin-right: 2%;
	}
	
	.ns-good-box:nth-child(3n) {
		margin-right: 0;
	}
}

/* 一行2个商品时的排布 */
@media screen and (max-width: 1499px) {
	.ns-good-box {
		width: 49%;
		margin-right: 2%;
	}
	
	.ns-good-box:nth-child(2n) {
		margin-right: 0;
	}
}

.ns-good-box .ns-table-title .ns-title-pic {
	width: 110px;
	height: 110px;
	line-height: 110px;
}

.ns-good-box .ns-table-title {
	align-items: flex-start;
}

.ns-empty-stock {
	display: none;
	position: absolute;
	bottom: 0;
	right: 0;
	width: 50px;
	height: 50px;
	text-align: right;
}

.disabled .ns-empty-stock {
	display: inline-block;
}

.ns-empty-stock img {
	max-width: 100%;
	max-height: 100%;
}

.disabled {
	background-color: #F5F5F5;
}

.disabled .good-name, .disabled .ns-good-price {
	color: #999999;
}

#goods_page {
	text-align: right;
}

.good-no, .store-stock {
	font-size: 12px;
	color: #999;
	line-height: 18px;
}

.good-no {
	margin-top: 5px;
}

.ns-good-price {
	position: absolute;
	bottom: 8px;
}

/* 右侧分类 */
.ns-good-right {
	float: left;
	width: 200px;
	height: 100%;
	flex-shrink: 0;
	border-left: 1px solid #F1F1F1;
}

.ns-good-right .layui-nav {
	background-color: transparent;
}

.ns-good-right .layui-nav .layui-nav-item a {
	color: #333333;
}

.ns-good-right .layui-nav-itemed>a {
	color: #FF3D17 !important;
}

.ns-good-right .layui-nav-tree .layui-nav-item a:hover {
	background-color: transparent;
}

.ns-good-right .layui-nav-tree .layui-nav-bar {
	width: 0;
}

.ns-good-right .layui-nav-itemed>.layui-nav-child {
	background-color: transparent !important;
	padding-left: 25px;
	box-sizing: border-box;
}

/* 会员 */
.layui-tab-item {
	height: 100%;
}

.ns-dial {
	position: relative;
}

.ns-menber-dial {
	position: absolute;
	text-align: center;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.dial {
	text-align: center;
}

.ns-menber-dial input {
	display: inline-block;
	width: 450px;
	height: 50px;
	line-height: 50px;
	margin: 15px 0;
}

.ns-menber-dial .layui-input-block {
	margin-left: 0;
}

.ns-menber-dial .ns-single-filter-box .layui-form .layui-input + .layui-btn {
	height: 48px;
	line-height: 48px;
	top: 16px;
	width: 60px;
}

.ns-menber-dial p {
	font-size: 18px;
	margin-bottom: 10px;
}

.ns-dial-table td {
	width: 99px;
	height: 50px;
	font-size: 18px;
	padding: 0;
	background-color: #FFFFFF;
	cursor: pointer;
}

.ns-dial-table td:active {
	background-color: #EEEEEE;
}

.ns-dial-table {
	width: auto;
	margin: 0 auto;
}

.iconfont {
	font-size: 32px;
}

.ns-bg-color {
	color: #FFFFFF;
}

.ns-form-row-sec {
	width: 100%;
	margin-left: 0;
	margin-top: 0;
	padding: 15px;
	box-sizing: border-box;
	border-top: 1px solid #F1F1F1;
	position: absolute;
	bottom: 0;
	background-color: #FFFFFF;
}

.ns-member {
	padding: 15px;
	box-sizing: border-box;
}

/* 添加会员 */
.ns-menber-add {
	width: 100%;
	padding-left: 50px;
	box-sizing: border-box;
}

.ns-menber-add p {
	font-size: 16px;
	font-weight: 600;
	line-height: 60px;
}

.ns-member-intro h3 {
	font-weight: 600;
	margin: 10px 0;
}

/* 会员详情 */
.layui-layer-page .layui-layer-content {
	overflow: auto !important;
}

.ns-menber-detail .ns-title-content>div {
	display: flex;
	align-items: center;
}

.ns-member-con-first h2 {
	font-weight: 600;
	margin-right: 10px;
}

.ns-member-con-first p {
	margin-right: 10px;
}

.ns-member-level {
	height: 20px;
	line-height: 20px;
	padding: 0 10px;
	background-color: #ffe7e2;
	text-align: center;
}

.ns-member-sex {
	width: 40px;
	height: 20px;
	line-height: 20px;
	text-align: center;
	background-color: #F5F5F5;
}

.ns-member-con-second p {
	margin-right: 30px;
}

.ns-menber-detail>button {
	margin-left: 65px;
	margin-top: 10px;
}

.ns-member-num {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	margin-top: 20px;
}

.ns-member-num-box {
	width: 100%;
	padding: 20px;
	margin-bottom: 20px;
	box-sizing: border-box;
	border: 1px solid #F1F1F1;
	display: flex;
	flex-wrap: wrap;
}

.ns-member-info-box {
	width: 33.3%;
}

.ns-member-num-price {
	font-size: 25px;
	font-weight: 600;
}

.ns-member-num-desc {
	height: 22px;
	line-height: 22px;
}

.ns-member-num .layui-card {
	width: 100%;
}

.ns-member-card {
	width: 100%;
	height: 80px;
	background: -moz-linear-gradient(left, #dbb97d 0%, #b88e40 100%);
	background: -webkit-gradient(linear, left top, right bottom, color-stop(0%, #dbb97d), color-stop(100%, #b88e40));
	background: -webkit-linear-gradient(left, #dbb97d 0%, #b88e40 100%);
	background: -o-linear-gradient(left, #dbb97d 0%, #b88e40 100%);
	background: -ms-linear-gradient(left, #dbb97d 0%, #b88e40 100%);
	background: linear-gradient(to right, #dbb97d 0%, #b88e40 100%);
	border-radius: 5px;
	display: flex;
	align-items: center;
	padding: 15px;
	box-sizing: border-box;
	cursor: pointer;
}

.ns-member-card p {
	color: #FFFFFF;
}

.ns-member-card-selected {
	position: relative;
}

/* 挂单列表 */
.ns-rest-cart-block {
	width: 100%;
	box-sizing: border-box;
	height: calc(100% - 55px);
	overflow: auto;
}

.ns-rest-cart-box {
	width: 100%;
	padding: 20px;
	border-bottom: 1px solid #F1F1F1;
	box-sizing: border-box;
}

.ns-rest-cart-box .ns-form-row, .ns-cash-settle .ns-form-row {
	display: block;
	text-align: right;
}

.ns-restcart-block, .ns-sellte-block { /* 顶层div盒子 */
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0px;
	left: 0px;
	padding: 15px;
	background-color: transparent;
}

.ns-restcart-block .ns-money-left, .ns-sellte-block .ns-money-left {
	background-color: rgba(255, 255, 255, .5);
}

.ns-restcart-block .ns-money-right, .ns-sellte-block .ns-money-right {
	background-color: #FFFFFF;
}

.ns-rest-cart-title {
	width: 100%;
	display: flex;
	justify-content: space-between;
	border-bottom: 1px solid #F1F1F1;
	padding: 15px;
	box-sizing: border-box;
	font-size: 16px;
	line-height: 24px;
}

.ns-rest-cart-title i {
	font-size: 20px;
	cursor: pointer;
}

/* 结算信息 */
.ns-cash-settle {
	width: 100%;
	height: calc(100% - 55px);
	box-sizing: border-box;
	position: relative;
}

.ns-cash-settle-box {
	height: calc(100% - 186px);
	overflow: auto;
}

.ns-cash-store {
	padding: 15px;
}

.ns-cash-store h3 {
	line-height: 30px;
}

.ns-cash-store-info {
	display: flex;
	flex-wrap: wrap;
	margin-top: 10px;
	
}

.ns-cash-store-info p {
	color: #666666;
}

.ns-cash-store-opendate {
	margin-right: 50px;
}

.ns-cash-coupon-list {
	border: 1px solid #FF3D17;
	width: 380px;
	height: 150px;
	padding: 20px 20px 0;
	box-sizing: border-box;
	margin-right: 20px;
	margin-bottom: 20px;
	border-radius: 5px;
	cursor: pointer;
	position: relative;
}

.ns-coupon-box {
	width: 100%;
	height: 100%;
	position: relative;
}

.ns-coupon-info {
	width: 100%;
	padding-bottom: 12px;
	border-bottom: 1px solid #F1F1F1;
}

.ns-coupon-type {
	font-size: 16px;
	color: #000;
	line-height: 30px;
}

.ns-coupon-time {
	line-height: 30px;
}

.ns-cash-coupon-selected {
	border: 1px solid #FF3D17;
}

.ns-coupon-price-box {
	font-size: 20px;
	position: absolute;
	top: 0;
	right: 0;
}

.ns-coupon-price {
	font-size: 50px;
}

.ns-coupon-name {
	font-size: 16px;
	line-height: 50px;
}

.selected-icon {
	display: none;
	position: absolute;
	bottom: 0;
	right: 0;
}

.ns-cash-coupon-selected .selected-icon {
	display: inline-block;
}

.ns-cash-settle .ns-form-row {
	display: flex;
	justify-content: flex-end;
}

.ns-cash-settle .ns-form-row div {
	margin-right: 30px;
	display: flex;
	align-items: flex-end;
}

.ns-cash-settle .ns-form-row .ns-basic-color {
	font-size: 30px;
}

/* 支付方式 */
.ns-pay-type {
	margin: 0;
	height: 100%;
	text-align: center !important;
	position: relative;
}

.ns-pay-type .layui-tab-title {
	position: unset;
	border-bottom-width: 0;
	padding: 15px;
	box-sizing: border-box;
	height: auto;
}

.ns-pay-type .layui-tab-title li {
	width: 120px;
	border-left: 0;
	background: #F5F9FC;
	padding: 35px 35px 20px;
	margin: 25px;
}

.ns-pay-type .layui-tab-title .layui-this {
	background-color: #EBF0F4;
}

.ns-pay-type .layui-tab-title .layui-this:after {
	border-width: 0;
}

.ns-pay-type .layui-tab-content {
    padding: 10px;
    box-sizing: border-box;
}

.ns-pay-box {
	width: 100%;
	margin-top: 60px;
}

.ns-pay-cash {
	width: 500px;
	margin: 0 auto;
}

.ns-input-money {
	display: inline-block;
	width: 450px;
	height: 50px;
	line-height: 50px;
	text-align: right;
	padding-right: 10px;
	color: #333333;
	font-size: 16px;
}

.ns-pay-cash span {
	position: absolute;
	display: inline-block;
	height: 50px;
	line-height: 50px;
	padding: 0 10px;
	font-size: 16px;
	color: #333333;
}

.ns-pay-cash p {
	display: inline-block;
	width: 450px;
	text-align: right;
	color: #666;
	line-height: 40px;
}

.ns-pay-type .ns-pay-cash .ns-form-row {
	width: 450px;
	display: inline-block;
	margin-left: 0;
}

.ns-pay-type .ns-form-row {
	display: block;
	text-align: right;
}

/* 挂单数 */
.rest-cart-btn {
	position: relative;
}

.rest-cart-num {
	display: none;
	position: absolute;
	top: -8px;
	right: -8px;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background-color: red;
	color: #FFFFFF;
	font-size: 12px;
	line-height: 16px;
}

/* 取单按钮 */
.rest-cart-btn.disabled {
	background-color: #E1E1E1;
}

/* 支付成功 */
.ns-pay-success {
	width: 100%;
	height: 100%;
	position: relative;
}

.ns-success {
	width: 100%;
	text-align: center;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}

.ns-success-img {
	display: inline-block;
	width: 80px;
	height: 80px;
	line-height: 80px;
	text-align: center;
	background-color: #FF3D17;
	border-radius: 50%;
	margin-bottom: 15px;
}

.ns-success-img .iconfont {
	font-size: 50px;
	color: #FFFFFF;
}

.ns-success p {
	font-size: 20px;
}

.ns-pay-success .ns-form-row {
	display: block;
	text-align: right;
}

/* 登录退出 */
.ns-cash-admin {
	height: 50px;
	border-bottom: 1px solid #F1F1F1;
	line-height: 50px;
	color: #666666;
	padding: 0 28px;
	box-sizing: border-box;
	display: flex;
	position: fixed;
	top: 0;
	right: 0;
	background-color: #FFFFFF;
	z-index: 99999;
}

.ns-cash-logout {
	cursor: pointer;
	margin-left: 50px;
}

.ns-cash-logout .iconfont {
	font-size: 18px;
	margin-right: 5px;
}

.ns-cash-logout {
	display: flex;
	align-items: center;
}


/* 会员添加 */
.ns-member-add h2 {
	text-align: center;
	margin-bottom: 30px;
}

/* 微信二维码 */
.wx-ewm {
	display: inline-block;
	width: 150px;
	height: 150px;
	line-height: 150px;
	text-align: center;
	border-radius: 50%;
	margin-bottom: 15px;
}