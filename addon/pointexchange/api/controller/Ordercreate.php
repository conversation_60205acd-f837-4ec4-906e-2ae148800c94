<?php
/**
 * Index.php
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2015-2025 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: http://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 * <AUTHOR> niuteam
 * @date : 2015.1.17
 * @version : v1.0.0.0
 */

namespace addon\pointexchange\api\controller;

use app\api\controller\BaseApi;
use addon\pointexchange\model\OrderCreate as OrderCreateModel;

/**
 * 订单创建
 * <AUTHOR>
 *
 */
class Ordercreate extends BaseApi
{
	/**
	 * 创建订单
	 */
	public function create()
	{
		$token = $this->checkToken();
		if ($token['code'] < 0) return $this->response($token);
		$order_create = new OrderCreateModel();
		$data = [
			'id' => isset($this->params['id']) ? $this->params['id'] : '',//兑换id
			'num' => isset($this->params['num']) ? $this->params['num'] : 1,//兑换数量(买几套)
			'member_id' => $this->member_id,
			'order_from' => $this->params['app_type'],
			'order_from_name' => $this->params['app_type_name'],
            'buyer_message' => $this->params['buyer_message'],
			'member_address' => isset($this->params["member_address"]) && !empty($this->params["member_address"]) ? json_decode($this->params["member_address"], true) : []
		];
		if (empty($data['id'])) {
			return $this->response($this->error('', '缺少必填参数商品数据'));
		}
		$res = $order_create->create($data);
		return $this->response($res);
	}
	

	
	/**
	 * 待支付订单 数据初始化
	 * @return string
	 */
	public function payment()
	{
		$token = $this->checkToken();
		if ($token['code'] < 0) return $this->response($token);
		$order_create = new OrderCreateModel();
		$data = [
            'id' => isset($this->params['id']) ? $this->params['id'] : '',//兑换id
            'num' => isset($this->params['num']) ? $this->params['num'] : 1,//兑换数量(买几套)
            'member_id' => $this->member_id,
            'order_from' => $this->params['app_type'],
            'order_from_name' => $this->params['app_type_name'],
		];
		if (empty($data['id'])) {
			return $this->response($this->error('', '缺少必填参数商品数据'));
		}
		$res = $order_create->payment($data);
		return $this->response($res);
	}
	
}