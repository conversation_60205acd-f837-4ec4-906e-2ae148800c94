<?php


namespace addon\cpcnpay\model;


use app\model\BaseModel;
use app\model\member\Member;
use app\model\system\Pay as PayCommon;
use app\model\system\Pay as PayModel;
use app\service\member\MemberService;
use EasyWeChatComposer\Laravel\ServiceProvider;
use payments\PayFactory;
use think\Exception;
use think\facade\Log;
use think\helper\Str;
use function EasyWeChat\Kernel\Support\str_random;

class Pay extends BaseModel
{
    private $is_weapp = 0;
    private $config = [];
    private $app = null;
    //支付渠道
    protected $pay_channel = [
        'weapp' => 'WECHATPAY',
        'wechat' => 'WECHATPAY',
    ];

    //支付渠道
    protected $pay_type = [
        'weapp' => '51',
        'h5' => '50',
        'wechat' => '50',
    ];

    //支付渠道
    protected $source_type = [
        'weapp' => 40,
        'h5' => 20,
        'wechat' => 30,
    ];

    protected $payType = 'cpcnpay';

    public function __construct($is_weapp = 0)
    {
        $this->is_weapp = $is_weapp;
        $config_model = new Config();
        $config = $config_model->getPayConfig();
        $this->config = $config;
    }

    public function pay($param)
    {

        $pay_channel = $this->pay_channel[$param['app_type']] ?? '';
        $pay_type = $this->pay_type[$param['app_type']] ?? '';

        if (empty($pay_channel)) {
            return $this->error([], '暂不支持该支付方式');
        }

        $mch_info = $param['mch_info'] ?? '';

        if (!empty($mch_info)) {
            $mch_info = json_decode($mch_info, true);

            if (isset($mch_info['payment'])) {
                if(isset($mch_info['payment']['QRAuthCode']) && is_string($mch_info['payment']['QRAuthCode']))
                    $mch_info['payment']['QRAuthCode'] = json_decode($mch_info['payment']['QRAuthCode'], true);
                $data = [
                    'pay_info' => $mch_info['payment']['QRAuthCode'],
                    'pay_type' => $this->payType
                ];

                //再次支付，直接返回支付对象
                return $this->success($data);
            }
        }

        //获取用户的open_id
        $openid = "";
        $member_model = new Member();

        switch ($param["trade_type"]){
            case 'JSAPI' :
                $openid = $param['openid'] ?: (new MemberService())->getMemberOpenid($param['member_id'], $this->is_weapp);
                break;
            case 'MWEB' :
                $openid = $param['openid'] ?: (new MemberService())->getMemberOpenid($param['member_id'], false);
                break;
        }

        if (empty($openid)) {
            return $this->error([], '支付失败');
        }
        $RedirectSource = $this->source_type[$param['app_type']];

        $params = [
            'InstitutionID'  => $this->config['InstitutionID'],
            'PayeeAccountNumber' => $this->config['PayeeAccountNumber'],
            'TxSN' => $param["out_trade_no"],
            'OrderNo' => $param["order_no"] ?? '',
            'PayerUserID' => $param["member_id"] ?? 0,
            'PayeeUserID' => $this->config['PayeeUserID'],
            'PaymentWay' => 80,
            'Amount' => $param["pay_money"] * 100,
            'ExpirePeriod' => '120m',
            'SourceTxTime' => $param["create_time"] ? date("Ymd", $param["create_time"]) : date("Ymd", time()),
            'PageURL' => '',
           // 'GoodsName' => mb_strlen($param['pay_detail'], "utf-8")>35 ? trim(mb_substr($param['pay_detail'],0,35)).'...' : trim($param['pay_detail']),
            'GoodsName' => str_replace("+", "", mb_strlen($param['pay_detail'], "utf-8")>35 ? trim(mb_substr($param['pay_detail'],0,35)).'...' : trim($param['pay_detail'])),
            'PlatformName' => 31,
            'RedirectSource' => $RedirectSource,
            'PayWay' => $pay_type,
            'PayType' => 31,
            'LimitPay' => 10,
            'SubAppID' => $this->config['MerchantAppID'],
            'SubOpenID' => $openid,
            'NoticeURL' => addon_url("pay/pay/cpcnpayNotify"),
            'HasSubsequentSplit' => 1,
            'DeductionSettlementFlag' => '0001',
        ];

        if($RedirectSource == 30)
            $params['PageURL'] = request()->domain()."/mini-h5/otherpages/order/weixin_receipt/weixin_receipt";

        $params['GoodsName'] = str_replace("【", " ", $params['GoodsName']);
        $params['GoodsName'] = str_replace("】", " ", $params['GoodsName']);
        $params['GoodsName'] = str_replace("*", "x", $params['GoodsName']);
        try
        {
            $xmlStr = $this->formatXML($params);
            $signature = $this->cfcasign_pkcs12(trim($xmlStr));

            $message = base64_encode(trim($xmlStr));
            $url = $this->config['Host']."/Gateway/InterfaceII";

            if($this->config['isDgEnv'] == "YES")
            {
                $response = $this->process_encrypt(trim($xmlStr),$signature,"SHA1withRSA", $this->config['InstitutionID'] , 1);
            }
            else
                $response = $this->cfcatx_transfer($url, $message,$signature);
            $plainText = trim(base64_decode($response[0]));
            Log::info('中金支付返回数据');
            Log::info($plainText);
            Log::info('提交数据');
            Log::info(trim($xmlStr));

            $retData = json_decode(json_encode(simplexml_load_string($plainText)), true);

            if(isset($retData['Head']) && isset($retData['Head']['Code']) && $retData['Head']['Code'] == '2000' && isset($retData['Body']) && $retData['Body']['Status'] == 20)
            {
                $payInfo = $retData['Body'];

                $pay_model = new PayModel();
                $pay_model->bindMchPay($param["out_trade_no"], [
                    'app_id'        => $params['SubAppID'],
                    'payment'       => $payInfo,
                    'expired_at'    => time() + 2*60*60        //2小时候过期
                ]);
                $data = [
                    'pay_info' => json_decode($payInfo['QRAuthCode'], true),
                    'pay_type' => $this->payType
                ];
                //成功处理
                model('pay')->update(['trade_no' => $payInfo['BankTraceNo']], ['out_trade_no' => $param["out_trade_no"]]);
                return $this->success($data);

            }
            else
            {
                Log::error('中金支付错误');
                Log::error($plainText);
                return $this->error([], '支付错误, 请联系管理员');
            }
        }
        catch (\Exception $e) {
            //失败处理
            Log::error('中金支付错误: ' . $e->getMessage());
            return $this->error([], '支付错误, 请联系管理员');
        }
    }


    public function formatXML(array $params)
    {
        $xmlData = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request version="2.0">
<Head>
<TxCode>5011</TxCode>
</Head>
<Body>
<InstitutionID/>
<TxSN/>
<OrderNo/>
<TerminalUserID/>
<PayerUserID/>
<PayeeUserID/>
<PayeeAccountNumber/>
<PaymentWay/>
<Amount/>
<ExpirePeriod/>
<SourceTxTime/>
<PageURL/>
<GoodsTag/>
<GoodsName/>
<PlatformName/>
<ClientIP/>
<HasSubsequentSplit/>
<NoticeURL/>
<Remark/>
<DeductionSettlementFlag/>
<Extension/>
</Body>
</Request>
XML;

        $simpleXML= new \SimpleXMLElement($xmlData);

        // 4.赋值
        $simpleXML->Body->InstitutionID = $params['InstitutionID'];
        $simpleXML->Body->TxSN = $params['TxSN'];
        $simpleXML->Body->OrderNo = $params['OrderNo'];
        $simpleXML->Body->TerminalUserID = $params['TerminalUserID'] ?? '';
        $simpleXML->Body->PayerUserID = $params['PayerUserID'];
        $simpleXML->Body->PayeeUserID = $params['PayeeUserID'];
        $simpleXML->Body->PaymentWay = $params['PaymentWay'];
        $simpleXML->Body->Amount = $params['Amount'];
        $simpleXML->Body->ExpirePeriod = $params['ExpirePeriod'];
        $simpleXML->Body->SourceTxTime = $params['SourceTxTime'];
        $simpleXML->Body->PageURL = $params['PageURL'];
        $simpleXML->Body->GoodsName = $params['GoodsName'];
        $simpleXML->Body->GoodsTag = $params['GoodsTag'] ?? '';
        $simpleXML->Body->NoticeURL= $params['NoticeURL'];
        $simpleXML->Body->PlatformName = $params['PlatformName'];
        $simpleXML->Body->ClientIP = $params['ClientIP'] ?? '';
        $simpleXML->Body->PayeeAccountNumber = $params['PayeeAccountNumber'];
        $simpleXML->Body->HasSubsequentSplit = $params['HasSubsequentSplit'];
        $simpleXML->Body->Remark = $params['Remark'] ?? '';
        $simpleXML->Body->DeductionSettlementFlag = $params['DeductionSettlementFlag'] ?? '';
        $simpleXML->Body->Extension = $params['Extension'] ?? '';


        $simpleXML->Body->addChild("RedirectPay");
        $simpleXML->Body->RedirectPay->addChild("RedirectSource",$params['RedirectSource']);
        $simpleXML->Body->RedirectPay->addChild("PayWay", $params['PayWay']);
        $simpleXML->Body->RedirectPay->addChild("PayType", $params['PayType']);
        $simpleXML->Body->RedirectPay->addChild("RedirectPayBankID", $params['RedirectPayBankID'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("LimitPay", $params['LimitPay']);
        $simpleXML->Body->RedirectPay->addChild("SubAppID", $params['SubAppID']);
        $simpleXML->Body->RedirectPay->addChild("SubOpenID", $params['SubOpenID']);
        $simpleXML->Body->RedirectPay->addChild("SubOpenID", $params['SubOpenID']);
        $simpleXML->Body->RedirectPay->addChild("InstallmentType", $params['InstallmentType'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("FeeMode", $params['FeeMode'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("BankAccountNumber", $params['BankAccountNumber'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("NumberOfInstallments", $params['NumberOfInstallments'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("MaskAccNo", $params['MaskAccNo'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("InstallmentForce", $params['InstallmentForce'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("PhoneNumber", $params['PhoneNumber'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("LoginState", $params['LoginState'] ?? '');
        $simpleXML->Body->RedirectPay->addChild("IdentityInfo", $params['IdentityInfo'] ?? '');

        return $simpleXML->asXML();
    }


    // 验签函数
    function cfcaverify($plainText,$signature){
        //国际证书
        $certFile = config_path()."cpcnpay/payment.cer";
        $fcert = fopen($certFile, "r");
        $cert = fread($fcert, 8192);
        fclose($fcert);
        $binary_signature = pack("H" . strlen($signature), $signature);
        $ok = openssl_verify($plainText, $binary_signature, $cert);//默认OPENSSL_ALGO_SHA1
        return $ok;
    }

    //验签
    public function validSign($array)
    {
        $sign =$array['sign'];
        unset($array['sign']);
        ksort($array);
        $bufSignSrc = $this->ToUrlParams($array);
        $public_key = $this->config['rsa_public_key'];
        $public_key = chunk_split($public_key , 64, "\n");
        $key = "-----BEGIN PUBLIC KEY-----\n$public_key-----END PUBLIC KEY-----\n";
        $result= openssl_verify($bufSignSrc,base64_decode($sign), $key );
        return $result;
    }

    public function payNotify()
    {
        $xmlStr = file_get_contents('php://input');
        Log::info('中金支付回调通知--------');
        Log::info($xmlStr);
        try {

            $requestData = input();
            if($requestData['isDgEnv'] == "YES")
            {
                $response = $this->process_decrypt($requestData['message'], $requestData['signature'], $requestData['signAlgorithm'], $requestData['signSN'], $requestData['encryptSN'], $requestData['digitalEnvelope']);
            }
            else
            {
                $plainText = trim(base64_decode($requestData['message']));
                $xmlData = json_decode(json_encode(simplexml_load_string($plainText)), true);

                if(isset($xmlData['Body']))
                {
                    $retData = $xmlData['Body'];
                    if(isset($retData['Status']) && $retData['Status'] == 30)
                    {
                        $pay_common = new PayCommon();
                        $result = $pay_common->onlinePay($retData['TxSN'], "cpcnpay", $retData['OrderNo'], "cpcnpay", $retData['BankTraceNo']);
                        Log::info('订单支付情况');
                        Log::info($result);

                        $response = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<Head>
<Code>2000</Code>
<Message>OK.</Message>
</Head>
</Response>
XML;

                        $responseXML= new \SimpleXMLElement($response);
                        echo base64_encode($responseXML->asXML());
                    }
                    else
                    {
                        Log::error("中金支付失败回调:");
                        Log::error(json_encode($retData));
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("中金支付回调错误: {$e->getMessage()}" .PHP_EOL.$e->getTraceAsString());
            echo "erro";
            exit();
        }
    }

    public function payIsNotOver($param)
    {
        $mch_info = $param['mch_info'] ?? '';
        if (!empty($mch_info)) {
            $mch_info = json_decode($mch_info, true);
            if (isset($mch_info['expired_at']) && $mch_info['expired_at'] > time()) {
                return $this->error([
                    'is_expired'    => true
                ], '订单支付超时');
            }
        }
        return $this->success();
    }

    public function refund($param)
    {
        Log::info('退款调试 ------');
        Log::info($param);

    }

    public function paymentReverse($param)
    {
        $xmlData = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request version="2.0">
<Head>
<TxCode>5021</TxCode>
</Head>
<Body>
<InstitutionID/>
<TxSN/>
<PaymentTxSN/>
<SourceTxTime/>
<RefundWay/>
<Amount/>
<CancelAmount/>
<NoticeURL/>
<Remark/>
</Body>
</Request>
XML;


        $simpleXML= new \SimpleXMLElement($xmlData);

        // 4.赋值
        $simpleXML->Head->InstitutionID = $this->config['InstitutionID'];
        $simpleXML->Body->TxSN = $param['refund_no'];
        $simpleXML->Body->PaymentTxSN = $param['pay_info']['out_trade_no'];
        $simpleXML->Body->SourceTxTime = '';
        $simpleXML->Body->RefundWay = 20;
        $simpleXML->Body->Amount = $param['reverse_amt'] * 100;;
        $simpleXML->Body->CancelAmount = '';

        $simpleXML->Body->Remark = '';

        $reverseNotify = addon_url('pay/pay/cpcnPayReverseNotify');

        if(env('APP_DEBUG') && strpos($reverseNotify, $_SERVER['HTTP_HOST']) !== false && strpos($reverseNotify, "8443") === false)
        {
            $reverseNotify = str_replace($_SERVER['HTTP_HOST'], $_SERVER['HTTP_HOST'].":8443", $reverseNotify);
        }
        $simpleXML->Body->NoticeURL = $reverseNotify;

        $xmlStr = $simpleXML->asXML();

        try
        {
            $signature = $this->cfcasign_pkcs12(trim($xmlStr));

            $message = base64_encode(trim($xmlStr));
            $url = $this->config['Host']."/Gateway/InterfaceII";
            $response = $this->cfcatx_transfer($url, $message,$signature);
            $plainText = trim(base64_decode($response[0]));
            Log::info('中金支付退款返回数据');
            Log::info($plainText);
            Log::info('中金支付退款提交数据');
            Log::info(trim($xmlStr));
           // $ok = $this->cfcaverify($plainText,$response[1]);
            $retData = json_decode(json_encode(simplexml_load_string($plainText)), true);

            if(isset($retData['Head']) && isset($retData['Head']['Code']) && $retData['Head']['Code'] == '2000')
            {
                $refundData = $retData['Body'];
                $tradeNo = $refundData['TxSN'];

                $pay = new \app\model\system\Pay();
                $event = $param['event'] ?? '';
                if ($event != 'pintuan') {
                    // 拼团不用
                    $pay->refundApplySuccess([
                        'refund_no' => $param['refund_no'],
                        'refund_trade_no' => $tradeNo
                    ]);
                }

                //成功处理
                return $this->success([
                    'refund_no' => $param['refund_no'],
                    'refund_trade_no' => $tradeNo
                ]);
            }
            else
            {
                Log::error('中金支付退款失败: ');
                Log::error($plainText);
                return $this->error('', '退款失败');
            }
        }
        catch (\Exception $e)
        {
            Log::error('中金支付退款失败: ' . $e->getMessage());
            return $this->error('', '退款失败');
        }
    }

    public function reverseNotify(array $params)
    {
        model('pay_reverse')->startTrans();
        $xmlStr = file_get_contents('php://input');
        Log::info('中金支付退款回调通知--------');
        Log::info($xmlStr);
        Log::info(input());


        try
        {

            $requestData = input();
            if($requestData['isDgEnv'] == "YES")
            {
                $response = $this->process_decrypt($requestData['message'], $requestData['signature'], $requestData['signAlgorithm'], $requestData['signSN'], $requestData['encryptSN'], $requestData['digitalEnvelope']);
            }
            else
            {
                $plainText = trim(base64_decode($requestData['message']));
                $xmlData = json_decode(json_encode(simplexml_load_string($plainText)), true);
                Log::info('中金支付退款回调数据--------');
                Log::info($xmlData);

                if(isset($xmlData['Body']))
                {
                    $retData = $xmlData['Body'];
                    if(isset($retData['Status']) && $retData['Status'] == 20)
                    {
                        $tradeNo = $retData['OrderNo'];
                        $outTradeNo = $retData['TxSN'];

                        //检测是否已经处理过
                        $reverseInfo = model('pay_reverse')->getInfo(['reverse_no' => $outTradeNo]);
                        if (!empty($reverseInfo['reverse_id']))
                        {
                            Log::info('支付撤销已处理: ');
                            throw new \Exception('支付撤销已处理', 200);
                        }

                        if ($reverseInfo['event'] == 'pintuan')
                        {
                            $upData['is_refund'] = 1;
                            $upData['edit_time'] = time();
                            $where['refund_no'] = $outTradeNo;
                            model('pintuan_order')->update($upData,$where);
                        } else
                        {
                            $data = [
                                'refund_no'     => $outTradeNo,
                                'refund_fee'    => $retData['Amount'],
                                'succeed_time'  => $retData['AcceptedSuccessTime'],
                                'created_time'  => $retData['ResponseTime'],
                                'charge_fee'    => 0
                            ];
                            $pay = new PayModel();
                            $pay->refundSuccessByRefundNo($data);
                        }
                        $payReverseUpdateData = [
                            'reverse_id' => $tradeNo,
                            'reverse_amt' => $retData['Amount'],
                            'reversed_amt' => $retData['Amount'],
                            'confirmed_amt' => $retData['Amount'],
                            'refunded_amt' => $retData['Amount'],
                            'reverse_obj' => $xmlStr,
                            'status' => 2,
                            'update_time' => time(),
                            'succeed_time' => time()
                        ];

                        model('pay_reverse')->update($payReverseUpdateData, ['reverse_no' => $outTradeNo, 'status' => 1]);
                        model('pay_reverse')->commit();

                        $response = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<Head>
<Code>2000</Code>
<Message>OK.</Message>
</Head>
</Response>
XML;

                        $responseXML= new \SimpleXMLElement($response);
                        echo base64_encode($responseXML->asXML());
                    }
                    else
                    {
                        Log::error("中金支付失败回调:");
                        Log::error(json_encode($retData));
                    }
                }
            }


        }
        catch (\Exception $e)
        {
            Log::error("中金支付失败回调:".$e->getMessage());
            Log::error("中金支付失败回调:".PHP_EOL.$e->getMessage().PHP_EOL.$e->getTraceAsString());
        }

    }

    public function cfcasign_pkcs12($plainText)
    {
        //初始化so库扩展
        $nResult = 0;
        $strLogCofigFilePath = config_path()."cpcnpay/cfcalog.conf";
        /*
        扩展初始化。调用本函数库中其它函数之前调用 cfca_initialize()。
        如果需要在多线程环境下调用此函数库中的函数，cfca_initialize()需要在开启多线程之前调用。
        此函数只需要调用一次。
        */
        $nResult = \cfca_initialize($strLogCofigFilePath);
        if (0 != $nResult) {
            throw new Exception("\n cfca_Initialize error:".$nResult."\n");
        }
        //echo "cfca_initialize:nResult:".$nResult."\n\n";
        //国密证书
        $strSignAlg = "SM2";

        /*
        此处需要修改，国密证书私钥（sm2后缀）路径
        */
        $strPfxFilePath = config_path()."cpcnpay/private_key.sm2";
        $strPfxPassword = $this->config['PfxPassword'];//证书密码
        $strHashAlg = "SM3";
        //  签名函数

        try{
            $strMsgPKCS7DetachedSignature="";
            $result = \cfca_signData_PKCS1($strSignAlg, $plainText, $strPfxFilePath,$strPfxPassword, $strHashAlg,$strMsgPKCS7DetachedSignature);
            if (0 != $nResult) {
                throw new Exception("\n cfca_signData_PKCS7Detached error:".$nResult."\n");
            }
            Log::info("签名数据：".$strMsgPKCS7DetachedSignature);
            $signature_bin=base64_decode($strMsgPKCS7DetachedSignature);
            $signature_hex=bin2hex($signature_bin);
            /*
            调用本函数库中其它函数之后调用cfca_uninitialize()。
            如果需要在多线程环境下调用此函数库中的函数，cfca_uninitialize()需要在多线程结束之后调用。
            此函数只需要调用一次。
            */
            $nResult = \cfca_uninitialize();
            if (0 != $nResult) {
                throw new \Exception("\n cfca_uninitialize error:".$nResult."\n");
            }
            //echo "cfca_uninitialize-nResult:".$nResult."\n";


        }
        catch(\Exception $e)
        {
            Log::info("证书异常".$e->getMessage());
            \cfca_uninitialize();
        }
        Log::info("signature_hex:::".$signature_hex);
        return $signature_hex;
    }

    //同步交易方式向支付平台发送请求，支付平台返回一个数组，其中第一个元素为message，第二个为signature。注意这两个参数为支付平台返回。
    function cfcatx_transfer($url, $message,$signature){
        $post_data = array();
        $post_data['message'] = $message;
        $post_data['signature'] = $signature;

        $response= $this->request($url,$this->data_encode($post_data) );
        $response=trim($response);

        return explode(",",$response);
    }

    //提交数据前要进行一下urlencode转换
    function data_encode($data, $keyprefix = "", $keypostfix = "") {
        assert( is_array($data) );
        $vars=null;
        foreach($data as $key=>$value) {
            if(is_array($value)) $vars .= $this->data_encode($value, $keyprefix.$key.$keypostfix.urlencode("["), urlencode("]"));
            else $vars .= $keyprefix.$key.$keypostfix."=".urlencode($value)."&";
        }
        return $vars;
    }

    //发送数据
    function request($url, $curl_data)
    {
        $options = array(
            CURLOPT_RETURNTRANSFER => true,         // return web page
            CURLOPT_HEADER         => false,        // don't return headers
            CURLOPT_FOLLOWLOCATION => true,         // follow redirects
            CURLOPT_ENCODING       => "",           // handle all encodings
            CURLOPT_USERAGENT      => "institution",     // who am i
            CURLOPT_AUTOREFERER    => true,         // set referer on redirect
            CURLOPT_CONNECTTIMEOUT => 120,          // timeout on connect
            CURLOPT_TIMEOUT        => 120,          // timeout on response
            CURLOPT_MAXREDIRS      => 10,           // stop after 10 redirects
            CURLOPT_POST            => 1,            // i am sending post data
            CURLOPT_POSTFIELDS     => $curl_data,    // this are my post vars
            CURLOPT_SSL_VERIFYHOST => 0,            // don't verify ssl
            CURLOPT_SSL_VERIFYPEER => false,        //
            CURLOPT_VERBOSE        => 1                //
        );

        $ch = curl_init($url);
        //$ch      = curl_init("https://test.cpcn.com.cn/Gateway/InterfaceII");
        curl_setopt_array($ch,$options);
        curl_setopt($ch,CURLOPT_HTTPHEADER,array("Expect:"));
        $content = curl_exec($ch);
        curl_close($ch);
        return $content;

    }

     //解密中金返回参数
    public function process_decrypt($message,$signature,$signAlgorithm,$signSN,$encryptSN,$digitalEnvelope){
        //解密对称秘钥
        $ResponseRandomKeyData_pri = $this->getDecryptKeyByteByRSA($digitalEnvelope);

        $arr_ResponseRandomKeyData=explode('|',$ResponseRandomKeyData_pri);
        $ResponseRandomKeyData = $arr_ResponseRandomKeyData[1];
        //非对称解密获取报文明文
        $plainText = trim($this->decode($message,$ResponseRandomKeyData));

        return $plainText;
    }

    //RSA非对称解密
    public function getDecryptKeyByteByRSA($signData){
        $plainDate = '';
        $p12cert = array();
        $file = config_path().'cpcnpay/test.pfx';
        $fd = fopen($file, 'r');
        $p12buf = fread($fd, filesize($file));
        fclose($fd);
        openssl_pkcs12_read($p12buf, $p12cert, 'cfca1234');
        $pkeyid = $p12cert['pkey'];

        openssl_private_decrypt(base64_decode($signData), $plainDate, $pkeyid);
        return $plainDate;
    }

    /**
     * [decode aes256解密]
     * @param  [type] $encryptedMessage [报文密文responseMessage]
     * @param  [type] $key              [32位大写十六进制字符串]
     * @return [type]                   [报文明文]
     */
    public function decode($encryptedMessage,$key){

        return openssl_decrypt(base64_decode($encryptedMessage), 'aes-128-ecb', $key,1,substr(0,16));
    }

    //数字信封数据封装
    /*
    [message] 对称加密message
    [signature] 数据签名
    [isDgEnv] 是否是数字信封发送
    [signAlgorithm] 签名算法
    [signSN] 签名证书序列号
    [encryptSN] 加密证书序列号
    [digitalEnvelope] 数字信封
    [institutionID] 机构号
    */
    public function process_encrypt($xmlStr,$signature,$signAlgorithm,$institutionID,$url_flag){
        //生成随机字符串
        Log::info("数字信封数据");
        $randomKeyData = DigitalEnvelopeUtils::randomHexString();
        Log::info("randomKeyData={$randomKeyData}");
        //用对称秘钥加密数据message
        $message= DigitalEnvelopeUtils::encode($xmlStr,$randomKeyData);
        Log::info("message={$message}");
        //生成数字信封digitalEnvelope
        $digitalEnvelope = DigitalEnvelopeUtils::encryptByRSA($randomKeyData);
        Log::info("digitalEnvelope={$digitalEnvelope}");
        //获取到签名证书序列号
        $signSN = DigitalEnvelopeUtils::getSignSN($this->config['PfxPassword']);
        Log::info("signSN={$signSN}");
        //获取中金证书序列号
        $encryptSN = DigitalEnvelopeUtils::getEncryptSN();
        Log::info("encryptSN={$encryptSN}");

        //发送数据
        $response = $this->cfcatx_transfer_dig($message,$signature,"YES",$signAlgorithm,$signSN,$encryptSN,$digitalEnvelope,$institutionID,$url_flag);
        return $response;

    }


    //发送数据到中金并获取返回参数
    function cfcatx_transfer_dig($message,$signature,$isDgEnv,$signAlgorithm,$signSN,$encryptSN,$digitalEnvelope,$institutionID,$url_flag){
        $post_data = array();
        $post_data['message'] = $message;
        $post_data['signature'] = $signature;
        $post_data['isDgEnv'] = $isDgEnv;
        $post_data['signAlgorithm'] = $signAlgorithm;
        $post_data['signSN'] = $signSN;
        $post_data['encryptSN'] = $encryptSN;
        $post_data['digitalEnvelope'] = $digitalEnvelope;
        $post_data['institutionID'] = $institutionID;

        //var_dump($post_data);
        $responseArray= dig_https(data_encode($post_data),$url_flag);
        $header=$responseArray["header"];
        $body=$responseArray["body"];
        //将body拆分
        $bdoymessage=explode(",",$body);
        //将header拆分为数组
        $headArr = explode("\r\n", $header);
        //遍历获取header中所需参数
        foreach ($headArr as $loop) {
            if(strpos($loop, "isDgEnv") !== false){
                $isDgEnv = trim(substr($loop, 8));
            }
            if(strpos($loop, "signAlgorithm") !== false){
                $signAlgorithm = trim(substr($loop, 14));
            }
            if(strpos($loop, "signSN") !== false){
                $signSN = trim(substr($loop, 7));
            }
            if(strpos($loop, "encryptSN") !== false){
                $encryptSN = trim(substr($loop, 10));
            }
            if(strpos($loop, "digitalEnvelope") !== false){
                $digitalEnvelope = trim(substr($loop, 16));
            }
        }
        //将获取到的参数封装到数组中
        $responseArray = array(
            'message' => $bdoymessage[0],
            'signature' => $bdoymessage[1],
            'isDgEnv' => $isDgEnv,
            'signAlgorithm' => $signAlgorithm,
            'signSN' => $signSN,
            'encryptSN' => $encryptSN,
            'digitalEnvelope' => $digitalEnvelope,
        );

        return $responseArray;

    }
}