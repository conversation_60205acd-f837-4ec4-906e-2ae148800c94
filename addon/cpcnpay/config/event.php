<?php
// 事件定义文件
return [
    'bind'      => [

    ],

    'listen'    => [
        //支付异步回调
        'CpcnpayNotify' => [
            'addon\cpcnpay\event\PayNotify'
        ],
        //支付异步回调
        'PayNotify' => [
            'addon\cpcnpay\event\PayNotify'
        ],
        //支付方式，后台查询
        'PayType' => [
            'addon\cpcnpay\event\PayType'
        ],
        //支付，前台应用
        'Pay' => [
            'addon\cpcnpay\event\Pay'
        ],
        'PayClose' => [
            'addon\cpcnpay\event\PayClose'
        ],
        'PayRefund' => [
            'addon\cpcnpay\event\PayRefund'
        ],
        'PayTransfer' => [
            'addon\cpcnpay\event\PayTransfer'
        ],
        'TransferType' => [
            'addon\cpcnpay\event\TransferType'
        ],
        //退款异步回调
        'PayRefundNotify'   => [
            'addon\cpcnpay\event\PayRefundNotify'
        ],
        //支付超时
        'PayIsOver'     => [
            'addon\cpcnpay\event\PayIsOver'
        ],
    ],

    'subscribe' => [
    ],
];

