{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.reduction-rule{display: flex;flex-wrap: wrap;}
</style>
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>满减活动列表展示商品的满减活动相关信息</li>
			<li>可根据满减活动名称搜索出具体活动信息</li>
			<li>点击详情按钮，查看活动详细信息</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="manjian_name" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<div class="layui-tab ns-table-tab"  lay-filter="manjian_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">所有活动</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
		<li lay-id="-1">已关闭</li>
		<li lay-id="0">未开始</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="activity_list" lay-filter="activity_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="detail">详情</a>
		{{#  if(d.status == 1) {  }}
		<a class="layui-btn" lay-event="close">关闭</a><br />
		{{#  }else {  }}
		<a class="layui-btn" lay-event="delete">删除</a><br />
		{{#  }  }}
	</div>
</script>

<!-- 满减规则 -->
<script type="text/html" id="reductionRule">
	{{#  var rule = JSON.parse(d.rule_json);  }}
	{{#  for (var key in rule) {  }}
	<p class="reduction-rule">单笔订单满<span class="ns-text-color-red money-num">{{rule[key].money}}</span>元</p>
	<p class="reduction-rule">立减<span class="ns-text-color-red discount_money-num">{{rule[key].discount_money}}</span>元</p>
	{{#  }  }}
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{foreach $manjian_status_arr as $manjian_status_k => $manjian_status_v}
		{{#  if(d.status == {$manjian_status_k}){  }}
			{$manjian_status_v}
		{{#  }  }}
	{/foreach}
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'table', 'laytpl', 'element'], function() {
		var table,
			form = layui.form,
			laytpl = layui.laytpl,
			element = layui.element,
			repeat_flag = false; //防重复标识
		form.render();

		//监听Tab切换，以改变地址hash值
		element.on('tab(manjian_tab)', function(){
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status':this.getAttribute('lay-id')
				}
			});
		});

		table = new Table({
			elem: '#activity_list',
			url: ns.url("manjian://admin/manjian/lists"),
			cols: [
				[{
					field: 'manjian_name',
					title: '活动名称',
					unresize: 'false',
					width: '15%'
				},{
					title: '满减规则',
					unresize: 'false',
					width: '15%',
					templet: '#reductionRule'
				},	{
					field: 'site_name',
					title: '商家名称',
					unresize: 'false',
					width: '18%'
				}, {
					field: 'start_time',
					title: '开始时间',
					unresize: 'false',
					width: '16%',
					templet: function(data) {
						return ns.time_to_date(data.start_time);
					}
				}, {
					field: 'end_time',
					title: '结束时间',
					unresize: 'false',
					width: '16%',
					templet: function(data) {
						return ns.time_to_date(data.end_time);
					}
				},
				{
					field: 'status',
					title: '状态',
					unresize: 'false',
					width: '8%',
					templet: '#status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '12%'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'detail': //详情
                    location.href = ns.url("manjian://admin/manjian/detail", {"manjian_id": data.manjian_id,"site_id" : data.site_id});
//					detailManjian(data);
					break;
				case 'delete': //删除
					deleteManjian(data.manjian_id,data.site_id);
					break;
				case 'close': //关闭
					close(data.manjian_id,data.site_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteManjian(manjian_id,site_id) {
			if (repeat_flag) return false;
			repeat_flag = true;
		
			layer.confirm('确定要删除吗?', function() {
				$.ajax({
					url: ns.url("manjian://admin/manjian/delete"),
					data: {manjian_id,site_id},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
		
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
		
		/**
		 * 关闭
		 */
		function close(manjian_id,site_id) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			layer.confirm('确定关闭吗?', function() {
				$.ajax({
					url: ns.url("manjian://admin/manjian/close"),
					data: {manjian_id,site_id},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
					
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
		
		/**
		 * 详情
		 */
		function detailManjian(data) {
			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("manjian://admin/manjian/detail"),
				data: {'manjian_id': data.manjian_id},
				success: function (res) {
					laytpl($("#detail").html()).render(res.data, function(html) {
						layer.open({
							title: '活动详情',
							skin: 'layer-tips-class',
							type: 1,
							area: ['450px'],
							content: html
						});
					});
				}
			});
		};
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
	});
</script>

<!-- 详情弹框html -->
<script type="text/html" id="detail">
	<table class="layui-table ns-table-detail">
		<colgroup>
			<col width="120">
			<col width="270">
		</colgroup>
		
		<tbody>
			<tr>
				<td>活动名称</td>
				<td>{{d.manjian_name}}</td>
			</tr>
			<tr>
				<td>开始时间</td>
				<td>{{ ns.time_to_date(d.start_time, "YYYY-MM-DD hh:mm:ss") }}</td>
			</tr>
			<tr>
				<td>结束时间</td>
				<td>{{ ns.time_to_date(d.end_time, "YYYY-MM-DD hh:mm:ss") }}</td>
			</tr>
			<tr>
				<td id="rule_name">满减规则</td>
				<td id="rule">
					{{#  var rule = JSON.parse(d.rule_json);  }}
					{{#  for (var key in rule) {  }}
					<p>单笔订单满<span class="ns-text-color-red money-num">{{rule[key].money}}</span>元，立减现金<span class="ns-text-color-red discount_money-num">{{rule[key].discount_money}}</span>元</p></span>
					{{#  }  }}
				</td>
			</tr>
			<tr>
				<td>备注</td>
				<td id="remark">{{d.remark}}</td>
			</tr>
		</tbody>
	</table>
</script>
{/block}