<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+
declare (strict_types = 1);

namespace addon\unionpay\event;

use addon\unionpay\model\Pay as PayModel;
/**
 * 原路退款
 */
class PayReverseNotify
{
    /**
     * 原路退款
     */
    public function handle(array $params)
    {
        try {
            $pay_model = new PayModel();
            return $pay_model->reverseNotify($params);
        } catch (\Exception $e) {
            return '';
        }
    }
}
