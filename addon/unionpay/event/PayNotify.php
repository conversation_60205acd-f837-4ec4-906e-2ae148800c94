<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+
declare (strict_types=1);

namespace addon\unionpay\event;

use addon\unionpay\model\Pay as PayModel;
use think\facade\Log;

/**
 * 支付回调
 */
class PayNotify
{
    /**
     * 支付方式及配置
     */
    public function handle($param = [])
    {
        try {

            $pay_model = new PayModel();
            $pay_model->payNotify();
        } catch (\Exception $e) {
            Log::error('通联支付回调错误:'.$e->getMessage());
            Log::error($e->getFile().':'.$e->getLine());
            return 'error';
        }
    }
}