<?php


namespace addon\unionpay\event;

use addon\unionpay\model\Pay as PayModel;

class Pay
{
    /**
     * 支付
     */
    public function handle($params)
    {

        if($params["pay_type"] == "unionpay"){

            $app_type = $params['app_type'];
            $is_weapp = 0;
            $trade_type = '';
            switch ($app_type){
                case 'h5' :
                    $trade_type = "MWEB";
                    break;
                case 'wechat' :
                    $trade_type = "JSAPI";
                    break;
                case 'weapp' :
                    $is_weapp = 1;
                    $trade_type = "JSAPI";
                    break;
            }
            $params["trade_type"] = $trade_type;
            $pay_model = new PayModel($is_weapp);
            $result = $pay_model->pay($params);
            return $result;
        }
    }
}