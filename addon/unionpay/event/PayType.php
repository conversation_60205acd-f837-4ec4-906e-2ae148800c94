<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+
declare (strict_types = 1);

namespace addon\unionpay\event;

use addon\newpay\model\Config;
use think\facade\Log;

/**
 * 支付方式  (前后台调用)
 */
class PayType
{
	/**
	 * 支付方式及配置
	 */
	public function handle($params)
	{
	    $app_type = isset($params['app_type']) ? $params['app_type'] : '';
	    if(!empty($app_type))
	    {
              $config_model = new Config();
              $app_type_array = $config_model->app_type;
              if(!in_array(strtolower($app_type), $app_type_array))
              {
                  return '';
              }
              $config_result = $config_model->getPayConfig();
              $config = $config_result["data"]["value"] ?? [];
              $pay_status = $config["pay_status"] ?? 0;
              if($pay_status == 0){
                  return '';
              }
	    }
          $info = array(
              "pay_type" => "unionpay",
              "pay_type_name" => "通联支付",
              "edit_url" => "unionpay://admin/pay/config",
              "logo" => "addon/unionpay/icon.png",
              "desc" => "通联支付"
          );
          return $info;

	}
}