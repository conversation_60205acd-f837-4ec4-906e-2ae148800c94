<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+
declare (strict_types = 1);

namespace addon\unionpay\event;

use addon\unionpay\model\Pay as PayModel;
/**
 * 原路退款
 */
class PayRefundNotify
{
    /**
     * 原路退款
     */
    public function handle()
    {
        try {
            $pay_model = new PayModel();
            $pay_model->payRefundNotify();
        } catch (\Exception $e) {
            return '';
        }
    }
}
