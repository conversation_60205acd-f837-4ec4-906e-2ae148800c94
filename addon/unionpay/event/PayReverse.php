<?php
// +---------------------------------------------------------------------+
// | NiuCloud | [ WE CAN DO IT JUST NiuCloud ]                |
// +---------------------------------------------------------------------+
// | Copy right 2019-2029 www.niucloud.com                          |
// +---------------------------------------------------------------------+
// | Author | NiuCloud <<EMAIL>>                       |
// +---------------------------------------------------------------------+
// | Repository | https://github.com/niucloud/framework.git          |
// +---------------------------------------------------------------------+
declare (strict_types = 1);

namespace addon\unionpay\event;

use addon\unionpay\model\Pay as PayModel;
use think\facade\Log;

/**
 * 原路退款
 */
class PayReverse
{
    /**
     * 原路退款
     */
    public function handle($params = [])
    {
        if($params["pay_info"]["pay_type"] == "unionpay"){
            $pay_model = new PayModel();
            $result = $pay_model->paymentReverse($params);
            return $result;
        }
    }
}