<?php


namespace addon\unionpay\model;


use app\model\BaseModel;
use app\model\member\Member;
use app\model\system\Pay as PayCommon;
use app\model\system\Pay as PayModel;
use app\service\member\MemberService;
use payments\PayFactory;
use think\facade\Log;
use think\helper\Str;
use function EasyWeChat\Kernel\Support\str_random;

class Pay extends BaseModel
{
    private $is_weapp = 0;
    private $config = [];
    private $app = null;
    //支付渠道
    protected $pay_channel = [
        'weapp' => 'WECHATPAY',
        'wechat' => 'WECHATPAY',
    ];

    //支付渠道
    protected $pay_type = [
        'weapp' => 'W06',
        'h5' => 'W02',
        'wechat' => 'W02',
    ];

    protected $payType = 'unionpay';

    public function __construct($is_weapp = 0)
    {
        $this->is_weapp = $is_weapp;
        $config_model = new Config();
        $config = $config_model->getPayConfig();
        $this->config = $config;
    }

    public function pay($param)
    {

        $pay_channel = $this->pay_channel[$param['app_type']] ?? '';
        $pay_type = $this->pay_type[$param['app_type']] ?? '';

        if (empty($pay_channel)) {
            return $this->error([], '暂不支持该支付方式');
        }

        $mch_info = $param['mch_info'] ?? '';

        if (!empty($mch_info)) {
            $mch_info = json_decode($mch_info, true);

            if (isset($mch_info['payment'])) {
                if(isset($mch_info['payment']['payinfo']) && is_string($mch_info['payment']['payinfo']))
                    $mch_info['payment']['payinfo'] = json_decode($mch_info['payment']['payinfo'], true);
                $data = [
                    'pay_info' => $mch_info['payment']['payinfo'],
                    'pay_type' => $this->payType
                ];

                //再次支付，直接返回支付对象
                return $this->success($data);
            }
        }

        //获取用户的open_id
        $openid = "";
        $member_model = new Member();

        switch ($param["trade_type"]){
            case 'JSAPI' :
                $openid = $param['openid'] ?: (new MemberService())->getMemberOpenid($param['member_id'], $this->is_weapp);
                break;
            case 'NATIVE' :
                break;
            case 'MWEB' :
                $openid = $param['openid'] ?: (new MemberService())->getMemberOpenid($param['member_id'], false);
                break;
            case 'APP' :
                break;
        }

        if (empty($openid)) {
            return $this->error([], '支付失败');
        }

        $params = [
            'cusid'             => $this->config['cusid'],
            'appid'            => $this->config['app_id'],
            'trxamt'           => $param["pay_money"] * 100,
            'reqsn'           => $param["out_trade_no"],
            'paytype'          => $pay_type,
            'randomstr'     => Str::random(16),
            'body'     => mb_strlen($param['pay_detail'], "utf-8")>35 ? trim(mb_substr($param['pay_detail'],0,35)).'...' : trim($param['pay_detail']),
            'validtime'     => 1440,
            'acct'          => $openid,
            'notify_url'     => addon_url("pay/pay/unionPayNotify"),
            //'front_url'     => $param['app_type'] == 'h5' ? addon_url("pay/pay/newpayreturn", ['out_trade_no' => $param['out_trade_no'], 'app_type' => $param['app_type']]) : "",
            //'notify_url'     => "https://suppliershop.liancaiwang.cn/api/public/index.php/h5/pay/payApply",
            //'front_url'     => "https://suppliershop.liancaiwang.cn/api/public/index.php/h5/pay/payApply",
            'sub_appid'     => $this->config['sub_appid'],
            'signtype'      => 'RSA',

        ];
        // /.. 会触发通联风控检测，遍历目录
        $params['body'] = str_replace('/..', '..', $params['body']);
        if($pay_type == 'W02')
            $params['front_url'] = request()->domain()."/mini-h5/otherpages/order/weixin_receipt/weixin_receipt";

        $sign = urlencode($this->Sign($params));//签名

        foreach ($params as $i=>$p)
            $params[$i] = urlencode($p);

        $params["sign"] = $sign;

        $paramsStr = $this->ToUrlParams($params);
        $url = $this->config['host'].'/apiweb/unitorder/pay';

        try
        {
            $rsp = $this->request($url, $paramsStr);
            Log::info('通联支付结果: ' . $rsp);
            Log::info('通联支付提交参数: ' . $paramsStr);

            $rspArray = json_decode($rsp, true);
            $wxPayInfo = $rspArray['payinfo'];
            if(is_string($wxPayInfo))
                $wxPayInfo = json_decode($wxPayInfo, true);

            $pay_model = new PayModel();
            $pay_model->bindMchPay($param["out_trade_no"], [
                'app_id'        => $this->config['app_id'],
                'payment'       => $rspArray,
                'expired_at'    => time() + 24*60*60        //24小时候过期
            ]);
            $data = [
                'pay_info' => $wxPayInfo,
                'pay_type' => $this->payType
            ];
            //成功处理
            model('pay')->update(['trade_no' => $rspArray['reqsn']], ['out_trade_no' => $param["out_trade_no"]]);
            return $this->success($data);
        }
        catch (\Exception $e) {
            //失败处理
            Log::error('通联支付错误: ' . $e->getMessage());
            return $this->error([], '支付错误, 请联系管理员');
        }
    }



    //验签
    public function validSign($array)
    {
        $sign =$array['sign'];
        unset($array['sign']);
        ksort($array);
        $bufSignSrc = $this->ToUrlParams($array);
        $public_key = $this->config['rsa_public_key'];
        $public_key = chunk_split($public_key , 64, "\n");
        $key = "-----BEGIN PUBLIC KEY-----\n$public_key-----END PUBLIC KEY-----\n";
        $result= openssl_verify($bufSignSrc,base64_decode($sign), $key );
        return $result;
    }

    //RSA签名
    public function  Sign(array $array){
        ksort($array);
        $bufSignSrc = $this->ToUrlParams($array);
        $private_key = $this->config['rsa_private_key'];
        $private_key = chunk_split($private_key , 64, "\n");

        $key = "-----BEGIN RSA PRIVATE KEY-----\n".wordwrap($private_key)."-----END RSA PRIVATE KEY-----";
        //   echo $key;
        if(openssl_sign($bufSignSrc, $signature, $key )){
            //		echo 'sign success';
        }else{
            echo 'sign fail';
        }

        $sign = base64_encode($signature);//加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的
//echo $sign;
// echo $signature,"\n";
        return $sign;

    }

    public function ToUrlParams(array $array)
    {
        $buff = "";
        foreach ($array as $k => $v)
        {
            if($v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }

        $buff = trim($buff, "&");
        return $buff;
    }

    //发送请求操作仅供参考,不为最佳实践
    public function request($url,$params)
    {
        $ch = curl_init();
        $this_header = array("content-type: application/x-www-form-urlencoded;charset=UTF-8");
        curl_setopt($ch,CURLOPT_HTTPHEADER,$this_header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);//如果不加验证,就设false,商户自行处理
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        $output = curl_exec($ch);
        curl_close($ch);
        return  $output;
    }


    public function close()
    {
        Log::info('关闭订单调试--');

    }

    public function payNotify()
    {
        Log::info('通联支付回调通知--------');
        Log::info('通联支付回调参数'.json_encode($_POST));
        try {

            $params = array();
            foreach($_POST as $key=>$val) {//动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容由于收银宝加字段而引起的签名异常
                $params[$key] = $val;
            }
            if(count($params)<1){//如果参数为空,则不进行处理
                echo "error";
                exit();
            }
            //if($this->ValidSign($params)){//验签成功
                $pay_common = new PayCommon();
                $result = $pay_common->onlinePay($params['cusorderid'], "unionpay", $params['trxid'], "unionpay", $params['chnltrxid']);
                Log::info('订单支付情况');
                Log::info($result);

                echo "success";
           /* }
            else{
                echo "erro";
                $paramsStr = json_encode($params);
                Log::error("验签失败:{$paramsStr}");
                exit();
            }*/

        } catch (\Exception $e) {
            Log::error("通联支付回调错误: {$e->getMessage()}" .PHP_EOL.$e->getTraceAsString());
            echo "erro";
            exit();
        }
    }

    public function payIsNotOver($param)
    {
        $mch_info = $param['mch_info'] ?? '';
        if (!empty($mch_info)) {
            $mch_info = json_decode($mch_info, true);
            if (isset($mch_info['expired_at']) && $mch_info['expired_at'] > time()) {
                return $this->error([
                    'is_expired'    => true
                ], '订单支付超时');
            }
        }
        return $this->success();
    }

    public function refund($param)
    {
        Log::info('退款调试 ------');
        Log::info($param);

    }

    public function paymentReverse($param)
    {
        $order_sign = $param['order_sign'];
        $params = array();
        $params["cusid"] = $this->config['cusid'];
        $params["appid"] = $this->config['app_id'];
        $params["trxamt"] = $param['reverse_amt'] * 100;
        $params["reqsn"] = $param['refund_no'];
        $params["oldreqsn"] = $param['pay_info']['out_trade_no'];//原来订单号
        $params["randomstr"] = str_random(16);//
        $params["signtype"] ='RSA';
        $params["sign"] = urlencode($this->Sign($params));//签名
        $paramsStr = $this->ToUrlParams($params);
        $url = $this->config['host'].'/apiweb/tranx/refund';
        $rsp = $this->request($url, $paramsStr);
        Log::info('通联支付退款提交参数 ------');
        Log::info($paramsStr);
        Log::info('通联支付退款结果 ------');
        Log::info($rsp);
        $rspArray = json_decode($rsp, true);

        $tradeNo = $rspArray['trxid'];

        $pay = new \app\model\system\Pay();
        $event = $param['event'] ?? '';
        if ($event != 'pintuan') {
            // 拼团不用
            $pay->refundApplySuccess([
                'refund_no' => $param['refund_no'],
                'refund_trade_no' => $tradeNo
            ]);
        }

        if(isset($rspArray['trxstatus']) && $rspArray['trxstatus'] === '0000')
        {
            $data['rspArray'] = $rspArray;
            $data['refund_fee'] = $param['pay_info']['pay_money'];
            $data['order_sign'] = $order_sign;
            session('unionpayReverseRes',  $data);

            //成功处理
            return $this->success([
                'refund_no' => $param['refund_no'],
                'refund_trade_no' => $rspArray['trxid']
            ]);
        }
        else
        {
            $errmsg = $rspArray['errmsg'] ?? "";
            return $this->error([], "退款失败:{$errmsg}");
        }


/*        if(AppUtil::validSign($rspArray)){
            echo "验签正确,进行业务处理";
        }*/
    }

    public function reverseNotify(array $params)
    {
        $result = $params['rspArray'];
        $order_sign = $params['order_sign'];
        $refund_fee = $params['refund_fee'];

        model('pay_reverse')->startTrans();
        try {
            $outTradeNo = $result['reqsn'];
            $tradeNo = $result['trxid'];

            //检测是否已经处理过
            $reverseInfo = model('pay_reverse')->getInfo(['reverse_no' => $outTradeNo]);
            if (!empty($reverseInfo['reverse_id'])) {
                Log::info('支付撤销已处理: ');
                Log::info(json_encode($result));
                throw new \Exception('支付撤销已处理', 200);
            }

            if ($order_sign == 'pintuan') {
                $upData['is_refund'] = 1;
                $upData['edit_time'] = time();
                $where['refund_no'] = $outTradeNo;
                model('pintuan_order')->update($upData,$where);
            } else {
                $data = [
                    'refund_no'     => $outTradeNo,
                ];

                $pay = new PayModel();
                $pay->refundSuccessByRefundNo($data);
            }

            $payReverseUpdateData = [
                'reverse_id' => $tradeNo,
                'reverse_amt' => $refund_fee,
                'reversed_amt' => $refund_fee,
                'confirmed_amt' => $refund_fee,
                'refunded_amt' => $refund_fee,
                'reverse_obj' => json_encode($result),
                'status' => 2,
                'update_time' => time(),
                'succeed_time' => strtotime($result['fintime'])
            ];

            model('pay_reverse')->update($payReverseUpdateData, ['reverse_no' => $outTradeNo, 'status' => 1]);
            model('pay_reverse')->commit();
            return '200';
        } catch (\Exception $e) {

            Log::info('通联支付撤销异步回调处理异常:');
            Log::info($e->getMessage());
            Log::info($e->getTraceAsString());
            model('pay_reverse')->rollback();
            return 'fail';
        }
    }
}