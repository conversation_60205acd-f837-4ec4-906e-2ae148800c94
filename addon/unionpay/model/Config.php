<?php


namespace addon\unionpay\model;


use app\model\system\Config as ConfigModel;

class Config
{
    /**
     * 支持端口
     * @var unknown
     */
    public $app_type = ['h5', 'wechat', 'weapp'];

    /**
     * 设置支付配置
     * array $data
     */
    public function setPayConfig($data)
    {
        $config = new ConfigModel();
        $res = $config->setConfig($data, '通联支付配置', 1, [['site_id', '=', 0], ['app_module', '=', 'admin'], ['config_key', '=', 'UNION_PAY_CONFIG']]);
        return $res;
    }

    /**
     * 获取支付配置
     */
    public function getPayConfig()
    {
        $config = new ConfigModel();
        $res = $config->getConfig([['site_id', '=', 0], ['app_module', '=', 'admin'], ['config_key', '=', 'UNION_PAY_CONFIG']]);

        $info = $res["data"] ?? [];
        $infoValue = is_array($info) && isset($info['value']) ? $info['value'] : [];

        return $infoValue;
    }
}