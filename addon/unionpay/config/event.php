<?php
// 事件定义文件
return [
    'bind'      => [

    ],

    'listen'    => [
        //支付异步回调
        'UnionPayNotify' => [
            'addon\unionpay\event\PayNotify'
        ],
        //支付异步回调
        'PayNotify' => [
            'addon\unionpay\event\PayNotify'
        ],
        //支付方式，后台查询
        'PayType' => [
            'addon\unionpay\event\PayType'
        ],
        //支付，前台应用
        'Pay' => [
            'addon\unionpay\event\Pay'
        ],
        'PayClose' => [
            'addon\unionpay\event\PayClose'
        ],
        'PayRefund' => [
            'addon\unionpay\event\PayRefund'
        ],
        'PayTransfer' => [
            'addon\unionpay\event\PayTransfer'
        ],
        'TransferType' => [
            'addon\unionpay\event\TransferType'
        ],
        //退款异步回调
        'PayRefundNotify'   => [
            'addon\unionpay\event\PayRefundNotify'
        ],
        //支付超时
        'PayIsOver'     => [
            'addon\unionpay\event\PayIsOver'
        ],
    ],

    'subscribe' => [
    ],
];

