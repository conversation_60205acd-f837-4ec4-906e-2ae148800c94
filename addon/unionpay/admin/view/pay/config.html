{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
  .ns-input-text span{margin-right: 15px;}
  .ns-form {margin-top: 0;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form">

  <div class="layui-form-item">
    <label class="layui-form-label">公钥：</label>
    <div class="layui-input-block">
      <input name="rsa_public_key" type="text" value="{$info.rsa_public_key ?? ''}" class="layui-input ns-len-long">
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">私钥：</label>
    <div class="layui-input-block">
      <textarea name="rsa_private_key" class="layui-textarea ns-len-long">{$info.rsa_private_key ?? ''}</textarea>
    </div>
  </div>


  <div class="layui-form-item">
    <label class="layui-form-label">app_id：</label>
    <div class="layui-input-block">
      <input type="text" name="app_id" class="layui-input ns-len-long" value="{$info.app_id ?? ''}">
    </div>
    <div class="ns-word-aux"></div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">sub_appid：</label>
    <div class="layui-input-block">
      <input name="sub_appid" type="text" value="{$info.sub_appid ?? ''}" class="layui-input ns-len-long">
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">cusid：</label>
    <div class="layui-input-block">
      <input name="cusid" type="text" value="{$info.cusid ?? ''}" class="layui-input ns-len-long">
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">host：</label>
    <div class="layui-input-block">
      <input name="host" type="text" value="{$info.host ?? ''}" class="layui-input ns-len-long">
    </div>
  </div>

  <div class="ns-form-row">
    <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
    <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
  </div>
</div>

{/block}
{block name="script"}
<script>
  layui.use('form', function() {
    var form = layui.form;
    repeat_flag = false; //防重复标识
    form.render();

    /**
     * 监听提交
     */
    form.on('submit(save)', function(data) {

      if (repeat_flag) return false;
      repeat_flag = true;

      $.ajax({
        url: ns.url("unionpay://admin/pay/config"),
        data: data.field,
        dataType: 'JSON',
        type: 'POST',
        success: function(res) {
          repeat_flag = false;

          if (res.code == 0) {
            layer.confirm('编辑成功', {
              title:'操作提示',
              btn: ['返回列表', '继续操作'],
              yes: function(){
                location.href = ns.url("admin/config/pay")
              },
              btn2: function() {
                location.reload();
              }
            });
          }else{
            layer.msg(res.message);
          }
        }
      });
    });
  });

  function back() {
    location.href = ns.url("admin/config/pay");
  }
</script>
{/block}