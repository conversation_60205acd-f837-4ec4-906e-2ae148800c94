<?php


namespace addon\unionpay\admin\controller;


use addon\unionpay\model\Config as ConfigModel;
use app\admin\controller\BaseAdmin;

class Pay extends BaseAdmin
{
    public function config()
    {
        $config_model = new ConfigModel();
        $info = $config_model->getPayConfig();
        $infoValue = is_array($info) && isset($info['value']) ? $info['value'] : [];

        if(request()->isAjax()){
            $inputs = [
                'app_id'                => input('app_id', ''),
                'cusid'                 => input('cusid', ''),
                'rsa_public_key'        => input('rsa_public_key', ""),
                'rsa_private_key'       => input('rsa_private_key', ''),
                'sub_appid'             => input('sub_appid', ''),
                'host'             => input('host', ''),
            ];

            $data = array_merge( $infoValue, $inputs);

            $result = $config_model->setPayConfig($data);
            return $result;
        }else {
            $this->assign("info", $info);
            return $this->fetch("pay/config");
        }
    }
}