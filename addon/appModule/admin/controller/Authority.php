<?php
namespace addon\appModule\admin\controller;
use addon\appModule\model\Modules;
use app\admin\controller\BaseAdmin;
use app\model\system\Group;
use app\model\system\Menu;

class Authority extends BaseAdmin
{
	public function group()
	{
		if (request()->isAjax()) {
			$group_name = input('group_name', '');
			$menu_array = input('menu_array', '');
			$desc = input('desc', '');
			$group_model = new Group();
			$data = array(
				"group_name" => $group_name,
				"site_id" => $this->site_id,
				"app_module" => $this->app_module,
				"group_status" => 1,
				"menu_array" => $menu_array,
				"desc" => $desc,
				"is_system" => 0
			);
			$this->addLog("添加用户组:" . $data['group_name']);
			$result = $group_model->addGroup($data);
			return $result;
		} else {
			$menu_model = new Menu();
			$menu_list = $menu_model->getMenuList([ [ 'app_module', "=", $this->app_module ] ], "title, name, parent, level", "sort asc");
			$menu_tree = list_to_tree($menu_list['data'], 'name', 'parent', 'child_list', '');
			$this->assign('tree_data', $menu_tree);
            $this->forthMenu();
			return $this->fetch('authority/group');
		}
	}

	public function lists(){
        if (!request()->isAjax()){
            return $this->fetch('authority/lists');
        }

        $module = new Modules();
//	    dd($module->select());
    }
}