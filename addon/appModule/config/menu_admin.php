<?php
// +----------------------------------------------------------------------
// | 平台端菜单设置
// +----------------------------------------------------------------------
return [
    [
        'name' => 'APP_MODULE',
        'title' => '应用模块',
        'url' => 'appModule://admin/authority/lists',
        'parent' => 'CONFIG_BASE',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => '',
        'picture_select' => '',
        'sort' => 1,
        'child_list' => [
            [
                'name' => 'APP_MODULE_LIST',
                'title' => '应用模块列表',
                'url' => 'appModule://admin/authority/lists',
                'parent' => 'APP_MODULE',
                'is_show' => 1,
                'child_list' => [
                    [
                        'name' => 'APP_MODULE_ADD',
                        'title' => '添加模块',
                        'url' => 'appModule://admin/authority/add',
                        'sort' => 1,
                        'is_show' => 0
                    ],
                    [
                        'name' => 'APP_MODULE_DETAIL',
                        'title' => '应用模块详情',
                        'url' => 'appModule://admin/authority/detail',
                        'sort' => 1,
                        'is_show' => 0
                    ]
                ]
            ],
        ]
    ],
    [
        'name' => 'APP_MODULE_AUTHORITY',
        'title' => '权限组管理',
        'url' => 'appModule://admin/authority/group',
        'parent' => 'CONFIG_USER',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => '',
        'picture_select' => '',
        'sort' => 2,
    ],
];
