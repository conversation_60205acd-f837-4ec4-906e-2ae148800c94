{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
	.ns-form {margin-top: 0;}
	.layui-btn+.layui-btn {margin-left: 0;}
	.layui-btn {margin-right: 10px; margin-bottom: 15px;}
	
	.ns-level-rate {padding: 0 20px; position: relative;}
	.ns-level-rate:hover {border: 1px solid #4685FD;}
	.ns-level-rate-selected {border: 1px solid #4685FD;}
	.ns-level-rate-selected:after, .ns-disabled:after {
		content: "";
		display: inline-block;
		width: 20px;
		height: 20px;
		background-image: url(__STATIC__/img/selected.png);
		position: absolute;
		bottom: 0;
		right: 0;
	}
	.ns-disabled {border: 1px solid #4685FD; cursor: not-allowed; background-color: #F1F1F1;}
</style>
{/block}
{block name="main"}
<div class="layui-form ns-form">
    <!-- 基础上传 -->
    <div class="layui-card ns-card-common ns-card-brief">
        <div class="layui-card-header">
            <span class="ns-card-title">等级佣金比例</span>
        </div>
        <div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>等级名称：</label>
				<div class="layui-input-block">
					<input type="text" name="level_name" lay-verify="required"  autocomplete="off" class="layui-input ns-len-mid">
				</div>
			</div>

			{if $basics_info.level == 1}
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>一级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="one_rate" lay-verify="required|money"  autocomplete="off" class="layui-input ns-len-short">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			{elseif $basics_info.level == 2 /}
			<div class="layui-form-item">
				<label class="layui-form-label">一级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="one_rate" lay-verify="required|money"  autocomplete="off" class="layui-input ns-len-small">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">二级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="two_rate" lay-verify="required|mondy"  autocomplete="off" class="layui-input ns-len-small">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			{elseif $basics_info.level == 3 /}
			<div class="layui-form-item">
				<label class="layui-form-label">一级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="one_rate" lay-verify="required|money" autocomplete="off" class="layui-input ns-len-small">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">二级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="two_rate" lay-verify="required|money" autocomplete="off" class="layui-input ns-len-small">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">三级佣金比例：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="three_rate" lay-verify="required|money"  autocomplete="off" class="layui-input ns-len-small">
				</div>
				<div class="layui-form-mid">%</div>
			</div>
			{/if}
		</div>
    </div>

    <div class="layui-card ns-card-common ns-card-brief">
        <div class="layui-card-header">
            <span class="ns-card-title">升级条件</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">升级方式：</label>
                <div class="layui-input-block">
					<input type="radio" name="upgrade_type" value="1" lay-filter="withdraw_type" title="满足以下任意条件" checked />
					<input type="radio" name="upgrade_type" value="2" lay-filter="withdraw_type" title="满足以下全部条件" />
                </div>
            </div>
			
			<div class="layui-form-item">
			    <label class="layui-form-label"></label>
			    <div class="layui-input-block">
					<!-- <button class="layui-btn layui-btn-primary ns-level-btn">分销订单总额<input type="hidden" value="2" /></button> -->
					<!-- <button class="layui-btn layui-btn-primary ns-level-btn">分销订单总数<input type="hidden" value="1" /></button> -->
					<button class="layui-btn layui-btn-primary ns-level-btn">一级分销订单总数<input type="hidden" value="1" /></button>
					<button class="layui-btn layui-btn-primary ns-level-btn">一级分销订单总额<input type="hidden" value="2" /></button>
					<button class="layui-btn layui-btn-primary ns-level-btn">自购订单总数<input type="hidden" value="1" /></button>
					<button class="layui-btn layui-btn-primary ns-level-btn">自购订单总额<input type="hidden" value="2" /></button>
					<!-- <button class="layui-btn layui-btn-primary ns-level-btn">下线人数<input type="hidden" value="1" /></button> -->
					<!-- <button class="layui-btn layui-btn-primary ns-level-btn">下线分销商人数<input type="hidden" value="1" /></button> -->
					<button class="layui-btn layui-btn-primary ns-level-btn">一级下线人数<input type="hidden" value="1" /></button>
					<button class="layui-btn layui-btn-primary ns-level-btn">一级下线分销商<input type="hidden" value="1" /></button>
			    </div>
			</div>
		</div>
	</div>
	
	<div class="layui-card ns-card-common ns-card-brief">
	    <div class="layui-card-header">
	        <span class="ns-card-title">升级条件限制</span>
	    </div>
	    <div class="layui-card-body ns-level-term">
           <!--  <div class="layui-form-item layui-hide">
                <label class="layui-form-label">分销订单总数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="fenxiao_order_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">个</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">分销订单总额：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="fenxiao_order_meney" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">元</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div> -->
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">一级分销订单总数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="one_fenxiao_order_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">个</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">一级分销订单总额：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="one_fenxiao_order_money" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">元</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">自购订单总数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="order_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">个</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">自购订单总额：</label>
				<div class="layui-input-inline">
					<input type="number" min="0" name="order_money" autocomplete="off" class="layui-input ns-len-short">
				</div>
				<div class="layui-form-mid">元</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <!-- <div class="layui-form-item layui-hide">
                <label class="layui-form-label">下线人数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="child_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">人</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">下线分销商人数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="child_fenxiao_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">人</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div> -->
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">一级下线人数：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="one_child_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">人</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
            <div class="layui-form-item layui-hide">
                <label class="layui-form-label">一级下线分销商：</label>
                <div class="layui-input-inline">
					<input type="number" min="0" name="one_child_fenxiao_num" autocomplete="off" class="layui-input ns-len-short">
                </div>
				<div class="layui-form-mid">人</div>
				<a href="#" class="ns-text-color layui-form-mid" onclick="delDiv(this)">删除</a>
            </div>
			
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
				<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
			</div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    layui.use(['form'], function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
			

		
		$(".ns-level-rate").click(function() {

			if (!$(this).hasClass("ns-disabled")) {
				$(this).addClass("ns-level-rate-selected");
				$(this).siblings().removeClass("ns-level-rate-selected");
			}
		});
		
		$(".ns-level-btn").click(function() {
			var _index = $(this).index();
			
			if (!$(this).hasClass("ns-border-color")) {
				$(this).addClass("ns-border-color");
				$(".ns-level-term>div").eq(_index).removeClass("layui-hide");
				if ($(this).find("input").val() == 1) {
					$(".ns-level-term>div").eq(_index).find("input").attr("lay-verify", "required|num");
				} else {
					$(".ns-level-term>div").eq(_index).find("input").attr("lay-verify", "required|money");
				}
			}
		})

        form.on('submit(save)', function(data) {
			
			let arr = $(".layui-card-body").eq(2).children('.layui-form-item').length;
			let arr1 = $(".layui-card-body").eq(2).children('.layui-form-item.layui-hide').length;		
			if(arr==arr1){
				layer.msg('请选择升级条件');
				return;
			}

            if (repeat_flag) return;
            repeat_flag = true;
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("fenxiao://admin/level/add"),
                data: data.field,
                success: function(res) {
                    if (res.code == 0) {
                        layer.confirm('添加成功', {
                            title:'操作提示',
                            btn: ['返回列表', '继续添加'],
                            yes: function(){
                                location.href = ns.url("fenxiao://admin/level/lists");
                            },
                            btn2: function() {
                                location.href = ns.url("fenxiao://admin/level/add");
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            });
        });
		
		/**
		 * 表单验证
		 */
		form.verify({
			num: function(value, item) {
				var str = $(item).parents(".layui-form-item").find("label").text().split("*").join("");
				str = str.substring(0, str.length - 1);
				
				if (value <= 0) {
					return str + '必须大于0';
				}
				if (value % 1 != 0) {
					return str + '必须为整数';
				}
			},
			money: function(value, item) {
				var str = $(item).parents(".layui-form-item").find("label").text().split("*").join("");
				str = str.substring(0, str.length - 1);
				
				if (value <= 0) {
					return str + '必须大于0';
				}
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return str + "最多可保留两位小数";
				}
			}
		});
    });
	
	function delDiv(e) {
		
		var _len = $(e).parents(".layui-form-item").index();
		$(e).parents(".layui-form-item").addClass("layui-hide");
		$(e).parents(".layui-form-item").find("input").removeAttr("lay-verify");
		$(e).parents(".layui-form-item").find("input").val("");
		$(".ns-level-btn").eq(_len).removeClass("ns-border-color");
	}
	
	function back() {
		location.href = ns.url("fenxiao://admin/level/lists");
	}
</script>
{/block}