{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>等级序号数值越大代表等级越高</li>
		</ul>
	</div>
</div>

<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="clickAdd()">添加等级</button>
</div>
<!-- 列表 -->
<table id="level_list" lay-filter="level_list"></table>

<!-- 商品 -->
<script type="text/html" id="goods">
	<div class="ns-table-tuwen-box">
		<div class="ns-img-box">
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0])}}"/>
		</div>
		<div class="ns-font-box">
			<a href="javascript:;" class="ns-multi-line-hiding ns-text-color">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始时间：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束时间：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{#  if(d.status == 0){  }}
	停用
	{{#  }else if(d.status == 1){  }}
	启用
	{{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">

		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="del">删除</a>
	</div>
</script>
{/block}

{block name="script"}
<script>
    layui.use('element', function() {
        var table,
            element = layui.element,
            repeat_flag = false; //防重复标识

        table = new Table({
            elem: '#level_list',
            url: ns.url("fenxiao://admin/level/lists"),
            cols: [
                [{
                    title: '等级序号',
                    unresize: 'false',
                    width: '10%',
					templet: function (data) {
						return data.LAY_INDEX;
					}

                }, {
                    field: 'level_name',
                    title: '等级名称',
                    unresize: 'false',
                    width: '11%'
                }, {
                    field: 'one_rate',
                    title: '一级佣金比例',
                    unresize: 'false',
                    width: '10%',
                    templet: function(data) {
                        return data.one_rate + '%';
                    },
					hide: {if $basics_info.level >= 1}  false  {else /}  true  {/if}
                }, {
                    field: 'two_rate',
                    title: '二级佣金比例',
                    unresize: 'false',
                    width: '11%',
                    templet: function(data) {
                        return data.two_rate + '%';
                    },
					hide: {if $basics_info.level >= 2}  false  {else /}  true  {/if}
                }, {
                	field:'three_rate',  
                    title: '三级佣金比例',
                    unresize: 'false',
                    width: '10%',
                    templet: function(data) {
                        return data.three_rate + '%';
                    },
					hide: {if $basics_info.level >= 3}  false  {else /}  true  {/if}
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
                    width: '12%'
                }]
            ],

        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'del': //删除
                    del(data.level_id);
                    break;
                case 'start': //启用
                    start_status(data.level_id,1);
                    break;
                case 'stop': //停用
                    stop_status(data.level_id,0);
                    break;
                case 'edit': //编辑
                    location.href = ns.url("fenxiao://admin/level/edit", {"level_id": data.level_id});
                    break;
            }
        });

        /**
         * 删除
         */
        function del(level_id){
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要删除该分销等级吗?', function() {
                $.ajax({
                    url: ns.url("fenxiao://admin/level/delete"),
                    data: {level_id:level_id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        };

        /**
         * 停用
         */
        function stop_status(level_id,type){
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要停用该分销等级吗?', function() {
                $.ajax({
                    url: ns.url("fenxiao://admin/level/status"),
                    data: {level_id:level_id,type:type},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        };

        /**
         * 启用
         */
        function start_status(level_id,type){
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要启用该分销等级吗?', function() {
                $.ajax({
                    url: ns.url("fenxiao://admin/level/status"),
                    data: {level_id:level_id,type:type},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        };

    });

	function clickAdd()
	{
	    location.href = ns.url('fenxiao/admin/level/add');
	}

</script>

{/block}