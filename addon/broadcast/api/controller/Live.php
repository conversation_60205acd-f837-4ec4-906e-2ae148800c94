<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\broadcast\api\controller;

use addon\broadcast\model\Room;
use app\api\controller\BaseApi;
use EasyWeChat\Factory;
use think\Exception;
use addon\broadcast\model\Live as LiveModel;
use think\facade\Db;

/**
 * 直播
 */
class Live extends BaseApi
{

    /**
     * 获取直播间列表
     * @return false|string
     */
	public function roomList(){
        $page = isset($this->params[ 'page' ]) ? $this->params[ 'page' ] : 1;
        $page = $page-1;
        $page_size = isset($this->params[ 'page_size' ]) ? $this->params[ 'page_size' ] : PAGE_LIST_ROWS;
        $type = isset($this->params['type'])? (int)$this->params['type']: 0;

        try{
            $orderBy = 'start_time desc';
            $condition = [];
            $condition[] =  ['is_show','=',1];
            if ($type==1){
                $condition[] = ['live_status','=',102];
                $orderBy = 'start_time asc';
            }else{
                $condition[] = ['live_status','in',[101,103]];
                $condition[] = ['','exp',Db::raw('( close_replay = 0 or live_status <> 107 )')];
            }
            $data = (new Room())->getRoomPageList($condition,'*',$orderBy,$page, $page_size);

            return $this->response($data);
        }catch (\Exception $e){
            return $this->response($this->error([], $e->getMessage()));
        }
    }

    /**
     * 直播间详情
     * @return false|string
     */
    public function roomInfo(){
	    $roomId = input('room_id');

	    $data = $this->error([],'参数错误');
	    if (!empty($roomId)) {
            $data = (new Room())->getRoomInfo(['roomid'=>$roomId]);
        }
	    return $this->response($data);
    }

    /**
     * 获取直播回放
     * @return string
     */
    public function getPlaybacks(){
        $roomId = isset($this->params[ 'room_id' ]) ? $this->params[ 'room_id' ] : 0;
        $start = ($this->params['start']) ?? 0;
        $limit = ($this->params['limit']) ?? PAGE_LIST_ROWS;

        try{
            $data = (new LiveModel())->getPlaybackInfo($roomId, $start, $limit);
            foreach ($data['live_replay'] as $key => $v) {
                $data['live_replay'][$key]['create_time'] = date('Y-m-d H:i:s', strtotime($v['create_time']));
                $data['live_replay'][$key]['expire_time'] = date('Y-m-d H:i:s', strtotime($v['expire_time']));
            }
            return $this->response($this->success($data));
        }catch (\Exception $e){
            return $this->response($this->error([], $e->getMessage()));
        }
    }

    /**
     * 手动刷新直播间
     * @return false|string
     * @throws Exception
     */
    public function freshRoom(){
        try{
            $page = input('page',0);
            $page_size = input('page_size',10);

            $liveMode = new Room();
            $res = $liveMode->syncLiveRoom($page,$page_size);

        }catch (\Exception $e){
            $res = ['error'=>$e->getMessage()];
        }
        return json_encode($res);
    }
}