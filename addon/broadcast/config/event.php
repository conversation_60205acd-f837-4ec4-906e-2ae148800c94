<?php
// 事件定义文件
return [
    'bind'      => [
        
    ],

    'listen'    => [
        //展示活动
        'BroadcastShowPromotion' => [
            'addon\broadcast\event\ShowPromotion',
        ],
        'BroadcastWeappMenu' => [
            'addon\broadcast\event\WeappMenu',
        ],
        // 轮询更新直播商品状态
        'BroadcastLiveGoodsStatus' => [
            'addon\broadcast\event\LiveGoodsStatus',
        ],
        // 轮询更新直播间状态
        'BroadcastLiveRoomStatus' => [
            'addon\broadcast\event\LiveRoomStatus',
        ]
    ],

    'subscribe' => [
    ],
];
