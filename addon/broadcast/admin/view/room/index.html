{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.progress-layer {width:400px;background:#fff;position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);box-shadow:1px 1px 50px rgba(0,0,0,.3);padding:20px 20px;z-index:100;display:none;}
.progress-layer h3 {line-height:1;margin-bottom:15px;text-align:center;font-size:14px;}
.progress-layer .layui-progress-big,.progress-layer .layui-progress-big .layui-progress-bar {height:14px;line-height:14px;}
.progress-layer .layui-progress-text {line-height:14px;}
.room-info {padding: 5px 0;align-items: center;flex-wrap:unset!important;}
.room-info .room-name {padding-left: 5px;line-height: 1}
.room-info img {width:50px;height: 50px;}
.ns-single-filter-box{justify-content: end}
.push-layer{text-align: center;padding: 30px 0;}
.push-layer .qrcode{width:100px;height:100px;margin:10px auto;display:block}
</style>
{/block}

{block name="main"}
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="sync()">同步直播间</button>
</div>

<table id="room_list" lay-filter="room_list"></table>

<!-- 直播间信息 -->
<script type="text/html" id="roominfo">
	<div class="ns-table-btn room-info">
		<img src="{{ ns.img(d.share_img) }}">
		<span class="room-name" title="{{ d.name }}">{{ d.name }}</span>
	</div>
</script>


<div class="progress-layer">
	<h3>正在同步中...</h3>
	<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="progress">
		<div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
	</div>
</div>

<script type="text/html" id="push">
	<div class="push-layer">
		<img src="LIVE_IMG/weapp_push.png" class="qrcode">
		<p>主播可通过微信扫该小程序码或搜一搜“小程序直播”进入主播小程序进行推流</p>
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		{{#  if( d.audit_status == '0' ){ }}
			<a class="layui-btn" lay-event="agree">通过</a>
			<a class="layui-btn" lay-event="refuse">拒绝</a>
		{{#  } }}
		<a class="layui-btn" lay-event="detail">查看</a>
	</div>
</script>
{/block}

{block name="script"}
<script>
	var form,table,element,syncClick = false;
	layui.use(['form', 'element'], function() {
		form = layui.form;
		element = layui.element;
		
		table = new Table({
			elem: '#room_list',
			url: ns.url("live://admin/room/index"),
			cols: [
				[{
					title: '直播间信息',
					unresize: 'false',
					width: '14%',
					templet: "#roominfo"
				}, {
					field: 'site_name',
					title: '站点名称',
					unresize: 'false',
					width: '8%'
				}, {
					field: 'roomid',
					title: '直播间ID',
					unresize: 'false',
					width: '8%'
				}, {
					field: 'anchor_name',
					title: '主播昵称',
					unresize: 'false',
					width: '8%'
				}, {
					field: 'start_time',
					title: '开播时间',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return ns.time_to_date(data.start_time);
					}
				}, {
					field: 'end_time',
					title: '结束时间',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return ns.time_to_date(data.end_time);
					}
				}, {
					title: '商品数量',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return data.goods.length;
					}
				}, {
					field: 'status_name',
					title: '状态',
					unresize: 'false',
					width: '10%',
				}, {
					field: 'audit_status_name',
					title: '审核状态',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						switch (data.audit_status) {
							case 0:
								return '审核中';
								break;
							case 1:
								return '已审核';
								break;
							case -1:
								return '已拒绝';
								break;
						}

					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '10%'
				}]
			]
		});
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'agree': // 运营
					agree(data);
					break;
				case 'refuse':
					refuse(data);
					break;
				case 'detail':
					location.href = ns.url("live://admin/room/detail", {"id": data.id});
					break;
			}
		})

	});

	// 同步直播间
	function sync(start){
		if (syncClick) return;
		syncClick = true;
		var start = start == undefined ? 0 : start;

		$.ajax({
			url: ns.url("live://admin/room/sync"),
			data: {
				start: 0,
			},
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				syncClick = false;
				if (res.code == 0) {
					var data = res.data,
						next = parseInt(start) + 1;

						if (next < data.total_page) {
							if (start == 0) {
								$(".progress-layer").fadeOut();
							}
							var progress = (next / data.total_page * 100).toFixed(2);
							element.progress('progress', progress + '%');
							// 拉取下一页
							sync(next);
						} else {
							if (!$(".progress-layer").is(':hidden')) $(".progress-layer").fadeOut();
							layer.closeAll();
							layer.msg('同步成功');
							table.reload();
						}
				} else {
					layer.msg(res.message);
				}
			}
		});
	}


	/**
	 * 通过审核
	 */
	var agree_flag = false;
	function agree(field) {
		if(agree_flag) return false;
		agree_flag = true;

		layer.confirm('确定要通过当前直播申请吗?', {title: '提示'}, function() {
			$.ajax({
				url: ns.url("live://admin/room/agree"),
				data: field,
				dataType: 'JSON', //服务器返回json格式数据
				type: 'POST', //HTTP请求类型
				success: function(res) {
					agree_flag = false;

					if (res.code >= 0) {
						table.reload({
							page: {
								curr: 1
							}
						});
						location.reload();
					} else {

						layer.closeAll();
						layer.msg(res.message);
					}
				}
			});
		}, function () {
			layer.closeAll();
			agree_flag = false;
		});
	}

	/**
	 * 通过审核
	 */
	var refuse_flag = false;
	function refuse(field) {
		if(refuse_flag) return false;
		refuse_flag = true;

		layer.confirm('确定要拒绝当前直播申请吗?', {title: '提示'}, function() {

			layer.prompt({title: '填写拒绝理由，并确认', formType: 2}, function(text, index){

				field.reason = text;
				$.ajax({
					url: ns.url("live://admin/room/refuse"),
					data: field,
					dataType: 'JSON', //服务器返回json格式数据
					type: 'POST', //HTTP请求类型
					success: function(res) {
						refuse_flag = false;

						if (res.code >= 0) {
							table.reload({
								page: {
									curr: 1
								}
							});
							location.reload();
						} else {

							layer.closeAll();
							layer.msg(res.message);
						}
					}
				});
			});

		}, function () {
			layer.closeAll();
			refuse_flag = false;
		});
	}
</script>
{/block}