<?php


namespace addon\broadcast\admin\controller;


use addon\broadcast\model\WeappGoods;
use app\admin\controller\BaseAdmin;
use addon\broadcast\model\Goods as GoodsModel;

class Goods extends BaseAdmin
{
    public function index()
    {
        if (request()->isAjax()) {
            $goods = new GoodsModel();
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $condition = [
                'site_id' => $this->site_id
            ];
            $data = $goods->getGoodsPageList($condition, '*', 'id desc', $page, $page_size);
            return $data;
        } else {
            return $this->fetch("goods/index");
        }
    }

    /**
     * 同步直播商品库到本地
     */
    public function sync(){
        if (request()->isAjax()) {
            $goods = new GoodsModel();
            $start = input('start', 0);
            $res = $goods->syncGoods($start,20);
            return $res;
        }
    }

    /**
     * 添加商品
     * @return mixed
     */
    public function add(){
        if (request()->isAjax()) {
            $goods = new GoodsModel();
            $data = [
                'site_id' => 0,
                'name' => input('name', ''),
                'goods_pic' => input('goods_pic', ''),
                'price_type' => input('price_type', ''),
                'price' => input('price', ''),
                'price2' => input('price2', ''),
                'url' => input('url', ''),
            ];
            $res = $goods->addGoods($data);
            return $res;
        }
//        return $this->fetch("goods/add");
    }

    /**
     * 删除商品
     */
    public function delete(){
        if (request()->isAjax()) {
            $id = input('id', '');
            $goods = new GoodsModel();
            $res = $goods->deleteGoods($id, $this->site_id);
            return $res;
        }
    }

    /**
     * 上传直播商品
     * @param string $goods_ids 商品id
     * @return array
     */
    public function uploadBroadcastGoods()
    {
        $goods_ids = input('goods_ids', '');
        $res = (new GoodsModel())->uploadBroadcastGoods($goods_ids);
        if (!empty($res['success']))
            return success('0',$res['success'].'同步成功，'.$res['fail'].'同步失败！');
        else
            return error(-1,'同步失败');
    }
}