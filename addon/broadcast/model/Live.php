<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\broadcast\model;

use app\model\BaseModel;
use addon\weapp\model\Config as WeappConfigModel;
use EasyWeChat\Factory;
use think\Exception;
use think\facade\Cache;
use think\facade\Log;

/**
 *  好物圈
 */
class Live extends BaseModel
{
    private $app;

    /**
     * 微信直播间接口错误码
     * @var array
     */
    private $room_error = [
        1003 => '商品id不存在',
        47001 => '入参格式不符合规范',
        200002 => '入参错误',
        300001 => '禁止创建/更新商品 或 禁止编辑&更新房间',
        300002 => '名称长度不符合规则',
        300006 => '图片上传失败',
        300022 => '此房间号不存在',
        300023 => '房间状态 拦截（当前房间状态不允许此操作）',
        300024 => '商品不存在',
        300025 => '商品审核未通过',
        300026 => '房间商品数量已经满额',
        300027 => '导入商品失败',
        300028 => '房间名称违规',
        300029 => '主播昵称违规',
        300030 => '主播微信号不合法',
        300031 => '直播间封面图不合规',
        300032 => '直播间分享图违规',
        300033 => '添加商品超过直播间上限',
        300034 => '主播微信昵称长度不符合要求',
        300035 => '主播微信号不存在',
        300036 => '主播微信号未实名认证',
    ];

    /**
     * 微信直播商品接口错误码
     * @var array
     */
    private $goods_error = [
        300001 => '商品创建功能被封禁',
        300002 => '名称长度不符合规则',
        300003 => '价格输入不合规',
        300004 => '商品名称存在违规违法内容',
        300005 => '商品图片存在违规违法内容',
        300006 => '图片上传失败',
        300007 => '线上小程序版本不存在该链接',
        300008 => '添加商品失败',
        300009 => '商品审核撤回失败',
        300010 => '商品审核状态不对',
        300011 => 'API不允许操作非API创建的商品',
        300012 => '没有提审额度（每天500次提审额度）',
        300013 => '提审失败',
        300014 => '审核中，无法删除',
        300017 => '商品未提审',
        300021 => '商品添加成功，审核失败',
    ];


    public function __construct()
    {
        //微信小程序配置

        $config = config('easywechat');
        $this->app = Factory::miniProgram($config);

    }

    /**
     * 添加永久图片素材
     * @param $path '图片路径，非url'
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function addImageMedia($path){
        try {
            $result = $this->app->media->uploadImage($path);
            throw_if(isset($result['errcode']),'Exception','图片素材上传失败');
            return $result;
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 获取直播间列表
     * @param int $start
     * @param int $limit
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getRoomList($start = 0, $limit = PAGE_LIST_ROWS){

        $wxResult = $this->app->broadcast->getRooms($start, $limit);
        $data = [
            'room_info' => $wxResult['room_info'] ?? [],
            'total'     => $wxResult['total'] ?? 0,
            'live_replay'=> $wxResult['live_replay'] ?? [],
        ];
        throw_if(isset($wxResult['errcode']) && $wxResult['errcode'] != 0, 'Exception',$this->room_error[ abs($wxResult['errcode']) ] ?? $wxResult['errmsg']);

        return $data;

    }

    /**
     * 获取回放视频
     * @param $room_id
     * @param int $start
     * @param int $limit
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getPlaybackInfo($room_id, $start = 0, $limit = 100){

        $cacheKey = "BROADCASTPLAYBACK_".json_encode($room_id,$start,$limit);
        $data =  Cache::get($cacheKey, []);
        if (empty($data)) {
            $wxResult = $this->app->broadcast->getPlaybacks($room_id, $start, $limit);
            $data = [
                'room_info' => $wxResult['room_info'] ?? [],
                'total'     => $wxResult['total'] ?? 0,
                'live_replay'=> $wxResult['live_replay'] ?? [],
            ];
            throw_if(isset($wxResult['errcode']) && $wxResult['errcode'] != 0,'Exception',$this->room_error[ abs($wxResult['errcode']) ] ?? ($wxResult['errmsg'] ?? ''));
            // 最后一个视频播放地址,最后一个视频是mp4格式，则不缓存，
            $video = [];
            count($data['live_replay']) && $video = $data['live_replay'][count($data['live_replay'])-1];
            if (!empty($video['media_url']) && pathinfo($video['media_url'], PATHINFO_EXTENSION) != 'mp4'){
                Cache::set($cacheKey, $data,60*60*48);
            }
        }

        return $data;

    }

    /**
     * 创建直播间
     * @param $param
     */
    public function createRoom($param){
        $result = $this->app->broadcast->createLiveRoom($param);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0,'Exception',$this->room_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));
        return $result;
    }

    /**
     * 给直播间推送商品
     * @param int $room_id
     * @param int $goods_id
     * @return boolean
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function roomAddGoods(int $room_id, int $goods_id){
        $result = $this->app->broadcast->pushGoods(['roomId' => $room_id, 'goodsId' => $goods_id ]);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', '推送失败！');

        return true;
    }

    /**
     * 获取直播商品列表
     * @param int $start
     * @param int $limit
     * @param int $status
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getGoodsList($start = 0, $limit = PAGE_LIST_ROWS, $status = 2){
        $result = $this->app->broadcast->getApproved(['offset' => $start, 'limit' => $limit, 'status' => $status]);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', $this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return ['goods_list'=>$result['goods'] ?? [],'total'=> $result['total'] ?? 0];
    }

    /**
     * 上传商品并提审
     * @param $param
     * @return array
     */
    public function uploadGoods($param){
        $result = $this->app->broadcast->create($param);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', $this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return $result;
    }

    /**
     * 上传商品并提审
     * @param $param
     * @return array
     */
    public function updateGoods($param){
        $result = $this->app->broadcast->update($param);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', $this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return $result;
    }

    /**
     * 商品重新提交审核
     * @param $param
     * @return array
     */
    public function resubmitAudit($param){
        $result = $this->app->broadcast->resubmitAudit($param);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', $this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return $result;
    }

    /**
     * 删除直播库的商品
     */
    public function deleteGoods(int $goods_id){
        $result = $this->app->broadcast->delete($goods_id);

        throw_if(isset($result['errcode']) && $result['errcode'] != 0, 'Exception', $this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return $this->success($result);
    }

    /**
     * 获取商品状态--最多支持一次获取20个商品状态
     * @param array $goods_ids
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getGoodsStatus(array $goods_ids){
        $result = $this->app->broadcast->getGoodsWarehouse($goods_ids);
        throw_if(isset($result['errcode']) && $result['errcode'] != 0,'Exception',$this->goods_error[ abs($result['errcode']) ] ?? ($result['errmsg'] ?? ''));

        return ['goods' => $result['goods'] ?? [], 'total' => $result['total'] ?? 0];
    }
}