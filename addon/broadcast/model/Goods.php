<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\broadcast\model;

use app\Frame\Model\Admin\WeappGoods;
use app\model\BaseModel;
use app\model\goods\GoodsModel;
use app\model\system\Cron;
use app\model\upload\Upload;
use think\Exception;
use think\facade\Log;
use think\Model;

class Goods extends Model
{
    private $liveStatus = [
        0 => '未审核',
        1 => '审核中',
        2 => '审核通过',
        3 => '审核驳回'
    ];

    /**
     * 获取直播间列表
     * @param array $condition
     * @param bool $field
     * @param string $order
     * @param int $page
     * @param int $list_rows
     * @param string $alias
     * @param array $join
     * @return array
     */
    public function getGoodsPageList($condition = [], $field = true, $order = '', $page = 1, $list_rows = PAGE_LIST_ROWS, $alias = 'a', $join = []){
        $data = model('weapp_goods')->pageList($condition, $field, $order, $page, $list_rows, $alias, $join);
        if (!empty($data['list'])) {
            foreach ($data['list'] as $k => $item) {
                $data['list'][$k]['status_name'] = $this->liveStatus[ $item['status'] ] ??  '';
            }
        }
        return $this->success($data);
    }

    /**
     * 同步商品库商品
     */
    public function syncGoods($start, $limit, $status = 2){
        $live = new Live();
        $result = $live->getGoodsList($start, $limit, $status);
        if ($result['code'] < 0) return $result;

        if (!empty($result['data']['goods'])) {

            foreach ($result['data']['goods'] as $item) {
                $goods_info = model('weapp_goods')->getInfo([ ['goods_id', '=', $item['goodsId'] ] ], '*');
                if (!empty($goods_info)) {
                    preg_match("/(pages\/goods\/detail\/detail\?sku_id=)(\d*)$/", $item['url'], $matches);
                    $upload = new Upload($goods_info['site_id']);
                    $upload->setPath('upload/live/goods/');
                    if (is_url($item['coverImgUrl'])) {
                        $pull_result = $upload->remotePull($item['coverImgUrl']);
                        $pull_result = $pull_result['data'];
                        if (isset($pull_result['pic_path']) && !empty($pull_result['pic_path'])) {
                            $data['cover_img'] = $pull_result['pic_path'];
                        } else {
                            $data['cover_img'] = $item['coverImgUrl'];
                        }
                    }
                    $data = [
                        'goods_id' => $item['goodsId'],
                        'name' => $item['name'],
                        'price' => $item['price'],
                        'status' => $status,
                        'url' => $item['url'],
                        'sku_id' => $matches[2] ?? 0,
                        'third_party_tag' => $item['thirdPartyTag']
                    ];
                    model('weapp_goods')->update($data, [ ['id', '=', $goods_info['id'] ] ]);
                }
            }
            $total_page = ceil($result['data']['total'] / $limit);
            return $this->success(['page' => $start, 'total_page' => $total_page ]);
        } else {
            return $this->success(['page' => $start, 'total_page' => 1 ]);
        }
    }

    /**
     * @param $goods_ids
     * @return mixed
     */
    public function uploadBroadcastGoods($goods_ids){
        $goods_ids = is_array($goods_ids)? $goods_ids: explode(',',$goods_ids);
        $succ_num = $fail_num = 0;
        foreach ($goods_ids as $key => $id){
            try{
                // 添加
                $goods = GoodsModel::where(['g.goods_id'=>$id])
                    ->where('sku.is_delete',0)
                    ->where('sku.goods_state',1)->alias('g')
                    ->join('goods_sku sku','sku.goods_id=g.goods_id','inner')
                    ->field(['g.goods_id','g.goods_image','g.goods_name','g.reward_shop_rate','g.sku_id','sku.price','sku.discount_price','sku.market_price','min(sku.discount_price) min_price','max(sku.discount_price) max_price'])
                    ->findOrEmpty();
                throw_if($goods->isEmpty(),'Exception','商品已下架或不存在！');
                $cast_goods = WeappGoods::where('self_goods_id',$id)->findOrEmpty();

                // 价格类型，1：一口价（只需要传入price，price2不传）
                // 2：价格区间（price字段为左边界，price2字段为右边界，price和price2必传）
                // 3：显示折扣价（price字段为原价，price2字段为现价， price和price2必传）
                $price_type = 1;
                if ($goods->market_price>$goods->discount_price){
                    $price_type = 3;
                    $price = $goods->market_price;
                    $price2 = currencyFormat(currencyFormat($goods->price*$goods->reward_shop_rate*0.01)+$goods->price);
                }else if ($goods->min_price*100==$goods->max_price*100){
                    $price_type = 1;
                    $price = currencyFormat(currencyFormat($goods->price*$goods->reward_shop_rate*0.01)+$goods->price);;
                    $price2 = '';
                }else{
                    $price_type = 2;
                    $price = currencyFormat(currencyFormat($goods->min_price*$goods->reward_shop_rate*0.01)+$goods->min_price);;
                    $price2 = currencyFormat(currencyFormat($goods->max_price*$goods->reward_shop_rate*0.01)+$goods->max_price);;
                }
                if (!$cast_goods->isEmpty()){
                    // 审核通过的商品仅允许更新价格类型与价格，审核中的商品不允许更新，未审核的商品允许更新所有字段
                    if (in_array($cast_goods->status,[0,3])){
                        // 未审核/审核失败的，重新审核

                    }else{
                        // 审核中/审核通过的，成功数量+1
                        if ($cast_goods->status==1){
                            // 审核中商品，不允许更新
                        }else if ($cast_goods->status==2){
                            // 审核通过的商品，更新价格
                            $goodsInfo = [
                                'price_type'=> $price_type,
                                'price'     => $price,
                                'price2'    => $price2,
                                'goods_id'  => $id,
                                'name'      => $goods->goods_name,
                                'url'       => 'pages/goods/detail/detail?sku_id='.$goods->sku_id,
                            ];
                            $this->updateGoods($goodsInfo);
                        }
                        $succ_num++;
                        continue;
                    }
                }else{
                    $goods_image = explode(',',$goods->goods_image);
                    $goodsInfo = [
                        'goods_pic' => $goods_image[0],
                        'name'      => $goods->goods_name,
                        'price_type'=> $price_type,
                        'price'     => $price,
                        'price2'    => $price2,
                        'url'       => 'pages/goods/detail/detail?sku_id='.$goods->sku_id,
                        'goods_id'  => $id,
                    ];
                    $result = $this->addGoods($goodsInfo);
                }
                $succ_num++;
            }catch (\Exception $e){
                $fail_num++;
                Log::error('goods_id：'.$id.'上传直播库失败：'.$e->getMessage());
                Log::error($e->getTraceAsString());
            }
        }
        return ['success'=>$succ_num,'fail'=>$fail_num];
    }

    /**
     * 添加商品
     * @param $param
     */
    public function addGoods($param) {
        try {
            if (!preg_match("/(pages\/goods\/detail\/detail\?sku_id=)(\d*)$/", $param['url'], $matches)) {
                return $this->error('', '商品链接格式不正确');
            }
//        $count = model('weapp_goods')->getCount([['sku_id', '=', $param['sku_id']]]);
            $count = model('weapp_goods')->getCount(['self_goods_id' => $param['goods_id']]);
            if ($count > 0) {
                return $this->error([], '当前商品已经是直播商品');
            }
            // 上传的图片素材宽高不能超过300px
            $goods_pic = $this->checkGoodsPic($param['goods_pic']);
            $live = new Live();
            $result = $live->addImageMedia($goods_pic);

            $audit = [
                'coverImgUrl' => $result['media_id'],
                'name' => mb_strlen($param['name'])>14? mb_substr($param['name'],0,10).'……':$param['name'],
                'priceType' => $param['price_type'],
                'price' => (float)($param['price']),
                'price2' => (float)$param['price2'],
                'url' => $param['url']

            ];
            if ($param['price_type'] == 1) {
                unset($audit['price2']);
            }
            $result = $live->uploadGoods($audit);

            $data = [
                'site_id' => $param['site_id'] ?? 0,
                'goods_id' => $result['goodsId'],
                'name' => $param['name'],
                'cover_img' => $goods_pic,
                'price' => $param['price'],
                'price2' => $param['price2'],
                'price_type' => $param['price_type'],
                'status' => 1,
                'url' => $param['url'],
                'audit_id' => $result['auditId'],
                'sku_id' => $matches[2],
                'self_goods_id'   => $param['goods_id'],
                'third_party_tag' => 2
            ];
            $result = model('weapp_goods')->add($data);
            return $result;
        }catch (Exception $e){
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 更新商品到直播库
     * @param array $param
     * @return bool
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function updateGoods(array $param = [])
    {
        try{
            $weapp_goods = WeappGoods::where('self_goods_id',$param['goods_id'])->findOrEmpty();
            $liveModel = new Live();

            throw_if($weapp_goods->isEmpty(),'Exception','该商品还没上传直播库');
            $adjust_status = $weapp_goods->status;

            if ($adjust_status==1) {
                $adjust_status = $liveModel->getGoodsStatus([$weapp_goods->wx_goods_id]);
                $adjust_status = $adjust_status['goods'][0]['audit_status'];
            }

            throw_if($adjust_status==1, 'Exception','商品审核中，不允许修改');
            throw_if($adjust_status!=2, 'Exception','该商品不是审核通过状态！');

            $arr = [
                "goodsId"   => $weapp_goods->goods_id,
                'price'     => (float)$param['price'],
            ];

            !empty($param['price_type']) && $arr['priceType'] = $param['price_type'];
            !empty($param['price2']) && $arr['price2'] = (float)$param['price2'];

            $liveModel->updateGoods($arr);
            $weapp_goods->status = $adjust_status;
            $weapp_goods->price = $param['price'];
            $weapp_goods->price2 = $param['price2'];
            $weapp_goods->price_type = $param['price_type'];
            $weapp_goods->save();
            Log::info('weapp_goods如何');
            return true;

        }catch (\Exception $e){
            throw new Exception($e->getMessage());
        }

    }

    /**
     * 删除商品
     * @param $id
     * @param $site_id
     */
    public function deleteGoods($id, $site_id){
        $info = WeappGoods::where('id',$id)->findOrEmpty();
        if ($info->isEmpty()) return error(-1, '未获取到商品信息');


        $live = new Live();
        $result = $live->deleteGoods($info->goods_id);
        if ($result['code'] < 0) return $result;

        $res = $info->delete();
        return success(0,'操作成功',$res);
    }

    /**
     * 获取直播商品审核状态
     * @param $id
     */
    public function getGoodsAuditStatus() {
        $prefix = config("database")["connections"]["mysql"]["prefix"];
        $data = model('weapp_goods')->query("SELECT GROUP_CONCAT(goods_id) as goods_id FROM {$prefix}weapp_goods WHERE status = 1");
        if (isset($data[0]) && isset($data[0]['goods_id']) && !empty($data[0]['goods_id'])) {
            $live = new Live();
            $result = $live->getGoodsStatus(explode(',', $data[0]['goods_id']));
            if ($result['code'] < 0) return $result;

            foreach ($result['data'] as $item) {
                if ($item['audit_status'] != 1) {
                    model('weapp_goods')->update([ 'status' => $item['audit_status'] ], [['goods_id', '=', $item['goods_id'] ] ]);
                }
            }
        }
    }

    /**
     * 更新直播商品
     * @param int $goods_id
     * @return bool
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateGoodsAduitStatus($goods_id=0){
        try{
            $wx_goods_id = WeappGoods::where('status',1);
            if ($goods_id){
                $wx_goods_id = $wx_goods_id->where('self_goods_id',$goods_id);
            }
            $wx_goods_id = $wx_goods_id->limit(20)->select()->column('goods_id');

            $wx_goods_id = collect($wx_goods_id)->toArray();

            if($wx_goods_id){
                $wxResult = (new Live())->getGoodsStatus($wx_goods_id);
                foreach ($wxResult['goods'] as $item){
                    $model = WeappGoods::where('goods_id',$item['goods_id'])->findOrEmpty();
                    $model->status = $item['audit_status'];
                    $model->save();
                }
            }
            return true;

        }catch (\Exception $e){
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 直播商品缩略图
     * @param $file_path 图片路径
     * @return string
     */
    public function checkGoodsPic($file_path){
        $image = new Upload();
        // 禁用云上传
        $image->disableCloud();
        $ext = pathinfo($file_path);
        $thumb_path = 'upload/broadcast/goods/'.date('Ymd').'/';

        if (!file_exists($file_path)){
            $image->setPath('broadcast/remotePic/');
            $pull = $image->remotePull($file_path);
            $file_path = $pull['data']['pic_path'];
            $ext = pathinfo($file_path);
        }
        $thumb_path .= $ext['filename'].'_300.'.$ext['extension'];
        if (!file_exists($thumb_path)){
            $image->checkPath(pathinfo($thumb_path,PATHINFO_DIRNAME));
            $image->imageThumb($file_path,$thumb_path,300,300);
        }
        return $thumb_path;
    }
}