{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>领卡客户</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="layui-tab ns-table-tab" lay-filter="use_status">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		<li data-status="1">未使用</li>
		<li data-status="2">已使用</li>
	</ul>
	
	<div class="layui-tab-content">
	    <!-- 列表 -->
	    <table id="charge_list" lay-filter="charge_list"></table>
	</div>
</div>

<!-- 套餐信息 -->
<script type="text/html" id="cover_img">
	<div class="ns-table-tuwen-box">
		<div class='ns-img-box'>
		{{#  if(d.cover_img){  }}
			<img layer-src src="{{ns.img(d.cover_img.split(',')[0])}}"/>
		{{#  }  }}
		</div>
		<div class='ns-font-box'>
			<p class="ns-multi-line-hiding">面值：{{d.face_value}}</p>
			<p class="ns-multi-line-hiding">价格：{{d.buy_price}}</p>
		</div>
	</div>
</script>

<!-- 礼包 -->
<script type="text/html" id="libao">
	<div class="ns-table-tuwen-box">
		<div class='ns-font-box'>
			<p class="ns-multi-line-hiding">积分：{{d.point}}</p>
			<p class="ns-multi-line-hiding">成长值：{{d.growth}}</p>
		</div>
	</div>
</script>

<!-- 用户信息 -->
<script type="text/html" id="user_info">
	<div class="ns-table-tuwen-box">
		<div class='ns-img-box'>
			<img layer-src src="{{ns.img(d.member_img.split(',')[0])}}"/>
		</div>
		<div class='ns-font-box'>
			<p class="ns-multi-line-hiding">{{d.nickname}}</p>
		</div>
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="use_status">
	{{d.use_status == 1 ? '未使用' : '已使用'}}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="card_detail">详情</a>
	</div>
</script>
{/block}

{block name="script"}
<script>
	layui.use(['form', 'element'], function() {
		var table,
			form = layui.form,
			element = layui.element,
			repeat_flag = false; //防重复标识
		form.render();

		table = new Table({
			elem: '#charge_list',
			url: ns.url("memberrecharge://admin/memberrecharge/card_lists"),
			cols: [
				[{
			    	field:'card_account',
					title: '充值卡号',
					unresize: 'false',
					templet: '#member_img'
				}, {
                    field: 'recharge_name',
                    title: '套餐名称',
                    unresize: 'false',
                }, {
                    field: 'cover_img',
                    title: '套餐信息',
                    unresize: 'false',
                    templet: '#cover_img'
                }, {
                    title: '赠送礼包',
                    unresize: 'false',
                    templet: '#libao'
                }, {
                    field: 'pay_type_name',
                    title: '用户信息',
                    unresize: 'false',
                    templet: '#user_info'
                }, {
					field: 'order_no',
					title: '订单号',
					unresize: 'false'
				}, {
					title: '购买时间',
					unresize: 'false',
					templet: function(data) {
						return ns.time_to_date(data.create_time);
					}
				}, {
					title: '使用状态',
					unresize: 'false',
					templet: '#use_status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false'
				}]
			],

		});

		//监听Tab切换
		element.on('tab(use_status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload( {
				page: {
					curr: 1
				},
				where: {
					'use_status': status
				}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'card_detail': //编辑
					location.href = ns.url("memberrecharge://admin/memberrecharge/card_detail?card_id=" + data.card_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteMemberRecharge(recharge_id) {
			layer.confirm('确定要删除该开卡客户吗?', function() {
				if (repeat_flag) return;
				repeat_flag = true;

				$.ajax({
					url: ns.url("memberrecharge://admin/memberrecharge/delete"),
					data: {
						recharge_id: recharge_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		//使失效
		function invalidMemberRecharge(recharge_id) {
			layer.confirm('确定关闭该充值套餐吗?', function() {
				if (repeat_flag) return;
				repeat_flag = true;

				$.ajax({
					url: ns.url("memberrecharge://admin/memberrecharge/invalid"),
					data: {
						recharge_id: recharge_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}
	});

	function add() {
		location.href = ns.url("memberrecharge://admin/memberrecharge/add");
	}
</script>
{/block}