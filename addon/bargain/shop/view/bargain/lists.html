{extend name="app/shop/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>砍价活动列表展示砍价活动的相关信息</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
	<button class="layui-btn ns-bg-color" onclick="add()">添加砍价</button>
	
	<div class="layui-form">
		<div class="layui-input-inline">
		    <input type="text" name="start_time" id="start_time" placeholder="开始时间" class="layui-input" autocomplete="off" readonly>
		    <i class="ns-calendar"></i>
		</div>
		<div class="layui-input-inline end-time">
		    <input type="text" name="end_time" id="end_time" placeholder="结束时间" class="layui-input" autocomplete="off" readonly>
		    <i class="ns-calendar"></i>
		</div>
		
		<div class="layui-input-inline">
			<input type="text" name="bargain_name" placeholder="请输入活动名称" class="layui-input" autocomplete="off">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="bargain_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		{foreach $bargain_status as $k=>$v}
		<li data-status="{$k}">{$v}</li>
		{/foreach}
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="bargain_list" lay-filter="bargain_list"></table>
	</div>
</div>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="detail">查看</a>
		{{# if(d.status == 0){ }}
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="del">删除</a>
		{{# }else if(d.status == 1){ }}
		<a class="layui-btn" lay-event="close">结束</a>
		{{# }else{ }}
		<a class="layui-btn" lay-event="del">删除</a>
		{{# } }}
	</div>
</script>
{/block}

{block name="script"}
<script>
	layui.use(['form', 'element', 'laytpl', 'laydate'], function() {
		var table,
			form = layui.form,
			element = layui.element,
			laytpl = layui.laytpl,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

		//监听Tab切换，以改变地址hash值
		element.on('tab(bargain_tab)', function() {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('data-status')
				}
			});
		});
		
		//开始时间
		laydate.render({
		    elem: '#start_time' //指定元素
		    ,done: function(value, date, endDate){
		        start_time = ns.date_to_time(value);
		
		    }
		});
		//结束时间
		laydate.render({
		    elem: '#end_time' //指定元素
		    ,done: function(value, date, endDate){
		        end_time = ns.date_to_time(value);
		    }
		});

		table = new Table({
			elem: '#bargain_list',
			url: ns.url("bargain://shop/bargain/lists"),
			cols: [
				[{
                    field: 'bargain_name',
					title: '活动名称',
					unresize: 'false',
					width: '16%'
				}, {
					field: 'bargain_num',
					title: '帮砍人数',
					unresize: 'false',
					width: '12%'
				}, {
                    field: 'is_owen',
                    title: '是否自己砍价',
                    unresize: 'false',
					width: '12%',
                    templet:function(data){
						if(data.is_own == 1){
							return '是';
						}else{
						    return '否';
						}
					}
                }, {
			    	field:'bargain_time',
                    title: '<span title="砍价有效期（小时）">砍价有效期（小时）</span>',
                    unresize: 'false',
					width: '12%'
                }, {
					title: '活动时间',
					unresize: 'false',
					width: '24%',
					templet: '#time'
				}, {
			    	field:'status_name',
					title: '状态',
					unresize: 'false',
					width: '10%'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '14%'
				}]
			],

		});
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		//监听Tab切换
		element.on('tab(status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': status
				}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
                case 'detail': //详情
                    location.href = ns.url("bargain://shop/bargain/detail", {"bargain_id": data.bargain_id});
                    break;
				case 'edit': //编辑
					location.href = ns.url("bargain://shop/bargain/edit", {"bargain_id": data.bargain_id});
					break;
				case 'del': //删除
					deleteGroupbuy(data.bargain_id);
					break;
				case 'close': //使结束
					closeGroupbuy(data.bargain_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteGroupbuy(bargain_id) {
			layer.confirm('确定要删除该砍价活动吗?', function() {
				if (repeat_flag) return;
				repeat_flag = true;

				$.ajax({
					url: ns.url("bargain://shop/bargain/delete"),
					data: {
						bargain_id: bargain_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		//使结束
		function closeGroupbuy(bargain_id) {

			layer.confirm('确定要结束该砍价活动吗?', function() {
				if (repeat_flag) return;
				repeat_flag = true;

				$.ajax({
					url: ns.url("bargain://shop/bargain/finish"),
					data: {
						bargain_id: bargain_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}
	});

	function add() {
		location.href = ns.url("bargain://shop/bargain/add");
	}
</script>
{/block}