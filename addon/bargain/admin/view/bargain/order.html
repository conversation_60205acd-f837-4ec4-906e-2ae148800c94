{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<!-- <div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>爆品活动列表展示商品的爆品活动相关信息</li>
			<li>可根据爆品活动名称搜索出具体活动信息</li>
			<li>点击详情按钮，查看活动详细信息</li>
			<li>进行中的活动需先关闭才可进行删除操作</li>
			<li>时间超过活动的结束时间时，活动自动结束</li>
		</ul>
	</div>
</div> -->

<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">筛选</h2>

		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">订单编号：</label>
					<div class="layui-input-inline">
						<input type="text" id="order_no" name="order_no" placeholder="" class="layui-input" autocomplete="off">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">支付流水号：</label>
					<div class="layui-input-inline">
						<input type="text" id="out_trade_no" name="out_trade_no" placeholder="" class="layui-input" autocomplete="off">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">店主账号：</label>
					<div class="layui-input-inline">
						<input type="text" id="username" name="username" placeholder="" class="layui-input" autocomplete="off">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">订单状态：</label>
					<div class="layui-input-inline">
						<select name="status" lay-filter="" lay-search="">
							<option value="">全部</option>
							{volist name="$order_type_list" id="status" }
							<option value="{$key}">{$status}</option>
							{/volist}
						</select>
					</div>
				</div>


			</div>

			<div class="ns-form-row layui-form-item">
				<!-- <div class="layui-inline">
					<label class="layui-form-label">创建时间：</label> -->
					<!-- <div class="layui-input-inline">
						<input type="text" class="layui-input" name="expire_time" id="expire_time" autocomplete="off" >
					</div> -->
					<!-- <div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time"  id="start_time" autocomplete="off" placeholder="开始时间" readonly>
						<i class="ns-calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" id="end_time" autocomplete="off" placeholder="结束时间" readonly>
						<i class="ns-calendar"></i>
					</div>
				</div> -->

				<div class="layui-inline">
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
					<button class="layui-btn ns-bg-color" lay-submit lay-filter="export">导出excel表</button>
					<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab ns-table-tab"  lay-filter="moldbaby_activity_tab">
	<!-- <ul class="layui-tab-title">
		<li class="layui-this" lay-id="">所有活动</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
		<li lay-id="-1">已关闭</li>
		<li lay-id="0">未开始</li>
	</ul> -->
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="bargain_order_list" lay-filter="bargain_order_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
	</div>
</script>

{/block}
{block name="script"}
<script>
	layui.use(['form',  'laydate'], function() {
		var table,
			form = layui.form,
			laytpl = layui.laytpl,
            laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

        //渲染时间
        laydate.render({
            elem: '#start_time',
            type: 'date'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'date'
        });

        //监听Tab切换，以改变地址hash值
        // element.on('tab(moldbaby_activity_tab)', function(){
        //     table.reload({
        //         page: {
        //             curr: 1
        //         },
        //         where: {
        //             'status':this.getAttribute('lay-id')
        //         }
        //     });
        // });

		table = new Table({
			elem: '#bargain_order_list',
			url: ns.url("bargain://admin/bargain/order?bargain_id="+{$bargain_id?$bargain_id:'0'}),
			/*parseData: function(res) {
				if(res.code == 1001) {
					layer.msg(res.message);
					return false;
				}

				return {
					"code": res.code,
					"msg": res.message,
					"count": res.data.count,
					"data": res.data.list
				};
			},*/
			cols: [
				[{
					field: 'order_no',
					title: '订单编号',
					unresize: 'false',
					width: '15%'
				}, {
					field: 'out_trade_no',
					title: '支付流水号',
					unresize: 'false',
					width: '15%'
				}, {
					field: 'order_money',
					title: '订单金额',
					unresize: 'false',
					width: '10%',
				}, {
					field: 'pay_type_name',
					title: '支付方式',
					unresize: 'false',
					width: '10%',
					
				},{
					field: 'pay_status',
					title: '支付状态',
					unresize: 'false',
					width: '10%',
				}, {
					field: 'shop_reward',
					title: '店主奖励',
					unresize: 'false',
					width: '10%',
				},{
					field: 'president_reward',
					title: '会长奖励',
					unresize: 'false',
					width: '10%',
				}, {
					field: 'username',
					title: '店主账号',
					unresize: 'false',
					width: '10%',
				}]
			],
		});
		
		/**
		 * 监听工具栏操作
		 */
		/*table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("admin/moldbaby_activity/edit", {"id": data.id});
					break;
				case 'activity_order': //活动订单
					// location.href = ns.url("admin/moldbaby_activity/activity_order", {"id": data.id});
					break;
				case 'detail': //详情
                    location.href = ns.url("admin/moldbaby_activity/detail", {"id": data.id});
                    break;
//					detailManjian(data);
//					break;
				case 'delete': //删除
					// deleteManjian(data.moldbaby_activity_id);
					break;
				case 'close': //关闭
					close(data.id, data.status);
					break;
			}
		});*/

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});

		form.on('submit(export)', function(data) {
			data.field.bargain_id = {$bargain_id};
			location.href = ns.url("bargain://admin/bargain/export",data.field);
			return false;
		});

	});
	

</script>

<!-- 详情弹框html -->
<script type="text/html" id="detail">
	<table class="layui-table ns-table-detail">
		<colgroup>
			<col width="120">
			<col width="270">
		</colgroup>
		
		<tbody>
			<tr>
				<td>活动名称</td>
				<td>{{d.moldbaby_activity_name}}</td>
			</tr>
			<tr>
				<td>开始时间</td>
				<td>{{ ns.time_to_date(d.start_time, "YYYY-MM-DD hh:mm:ss") }}</td>
			</tr>
			<tr>
				<td>结束时间</td>
				<td>{{ ns.time_to_date(d.end_time, "YYYY-MM-DD hh:mm:ss") }}</td>
			</tr>
			<tr>
				<td id="rule_name">爆品规则</td>
				<td id="rule">
					{{#  var rule = JSON.parse(d.rule_json);  }}
					{{#  for(var key in rule){  }}
						<p>单笔订单满<span class="ns-text-color-red money-num">{{ rule[key].money }}</span>元，立减现金<span class="ns-text-color-red discount_money-num">{{ rule[key].discount_money }}</span>元</p>
						<p>单笔订单满<span class="ns-text-color-red money-num">{{ rule[key].money }}</span>元，立减现金<span class="ns-text-color-red discount_money-num">{{ rule[key].discount_money }}</span>元</p>
					{{#  }  }}
				</td>
			</tr>
			<tr>
				<td>备注</td>
				<td>{{ d.remark }}</td>
			</tr>
		</tbody>
	</table>
</script>
{/block}