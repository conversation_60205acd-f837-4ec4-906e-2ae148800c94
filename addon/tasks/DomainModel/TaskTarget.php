<?php


namespace addon\tasks\DomainModel;


use think\Model;

class TaskTarget
{
    public $model = null;
    /**
     * @var string
     */
    protected $keyName = "";

    /**
     * @var int
     */
    protected $targetValue = 0;

    public function __construct(\addon\tasks\model\TaskTarget $target=null)
    {
        $this->model = $target ?? new \addon\tasks\model\TaskTarget();
    }

    /**
     * @return string
     */
    public function getKeyName(): string
    {
        return $this->keyName;
    }

    /**
     * @param string $keyName
     */
    public function setKeyName(string $keyName): void
    {
        $this->model->keyname = $keyName;
        $this->keyName = $keyName;
    }

    /**
     * @return int
     */
    public function getTargetValue(): int
    {
        return $this->targetValue;
    }

    /**
     * @param int $targetValue
     */
    public function setTargetValue(int $targetValue): void
    {
        $this->model->target_value = $targetValue;
        $this->targetValue = $targetValue;
    }

    public static function createByDb(Model $model)
    {
        $domainModel = new self($model);
        $params = $model->toArray();
        foreach($params as $column=>$v)
        {
            $classMethods = "";
            $terms = explode("_", $column);
            foreach($terms as $s)
            {
                $mname = ucfirst($s);
                $classMethods.=$mname;
            }
            $setMethod = "set".$classMethods;
            if(method_exists($domainModel, $setMethod))
            {
                if($v !== null)
                {
                    $domainModel->$setMethod($v);
                }
            }
        }

        return $domainModel;
    }
}