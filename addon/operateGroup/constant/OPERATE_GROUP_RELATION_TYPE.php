<?php

namespace addon\operateGroup\constant;

use app\model\goods\GoodsModel;

/**
 * 运营组关联类型
 * Class OPERATE_GROUP_RELATION_TYPE
 * @package app\constant\operate_group
 */
class OPERATE_GROUP_RELATION_TYPE
{
    // 管理员
    public const ADMIN = 'admin';
    //先迈经理
    public const MANAGE = 'manage';

    //店铺装修
    public const DIY_VIEW = 'diy_view';

    //banner管理
    public const INDEX_BANNER = 'index_banner';

    // 商品
    public const GOODS = GoodsModel::class;

    //秒杀
    public const SECKILL = 'seckill';

    //拼团
    public const PINTUAN = 'pintuan';

	
    // 商品优惠券
    public const GOODSCOUPON = "goodscoupon";

    // 多件折扣
    public const MULTIPLE_DISCOUNT = "multiple_discount";

    // 专题活动
    public const Topic = "topic";

    // 优惠券规则
    public const GOODS_COUPON_RULE = 'goods_coupon_rule';

}