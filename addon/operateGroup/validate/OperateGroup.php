<?php

namespace addon\operateGroup\validate;

use addon\operateGroup\constant\OPERATE_GROUP_STATUS;
use think\Validate;

class OperateGroup extends Validate
{
    protected $rule = [
        'operate_group_id|运营组ID' => 'require|number',
        'operate_group_name|运营组名' => 'require|max:20',
        'description|描述' => 'require|max:40',
        'status|状态' => ['require', 'in' => [
            OPERATE_GROUP_STATUS::ENABLE,
            OPERATE_GROUP_STATUS::DISABLE
        ]],
        'admin_id|运营组管理员' => 'require|number',
        'manage_ids|经理列表' => 'require|array'
    ];

    protected $scene = [
        'add' => [
            'operate_group_name',
            'description',
            'status',
        ],
        'edit' => [
            'operate_group_id',
            'operate_group_name',
            'description',
            'status',
//            'admin_id',
//            'manage_ids',
        ],
    ];
}