<script type="text/html" id="operate_group_info">
    <div class="layui-form ns-form">

        {{# if(d.operate_group_id){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">运营组ID：</label>
            <div class="layui-input-block">
                {{ d.operate_group_id}}
            </div>
            <input name="operate_group_id" type="hidden" value="{{ d.operate_group_id||0 }}" />
        </div>
        {{# } }}

        <div class="layui-form-item">
            <label class="layui-form-label"><span class="required">*</span>运营组名：</label>
            <div class="layui-input-block">
                <input name="operate_group_name" type="text" autocomplete="off"
                       lay-verify="required|checkOperateGroupName"
                       value="{{ d.operate_group_name||'' }}"
                       class="layui-input ns-len-long">
            </div>
            <div class="ns-word-aux">请输入运营组名</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span class="required">*</span>运营组描述：</label>
            <div class="layui-input-block">
                <input name="description" type="text" autocomplete="off"
                       value="{{ d.description||'' }}"
                       lay-verify="required|checkDescription"
                       class="layui-input ns-len-long">
            </div>
            <div class="ns-word-aux">请输入运营组描述</div>
        </div>

<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label">运营组状态：</label>-->
<!--            <div class="layui-input-block">-->
<!--                <input type="checkbox" name="status" value="1" lay-skin="switch"-->
<!--                       {{# if(d.status=== 1){ }}-->
<!--                       checked-->
<!--                       {{# } }}-->
<!--                />-->
<!--            </div>-->
<!--            <div class="ns-word-aux">运营组状态被禁用则无法进入管理</div>-->
<!--        </div>-->

        <div class="ns-form-row">
            <button class="layui-btn ns-bg-color" lay-submit lay-filter="operate_group_save">{{d.operate_group_id ? '保存'
                : '新增'}}
            </button>
            <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
        </div>
    </div>
</script>

<script>
    layui.use(['form', 'transfer', 'laytpl', 'layer'], function () {
        let {laytpl, form, layer} = layui
        operateGroup.info = (data = {}) => {
            console.log(data)
            layer.open({
                type: 1,
                title: (data.operate_group_id ? '编辑' : '添加') + '运营组',
                shadeClose: false,
                area: ['850px', '400px'],
                content: laytpl($('#operate_group_info').html()).render(data)
            });
            form.render();
        }

        operateGroup.save = (data) => {
            console.log(data)
            if (operateGroup.repeat_flag) return;
            operateGroup.repeat_flag = true;

            $.post(ns.url("operateGroup://admin/operateGroup/submit"), {
                ...data.field,
                status: data.field.status || 0
            }, (res) => {
                console.log(res, this)
                operateGroup.repeat_flag = false;
                if (res.code === 0) {
                    layer.confirm(res.message, {
                        title: '操作提示',
                        closeBtn:false,
                        btn: ['确认'],
                        yes: function () {
                            location.reload()
                        }
                    })
                } else {
                    layer.msg(res.message);
                }
            });
        }

        /**
         * 监听提交
         */
        form.on('submit(operate_group_save)', operateGroup.save);

        /**
         * 表单验证
         */
        form.verify({
            checkOperateGroupName: function (value) {
                if ($.trim(value).length > 20) {
                    return '不能大于20个字符';
                }
            },
            checkDescription: function (value) {
                if ($.trim(value).length > 40) {
                    return '不能大于40个字符';
                }
            },
        });
    });
</script>