<style>
    .layui-layer-content .layui-form {
        padding: 0;
    }

    .layui-layer-page .layui-table, .layui-layer-page .layui-table-view {
        margin: 0;
    }
    .layui-table-search .layui-input,.layui-table-search .layui-btn{
        height: 38px;
    }
    .layui-table-search .layui-form-item{
        margin-bottom: 5px;
    }
</style>
<script type="text/html" id="manage_select_operation">
    {{# if(!d.is_relation_group){ }}
    <a class="layui-btn layui-btn-normal" lay-event="add_operate_group_manage">添加</a>
    {{# } }}
</script>

<script>
    layui.use(['form', 'table', 'laytpl', 'layer'], function () {
        let {form, layer, table} = layui
        operateGroup.manage.select = () => {
            layer.open({
                type: 1,
                title: '选择经理',
                shadeClose: false,
                area: ['850px', '500px'],
                content: '<div class="layui-table-search">\n' +
                    '       <form class="layui-form layui-form-pane">\n' +
                    '           <div class="layui-form-item">\n' +
                    '               <div class="layui-inline">\n' +
                    '                   <label class="layui-form-label">手机号</label>\n' +
                    '                   <div class="layui-input-inline">\n' +
                    '                       <input class="layui-input" name="keyword" autocomplete="off">\n' +
                    '                   </div>\n' +
                    '                   <button type="button" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn">搜 索</button>\n' +
                    '               </div>\n' +
                    '           </div>\n' +
                    '       </form>\n' +
                    '       <table id="manage_select" lay-filter="manage_select"></table>' +
                    '</div>',
                success() {
                    table = new Table({
                        elem: '#manage_select',
                        // size: 'sm', //小尺寸的表格
                        page: false,
                        height: 350,
                        url: ns.url("operateGroup://admin/operateGroup/manageList", {
                            action: 'select',
                            operate_group_id: {$operate_group_id}
                        }),
                        cols: [[
                            {
                                field: 'mobile',
                                title: '手机号',
                                width: '20%',
                                unresize: false,
                            }, {
                                field: 'user_count',
                                title: '下属团队人数',
                                width: '18%',
                                unresize: 'false',
                            }, {
                                field: 'operate_group_name',
                                title: '所属运营组',
                                width: '20%',
                                unresize: 'false',
                                templet: function (data) {
                                    return data.is_relation_group ? data.bind_operate_group.operate_group_name : ''
                                }
                            }, {
                                title: '操作',
                                width: '15%',
                                toolbar: '#manage_select_operation',
                                unresize: 'false'
                            }
                        ]]
                    });

                    /**
                     * 监听工具栏操作
                     */
                    table.tool(function (obj) {
                        var data = obj.data;
                        switch (obj.event) {
                            case 'add_operate_group_manage': //编辑
                                operateGroup.manage.add(data)
                                break;
                        }
                    });

                    // 搜索
                    form.on('submit(data-search-btn)', function (data){
                        table.reload({
                            where:data.field
                        })
                    })
                }
            });
        }

        operateGroup.manage.add = (data) => {
            console.log(data)
            if (operateGroup.repeat_flag) return;
            operateGroup.repeat_flag = true;

            $.post(ns.url("operateGroup://admin/operateGroup/manageList", {
                action: 'add',
                operate_group_id: {$operate_group_id},
                manage_id: data.id
            }), (res) => {
                console.log(res, this)
                operateGroup.repeat_flag = false;
                if (res.code === 0) {
                    layer.confirm(res.message, {
                        title: '操作提示',
                        closeBtn: false,
                        btn: ['确认'],
                        yes: function () {
                            back()
                            operateGroup.manage.list()
                        }
                    })
                } else {
                    layer.msg(res.message);
                }
            });
        }

        /**
         * 监听提交
         */
        form.on('submit(operate_group_save)', operateGroup.save);

        /**
         * 表单验证
         */
        form.verify({
            checkOperateGroupName: function (value) {
                if ($.trim(value).length > 20) {
                    return '不能大于20个字符';
                }
            },
            checkDescription: function (value) {
                if ($.trim(value).length > 40) {
                    return '不能大于40个字符';
                }
            },
        });
    });
</script>