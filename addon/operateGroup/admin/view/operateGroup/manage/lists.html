<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>每个经理只能从属于一个运营组</li>
            <li>已有运营组的无法添加，只能先移除后才可以添加</li>
            <li>经理加入运营组后，该经理团队自动加入到该运营组</li>
        </ul>
    </div>
</div>
<!-- 搜索框 -->
<div class="ns-single-filter-box">
    <button class="layui-btn ns-bg-color" onClick="operateGroup.manage.select()">添加组员</button>
</div>

<!-- 列表 -->
<table id="admin_list" lay-filter="admin_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="remove">移除</a>
    </div>
</script>