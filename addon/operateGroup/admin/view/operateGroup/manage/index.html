{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
{include file="operateGroup/manage/lists" /}
{include file="operateGroup/manage/select" /}
{/block}
{block name="script"}
<script>
    let table, form;
    let operateGroup = {repeat_flag: false, manage: {}};

    layui.use(['form', 'layer', 'laytpl'], function () {
        let form = layui.form
        layer = layui.layer;
        form.render();

        operateGroup.manage.list = (data) => {
            table = new Table({
                elem: '#admin_list',
                page: false,
                url: location.href,
                cols: [
                    [{
                        field: 'id',
                        title: '先迈ID',
                        width: '10%',
                        unresize: false,
                    },{
                        field: 'mobile',
                        title: '手机号码',
                        width: '20%',
                        unresize: false,
                    }, {
                        field: 'user_count',
                        title: '团队人数',
                        width: '20%',
                        unresize: 'false',
                    }, {
                        title: '操作',
                        width: '18%',
                        toolbar: '#operation',
                        unresize: 'false'
                    }]
                ],
            });

            /**
             * 监听工具栏操作
             */
            table.tool(function (obj) {
                let data = obj.data;
                if (operateGroup.repeat_flag) return false
                switch (obj.event) {
                    case 'remove': //编辑
                        operateGroup.manage.remove(data)
                        break;
                }
            });

            /**
             * 搜索功能
             */
            form.on('submit(search)', function (data) {
                table.reload({
                    page: {
                        curr: 1
                    },
                    where: data.field
                });
            });
        }

        operateGroup.manage.remove = (data) => {
            layer.confirm('确定要删除该组员吗?', function () {
                operateGroup.repeat_flag = true
                $.post(ns.url("operateGroup://admin/operateGroup/manageList", {
                    action: 'remove',
                    operate_group_id: {$operate_group_id},
                    manage_id: data.id
                }), function (res) {
                    layer.msg(res.message);
                    operateGroup.repeat_flag = false;
                    if (res.code === 0) {
                        table.reload({});
                    }
                });
            }, function () {
                back()
            });
        }
        operateGroup.manage.list()
    });

    function back() {
        layer.closeAll()
        operateGroup.repeat_flag = false
    }
</script>
{/block}