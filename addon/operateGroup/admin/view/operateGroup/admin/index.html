{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
{include file="operateGroup/admin/lists" /}
{include file="operateGroup/admin/select" /}
{/block}
{block name="script"}
<script>
    let table, form;
    let operateGroup = {repeat_flag: false, admin: {}};

    layui.use(['form', 'layer', 'laytpl'], function () {
        let form = layui.form
        layer = layui.layer;
        form.render();

        operateGroup.admin.list = (data) => {
            table = new Table({
                elem: '#admin_list',
                page: false,
                url: location.href,
                cols: [
                    [{
                        field: 'username',
                        title: '管理员账号',
                        width: '20%',
                        unresize: false,
                    }, {
                        field: 'realname',
                        title: '真实姓名',
                        width: '20%',
                        unresize: 'false',
                    }, {
                        field: 'group_name',
                        title: '用户组',
                        width: '20%',
                        unresize: 'false',
                    }, {
                        title: '操作',
                        width: '18%',
                        toolbar: '#operation',
                        unresize: 'false'
                    }]
                ],
            });

            /**
             * 监听工具栏操作
             */
            table.tool(function (obj) {
                let data = obj.data;
                if (operateGroup.repeat_flag) return false
                switch (obj.event) {
                    case 'remove': //编辑
                        operateGroup.admin.remove(data)
                        break;
                }
            });

            /**
             * 搜索功能
             */
            form.on('submit(search)', function (data) {
                table.reload({
                    page: {
                        curr: 1
                    },
                    where: data.field
                });
            });
        }

        operateGroup.admin.remove = (data) => {
            layer.confirm('确定要删除该管理员吗?', function () {
                operateGroup.repeat_flag = true
                $.post(ns.url("operateGroup://admin/operateGroup/adminList", {
                    action: 'remove',
                    operate_group_id: {$operate_group_id},
                    admin_id: data.uid
                }), function (res) {
                    layer.msg(res.message);
                    operateGroup.repeat_flag = false;
                    if (res.code === 0) {
                        table.reload({});
                    }
                });
            }, function () {
                back()
            });
        }
        operateGroup.admin.list()
    });

    function back() {
        layer.closeAll()
        operateGroup.repeat_flag = false
    }
</script>
{/block}