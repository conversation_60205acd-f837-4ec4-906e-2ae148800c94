<!--&lt;!&ndash; 操作 &ndash;&gt;-->
<!--<script type="text/html" id="operation">-->
<!--    <div class="ns-table-btn">-->
<!--        <a class="layui-btn" lay-event="remove">移除</a>-->
<!--    </div>-->
<!--</script>-->
<!-- 操作 -->
<style>
    .layui-layer-content .layui-form {
        padding: 0;
    }

    .layui-layer-page .layui-table, .layui-layer-page .layui-table-view {
        margin: 0;
    }
</style>
<script type="text/html" id="admin_select_operation">
    {{# if(!d.operate_group_name){ }}
    <a class="layui-btn layui-btn-normal" lay-event="add_operate_group_admin">添加</a>
    {{# } }}
</script>

<script>
    layui.use(['form', 'table', 'laytpl', 'layer'], function () {
        let {form, layer, table} = layui
        operateGroup.admin.select = () => {
            layer.open({
                type: 1,
                title: '选择管理员',
                shadeClose: false,
                area: ['850px', '450px'],
                content: '<table id="admin_select" lay-filter="admin_select"></table>',
                success() {
                    table = new Table({
                        elem: '#admin_select',
                        // size: 'sm', //小尺寸的表格
                        page: false,
                        height: 350,
                        url: ns.url("operateGroup://admin/operateGroup/adminList", {
                            action: 'select',
                            operate_group_id: {$operate_group_id}
                        }),
                        cols: [[
                            {
                                field: 'realname',
                                title: '姓名',
                                width: '15%',
                                unresize: false,
                                templet: function (data) {
                                    return data.realname || '未填写'
                                }
                            },
                            {
                                field: 'username',
                                title: '账号',
                                width: '20%',
                                unresize: false
                            }, {
                                field: 'group_name',
                                title: '管理组',
                                width: '20%',
                                unresize: 'false',
                                sort: true
                            }, {
                                field: 'operate_group_name',
                                title: '所属运营组',
                                width: '20%',
                                unresize: 'false',
                                sort: true
                            }, {
                                title: '操作',
                                width: '15%',
                                toolbar: '#admin_select_operation',
                                unresize: 'false'
                            }
                        ]]
                    });

                    /**
                     * 监听工具栏操作
                     */
                    table.tool(function (obj) {
                        var data = obj.data;
                        switch (obj.event) {
                            case 'add_operate_group_admin': //编辑
                                operateGroup.admin.add(data)
                                break;
                        }
                    });
                }
            });
        }

        operateGroup.admin.add = (data) => {
            console.log(data)
            if (operateGroup.repeat_flag) return;
            operateGroup.repeat_flag = true;

            $.post(ns.url("operateGroup://admin/operateGroup/adminList", {
                action: 'add',
                operate_group_id: {$operate_group_id},
                admin_id: data.uid
            }), (res) => {
                console.log(res, this)
                operateGroup.repeat_flag = false;
                if (res.code === 0) {
                    layer.confirm(res.message, {
                        title: '操作提示',
                        closeBtn: false,
                        btn: ['确认'],
                        yes: function () {
                            back()
                            operateGroup.admin.list()
                        }
                    })
                } else {
                    layer.msg(res.message);
                }
            });
        }

        /**
         * 监听提交
         */
        form.on('submit(operate_group_save)', operateGroup.save);

        /**
         * 表单验证
         */
        form.verify({
            checkOperateGroupName: function (value) {
                if ($.trim(value).length > 20) {
                    return '不能大于20个字符';
                }
            },
            checkDescription: function (value) {
                if ($.trim(value).length > 40) {
                    return '不能大于40个字符';
                }
            },
        });

        // // 拉取经理列表
        // $.get(ns.url("operateGroup://admin/operateGroup/manageList"), result => {
        //     let {code, data} = result
        //     if (code === 0 && data) {
        //         // 经理选择界面
        //         transfer.render({
        //             elem: '#manager_list'  //绑定元素
        //             , data: data.list.map(_ => ({"value": _.id, "title": _.title, "disabled": _.is_relation_group}))
        //             , value: []
        //             , title: ['全选', '全选']
        //             , showSearch: true
        //             , width: 300
        //             , height: 400
        //             , id: 'manager_list' //定义索引
        //         });
        //     }
        // })
    });
</script>