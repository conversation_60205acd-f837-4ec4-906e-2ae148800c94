<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>运营组在原用户组的基础上，增加多数据分割和限制</li>
            <li>当管理员被加入到运营组管理员时，此管理员只能看到对应运营组的数据</li>
        </ul>
    </div>
</div>

<!-- 搜索框 -->
<div class="ns-single-filter-box">
    <button class="layui-btn ns-bg-color" onclick="operateGroup.info()">添加运营组</button>

    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_keys" placeholder="搜索运营组" autocomplete="off" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="user_list" lay-filter="user_list"></table>


<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="admin_setting">管理员设置</a>
        <a class="layui-btn" lay-event="manage_setting">组员管理</a>
    </div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
    {{ d.status == 1 ? '正常' : '禁用'}}
</script>