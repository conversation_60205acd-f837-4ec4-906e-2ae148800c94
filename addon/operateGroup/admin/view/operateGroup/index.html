{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
{include file="operateGroup/lists" /}
{include file="operateGroup/info" /}
{/block}
{block name="script"}
<script>
    let table, form;
    let operateGroup = {repeat_flag: false};

    layui.use(['form', 'layer', 'laytpl'], function () {
        let form = layui.form
        layer = layui.layer;
        form.render();

        operateGroup.list = () => {
            table = new Table({
                elem: '#user_list',
                page: false,
                url: ns.url("operateGroup://admin/operateGroup/lists"),
                cols: [
                    [{
                        field: 'operate_group_id',
                        title: 'ID',
                        width: '8%',
                        unresize: 'false'
                    }, {
                        field: 'operate_group_name',
                        title: '运营组名',
                        width: '20%',
                        unresize: false,
                    }, {
                        field: 'manage_count',
                        title: '成员人数',
                        width: '10%',
                        unresize: 'false',
                    },
                    // {
                    //     field: 'status',
                    //     title: '运营组状态',
                    //     width: '10%',
                    //     unresize: 'false',
                    //     templet: '#status'
                    // },
                        {
                        title: '操作',
                        width: '18%',
                        toolbar: '#operation',
                        unresize: 'false'
                    }]
                ],
            });

            /**
             * 监听工具栏操作
             */
            table.tool(function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    case 'edit': //编辑
                        operateGroup.info(data)
                        break;
                    case 'admin_setting': //管理员设置
                        location.href = ns.url("operateGroup://admin/operateGroup/adminList", {
                            action: 'list',
                            operate_group_id: data.operate_group_id
                        })
                        break;
                    case 'manage_setting': //组员管理
                        location.href = ns.url("operateGroup://admin/operateGroup/manageList", {
                            action: 'list',
                            operate_group_id: data.operate_group_id
                        })
                        break;
                }
            });

            /**
             * 搜索功能
             */
            form.on('submit(search)', function (data) {
                table.reload({
                    where: data.field
                });
            });
        }
        operateGroup.list()
    });

    function back() {
        layer.closeAll()
        operateGroup.repeat_flag = false
    }
</script>
{/block}