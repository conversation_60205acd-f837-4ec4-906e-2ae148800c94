<?php

namespace addon\operateGroup\admin\controller;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_SAVE_MODE;
use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\service\OperateGroupRelationService;
use addon\operateGroup\service\OperateGroupService;
use app\admin\controller\BaseAdmin;
use think\exception\ValidateException;
use think\Request;

/**
 * 运营组
 * Class OperateGroup
 * @package addon\operateGroup\admin\controller
 */
class OperateGroup extends BaseAdmin
{
    // 运营组列表模版
    public function index() {
        $this->forthMenu();
        return $this->fetch('operateGroup/index');
    }

    // 管理员操作
    public function adminList(Request $request, OperateGroupRelationService $operateGroupRelationService, OperateGroupService $operateGroupService) {
        $operate_group_id = $request->get('operate_group_id', 0);
        $admin_id = $request->get('admin_id', 0);
        $this->assign('operate_group_id', $operate_group_id);

        // 非ajax则加载页面
        if (!request()->isAjax()) {
            $this->forthMenu();
            return $this->fetch('operateGroup/admin/index');
        }

        $message = '';
        $action = $request->get('action', 'list');
        switch ($action) {
            // 管理员列表
            case 'list':
                $result = [
                    'list' => $operateGroupRelationService
                        ->with('admin')
                        ->selectByOperateGroupId($operate_group_id, OPERATE_GROUP_RELATION_TYPE::ADMIN)
                        ->column('admin')
                ];
                break;
            // 移除管理员
            case 'remove':
                $result = $operateGroupRelationService->save(
                    $operate_group_id,
                    OPERATE_GROUP_RELATION_TYPE::ADMIN,
                    $admin_id,
                    OPERATE_GROUP_RELATION_SAVE_MODE::DELETE
                );
                break;
            // 选择管理员
            case 'select':
                $result = ['list' => $operateGroupService->adminList()->map(function ($item) {
                    return [
                        'uid' => $item['uid'],
                        'group_name' => $item['group_name'],
                        'username' => $item['username'],
                        'realname' => $item['realname'],
                        'operate_group_name' => $item['belongOperateGroup'] ? $item['belongOperateGroup']['operateGroup']['operate_group_name'] : '',
                        'operate_group_id' => $item['belongOperateGroup'] ? $item['belongOperateGroup']['operate_group_id'] : 0
                    ];
                })];
                break;
            // 添加管理员
            case 'add':
                if (!empty($operateGroupRelationService->selectByRelationId($admin_id, OPERATE_GROUP_RELATION_TYPE::ADMIN, true))) {
                    return json(error(-1, '不允许加入多个运营组', ''));
                }
                $result = $operateGroupRelationService->save(
                    $operate_group_id,
                    OPERATE_GROUP_RELATION_TYPE::ADMIN,
                    $admin_id,
                    OPERATE_GROUP_RELATION_SAVE_MODE::APPEND
                );
                $message = $result ? '添加成功' : '添加失败';
                break;
            default:
                return json(error(-1, '错误的action', ''));
        }

        // 记录日志
        $action_name = ['remove' => '删除', 'add' => '添加'];
        if (isset($action_name[$action])) {
            $this->addLog($action_name[$action] . '运营组管理员' . ",运营组ID:{$operate_group_id},管理员ID:{$admin_id}");
        }
        $message = $message ?: ($result ? '操作成功' : '操作失败');
        return json(success(0, $message, $result));
    }

    // 运营组列表
    public function lists(Request $request) {
        $operateGroup = \addon\operateGroup\model\OperateGroup::order('operate_group_id', 'desc')->with('adminRelation.admin');
        if (!empty($search_keys = $request->get('search_keys', ''))) {
            $operateGroup = $operateGroup->where('operate_group_name', 'like', "%{$search_keys}%");
        }
        $list = $operateGroup->withCount('manage')->select();
        return json(success(0, '', ['list' => $list->toArray()]));
    }

    // 运营组数据提交提交
    public function submit(OperateGroupService $operateGroupService, Request $request) {
        $post = $request->only(['operate_group_id', 'operate_group_name', 'description', 'status', 'admin_id', 'manage_ids']);
        $is_edit = isset($post['operate_group_id']);
        try {
            validate(\addon\operateGroup\validate\OperateGroup::class)->scene($is_edit ? 'edit' : 'add')->check($post);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return json(error(-1, $e->getError()));
        }
        if ($is_edit) {
            $operate_group = $operateGroupService->with('adminRelations.admin')->find($post['operate_group_id']);
            if (empty($operate_group)) {
                $this->error('错误的运营组ID');
            }
            $result = $operateGroupService->save($operate_group->operate_group_id, $post);
        } else {
            $result = $operateGroupService->create($post['operate_group_name'], $post['status'], $post['description']);
        }
        // 记录日志
        $this->addLog(($is_edit ? '编辑' : '添加') . "运营组,ID:{$result['operate_group_id']},名称:{$result['operate_group_name']}");
        if ($result) {
            return json(success(0, '操作成功'));
        }
        return json(error(-1, '操作失败'));
    }

    // 运营组经理操作
    public function manageList(OperateGroupService $operateGroupService, OperateGroupRelationService $operateGroupRelationService, Request $request) {
        $operate_group_id = $request->get('operate_group_id', 0);
        $manage_id = $request->get('manage_id', 0);
        $keyword = $request->get('keyword', '');
        $this->assign('operate_group_id', $operate_group_id);

        // 非ajax则加载页面
        if (!request()->isAjax()) {
            $this->forthMenu();
            return $this->fetch('operateGroup/manage/index');
        }

        $message = '';
        $action = $request->get('action', 'list');
        switch ($action) {
            // 经理列表
            case 'list':
                $result = [
                    'list' => $operateGroupService->managerList($operate_group_id)
                ];
                break;
            // 删除经理
            case 'remove':
                $result = $operateGroupRelationService->save(
                    $operate_group_id,
                    OPERATE_GROUP_RELATION_TYPE::MANAGE,
                    $manage_id,
                    OPERATE_GROUP_RELATION_SAVE_MODE::DELETE
                );
                break;
            // 选择经理列表
            case 'select':
                $result = ['list' => $operateGroupService->managerList(0, ['keyword' => $keyword])];
                break;
            // 添加经理到运营组
            case 'add':
                if (!empty($operateGroupRelationService->selectByRelationId($manage_id, OPERATE_GROUP_RELATION_TYPE::MANAGE, true))) {
                    return json(error(-1, '不允许加入多个运营组', ''));
                }
                $result = $operateGroupRelationService->save(
                    $operate_group_id,
                    OPERATE_GROUP_RELATION_TYPE::MANAGE,
                    $manage_id,
                    OPERATE_GROUP_RELATION_SAVE_MODE::APPEND
                );
                break;
            default:
                return json(error(-1, '错误的action', ''));
        }
        // 记录日志
        $action_name = ['remove' => '删除', 'add' => '添加'];
        if (isset($action_name[$action])) {
            $this->addLog($action_name[$action] . '运营组组员' . ",运营组ID:{$operate_group_id},组员ID:{$manage_id}");
        }

        $message = $message ?: ($result ? '操作成功' : '操作失败');
        return json(success(0, $message, $result));
    }
}