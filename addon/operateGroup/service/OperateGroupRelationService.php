<?php

namespace addon\operateGroup\service;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_SAVE_MODE;
use addon\operateGroup\model\OperateGroupRelation;
use app\Exceptions\ServerException;
use think\facade\Db;

class OperateGroupRelationService
{
    /**
     * 运营组关联模型
     * @var OperateGroupRelation
     */
    private $model;

    public function __construct() {
        $this->model = new OperateGroupRelation();
    }

    /**
     * 关联预载入
     * @param string $relation
     * @return $this
     */
    public function with(string $relation): OperateGroupRelationService {
        $this->model = $this->model->with($relation);
        return $this;
    }

    /**
     * 检查关联数据
     * @param string $type 关联类型，如是模型则会自动检验主键
     * @param array|int $ids 关联主键
     * @return array|int
     * @throws ServerException
     */
    public function checkRelationData(string $type, $ids) {
        if (!is_array($ids)) $ids = [$ids];
        // 如关联类型是模型则验证主键
        if (class_exists($type) && ($model = new $type)) {
            if (($type_parent_class = get_parent_class($model)) != 'think\Model') {
                throw new ServerException($type . ' 必须的 think\Model 的子类，当前的父类是 ' . $type_parent_class);
            }
            $pk = $model->getPk();
            $pks = $model->whereIn($pk, $ids)->column($pk);
            // 对比差值，验证合法性
            $diff = array_diff($ids, $pks);
            if (count($diff) > 0) {
                throw new ServerException('错误的的主键ID：' . implode(',', $diff));
            }
        }
        return $ids;
    }

    /**
     * 查找运营组关联数据
     * @param int $operate_group_id 运营组ID
     * @param string|array $relation_type 关联类型，具体看 thinkphp 模型关联 实现
     * @param bool $one 是否只返回单个数据
     * @return OperateGroupRelation[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function selectByOperateGroupId(int $operate_group_id, $relation_type = '', bool $one = false) {
        $where = ['operate_group_id' => $operate_group_id];
        if (!empty($relation_type)) {
            $where['relation_type'] = $relation_type;
        }
        $method = $one ? '_find' : '_select';
        return $this->$method($where);
    }

    /**
     * 根据关联ID和类型查询数据
     * @param int $relation_id 关联ID
     * @param string $relation_type 关联类型
     * @param bool $one 是否只返回单个数据
     * @return OperateGroupRelation[]|\think\Collection|OperateGroupRelation|null
     */
    public function selectByRelationId(int $relation_id, string $relation_type, bool $one = false) {
        $method = $one ? '_find' : '_select';
        return $this->$method(['relation_id' => $relation_id, 'relation_type' => $relation_type]);
    }

    /**
     * 公共查询方法
     * @param array $where 查询条件
     */
    private function _find(array $where) {
        $data = $this->_select($where, $this->model->limit(1));
        if ($data->isEmpty()) {
            return null;
            // throw new ServerException('找不到运营组关联数据');
        }
        return $data->first();
    }

    /**
     * 统一查询方法
     * @param array $where
     * @param null $model
     * @return OperateGroupRelation[]|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function _select(array $where, $model = null) {
        return ($model ?: $this->model)->where($where)->select();
    }

    /**
     * 修改运营组
     * @param int $operate_group_id 运营组ID
     * @param string $relation_type 关联类型
     * @param int|array $relation_ids 关联ID
     * @param string $mode 更新类型：覆盖，追加、删除
     * @return bool
     * @throws ServerException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function save(int $operate_group_id, string $relation_type, $relation_ids, string $mode = OPERATE_GROUP_RELATION_SAVE_MODE::COVER): bool {
        $old_data = $this->selectByOperateGroupId($operate_group_id);
        $old_relation_id = $old_data->column('relation_id');
        $check_data = $this->checkRelationData($relation_type, $relation_ids);
        $update_data = [];
        Db::startTrans();
        try {
            switch ($mode) {
                // 刪除
                case OPERATE_GROUP_RELATION_SAVE_MODE::DELETE:
                    $this->delete($operate_group_id, $relation_type, $check_data);
                    break;
                // 追加，旧的跳过更新
                case OPERATE_GROUP_RELATION_SAVE_MODE::APPEND:
                    $update_data = array_values(array_diff($check_data, array_unique($old_relation_id)));
                    break;
                // 覆盖更新
                case OPERATE_GROUP_RELATION_SAVE_MODE::COVER:
                    $update_data = $check_data;
                    break;
            }

            if (count($update_data) > 0) {
                // 能到下面都是需要入库的操作
                foreach ($update_data as $id) {
                    $temp_data = [
                        'operate_group_id' => $operate_group_id,
                        'relation_id' => $id,
                        'relation_type' => $relation_type,
                    ];
                    $old_data_index = array_search($id, $old_relation_id);
                    // 如存在旧数据，利用查询出来的model保存数据
                    if (!($old_data_index === false ? $this->model->create($temp_data) : $old_data[$old_data_index]->save($temp_data))) {
                        throw new ServerException('关联数据入库失败');
                    };
                }
            }
        } catch (\Exception $exception) {
            Db::rollback();
            throw $exception;
        }
        Db::commit();
        return true;
    }

    /**
     * 删除运营组关联数据
     * @param int $operate_group_id 运营组OD
     * @param string $relation_type 删除的类型
     * @param array $relation_ids 如指定关联ID则删除选中部分
     * @return int
     */
    public function delete(int $operate_group_id, string $relation_type, array $relation_ids = []): int {
        $model = $this->model->where([
            'operate_group_id' => $operate_group_id,
            'relation_type' => $relation_type
        ]);
        if (!empty($relation_ids)) {
            $model = $model->whereIn('relation_id', $relation_ids);
        }
        return $model->delete();
    }
}