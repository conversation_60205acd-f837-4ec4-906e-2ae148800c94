<?php

namespace addon\operateGroup\service;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use addon\operateGroup\constant\OPERATE_GROUP_STATUS;
use addon\operateGroup\model\OperateGroup;
use addon\operateGroup\model\OperateGroupRelation;
use app\constant\operate_group\SNAPSHOT_RELATION_TYPE;
use app\Exceptions\ServerException;
use app\model\user\OperateGroupSnapshotRelationModel;
use app\model\user\SyncUsersModel;
use app\model\user\UserModel;
use think\facade\Db;

/**
 * 运营组服务
 * Class OperateGroupService
 * @package addon\operateGroup\service
 */
class OperateGroupService
{
    /**
     * @var OperateGroup
     */
    private $model;

    public function __construct() {
        $this->model = new OperateGroup();
    }

    /**
     * 关联预载入
     * @param string $relation
     * @return $this
     */
    public function with(string $relation): OperateGroupService {
        $this->model = $this->model->with($relation);
        return $this;
    }

    /**
     * 创建运营组
     * @param string $operate_group_name
     * @param int $status
     * @param string $description
     * @return OperateGroup|\think\Model
     */
    public function create(string $operate_group_name, int $status = OPERATE_GROUP_STATUS::DISABLE, string $description = '') {
        return $this->model->create([
            'operate_group_name' => $operate_group_name,
            'status' => $status,
            'description' => $description,
        ]);
    }

    /**
     * 修改运营组
     * @param int $operate_group_id
     * @param array $data
     * @return OperateGroup|array|false|\think\Model
     * @throws ServerException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function save(int $operate_group_id, array $data) {
        $model = $this->find($operate_group_id);
        return !$model->save($data) ?: $model;
    }

    /**
     * 查找运营组
     * @param int $operate_group_id 运营组ID
     * @return OperateGroup|array|\think\Model
     * @throws ServerException
     */
    public function find(int $operate_group_id) {
        $where = ['operate_group_id' => $operate_group_id];
        return $this->_find($where);
    }

    private function _find($where) {
        $info = $this->model->where($where)->find();
//        if (empty($info)) {
//            throw new ServerException('错误的运营组条件:' . json_encode($where));
//        }
        return $info;
    }

    /**
     * 删除运营组
     * @param int $operate_group_id
     * @return bool
     * @throws ServerException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delete(int $operate_group_id) {
        return $this->find($operate_group_id)->delete();
    }

    /**
     * 获取运营组管理员列表
     * @return UserModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function adminList() {
        return UserModel::with('belongOperateGroup.operateGroup')->where([
            'app_module' => 'admin',
            'status' => '1',
            'is_admin' => 0
        ])->order('group_id')->order('uid', 'desc')->select();
    }

    /**
     * 获取当前经理列表
     * @param int $operate_group_id 运营组ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function managerList($operate_group_id = 0, $where = []) {
        $keyword = trim($where['keyword'] ?? '');
        // 经理列表主查询
        $xm_manage_orm = SyncUsersModel::field('uni_mobile as mobile,xm_manage_uid,xm_uid as id')
            ->where('is_group_manager', 1)
            ->where('uni_mobile', 'not like', '%\_%');
        // 限制搜索条件
        if (!empty($keyword)) {
            $xm_manage_orm = $xm_manage_orm->where('uni_mobile', 'like', "%{$keyword}%");
        }

        // 默认运营组关联条件
        $relation_where = [
            'relation_type' => OPERATE_GROUP_RELATION_TYPE::MANAGE
        ];
        // 关联运营组主查询
        $relation_group_orm = OperateGroupRelation::with('operateGroup');

        // 如果传入运营组ID，则做相关限制
        if ($operate_group_id > 0) {
            // 限制关联数据为对应运营组
            $relation_where['operate_group_id'] = $operate_group_id;
        }

        // 查询对应经理绑定的运营组信息
        $relation_group = $relation_group_orm->where($relation_where)->select();

        // 运营组名字和经理ID对应数据
        $relation_info_operate_group = $relation_group->column('operateGroup', 'relation_id');

        // 如有传运营组ID则需要后置查询，根据运营组关联结果限制经理列表查询
        if ($operate_group_id > 0) {
            $xm_manage_orm = $xm_manage_orm->whereIn('xm_manage_uid', $relation_group->column('relation_id'));
        }
        // 所有经理列表
        $xm_manage = $xm_manage_orm->select();
        // 所有经理ID
        $xm_manage_uid = $xm_manage->column('xm_manage_uid');
        // 经理团队人数
        $relation_user_count = SyncUsersModel::whereIn('xm_manage_uid', $xm_manage_uid)
            ->group('xm_manage_uid')
            ->where('yp_uid', '>', 0)
            ->where('uni_mobile', 'not like', '%\_%')
            ->field(['xm_manage_uid', 'count(id) as count'])
            ->select()
            ->column('count', 'xm_manage_uid');
        // 构造返回数据
        return $xm_manage->each(function ($user) use ($relation_info_operate_group, $relation_user_count) {
            $user['is_relation_group'] = isset($relation_info_operate_group[$user['id']]) ? 1 : 0;
            $user['bind_operate_group'] = $relation_info_operate_group[$user['id']] ?? '';
            $user['user_count'] = $relation_user_count[$user['id']] ?? 0;
            return $user;
        })->toArray();
    }

    public function findByOrderId($orderId) {
        $snapShot = OperateGroupSnapshotRelationModel::where("relation_type", SNAPSHOT_RELATION_TYPE::ORDER)->where("relation_id", $orderId)->find();
        if (empty($snapShot))
            return null;
        return $snapShot->belongOperateGroup;
    }
}