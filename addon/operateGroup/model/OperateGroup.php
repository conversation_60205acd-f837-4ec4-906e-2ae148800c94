<?php

namespace addon\operateGroup\model;

use addon\operateGroup\constant\OPERATE_GROUP_RELATION_TYPE;
use think\Model;

/**
 * 运营组模型
 * Class OperateGroup
 * @package addon\operateGroup\model
 */
class OperateGroup extends Model
{
    protected $pk = 'operate_group_id';

    protected $autoWriteTimestamp = true;

    // 所有关联数据
    public function relation() {
        return $this->hasMany(OperateGroupRelation::class);
    }

    // 单个关联管理员
    public function adminRelation() {
        return $this->hasOne(OperateGroupRelation::class)->where('relation_type', OPERATE_GROUP_RELATION_TYPE::ADMIN);
    }

    // 所有关联管理员
    public function adminRelations() {
        return $this->hasMany(OperateGroupRelation::class)->where('relation_type', OPERATE_GROUP_RELATION_TYPE::ADMIN);
    }

    // 关联的所有经理
    public function manage() {
        return $this->hasMany(OperateGroupRelation::class)->where('relation_type', OPERATE_GROUP_RELATION_TYPE::MANAGE);
    }
}