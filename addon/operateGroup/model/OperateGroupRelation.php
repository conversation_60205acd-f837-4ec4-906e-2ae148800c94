<?php

namespace addon\operateGroup\model;

use app\model\goods\GoodsModel;
use app\model\user\SyncUsersModel;
use app\model\user\UserModel;
use think\Model;

/**
 * 运营组关联模型
 * Class OperateGroupRelation
 * @package addon\operateGroup\model
 */
class OperateGroupRelation extends Model
{
    protected $pk = 'operate_group_relation_id';

    protected $autoWriteTimestamp = true;

    protected $createTime = 'relation_time';

    // 关联的商品（目前暂没用到，暂做例子）
    public function goods() {
        return $this->hasOne(GoodsModel::class, 'goods_id', 'relation_id');
    }

    // 关联的管理员
    public function admin() {
        return $this->hasOne(UserModel::class, 'uid', 'relation_id')->field([
            'uid',
            'app_module',
            'app_group',
            'is_admin',
            'site_id',
            'group_id',
            'group_name',
            'username',
            'member_id',
            'status',
            'login_time',
            'login_ip',
            'realname',
            'email'
        ]);
    }

    // 关联的运营组
    public function operateGroup() {
        return $this->belongsTo(OperateGroup::class);
    }

    // 关联的经理数据
    public function syncUserByManage() {
        return $this->hasMany(SyncUsersModel::class, 'xm_manage_uid', 'relation_id');
    }
}