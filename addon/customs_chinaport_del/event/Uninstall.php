<?php
namespace addon\customs_chinaport\event;

/**
 * 应用卸载
 */
class Uninstall
{
    /**
     * 执行卸载
     */
    public function handle()
    {
        try {
            // 执行卸载SQL
            if (file_exists('addon/customs_chinaport/data/uninstall.sql')) {
                execute_sql('addon/customs_chinaport/data/uninstall.sql');
            }
            
            // 清理配置
            \think\facade\Db::name('config')->where([
                ['app_module', '=', 'admin'],
                ['config_key', 'like', 'customs_chinaport%']
            ])->delete();
            
            return success();
        } catch (\Exception $e) {
            return error(-1, '卸载插件失败：' . $e->getMessage());
        }
    }
} 