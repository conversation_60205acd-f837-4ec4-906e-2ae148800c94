<?php
/**
 * 海关接口模块API配置文件
 */
return [
    /**
     * 海关数据获取接口
     */
    'platDataOpen' => [
        'name' => '海关数据获取接口',
        'description' => '企业接收海关发起的支付相关实时数据获取请求',
        'path' => 'customs_chinaport/chinaport/platDataOpen',
        'method' => 'POST',
        'params' => [
            'orderNo' => [
                'required' => true,
                'type' => 'string',
                'description' => '申报订单的订单编号',
            ],
            'sessionID' => [
                'required' => true,
                'type' => 'string',
                'description' => '海关发起请求时，平台接收的会话ID',
            ],
            'serviceTime' => [
                'required' => true,
                'type' => 'integer',
                'description' => '调用时的系统时间',
            ],
        ],
    ],
]; 