<?php
/**
 * 海关接口模块配置文件
 */
return [
    'name' => '海关接口模块',
    'title' => '海关跨境电商进口统一版信息化系统平台对接模块',
    'description' => '对接海关跨境电商进口统一版信息化系统平台，实现数据实时获取接口功能',
    'status' => 1,
    'author' => 'Youpin Team',
    'version' => '1.0.0',
    'api_url' => [
        'test' => 'https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload',  // 测试环境URL
        'prod' => 'https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload',   // 生产环境URL
    ],
    'signature_service' => [
        'url' => 'http://*************:8080/rpc/eport/signature', // 签名服务地址
        'timeout' => 10, // 超时时间（秒）
        'auth_token' => 'DefaultAuthToken', // 认证令牌
    ],
    'env' => 'test', // 环境配置: test - 测试环境, prod - 生产环境
]; 