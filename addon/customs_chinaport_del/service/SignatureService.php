<?php
namespace addon\customs_chinaport\service;

/**
 * 海关数据签名服务
 * 对接chinaport-data-signature服务，提供数据签名功能
 */
class SignatureService
{
    /**
     * 签名服务地址
     * @var string
     */
    private $signServiceUrl;
    
    /**
     * 超时时间（秒）
     * @var int
     */
    private $timeout;
    
    /**
     * 认证令牌
     * @var string
     */
    private $authToken;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $config = config('addon.customs_chinaport.config');
        $this->signServiceUrl = $config['signature_service']['url'];
        $this->timeout = $config['signature_service']['timeout'];
        $this->authToken = $config['signature_service']['auth_token'];
    }
    
    /**
     * 对数据进行签名
     * @param string $data 需要签名的原始数据
     * @return array ['code' => '状态码', 'message' => '消息', 'signedData' => '签名后数据', 'certificate' => '证书内容']
     * @throws \Exception
     */
    public function sign($data)
    {
        try {
            // 自动检测数据类型
            if ($this->isXmlData($data)) {
                return $this->signXml($data);
            } else if ($this->is179Data($data)) {
                return $this->sign179($data);
            } else {
                // 默认处理，原方式保持不变
                // 构造请求参数
                $params = [
                    'id' => 1,
                    'data' => $data
                ];
                
                // 调用签名服务
                $result = $this->sendRequest('POST', $params);
                
                // 解析结果
                if (isset($result['success']) && $result['success'] === true) {
                    $signData = $result['data'] ?? [];
                    return [
                        'code' => '10000',
                        'message' => '签名成功',
                        'signedData' => $signData['signatureValue'] ?? '',
                        'certificate' => $signData['x509Certificate'] ?? '',
                        'digestValue' => $signData['digestValue'] ?? '',
                        'signatureNode' => $signData['signatureNode'] ?? '',
                        'certNo' => $signData['certNo'] ?? '',
                        'success' => $signData['success'] ?? false
                    ];
                } else {
                    return [
                        'code' => '20001',
                        'message' => $result['message'] ?? '签名服务调用失败',
                        'signedData' => '',
                        'certificate' => ''
                    ];
                }
            }
        } catch (\Exception $e) {
            return [
                'code' => '20002',
                'message' => '签名异常：' . $e->getMessage(),
                'signedData' => '',
                'certificate' => ''
            ];
        }
    }
    
    /**
     * 对XML数据进行签名
     * 
     * @param string $xmlData 原始XML数据
     * @return array 签名结果
     * @throws \Exception
     */
    public function signXml($xmlData)
    {
        try {
            // 构造请求参数
            $params = [
                'id' => 1,
                'data' => $xmlData
            ];
            
            // 调用签名服务
            $result = $this->sendRequest('POST', $params);
            
            // 解析结果
            if (isset($result['success']) && $result['success'] === true) {
                $signData = $result['data'] ?? [];
                
                // 返回XML专用字段
                return [
                    'code' => '10000',
                    'message' => '签名成功',
                    'signedData' => $signData['signatureValue'] ?? '',
                    'certificate' => $signData['x509Certificate'] ?? '',
                    'digestValue' => $signData['digestValue'] ?? '',
                    'signatureNode' => $signData['signatureNode'] ?? '',
                    'certNo' => $signData['certNo'] ?? '',
                    'success' => $signData['success'] ?? false
                ];
            } else {
                return [
                    'code' => '20001',
                    'message' => $result['message'] ?? 'XML签名服务调用失败',
                    'signedData' => '',
                    'certificate' => ''
                ];
            }
        } catch (\Exception $e) {
            return [
                'code' => '20002',
                'message' => 'XML签名异常：' . $e->getMessage(),
                'signedData' => '',
                'certificate' => ''
            ];
        }
    }
    
    /**
     * 对179数据进行签名
     * 
     * @param string $data179 原始179数据
     * @return array 签名结果
     * @throws \Exception
     */
    public function sign179($data179)
    {
        try {
            // 构造请求参数
            $params = [
                'id' => 1,
                'data' => $data179
            ];
            
            // 调用签名服务
            $result = $this->sendRequest('POST', $params);
            
            // 解析结果
            if (isset($result['success']) && $result['success'] === true) {
                $signData = $result['data'] ?? [];
                
                // 返回179专用字段
                return [
                    'code' => '10000',
                    'message' => '签名成功',
                    'signedData' => $signData['signatureValue'] ?? '',
                    'certNo' => $signData['certNo'] ?? '',
                    'success' => $signData['success'] ?? false
                ];
            } else {
                return [
                    'code' => '20001',
                    'message' => $result['message'] ?? '179数据签名服务调用失败',
                    'signedData' => '',
                    'certNo' => ''
                ];
            }
        } catch (\Exception $e) {
            return [
                'code' => '20002',
                'message' => '179数据签名异常：' . $e->getMessage(),
                'signedData' => '',
                'certNo' => ''
            ];
        }
    }
    
    /**
     * 构建带有签名的完整XML
     * 
     * @param string $xmlData 原始XML
     * @param array $signResult 签名结果
     * @return string 带签名的完整XML
     */
    public function buildSignedXml($xmlData, $signResult)
    {
        // 检查签名结果是否成功
        if ($signResult['code'] != '10000' || empty($signResult['signatureNode']) || empty($signResult['signedData'])) {
            throw new \Exception('签名结果不完整，无法构建签名XML');
        }
        
        try {
            // 解析原始XML
            $xmlDoc = new \DOMDocument();
            $xmlDoc->loadXML($xmlData);
            
            // 获取根节点
            $rootNode = $xmlDoc->documentElement;
            
            // 创建Signature节点
            $signatureNode = $xmlDoc->createElementNS('http://www.w3.org/2000/09/xmldsig#', 'ds:Signature');
            $rootNode->appendChild($signatureNode);
            
            // 创建SignedInfo节点 - 从签名结果中获取
            $signedInfoFragment = $xmlDoc->createDocumentFragment();
            $signedInfoFragment->appendXML($signResult['signatureNode']);
            $signatureNode->appendChild($signedInfoFragment);
            
            // 创建SignatureValue节点
            $signatureValueNode = $xmlDoc->createElement('ds:SignatureValue', $signResult['signedData']);
            $signatureNode->appendChild($signatureValueNode);
            
            // 创建KeyInfo节点
            $keyInfoNode = $xmlDoc->createElement('ds:KeyInfo');
            $signatureNode->appendChild($keyInfoNode);
            
            // 创建X509Data节点
            $x509DataNode = $xmlDoc->createElement('ds:X509Data');
            $keyInfoNode->appendChild($x509DataNode);
            
            // 创建X509Certificate节点
            $x509CertificateNode = $xmlDoc->createElement('ds:X509Certificate', $signResult['certificate']);
            $x509DataNode->appendChild($x509CertificateNode);
            
            // 返回完整的XML
            return $xmlDoc->saveXML();
        } catch (\Exception $e) {
            throw new \Exception('构建签名XML失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 判断是否为XML数据
     * 
     * @param string $data 待检测数据
     * @return bool
     */
    private function isXmlData($data)
    {
        $data = trim($data);
        return (stripos($data, '<?xml') === 0 || stripos($data, '<ceb:') === 0);
    }
    
    /**
     * 判断是否为179数据
     * 
     * @param string $data 待检测数据
     * @return bool
     */
    private function is179Data($data)
    {
        $data = trim($data);
        return (stripos($data, '"sessionID":"') !== false && stripos($data, '"payExchangeInfoHead":"') !== false);
    }
    
    /**
     * 发送HTTP请求
     * @param string $method 请求方法
     * @param array $params 请求参数
     * @return array
     * @throws \Exception
     */
    private function sendRequest($method, $params)
    {
        // 初始化CURL
        $ch = curl_init();
        
        // 设置请求地址
        curl_setopt($ch, CURLOPT_URL, $this->signServiceUrl);
        
        // 设置超时
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $this->timeout);
        
        // 设置返回数据格式
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // 设置请求方法
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'x-auth-token-eport-sign: ' . $this->authToken
            ]);
        }
        
        // 发送请求
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // 关闭CURL
        curl_close($ch);
        
        // 处理错误
        if ($error) {
            throw new \Exception('CURL请求错误: ' . $error);
        }
        
        if ($httpCode != 200) {
            throw new \Exception('HTTP状态码错误: ' . $httpCode);
        }
        
        // 解析响应数据
        $result = json_decode($response, true);
        if (json_last_error() != JSON_ERROR_NONE) {
            throw new \Exception('JSON解析错误: ' . json_last_error_msg() . ', 原始数据: ' . $response);
        }
        
        return $result;
    }
} 