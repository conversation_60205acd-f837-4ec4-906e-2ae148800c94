<?php
namespace addon\customs_chinaport\service;

use addon\customs_chinaport\model\ChinaportLog;
use addon\customs_chinaport\model\ChinaportReceipt;

/**
 * 海关回执处理服务
 * 负责处理海关回执数据，包括解析回执XML、保存回执状态和处理业务逻辑
 */
class ReceiptService
{
    /**
     * 接收并处理回执数据
     * 
     * @param string $receiptXml 回执XML数据
     * @return array 处理结果 ['code' => '', 'message' => '']
     */
    public function processReceipt($receiptXml)
    {
        try {
            // 记录原始回执数据
            $log_model = new ChinaportLog();
            $log_id = $log_model->addLog(5, $receiptXml, [], 0);
            
            // 解析回执XML
            $receiptData = $this->parseReceiptXml($receiptXml);
            if (empty($receiptData)) {
                $log_model->addLog(5, $receiptXml, ['code' => '20001', 'message' => '解析回执XML失败'], 0, '解析回执XML失败');
                return [
                    'code' => '20001',
                    'message' => '解析回执XML失败'
                ];
            }
            
            // 保存回执数据
            $receipt_model = new ChinaportReceipt();
            $result = $receipt_model->saveReceipt($receiptData);
            
            if ($result['code'] != 0) {
                $log_model->addLog(5, $receiptXml, $result, 0, '保存回执数据失败');
                return [
                    'code' => '20002',
                    'message' => '保存回执数据失败: ' . $result['message']
                ];
            }
            
            // 根据回执类型处理业务逻辑
            $processResult = $this->processReceiptByType($receiptData);
            
            // 更新处理结果
            $updateData = [
                'process_status' => $processResult['code'] == '10000' ? 1 : 0,
                'process_message' => $processResult['message'],
                'process_time' => time()
            ];
            $receipt_model->updateReceipt($receiptData['receipt_id'], $updateData);
            
            // 记录处理结果日志
            $log_model->addLog(5, $receiptXml, $processResult, $processResult['code'] == '10000' ? 1 : 0, $processResult['message']);
            
            return $processResult;
            
        } catch (\Exception $e) {
            $log_model = new ChinaportLog();
            $log_model->addLog(5, $receiptXml, [], 0, '处理异常: ' . $e->getMessage());
            
            return [
                'code' => '20003',
                'message' => '处理异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 解析回执XML
     * 
     * @param string $receiptXml 回执XML数据
     * @return array|null 解析后的回执数据
     */
    private function parseReceiptXml($receiptXml)
    {
        try {
            // 加载XML文档
            $doc = new \DOMDocument();
            $doc->loadXML($receiptXml);
            
            // 获取根节点
            $root = $doc->documentElement;
            
            // 判断回执类型
            $receiptType = $root->nodeName;
            
            // 根据不同回执类型进行解析
            switch ($receiptType) {
                case 'ceb:CEB312Message': // 订单回执
                    return $this->parseOrderReceipt($doc);
                
                case 'ceb:CEB622Message': // 支付回执
                    return $this->parsePaymentReceipt($doc);
                
                default:
                    // 未知回执类型
                    return [
                        'receipt_type' => 'unknown',
                        'receipt_id' => uniqid('RECEIPT_'),
                        'original_xml' => $receiptXml,
                        'receipt_time' => time(),
                        'customs_code' => '',
                        'customs_name' => '',
                        'status' => -1,
                        'status_desc' => '未知回执类型: ' . $receiptType,
                        'order_no' => '',
                        'payment_no' => '',
                        'note' => '未知回执类型: ' . $receiptType
                    ];
            }
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 解析订单回执
     * 
     * @param \DOMDocument $doc XML文档对象
     * @return array 解析后的订单回执数据
     */
    private function parseOrderReceipt($doc)
    {
        // 获取根节点
        $root = $doc->documentElement;
        
        // 获取回执信息
        $receiptInfo = $root->getElementsByTagName('OrderReturn')->item(0);
        
        if (!$receiptInfo) {
            return null;
        }
        
        // 解析回执数据
        $receiptData = [
            'receipt_type' => 'order',
            'receipt_id' => $this->getNodeValue($receiptInfo, 'guid'),
            'original_xml' => $doc->saveXML(),
            'receipt_time' => time(),
            'customs_code' => $this->getNodeValue($receiptInfo, 'customsCode'),
            'customs_name' => $this->getNodeValue($receiptInfo, 'customsName'),
            'status' => $this->getNodeValue($receiptInfo, 'returnStatus'),
            'status_desc' => $this->getStatusDesc($this->getNodeValue($receiptInfo, 'returnStatus')),
            'order_no' => $this->getNodeValue($receiptInfo, 'orderNo'),
            'payment_no' => '',
            'note' => $this->getNodeValue($receiptInfo, 'returnInfo')
        ];
        
        return $receiptData;
    }
    
    /**
     * 解析支付回执
     * 
     * @param \DOMDocument $doc XML文档对象
     * @return array 解析后的支付回执数据
     */
    private function parsePaymentReceipt($doc)
    {
        // 获取根节点
        $root = $doc->documentElement;
        
        // 获取回执信息
        $receiptInfo = $root->getElementsByTagName('PaymentReturn')->item(0);
        
        if (!$receiptInfo) {
            return null;
        }
        
        // 解析回执数据
        $receiptData = [
            'receipt_type' => 'payment',
            'receipt_id' => $this->getNodeValue($receiptInfo, 'guid'),
            'original_xml' => $doc->saveXML(),
            'receipt_time' => time(),
            'customs_code' => $this->getNodeValue($receiptInfo, 'customsCode'),
            'customs_name' => $this->getNodeValue($receiptInfo, 'customsName'),
            'status' => $this->getNodeValue($receiptInfo, 'returnStatus'),
            'status_desc' => $this->getStatusDesc($this->getNodeValue($receiptInfo, 'returnStatus')),
            'order_no' => '',
            'payment_no' => $this->getNodeValue($receiptInfo, 'paymentNo'),
            'note' => $this->getNodeValue($receiptInfo, 'returnInfo')
        ];
        
        return $receiptData;
    }
    
    /**
     * 获取节点值
     * 
     * @param \DOMElement $parent 父节点
     * @param string $tagName 标签名
     * @return string 节点值
     */
    private function getNodeValue($parent, $tagName)
    {
        $nodes = $parent->getElementsByTagName($tagName);
        if ($nodes->length > 0) {
            return $nodes->item(0)->nodeValue;
        }
        return '';
    }
    
    /**
     * 获取状态描述
     * 
     * @param string $status 状态码
     * @return string 状态描述
     */
    private function getStatusDesc($status)
    {
        $statusMap = [
            '1' => '申报成功',
            '2' => '审核通过',
            '3' => '审核不通过',
            '4' => '退单',
            '100' => '处理中',
        ];
        
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }
    
    /**
     * 根据回执类型处理业务逻辑
     * 
     * @param array $receiptData 回执数据
     * @return array 处理结果 ['code' => '', 'message' => '']
     */
    private function processReceiptByType($receiptData)
    {
        // 根据回执类型处理不同业务逻辑
        switch ($receiptData['receipt_type']) {
            case 'order':
                return $this->processOrderReceipt($receiptData);
                
            case 'payment':
                return $this->processPaymentReceipt($receiptData);
                
            default:
                return [
                    'code' => '20004',
                    'message' => '未知回执类型: ' . $receiptData['receipt_type']
                ];
        }
    }
    
    /**
     * 处理订单回执
     * 
     * @param array $receiptData 回执数据
     * @return array 处理结果 ['code' => '', 'message' => '']
     */
    private function processOrderReceipt($receiptData)
    {
        // 处理订单回执的业务逻辑
        // 在实际业务中，可能需要更新订单状态、触发后续流程等
        
        // 获取订单号
        $orderNo = $receiptData['order_no'];
        if (empty($orderNo)) {
            return [
                'code' => '20005',
                'message' => '订单回执缺少订单号'
            ];
        }
        
        // 根据回执状态处理不同业务逻辑
        switch ($receiptData['status']) {
            case '1': // 申报成功
                // TODO: 更新订单状态为"申报成功"
                return [
                    'code' => '10000',
                    'message' => '订单申报成功，已更新订单状态'
                ];
                
            case '2': // 审核通过
                // TODO: 更新订单状态为"审核通过"
                return [
                    'code' => '10000',
                    'message' => '订单审核通过，已更新订单状态'
                ];
                
            case '3': // 审核不通过
                // TODO: 更新订单状态为"审核不通过"，可能需要发送通知
                return [
                    'code' => '10001',
                    'message' => '订单审核不通过: ' . $receiptData['note']
                ];
                
            case '4': // 退单
                // TODO: 更新订单状态为"退单"，可能需要发送通知
                return [
                    'code' => '10002',
                    'message' => '订单退单: ' . $receiptData['note']
                ];
                
            default:
                return [
                    'code' => '20006',
                    'message' => '未知订单回执状态: ' . $receiptData['status']
                ];
        }
    }
    
    /**
     * 处理支付回执
     * 
     * @param array $receiptData 回执数据
     * @return array 处理结果 ['code' => '', 'message' => '']
     */
    private function processPaymentReceipt($receiptData)
    {
        // 处理支付回执的业务逻辑
        // 在实际业务中，可能需要更新支付状态、触发后续流程等
        
        // 获取支付单号
        $paymentNo = $receiptData['payment_no'];
        if (empty($paymentNo)) {
            return [
                'code' => '20007',
                'message' => '支付回执缺少支付单号'
            ];
        }
        
        // 根据回执状态处理不同业务逻辑
        switch ($receiptData['status']) {
            case '1': // 申报成功
                // TODO: 更新支付状态为"申报成功"
                return [
                    'code' => '10000',
                    'message' => '支付申报成功，已更新支付状态'
                ];
                
            case '2': // 审核通过
                // TODO: 更新支付状态为"审核通过"
                return [
                    'code' => '10000',
                    'message' => '支付审核通过，已更新支付状态'
                ];
                
            case '3': // 审核不通过
                // TODO: 更新支付状态为"审核不通过"，可能需要发送通知
                return [
                    'code' => '10001',
                    'message' => '支付审核不通过: ' . $receiptData['note']
                ];
                
            default:
                return [
                    'code' => '20008',
                    'message' => '未知支付回执状态: ' . $receiptData['status']
                ];
        }
    }
} 