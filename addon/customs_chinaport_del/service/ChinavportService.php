<?php
namespace addon\customs_chinaport\service;

use addon\customs_chinaport\model\ChinaportLog;

/**
 * 海关接口数据服务
 * 负责处理海关接口的业务逻辑和数据构建
 */
class ChinavportService
{
    /**
     * 签名服务实例
     * @var SignatureService
     */
    private $signatureService;
    
    /**
     * 海关API URL
     * @var string
     */
    private $apiUrl;
    
    /**
     * 超时时间（秒）
     * @var int
     */
    private $timeout;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->signatureService = new SignatureService();
        
        // 获取配置
        $config = config('addon.customs_chinaport.config');
        $env = $config['env']; // 环境: test/prod
        $this->apiUrl = $config['api_url'][$env]; // 根据环境获取API URL
        $this->timeout = $config['signature_service']['timeout']; // 使用相同的超时配置
    }
    
    /**
     * 处理海关数据获取请求
     * @param array $params 请求参数
     * @return array
     */
    public function handlePlatDataOpen($params)
    {
        // 基本参数校验已在Controller层完成
        $orderNo = $params['orderNo'];
        $sessionID = $params['sessionID'];
        $serviceTime = $params['serviceTime'];
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',  // 默认成功
            'message' => '',
            'serviceTime' => time() * 1000, // 毫秒时间戳
            'data' => []
        ];
        
        try {
            // 1. 查询订单数据
            $orderData = $this->getOrderData($orderNo);
            if (empty($orderData)) {
                return $this->buildErrorResponse('20001', '订单不存在');
            }
            
            // 2. 构建回复数据
            $responseData = $this->buildResponseData($orderData);
            
            // 3. 进行数据签名
            $jsonData = json_encode($responseData, JSON_UNESCAPED_UNICODE);
            $signResult = $this->signatureService->sign179($jsonData);
            
            if ($signResult['code'] != '10000') {
                return $this->buildErrorResponse('20002', '数据签名失败: ' . $signResult['message']);
            }
            
            // 4. 构造返回数据
            $return_data['data'] = [
                'sessionID' => $sessionID,
                'payExInfoStr' => $signResult['signedData'],
                'serviceTime' => $serviceTime,
                'certNo' => $signResult['certNo'] ?? ''
            ];
            
            // 记录成功日志
            $this->recordLog(1, $params, $return_data, 1);
            
            return $return_data;
            
        } catch (\Exception $e) {
            return $this->buildErrorResponse('20003', '处理异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成并签名CEB311订单申报报文
     * 
     * @param array $orderData 订单数据
     * @return array 结果 ['code' => '', 'message' => '', 'signedXml' => '']
     */
    public function generateCEB311OrderXml($orderData)
    {
        try {
            // 1. 构建XML报文
            $xmlData = $this->buildCEB311Xml($orderData);
            
            // 2. 签名XML
            $signResult = $this->signatureService->signXml($xmlData);
            
            if ($signResult['code'] != '10000') {
                return [
                    'code' => '20002',
                    'message' => '签名失败: ' . $signResult['message'],
                    'signedXml' => ''
                ];
            }
            
            // 3. 构建完整的签名XML
            $signedXml = $this->signatureService->buildSignedXml($xmlData, $signResult);
            
            // 4. 记录日志
            $this->recordLog(2, $orderData, ['signedXml' => $signedXml], 1);
            
            return [
                'code' => '10000',
                'message' => 'XML生成并签名成功',
                'signedXml' => $signedXml
            ];
            
        } catch (\Exception $e) {
            $this->recordLog(2, $orderData, [], 0, $e->getMessage());
            
            return [
                'code' => '20003',
                'message' => 'XML处理异常: ' . $e->getMessage(),
                'signedXml' => ''
            ];
        }
    }
    
    /**
     * 提交海关订单数据
     * 
     * @param string $orderNo 订单号
     * @return array 结果 ['code' => '', 'message' => '', 'data' => []]
     */
    public function submitOrderToCustoms($orderNo)
    {
        try {
            // 1. 获取订单数据
            $orderData = $this->getOrderData($orderNo);
            if (empty($orderData)) {
                return $this->buildErrorResponse('20001', '订单不存在');
            }
            
            // 2. 生成并签名XML
            $xmlResult = $this->generateCEB311OrderXml($orderData);
            if ($xmlResult['code'] != '10000') {
                return $this->buildErrorResponse('20004', '生成XML失败: ' . $xmlResult['message']);
            }
            
            // 3. 提交数据到海关
            $submitResult = $this->submitToCustoms($xmlResult['signedXml']);
            
            // 4. 记录提交日志
            $this->recordLog(3, ['orderNo' => $orderNo], $submitResult, 
                             $submitResult['code'] == '10000' ? 1 : 0, 
                             $submitResult['message']);
            
            return $submitResult;
            
        } catch (\Exception $e) {
            return $this->buildErrorResponse('20005', '提交异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 提交179支付数据到海关
     * 
     * @param array $paymentData 支付数据
     * @return array 结果 ['code' => '', 'message' => '', 'data' => []]
     */
    public function submitPaymentToCustoms($paymentData)
    {
        try {
            // 1. 构建支付数据
            $data179 = $this->build179PaymentData($paymentData);
            
            // 2. 签名支付数据
            $signResult = $this->signatureService->sign179($data179);
            
            if ($signResult['code'] != '10000') {
                return $this->buildErrorResponse('20006', '支付数据签名失败: ' . $signResult['message']);
            }
            
            // 3. 构建提交数据
            $submitData = [
                'payExInfoStr' => $signResult['signedData'],
                'sessionID' => $paymentData['sessionID'] ?? 'SESSION' . time(),
                'serviceTime' => (time() * 1000),
                'certNo' => $signResult['certNo'] ?? ''
            ];
            
            // 4. 提交数据到海关
            $submitResult = $this->submitToCustoms(json_encode($submitData, JSON_UNESCAPED_UNICODE));
            
            // 5. 记录提交日志
            $this->recordLog(4, $paymentData, $submitResult, 
                             $submitResult['code'] == '10000' ? 1 : 0, 
                             $submitResult['message']);
            
            return $submitResult;
            
        } catch (\Exception $e) {
            return $this->buildErrorResponse('20007', '提交支付数据异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 提交数据到海关
     * 
     * @param string $data 要提交的数据
     * @return array 结果 ['code' => '', 'message' => '', 'data' => []]
     */
    private function submitToCustoms($data)
    {
        try {
            $maxRetries = 10; // 最大重试次数
            $retryCount = 0;
            $retryDelay = 1; // 重试延迟时间（秒）
            
            while ($retryCount <= $maxRetries) {
                // 初始化CURL
                $ch = curl_init();
                
                // 设置请求地址
                curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
                
                // 设置超时
                curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $this->timeout);
                
                // 设置返回数据格式
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                // 设置POST请求
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'Content-Length: ' . strlen($data)
                ]);
                
                // 发送请求
                $response = curl_exec($ch);
                $error = curl_error($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                
                // 关闭CURL
                curl_close($ch);
                
                // 处理错误
                if ($error) {
                    return [
                        'code' => '20008',
                        'message' => 'CURL请求错误: ' . $error,
                        'data' => []
                    ];
                }
                
                // 处理限流情况
                if ($httpCode == 429) {
                    $retryCount++;
                    
                    // 记录限流日志
                    $this->recordLog(6, $data, [
                        'code' => '429',
                        'message' => 'Too Many Requests, retry ' . $retryCount . '/' . $maxRetries,
                        'data' => null
                    ], 0, '触发限流，等待重试');
                    
                    // 已达到最大重试次数
                    if ($retryCount > $maxRetries) {
                        return [
                            'code' => '20013',
                            'message' => '请求频率限制，已重试' . $maxRetries . '次仍失败',
                            'data' => []
                        ];
                    }
                    
                    // 等待后重试
                    sleep($retryDelay);
                    continue;
                }
                
                if ($httpCode != 200) {
                    return [
                        'code' => '20009',
                        'message' => 'HTTP状态码错误: ' . $httpCode,
                        'data' => []
                    ];
                }
                
                // 解析响应数据
                $result = json_decode($response, true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    return [
                        'code' => '20010',
                        'message' => 'JSON解析错误: ' . json_last_error_msg() . ', 原始数据: ' . $response,
                        'data' => []
                    ];
                }
                
                // 处理业务结果
                if (isset($result['code']) && $result['code'] == '10000') {
                    return [
                        'code' => '10000',
                        'message' => '提交成功',
                        'data' => $result['data'] ?? []
                    ];
                } else {
                    return [
                        'code' => $result['code'] ?? '20011',
                        'message' => $result['message'] ?? '提交失败，服务端返回未知错误',
                        'data' => $result['data'] ?? []
                    ];
                }
            }
            
            // 不应该走到这里，但为了代码完整性添加
            return [
                'code' => '20014',
                'message' => '未知错误，请求未能完成',
                'data' => []
            ];
            
        } catch (\Exception $e) {
            return [
                'code' => '20012',
                'message' => '提交处理异常: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }
    
    /**
     * 构建179支付数据
     * 
     * @param array $paymentData 支付数据
     * @return string 格式化的179数据
     */
    private function build179PaymentData($paymentData)
    {
        // 构建头部数据
        $head = [
            'guid' => $this->generateGuid(),
            'initalRequest' => $paymentData['initalRequest'] ?? '',
            'initalResponse' => $paymentData['initalResponse'] ?? 'ok',
            'ebpCode' => $paymentData['ebpCode'] ?? '示例电商平台编码',
            'payCode' => $paymentData['payCode'] ?? '示例支付企业编码',
            'payTransactionId' => $paymentData['payment_no'],
            'totalAmount' => $paymentData['payment_amount'] / 100, // 转换为元
            'currency' => $paymentData['currency'] ?? '142', // 人民币
            'verDept' => $paymentData['verDept'] ?? '1',    // 签名机构：1-银联
            'payType' => $paymentData['payType'] ?? '1',    // 支付类型：1-直接支付
            'tradingTime' => str_replace([' ', ':', '-'], '', $paymentData['payment_time']),
            'note' => $paymentData['note'] ?? '',
        ];
        
        // 构建订单商品信息
        $goods = [];
        if (!empty($paymentData['goods_name'])) {
            $goods[] = [
                'gname' => $paymentData['goods_name'],
                'itemLink' => $paymentData['goods_url'] ?? ''
            ];
        }
        
        // 构建订单列表
        $lists = [
            [
                'orderNo' => $paymentData['order_no'],
                'goodsInfo' => $goods,
                'recpAccount' => $paymentData['recpAccount'] ?? '',
                'recpCode' => $paymentData['recpCode'] ?? '',
                'recpName' => $paymentData['recpName'] ?? ''
            ]
        ];
        
        // 构造179格式数据
        $sessionID = $paymentData['sessionID'] ?? 'SID' . time();
        $serviceTime = (string)(time() * 1000);
        
        $headJson = json_encode($head, JSON_UNESCAPED_UNICODE);
        $listsJson = json_encode($lists, JSON_UNESCAPED_UNICODE);
        
        // 拼接成特定格式的179数据
        return '"sessionID":"' . $sessionID . '"||"payExchangeInfoHead":"' . $headJson . '"||"payExchangeInfoLists":"' . $listsJson . '"||"serviceTime":"' . $serviceTime . '"';
    }
    
    /**
     * 构建CEB311订单申报XML
     * 
     * @param array $orderData 订单数据
     * @return string XML报文
     */
    private function buildCEB311Xml($orderData)
    {
        $guid = $this->generateGuid();
        $timestamp = date('YmdHis');
        
        // 创建XML文档
        $doc = new \DOMDocument('1.0', 'UTF-8');
        $doc->formatOutput = true;
        
        // 创建根节点
        $root = $doc->createElementNS('http://www.chinaport.gov.cn/ceb', 'ceb:CEB311Message');
        $root->setAttribute('guid', $guid);
        $root->setAttribute('version', '1.0');
        $doc->appendChild($root);
        
        // 创建Order节点
        $order = $doc->createElement('ceb:Order');
        $root->appendChild($order);
        
        // 创建OrderHead节点
        $orderHead = $doc->createElement('ceb:OrderHead');
        $order->appendChild($orderHead);
        
        // 添加OrderHead子节点
        $this->addElement($doc, $orderHead, 'ceb:guid', $guid);
        $this->addElement($doc, $orderHead, 'ceb:appType', '1');
        $this->addElement($doc, $orderHead, 'ceb:appTime', $timestamp);
        $this->addElement($doc, $orderHead, 'ceb:appStatus', '2');
        $this->addElement($doc, $orderHead, 'ceb:orderType', 'I');
        $this->addElement($doc, $orderHead, 'ceb:orderNo', $orderData['order_no']);
        $this->addElement($doc, $orderHead, 'ceb:ebpCode', '4601630004');
        $this->addElement($doc, $orderHead, 'ceb:ebpName', '海南省荣誉进出口贸易有限公司');
        $this->addElement($doc, $orderHead, 'ceb:ebcCode', '4601630004');
        $this->addElement($doc, $orderHead, 'ceb:ebcName', '海南省荣誉进出口贸易有限公司');
        $this->addElement($doc, $orderHead, 'ceb:goodsValue', number_format($orderData['payment_amount'] / 100, 2, '.', ''));
        $this->addElement($doc, $orderHead, 'ceb:freight', '0');
        $this->addElement($doc, $orderHead, 'ceb:discount', '0');
        $this->addElement($doc, $orderHead, 'ceb:taxTotal', '0');
        $this->addElement($doc, $orderHead, 'ceb:acturalPaid', number_format($orderData['payment_amount'] / 100, 2, '.', ''));
        $this->addElement($doc, $orderHead, 'ceb:currency', '142');
        $this->addElement($doc, $orderHead, 'ceb:buyerRegNo', '4');
        $this->addElement($doc, $orderHead, 'ceb:buyerName', $orderData['buyer_name']);
        $this->addElement($doc, $orderHead, 'ceb:buyerTelephone', $orderData['buyer_tel']);
        $this->addElement($doc, $orderHead, 'ceb:buyerIdType', '1');
        $this->addElement($doc, $orderHead, 'ceb:buyerIdNumber', $orderData['buyer_id_number']);
        $this->addElement($doc, $orderHead, 'ceb:consignee', $orderData['buyer_name']);
        $this->addElement($doc, $orderHead, 'ceb:consigneeTelephone', $orderData['buyer_tel']);
        $this->addElement($doc, $orderHead, 'ceb:consigneeAddress', $orderData['buyer_address'] ?? '测试地址');
        $this->addElement($doc, $orderHead, 'ceb:note', 'test');
        
        // 创建OrderList节点
        $orderList = $doc->createElement('ceb:OrderList');
        $order->appendChild($orderList);
        
        // 添加OrderList子节点
        $this->addElement($doc, $orderList, 'ceb:gnum', '1');
        $this->addElement($doc, $orderList, 'ceb:itemNo', '1');
        $this->addElement($doc, $orderList, 'ceb:itemName', $orderData['goods_name'] ?? '测试商品');
        $this->addElement($doc, $orderList, 'ceb:gmodel', $orderData['goods_spec'] ?? '测试规格');
        $this->addElement($doc, $orderList, 'ceb:itemDescribe', '');
        $this->addElement($doc, $orderList, 'ceb:barCode', '1');
        $this->addElement($doc, $orderList, 'ceb:unit', '011');
        $this->addElement($doc, $orderList, 'ceb:qty', '1');
        $this->addElement($doc, $orderList, 'ceb:price', number_format($orderData['payment_amount'] / 100, 2, '.', ''));
        $this->addElement($doc, $orderList, 'ceb:totalPrice', number_format($orderData['payment_amount'] / 100, 2, '.', ''));
        $this->addElement($doc, $orderList, 'ceb:currency', '142');
        $this->addElement($doc, $orderList, 'ceb:country', '136');
        $this->addElement($doc, $orderList, 'ceb:note', 'test');
        
        // 创建BaseTransfer节点
        $baseTransfer = $doc->createElement('ceb:BaseTransfer');
        $root->appendChild($baseTransfer);
        
        // 添加BaseTransfer子节点
        $this->addElement($doc, $baseTransfer, 'ceb:copCode', '4601630004');
        $this->addElement($doc, $baseTransfer, 'ceb:copName', '海南省荣誉进出口贸易有限公司');
        $this->addElement($doc, $baseTransfer, 'ceb:dxpMode', 'DXP');
        $this->addElement($doc, $baseTransfer, 'ceb:dxpId', 'DXPENT0000530815');
        $this->addElement($doc, $baseTransfer, 'ceb:note', 'test');
        
        return $doc->saveXML();
    }
    
    /**
     * 辅助方法：添加XML元素
     * 
     * @param \DOMDocument $doc 文档对象
     * @param \DOMElement $parent 父元素
     * @param string $name 元素名称
     * @param string $value 元素值
     */
    private function addElement($doc, $parent, $name, $value)
    {
        $element = $doc->createElement($name, $value);
        $parent->appendChild($element);
    }
    
    /**
     * 查询订单数据
     * @param string $orderNo 订单号
     * @return array|null 订单数据
     */
    public function getOrderData($orderNo)
    {
        // TODO: 实现实际的订单查询逻辑
        // 这里先返回模拟数据用于开发测试
        return [
            'order_no' => $orderNo,
            'payment_no' => 'PAY' . $orderNo,
            'payment_amount' => 10000, // 单位：分
            'payment_time' => date('Y-m-d H:i:s'),
            'payment_status' => 'SUCCESS',
            'buyer_id_number' => '110101199001011234',
            'buyer_name' => '测试用户',
            'buyer_tel' => '13800138000',
            'buyer_address' => '北京市东城区测试地址',
            'goods_name' => '测试商品',
            'goods_spec' => '测试规格',
        ];
    }
    
    /**
     * 构建响应数据
     * @param array $orderData 订单数据
     * @return array
     */
    private function buildResponseData($orderData)
    {
        // 构造签名内容数据结构
        // 根据海关文档要求构建
        return [
            'payExchangeInfoHead' => [
                'guid' => $this->generateGuid(),
                'initalRequest' => '',
                'initalResponse' => '',
                'ebpCode' => '示例电商平台编码',
                'payCode' => '示例支付企业编码',
                'payTransactionId' => $orderData['payment_no'],
                'totalAmount' => $orderData['payment_amount'] / 100, // 转换为元
                'currency' => '142', // 人民币
                'verDept' => '1',    // 签名机构：1-银联
                'payType' => '1',    // 支付类型：1-直接支付
                'tradingTime' => str_replace([' ', ':', '-'], '', $orderData['payment_time']),
                'note' => '',
            ],
            'payExchangeInfoLists' => [
                [
                    'orderNo' => $orderData['order_no'],
                    'goodsInfo' => '',
                    'paymentAmount' => $orderData['payment_amount'] / 100, // 转换为元
                ]
            ]
        ];
    }
    
    /**
     * 生成GUID
     * @return string
     */
    private function generateGuid()
    {
        if (function_exists('com_create_guid')) {
            return trim(com_create_guid(), '{}');
        }
        
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid = substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12);
        
        return $uuid;
    }
    
    /**
     * 构建错误返回
     * @param string $code 错误码
     * @param string $message 错误信息
     * @return array
     */
    private function buildErrorResponse($code, $message)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'serviceTime' => time() * 1000,
            'data' => []
        ];
        
        // 记录错误日志
        $this->recordLog(1, [], $response, 0, $message);
        
        return $response;
    }
    
    /**
     * 记录日志
     * 
     * @param int $type 日志类型 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试
     * @param array|string $requestData 请求数据
     * @param array|string $responseData 响应数据
     * @param int $status 状态 0-失败 1-成功
     * @param string $errorMsg 错误信息
     * @return bool
     */
    private function recordLog($type, $requestData, $responseData, $status, $errorMsg = '')
    {
        try {
            $logModel = new ChinaportLog();
            
            // 如果是字符串，先尝试处理UTF-8编码问题
            if (is_string($requestData)) {
                $requestData = $this->sanitizeUtf8String($requestData);
            }
            
            if (is_string($responseData)) {
                $responseData = $this->sanitizeUtf8String($responseData);
            }
            
            $logModel->addLog($type, $requestData, $responseData, $status, $errorMsg);
            return true;
        } catch (\Exception $e) {
            // 记录日志失败不影响业务
            return false;
        }
    }
    
    /**
     * 清理UTF-8字符串中的无效字符
     * @param string $string 待处理字符串
     * @return string 处理后的字符串
     */
    private function sanitizeUtf8String($string)
    {
        // 移除UTF-8 BOM
        if (substr($string, 0, 3) == pack('CCC', 0xEF, 0xBB, 0xBF)) {
            $string = substr($string, 3);
        }
        
        // 替换无效UTF-8字符
        if (function_exists('iconv')) {
            // 使用iconv转换，忽略无效字符
            $result = @iconv('UTF-8', 'UTF-8//IGNORE', $string);
            if ($result !== false) {
                return $result;
            }
        }
        
        // 如果iconv失败或不可用，使用mb_convert_encoding作为备选方案
        return mb_convert_encoding($string, 'UTF-8', 'UTF-8');
    }
} 