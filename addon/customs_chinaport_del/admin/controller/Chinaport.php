<?php
namespace addon\customs_chinaport\admin\controller;

use app\admin\controller\BaseAdmin;
use addon\customs_chinaport\model\ChinaportLog;
use think\facade\Db;

/**
 * 海关接口后台管理控制器
 */
class Chinaport extends BaseAdmin
{
    /**
     * 接口概览
     */
    public function index()
    {
        // 获取统计数据
        $log_model = new ChinaportLog();
        
        // 总调用次数
        $total_count = Db::name('customs_chinaport_log')->count();
        
        // 成功调用次数
        $success_count = Db::name('customs_chinaport_log')->where('status', 1)->count();
        
        // 失败调用次数
        $fail_count = Db::name('customs_chinaport_log')->where('status', 0)->count();
        
        // 成功率
        $success_rate = $total_count > 0 ? round($success_count / $total_count, 2) : 0;
        
        // 最近10条记录
        $recent_logs = Db::name('customs_chinaport_log')
            ->order('create_time desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        $this->assign('total_count', $total_count);
        $this->assign('success_count', $success_count);
        $this->assign('fail_count', $fail_count);
        $this->assign('success_rate', $success_rate);
        $this->assign('recent_logs', $recent_logs);
        
        return $this->fetch('chinaport/index');
    }
    
    /**
     * 接口日志
     */
    public function log()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 10);
            $type = input('type', '');
            $status = input('status', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $condition = [];
            
            // 筛选条件
            if ($type !== '') {
                $condition[] = ['type', '=', $type];
            }
            
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            if ($start_time && $end_time) {
                $condition[] = ['create_time', 'between', [strtotime($start_time), strtotime($end_time)]];
            }
            
            $log_model = new ChinaportLog();
            $list = $log_model->getLogPageList($condition, '*', 'create_time desc', $page, $page_size);
            
            return $list;
        } else {
            return $this->fetch('chinaport/log');
        }
    }
    
    /**
     * 接口配置
     */
    public function config()
    {
        if (request()->isAjax()) {
            // 更新配置
            $config = input('config', '');
            if ($config) {
                $config = json_decode($config, true);
                $result = $this->updateConfig($config);
                return $result;
            }
        } else {
            // 获取当前配置
            $config = config('addon.customs_chinaport');
            $this->assign('config', $config);
            return $this->fetch('chinaport/config');
        }
    }
    
    /**
     * 更新配置
     */
    private function updateConfig($config)
    {
        // 构造更新数据
        $update_data = [];
        
        // 环境配置
        if (isset($config['env'])) {
            $update_data['env'] = $config['env'];
        }
        
        // 签名服务配置
        if (isset($config['signature_service']['url'])) {
            $update_data['signature_service']['url'] = $config['signature_service']['url'];
        }
        
        if (isset($config['signature_service']['timeout'])) {
            $update_data['signature_service']['timeout'] = $config['signature_service']['timeout'];
        }
        
        // TODO: 实现配置保存逻辑
        // 更新配置文件或数据库中的配置
        
        return $this->success('配置更新成功');
    }
} 