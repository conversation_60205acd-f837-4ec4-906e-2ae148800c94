{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .ns-word-aux {
        margin-top: 5px;
        color: #999;
        font-size: 12px;
    }
    .ns-len-long {
        width: 450px;
    }
    .ns-len-mid {
        width: 250px;
    }
    .ns-len-short {
        width: 120px;
    }
    .ns-form-row {
        margin-top: 20px;
    }
</style>
{/block}

{block name="main"}
<div class="ns-card-brief layui-card ns-card-common ns-card-brief">
    <div class="ns-form-content">
        <h2 class="ns-form-title ns-card-title">海关接口配置</h2>
    </div>
</div>

<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>本页面用于配置海关跨境电商数据对接接口的相关参数</li>
            <li>请正确填写海关商户编码、商户名称和备案海关代码</li>
            <li>签名服务配置需要填写正确的签名服务地址和证书序列号</li>
            <li>测试和生产环境使用不同的接口地址，请根据实际情况选择环境</li>
            <li>海关编码说明：
                <ul>
                    <li>海关商户编码：由海关颁发的跨境电商企业编码</li>
                    <li>海关商户名称：在海关备案的企业名称，需与编码匹配</li>
                    <li>备案海关：企业主要备案的海关代码，如广州海关为"5423"</li>
                </ul>
            </li>
            <li>签名服务地址必须填写能够提供XML和数据签名功能的服务接口地址</li>
            <li>证书序列号必须是海关签发的有效证书序列号</li>
            <li>所有标有<span class="required">*</span>的字段为必填项</li>
        </ul>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">基础配置</span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="config_form">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>运行环境：</label>
                <div class="layui-input-block">
                    <input type="radio" name="env" value="test" title="测试环境" {if $config.env == 'test' || empty($config.env)}checked{/if}>
                    <input type="radio" name="env" value="prod" title="生产环境" {if $config.env == 'prod'}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>海关商户编码：</label>
                <div class="layui-input-block">
                    <input type="text" name="merchant_code" lay-verify="required" placeholder="请输入海关商户编码" autocomplete="off" class="layui-input ns-len-long" value="{$config.merchant_code|default=''}">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>海关商户名称：</label>
                <div class="layui-input-block">
                    <input type="text" name="merchant_name" lay-verify="required" placeholder="请输入海关商户名称" autocomplete="off" class="layui-input ns-len-long" value="{$config.merchant_name|default=''}">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>备案海关：</label>
                <div class="layui-input-block">
                    <input type="text" name="customs_code" lay-verify="required" placeholder="请输入备案海关代码" autocomplete="off" class="layui-input ns-len-mid" value="{$config.customs_code|default=''}">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">重试次数：</label>
                <div class="layui-input-block">
                    <input type="number" name="retry_times" placeholder="请输入重试次数" autocomplete="off" class="layui-input ns-len-short" value="{$config.retry_times|default='10'}">
                    <div class="ns-word-aux">当接口返回限流状态码(429)时的重试次数，默认为10次</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">重试间隔：</label>
                <div class="layui-input-block">
                    <input type="number" name="retry_interval" placeholder="请输入重试间隔(秒)" autocomplete="off" class="layui-input ns-len-short" value="{$config.retry_interval|default='1'}">
                    <div class="ns-word-aux">重试间隔时间，单位：秒，默认为1秒</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">超时时间：</label>
                <div class="layui-input-block">
                    <input type="number" name="timeout" placeholder="请输入超时时间(秒)" autocomplete="off" class="layui-input ns-len-short" value="{$config.timeout|default='10'}">
                    <div class="ns-word-aux">API请求超时时间，单位：秒，默认为10秒</div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">签名服务配置</span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="signature_form">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>签名服务地址：</label>
                <div class="layui-input-block">
                    <input type="text" name="signature_service[url]" lay-verify="required" placeholder="请输入签名服务地址" autocomplete="off" class="layui-input ns-len-long" value="{$config.signature_service.url|default=''}">
                    <div class="ns-word-aux">签名服务的API地址，如：http://xxx.com/api/sign</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">签名服务超时：</label>
                <div class="layui-input-block">
                    <input type="number" name="signature_service[timeout]" placeholder="请输入签名服务超时时间(秒)" autocomplete="off" class="layui-input ns-len-short" value="{$config.signature_service.timeout|default='10'}">
                    <div class="ns-word-aux">签名服务API请求超时时间，单位：秒，默认为10秒</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>证书序列号：</label>
                <div class="layui-input-block">
                    <input type="text" name="signature_service[cert_no]" lay-verify="required" placeholder="请输入签名证书序列号" autocomplete="off" class="layui-input ns-len-long" value="{$config.signature_service.cert_no|default=''}">
                    <div class="ns-word-aux">海关提供的签名证书序列号</div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">海关接口配置</span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="api_form">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>测试环境接口地址：</label>
                <div class="layui-input-block">
                    <input type="text" name="api[test_url]" lay-verify="required" placeholder="请输入测试环境接口地址" autocomplete="off" class="layui-input ns-len-long" value="{$config.api.test_url|default=''}">
                    <div class="ns-word-aux">海关提供的测试环境接口地址</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>生产环境接口地址：</label>
                <div class="layui-input-block">
                    <input type="text" name="api[prod_url]" lay-verify="required" placeholder="请输入生产环境接口地址" autocomplete="off" class="layui-input ns-len-long" value="{$config.api.prod_url|default=''}">
                    <div class="ns-word-aux">海关提供的生产环境接口地址</div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="ns-form-row">
    <button class="layui-btn ns-bg-color" lay-submit lay-filter="save">保存</button>
    <button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
</div>
{/block}

{block name="script"}
<script>
    layui.use(['form'], function() {
        var form = layui.form;
        var repeat_flag = false; // 防重复提交
        
        form.on('submit(save)', function(data) {
            if (repeat_flag) return false;
            repeat_flag = true;
            
            // 合并三个表单数据
            var formData1 = form.val('config_form');
            var formData2 = form.val('signature_form');
            var formData3 = form.val('api_form');
            
            var config = Object.assign({}, formData1, formData2, formData3);
            
            $.ajax({
                url: ns.url("customsChinaport://admin/chinaport/config"),
                data: {
                    config: JSON.stringify(config)
                },
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.msg(res.message);
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        });
        
        // 表单验证
        form.verify({
            url: function(value) {
                if (value && !/^(http|https):\/\/[^\s]+/.test(value)) {
                    return '请输入正确的URL地址';
                }
            }
        });
    });
    
    function back() {
        location.href = ns.url("customsChinaport://admin/chinaport/index");
    }
</script>
{/block} 