{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .ns-card-count {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 130px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .ns-card-count-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
    }
    .ns-card-count-num {
        font-size: 30px;
        font-weight: bold;
    }
    .ns-count-zheng {
        color: #2ecc71;
    }
    .ns-count-cha {
        color: #e74c3c;
    }
</style>
{/block}

{block name="main"}
<div class="ns-card-brief layui-card ns-card-common ns-card-brief">
    <div class="ns-form-content">
        <h2 class="ns-form-title ns-card-title">海关接口概览</h2>
    </div>
</div>

<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>本页面展示海关跨境电商数据对接接口的使用情况和最近调用记录</li>
            <li>可以查看总调用次数、成功率和最近的接口调用明细</li>
            <li>如需查看完整的接口调用记录，请点击"接口日志"菜单</li>
            <li>如需配置接口参数，请点击"接口配置"菜单</li>
            <li>本插件支持海关跨境电商进口统一版信息化系统平台的接口对接</li>
            <li>支持订单申报、支付单申报和查询回执等功能</li>
        </ul>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">接口统计</span>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="ns-card-common ns-card-count">
                    <div class="ns-card-count-title">
                        <span>总调用次数</span>
                    </div>
                    <div class="ns-card-count-num ns-count-{$total_count > 0 ? 'cha' : 'zheng'}">
                        <span>{$total_count}</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="ns-card-common ns-card-count">
                    <div class="ns-card-count-title">
                        <span>成功调用</span>
                    </div>
                    <div class="ns-card-count-num ns-count-zheng">
                        <span>{$success_count}</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="ns-card-common ns-card-count">
                    <div class="ns-card-count-title">
                        <span>失败调用</span>
                    </div>
                    <div class="ns-card-count-num ns-count-cha">
                        <span>{$fail_count}</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="ns-card-common ns-card-count">
                    <div class="ns-card-count-title">
                        <span>成功率</span>
                    </div>
                    <div class="ns-card-count-num ns-count-{$success_rate > 0.8 ? 'zheng' : 'cha'}">
                        <span>{$success_rate * 100}%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">最近10条接口调用记录</span>
    </div>
    <div class="layui-card-body">
        <table id="recent_log_list" lay-filter="recent_log_list"></table>
    </div>
</div>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'table'], function() {
        var table = layui.table;
        var form = layui.form;
        
        table.render({
            elem: '#recent_log_list',
            url: ns.url("customsChinaport://admin/chinaport/log", {recent: 1}),
            cols: [
                [{
                    field: 'id',
                    title: 'ID',
                    width: '5%'
                }, {
                    field: 'type',
                    title: '接口类型',
                    width: '10%',
                    templet: function(data) {
                        var types = {
                            1: '接收请求',
                            2: '生成XML',
                            3: '提交订单',
                            4: '提交支付',
                            5: '回执处理',
                            6: '限流重试'
                        };
                        return types[data.type] || '未知类型';
                    }
                }, {
                    field: 'status',
                    title: '状态',
                    width: '5%',
                    templet: function(data) {
                        return data.status == 1 ? '<span class="layui-text-green">成功</span>' : '<span class="layui-text-red">失败</span>';
                    }
                }, {
                    field: 'error_msg',
                    title: '错误信息',
                    width: '20%'
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    width: '15%',
                    templet: function(data) {
                        return ns.time_to_date(data.create_time);
                    }
                }]
            ]
        });
    });
</script>
{/block} 