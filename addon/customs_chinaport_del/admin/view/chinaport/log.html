{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
    .layui-form-label {
        width: 90px;
    }
    .layui-input-inline {
        width: 220px;
    }
    pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 13px;
        line-height: 1.5;
    }
    .search-container {
        margin-bottom: 15px;
    }
    .search-form {
        background-color: #fff;
        padding: 15px;
        border-radius: 5px;
    }
    .search-form .layui-form-item {
        margin-bottom: 0;
    }
    .ns-data-table {
        margin-top: 15px;
    }
</style>
{/block}

{block name="main"}
<div class="ns-card-brief layui-card ns-card-common ns-card-brief">
    <div class="ns-form-content">
        <h2 class="ns-form-title ns-card-title">海关接口日志</h2>
    </div>
</div>

<div class="layui-collapse ns-tips">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>本页面展示海关跨境电商数据对接接口的调用日志记录</li>
            <li>可以按照接口类型、状态和时间范围进行筛选查询</li>
            <li>点击"查看详情"可以查看完整的请求和响应数据</li>
            <li>接口类型说明：
                <ul>
                    <li>接收请求：系统接收海关系统发来的数据请求</li>
                    <li>生成XML：系统生成海关申报XML数据</li>
                    <li>提交订单：系统向海关提交订单申报数据</li>
                    <li>提交支付：系统向海关提交支付单数据</li>
                    <li>回执处理：系统处理海关回执数据</li>
                    <li>限流重试：因海关接口限流导致的重试</li>
                </ul>
            </li>
            <li>错误信息字段会显示接口调用失败的具体原因，可用于排查问题</li>
        </ul>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">日志查询</span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="log_search_form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">接口类型：</label>
                    <div class="layui-input-inline">
                        <select name="type">
                            <option value="">全部</option>
                            <option value="1">接收请求</option>
                            <option value="2">生成XML</option>
                            <option value="3">提交订单</option>
                            <option value="4">提交支付</option>
                            <option value="5">回执处理</option>
                            <option value="6">限流重试</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">状态：</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value="">全部</option>
                            <option value="1">成功</option>
                            <option value="0">失败</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开始时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="start_time" id="start_time" autocomplete="off" class="layui-input" placeholder="请选择开始时间">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">结束时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="end_time" id="end_time" autocomplete="off" class="layui-input" placeholder="请选择结束时间">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-primary" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card ns-card-common ns-card-brief">
    <div class="layui-card-header">
        <span class="ns-card-title">日志列表</span>
    </div>
    <div class="layui-card-body">
        <table id="log_list" lay-filter="log_list"></table>
    </div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="ns-table-btn">
        <a class="layui-btn" lay-event="detail">查看详情</a>
    </div>
</script>
{/block}

{block name="script"}
<script>
    layui.use(['form', 'table', 'laydate'], function() {
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        
        // 日期选择器
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });
        
        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });
        
        table.render({
            elem: '#log_list',
            url: ns.url("customsChinaport://admin/chinaport/log"),
            cols: [
                [{
                    field: 'id',
                    title: 'ID',
                    width: '5%'
                }, {
                    field: 'type',
                    title: '接口类型',
                    width: '10%',
                    templet: function(data) {
                        var types = {
                            1: '接收请求',
                            2: '生成XML',
                            3: '提交订单',
                            4: '提交支付',
                            5: '回执处理',
                            6: '限流重试'
                        };
                        return types[data.type] || '未知类型';
                    }
                }, {
                    field: 'status',
                    title: '状态',
                    width: '5%',
                    templet: function(data) {
                        return data.status == 1 ? '<span class="layui-text-green">成功</span>' : '<span class="layui-text-red">失败</span>';
                    }
                }, {
                    field: 'error_msg',
                    title: '错误信息',
                    width: '20%'
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    width: '15%',
                    templet: function(data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    title: '操作',
                    width: '10%',
                    toolbar: '#operation'
                }]
            ],
            page: true
        });
        
        // 搜索
        form.on('submit(search)', function(data) {
            table.reload('log_list', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 查看详情
        table.on('tool(log_list)', function(obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    type: 1,
                    title: '日志详情',
                    area: ['800px', '600px'],
                    content: '<div class="layui-card ns-card-common"><div class="layui-card-body"><pre style="overflow: auto; max-height: 500px;">' + 
                             '<strong>请求数据:</strong><br>' + formatJson(data.request_data) + 
                             '<br><br><strong>响应数据:</strong><br>' + formatJson(data.response_data) + 
                             '</pre></div></div>'
                });
            }
        });
        
        // 格式化JSON
        function formatJson(json) {
            try {
                if (!json) return '';
                if (typeof json === 'string') {
                    json = JSON.parse(json);
                }
                return JSON.stringify(json, null, 4);
            } catch (e) {
                return json;
            }
        }
    });
</script>
{/block} 