<?php
namespace addon\customs_chinaport\model;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 海关接口日志模型
 */
class ChinaportLog extends BaseModel
{
    // 表名
    protected $name = 'customs_chinaport_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    /**
     * 添加日志
     * @param int $type 日志类型 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试
     * @param array|string $requestData 请求数据
     * @param array|string $responseData 响应数据
     * @param int $status 状态 0-失败 1-成功
     * @param string $errorMsg 错误信息
     * @return array
     */
    public function addLog($type, $requestData, $responseData, $status, $errorMsg = '')
    {
        // 处理请求数据
        if (is_array($requestData)) {
            $request_data = json_encode($requestData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
            // 如果JSON编码失败，尝试递归清理数组中的无效UTF-8字符
            if ($request_data === false) {
                $requestData = $this->sanitizeArrayData($requestData);
                $request_data = json_encode($requestData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
            }
        } else {
            $request_data = $requestData;
        }
        
        // 处理响应数据
        if (is_array($responseData)) {
            $response_data = json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
            // 如果JSON编码失败，尝试递归清理数组中的无效UTF-8字符
            if ($response_data === false) {
                $responseData = $this->sanitizeArrayData($responseData);
                $response_data = json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);
            }
        } else {
            $response_data = $responseData;
        }
        
        $data = [
            'type' => $type,
            'request_data' => $request_data,
            'response_data' => $response_data,
            'status' => $status,
            'error_msg' => $errorMsg,
            'create_time' => time(),
            'update_time' => time(),
        ];
        
        $res = Db::name($this->name)->insert($data);
        return $this->success($res);
    }
    
    /**
     * 递归清理数组中的无效UTF-8字符
     * @param array $data 待处理数组
     * @return array 处理后的数组
     */
    private function sanitizeArrayData($data)
    {
        $result = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->sanitizeArrayData($value);
            } elseif (is_string($value)) {
                // 清理字符串中的无效UTF-8字符
                if (function_exists('iconv')) {
                    $result[$key] = @iconv('UTF-8', 'UTF-8//IGNORE', $value);
                    if ($result[$key] === false) {
                        $result[$key] = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                    }
                } else {
                    $result[$key] = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                }
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    /**
     * 获取日志列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getLogPageList($condition = [], $field = '*', $order = 'create_time desc', $page = 1, $page_size = 20)
    {
        $count = Db::name($this->name)->where($condition)->count();
        $list = Db::name($this->name)
            ->where($condition)
            ->field($field)
            ->order($order)
            ->page($page, $page_size)
            ->select()
            ->toArray();
        
        return $this->success(
            [
                'count' => $count,
                'list' => $list,
                'page' => $page,
                'page_size' => $page_size,
            ]
        );
    }
} 