<?php
namespace addon\customs_chinaport\model;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 海关回执记录模型
 */
class ChinaportReceipt extends BaseModel
{
    // 表名
    protected $name = 'customs_chinaport_receipt';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    /**
     * 保存回执数据
     * 
     * @param array $data 回执数据
     * @return array
     */
    public function saveReceipt($data)
    {
        $insertData = [
            'receipt_id' => $data['receipt_id'],
            'receipt_type' => $data['receipt_type'],
            'original_xml' => $data['original_xml'],
            'customs_code' => $data['customs_code'],
            'customs_name' => $data['customs_name'],
            'status' => $data['status'],
            'status_desc' => $data['status_desc'],
            'order_no' => $data['order_no'],
            'payment_no' => $data['payment_no'],
            'note' => $data['note'],
            'receipt_time' => $data['receipt_time'],
            'process_status' => 0,
            'process_message' => '',
            'process_time' => 0,
            'create_time' => time(),
            'update_time' => time(),
        ];
        
        try {
            // 先查询是否已存在相同回执ID的记录
            $exist = Db::name($this->name)->where('receipt_id', $data['receipt_id'])->find();
            
            if ($exist) {
                // 如果已存在，则更新
                $result = Db::name($this->name)
                    ->where('receipt_id', $data['receipt_id'])
                    ->update($insertData);
                return $this->success($result);
            } else {
                // 否则插入新记录
                $result = Db::name($this->name)->insert($insertData);
                return $this->success($result);
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新回执处理结果
     * 
     * @param string $receiptId 回执ID
     * @param array $data 更新数据
     * @return array
     */
    public function updateReceipt($receiptId, $data)
    {
        try {
            $updateData = [
                'update_time' => time()
            ];
            
            // 合并更新数据
            if (isset($data['process_status'])) {
                $updateData['process_status'] = $data['process_status'];
            }
            
            if (isset($data['process_message'])) {
                $updateData['process_message'] = $data['process_message'];
            }
            
            if (isset($data['process_time'])) {
                $updateData['process_time'] = $data['process_time'];
            }
            
            $result = Db::name($this->name)
                ->where('receipt_id', $receiptId)
                ->update($updateData);
                
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取回执分页列表
     * 
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序规则
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getReceiptPageList($condition = [], $field = '*', $order = '', $page = 1, $page_size = PAGE_LIST_ROWS)
    {
        $list = Db::name($this->name)
            ->where($condition)
            ->field($field)
            ->order($order)
            ->paginate([
                'list_rows' => $page_size,
                'page' => $page
            ]);
            
        return $this->success($list);
    }
    
    /**
     * 获取回执详情
     * 
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @return array
     */
    public function getReceiptInfo($condition = [], $field = '*')
    {
        $info = Db::name($this->name)
            ->where($condition)
            ->field($field)
            ->find();
            
        return $this->success($info);
    }
    
    /**
     * 获取单个订单的回执历史
     * 
     * @param string $orderNo 订单号
     * @return array
     */
    public function getOrderReceiptHistory($orderNo)
    {
        $list = Db::name($this->name)
            ->where('order_no', $orderNo)
            ->order('receipt_time desc')
            ->select()
            ->toArray();
            
        return $this->success($list);
    }
    
    /**
     * 获取单个支付单的回执历史
     * 
     * @param string $paymentNo 支付单号
     * @return array
     */
    public function getPaymentReceiptHistory($paymentNo)
    {
        $list = Db::name($this->name)
            ->where('payment_no', $paymentNo)
            ->order('receipt_time desc')
            ->select()
            ->toArray();
            
        return $this->success($list);
    }
} 