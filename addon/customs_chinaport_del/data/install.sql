-- 海关接口日志表
CREATE TABLE IF NOT EXISTS `customs_chinaport_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '日志类型: 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-失败 1-成功',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `create_time` int(10) NOT NULL COMMENT '创建时间',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关接口日志表';

-- 海关回执记录表
CREATE TABLE IF NOT EXISTS `customs_chinaport_receipt` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `receipt_id` varchar(100) NOT NULL COMMENT '回执ID',
  `receipt_type` varchar(20) NOT NULL COMMENT '回执类型: order-订单 payment-支付',
  `original_xml` text COMMENT '原始XML数据',
  `customs_code` varchar(50) DEFAULT '' COMMENT '海关编码',
  `customs_name` varchar(100) DEFAULT '' COMMENT '海关名称',
  `status` varchar(10) DEFAULT '' COMMENT '回执状态',
  `status_desc` varchar(100) DEFAULT '' COMMENT '状态描述',
  `order_no` varchar(50) DEFAULT '' COMMENT '订单号',
  `payment_no` varchar(50) DEFAULT '' COMMENT '支付单号',
  `note` varchar(500) DEFAULT '' COMMENT '回执信息',
  `receipt_time` int(11) NOT NULL DEFAULT '0' COMMENT '回执时间',
  `process_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态: 0-未处理 1-已处理',
  `process_message` varchar(500) DEFAULT '' COMMENT '处理消息',
  `process_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_type` (`receipt_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_no` (`payment_no`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_receipt_time` (`receipt_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关回执记录表'; 