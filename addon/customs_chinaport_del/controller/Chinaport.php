<?php
namespace addon\customs_chinaport\controller;

use app\Controller;
use addon\customs_chinaport\model\ChinaportLog;
use addon\customs_chinaport\service\ChinavportService;
use addon\customs_chinaport\service\ReceiptService;
use think\facade\Validate;

/**
 * 海关接口控制器
 */
class Chinaport extends Controller
{
    /**
     * 海关数据获取接口入口
     * 接收海关发起的数据获取请求
     */
    public function platDataOpen()
    {
        // 获取参数
        $params = $this->request->post();
        
        // 记录请求日志
        $log_model = new ChinaportLog();
        $log_id = $log_model->addLog(1, $params, [], 0);
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '',
            'serviceTime' => time() * 1000 // 毫秒时间戳
        ];
        
        try {
            // 验证参数
            $validate_rules = config('addon.customs_chinaport.validate.platDataOpen');
            $validate = Validate::rule($validate_rules);
            
            if (!$validate->check($params)) {
                $return_data['code'] = '20000';
                $return_data['message'] = $validate->getError();
                
                // 记录验证失败日志
                $log_model->addLog(1, $params, $return_data, 0, $return_data['message']);
                
                return json($return_data);
            }
            
            // 调用海关服务处理请求
            $service = new ChinavportService();
            $result = $service->handlePlatDataOpen($params);
            
            // 返回处理结果
            return json($result);
            
        } catch (\Exception $e) {
            $return_data['code'] = '20003';
            $return_data['message'] = '处理异常: ' . $e->getMessage();
            
            // 记录异常日志
            $log_model->addLog(1, $params, $return_data, 0, $return_data['message']);
            
            return json($return_data);
        }
    }
    
    /**
     * 提交订单数据到海关
     * 由系统内部调用，将订单数据提交到海关
     */
    public function submitOrder()
    {
        // 获取参数
        $params = $this->request->post();
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '',
            'data' => []
        ];
        
        try {
            // 验证参数
            $validate = Validate::rule([
                'order_no' => 'require|max:50'
            ]);
            
            if (!$validate->check($params)) {
                $return_data['code'] = '20000';
                $return_data['message'] = $validate->getError();
                return json($return_data);
            }
            
            // 调用海关服务处理请求
            $service = new ChinavportService();
            $result = $service->submitOrderToCustoms($params['order_no']);
            
            // 返回处理结果
            return json($result);
            
        } catch (\Exception $e) {
            $return_data['code'] = '20003';
            $return_data['message'] = '处理异常: ' . $e->getMessage();
            return json($return_data);
        }
    }
    
    /**
     * 提交支付数据到海关
     * 由系统内部调用，将支付数据提交到海关
     */
    public function submitPayment()
    {
        // 获取参数
        $params = $this->request->post();
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '',
            'data' => []
        ];
        
        try {
            // 验证参数
            $validate = Validate::rule([
                'order_no' => 'require|max:50',
                'payment_no' => 'require|max:50',
                'payment_amount' => 'require|integer|gt:0',
                'payment_time' => 'require|date'
            ]);
            
            if (!$validate->check($params)) {
                $return_data['code'] = '20000';
                $return_data['message'] = $validate->getError();
                return json($return_data);
            }
            
            // 调用海关服务处理请求
            $service = new ChinavportService();
            $result = $service->submitPaymentToCustoms($params);
            
            // 返回处理结果
            return json($result);
            
        } catch (\Exception $e) {
            $return_data['code'] = '20003';
            $return_data['message'] = '处理异常: ' . $e->getMessage();
            return json($return_data);
        }
    }
    
    /**
     * 生成并签名订单XML
     * 由系统内部调用，生成并签名订单XML，但不提交到海关
     */
    public function generateOrderXml()
    {
        // 获取参数
        $params = $this->request->post();
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '',
            'data' => []
        ];
        
        try {
            // 验证参数
            $validate = Validate::rule([
                'order_no' => 'require|max:50'
            ]);
            
            if (!$validate->check($params)) {
                $return_data['code'] = '20000';
                $return_data['message'] = $validate->getError();
                return json($return_data);
            }
            
            // 调用海关服务获取订单数据
            $service = new ChinavportService();
            $orderData = $service->getOrderData($params['order_no']);
            
            if (empty($orderData)) {
                $return_data['code'] = '20001';
                $return_data['message'] = '订单不存在';
                return json($return_data);
            }
            
            // 生成并签名XML
            $result = $service->generateCEB311OrderXml($orderData);
            
            // 返回处理结果
            return json($result);
            
        } catch (\Exception $e) {
            $return_data['code'] = '20003';
            $return_data['message'] = '处理异常: ' . $e->getMessage();
            return json($return_data);
        }
    }
    
    /**
     * 接收海关回执接口
     * 海关推送回执信息的接口
     */
    public function receiveReceipt()
    {
        // 接收原始POST数据
        $receiptXml = file_get_contents('php://input');
        
        // 记录请求日志
        $log_model = new ChinaportLog();
        $log_id = $log_model->addLog(5, $receiptXml, [], 0);
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '接收成功',
            'serviceTime' => time() * 1000 // 毫秒时间戳
        ];
        
        try {
            if (empty($receiptXml)) {
                $return_data['code'] = '20000';
                $return_data['message'] = '回执数据为空';
                
                // 记录失败日志
                $log_model->addLog(5, [], $return_data, 0, $return_data['message']);
                
                return json($return_data);
            }
            
            // 调用回执处理服务处理回执数据
            $service = new ReceiptService();
            $result = $service->processReceipt($receiptXml);
            
            // 回执处理失败也返回接收成功，避免海关重复推送
            if ($result['code'] != '10000') {
                // 记录处理失败日志
                $log_model->addLog(5, $receiptXml, $result, 0, $result['message']);
            }
            
            // 返回处理结果
            return json($return_data);
            
        } catch (\Exception $e) {
            $return_data['code'] = '10000'; // 依然返回成功，避免海关重复推送
            $return_data['message'] = '接收成功，但处理异常';
            
            // 记录异常日志
            $log_model->addLog(5, $receiptXml, [], 0, '处理异常: ' . $e->getMessage());
            
            return json($return_data);
        }
    }
    
    /**
     * 查询回执历史接口
     * 由系统内部调用，查询订单或支付单的回执历史
     */
    public function queryReceiptHistory()
    {
        // 获取参数
        $params = $this->request->get();
        
        // 返回数据结构
        $return_data = [
            'code' => '10000',
            'message' => '',
            'data' => []
        ];
        
        try {
            // 验证参数
            $validate = Validate::rule([
                'type' => 'require|in:order,payment',
                'no' => 'require|max:50'
            ]);
            
            if (!$validate->check($params)) {
                $return_data['code'] = '20000';
                $return_data['message'] = $validate->getError();
                return json($return_data);
            }
            
            // 引入回执模型
            $receipt_model = new \addon\customs_chinaport\model\ChinaportReceipt();
            
            // 根据类型查询不同的历史
            if ($params['type'] == 'order') {
                $result = $receipt_model->getOrderReceiptHistory($params['no']);
            } else {
                $result = $receipt_model->getPaymentReceiptHistory($params['no']);
            }
            
            // 构建返回数据
            if ($result['code'] == 0) {
                $return_data['data'] = $result['data'];
            } else {
                $return_data['code'] = '20001';
                $return_data['message'] = $result['message'];
            }
            
            // 返回处理结果
            return json($return_data);
            
        } catch (\Exception $e) {
            $return_data['code'] = '20003';
            $return_data['message'] = '处理异常: ' . $e->getMessage();
            return json($return_data);
        }
    }
} 