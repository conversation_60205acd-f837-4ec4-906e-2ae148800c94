.qrcode-body {
    padding: 10px;
    margin: 10px 0;
    background: #fff;
}

#albumList {
    overflow: hidden;
}

.input-file {
    position: absolute;
    top: -10px !important;
    right: 9px !important;
    height: 26px !important;
    width: 94px !important;
    filter: alpha(opacity:0) !important;
    opacity: 0 !important;
    line-height: 26px;
}

.qrcode_button {
    background-color: #51a351;
    border: none;
    margin-top: 15px;
    width: 100%;
    color: #FFF;
    padding: 5px 10px;
}

.check {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 100%;
    right: -1px;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
}

.check div {
    width: 60%;
    height: 30px;
    margin-left: 20%;
    border: 1px solid #fff;
    margin-top: 10%;
    text-align: center;
}

.check div:hover {
    background: #126AE4;
    border: 1px solid #126AE4;
}

.check div span {
    font-size: 15px;
    color: #fff;
    line-height: 30px;
}

.options-btn {
    padding-bottom: 10px;
}

#albumList li {
    opacity: 1;
    height: auto;
    position: relative;
    float: left;
    margin-right: 1%;
    margin-top: 10px;
    margin-bottom: 10px;
    line-height: 20px;
    overflow: hidden;
}

.img-block {
    width: 100%;
}
.qrcode {
    width: 320px;
    height: 569px;
    background: #f5f5f5;
    position: relative;
}
.qrcode #imgLogo {
    max-width: 320px;
    max-height: 569px;
}
.tdrag-header {
    width: 45px;
    height: 45px;
    position: absolute;
    left: 59px; top: 15px;
    cursor: move;
}
.tdrag-logo {
    width: 43px;
    position: absolute;
    left: 60px;
    top: 210px;
    cursor: move;
}
.tdrag-code{
    width: 144px;
    height: 144px;
    position: absolute;
    left: 70px;
    top: 300px;
    cursor: move;
}
.tdrag-name{
    font-size: 14px;
    color: #000;
    position: absolute;
    left: 128px;
    top: 23px;
    cursor: move;
}