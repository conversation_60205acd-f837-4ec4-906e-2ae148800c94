<!DOCTYPE html>
<html>
<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title>{$menu_info['title']|default=""} - {$website['title']|default="Niushop开源商城"}</title>
	<meta name="keywords" content="{$website['keywords']|default='Niushop开源商城'}">
	<meta name="description" content="{$website['desc']|default='描述'}">
	<link rel="icon" type="image/x-icon" href="__STATIC__/img/bitbug_favicon.ico" />
	<link rel="stylesheet" type="text/css" href="STATIC_CSS/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="ADMIN_CSS/common.css" />
	<link rel="stylesheet" href="WECHAT_CSS/wx_material.css">
	<script src="__STATIC__/js/jquery-3.1.1.js"></script>
	<script src="__STATIC__/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function() {});
		window.ns_url = {
			baseUrl: "ROOT_URL/",
			route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
		};
	</script>
	<script src="__STATIC__/js/common.js"></script>
</head>

<body>
	<!-- 内容 -->
	{if $type == 1}
	<div class="layui-tab layui-tab-brief" id="marterial_graphic_message">
		<ul class="layui-tab-title">
			<li class="layui-this">图文消息</li>
			<!-- <li>高级图文</li> -->
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<table id="marterial_graphic_message_list" lay-filter="marterial_graphic_message"></table>
				<!-- 标题 -->
				<script type="text/html" id="graphic_message_title">
					<div class="layui-row grid-demo">
					{{# for (var index in d.value) { }}
	     			 	<div class="layui-col-md12 layui-clear">
							<div class="layui-col-md3 article-img" style="float:left;">
								<span class="ns-bg-color" style="color: #fff;padding: 2px 4px">图文</span>&nbsp;&nbsp;
							</div>
							<div class="layui-col-md3 title" style="float:left;">
								<a href="javascript:void(0);" onclick="preview({{d.id}}, {{index}})">{{d.value[index].title}}</a>
							</div>
						</div>
					{{# } }}
					{{# if (d.value.length == 1) { }}
						<div class='layui-col-md12 read-all layui-clear' onclick="preview({{d.id}})">
							<div class='layui-col-md4' style="float:left;">阅读全文</div>
							<div class='layui-col-md4 layui-col-md-offset4'>  </div>
						</div>
					{{# } }}
					</div>
				</script>
				<!-- 创建时间 -->
				<script type="text/html" id="create_time">
					<div>{{ ns.time_to_date(d.create_time) }}</div>
				</script>
				<!-- 修改时间 -->
				<script type="text/html" id="update_time">
					<div>{{ ns.time_to_date(d.update_time) }}</div>
				</script>
				<!-- 列表操作 -->
				<script type="text/html" id="operation">
					<a class="default layui-btn-sm" lay-event="choose">选取</a>
				</script>
			</div>
		</div>
	</div>
	{else/}


	<!-- 文本消息 -->
	<div class="layui-tab layui-tab-brief" id="material_text">
		<ul class="layui-tab-title">
			<li class="layui-this">文本消息</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<table id="material_text_list" lay-filter="material_text"></table>
				<!-- 内容 -->
				<script type="text/html" id="text_content">
					<div class="layui-row grid-demo">
						<div class="layui-col-md12 layui-clear">
							<div class="layui-col-md3 article-img" style="float:left;">
								<span class="ns-bg-color" style="color: #fff;padding: 2px 4px">文本</span>&nbsp;&nbsp;
							</div>
							<div class="layui-col-md3 title" style="float:left;">
								<a href="javascript:void(0);" onclick="previewText('{{d.value.content}}')">{{d.value.content}}</a>
							</div>
						</div>
					</div>
				</script>
				<!-- 创建时间 -->
				<script type="text/html" id="create_time">
					<div>{{ ns.time_to_date(d.create_time) }}</div>
				</script>
				<!-- 修改时间 -->
				<script type="text/html" id="update_time">
					<div>{{ ns.time_to_date(d.update_time) }}</div>
				</script>
				<!-- 列表操作 -->
				<script type="text/html" id="operation">
					<a class="default layui-btn-sm" lay-event="choose">选取</a>
				</script>
			</div>
		</div>
	</div>
	{/if}
</body>

<script type="text/javascript" src="WECHAT_JS/wx_material_mannager.js"></script>
<script type="text/javascript">
	loadMaterialList({$type});
</script>

</html>