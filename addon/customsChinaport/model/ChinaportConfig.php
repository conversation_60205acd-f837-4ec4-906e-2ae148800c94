<?php
namespace addon\customsChinaport\model;

use app\model\BaseModel;

/**
 * 海关配置管理模型
 */
class ChinaportConfig extends BaseModel
{
    /**
     * 获取所有配置
     * @return array
     */
    public function getConfig()
    {
        $config_list = model('customs_chinaport_config')->getList([], '*', 'id asc');
        
        $config_data = [];
        if (!empty($config_list)) {
            foreach ($config_list as $item) {
                $config_data[$item['config_key']] = $item['config_value'];
            }
        }
        
        return $this->success($config_data);
    }
    
    /**
     * 根据键名获取配置
     * 
     * @param string $key 键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfigValueByKey($key, $default = '')
    {
        $info = model('customs_chinaport_config')->getInfo([['config_key', '=', $key]]);
        
        if (!empty($info)) {
            return $info['config_value'];
        }
        
        return $default;
    }
    
    /**
     * 设置配置
     * 
     * @param array $data 配置数据
     * @return array
     */
    public function setConfig($data)
    {
        if (empty($data)) {
            return $this->error(-1, '参数错误');
        }
        
        model('customs_chinaport_config')->startTrans();
        try {
            foreach ($data as $key => $value) {
                $count = model('customs_chinaport_config')->getCount([['config_key', '=', $key]]);
                
                $time = time();
                
                if ($count > 0) {
                    // 更新
                    model('customs_chinaport_config')->update([
                        'config_value' => $value,
                        'update_time' => $time
                    ], [['config_key', '=', $key]]);
                } else {
                    // 新增
                    model('customs_chinaport_config')->add([
                        'config_key' => $key,
                        'config_value' => $value,
                        'create_time' => $time,
                        'update_time' => $time
                    ]);
                }
            }
            
            model('customs_chinaport_config')->commit();
            return $this->success();
        } catch (\Exception $e) {
            model('customs_chinaport_config')->rollback();
            return $this->error(-1, $e->getMessage());
        }
    }
} 