<?php
namespace addon\customsChinaport\model;

use app\model\BaseModel;

/**
 * 海关任务处理模型
 */
class ChinaportTask extends BaseModel
{
    /**
     * 创建任务
     * 
     * @param array $data 任务数据
     * @return array
     */
    public function createTask($data)
    {
        // 数据校验
        if (empty($data['task_type']) || empty($data['business_id']) || empty($data['data'])) {
            return $this->error(-1, '任务数据不完整');
        }
        
        // 生成唯一任务ID
        $task_id = $this->generateTaskId($data['task_type'], $data['business_id']);
        
        // 检查任务是否已存在
        $task_info = model('customs_chinaport_task')->getInfo([
            ['task_id', '=', $task_id]
        ]);
        
        if (!empty($task_info)) {
            return $this->error(-1, '任务已存在');
        }
        
        // 获取最大重试次数
        $config_model = new ChinaportConfig();
        $retry_max_times = $config_model->getConfigValueByKey('retry_max_times', 5);
        
        // 组装任务数据
        $task_data = [
            'task_id' => $task_id,
            'task_type' => $data['task_type'],
            'business_id' => $data['business_id'],
            'data' => json_encode($data['data'], JSON_UNESCAPED_UNICODE), // 将数组序列化为JSON字符串
            'status' => 0, // 待处理
            'retry_count' => 0,
            'max_retry' => $retry_max_times,
            'next_retry_time' => time(),
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 创建任务
        $result = model('customs_chinaport_task')->add($task_data);
        
        if ($result === false) {
            return $this->error(-1, '创建任务失败');
        }
        
        return $this->success($task_id);
    }
    
    /**
     * 更新任务状态
     * 
     * @param string $task_id 任务ID
     * @param int $status 任务状态：0-待处理 1-处理中 2-处理成功 3-处理失败
     * @param string $error_msg 错误信息
     * @return array
     */
    public function updateTaskStatus($task_id, $status, $error_msg = '')
    {
        $update_data = [
            'status' => $status,
            'update_time' => time()
        ];
        
        // 如果处理失败，计算下次重试时间
        if ($status == 3) {
            $update_data['last_error'] = $error_msg;
            
            // 获取任务信息
            $task_info = model('customs_chinaport_task')->getInfo([
                ['task_id', '=', $task_id]
            ]);
            
            if (empty($task_info)) {
                return $this->error(-1, '任务不存在');
            }
            
            // 重试次数+1
            $update_data['retry_count'] = $task_info['retry_count'] + 1;
            
            // 判断是否达到最大重试次数
            if ($update_data['retry_count'] < $task_info['max_retry']) {
                // 计算下次重试时间（采用指数退避算法）
                $config_model = new ChinaportConfig();
                $retry_interval = $config_model->getConfigValueByKey('retry_interval', 30);
                $next_retry_seconds = $retry_interval * pow(2, $update_data['retry_count'] - 1);
                $update_data['next_retry_time'] = time() + $next_retry_seconds;
                $update_data['status'] = 0; // 重新设置为待处理状态
            }
        }
        
        // 更新任务
        $result = model('customs_chinaport_task')->update($update_data, [
            ['task_id', '=', $task_id]
        ]);
        
        if ($result === false) {
            return $this->error(-1, '更新任务状态失败');
        }
        
        return $this->success();
    }
    
    /**
     * 获取待处理任务列表
     * 
     * @param int $limit 获取数量
     * @return array
     */
    public function getPendingTasks($limit = 10)
    {
        $task_list = model('customs_chinaport_task')->getList([
            ['status', '=', 0],
            ['next_retry_time', '<=', time()]
        ], 'id,task_id,task_type,business_id,data,retry_count', 'next_retry_time asc', 0, $limit);
        
        return $this->success($task_list);
    }
    
    /**
     * 获取任务列表
     * 
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @param string $order 排序
     * @param string $field 查询字段
     * @return array
     */
    public function getTaskList($condition = [], $page = 1, $page_size = 20, $order = 'id desc', $field = '*')
    {
        $list = model('customs_chinaport_task')->pageList($condition, $order, $page, $page_size, $field);
        return $this->success($list);
    }
    
    /**
     * 更新任务
     * 
     * @param int $id 任务ID
     * @param array $data 更新数据
     * @return array
     */
    public function updateTask($id, $data)
    {
        $data['update_time'] = time();
        $res = model('customs_chinaport_task')->update($data, [['id', '=', $id]]);
        return $this->success($res);
    }
    
    /**
     * 生成唯一任务ID
     * 
     * @param string $task_type 任务类型
     * @param string $business_id 业务ID
     * @return string
     */
    private function generateTaskId($task_type, $business_id)
    {
        // 创建规范化的任务ID，格式：CP_{任务类型}_{业务ID的部分哈希}_{时间戳}
        $timestamp = time();
        $business_hash = substr(md5($business_id), 0, 8); // 取业务ID哈希的前8位
        return 'CP_' . strtoupper($task_type) . '_' . $business_hash . '_' . $timestamp;
    }
} 