<?php
namespace addon\customsChinaport\command;

use addon\customsChinaport\model\ChinaportTask as ChinaportTaskModel;
use addon\customsChinaport\service\ChinaportService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

/**
 * 海关数据上报定时任务
 */
class ChinaportTask extends Command
{
    /**
     * 配置指令
     */
    protected function configure()
    {
        $this->setName('chinaport:task')
            ->setDescription('处理海关数据上报任务');
    }
    
    /**
     * 执行命令
     * 
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $start_time = time();
        $output->writeln("开始处理海关数据上报任务: " . date('Y-m-d H:i:s', $start_time));
        
        try {
            // 处理待上报的订单任务
            $this->processOrderTasks($output);
            
            // 处理待上报的支付任务
            $this->processPaymentTasks($output);
            
            $end_time = time();
            $output->writeln("海关数据上报任务处理完成，耗时: " . ($end_time - $start_time) . "秒");
        } catch (\Exception $e) {
            $output->writeln("处理海关数据上报任务异常: " . $e->getMessage());
        }
    }
    
    /**
     * 处理订单上报任务
     * 
     * @param Output $output
     * @return void
     */
    protected function processOrderTasks(Output $output)
    {
        $task_model = new ChinaportTaskModel();
        $service = new ChinaportService();
        
        // 获取待处理的订单任务
        $tasks = $task_model->getTaskList(['type' => 1, 'status' => 0], 1, 20);
        
        if (empty($tasks['data'])) {
            $output->writeln("没有待处理的订单任务");
            return;
        }
        
        $output->writeln("发现 " . count($tasks['data']) . " 个待处理订单任务");
        
        foreach ($tasks['data'] as $task) {
            try {
                $output->write("处理订单任务 #{$task['id']} (订单号: {$task['order_no']})... ");
                
                // 获取订单数据
                $order_data = json_decode($task['data'], true);
                if (empty($order_data)) {
                    throw new \Exception("任务数据为空");
                }
                
                // 上报订单数据
                $result = $service->reportOrder($order_data);
                
                if ($result['code'] == 0) {
                    // 更新任务状态为成功
                    $task_model->updateTask($task['id'], [
                        'status' => 1,
                        'result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
                        'message' => '上报成功',
                        'process_time' => time()
                    ]);
                    $output->writeln("成功");
                } else {
                    // 更新任务状态为失败
                    $task_model->updateTask($task['id'], [
                        'status' => 2,
                        'message' => $result['message'],
                        'process_time' => time(),
                        'retry_count' => $task['retry_count'] + 1
                    ]);
                    $output->writeln("失败: " . $result['message']);
                }
            } catch (\Exception $e) {
                // 更新任务状态为失败
                $task_model->updateTask($task['id'], [
                    'status' => 2,
                    'message' => $e->getMessage(),
                    'process_time' => time(),
                    'retry_count' => $task['retry_count'] + 1
                ]);
                $output->writeln("异常: " . $e->getMessage());
            }
            
            // 避免请求过于频繁
            usleep(500000); // 休眠0.5秒
        }
    }
    
    /**
     * 处理支付上报任务
     * 
     * @param Output $output
     * @return void
     */
    protected function processPaymentTasks(Output $output)
    {
        $task_model = new ChinaportTaskModel();
        $service = new ChinaportService();
        
        // 获取待处理的支付任务
        $tasks = $task_model->getTaskList(['type' => 2, 'status' => 0], 1, 20);
        
        if (empty($tasks['data'])) {
            $output->writeln("没有待处理的支付任务");
            return;
        }
        
        $output->writeln("发现 " . count($tasks['data']) . " 个待处理支付任务");
        
        foreach ($tasks['data'] as $task) {
            try {
                $output->write("处理支付任务 #{$task['id']} (订单号: {$task['order_no']})... ");
                
                // 获取支付数据
                $payment_data = json_decode($task['data'], true);
                if (empty($payment_data)) {
                    throw new \Exception("任务数据为空");
                }
                
                // 上报支付数据
                $result = $service->reportPayment($payment_data);
                
                if ($result['code'] == 0) {
                    // 更新任务状态为成功
                    $task_model->updateTask($task['id'], [
                        'status' => 1,
                        'result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
                        'message' => '上报成功',
                        'process_time' => time()
                    ]);
                    $output->writeln("成功");
                } else {
                    // 更新任务状态为失败
                    $task_model->updateTask($task['id'], [
                        'status' => 2,
                        'message' => $result['message'],
                        'process_time' => time(),
                        'retry_count' => $task['retry_count'] + 1
                    ]);
                    $output->writeln("失败: " . $result['message']);
                }
            } catch (\Exception $e) {
                // 更新任务状态为失败
                $task_model->updateTask($task['id'], [
                    'status' => 2,
                    'message' => $e->getMessage(),
                    'process_time' => time(),
                    'retry_count' => $task['retry_count'] + 1
                ]);
                $output->writeln("异常: " . $e->getMessage());
            }
            
            // 避免请求过于频繁
            usleep(500000); // 休眠0.5秒
        }
    }
} 