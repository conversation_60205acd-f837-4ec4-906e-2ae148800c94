<?php
/**
 * 海关跨境电商数据对接事件配置
 */
return [
    'bind' => [
    ],
    'listen' => [
        // 订单支付成功事件，用于上报订单数据
        'OrderPaySuccess' => [
            'addon\\customsChinaport\\event\\OrderPaySuccess'
        ],
        // 支付成功事件，用于上报支付数据
        'PaymentSuccess' => [
            'addon\\customsChinaport\\event\\PaymentSuccess'
        ],
        // 定时任务注册事件
        'CronAddTasks' => [
            'addon\\customsChinaport\\event\\CronTask'
        ],
    ],
    'subscribe' => [
    ],
]; 