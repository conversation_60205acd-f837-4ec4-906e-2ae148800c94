<?php
/**
 * 海关接口模块API配置文件
 */
return [
    /**
     * 海关数据获取接口
     */
    'platDataOpen' => [
        'name' => '海关数据获取接口',
        'description' => '企业接收海关发起的支付相关实时数据获取请求',
        'path' => 'customsChinaport/api/platDataOpen',
        'method' => 'POST',
        'params' => [
            'orderNo' => [
                'required' => true,
                'type' => 'string',
                'description' => '申报订单的订单编号',
            ],
            'sessionID' => [
                'required' => true,
                'type' => 'string',
                'description' => '海关发起请求时，平台接收的会话ID',
            ],
            'serviceTime' => [
                'required' => true,
                'type' => 'integer',
                'description' => '调用时的系统时间',
            ],
        ],
    ],
    
    /**
     * 订单数据下发接口
     */
    'getOrderData' => [
        'name' => '订单数据下发接口',
        'description' => '客户端定期请求获取需要报关的订单数据',
        'path' => 'customsChinaport/api/getOrderData',
        'method' => 'POST',
        'params' => [
            'timestamp' => [
                'required' => true,
                'type' => 'integer',
                'description' => '时间戳，单位秒',
            ],
            'sign' => [
                'required' => true,
                'type' => 'string',
                'description' => 'md5(timestamp+signkey)签名',
            ],
        ],
    ],
]; 