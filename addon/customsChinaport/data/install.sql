-- 配置表
CREATE TABLE IF NOT EXISTS `xm_customs_chinaport_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键名',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关接口配置表';

-- 日志表
CREATE TABLE IF NOT EXISTS `xm_customs_chinaport_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '日志类型: 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-失败 1-成功',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关接口日志表';

-- 回执记录表
CREATE TABLE IF NOT EXISTS `xm_customs_chinaport_receipt` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `receipt_id` varchar(100) NOT NULL COMMENT '回执ID',
  `receipt_type` varchar(20) NOT NULL COMMENT '回执类型: order-订单 payment-支付',
  `original_xml` text COMMENT '原始XML数据',
  `customs_code` varchar(50) DEFAULT '' COMMENT '海关编码',
  `customs_name` varchar(100) DEFAULT '' COMMENT '海关名称',
  `status` varchar(10) DEFAULT '' COMMENT '回执状态',
  `status_desc` varchar(100) DEFAULT '' COMMENT '状态描述',
  `order_no` varchar(50) DEFAULT '' COMMENT '订单号',
  `payment_no` varchar(50) DEFAULT '' COMMENT '支付单号',
  `note` varchar(500) DEFAULT '' COMMENT '回执信息',
  `receipt_time` int(11) NOT NULL DEFAULT '0' COMMENT '回执时间',
  `process_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态: 0-未处理 1-已处理',
  `process_message` varchar(500) DEFAULT '' COMMENT '处理消息',
  `process_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_type` (`receipt_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_no` (`payment_no`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_receipt_time` (`receipt_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关回执记录表';

-- 任务队列表
CREATE TABLE IF NOT EXISTS `xm_customs_chinaport_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `task_id` varchar(50) NOT NULL COMMENT '任务ID（全局唯一）',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型：order-订单上报 payment-支付上报',
  `business_id` varchar(50) NOT NULL COMMENT '业务ID（订单号/支付号）',
  `data` text COMMENT '任务数据（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务状态：0-待处理 1-处理中 2-处理成功 3-处理失败',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry` int(11) NOT NULL DEFAULT '5' COMMENT '最大重试次数',
  `next_retry_time` int(11) NOT NULL DEFAULT '0' COMMENT '下次重试时间',
  `last_error` varchar(500) DEFAULT '' COMMENT '最后一次错误信息',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_id` (`task_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_status` (`status`),
  KEY `idx_next_retry_time` (`next_retry_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关任务队列表';

-- 初始配置数据
INSERT INTO `xm_customs_chinaport_config` (`config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES
('api_url_test', 'https://swapptest.singlewindow.cn/ceb2grab/grab/realTimeDataUpload', '测试环境API地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('api_url_prod', 'https://customs.chinaport.gov.cn/ceb2grab/grab/realTimeDataUpload', '生产环境API地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('environment', 'test', '当前环境配置（test/prod）', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_url', 'http://localhost:8080/rpc/eport/signature', '签名服务地址', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_timeout', '10', '签名服务超时时间', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('signature_service_token', '', '签名服务认证令牌', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('ebp_code', '', '电商平台代码', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('pay_code', '', '支付企业代码', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('retry_interval', '30', '重试初始间隔（秒）', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('retry_max_times', '5', '最大重试次数', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('client_sign_key', 'default_sign_key_please_change', '客户端签名密钥', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('goods_url_prefix', 'https://example.com/goods', '商品URL前缀', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('recp_account', '', '收款账户', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('recp_name', '', '收款人姓名', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()); 