<?php
namespace addon\customsChinaport\api\controller;

use app\Controller;
use addon\customsChinaport\model\ChinaportTask;
use addon\customsChinaport\service\ChinaportService;
use think\facade\Validate;
use think\facade\Log;

/**
 * 海关接口控制器
 */
class Api extends Controller
{
    /**
     * 构造函数，处理公共逻辑
     */
    public function __construct()
    {
        // 调用父类构造函数
        parent::__construct();
    }

    /**
     * 海关数据获取接口入口
     * 接收海关发起的数据获取请求
     */
    public function platDataOpen()
    {
        // 获取参数
        $input_params = input();
        $headers = request()->header();
        
        // 记录请求信息和请求头到同一条日志记录中
        Log::info('海关获取数据请求: ' . json_encode(['params' => $input_params, 'headers' => $headers]));
        
        // 返回数据结构 - 无论处理结果如何，都返回成功状态
        $return_data = [
            'code' => '10000',
            'message' => '',
            'serviceTime' => time() * 1000 // 毫秒时间戳
        ];
        
        try {
            // 处理请求数据格式
            $params = [];
            
            // 处理openReq参数 - 可能是JSON字符串、数组或对象
            if (isset($input_params['openReq'])) {
                if (is_string($input_params['openReq'])) {
                    $params = json_decode($input_params['openReq'], true);
                } else {
                    $params = $input_params['openReq'];
                }
            } else {
                // 兼容直接发送参数的情况
                $params = $input_params;
            }
            
            // 处理单个订单对象的情况，将其转换为数组格式
            if (isset($params['orderNo']) && isset($params['sessionID'])) {
                $params = [$params];
            }
            
            Log::info('处理后的海关请求数据: ' . json_encode($params));
            
            // 调用服务处理请求，同时传递header信息
            $service = new ChinaportService();
            $result = $service->handlePlatDataOpen($params, $headers);
            
            // 根据处理结果记录日志，但不影响返回给海关的成功状态
            if ($result['code'] != 0) {
                Log::warning('海关获取数据任务创建可能有问题，但仍返回成功状态: ' . json_encode($result));
            } else {
                Log::info('海关获取数据处理成功: ' . json_encode($result['data']));
            }
            
        } catch (\Exception $e) {
            // 记录异常
            Log::error('海关获取数据异常: ' . $e->getMessage() . ', 堆栈: ' . $e->getTraceAsString());
            // 即使发生异常也返回成功状态
        }
        
        // 无论处理结果如何，都返回成功状态
        return json($return_data);
    }
    
    /**
     * 订单数据下发接口
     * 供客户端定期请求获取需要报关的订单数据
     */
    public function getOrderData()
    {
        // 记录请求信息
        Log::info('客户端获取订单数据请求:' . json_encode(input()));
        
        // 获取参数
        $params = input();
        
        // 返回数据结构
        $return_data = [];
        
        try {
            // 验证参数
            $validate = Validate::rule([
                'timestamp' => 'require|integer',
                'sign' => 'require|length:32'
            ]);
            
            if (!$validate->check($params)) {
                Log::error('客户端获取订单数据参数错误:' . json_encode($params));
                return json($return_data);
            }
            
            // 验证签名
            $service = new ChinaportService();
            $valid = $service->verifySign($params['timestamp'], $params['sign']);
            
            if (!$valid) {
                Log::error('客户端获取订单数据签名验证失败');
                return json($return_data);
            }
            
            // 获取待上报的订单数据
            $order_data = $service->getOrderDataForClient();
            
            // 返回订单数据
            return json($order_data);
            
        } catch (\Exception $e) {
            // 记录异常
            Log::error('客户端获取订单数据异常:' . $e->getMessage());
            return json($return_data);
        }
    }
} 