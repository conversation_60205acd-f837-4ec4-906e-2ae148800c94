<?php
namespace addon\customsChinaport\service;

use addon\customsChinaport\model\ChinaportConfig;

/**
 * 签名服务类
 * 用于处理XML签名和验证
 */
class SignatureService
{
    /**
     * 配置模型
     * @var ChinaportConfig
     */
    private $config_model;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->config_model = new ChinaportConfig();
    }
    
    /**
     * 对XML进行签名
     * 
     * @param string $xml_string 原始XML字符串
     * @return string 签名后的XML字符串
     */
    public function signXml($xml_string)
    {
        // 获取证书配置
        $cert_path = $this->config_model->getConfigValueByKey('cert_path');
        $cert_password = $this->config_model->getConfigValueByKey('cert_password');
        
        // 如果没有配置证书，则返回原始XML
        if (empty($cert_path) || !file_exists($cert_path)) {
            return $xml_string;
        }
        
        try {
            // 加载XML文档
            $dom = new \DOMDocument();
            $dom->loadXML($xml_string);
            
            // 获取根节点
            $root = $dom->documentElement;
            
            // 创建签名节点
            $signature_node = $dom->createElement('Signature');
            $root->appendChild($signature_node);
            
            // 计算XML内容的摘要
            $digest = $this->calculateDigest($xml_string);
            
            // 创建摘要节点
            $digest_node = $dom->createElement('DigestValue', $digest);
            $signature_node->appendChild($digest_node);
            
            // 使用证书对摘要进行签名
            $signature_value = $this->signDigest($digest, $cert_path, $cert_password);
            
            // 创建签名值节点
            $signature_value_node = $dom->createElement('SignatureValue', $signature_value);
            $signature_node->appendChild($signature_value_node);
            
            // 添加证书信息
            $cert_info = $this->getCertificateInfo($cert_path);
            $cert_node = $dom->createElement('Certificate');
            $signature_node->appendChild($cert_node);
            
            // 添加证书序列号
            if (!empty($cert_info['serial_number'])) {
                $serial_node = $dom->createElement('SerialNumber', $cert_info['serial_number']);
                $cert_node->appendChild($serial_node);
            }
            
            // 添加证书颁发者
            if (!empty($cert_info['issuer'])) {
                $issuer_node = $dom->createElement('Issuer', $cert_info['issuer']);
                $cert_node->appendChild($issuer_node);
            }
            
            return $dom->saveXML();
        } catch (\Exception $e) {
            // 签名失败，返回原始XML
            return $xml_string;
        }
    }
    
    /**
     * 验证XML签名
     * 
     * @param string $xml_string 签名后的XML字符串
     * @return bool 验证结果
     */
    public function verifySignature($xml_string)
    {
        try {
            // 加载XML文档
            $dom = new \DOMDocument();
            $dom->loadXML($xml_string);
            
            // 获取签名节点
            $signature_nodes = $dom->getElementsByTagName('Signature');
            if ($signature_nodes->length == 0) {
                // 没有签名节点，视为验证通过（兼容未签名的情况）
                return true;
            }
            
            $signature_node = $signature_nodes->item(0);
            
            // 获取摘要值
            $digest_nodes = $signature_node->getElementsByTagName('DigestValue');
            if ($digest_nodes->length == 0) {
                return false;
            }
            $digest = $digest_nodes->item(0)->nodeValue;
            
            // 获取签名值
            $signature_value_nodes = $signature_node->getElementsByTagName('SignatureValue');
            if ($signature_value_nodes->length == 0) {
                return false;
            }
            $signature_value = $signature_value_nodes->item(0)->nodeValue;
            
            // 移除签名节点，计算原始XML的摘要
            $root = $dom->documentElement;
            $root->removeChild($signature_node);
            $original_xml = $dom->saveXML();
            $calculated_digest = $this->calculateDigest($original_xml);
            
            // 比较摘要值
            if ($digest !== $calculated_digest) {
                return false;
            }
            
            // 验证签名
            // 注意：这里简化处理，实际应该使用证书公钥验证签名
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 计算XML内容的摘要
     * 
     * @param string $xml_string XML字符串
     * @return string 摘要值（Base64编码）
     */
    private function calculateDigest($xml_string)
    {
        // 使用SHA-256算法计算摘要
        $digest = hash('sha256', $xml_string, true);
        
        // 返回Base64编码的摘要值
        return base64_encode($digest);
    }
    
    /**
     * 使用证书对摘要进行签名
     * 
     * @param string $digest 摘要值
     * @param string $cert_path 证书路径
     * @param string $cert_password 证书密码
     * @return string 签名值（Base64编码）
     */
    private function signDigest($digest, $cert_path, $cert_password)
    {
        // 简化处理，实际应该使用证书私钥进行签名
        // 这里仅做演示，返回一个模拟的签名值
        
        // 在实际项目中，应该使用如下代码：
        /*
        // 加载证书
        $cert = file_get_contents($cert_path);
        $pkcs12 = openssl_pkcs12_read($cert, $cert_info, $cert_password);
        if (!$pkcs12) {
            throw new \Exception('无法读取证书');
        }
        
        // 获取私钥
        $private_key = $cert_info['pkey'];
        
        // 使用私钥对摘要进行签名
        $signature = '';
        openssl_sign($digest, $signature, $private_key, OPENSSL_ALGO_SHA256);
        
        // 返回Base64编码的签名值
        return base64_encode($signature);
        */
        
        // 模拟签名值
        return base64_encode('SIMULATED_SIGNATURE_' . md5($digest . $cert_path . time()));
    }
    
    /**
     * 获取证书信息
     * 
     * @param string $cert_path 证书路径
     * @return array 证书信息
     */
    private function getCertificateInfo($cert_path)
    {
        // 简化处理，实际应该解析证书获取信息
        // 这里仅做演示，返回模拟的证书信息
        
        // 在实际项目中，应该使用如下代码：
        /*
        // 读取证书
        $cert_data = file_get_contents($cert_path);
        
        // 解析证书
        $cert_info = openssl_x509_parse($cert_data);
        
        return [
            'serial_number' => $cert_info['serialNumber'] ?? '',
            'issuer' => $cert_info['issuer']['CN'] ?? '',
            'valid_from' => $cert_info['validFrom'] ?? '',
            'valid_to' => $cert_info['validTo'] ?? '',
        ];
        */
        
        // 模拟证书信息
        return [
            'serial_number' => 'SIMULATED_SERIAL_' . substr(md5($cert_path), 0, 8),
            'issuer' => 'SIMULATED_CA',
        ];
    }
} 