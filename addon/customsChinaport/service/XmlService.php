<?php
namespace addon\customsChinaport\service;

/**
 * XML服务类
 * 用于生成和解析符合海关要求的XML数据
 */
class XmlService
{
    /**
     * 生成订单上报XML
     * 
     * @param array $order_data 订单数据
     * @return string 生成的XML字符串
     */
    public function generateOrderXml($order_data)
    {
        // 创建XML文档
        $xml = new \DOMDocument('1.0', 'UTF-8');
        
        // 创建根节点
        $root = $xml->createElement('CEB311Message');
        $root->setAttribute('xmlns', 'http://www.chinaport.gov.cn/ceb');
        $root->setAttribute('version', '1.0');
        $xml->appendChild($root);
        
        // 创建订单信息节点
        $order = $xml->createElement('Order');
        $root->appendChild($order);
        
        // 电商平台信息
        $ebp_code = $xml->createElement('ebpCode', $order_data['ebp_code'] ?? '');
        $order->appendChild($ebp_code);
        
        $ebp_name = $xml->createElement('ebpName', $order_data['ebp_name'] ?? '');
        $order->appendChild($ebp_name);
        
        // 订单信息
        $order_no = $xml->createElement('orderNo', $order_data['order_no'] ?? '');
        $order->appendChild($order_no);
        
        // 订单日期时间
        if (!empty($order_data['order_time'])) {
            $order_time = $xml->createElement('orderDate', date('YmdHis', $order_data['order_time']));
            $order->appendChild($order_time);
        }
        
        // 买家信息
        if (!empty($order_data['buyer_name'])) {
            $buyer_name = $xml->createElement('buyerName', $order_data['buyer_name']);
            $order->appendChild($buyer_name);
        }
        
        if (!empty($order_data['buyer_id_number'])) {
            $buyer_id_number = $xml->createElement('buyerIdNumber', $order_data['buyer_id_number']);
            $order->appendChild($buyer_id_number);
        }
        
        if (!empty($order_data['buyer_telephone'])) {
            $buyer_telephone = $xml->createElement('buyerTelephone', $order_data['buyer_telephone']);
            $order->appendChild($buyer_telephone);
        }
        
        // 收货人信息
        if (!empty($order_data['consignee'])) {
            $consignee = $xml->createElement('consignee', $order_data['consignee']);
            $order->appendChild($consignee);
        }
        
        if (!empty($order_data['consignee_address'])) {
            $consignee_address = $xml->createElement('consigneeAddress', $order_data['consignee_address']);
            $order->appendChild($consignee_address);
        }
        
        if (!empty($order_data['consignee_telephone'])) {
            $consignee_telephone = $xml->createElement('consigneeTelephone', $order_data['consignee_telephone']);
            $order->appendChild($consignee_telephone);
        }
        
        // 订单商品列表
        if (!empty($order_data['goods_list'])) {
            foreach ($order_data['goods_list'] as $goods) {
                $goods_info = $xml->createElement('OrderList');
                $order->appendChild($goods_info);
                
                // 商品信息
                if (!empty($goods['goods_id'])) {
                    $goods_id = $xml->createElement('gnum', $goods['goods_id']);
                    $goods_info->appendChild($goods_id);
                }
                
                if (!empty($goods['goods_name'])) {
                    $goods_name = $xml->createElement('gname', $goods['goods_name']);
                    $goods_info->appendChild($goods_name);
                }
                
                if (!empty($goods['goods_quantity'])) {
                    $goods_quantity = $xml->createElement('qty', $goods['goods_quantity']);
                    $goods_info->appendChild($goods_quantity);
                }
                
                if (!empty($goods['goods_unit'])) {
                    $goods_unit = $xml->createElement('unit', $goods['goods_unit']);
                    $goods_info->appendChild($goods_unit);
                }
                
                if (!empty($goods['goods_price'])) {
                    $goods_price = $xml->createElement('price', $goods['goods_price']);
                    $goods_info->appendChild($goods_price);
                }
                
                if (!empty($goods['goods_total'])) {
                    $goods_total = $xml->createElement('total', $goods['goods_total']);
                    $goods_info->appendChild($goods_total);
                }
                
                if (!empty($goods['currency'])) {
                    $currency = $xml->createElement('currency', $goods['currency']);
                    $goods_info->appendChild($currency);
                }
                
                if (!empty($goods['country'])) {
                    $country = $xml->createElement('country', $goods['country']);
                    $goods_info->appendChild($country);
                }
            }
        }
        
        // 生成XML字符串
        $xml->formatOutput = true;
        return $xml->saveXML();
    }
    
    /**
     * 生成支付上报XML
     * 
     * @param array $payment_data 支付数据
     * @return string 生成的XML字符串
     */
    public function generatePaymentXml($payment_data)
    {
        // 创建XML文档
        $xml = new \DOMDocument('1.0', 'UTF-8');
        
        // 创建根节点
        $root = $xml->createElement('CEB411Message');
        $root->setAttribute('xmlns', 'http://www.chinaport.gov.cn/ceb');
        $root->setAttribute('version', '1.0');
        $xml->appendChild($root);
        
        // 创建支付信息节点
        $payment = $xml->createElement('Payment');
        $root->appendChild($payment);
        
        // 支付企业信息
        $pay_code = $xml->createElement('payCode', $payment_data['pay_code'] ?? '');
        $payment->appendChild($pay_code);
        
        $pay_name = $xml->createElement('payName', $payment_data['pay_name'] ?? '');
        $payment->appendChild($pay_name);
        
        // 支付交易信息
        $payment_no = $xml->createElement('payTransactionId', $payment_data['payment_no'] ?? '');
        $payment->appendChild($payment_no);
        
        $order_no = $xml->createElement('orderNo', $payment_data['order_no'] ?? '');
        $payment->appendChild($order_no);
        
        // 支付时间
        if (!empty($payment_data['pay_time'])) {
            $pay_time = $xml->createElement('payTime', date('YmdHis', $payment_data['pay_time']));
            $payment->appendChild($pay_time);
        }
        
        // 支付金额
        if (!empty($payment_data['pay_amount'])) {
            $pay_amount = $xml->createElement('totalAmount', $payment_data['pay_amount']);
            $payment->appendChild($pay_amount);
        }
        
        // 币种
        if (!empty($payment_data['currency'])) {
            $currency = $xml->createElement('currency', $payment_data['currency']);
            $payment->appendChild($currency);
        }
        
        // 支付人信息
        if (!empty($payment_data['payer_id_number'])) {
            $payer_id_number = $xml->createElement('payerIdNumber', $payment_data['payer_id_number']);
            $payment->appendChild($payer_id_number);
        }
        
        if (!empty($payment_data['payer_name'])) {
            $payer_name = $xml->createElement('payerName', $payment_data['payer_name']);
            $payment->appendChild($payer_name);
        }
        
        // 生成XML字符串
        $xml->formatOutput = true;
        return $xml->saveXML();
    }
    
    /**
     * 解析回执XML
     * 
     * @param string $xml_string XML字符串
     * @return array 解析结果
     */
    public function parseReceiptXml($xml_string)
    {
        try {
            $result = [
                'success' => false,
                'receipt_type' => '',
                'receipt_id' => '',
                'customs_code' => '',
                'customs_name' => '',
                'status' => '',
                'status_desc' => '',
                'order_no' => '',
                'payment_no' => '',
                'note' => '',
                'original_xml' => $xml_string
            ];
            
            // 加载XML字符串
            $xml = new \DOMDocument();
            $xml->loadXML($xml_string);
            
            // 判断回执类型
            if ($xml->getElementsByTagName('CEB312Message')->length > 0) {
                // 订单回执
                $result['receipt_type'] = 'order';
                $receipt = $xml->getElementsByTagName('OrderReturn')->item(0);
            } elseif ($xml->getElementsByTagName('CEB412Message')->length > 0) {
                // 支付回执
                $result['receipt_type'] = 'payment';
                $receipt = $xml->getElementsByTagName('PaymentReturn')->item(0);
            } else {
                return $result;
            }
            
            // 解析回执基本信息
            if ($receipt) {
                // 回执ID
                $receipt_id_node = $receipt->getElementsByTagName('returnInfo')->item(0);
                if ($receipt_id_node) {
                    $result['receipt_id'] = $receipt_id_node->nodeValue;
                }
                
                // 海关编码
                $customs_code_node = $receipt->getElementsByTagName('customsCode')->item(0);
                if ($customs_code_node) {
                    $result['customs_code'] = $customs_code_node->nodeValue;
                }
                
                // 海关名称
                $customs_name_node = $receipt->getElementsByTagName('customsName')->item(0);
                if ($customs_name_node) {
                    $result['customs_name'] = $customs_name_node->nodeValue;
                }
                
                // 回执状态
                $status_node = $receipt->getElementsByTagName('returnStatus')->item(0);
                if ($status_node) {
                    $result['status'] = $status_node->nodeValue;
                }
                
                // 回执状态描述
                $status_desc_node = $receipt->getElementsByTagName('returnInfo')->item(0);
                if ($status_desc_node) {
                    $result['status_desc'] = $status_desc_node->nodeValue;
                }
                
                // 回执备注
                $note_node = $receipt->getElementsByTagName('returnNote')->item(0);
                if ($note_node) {
                    $result['note'] = $note_node->nodeValue;
                }
                
                // 订单号
                $order_no_node = $receipt->getElementsByTagName('orderNo')->item(0);
                if ($order_no_node) {
                    $result['order_no'] = $order_no_node->nodeValue;
                }
                
                // 支付单号（仅支付回执有）
                if ($result['receipt_type'] == 'payment') {
                    $payment_no_node = $receipt->getElementsByTagName('payTransactionId')->item(0);
                    if ($payment_no_node) {
                        $result['payment_no'] = $payment_no_node->nodeValue;
                    }
                }
                
                $result['success'] = true;
            }
            
            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_xml' => $xml_string
            ];
        }
    }
    
    /**
     * 验证XML签名
     * 
     * @param string $xml_string XML字符串
     * @return bool 验证结果
     */
    public function verifySignature($xml_string)
    {
        $signature_service = new SignatureService();
        return $signature_service->verifySignature($xml_string);
    }
    
    /**
     * 对XML进行签名
     * 
     * @param string $xml_string XML字符串
     * @return string 签名后的XML
     */
    public function signXml($xml_string)
    {
        $signature_service = new SignatureService();
        return $signature_service->signXml($xml_string);
    }
} 