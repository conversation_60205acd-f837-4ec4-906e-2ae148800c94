<?php
namespace addon\customsChinaport\service;

use addon\customsChinaport\model\ChinaportConfig;
use addon\customsChinaport\model\ChinaportTask;
use app\model\BaseModel; // 添加BaseModel引用

/**
 * 海关服务类
 */
class ChinaportService extends BaseModel // 继承BaseModel类
{
    /**
     * 配置模型
     * @var ChinaportConfig
     */
    private $config_model;
    
    /**
     * XML服务
     * @var XmlService
     */
    private $xml_service;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->config_model = new ChinaportConfig();
        $this->xml_service = new XmlService();
    }
    
    /**
     * 上报订单数据
     * 
     * @param array $order_data 订单数据
     * @return array
     */
    public function reportOrder($order_data)
    {
        try {
            // 记录日志
            $this->addLog(1, $order_data, '', 1);
            
            // 生成订单XML数据
            $xml_data = $this->xml_service->generateOrderXml($order_data);
            
            // 记录日志
            $this->addLog(2, $order_data, $xml_data, 1);
            
            // 发送数据到海关
            $response = $this->sendToCustoms($xml_data);
            
            // 记录日志
            $this->addLog(3, $xml_data, $response, 1);
            
            return ['code' => 0, 'data' => $response, 'message' => '上报成功'];
        } catch (\Exception $e) {
            // 记录错误日志
            $this->addLog(3, $order_data, '', 0, $e->getMessage());
            
            return ['code' => -1, 'message' => '上报订单失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 上报支付数据
     * 
     * @param array $payment_data 支付数据
     * @return array
     */
    public function reportPayment($payment_data)
    {
        try {
            // 记录日志
            $this->addLog(1, $payment_data, '', 1);
            
            // 生成支付XML数据
            $xml_data = $this->xml_service->generatePaymentXml($payment_data);
            
            // 记录日志
            $this->addLog(2, $payment_data, $xml_data, 1);
            
            // 发送数据到海关
            $response = $this->sendToCustoms($xml_data);
            
            // 记录日志
            $this->addLog(4, $xml_data, $response, 1);
            
            return ['code' => 0, 'data' => $response, 'message' => '上报成功'];
        } catch (\Exception $e) {
            // 记录错误日志
            $this->addLog(4, $payment_data, '', 0, $e->getMessage());
            
            return ['code' => -1, 'message' => '上报支付失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 处理回执数据
     * 
     * @param string $receipt_xml 回执XML数据
     * @return array
     */
    public function processReceipt($receipt_xml)
    {
        try {
            // 解析回执XML数据
            $receipt_data = $this->xml_service->parseReceiptXml($receipt_xml);
            
            if (!$receipt_data['success']) {
                throw new \Exception($receipt_data['error'] ?? '解析回执XML失败');
            }
            
            // 添加时间戳
            $receipt_data['receipt_time'] = time();
            
            // 记录回执
            $receipt_id = $this->saveReceipt($receipt_data);
            
            // 记录日志
            $this->addLog(5, $receipt_xml, $receipt_data, 1);
            
            // 处理回执业务逻辑
            $this->handleReceiptBusiness($receipt_data);
            
            // 更新回执处理状态
            model('customs_chinaport_receipt')->update([
                'process_status' => 1,
                'process_message' => '处理成功',
                'process_time' => time(),
                'update_time' => time()
            ], [['id', '=', $receipt_id]]);
            
            return ['code' => 0, 'data' => $receipt_data, 'message' => '处理成功'];
        } catch (\Exception $e) {
            // 记录错误日志
            $this->addLog(5, $receipt_xml, '', 0, $e->getMessage());
            
            return ['code' => -1, 'message' => '处理回执失败：' . $e->getMessage()];
        }
    }
    
    /**
     * 处理回执业务逻辑
     * 
     * @param array $receipt_data 回执数据
     * @return void
     */
    private function handleReceiptBusiness($receipt_data)
    {
        // 获取回执类型和状态
        $receipt_type = $receipt_data['receipt_type'] ?? '';
        $status = $receipt_data['status'] ?? '';
        $order_no = $receipt_data['order_no'] ?? '';
        
        if (empty($receipt_type) || empty($status) || empty($order_no)) {
            return;
        }
        
        // 根据回执类型处理不同业务逻辑
        switch ($receipt_type) {
            case 'order':
                // 处理订单回执
                $this->handleOrderReceipt($receipt_data);
                break;
                
            case 'payment':
                // 处理支付回执
                $this->handlePaymentReceipt($receipt_data);
                break;
                
            default:
                // 未知回执类型，不处理
                break;
        }
    }
    
    /**
     * 处理订单回执
     * 
     * @param array $receipt_data 回执数据
     * @return void
     */
    private function handleOrderReceipt($receipt_data)
    {
        $order_no = $receipt_data['order_no'];
        $status = $receipt_data['status'];
        
        // 查询订单信息
        $order_info = model('order')->getInfo([['order_no', '=', $order_no]]);
        if (empty($order_info)) {
            return;
        }
        
        // 更新订单海关状态
        $update_data = [
            'customs_status' => $status,
            'customs_message' => $receipt_data['status_desc'] ?? '',
            'customs_time' => time(),
            'update_time' => time()
        ];
        
        // 如果海关处理成功，更新订单状态
        if ($status == '2') {
            $update_data['customs_check'] = 1; // 已通过海关审核
        } else {
            $update_data['customs_check'] = 0; // 未通过海关审核
        }
        
        // 更新订单
        model('order')->update($update_data, [['order_no', '=', $order_no]]);
        
        // 记录订单日志
        $this->addOrderLog($order_info['order_id'], '海关回执', '订单海关状态更新：' . ($receipt_data['status_desc'] ?? ''));
    }
    
    /**
     * 处理支付回执
     * 
     * @param array $receipt_data 回执数据
     * @return void
     */
    private function handlePaymentReceipt($receipt_data)
    {
        $order_no = $receipt_data['order_no'];
        $payment_no = $receipt_data['payment_no'];
        $status = $receipt_data['status'];
        
        // 查询订单信息
        $order_info = model('order')->getInfo([['order_no', '=', $order_no]]);
        if (empty($order_info)) {
            return;
        }
        
        // 更新订单支付海关状态
        $update_data = [
            'payment_customs_status' => $status,
            'payment_customs_message' => $receipt_data['status_desc'] ?? '',
            'payment_customs_time' => time(),
            'update_time' => time()
        ];
        
        // 如果海关处理成功，更新订单支付状态
        if ($status == '2') {
            $update_data['payment_customs_check'] = 1; // 已通过海关支付审核
        } else {
            $update_data['payment_customs_check'] = 0; // 未通过海关支付审核
        }
        
        // 更新订单
        model('order')->update($update_data, [['order_no', '=', $order_no]]);
        
        // 记录订单日志
        $this->addOrderLog($order_info['order_id'], '海关支付回执', '订单支付海关状态更新：' . ($receipt_data['status_desc'] ?? ''));
    }
    
    /**
     * 发送数据到海关
     * 
     * @param string $xml_data XML数据
     * @return string
     */
    private function sendToCustoms($xml_data)
    {
        // 获取API地址
        $environment = $this->config_model->getConfigValueByKey('environment', 'test');
        $api_key = $environment == 'prod' ? 'api_url_prod' : 'api_url_test';
        $api_url = $this->config_model->getConfigValueByKey($api_key);
        
        if (empty($api_url)) {
            throw new \Exception('未配置API地址');
        }
        
        // 获取证书信息
        $cert_path = $this->config_model->getConfigValueByKey('cert_path');
        $cert_password = $this->config_model->getConfigValueByKey('cert_password');
        
        // 对XML数据进行签名
        $signed_xml = $this->xml_service->signXml($xml_data);
        
        // 设置请求头
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: ""'
        ];
        
        // 创建CURL请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $signed_xml);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // 如果配置了证书，则使用证书
        if (!empty($cert_path) && file_exists($cert_path)) {
            curl_setopt($ch, CURLOPT_SSLCERT, $cert_path);
            if (!empty($cert_password)) {
                curl_setopt($ch, CURLOPT_SSLCERTPASSWD, $cert_password);
            }
        }
        
        // 执行请求
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // 检查是否有错误
        if ($error) {
            throw new \Exception('CURL请求错误: ' . $error);
        }
        
        // 检查HTTP状态码
        if ($http_code != 200) {
            throw new \Exception('HTTP请求失败，状态码: ' . $http_code);
        }
        
        // 检查响应内容
        if (empty($response)) {
            throw new \Exception('服务器返回空响应');
        }
        
        return $response;
    }
    
    /**
     * 保存回执记录
     * 
     * @param array $receipt_data 回执数据
     * @return int
     */
    private function saveReceipt($receipt_data)
    {
        $time = time();
        
        $data = [
            'receipt_id' => $receipt_data['receipt_id'],
            'receipt_type' => $receipt_data['receipt_type'],
            'original_xml' => $receipt_data['original_xml'],
            'customs_code' => $receipt_data['customs_code'],
            'customs_name' => $receipt_data['customs_name'],
            'status' => $receipt_data['status'],
            'status_desc' => $receipt_data['status_desc'],
            'order_no' => $receipt_data['order_no'],
            'payment_no' => $receipt_data['payment_no'],
            'note' => $receipt_data['note'],
            'receipt_time' => $receipt_data['receipt_time'],
            'process_status' => 0,
            'process_message' => '',
            'process_time' => 0,
            'create_time' => $time,
            'update_time' => $time
        ];
        
        return model('customs_chinaport_receipt')->add($data);
    }
    
    /**
     * 添加日志
     * 
     * @param int $type 日志类型: 1-接收请求 2-生成XML 3-提交订单 4-提交支付 5-回执处理 6-限流重试
     * @param mixed $request_data 请求数据
     * @param mixed $response_data 响应数据
     * @param int $status 状态：0-失败 1-成功
     * @param string $error_msg 错误信息
     * @return int 日志ID
     */
    private function addLog($type, $request_data, $response_data, $status, $error_msg = '')
    {
        $time = time();
        
        // 将数组数据转换为JSON字符串
        $request_json = is_array($request_data) ? json_encode($request_data, JSON_UNESCAPED_UNICODE) : $request_data;
        $response_json = is_array($response_data) ? json_encode($response_data, JSON_UNESCAPED_UNICODE) : $response_data;
        
        $data = [
            'type' => $type,
            'request_data' => $request_json,
            'response_data' => $response_json,
            'status' => $status,
            'error_msg' => $error_msg,
            'create_time' => $time,
            'update_time' => $time
        ];
        
        // 添加日志记录
        $log_id = model('customs_chinaport_log')->add($data);
        
        // 记录到系统日志
        $log_message = "海关接口日志 [类型:{$type}] [状态:{$status}]";
        if ($status == 0 && !empty($error_msg)) {
            \think\facade\Log::error($log_message . " 错误: {$error_msg}");
        } else {
            \think\facade\Log::info($log_message);
        }
        
        return $log_id;
    }
    
    /**
     * 添加订单日志
     * 
     * @param int $order_id 订单ID
     * @param string $action 操作
     * @param string $desc 描述
     * @return int
     */
    private function addOrderLog($order_id, $action, $desc)
    {
        $time = time();
        
        $data = [
            'order_id' => $order_id,
            'action' => $action,
            'uid' => 0,
            'username' => '系统',
            'desc' => $desc,
            'create_time' => $time
        ];
        
        return model('order_log')->add($data);
    }

    
    /**
     * 处理海关数据获取请求
     * 
     * @param array $params 请求参数数组，包含多个订单对象
     * @param array $headers 请求头信息
     * @return array
     */
    public function handlePlatDataOpen($params, $headers = [])
    {
        $log_id = $this->addLog(1, ['params' => $params], [], 1);

        try {
            $task_model = new ChinaportTask();
            $results = [];
            $success_count = 0;
            $failed_count = 0;
            $now = time();

            // 处理多个订单对象
            foreach ($params as $order_data) {
                // 确保订单数据包含必要字段
                if (empty($order_data['orderNo']) || empty($order_data['sessionID'])) {
                    $results[] = $this->error(-1, '订单数据缺少必要字段');
                    $failed_count++;
                    continue;
                }

                // 组装任务数据，字段对齐表结构
                $task_data = [
                    // 移除task_id，由ChinaportTask模型生成
                    'task_type'      => 'order', // 按表结构，order-订单上报，payment-支付上报
                    'business_id'    => $order_data['orderNo'],
                    'data'           => [
                        'order_no'     => $order_data['orderNo'],
                        'session_id'   => $order_data['sessionID'],
                        'service_time' => $order_data['serviceTime'] ?? '',
                        'request_time' => $now
                    ],
                    // 其余字段由createTask内部补全（如status、retry_count等），如需外部指定可补充
                ];

                // 通过模型的createTask方法创建任务
                $result = $task_model->createTask($task_data);
                $results[] = $result;

                if ($result['code'] == 0) {
                    $success_count++;
                } else {
                    $failed_count++;
                }

                // 记录单个订单处理日志
                $log_func = $result['code'] == 0 ? 'info' : 'error';
                $log_msg = $result['code'] == 0
                    ? '海关数据获取请求创建任务成功, 任务ID: ' . $result['data'] . ', 订单号: ' . $order_data['orderNo']
                    : '海关数据获取请求创建任务失败: ' . $result['message'] . ', 订单号: ' . $order_data['orderNo'];
                \think\facade\Log::$log_func($log_msg);
            }

            // 更新总体日志
            $summary = [
                'total' => count($params),
                'success' => $success_count,
                'failed' => $failed_count,
                'results' => $results
            ];

            $log_update = [
                'response_data' => json_encode($summary, JSON_UNESCAPED_UNICODE),
                'update_time'   => $now,
                'status'        => $failed_count == 0 ? 1 : 0,
                'error_msg'     => $failed_count > 0 ? "处理失败: {$failed_count}/{$success_count}" : ''
            ];
            model('customs_chinaport_log')->update($log_update, [['id', '=', $log_id]]);

            // 返回处理结果摘要
            return [
                'code' => $failed_count == 0 ? 0 : -1,
                'message' => $failed_count == 0 ? '全部处理成功' : "部分处理失败: {$failed_count}/{$success_count}",
                'data' => $summary
            ];

        } catch (\Exception $e) {
            $error_message = '处理海关数据获取请求异常: ' . $e->getMessage();
            model('customs_chinaport_log')->update([
                'status'      => 0,
                'error_msg'   => $error_message,
                'update_time' => time()
            ], [['id', '=', $log_id]]);
            \think\facade\Log::error($error_message . ', 堆栈: ' . $e->getTraceAsString());
            return $this->error(-1, $error_message);
        }
    }

    /**
     * 获取客户端需要的订单数据
     * 
     * @param int $limit 获取数量限制
     * @return array
     */
    public function getOrderDataForClient($limit = 15)
    {
        // 获取待处理的海关179数据任务
        $task_model = new ChinaportTask();
        $task_result = $task_model->getTaskList([
            ['task_type', '=', 'customs_179_data'],
            ['status', '=', 0]
        ], 1, $limit);
        
        if ($task_result['code'] != 0 || empty($task_result['data']['list'])) {
            return [];
        }
        
        $result = [];
        foreach ($task_result['data']['list'] as $task) {
            // 确保任务数据是数组格式
            $task_data = is_string($task['data']) ? json_decode($task['data'], true) : $task['data'];
            if (empty($task_data)) {
                \think\facade\Log::warning('任务数据解析失败: ' . json_encode($task));
                continue;
            }
            
            $order_no = $task_data['order_no'];
            
            // 查询订单信息
            $order_info = $this->getOrderDetail($order_no);
            if (empty($order_info)) {
                continue;
            }
            
            // 查询支付信息
            $payment_info = $this->getPaymentInfo($order_no);
            if (empty($payment_info)) {
                continue;
            }
            
            // 组装返回数据
            $item = [
                'initalRequest' => $payment_info['initial_request'] ?? '',
                'initalResponse' => $payment_info['initial_response'] ?? '',
                'payTransactionId' => $payment_info['transaction_id'] ?? '',
                'verDept' => $payment_info['ver_dept'] ?? '3', // 默认其他
                'totalAmount' => $order_info['pay_money'],
                'tradingTime' => date('YmdHis', strtotime($payment_info['pay_time'] ?? date('Y-m-d H:i:s'))),
                'goods' => $this->getOrderGoods($order_no),
                'ebpCode' => $this->config_model->getConfigValueByKey('ebp_code'),
                'payCode' => $payment_info['pay_code'] ?? $this->getPayCode($payment_info['pay_type'] ?? ''),
                'currency' => '142', // 人民币
                'recpAccount' => $this->config_model->getConfigValueByKey('recp_account'),
                'recpName' => $this->config_model->getConfigValueByKey('recp_name'),
                'orderNo' => $order_no,
                'sessionID' => $task_data['session_id']
            ];
            
            $result[] = $item;
        }
        
        return $result;
    }

    /**
     * 验证客户端签名
     * 
     * @param int $timestamp 时间戳
     * @param string $sign 签名
     * @return bool
     */
    public function verifySign($timestamp, $sign)
    {
        // 获取签名密钥
        $sign_key = $this->config_model->getConfigValueByKey('client_sign_key');
        
        // 检查时间戳是否在有效期内（30秒）
        if (abs(time() - $timestamp) > 30) {
            return false;
        }
        
        // 验证签名
        $correct_sign = md5($timestamp . $sign_key);
        
        return strtolower($sign) === strtolower($correct_sign);
    }

    /**
     * 获取订单商品信息
     * 
     * @param string $order_no 订单编号
     * @return array
     */
    private function getOrderGoods($order_no)
    {
        $goods_list = model('order_goods')->getList([['order_no', '=', $order_no]]);
        if (empty($goods_list)) {
            return [];
        }
        
        $result = [];
        foreach ($goods_list as $goods) {
            $item = [
                'gname' => $goods['goods_name'],
                'itemLink' => $this->generateGoodsUrl($goods['goods_id'])
            ];
            $result[] = $item;
        }
        
        return $result;
    }

    /**
     * 生成商品展示链接
     * 
     * @param int $goods_id 商品ID
     * @return string
     */
    private function generateGoodsUrl($goods_id)
    {
        $base_url = $this->config_model->getConfigValueByKey('goods_url_prefix', 'https://example.com');
        return $base_url . '?id=' . $goods_id;
    }

    /**
     * 根据支付类型获取支付企业海关编码
     *
     * @param string $pay_type 支付类型
     * @return string
     */
    private function getPayCode($pay_type)
    {
        $pay_code_map = [
            'wechatpay' => '4403169D3W', // 微信支付
            'alipay' => '31222699S7',    // 支付宝
            // 可以添加更多支付方式对应的海关编码
        ];
        
        return $pay_code_map[$pay_type] ?? '';
    }

    /**
     * 获取订单详情
     *
     * @param string $order_no 订单编号
     * @return array|null
     */
    private function getOrderDetail($order_no)
    {
        // 查询订单信息
        $order_info = model('order')->getInfo([['order_no', '=', $order_no]]);
        return $order_info;
    }

    /**
     * 获取支付信息
     *
     * @param string $order_no 订单编号
     * @return array|null
     */
    private function getPaymentInfo($order_no)
    {
        // 查询支付信息
        $payment_info = model('pay_record')->getInfo([
            ['out_trade_no', '=', $order_no],
            ['pay_status', '=', 2] // 已支付状态
        ]);
        
        return $payment_info;
    }
} 