{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.layui-table-cell {
    max-height: unset;
    height: auto;
    white-space: normal;
    word-break: break-all;
}
.layui-table-view {
    margin: 10px 0;
}
</style>
{/block}

{block name="main"}
<!-- 筛选 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">筛选</h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">任务类型：</label>
                    <div class="layui-input-inline">
                        <select name="task_type" lay-filter="task_type">
                            <option value="">全部</option>
                            <option value="order">订单上报</option>
                            <option value="payment">支付上报</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">任务状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-filter="status">
                            <option value="">全部</option>
                            <option value="0">待处理</option>
                            <option value="1">处理中</option>
                            <option value="2">处理成功</option>
                            <option value="3">处理失败</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">业务ID：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="business_id" placeholder="订单号/支付单号" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">创建时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="start_time" name="start_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">查询</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<table id="task_list" lay-filter="task_list"></table>

<!-- 工具栏 -->
<script type="text/html" id="action_list">
    <div class="ns-table-btn">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看详情</a>
        {{#  if(d.status == 3){ }}
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="retry">重试</a>
        {{#  } }}
    </div>
</script>

<!-- 任务类型 -->
<script type="text/html" id="task_type">
    {{#  if(d.task_type == 'order'){ }}
    <span>订单上报</span>
    {{#  } else if(d.task_type == 'payment'){ }}
    <span>支付上报</span>
    {{#  } else { }}
    <span>{{d.task_type}}</span>
    {{#  } }}
</script>

<!-- 任务状态 -->
<script type="text/html" id="status">
    {{#  if(d.status == 0){ }}
    <span class="layui-badge layui-bg-blue">待处理</span>
    {{#  } else if(d.status == 1){ }}
    <span class="layui-badge layui-bg-orange">处理中</span>
    {{#  } else if(d.status == 2){ }}
    <span class="layui-badge layui-bg-green">处理成功</span>
    {{#  } else if(d.status == 3){ }}
    <span class="layui-badge">处理失败</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-gray">未知</span>
    {{#  } }}
</script>

<!-- 详情弹窗 -->
<script type="text/html" id="taskDetail">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">任务ID：</label>
            <div class="layui-input-block">{{d.task_id}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">任务类型：</label>
            <div class="layui-input-block">{{d.task_type_text}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">业务ID：</label>
            <div class="layui-input-block">{{d.business_id}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态：</label>
            <div class="layui-input-block">{{d.status_text}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">重试次数：</label>
            <div class="layui-input-block">{{d.retry_count}}/{{d.max_retry}}</div>
        </div>
        {{# if(d.status == 3 && d.next_retry_time > 0){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">下次重试时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.next_retry_time)}}</div>
        </div>
        {{# } }}
        {{# if(d.last_error){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">错误信息：</label>
            <div class="layui-input-block">{{d.last_error}}</div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">创建时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.create_time)}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">更新时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.update_time)}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">任务数据：</label>
            <div class="layui-input-block">
                <textarea class="layui-textarea" readonly>{{d.data}}</textarea>
            </div>
        </div>
    </div>
</script>
{/block}

{block name="script"}
<script>
layui.use(['form', 'laydate', 'table', 'laytpl'], function() {
    var form = layui.form,
        table = layui.table,
        laydate = layui.laydate,
        laytpl = layui.laytpl,
        repeat_flag = false; //防重复标识
    
    // 日期时间选择器
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    
    // 表格渲染
    table.render({
        elem: '#task_list',
        url: ns.url("customsChinaport://admin/chinaport/task"),
        page: true,
        limit: 10,
        limits: [10, 20, 50, 100],
        parseData: function(res) {
            return {
                "code": 0,
                "msg": "",
                "count": res.count,
                "data": res.list
            };
        },
        cols: [[
            {field: 'task_id', title: '任务ID', width: '15%'},
            {field: 'task_type', title: '任务类型', width: '8%', unresize: true, templet: '#task_type'},
            {field: 'business_id', title: '业务ID', width: '12%'},
            {field: 'status', title: '状态', width: '8%', unresize: true, templet: '#status'},
            {field: 'retry_count', title: '重试', width: '6%', templet: function(d) { return d.retry_count + '/' + d.max_retry; }},
            {field: 'last_error', title: '错误信息', width: '15%'},
            {
                field: 'create_time', 
                title: '创建时间', 
                width: '12%',
                templet: function(d) {
                    return d.create_time ? ns.time_to_date(d.create_time) : '';
                }
            },
            {title: '操作', width: '15%', toolbar: '#action_list'}
        ]]
    });
    
    // 搜索
    form.on('submit(search)', function(data) {
        table.reload('task_list', {
            page: {
                curr: 1
            },
            where: data.field
        });
        return false;
    });
    
    // 监听工具条
    table.on('tool(task_list)', function(obj) {
        var data = obj.data;
        
        if (obj.event === 'view') {
            // 格式化数据
            data.task_type_text = data.task_type == 'order' ? '订单上报' : (data.task_type == 'payment' ? '支付上报' : data.task_type);
            
            switch (data.status) {
                case 0: data.status_text = '待处理'; break;
                case 1: data.status_text = '处理中'; break;
                case 2: data.status_text = '处理成功'; break;
                case 3: data.status_text = '处理失败'; break;
                default: data.status_text = '未知';
            }
            
            laytpl(document.getElementById('taskDetail').innerHTML).render(data, function(html) {
                layer.open({
                    type: 1,
                    title: '任务详情',
                    area: ['800px', '600px'], // 使用百分比高度
                    content: html,
                    scrollbar: true, // 允许滚动条
                    resize: false // 禁止调整大小
                });
            });
        } else if (obj.event === 'retry') {
            layer.confirm('确定要重试该任务吗？', function(index) {
                if (repeat_flag) return false;
                repeat_flag = true;
                
                $.ajax({
                    url: ns.url("customsChinaport://admin/chinaport/retryTask"),
                    data: {
                        task_id: data.task_id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        repeat_flag = false;
                        
                        if (res.code == 0) {
                            layer.msg("重试任务已成功添加", {
                                icon: 1,
                                time: 2000,
                                shade: [0.4, '#000']
                            }, function() {
                                table.reload('task_list');
                            });
                        } else {
                            layer.msg(res.message, {
                                icon: 5,
                                time: 2000,
                                shade: [0.4, '#000']
                            });
                        }
                        layer.close(index);
                    }
                });
            });
        }
    });
});
</script>
{/block} 