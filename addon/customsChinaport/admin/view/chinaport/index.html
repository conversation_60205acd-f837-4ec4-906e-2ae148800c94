{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.card-body {
    padding: 20px;
}
.stats-card {
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}
.stats-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}
.stats-card .card-body {
    padding: 20px;
}
.stats-card .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}
.stats-card .stats-title {
    font-size: 14px;
    color: #777;
    margin-top: 5px;
}
</style>
{/block}

{block name="main"}
<div class="layui-card ns-card">
    <div class="layui-card-body">
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="card-header">
                        <span>日志总数</span>
                    </div>
                    <div class="card-body">
                        <div class="stats-value">{$log_count}</div>
                        <div class="stats-title">日志记录总数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="card-header">
                        <span>成功记录</span>
                    </div>
                    <div class="card-body">
                        <div class="stats-value">{$success_count}</div>
                        <div class="stats-title">成功处理记录数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="card-header">
                        <span>回执记录</span>
                    </div>
                    <div class="card-body">
                        <div class="stats-value">{$receipt_count}</div>
                        <div class="stats-title">回执处理记录数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="card-header">
                        <span>任务数</span>
                    </div>
                    <div class="card-body">
                        <div class="stats-value">{$task_count}</div>
                        <div class="stats-title">任务处理总数</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md12">
                <div class="stats-card">
                    <div class="card-header">
                        <span>使用说明</span>
                    </div>
                    <div class="card-body">
                        <p>本插件用于与海关跨境电商进口统一版信息化系统平台进行数据实时对接，支持以下功能：</p>
                        <ul>
                            <li>跨境订单实时上报</li>
                            <li>支付信息实时上报</li>
                            <li>海关回执数据处理</li>
                            <li>支持重试机制和任务管理</li>
                        </ul>
                        <p>使用前请在【配置管理】中设置相关参数，确保系统能够正常连接到海关服务。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block} 