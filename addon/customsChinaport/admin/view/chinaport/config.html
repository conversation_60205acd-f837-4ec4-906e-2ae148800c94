{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.form-wrap {
    margin-top: 10px;
}
.layui-tab-content {
    padding: 20px 0;
}
.layui-btn-container {
    margin-top: 30px;
}
.layui-form-item .layui-input-block {
    width: 600px;
}
.required::before {
    content: '*';
    color: red;
    margin-right: 5px;
}
</style>
{/block}

{block name="main"}
<div class="layui-form ns-form">
    <div class="form-wrap">
        <div class="layui-tab layui-tab-brief" lay-filter="config_tab">
            <ul class="layui-tab-title">
                <li class="layui-this">基本配置</li>
                <li>API配置</li>
                <li>企业信息</li>
                <li>任务配置</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">当前环境：</label>
                        <div class="layui-input-block">
                            <input type="radio" name="environment" value="test" title="测试环境" lay-filter="environment" checked>
                            <input type="radio" name="environment" value="prod" title="生产环境" lay-filter="environment">
                        </div>
                        <div class="ns-word-aux">选择当前使用的环境，测试环境和生产环境的接口地址不同</div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">测试环境API地址：</label>
                        <div class="layui-input-block">
                            <input type="text" name="api_url_test" lay-verify="required" placeholder="请输入测试环境API地址" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">海关跨境电商进口统一版信息化系统平台测试环境接口地址</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">生产环境API地址：</label>
                        <div class="layui-input-block">
                            <input type="text" name="api_url_prod" lay-verify="required" placeholder="请输入生产环境API地址" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">海关跨境电商进口统一版信息化系统平台生产环境接口地址</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">签名服务地址：</label>
                        <div class="layui-input-block">
                            <input type="text" name="signature_service_url" placeholder="请输入签名服务地址" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">海关数据签名服务地址，用于对上报数据进行电子签名</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">签名服务超时时间：</label>
                        <div class="layui-input-block">
                            <input type="text" name="signature_service_timeout" placeholder="请输入签名服务超时时间（秒）" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">调用签名服务的超时时间，单位为秒</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">签名服务令牌：</label>
                        <div class="layui-input-block">
                            <input type="text" name="signature_service_token" placeholder="请输入签名服务令牌" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">调用签名服务的认证令牌</div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">电商平台代码：</label>
                        <div class="layui-input-block">
                            <input type="text" name="ebp_code" lay-verify="required" placeholder="请输入电商平台代码" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">海关颁发的电商平台代码，用于标识电商平台</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">支付企业代码：</label>
                        <div class="layui-input-block">
                            <input type="text" name="pay_code" lay-verify="required" placeholder="请输入支付企业代码" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">海关颁发的支付企业代码，用于标识支付企业</div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label">重试间隔（秒）：</label>
                        <div class="layui-input-block">
                            <input type="text" name="retry_interval" placeholder="请输入重试初始间隔（秒）" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">任务重试的初始间隔时间，单位为秒，默认为30秒</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">最大重试次数：</label>
                        <div class="layui-input-block">
                            <input type="text" name="retry_max_times" placeholder="请输入最大重试次数" class="layui-input ns-len-long">
                        </div>
                        <div class="ns-word-aux">任务失败后最大重试次数，默认为5次</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="save">保存</button>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
layui.use(['form', 'element'], function() {
    var form = layui.form,
        element = layui.element,
        repeat_flag = false; //防重复标识
    
    form.render();
    
    // 加载配置
    loadConfig();
    
    /**
     * 加载配置
     */
    function loadConfig() {
        $.ajax({
            url: ns.url("customsChinaport://admin/chinaport/config"),
            dataType: 'JSON',
            type: 'GET',
            success: function(res) {
                if (res.code == 0 && res.data) {
                    var data = res.data;
                    for (var key in data) {
                        if (key === 'environment') {
                            $("input[name='environment'][value='" + data[key] + "']").prop('checked', true);
                        } else {
                            $("input[name='" + key + "']").val(data[key]);
                        }
                    }
                    form.render();
                }
            }
        });
    }
    
    /**
     * 保存配置
     */
    form.on('submit(save)', function(data) {
        if (repeat_flag) return false;
        repeat_flag = true;
        
        $.ajax({
            url: ns.url("customsChinaport://admin/chinaport/config"),
            data: data.field,
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                repeat_flag = false;
                
                if (res.code == 0) {
                    layer.msg("保存成功", {
                        icon: 1,
                        time: 2000,
                        shade: [0.4, '#000']
                    });
                } else {
                    layer.msg(res.message, {
                        icon: 5,
                        time: 2000,
                        shade: [0.4, '#000']
                    });
                }
            }
        });
        
        return false;
    });
});
</script>
{/block} 