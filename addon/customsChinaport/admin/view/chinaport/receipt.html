{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.layui-table-cell {
    max-height: unset;
    height: auto;
    white-space: normal;
    word-break: break-all;
}
.layui-table-view {
    margin: 10px 0;
}
</style>
{/block}

{block name="main"}
<!-- 筛选 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">筛选</h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">回执类型：</label>
                    <div class="layui-input-inline">
                        <select name="receipt_type" lay-filter="receipt_type">
                            <option value="">全部</option>
                            <option value="order">订单</option>
                            <option value="payment">支付</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">处理状态：</label>
                    <div class="layui-input-inline">
                        <select name="process_status" lay-filter="process_status">
                            <option value="">全部</option>
                            <option value="0">未处理</option>
                            <option value="1">已处理</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">订单号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="order_no" placeholder="请输入订单号" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">回执时间：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="start_time" name="start_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">查询</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<table id="receipt_list" lay-filter="receipt_list"></table>

<!-- 工具栏 -->
<script type="text/html" id="action_list">
    <div class="ns-table-btn">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看详情</a>
    </div>
</script>

<!-- 回执类型 -->
<script type="text/html" id="receipt_type">
    {{#  if(d.receipt_type == 'order'){ }}
    <span>订单</span>
    {{#  } else if(d.receipt_type == 'payment'){ }}
    <span>支付</span>
    {{#  } else { }}
    <span>{{d.receipt_type}}</span>
    {{#  } }}
</script>

<!-- 处理状态 -->
<script type="text/html" id="process_status">
    {{#  if(d.process_status == 1){ }}
    <span class="layui-badge layui-bg-green">已处理</span>
    {{#  } else { }}
    <span class="layui-badge">未处理</span>
    {{#  } }}
</script>

<!-- 详情弹窗 -->
<script type="text/html" id="receiptDetail">
    <div class="layui-form" style="padding: 10px;">
        <div class="layui-form-item">
            <label class="layui-form-label">回执ID：</label>
            <div class="layui-input-block">{{d.receipt_id}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回执类型：</label>
            <div class="layui-input-block">{{d.receipt_type_text}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">海关编码：</label>
            <div class="layui-input-block">{{d.customs_code}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">海关名称：</label>
            <div class="layui-input-block">{{d.customs_name}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回执状态：</label>
            <div class="layui-input-block">{{d.status}} - {{d.status_desc}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">订单号：</label>
            <div class="layui-input-block">{{d.order_no}}</div>
        </div>
        {{# if(d.payment_no){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">支付单号：</label>
            <div class="layui-input-block">{{d.payment_no}}</div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">回执信息：</label>
            <div class="layui-input-block">{{d.note}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">回执时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.receipt_time)}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">处理状态：</label>
            <div class="layui-input-block">{{d.process_status_text}}</div>
        </div>
        {{# if(d.process_message){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">处理消息：</label>
            <div class="layui-input-block">{{d.process_message}}</div>
        </div>
        {{# } }}
        {{# if(d.process_time > 0){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">处理时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.process_time)}}</div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">原始XML：</label>
            <div class="layui-input-block">
                <textarea class="layui-textarea" readonly>{{d.original_xml}}</textarea>
            </div>
        </div>
    </div>
</script>
{/block}

{block name="script"}
<script>
layui.use(['form', 'laydate', 'table', 'laytpl'], function() {
    var form = layui.form,
        table = layui.table,
        laydate = layui.laydate,
        laytpl = layui.laytpl;
    
    // 日期时间选择器
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    
    // 表格渲染
    table.render({
        elem: '#receipt_list',
        url: ns.url("customsChinaport://admin/chinaport/receipt"),
        page: true,
        limit: 10,
        limits: [10, 20, 50, 100],
        parseData: function(res) {
            return {
                "code": 0,
                "msg": "",
                "count": res.count,
                "data": res.list
            };
        },
        cols: [[
            {field: 'receipt_id', title: '回执ID', width: '15%'},
            {field: 'receipt_type', title: '回执类型', width: '8%', unresize: true, templet: '#receipt_type'},
            {field: 'status_desc', title: '回执状态', width: '12%'},
            {field: 'order_no', title: '订单号', width: '15%'},
            {field: 'process_status', title: '处理状态', width: '8%', unresize: true, templet: '#process_status'},
            {
                field: 'receipt_time', 
                title: '回执时间', 
                width: '15%',
                templet: function(d) {
                    return d.receipt_time ? ns.time_to_date(d.receipt_time) : '';
                }
            },
            {title: '操作', width: '10%', toolbar: '#action_list'}
        ]]
    });
    
    // 搜索
    form.on('submit(search)', function(data) {
        table.reload('receipt_list', {
            page: {
                curr: 1
            },
            where: data.field
        });
        return false;
    });
    
    // 监听工具条
    table.on('tool(receipt_list)', function(obj) {
        var data = obj.data;
        
        if (obj.event === 'view') {
            // 格式化数据
            data.receipt_type_text = data.receipt_type == 'order' ? '订单' : (data.receipt_type == 'payment' ? '支付' : data.receipt_type);
            data.process_status_text = data.process_status == 1 ? '已处理' : '未处理';
            
            laytpl(document.getElementById('receiptDetail').innerHTML).render(data, function(html) {
                layer.open({
                    type: 1,
                    title: '回执详情',
                    area: ['800px', '600px'],
                    content: html
                });
            });
        }
    });
});
</script>
{/block} 