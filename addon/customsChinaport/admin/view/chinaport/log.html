{extend name="app/admin/view/base.html"/}
{block name="resources"}
<style>
.layui-table-cell {
    max-height: unset;
    height: auto;
    white-space: normal;
    word-break: break-all;
}
.layui-table-view {
    margin: 10px 0;
}
</style>
{/block}

{block name="main"}
<!-- 筛选 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">筛选</h2>
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">日志类型：</label>
                    <div class="layui-input-inline">
                        <select name="type" lay-filter="type">
                            <option value="">全部</option>
                            <option value="1">接收请求</option>
                            <option value="2">生成XML</option>
                            <option value="3">提交订单</option>
                            <option value="4">提交支付</option>
                            <option value="5">回执处理</option>
                            <option value="6">限流重试</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-filter="status">
                            <option value="">全部</option>
                            <option value="1">成功</option>
                            <option value="0">失败</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">时间范围：</label>
                    <div class="layui-input-inline">
                        <input type="text" id="start_time" name="start_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" id="end_time" name="end_time" class="layui-input" autocomplete="off" readonly>
                    </div>
                </div>
                <div class="ns-form-row">
                    <button class="layui-btn ns-bg-color" lay-submit lay-filter="search">查询</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 列表 -->
<table id="log_list" lay-filter="log_list"></table>

<!-- 工具栏 -->
<script type="text/html" id="action_list">
    <div class="ns-table-btn">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看详情</a>
    </div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
    {{#  if(d.status == 1){ }}
    <span class="layui-badge layui-bg-green" style="white-space: nowrap;">成功</span>
    {{#  } else { }}
    <span class="layui-badge" style="white-space: nowrap;">失败</span>
    {{#  } }}
</script>

<!-- 类型 -->
<script type="text/html" id="type">
    {{#  if(d.type == 1){ }}
    <span style="white-space: nowrap;">接收请求</span>
    {{#  } else if(d.type == 2){ }}
    <span style="white-space: nowrap;">生成XML</span>
    {{#  } else if(d.type == 3){ }}
    <span style="white-space: nowrap;">提交订单</span>
    {{#  } else if(d.type == 4){ }}
    <span style="white-space: nowrap;">提交支付</span>
    {{#  } else if(d.type == 5){ }}
    <span style="white-space: nowrap;">回执处理</span>
    {{#  } else if(d.type == 6){ }}
    <span style="white-space: nowrap;">限流重试</span>
    {{#  } else { }}
    <span style="white-space: nowrap;">未知</span>
    {{#  } }}
</script>

<!-- 详情弹窗 -->
<script type="text/html" id="logDetail">
    <div class="layui-form" style="padding: 10px;">
        <div class="layui-form-item">
            <label class="layui-form-label">日志类型：</label>
            <div class="layui-input-block">{{d.type_text}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态：</label>
            <div class="layui-input-block">{{d.status_text}}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">创建时间：</label>
            <div class="layui-input-block">{{ns.time_to_date(d.create_time)}}</div>
        </div>
        {{# if(d.error_msg){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">错误信息：</label>
            <div class="layui-input-block">{{d.error_msg}}</div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">请求数据：</label>
            <div class="layui-input-block">
                <pre class="layui-code" style="height:220px; overflow: auto;" lay-title="请求数据" lay-skin="notepad">{{d.formatted_request || d.request_data}}</pre>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">响应数据：</label>
            <div class="layui-input-block">
                <pre class="layui-code" style="height:220px; overflow: auto;" lay-title="响应数据" lay-skin="notepad">{{d.formatted_response || d.response_data || ''}}</pre>
            </div>
        </div>
    </div>
</script>
{/block}

{block name="script"}
<script>
layui.use(['form', 'laydate', 'table', 'laytpl', 'code'], function() {
    var form = layui.form,
        table = layui.table,
        laydate = layui.laydate,
        laytpl = layui.laytpl;
    
    // 初始化代码高亮
    layui.code({
        elem: 'pre.layui-code',
        about: false
    });
    
    // 日期时间选择器
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    
    // 表格渲染
    table.render({
        elem: '#log_list',
        url: ns.url("customsChinaport://admin/chinaport/log"),
        page: true,
        limit: 10,
        limits: [10, 20, 50, 100],
        parseData: function(res) {
            return {
                "code": 0,
                "msg": "",
                "count": res.count,
                "data": res.list
            };
        },
        cols: [[
            {field: 'id', title: 'ID', width: '5%'},
            {field: 'type', title: '日志类型', width: '10%', unresize: true, templet: '#type'},
            {field: 'status', title: '状态', width: '8%', unresize: true, templet: '#status'},
            {field: 'params', title: '请求参数', width: '27%', templet: function(d){
                try {
                    var data = JSON.parse(d.request_data);
                    if(data.params) {
                        return JSON.stringify(data.params);
                    }
                    return '';
                } catch(e) {
                    return '';
                }
            }},
            {field: 'error_msg', title: '错误信息', minWidth: 100, templet: function(d){
                return d.error_msg || '';
            }},
            {
                field: 'create_time', 
                title: '创建时间', 
                width: '15%',
                templet: function(d) {
                    return d.create_time ? ns.time_to_date(d.create_time) : '';
                }
            },
            {title: '操作', width: '10%', toolbar: '#action_list'}
        ]]
    });
    
    // 搜索
    form.on('submit(search)', function(data) {
        table.reload('log_list', {
            page: {
                curr: 1
            },
            where: data.field
        });
        return false;
    });
    
    // 监听工具条
    table.on('tool(log_list)', function(obj) {
        var data = obj.data;
        
        if (obj.event === 'view') {
            // 设置类型文本
            var typeText = '';
            switch (data.type) {
                case 1: typeText = '接收请求'; break;
                case 2: typeText = '生成XML'; break;
                case 3: typeText = '提交订单'; break;
                case 4: typeText = '提交支付'; break;
                case 5: typeText = '回执处理'; break;
                case 6: typeText = '限流重试'; break;
                default: typeText = '未知';
            }
            data.type_text = typeText;
            
            // 设置状态文本
            data.status_text = data.status == 1 ? '成功' : '失败';
            
            // 安全格式化JSON
            try {
                if (data.request_data) {
                    var requestObj = JSON.parse(data.request_data);
                    data.formatted_request = JSON.stringify(requestObj, null, 2);
                }
            } catch (e) {
                data.formatted_request = data.request_data;
                console.log('请求数据JSON解析失败:', e);
            }
            
            try {
                if (data.response_data && data.response_data !== '[]') {
                    var responseObj = JSON.parse(data.response_data);
                    data.formatted_response = JSON.stringify(responseObj, null, 2);
                } else {
                    data.formatted_response = data.response_data || '';
                }
            } catch (e) {
                data.formatted_response = data.response_data || '';
                console.log('响应数据JSON解析失败:', e);
            }
            
            laytpl(document.getElementById('logDetail').innerHTML).render(data, function(html) {
                layer.open({
                    type: 1,
                    title: '日志详情',
                    area: ['900px', '80%'], // 调整宽度和高度，使用百分比高度
                    maxmin: true, // 允许最大化
                    content: html,
                    success: function() {
                        // 重新渲染代码高亮
                        layui.code({
                            elem: 'pre.layui-code',
                            about: false
                        });
                    }
                });
            });
        }
    });
});
</script>
{/block} 