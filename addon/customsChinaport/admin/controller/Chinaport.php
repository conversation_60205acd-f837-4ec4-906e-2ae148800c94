<?php
namespace addon\customsChinaport\admin\controller;

use app\admin\controller\BaseAdmin;
use addon\customsChinaport\model\ChinaportConfig;
use addon\customsChinaport\model\ChinaportTask;

/**
 * 海关数据对接管理控制器
 */
class Chinaport extends BaseAdmin
{
    /**
     * 概览
     */
    public function index()
    {
        // 获取各项数据统计
        $log_count = model('customs_chinaport_log')->getCount([]);
        $success_count = model('customs_chinaport_log')->getCount([['status', '=', 1]]);
        $receipt_count = model('customs_chinaport_receipt')->getCount([]);
        $task_count = model('customs_chinaport_task')->getCount([]);
        
        $this->assign('log_count', $log_count);
        $this->assign('success_count', $success_count);
        $this->assign('receipt_count', $receipt_count);
        $this->assign('task_count', $task_count);
        
        return $this->fetch('chinaport/index');
    }
    
    /**
     * 配置管理
     */
    public function config()
    {
        if (request()->isAjax()) {
            if (request()->isPost()) {
                $data = input('post.', []);
                
                $config_model = new ChinaportConfig();
                $result = $config_model->setConfig($data);
                
                return $result;
            } else {
                $config_model = new ChinaportConfig();
                $result = $config_model->getConfig();
                
                return $result;
            }
        } else {
            return $this->fetch('chinaport/config');
        }
    }
    
    /**
     * 日志查询
     */
    public function log()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $type = input('type', '');
            $status = input('status', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $condition = [];
            
            if ($type !== '') {
                $condition[] = ['type', '=', $type];
            }
            
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            if ($start_time && $end_time) {
                $condition[] = ['create_time', 'between', [strtotime($start_time), strtotime($end_time)]];
            }
            
            $field = 'id, type, request_data, response_data, status, error_msg, create_time';
            $order = 'create_time desc';
            
            $list = model('customs_chinaport_log')->pageList($condition, $field, $order, $page, $page_size);
            return $list;
        } else {
            return $this->fetch('chinaport/log');
        }
    }
    
    /**
     * 回执记录
     */
    public function receipt()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $receipt_type = input('receipt_type', '');
            $status = input('status', '');
            $process_status = input('process_status', '');
            $order_no = input('order_no', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $condition = [];
            
            if ($receipt_type) {
                $condition[] = ['receipt_type', '=', $receipt_type];
            }
            
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            if ($process_status !== '') {
                $condition[] = ['process_status', '=', $process_status];
            }
            
            if ($order_no) {
                $condition[] = ['order_no', 'like', '%' . $order_no . '%'];
            }
            
            if ($start_time && $end_time) {
                $condition[] = ['receipt_time', 'between', [strtotime($start_time), strtotime($end_time)]];
            }
            
            $field = 'id, receipt_id, receipt_type, customs_code, customs_name, status, status_desc, order_no, payment_no, note, receipt_time, process_status, process_message, process_time, create_time';
            $order = 'receipt_time desc';
            
            $list = model('customs_chinaport_receipt')->pageList($condition, $field, $order, $page, $page_size);
            return $list;
        } else {
            return $this->fetch('chinaport/receipt');
        }
    }
    
    /**
     * 任务管理
     */
    public function task()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $task_type = input('task_type', '');
            $status = input('status', '');
            $business_id = input('business_id', '');
            $start_time = input('start_time', '');
            $end_time = input('end_time', '');
            
            $condition = [];
            
            if ($task_type) {
                $condition[] = ['task_type', '=', $task_type];
            }
            
            if ($status !== '') {
                $condition[] = ['status', '=', $status];
            }
            
            if ($business_id) {
                $condition[] = ['business_id', 'like', '%' . $business_id . '%'];
            }
            
            if ($start_time && $end_time) {
                $condition[] = ['create_time', 'between', [strtotime($start_time), strtotime($end_time)]];
            }
            
            $field = 'id, task_id, task_type, business_id, data, status, retry_count, max_retry, next_retry_time, last_error, create_time, update_time';
            $order = 'create_time desc';
            
            $list = model('customs_chinaport_task')->pageList($condition, $field, $order, $page, $page_size);
            return $list;
        } else {
            return $this->fetch('chinaport/task');
        }
    }
    
    /**
     * 重试任务
     */
    public function retryTask()
    {
        if (request()->isAjax()) {
            $task_id = input('task_id', '');
            
            if (empty($task_id)) {
                return $this->error('', '参数错误');
            }
            
            $task_info = model('customs_chinaport_task')->getInfo([['task_id', '=', $task_id]]);
            if (empty($task_info)) {
                return $this->error('', '任务不存在');
            }
            
            $task_model = new ChinaportTask();
            $result = $task_model->updateTaskStatus($task_id, 0);
            
            return $result;
        }
    }
} 