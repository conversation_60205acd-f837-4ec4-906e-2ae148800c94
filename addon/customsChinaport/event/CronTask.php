<?php
namespace addon\customsChinaport\event;

/**
 * 定时任务注册事件
 */
class CronTask
{
    /**
     * 执行事件
     */
    public function handle()
    {
        return [
            // 每5分钟执行一次任务处理
            'chinaport_task' => [
                'title' => '海关数据上报任务处理',
                'type' => 1, // 1-系统任务
                'period_type' => 'minute', // 按分钟执行
                'period_value' => '5', // 每5分钟执行一次
                'command' => 'chinaport:task', // 对应命令
                'status' => 1, // 开启状态
                'remark' => '处理海关数据上报任务，包括订单和支付数据上报'
            ]
        ];
    }
} 