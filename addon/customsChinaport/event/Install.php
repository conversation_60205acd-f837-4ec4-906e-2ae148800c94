<?php
namespace addon\customsChinaport\event;

/**
 * 海关跨境电商数据对接插件安装事件
 */
class Install
{
    /**
     * 执行安装
     */
    public function handle()
    {
        try {
            // 执行安装SQL
            execute_sql('addon/customsChinaport/data/install.sql');
            return success();
        } catch (\Exception $e) {
            return error(-1, '安装海关跨境电商数据对接插件失败：' . $e->getMessage());
        }
    }
} 