<?php
namespace addon\customsChinaport\event;

use addon\customsChinaport\model\ChinaportTask;

/**
 * 订单支付成功事件处理
 */
class OrderPaySuccess
{
    /**
     * 订单支付成功后，创建订单上报任务
     */
    public function handle($params)
    {
        // 判断订单是否为跨境订单，非跨境订单不处理
        if (empty($params['order_id']) || empty($params['out_trade_no'])) {
            return success();
        }

        try {
            // 获取订单详情，判断是否需要报关
            $order_model = new \app\model\order\Order();
            $order_info = $order_model->getOrderInfo([['order_id', '=', $params['order_id']]], 'order_id,order_no,pay_time');
            $order_info = $order_info['data'];

            if (empty($order_info)) {
                return error(-1, '订单不存在');
            }

            // 判断是否为跨境订单（此处仅为示例，实际业务需根据自身订单系统判断）
            // 此处应该判断订单是否为跨境订单，如包含海外商品等
            $is_cross_border = true; // 实际业务中需要根据订单信息判断
            
            if (!$is_cross_border) {
                return success();
            }

            // 创建订单上报任务
            $task_model = new ChinaportTask();
            $task_data = [
                'task_type' => 'order',
                'business_id' => $order_info['order_no'],
                'data' => json_encode([
                    'order_id' => $params['order_id'],
                    'order_no' => $order_info['order_no'],
                    'pay_time' => $order_info['pay_time'],
                    'out_trade_no' => $params['out_trade_no']
                ])
            ];
            
            $result = $task_model->createTask($task_data);
            
            if ($result['code'] < 0) {
                return error($result['code'], $result['message']);
            }
            
            return success();
        } catch (\Exception $e) {
            return error(-1, '创建订单上报任务失败：' . $e->getMessage());
        }
    }
} 