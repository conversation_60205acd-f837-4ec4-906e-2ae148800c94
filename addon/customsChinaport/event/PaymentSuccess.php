<?php
namespace addon\customsChinaport\event;

use addon\customsChinaport\model\ChinaportTask;

/**
 * 支付成功事件处理
 */
class PaymentSuccess
{
    /**
     * 支付成功后，创建支付上报任务
     */
    public function handle($params)
    {
        // 判断是否有支付数据
        if (empty($params['out_trade_no']) || empty($params['pay_time'])) {
            return success();
        }

        try {
            // 支付单号
            $out_trade_no = $params['out_trade_no'];
            // 支付时间
            $pay_time = $params['pay_time'];
            
            // 获取关联订单信息，判断是否需要报关
            $order_model = new \app\model\order\Order();
            $order_info = $order_model->getOrderInfo([['out_trade_no', '=', $out_trade_no]], 'order_id,order_no');
            
            if (empty($order_info) || empty($order_info['data'])) {
                return error(-1, '找不到支付单对应的订单信息');
            }
            
            $order_info = $order_info['data'];
            
            // 判断是否为跨境订单（此处仅为示例，实际业务需根据自身订单系统判断）
            $is_cross_border = true; // 实际业务中需要根据订单信息判断
            
            if (!$is_cross_border) {
                return success();
            }
            
            // 创建支付上报任务
            $task_model = new ChinaportTask();
            $task_data = [
                'task_type' => 'payment',
                'business_id' => $out_trade_no,
                'data' => json_encode([
                    'order_id' => $order_info['order_id'],
                    'order_no' => $order_info['order_no'],
                    'out_trade_no' => $out_trade_no,
                    'pay_time' => $pay_time,
                    'pay_channel' => isset($params['pay_type']) ? $params['pay_type'] : '',
                    'pay_amount' => isset($params['pay_money']) ? $params['pay_money'] : 0
                ])
            ];
            
            $result = $task_model->createTask($task_data);
            
            if ($result['code'] < 0) {
                return error($result['code'], $result['message']);
            }
            
            return success();
        } catch (\Exception $e) {
            return error(-1, '创建支付上报任务失败：' . $e->getMessage());
        }
    }
} 