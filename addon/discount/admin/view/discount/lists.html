{extend name="app/admin/view/base.html"/}
{block name="resources"}
{/block}
{block name="main"}
<div class="layui-collapse ns-tips">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>展示了限时折扣的相关信息列表</li>
			<li>通过活动名称搜索出具体折扣信息</li>
			<li>点击查看详情按钮，查看活动详细信息</li>
		</ul>
	</div>
</div>

<!-- 筛选面板 -->
<div class="ns-screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">筛选</h2>
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">活动名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="discount_name" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">店铺名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="site_name" placeholder="请输入店铺名称" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
			
			<div class="ns-form-row">
				<button class="layui-btn ns-bg-color" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab ns-table-tab" lay-filter="discount_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">所有活动</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
		<li lay-id="-1">已关闭</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="discount_list" lay-filter="discount_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="ns-table-btn">
		<a class="layui-btn" lay-event="detail">查看详情</a>
		{{#  if(!(d.status == 1)){ }}
		<a class="layui-btn" lay-event="del">删除</a>
		{{#  } }}
		{{#  if((d.status == 1)){ }}
		<a class="layui-btn" lay-event="colse">关闭</a>
		{{#  } }}
	</div>
</script>
{/block}
{block name="script"}
<script>
	layui.use(['form', 'element'], function() {
		var table,
			form = layui.form,
			element = layui.element,
			repeat_flag = false; //防重复标识
		form.render();

		//监听Tab切换，以改变地址hash值
		element.on('tab(discount_tab)', function() {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('lay-id')
				}
			});
		});

		table = new Table({
			elem: '#discount_list',
			url: ns.url("discount://admin/discount/lists"),
			cols: [
				[{
					field: 'discount_name',
					title: '活动名称',
					unresize: 'false',
					width: '16%'
				}, {
					field: 'site_name',
					title: '商家名称',
					unresize: 'false',
					width: '16%'
				}, {
					field: 'start_time',
					title: '开始时间',
					unresize: 'false',
					width: '18%',
					templet: function(data) {
						return ns.time_to_date(data.start_time);
					} //创建时间转换方法
				}, {
					field: 'end_time',
					title: '结束时间',
					unresize: 'false',
					width: '18%',
					templet: function(data) {
						return ns.time_to_date(data.end_time); //创建时间转换方法
					}
				}, {
					field: 'status_name',
					title: '状态',
					unresize: 'false',
					width: '16%'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					width: '16%'
				}]
			],
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.href = ns.url("discount://admin/discount/lists");
					break;
				case 'del': //删除
					layer.confirm('确定要删除该限时折扣活动吗?', function() {
						if (repeat_flag) return false;
						repeat_flag = true;
						
						$.ajax({
							url: ns.url("discount://admin/discount/delete"),
							data: data,
							dataType: 'JSON',
							type: 'POST',
							success: function(res) {
								layer.msg(res.message);
								repeat_flag = false;

								if (res.code == 0) {
									table.reload();
								}
							}
						});
					}, function() {
						layer.close();
						repeat_flag = false;
					});
					break;
				case 'colse': //关闭
					layer.confirm('确定要关闭该活动吗?', function() {
						if (repeat_flag) return false;
						repeat_flag = true;
						$.ajax({
							url: ns.url("discount://admin/discount/close"),
							data: data,
							dataType: 'JSON',
							type: 'POST',
							success: function(res) {
								layer.msg(res.message);
								repeat_flag = false;

								if (res.code == 0) {
									table.reload();
								}
							}
						});
					}, function() {
						layer.close();
						repeat_flag = false;
					});
					break;
				case 'detail': //详情
					location.href = ns.url("discount://admin/discount/detail?discount_id=" + data.discount_id + "&site_id=" + data.site_id);
					break;
			}
		});
		// 搜索
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
	});
</script>
{/block}