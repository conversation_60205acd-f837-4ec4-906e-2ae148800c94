-- 资讯文章
CREATE TABLE `xm_user_share_experience` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `title` VARCHAR(200) NOT NULL COMMENT '标题', 
    `image` VARCHAR(255) NOT NULL COMMENT '列表展示图片', 
    `goods_ids` VARCHAR(1000) NOT NULL COMMENT '商品列表，逗号隔开', 
    `sort` INT NOT NULL DEFAULT 1 COMMENT '排序', 
    `initial_view_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '初始浏览量', 
    `real_view_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '真实浏览量', 
    `like_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数', 
    `transmit_num` INT NOT NULL DEFAULT 0 COMMENT '转发数量', 
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态 1.正常显示 0.隐藏', 
    `content` TEXT NOT NULL COMMENT '文章内容', 
    `create_time` INT NOT NULL COMMENT '创建时间',
     PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8 ; 


-- 资讯文章交互表
CREATE TABLE `xm_user_share_experience_interact` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `user_share_experience_id` INT UNSIGNED NOT NULL  COMMENT '资讯文章id',
    `interact_type` VARCHAR(20) NOT NULL DEFAULT '' COMMENT 'like:点赞  transmit:转发', 
    `member_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id', 
    `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间', 
    PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8; 



ALTER TABLE `xm_user_share_experience_interact` ADD INDEX (`user_share_experience_id`);
ALTER TABLE `xm_user_share_experience_interact` ADD INDEX (`member_id`); 

-- 创建导航
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '用户分享体验区', 'USER_SHARE_EXPERIENCE', 'CONTENT_ROOT', 2, 'admin/UserShareExperience/lists', 1, 120, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '编辑用户分享体验', 'EDIT_USER_SHARE_EXPERIENCE', 'USER_SHARE_EXPERIENCE', 3, 'admin/UserShareExperience/edit', 0, 120, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '添加用户分享体验', 'ADD_USER_SHARE_EXPERIENCE', 'USER_SHARE_EXPERIENCE', 3, 'admin/UserShareExperience/add', 0, 120, '', 0, '', '', 1);

