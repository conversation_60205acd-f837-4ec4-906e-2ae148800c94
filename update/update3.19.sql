ALTER TABLE `xm_pintuan_order` ADD INDEX `pintuan_id`(`pintuan_id`) USING BTREE,
ADD INDEX `member_id`(`member_id`) USING BTREE,
ADD INDEX `head_id`(`head_id`) USING BTREE,
ADD INDEX `goods_id`(`goods_id`) USING BTREE;

ALTER TABLE `xm_pintuan_order` ADD COLUMN
    `refund_type` TINYINT DEFAULT 1 NOT NULL COMMENT '退款类型 1.原路退回  2.退回余额' AFTER `refund_no`;
ALTER TABLE `xm_pintuan` ADD COLUMN `group_invalid_min` INT UNSIGNED DEFAULT 0 NOT NULL COMMENT '失效时间';
ALTER TABLE `xm_pintuan` ADD COLUMN `leader_win` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '团长中奖限制(1-不限制 2-团长必中奖 3-团长必不中奖)';
ALTER TABLE `xm_pintuan_group` ADD COLUMN  `pintuan_info` text COMMENT '拼团信息';