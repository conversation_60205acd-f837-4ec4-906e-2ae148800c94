ALTER TABLE `xm_league_task_point_config`
    ADD COLUMN `goods_reward_points` tinyint NOT NULL DEFAULT 0 COMMENT '商品推广任务获贡献值，0:关闭，1：开启' AFTER `rules`;

CREATE TABLE `xm_league_task_goods_task` (
   `goods_task_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
   `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
   `reward_points` int(11) NOT NULL DEFAULT 0 COMMENT '奖励积分',
   `rules` text COMMENT '规则说明',
   `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
   `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`goods_task_id`),
   <PERSON><PERSON>Y `idx_league_task_key` (`league_task_key`),
   <PERSON><PERSON>Y `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='加盟任务-商品推广任务';

CREATE TABLE `xm_league_task_member_goods_task` (
    `member_goods_task_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `goods_task_id` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态，0进行中，10待审核，100已通过，-1已驳回',
    `reward_points` int(11) NOT NULL DEFAULT 0 COMMENT '奖励积分',
    `images` varchar(1000) NOT NULL DEFAULT '' COMMENT '附件（图片地址），逗号分隔',
    `remark` varchar(1000) NOT NULL DEFAULT '' COMMENT '备注',
    `submit_time` timestamp NULL DEFAULT NULL COMMENT '提交时间',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`member_goods_task_id`),
    KEY `idx_goods_task_id` (`goods_task_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='加盟任务-商品推广任务';

INSERT INTO `xm_menu`(`addon`,`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('leaguePoints','推广商品管理', 'PROMOTE_PRODUCT_MANAGEMENT', 'PROMOTION_LEAGUEPOINTS', 4, 1, 'leaguePoints://admin/PromoteProductManagement/lists',2);
INSERT INTO `xm_menu`(`addon`,`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('leaguePoints','推广任务审核', 'PROMOTE_TASK_AUDITS', 'PROMOTION_LEAGUEPOINTS', 4, 0, 'leaguePoints://admin/PromoteProductManagement/audits',3);

-- 为会员商品任务表添加缺少的字段
ALTER TABLE `xm_league_task_member_goods_task`
    MODIFY COLUMN `goods_task_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品任务ID',
    ADD COLUMN `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员ID' AFTER `member_goods_task_id`,
    ADD COLUMN `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID' AFTER `member_id`,
    ADD COLUMN `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID' AFTER `goods_id`,
    ADD COLUMN `league_task_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识' AFTER `goods_task_id`;

-- 添加索引以提高查询性能

ALTER TABLE `xm_league_task_member_goods_task`
    ADD INDEX `idx_member_id` (`member_id`),
    ADD INDEX `idx_goods_id` (`goods_id`),
    ADD INDEX `idx_order_id` (`order_id`),
    ADD INDEX `idx_league_task_key` (`league_task_key`);

ALTER TABLE `xm_league_task_member_goods_task`
    ADD COLUMN `fail_reason` varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因' AFTER `备注`;

ALTER TABLE `xm_league_task_point_config`
    ADD COLUMN `task_complete_max` int NOT NULL DEFAULT 0 COMMENT '商品推广任务奖励每月限制' AFTER `rules`;

ALTER TABLE `xm_member_league_task_point_log`
    ADD COLUMN `remark` varchar(1000) NOT NULL DEFAULT '' COMMENT '备注' AFTER `status`;

ALTER TABLE `xm_member_league_task_point_log`
    ADD COLUMN `admin_user` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人' AFTER `status`;