/*新模式消费送豆*/

CREATE TABLE IF NOT EXISTS `xm_member_recommend`
(
    `id`          int(10) unsigned                NOT NULL AUTO_INCREMENT,
    `pid`         int(10) unsigned                NOT NULL COMMENT '推荐人的member_id',
    `member_id`   int(10)                         NOT NULL COMMENT '会员id',
    `related_ids` varchar(768) CHARACTER SET utf8 NULL COMMENT '所有上级id，用（-）隔开',
    `update_time` int(10)                         NULL COMMENT '更新时间',
    `create_time` int(10)                         NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `parent_member` (`pid`) USING BTREE,
    KEY `member_id` (`member_id`) USING BTREE,
    KEY `related_ids` (`related_ids`(255)) USING BTREE
) ENGINE = InnoDB COMMENT ='会员推荐关系表';

CREATE TABLE IF NOT EXISTS `xm_member_apply_shop`
(
    `id`            int(11)          NOT NULL AUTO_INCREMENT,
    `shop_id`       int(10) unsigned NOT NULL COMMENT '店铺id',
    `member_id`     int(10) unsigned NOT NULL COMMENT '会员id',
    `parent_member` int(10) unsigned NOT NULL COMMENT '推荐人的会员id',
    `status`        tinyint(2)       NOT NULL DEFAULT '1' COMMENT '状态，0=待审核，1=审核通过，2=审核不通过',
    `wechat_no`     varchar(50)      NULL COMMENT '微信号',
    `real_name`     varchar(50)      NULL COMMENT '真实姓名',
    `mobile`        varchar(20)      NOT NULL COMMENT '开通店铺的手机',
    `update_time`   int(10)          NULL COMMENT '修改时间',
    `create_time`   int(10)          NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_shop_id` (`shop_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE = InnoDB COMMENT ='会员申请开店表';

CREATE TABLE IF NOT EXISTS `xm_members_order_recommend`
(
    `id`            int(11)          NOT NULL AUTO_INCREMENT,
    `shop_id`       int(11) unsigned NOT NULL COMMENT '店铺id',
    `order_id`      int(11) unsigned NOT NULL COMMENT '订单id',
    `order_no`      varchar(50)      NOT NULL DEFAULT '' COMMENT '订单编号',
    `parent_member` varchar(20)      NOT NULL DEFAULT '' COMMENT '推荐人member_id',
    `member_id`     varchar(255)     NOT NULL DEFAULT '' COMMENT '会员id',
    `order_money`   decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '订单金额',
    `sales_volume`  decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '销售额',
    `shop_rate`     decimal(10, 2)   NOT NULL COMMENT '店铺佣金比例',
    `status`        tinyint(1)       NOT NULL DEFAULT '0' COMMENT '1=未发放, 2=已发放, 3=已失效',
    `settle_time`   int(11)          NOT NULL DEFAULT '0' COMMENT '结算时间',
    `update_time`   int(11)          NOT NULL DEFAULT '0' COMMENT '更新时间',
    `create_time`   int(11)          NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`) USING BTREE,
    KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_parent_member` (`parent_member`) USING BTREE,
    KEY `idx_shop_id` (`shop_id`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
) ENGINE = InnoDB COMMENT ='会员订单推荐表';

CREATE TABLE IF NOT EXISTS `xm_tag`
(
    `id`            INT UNSIGNED        NOT NULL AUTO_INCREMENT,
    `tag_name`      VARCHAR(32)         NOT NULL COMMENT '名称',
    `key`           VARCHAR(16)         NOT NULL COMMENT '唯一值',
    `enable`        TINYINT(1) UNSIGNED NOT NULL COMMENT '1.开启  0.关闭',
    `tag_config_id` INT UNSIGNED        NOT NULL COMMENT '配置id',
    `create_time`   INT(11)             NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX (`key`),
    INDEX (`tag_config_id`)
) ENGINE = InnoDB COMMENT ='标签表';

CREATE TABLE IF NOT EXISTS `xm_tag_config`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `tag_id`      INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '标签id（xm_tag）',
    `config`      TEXT COMMENT '配置json',
    `create_time` INT          NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX (`tag_id`)
) ENGINE = InnoDB COMMENT ='标签配置表';

CREATE TABLE IF NOT EXISTS `xm_tag_config_relation`
(
    `id`            INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `module`        VARCHAR(32)  NOT NULL COMMENT '模块',
    `relation_id`   INT UNSIGNED NOT NULL COMMENT '关联模块id （例如xm_orders 的主键）',
    `tag_config_id` INT          NOT NULL COMMENT '配置id',
    `create_time`   INT          NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX (`relation_id`),
    INDEX (`tag_config_id`)
) ENGINE = InnoDB COMMENT ='标签配置关联表';

CREATE TABLE IF NOT EXISTS `xm_member_maidou`
(
    `id`            INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id`     INT          NOT NULL COMMENT '会员id',
    `total_maidou`  INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '迈豆总数',
    `canuse_maidou` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '可用迈豆',
    `nouser_maidou` INT UNSIGNED NOT NULL COMMENT '冻结迈豆',
    `create_time`   INT          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`   INT          NOT NULL DEFAULT 0 COMMENT '变化时间',
    PRIMARY KEY (`id`),
    INDEX (`member_id`)
) ENGINE = InnoDB COMMENT ='会员迈豆表';

CREATE TABLE IF NOT EXISTS `xm_maidou_account`
(
    `id`          INT UNSIGNED     NOT NULL AUTO_INCREMENT,
    `account_no`  VARCHAR(50)      NOT NULL COMMENT '流水号',
    `member_id`   INT UNSIGNED     NOT NULL COMMENT '用户id',
    `status`      TINYINT          NOT NULL DEFAULT 0 COMMENT '-1.扣减  0.冻结  1.已到账',
    `num`         INT              NOT NULL DEFAULT 0 COMMENT '数量',
    `type`        TINYINT UNSIGNED NOT NULL COMMENT '来源类型 （1、订单）',
    `type_id`     INT              NOT NULL DEFAULT 0 COMMENT '来源id',
    `remark`      TEXT COMMENT '备注',
    `chang_time`  INT              NOT NULL DEFAULT 0 COMMENT '变化时间（解冻、扣减）',
    `create_time` INT              NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX (`member_id`),
    INDEX (`type_id`)
) ENGINE = InnoDB COMMENT ='用户迈豆流水表';

# 迈豆专区佣金设置菜单
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '迈豆专区佣金设置', 'MAIDOU_COMMISSION_SETTING', 'CONFIG_ROOT', '2', 'admin/config/maidou_commission', '1', '1', '', '0', '', '', '1');

/**
 * 添加菜单
 * wjj
 * 2020-12-25
 */
UPDATE `xm_menu` SET `level` = 4, `is_show` = 1 WHERE `name`='SHOP_LIST';
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`,`is_control`)
VALUES ( 'admin', '', '待审核店铺', 'PENDING_SHOP_LIST', 'SHOP_INDEX', 4, 'admin/shop/pending_shop', 1, 100, 1),
( 'admin', '', '店铺审核', 'PENDING_SHOP_EDIT', 'SHOP_INDEX', 3, 'admin/shop/adjust_shop', 0, 100, 1);
/**
* 补充推荐关系
* wjj
* 2020-12-28
*/
-- 开店的会员
INSERT INTO xm_member_recommend (pid,member_id,related_ids,update_time,create_time)
SELECT 0,m.member_id,CONCAT('-',m.member_id,'-'),UNIX_TIMESTAMP(),UNIX_TIMESTAMP() from xm_member m
left JOIN xm_shop s on m.mobile=s.username WHERE s.site_id>0 group by m.member_id;

-- 未开店的会员
INSERT INTO xm_member_recommend (pid,member_id,related_ids,update_time,create_time)
SELECT m.member_id,sm.member_id,CONCAT('-',m.member_id,'-',sm.member_id,'-'),UNIX_TIMESTAMP(),UNIX_TIMESTAMP()
from xm_shop_member sm
INNER JOIN xm_shop s on s.site_id=sm.site_id
INNER JOIN xm_member m on m.mobile=s.username
WHERE sm.member_id in (SELECT m.member_id from  xm_member m left JOIN xm_shop s on m.mobile=s.username WHERE s.site_id is null) group by sm.member_id;

# 迈豆流水菜单
INSERT INTO `xm_menu` (`title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`)
VALUES ('迈豆流水', 'MAIDOU_RECORD', 'ACCOUNT_ROOT', 2, 'admin/MaidouRecord/lists', 1, 17);

-- tag添加默认迈豆类型
INSERT INTO `xm_tag` (`tag_name`, `key`, `enable`, `tag_config_id`, `create_time`) VALUES ('迈豆专区', 'maidou', '1', '0', '**********'); 

-- 一些字段修改
ALTER TABLE `xm_order` CHANGE `pay_type` `pay_type` VARCHAR(55) CHARSET utf8 COLLATE utf8_general_ci DEFAULT '' NOT NULL COMMENT '支付方式 ONLINE_PAY=在线支付,BALANCE =余额支付,OFFLINE_PAY=线下支付,MAIDOU = 迈豆支付'; 
ALTER TABLE `xm_order` ADD COLUMN `maidou_money` DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '迈豆支付金额'  ; 
ALTER TABLE `xm_order` CHANGE `order_create_type` `order_create_type` TINYINT(2) UNSIGNED DEFAULT 1 NULL COMMENT '1=普通订单,2=周期购订单,3=秒杀订单,4=分享赚订单,5=砍价订单,6=迈豆专区订单';

# 修改后台页面标题
UPDATE `xm_menu` SET `title` = '用户迈豆明细列表' WHERE `name` = 'MEMBER_POINT_LISTS';

-- 添加tag表id
 ALTER TABLE `xm_tag_config_relation` ADD COLUMN `tag_id` INT NOT NULL COMMENT 'xm_tag  id' AFTER `tag_config_id`; 