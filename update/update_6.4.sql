INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '素材管理', 'EW_MATERIAL_MANAGE', 'ENTERPRISE_WX_MATERIAL', 3, 'admin/EnterpriseMaterial/lists', 1, 120, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '企微素材库', 'ENTERPRISE_WX_MATERIAL', 'CONTENT_ROOT', 2, 'admin/EnterpriseMaterial/lists', 1, 111, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签管理', 'MATERIAL_TAG_MANAGE', 'ENTERPRISE_WX_MATERIAL', 3, 'admin/EnterpriseMaterial/tagAdd', 0, 0, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签值', 'MATERIAL_TAG_VALUE_LIST', 'MATERIAL_TAG_GROUP', 4, 'admin/EnterpriseMaterial/tagValueList', 1, 2, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签管理', 'MATERIAL_TAG_GROUP', 'ENTERPRISE_WX_MATERIAL', 3, 'admin/EnterpriseMaterial/tagGroupList', 1, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签组', 'MATERIAL_TAG_GROUP_LIST', 'MATERIAL_TAG_GROUP', 4, 'admin/EnterpriseMaterial/tagGroupList', 1, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '素材详情', 'EW_MATERIAL_DETAIL', 'ENTERPRISE_WX_MATERIAL', 3, 'admin/EnterpriseMaterial/detail', 0, 100, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '编辑素材', 'EW_MATERIAL_EDIT', 'ENTERPRISE_WX_MATERIAL', 3, 'admin/EnterpriseMaterial/edit', 0, 100, '', 0, '', '', 1);


CREATE TABLE IF NOT EXISTS `xm_enterprise_wx_material`
(
    `material_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
    `type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型',
    `subtitle` varchar(100) NOT NULL DEFAULT '' COMMENT '副标题',
    `content` text DEFAULT NULL COMMENT '正文内容',
    `url` varchar(1000) NOT NULL DEFAULT '' COMMENT '链接',
    `path` varchar(200) NOT NULL DEFAULT '' COMMENT '小程序路径',
    `send_nums` int(11) NOT NULL DEFAULT '0' COMMENT '发送次数',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(11) NOT NULL DEFAULT '0' COMMENT '状态',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`material_id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='企微素材表';

CREATE TABLE IF NOT EXISTS `xm_material_resources`
(
    `resource_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型',
    `md5` varchar(32) NOT NULL DEFAULT '' COMMENT 'MD5',
    `path` varchar(200) NOT NULL DEFAULT '' COMMENT '保存路径',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`resource_id`),
    KEY `idx_type` (`type`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='企微素材资源表';

CREATE TABLE IF NOT EXISTS `xm_material_tag`
(
    `tag_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签id',
    `tag_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签组id',
    `tag_name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签名',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，越小排越前',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`tag_id`),
    KEY `idx_tag_group_id` (`tag_group_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='素材标签表';

CREATE TABLE IF NOT EXISTS `xm_material_tag_group`
(
    `tag_group_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签组id',
    `group_name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签组名',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，越大排越前',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`tag_group_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='素材标签组表';

CREATE TABLE IF NOT EXISTS `xm_material_tag_relation`
(
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `material_id` int(11) NOT NULL DEFAULT '0' COMMENT '素材id',
    `tag_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_material_id` (`material_id`),
    KEY `idx_tag_id` (`tag_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='素材标签关联表';

CREATE TABLE IF NOT EXISTS `xm_material_resources_relation`
(
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `material_id` int(11) NOT NULL DEFAULT '0' COMMENT '素材id',
    `resource_id` int(11) NOT NULL DEFAULT '0' COMMENT '资源id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_material_id` (`material_id`),
    KEY `idx_resource_id` (`resource_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='素材资源关联表';

ALTER TABLE `xm_material_resources`
    ADD COLUMN `media_id` varchar(50) NOT NULL DEFAULT '' COMMENT '企微media_id',
    ADD COLUMN `media_id_time_out` timestamp NULL DEFAULT NULL COMMENT '企微media_id过期时间';

ALTER TABLE `xm_enterprise_wx_material`
    ADD COLUMN `thumbnail` varchar(200) NOT NULL DEFAULT '' COMMENT '缩略图';

ALTER TABLE `xm_material_resources`
    MODIFY COLUMN `media_id` varchar(100) NOT NULL DEFAULT '' COMMENT '企微media_id'
