CREATE TABLE `xm_order_manual_subsidy` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '订单编号',
  `old_site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '旧site_id',
  `new_site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '新site_id',
  `update_table` varchar(255) NOT NULL DEFAULT '' COMMENT '更改过程涉及到的表名',
  `before_update_data` text COMMENT '更新前的数据',
  `update_data` text COMMENT '需要更新的数据',
  `action_manager` varchar(255) NOT NULL DEFAULT '' COMMENT '操作的管理员',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='订单手动发放补贴';


INSERT INTO xm_menu (`app_module`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `is_control`) VALUES ('admin', '订单手动发放补贴', 'ORDER_MANUAL_SUBSIDY', 'ORDER_ROOT', 2, 'admin/orderManualSubsidy/lists', 1, 20, 1);