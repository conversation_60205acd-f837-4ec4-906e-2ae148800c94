-- INSERT INTO `xm_diy_view_util` (`name`, `title`, `type`, `value`, `controller`, `max_count`) VALUES ('KINGKONG_AREA', '金刚区', 'SYSTEM', '{"backgroundColor":""}', 'Kingkong', 1); 
INSERT INTO `xm_diy_view_util` (`name`, `title`, `type`, `value`, `controller`, `addon_name`, `max_count`) VALUES ('MAIDOU_LIST', '迈豆', 'OTHER', '{"backgroundColor":""}', 'Maidou', 'maidou', 1); 
INSERT INTO `xm_diy_view_util` (`name`, `title`, `type`, `value`, `controller`, `max_count`) VALUES ('ACTIVITY_ADS', '活动广告', 'SYSTEM', '{"backgroundColor":""}', 'ActivityAds', 0); 
-- INSERT INTO `xm_diy_view_util` (`name`, `title`, `type`, `value`, `controller`, `max_count`) VALUES ('SHOPER_RECOMMAND', '店主推荐', 'SYSTEM', '{"backgroundColor":""}', 'ShoperRecommand', 1); 


UPDATE `xm_diy_view_util` SET `value` = '{\"textColor\":\"#666666\",\"backgroundColor\":\"\",\"selectedTemplate\":\"imageNavigation\",\"scrollSetting\":\"fixed\",\"imageScale\":100,\"padding\":0,\"list\":[{\"imageUrl\":\"\",\"title\":\"\",\"link\":{}},{\"imageUrl\":\"\",\"title\":\"\",\"link\":{}},{\"imageUrl\":\"\",\"title\":\"\",\"link\":{}},{\"imageUrl\":\"\",\"title\":\"\",\"link\":{}},{\"imageUrl\":\"\",\"title\":\"\",\"link\":{}}]}' WHERE `name` = 'GRAPHIC_NAV'; 
UPDATE `xm_diy_view_util` SET `value` = '{\"backgroundColor\":\"\",\"moreText\": \"更多内容\",\"moreUrl\": \"/promotionpages/pintuan/list/list\"}' WHERE `name` = 'PINTUAN_LIST'; 
UPDATE `xm_diy_view_util` SET `value` = '{\"backgroundColor\":\"\",\"moreText\": \"更多内容\",\"moreUrl\": \"/promotionpages/new_seckill/list/list\"}' WHERE `name` = 'SECKILL_LIST'; 
UPDATE `xm_diy_view_util` SET `value` = '{\"backgroundColor\":\"\",\"moreText\": \"更多内容\",\"moreUrl\": \"/promotionpages/maidou/list/list\"}' WHERE `name` = 'MAIDOU_LIST'; 



-- delete from `xm_site_diy_view` where `name` =  "DIYVIEW_SHOP_INDEX";

UPDATE `xm_diy_view_util` SET `value` = '{"currentSelect":1,"currentSelectType":1,"currentSelectTypeList":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}],"sourcesList":[{"select":1,"text":"模板1","imgLength":2,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}]},{"select":2,"text":"模板2","imgLength":3,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}]},{"select":3,"text":"模板3","imgLength":4,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}]},{"select":4,"text":"模板4","imgLength":4,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}]},{"select":5,"text":"模板5","imgLength":3,"type":[{"text":"类型1","value":1}]},{"select":6,"text":"模板6","imgLength":3,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2},{"text":"类型3","value":3}]},{"select":7,"text":"模板7","imgLength":4,"type":[{"text":"类型1","value":1},{"text":"类型2","value":2}]}],"list":[{"imageUrl":"","link":"","title":""},{"imageUrl":"","link":"","title":""}],"imgSizeList":[{"select":1,"typeList":[{"type":1,"list":[{"width":"342px","height":"162px"},{"width":"342px","height":"162px"}]},{"type":2,"list":[{"width":"342px","height":"266px"},{"width":"342px","height":"266px"}]},{"type":3,"list":[{"width":"342px","height":"342px"},{"width":"342px","height":"342px"}]}]},{"select":2,"typeList":[{"type":1,"list":[{"width":"222px","height":"140px"},{"width":"222px","height":"140px"},{"width":"222px","height":"140px"}]},{"type":2,"list":[{"width":"222px","height":"222px"},{"width":"222px","height":"222px"},{"width":"222px","height":"222px"}]},{"type":3,"list":[{"width":"222px","height":"296px"},{"width":"222px","height":"296px"},{"width":"222px","height":"296px"}]}]},{"select":3,"typeList":[{"type":1,"list":[{"width":"162px","height":"162px"},{"width":"162px","height":"162px"},{"width":"162px","height":"162px"},{"width":"162px","height":"162px"}]},{"type":2,"list":[{"width":"162px","height":"128px"},{"width":"162px","height":"128px"},{"width":"162px","height":"128px"},{"width":"162px","height":"128px"}]},{"type":3,"list":[{"width":"162px","height":"200px"},{"width":"162px","height":"200px"},{"width":"162px","height":"200px"},{"width":"162px","height":"200px"}]}]},{"select":4,"typeList":[{"type":1,"list":[{"width":"342px","height":"194px"},{"width":"342px","height":"194px"},{"width":"342px","height":"194px"},{"width":"342px","height":"194px"}]},{"type":2,"list":[{"width":"342px","height":"266px"},{"width":"342px","height":"266px"},{"width":"342px","height":"266px"},{"width":"342px","height":"266px"}]},{"type":3,"list":[{"width":"342px","height":"342px"},{"width":"342px","height":"342px"},{"width":"342px","height":"342px"},{"width":"342px","height":"342px"}]}]},{"select":5,"typeList":[{"type":1,"list":[{"width":"342px","height":"342px"},{"width":"342px","height":"162px"},{"width":"342px","height":"162px"}]}]},{"select":6,"typeList":[{"type":1,"list":[{"width":"702px","height":"246px"},{"width":"342px","height":"194px"},{"width":"342px","height":"194px"}]},{"type":2,"list":[{"width":"702px","height":"246px"},{"width":"171px","height":"266px"},{"width":"171px","height":"266px"}]},{"type":3,"list":[{"width":"702px","height":"246px"},{"width":"342px","height":"342px"},{"width":"342px","height":"342px"}]}]},{"select":7,"typeList":[{"type":1,"list":[{"width":"342px","height":"342px"},{"width":"342px","height":"162px"},{"width":"162px","height":"162px"},{"width":"162px","height":"162px"}]},{"type":2,"list":[{"width":"342px","height":"218px"},{"width":"342px","height":"100px"},{"width":"162px","height":"100px"},{"width":"162px","height":"100px"}]}]}]}' WHERE `name` = 'ACTIVITY_ADS'; 
UPDATE `xm_diy_view_util` SET `value` = '{\"addon_name\":\"\",\"backgroundColor\":\"\",\"controller\":\"Search\",\"isJump\":1,\"lazyLoad\":true,\"lazyLoadCss\":true,\"link\":\"/otherpages/goods/search/search\",\"name\":\"商品搜索\",\"outerCountJs\":1,\"placeholder\":\"\",\"sort\":\"0\",\"sourcesList\":[{\"text\":\"跳转\",\"value\":1},{\"text\":\"不跳转\",\"value\":2}],\"type\":\"SEARCH\"}' WHERE `name` = 'SEARCH'; 

UPDATE `xm_menu` SET `parent` = 'SHOP_DECORATE' WHERE `name` = 'WEBSITE_MID_CONFIG';