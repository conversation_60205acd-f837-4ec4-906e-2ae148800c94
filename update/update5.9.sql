CREATE TABLE IF NOT EXISTS `xm_goods_coupon_rules_tag_relation`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `rule_id` int(11) NOT NULL DEFAULT '0' COMMENT '规则id',
    `tag_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签',
    `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updated_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='优惠券发放规则用户标签关联表';

CREATE TABLE IF NOT EXISTS `xm_goods_coupon_type_tag_relation`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `goodscoupon_type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型Id',
    `tag_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签',
    `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updated_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='优惠券类型用户标签关联表';

ALTER TABLE `xm_promotion_goods_coupon_rules`
    ADD COLUMN `relation_tag_status` tinyint NULL DEFAULT 0 COMMENT '关联的标签状态' AFTER `updated_at`;

ALTER TABLE `xm_promotion_goodscoupon_type`
    ADD COLUMN `relation_tag_status` tinyint NULL DEFAULT 0 COMMENT '关联的标签状态' AFTER `use_remind`;



CREATE TABLE `xm_index_banner_tag_relation` 
( 
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `tag_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '标签id', 
    `index_banner_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'banner表id', 
    `created_at` INT UNSIGNED NOT NULL COMMENT '创建时间', 
    PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8; 
 ALTER TABLE `xm_index_banner_tag_relation` ADD INDEX (`tag_id`) , ADD INDEX (`index_banner_id`); 