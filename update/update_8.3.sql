CREATE TABLE IF NOT EXISTS `xm_access_track`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `ip` varchar(20) NULL DEFAULT '' COMMENT 'ip',
    `page_url` varchar(200) NOT NULL DEFAULT '' COMMENT '页面url',
    `model` varchar(30) NOT NULL DEFAULT '' COMMENT '模块',
    `action` varchar(30) NOT NULL DEFAULT '' COMMENT '动作',
    `referer` varchar(200) NOT NULL DEFAULT '' COMMENT '',
    `region` varchar(50) NOT NULL DEFAULT '' COMMENT '',
    `region_storeID_create_time` varchar(30) NOT NULL DEFAULT '' COMMENT '',
    `storeID` varchar(50) NOT NULL DEFAULT '' COMMENT '',
    `weapp_referer` varchar(500) NOT NULL DEFAULT '' COMMENT '',
    `weapp_scene` text DEFAULT NULL COMMENT '',
    `live_custom_params` text DEFAULT NULL COMMENT '',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_ip` (`ip`),
    KEY `idx_model` (`model`),
    KEY `idx_action` (`action`),
    KEY `idx_region` (`region`),
    KEY `idx_storeID` (`storeID`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='访问信息跟踪表';

ALTER TABLE `xm_sign_log`
    ADD COLUMN `access_id` integer NOT NULL DEFAULT '0' COMMENT '访问id';

CREATE TABLE IF NOT EXISTS `xm_sign_access`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `ip` varchar(20) NULL DEFAULT '' COMMENT 'ip',
    `page_url` varchar(200) NOT NULL DEFAULT '' COMMENT '页面url',
    `sign_activity_id` integer NOT NULL DEFAULT '0' COMMENT '签到活动id',
    `date` varchar(20) NOT NULL DEFAULT '' COMMENT '访问日期',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_ip` (`ip`),
    KEY `idx_date` (`date`),
    KEY `idx_sign_activity_id` (`sign_activity_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='签到访问记录表';

ALTER TABLE `xm_sign_access`
    ADD COLUMN `access_id` integer NOT NULL DEFAULT '0' COMMENT '访问id';


ALTER TABLE `xm_access_track`
    ADD COLUMN `share_in_page` integer NOT NULL DEFAULT '0' COMMENT '';

ALTER TABLE `xm_access_track`
    ADD COLUMN `live_room_id` integer NOT NULL DEFAULT '0' COMMENT '直播房间id';

ALTER TABLE `xm_access_track`
    ADD INDEX `idx_live_room_id`(`live_room_id`) USING BTREE;

CREATE TABLE IF NOT EXISTS `xm_order_live`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id` integer NOT NULL DEFAULT '0' COMMENT '订单id',
    `live_room_id` integer NOT NULL DEFAULT '0' COMMENT '直播房间id',
    `access_id` integer NOT NULL DEFAULT '0' COMMENT '访问id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_access_id` (`access_id`),
    KEY `idx_live_room_id` (`live_room_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='订单直播关联表';

ALTER TABLE `xm_order_live`
    ADD COLUMN `order_goods_id` integer NOT NULL DEFAULT '0' COMMENT '订单商品id';