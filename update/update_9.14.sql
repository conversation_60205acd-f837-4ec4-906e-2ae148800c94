CREATE TABLE IF NOT EXISTS `xm_questionnaire`
(
    `questionnaire_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
    `share_title` varchar(255) NOT NULL DEFAULT '' COMMENT '分享语',
    `start_time` timestamp NULL COMMENT '开始时间',
    `end_time` timestamp NULL COMMENT '结束时间',
    `status` integer NOT NULL DEFAULT 0 COMMENT '状态0未开始，10进行中，100已结束',
    `join_limit` varchar(1000) NULL COMMENT 'json数据结构，参与限制，not_limit不限制，tag限制标签，gender限制性别，age限制年龄，realname限制实名',
    `submit_limit` tinyint NOT NULL DEFAULT 0 COMMENT '提交次数限制，0为不限制',
    `edit_limit` tinyint NOT NULL DEFAULT 0 COMMENT '修改限制，1可修改，0不可修改',
    `image` varchar(500) NOT NULL DEFAULT '' COMMENT '宣传图url',
    `content` text NULL COMMENT '活动说明',
    `column_data` text NULL COMMENT '字段数据',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`questionnaire_id`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_end_time` (`end_time`),
    KEY `idx_submit_limit` (`submit_limit`),
    KEY `idx_edit_limit` (`edit_limit`),
    KEY `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='问卷活动';

CREATE TABLE IF NOT EXISTS `xm_questionnaire_share_member`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `questionnaire_id` integer NOT NULL DEFAULT 0 COMMENT '问卷id',
    `member_id` integer NOT NULL DEFAULT 0 COMMENT '用户id',
    `enable` tinyint NOT NULL DEFAULT 0 COMMENT '权限，0关闭1启用',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_questionnaire_id` (`questionnaire_id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='问卷分享人表';

CREATE TABLE IF NOT EXISTS `xm_questionnaire_submit`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `questionnaire_id` integer NOT NULL DEFAULT 0 COMMENT '问卷id',
    `member_id` integer NOT NULL DEFAULT 0 COMMENT '用户id',
    `data` text NULL COMMENT '问卷数据',
    `last_update_time` timestamp NULL DEFAULT NULL COMMENT '最后更新时间',
    `share_member_id` integer NOT NULL DEFAULT 0 COMMENT '分享用户id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_questionnaire_id` (`questionnaire_id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='问卷提交表';

CREATE TABLE IF NOT EXISTS `xm_questionnaire_access`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `questionnaire_id` integer NOT NULL DEFAULT 0 COMMENT '问卷id',
    `questionnaire_url` varchar(500) NOT NULL DEFAULT '' COMMENT '问卷url',
    `share_member_id` integer NOT NULL DEFAULT 0 COMMENT '分享用户id',
    `access_member_id` integer NOT NULL DEFAULT 0 COMMENT '访问用户id',
    `access_time` timestamp NULL DEFAULT NULL COMMENT '访问时间',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_questionnaire_id` (`questionnaire_id`),
    KEY `idx_share_member_id` (`share_member_id`),
    KEY `idx_access_member_id` (`access_member_id`),
    KEY `idx_access_time` (`access_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='问卷访问';

ALTER TABLE `xm_questionnaire`
    ADD COLUMN `bottom_image` varchar(500) NOT NULL DEFAULT '' COMMENT '尾部宣传图url';

CREATE TABLE IF NOT EXISTS `xm_member_auth_by_sm`
(
    `auth_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '柚品会员ID',
    `auth_card_name` varchar(50) NOT NULL DEFAULT '' COMMENT '实名姓名',
    `auth_card_no` varchar(18) NOT NULL DEFAULT '' COMMENT '实名身份证',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`auth_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_auth_card_no` (`auth_card_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员实名认证表(先迈来源)';