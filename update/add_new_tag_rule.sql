INSERT INTO `xm_enterprise_tag_rule` (`key`, `rule`, `rule_value`, `rule_value_extral`, `sort`, `create_time`) VALUES ('league_quit_prices', '近%s天新增的提前退出金额在%s元到%s元之间', '[{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0}]', '[{\"type\":\"between\",\"info\":[{\"index\":1 ,\"class\": \"min-btw\"},{\"index\":2 ,\"class\": \"max-btw\"}]}]', 18, 1666161752);
SET @last_id := LAST_INSERT_ID();

SET @rule_group_id := (select tag_rule_group_id from xm_enterprise_tag_rule_group where group_key='league' and tag_rule_type='auto');
INSERT INTO `xm_enterprise_tag_rule_group_middle` (`tag_rule_group_id`, `tag_rule_id`, `create_time`) VALUES (@rule_group_id, @last_id, 1666162506);

SET @rule_group_id := (select tag_rule_group_id from xm_enterprise_tag_rule_group where group_key='league' and tag_rule_type='auto_cancel');
INSERT INTO `xm_enterprise_tag_rule_group_middle` (`tag_rule_group_id`, `tag_rule_id`, `create_time`) VALUES (@rule_group_id, @last_id, 1666162506);

