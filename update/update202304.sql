# 修改返佣比例，支持4位小数
ALTER TABLE `xm_goods`
    MODIFY COLUMN `reward_shop_rate` decimal(10, 4) UNSIGNED NOT NULL DEFAULT 0.0000 COMMENT '店主返佣比例(单位：%)' AFTER `sku_id`,
    MODIFY COLUMN `reward_company_rate` decimal(10, 4) UNSIGNED NOT NULL DEFAULT 0.0000 COMMENT '公司返佣比例(单位：%)' AFTER `reward_shop_rate`;

ALTER TABLE `xm_goods_sku`
    ADD COLUMN `reward_shop_rate` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '店主返佣比例(单位：%)' AFTER `xm_spec_goods_id`,
    ADD COLUMN `reward_company_rate` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '公司返佣比例(单位：%)' AFTER `reward_shop_rate`;

ALTER TABLE `xm_order_goods`
    MODIFY COLUMN `reward_shop_rate` decimal(10, 4) UNSIGNED NOT NULL DEFAULT 0.0000 COMMENT '店主返佣比例(单位：%)' AFTER `sku_id`,
    MODIFY COLUMN `reward_company_rate` decimal(10, 4) UNSIGNED NOT NULL DEFAULT 0.0000 COMMENT '公司返佣比例(单位：%)' AFTER `reward_shop_rate`;

# 更新比例到sku表
UPDATE `xm_goods_sku` sku SET `reward_company_rate` = (select reward_company_rate from xm_goods g where g.goods_id=sku.goods_id limit 1);
UPDATE `xm_goods_sku` sku SET `reward_shop_rate` = (select reward_shop_rate from xm_goods g where g.goods_id=sku.goods_id limit 1);