CREATE TABLE `xm_goods_stat`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT,
    `goods_id`         int(11) NOT NULL COMMENT '商品id',
    `click_num`        int(11) NOT NULL DEFAULT '0' COMMENT '用户点击量（按次数算）',
    `user_view`        int(11) NOT NULL DEFAULT '0' COMMENT '用户浏览量（按用户算）',
    `order_member`     int(11) NOT NULL COMMENT '购买人数',
    `order_num`        int(11) NOT NULL COMMENT '支付订单数量(按商品算order_goods)',
    `goods_money`      decimal(10, 2) NOT NULL COMMENT '销售金额（real_goods_money-goodscoupon_money-multiple_discount_money-promotion_money）',
    `cost_money`       decimal(10, 2) NOT NULL COMMENT '总成本',
    `stat_time`        datetime       NOT NULL COMMENT '统计日期',
    `add_time`         datetime       NOT NULL COMMENT '创建时间',
    `edit_time`        datetime       NOT NULL COMMENT '更新时间',
    `operate_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '运营组id',
    PRIMARY KEY (`id`),
    KEY                `goods_id` (`goods_id`),
    KEY                `stat_time` (`stat_time`),
    KEY                `add_time` (`add_time`),
    KEY                `edit_time` (`edit_time`),
    KEY                `operate_group_id` (`operate_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品数据统计';

ALTER TABLE `xm_goods_browse_log`
    ADD INDEX `idx_create_time`(`create_time`);