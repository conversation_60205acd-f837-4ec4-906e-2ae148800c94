ALTER TABLE `xm_text_extraction_result`
    DROP COLUMN `associate_nums`,
    DROP COLUMN `associate_hit`,
    DROP COLUMN `extraction_result`;


CREATE TABLE IF NOT EXISTS `xm_search_logs`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `member_id` int(0) NOT NULL DEFAULT 0  COMMENT '用户id',
    `search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '搜索文本',
    `source` varchar(50) NOT NULL DEFAULT '' COMMENT '来源',
    `source_search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '原搜索文本',
    `search_date` varchar(20) NOT NULL DEFAULT '' COMMENT '搜索日期',
    `search_time` varchar(20) NOT NULL DEFAULT '' COMMENT '搜索时间',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_search_text` (`search_text`),
    KEY `idx_source` (`source`),
    KEY `idx_source_search_text` (`source_search_text`),
    KEY `idx_search_date` (`search_date`),
    KEY `idx_search_time` (`search_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索日志表';

CREATE TABLE IF NOT EXISTS `xm_search_statistics`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '搜索文本',
    `date` varchar(20) NOT NULL DEFAULT '' COMMENT '日期',
    `time` int NOT NULL DEFAULT 0 COMMENT '时间',
    `search_nums` int unsigned NOT NULL DEFAULT '0' COMMENT '搜索次数',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_search_text` (`search_text`),
    KEY `idx_date` (`date`),
    KEY `idx_time` (`time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索统计表';


CREATE TABLE IF NOT EXISTS `xm_guess_logs`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `member_id` int(0) NOT NULL DEFAULT 0  COMMENT '用户id',
    `search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '搜索文本',
    `guess_result` varchar(2000) NOT NULL DEFAULT '' COMMENT '联想返回',
    `guess_date` varchar(20) NOT NULL DEFAULT '' COMMENT '联想日期',
    `guess_time` int NOT NULL DEFAULT 0 COMMENT '联想时间',
    `is_success` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否成功得联想',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_search_text` (`search_text`),
    KEY `idx_guess_date` (`guess_date`),
    KEY `idx_guess_time` (`guess_time`),
    KEY `idx_is_success` (`is_success`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='联想日志表';

CREATE TABLE IF NOT EXISTS `xm_guess_logs`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `member_id` int(0) NOT NULL DEFAULT 0  COMMENT '用户id',
    `search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '搜索文本',
    `guess_result` varchar(2000) NOT NULL DEFAULT '' COMMENT '联想返回',
    `guess_date` varchar(20) NOT NULL DEFAULT '' COMMENT '联想日期',
    `guess_time` int NOT NULL DEFAULT 0 COMMENT '联想时间',
    `is_success` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否成功得联想',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_search_text` (`search_text`),
    KEY `idx_guess_date` (`guess_date`),
    KEY `idx_guess_time` (`guess_time`),
    KEY `idx_is_success` (`is_success`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='联想日志表';

ALTER TABLE `xm_text_extraction_statistics`
    ADD COLUMN `source` varchar(50) NOT NULL DEFAULT 'goods' COMMENT '来源' AFTER `keyword_text`;

ALTER TABLE `xm_text_extraction_statistics`
    ADD INDEX `idx_source`(`source`) USING BTREE;

INSERT INTO `xm_menu`(`title`, `name`, `parent`,`url`,`is_show`,`sort`)
VALUES ('搜索热词分析', 'SEARCH_HOT_WORD_ANALYSIS','STAT_ROOT','admin/Analyse/searchHotWordAnalysis',1,10);


CREATE TABLE IF NOT EXISTS `xm_search_data`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `search_text` varchar(200) NOT NULL DEFAULT '' COMMENT '搜索文本',
    `guess_text` varchar(2000) NOT NULL DEFAULT '' '联想关键词',
    `up_nums` int NOT NULL DEFAULT 0 COMMENT '上架商品数',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    key `idx_search_text` (`search_text`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索数据表';
