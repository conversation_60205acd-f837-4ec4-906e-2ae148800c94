ALTER TABLE `xm_sign_activity`
    ADD COLUMN `complete_rules` varchar(1000) NOT NULL DEFAULT '' COMMENT '签到规则',
    ADD COLUMN `use_rules` integer NOT NULL DEFAULT '0' COMMENT '是否开启规则';

ALTER TABLE `xm_sign_activity_complete`
    ADD COLUMN `invite_member_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '邀请用户id';

ALTER TABLE `xm_sign_activity_complete`
    ADD COLUMN `code` varchar(20) NOT NULL DEFAULT '' COMMENT '兑换码';

ALTER TABLE `xm_sign_activity_complete`
    ADD COLUMN `goodscoupon_id` integer NOT NULL DEFAULT '0' COMMENT '发放的优惠券id';

ALTER TABLE `xm_sign_activity_complete`
    ADD COLUMN `need_invite_nums` integer NOT NULL DEFAULT '0' COMMENT '需邀请好友数';

CREATE TABLE IF NOT EXISTS `xm_enterprise_wechat_follow_data`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `mew_id` integer NOT NULL DEFAULT '0' COMMENT '用户企业微信id',
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `unionid` varchar(100) NOT NULL DEFAULT '' COMMENT '唯一id',
    `follow_userid` varchar(50) NOT NULL DEFAULT '' COMMENT '绑定客服id',
    `remark` varchar(50) NOT NULL DEFAULT '' COMMENT '备注',
    `follow_time` integer NOT NULL DEFAULT '0' COMMENT '绑定时间',
    `tags` text NULL COMMENT '标签',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_mew_id` (`mew_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_unionid` (`unionid`),
    KEY `idx_follow_userid` (`follow_userid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业微信绑定表';