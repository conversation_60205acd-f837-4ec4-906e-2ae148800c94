ALTER TABLE `xm_promotion_goodscoupon_type`
    ADD COLUMN `privacy_status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '隐私状态 (0-内部券 1-公开券)';

CREATE TABLE `xm_multiple_discount` (
    `multiple_discount_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '多件折扣id',
    `multiple_discount_name` varchar(50) NOT NULL DEFAULT '' COMMENT '多件折扣名称',
    `discount` decimal(10,1) NOT NULL COMMENT '折扣',
    `max_fetch` int(11) NOT NULL DEFAULT '0' COMMENT '优惠订单数达上限时自动结束该活动,设置为0时，视为无上限',
    `at_least` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单品满多少件享受',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态（0未开始1进行中2已结束-1已关闭）',
    `use_scenario` tinyint(3) NOT NULL DEFAULT '1' COMMENT '使用场景（1全场通用 2指定分类 3指定商品,4指定商品（排除）,5指定专区）',
    `category_ids` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类ids或专区ids',
    `category_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类名称或专区名称',
    `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动开始时间',
    `over_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`multiple_discount_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='多件折扣表';

CREATE TABLE `xm_multiple_discount_goods` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `multiple_discount_id` int(11) NOT NULL DEFAULT '0' COMMENT '多件折扣id',
    `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT 'skuid',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关联状态(-1删除 1正常)',
    `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`),
    KEY `idx_multiple_discount_id` (`multiple_discount_id`),
    KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='多件折扣商品关联表';

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '多件折扣', 'MULTIPLE_DISCOUNT', 'PROMOTION_PLATFORM', 3, 'multipleDiscount://admin/multipleDiscount/lists', 0, 99, '', 0, '', '', 0);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '多件折扣详情', 'MULTIPLE_DISCOUNT_DETAIL', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/detail', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '添加多件折扣', 'MULTIPLE_DISCOUNT_ADD', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/add', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '编辑多件折扣', 'MULTIPLE_DISCOUNT_EDIT', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/edit', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '关闭多件折扣', 'MULTIPLE_DISCOUNT_CLOSE', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/close', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '删除多件折扣', 'MULTIPLE_DISCOUNT_DELETE', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/delete', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '多件折扣优惠记录', 'MULTIPLE_DISCOUNT_RECEIVE', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/receive', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '多件折扣全部优惠记录', 'MULTIPLE_DISCOUNT_RECEIVE_ALL', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/receiveAll', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'multipleDiscount', '多件折扣商品管理', 'MULTIPLE_DISCOUNT_GOODS', 'MULTIPLE_DISCOUNT', 4, 'multipleDiscount://admin/multipleDiscount/goods', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_addon` (`name`, `icon`, `title`, `description`, `status`, `author`, `version`, `content`, `create_time`) VALUES ('multipleDiscount', 'addon/multipleDiscount/icon.png', '多件折扣', '单个商品满件数折扣优惠', 1, '', '1.0', '', 1598323451);

CREATE TABLE `xm_order_multiple_discount` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单多件折扣快照id',
    `order_goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单商品id',
    `data` text NOT NULL COMMENT '快照数据',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_goods_id` (`order_goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单多件折扣商品快照表';

ALTER TABLE `xm_order_goods`
    ADD COLUMN `multiple_discount_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '多件折扣优惠价格' AFTER `goodscoupon_money`;

ALTER TABLE `xm_order`
    ADD COLUMN `multiple_discount_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '多件折扣优惠价格' AFTER `goodscoupon_money`;

ALTER TABLE `xm_multiple_discount`
    ADD COLUMN `is_use_goodscoupon` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可以叠加商品优惠券' AFTER `category_name`;

ALTER TABLE `xm_order_multiple_discount`
    ADD COLUMN `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '是否可以叠加商品优惠券' AFTER `id`;

ALTER TABLE `xm_order_multiple_discount`
    ADD COLUMN `multiple_discount_id` int(11) NOT NULL DEFAULT '0' COMMENT '多件折扣id' AFTER `order_id`;
