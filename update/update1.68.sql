/**
 * 供应商汇总菜单
 * czk
 */
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '供应商欠款汇总', 'SUPPLIER_FUNDS', 'ACCOUNT_ROOT', 2, 'admin/SupplierFunds/lists', 1, 14, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '供应商充值', 'SUPPLIER_RECHARGE', 'ACCOUNT_ROOT', 2, 'admin/SupplierFunds/recharge', 0, 14, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '供应商欠款明细', 'SUPPLIER_DETAIL', 'ACCOUNT_ROOT', 2, 'admin/SupplierFunds/showDetail', 0, 14, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '供应商充值记录', 'SUPPLIER_RECHARGELOG', 'ACCOUNT_ROOT', 2, 'admin/SupplierFunds/rechargeLog', 0, 14, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '店铺升级收入', 'SHOP_PAY_UPGRADE', 'ACCOUNT_ROOT', 2, 'admin/Funds/shopPayUpgrade', 1, 15, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '商品营业收入成本', 'GOODS_INCOME', 'ACCOUNT_ROOT', 2, 'admin/Funds/goodsIncome', 1, 16, '', 0, '', '', 1);
/**
 * czk
 * 开店支付回调更新
 */
alter table xm_member_open_shop_log add column `shop_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id';
alter table xm_member_open_shop_log add column `pay_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '支付时间';
/**
 * 商品营业收入成本表
 * czk
 * @type {[type]}
 */
CREATE TABLE `xm_supplier_revenue_cost` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT '供应商id',
  `supplier_name` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商名称',
  `total_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '商品销售收入',
  `delivery_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '快递费收入',
  `cost_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '商品成本（供应商价格）',
  `shop_brok` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '店主佣金',
  `president_brok` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '会长佣金',
  `profit` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '毛利',
  `setting_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '统计日期',
  `add_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建日期',
  `edit_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改日期',
  PRIMARY KEY (`id`),
  KEY `supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商营业收入成本表';

/**
 * [ENGINE 供应商充值]
 * czk
 * @type {[type]}
 */
CREATE TABLE `xm_supplier_recharge` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT '供应商id',
  `supplier_name` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商名称',
  `money` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '管理员id',
  `admin_name` varchar(50) NOT NULL DEFAULT '' COMMENT '管理员',
  `desc` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '充值时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商充值表';

/**
 * [ENGINE 供应商每日销售统计表]
 * czk
 * @type {[type]}
 */
CREATE TABLE `xm_supplier_sale_every_day` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT '供应商id',
  `supplier_name` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商名称',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `setting_time` int(11) NOT NULL DEFAULT '0' COMMENT '统计时间',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商每日销售统计表';