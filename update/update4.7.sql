CREATE TABLE `xm_order_goods_aftersale`
(
    `id`                          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `order_id`                    int(10) unsigned NOT NULL COMMENT 'order表id',
    `order_goods_id`              int(10) unsigned NOT NULL COMMENT 'order_goods表 id',
    `refund_num`                  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请售后数量',
    `upload_vouchers`             varchar(1000)         DEFAULT NULL COMMENT '上传凭证',
    `refund_recept_name`          varchar(32)           DEFAULT NULL COMMENT '买家收货名称（售后）',
    `refund_recept_phone`         varchar(20)           DEFAULT NULL COMMENT '买家收货手机（售后）',
    `refund_recept_province_id`   int(11) NOT NULL DEFAULT '0' COMMENT '购买人省id（售后）',
    `refund_recept_city_id`       int(11) NOT NULL DEFAULT '0' COMMENT '购买人市id（售后）',
    `refund_recept_district_id`   int(11) NOT NULL DEFAULT '0' COMMENT '购买人区县id（售后）',
    `refund_recept_short_address` varchar(255) NOT NULL DEFAULT '' COMMENT '购买人地址（售后）',
    `refund_recept_address`       varchar(200)          DEFAULT NULL COMMENT '买家收货地址（售后）',
    `refund_recept_delivery_company_id`       int(11)   NOT NULL  DEFAULT '0'  COMMENT '卖家重发物流公司id',
    `refund_recept_delivery_name` varchar(50)           DEFAULT NULL COMMENT '卖家重发物流公司',
    `refund_recept_delivery_no`   varchar(20)           DEFAULT NULL COMMENT '卖家重发物流编号',
    `apply_end_time`              int(10) unsigned DEFAULT '0' COMMENT '退/换货申请自动同意到期时间',
    `buyer_not_send_time`         int(11) DEFAULT '0' COMMENT '买家未发货到期时间',
    `seller_not_received_time`    int(11) DEFAULT '0' COMMENT '买家发货后卖家未处理到期时间',
    `create_time`                 int(11) unsigned NOT NULL COMMENT '创建时间',
    `send_again_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '卖家发货（换货）',
    PRIMARY KEY (`id`),
    KEY                           `order_id` (`order_id`),
    KEY                           `order_goods_id` (`order_goods_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

  ALTER TABLE `xm_order_goods_aftersale` ADD INDEX (`order_id`) , ADD INDEX (`order_goods_id`); 

  ALTER TABLE `xm_order_goods` 
  ADD COLUMN `refund_name` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '退货收货人名称' AFTER `refund_delivery_remark`, 
  ADD COLUMN `refund_phone` VARCHAR(20) DEFAULT '' NOT NULL COMMENT '退货收货人电话' AFTER `refund_name`;

ALTER TABLE `xm_supplier`
    ADD COLUMN `supplier_contact` varchar(255) NOT NULL DEFAULT 'admin' COMMENT '供应商联系人',
    ADD COLUMN `province_id` int(11) NOT NULL DEFAULT '0' COMMENT '省id',
  ADD COLUMN `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市id',
  ADD COLUMN `district_id` int(11) NOT NULL DEFAULT '0' COMMENT '区县id';

ALTER TABLE `xm_order_goods`
    MODIFY COLUMN `refund_status` int(11) NOT NULL DEFAULT '0' COMMENT '退款状态 -2用户撤销,1=已申请退款,2=已确认,3=已完成,4=等待买家发货,5=等待卖家收货,6=卖家确认收货,7=确认等待转账中(为处理第三方转账异步回调),-1=卖家拒绝退款,8-等待买家收货,9-卖家未收到货,10-超时未退货';
  ALTER TABLE `xm_order_goods` ADD COLUMN `refund_delivery_company_id` INT DEFAULT 0 NOT NULL COMMENT '退款物流公司id' AFTER `refund_real_money`; 


ALTER TABLE `xm_supply_chain_refund_log` ADD COLUMN `refund_type` VARCHAR(32) DEFAULT 'refund' NOT NULL COMMENT 'refund:退款  return:退货退款  exchange:换货' AFTER `refund_remark`; 
ALTER TABLE `xm_supply_chain_refund_log` CHANGE `refund_status` `refund_status` VARCHAR(20) CHARSET utf8 COLLATE utf8_general_ci DEFAULT 'apply' NOT NULL COMMENT '退款状态:cancel:驳回,apply:申请退款中,complete:通过,return_goods:买家退货,acept:卖家同意,overtime:买家超时未发货,not_received:卖家未收到货'; 