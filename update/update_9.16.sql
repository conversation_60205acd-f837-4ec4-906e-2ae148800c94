ALTER TABLE `xm_promotion_goodscoupon_goods`
    ADD COLUMN `sort` integer default 0 COMMENT '排序' AFTER `add_time`,
    ADD INDEX `idx_sort`(`sort`) USING BTREE;


CREATE TABLE IF NOT EXISTS `xm_realname_check_fail`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `auth_card_name` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
    `auth_card_no` varchar(18) NOT NULL DEFAULT '' COMMENT '身份证号',
    `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '手机号',
    `fail_check` integer NOT NULL DEFAULT 0 COMMENT '1失败检测 1身份证不符合格式，2 2要素, 3 3要素',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `idx_auth_card_name` (`auth_card_name`),
    <PERSON>EY `idx_auth_card_no` (`auth_card_no`),
    KEY `idx_mobile` (`mobile`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='实名认证失败表';