CREATE TABLE `xm_clue_users`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT,
    `mobile` varchar(20) NOT NULL DEFAULT '0' COMMENT '手机号',
    `source`             varchar(30) NOT NULL DEFAULT '' COMMENT '来源',
    `enterprise_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '企业微信管理员id',
    `is_reg`             tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已注册会员：0-否，1-是',
    `amount` decimal(10,2) NOT NULL COMMENT '消费金额',
    `create_time`        int(11) DEFAULT '0',
    `update_time`        int(11) DEFAULT '0',
    `add_wx_time`        int(11) DEFAULT '0' COMMENT '添加微信时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `mobile` (`mobile`),
    KEY                  `source` (`source`),
    KEY                  `enterprise_user_id` (`enterprise_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='线索公海用户';
CREATE TABLE `xm_clue_users_orders`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `clue_user_id`      int(11) NOT NULL DEFAULT '0' COMMENT '公海用户id',
    `order_no`          varchar(50)    NOT NULL DEFAULT '' COMMENT '订单编号',
    `source`            varchar(30)    NOT NULL DEFAULT '' COMMENT '来源',
    `goods_info`        varchar(255)   NOT NULL DEFAULT '' COMMENT '商品信息',
    `order_amount`      decimal(10, 2) NOT NULL COMMENT '订单金额',
    `goods_cate`        varchar(255)   NOT NULL DEFAULT '' COMMENT '商品类目',
    `order_create_time` int(11) NOT NULL DEFAULT '0' COMMENT '下单时间',
    `create_time`       int(11) NOT NULL DEFAULT '0',
    `update_time`       int(11) NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY                 `clue_user_id` (`clue_user_id`),
    KEY                 `order_no` (`order_no`),
    KEY                 `source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='线索公海用户导入的订单';
CREATE TABLE `xm_clue_users_tag`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `clue_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '线索公海id',
    `mobile` varchar(11) NOT NULL DEFAULT '0' COMMENT '手机号',
    `tag_id`       int(11) NOT NULL DEFAULT '0' COMMENT '标签id',
    `tag_name` varchar(255) NOT NULL DEFAULT '' COMMENT '标签名',
    `create_time`  int(11) NOT NULL DEFAULT '0',
    `update_time`  int(11) NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY            `clue_user_id` (`clue_user_id`),
    KEY            `tag_id` (`tag_id`),
    KEY            `mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='公海用户关联标签表';


CREATE TABLE `xm_enterprise_department`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `department_id` BIGINT UNSIGNED NOT NULL COMMENT '企业微信部门id',
    `name` VARCHAR(64) NOT NULL COMMENT '部门名称',
    `name_en` VARCHAR(64) NOT NULL COMMENT '部门english名称',
    `parent_department_id` BIGINT NOT NULL COMMENT '企业微信部门父id',
    `update_time` INT NOT NULL DEFAULT '0',
    `create_time` INT NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) ,
    KEY  `department_id` (`department_id`),
    KEY  `parent_department_id` (`parent_department_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='企业微信部门表';


CREATE TABLE `xm_enterprise_user`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(64) NOT NULL COMMENT '企业微信成员名称',
    `mobile` VARCHAR(20) NOT NULL COMMENT '企业微信成员手机号码',
    `avatar` VARCHAR(1000) COMMENT '企业微信成员头像',
    `userid` VARCHAR(100) NOT NULL COMMENT '企业微信成员id',
    `update_time` INT NOT NULL, `create_time` INT NOT NULL,
    PRIMARY KEY (`id`) ,
    UNIQUE INDEX (`userid`)
) ENGINE=INNODB CHARSET=utf8mb4 COMMENT='企业微信成员表';

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '线索管理', 'CLUE_MANAGE', 'SHOP_ROOT', 2, 'admin/clue/userList', 1, 256, '', 0, '', '', 1);
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '公海用户列表', 'CLUE_USER_LISTS', 'CLUE_MANAGE', 3, 'admin/clue/userList', 0, 0, '', 0, '', '', 1);
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '导入线索', 'CLUE_USER_LISTS_IMPORT', 'CLUE_MANAGE', 3, 'admin/clue/importOrder', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '用户详情/保存', 'CLUE_USER_LISTS_DETAIL', 'CLUE_USER_LISTS', 4, 'admin/clue/editUser', 1, 2, '', 0, '', '', 1);
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '用户订单管理', 'CLUE_USER_LISTS_ORDER', 'CLUE_USER_LISTS', 4, 'admin/clue/order', 1, 3, '', 0, '', '', 1);

 ALTER TABLE `xm_clue_users` ADD COLUMN `copy_times` INT DEFAULT 0 NOT NULL COMMENT '复制次数' AFTER `amount`; 