/**
 * 绑定银行卡和提现需要选择省、市
 * czk
 * 2020-12-17
 */
ALTER TABLE `xm_shop_cert` ADD COLUMN `prov_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在省份编码';
ALTER TABLE `xm_shop_cert` ADD COLUMN `area_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在城市编码';
ALTER TABLE `xm_shop_cert` ADD COLUMN `bank_branch_name` varchar(50) NOT NULL DEFAULT '' COMMENT '开户银行支行名称';
ALTER TABLE `xm_shop_withdraw` ADD COLUMN `prov_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在省份编码';
ALTER TABLE `xm_shop_withdraw` ADD COLUMN `area_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在城市编码';
ALTER TABLE `xm_shop_withdraw` ADD COLUMN `bank_branch_name` varchar(50) NOT NULL DEFAULT '' COMMENT '开户银行支行名称';
alter table xm_shop_cert modify column `bank_name` varchar(50) NOT NULL DEFAULT '' COMMENT '开户银行名称';