INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', 'leaguePoints', '兑换商城管理', 'EXCHANGE_MANAGEMENT', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/ExchangeManagement/lists', 1, 3, '', 0, '', '', 1);

-- 兑换物品表
CREATE TABLE IF NOT EXISTS `xm_league_exchange_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '兑换物名称',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '物品类型 1:优惠券',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联ID(优惠券ID等)',
  `points` int(11) NOT NULL DEFAULT 0 COMMENT '花费积分值',
  `exchange_count` int(11) NOT NULL DEFAULT 0 COMMENT '兑换次数',
  `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '累计消耗积分值',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 0:下架 1:正常',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分兑换商品表';

-- 兑换记录表
CREATE TABLE IF NOT EXISTS `xm_league_exchange_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '兑换商品ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
  `points` int(11) NOT NULL DEFAULT 0 COMMENT '消耗积分',
  `relation_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联ID(优惠券ID等)',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '物品类型 1:优惠券',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '兑换时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分兑换记录表';
