CREATE TABLE IF NOT EXISTS `xm_member_analysis_today`
(
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
`date` varchar(20) NOT NULL DEFAULT '' COMMENT '日期',
`pv` integer NOT NULL DEFAULT '0' COMMENT '访问量',
`uv` integer NOT NULL DEFAULT '0' COMMENT '活跃用户数',
`join_share_register_nums` integer NOT NULL DEFAULT '0' COMMENT '参与拉新人数',
`share_register_nums` integer NOT NULL DEFAULT '0' COMMENT '拉新人数',
`register_nums` integer NOT NULL DEFAULT '0' COMMENT '新增会员数',
`league_nums` integer NOT NULL DEFAULT '0' COMMENT '加盟人数',
`league_order_nums` integer NOT NULL DEFAULT '0' COMMENT '加盟订单数',
`order_nums` integer NOT NULL DEFAULT '0' COMMENT '订单数',
`order_money` decimal(10, 2) NOT NULL DEFAULT '0' COMMENT '订单总金额',
`buy_nums` integer NOT NULL DEFAULT '0' COMMENT '购物人数',
`unit_price` decimal(10, 2) NOT NULL DEFAULT '0' COMMENT '客单价',
`sign_nums` integer NOT NULL DEFAULT '0' COMMENT  '签到人数',
`create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
`update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_member_id` (`member_id`),
KEY `idx_date` (`date`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4 COMMENT ='每日用户数据分析';

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '用户数据分析', 'MEMBER_STAT', 'STAT_ROOT', 2, 'admin/Analyse/memberAnalysis', 1, 9, '', 0, '', '', 1);
