/**
 * 管理员增加真实姓名
 */
ALTER TABLE `xm_user`
ADD COLUMN `realname` varchar(50) NULL COMMENT '真实姓名';
/**
 * 管理员登录日志表
 */
CREATE TABLE `xm_user_action_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `module` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '模块：admin(后台)，shop(商家)',
  `action` varchar(50) NOT NULL DEFAULT 'login' COMMENT '动作，login(登录)',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id(xm_user.id)',
  `username` varchar(255) NOT NULL COMMENT '登录用户名',
  `ip` varchar(20) NOT NULL COMMENT '登录ip',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '登录设备信息',
  `params_json` varchar(2000) DEFAULT NULL COMMENT '所有请求参数的json',
  `is_success` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否成功，0失败，1成功',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;