ALTER TABLE `xm_goods_cart`
ADD COLUMN `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `num`,
ADD COLUMN `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间' AFTER `create_time`;

CREATE TABLE `xm_add_cart_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cart_id` int(11) NOT NULL DEFAULT '0' COMMENT '购物车id',
    `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员id',
    `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT 'sku_id',
    `num` int(11) NOT NULL DEFAULT '0' COMMENT '数量',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品数据统计';

ALTER TABLE `xm_clue_users`
ADD COLUMN `status` tinyint NULL DEFAULT 1 COMMENT '是否有效：0-否，1-是' AFTER `add_wx_time`;