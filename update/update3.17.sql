ALTER TABLE `xm_main_stat`
ADD COLUMN `total_user` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前总用户数' AFTER `edit_time`;

CREATE TABLE `xm_goods_browse_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `site_id` int(10) NOT NULL DEFAULT '0' COMMENT '站点id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '浏览人',
    `goods_id` int(10) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT 'sku_id',
    `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类',
    `category_id_1` int(11) NOT NULL DEFAULT '0' COMMENT '商品一级分类id',
    `category_id_2` int(11) NOT NULL DEFAULT '0' COMMENT '商品二级分类id',
    `category_id_3` int(11) NOT NULL DEFAULT '0' COMMENT '商品三级分类id',
    `browse_date` date NOT NULL COMMENT '浏览时间',
    `create_time` int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_ns_goods_browse_member_id` (`member_id`) USING BTREE,
    KEY `IDX_ns_goods_browse_site_id` (`site_id`) USING BTREE,
    KEY `IDX_ns_goods_browse_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品浏览历史日志';