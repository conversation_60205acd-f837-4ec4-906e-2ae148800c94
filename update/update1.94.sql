
 -- 菜单栏 -chj
 INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '供应链选品库', 'SUPPLY_GOODS_WAREHOUSE', 'GOODS_ROOT', '2', 'admin/supplyGoods/lists', '1', '8', '', '0', '', '', '1');


 INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '已选商品', 'SUPPLY_GOODS_SELECT', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/lists', '1', '1', '', '0', '', '', '1'),
('admin', '', '已选商品详情', 'SUPPLY_GOODS_INFO', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/show', '0', '1', '', '0', '', '', '1'),
('admin', '', '已选商品详情', 'SUPPLY_GOODS_EDIT', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/editGoods', '0', '1', '', '0', '', '', '1'),
('admin', '', '异常商品', 'SUPPLY_GOODS_ABNORMAL', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/abnormal', '0', '1', '', '0', '', '', '1');

 INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '待选商品', 'SUPPLY_GOODS_NOT_SELECT', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/not_lists', '1', '2', '', '0', '', '', '1'),
('admin', '', '待选商品详情', 'SUPPLY_GOODS_WAITING_INFO', 'SUPPLY_GOODS_WAREHOUSE', '4', 'admin/supplyGoods/waitingInfo', '0', '2', '', '0', '', '', '1');

 CREATE TABLE `xm_supply_chain_goods`
 (
     `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT '自增id',
     `pro_id`        int(11)      NOT NULL COMMENT '产品id',
     `pro_no`        varchar(100) NOT NULL COMMENT '产品唯一标识',
     `title`         varchar(255) NOT NULL COMMENT '产品标题',
     `cid`           int(11)      NOT NULL COMMENT '分类id',
     `brand_cid`     int(11)      NOT NULL COMMENT '品牌id',
     `warehouse_cid` int(11)      NOT NULL COMMENT '仓库id',
     `supply_id`     int(11)      NOT NULL COMMENT '供货商id',
     `supply_name`   varchar(100) DEFAULT NULL COMMENT '供货商名称',
     `freight_id`    int(11)      NOT NULL COMMENT '邮费模板id（-1免邮）',
     `sale_num`      int(11)      NOT NULL COMMENT '起售数量',
     `total_stock`   int(11)      NOT NULL COMMENT '总库存',
     `main_img`      varchar(255) DEFAULT NULL COMMENT '主图',
     `isout`         int(11)      NOT NULL COMMENT '上下架1-上架（默认）0-下架',
     `uptime`        datetime     DEFAULT NULL COMMENT '上架时间',
     `unit`          varchar(100) DEFAULT NULL COMMENT '单位',
     `sku`           longtext,
     `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     UNIQUE KEY `pro_id` (`pro_id`),
     KEY `pro_no` (`pro_no`),
     KEY `cid` (`cid`),
     KEY `brand_cid` (`brand_cid`),
     KEY `isout` (`isout`)
 ) ENGINE = MyISAM
   DEFAULT CHARSET = utf8mb4 COMMENT ='供应链商品';

 CREATE TABLE `xm_goods_abnormal`
 (
     `abnormal_id`   int(11)             NOT NULL AUTO_INCREMENT COMMENT '异常ID',
     `type`          tinyint(3) unsigned NOT NULL COMMENT '异常类型：1.价格变动，2.库存变动，3.商品下架',
     `goods_id`      int(11)             NOT NULL COMMENT '商品ID',
     `msg`           varchar(200)        NOT NULL COMMENT '异常消息',
     `abnormal_time` timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '异常时间',
     `delete_time`   int(11)                      DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`abnormal_id`),
     KEY `goods_id` (`goods_id`)
 ) ENGINE = InnoDB
   DEFAULT CHARSET = utf8mb4 COMMENT ='商品异常记录表';

 CREATE TABLE `xm_supply_chain_goods_relation`
 (
     `goods_relation_id` int(11)   NOT NULL AUTO_INCREMENT COMMENT '商品关联ID',
     `pro_id`            int(11)   NOT NULL COMMENT '供应链商品ID',
     `goods_id`          int(11)   NOT NULL COMMENT '本地商品ID',
     `relation_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
     `delete_time`       int(11)            DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`goods_relation_id`),
     UNIQUE KEY `pro_id` (`pro_id`),
     UNIQUE KEY `goods_id` (`goods_id`)
 ) ENGINE = InnoDB
   DEFAULT CHARSET = utf8mb4 COMMENT ='供应链商品关联表';


ALTER TABLE `xm_goods_abnormal`
ADD COLUMN `pro_id` int(11) NOT NULL COMMENT '供应链产品id',
ADD INDEX `pro_id`(`pro_id`) USING BTREE;



CREATE TABLE `xm_supply_chain_order` 
( 
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
`order_id` INT UNSIGNED NOT NULL COMMENT 'xm_order主键', 
`supply_order_no` VARCHAR(32) NULL COMMENT '供应链订单号（取第一个）', 
`sync_status` TINYINT NOT NULL COMMENT '-1.同步失败 0.未同步 1.同步中 2.同步成功', 
`sync_text` VARCHAR(64) DEFAULT '' COMMENT '状态说明', 
`sync_goods_json` TEXT COMMENT '同步商品json', 
`sync_success_time` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '同步成功时间', 
`create_time` INT UNSIGNED NOT NULL COMMENT '创建时间', 
PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8mb4 COMMENT ='供应链订单关联表';  

 ALTER TABLE  `xm_supply_chain_order` ADD INDEX (`order_id`); 

-- ALTER TABLE `xm_supply_chain_order` CHANGE `supply_order_no` `supply_order_json` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '供应链返回信息'; 
-- ALTER TABLE `xm_supply_chain_order` ADD COLUMN `supply_refund_status` TINYINT DEFAULT 0 NOT NULL COMMENT '-1.退款失败  0.没有退款  1.退款中  2.退款成功（供应链的状态）' AFTER `sync_success_time`, 
-- ADD COLUMN `supply_refund_sku_no` VARCHAR(100) NULL COMMENT '退款商品的sku编号，为空则是全部退款' AFTER `supply_refund_status`; 


/**
 * 供应链商品退款日志
 * 2021/01/19
 * wjj
 */
CREATE TABLE `xm_supply_chain_refund_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单商品id',
  `refund_status` varchar(10) NOT NULL DEFAULT 'apply' COMMENT '退款状态:cancel:驳回,apply:申请退款中,complete:通过',
  `result_code` int(11) NOT NULL DEFAULT '0' COMMENT '结果状态',
  `result` varchar(255) NOT NULL DEFAULT '' COMMENT '请求结果',
  `request_param` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数',
  `source` varchar(15) NOT NULL DEFAULT 'self' COMMENT '来源（supplychain:供应链,self:自有）',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `order_goods_id` (`order_goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应链订单申请退款操作表';

ALTER TABLE `xm_supply_chain_order` ADD INDEX (`order_id`); 
/**
 * 发货回调记录
*/
CREATE TABLE `xm_supply_chain_delivery_log` ( 
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`data` TEXT NOT NULL, 
`order_no` VARCHAR(50) NOT NULL COMMENT '订单号', 
`create_time` INT NOT NULL COMMENT '回调时间', PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8 COMMENT = '发货回调'; 

 ALTER TABLE `xm_supply_chain_delivery_log` ADD COLUMN `log_text` VARCHAR(50) NULL COMMENT '说明' AFTER `order_no`;

 ALTER TABLE `xm_youpin`.`xm_goods`
     MODIFY COLUMN `goods_spec_format` varchar(10000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品规格格式' AFTER `shipping_template`;

 ALTER TABLE `xm_goods_sku`
     MODIFY COLUMN `goods_spec_format` varchar(10000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品规格格式' AFTER `shipping_template`;

# 20210222 添加字段提供排序
 ALTER TABLE `xm_youpin`.`xm_supply_chain_goods`
     ADD COLUMN `orgin_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '原价' AFTER `unit`,
     ADD COLUMN `supply_price`         decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '供应链价格' AFTER `unit`;

-- 供应链返回说明
ALTER TABLE `xm_youpin`.`xm_supply_chain_refund_log` ADD COLUMN `refund_remark` VARCHAR(255) NULL COMMENT '说明' AFTER `source`; 