-- 线上临时任务，2020-11-17 00:00:00之前的先迈店主都要在柚品更新为vip店主
-- update xm_shop s1
--     left join xm_shop s2
-- on s1.site_id = s2.site_id
--     set s1.is_vip = 1,s1.vip_level = 1,s1.vip_open_time = s2.create_time,s1.vip_expired_time=s2.create_time+365*86400
-- where s1.xm_shop_id > 0 and s1.is_vip = 0 and s1.create_time > ********** ;

-- 线上柚品member表已确定手机号没重复
-- 迁单：迁出做注销处理：shop表，username、mobile字段加'_'，shop_status 设置 0 已过期
-- 迁单：迁入做注销处理：shop表，迁出的账号account、shop_sales、account_withdraw、追加到迁入账号

-- 开始事务
-- 开启存储过程

DELIMITER $$
CREATE PROCEDURE `shopDeDuplication`(IN outSiteId INT,IN inSiteId INT,IN mobile VARCHAR(11) )
BEGIN
DECLARE code CHAR(5) DEFAULT '00000';
  DECLARE msg TEXT;
  DECLARE rowss INT;
  DECLARE result TEXT;
DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
BEGIN
      -- 获取异常code,异常信息
GET DIAGNOSTICS CONDITION 1
    code = RETURNED_SQLSTATE, msg = MESSAGE_TEXT;
END;
START TRANSACTION;
SET @moveOutSiteId = outSiteId;
SET @moveInSiteId = inSiteId;
SET @correctMobile = mobile;
-- 1.迁移流水数据
update xm_shop_member set site_id = @moveInSiteId where site_id = @moveOutSiteId;   -- 锁粉
update xm_shop_member_change_log set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;   -- 锁粉记录
update xm_shop_member_log set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;
update xm_shop_account set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_shop_finish_task_log set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;  -- 店主任务完成记录表,跟shop表account_withdraw字段、shop_youli_account表有关
update xm_shop_goods_hot_words set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;
update xm_shop_order set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_shop_youli_account set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_shop_withdraw set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_order set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_order_goods set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_pintuan_order set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_youli_order set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;
update xm_shop_goods_day_browse set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_goods_browse_log set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_goods_browse set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_league_task_goods_day_browse set share_site_id = @moveInSiteId where share_site_id = @moveOutSiteId;
update xm_shop_goods_day_sale set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_site_diy_view set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_league_task set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_express_delivery_package set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_league_goods_share set share_site_id = @moveInSiteId where share_site_id = @moveOutSiteId;
update xm_pay_refund set site_id = @moveInSiteId where site_id = @moveOutSiteId;
update xm_members_order_recommend set shop_id = @moveInSiteId where shop_id = @moveOutSiteId;

-- 2.迁移账户数据
update xm_shop s1,xm_shop s2
set s1.account = s1.account+s2.account, s1.shop_sales = s1.shop_sales+s2.shop_sales, s1.account_withdraw = s1.account_withdraw+s2.account_withdraw,
    s1.youli_withdraw = s1.youli_withdraw+s2.youli_withdraw, s1.youli_no_withdraw = s1.youli_no_withdraw+s2.youli_no_withdraw,
    s1.residue_shop_num = s1.residue_shop_num+s2.residue_shop_num,
    s1.mobile  = @correctMobile,s1.username  = @correctMobile
where s1.site_id = @moveInSiteId and s2.site_id = @moveOutSiteId;
update xm_user set username = @correctMobile where site_id = @moveInSiteId;

-- xm_stat_shop更新
-- 同一天
update xm_stat_shop ss1,xm_stat_shop ss2
set ss1.order_total = ss1.order_total+ss2.order_total, ss1.shipping_total = ss1.shipping_total+ss2.shipping_total, ss1.refund_total = ss1.refund_total+ss2.refund_total,
    ss1.order_pay_count = ss1.order_pay_count+ss2.order_pay_count, ss1.goods_pay_count = ss1.goods_pay_count+ss2.goods_pay_count,
    ss1.shop_money = ss1.shop_money+ss2.shop_money, ss1.platform_money = ss1.platform_money+ss2.platform_money,
    ss1.collect_shop = ss1.collect_shop+ss2.collect_shop, ss1.collect_goods = ss1.collect_goods+ss2.collect_goods,
    ss1.visit_count = ss1.visit_count+ss2.visit_count, ss1.order_count = ss1.order_count+ss2.order_count,
    ss1.goods_count = ss1.goods_count+ss2.goods_count, ss1.add_goods_count = ss1.add_goods_count+ss2.add_goods_count,
    ss1.member_count = ss1.member_count+ss2.member_count
where ss1.site_id = @moveInSiteId and ss2.site_id = @moveOutSiteId and ss1.day_time = ss2.day_time;
-- where ss1.site_id = @moveInSiteId and ss2.site_id = @moveOutSiteId and ss1.year = ss2.year and ss1.month = ss2.month and ss1.day = ss2.day;

-- xm_stat_shop更新
-- 不同一天，直接迁移,并删除迁出
UPDATE xm_stat_shop
SET site_id = @moveInSiteId
WHERE id IN (
    SELECT id
    FROM
          (
              SELECT *
              FROM (
                       SELECT id, site_id, CONCAT(`year`, `month`, `day`) AS `date`
                       FROM xm_stat_shop
                       WHERE site_id = @moveOutSiteId) ss1
              WHERE `date` NOT IN
                    (SELECT CONCAT(`year`, `month`, `day`) AS `date` FROM xm_stat_shop WHERE site_id = @moveInSiteId)
          ) ss3

);
delete from xm_stat_shop where site_id = @moveOutSiteId;

-- 3.执行注销迁出账号
update xm_shop set username = CONCAT(`username`,'_'), mobile = CONCAT(`username`), shop_status = 0, member_id = 0, xm_shop_id = 0, xm_pid = 0 where site_id = @moveOutSiteId;
update xm_user set username = CONCAT(`username`,'_'), `status` = 0 where site_id = @moveOutSiteId and app_module = 'shop';

IF code = '00000' THEN
    GET DIAGNOSTICS rowss = ROW_COUNT;
    SET result = CONCAT('insert succeeded, row count = ',rowss);
    COMMIT;
ELSE
    -- 复制异常code，异常信息
    SET result = CONCAT('insert failed, error = ',code,', message = ',msg);
    -- 输入执行结果
    ROLLBACK;
    SELECT result;
END IF;
END$$
DELIMITER ;
-- call shopDeDuplication();

-- 存储过程结束
-- 提交事务

-- 4.根据手机号，修正xm_shop\xm_user的member_id，shop绑定member
DELIMITER $$
CREATE PROCEDURE `shopBindMember`(IN mobile VARCHAR(11) )
BEGIN
SET @correctMobile = mobile;
update xm_user u , xm_member m set u.member_id = m.member_id where u.username = @correctMobile and m.mobile = @correctMobile;
update xm_shop s , xm_member m set s.member_id = m.member_id where s.mobile = @correctMobile and m.mobile = @correctMobile;
END$$
DELIMITER ;


上线流程：
1.先执行以上两个存储过程
2.根据之前查出的线上重复数据，迁单：手动第一个存储过程
特殊处理例子：
-- php think shopDeDuplication 117970 153845 ***********
-- 因为线上是实时变动的，所以执行4添加唯一索引的时候报错，就找出重复的店铺
-- 查看柚品店铺名相同
-- SELECT
--     s.site_id,
--     username,
--     mobile,
--     xm_shop_id,
--     member_id,
--     account,
--     FROM_UNIXTIME(s.create_time, '%Y-%m-%d %H:%i:%s') as ctime,
--     SUM( ss.order_total ) AS order_total,
--     SUM( ss.shipping_total ) AS shipping_total,
--     SUM( ss.order_pay_count ) AS order_pay_count,
--     SUM( ss.order_count ) AS order_count,
--     SUM( ss.goods_count ) AS goods_count
-- FROM
--     xm_shop s
--         LEFT JOIN xm_stat_shop AS ss ON s.site_id = ss.site_id
-- WHERE
--         username IN ( SELECT username FROM xm_shop WHERE username <> '' GROUP BY username HAVING count(*)> 1 )
--    OR xm_shop_id IN ( SELECT xm_shop_id FROM xm_shop WHERE username <> '' AND xm_shop_id > 0 GROUP BY xm_shop_id HAVING count(*)> 1 )
--    OR mobile IN ( SELECT mobile FROM xm_shop WHERE username <> '' AND mobile <> '' GROUP BY mobile HAVING count(*)> 1 )
-- GROUP BY
--     s.site_id
-- ORDER BY
--     s.xm_shop_id;

-- 查询重复user数据
-- SELECT
--     uid,
--     site_id,
--     create_time,
--     username,
--     app_module,
--     FROM_UNIXTIME( create_time, '%Y-%m-%d %H:%i:%s' ) AS ctime
-- FROM
--     xm_user
-- WHERE
--         username IN ( SELECT username FROM xm_user WHERE app_module = 'shop' GROUP BY username HAVING count(*) > 1 );

-- 3.运行第二个存储过程（shopBindMember绑定用户）：php think ShopBindMember 手机号
4.去重完后才能执行添加唯一索引，否则加不了唯一索引
update xm_user u left join xm_shop s on u.site_id = s.site_id set u.username = s.mobile where s.mobile <> '' and app_module = 'shop';
-- -- 添加唯一索引
ALTER TABLE `xm_shop`
    ADD UNIQUE INDEX(`username`),
    ADD UNIQUE INDEX(`mobile`);
ALTER TABLE `xm_member` ADD UNIQUE INDEX(`mobile`);

ALTER TABLE `xm_user`
    ADD UNIQUE INDEX(`app_module`, `username`);

update xm_user u left join xm_member m on u.username = m.mobile set u.member_id = m.member_id where m.mobile <> '' and u.app_module = 'shop';
update xm_shop s left join xm_member m on s.mobile = m.mobile set s.member_id = m.member_id;

-- 复制sync表，以便出错时数据能返回
CREATE  TABLE xm_yp_sync_users_copy01 (LIKE xm_yp_sync_users);
INSERT INTO xm_yp_sync_users_copy01 SELECT * FROM xm_yp_sync_users;

-- 复制member_recommend表，以便出错时数据能返回
CREATE  TABLE xm_member_recommend_copy01 (LIKE xm_member_recommend);
INSERT INTO xm_member_recommend_copy01 SELECT * FROM xm_member_recommend;

truncate xm_yp_sync_users;
-- 看鸿源是否处理完该表重复数据
ALTER TABLE `xm_yp_sync_users`
    ADD UNIQUE INDEX(`yp_mid`),
    ADD UNIQUE INDEX(`uni_mobile`),
    ADD UNIQUE INDEX(`xm_uid`);
-- 执行鸿源脚本



-- 特殊处理：