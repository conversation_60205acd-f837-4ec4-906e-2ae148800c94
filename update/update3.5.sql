-- 开店申请扩展表
CREATE TABLE `xm_member_apply_shop_extend` ( 
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `member_apply_shop_id` INT UNSIGNED NOT NULL COMMENT 'member_apply_shop表id', 
    `province` VARCHAR(32) COMMENT '省', 
    `city` VARCHAR(32) COMMENT '市', 
    `area` VARCHAR(32) COMMENT '区', 
    `profession` VARCHAR(32) COMMENT '职业', 
    `skill` VARCHAR(32) COMMENT '特长', 
    `society_experience` VARCHAR(64) COMMENT '社群经验', 
    `society_num` INT UNSIGNED DEFAULT 0 COMMENT '社群数量', 
    `images` TEXT COMMENT '图片（多张）', 
    `create_time` INT UNSIGNED COMMENT '创建时间', 
    PRIMARY KEY (`id`) ) ENGINE=INNODB CHARSET=utf8mb4 COMMENT "开店申请扩展表"; 