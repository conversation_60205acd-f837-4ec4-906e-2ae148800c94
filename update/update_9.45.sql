CREATE TABLE IF NOT EXISTS `xm_customer_service_feedback`
(
    `feedback_id` int(11) NOT NULL AUTO_INCREMENT,
    `reception_id` varchar(20) NOT NULL default '' COMMENT '工单id',
    `evaluate` tinyint NOT NULL default 0 COMMENT '评价',
    `reception_user_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '接待客服UserId',
    `msgid` varchar(32) NOT NULL DEFAULT '' COMMENT '消息id',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`feedback_id`),
    KEY `idx_reception_id` (`reception_id`),
    KEY `idx_reception_user_id` (`reception_user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服反馈表';
INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('工单管理', 'SERVICE_ORDER_LIST', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/serviceOrderList',259);
UPDATE `xm_menu` SET `sort` = 258 WHERE `name` = 'RECEPTION_RECORD_LIST';
UPDATE `xm_menu` SET `sort` = 260 WHERE `name` = 'TYPE_MANAGE';
UPDATE `xm_menu` SET `sort` = 261 WHERE `name` = 'DISCOURSE_MANAGE';
UPDATE `xm_menu` SET `sort` = 262 WHERE `name` = 'CUSTOMER_SERVICE_CONFIGURATION';

ALTER TABLE `xm_customer_service_order`
    MODIFY COLUMN `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态，1跟进中，2待跟进，10已完结' AFTER `external_userid`;

ALTER TABLE `xm_customer_service_order`
ADD COLUMN `nickname` varchar(200) NOT NULL DEFAULT '' COMMENT '昵称(不与接待记录关联的工单写入)' AFTER `end_time`,
ADD COLUMN `xm_user_id` int(11) NOT NULL DEFAULT "0" COMMENT '先迈用户id(不与接待记录关联的工单写入)' AFTER `nickname`,
ADD COLUMN `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '手机号(不与接待记录关联的工单写入)' AFTER `xm_user_id`,
ADD COLUMN `question_type_id` int(11) NOT NULL DEFAULT "0" COMMENT '客服类型id(不与接待记录关联的工单写入)' AFTER `mobile`,
ADD COLUMN `question_title` varchar(200) NOT NULL DEFAULT '' COMMENT '客服类型(不与接待记录关联的工单写入)' AFTER `question_type_id`;