CREATE TABLE IF NOT EXISTS `xm_combo`
(
    `combo_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '套餐名称',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
    `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
    `sale_nums` int(11) NOT NULL DEFAULT '0' COMMENT '销量',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`combo_id`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4 COMMENT ='套餐组合表';

CREATE TABLE IF NOT EXISTS `xm_combo_sku`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `combo_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐id',
    `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品sku_id',
    `combo_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐单价',
    `buy_nums` int(11) NOT NULL DEFAULT '0' COMMENT '必须购买的数量',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_combo_id` (`combo_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_sku_id` (`sku_id`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4 COMMENT ='套餐组合表';

CREATE TABLE IF NOT EXISTS `xm_combo_order`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `combo_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐id',
    `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_combo_id` (`combo_id`),
    KEY `idx_order_id` (`order_id`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4 COMMENT ='套餐组合表';

CREATE TABLE IF NOT EXISTS `xm_combo_sale_record`
(
    `combo_sale_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `combo_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐id',
    `combo_name` varchar(50) NOT NULL DEFAULT '' COMMENT '套餐名称',
    `sale_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '销售金额',
    `cheap_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
    `pay_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
    `refund_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
    `combo_info` text DEFAULT NULL COMMENT '套餐快照',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`combo_sale_id`),
    KEY `idx_combo_id` (`combo_id`),
    KEY `idx_member_id` (`member_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='套餐销售记录';

CREATE TABLE IF NOT EXISTS `xm_combo_sale_order`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `combo_sale_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐销售id',
    `combo_id` int(11) NOT NULL DEFAULT '0' COMMENT '套餐id',
    `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_combo_id` (`combo_id`),
    KEY `idx_order_id` (`order_id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='套餐销售订单关联表';

ALTER TABLE `xm_order`
    ADD COLUMN `combo_cheap_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐优惠金额' AFTER `multiple_discount_money`;
ALTER TABLE `xm_order_goods`
    ADD COLUMN `combo_cheap_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐优惠金额' AFTER `multiple_discount_money`;
ALTER TABLE `xm_combo_sale_record`
    ADD COLUMN `pay_status` int(11) NOT NULL DEFAULT '0' COMMENT '支付状态' AFTER `combo_name`;

ALTER TABLE `xm_combo`
    ADD COLUMN `combo_min_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐组合最低价' AFTER `name`,
    ADD COLUMN `combo_max_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐组合最高价' AFTER `name`,
    ADD COLUMN `combo_default_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐默认价' AFTER `name`,
    ADD COLUMN `combo_sale_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐组合原售价' AFTER `name`,
    ADD COLUMN `combo_cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐成本价' AFTER `name`;

ALTER TABLE `xm_combo_sale_record`
    ADD COLUMN `refund_nums` int(11) NOT NULL DEFAULT '0' COMMENT '退款订单数' AFTER `refund_price`;



