CREATE TABLE IF NOT EXISTS `xm_league_task_point_config`
(
    `config_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `cid` int NOT NULL DEFAULT 0 COMMENT '类目id，为0则全类目默认配置',
    `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
    `task_use_point` int NOT NULL DEFAULT 0 COMMENT '任务消耗积分',
    `rules` text NULL COMMENT '积分获取规则',
    `rule_content` text NULL COMMENT '规则说明',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`config_id`),
    KEY `idx_cid` (`cid`),
    KEY `idx_league_task_key` (`league_task_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='加盟任务积分配置';

CREATE TABLE IF NOT EXISTS `xm_league_task_point_browse`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
    `browse_type` varchar(20) NOT NULL DEFAULT '' COMMENT '浏览类型，child直推浏览，shop_fans店铺粉丝浏览',
    `browse_nums` int NOT NULL DEFAULT 0 COMMENT '浏览数',
    `browse_ids` text NULL  COMMENT '浏览记录id，对应goods_browse_log表id',
    `config_id` int NOT NULL DEFAULT 0 COMMENT '加盟任务积分配置id，对应xm_league_task_point_config表id',
    `is_complete` int NOT NULL DEFAULT 0 COMMENT '是否已完成积分结算,0未完成，1已完成',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_browse_type` (`browse_type`),
    KEY `idx_config_id` (`config_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='加盟任务积分浏览结算表';

CREATE TABLE IF NOT EXISTS `xm_member_league_task_point_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
    `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
    `point_type` varchar(30) NOT NULL DEFAULT '' COMMENT '积分方式',
    `point` int NOT NULL DEFAULT 0 COMMENT '获取(扣减)积分',
    `relation_key` varchar(50) NOT NULL DEFAULT '' COMMENT '关联键',
    `relation_value` varchar(1000) NOT NULL DEFAULT '' COMMENT '关联值',
    `surplus_point` int NOT NULL DEFAULT 0 COMMENT '剩余积分',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_point_type` (`point_type`),
    KEY `idx_relation_key` (`relation_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户领取加盟积分记录表';

CREATE TABLE IF NOT EXISTS `xm_league_task_point_complete_record`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
    `user_league_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户加盟任务id',
    `league_name` varchar(255) NOT NULL DEFAULT '' COMMENT '加盟任务名称',
    `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
    `status` int NOT NULL DEFAULT 0 COMMENT '状态（0：等待完成，1积分自动完成，-1已取消（正常完成或取消））',
    `league_data` text NULL COMMENT '加盟数据',
    `point` int NOT NULL DEFAULT 0 COMMENT '完成积分数',
    `complete_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_user_league_id` (`user_league_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户加盟任务积分完成表';

CREATE TABLE IF NOT EXISTS `xm_league_task_point_join_member`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
    `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
    `point` int NOT NULL DEFAULT 0 COMMENT '可使用积分',
    `enable` int NOT NULL DEFAULT 0 COMMENT '是否开放，0不开放，1开放',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_league_task_key` (`league_task_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='加盟任务积分参与表';

ALTER TABLE `xm_member_league_task_point_log`
    ADD COLUMN `status` tinyint COMMENT '状态，（0进行中，1已完成，-1已取消）' AFTER `relation_value`;


INSERT INTO `xm_menu`(`addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `is_control`) VALUES ('leaguePoints', '加盟贡献值活动', 'PROMOTION_LEAGUEPOINTS', 'PROMOTION_ROOT', 3, 'leaguePoints://admin/JoinUsers/lists', 1, 202, 1);

INSERT INTO `xm_menu`(`addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `is_control`) VALUES ('leaguePoints', '活动用户管理', 'PROMOTION_LEAGUEPOINTS_JOINUSERS', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/JoinUsers/lists', 1, 1, 1);

INSERT INTO `xm_menu`(`addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `is_control`) VALUES ('leaguePoints', '贡献值记录明细', 'PROMOTION_LEAGUEPOINTS_POINTSRECORD', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/PointsRecord/lists', 1, 1, 1);

INSERT INTO `xm_menu`(`addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `is_control`) VALUES ('leaguePoints', '贡献值规则配置', 'PROMOTION_LEAGUEPOINTS_POINTSRULES', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/PointsRules/config', 1, 1, 1);
