CREATE TABLE IF NOT EXISTS `xm_customer_service_order`
(
    `cs_order_id` int(11) NOT NULL AUTO_INCREMENT,
    `number` varchar(20) NOT NULL default '' COMMENT '工单编号',
    `kfid` varchar(100) NOT NULL default '' COMMENT '客服id',
    `external_userid` varchar(100) NOT NULL default '' COMMENT '外部联系人id',
    `status` integer NOT NULL default 0 COMMENT '状态，1跟进中，10已完结',
    `end_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '结束跟进时间',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`cs_order_id`),
    KEY `idx_number` (`number`),
    KEY `idx_kfid` (`kfid`),
    KEY `idx_external_userid` (`external_userid`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服工单表';

CREATE TABLE IF NOT EXISTS `xm_customer_service_order_record`
(
    `record_id` int(11) NOT NULL AUTO_INCREMENT,
    `cs_order_id` varchar(20) NOT NULL default '' COMMENT '工单id',
    `create_user_id` varchar(100) NOT NULL default '' COMMENT '创建的客服userid',
    `create_user` varchar(100) NOT NULL default '' COMMENT '创建的客服名称',
    `content` text NULL  COMMENT '内容',
    `files` text NULL  COMMENT '附件',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`record_id`),
    KEY `idx_cs_order_id` (`cs_order_id`),
    KEY `idx_create_user_id` (`create_user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服工单跟进记录表';

ALTER TABLE `xm_customer_reception`
    ADD COLUMN `cs_order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '工单id' after `source`;