CREATE TABLE IF NOT EXISTS `xm_customer_question_type`
(
    `question_type_id` int(11) NOT NULL AUTO_INCREMENT,
    `question_text` varchar(200) NOT NULL DEFAULT '' COMMENT '',
    `level` int unsigned NOT NULL DEFAULT 1 COMMENT '级别',
    `parent_id` int NOT NULL DEFAULT 0 COMMENT '父级id',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`question_type_id`),
    KEY `idx_level` (`level`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服问题类型表';

CREATE TABLE IF NOT EXISTS `xm_customer_speak`
(
    `speak_id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(400) NOT NULL DEFAULT '' COMMENT '标题',
    `content` varchar(2000) NOT NULL DEFAULT '' COMMENT '内容',
    `speak_cate_id` int NOT NULL DEFAULT 0 COMMENT '话术分类id',
    `use_nums` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`speak_id`),
    KEY `idx_title` (`title`),
    KEY `idx_speak_cate_id` (`speak_cate_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服话术表';

CREATE TABLE IF NOT EXISTS `xm_customer_speak_cate`
(
    `speak_cate_id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(400) NOT NULL DEFAULT '' COMMENT '分类名',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`speak_cate_id`),
    KEY `idx_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服话术分类表';

INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `sort`) VALUES ('客服管理', 'SERVICE_MANAGE', 'SHOP_ROOT', 2, 1, 257);
INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('接待记录', 'RECEPTION_RECORD_LIST', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/receptionRecordList',257);
INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('类型管理', 'TYPE_MANAGE', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/typeManage', 257);
INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('话术管理', 'DISCOURSE_MANAGE', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/discourseManage', 257);

CREATE TABLE IF NOT EXISTS `xm_customer_reception`
(
    `reception_id` int(11) NOT NULL AUTO_INCREMENT,
    `kfid` varchar(100) NOT NULL  COMMENT '客服id',
    `external_userid` varchar(100) NOT NULL  COMMENT '外部联系人id',
    `scene` varchar(100) NOT NULL default '' COMMENT '场景值',
    `scene_param` varchar(300) NOT NULL default '' COMMENT '场景参数密文',
    `nickname` varchar(200) NOT NULL default '' COMMENT '昵称',
    `xm_user_id` int NOT NULL default 0 COMMENT '先迈用户id',
    `mobile` varchar(11) NOT NULL default '' COMMENT '手机号',
    `status` int NOT NULL default 0 COMMENT '企微客服会话状态',
    `link_time` int NOT NULL default 0 COMMENT '接入时间',
    `reception_time` int NOT NULL default 0 COMMENT '接待时间',
    `reception_user_id`  varchar(100) NOT NULL default '' '接待客服UserId',
    `end_time` int NOT NULL default 0 COMMENT '结束会话时间',
    `question_type_id` int NOT NULL default 0 COMMENT '客服类型id',
    `question_title` varchar(200) NOT NULL default '' COMMENT '客服类型',
    `question_content` varchar(2000) NOT NULL default '' COMMENT '备注内容',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`reception_id`),
    KEY `idx_kfid` (`kfid`),
    KEY `idx_external_userid` (`external_userid`),
    KEY `idx_scene` (`scene`),
    KEY `idx_nickname` (`nickname`),
    KEY `idx_xm_user_id` (`xm_user_id`),
    KEY `idx_mobile` (`mobile`),
    KEY `idx_status` (`status`),
    KEY `idx_reception_user_id` (`reception_user_id`),
    KEY `idx_question_type_id` (`question_type_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服接待表';

CREATE TABLE IF NOT EXISTS `xm_customer_msg_page`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `kfid` varchar(100) NOT NULL  COMMENT '客服id',
    `last_msg_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后发送消息时间',
    `current_page` varchar(100) NOT NULL default '' COMMENT '当前页码',
    `next_page` varchar(100) NOT NULL default '' COMMENT '下页',
    `last_msg_id` varchar(100) NOT NULL default '' COMMENT '最后处理消息id',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_kfid` (`kfid`),
    KEY `idx_last_msg_time` (`last_msg_time`),
    KEY `idx_current_page` (`current_page`),
    KEY `idx_next_page` (`next_page`),
    KEY `idx_last_msg_id` (`last_msg_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服消息索引表';

CREATE TABLE IF NOT EXISTS `xm_customer_user_info`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `external_userid` varchar(100) NOT NULL  COMMENT '客户联系人id',
    `xm_user_id` int(10) NOT NULL default 0 COMMENT '先迈用户id',
    `mobile` varchar(11) NOT NULL default '' COMMENT '手机号',
    `nickname` varchar(200) NOT NULL default '' COMMENT '昵称',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_external_userid` (`external_userid`),
    KEY `idx_xm_user_id` (`xm_user_id`),
    KEY `idx_mobile` (`mobile`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客户信息表';

ALTER TABLE `xm_customer_reception`
    ADD COLUMN `source` tinyint NOT NULL DEFAULT 0 COMMENT '来源0未知，1ios，2安卓，3小程序，4h5';

ALTER TABLE `xm_customer_reception`
    ADD COLUMN `reception_user` varchar(200) NOT NULL DEFAULT '' COMMENT '接待人昵称' after `reception_user_id`;


CREATE TABLE IF NOT EXISTS `xm_customer_service_work_date`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `date` varchar(20) NOT NULL default '' COMMENT '日期',
    `type` varchar(20) NOT NULL default '' COMMENT 'work工作日，free休息日',
    `time_setting` varchar(1000) NULL  COMMENT  '排班时间配置',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_date` (`date`),
    KEY `idx_type` (`type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服每日排班表';

CREATE TABLE IF NOT EXISTS `xm_customer_service_intent`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `group_id` varchar(20) NOT NULL default '' COMMENT '企微分组id',
    `intent_id` varchar(20) NOT NULL default '' COMMENT '企微问答id',
    `question` varchar(1000) NULL  COMMENT  '提问',
    `answers` text  NULL  COMMENT '回答',
    `answers_attachments` text  NULL  COMMENT '回答附件',
    `is_main` integer  NOT NULL default 0 COMMENT '是否主问题',
    `main_id` integer  NOT NULL default 0 COMMENT '主问题id',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_intent_id` (`intent_id`),
    KEY `idx_main_id` (`main_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服知识库表';

INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('客服配置', 'CUSTOMER_SERVICE_CONFIGURATION', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/customerServiceConfiguration', 257);

