本分支上线需要先迈2022_s1分支配合一起上线
CREATE TABLE `xm_enterprise_tag_rule_group`  (
  `tag_rule_group_id` INT NOT NULL AUTO_INCREMENT,
  `tag_rule_group_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标签规则组名称',
  `tag_group_id` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT '标签组id',
  `tag_rule_type` VARCHAR(32) NOT NULL DEFAULT 'auto' COMMENT 'auto:自动打标;auto_cancel:自动取消打标',
  `create_time` INT UNSIGNED  NOT NULL,
  PRIMARY KEY (`tag_rule_group_id`),
  INDEX(`tag_group_id`)
) ENGINE = INNODB DEFAULT CHARSET=utf8;
ALTER TABLE `xm_enterprise_tag_rule_group` ADD COLUMN `group_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '标识' AFTER `tag_rule_group_id`; 


CREATE TABLE `xm_enterprise_tag_rule_group_middle`  (
  `tag_rule_group_middle_id` INT NOT NULL AUTO_INCREMENT,
  `tag_rule_group_id` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT '标签规则组id',
  `tag_rule_id` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT '标签规则id',
  `create_time` INT UNSIGNED  NOT NULL ,
  PRIMARY KEY (`tag_rule_group_middle_id`),
  INDEX(`tag_rule_group_id`),
  INDEX(`tag_rule_id`)
) ENGINE = INNODB DEFAULT  CHARSET=utf8;


CREATE TABLE `xm_enterprise_tag_rule_middle`  (
  `tag_rule_middle_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `tag_id` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT 'xm_enterprise_tag主键',
  `tag_rule_id` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT 'xm_enterprise_tag_rule主键',
  `rule_value` VARCHAR(1000) NOT NULL COMMENT '规则值',
  `tag_rule_type` VARCHAR(32) NOT NULL DEFAULT 'auto' COMMENT 'auto:自动打标;auto_cancel:自动取消打标',
  `create_time` INT UNSIGNED NOT NULL,
  PRIMARY KEY (`tag_rule_middle_id`),
  INDEX(`tag_id`),
  INDEX(`tag_rule_id`)
) ENGINE = INNODB DEFAULT CHARSET=utf8;


CREATE TABLE `xm_enterprise_tag_rule`  (
  `tag_rule_id` INT NOT NULL AUTO_INCREMENT,
  `key` VARCHAR(64) NOT NULL COMMENT '规则标识',
  `rule` VARCHAR(255) NOT NULL COMMENT '规则详情',
  `rule_value` VARCHAR(1000) NOT NULL COMMENT '规则默认值配置',
  `sort` INT UNSIGNED  NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` INT UNSIGNED  NOT NULL,
  PRIMARY KEY (`tag_rule_id`)
) ENGINE = INNODB DEFAULT CHARSET=utf8;

ALTER TABLE `xm_enterprise_tag`
    ADD COLUMN `source` varchar(50) NOT NULL COMMENT '来源' AFTER `tag_name`;

ALTER TABLE `xm_enterprise_tag`
    ADD COLUMN `is_sync_enterprise_wx` tinyint NOT NULL DEFAULT 1 COMMENT '是否同步到企业微信' AFTER `source`;

ALTER TABLE `xm_enterprise_tag` ADD COLUMN `auto_tag` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '自动打标 1.启用 0.未启用' AFTER `order`, 
ADD COLUMN `auto_cancel_tag` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '自动取消打标 1.启用 0.未启用' AFTER `auto_tag`; 

ALTER TABLE `xm_enterprise_tag`
DROP INDEX `enterprise_wechat_tag_id`,
ADD INDEX `enterprise_wechat_tag_id`(`enterprise_wechat_tag_id`) USING BTREE;

ALTER TABLE `xm_enterprise_tag`
    ADD UNIQUE INDEX `tag_name`(`tag_name`) USING BTREE;

ALTER TABLE `xm_enterprise_tag_group`
    ADD UNIQUE INDEX `group_name`(`group_name`) USING BTREE;

ALTER TABLE `xm_league_task`
    ADD COLUMN `early_exit_time` int(11) NOT NULL COMMENT '提前退出时间',
    ADD COLUMN `initial_fee` decimal(10,2) DEFAULT NULL COMMENT '单份加盟费',
    ADD COLUMN `buy_count` int(11) DEFAULT '1' COMMENT '加盟份数';

ALTER TABLE `xm_league_task`
    ADD INDEX `idx_start_time`(`start_time`),
ADD INDEX `idx_end_time`(`end_time`),
ADD INDEX `idx_early_exit_time`(`early_exit_time`),
ADD INDEX `idx_initial_fee`(`initial_fee`);


-- 同步加盟任务保证金和份额
-- UPDATE xm_youpin.xm_league_task AS t,
--     `xmpt-v2-dev`.xm_user_league AS l
-- SET t.initial_fee = l.initial_fee
--         , t.buy_count = l.buy_count
-- WHERE
--     t.league_id = l.id;

-- 提前退出
-- UPDATE xm_youpin.xm_league_task AS t,
--     `xmpt-v2-dev`.xm_user_league AS l
-- SET t.early_exit_time = UNIX_TIMESTAMP(l.broke_at)
-- WHERE
--     t.league_id = l.id and t.state =1 and l.broke_at>0;

 CREATE TABLE `xm_member_authorize_login` (
  `id` INT NOT NULL AUTO_INCREMENT, 
  `member_id` INT UNSIGNED NOT NULL COMMENT 'member会员表唯一id', 
  `wechat_unionid` VARCHAR(64) COMMENT '微信开放平台用户唯一标识', 
  `created_at` TIMESTAMP, PRIMARY KEY (`id`) 
 ) ENGINE=INNODB CHARSET=utf8; 
 ALTER TABLE `xm_member_authorize_login` ADD KEY (`member_id`) , ADD KEY (`wechat_unionid`);

ALTER TABLE xm_member_authorize DROP COLUMN oper_userid;

CREATE TABLE `xm_member_enterprise_wechat` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `member_id` int(11) NOT NULL COMMENT '用户id',
    `external_userid` varchar(100) NOT NULL COMMENT '企业微信客户id',
    `external_contact_data` text NOT NULL COMMENT '企业微信信息',
    `follow_user_data` text NOT NULL COMMENT '企业微信添加人信息',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_external_userid` (`external_userid`)
) ENGINE=InnoDB;

CREATE TABLE `xm_jobs` (
   `id` int NOT NULL AUTO_INCREMENT,
   `queue` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
   `payload` longtext COLLATE utf8mb4_general_ci NOT NULL,
   `attempts` tinyint unsigned NOT NULL,
   `reserve_time` int unsigned DEFAULT NULL,
   `available_time` int unsigned NOT NULL,
   `create_time` int unsigned NOT NULL,
   PRIMARY KEY (`id`),
   KEY `queue` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `xm_enterprise_tag_rule` ADD COLUMN `rule_value_extral` VARCHAR(500) NULL COMMENT '规则额外配置' AFTER `rule_value`; 


 INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
 VALUES ('admin', '', '操作标签规则', 'TAG_VALUES_RULE', 'TAG_VALUES', '5', 'admin/memberTags/tagValueAdd', '0', '0', '', '0', '', '', '1');

ALTER TABLE `xm_enterprise_tag_group`
DROP INDEX `enterprise_wechat_group_id`,
ADD INDEX `enterprise_wechat_group_id`(`enterprise_wechat_group_id`) USING BTREE;

# 队列失败记录
CREATE TABLE `xm_failed_jobs`
(
    `id`         int(11)   NOT NULL AUTO_INCREMENT,
    `connection` text,
    `queue`      text,
    `payload`    longtext,
    `exception`  longtext,
    `fail_time`  timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = MyISAM;

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签管理', 'TAGS_MANAGE', 'SHOP_ROOT', 2, '', 1, 256, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签组', 'TAG_GROUPS', 'TAGS_MANAGE', 4, 'admin/memberTags/groupList', 1, 0, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '标签值', 'TAG_VALUES', 'TAGS_MANAGE', 4, 'admin/memberTags/valueList', 1, 0, '', 0, '', '', 1);

ALTER TABLE `xm_enterprise_tag_group`
    MODIFY COLUMN `enterprise_wechat_group_id` varchar(50) NOT NULL DEFAULT '' COMMENT '企微标签组id' AFTER `tag_group_id`;

ALTER TABLE `xm_enterprise_tag`
    MODIFY COLUMN `enterprise_wechat_tag_id` varchar(50)  NOT NULL DEFAULT '' COMMENT '企微标签id' AFTER `tag_group_id`;
--  php think queue:listen --queue=execute_rule   //执行全部规则队列













-- 20220216 修改加盟两条规则
UPDATE `xm_enterprise_tag_rule` SET `rule` = '近%s天当前用户加盟保证金总额在%s到%s元之间' , 
`rule_value` = '[{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0}]' , 
`rule_value_extral` = '[{\"type\":\"between\",\"info\":[{\"index\":1 ,\"class\": \"min-btw\"},{\"index\":2 ,\"class\": \"max-btw\"}]}]' 
WHERE `key` = 'league_initial_fee'; 



UPDATE `xm_enterprise_tag_rule` SET `rule` = '近%s天成功推荐加盟的人数在%s到%s人之间' , 
`rule_value` = '[{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0},{\"type\":\"input\",\"content\":0}]' , 
`rule_value_extral` = '[{\"type\":\"between\",\"info\":[{\"index\":1 ,\"class\": \"min-btw\"},{\"index\":2 ,\"class\": \"max-btw\"}]}]' 
WHERE `key` = 'league_invite_num'; 



DELETE FROM `xm_enterprise_tag_rule_middle` WHERE tag_rule_id IN (
SELECT tag_rule_id FROM `xm_enterprise_tag_rule` WHERE `key` IN ('league_initial_fee','league_invite_num'));