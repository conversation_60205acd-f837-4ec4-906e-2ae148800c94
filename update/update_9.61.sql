INSERT INTO `xm_diy_view_util` (`name`, `title`, `type`, `controller`, `value`, `addon_name`, `sort`, `support_diy_view`, `max_count`)
VALUES ('NEW_PRODUCT_AREA','新品专区','SYSTEM','NewProductArea','{"selectedTemplate":1,"title":"新品专区","moreText":"查看更多","fontColor":"#000000","backgroundColor":"#FFFFFF","backgroundImg":""}','',0,'',1);

ALTER TABLE `xm_goods`
    ADD COLUMN `monitor_status` tinyint default 0 COMMENT '状态，（0不监控，1监控中，10监控结束）',
    ADD COLUMN `up_time` int default 0 COMMENT '在线时长（分钟）';

CREATE TABLE IF NOT EXISTS `xm_goods_uptime_range` (
      `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `goods_id` varchar(255) NOT NULL DEFAULT '' COMMENT '商品id',
      `up_time` int NOT NULL DEFAULT 0 COMMENT '在线时长（分钟），固定1440',
      `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '开始日期',
      `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '结束日期',
      `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
      `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `idx_goods_id` (`goods_id`),
      KEY `idx_start_date` (`start_date`),
      KEY `idx_end_date` (`end_date`)
) ENGINE=InnoDB CHARSET=utf8mb4 COMMENT='商品在线时长范围';

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '新品上线监测', 'NEW_GOODS_UP_MONITOR', 'STAT_ROOT', 3, 'admin/Analyse/goodsUpMonitor', 1, 10, '', 0, '', '', 1);

ALTER TABLE `xm_goods`
    ADD COLUMN `end_monitor_date` varchar(20) default '' COMMENT '结束监控日期';
