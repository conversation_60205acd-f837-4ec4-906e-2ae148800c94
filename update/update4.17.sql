INSERT INTO `xm_message` (`addon`, `keywords`, `title`, `message_type`, `message_json`, `email_is_open`, `email_title`, `email_content`, `sms_is_open`, `sms_addon`, `sms_json`, `sms_content`, `wechat_is_open`, `wechat_json`, `weapp_is_open`, `weapp_json`, `aliapp_is_open`, `aliapp_json`, `support_type`) VALUES ('', 'ORDER_WAIT_DELIVERY', '订单待发货通知', 3, '', 1, '订单待发货通知', '', 0, '', '', '', 0, '', 0, '', 0, '', '');
INSERT INTO `xm_message` (`addon`, `keywords`, `title`, `message_type`, `message_json`, `email_is_open`, `email_title`, `email_content`, `sms_is_open`, `sms_addon`, `sms_json`, `sms_content`, `wechat_is_open`, `wechat_json`, `weapp_is_open`, `weapp_json`, `aliapp_is_open`, `aliapp_json`, `support_type`) VALUES ('', 'WITHDRAWAL_WAIT_VERIFY', '提现待审核通知', 3, '', 1, '提现待审核通知', '', 0, '', '', '', 0, '', 0, '', 0, '', '');
INSERT INTO `xm_message` (`addon`, `keywords`, `title`, `message_type`, `message_json`, `email_is_open`, `email_title`, `email_content`, `sms_is_open`, `sms_addon`, `sms_json`, `sms_content`, `wechat_is_open`, `wechat_json`, `weapp_is_open`, `weapp_json`, `aliapp_is_open`, `aliapp_json`, `support_type`) VALUES ('', 'AFTER_SALE_WAIT_HANDLE', '售后待处理通知', 3, '', 1, '售后待处理通知', '', 0, '', '', '', 0, '', 0, '', 0, '', '');

INSERT INTO `xm_menu` (`id`, `app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '编辑邮件模板', 'WAIT_HANDLE_MESSAGE', 'MESSAGE_LISTS', 4, 'admin/message/waitHandleMessage', 0, 1, '', 0, '', '', 1);

ALTER TABLE `xm_order_goods_aftersale`
    ADD COLUMN `buyer_send_time` int NOT NULL DEFAULT 0 COMMENT '买家发货时间' AFTER `send_again_time`;

ALTER TABLE `xm_order_goods_aftersale`
    ADD COLUMN `seller_receive_time` int NOT NULL DEFAULT 0 COMMENT '卖家收货时间' AFTER `buyer_send_time`;

ALTER TABLE `xm_order_goods_aftersale`
    ADD COLUMN `buyer_receive_time` int NOT NULL DEFAULT 0 COMMENT '买家收货时间' AFTER `seller_receive_time`;

ALTER TABLE `xm_user`
    ADD COLUMN `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮箱' AFTER `realname`;

CREATE TABLE `xm_wait_handle_send_template` (
    `template_id` int(11) NOT NULL AUTO_INCREMENT,
    `keywords` varchar(50) NOT NULL DEFAULT '' COMMENT '关键字',
    `send_cycle` int(11) NOT NULL DEFAULT '0' COMMENT '发送周期1每日',
    `send_time` varchar(30) NOT NULL DEFAULT '' COMMENT '发送时间',
    `send_groups` varchar(255) NOT NULL DEFAULT '' COMMENT '用户组，为0则通知所有用户组',
    `level` int(11) NOT NULL DEFAULT '0' COMMENT '级别1日常，2异常，3紧急',
    `wait_time` int(11) NOT NULL DEFAULT '0' COMMENT '等待未处理时间（小时）',
    `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
    `content` text NOT NULL COMMENT '内容',
    `enable` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否开启 1开启 0不开启',
    `created_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updated_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('ORDER_WAIT_DELIVERY', 1, '11:44:00', '1,13', 1, 0, '[日常提醒]先迈商城有{wait_send_nums}个待发货订单', '当前已有{wait_send_nums}个待发货订单，请及时发货。', 0, 1635303261, 1635824576);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('ORDER_WAIT_DELIVERY', 1, '11:44:00', '1,13', 2, 48, '[异常提醒]先迈商城有{wait_send_nums}个待发货订单', '当前有{wait_send_nums}个支付超{wait_time}小时未发货的订单，请尽快安排发货。', 0, 1635303261, 1635824576);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('ORDER_WAIT_DELIVERY', 1, '11:44:00', '1,13', 3, 72, '[紧急提醒]先迈商城有{wait_send_nums}个待发货订单', '当前有{wait_send_nums}个支付超{wait_time}小时未发货的订单，请尽快安排发货。', 0, 1635303261, 1635824576);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('WITHDRAWAL_WAIT_VERIFY', 1, '11:44:00', '1,13', 1, 0, '[日常提醒]先迈商城有{wait_verify_nums}个待审核的提现申请', '当前已有{wait_verify_nums}个待审核的提现申请，请及时处理。', 0, 1635303261, 1635824586);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('AFTER_SALE_WAIT_HANDLE', 1, '11:44:00', '1,13', 1, 0, '[日常提醒]先迈商城有{after_sale_nums}个待处理的售后订单', '<p>当前共有{after_sale_nums}个待处理的售后订单，请及时处理。</p><p><span style=\"color: rgb(255, 0, 0);\">申请退款中{apply_refund_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待审核{verifying_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待收货{receiving_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待退款{refunding_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待发货{sending_nums}个</span></p>', 0, 1635303261, 1635824605);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('AFTER_SALE_WAIT_HANDLE', 1, '11:44:00', '1,13', 2, 48, '[异常提醒]先迈商城有{after_sale_nums}个待处理的售后订单', '<p>当前共有{after_sale_nums}个提交超{wait_time}小时未处理的售后订单，请尽快安排处理。</p><p><span style=\"color: rgb(255, 0, 0);\">申请退款中{apply_refund_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待审核{verifying_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待收货{receiving_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待退款{refunding_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待发货{sending_nums}个</span></p>', 0, 1635303261, 1635824605);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('WITHDRAWAL_WAIT_VERIFY', 1, '11:44:00', '1,13', 2, 48, '[异常提醒]先迈商城有{wait_verify_nums}个待审核的提现申请', '当前有{wait_verify_nums}个提交超{wait_time}小时未审核的提现申请，请尽快安排处理。', 0, 1635303261, 1635824586);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('WITHDRAWAL_WAIT_VERIFY', 1, '11:44:00', '1,13', 3, 72, '[紧急提醒]先迈商城有{wait_verify_nums}个待审核的提现申请', '当前有{wait_verify_nums}个提交超{wait_time}小时未审核的提现申请，请尽快安排处理。', 0, 1635303261, 1635824586);
INSERT INTO `xm_wait_handle_send_template` (`keywords`, `send_cycle`, `send_time`, `send_groups`, `level`, `wait_time`, `title`, `content`, `enable`, `created_at`, `updated_at`) VALUES ('AFTER_SALE_WAIT_HANDLE', 1, '11:44:00', '1,13', 3, 72, '[紧急提醒]先迈商城有{after_sale_nums}个待处理的售后订单', '<p>当前共有{after_sale_nums}个提交超{wait_time}小时未处理的售后订单，请尽快安排处理。</p><p><span style=\"color: rgb(255, 0, 0);\">申请退款中{apply_refund_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待审核{verifying_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待收货{receiving_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待退款{refunding_nums}个</span></p><p><span style=\"color: rgb(255, 0, 0);\">待发货{sending_nums}个</span></p>', 0, 1635303261, 1635824605);


