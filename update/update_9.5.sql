CREATE TABLE IF NOT EXISTS `xm_fans_task`
(
    `task_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
    `type` varchar(20) NOT NULL DEFAULT '' COMMENT '任务类型',
    `status` integer NOT NULL DEFAULT '0' COMMENT '状态',
    `reward_balance` decimal(10,2) NOT NULL default 0 COMMENT '奖励余额',
    `start_time` integer NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` integer NOT NULL DEFAULT '0' COMMENT '结束时间',
    `target_fans` varchar(30) NOT NULL DEFAULT '' COMMENT '目标粉丝',
    `target_reg_start_time` integer NOT NULL DEFAULT '0' COMMENT '目标注册开始时间',
    `target_reg_end_time` integer NOT NULL DEFAULT '0' COMMENT '目标注册开始时间',
    `target_rules` varchar(1000) NOT NULL DEFAULT '' COMMENT '任务指标（规则）',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`task_id`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='粉丝任务表';

CREATE TABLE IF NOT EXISTS `xm_fans_task_complete`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `task_id` integer NOT NULL DEFAULT '0' COMMENT '任务id',
    `task_name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
    `target_rules` varchar(1000) NOT NULL DEFAULT '' COMMENT '任务指标（规则）',
    `complete_member_id` integer NOT NULL DEFAULT '0' COMMENT '达标用户id',
    `parent_member_id` integer NOT NULL DEFAULT '0' COMMENT '推荐人用户id',
    `reward_balance` decimal(10,2) NOT NULL default 0 COMMENT '奖励余额',
    `complete_time` integer NOT NULL DEFAULT '0' COMMENT '达标时间',
    `is_send` tinyint NOT NULL DEFAULT '0' COMMENT '是否已发放奖励',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_complete_member_id` (`complete_member_id`),
    KEY `idx_parent_member_id` (`parent_member_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='粉丝任务达标表';