CREATE TABLE IF NOT EXISTS `xm_sign_activity`
(
    `sign_activity_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
    `sign_days` int(11) NOT NULL DEFAULT '0' COMMENT '连续签到天数',
    `is_open_new` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启新人签到',
    `new_sign_days` int(11) NOT NULL DEFAULT '0' COMMENT '新人连续签到天数',
    `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
    `rule` varchar(500) NOT NULL DEFAULT '' COMMENT '规则',
    `default_img` varchar(200) NOT NULL DEFAULT '' COMMENT '主图',
    `advertise` varchar(200) NOT NULL DEFAULT '' COMMENT '广告',
    `look_time` tinyint(11) NOT NULL DEFAULT '0' COMMENT '浏览时长',
    `advertise_link` varchar(200) NOT NULL DEFAULT '' COMMENT '广告链接',
    `goodscoupon_type_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '优惠券活动id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`sign_activity_id`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='签到活动表';


CREATE TABLE IF NOT EXISTS `xm_sign_activity_complete`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `sign_activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `comexternal_userid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业微信客户id',
    `unionid` varchar(50) NOT NULL DEFAULT '' COMMENT '',
    `continuous_nums` int(11) NOT NULL DEFAULT '0' COMMENT '连续签到天数',
    `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
    `complete_date` varchar(20) NOT NULL DEFAULT '' COMMENT '完成日期',
    `is_send_reward` tinyint NOT NULL DEFAULT '0' COMMENT '是否已发放奖励',
    `reward_goodscoupon_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '奖励优惠券ids',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_sign_activity_id` (`sign_activity_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_unionid` (`unionid`),
    KEY `idx_complete_date` (`complete_date`),
    KEY `idx_comexternal_userid` (`comexternal_userid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='签到活动完成表';

CREATE TABLE IF NOT EXISTS `xm_sign_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `sign_activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `sign_date`  varchar(20) NOT NULL DEFAULT '' COMMENT '签到日期',
    `sign_time`  int(11) NOT NULL DEFAULT '0' COMMENT '签到时间',
    `continuous_nums`  int(11) NOT NULL DEFAULT '0' COMMENT '连续签到次数',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_sign_activity_id` (`sign_activity_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_sign_date` (`sign_date`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='签到活动记录表';

ALTER TABLE `xm_sign_activity`
    ADD COLUMN `advertise_type` varchar(20) NOT NULL DEFAULT 'image' COMMENT '广告类型，image或video' AFTER `advertise`;


ALTER TABLE `xm_sign_activity`
    ADD COLUMN `enterprise_wx_userids` varchar(200) NOT NULL DEFAULT '' COMMENT '客服企业微信userid' AFTER `goodscoupon_type_ids`;


ALTER TABLE `xm_sign_activity_complete`
    ADD COLUMN `enterprise_wx_userid` varchar(50) NOT NULL DEFAULT '' COMMENT '客服企业微信userid' AFTER `reward_goodscoupon_ids`;