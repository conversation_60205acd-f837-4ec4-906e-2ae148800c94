-- 菜单
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '佣金设置', 'COMMISSION_SETTING', 'CONFIG_ROOT', 2, 'admin/config/commission', 1, 0, '', 0, '', '', 1);

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '升级会员设置', 'UPGRADE_VIP_SETTING', 'CONTENT_ROOT', 2, 'admin/upgrade/lists', 1, 120, '', 0, '', '', 1);

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '添加升级会员设置', 'UPGRADE_VIP_SETTING_ADD', 'UPGRADE_VIP_SETTING', 3, 'admin/upgrade/add', 0, 1, '', 0, '', '', 1);

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '编辑升级会员设置', 'UPGRADE_VIP_SETTING_EDIT', 'UPGRADE_VIP_SETTING', 3, 'admin/upgrade/edit', 0, 2, '', 0, '', '', 1);

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '升级会员设置详情', 'UPGRADE_VIP_SETTING_SHOW', 'UPGRADE_VIP_SETTING', 3, 'admin/upgrade/show', 0, 3, '', 0, '', '', 1);

INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '启用/禁用升级会员设置', 'UPGRADE_VIP_SETTING_STATUS', 'UPGRADE_VIP_SETTING', 3, 'admin/upgrade/change', 0, 4, '', 0, '', '', 1);



-- 升级管理设置
CREATE TABLE `xm_upgrade` ( 
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `title` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '标题', 
    `path` VARCHAR(255) NOT NULL COMMENT '图片路径', 
    `content` TEXT COMMENT '内容', `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1.可用  0.不可用', 
    `create_time` INT NOT NULL DEFAULT 0 COMMENT '创建时间', 
    `update_time` INT NOT NULL DEFAULT 0 COMMENT '更新时间', 
    PRIMARY KEY (`id`) 
) ENGINE=INNODB CHARSET=utf8mb4; 


alter table xm_order add column `is_vip` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否vip价购买：0-否，1-是';
alter table xm_order_goods add column `is_vip` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否vip价购买：0-否，1-是';

CREATE TABLE `xm_encrypt_data`  (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `encrypt_prefix` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'MD5前8位',
    `full_encrypt` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'md5全',
    `encrypt_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '数据',
    `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`),
    INDEX `idx_encrypt_prefix`(`encrypt_prefix`) USING BTREE
);