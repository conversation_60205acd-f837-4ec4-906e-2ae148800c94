CREATE TABLE IF NOT EXISTS `xm_pintuan_goods_status_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `pintuan_goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团商品id',
    `old_status` int(11) NOT NULL DEFAULT '0' COMMENT '旧状态',
    `new_status` int(11) NOT NULL DEFAULT '0' COMMENT '新状态',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团商品上下架记录表';

CREATE TABLE IF NOT EXISTS `xm_pintuan_share_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `pintuan_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团id',
    `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '分享人用户id',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团分享记录表';

ALTER TABLE `xm_pintuan_goods`
    ADD COLUMN `share_nums` int(11) NOT NULL DEFAULT '0' COMMENT '分享次数';

CREATE TABLE IF NOT EXISTS `xm_pintuan_open_share_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `pintuan_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团id',
    `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '打开分享用户id',
    `share_id` int(11) NOT NULL DEFAULT '0' COMMENT '分享人用户id',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团打开分享记录表';