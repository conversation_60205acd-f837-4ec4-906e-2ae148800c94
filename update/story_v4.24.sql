/**
 * 直播间商品
 * wjj
 * 2021-11-08
 */
ALTER TABLE `xm_weapp_goods`
ADD COLUMN `self_goods_id` int(11) UNSIGNED NOT NULL COMMENT '商城商品id';
ADD COLUMN `price2` decimal(11, 2) NULL COMMENT '商品价格2（参考微信接口）',
ADD COLUMN `price_type` smallint(1) NOT NULL COMMENT '价格类型';

-- 直播间
ALTER TABLE `xm_weapp_live_room`
ADD COLUMN `close_kf` smallint(1) NOT NULL DEFAULT 1 COMMENT '是否关闭客服，（0开启，1关闭）默认关闭',
ADD COLUMN `close_replay` smallint(1) NOT NULL DEFAULT 1 COMMENT '是否关闭回放（0开启，1关闭）默认关闭',
ADD COLUMN `is_feeds_public` smallint(1) NOT NULL COMMENT '是否开启官方收录（1开启，0关闭）',
ADD COLUMN `feeds_img` varchar(255) NULL COMMENT '官方收录封面',
ADD COLUMN `creater_openid` varchar(255) NULL COMMENT '创建者openid';