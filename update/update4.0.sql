CREATE TABLE `xm_weapp_notice`  (
    `template_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增模板id',
    `template_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信的模板id',
    `scene` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场景',
    `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容',
    `type` tinyint(1) NOT NULL COMMENT '模版类型，2 为一次性订阅，3 为长期订阅',
    `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`template_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4;

CREATE TABLE `xm_weapp_notice_subscribe`  (
    `subscribe_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `member_id` int(11) NOT NULL COMMENT '用户id',
    `scene` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '场景',
    `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0为未发送 1为已发送',
    `source` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '来源',
    `source_id` int(11) NULL DEFAULT 0 COMMENT '来源id',
    `created_at` int(11) NOT NULL,
    `updated_at` int(11) NOT NULL,
    PRIMARY KEY (`subscribe_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4;

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '推荐搜索', 'HOT_WORDS', 'SHOP_DECORATE', 2, 'admin/hotWord/lists', 1, 0, '', 0, '', '', 1);

ALTER TABLE `xm_shop_goods_hot_words`
    ADD COLUMN `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(-1删除 0禁用 1正常)' AFTER `update_time`;

ALTER table `xm_weapp_notice_subscribe`
ADD INDEX(`scene`),
ADD INDEX(`status`),
ADD INDEX(`source`, `source_id`);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '添加热门关键字', 'HOT_WORDS_ADD', 'HOT_WORDS', 3, 'admin/hotWord/add', 0, 0, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '编辑热门关键字', 'HOT_WORDS_EDIT', 'HOT_WORDS', 3, 'admin/hotWord/update', 0, 0, '', 0, '', '', 1);