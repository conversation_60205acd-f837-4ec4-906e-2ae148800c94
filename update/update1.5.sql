/**
 * czk
 */
-- 执行php think initGoods 4脚本之前执行
alter table xm_goods add column `price_back` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';
alter table xm_goods add column `sn` varchar(255) DEFAULT NULL COMMENT '商品货号';
alter table xm_goods modify column `reward_company_rate` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '公司返佣比例(单位：%)';
-- 执行完php think initGoods 4脚本后执行

update xm_goods set price_back = pirce where xm_id = 0;
alter table xm_goods change   `price` `price_back1` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';
alter table xm_goods change   `price_back` `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';

-- alter table xm_goods change   `price_back2` `price_back` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';

-- alter table xm_goods change   `price` `price_back2` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';
-- alter table xm_goods change   `price_back1` `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格（取第一个sku）(供货价)';
