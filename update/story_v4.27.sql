INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` )
VALUES
( 'admin', '', 'APPh5装修配置', 'APP_H5_CUSTOM_CONFIG', 'SHOP_DECORATE', 2, 'admin/diy/appIndex', 1, 1, '', 0, 'app/admin/view/public/img/menu_icon/website_set.png', '', 1 );



INSERT INTO `xm_menu` ( `app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` )
VALUES
( 'admin', '', '首页', 'APP_H5_CUSTOM_INDEX', 'APP_H5_CUSTOM_CONFIG', 3, 'admin/diy/appIndex', 1, 1, '', 0, '', '', 1 );


INSERT INTO `xm_site_diy_view` ( `site_id`, `name`, `addon_name`, `title`, `value`, `create_time`, `update_time`, `type`, `icon` )
VALUES
( 0, 'DIYVIEW_APP_INDEX', '', 'app网站主页', '{"global":{"title":"app网站主页","openBottomNav":false,"bgColor":"#ffffff","":"","subpage":false,"version":"123123123"},"value":[{"selectedTemplate":"carousel-posters","imageClearance":0,"padding":0,"height":0,"list":[{"imageUrl":"upload/common/images/20211213/20211213025903163937874373321.jpeg","title":"123123","link":"123123"}],"addon_name":"","type":"IMAGE_ADS","name":"图片广告","controller":"ImageAds"},{"sources":"default","skuId":"","categoryId":0,"goodsCount":"6","addon_name":"","type":"GOODS_LIST","name":"商品列表","controller":"GoodsList"}]}', 1639378749, 1639381455, 'admin', '' );