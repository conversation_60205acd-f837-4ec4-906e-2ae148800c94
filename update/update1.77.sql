/**
 * 推广海报
 * 2020-12-11
 * wjj
 */
 CREATE TABLE `xm_spread_poster` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `poster_name` varchar(100) NOT NULL DEFAULT '' COMMENT '海报名称',
  `path` varchar (2000) NOT NULL DEFAULT '' COMMENT '宣传图片，多张用“ , ”分割',
  `wx_qrcode` varchar(100) NOT NULL DEFAULT '' COMMENT '微信二维码',
  `wechat_no` varchar(100) NOT NULL DEFAULT '' COMMENT '微信号',
  `mobile`    varchar(11) NOT NULL DEFAULT '' COMMENT '手机号',
  `is_enable` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '是否启用，1=启用，0=禁用',
  `browse` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `consult_status` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '留言咨询状态',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推广海报表';

/**
 * 添加菜单
 * 2020-12-14
 * wjj
 */
INSERT INTO `xm_menu`
(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', '', '推广单页', 'SPREAD_POSTER_LISTS', 'SHOP_DECORATE', 2, 'admin/spreadPoster/lists', 1, 0, '', 0, '', '', 1),
( 'admin', '', '新增推广页', 'SPREAD_POSTER_ADD', 'SPREAD_POSTER_LISTS', 3, 'admin/spreadPoster/add', 0, 0, '', 0, '', '', 1),
( 'admin', '', '编辑推广单页', 'SPREAD_POSTER_EDIT', 'SPREAD_POSTER_LISTS', 3, 'admin/spreadPoster/edit', 0, 0, '', 0, '', '', 1);
