CREATE TABLE IF NOT EXISTS `goods_price_change_log`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `goods_id` integer NOT NULL DEFAULT 0 COMMENT '商品id',
    `change_data` text NULL COMMENT '变更数据',
    `change_time` integer NOT NULL DEFAULT 0 COMMENT '变更时间',
    `supply_change_time` integer NOT NULL DEFAULT 0 COMMENT '供应链改价时间',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_change_time` (`change_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品价格变更记录表';

ALTER TABLE `xm_goods_sku`
    ADD COLUMN `supply_price_change_time` int(11) NOT NULL DEFAULT 0 COMMENT '供应链最后改价时间' AFTER `is_activity_goods`;