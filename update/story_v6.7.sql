CREATE TABLE `xm_supply_chain_goods_relation_snapshot`
(
    `id`          int(11)   NOT NULL AUTO_INCREMENT,
    `pro_id`      int(11)   NOT NULL COMMENT '供应链商品ID',
    `goods_id`    int(11)   NOT NULL COMMENT '本地商品ID',
    `data`        longtext  NOT NULL COMMENT '快照数据',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `pro_id` (`pro_id`) USING BTREE,
    UNIQUE KEY `goods_id` (`goods_id`) USING BTREE
) COMMENT ='供应链商品关联快照';