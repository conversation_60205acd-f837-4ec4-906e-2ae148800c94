ALTER TABLE `xm_pintuan`
    ADD COLUMN `use_robot` tinyint NOT NULL DEFAULT 0 COMMENT '是否使用机器人' AFTER `leader_win`,
    ADD COLUMN `robot_nums` integer NOT NULL DEFAULT 0 COMMENT '机器人数量' AFTER `use_robot`;

CREATE TABLE IF NOT EXISTS `xm_robot_info`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
    `headimg` varchar(200) NOT NULL DEFAULT '' COMMENT '头像',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='机器人信息表';

CREATE TABLE IF NOT EXISTS `xm_pintuan_use_robot`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `piantuan_id` integer NOT NULL DEFAULT '0' COMMENT '拼团id',
    `robot_member_id` integer NOT NULL DEFAULT '0' COMMENT '机器人member_id',
    `robot_info_id` integer NOT NULL DEFAULT '0' COMMENT '机器人信息id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团机器人使用表';

ALTER TABLE `xm_member`
    ADD COLUMN `is_robot` tinyint NOT NULL DEFAULT 0 COMMENT '是否是机器人';