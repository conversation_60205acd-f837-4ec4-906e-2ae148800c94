CREATE TABLE IF NOT EXISTS `xm_operate_group`
(
    `operate_group_id`   int(11)     NOT NULL AUTO_INCREMENT,
    `operate_group_name` varchar(50) NOT NULL COMMENT '运营组名',
    `description`        varchar(100)     DEFAULT NULL COMMENT '运营组描述',
    `status`             tinyint(4)  NOT NULL COMMENT '状态：0禁用、1启用',
    `create_time`        timestamp   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`        timestamp   NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`operate_group_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营组表';

CREATE TABLE IF NOT EXISTS `xm_operate_group_relation`
(
    `operate_group_relation_id` int(11)     NOT NULL AUTO_INCREMENT,
    `operate_group_id`          int(11)     NOT NULL COMMENT '运营组ID',
    `relation_id`               int(11)     NOT NULL COMMENT '关联ID',
    `relation_type`             varchar(50) NOT NULL COMMENT '关联类型',
    `relation_time`             timestamp   NULL DEFAULT NULL COMMENT '关联时间',
    `update_time`               timestamp   NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`operate_group_relation_id`),
    KEY `operate_group_id` (`operate_group_id`),
    KEY `relation_id` (`relation_id`),
    KEY `relation_type` (`relation_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营组关联表';

CREATE TABLE `xm_operate_group_snapshot_relation`
(
    `id`               int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `relation_type`    varchar(50) NOT NULL DEFAULT '' COMMENT '关联类型',
    `relation_id`      int(11)     NOT NULL DEFAULT 0 COMMENT '关联id',
    `operate_group_id` int(11)     NOT NULL DEFAULT '0' COMMENT '关联组id',
    `xm_trustee_uid`   int(11)     NOT NULL DEFAULT 0 COMMENT '当前用户的董事用户id',
    `xm_manage_uid`    int(11)     NOT NULL DEFAULT '0' COMMENT '当前用户的经理用户id',
    `xm_director_uid`  int(11)     NOT NULL DEFAULT 0 COMMENT '当前用户的主管用户id',
    `create_time`      int(11)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`      int(11)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_relation_type` (`relation_type`),
    KEY `idx_relation_id` (`relation_id`),
    KEY `idx_operate_group_id` (`operate_group_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='运营组快照关联表';

update xm_menu set is_show = 0 where name = 'AGENT_INDEX';
update xm_menu set is_show = 0 where name = 'DISTRIBUTION_CUSTOMERS_ROOT';
update xm_menu set is_show = 0 where name = 'STAT_AGENT';
update xm_menu set is_show = 0 where name = 'STAT_DISTRIBUTION';
update xm_menu set is_show = 0 where name = 'STAT_USER';
update xm_menu set url = 'admin/stat/overView' where name = 'STAT_ROOT' and app_module = 'admin';
alter table xm_main_stat add column `operate_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '运营组id';
alter table xm_sale_goods_stat add column `operate_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '运营组id';
alter table xm_goods_browse_log add column `operate_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '运营组id';
INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '组别总览', 'OPERATE_GROUP_MAIN_STAT', 'STAT_ROOT', 2, 'admin/stat/groupOverView', 1, 5, '', 0, '', '', 1);

CREATE TABLE `xm_pintuan_snapshot_relation`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `relation_type`    varchar(50) NOT NULL DEFAULT '' COMMENT '关联类型',
    `relation_id`      int(11) NOT NULL DEFAULT '0' COMMENT '关联id',
    `operate_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联组id',
    `xm_trustee_uid`   int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的董事用户id',
    `xm_manage_uid`    int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的经理用户id',
    `xm_director_uid`  int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的主管用户id',
    `create_time`      int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`      int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                `idx_relation_type` (`relation_type`),
    KEY                `idx_relation_id` (`relation_id`),
    KEY                `idx_operate_group_id` (`operate_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='运营组拼团快照关联表';

-- 备份、清空 xm_main_stat 表
-- 备份、清空 xm_sale_goods_stat 表
-- 备份、清空 xm_goods_browse_log 表
