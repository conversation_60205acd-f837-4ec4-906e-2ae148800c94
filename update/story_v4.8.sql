INSERT INTO `xm_addon` (`name`, `icon`, `title`, `description`, `status`, `author`, `version`, `content`, `create_time`)
VALUES ('goodscoupon', 'addon/goodscoupon/icon.png', '平台商品优惠券', '指定商品满减优惠功能', 1, '', '1.0', '', 1598323451);


CREATE TABLE `xm_promotion_goodscoupon` (
   `goodscoupon_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券id',
   `goodscoupon_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
   `goodscoupon_type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型id',
   `goodscoupon_code` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券编码',
   `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '领用人',
   `use_order_id` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券使用订单id',
   `at_least` decimal(10,2) NOT NULL COMMENT '最小金额',
   `money` decimal(10,2) NOT NULL COMMENT '面额',
   `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '优惠券状态 1已领用（未使用） 2已使用 3已过期',
   `get_type` int(11) NOT NULL DEFAULT '0' COMMENT '获取方式1订单2.直接领取3.活动领取',
   `fetch_time` int(11) NOT NULL DEFAULT '0' COMMENT '领取时间',
   `use_time` int(11) NOT NULL DEFAULT '0' COMMENT '使用时间',
   `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '有效期结束时间',
   `is_shop_commission` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '使用优惠券是否进行店主分佣(0-否 1-是)',
   `use_scenario` tinyint(3) NOT NULL DEFAULT '1' COMMENT '使用场景（1全场通用 2指定分类可用 3指定商品可用 4指定商品不可用）',
   `category_ids` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类ids',
   `category_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类名称',
   `over_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
   PRIMARY KEY (`goodscoupon_id`),
   KEY `IDX_promotion_goodscoupon_member_id` (`member_id`),
   KEY `IDX_promotion_goodscoupon_goodscoupon_type_id` (`goodscoupon_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='优惠券表';


CREATE TABLE `xm_promotion_goodscoupon_type` (
    `goodscoupon_type_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券类型Id',
    `goodscoupon_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
    `money` decimal(10,2) NOT NULL COMMENT '发放面额',
    `count` int(11) NOT NULL DEFAULT '0' COMMENT '发放数量',
    `lead_count` int(11) NOT NULL DEFAULT '0' COMMENT '已领取数量',
    `max_fetch` int(11) NOT NULL DEFAULT '0' COMMENT '每人最大领取个数 0无限制',
    `at_least` decimal(10,2) NOT NULL COMMENT '满多少元使用 0代表无限制',
    `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '有效日期结束时间',
    `image` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券图片',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
    `validity_type` int(1) NOT NULL DEFAULT '0' COMMENT '有效期类型 0固定时间 1领取之日起',
    `fixed_term` int(3) NOT NULL DEFAULT '1' COMMENT '领取之日起N天内有效',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态（1进行中2已结束-1已关闭）',
    `is_show` int(11) NOT NULL DEFAULT '0' COMMENT '是否显示',
    `is_shop_commission` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '使用优惠券是否进行店主分佣(0-否 1-是)',
    `use_scenario` tinyint(3) NOT NULL DEFAULT '1' COMMENT '使用场景（1全场通用 2指定分类可用 3指定商品可用 4指定商品不可用）',
    `category_ids` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类ids',
    `category_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '分类名称',
    `over_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
    PRIMARY KEY (`goodscoupon_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='优惠券类型表';


CREATE TABLE `xm_promotion_goodscoupon_goods` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `goodscoupon_type_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团id',
    `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT 'skuid',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关联状态(-1删除 1正常)',
    `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`),
    KEY `idx_goodscoupon_type_id` (`goodscoupon_type_id`),
    KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商品优惠券商品关联表';


INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券', 'PROMOTION_GOODSCOUPON', 'PROMOTION_PLATFORM', 3, 'goodscoupon://admin/goodscoupon/lists', 0, 99, '', 0, '', '', 0);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券详情', 'PROMOTION_GOODSCOUPON_DETAIL', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/detail', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '添加平台商品优惠券', 'PROMOTION_GOODSCOUPON_ADD', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/add', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '编辑平台商品优惠券', 'PROMOTION_GOODSCOUPON_EDIT', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/edit', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '关闭平台商品优惠券', 'PROMOTION_GOODSCOUPON_CLOSE', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/close', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '删除平台商品优惠券', 'PROMOTION_GOODSCOUPON_DELETE', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/delete', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券领取记录', 'PROMOTION_GOODSCOUPON_RECEIVE', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/receive', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券全部领取记录', 'PROMOTION_GOODSCOUPON_RECEIVE_ALL', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/receiveAll', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券商品管理', 'PROMOTION_GOODSCOUPON_GOODS', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/goods', 0, 1, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', 'goodscoupon', '平台商品优惠券主动发券', 'PROMOTION_GOODSCOUPON_SEND_PAGE', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodscoupon/sendPage', 0, 1, '', 0, '', '', 1);

ALTER TABLE `xm_order_goods`
ADD COLUMN `goodscoupon_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券优惠价格' AFTER `goods_money`;

ALTER TABLE `xm_order`
ADD COLUMN `goodscoupon_id` int NOT NULL DEFAULT 0 COMMENT 'id' AFTER `balance_money`;

ALTER TABLE `xm_order`
ADD COLUMN `goodscoupon_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券优惠价格' AFTER `goodscoupon_id`;

ALTER TABLE `xm_promotion_goodscoupon`
    MODIFY COLUMN `get_type` int(11) NOT NULL DEFAULT 0 COMMENT '获取方式1订单2.直接领取3.活动领取4.后台发放';

ALTER TABLE `xm_order`
    ADD COLUMN `split_order` varchar(255) NULL COMMENT '拆分订单号' AFTER `is_vip`;

CREATE TABLE `xm_order_delete` (
    `order_id` int(11) NOT NULL COMMENT '订单id',
    `data` longtext NOT NULL COMMENT '订单数据',
    `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;