CREATE TABLE IF NOt EXISTS  `xm_enterprise_customer_service_group` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `enterprise_user_id` varchar(100) DEFAULT '' COMMENT '企业微信用户id',
    `xm_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的组别id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_enterprise_user_id` (`enterprise_user_id`) USING BTREE,
    KEY `idx_xm_group_id` (`xm_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业微信客服人员所属运营组别';

ALTER TABLE `xm_youpin`.`xm_enterprise_customer_service_group`
    ADD COLUMN `relation_key` varchar(50) DEFAULT '' COMMENT '关联键(表名字)' AFTER `xm_group_id`,
    ADD COLUMN `relation_value` varchar(50) DEFAULT '' COMMENT '关联键(关联表的主键值)' AFTER `relation_key`,
    ADD INDEX `idx_relation_key`(`relation_key`) USING BTREE,
    ADD INDEX `idx_relation_value`(`relation_value`) USING BTREE;