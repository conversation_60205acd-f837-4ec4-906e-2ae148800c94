CREATE TABLE IF NOT EXISTS `xm_recommend_reward_risk`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `date` varchar(20) NOT NULL DEFAULT '' COMMENT '日期',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推荐奖励风控记录表';

CREATE TABLE IF NOT EXISTS `xm_recommend_reward_black`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` integer NOT NULL DEFAULT '0' COMMENT '用户id',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推荐奖励黑名单表';
