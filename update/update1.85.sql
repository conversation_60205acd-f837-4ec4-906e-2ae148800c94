/**
 * czk
 * 2020/12/24
 * 统计报表菜单
 */
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '总览', 'MAIN_STAT', 'STAT_ROOT', 2, 'admin/stat/overView', 1, 4, '', 0, '', '', 1);
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '卖货统计', 'STAT_SALE_GOODS', 'STAT_ROOT', 2, 'admin/stat/saleGoods', 1, 5, '', 0, '', '', 1);

/**
 * czk
 * 2020/12/24
 * 统计报表总览
 */
CREATE TABLE `xm_main_stat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_active_num` int(11) NOT NULL DEFAULT '0' COMMENT '小程序活跃用户数',
  `reg_num` int(11) NOT NULL DEFAULT '0' COMMENT '新用户注册数量',
  `reg_rate` decimal(11,2) NOT NULL COMMENT '新用户占比',
  `free_shop_reg_num` int(11) NOT NULL DEFAULT '0' COMMENT '新免费店主数',
  `free_shop_active_num` int(11) NOT NULL DEFAULT '0' COMMENT '活跃免费店主数',
  `free_shop_reg_rate` decimal(11,2) NOT NULL COMMENT '新增免费店主占比',
  `vip_shop_reg_num` int(11) NOT NULL DEFAULT '0' COMMENT '新增vip店铺数',
  `buy_vip_shop_reg_num` int(11) NOT NULL DEFAULT '0' COMMENT '付费购买店铺数',
  `sale_vip_shop_amount` decimal(11,2) NOT NULL COMMENT '付费购买店铺总金额',
  `order_persons_num` int(11) NOT NULL DEFAULT '0' COMMENT '购买商品人数',
  `average_amount` decimal(11,2) NOT NULL COMMENT '客单价',
  `order_sale_amount` decimal(11,2) NOT NULL COMMENT '销售金额',
  `stat_time` datetime NOT NULL COMMENT '统计日期',
  `add_time` datetime NOT NULL COMMENT '创建时间',
  `edit_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='总览统计表';

/**
 * czk
 * 2020/12/24
 * 卖货统计表
 */
CREATE TABLE `xm_sale_goods_stat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mem_login_num` int(11) NOT NULL DEFAULT '0' COMMENT '小程序登录次数',
  `shop_num` int(11) NOT NULL DEFAULT '0' COMMENT '店铺总数量',
  `reg_num` int(11) NOT NULL DEFAULT '0' COMMENT '注册用户数',
  `order_persons_num` int(11) NOT NULL DEFAULT '0' COMMENT '下单人数（包含未支付）',
  `order_num` int(11) NOT NULL DEFAULT '0' COMMENT '下单数量（包含未支付）',
  `order_pay_persons_num` int(11) NOT NULL DEFAULT '0' COMMENT '付款人数',
  `order_pay_num` int(11) NOT NULL DEFAULT '0' COMMENT '付款订单数',
  `order_pay_amount` decimal(11,2) NOT NULL COMMENT '付款金额',
  `stat_time` datetime NOT NULL COMMENT '统计日期',
  `add_time` datetime NOT NULL COMMENT '创建时间',
  `edit_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='卖货统计表';