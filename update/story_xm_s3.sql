 -- 升级统计表
 CREATE TABLE `xm_upgrade_statistics` 
 ( 
 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
 `site_id` INT UNSIGNED NOT NULL COMMENT '店铺id', 
 `username` VARCHAR(128) NOT NULL COMMENT '手机号码', 
 `nickname` VARCHAR(128) DEFAULT '' COMMENT '昵称', 
 `identity` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '1.普通用户  2.店主', `trustee_mobile` VARCHAR(20) COMMENT '董事手机号码', 
 `manager_mobile` VARCHAR(20) COMMENT '经理手机号码', 
 `direct_mobile` VARCHAR(20) COMMENT '直推人手机号码', 
 `indirect_mobile` VARCHAR(20) COMMENT '间推人手机号码', 
 `direct_vip_num` INT NOT NULL DEFAULT 0 COMMENT '直接推荐vip人数', 
 `indirect_vip_num` INT NOT NULL DEFAULT 0 COMMENT '间接推荐vip人数', 
 `cumulative_reward` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计获得佣金', 
 `register_source` TINYINT NOT NULL DEFAULT 1 COMMENT '1.先迈商城  2.先迈app', 
 `consume_times` INT NOT NULL DEFAULT 0 COMMENT '消费次数', 
 `consume_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '消费金额', 
 `last_login_time` INT NOT NULL COMMENT '最后登录时间', 
 `register_time` INT NOT NULL COMMENT '注册时间', 
 `vip_expired_time` INT NOT NULL COMMENT 'vip到期时间', 
 `update_time` INT UNSIGNED NOT NULL COMMENT '变更时间', 
 `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间', 
 PRIMARY KEY (`id`) 
 ) ENGINE=INNODB CHARSET=utf8mb4 COMMENT "推荐升级统计"; 


INSERT INTO `xm_menu` ( `app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` ) VALUES ( ''admin'', ''adapay'', ''汇付天下支付编辑'', ''WECHAT_PAY_CONFIG'', ''CONFIG_PAY'', ''4'', ''adapay://admin/pay/config'', ''0'', ''1'', '''', ''0'', '''', '''', ''1'' );
