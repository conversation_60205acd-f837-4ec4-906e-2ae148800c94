
-- 经理组选品库表
/* CREATE TABLE `xm_manager_goods` ( 
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `xm_manage_uid` INT UNSIGNED NOT NULL COMMENT '先迈经理用户id', 
    `goods_id` INT NOT NULL COMMENT '商品id', 
    `goods_state` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0.待选  1.在售', 
    `on_sale_time` INT NOT NULL DEFAULT 0 COMMENT '上架时间', 
    `off_sale_time` INT NOT NULL DEFAULT 0 COMMENT '下架时间', 
    `create_time` INT NOT NULL DEFAULT 0 COMMENT '创建时间', PRIMARY KEY (`id`) 
    ) ENGINE=INNODB CHARSET=utf8 ;
ALTER TABLE `xm_manager_goods` ADD UNIQUE INDEX (`xm_manage_uid` , `goods_id`); 


-- 经理组配置表
CREATE TABLE `xm_manager_config` ( 
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT, 
    `xm_manage_uid` INT UNSIGNED NOT NULL COMMENT '先迈经理用户id', 
    `config_key` VARCHAR(255) NOT NULL COMMENT '配置项关键字', 
    `value` VARCHAR(5000) NOT NULL COMMENT '配置值json', 
    `config_desc` VARCHAR(1000) COMMENT '描述', 
    `create_time` INT NOT NULL DEFAULT 0 COMMENT '创建时间', 
    `modify_time` INT NOT NULL COMMENT '修改时间', 
    PRIMARY KEY (`id`) , 
    KEY (`xm_manage_uid`) 
    ) ENGINE=INNODB CHARSET=utf8;  */

    INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
    VALUES ('shop', '', '店铺配置', 'STORE_MANAGE', '', 1, 'business/StoreManage/storeManage', 1, 2, '', 0, '', '', 1);

    INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
    VALUES ('shop', '', '选品库', 'CHOOSE_GOODS_WAREHOUSE', '', 1, 'business/Goods/lists', 1, 4, '', 0, '', '', 1);

    INSERT INTO `xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
    VALUES ('shop', '', '营销库', 'MARKETING_WAREHOUSE', '', 1, 'business/StoreManage/marketPage', 1, 6, '', 0, '', '', 1);

CREATE TABLE `xm_manage_global_config`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type`      tinyint(4)    DEFAULT NULL COMMENT '配置类型（1分类、2广告、3营销库、4商品）',
    `config_key`       varchar(150)  DEFAULT NULL COMMENT '配置键',
    `config_value`     varchar(2000) DEFAULT NULL COMMENT 'JSON 配置值',
    PRIMARY KEY (`id`),
    KEY `config_type` (`config_type`),
    KEY `config_key` (`config_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='经理全局配置';

CREATE TABLE `xm_manage_private_config`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_type`       tinyint(4)   DEFAULT NULL COMMENT '配置类型（1分类、2广告、3营销库、4商品）',
    `config_key`        varchar(150) DEFAULT NULL COMMENT '配置键',
    `related_manage_id` int(11)      DEFAULT NULL COMMENT '关联经理(运营组)ID',
    `related_value`     int(11)      DEFAULT NULL COMMENT '关联值(理论上都是关联主键或int类型索引数据)',
    `sort`              smallint(6)  DEFAULT '0' COMMENT '关联各种类型的排序',
    PRIMARY KEY (`id`),
    KEY `config_type` (`config_type`),
    KEY `config_key` (`config_key`),
    KEY `related_manage_id` (`related_manage_id`),
    KEY `related_value` (`related_value`),
    KEY `sort` (`sort`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='经理私有配置';