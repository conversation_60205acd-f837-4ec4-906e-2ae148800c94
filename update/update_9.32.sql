CREATE TABLE IF NOT EXISTS `xm_text_extraction`
(
    `text_id` int(11) NOT NULL AUTO_INCREMENT,
    `text` text NULL COMMENT '文本',
    `source` varchar(50) NOT NULL DEFAULT '' COMMENT '来源',
    `source_id` integer NOT NULL DEFAULT 0 COMMENT '来源ID',
    `topk` int(11) NOT NULL default 0 COMMENT '提取参数',
    `extraction_result` varchar(2000) NULL COMMENT '提取结果',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`text_id`),
    KEY `idx_source` (`source`),
    KEY `idx_source_id` (`source_id`),
    KEY `idx_source_union` (`source`,`source_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文本提取表';

CREATE TABLE IF NOT EXISTS `xm_text_extraction_result`
(
    `extraction_result_id` int(11) NOT NULL AUTO_INCREMENT,
    `text_id` integer NOT NULL DEFAULT 0 COMMENT '文本Id',
    `keyword_text` varchar(100) NOT NULL DEFAULT '' COMMENT '关键字文本',
    `extraction_weight` float(10, 6) NOT NULL DEFAULT 0 COMMENT '提取权重',
    `associate_nums` integer NOT NULL DEFAULT 0 COMMENT '联想次数',
    `associate_hit` integer NOT NULL DEFAULT 0 COMMENT '联想命中次数',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`extraction_result_id`),
    KEY `idx_text_id` (`text_id`),
    KEY `idx_keyword_text` (`keyword_text`),
    KEY `idx_associate_nums` (`associate_nums`),
    KEY `idx_associate_hit` (`associate_hit`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文本提取结果表';

ALTER TABLE `xm_text_extraction`
    ADD COLUMN `is_enable` int NOT NULL DEFAULT 1 COMMENT '是否启用';

ALTER TABLE `xm_text_extraction`
    ADD INDEX `idx_is_enable`(`is_enable`) USING BTREE;


ALTER TABLE `xm_promotion_seckill`
    ADD COLUMN `keywords` varchar(255)  NOT NULL DEFAULT '' COMMENT '关键词' AFTER `name`;

ALTER TABLE `xm_promotion_topic`
    ADD COLUMN `keywords` varchar(255)  NOT NULL DEFAULT '' COMMENT '关键词' AFTER `topic_name`;

ALTER TABLE `xm_text_extraction_result`
    ADD COLUMN `source_sort` int NOT NULL DEFAULT 0 COMMENT '来源排序权重，越大排越前';

CREATE TABLE IF NOT EXISTS `xm_text_extraction_statistics`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `keyword_text` varchar(100) NOT NULL DEFAULT '' COMMENT '关键字文本',
    `associate_nums` integer NOT NULL DEFAULT 0 COMMENT '联想次数',
    `associate_hit` integer NOT NULL DEFAULT 0 COMMENT '联想命中次数',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_keyword_text` (`keyword_text`),
    KEY `idx_associate_nums` (`associate_nums`),
    KEY `idx_associate_hit` (`associate_hit`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文本提取统计表';


