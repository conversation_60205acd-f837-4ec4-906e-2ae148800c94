INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', 'leaguePoints', '抽奖活动管理', 'RAFFLE_MANAGEMENT', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/RaffleManagement/config', 1, 3, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ('admin', 'leaguePoints', '抽奖记录', 'RAFFLE_RECORD', 'RAFFLE_MANAGEMENT', 5, 'leaguePoints://admin/RaffleManagement/record', 1, 3, '', 0, '', '', 1);

-- 向配置表中添加抽奖活动基础配置
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`)
VALUES (0, 'admin', 'leaguepoints_raffle_config', '{"points":20}', '抽奖活动基础配置', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 创建抽奖奖项表
CREATE TABLE `xm_league_raffle_reward` (
  `reward_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '奖项ID',
  `reward_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '奖项类型（0谢谢参与，1优惠券）',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID（优惠券ID）',
  `reward_name` varchar(255) NOT NULL DEFAULT '' COMMENT '奖项名称',
  `probability` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '中奖概率（百分比）',
  `reward_limit` int(11) NOT NULL DEFAULT '0' COMMENT '奖项数量限制（-1表示不限）',
  `reward_count` int(11) NOT NULL DEFAULT '0' COMMENT '已发放数量',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0下架/删除，1正常）',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '下架/删除时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`reward_id`),
  KEY `idx_relation_id` (`relation_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='抽奖活动奖项表';

-- 创建抽奖记录表
CREATE TABLE `xm_league_raffle_record` (
  `record_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '消耗积分',
  `reward_id` int(11) NOT NULL DEFAULT '0' COMMENT '奖项ID',
  `reward_name` varchar(255) NOT NULL DEFAULT '' COMMENT '奖项名称',
  `reward_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '奖项类型（0谢谢参与，1优惠券）',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID（优惠券ID等）',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态（0待发放，100已发放）',
  `reward_relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '发放奖励的关联id',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '抽奖时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_reward_id` (`reward_id`),
  KEY `idx_relation_id` (`relation_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`),
  KEY `idx_reward_relation_id` (`reward_relation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='抽奖活动记录表';

-- 向抽奖奖项表添加默认的"谢谢参与"奖项
INSERT INTO `xm_league_raffle_reward` ( `reward_type`, `relation_id`, `reward_name`, `probability`, `reward_limit`, `reward_count`, `status`, `delete_time`,`create_time`, `update_time`)
VALUES (0, 0, '谢谢参与', 100.00, -1, 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

ALTER TABLE `xm_league_raffle_record`
    ADD COLUMN `raffle_setting` text COMMENT '奖项配置' AFTER `reward_relation_id`;
ALTER TABLE `xm_league_raffle_record`
    ADD COLUMN `roll` int default 0 COMMENT '摇点' AFTER `reward_relation_id`;

ALTER TABLE `xm_league_raffle_reward`
    ADD COLUMN `relation_value` varchar(100) default '' COMMENT '关联值（类型是话费充值存放充值的金额）' AFTER `relation_id`;

ALTER TABLE `xm_league_raffle_record`
    ADD COLUMN `recharge_mobile` varchar(11) default '' COMMENT '充值手机号' AFTER `raffle_setting`;
