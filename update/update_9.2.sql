ALTER TABLE `xm_goods_stat`
    ADD COLUMN `share_success_nums` int default 0 COMMENT '有效分享数';

ALTER TABLE `xm_access_track`
    ADD INDEX `idx_page_url`(`page_url`);

ALTER TABLE `xm_goods_stat`
    ADD COLUMN `in_cart_member_nums` int default 0 COMMENT '加购人数';

ALTER TABLE `xm_goods_stat`
    ADD COLUMN `sale_nums` int default 0 COMMENT '售出数量';

ALTER TABLE `xm_goods_stat`
    ADD COLUMN `sale_after_nums` int default 0 COMMENT '售后订单数量';

ALTER TABLE `xm_goods_stat`
    ADD COLUMN `refund_money` decimal(10,2) default 0 COMMENT '售后退款金额';