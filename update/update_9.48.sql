CREATE TABLE IF NOT EXISTS `xm_customer_speak_log`
(
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `reception_id` int(11) NOT NULL default 0 COMMENT '接待id',
    `kfid` varchar(100) NOT NULL default '' COMMENT '客服id',
    `external_userid` varchar(100) NOT NULL default '' COMMENT '外部联系人id',
    `reception_user_id` varchar(100) NOT NULL default '' COMMENT '接待人id',
    `origin` int(11) NOT NULL DEFAULT 0 COMMENT '消息来源。3-微信客户发送的消息 4-系统推送的事件消息 5-接待人员在企业微信客户端发送的消息',
    `msgtype` varchar(32) NOT NULL DEFAULT '' COMMENT '参照企微msgtype',
    `media_url` varchar(200) NOT NULL default '' COMMENT '素材地址',
    `content` text NOT NULL DEFAULT '' COMMENT '内容',
    `send_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间',
    `msg_data` text NULL COMMENT '消息数据',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`log_id`),
    KEY `idx_reception_id` (`reception_id`),
    KEY `idx_reception_user_id` (`reception_user_id`),
    KEY `idx_external_userid` (`external_userid`),
    KEY `idx_kfid` (`kfid`),
    KEY `idx_msgtype` (`msgtype`),
    KEY `idx_origin` (`origin`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接待聊天记录';

ALTER TABLE `xm_customer_speak_log`
    ADD COLUMN `media_name` varchar(100) NOT NULL DEFAULT '' COMMENT '资源名' AFTER `msgtype`;