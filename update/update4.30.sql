CREATE TABLE `xm_enterprise_tag_group` (
    `tag_group_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签组id',
    `enterprise_wechat_group_id` varchar(50) NOT NULL unique COMMENT '企微标签组id',
    `group_name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签组名',
    `order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序，越大排越前',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`tag_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='企业标签组表';

CREATE TABLE `xm_enterprise_tag` (
    `tag_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签id',
    `tag_group_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签组id',
    `enterprise_wechat_tag_id` varchar(50) NOT NULL unique COMMENT '企微标签id',
    `tag_name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签名',
    `order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序，越大排越前',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`tag_id`),
    KEY `idx_tag_group_id` (`tag_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='企业标签表';

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '企微同步', 'MEMBER_LISTS_ENTERPRISE_WECHAT', 'MEMBER_ROOT', 4, 'admin/member/enterprise_wechat', 0, 0, '', 0, '', '', 1);
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) VALUES ('admin', '', '企微同步', 'ENTERPRISE_WECHAT', 'MEMBER_INDEX', 4, 'admin/member/enterprise_wechat', 1, 200, '', 0, '', '', 1);

CREATE TABLE `xm_member_enterprise_tag` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签id',
    `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `tag_id` int(11) NOT NULL DEFAULT '0' COMMENT '标签id',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户标签表';

ALTER TABLE `xm_member_authorize`
    ADD COLUMN `oper_userid` varchar(255) NOT NULL DEFAULT '' COMMENT '企微客户userid' AFTER `wechat_official_openid`;