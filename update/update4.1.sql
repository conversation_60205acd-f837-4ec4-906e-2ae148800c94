CREATE TABLE `xm_member_authorize`  (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT 'member会员表唯一id',
    `wechat_unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信开放平台用户唯一标识',
    `wechat_applet_openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信小程序openid',
    `wechat_official_openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信公众号openid',
    `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`),
    INDEX `idx_member_id`(`member_id`) USING BTREE,
    INDEX `idx_wechat_unionid`(`wechat_unionid`) USING BTREE,
    INDEX `idx_wechat_applet_openid`(`wechat_applet_openid`) USING BTREE,
    INDEX `idx_wechat_official_openid`(`wechat_official_openid`) USING BTREE
);

# 授权变动记录表
CREATE TABLE `xm_member_authorize_change_log`  (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'member会员表唯一id',
    `old_auth_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '旧的授权信息id',
    `new_auth_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '新的授权信息id',
    `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`),
    INDEX `idx_member_id`(`member_id`) USING BTREE
);

# 先迈网和商城通讯记录表
CREATE TABLE `xm_xianmai_access_log`  (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `type` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '流出流入(0: 流入 1:流出)',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
    `method` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
    `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`)
);


ALTER TABLE `xm_member`
    ADD COLUMN `salt` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '盐值, token加密做逼退处理';


ALTER TABLE `xm_member_authorize_change_log`
    ADD COLUMN `type` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT '变更原因类型(暂定: 1:修改手机号 2:注销)' AFTER `new_auth_id`;


# 将xm_member表已有的授权信息复制一份到第三方授权表
INSERT INTO xm_member_authorize ( `member_id`, `wechat_unionid`, `wechat_applet_openid`, `wechat_official_openid` ) (
    SELECT
        0 AS member_id,
        wx_unionid AS wechat_unionid,
        weapp_openid AS wechat_applet_openid,
        wx_openid AS wechat_official_openid
    FROM
        xm_member
    WHERE
            wx_unionid != ''
       OR weapp_openid != ''
       OR wx_openid != '');

ALTER TABLE `xm_shop` modify column `mobile` varchar(15) NOT NULL DEFAULT '' COMMENT '联系手机号';

ALTER TABLE `xm_yp_sync_users`
ADD COLUMN `xm_recommend_mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '先迈用户推荐人的手机号' AFTER `uni_mobile`;

CREATE TABLE `xm_sync_change_record` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `type` varchar(20) COLLATE utf8_unicode_ci NOT NULL COMMENT '类型（update|add）',
        `old_mobile` varchar(20) COLLATE utf8_unicode_ci NOT NULL COMMENT '旧手机号',
        `new_mobile` varchar(20) COLLATE utf8_unicode_ci NOT NULL COMMENT '新手机号',
        `old_data` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '旧数据',
        `new_data` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '新数据',
        `created_at` int(11) DEFAULT NULL COMMENT '创建时间',
        `updated_at` int(11) DEFAULT NULL COMMENT '变更时间',
        PRIMARY KEY (`id`),
        KEY `old_mobile` (`old_mobile`),
        KEY `new_mobile` (`new_mobile`),
        KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

