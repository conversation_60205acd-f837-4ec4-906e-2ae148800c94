
alter table xm_order modify column `order_create_type` tinyint(2) unsigned DEFAULT '1' COMMENT '1=普通订单,2=周期购订单,3=秒杀订单,4=分享赚订单,5=砍价订单,6=迈豆专区订单,7=新人专享订单,8-拼团订单';
alter table xm_member_bank_account add column `province_id` int(11) DEFAULT NULL COMMENT '省id';
alter table xm_member_bank_account add column `city_id` int(11) DEFAULT NULL COMMENT '市id';
alter table xm_member_bank_account add column `branch_branch_bank_name` varchar(50) DEFAULT '' COMMENT '银行支行名称';
alter table xm_pay add `order_type` tinyint(3) DEFAULT '0' COMMENT '订单类型：0-普通订单，1-拼团订单';
alter table xm_website add `pintuan_rule` varchar(255) NOT NULL DEFAULT '' COMMENT '拼团规则';

CREATE TABLE `xm_pintuan` (
  `pintuan_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '拼团id',
  `pintuan_name` varchar(30) NOT NULL DEFAULT '' COMMENT '活动名称',
  `award_config` text NOT NULL COMMENT '拼团配置奖励',
  `promotion_type` varchar(255) NOT NULL DEFAULT '' COMMENT '活动类型：business-拼团商',
  `pintuan_num` int(11) NOT NULL DEFAULT '0' COMMENT '参团人数',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
  `is_recommend` int(11) NOT NULL DEFAULT '0' COMMENT '是否推荐',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `buy_num` int(11) NOT NULL DEFAULT '0' COMMENT '拼团限制购买',
  `show_pintuan_price` tinyint(1) NOT NULL COMMENT '显示拼团价',
  `is_single_buy` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否单独购买',
  `is_virtual_buy` tinyint(1) DEFAULT '0' COMMENT '是否虚拟成团',
  `is_promotion` tinyint(1) DEFAULT '0' COMMENT '是否团长优惠',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1活动进行中  2活动已结束  3失效  4删除）【暂时不用】',
  `group_num` int(11) NOT NULL DEFAULT '0' COMMENT '开团组数',
  `winning_num` int(11) NOT NULL DEFAULT '0' COMMENT '中奖人数',
  PRIMARY KEY (`pintuan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='拼团活动表';

CREATE TABLE `xm_pintuan_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pintuan_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团id',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
  `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT 'skuid【暂时不用】',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '活动库存',
  `pintuan_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '拼团价',
  `promotion_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '团长优惠价',
  `order_num` int(11) NOT NULL DEFAULT '0' COMMENT '拼团订单单数',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0.下架 1.上架',
  `sort` smallint(6) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_pintuan_id` (`pintuan_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='拼团商品表';

CREATE TABLE `xm_pintuan_group` (
  `group_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '拼团分组id',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
  `pintuan_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团活动id',
  `head_id` int(11) NOT NULL DEFAULT '0' COMMENT '团长id',
  `pintuan_num` int(11) NOT NULL DEFAULT '0' COMMENT '拼团数量',
  `pintuan_count` int(11) NOT NULL DEFAULT '1' COMMENT '当前数量',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '拼团结束时间',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '当前状态 0未支付 1拼团失败 2.组团中3.拼团成功',
  `is_virtual_buy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否虚拟成团',
  `is_single_buy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否单独购买',
  `is_promotion` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否团长优惠',
  `buy_num` int(11) NOT NULL DEFAULT '0' COMMENT '拼团限制购买',
  `head_member_img` varchar(255) NOT NULL DEFAULT '' COMMENT '组长会员头像',
  `head_nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '组长会员昵称',
  `award_config` text NOT NULL COMMENT '拼团配置奖励',
  `pintuan_price` decimal(10,2) NOT NULL COMMENT '拼团价',
  `promotion_price` decimal(10,2) NOT NULL COMMENT '团长优惠价',
  `winning_num` int(11) NOT NULL DEFAULT '0' COMMENT '中奖人数',
  PRIMARY KEY (`group_id`),
  KEY `idx_pintuan_id` (`pintuan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='拼团组';

CREATE TABLE `xm_pintuan_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pintuan_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团id',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '拼团订单编号',
  `out_trade_no` varchar(20) NOT NULL DEFAULT '' COMMENT '支付流水号',
  `group_id` int(11) NOT NULL DEFAULT '0' COMMENT '拼团分组id',
  `pintuan_status` int(11) NOT NULL DEFAULT '0' COMMENT '拼团状态(0未支付 1拼团失败 2组团中 3拼团成功 -1已失效【已关闭】)',
  `head_id` int(11) NOT NULL DEFAULT '1' COMMENT '团长id',
  `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单会员id',
  `member_img` varchar(255) NOT NULL DEFAULT '' COMMENT '会员头像图',
  `nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '会员昵称',
  `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '购买人手机',
  `inviter_id` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人id',
  `pay_status` tinyint(11) NOT NULL DEFAULT '0' COMMENT '支付状态 0=未支付，1=已支付',
  `pay_type` varchar(55) NOT NULL DEFAULT '' COMMENT '支付方式 ONLINE_PAY=在线支付,BALANCE =余额支付,OFFLINE_PAY=线下支付,MAIDOU = 迈豆支付',
  `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '订单支付时间',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '购买人姓名',
  `province_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人省id',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人市id',
  `district_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人区县id',
  `address` varchar(255) NOT NULL DEFAULT '' COMMENT '购买人地址',
  `full_address` varchar(255) NOT NULL DEFAULT '' COMMENT '购买人详细地址',
  `pay_money` decimal(10,2) NOT NULL COMMENT '支付金额',
  `win_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '中奖状态 0=未中奖，1=已中奖',
  `is_award` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已发放奖励：0-未发放，1-已发放',
  `order_type` int(11) NOT NULL DEFAULT '1' COMMENT '订单类型',
  `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品skuid',
  `sku_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `sku_image` varchar(255) NOT NULL DEFAULT '' COMMENT '商品图片',
  `sku_no` varchar(255) NOT NULL DEFAULT '',
  `sku_spec_format` varchar(1000) DEFAULT NULL COMMENT 'sku规格格式',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
  `trade_no` varchar(20) NOT NULL DEFAULT '' COMMENT '第三方交易流水号',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '商家id',
  `site_name` varchar(50) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `is_delete` int(11) DEFAULT '0' COMMENT '删除状态：0-未删除，时间戳-删除时间',
  `is_refund` tinyint(3) DEFAULT '0' COMMENT '未中奖退款，是否已退款：0-未退款，1-已退款',
  `refund_no` varchar(20) NOT NULL DEFAULT '' COMMENT '退款编号',
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='拼团订单';

/**
 * 拼团订单菜单
 * wjj
 * 2021-01-26
 */
INSERT INTO `xm_youpin`.`xm_menu`(`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
VALUES ( 'admin', '', '拼团开团列表', 'PINTUAN_GROUP', 'PINTUAN', 2, 'admin/pintuangrouporder/groupLists', 0, 110, '', 0, '', '', 1),
( 'admin', '', '拼团订单列表', 'PINTUAN_ORDER', 'PINTUAN', 2, 'admin/pintuangrouporder/orderLists', 0, 110, '', 0, '', '', 1);