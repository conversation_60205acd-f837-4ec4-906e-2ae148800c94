CREATE TABLE `xm_user_share_experience_goods`  (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_share_experience_id` INT UNSIGNED NOT NULL DEFAULT 0,
  `goods_id` INT UNSIGNED NOT NULL DEFAULT 0,
  `create_time` INT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  INDEX `user_share_experience_id`(`user_share_experience_id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE
) ENGINE = INNODB CHARACTER SET = utf8 ;


ALTER TABLE `xm_user_share_experience` CHARACTER SET = utf8mb4;

-- ALTER TABLE `xm_user_share_experience` ADD COLUMN `share_type` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'image:图片 video:视频' AFTER `image`;

ALTER TABLE `xm_user_share_experience` ADD COLUMN `share_resource` VARCHAR(5000) NOT NULL DEFAULT '' COMMENT '资源内容，多个逗号隔开' AFTER `image`;

ALTER TABLE `xm_user_share_experience` ADD COLUMN `member_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id，0则为平台' AFTER `share_resource`;

ALTER TABLE `xm_user_share_experience` ADD COLUMN `sort_weight` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重' AFTER `sort`;

ALTER TABLE `xm_user_share_experience` MODIFY COLUMN `status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '状态 -1.删除 0.隐藏 1.正常显示  2.审核中' AFTER `transmit_num`;

ALTER TABLE `xm_user_share_experience` MODIFY COLUMN `create_time` INT(11) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `content_type`;

ALTER TABLE `xm_user_share_experience` ADD INDEX `member_id`(`member_id`) USING BTREE;

ALTER TABLE `xm_user_share_experience` ADD COLUMN `nickname` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '昵称（平台发布专用）' AFTER `member_id`, 
ADD COLUMN `headimg` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '头像（平台发布专用）' AFTER `nickname`; 

ALTER TABLE `xm_user_share_experience` CHANGE `content_type` `content_type` TINYINT(1) DEFAULT 1 NOT NULL COMMENT '正文类型：1-富文本；2-外链接 3-图片 4-视频'; 


CREATE TABLE `xm_user_share_experience_comment`  (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_share_experience_id` INT UNSIGNED NOT NULL DEFAULT 0,
  `member_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论用户id',
  `reply_member_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '被回复的用户id',
  `pid` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '回复的评论id，0则为主评论',
  `status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '-1.删除 0.隐藏 1.显示 2.审核中',
  `comment_content` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '评论内容',
  `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `user_share_experience_id`(`user_share_experience_id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `reply_member_id`(`reply_member_id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = INNODB CHARACTER SET = utf8mb4;


-- 菜单栏相关
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '种草内容管理', 'SHARE_CONTENT_MANAGER', 'CONTENT_ROOT', 2, 'admin/UserShareExperience/lists', 1, 110, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '用户发布', 'USER_SHARE_EXPERIENCE_MEMBER', 'SHARE_CONTENT_MANAGER', 3, 'admin/UserShareExperience/user_lists', 1, 120, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '用户发布-内容详情', 'SHOW_USER_SHARE_EXPERIENCE_MANAGER', 'USER_SHARE_EXPERIENCE_MEMBER', 4, 'admin/UserShareExperience/user_detail', 0, 0, '', 0, '', '', 1);

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`) 
VALUES ('admin', '', '评论管理', 'USER_SHARE_EXPERIENCE_COMMENT', 'SHARE_CONTENT_MANAGER', 3, 'admin/UserShareExperienceComment/lists', 1, 130, '', 0, '', '', 1);


UPDATE `xm_menu` SET `parent` = 'SHARE_CONTENT_MANAGER' , `level` = '3', `title` = '平台发布' WHERE `name` = 'USER_SHARE_EXPERIENCE'; 
UPDATE `xm_menu` SET `level` = '4' WHERE `name` = 'EDIT_USER_SHARE_EXPERIENCE'; 
UPDATE `xm_menu` SET `level` = '4' WHERE `name` = 'ADD_USER_SHARE_EXPERIENCE';

ALTER TABLE `xm_user_share_experience`
    ADD INDEX `content_type`(`content_type`),
ADD INDEX `status`(`status`),
ADD INDEX `sort`(`sort`);

-- 等挥剑更新完旧数据在执行

ALTER TABLE `xm_user_share_experience_comment`
ADD COLUMN `reply_nums` int NOT NULL DEFAULT 0 COMMENT '评论数' AFTER `comment_content`,
ADD COLUMN `link` text NULL COMMENT '链' AFTER `reply_nums`;

ALTER TABLE `xm_user_share_experience_comment`
ADD COLUMN `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间' AFTER `create_time`;
