ALTER TABLE `xm_youpin`.`xm_goods`
    ADD COLUMN `virtual_sale_num` int(11) NULL DEFAULT 0 COMMENT '虚拟销量' AFTER `sale_num`;


ALTER TABLE `xm_youpin`.`xm_index_banner`
    ADD COLUMN `sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '位置标示' AFTER `state`,
ADD COLUMN `parent_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID' AFTER `sign`;

alter table xm_order modify column `order_create_type` tinyint(2) unsigned DEFAULT '1' COMMENT '1=普通订单,2=周期购订单,3=秒杀订单,4=分享赚订单,5=砍价订单,6=迈豆专区订单,7=新人专享订单';


TRUNCATE xm_index_banner;
INSERT INTO xm_index_banner (id, banner_name, state) VALUES (1, '首页广告管理', 1);
INSERT INTO xm_index_banner (id, banner_name, state) VALUES (2, '新人专区页面广告管理', 1);
INSERT INTO xm_index_banner (id, banner_name, state) VALUES (3, '优选单品页面广告管理', 1);
INSERT INTO xm_index_banner (id, banner_name, state) VALUES (4, '迈豆专区页面广告管理', 1);