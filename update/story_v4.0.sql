ALTER TABLE `xm_index_banner` ADD COLUMN `show_group` varchar(255) DEFAULT '' COMMENT '面向用户群显示：new_mem-新用户可见，old_mem-老用户可见，shop_mem-分销商可见';
update xm_menu set is_show = 0 where name = 'PROMOTION_CONFIG' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PROMOTION_SHOP' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PROMOTION_MEMBER' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'MOLDBABY_ACTIVITY' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'DEPOSIT_ACTIVITY' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PROMOTION_ADMIN_BARGAIN' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'ASSISTATAR_ACTIVITY' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PERIOD_BUY_INDEX' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PERIOD_BUY_ORDER' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'DISTRIBUTION_ASSIST' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'SHARE_BUY' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'LARGE_TURNTABLE' and app_module = 'admin';
update xm_menu set is_show = 0 where name = 'PROMOTION_TOPIC_GOODS_LIST' and app_module = 'admin';
update xm_menu set url = 'admin/promotion/platform' where name = 'PROMOTION_ROOT' and app_module = 'admin';
alter table `xm_promotion_topic` add column `topic_bg` varchar(255) NOT NULL DEFAULT '' COMMENT '专题背景';
alter table `xm_promotion_topic` add column `topic_type` varchar(255) NOT NULL DEFAULT 'short_time' COMMENT '活动类型：short_time-限时；long_time-长期有效';
alter table `xm_promotion_topic_goods` add column `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '0.下架 1.上架';
alter table `xm_promotion_topic_goods` add column `sort` smallint(6) NOT NULL DEFAULT '0' COMMENT '排序';