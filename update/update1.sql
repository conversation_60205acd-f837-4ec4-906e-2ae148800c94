/**
 * 新增店铺首页广告图表
 * hwg
 * 2020-08-31
 */
CREATE TABLE `xm_index_banner` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `banner_name` varchar(100) NOT NULL DEFAULT '' COMMENT 'banner名称',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT 'banner图片',
  `banner_url` varchar(100) NOT NULL DEFAULT '' COMMENT 'banner链接',
  `state` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '是否启用，1=启用，0=禁用',
  `start_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺首页广告图表';

/**
 * 新增店铺首页频道表
 * hwg
 * 2020-08-31
 */
CREATE TABLE `xm_index_channel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `channel_name` varchar(100) NOT NULL DEFAULT '' COMMENT '频道名称',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '频道图片',
  `channel_url` varchar(100) NOT NULL DEFAULT '' COMMENT '频道链接',
  `state` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '是否启用，1=启用，0=禁用',
  `start_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺首页频道表';

/**
 * 新增店铺首页活动表
 * hwg
 * 2020-08-31
 */
CREATE TABLE `xm_index_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_name` varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '活动图片',
  `activity_url` varchar(100) NOT NULL DEFAULT '' COMMENT '活动链接',
  `state` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '是否启用，1=启用，0=禁用',
  `start_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺首页活动表';

/**
 * 新增代理表
 * lyf
 * 2020-08-31
 */
CREATE TABLE `xm_agent` (
  `site_id` int(11) unsigned NOT NULL,
  `enterprise_name` varchar(200) NOT NULL DEFAULT '' COMMENT '企业名称',
  `legal_person_name` varchar(50) NOT NULL DEFAULT '' COMMENT '法人姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系方式',
  `username` varchar(255) NOT NULL DEFAULT '' COMMENT '管理员用户',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '代理开始时间表',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '代理结束时间表',
  `integral_config_id` int(11) NOT NULL DEFAULT '0' COMMENT '积分,xm_integral_config表id',
  `business_license_url` varchar(255) NOT NULL DEFAULT '' COMMENT '营业执照url',
  `contract_agreement_url` varchar(255) NOT NULL DEFAULT '' COMMENT '合同协议url',
  `cumulative_integral` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '累计充值积分',
  `surplus_integral` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '剩余可用积分',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='代理表';

/**
 * 新增积分配置表
 * lyf
 * 2020-08-31
 */
CREATE TABLE `xm_integral_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `integral` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '积分',
  `sales_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0' COMMENT '销售金额',
  `discount` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '折扣',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='积分配置表';

/**
 * 新增店铺表-代理id
 * lyf
 * 2020-08-31
 */
alter table xm_shop add column agent_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '代理id,xm_agent表id';

/**
 * 新增会员表-会员能否购物的状态
 * lyf
 * 2020-09-11
 */
alter table xm_member add column `is_shopping_status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '会员能否购物的状态，0禁止购物1正常';

/**
 * 修改店铺提现表-提现状态、审核拒绝理由、转账驳回理由
 * lyf
 * 2020-09-11
 */
alter table xm_shop_withdraw modify column `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态0待审核1.待转账2已转账3审核失败4转账失败';
alter table xm_shop_withdraw add column `apply_reject_memo` varchar(20) NOT NULL DEFAULT '' COMMENT '审核-拒绝-理由';
alter table xm_shop_withdraw add column `transfer_accounts_reject_memo` varchar(20) NOT NULL DEFAULT '' COMMENT '转账-驳回-理由';

/**
 * 新增订单-供应商id
 * fjq
 * 2020-08-31
 */
ALTER TABLE `xm_order`
ADD COLUMN `supply_shop_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '供应商id'
ADD COLUMN `supply_shop_name`  varchar (255)  NULL DEFAULT '' COMMENT '供应商名称' ,
ADD INDEX `idx_supply_shop_id` (`supply_shop_id`) USING BTREE ;

ALTER TABLE `xm_order_goods`
ADD COLUMN `supply_shop_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '供应商id' ,
ADD COLUMN `supply_shop_name`  varchar (255)  NULL DEFAULT '' COMMENT '供应商名称' ,
ADD INDEX `idx_supply_shop_id` (`supply_shop_id`) USING BTREE ;

/**
 * 新增订单-订单商品表
 * fjq
 * 2020-08-31
 */
ALTER TABLE `xm_order_goods`
ADD COLUMN `sku_spec_format`  varchar(1000) NULL COMMENT 'sku规格格式',
ADD COLUMN  `add_price` decimal(10,2) unsigned NOT NULL DEFAULT 0.00 COMMENT '商品加价',
ADD COLUMN  `single_price` decimal(10,2) unsigned NOT NULL DEFAULT 0.00 COMMENT '商品单价（去除加价）',
ADD COLUMN `goods_name`  varchar(255) NULL DEFAULT '' COMMENT '商品名称(去掉规格名)',
ADD COLUMN `reward_shop_rate`  tinyint(3) NULL DEFAULT 0 COMMENT '佣金比例，奖励给店主的佣金比例',
;



/**
 * 新增店铺商品表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `add_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品加价',
  `status` tinyint(1) unsigned  NOT NULL DEFAULT '0' COMMENT '是否上架，1=上架，0=下架',
  `start_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '上架时间 0为立即上架',
  `end_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '下架时间 0为不限时下架',
  `sort` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `sale_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '该店铺的商品销量',
  `browse_num` int(11) NOT NULL DEFAULT '0' COMMENT '该店铺的商品浏览量',
  `sale_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '售价(包括收益)',
  `income_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '收益(包括佣金，加价)',
  `reward_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '佣金',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `xm_goods_id` int(11) unsigned NOT NULL COMMENT '同步过来的先迈商品ID',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺商品加价表';


/**
 * 店铺商品视图
 * hxr
 * 2020-09-01
 */
CREATE VIEW view_shop_goods as 
SELECT
	g.`goods_id`, `goods_name`, `goods_class`, `goods_class_name`, `goods_attr_class`, `goods_attr_name`,
	sg.`site_id`, `site_name`, `website_id`, `category_id`, `category_id_1`, `category_id_2`, `category_id_3`, `category_name`, `brand_id`, `brand_name`, `goods_image`, `goods_content`,
	`is_own`, IF( goods_state = 0, 0, sg.`status` ) AS goods_state, `verify_state`, `verify_state_remark`, ( `price` + `price`*`reward_shop_rate`/100 + sg.add_price ) AS price,
	( `market_price` + sg.add_price ) AS market_price, `cost_price`, `goods_stock`, `goods_stock_alarm`, `is_virtual`, `virtual_indate`, `is_free_shipping`,
	`shipping_template`, `goods_spec_format`, `goods_attr_format`, `is_delete`, `introduction`, `keywords`, `unit`, sg.`sort`, sg.`create_time`, sg.`update_time`, `commission_rate`,
	`video_url`, `discount_id`, `seckill_id`, `topic_id`, `pintuan_id`, `bargain_id`, `sale_num`, `goods_shop_category_ids`, `evaluate`, `evaluate_shaitu`,
	`evaluate_shipin`, `evaluate_zhuiping`, `evaluate_haoping`, `evaluate_zhongping`, `evaluate_chaping`, `is_fenxiao`, `fenxiao_type`, `supplier_id`, `sku_id`, `reward_shop_rate` 
FROM
	xm_shop_goods AS sg LEFT JOIN xm_goods AS g ON sg.goods_id = g.goods_id;
	


/**
 * 新增店铺装修广告表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_diy_banner` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `banner_id` int(11) unsigned NOT NULL COMMENT 'xm_index_banner ID',
  `sort` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `position` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '位置 1:首页',
  `shop_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 显示 0 隐藏',
  `prestore_status` tinyint(1) NOT NULL DEFAULT '-1' COMMENT '-1 不作修改 0 隐藏 1 显示   预编辑，当点击应用到店铺则覆盖shop_status',
  `prestore_sort` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '预设 排序 -1不作修改',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺装修广告表';


/**
 * 新增店铺装修频道表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_diy_channel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `channel_id` int(11) unsigned NOT NULL COMMENT 'xm_index_channel ID',
  `sort` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `position` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '位置 1:首页',
  `shop_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 显示 0 隐藏',
  `prestore_status` tinyint(1) NOT NULL DEFAULT '-1' COMMENT '-1 不作修改 0 隐藏 1 显示   预编辑，当点击应用到店铺则覆盖shop_status',
  `prestore_sort` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '预设 排序 -1不作修改',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺装修频道表';

/**
 * 新增店铺装修活动表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_diy_activity` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `activity_id` int(11) unsigned NOT NULL COMMENT 'xm_index_activity ID',
  `sort` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `position` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '位置 1:首页',
  `shop_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 显示 0 隐藏',
  `prestore_status` tinyint(1) NOT NULL DEFAULT '-1' COMMENT '-1 不作修改 0 隐藏 1 显示   预编辑，当点击应用到店铺则覆盖shop_status',
  `prestore_sort` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '预设 排序 -1不作修改',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺装修活动表';



/**
 * 新增店铺装修商品模块表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_diy_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT 'goods ID',
  `sort` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `position` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '位置 1:首页',
  `status` tinyint(1) unsigned unsigned  NOT NULL DEFAULT '0' COMMENT '是否上架，1=上架，0=下架, 可能同时存在多个样式模板，上架的模板才显示',
  `type` tinyint(1) unsigned  NOT NULL DEFAULT '1' COMMENT '模块样式模板 1两列商品、2三列商品、3列表商品',
  `shop_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 显示 0 隐藏',
  `prestore_status` tinyint(1) NOT NULL DEFAULT '-1' COMMENT '-1 不作修改 0 隐藏 1 显示   预编辑，当点击应用到店铺则覆盖shop_status',
  `prestore_sort` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '预设 排序 -1不作修改',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺装修商品模块表';

/**
 * 供应商表
 * xrf
 */
ALTER TABLE `xm_supplier`
ADD COLUMN `reward_shop_rate` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店主返佣比例(单位：%)';

ALTER TABLE `xm_supplier`
ADD COLUMN `supplier_state`  tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '供应商状态；0：禁用；1：启用';


/**
 * 商品表
 * xrf
 */
ALTER TABLE `xm_goods`
MODIFY COLUMN `site_id`  int(11) NOT NULL DEFAULT 0 COMMENT '所属店铺id（改为关联供应商：xm_xm_supplier.supplier_id）';

ALTER TABLE `xm_goods`
MODIFY COLUMN `supplier_id`  int(11) NOT NULL DEFAULT 0 COMMENT '供应商id（冗余）';

ALTER TABLE `xm_goods`
MODIFY COLUMN `site_name`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '所属店铺名称（供应商名称）';

ALTER TABLE `xm_goods`
MODIFY COLUMN `price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '商品价格（取第一个sku）（供货价）';

ALTER TABLE `xm_goods`
MODIFY COLUMN `market_price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '市场价格（取第一个sku）（原价）';

ALTER TABLE `xm_goods`
MODIFY COLUMN `cost_price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成本价（取第一个sku）（供应商价格）';

ALTER TABLE `xm_goods`
ADD COLUMN `reward_shop_rate` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店主返佣比例(单位：%)';

ALTER TABLE `xm_goods`
ADD COLUMN `reward_company_rate` decimal(3,1) UNSIGNED NOT NULL DEFAULT 0.0 COMMENT '公司返佣比例(单位：%)';


/**
 * 商品sku表
 * xrf
 */
ALTER TABLE `xm_goods_sku`
MODIFY COLUMN `price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'sku单价（供货价）';

ALTER TABLE `xm_goods_sku`
MODIFY COLUMN `market_price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'sku市场价（原价）';

ALTER TABLE `xm_goods_sku`
MODIFY COLUMN `cost_price`  decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'sku成本价（供应商价格）';


/**
 * 商品分类表
 * xrf
 */
ALTER TABLE `xm_goods_category`
ADD COLUMN `reward_company_rate` decimal(3,1) UNSIGNED NOT NULL DEFAULT 0.0 COMMENT '公司返佣比例(单位：%)';


/**
 * 站内信表
 * xrf
 */
CREATE TABLE `xm_website_message` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '消息标题',
  `content` text NOT NULL COMMENT '内容',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '消息类型；0：站内信、1：活动通知、2：课程通知',
  `send_to_agent` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送给代理',
  `send_to_shop` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送给店铺',
  `send_to_member` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送给用户',
  `send_to_part` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送给部分用户',
  `send_time_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间类型；0：立即发送，1：定时发送',
  `send_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态；0：待发送、1：已发送、2：发送失败',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `IDX_type` (`type`) USING BTREE,
  KEY `IDX_send_to_part` (`send_to_part`) USING BTREE,
  KEY `IDX_state` (`state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='站内信表';


/**
 * 站内信部分用户表
 * xrf
 */
CREATE TABLE `xm_website_message_member` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `message_id` int(10) unsigned NOT NULL COMMENT 'xm_website_message_id',
  `data` mediumtext COMMENT '用户数据（json）',
  PRIMARY KEY (`id`),
  KEY `IDX_message_id` (`message_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='站内信部分用户信息表';


/**
 * 站内信发送记录表
 * xrf
 */
CREATE TABLE `xm_website_message_send_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `message_id` int(10) unsigned NOT NULL COMMENT 'website_message_id',
  `user_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '用户类型；0：用户，1：店铺，2：代理',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id,根据user_type关联不同的用户表',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态；0：未读、1：已读',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `IDX_message_id` (`message_id`) USING BTREE,
  KEY `IDX_user_type` (`user_type`) USING BTREE,
  KEY `IDX_user_id` (`user_id`) USING BTREE,
  KEY `IDX_state` (`state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='站内信发送记录表';


/**
 * 新增店铺装修商品模块表
 * hxr
 * 2020-09-01
 */
CREATE TABLE `xm_shop_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` int(11) unsigned NOT NULL COMMENT '店铺ID(xm_shop.site_id)',
  `order_id` int(11) unsigned NOT NULL COMMENT '订单id(xm_order.order_id)',
  `supplier_id` int(11) unsigned NOT NULL COMMENT '供应商id(xm_supplier.supplier_id)',
  `order_no` int(11) unsigned NOT NULL COMMENT '订单编号(xm_order.order_no)',
  `income_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '收益',
  `reward_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '佣金',
  `add_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '差价(订单总加价)  ',
  `create_time` int(11) unsigned  NOT NULL DEFAULT '0' COMMENT '创建时间',
  `share_money` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分销客的佣金',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺订单表';

/**
 * 店铺表
 * hwg
 * 202-09-03
 */
ALTER TABLE `xm_shop`
MODIFY COLUMN `ww` varchar(20) NOT NULL DEFAULT '' COMMENT '联系人阿里旺旺',
MODIFY COLUMN `shop_status` int(11) NOT NULL DEFAULT '1' COMMENT '店铺经营状态（0.已到期 1.正常 2.冻结）';

/**
 * 新增商品搜索热词表
 * hhj
 * 2020-09-03
 */
CREATE TABLE `xm_shop_goods_hot_words`(
   `id` INT(10) UNSIGNED NOT NULL auto_increment COMMENT '自增id',
   `shop_id` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺id',
   `word` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '热词',
   `create_time` INT(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
   `update_time` INT(10) NOT NULL DEFAULT 0 COMMENT '更新时间',
   PRIMARY KEY (`id`),
   KEY `idx_shop_id` (`shop_id`) USING BTREE
)ENGINE=INNODB DEFAULT charset=UTF8 COMMENT='商品搜索热词表';


/**
 * 协议管理表
 * chj
 * 2020-09-07
 */
CREATE TABLE `xm_protocol` (
`protocol_id` INT(10) unsigned NOT NULL AUTO_INCREMENT,
`terminal_type` TINYINT unsigned NOT NULL DEFAULT 1 COMMENT '终端(1.店主app 2.c端小程序)',
`protocol_type` TINYINT unsigned NOT NULL DEFAULT 1 COMMENT '协议(1.用户协议 2.隐私政策)',
`content` TEXT NOT NULL COMMENT '协议内容',
`update_time` INT unsigned NOT NULL COMMENT '修改时间',
`create_time` INT unsigned NOT NULL COMMENT '创建时间',
PRIMARY KEY (`protocol_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='协议管理表';

/**
 * 问题类型表
 * chj
 * 2020-09-08
 */
CREATE TABLE `xm_problem` (
`id` INT unsigned NOT NULL AUTO_INCREMENT,
`problem_name` VARCHAR(32) NOT NULL COMMENT '问题类型',
`problem_status` TINYINT unsigned NOT NULL DEFAULT 0 COMMENT '状态(0.禁用.1.启用)',
`create_time` INT unsigned NOT NULL COMMENT '创建时间', PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='如何成为店长表';


/**
 * 意见反馈表
 * chj
 * 2020-09-08
 */
CREATE TABLE `xm_complain_advice` (
`id` INT unsigned NOT NULL AUTO_INCREMENT,
`problem_id` INT unsigned NOT NULL COMMENT '问题类型id',
`member_id` INT unsigned NOT NULL COMMENT '会员id',
`status` TINYINT unsigned NOT NULL DEFAULT 1 COMMENT '状态(1.待回复 2.已回复)',
`content` TEXT COMMENT '反馈意见',
`reply_content` VARCHAR(32) COMMENT '回复内容',
`reply_time` INT unsigned COMMENT '回复时间',
`create_time` INT unsigned NOT NULL COMMENT '提交时间',
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8  COMMENT='意见反馈表';


/**
 * 意见反馈图片表
 * chj
 * 2020-09-08
 */
CREATE TABLE `xm_complain_advice_image` (
`id` BIGINT unsigned NOT NULL AUTO_INCREMENT,
`complain_advice_id` INT unsigned NOT NULL COMMENT '意见反馈id',
`complain_advice_image_url` VARCHAR(255) NOT NULL COMMENT '图片地址',
`create_time` INT unsigned NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='意见反馈图片表';



/**
 * adapay支付配置
 * fjq
 * 2020-09-07
 */
 INSERT INTO `xm_addon` ( `name`, `icon`, `title`, `description`, `status`, `author`, `version`, `content`, `create_time`) VALUES ('adapay', 'addon/adapay/icon.png', '汇付天下', '汇付天下功能', 1, '', '1.0', '', 1598931790);
INSERT INTO `xm_youpin`.`xm_config` (`id`, `site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('783', '0', 'admin', 'ADAPAY_PAY_CONFIG', '{\"api_key_live\":\"api_live_17ac5b22-9736-409f-a6c5-c3ab7b461b7b\",\"api_key_test\":\"api_test_12b20c91-a28e-49d0-868d-919e7ca40a47\",\"rsa_public_key\":\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG\\/356Ur3cPbc2Fe8ArNd\\/0gZbC9q56Eb16JTkVNA\\/fye4SXznWxdyBPR7+guuJZHc\\/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB\",\"rsa_private_key\":\"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAK4t\\/7tIl0+djisJ7ALKEQo17QT8F4e+5XGMayDxSklHmdY9H\\/SuX48Lq\\/CWPkgYk51vgRCC5YQJOxlK3IHdSPKcKf8cO3F+V7onq40zSEbD+WMT8W\\/DOol211jGWC0zO3suGiGw+0IGRylS99+3vu0bCN9xFEBSikkLBXsBi8PPAgMBAAECgYA38fpj80r2fYq+dC9kLfcPKob4xOfh6ATWVd3K9PWBWAgncfajpqZ20a7mVbuneI1hUgFIhmKl6DkdRJhS9fqJusoSdTE676mmq2py87u0wUPbYZO09w8oaql8GlbF2PhMfwLhwLLue82oHVg3yAgDMLtmFcc4Uz2C4KcYeig8oQJBANW8EZxX313gOHFyaBg1LeRw2KYBCekinmBXK7ghBoDsh6TwWL0h+v8eQMtgA8uMFOWc9h3Wn2ywMWMnel4X5pkCQQDQn4gU2NfAuWTNlYT17OE10vYdS4FgVK8DHc3n65n9qM6fLuIuPXMd4DUvB2pIScQuh+vwD1DxQuxb1W1SqsanAkEAtmnNae7BL0CXS5OF7estIMOC06MdT0EBhF3BbWOaRlwYpJeQVNL0gdnTGP\\/4HTeP+ivNDchHxh5V+DcQQ9AIOQJBAMlf9F8LINdrHR5EoV8xFAJ8bAzDAVMW6wg7ELI4\\/R+Yfjmxa6nurtu7vBp1MeYtLi0sDlZesmbvjm7miOwu5CkCQQCxpy2QU6SI+ylEoMYjGB\\/yVbheqyXYN85lxIg5JqIW5Nj26FjvVM0Ahi\\/miYiKkWBo1XW+AStDQ2GzAiXQysPd\",\"app_id\":\"app_7b9c55d4-81e2-44a7-9114-8bf38f549197\",\"notify_url\":\"\",\"pay_status\":1}', '汇付天下', '1', '1598843251', '1599472029');

/**
 * 店铺用户访问日志
 * wjj
 * 2020-09-08
 */
CREATE TABLE `xm_shop_member_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `shop_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id',
  `member_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `scene_id` int(10) NOT NULL DEFAULT '0' COMMENT '场景值',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='店铺用户访问日志';


/**
 * 银行管理表
 * chj
 * 2020-09-09
 */
CREATE TABLE `xm_bank` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `bank_name` varchar(32) NOT NULL COMMENT '银行名称',
 `code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行代码',
 `bank_status` tinyint(1) unsigned NOT NULL COMMENT '0.禁用 1.启用',
 `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='银行管理表';

/**
 插入银行数据
 */
INSERT INTO `xm_bank` (`id`, `bank_name`, `code`, `bank_status`, `create_time`) VALUES
(1, '中国工商银行', '********', 1, **********),
(2, '中国农业银行', '********', 1, **********),
(3, '中国银行', '********', 1, **********),
(4, '中国建设银行', '********', 1, **********),
(5, '中国邮政储蓄银行股份有限公司', '********', 1, **********),
(6, '平安银行股份有限公司', '********', 1, **********),
(7, '中国民生银行', '********', 1, **********),
(8, '中国光大银行', '********', 1, **********),
(9, '广东发展银行', '********', 1, **********),
(10, '中信银行', '********', 1, **********),
(11, '兴业银行', '********', 1, **********),
(12, '华夏银行', '********', 1, **********),
(13, '招商银行', '********', 1, **********),
(14, '上海浦东发展银行', '********', 1, **********),
(15, '交通银行', '********', 1, **********);

/**
 * 短信发送记录表
 * cnn
 * 2020-09-09
 */
CREATE TABLE `xm_verify_code` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `mobile` char(11) NOT NULL COMMENT '手机号',
  `type` tinyint(3) unsigned NOT NULL COMMENT '验证码类型(1注册 2修改密码3短信登录4安全验证5修改用户账号)',
  `code` smallint(5) unsigned DEFAULT '0' COMMENT '验证码',
  `cnt` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '次数',
  `instime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `mobile` (`mobile`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='短信发送记录表';

/**
 * 店实认证表 添加 银行预留的手机号字段 (settlement_bank_account_mobile)
 * cnn
 * 2020-09-09
 */
ALTER TABLE `xm_shop_cert` ADD  `settlement_bank_account_mobile` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '银行预留的手机号';

/**
 * 初始化店铺首页标签链接
 * hxr 
 * 2020-09-09
 */
UPDATE `xm_link` SET `wap_url` = '/otherpages/shop/home/<USER>' WHERE `name` = 'SHOP_INDEX';
INSERT INTO `xm_link`(`addon_name`, `name`, `title`, `web_url`, `wap_url`, `icon`, `support_diy_view`) VALUES ('', 'OLD_SHOP_INDEX', '原店铺首页', '', '/otherpages/shop/index/index', '', 'DIY_VIEW_SHOP');

/**
 * 订单商品增加退款交易流水号
 * fjq
 * 2020-09-09
 */
ALTER TABLE `xm_order_goods`
ADD COLUMN `refund_trade_no`  varchar(255) NULL DEFAULT NULL COMMENT '退款交易号(存第三方的退款凭证)';


ALTER TABLE `xm_protocol` ADD COLUMN `status` TINYINT(1) unsigned DEFAULT 0 NOT NULL COMMENT '0.禁用 1.启用' AFTER `content`;

/**
 * 店铺与用户增加绑定有效期
 * wjj
 * 2020-09-09
 */
 ALTER TABLE `xm_shop_member`
 ADD COLUMN `expire_time` int(11) not null default 0 COMMENT '关系绑定有效期，-1为永久有效',
 ADD COLUMN `lock_time` int(11) not null default 0 COMMENT '关系锁定时间',
 ADD COLUMN `unlock_time` int(11) null COMMENT '关系解锁时间';

 
 /**
 * xm_goods_browse商品浏览表增加goods_id 索引
 * hxr 
 * 2020-09-10
 */

 ALTER TABLE `xm_goods_browse` ADD INDEX `IDX_ns_goods_browse_goods_id`(`goods_id`);


 /**
 * 修改商户信息类型
 * fjq
 * 2020-09-10
 */
 ALTER TABLE `xm_pay`
MODIFY COLUMN `mch_info`  text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商户信息' ;



 /**
 * xm_order订单表增加site_id 索引
 * gwj 
 * 2020-09-10
 */
 ALTER TABLE `xm_order` ADD INDEX `IDX_ns_order_site_id`(`site_id`);

 
 /**
 * xm_order_goods订单表增加order_id 索引
 * gwj 
 * 2020-09-10
 */
 ALTER TABLE `xm_order_goods` ADD INDEX `IDX_ns_order_goods_order_id`(`order_id`);

 /**
 * xm_shop_cert 增加字段 bank_id银行ID
 * cnn
 * 2020-09-10
 */
ALTER TABLE `xm_shop_cert` ADD `bank_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '银行ID' AFTER `bank_type`;


/**
 * 积分充值记录表
 * chj
 * 2020-09-10
 */
CREATE TABLE `xm_integral_recharge` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`site_id` INT NOT NULL COMMENT '代理商id',
`recharge_no` VARCHAR(20) NOT NULL COMMENT '充值订单号',
`recharge_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '充值金额',
`recharge_integral` INT UNSIGNED NOT NULL COMMENT '充值积分',
`discount` TINYINT UNSIGNED NOT NULL COMMENT '折扣',
`recharge_time` INT UNSIGNED NOT NULL COMMENT '充值时间',
`create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='积分充值记录表';

/**
 * 店铺用户解/锁变更日志
 * wjj
 * 2020-09-08
 */
CREATE TABLE `xm_shop_member_change_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `shop_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id',
  `member_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `action` int(10) NOT NULL DEFAULT 1 COMMENT '变更动作，1锁定关系，2解锁关系',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `remark` varchar(255) NULL COMMENT '备注说明',
  PRIMARY KEY (`id`),
  KEY `shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='店铺用户关系变更日志';

/**
 * 商品浏览历史记录，增加创建时间
 * wjj
 * 2020-09-11
 */
ALTER TABLE `xm_goods_browse`
ADD COLUMN `create_time` int(11) NOT NULL COMMENT '创建时间';


/**
 * 订单支付方式
 * fjq
 * 2020-09-11
 */
ALTER TABLE `xm_pay`
ADD COLUMN `app_type`  varchar(20) NULL,
ADD COLUMN `app_type_name`  varchar(50) NULL ;


ALTER TABLE `xm_order`
ADD COLUMN `app_type`  varchar(20) NULL,
ADD COLUMN `app_type_name`  varchar(50) NULL ;

/**
 * 汇付天下菜单配置
 * fjq
 * 2020-09-11
 */
INSERT INTO `xm_youpin`.`xm_menu` ( `app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` ) VALUES ( ''admin'', ''adapay'', ''汇付天下支付编辑'', ''WECHAT_PAY_CONFIG'', ''CONFIG_PAY'', ''4'', ''adapay://admin/pay/config'', ''0'', ''1'', '''', ''0'', '''', '''', ''1'' );

/**
 新增member_id
 */
ALTER TABLE `xm_shop_cert` ADD `member_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'shop_bank_info表的商户下的用户id' AFTER `settlement_bank_address`;

/**
 店主绑定银行卡信息
 */
CREATE TABLE `xm_shop_bank_info` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `adapay_id` varchar(64) NOT NULL COMMENT '由 Adapay 生成的结算账户对象 id',
 `member_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商户下的用户id 存shop表的site_id',
 `card_id` varchar(64) NOT NULL DEFAULT '' COMMENT '银行卡号',
 `card_name` varchar(100) NOT NULL DEFAULT '' COMMENT '银行卡对应的户名',
 `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态 1 正常 0禁用',
 `cert_id` varchar(18) NOT NULL DEFAULT '' COMMENT '身份证号',
 `cert_type` varchar(20) NOT NULL DEFAULT '00' COMMENT '证件类型，仅支持：00-身份证，银行账户类型为对私时，必填',
 `tel_no` varchar(20) NOT NULL DEFAULT '0' COMMENT '手机号',
 `bank_code` varchar(20) NOT NULL DEFAULT '0' COMMENT '银行编码',
 `bank_name` varchar(64) NOT NULL DEFAULT '' COMMENT '开户银行名称',
 `bank_acct_type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '银行账户类型：1-对公；2-对私',
 `prov_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在省份编码',
 `area_code` varchar(20) NOT NULL DEFAULT '' COMMENT '银行账户开户银行所在地区编码',
 `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
 `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '编辑时间',
 PRIMARY KEY (`id`),
 KEY `member_id` (`member_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='店主绑定银行卡信息';


ALTER TABLE `xm_pay`
MODIFY COLUMN `mch_info` text NOT NULL default '' COMMENT '商户信息' ;


/**
    document表 视频网址+视频封面
 */
ALTER TABLE `xm_document` ADD COLUMN `video_url` VARCHAR(255) NULL COMMENT '视频网址' AFTER `title`,
ADD COLUMN `video_cover` VARCHAR(255) NULL COMMENT '视频封面' AFTER `video_url`;


/**
 店铺订单分销表
 fjq
 2020-09-16
 */
CREATE TABLE `xm_shop_order_share` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) unsigned NOT NULL COMMENT '订单id(xm_order.order_id)',
  `order_no` varchar(20) NOT NULL COMMENT '订单编号',
  `shop_id` int(11) unsigned NOT NULL COMMENT '店铺id(xm_shop.site_id)',
  `member_id` int(11) unsigned NOT NULL COMMENT '用户id，奖励人id(xm_member.member_id)',
  `fee_rate` decimal(10,2) NOT NULL,
  `fee_type` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '佣金类型 1=分享佣金，2=购买佣金',
  `fee` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '佣金',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1=未发放, 2=已发放',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `settle_time` int(11) NOT NULL DEFAULT '0' COMMENT '结算时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_order_no` (`order_no`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE,
  KEY `idx_fee_type` (`fee_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



/**
 * 增加分销客佣金表
 * lyf
 * 2020-09-16
 */
CREATE TABLE `xm_distribution_customers_brokerage` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `grade` varchar (6) NOT NULL DEFAULT '' COMMENT '分销客级别',
  `full_brokerage` decimal(10,2) unsigned NOT NULL DEFAULT 0 COMMENT '佣金满多少元',
  `share_proportion` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '分享比例，如字段值是8，比例就是8%',
  `purchase_proportion` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '购买比例，如字段值是8，比例就是8%',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='分销客佣金表';

/**
 * 修改店铺表-新增低价开团字段、绑定状态
 * lyf
 * 2020-09-16
 */
alter table xm_shop add column `low_price_group` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '低价拼团，0否1是';
alter table xm_shop add column `bing_status` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '绑定状态，1弱绑定2强绑定';

/*
 * 会员表增加索引
 * hwg
 * 2020-09-16
*/
ALTER TABLE `xm_member`
ADD KEY `idx_reg_time`(`reg_time`);

/**
 * 新增购买店铺设置
 * xrf
 */
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('0', 'admin', 'BUY_SHOP_CONFIG', '{\"integral\":\"0\"}', '店铺购买积分', '1', '1600226243', '0');

/**
 * 申请分销客记录
 * wjj
 * 2020-09-16
 */
CREATE TABLE `xm_apply_distributor_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) unsigned NOT NULL COMMENT '申请会员id',
  `shop_id` int(11) unsigned NOT NULL COMMENT '申请店铺',
  `bind_status` tinyint(2) NOT NULL COMMENT '绑定状态，0:弱绑定，1：强绑定',
  `audit_status` tinyint(2) NOT NULL COMMENT '审核状态：0待审核，1：审核通过，2审核拒绝',
  `remark` varchar(255) NULL COMMENT '审核备注',
  `update_time` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `shop_id` (`shop_id`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='申请分销客记录';

/**
 * 分销客表
 * wjj
 * 2020-09-16
 */
CREATE TABLE `xm_distributor`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) unsigned NOT NULL COMMENT '会员id',
  `shop_id` int(11) unsigned NOT NULL COMMENT '绑定店铺',
  `dcb_id` int(11) unsigned NOT NULL COMMENT 'xm_distribution_customers_brokerage表id',
  `bind_status` tinyint(2) NOT NULL COMMENT '绑定状态，0:弱绑定，1：强绑定',
  `status` tinyint(2) NOT NULL COMMENT '状态：0禁用，1：启用',
  `remark` varchar(255) NULL COMMENT '备注',
  `success_time` tinyint(2) NOT NULL DEFAULT '1' COMMENT '成功申请次数，默认1，即该记录生成就成功1次',
  `update_time` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `member_id` (`member_id`) USING BTREE,
   KEY `shop_id` (`shop_id`) USING BTREE,
   KEY `idx_dcb_id` (`dcb_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='分销客';

/**
 * 分销客店铺自定义佣金表
 * wx
 * 2020-09-16
 */
CREATE TABLE `xm_shop_distribution_customers_brokerage` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) unsigned NOT NULL COMMENT '绑定店铺id(xm_shop的site_id)',
  `dcb_id` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'xm_distribution_customers_brokerage表id，指向默认的比例和共用的等级',
  `share_proportion` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '分享比例，如字段值是8，比例就是8%',
  `purchase_proportion` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '购买比例，如字段值是8，比例就是8%',
  `created_at` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_dcb_id` (`dcb_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='分销客店铺自定义佣金表';

/**
 * 店铺微信二维码
 * wx
 * 2020-09-16
 */
ALTER TABLE `xm_shop` ADD `wechat_qrcode_img` varchar(255) NOT NULL DEFAULT '' COMMENT '微信二维码';
ALTER TABLE `xm_shop` ADD `wechat_group_qrcode_img` varchar(255) NOT NULL DEFAULT '' COMMENT '微信群二维码';

/**
 * 用户流水号
 * fjq
 * 2020-09-16
 */
ALTER TABLE `xm_member_account`
ADD COLUMN `account_no`  varchar(255) NOT NULL COMMENT '流水号' ;


/**
 * 店主app管理设置
 * xrf
 */
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('0', 'admin', 'SITE_APP_CONFIG', '', '店主APP管理', '1', '**********', '0');

/**
 * 代理积分扣除记录表
 * lyf
 * 2020-09-17
 */
CREATE TABLE `xm_integral_deduction` (
`id` int(11) unsigned NOT NULL AUTO_INCREMENT,
`agent_site_id` int(11) unsigned NOT NULL COMMENT '代理商id',
`shop_site_id` int(11) unsigned NOT NULL COMMENT '店铺id',
`shop_username` varchar(255) NOT NULL DEFAULT '' COMMENT '店铺登陆账号',
`deduction_integral` int(11) unsigned NOT NULL COMMENT '使用积分',
`surplus_integral` int(11) unsigned NOT NULL COMMENT '剩余积分',
`deduction_time` int(11) unsigned NOT NULL COMMENT '扣除时间',
`create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='代理积分扣除记录表';

/**
 * 分销客-规则设置（分享/购买设置）
 * lyf
 */
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('0', 'admin', 'DISTRIBUTION_CUSTOMERS_SHARE_RULE_CONFIG', '', '分销客-规则设置-分享设置', '1', '**********', '0');
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('0', 'admin', 'DISTRIBUTION_CUSTOMERS_PURCHASE_RULE_CONFIG', '', '分销客-规则设置-购买设置', '1', '**********', '0');

/**
 * 小程序消息通知订阅行为
 * wjj
 * 2020-09-17
 */
CREATE TABLE `xm_weapp_notice_book`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `template_key` varchar(255) NOT NULL COMMENT 'applet配置文件对应template_id的key',
  `type` tinyint(2) NOT NULL COMMENT '订阅次数，0：永久订阅,1:一次订阅',
  `status` tinyint(2) NOT NULL COMMENT '发送状态，0：未发送，1：已发送',
  `create_time` int(11) UNSIGNED NULL COMMENT '订阅时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='消息订阅行为';

/**
 * 店铺成本价
 * fjq
 */
ALTER TABLE `xm_shop_order`
ADD COLUMN `prime_cost`  decimal(10,2) UNSIGNED NULL DEFAULT 0.00 COMMENT '采购成本' ;

/**
 * 微信小程序通知模版列表
 * cnn
 * 2020-09-18
 */
CREATE TABLE `xm_weapp_template` (
 `tid` int(11) NOT NULL AUTO_INCREMENT,
 `template_id` varchar(255) NOT NULL DEFAULT '' COMMENT '微信模版id',
 `t_name` varchar(255) NOT NULL DEFAULT '' COMMENT '模版名称',
 `t_value` varchar(2048) NOT NULL DEFAULT '' COMMENT '订阅模版规则(JSON)',
 `content` varchar(2048) NOT NULL DEFAULT '' COMMENT '备注',
 `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态，1：启用，0：禁用',
 `create_time` int(11) unsigned DEFAULT NULL COMMENT '创建时间',
 PRIMARY KEY (`tid`),
 UNIQUE KEY `template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信小程序通知模版列表';


/**
 * 订单商品公司比例
 * fjq
 * 2020-09-18
 */
ALTER TABLE `xm_order_goods`
ADD COLUMN `reward_company_rate`  decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '公司比例' ;



/**
 * 公司账户表
 * xrf
 */
CREATE TABLE `xm_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '公司id',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  PRIMARY KEY (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='公司账户表';

/**
 * 默认生成一个账户代表公司 id为1
 * xrf
 */
INSERT INTO `xm_company` (`company_id`, `balance`) VALUES ('1', '0');


/**
 * 公司账户流水
 * xrf
 */
CREATE TABLE `xm_company_account` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '订单号',
  `account_no` varchar(255) NOT NULL DEFAULT '' COMMENT '流水号',
  `company_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公司id',
  `member_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `account_type` varchar(255) NOT NULL DEFAULT 'point' COMMENT '账户类型',
  `account_data` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户数据',
  `from_type` varchar(255) NOT NULL DEFAULT '' COMMENT '来源类型',
  `type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '来源类型名称',
  `type_tag` varchar(255) NOT NULL DEFAULT '' COMMENT '关联关键字',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `IDX_from_type` (`from_type`) USING BTREE,
  KEY `IDX_create_time` (`create_time`) USING BTREE,
  KEY `IDX_member_id` (`member_id`) USING BTREE,
  KEY `IDX_order_no` (`order_no`) USING BTREE,
  KEY `IDX_account_no` (`account_no`) USING BTREE,
  KEY `IDX_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公司账户流水';

/**
 * 订单商品表添加退运费字段
 * caizc
 * 2020-09-21
 */
ALTER TABLE `xm_order_goods`
ADD COLUMN `refund_delivery_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款申请退运费金额';

/**
 * 订单表修改字段注释
 * caizc
 * 2020-09-21
 */
ALTER TABLE `xm_order`
MODIFY `order_status` int(11) NOT NULL DEFAULT '0' COMMENT '订单状态 0=待支付,1=待发货,3=已发货,4=已收货,10=已完成,-1=已关闭',
MODIFY `pay_status` int(11) NOT NULL DEFAULT '0' COMMENT '支付状态 0=未支付，1=已支付',
MODIFY `delivery_status` int(11) NOT NULL DEFAULT '0' COMMENT '配送状态 0=待发货,1=已发货,2=已收货',
MODIFY `refund_status` int(11) NOT NULL DEFAULT '0' COMMENT '退款状态 1=已申请退款,2=已确认,3=已完成,4=等待买家发货,5=等待卖家收货,6=卖家确认收货,7=确认等待转账中(为处理第三方转账异步回调),-1=卖家拒绝退款',
MODIFY `pay_type` varchar(55) NOT NULL DEFAULT '' COMMENT '支付方式：ONLINE_PAY=在线支付,BALANCE =余额支付,OFFLINE_PAY=线下支付';

/**
 * 订单商品表修改字段注释
 * caizc
 * 2020-09-21
 */
ALTER TABLE `xm_order_goods`
MODIFY `refund_status` int(11) NOT NULL DEFAULT '0' COMMENT '退款状态 1=已申请退款,2=已确认,3=已完成,4=等待买家发货,5=等待卖家收货,6=卖家确认收货,7=确认等待转账中(为处理第三方转账异步回调),-1=卖家拒绝退款',
MODIFY `refund_type` int(11) NOT NULL DEFAULT '0' COMMENT '退款方式 1=仅退款,2=退款退货,3=仅换货';

/**
 * 用户流水
 * fjq
 * 2020-09-21
 */
ALTER TABLE `xm_member_account`
ADD COLUMN `balance`  decimal(10,2) UNSIGNED NULL DEFAULT 0.00 COMMENT '余额' ;


/**
 * 店铺流水
 * fjq
 * 2020-09-21
 */
ALTER TABLE `xm_shop_account`
ADD COLUMN `balance`  decimal(10,2) UNSIGNED NULL DEFAULT 0.00 COMMENT '余额' ;

/**
 * 分销客佣金-初始值
 * lyf
 * 2020-09-22
 */
INSERT INTO `xm_distribution_customers_brokerage`(`id`, `grade`, `full_brokerage`, `share_proportion`, `purchase_proportion`) VALUES (null, '分销客1级', 0.00, 70, 70);


/**
 * 订单时间设置初始化
 * xrf
 */
UPDATE `xm_config`  SET `site_id` = 0 , `app_module` = 'admin' , `config_key` = 'ORDER_EVENT_TIME_CONFIG' , `value` = '{\"auto_close\":\"120\",\"auto_take_delivery\":14,\"auto_complete\":7,\"auto_confirm\":\"14\"}' , `config_desc` = '订单事件时间设置' , `is_use` = 1 , `modify_time` = **********  WHERE  `site_id` = 0  AND `app_module` = 'admin'  AND `config_key` = 'ORDER_EVENT_TIME_CONFIG';

/**
 * 支付表商户信息
 * fjq
 */
ALTER TABLE `xm_pay`
MODIFY COLUMN `mch_info`  text  NULL COMMENT '商户信息';

/**
 * 店铺增加当前可用保证金
 * hxr
 */
ALTER TABLE `xm_shop` 
ADD COLUMN `shop_usable_baozhrmb` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前店铺可用保证金',
ADD COLUMN `shop_using_baozhrmb` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用中的保证金，如生成的优惠券预扣除的(当使用优惠券下单后，要减掉相应的)';

/**
 * 优惠券增加保证金
 * hxr
 */
ALTER TABLE `xm_promotion_coupon_type` 
ADD COLUMN `is_back_baozhrmb` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经返还保证金，0否，1是',
ADD COLUMN `coupon_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券的结束时间，validity_type=1 取 end_use_time与end_time的最大值，<>1 取end_time',
ADD COLUMN `single_baozhrmb` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '一张优惠券的保证金',
ADD INDEX `IDX_ns_promotion_coupon_end_time_is_back_baozhrmb`(`coupon_end_time`, `is_back_baozhrmb`) USING BTREE;


/**
 * 店铺保证金记录
 * hxr
 */
CREATE TABLE `xm_shop_baozhrmb_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `no` varchar(255) NOT NULL DEFAULT '' COMMENT '流水号',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点id',
  `type` varchar(255) NOT NULL DEFAULT 'coupon' COMMENT '类型 coupon=使用优惠券',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '类型id',
  `baozhrmb` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '保证金',
  `from_type` varchar(255) NOT NULL DEFAULT '' COMMENT '来源类型 order=通过订单支付',
  `from_type_id` int(11) NOT NULL DEFAULT '0' COMMENT '来源类型id',
  `type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
  `relate_tag` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `operation_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '操作的数据库字段，1 shop表的shop_using_baozhrmb字段 2 shop表的shop_usable_baozhrmb',
  PRIMARY KEY (`id`),
  KEY `IDX_site_id` (`site_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺保证金记录';

/**
 * 优惠券表 自增ID
 * cnn
 */
ALTER TABLE `xm_promotion_coupon` MODIFY COLUMN `coupon_id`  int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '优惠券id' FIRST ;
ALTER TABLE `xm_promotion_coupon` MODIFY COLUMN `type`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '优惠券类型 reward-满减 discount-折扣 random-随机 deduction- 低扣 fixed-固定' AFTER `coupon_id`;
ALTER TABLE `xm_promotion_coupon_type` MODIFY COLUMN `type`  varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '优惠券类型 reward-满减 discount-折扣 random-随机 deduction-低扣 fixed-固定' AFTER `coupon_type_id`;


/**
 * app版本信息表
 * xrf
 */
CREATE TABLE `xm_app` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增长ID号，主键',
  `platform` varchar(10) NOT NULL COMMENT '平台，如：ios，android',
  `version` varchar(15) NOT NULL COMMENT '软件版本号',
  `build` int(10) unsigned NOT NULL COMMENT '编译版本代码',
  `timlimit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '强制更新时限，单位：天；目前只对Android平台起作用',
  `is_block` char(1) NOT NULL DEFAULT 'N' COMMENT '是否禁用',
  `note` varchar(255) DEFAULT NULL COMMENT '版本说明，用于app版本更新提示',
  `is_must_update` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否强制更新0否1是',
  `check_on_off` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '审核模式开关 1 开启 0关闭',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发布时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_platform_version_build` (`platform`,`version`,`build`),
  KEY `idx_platform` (`platform`) USING BTREE,
  KEY `idx_build` (`build`) USING BTREE,
  KEY `idx_isblock` (`is_block`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='app版本信息表';


/**
 * app平台渠道表
 * xrf
 */
CREATE TABLE `xm_app_platform_channel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '平台类型 1 ios 2 android',
  `code` varchar(20) NOT NULL DEFAULT '' COMMENT '渠道编号',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道名称',
  `is_system` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统默认 1是 0否',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='app平台渠道表';



/**
 * app渠道关联表
 * xrf
 */
CREATE TABLE `xm_app_channel_relation` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `app_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'app版本id',
  `channel_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '渠道id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_appid_channelid` (`app_id`,`channel_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='app渠道关联表';


/**
 * 爆品活动
 * hwg
 * 0926
 */
CREATE TABLE `xm_moldbaby_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '活动状态 1启用 0禁用',
  `user_num` int(11) NOT NULL DEFAULT '0' COMMENT '前X个用户购买',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '店主获取X元奖励，奖励金额',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`) USING BTREE,
  KEY `idx_end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='爆品活动';

CREATE TABLE `xm_moldbaby_activity_goods` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '对应活动Id',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品goods_id',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='爆品活动商品列表';


/**
 *banner管理
 *chj
 *0927
 */
 CREATE TABLE `xm_banner` (
 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
 `banner_name` VARCHAR(16) NOT NULL COMMENT 'banner名称',
 `banner_image` VARCHAR(255) NOT NULL COMMENT 'banner图片',
 `start_time` INT UNSIGNED COMMENT '开始时间',
 `end_time` INT UNSIGNED COMMENT '结束时间',
 `status` TINYINT(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '0.下架 1.上架',
 `banner_url` VARCHAR(255) NOT NULL COMMENT 'banner链接',
 `update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
 `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
 PRIMARY KEY (`id`)
 ) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='banner管理';

 /**
 *爆品订单表
 *fjq
 *0927
 */
CREATE TABLE `xm_moldbaby_activity_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) unsigned DEFAULT '0' COMMENT '订单id',
  `activity_id` int(11) unsigned DEFAULT '0' COMMENT '活动id',
  `shop_id` int(11) unsigned DEFAULT '0' COMMENT '店铺id',
  `status` tinyint(1) unsigned DEFAULT '1' COMMENT '1=未发放佣金,2=已发放佣金',
  `supply_id` int(11) unsigned DEFAULT '0' COMMENT '供应商id',
  `member_id` int(11) unsigned DEFAULT '0' COMMENT '用户id',
  `create_time` int(10) unsigned DEFAULT '0' COMMENT '创建时间',
  `activity_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '店主奖励金额',
  `rank` int(10) unsigned DEFAULT '0' COMMENT '排名',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_shop_id` (`shop_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_supply_id` (`supply_id`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT DEFAULT CHARSET=utf8;






 /**
 * 学习类型表
 * lyf
 * 2020-09-27
 */
 CREATE TABLE `xm_study_type` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '类型名称 ',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0否1是',
	`create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='学习类型表';

/**
 * 学习管理表
 * lyf
 * 2020-09-27
 */
CREATE TABLE `xm_study` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type_id` int(11) unsigned NOT NULL COMMENT '学习类型id(xm_study_type表)',
  `title` varchar(16) NOT NULL DEFAULT '' COMMENT '学习标题',
  `is_share` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否分享，0否1是',
  `icon_url` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图片url',
  `content` text NOT NULL DEFAULT '' COMMENT '学习内容',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否上架，0否1是',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='学习管理表';


/**
 * 店铺爆品商品
 * hxr
 * 2020-09-27
 */
CREATE TABLE `xm_shop_moldbaby_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_id` int(10) unsigned NOT NULL COMMENT 'xm_moldbaby_activity id',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点id',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_site_id_goods_id` (`site_id`, `goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺爆品商品';



/**
 * 店铺定金商品
 * hxr
 * 2020-09-28
 */
CREATE TABLE `xm_shop_deposit_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '站点id',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `deposit` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '用户需缴纳的定金金额',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '当前商品营销开始的时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '当前商品营销结束的时间',
  `valid_pay_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效的支付时间(意为下定金后的有效支付后续金额的时间, 超过此时间还没支付的, 统一做无效处理)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id_goods_id` (`site_id`,`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺定金商品';




CREATE TABLE `xm_member_deposit_goods` (
  `id` int(11) NOT NULL,
  `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺ID',
  `site_name` varchar(255) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `sku_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品规格ID',
  `sku_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `sku_image` varchar(255) NOT NULL DEFAULT '' COMMENT '商品图片',
  `sku_no` varchar(10) NOT NULL DEFAULT '' COMMENT '商品编码',
  `num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '购买数量',
  `price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品卖价',
  `total_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '商品总价',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称(去掉规格名)',
  `member_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `out_trade_no` varchar(255) NOT NULL DEFAULT '' COMMENT '支付流水号',
  `transaction_no` varchar(255) NOT NULL DEFAULT '' COMMENT '交易单号',
  `pay_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `pay_status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '支付状态(0-未支付 1-已支付)',
  `pay_type` varchar(55) NOT NULL DEFAULT '' COMMENT '支付方式 ONLINE_PAY=在线支付,BALANCE =余额支付,OFFLINE_PAY=线下支付',
  `pay_type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '支付类型名称',
  `pay_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '支付时间',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '此订单商品关联的活动开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '此订单商品关联的活动结束时间',
  `valid_pay_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效的支付时间(意为下定金后的有效支付后续金额的时间, 超过此时间还没支付的, 统一做无效处理)',
  `sku_spec_format` varchar(1000) DEFAULT NULL COMMENT 'sku规格格式',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1-正常 0-已失效)',
  `coupon_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联的优惠券'
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '后续付款生成真实订单的ID',
  `order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '后续付款生成真实订单的订单号',
  `activity_id` int(10) NOT NULL DEFAULT '0' COMMENT '活动ID,xm_payment_expansion_activity.id',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`) USING BTREE,
  KEY `idx_site_id` (`site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE,
  KEY `idx_sku_id` (`sku_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员下定金临时订单表';


/**
 * 导师表
 * xrf
 */
CREATE TABLE `xm_mentor` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
  `qr_code` varchar(255) NOT NULL COMMENT '二维码地址',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='导师表';


/**
 * 初始化导师表
 */
INSERT INTO `xm_mentor` (`name`, `qr_code`, `create_time`, `update_time`) VALUES ('导师二维码', '', '1601280931', '');





/**
 * 素材表
 * xrf
 */
CREATE TABLE `xm_material` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `path` text NOT NULL COMMENT '图片地址；最多9张用“ , ”分割',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='素材表';



/**
 * 海报表
 * xrf
 */
CREATE TABLE `xm_poster` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(60) NOT NULL DEFAULT '' COMMENT '海报名称',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '海报地址',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型，0：普通海报，1：VIP海报',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='海报表';

/**
 * 初始化海报表信息
 * xrf
 */
INSERT INTO `xm_poster` (`id`, `name`, `path`, `type`, `create_time`, `update_time`) VALUES ('1', '普通海报', '/public/static/img/shop_poster.png', '0', '1601289649', '0');
INSERT INTO `xm_poster` (`id`, `name`, `path`, `type`, `create_time`, `update_time`) VALUES ('2', 'VIP海报', '/public/static/img/shop_vip_poster.png', '1', '1601289636', '0');



/**
 * 会员提现
 * chj
 */
alter table xm_member_withdraw add column transfer_refuse_reason varchar (20)  COMMENT '转账-驳回-理由' AFTER `refuse_reason`;
alter table xm_member_withdraw modify column `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态0待审核1.待转账2已转账3审核失败4转账失败';


/**
 * 会长表
 * xrf
 */
CREATE TABLE `xm_president` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
  `balance` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '余额',
  `balance_withdraw` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '已提现金额',
  `balance_withdraw_apply` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '申请提现中金额',
  `youli_withdraw` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '可提现柚粒',
  `youli_no_withdraw` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '不可提现的柚粒',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '等级',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会长表';

/**
 * 初始化会长数据
 */
INSERT INTO `xm_president` (`username`, `balance`, `balance_withdraw`, `balance_withdraw_apply`, `youli_withdraw`, `youli_no_withdraw`, `level`, `create_time`, `update_time`) VALUES ('柚品会长', '0.00', '0.00', '0.00', '0', '0', '0', '0', '0');


/**
 * 会员开通店铺表
 * wjj
 * 2020-09-28
 */
CREATE TABLE `xm_member_open_shop_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(10) unsigned NOT NULL COMMENT '用户id',
  `payment_sn` varchar(255) NOT NULL COMMENT '支付单号',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '续费类型（0=新开通付费，1=付费续期，2=非付费续期）',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '开通服务金额',
  `pay_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '支付状态，0未支付，1已支付',
  `from_shop` int(10) unsigned NOT NULL COMMENT '邀请店铺id',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '开店手机号',
  `expire_days` int(10) NOT NULL COMMENT '开通天数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `member_id` (`member_id`) USING BTREE,
  KEY `payment_sn` (`payment_sn`) USING BTREE,
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员开通店铺日志表';

/**
 * 会员开通店铺表
 * wjj
 * 2020-09-28
 */
CREATE TABLE `xm_member_open_shop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) unsigned NOT NULL COMMENT '店铺id',
  `member_id` int(10) unsigned NOT NULL COMMENT '用户id',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '续费类型（0=新开通付费，1=付费续期，2=非付费续期）',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '开通服务金额',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态，0=未生效，1=生效中，2=已过期',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '开店手机号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `from_shop` int(10) unsigned NOT NULL COMMENT '邀请店铺id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE,
  KEY `member_id` (`member_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员开通店铺记录表';

/**
 * 店铺关系表
 * wjj
 * 2020-09-28
 */
CREATE TABLE `xm_shop_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) unsigned NOT NULL COMMENT '店铺id',
  `from_shop` int(10) unsigned NOT NULL COMMENT '邀请店铺id',
  `mentor_id` int(1) unsigned NOT NULL DEFAULT '0' COMMENT '导师id',
  `yunying_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会长id',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺关系表';

/**
 * 定金膨胀活动
 * hwg
 * 0929
 */
CREATE TABLE `xm_payment_expansion_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '活动状态 1启用 0禁用',
  `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `coupon_start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券使用开始时间',
  `coupon_end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券使用截止时间',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`) USING BTREE,
  KEY `idx_end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定金膨胀活动';


CREATE TABLE `xm_payment_expansion_activity_goods` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '对应活动Id',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品goods_id',
  `payment` decimal(10,2) unsigned NOT NULL DEFAULT '0' COMMENT '定金',
  `price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '膨胀金额',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定金膨胀活动商品列表';

/**
 * 店铺表新增字段，添加索引
 * xrf
 */
alter table xm_shop ADD COLUMN `youli_withdraw` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '可提现柚粒';
alter table xm_shop ADD COLUMN `youli_no_withdraw` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '不可提现的柚粒';
alter table xm_shop ADD COLUMN `is_vip` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否vip店铺; 0：0元店铺 1：vip店铺';
alter table xm_shop ADD COLUMN `vip_level` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'vip店铺等级';
alter table xm_shop ADD COLUMN `vip_open_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'vip开通时间';
alter table xm_shop ADD COLUMN `vip_expired_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'vip过期时间';
alter table xm_shop ADD COLUMN `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '邀请店主id、对应xm_shop.site_id';
alter table xm_shop ADD COLUMN `president_id` int(10) unsigned NOT NULL COMMENT '会长id，对应xm_president.id';

ALTER TABLE `xm_shop`
ADD INDEX `IDX_pid` (`pid`) ,
ADD INDEX `IDX_president_id` (`president_id`);


/**
 * 店铺柚粒记录
 * xrf
 */
CREATE TABLE `xm_shop_youli_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_no` varchar(255) NOT NULL DEFAULT '' COMMENT '流水号',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点id',
  `account_type` varchar(255) NOT NULL DEFAULT 'order' COMMENT '类型',
  `account_data` int(10) NOT NULL DEFAULT '0' COMMENT '柚粒数',
  `from_type` varchar(255) NOT NULL DEFAULT '' COMMENT '来源类型',
  `type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
  `relate_tag` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `remark` text COMMENT '说明',
  `youli` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '柚粒余额',
  PRIMARY KEY (`id`),
  KEY `IDX_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺柚粒记录';


/**
 * 店长签到表
 * gwj
 * 2020-09-29
 */
CREATE TABLE `xm_shop_signin` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '签到主键id',
  `site_id` int(11) NOT NULL COMMENT '店铺id',
  `number` int(10) NOT NULL DEFAULT '1' COMMENT '连续签到天数',
  `create_time` int(11) NOT NULL COMMENT '签到时间 格式: ******** ',
  `create_timestamp` int(11) NOT NULL COMMENT '签到时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_create_time` (`site_id`,`create_time`),
  KEY `INDEX_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='店长签到表';


/**
 * 周期购表
 * chj
 * 2020-09-29
 */

 CREATE TABLE `xm_period_buy` (
 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
 `goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
 `buy_price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '周期购总价格',
 `buy_count` INT UNSIGNED NOT NULL COMMENT '周期购数量',
 `period_count` TINYINT UNSIGNED COMMENT '周期数',
 `status` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0.下架 2.上架',
 `update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
 `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
 KEY `idx_goods_id` (`goods_id`),
 PRIMARY KEY (`id`) ) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='周期购表';
 
 
 /**
 * 店铺定金膨胀活动
 * hxr
 * 0929
 */
CREATE TABLE `xm_shop_payment_expansion_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` int(10) unsigned NOT NULL  DEFAULT '0' COMMENT '店铺id',
  `activity_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金膨胀活动 xm_payment_expansion_activity 表ID',
  `coupon_type_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'xm_promotion_coupon_type 表 id',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`) USING BTREE,
  KEY `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺定金膨胀活动';

 /**
 * 柚粒佣金分配订单表
 * fjq
 * 0929
 */
CREATE TABLE `xm_youli_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) unsigned DEFAULT '0' COMMENT '订单id',
  `shop_id` int(11) unsigned DEFAULT '0' COMMENT '店铺id',
  `president_id` int(11) unsigned DEFAULT '0' COMMENT '会长id',
  `agent_id` int(11) unsigned DEFAULT '0' COMMENT '代理id',
  `status` tinyint(1) unsigned DEFAULT '1' COMMENT '1=未发放,2=已发放',
  `supply_id` int(11) unsigned DEFAULT '0' COMMENT '订单所属的供应商id',
  `member_id` int(11) unsigned DEFAULT '0' COMMENT '用户id,购买者',
  `order_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '订单支付金额',
  `shop_rate` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给店主的比例',
  `shop_youli` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给店主的柚粒',
  `president_rate` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给会长的比例',
  `president_youli` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给会长的柚粒',
  `agent_rate` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给代理的比例',
  `agent_youli` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励给店主的柚粒',
  `create_time` int(10) unsigned DEFAULT '0' COMMENT '创建时间',
  `shop_level` int(10) unsigned DEFAULT '0' COMMENT '店铺等级',
  `president_level` int(10) unsigned DEFAULT '0' COMMENT '会长等级',
  `agent_level` int(10) unsigned DEFAULT '0' COMMENT '代理等级',
  `shop_price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '店主佣金',
  `president_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '会长佣金',
  `agent_price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '代理佣金',
  PRIMARY KEY (`id`,`president_price`),
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_shop_id` (`shop_id`) USING BTREE,
  KEY `president_id` (`president_id`) USING BTREE,
  KEY `agent_id` (`agent_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_supply_id` (`supply_id`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



/**
 * 优惠券表添加显示状态字段
 * gwj
 * 2020-09-29
 */
ALTER TABLE `xm_promotion_coupon_type`
ADD COLUMN `display_status`  tinyint(1) NOT NULL DEFAULT 1 COMMENT '显示状态 1展示(默认)';

/**
 * 店铺用户浏览日志增加是否店主分享标识
 * wjj
 * 2020-09-30
 */
ALTER TABLE `xm_shop_member_log`
ADD COLUMN `is_shopkeeper`  tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是店主分享，1是，0否';



/**
 * 店铺助力活动表
 * gwj
 * 2020-09-29
 */
CREATE TABLE `xm_shop_assist_activity` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '店铺助力活动表 主键id',
  `shop_id` int(11) NOT NULL COMMENT '店铺id',
  `activity_id` int(11) NOT NULL COMMENT '活动id xm_assist_star_activity表主键id',
  `number` int(10) NOT NULL DEFAULT '0' COMMENT '活动参与人数',
  `create_time` int(11) NOT NULL COMMENT '添加活动时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_activity_id` (`shop_id`,`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='店铺助力活动表';

/**
 * 店主任务配置
 * hwg
 */
INSERT INTO `xm_config` (`site_id`, `app_module`, `config_key`, `value`, `config_desc`, `is_use`, `create_time`, `modify_time`) VALUES ('0', 'admin', 'SHOP_TASK_CONFIG', '{\"signin\":\"5\",\"share_shop\":\"10\",\"share_shop_user_num\":\"10\",\"daily_self_first_order\":\"5\",\"daily_Sale_first_order\":\"5\"}', '店主任务设置', '1', '1601260330', '1601448219');


/**
 * 店主任务完成记录表
 * hxr
 * 2020-09-30
 */
CREATE TABLE `xm_shop_finish_task_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` int(10) unsigned NOT NULL  DEFAULT '0' COMMENT '店铺id',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '任务类型 1=每日签到 2=分享店铺 3=自购 4=卖货',
  `points` int(11) unsigned NOT NULL COMMENT '获取的积分',
  `finish_day` int(11) unsigned NOT NULL COMMENT '完成时间 格式: ******** ',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id_finish_day` (`shop_id`,`finish_day`) USING BTREE,
  KEY `idx_finish_day` (`finish_day`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店主任务完成记录表';

/**
 * 周期购订单表
 * hwg
 * 2020-10-09
 */

 CREATE TABLE `xm_period_buy_order` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人uid',
  `shop_id` INT UNSIGNED NOT NULL COMMENT '店铺id',
  `goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
  `sku_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '规格id',
  `supply_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '供应商id',
  `reward_shop_rate` decimal(5,2) DEFAULT '0' COMMENT '佣金比例，奖励给店主的佣金比例',
  `reward_company_rate` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '公司比例',
  `out_trade_no` varchar(20) NOT NULL DEFAULT '' COMMENT '支付流水号',
  `buy_price` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '周期购总价格',
  `period_count` TINYINT UNSIGNED COMMENT '周期数',
  `pay_status` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态：0.待支付 1.已支付 2.支付失败',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '购买人姓名',
  `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '购买人手机',
  `telephone` varchar(20) NOT NULL DEFAULT '' COMMENT '购买人固定电话',
  `province_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人省id',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人市id',
  `district_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人区县id',
  `community_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买人社区id',
  `address` varchar(255) NOT NULL DEFAULT '' COMMENT '购买人地址',
  `full_address` varchar(255) NOT NULL DEFAULT '' COMMENT '购买人详细地址',
  `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_out_trade_no` (`out_trade_no`) USING BTREE,
  KEY `idx_sku_id` (`sku_id`) USING BTREE,
  KEY `idx_supply_id` (`supply_id`) USING BTREE
  ) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='周期购订单表';

 /**
 * 周期购订单期数表
 * hwg
 * 2020-10-09
 */

 CREATE TABLE `xm_period_buy_order_count` (
 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
 `pb_order_id` INT UNSIGNED NOT NULL COMMENT '周期购订单id,xm_period_buy_order.id',
 `goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
 `sku_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '规格id',
 `supply_id`  int(11) UNSIGNED NULL DEFAULT 0 COMMENT '供应商id',
 `reward_shop_rate` decimal(5,2) DEFAULT '0' COMMENT '佣金比例，奖励给店主的佣金比例',
 `reward_company_rate` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '公司比例',
 `order_id` INT UNSIGNED NOT NULL COMMENT '订单id,xm_order.order_id',
 `buy_price` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '周期购价格，第一期总价格，其余期数0元',
 `earnings` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '每一期店主收益',
 `period` TINYINT UNSIGNED COMMENT '当前是第几期',
 `refund_time` INT UNSIGNED NOT NULL COMMENT '预计发货时间',
 `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
 `update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_sku_id` (`sku_id`) USING BTREE,
  KEY `idx_supply_id` (`supply_id`) USING BTREE
  ) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='周期购订单期数表';

/*
 * 店铺保证金冻结记录
 * cnn
 * 2020-10-09
 */
CREATE TABLE `xm_shop_baozhrmb_freeze_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺ID (shop表的site_id)',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型 1 保证金减少 2 保证金增加',
  `baozhrmb` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '保证金',
  `shop_usable_baozhrmb` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '店铺当前可用保证金 shop表',
  `shop_using_baozhrmb` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '店铺当前使用中的保证金总额 shop表',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  PRIMARY KEY (`id`),
  KEY `IDX_site_id_type` (`site_id`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺保证金冻结记录';

/**
 * 周期购 字段(弃用)
 * chj
 */
ALTER TABLE `xm_period_buy` CHANGE `buy_count` `buy_count` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '周期购数量(弃用)';


/**
 * 店铺周期购表
 * hxr
 * 2020-09-29
 */

CREATE TABLE `xm_shop_period_buy` (
 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
 `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺ID (shop表的site_id)',
 `goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
 `period_count` TINYINT UNSIGNED COMMENT '周期数',
 `update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
 `create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
 PRIMARY KEY (`id`),
 KEY `idx_site_id` (`site_id`) USING BTREE,
 KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='店铺周期购表';

/**
 * 订单新增订单类型
 * fjq
 * 2020-09-29
 */
ALTER TABLE `xm_order`
ADD COLUMN `order_create_type`  tinyint(2) UNSIGNED NULL DEFAULT 1 COMMENT '1=普通订单,2=周期购订单' ;

 /* 店铺升级记录表
 * hwg
 * 2020-10-10
 */
CREATE TABLE `xm_shop_upgrade_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) unsigned NOT NULL COMMENT '店铺id',
  `old_vip_level` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '旧vip店铺等级',
  `new_vip_level` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新vip店铺等级',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '升级类型（0=邀请店主升级，1=付费升级）',
  `shop_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '获得店铺数量',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_shop_id` (`shop_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='店铺升级记录表';


/**
 * 新增店铺表-剩余店铺数
 * hwg
 * 2020-10-10
 */
alter table xm_shop add column residue_shop_num int(10) unsigned NOT NULL DEFAULT '0' COMMENT '剩余店铺数';

/**
 * 导师表字段新增
 * chj
 * 2020-10-10
 */
ALTER TABLE `xm_mentor` ADD COLUMN `sex` TINYINT(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '性别 1.男 2.女' AFTER `name`,
ADD COLUMN `age` SMALLINT UNSIGNED NOT NULL COMMENT '年龄' AFTER `sex`,
ADD COLUMN `contact` VARCHAR(64) NULL COMMENT '联系方式' AFTER `age`;
ALTER TABLE `xm_mentor` ADD COLUMN `wx_number` VARCHAR(32) NULL COMMENT '导师微信号' AFTER `contact`;

/**
 * 会长表字段新增
 * chj
 * 2020-10-10
 */

 ALTER TABLE `xm_president` ADD COLUMN `sex` TINYINT(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1.男 2.女' AFTER `username`,
 ADD COLUMN `age` SMALLINT UNSIGNED NULL COMMENT '年龄' AFTER `sex`,
 ADD COLUMN `contact` VARCHAR(32) NULL COMMENT '联系方式' AFTER `age`,
 ADD COLUMN `wx_number` VARCHAR(32) DEFAULT '0' NOT NULL COMMENT '会长微信号' AFTER `contact`,
 ADD COLUMN `team_num` INT UNSIGNED DEFAULT 0 NOT NULL COMMENT '团队人数' AFTER `wx_number`,
 ADD COLUMN `member_no` VARCHAR(32) NULL COMMENT '会员群编号' AFTER `team_num`;

/**
 * 店铺表-增加导师等字段
 * lyf
 * 2020-10-12
 */
alter table xm_shop add column `mentor_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '导师id，对应xm_mentor.id';
alter table xm_shop add column `is_president` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否会长，0否1是';

/**
 * 订单表-增加会长id字段
 * gwj
 * 2020-10-12
 */
ALTER TABLE `xm_order`
ADD COLUMN `president_id`  int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会长id,下单时获取shop表的president_id,对应xm_president.id';

/**
 * 邀请店铺申请表
 * cnn
 * 2020-10-12
 */
CREATE TABLE `xm_shop_invite_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '上级店铺site_id',
  `member_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请会员id',
  `state` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '申请状态 1. 待审核 2. 审核通过  3. 审核拒绝',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` INT(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE `IDX_mobile` (`mobile`) USING BTREE,
  UNIQUE `IDX_member_id` (`member_id`) USING BTREE,
  KEY `IDX_pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='邀请店铺申请表';

/**
 * 会长账户记录表
 * lsq
 * 2020-10-12
 */
CREATE TABLE `xm_president_account_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_no` varchar(255) NOT NULL DEFAULT '' COMMENT '流水号',
  `president_id` int(11) NOT NULL DEFAULT '0' COMMENT '会长id',
  `account_type` varchar(255) NOT NULL DEFAULT 'order' COMMENT '类型',
  `account_data` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `from_type` varchar(255) NOT NULL DEFAULT '' COMMENT '来源类型',
  `type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
  `relate_tag` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `remark` text COMMENT '说明',
  `balance` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '余额',
  PRIMARY KEY (`id`),
  KEY `idx_president_id` (`president_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='会长账户记录';

/**
 * 会员开通店铺记录表 xm_member_open_shop 增加来源 来源 0线上付费 1 直邀审核通过
 * cnn
 * 2020-10-12
 */
ALTER TABLE `xm_member_open_shop` ADD `platform` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '来源 0线上付费 1 直邀审核通过' ;

/**
 * 周期购表备注修改
 * chj
 * 2020-10-13
 */
ALTER TABLE `xm_period_buy` CHANGE `status` `status` TINYINT(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '0.下架 1.上架';

/**
 * 备注修改 新增柚粒兑换优惠券类型
 * gwj
 * 2020-10-13
 */
ALTER TABLE `xm_promotion_coupon`
MODIFY COLUMN `get_type`  tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '获取方式1订单2.直接领取3.活动领取 4转赠 5分享获取 6柚粒兑换';

/**
 * 秒杀活动表
 * czk
 * 2020-10-14
 */
ALTER TABLE `xm_promotion_seckill_goods` modify column `sku_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id(对应商品表id)';
ALTER TABLE `xm_promotion_seckill_goods` modify column `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点ID(已弃用)';
ALTER TABLE `xm_promotion_seckill_goods` ADD `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0.下架 1.上架';
ALTER TABLE `xm_promotion_seckill_goods` ADD `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存';
ALTER TABLE `xm_promotion_seckill_goods` ADD `remain_stock` int(11) NOT NULL DEFAULT '0' COMMENT '剩余库存';
ALTER TABLE `xm_promotion_seckill_goods` ADD `sale_num` int(11) NOT NULL DEFAULT '0' COMMENT '销量';

/**
 * 秒杀参与商品
 * czk
 * 2020-10-14
 */
ALTER TABLE `xm_promotion_seckill` ADD `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0.下架 1.上架';

/**
 * 店铺秒杀活动商品表
 * hxr
 * 2020-10-14
 */
CREATE TABLE `xm_shop_promotion_seckill_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `seckill_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主键',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '站点ID',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='店铺秒杀活动商品表';

/**
 * 会长管理状态
 * chj
 * 2020-10-14
 */
 ALTER TABLE `xm_president` ADD COLUMN `status` TINYINT(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1.启用 0.禁用' AFTER `member_no`;

/**
 * 分销助力
 * czk
 * 2020-10-15
 */
CREATE TABLE `xm_distribution_assist` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '分销助力活动',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '活动标题',
  `reward` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
  `aim_num` int(11) NOT NULL DEFAULT '0' COMMENT '目标数量',
  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动截止时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销助力表';

CREATE TABLE `xm_distribution_assist_item` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '分销助力选项id',
  `assist_id` int(11) NOT NULL DEFAULT '0' COMMENT '分销助力活动id',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '选项名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `assist_id_name` (`assist_id`,`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销助力选项表';


/**
 * sku site_id备注修改
 * gwj
 * 2020-10-15
 */
ALTER TABLE `xm_goods_sku`
MODIFY COLUMN `site_id`  int(11) NOT NULL DEFAULT 0 COMMENT '所属店铺id(改为关联供应商：supplier_id)';


/**
 * 分销助力投票记录
 * cnn
 * 2020-10-15
 */
CREATE TABLE `xm_distribution_assist_vote_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `assist_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '投票活动ID',
  `item_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '选项id',
  `openid` varchar(255) NOT NULL DEFAULT '' COMMENT '微信小程序openid',
  `member_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级店铺ID site_id',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE `as_id_site_id_openid` (`assist_id`,`item_id`, `site_id`, `openid`) USING BTREE,
  KEY `openid` (`openid`),
  KEY `member_id` (`member_id`),
  KEY `site_id` (`site_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='分销助力活动投票记录';

/**
 * 分销助力投票统计表
 * cnn
 * 2020-10-15
 */
CREATE TABLE `xm_distribution_assist_vote_count` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺ID site_id',
  `assist_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '投票活动ID',
  `item_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '选项id',
  `times` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '投票数量',
  `updtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE `site_id_assist_id_item_id` (`site_id`,`assist_id`,`item_id`) USING BTREE,
  KEY `assist_id` (`assist_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='分销助力投票统计表';

/**
  * xm_shop_account xm_member 添加索引
  * cnn
  * 2020-10-16
  */
ALTER TABLE `xm_shop_account` ADD INDEX `idx_site_id` (`site_id`);
ALTER TABLE `xm_shop_account` ADD INDEX `idx_from_type` (`from_type`);
ALTER TABLE `xm_shop_account` ADD INDEX `idx_relate_tag` (`relate_tag`);
ALTER TABLE `xm_member` ADD INDEX `IDX_sys_user_weapp_openid` (`weapp_openid`);


/**
 * 分享赚活动表
 * chj
 * 2020-10-19
 */
CREATE TABLE `xm_share_buy_activity` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`name` VARCHAR(128) NOT NULL COMMENT '活动名称',
`goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
`price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '活动金额',
`original_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '原价购买订单数量',
`start_time` INT UNSIGNED NOT NULL COMMENT '开始时间',
`end_time` INT UNSIGNED NOT NULL COMMENT '结束时间',
`num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动数量',
`status` TINYINT NOT NULL DEFAULT 0 COMMENT '1.启用 0.禁用',
`shop_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '推广店主奖励',
`president_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '会长奖励',
`update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
`create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`) ) ENGINE=INNODB CHARSET=utf8 COMMENT='分享赚活动表';

ALTER TABLE `xm_share_buy_activity` ADD INDEX `idx_goods_id` (`goods_id`);


/**
 * 分享赚活动会员开团表
 * hxr
 * 2020-10-19
 */
CREATE TABLE `xm_member_share_buy_activity` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`activity_id` INT UNSIGNED NOT NULL COMMENT 'xm_share_buy_activity id',
`site_id` INT UNSIGNED NOT NULL COMMENT '店铺id',
`member_id` INT UNSIGNED NOT NULL COMMENT '会员id',
`name` VARCHAR(128) NOT NULL COMMENT '活动名称',
`goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
`sku_id` INT UNSIGNED NOT NULL COMMENT '商品sku_id',
`num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买数量',
`price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '活动金额',
`original_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '需要原价购买订单数量',
`has_original_num` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已经完成的原价购买订单数量',
`start_time` INT UNSIGNED NOT NULL COMMENT '开始时间',
`end_time` INT UNSIGNED NOT NULL COMMENT '结束时间',
`shop_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '推广店主奖励',
`president_money` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '会长奖励',
`status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态 -1=失败,0=待成团 1=已成团 2=已完成',
`head_coupon_type_id` INT UNSIGNED NOT NULL COMMENT '活动金额 购买的coupin_type id',
`member_coupon_type_id` INT UNSIGNED NOT NULL COMMENT '原价购买的coupin_type id',
`head_order_id` INT UNSIGNED NOT NULL COMMENT 'xm_order id',
`update_time` INT UNSIGNED NOT NULL COMMENT '修改时间',
`create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
KEY `idx_activity_id` (`activity_id`) USING BTREE,
KEY `idx_site_id` (`site_id`) USING BTREE,
KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE=INNODB CHARSET=utf8 COMMENT='分享赚活动会员开团表';

/**
 * 分享赚会员参与活动表
 * hxr
 * 2020-10-19
 */
CREATE TABLE `xm_member_join_share_buy_activity` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`member_activity_id` INT UNSIGNED NOT NULL COMMENT 'xm_member_share_buy_activity id',
`site_id` INT UNSIGNED NOT NULL COMMENT '店铺id',
`member_id` INT UNSIGNED NOT NULL COMMENT '会员id',
`goods_id` INT UNSIGNED NOT NULL COMMENT '商品id',
`sku_id` INT UNSIGNED NOT NULL COMMENT '商品sku_id',
`order_id` INT UNSIGNED NOT NULL COMMENT 'xm_order id',
`price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '购买价格',
`create_time` INT UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
KEY `idx_member_activity_id` (`member_activity_id`) USING BTREE,
KEY `idx_site_id` (`site_id`) USING BTREE,
KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE=INNODB CHARSET=utf8 COMMENT='分享赚会员参与活动表';

/**
 * 分销助力投票排名奖励
 * czk
 * 2020-10-20
 */
CREATE TABLE `xm_distribution_assist_reward` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '投票排名奖励id',
  `assist_id` int(11) NOT NULL DEFAULT '0' COMMENT '投票活动id',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
  `is_reward` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已发放奖励',
  `ranking` tinyint(3) NOT NULL DEFAULT '1' COMMENT '排序排名',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '发放时间',
  PRIMARY KEY (`id`),
  KEY `assist_id` (`assist_id`),
  KEY `site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销助力投票排名奖励';

/**
 * 同步先迈供应商数据
 * czk
 */
ALTER TABLE `xm_supplier` ADD `xm_id` int(11) NOT NULL DEFAULT '0' COMMENT '先迈供应商id';

/**
 * 先迈店主ID
 * cnn
 */
ALTER TABLE `xm_shop` ADD `xm_shop_id` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '先迈店主ID';


/**
 * 店铺商品每日浏览
 * hxr
 * 2020-10-21
 */
CREATE TABLE `xm_shop_goods_day_browse` (
`id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
`site_id` int(11) UNSIGNED NOT NULL COMMENT '店铺id',
`goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
`num` int(11) UNSIGNED NOT NULL COMMENT '当天浏览量',
`create_day` int(11) NOT NULL COMMENT '格式: ******** ',
`create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
KEY `idx_create_day_site_id_goods_id` (`create_day`,`site_id`,`goods_id`) USING BTREE,
KEY `idx_site_id` (`site_id`) USING BTREE,
KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=INNODB CHARSET=utf8 COMMENT='店铺商品每日浏览';

/**
 * 店铺商品每日销售量
 * hxr
 * 2020-10-21
 */
CREATE TABLE `xm_shop_goods_day_sale` (
`id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
`site_id` int(11) UNSIGNED NOT NULL COMMENT '店铺id',
`goods_id` int(11) UNSIGNED NOT NULL COMMENT '商品id',
`num` int(11) UNSIGNED NOT NULL COMMENT '当天销售量',
`create_day` int(11) NOT NULL COMMENT '格式: ******** ',
`create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
KEY `idx_create_day_site_id_goods_id` (`create_day`,`site_id`,`goods_id`) USING BTREE,
KEY `idx_site_id` (`site_id`) USING BTREE,
KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=INNODB CHARSET=utf8 COMMENT='店铺商品每日销售量';

CREATE TABLE `xm_large_turntable_activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '活动状态：0下架1上架',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '店主奖励金额',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `target_people_num` int(11) NOT NULL DEFAULT '0' COMMENT '目标人数',
  `awards_people_num` int(11) NOT NULL DEFAULT '0' COMMENT '获奖人数',
  `everyday_luck_draw_num` int(11) NOT NULL DEFAULT '0' COMMENT '每人每天最多抽奖次数',
  `crowd` varchar(255) NOT NULL DEFAULT '' COMMENT '活动对象',
  `content` varchar(255) NOT NULL DEFAULT '' COMMENT '活动内容',
  `tips` varchar(255) NOT NULL DEFAULT '' COMMENT '温馨提示',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT '大转盘图片（已弃用）',
  `banner` varchar(255) NOT NULL DEFAULT '' COMMENT '广告图片',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
  `everyday_luck_max_num` int(11) NOT NULL DEFAULT '1' COMMENT '每人每天最多中奖次数',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`) USING BTREE,
  KEY `idx_end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='大转盘活动';

/**
 * 大转盘奖品表
 * czk
 * 2020-10-21
 */
CREATE TABLE `xm_large_turntable_activity_prize` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '大转盘奖品id',
  `activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '大转盘活动id',
  `type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '奖品类型：1-谢谢参与，2-优惠券，3-现金红包，4-商品',
  `prize_name` varchar(50) NOT NULL DEFAULT '' COMMENT '奖品名称',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '优惠券金额或红包金额',
  `num` int(11) NOT NULL DEFAULT '0' COMMENT '优惠卷数量或红包数量或商品数量',
  `remain_num` int(11) NOT NULL DEFAULT '0' COMMENT '剩余奖品数量',
  `win_rate` int(3) NOT NULL DEFAULT '0' COMMENT '中奖概率',
  `used_permit` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券满多少元可以使用',
  `everyday_max_draw_num` int(11) unsigned zerofill NOT NULL DEFAULT '00000000000' COMMENT '每天最多抽中次数',
  `expired_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '有效期类型：1-固定时间，2-领取之日起',
  `expire_days` int(11) NOT NULL DEFAULT '0' COMMENT '领取后有效天数',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `img_url` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
  `goods_name` varchar(50) NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_deduction_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '抽中商品抵扣卷金额',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  KEY `activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='大转盘奖品';


/**
 * 先迈商品同步新增字段
 * xrf
 */
ALTER TABLE `xm_goods_attribute`
ADD COLUMN `xm_spec_id`  int(10) UNSIGNED NOT NULL COMMENT '先迈规格id,xm_xm_spec';

ALTER TABLE `xm_goods_attribute_value`
ADD COLUMN `xm_spec_item_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '先迈属性id,xm_xm_spec_item.id';

ALTER TABLE `xm_goods_sku`
ADD COLUMN `xm_spec_goods_id`  int UNSIGNED NOT NULL DEFAULT 0 COMMENT '先迈规格商品表id,xm_xm_spec_goods.id';


/**
 * 同步先迈供应商数据 xm_id
 * chj
 */
ALTER TABLE `xm_goods_category` ADD COLUMN `xm_id` INT UNSIGNED DEFAULT 0 NOT NULL COMMENT '先迈分类id' AFTER `reward_company_rate`;

/**
 * 先迈商品同步新增字段
 * lyf
 */
ALTER TABLE `xm_goods` ADD COLUMN `xm_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '先迈商品表（xm_goods）id';

/**
 * 大转盘抽奖与分享记录表
 * cnn
 */
CREATE TABLE `xm_large_turntable_draw_log` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
 `member_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
 `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店长ID',
 `activity_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '大转盘活动id',
 `times` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '抽奖次数',
 `share_times` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分享次数',
 `members` VARCHAR (1024) NOT NULL DEFAULT '' COMMENT '分享时下级会员ID',
 `ymd` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '日期20201022',
 PRIMARY KEY (`id`),
 UNIQUE KEY `m_id_a_id_ymd` (`member_id`,`activity_id`,`ymd`) USING BTREE,
 KEY `activity_id` (`activity_id`) USING BTREE,
 KEY `site_id` (`site_id`) USING BTREE,
 KEY `ymd` (`ymd`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='大转盘抽奖与分享记录表';

/**
 * 大转盘中奖记录表
 * cnn
 */
CREATE TABLE `xm_large_turntable_award_log` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
 `member_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
 `site_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店长ID',
 `activity_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '大转盘活动id',
 `prize_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '大转盘奖品id',
 `related_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '奖品关联ID',
 `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '奖品类型：1-谢谢参与，2-优惠券，3-现金红包，4-商品',
 `instime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
 `snapshot` varchar(4096) NOT NULL DEFAULT '' COMMENT '奖品快照',
 PRIMARY KEY (`id`),
 KEY `member_id` (`member_id`) USING BTREE,
 KEY `site_id` (`site_id`) USING BTREE,
 KEY `activity_id_prize_id` (`activity_id`,`prize_id`) USING BTREE,
 KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='大转盘中奖记录';


/**
 * 砍价活动
 * chj
 */
ALTER TABLE `xm_promotion_bargain`
CHANGE `buy_type` `buy_type` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '购买方式（0任意金额可购买 1砍到指定价格）(是否支持中途下单 0.任意金额 1.指定底价)',
CHANGE `bargain_num` `bargain_num` INT(11) DEFAULT 0 NOT NULL COMMENT '帮砍价人数（刀数）',
CHANGE `site_id` `site_id` INT(11) DEFAULT 0 NOT NULL COMMENT '站点id（弃用）',
CHANGE `site_name` `site_name` VARCHAR(255) CHARSET utf8 COLLATE utf8_general_ci DEFAULT '' NOT NULL COMMENT '站点名称（弃用）';

ALTER TABLE `xm_promotion_bargain_goods` CHANGE `bargain_stock` `bargain_stock` INT(11) DEFAULT 0 NOT NULL COMMENT '砍价库存（活动数量）',
ADD COLUMN `shop_reward` DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '砍价成功店主奖励' AFTER `is_own`,
ADD COLUMN `president_reward` DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '砍价成功会长奖励' AFTER `shop_reward`;

ALTER TABLE `xm_promotion_bargain_goods` ADD COLUMN `bargain_price` DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '商品售价' ;

/**
 * 大转盘活动店主奖励表
 * czk
 * 2020-10-23
 */
CREATE TABLE `xm_large_turntable_shop_award` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '奖励id',
  `activity_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动id',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '店主id',
  `is_reward` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已发放奖励',
  `ranking` tinyint(3) NOT NULL DEFAULT '0' COMMENT '排序排名',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '发放时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `activity_id_site_id` (`activity_id`,`site_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='大转盘活动奖励店主表';


/**
 * 修改商品属性表
 * 修改字段数据类型
 * xrf
 */
ALTER TABLE `xm_goods_attribute`
MODIFY COLUMN `attr_value_list`  text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性值列表（\',\'隔开注意键值对）',
MODIFY COLUMN `attr_value_format`  text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性值格式json';

/**
 * 先迈小程序配置
 * 2020-10-23
 * hwg
 */
INSERT INTO `xm_config` (
  `site_id`,
  `app_module`,
  `config_key`,
  `value`,
  `config_desc`,
  `is_use`,
  `create_time`,
  `modify_time`
)
VALUES
  (
    '0',
    'admin',
    'ADAPAY_PAY_CONFIG_XM',
    '{"api_key_live":"api_live_9a8c23f1-368b-4362-a32c-aa44ff764829","api_key_test":"api_test_345fee2d-e6ba-4e13-8909-ab6d614f8211","rsa_public_key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB","rsa_private_key":"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAK4t/7tIl0+djisJ7ALKEQo17QT8F4e+5XGMayDxSklHmdY9H/SuX48Lq/CWPkgYk51vgRCC5YQJOxlK3IHdSPKcKf8cO3F+V7onq40zSEbD+WMT8W/DOol211jGWC0zO3suGiGw+0IGRylS99+3vu0bCN9xFEBSikkLBXsBi8PPAgMBAAECgYA38fpj80r2fYq+dC9kLfcPKob4xOfh6ATWVd3K9PWBWAgncfajpqZ20a7mVbuneI1hUgFIhmKl6DkdRJhS9fqJusoSdTE676mmq2py87u0wUPbYZO09w8oaql8GlbF2PhMfwLhwLLue82oHVg3yAgDMLtmFcc4Uz2C4KcYeig8oQJBANW8EZxX313gOHFyaBg1LeRw2KYBCekinmBXK7ghBoDsh6TwWL0h+v8eQMtgA8uMFOWc9h3Wn2ywMWMnel4X5pkCQQDQn4gU2NfAuWTNlYT17OE10vYdS4FgVK8DHc3n65n9qM6fLuIuPXMd4DUvB2pIScQuh+vwD1DxQuxb1W1SqsanAkEAtmnNae7BL0CXS5OF7estIMOC06MdT0EBhF3BbWOaRlwYpJeQVNL0gdnTGP/4HTeP+ivNDchHxh5V+DcQQ9AIOQJBAMlf9F8LINdrHR5EoV8xFAJ8bAzDAVMW6wg7ELI4/R+Yfjmxa6nurtu7vBp1MeYtLi0sDlZesmbvjm7miOwu5CkCQQCxpy2QU6SI+ylEoMYjGB/yVbheqyXYN85lxIg5JqIW5Nj26FjvVM0Ahi/miYiKkWBo1XW+AStDQ2GzAiXQysPd","app_id":"app_bca99442-68ce-4cfa-9e20-00fb2c21f85b","notify_url":"888","pay_status":"1"}',
'微信支付配置',
    '1',
    '1598843251',
    '1599554008'
  );

/**
 * 用户行为记录表
 * 2020-10-26
 * hwg
 */
CREATE TABLE `xm_user_behavior_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `shop_id` int(11) NOT NULL DEFAULT '0' COMMENT '店主id',
  `type_id` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型id：1=访问，2=分享，3=登录',
  `ip` char(15) NOT NULL DEFAULT '' COMMENT '客户端IP信息',
  `platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '客户端：1=平台小程序，2=店主app，3=h5',
  `dev_mod` varchar(50) NOT NULL DEFAULT '' COMMENT '设备型号',
  `dev_sys_ver` varchar(20) NOT NULL DEFAULT '' COMMENT '设备系统版本号',
  `app_ver` varchar(10) NOT NULL DEFAULT '' COMMENT 'app的版本号',
  `page_id` int(11) NOT NULL DEFAULT '0' COMMENT '页面id',
  `page_url` varchar(50) NOT NULL DEFAULT '' COMMENT '页面url',
  `created_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE, 
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_shop_id` (`shop_id`) USING BTREE,
  KEY `idx_type_id` (`type_id`) USING BTREE,
  KEY `idx_platform` (`platform`) USING BTREE,
  KEY `idx_page_id` (`page_id`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='用户行为记录表';

/**
 * 订单商品表
 * czk
 * 2020-10-14
 */
ALTER TABLE `xm_order_goods` modify column `refund_type` int(11) NOT NULL DEFAULT '0' COMMENT '退款方式 0=已关闭 1=仅退款,2=退款退货,3=仅换货';

/**
 * 先迈用户同步
 * czk
 * 2020-11-09
 */
CREATE TABLE `xm_yp_sync_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `yp_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '柚品xm_user表uid',
  `yp_mid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '柚品xm_member表member_id',
  `xm_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '先迈xm_users表id',
  `xm_pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '先迈xm_users表pid',
  `uni_mobile` varchar(20) NOT NULL DEFAULT '0' COMMENT '手机号',
  `xm_trustee_uid` int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的董事用户id',
  `xm_manage_uid` int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的经理用户id',
  `xm_director_uid` int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的主管用户id',
  `xm_agent_uid` int(11) NOT NULL DEFAULT '0' COMMENT '当前用户的代理用户id',
  `is_trustee` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否董事 0否 1 是',
  `is_group_manager` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否经理 0否 1 是',
  `is_director` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主管 0否 1 是',
  `is_agent` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否代理,1=是，0=否',
  `add_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '同步时间',
  `edit_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_mobile` (`uni_mobile`),
  UNIQUE KEY `yp_uid_mid` (`yp_uid`,`yp_mid`),
  KEY `xm_manage_uid` (`xm_manage_uid`) USING BTREE,
  KEY `xm_trustee_uid` (`xm_trustee_uid`) USING BTREE,
  KEY `xm_director_uid` (`xm_director_uid`),
  KEY `xm_agent_uid` (`xm_agent_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='先迈柚品用户同步表';

<<<<<<< HEAD
/**
 * 用于加盟任务浏览量统计
 * czk
 * 2020-11-12
 */
CREATE TABLE `xm_league_task_goods_day_browse` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `share_site_id` int(11) unsigned NOT NULL COMMENT '店铺id',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品id',
  `num` int(11) unsigned NOT NULL COMMENT '当天浏览量',
  `create_day` int(11) NOT NULL COMMENT '格式: ******** ',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_day_site_id_goods_id` (`create_day`,`share_site_id`,`goods_id`) USING BTREE,
  KEY `idx_site_id` (`share_site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='加盟任务商品浏览量';

/**
 * 用于商品浏览量统计
 * czk
 * 2020-11-12
 */
CREATE TABLE `xm_goods_share` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `share_site_id` int(11) unsigned NOT NULL COMMENT '店铺id',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品id',
  `num` int(11) unsigned NOT NULL COMMENT '当天分享量',
  `create_day` int(11) NOT NULL COMMENT '格式: ******** ',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_day_site_id_goods_id` (`create_day`,`share_site_id`,`goods_id`) USING BTREE,
  KEY `idx_site_id` (`share_site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品分享表';


/**
 * 调整后台栏目
 * hxr
 * 2020-11-12
 */
update xm_menu set title = '店铺提现设置' where name = 'SHOP_ACCOUNT_WITHDRAW_CONFIG';

update xm_menu set is_show = 0 where name = 'SHOP_WITHDRAW_CONFIG';

/**
 * 关闭后台总售后保障菜单
 * wjj
 * 2020-11-12
 */
 update `xm_menu` set is_show=0 WHERE `name` = 'CONFIG_AFTERSALE' and app_module='admin';

/**
 * 用于加盟任务浏览量统计
 * czk
 * 2020-11-12
 */
CREATE TABLE `xm_league_task_goods_day_browse` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `share_site_id` int(11) unsigned NOT NULL COMMENT '店铺id',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品id',
  `num` int(11) unsigned NOT NULL COMMENT '当天浏览量',
  `create_day` int(11) NOT NULL COMMENT '格式: ******** (已弃用)',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `league_id` int(11) NOT NULL DEFAULT '0' COMMENT '加盟id',
  PRIMARY KEY (`id`),
  KEY `idx_create_day_site_id_goods_id` (`create_day`,`share_site_id`,`goods_id`) USING BTREE,
  KEY `idx_site_id` (`share_site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='加盟任务商品浏览量';

/**
 * 用于加盟任务商品浏览量统计
 * czk
 * 2020-11-12
 */
CREATE TABLE `xm_league_goods_share` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `share_site_id` int(11) unsigned NOT NULL COMMENT '店铺id',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品id',
  `num` int(11) unsigned NOT NULL COMMENT '当天分享量',
  `create_day` int(11) NOT NULL COMMENT '格式: ********（已弃用）',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `league_id` int(11) NOT NULL DEFAULT '0' COMMENT '加盟id',
  PRIMARY KEY (`id`),
  KEY `idx_create_day_site_id_goods_id` (`create_day`,`share_site_id`,`goods_id`) USING BTREE,
  KEY `idx_site_id` (`share_site_id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='加盟任务商品分享表';

CREATE TABLE `xm_league_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tid` int(11) NOT NULL DEFAULT '0' COMMENT '柚品分类id或商品id',
  `type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '任务类型：1-类目，2-单品',
  `league_id` int(11) NOT NULL DEFAULT '0' COMMENT '加盟id',
  `xm_uid` int(11) NOT NULL DEFAULT '0' COMMENT '先迈uid',
  `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '店主id',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '任务开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '任务结束时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `xm_tid` int(11) NOT NULL COMMENT '先迈分类id或商品id',
  PRIMARY KEY (`id`),
  KEY `tid` (`tid`),
  KEY `type` (`type`),
  KEY `league_id` (`league_id`),
  KEY `xm_uid` (`xm_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='加盟任务';

/**
 * 秒杀支付订单备注
 * czk
 * 2020/12/15
 */
alter table xm_pay add column `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注(自定义参数)';

/**
 * 后台审核备注
 */
alter table xm_order_goods add column `admin_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '后台审核备注';