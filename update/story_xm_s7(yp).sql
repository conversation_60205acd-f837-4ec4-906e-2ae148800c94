CREATE TABLE IF NOT EXISTS `xm_not_send_pintuan_reward`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `group_id` int NOT NULL DEFAULT 0 COMMENT '拼团分组id',
    `pintuan_order_id` int NOT NULL DEFAULT 0 COMMENT '拼团订单id',
    `site_id` int NOT NULL DEFAULT 0 COMMENT '店铺id',
    `data` text NULL COMMENT '数据',
    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_pintuan_order_id` (`pintuan_order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团奖励未发表';