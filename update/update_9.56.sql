INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control`)
 VALUES ('admin', 'leaguePoints', '销售任务管理', 'SALE_TASK_MANAGEMENT', 'PROMOTION_LEAGUEPOINTS', 4, 'leaguePoints://admin/SaleTaskManagement/lists', 1, 3, '', 0, '', '', 1);

ALTER TABLE `xm_league_task_point_config`
    ADD COLUMN `sale_task_enable` tinyint NOT NULL DEFAULT 0 COMMENT '销售任务奖励获贡献值，0:关闭，1：开启' AFTER `goods_reward_points`;
ALTER TABLE `xm_league_task_point_config`
    ADD COLUMN `sale_task_max` int NOT NULL DEFAULT 0 COMMENT '销售任务奖励每月限额' AFTER `goods_reward_points`;

CREATE TABLE `xm_league_task_sale_task_goods` (
        `sale_task_goods_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
        `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
        `month_sales` int(11) NOT NULL DEFAULT '0' COMMENT '月度销售指标',
        `reward_points` int(11) NOT NULL DEFAULT '0' COMMENT '奖励贡献值',
        `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0停用1启用',
        `disable_start_time` int(11) NOT NULL DEFAULT '0' COMMENT '停用开始时间',
        `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
        `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`sale_task_goods_id`),
        KEY `idx_league_task_key` (`league_task_key`),
        KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COMMENT='加盟任务-商品月度销售配置表';

CREATE TABLE `xm_league_task_member_sale_task` (
        `member_sale_task_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `league_task_key` varchar(50) NOT NULL DEFAULT '' COMMENT '加盟任务唯一标识,league_1任务1，league_2任务2',
        `site_id` int(11) NOT NULL DEFAULT '0' COMMENT '店铺Id',
        `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户Id',
        `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
        `month` varchar(20) NOT NULL DEFAULT '' COMMENT '月',
        `month_sales` int(11) NOT NULL DEFAULT '0' COMMENT '月度销售指标',
        `reward_points` int(11) NOT NULL DEFAULT '0' COMMENT '奖励贡献值',
        `complete_order_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '完成的订单id,逗号分隔',
        `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0待完成，100已完成，-1已失败',
        `reward_status` tinyint NOT NULL DEFAULT '0' COMMENT '发放奖励状态：0待发放，1发放成功，-1发放失败',
        `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
        `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`member_sale_task_id`),
        KEY `idx_league_task_key` (`league_task_key`),
        KEY `idx_site_id` (`site_id`),
        KEY `idx_goods_id` (`goods_id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_month` (`month`),
        KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='加盟任务-用户月度销售指标任务';

ALTER TABLE `xm_league_task_member_sale_task`
    ADD COLUMN `fail_reason` varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因' AFTER `reward_status`;