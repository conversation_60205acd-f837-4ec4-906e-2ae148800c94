CREATE TABLE `xm_promotion_goods_coupon_rules` (
   `rule_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
   `rule_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '规则名称',
   `send_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已派发数量',
   `type` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '执行规则类型(参考: GoodsCouponRuleType常量类)',
   `money` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '首单规则时需要支付的金额',
   `relation_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联优惠券活动ID',
   `relation_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联配置',
   `start_time` timestamp NULL DEFAULT NULL COMMENT '开始执行时间',
   `stop_time` timestamp NULL DEFAULT NULL COMMENT '停止执行之间',
   `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '状态(参考: GoodsCouponRuleStatus常量类)',
   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `xm_promotion_goodscoupon`
    ADD COLUMN `get_type_relation_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '当领取方式是出发规则派券时, 此字段绑定的是规则id';

# menu
INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` )
VALUES
( 'admin', 'goodscoupon', '自动派券规则列表', 'PROMOTION_GOODS_COUPON_RULE', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodsCouponRule/list', 0, 1, '', 0, '', '', 1 );

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` )
VALUES
( 'admin', 'goodscoupon', '添加自动派券规则', 'PROMOTION_GOODS_COUPON_RULE_ADD', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodsCouponRule/add', 0, 1, '', 0, '', '', 1 );

INSERT INTO `xm_menu` (`app_module`, `addon`, `title`, `name`, `parent`, `level`, `url`, `is_show`, `sort`, `desc`, `is_icon`, `picture`, `picture_select`, `is_control` )
VALUES
( 'admin', 'goodscoupon', '查看自动派券规则', 'PROMOTION_GOODS_COUPON_RULE_DETAIL', 'PROMOTION_GOODSCOUPON', 4, 'goodscoupon://admin/goodsCouponRule/detail', 0, 1, '', 0, '', '', 1 );


ALTER TABLE `xm_promotion_goodscoupon`
    ADD COLUMN `start_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动开始时间';

ALTER TABLE `xm_promotion_goodscoupon_type`
    ADD COLUMN `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间';