INSERT INTO `xm_menu`(`title`, `name`, `parent`, `level`, `is_show`, `url`, `sort`) VALUES ('在线记录', 'SERVICE_ONLINE_LIST', 'SERVICE_MANAGE', 3, 1, 'admin/serviceManage/serviceOnlineList',260);
UPDATE `xm_menu` SET `sort` = 261 WHERE `name` = 'TYPE_MANAGE';
UPDATE `xm_menu` SET `sort` = 262 WHERE `name` = 'DISCOURSE_MANAGE';
UPDATE `xm_menu` SET `sort` = 263 WHERE `name` = 'CUSTOMER_SERVICE_CONFIGURATION';


CREATE TABLE IF NOT EXISTS `xm_customer_service_online`
(
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `reception_user_id` varchar(100) NOT NULL default '' COMMENT '接待客服id',
    `nickname` varchar(200) NOT NULL default '' COMMENT '客服昵称',
    `date` varchar(20) NOT NULL default '' COMMENT '日期',
    `online_time`int(11) NOT NULL default 0 COMMENT '在线时长(分钟)',
    `last_status` int(11) NOT NULL default 0 COMMENT '最后在线状态()',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`log_id`),
    KEY `idx_reception_user_id` (`reception_user_id`),
    KEY `idx_date` (`date`),
    KEY `idx_last_status` (`last_status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服在线表';

CREATE TABLE IF NOT EXISTS `xm_customer_service_online_change_log`
(
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `reception_user_id` varchar(100) NOT NULL default '' COMMENT '接待客服id',
    `nickname` varchar(200) NOT NULL default '' COMMENT '客服昵称',
    `date` varchar(20) NOT NULL default '' COMMENT '日期',
    `status`int(11) NOT NULL default 0 COMMENT '0:在线,1:下线。',
    `stop_type`int(11) default null COMMENT '0:停止接待,1:暂时挂起。',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`log_id`),
    KEY `idx_reception_user_id` (`reception_user_id`),
    KEY `idx_date` (`date`),
    KEY `idx_status` (`status`),
    KEY `idx_stop_type` (`stop_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='客服在线变更记录表';

ALTER TABLE xm_customer_service_online
    MODIFY COLUMN `date` date NOT NULL COMMENT '日期' AFTER `nickname`;