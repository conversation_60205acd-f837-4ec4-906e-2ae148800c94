(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fb108bc6"],{"3b38":function(e,t,a){"use strict";a.d(t,"n",(function(){return l})),a.d(t,"m",(function(){return u})),a.d(t,"p",(function(){return o})),a.d(t,"l",(function(){return r})),a.d(t,"c",(function(){return d})),a.d(t,"g",(function(){return i})),a.d(t,"f",(function(){return c})),a.d(t,"e",(function(){return s})),a.d(t,"j",(function(){return b})),a.d(t,"k",(function(){return v})),a.d(t,"i",(function(){return f})),a.d(t,"h",(function(){return p})),a.d(t,"a",(function(){return m})),a.d(t,"o",(function(){return h})),a.d(t,"b",(function(){return _})),a.d(t,"q",(function(){return g})),a.d(t,"d",(function(){return O}));var n=a("b775");function l(e){return Object(n["a"])({url:"/admin/pintuan/data.html",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/admin/pintuan/store.html",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/admin/pintuan/change_status",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/admin/pintuan/editStock",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/admin/pintuan/editSort",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:e})}function _(e){return Object(n["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/admin/pintuan/update.html",method:"post",data:e})}function O(e){return Object(n["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:e})}},"4f11":function(e,t,a){"use strict";a("b0f5")},6229:function(e,t,a){"use strict";a.d(t,"n",(function(){return d})),a.d(t,"o",(function(){return i})),a.d(t,"h",(function(){return c})),a.d(t,"j",(function(){return s})),a.d(t,"m",(function(){return b})),a.d(t,"e",(function(){return v})),a.d(t,"i",(function(){return f})),a.d(t,"u",(function(){return p})),a.d(t,"t",(function(){return m})),a.d(t,"r",(function(){return h})),a.d(t,"s",(function(){return _})),a.d(t,"l",(function(){return g})),a.d(t,"g",(function(){return O})),a.d(t,"d",(function(){return j})),a.d(t,"b",(function(){return w})),a.d(t,"c",(function(){return y})),a.d(t,"a",(function(){return G})),a.d(t,"q",(function(){return I})),a.d(t,"p",(function(){return C})),a.d(t,"v",(function(){return S}));var n=a("b775"),l=a("6dab");a.d(t,"w",(function(){return l["i"]}));var u=a("d74f");a.d(t,"k",(function(){return u["i"]}));var o=a("3b38");a.d(t,"f",(function(){return o["a"]}));var r="/goodscoupon/admin";function d(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/lists.html"),method:"get",params:e})}function i(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/receive.html"),method:"post",data:e})}function c(e,t){return Object(n["a"])({url:"".concat(r,"/goodscoupon/deleteMemberCoupon.html?coupon_id=").concat(t),method:"post",data:e})}function s(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/export.html"),method:"post",data:e,responseType:"blob"})}function b(e){return Object(n["a"])({url:"".concat(r,"/goodsCouponRule/list.html"),method:"get",params:e})}function v(e){return Object(n["a"])({url:"".concat(r,"/goodsCouponRule/add.html"),method:"post",data:e})}function f(e){return Object(n["a"])({url:"".concat(r,"/goodsCouponRule/detailList.html"),method:"get",params:e})}function p(e){return Object(n["a"])({url:"".concat(r,"/goodsCouponRule/stopped.html"),method:"post",data:e})}function m(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/shutDown.html"),method:"post",data:e})}function h(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/sendPageData.html"),method:"get",params:e})}function _(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/sendToSelectedMember.html"),method:"post",data:e})}function g(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/goodsData.html"),method:"get",params:e})}function O(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/deleteGoods.html"),method:"post",data:e})}function j(e){return Object(n["a"])({url:"".concat(r,"/goodscoupon/addGoods.html"),method:"post",data:e})}function w(e){return Object(n["a"])({url:"/admin_plus/AddonGoodscoupon/detail",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/admin_plus/AddonGoodscoupon/lists",method:"post",data:e})}function G(e){return Object(n["a"])({url:"/admin_plus/AddonGoodsCouponRule/detail",method:"post",data:e})}function I(e){return Object(n["a"])({url:"/admin_plus/AddonGoodscoupon/sendPage",method:"post",data:e})}function C(e){return Object(n["a"])({url:"/goodscoupon/admin/goodsCouponRule/selectGoodsCoupon.html",method:"post",data:e})}function S(e){return Object(n["a"])({url:"/goodscoupon/admin/goodsCouponRule/tokenGetTempList.html",method:"post",data:e})}},"6dab":function(e,t,a){"use strict";a.d(t,"g",(function(){return l})),a.d(t,"i",(function(){return u})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return r})),a.d(t,"f",(function(){return d})),a.d(t,"d",(function(){return i})),a.d(t,"c",(function(){return c})),a.d(t,"e",(function(){return s})),a.d(t,"b",(function(){return b}));var n=a("b775");function l(e){return Object(n["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/admin/upload/upload.html",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/topic/admin/topic/add.html",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:e})}},af0e:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[e._v("优惠券详情")]),e._v(" "),a("div",{staticClass:"user-info"},[a("div",[a("span",[e._v("优惠券名称：")]),e._v("\n            "+e._s(e.details.goodscoupon_name)+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("优惠政策：")]),e._v("\n            满￥"+e._s(e.details.at_least)+"减￥"+e._s(e.details.money)+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("是否公开：")]),e._v("\n            "+e._s(e._f("getStatus")(e.details.privacy_status,e.couponTypeOpt))+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("发放总数量：")]),e._v("\n            "+e._s(e.details.count)+" 张\n        ")]),e._v(" "),a("div",[a("span",[e._v("用户领取限制：")]),e._v("\n            "+e._s(e.details.max_fetch)+" 张\n        ")]),e._v(" "),a("div",[a("span",[e._v("使用范围：")]),e._v("\n            "+e._s(e._f("getStatus")(e.details.use_scenario,e.useScenario))+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("领取有效期：")]),e._v(" "),0==e.details.validity_type?a("i",[e._v("永久有效")]):a("i",[e._v(e._s(e.details.fixed_term)+"天有效")])]),e._v(" "),a("div",[a("span",[e._v("优惠券图片：")]),e._v(" "),a("div",{on:{click:e.showImage}},[a("img",{attrs:{src:e.details.image}})])]),e._v(" "),a("div",[a("span",[e._v("活动开始时间：")]),e._v("\n            "+e._s(e.details.start_time)+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("活动结束时间：")]),e._v("\n            "+e._s(e.details.over_time)+"\n        ")]),e._v(" "),a("div",[a("span",[e._v("用券弹窗提醒：")]),e._v("\n            "+e._s(0==e.details.use_remind?"关闭":"开启")+"\n        ")])]),e._v(" "),e.isShowImage?a("div",{staticClass:"loupe",on:{click:e.showImage}},[a("img",{style:{height:e.height},attrs:{src:e.details.image}})]):e._e()])},l=[],u=a("c71b"),o=a("6229"),r={data:function(){return{couponTypeOpt:u["k"],height:"",isShowImage:!1,details:{},useScenario:[{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定专区可用",value:"5"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}]}},mounted:function(){this.onInit()},methods:{getImageHeight:function(e){var t=new Image;t.src=e,t.width>window.innerWidth?this.height="95vh":this.height=t.height},onInit:function(){var e=this,t=this.$route.query.goodscoupon_type_id;Object(o["b"])({goodscoupon_type_id:t}).then((function(t){var a=t.data;e.details=a.goodscoupon_type_info}))},showImage:function(){this.isShowImage=!this.isShowImage}}},d=r,i=(a("4f11"),a("2877")),c=Object(i["a"])(d,n,l,!1,null,"cc8d1fa4",null);t["default"]=c.exports},b0f5:function(e,t,a){},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"i",(function(){return l})),a.d(t,"H",(function(){return u})),a.d(t,"f",(function(){return o})),a.d(t,"A",(function(){return r})),a.d(t,"x",(function(){return d})),a.d(t,"e",(function(){return i})),a.d(t,"w",(function(){return c})),a.d(t,"c",(function(){return s})),a.d(t,"O",(function(){return b})),a.d(t,"j",(function(){return v})),a.d(t,"k",(function(){return f})),a.d(t,"l",(function(){return p})),a.d(t,"T",(function(){return m})),a.d(t,"d",(function(){return h})),a.d(t,"Q",(function(){return _})),a.d(t,"p",(function(){return g})),a.d(t,"P",(function(){return O})),a.d(t,"m",(function(){return j})),a.d(t,"I",(function(){return w})),a.d(t,"L",(function(){return y})),a.d(t,"N",(function(){return G})),a.d(t,"M",(function(){return I})),a.d(t,"S",(function(){return C})),a.d(t,"s",(function(){return S})),a.d(t,"B",(function(){return k})),a.d(t,"z",(function(){return A})),a.d(t,"K",(function(){return P})),a.d(t,"C",(function(){return R})),a.d(t,"h",(function(){return D})),a.d(t,"g",(function(){return T})),a.d(t,"o",(function(){return x})),a.d(t,"G",(function(){return L})),a.d(t,"J",(function(){return q})),a.d(t,"v",(function(){return E})),a.d(t,"F",(function(){return J})),a.d(t,"r",(function(){return M})),a.d(t,"b",(function(){return N})),a.d(t,"q",(function(){return B})),a.d(t,"R",(function(){return H})),a.d(t,"u",(function(){return V})),a.d(t,"t",(function(){return $})),a.d(t,"D",(function(){return z})),a.d(t,"E",(function(){return F})),a.d(t,"y",(function(){return K})),a.d(t,"n",(function(){return Q}));var n=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],l=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],o=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],r=[{label:"是",code:1},{label:"否",code:0}],d=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],i=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],s=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],b=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],v=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],p=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],O=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],j=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],w=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],y=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],G=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],I=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],C=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],S=[{value:"0",label:"参团"},{value:"1",label:"团长"}],k=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],A=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],P=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],R=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],D=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],T=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],x=[{value:"1",label:"是"},{value:"2",label:"否"}],L=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],q=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],E=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],J=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],M=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],B=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],H=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],V=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],$=[{label:"按订单编号",value:"order_no"}],z=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],F=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],K=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],Q=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]}}]);