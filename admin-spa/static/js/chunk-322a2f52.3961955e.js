(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-322a2f52"],{"0431":function(e,t,a){"use strict";a("41e3")},"0bbe":function(e,t,a){"use strict";a.d(t,"k",(function(){return l})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return s})),a.d(t,"g",(function(){return i})),a.d(t,"j",(function(){return o})),a.d(t,"d",(function(){return c})),a.d(t,"e",(function(){return u})),a.d(t,"n",(function(){return d})),a.d(t,"b",(function(){return p})),a.d(t,"f",(function(){return f})),a.d(t,"h",(function(){return h})),a.d(t,"o",(function(){return m})),a.d(t,"l",(function(){return v})),a.d(t,"i",(function(){return b})),a.d(t,"m",(function(){return _}));var n=a("b775");function l(e){return Object(n["a"])({url:"/admin/order/lists.html",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/admin_plus/Order/lists",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/admin/order/exportOrderGoods.html",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/admin/upload/file.html",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/admin/order/importOrderDelivery.html",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/admin/order/exportSupplierOrders.html",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/admin/order/exportSupplierRecord.html",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/admin/Order/orderHandSync.html",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/admin_plus/Order/detail",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/admin/express/expressCompany.html",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/shop/address/getAreaList.html",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/admin/order/sellerRemark.html",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/admin/order/orderComplain.html",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/admin/order/getOrderInfo",method:"post",data:e})}function _(e){return Object(n["a"])({url:"/admin/order/delivery",method:"post",data:e})}},"15fd":function(e,t,a){"use strict";function n(e,t){if(null==e)return{};var a,n,l={},r=Object.keys(e);for(n=0;n<r.length;n++)a=r[n],t.indexOf(a)>=0||(l[a]=e[a]);return l}function l(e,t){if(null==e)return{};var a,l,r=n(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(l=0;l<s.length;l++)a=s[l],t.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}a.d(t,"a",(function(){return l}))},"35a2":function(e,t){e.exports={rgb2hsl:function(e){var t,a,n,l=e[0]/255,r=e[1]/255,s=e[2]/255,i=Math.min(l,r,s),o=Math.max(l,r,s),c=o-i;return o==i?t=0:l==o?t=(r-s)/c:r==o?t=2+(s-l)/c:s==o&&(t=4+(l-r)/c),t=Math.min(60*t,360),t<0&&(t+=360),n=(i+o)/2,a=o==i?0:n<=.5?c/(o+i):c/(2-o-i),[t,100*a,100*n]},rgb2hsv:function(e){var t,a,n,l=e[0],r=e[1],s=e[2],i=Math.min(l,r,s),o=Math.max(l,r,s),c=o-i;return a=0===o?0:c/o*1e3/10,o==i?t=0:l==o?t=(r-s)/c:r==o?t=2+(s-l)/c:s==o&&(t=4+(l-r)/c),t=Math.min(60*t,360),t<0&&(t+=360),n=o/255*1e3/10,[t,a,n]},hsl2rgb:function(e){var t,a,n,l,r,s=e[0]/360,i=e[1]/100,o=e[2]/100;if(0===i)return r=255*o,[r,r,r];a=o<.5?o*(1+i):o+i-o*i,t=2*o-a,l=[0,0,0];for(var c=0;c<3;c++)n=s+1/3*-(c-1),n<0&&n++,n>1&&n--,r=6*n<1?t+6*(a-t)*n:2*n<1?a:3*n<2?t+(a-t)*(2/3-n)*6:t,l[c]=255*r;return l},hsl2hsv:function(e){var t,a,n=e[0],l=e[1]/100,r=e[2]/100;return r*=2,l*=r<=1?r:2-r,a=(r+l)/2,t=2*l/(r+l),[n,100*t,100*a]},hsv2rgb:function(e){var t=e[0]/60,a=e[1]/100,n=e[2]/100,l=Math.floor(t)%6,r=t-Math.floor(t),s=255*n*(1-a),i=255*n*(1-a*r),o=255*n*(1-a*(1-r));switch(n*=255,l){case 0:return[n,o,s];case 1:return[i,n,s];case 2:return[s,n,o];case 3:return[s,i,n];case 4:return[o,s,n];case 5:return[n,s,i]}},hsv2hsl:function(e){var t,a,n=e[0],l=e[1]/100,r=e[2]/100;return a=(2-l)*r,t=l*r,t/=a<=1?a:2-a,a/=2,[n,100*t,100*a]},rgb2hex:function(e,t,a){return"#"+((256+e<<8|t)<<8|a).toString(16).slice(1)},hex2rgb:function(e){return e="0x"+e.slice(1).replace(e.length>4?e:/./g,"$&$&")|0,[e>>16,e>>8&255,255&e]}}},3843:function(e,t,a){"use strict";a("51ec")},"41e3":function(e,t,a){},4810:function(e,t,a){},"51ec":function(e,t,a){},"61e0":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.storeDialog,"append-to-body":""},on:{"update:visible":function(t){e.storeDialog=t}}},[a("div",["驳回申请"==e.title?a("span",{staticClass:"reason danger"},[e._v("请填写驳回申请的原因")]):e._e(),e._v(" "),a("el-input",{attrs:{type:"textarea","show-word-limit":"",autosize:{minRows:6},placeholder:"备注内容",maxlength:e.max||"200"},on:{input:e.onInput},model:{value:e.remark,callback:function(t){e.remark=t},expression:"remark"}}),e._v(" "),a("p",[e._v("不超过"+e._s(e.max||200)+"个字")])],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.storeDialog=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确 定")])],1)])},l=[],r={data:function(){return{remark:"",storeDialog:!1,info:{}}},props:["value","title","max"],methods:{init:function(e,t){this.info=e,this.remark=t,this.storeDialog=!0},onInput:function(e){this.$emit("input",e)},onSubmit:function(){var e=this;this.$alert("保存后该信息将同步更新到用户端订单详情中，请确定。","提示",{callback:function(t){if(e.remark)return e.$emit("setRemark",e.remark,e.info),void(e.storeDialog=!1);e.$message.error("商家备注内容不能为空")}})}}},s=r,i=(a("0431"),a("2877")),o=Object(i["a"])(s,n,l,!1,null,"b3b74f70",null);t["a"]=o.exports},"6af2":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[e.showSearch?a("div",{staticClass:"filter-container"},[a("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),a("div",{staticClass:"flex-b-c buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(t){e.handleQuery(),e.fisrtSearch=!0}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),a("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.tabsIndex,callback:function(t){e.tabsIndex=t},expression:"tabsIndex"}},e._l(e.formopts.orderStateOpts,(function(e,t){return a("el-tab-pane",{key:t,attrs:{label:e.label,name:e.value}})})),1),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-list"},[a("div",{staticClass:"btns"},[a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.batchExport()}}},[e._v("批量导出")]),e._v(" "),a("el-upload",{ref:"upload",staticClass:"upload-menu",attrs:{action:"","show-file-list":!1,"auto-upload":!1,"on-change":e.handlePreview}},[a("el-button",{attrs:{slot:"trigger",plain:"",size:"small"},slot:"trigger"},[e._v("批量发货")])],1),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.orderExport()}}},[e._v("供应商订单导出")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.recordExport()}}},[e._v("供应商对账导出")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.handleSync()}}},[e._v("同步待发货订单到供应链")])],1),e._v(" "),a("order-table",{ref:"order",attrs:{isSearch:!0,searchForm:e.form,showSearch:e.showSearch},on:{toggleSearch:e.toggleSearch}})],1)],1)},l=[],r=(a("7f7f"),a("ac6a"),a("456d"),a("28a5"),a("c7eb")),s=(a("96cf"),a("1da1")),i=a("b885"),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"order-list"},[a("div",{staticClass:"toolbox"},[e.isSearch?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.showSearch?"隐藏搜索":"显示搜索",placement:"top"}},[a("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-search"},on:{click:function(t){return e.toggleSearch()}}})],1):e._e(),e._v(" "),e.hideRefresh?e._e():a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"刷新",placement:"top"}},[a("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-refresh"},on:{click:function(t){return e.refresh()}}})],1),e._v(" "),e.columnList?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"显隐列",placement:"top"}},[a("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-menu"},on:{click:function(t){return e.showColumn()}}})],1):e._e()],1),e._v(" "),a("table",[a("thead",[a("tr",e._l(e.columnList,(function(t,n){return a("td",{key:n,style:{width:t.width}},["check"==t.type?a("label",{staticClass:"o_checkbox",class:{active:e.checkAll||e.showCheck},on:{click:e.handleAllCheck}},[e.checkAll||e.showCheck?a("i",{class:e.checkAll?"el-icon-check":"el-icon-minus"}):e._e()]):a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.title,placement:"top"}},[a("span",{staticClass:"title"},[e._v(e._s(t.title))])])],1)})),0)]),e._v(" "),e._l(e.list,(function(t,n){return a("tbody",{key:n},[a("tr",{staticClass:"line"}),e._v(" "),a("tr",{staticClass:"order-info"},[a("td",{attrs:{colspan:e.columnList.length+1}},[a("div",[a("span",[e._v("订单编号："+e._s(t.order_no))]),e._v(" "),a("span",[e._v("会员ID："+e._s(t.member_id))]),e._v(" "),a("span",[e._v("会员手机号："+e._s(t.mobile))]),e._v(" "),a("span",[e._v("店铺名称："+e._s(t.site_name))]),e._v(" "),a("span",[e._v("供应商："+e._s(t.supply_shop_name))]),e._v(" "),a("span",[e._v("订单总优惠(元)："+e._s(t.goodscoupon_money))]),e._v(" "),a("span",[e._v("下单时间："+e._s(e._f("parseTime")(t.create_time)))]),e._v(" "),a("span",[e._v("支付方式："+e._s(t.pay_type_name))]),e._v(" "),a("span",[e._v("运营组："+e._s(t.operate_group_name||"无"))])]),e._v(" "),a("div",[a("span",[e._v("订单类型："+e._s(t.order_type_name))]),e._v(" "),a("span",{staticClass:"gyl_no"},[e._v("供应链单号："+e._s(t.supply_order_no))])])])]),e._v(" "),e._l(t.order_goods,(function(n,l){return a("tr",{key:l},[e._l(e.columnList,(function(r,s){var i=r.type,o=r.slot,c=r.faild;return[c?a("td",[1==i?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"￥"+n[c[0]]+"/"+n[c[1]],placement:"top"}},[a("span",{staticClass:"title"},[e._v("￥"+e._s(n[c[0]])+"/"+e._s(n[c[1]]))])]):a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:n[c],placement:"top"}},[a("span",{staticClass:"title"},[e._v(e._s(n[c]))])])],1):e._e(),e._v(" "),0==l&&o?a("td",{class:{check:"check"==i},attrs:{rowspan:t.order_goods.length}},["check"==i?a("label",{staticClass:"o_checkbox",class:{active:t.isActive},on:{click:function(a){return e.checkGoods(t)}}},[t.isActive?a("i",{staticClass:"el-icon-check"}):e._e()]):e._e(),e._v(" "),2==i?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.supply_order_no,placement:"top"}},[a("span",{staticClass:"title"},[e._v(e._s(t.supply_order_no))])]):e._e(),e._v(" "),3==i?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.name+" / "+t.mobile+" / "+t.full_address,placement:"top"}},[a("span",[e._v("\n                                "+e._s(t.name)+" "),a("br"),e._v("\n                                "+e._s(t.mobile)+" "),a("br"),e._v("\n                                "+e._s(t.full_address)+"\n                            ")])]):e._e(),e._v(" "),4==i?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.order_status_name+" / "+t.sync_text,placement:"top"}},[a("span",[e._v("\n                                "+e._s(t.order_status_name)+" "),a("br"),e._v(" "),a("span",{staticClass:"danger"},[e._v(e._s(t.sync_text))])])]):e._e(),e._v(" "),6==i?a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:n.real_pay_money+" "+(parseFloat(n.real_pay_money)<parseFloat(n.cost_money)?"/ 价格异常":""),placement:"top"}},[a("span",[e._v("\n                                "+e._s(n.real_pay_money)),a("br"),e._v(" "),parseFloat(n.real_pay_money)<parseFloat(n.cost_money)?a("span",{staticClass:"danger"},[e._v("价格异常")]):e._e()])]):e._e(),e._v(" "),5==i?a("div",{staticClass:"order-btns"},[a("button-single",{attrs:{permission:"view",text:"详情"},on:{click:function(a){return e.details(t.order_id)}}}),e._v(" "),2!=t.sync_status?a("button-single",{attrs:{permission:"sync",text:"手动同步"},on:{click:function(a){return e.handleSync(t.order_id)}}}):e._e(),e._v(" "),1==t.order_status?a("button-single",{attrs:{permission:"delivery",text:"发货"},on:{click:function(a){return e.$refs.express.init(t)}}}):e._e(),e._v(" "),e.applyStatus(t)?a("button-single",{attrs:{permission:"apply",text:"申请售后"},on:{click:function(a){return e.$refs.afterSales.init(t.order_goods,t.order_id)}}}):e._e(),e._v(" "),a("button-single",{attrs:{permission:"remark",text:"备注"},on:{click:function(a){return e.$refs.storeRemark.init(t,t.remark)}}}),e._v(" "),a("button-single",{attrs:{permission:"remark",text:"管理员备注"},on:{click:function(a){return e.$refs.adminRemark.init(t,t.admin_remark)}}})],1):e._e()],1):e._e()]}))],2)}))],2)}))],2),e._v(" "),0!=e.list.length||e.loading?e._e():a("div",{staticClass:"no-data"},[e._v("\n        暂无数据\n    ")]),e._v(" "),a("remark",{ref:"storeRemark",attrs:{title:"商家备注"},on:{setRemark:e.setStoreRemark},model:{value:e.formData.storeRemark,callback:function(t){e.$set(e.formData,"storeRemark",t)},expression:"formData.storeRemark"}}),e._v(" "),a("remark",{ref:"adminRemark",attrs:{title:"管理员备注"},on:{setRemark:e.setAdminRemark},model:{value:e.formData.adminRemark,callback:function(t){e.$set(e.formData,"adminRemark",t)},expression:"formData.adminRemark"}}),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.options.page||1,"page-sizes":[100,200,300,400],"page-size":e.options.page_size,layout:"total, sizes, prev, pager, next",total:e.options.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"显示隐藏列表",visible:e.openDialog,"append-to-body":""},on:{"update:visible":function(t){e.openDialog=t}}},[a("el-transfer",{attrs:{titles:["显示","隐藏"],props:e.cusProps,data:e.filterColumns},on:{change:e.dataChange},model:{value:e.hideColumns,callback:function(t){e.hideColumns=t},expression:"hideColumns"}})],1),e._v(" "),a("express",{ref:"express",on:{onComplete:e.applyAfterSales}}),e._v(" "),a("after-sales",{ref:"afterSales",on:{afterSales:e.applyAfterSales}})],1)},c=[],u=a("5530"),d=a("15fd"),p=a("c57b"),f=a("d2e6"),h=a("61e0"),m=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"发货",visible:e.sendGoodsDialog,width:"80%","append-to-body":""},on:{"update:visible":function(t){e.sendGoodsDialog=t}}},[a("o-table",{attrs:{columns:e.sendGoodsColimns,hideRefresh:!0,isPage:!1,data:e.sendGoodsList},scopedSlots:e._u([{key:"name",fn:function(t){var n=t.row;return[a("el-input",{attrs:{type:"text",placeholder:"收货人"},model:{value:n.name,callback:function(t){e.$set(n,"name",t)},expression:"row.name"}})]}},{key:"mobile",fn:function(t){var n=t.row;return[a("el-input",{attrs:{type:"text",placeholder:"收货人手机号码"},model:{value:n.mobile,callback:function(t){e.$set(n,"mobile",t)},expression:"row.mobile"}})]}},{key:"address",fn:function(t){var n=t.row;return[a("div",{staticClass:"city-item"},[a("el-select",{attrs:{size:"small",placeholder:"省份"},on:{change:function(t){return e.province_change(n,t)}},model:{value:n.province_id,callback:function(t){e.$set(n,"province_id",t)},expression:"row.province_id"}},e._l(e.provideList,(function(e,t){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),a("el-select",{attrs:{size:"small",placeholder:"城市"},on:{change:function(t){return e.city_change(n,t)}},model:{value:n.city_id,callback:function(t){e.$set(n,"city_id",t)},expression:"row.city_id"}},e._l(e.cityList,(function(e,t){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),a("el-select",{attrs:{size:"small",placeholder:"地区"},model:{value:n.district_id,callback:function(t){e.$set(n,"district_id",t)},expression:"row.district_id"}},e._l(e.areaList,(function(e,t){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),a("el-input",{attrs:{type:"text",placeholder:"收货人手机号码",size:"small"},model:{value:n.address,callback:function(t){e.$set(n,"address",t)},expression:"row.address"}})],1)]}},{key:"expressList",fn:function(t){var n=t.row;return[e._l(n.expressList,(function(t,l){return a("div",{key:l},[a("span",[e._v("包裹"+e._s(l+1))]),e._v(" "),a("div",{staticClass:"express"},[a("el-select",{staticStyle:{width:"90px"},attrs:{size:"small",placeholder:"地区"},model:{value:t.express_company_id,callback:function(a){e.$set(t,"express_company_id",a)},expression:"item.express_company_id"}},e._l(e.logisticsData,(function(e,t){return a("el-option",{key:t,attrs:{label:e.company_name,value:e.company_id}})})),1),e._v(" "),a("el-input",{attrs:{type:"text",placeholder:"快递单号",size:"small"},model:{value:t.delivery_no,callback:function(a){e.$set(t,"delivery_no",a)},expression:"item.delivery_no"}}),e._v(" "),a("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.delExpress(n,e.i)}}})],1)])})),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:function(t){return e.addExpress(n)}}},[e._v("增加发货包裹")])]}}])}),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.toConfirm(0)}}},[e._v("确 定")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.toConfirm(1)}}},[e._v("保存地址")])],1)],1)},v=[],b=(a("7514"),a("0bbe")),_=(a("ed08"),{data:function(){return{sendGoodsColimns:p["g"],sendGoodsList:[],logisticsData:f["b"],sendGoodsDialog:!1,provideList:[],cityList:[],areaList:[],companyList:[]}},created:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getPcaData(1,0);case 2:return this.provideList=e.sent,e.next=5,this.getExpressCompany();case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{checkData:function(){for(var e=0;e<this.sendGoodsList.length;e++){if(!this.sendGoodsList[e].name)return this.$message.error("收货人不能为空"),!1;if(!this.sendGoodsList[e].mobile)return this.$message.error("收货人手机号不能为空"),!1}return!0},getExpressCompany:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(b["f"])({page_size:100});case 3:t=e.sent,0==t.code&&(this.companyList=t.data.list),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),delExpress:function(e,t){e.expressList.splice(t,1)},addExpress:function(e){e.expressList.push({})},getPcaData:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t,a){var n,l;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=[],e.prev=1,e.next=4,Object(b["h"])({level:t,pid:a});case 4:l=e.sent,0==l.code&&(n=l.data),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:return e.abrupt("return",n);case 11:case"end":return e.stop()}}),e,null,[[1,8]])})));function t(t,a){return e.apply(this,arguments)}return t}(),province_change:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t,a){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getPcaData(2,a);case 2:return this.cityList=e.sent,e.next=5,this.getPcaData(3,this.cityList[0].id);case 5:this.areaList=e.sent,t.city_id=this.cityList[0].id,t.district_id=this.areaList[0].id;case 8:case"end":return e.stop()}}),e,this)})));function t(t,a){return e.apply(this,arguments)}return t}(),city_change:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t,a){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getPcaData(3,a);case 2:this.areaList=e.sent,t.district_id=this.areaList[0].id;case 4:case"end":return e.stop()}}),e,this)})));function t(t,a){return e.apply(this,arguments)}return t}(),getPca:function(e,t){var a={};return f["d"].find((function(n){e==n.id&&(a.cityList=n.children,n.children.find((function(e){t==e.id&&(a.areaList=e.children)})))})),a},init:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t){var a;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.province_id){e.next=4;break}return e.next=3,this.getPcaData(2,t.province_id);case 3:this.cityList=e.sent;case 4:if(!t.city_id){e.next=8;break}return e.next=7,this.getPcaData(3,t.city_id);case 7:this.areaList=e.sent;case 8:return e.prev=8,e.next=11,Object(b["i"])({order_id:t.order_id});case 11:a=e.sent,0==a.code&&(this.sendGoodsList=[a.data]),e.next=17;break;case 15:e.prev=15,e.t0=e["catch"](8);case 17:this.sendGoodsDialog=!0;case 18:case"end":return e.stop()}}),e,this,[[8,15]])})));function t(t){return e.apply(this,arguments)}return t}(),toConfirm:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t){var a,n,l;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.checkData()){e.next=2;break}return e.abrupt("return",!1);case 2:for(a={isAddress:t,name:this.sendGoodsList[0].name,mobile:this.sendGoodsList[0].mobile,province_id:this.sendGoodsList[0].province_id,city_id:this.sendGoodsList[0].city_id,district_id:this.sendGoodsList[0].district_id,address:this.sendGoodsList[0].address,order_id:this.sendGoodsList[0].order_id,order_status:this.sendGoodsList[0].order_status},n=0;n<this.sendGoodsList[0].expressList.length;n++)a["express[".concat(n,"][express_company_id]")]=this.sendGoodsList[0].expressList[n].express_company_id,a["express[".concat(n,"][delivery_no]")]=this.sendGoodsList[0].expressList[n].delivery_no;return e.prev=4,e.next=7,Object(b["m"])(a);case 7:l=e.sent,0==l.code?(this.$message.success(l.message),this.$emit("onComplete"),this.sendGoodsDialog=!1):this.$message.error(l.message),e.next=13;break;case 11:e.prev=11,e.t0=e["catch"](4);case 13:case"end":return e.stop()}}),e,this,[[4,11]])})));function t(t){return e.apply(this,arguments)}return t}()}}),y=_,g=(a("6f2f"),a("2877")),k=Object(g["a"])(y,m,v,!1,null,"58698dfc",null),x=k.exports,O=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"申请售后",width:"80%",visible:e.dialog,"append-to-body":""},on:{"update:visible":function(t){e.dialog=t}}},[a("el-form",{attrs:{"label-width":"180px"}},[a("el-form-item",{attrs:{label:"售后类型："}},[e._v("\n            退款\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"问题商品："}},[a("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{columns:e.afterSalesColumns,hideRefresh:!0,isPage:!1,data:e.list},scopedSlots:e._u([{key:"seckill",fn:function(t){var n=t.row;return[a("span",{staticClass:"danger"},[e._v("优惠券：￥"+e._s(n.goodscoupon_money))])]}},{key:"action",fn:function(t){var n=t.row,l=t.index;return[a("div",{staticClass:"tooltip"},[a("label",{staticClass:"o_checkbox",class:{active:n.isActive},on:{click:function(t){return e.selectAction(n,l)}}},[n.isActive?a("i",{staticClass:"el-icon-check"}):e._e()])])]}}])})],1),e._v(" "),a("el-form-item",{attrs:{label:"申请理由："}},[a("div",{staticClass:"textarea"},[a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea","show-word-limit":"",autosize:{minRows:4},placeholder:"备注内容",maxlength:"100"},model:{value:e.formData.complain_reason,callback:function(t){e.$set(e.formData,"complain_reason",t)},expression:"formData.complain_reason"}}),e._v(" "),a("p",[e._v("不超过100个字")])],1)]),e._v(" "),a("el-form-item",{attrs:{label:"退款金额（元）："}},[a("el-input",{staticStyle:{width:"50%"},attrs:{readonly:""},model:{value:e.formData.refund_money,callback:function(t){e.$set(e.formData,"refund_money",t)},expression:"formData.refund_money"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.onSubmit}},[e._v("提交")])],1)],1)],1)},w=[],C={data:function(){return{dialog:!1,loading:!1,afterSalesColumns:p["a"],list:[],formData:{},order_id:0}},methods:{init:function(e,t){console.log(e),this.list=e,this.order_id=t,this.dialog=!0},selectAction:function(e,t){if(1!=e.refund_status){e.isActive=!e.isActive,console.log(e);var a=0;this.list.forEach((function(e){e.isActive&&(a+=parseFloat(e.real_pay_money))})),this.formData.refund_money=a,this.$set(this.list,t,e)}},onSubmit:function(){var e=this;this.$confirm("提交后，将会把问题商品的退款状态变更为【已退款】，并将退款金额".concat(this.formData.refund_money,"元原路退回给支付账户，确定要提交吗？"),"信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){var t=e.list.map((function(e){return e.order_goods_id}));Object(b["l"])(Object(u["a"])(Object(u["a"])({order_goods_ids:t.join(",")},e.formData),{},{complain_type:"1"})).then((function(t){var a=t.code,n=t.message;e.$emit("afterSales"),e.dialog=!1,a<0?e.$message.success(n):e.$message.success("申请退款成功")}))})).catch((function(){}))}}},j=C,S=(a("a8d6"),Object(g["a"])(j,O,w,!1,null,"0ee4ec5f",null)),L=S.exports,$=["order_time","pay_time"],D={directives:{permission:i["h"]},props:{hideRefresh:{type:Boolean,default:!1},isSearch:{type:Boolean,default:!1},showSearch:{type:Boolean,default:!1},searchForm:{type:Object,default:function(){}},check:!1},components:{remark:h["a"],express:x,afterSales:L,ButtonSingle:i["b"]},data:function(){return{sendGoodsColimns:p["g"],sendGoodsDialog:!1,formData:{},adminDialog:!1,storeDialog:!1,columns:p["d"],options:{page:1,page_size:200,total:0},checkAll:!1,showCheck:!1,list:[],sendGoodsList:[],openDialog:!1,cusProps:{key:"id",label:"title"},columnList:p["d"],hideColumns:[],filterColumns:[],loading:!1}},methods:{applyAfterSales:function(){this.handleQuery()},setStoreRemark:function(e,t){var a=this,n=t.order_id;this.loading=!0,Object(b["o"])({mark_type:"seller",remark:e,order_id:n}).then((function(e){a.$message.success("商家备注提交成功"),a.handleQuery()}))},setAdminRemark:function(e,t){var a=this,n=t.order_id;this.loading=!0,Object(b["o"])({mark_type:"admin",remark:e,order_id:n}).then((function(e){a.$message.success("管理员备注提交成功"),a.handleQuery()}))},applyStatus:function(e){var t=[1,3,6,7],a=[1,3,4];return 1==e.pay_status&&t.indexOf(e.order_create_type)>-1&&(2==e.sync_status&&1==e.order_status||2!=e.sync_status&&a.indexOf(e.order_status)>-1)},handleAllCheck:function(){var e=this;this.checkAll=!this.checkAll,this.list.forEach((function(t){t.isActive=e.checkAll})),this.checkAll||(this.showCheck=!1)},checkGoods:function(e){var t=!1;this.list.forEach((function(a){a.order_no==e.order_no&&(a.isActive=!a.isActive),a.isActive&&(t=!0)})),this.showCheck=t,this.$forceUpdate()},details:function(e){this.$router.push({path:"/order/orderDetails",query:{id:e}})},init:function(){this.checkAll=!1,this.showCheck=!1,this.handleQuery()},handleSizeChange:function(e){this.handleQuery({page:this.options.page,page_size:e})},handleCurrentChange:function(e){this.handleQuery({pageNo:e,page_size:this.options.page_size})},handleSync:function(e){var t=this,a=[];e?a.push(e):this.list.map((function(e){e.isActive&&a.push(e.order_id)})),a.length>0?this.$confirm("确定重新请求同步订单供应链吗？","信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){Object(b["n"])({order_ids:a.join(",")}).then((function(e){t.handleQuery(),t.$message.success("同步订单供应链成功")}))})).catch((function(){})):this.$message.info("请选择待发货订单")},getTableList:function(e){var t=this;this.loading=!0;var a=this.searchForm,n=a.order_time,l=a.pay_time,r=Object(d["a"])(a,$);n&&(r.start_time=this.$format(n[0]),r.end_time=this.$format(n[1])),l&&(r.start_pay_time=this.$format(l[0]),r.end_pay_time=this.$format(l[1])),Object(b["k"])(Object(u["a"])(Object(u["a"])({},e),{},{order_label:void 0},r)).then((function(e){var a=e.data,n=a.count,l=a.list;t.options.total=n,t.list=l,t.loading=!1}))},handleQuery:function(e){var t;this.loading=!0,e&&(t={page:e.page,limit:e.page_size});var a=t||{page:1,limit:this.options.page_size};this.getTableList(a)},refresh:function(){this.getTableList()},toggleSearch:function(){this.$emit("toggleSearch")},dataChange:function(e){var t=JSON.parse(JSON.stringify(this.columns));this.columnList=t.filter((function(t){if(e.indexOf(t.id)<0)return t})),console.log(this.columnList)},showColumn:function(){var e=JSON.parse(JSON.stringify(this.columns));this.filterColumns=e.filter((function(e){return e.title})),this.openDialog=!0}}},z=D,A=(a("772e"),Object(g["a"])(z,o,c,!1,null,"5e85e50c",null)),G=A.exports,R=a("c71b"),E=[{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"收货人姓名",model:"member_name",placeholder:"请输入收货人姓名"},{type:"input",label:"收货人手机号",model:"mobile",placeholder:"请输入收货人手机号"},{type:"input",label:"商品名称",model:"order_name",placeholder:"请输入商品名称"},{type:"input",label:"店铺名称",model:"site_name",placeholder:"请输入店铺名称"},{type:"input",label:"物流单号",model:"delivery_no",placeholder:"请输入物流单号"},{type:"input",label:"流水号",model:"out_trade_no",placeholder:"请输入流水号"},{type:"input",label:"供应链单号",model:"supply_order_no",placeholder:"请输入供应链单号"},{type:"input",label:"会员手机号码",model:"member_phone",placeholder:"请输入会员手机号码"},{type:"input",label:"运营组",model:"operate_group",placeholder:"请输入运营组"},{type:"select",label:"供应商名称",model:"supplier",placeholder:"请选择",options:{name:"supplierOpts"}},{type:"select",label:"供应链同步状态",model:"order_chain",placeholder:"请选择",options:{name:"scmSyncOpts"}},{type:"select",label:"订单类型",model:"order_search",placeholder:"请选择",options:{name:"orderTypeOpts"}},{type:"quickTime",label:"下单时间",model:"order_time",timeType:"datetimerange"},{type:"time",label:"支付时间",model:"pay_time",timeType:"datetimerange"}],P=(a("35a2"),{components:{FormQuery:i["d"],orderTable:G},data:function(){return{checkList:[],showSearch:!0,baseConfig:{labelWidth:"120px"},tabsIndex:"all",formopts:{orderStateOpts:R["w"],supplierOpts:[],scmSyncOpts:[],orderTypeOpts:[]},form:{},formConfig:E,loading:!1,fisrtSearch:!1,pageData:{}}},created:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.tabsIndex=this.$route.query.order_type||"all","all"!=this.tabsIndex?this.form.order_status=this.tabsIndex:delete this.form.order_status,e.next=4,this.getData();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),mounted:function(){this.$refs.order.init()},methods:{getData:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t,a,n,l;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(b["a"])({});case 3:if(t=e.sent,0==t.code){for(a in this.pageData=t.data,this.formopts.supplierOpts=[{label:"全部",value:""}],t.data.supplier_list)this.formopts.supplierOpts.push({label:t.data.supplier_list[a],value:a});for(n in this.formopts.scmSyncOpts=[{label:"全部",value:""}],t.data.order_chain_type)this.formopts.scmSyncOpts.push({label:t.data.order_chain_type[n],value:n});for(l in this.formopts.orderTypeOpts=[{label:"全部",value:""}],t.data.order_type_search)this.formopts.orderTypeOpts.push({label:t.data.order_type_search[l].key,value:t.data.order_type_search[l].value})}e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),handleSync:function(){this.$refs.order.handleSync()},handlePreview:function(e){var t=this,a=e.raw;Object(b["g"])({file:a}).then((function(e){var a=e.data.path;Object(b["j"])({path:a}).then((function(e){var a=e.data,n=e.message;t.$confirm(n,"提示",{confirmButtonText:"下载失败订单",cancelButtonText:"确定",type:"success"}).then((function(){t.download(a),t.handleQuery()})).catch((function(){t.handleQuery()}))}))}))},orderExport:function(){var e=this;Object(b["d"])().then((function(t){var a=t.data;e.download(a)}))},recordExport:function(){var e=this;Object(b["e"])().then((function(t){var a=t.data;e.download(a)}))},download:function(e){var t=e.split(","),a=t[1].split("/").pop(),n=document.createElement("a");n.href=e,n.download=a,n.click()},batchExport:function(){var e=this,t=!1;Object.keys(this.form).map((function(e){t=!0})),t&&this.fisrtSearch?Object(b["c"])(this.form).then((function(t){var a=t.data;e.download(a)})):(this.$message.info("请至少设置一个筛选条件，并重新搜索后再导出"),console.log(this.form))},handle:function(){this.$message.warning("此功能暂不开放")},handleClick:function(e,t){this.fisrtSearch||(this.fisrtSearch=!0),"all"!=e.name?this.form.order_status=e.name:delete this.form.order_status,this.handleQuery()},handleChange:function(e){console.log(e)},goSkip:function(e,t){this.$router.push({path:e,query:t})},dleQuery:function(){},handleQuery:function(){this.$refs.order.init()},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch}}}),T=P,Q=(a("3843"),Object(g["a"])(T,n,l,!1,null,"24e1eb5a",null));t["default"]=Q.exports},"6f2f":function(e,t,a){"use strict";a("7b03")},"772e":function(e,t,a){"use strict";a("9e88")},"7b03":function(e,t,a){},"9e88":function(e,t,a){},a8d6:function(e,t,a){"use strict";a("4810")},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"i",(function(){return l})),a.d(t,"H",(function(){return r})),a.d(t,"f",(function(){return s})),a.d(t,"A",(function(){return i})),a.d(t,"x",(function(){return o})),a.d(t,"e",(function(){return c})),a.d(t,"w",(function(){return u})),a.d(t,"c",(function(){return d})),a.d(t,"O",(function(){return p})),a.d(t,"j",(function(){return f})),a.d(t,"k",(function(){return h})),a.d(t,"l",(function(){return m})),a.d(t,"T",(function(){return v})),a.d(t,"d",(function(){return b})),a.d(t,"Q",(function(){return _})),a.d(t,"p",(function(){return y})),a.d(t,"P",(function(){return g})),a.d(t,"m",(function(){return k})),a.d(t,"I",(function(){return x})),a.d(t,"L",(function(){return O})),a.d(t,"N",(function(){return w})),a.d(t,"M",(function(){return C})),a.d(t,"S",(function(){return j})),a.d(t,"s",(function(){return S})),a.d(t,"B",(function(){return L})),a.d(t,"z",(function(){return $})),a.d(t,"K",(function(){return D})),a.d(t,"C",(function(){return z})),a.d(t,"h",(function(){return A})),a.d(t,"g",(function(){return G})),a.d(t,"o",(function(){return R})),a.d(t,"G",(function(){return E})),a.d(t,"J",(function(){return P})),a.d(t,"v",(function(){return T})),a.d(t,"F",(function(){return Q})),a.d(t,"r",(function(){return I})),a.d(t,"b",(function(){return B})),a.d(t,"q",(function(){return F})),a.d(t,"R",(function(){return N})),a.d(t,"u",(function(){return M})),a.d(t,"t",(function(){return J})),a.d(t,"D",(function(){return q})),a.d(t,"E",(function(){return H})),a.d(t,"y",(function(){return K})),a.d(t,"n",(function(){return U}));var n=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],l=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],r=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],s=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],i=[{label:"是",code:1},{label:"否",code:0}],o=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],c=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],u=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],p=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],f=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],h=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],b=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],y=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],g=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],k=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],x=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],O=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],w=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],j=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],S=[{value:"0",label:"参团"},{value:"1",label:"团长"}],L=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],$=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],D=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],z=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],A=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],G=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],R=[{value:"1",label:"是"},{value:"2",label:"否"}],E=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],P=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],T=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],Q=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],I=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],B=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],F=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],N=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],M=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],J=[{label:"按订单编号",value:"order_no"}],q=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],H=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],K=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],U=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]}}]);