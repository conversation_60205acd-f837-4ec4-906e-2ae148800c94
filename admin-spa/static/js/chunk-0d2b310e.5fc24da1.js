(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0d2b310e"],{"15fd":function(e,t,a){"use strict";function l(e,t){if(null==e)return{};var a,l,n={},o=Object.keys(e);for(l=0;l<o.length;l++)a=o[l],t.indexOf(a)>=0||(n[a]=e[a]);return n}function n(e,t){if(null==e)return{};var a,n,o=l(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)a=i[n],t.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(o[a]=e[a])}return o}a.d(t,"a",(function(){return n}))},"1b6c":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[e.showSearch?a("div",{staticClass:"filter-container"},[a("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),a("div",{staticClass:"flex-b-c buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(t){return e.handleQuery()}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),a("div",{staticClass:"table-list"},[a("div",{staticClass:"btns"},[e._v("\n            批量操作：\n            "),a("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(t){return e.batchSelect()}}},[e._v("批量选品")]),e._v(" "),e.pageData.is_refreshing?a("el-button",{attrs:{plain:"",size:"small",disabled:""}},[e._v("同步中请稍候刷新查看")]):a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.refreshSelect()}}},[e._v("刷新选品库")]),e._v(" "),e.pageData.last_refresh_information?a("el-link",{staticStyle:{"margin-left":"5px"},attrs:{type:"danger"}},[e._v(e._s(e.pageData.last_refresh_information))]):e._e()],1),e._v(" "),a("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{selection:e.handleSelectionChange,toggleSearch:e.toggleSearch,"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},onSearch:e.getTableList},scopedSlots:e._u([{key:"title",fn:function(t){var l=t.row;return[a("div",{staticClass:"storeTitle"},[a("img",{attrs:{src:l.main_img,alt:""}}),e._v(" "),a("span",[e._v(e._s(l.title))])])]}},{key:"cid",fn:function(t){var a=t.row;return[e._v("\n                "+e._s(e.getClassify(a.cid))+"\n            ")]}},{key:"brand_cid",fn:function(t){var a=t.row;return[e._v("\n                "+e._s(e.getBrand(a.brand_cid))+"\n            ")]}},{key:"supply_price",fn:function(t){var a=t.row;return[e._v("\n                ￥"+e._s(a.supply_price)+"\n            ")]}},{key:"orgin_price",fn:function(t){var a=t.row;return[e._v("\n                ￥"+e._s(a.orgin_price)+"\n            ")]}},{key:"total_stock",fn:function(t){var a=t.row;return[e._v("\n                "+e._s(a.total_stock)+"\n            ")]}},{key:"isout",fn:function(t){var a=t.row;return[e._v("\n                "+e._s(e._f("getStatus")(a.isout,e.formopts.shelvesOpt))+"\n            ")]}},{key:"action",fn:function(t){var l=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.batchSelect(l)}}},[e._v("选品")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/goods/quality/hollowDetails",{pro_no:l.pro_no})}}},[e._v("详情")])]}}])}),e._v(" "),a("o-classify",{ref:"classify",on:{getClassify:e.setClassify},model:{value:e.dialogForm.category_name,callback:function(t){e.$set(e.dialogForm,"category_name",t)},expression:"dialogForm.category_name"}}),e._v(" "),a("el-dialog",{attrs:{title:"选品信息设置：（当前已选中"+e.selecIdx+"个商品）",visible:e.infoSetVisible,width:"40%"},on:{"update:visible":function(t){e.infoSetVisible=t}}},[a("el-form",{attrs:{"label-width":"150px",model:e.dialogForm,rules:e.rules}},[a("el-form-item",{attrs:{label:"商品分类：",prop:"category_name"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.dialogForm.category_name,callback:function(t){e.$set(e.dialogForm,"category_name",t)},expression:"dialogForm.category_name"}}),e._v(" "),a("el-button",{on:{click:function(t){return e.$refs.classify.init()}}},[e._v("选择")])],1),e._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例：",prop:"rebateRate"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:e.dialogForm.reward_company_rate,callback:function(t){e.$set(e.dialogForm,"reward_company_rate",t)},expression:"dialogForm.reward_company_rate"}}),e._v("\n                    %\n                ")],1),e._v(" "),a("el-form-item",{attrs:{label:"店主返佣比例："}},[a("el-input",{staticStyle:{width:"200px"},model:{value:e.dialogForm.reward_shop_rate,callback:function(t){e.$set(e.dialogForm,"reward_shop_rate",t)},expression:"dialogForm.reward_shop_rate"}}),e._v("\n                    % (整数)\n                ")],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.dialogLoading},on:{click:e.submit}},[e._v("确定同步商品")]),e._v(" "),a("el-button",{on:{click:function(t){e.infoSetVisible=!1}}},[e._v("取 消")])],1)],1)],1)])},n=[],o=(a("7f7f"),a("5530")),i=a("15fd"),r=(a("7514"),a("ac6a"),a("8615"),a("c7eb")),u=(a("96cf"),a("1da1")),s=a("2909"),c=a("b885"),d=a("966c"),f=a("327e"),b=a("c71b"),p=a("e720"),v=a("33a6"),m=a("70ff"),h=(a("daba"),["category_name","level"]),g=["upTime"],y={components:{FormQuery:c["d"],classifyDialog:v["a"]},data:function(){return{showSearch:!0,selecIdx:1,baseConfig:{labelWidth:"120px"},dialogForm:{},infoSetVisible:!1,formopts:{originalData:[],classifyData:[],shelvesOpt:b["G"]},form:{},formConfig:[].concat(Object(s["a"])(p["a"]),[{type:"casder",label:"原分类",model:"category_id",placeholder:"请输入原分类",options:{name:"classifyData"},props:{label:"name",value:"cate_id"}}]),loading:!1,columns:d["b"],list:[],dialogLoading:!1,tableSels:[],options:{page:1,page_size:10,total:0},rules:{category_name:[{required:!0,message:"商品分类不能为空",trigger:"change"}],rebateRate:[{required:!0,message:"公司返佣比例",trigger:"blur"}]},pageData:{}}},created:function(){var e=Object(u["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getData();case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),mounted:function(){var e=this;Object(m["c"])({type:2}).then((function(t){var a=t.data.cate;e.formopts.originalData=a})),Object(m["c"])({type:1}).then((function(t){var a=t.data;e.formopts.classifyData=Object.values(a),console.log(e.formopts.classifyData)})),this.handleQuery()},methods:{getData:function(){var e=Object(u["a"])(Object(r["a"])().mark((function e(){var t;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(m["a"])({});case 3:t=e.sent,0==t.code&&(this.pageData=t.data),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),refreshSelect:function(){var e=this;Object(m["d"])().then((function(t){var a=t.message;e.getData(),e.$notify({title:"成功",message:a,type:"success"})}))},getArray:function(e,t,a){var l=this;return e.find((function(e){e.category_id!=t?e.children&&(e.children=l.getArray(e.children,t,a)):e.children=a})),e},getClassifyList:function(e,t,a){var l=this,n={pid:e||0,level:t||0};a&&(n.category_str=a),Object(m["b"])(n).then((function(t){var a=t.data,n=a.map((function(e){return e.child_count>0&&(e.children=[]),e}));e?console.log(l.getArray(l.formopts.classifyData,e,n)):(l.formopts.classifyData=n,console.log(l.classifyData))}))},submit:function(){var e=this,t=[];this.tableSels.map((function(e){t.push(e.pro_no)})),this.dialogForm.goods_ids=t.join(","),this.dialogLoading=!0,Object(m["e"])(this.dialogForm).then((function(t){var a=t.code,l=t.message;e.dialogLoading=!1,0==a?(e.infoSetVisible=!1,e.$message.success(l)):e.$message.error(l)}))},decomep:function(e,t){var a=this,l=[];return e.find((function(e){if(e.category_id!=t){if(e.children){var n=a.decomep(e.children,t);n.length>0&&(l.push(e.category_name),l=l.concat(n))}}else l.push(e.category_name)})),l},setClassify:function(e){var t=e.checkInfo,a=(t.category_name,t.level,Object(i["a"])(t,h));this.dialogForm=Object(o["a"])(Object(o["a"])({},this.dialogForm),a),console.log(this.dialogForm),this.$refs.classify.hide()},getClassify:function(e){return this.decomep(f["a"],e).join(">")},batchSelect:function(e){if(this.tableSels.length>0||e)return this.infoSetVisible=!0,e?(this.selecIdx=1,void(this.tableSels=[e])):void(this.selecIdx=this.tableSels.length);this.$message.info("请选择需要操作的数据")},handleSelectionChange:function(e){this.tableSels=e},getBrand:function(e){var t="";return this.formopts.originalData.find((function(a){a.cate_id==e&&(t=a.name)})),t},goSkip:function(e,t){this.$router.push({path:e,query:t})},handleQuery:function(e){this.loading=!0,this.list=[];var t=e||{page:1,page_size:this.options.page_size};this.getTableList(t)},getTableList:function(e){var t=this,a=this.form,l=a.upTime,n=Object(i["a"])(a,g);l&&(n.up_start_time=this.$format(l[0],"{y}-{m}-{d}"),n.up_end_time=this.$format(l[1],"{y}-{m}-{d}")),Object(m["g"])(Object(o["a"])(Object(o["a"])({},e),n)).then((function(e){var a=e.data,l=a.count,n=a.list;t.options.total=l,t.list=n,t.loading=!1}))},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch}}},_=y,w=(a("9af2"),a("2877")),O=Object(w["a"])(_,l,n,!1,null,"6ef83e13",null);t["default"]=O.exports},"33a6":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"选择商品分类",visible:e.dialogVisible,top:"100px",width:"810px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"classify"},[a("div",{staticClass:"fist-level"},e._l(e.fistLevels,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[0]},on:{click:function(a){e.getClassify("seconddarys",2,t.category_id),e.teriarys=[],e.setTitle(t,0)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"second-level"},e._l(e.seconddarys,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[1]},on:{click:function(a){e.getClassify("teriarys",3,t.category_id),e.setTitle(t,1)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"teriary-level"},e._l(e.teriarys,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[2]},on:{click:function(a){return e.setTitle(t,2)}}},[e._v("\n                "+e._s(t.category_name)+"\n            ")])})),0)]),e._v(" "),a("div",{staticClass:"current"},[e._v("\n        您当前选择的是：\n        "+e._s(e.titles[0])+" "),e.titles[1]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[1])+" "),e.titles[2]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[2])+"\n    ")]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submit}},[e._v("保 存")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("关 闭")])],1)])},n=[],o=(a("28a5"),a("d74f")),i={data:function(){return{dialogVisible:!1,fistLevels:[],seconddarys:[],teriarys:[],titles:[],cateIds:[]}},props:["value"],mounted:function(){this.value&&(this.titles=this.value.split("/")),this.getClassify()},methods:{init:function(e){this.dialogVisible=!0,e&&(this.getClassify("seconddarys",2,e[0]),this.getClassify("teriarys",3,e[1]),this.cateIds=e)},getClassify:function(e,t,a){var l=this,n={level:t||1,pid:a||0};Object(o["i"])(n).then((function(t){var a=t.data;l[e||"fistLevels"]=a}))},setTitle:function(e,t){var a=e.category_name,l=e.category_id;this.titles[t]=a,this.cateIds[t]=l,t<2&&this.titles.splice(t+1,this.titles.length),this.$forceUpdate()},submit:function(){this.$emit("onClassify",this.cateIds),this.$emit("input",this.titles.join("/")),this.dialogVisible=!1}}},r=i,u=(a("564d"),a("2877")),s=Object(u["a"])(r,l,n,!1,null,"260665e1",null);t["a"]=s.exports},"3f5e":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i}));var l=a("b775");function n(e){return Object(l["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(l["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(l["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,a){"use strict";var l=a("a18c"),n={inserted:function(e,t,a){var n=t.value,o=l["a"].app._route.meta&&l["a"].app._route.meta.permissions;o.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},o=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(o)),n.install=o;t["a"]=n},"504c":function(e,t,a){var l=a("9e1e"),n=a("0d58"),o=a("6821"),i=a("52a7").f;e.exports=function(e){return function(t){var a,r=o(t),u=n(r),s=u.length,c=0,d=[];while(s>c)a=u[c++],l&&!i.call(r,a)||d.push(e?[a,r[a]]:r[a]);return d}}},"564d":function(e,t,a){"use strict";a("c16a")},6396:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,a,l){return e/=l/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var l=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),r=e-i,u=20,s=0;t="undefined"===typeof t?500:t;var c=function e(){s+=u;var o=Math.easeInOutQuad(s,i,r,t);n(o),s<t?l(e):a&&"function"===typeof a&&a()};c()}},6724:function(e,t,a){"use strict";a("8d41");var l={bind:function(e,t){e.addEventListener("click",(function(a){var l=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),o=n.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var i=o.getBoundingClientRect(),r=o.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(i.width,i.height)+"px",o.appendChild(r)),n.type){case"center":r.style.top=i.height/2-r.offsetHeight/2+"px",r.style.left=i.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-i.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-i.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(n)),l.install=n;t["a"]=l},"70ff":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"e",(function(){return r})),a.d(t,"d",(function(){return u})),a.d(t,"b",(function(){return s})),a.d(t,"f",(function(){return c}));var l=a("b775");function n(e){return Object(l["a"])({url:"/admin/supplyGoods/waitingLists.html",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/admin_plus/supplyGoods/waitingLists",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/admin/supplyGoods/supplyChainCate.html",method:"post",data:e})}function r(e){return Object(l["a"])({url:"/admin/supplyGoods/syncGoods.html",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/admin/supplyGoods/supplyChainReload.html",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/admin/supplyGoods/getCategoryByParent.html",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/admin_plus/supplyGoods/waitingInfo",method:"post",data:e})}},8615:function(e,t,a){var l=a("5ca1"),n=a("504c")(!1);l(l.S,"Object",{values:function(e){return n(e)}})},"8d41":function(e,t,a){},"9af2":function(e,t,a){"use strict";a("be82")},b885:function(e,t,a){"use strict";var l=a("e780");a.d(t,"d",(function(){return l["a"]}));var n=a("ad41");a.d(t,"c",(function(){return n["a"]}));var o=a("0476");a.d(t,"g",(function(){return o["a"]}));var i=a("6eb0");a.d(t,"a",(function(){return i["a"]}));var r=a("c87f");a.d(t,"f",(function(){return r["a"]}));var u=a("333d");a.d(t,"e",(function(){return u["a"]}));var s=a("05be");a.d(t,"b",(function(){return s["a"]}));a("9040");var c=a("4381");a.d(t,"h",(function(){return c["a"]}));var d=a("6724");a.d(t,"i",(function(){return d["a"]}))},be82:function(e,t,a){},c16a:function(e,t,a){},c40e:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return u})),a.d(t,"g",(function(){return s})),a.d(t,"b",(function(){return c}));var l=a("b775");function n(e){return Object(l["a"])({url:"/goods/product/state/",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"i",(function(){return n})),a.d(t,"H",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"A",(function(){return r})),a.d(t,"x",(function(){return u})),a.d(t,"e",(function(){return s})),a.d(t,"w",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"O",(function(){return f})),a.d(t,"j",(function(){return b})),a.d(t,"k",(function(){return p})),a.d(t,"l",(function(){return v})),a.d(t,"T",(function(){return m})),a.d(t,"d",(function(){return h})),a.d(t,"Q",(function(){return g})),a.d(t,"p",(function(){return y})),a.d(t,"P",(function(){return _})),a.d(t,"m",(function(){return w})),a.d(t,"I",(function(){return O})),a.d(t,"L",(function(){return j})),a.d(t,"N",(function(){return S})),a.d(t,"M",(function(){return C})),a.d(t,"S",(function(){return k})),a.d(t,"s",(function(){return x})),a.d(t,"B",(function(){return D})),a.d(t,"z",(function(){return T})),a.d(t,"K",(function(){return F})),a.d(t,"C",(function(){return L})),a.d(t,"h",(function(){return I})),a.d(t,"g",(function(){return $})),a.d(t,"o",(function(){return V})),a.d(t,"G",(function(){return q})),a.d(t,"J",(function(){return z})),a.d(t,"v",(function(){return A})),a.d(t,"F",(function(){return P})),a.d(t,"r",(function(){return R})),a.d(t,"b",(function(){return U})),a.d(t,"q",(function(){return E})),a.d(t,"R",(function(){return N})),a.d(t,"u",(function(){return G})),a.d(t,"t",(function(){return Q})),a.d(t,"D",(function(){return M})),a.d(t,"E",(function(){return B})),a.d(t,"y",(function(){return K})),a.d(t,"n",(function(){return H}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],o=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],r=[{label:"是",code:1},{label:"否",code:0}],u=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],b=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],p=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],v=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],y=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],_=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],S=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],k=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],x=[{value:"0",label:"参团"},{value:"1",label:"团长"}],D=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],T=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],F=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],L=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],I=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],$=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],V=[{value:"1",label:"是"},{value:"2",label:"否"}],q=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],z=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],A=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],P=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],R=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],U=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],E=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],N=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],G=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],Q=[{label:"按订单编号",value:"order_no"}],M=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],B=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],K=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],H=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},daba:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return u})),a.d(t,"f",(function(){return s}));var l=a("b775");function n(e){return Object(l["a"])({url:"/admin/config/award.html",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/admin_plus/config/award.html",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/admin/memberwithdraw/config.html",method:"post",data:e})}function r(e){return Object(l["a"])({url:"/admin_plus/Memberwithdraw/config",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/admin_plus/Order/config.html",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/admin/Order/config.html",method:"post",data:e})}},e720:function(e,t,a){"use strict";a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return o}));var l=[{type:"input",label:"商品ID",model:"goods_id",placeholder:"请输入商品ID"},{type:"input",label:"商品名称",model:"search_text",placeholder:"请输入商品名称"},{type:"input",label:"SPU/SKU",model:"spu",placeholder:"请输入SPU/SKU"},{type:"input",label:"供应商名称",model:"supplier_name",placeholder:"请输入供应商名称"},{type:"casder",label:"商品分类",model:"category_id",placeholder:"请选择",options:{name:"addCouponClassify"},props:{label:"category_name",value:"category_id"}},{type:"select",label:"是否任务商品",model:"is_league",placeholder:"请输入是否任务商品",options:{name:"isOpt"}},{type:"select",label:"商品品牌",model:"goods_brand",placeholder:"请输入商品品牌",options:{name:"brandOpt",value:"brand_id",label:"brand_name"}},{type:"select",label:"商品状态",model:"goods_state",placeholder:"请输入商品状态",options:{name:"shelvesOpt"}},{type:"select",label:"商品标签",model:"goods_tag_id",placeholder:"请输入商品标签",options:{name:"tagOpt"}},{type:"select",label:"是否直播商品",model:"is_broadcast",placeholder:"请输入是否直播商品",options:{name:"isOpt"}}],n=[{type:"input",label:"SPU/SKU",model:"sku_no",placeholder:"请输入供应链SPU/SKU"},{type:"input",label:"商品名称",model:"keyword",placeholder:"商品名称/SKU"},{type:"select",label:"原品牌",model:"brand_id",placeholder:"全部",options:{name:"originalData",label:"name",value:"cate_id"}},{type:"time",label:"下单时间",model:"upTime"}],o=[{type:"input",label:"商品名称",model:"search_text",placeholder:"请输入商品名称"},{type:"input",label:"供应链SPU",model:"spu_no",placeholder:"请输入供应链SPU"},{type:"select",label:"异常类型",model:"abnormal_type",options:{name:"warningOpt"}},{type:"time",label:"异常时间",model:"date",timeType:"datetimerange"}]},fe67:function(e,t,a){e.exports=a.p+"static/img/login_bg.e491666c.png"}}]);