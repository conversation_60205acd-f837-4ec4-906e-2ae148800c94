(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-548ecd08"],{"137e":function(t,e,l){},2868:function(t,e,l){"use strict";l.d(e,"d",(function(){return a})),l.d(e,"i",(function(){return n})),l.d(e,"a",(function(){return i})),l.d(e,"b",(function(){return o})),l.d(e,"c",(function(){return u})),l.d(e,"g",(function(){return r})),l.d(e,"f",(function(){return d})),l.d(e,"e",(function(){return s})),l.d(e,"j",(function(){return c})),l.d(e,"k",(function(){return f})),l.d(e,"l",(function(){return p})),l.d(e,"n",(function(){return m})),l.d(e,"p",(function(){return b})),l.d(e,"o",(function(){return v})),l.d(e,"h",(function(){return _})),l.d(e,"m",(function(){return h}));var a=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],n=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],i=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],o=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],u=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],r=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],d=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],s=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],c=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],f=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],p=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],m=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],b=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],v=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],_=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],h=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"3b38":function(t,e,l){"use strict";l.d(e,"n",(function(){return n})),l.d(e,"m",(function(){return i})),l.d(e,"p",(function(){return o})),l.d(e,"l",(function(){return u})),l.d(e,"c",(function(){return r})),l.d(e,"g",(function(){return d})),l.d(e,"f",(function(){return s})),l.d(e,"e",(function(){return c})),l.d(e,"j",(function(){return f})),l.d(e,"k",(function(){return p})),l.d(e,"i",(function(){return m})),l.d(e,"h",(function(){return b})),l.d(e,"a",(function(){return v})),l.d(e,"o",(function(){return _})),l.d(e,"b",(function(){return h})),l.d(e,"q",(function(){return g})),l.d(e,"d",(function(){return y}));var a=l("b775");function n(t){return Object(a["a"])({url:"/admin/pintuan/data.html",method:"get",params:t})}function i(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/admin/pintuan/store.html",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/pintuan/change_status",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/admin/pintuan/editStock",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/pintuan/editSort",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:t})}function _(t){return Object(a["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:t})}function h(t){return Object(a["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/admin/pintuan/update.html",method:"post",data:t})}function y(t){return Object(a["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:t})}},"3f5e":function(t,e,l){"use strict";l.d(e,"b",(function(){return n})),l.d(e,"c",(function(){return i})),l.d(e,"a",(function(){return o}));var a=l("b775");function n(t){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:t,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(t){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:t,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(t){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:t})}},4381:function(t,e,l){"use strict";var a=l("a18c"),n={inserted:function(t,e,l){var n=e.value,i=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;i.indexOf(n)<0&&t.parentNode&&t.parentNode.removeChild(t)}},i=function(t){t.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(i)),n.install=i;e["a"]=n},6396:function(t,e,l){"use strict";l.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,l,a){return t/=a/2,t<1?l/2*t*t+e:(t--,-l/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,l){var o=i(),u=t-o,r=20,d=0;e="undefined"===typeof e?500:e;var s=function t(){d+=r;var i=Math.easeInOutQuad(d,o,u,e);n(i),d<e?a(t):l&&"function"===typeof l&&l()};s()}},6724:function(t,e,l){"use strict";l("8d41");var a={bind:function(t,e){t.addEventListener("click",(function(l){var a=Object.assign({},e.value),n=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),i=n.ele;if(i){i.style.position="relative",i.style.overflow="hidden";var o=i.getBoundingClientRect(),u=i.querySelector(".waves-ripple");switch(u?u.className="waves-ripple":(u=document.createElement("span"),u.className="waves-ripple",u.style.height=u.style.width=Math.max(o.width,o.height)+"px",i.appendChild(u)),n.type){case"center":u.style.top=o.height/2-u.offsetHeight/2+"px",u.style.left=o.width/2-u.offsetWidth/2+"px";break;default:u.style.top=(l.pageY-o.top-u.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",u.style.left=(l.pageX-o.left-u.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return u.style.backgroundColor=n.color,u.className="waves-ripple z-active",!1}}),!1)}},n=function(t){t.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;e["a"]=a},"89d7":function(t,e,l){"use strict";l.r(e);var a=function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"app-container"},[t.showSearch?l("div",{staticClass:"filter-container"},[l("formQuery",{staticClass:"mb-20",attrs:{baseConfig:t.baseConfig,config:t.formConfig,options:t.formopts},model:{value:t.form,callback:function(e){t.form=e},expression:"form"}}),t._v(" "),l("div",{staticClass:"flex-b-c buttons"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(e){return t.handleQuery()}}},[t._v("搜索")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:t.handleReset}},[t._v("重置")])],1)],1):t._e(),t._v(" "),l("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-list"},[l("div",{staticClass:"btns"},[l("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(e){return t.goSkip("/market/teamwork/add")}}},[t._v("添加活动")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small"},on:{click:function(e){return t.goSkip("/market/teamwork/order")}}},[t._v("查看拼团订单")])],1),t._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:t.showSearch,options:t.options,columns:t.columns,data:t.list},on:{toggleSearch:t.toggleSearch,"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},onSearch:t.handleQuery},scopedSlots:t._u([{key:"goods_name",fn:function(e){var a=e.row;return[l("div",{staticClass:"goods-details"},[l("img",{attrs:{src:a.sku_image,alt:""}}),t._v(" "),l("div",[t._v("\n                            "+t._s(a.goods_name)+"\n                        ")])])]}},{key:"promotion_type",fn:function(e){var l=e.row;return[t._v("\n                "+t._s(t._f("getStatus")(l.promotion_type,t.formopts.teamwordTypeOpt))+"\n            ")]}},{key:"status",fn:function(e){var l=e.row;return[t._v("\n                "+t._s(t._f("getStatus")(l.status,t.formopts.teamwordOpt))+"\n            ")]}},{key:"action",fn:function(e){var a=e.row,n=a.pintuan_id,i=a.pintuan_name,o=a.status,u=a.promotion_type,r=a.grouped_num;a.valid_date;return[l("div",{staticClass:"actions"},[l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/market/teamwork/add",{pintuan_id:n,is_preview:1})}}},[t._v("查看活动")]),t._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/market/teamwork/goods",{pintuan_id:n})}}},[t._v("查看商品")]),t._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/market/teamwork/openGroup",{pintuan_id:n,pintuan_name:i,status:o,promotion_type:u})}}},[t._v("开团列表("+t._s(r)+")")]),t._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/market/teamwork/data",{pintuan_id:n,promotion_type:u,status:o})}}},[t._v("活动数据")])],1)]}}])})],1)])},n=[],i=l("5530"),o=l("b885"),u=l("2868"),r=l("c71b"),d=l("3b38"),s={components:{FormQuery:o["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{teamwordTypeOpt:r["N"],teamwordOpt:r["L"]},form:{},formConfig:[{type:"input",label:"活动名称",model:"topic_name",placeholder:"请输入活动名称"},{type:"select",label:"状态",model:"status",placeholder:"请选择",options:{name:"teamwordOpt"}}],loading:!1,columns:u["n"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){this.handleQuery()},methods:{goSkip:function(t,e){this.$router.push({path:t,query:e})},getTableList:function(t){var e=this;Object(d["n"])(Object(i["a"])(Object(i["a"])({},t),this.form)).then((function(t){var l=t.data,a=l.count,n=l.list;e.options.total=a,e.list=n,e.loading=!1}))},handleQuery:function(t){var e;console.log(t),this.loading=!0,t&&(e={page:t.page,limit:t.page_size});var l=e||{page:1,limit:this.options.page_size};this.getTableList(l)},handleReset:function(){},toggleSearch:function(){this.showSearch=!this.showSearch}}},c=s,f=(l("b178"),l("2877")),p=Object(f["a"])(c,a,n,!1,null,"74349f8a",null);e["default"]=p.exports},"8d41":function(t,e,l){},b178:function(t,e,l){"use strict";l("137e")},b885:function(t,e,l){"use strict";var a=l("e780");l.d(e,"d",(function(){return a["a"]}));var n=l("ad41");l.d(e,"c",(function(){return n["a"]}));var i=l("0476");l.d(e,"g",(function(){return i["a"]}));var o=l("6eb0");l.d(e,"a",(function(){return o["a"]}));var u=l("c87f");l.d(e,"f",(function(){return u["a"]}));var r=l("333d");l.d(e,"e",(function(){return r["a"]}));var d=l("05be");l.d(e,"b",(function(){return d["a"]}));l("9040");var s=l("4381");l.d(e,"h",(function(){return s["a"]}));var c=l("6724");l.d(e,"i",(function(){return c["a"]}))},c40e:function(t,e,l){"use strict";l.d(e,"e",(function(){return n})),l.d(e,"d",(function(){return i})),l.d(e,"f",(function(){return o})),l.d(e,"c",(function(){return u})),l.d(e,"a",(function(){return r})),l.d(e,"g",(function(){return d})),l.d(e,"b",(function(){return s}));var a=l("b775");function n(t){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/goods/product/page",method:"post",data:t})}},c71b:function(t,e,l){"use strict";l.d(e,"a",(function(){return a})),l.d(e,"i",(function(){return n})),l.d(e,"H",(function(){return i})),l.d(e,"f",(function(){return o})),l.d(e,"A",(function(){return u})),l.d(e,"x",(function(){return r})),l.d(e,"e",(function(){return d})),l.d(e,"w",(function(){return s})),l.d(e,"c",(function(){return c})),l.d(e,"O",(function(){return f})),l.d(e,"j",(function(){return p})),l.d(e,"k",(function(){return m})),l.d(e,"l",(function(){return b})),l.d(e,"T",(function(){return v})),l.d(e,"d",(function(){return _})),l.d(e,"Q",(function(){return h})),l.d(e,"p",(function(){return g})),l.d(e,"P",(function(){return y})),l.d(e,"m",(function(){return w})),l.d(e,"I",(function(){return k})),l.d(e,"L",(function(){return O})),l.d(e,"N",(function(){return j})),l.d(e,"M",(function(){return S})),l.d(e,"S",(function(){return x})),l.d(e,"s",(function(){return C})),l.d(e,"B",(function(){return I})),l.d(e,"z",(function(){return T})),l.d(e,"K",(function(){return D})),l.d(e,"C",(function(){return N})),l.d(e,"h",(function(){return L})),l.d(e,"g",(function(){return q})),l.d(e,"o",(function(){return z})),l.d(e,"G",(function(){return A})),l.d(e,"J",(function(){return R})),l.d(e,"v",(function(){return E})),l.d(e,"F",(function(){return Q})),l.d(e,"r",(function(){return G})),l.d(e,"b",(function(){return M})),l.d(e,"q",(function(){return P})),l.d(e,"R",(function(){return V})),l.d(e,"u",(function(){return F})),l.d(e,"t",(function(){return H})),l.d(e,"D",(function(){return W})),l.d(e,"E",(function(){return X})),l.d(e,"y",(function(){return B})),l.d(e,"n",(function(){return J}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],i=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],o=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],u=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],d=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],s=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],m=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],b=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],_=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],h=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],O=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],j=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],S=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],x=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],C=[{value:"0",label:"参团"},{value:"1",label:"团长"}],I=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],T=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],D=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],N=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],L=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],q=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],z=[{value:"1",label:"是"},{value:"2",label:"否"}],A=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],R=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],E=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],Q=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],G=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],M=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],P=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],V=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],F=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],H=[{label:"按订单编号",value:"order_no"}],W=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],X=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],B=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],J=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},fe67:function(t,e,l){t.exports=l.p+"static/img/login_bg.e491666c.png"}}]);