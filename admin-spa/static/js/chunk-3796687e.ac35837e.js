(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3796687e"],{"6bd7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[t._v("订单时间设置")]),t._v(" "),a("el-form",{attrs:{"label-width":"250px"}},[a("el-form-item",{attrs:{label:"普通订单未付款自动关闭时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_close,callback:function(e){t.$set(t.form,"auto_close",e)},expression:"form.auto_close"}}),t._v("\n                    分钟\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("订单开始后多长时间未付款自动关闭(注：不填写或0订单将不会自动关闭)")])]),t._v(" "),a("el-form-item",{attrs:{label:"所有未付款订单锁定时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.order_lock_time,callback:function(e){t.$set(t.form,"order_lock_time",e)},expression:"form.order_lock_time"}}),t._v("\n                    分钟\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("所有未付款订单（普通、秒杀、拼团等）在设置时间内不允许第二次支付或者取消等操作")])]),t._v(" "),a("el-form-item",{attrs:{label:"发货后自动收货时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_confirm,callback:function(e){t.$set(t.form,"auto_confirm",e)},expression:"form.auto_confirm"}}),t._v("\n                    天\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("订单多长时间后自动收货(注：若为0，则订单不会自动收货)")])]),t._v(" "),a("el-form-item",{attrs:{label:"收货后自动完成时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_complete,callback:function(e){t.$set(t.form,"auto_complete",e)},expression:"form.auto_complete"}}),t._v("\n                    天\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("收货后，多长时间订单自动完成")])]),t._v(" "),a("el-form-item",{attrs:{label:"买家发起退货退款申请："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_agree_return_refund_time,callback:function(e){t.$set(t.form,"auto_agree_return_refund_time",e)},expression:"form.auto_agree_return_refund_time"}}),t._v("\n                    天后商家未处理，系统将自动同意退货退款\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("最长可设置90天，如设置0天时则不会自动同意退货退款")])]),t._v(" "),a("el-form-item",{attrs:{label:"买家发起换货申请："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_agree_exchange_goods_time,callback:function(e){t.$set(t.form,"auto_agree_exchange_goods_time",e)},expression:"form.auto_agree_exchange_goods_time"}}),t._v("\n                    天后商家未处理，系统将自动同意换货\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("最长可设置90天，如设置0天时则不会自动同意换货")])]),t._v(" "),a("el-form-item",{attrs:{label:"商家同意退货/换货或商家未收到货："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_close_after_agree_time,callback:function(e){t.$set(t.form,"auto_close_after_agree_time",e)},expression:"form.auto_close_after_agree_time"}}),t._v("\n                    天后买家未处理（填写退货物流），系统将自动关闭售后\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("最长可设置90天，如设置0天时则不会自动同意自动关闭售后")])]),t._v(" "),a("el-form-item",{attrs:{label:"买家已退货："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.auto_confirm_after_return_time,callback:function(e){t.$set(t.form,"auto_confirm_after_return_time",e)},expression:"form.auto_confirm_after_return_time"}}),t._v("\n                    天后商家未处理，系统将自动确认收货\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("最长可设置90天，如设置0天时则不会自动同意自动确认收货")])])],1)],1),t._v(" "),a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[t._v("订单设置")]),t._v(" "),a("el-form",{attrs:{"label-width":"250px"}},[a("el-form-item",{attrs:{label:"拼团订单未付款自动关闭时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.pintuan_auto_close,callback:function(e){t.$set(t.form,"pintuan_auto_close",e)},expression:"form.pintuan_auto_close"}}),t._v("\n                    分钟\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("拼团订单开始后多长时间未付款自动关闭(注：不填写或0订单将不会自动关闭)")])]),t._v(" "),a("el-form-item",{attrs:{label:"拼团失败时间："}},[a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{type:"number"},model:{value:t.form.pintuan_failed_auto_close_time,callback:function(e){t.$set(t.form,"pintuan_failed_auto_close_time",e)},expression:"form.pintuan_failed_auto_close_time"}}),t._v("\n                    分钟\n                ")],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("开团后，多长时间没有成团，自动拼团失败，并退款")])]),t._v(" "),a("el-form-item",{attrs:{label:"拼团团队长参与抽奖："}},[a("div",[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:t.form.in_draw,callback:function(e){t.$set(t.form,"in_draw",e)},expression:"form.in_draw"}})],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("开启后，店主开团参与抽奖；关闭后，店主开团不中奖")])])],1)],1),t._v(" "),a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[t._v("任务完成设置")]),t._v(" "),a("el-form",{attrs:{"label-width":"250px"}},[a("el-form-item",{attrs:{label:"秒杀专区："}},[a("el-radio-group",{model:{value:t.form.task_complete_seckill,callback:function(e){t.$set(t.form,"task_complete_seckill",e)},expression:"form.task_complete_seckill"}},[a("el-radio",{attrs:{label:"0"}},[t._v("参与任务")]),t._v(" "),a("el-radio",{attrs:{label:"1"}},[t._v("不参与任务")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"新人专区："}},[a("div",[a("el-radio-group",{model:{value:t.form.task_complete_new,callback:function(e){t.$set(t.form,"task_complete_new",e)},expression:"form.task_complete_new"}},[a("el-radio",{attrs:{label:"0"}},[t._v("参与任务")]),t._v(" "),a("el-radio",{attrs:{label:"1"}},[t._v("不参与任务")])],1)],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("选择不参与任务时，用户购买或浏览该专区的商品时不计算完成任务")])]),t._v(" "),a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary",loading:t.loading,size:"small"},on:{click:t.onSave}},[t._v("保存")])],1)],1)],1)])},o=[],r=a("daba"),l={data:function(){return{form:{},loading:!1}},mounted:function(){this.init()},methods:{init:function(){var t=this;Object(r["b"])().then((function(e){var a=e.data;t.form=a.order_event_time_config}))},onSave:function(){var t=this;this.loading=!0,Object(r["f"])(this.form).then((function(e){t.loading=!1,t.$message.success("保存交易设置成功")}))}}},n=l,s=(a("9edb"),a("2877")),_=Object(s["a"])(n,i,o,!1,null,"d7999eca",null);e["default"]=_.exports},"95c6":function(t,e,a){},"9edb":function(t,e,a){"use strict";a("95c6")},daba:function(t,e,a){"use strict";a.d(e,"c",(function(){return o})),a.d(e,"d",(function(){return r})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return s})),a.d(e,"f",(function(){return _}));var i=a("b775");function o(t){return Object(i["a"])({url:"/admin/config/award.html",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/admin_plus/config/award.html",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/admin/memberwithdraw/config.html",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/admin_plus/Memberwithdraw/config",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin_plus/Order/config.html",method:"post",data:t})}function _(t){return Object(i["a"])({url:"/admin/Order/config.html",method:"post",data:t})}}}]);