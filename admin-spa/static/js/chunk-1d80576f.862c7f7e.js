(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d80576f"],{"15fd":function(t,e,l){"use strict";function a(t,e){if(null==t)return{};var l,a,n={},o=Object.keys(t);for(a=0;a<o.length;a++)l=o[a],e.indexOf(l)>=0||(n[l]=t[l]);return n}function n(t,e){if(null==t)return{};var l,n,o=a(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)l=i[n],e.indexOf(l)>=0||Object.prototype.propertyIsEnumerable.call(t,l)&&(o[l]=t[l])}return o}l.d(e,"a",(function(){return n}))},2868:function(t,e,l){"use strict";l.d(e,"d",(function(){return a})),l.d(e,"i",(function(){return n})),l.d(e,"a",(function(){return o})),l.d(e,"b",(function(){return i})),l.d(e,"c",(function(){return u})),l.d(e,"g",(function(){return r})),l.d(e,"f",(function(){return s})),l.d(e,"e",(function(){return d})),l.d(e,"j",(function(){return c})),l.d(e,"k",(function(){return f})),l.d(e,"l",(function(){return p})),l.d(e,"n",(function(){return m})),l.d(e,"p",(function(){return b})),l.d(e,"o",(function(){return v})),l.d(e,"h",(function(){return _})),l.d(e,"m",(function(){return h}));var a=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],n=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],o=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],i=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],u=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],r=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],s=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],d=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],c=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],f=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],p=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],m=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],b=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],v=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],_=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],h=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"3b38":function(t,e,l){"use strict";l.d(e,"n",(function(){return n})),l.d(e,"m",(function(){return o})),l.d(e,"p",(function(){return i})),l.d(e,"l",(function(){return u})),l.d(e,"c",(function(){return r})),l.d(e,"g",(function(){return s})),l.d(e,"f",(function(){return d})),l.d(e,"e",(function(){return c})),l.d(e,"j",(function(){return f})),l.d(e,"k",(function(){return p})),l.d(e,"i",(function(){return m})),l.d(e,"h",(function(){return b})),l.d(e,"a",(function(){return v})),l.d(e,"o",(function(){return _})),l.d(e,"b",(function(){return h})),l.d(e,"q",(function(){return g})),l.d(e,"d",(function(){return y}));var a=l("b775");function n(t){return Object(a["a"])({url:"/admin/pintuan/data.html",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:t})}function i(t){return Object(a["a"])({url:"/admin/pintuan/store.html",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/pintuan/change_status",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/admin/pintuan/editStock",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/pintuan/editSort",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:t})}function _(t){return Object(a["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:t})}function h(t){return Object(a["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/admin/pintuan/update.html",method:"post",data:t})}function y(t){return Object(a["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:t})}},"5c80":function(t,e,l){},6229:function(t,e,l){"use strict";l.d(e,"n",(function(){return r})),l.d(e,"o",(function(){return s})),l.d(e,"h",(function(){return d})),l.d(e,"j",(function(){return c})),l.d(e,"m",(function(){return f})),l.d(e,"e",(function(){return p})),l.d(e,"i",(function(){return m})),l.d(e,"u",(function(){return b})),l.d(e,"t",(function(){return v})),l.d(e,"r",(function(){return _})),l.d(e,"s",(function(){return h})),l.d(e,"l",(function(){return g})),l.d(e,"g",(function(){return y})),l.d(e,"d",(function(){return O})),l.d(e,"b",(function(){return j})),l.d(e,"c",(function(){return w})),l.d(e,"a",(function(){return k})),l.d(e,"q",(function(){return C})),l.d(e,"p",(function(){return S})),l.d(e,"v",(function(){return T}));var a=l("b775"),n=l("6dab");l.d(e,"w",(function(){return n["i"]}));var o=l("d74f");l.d(e,"k",(function(){return o["i"]}));var i=l("3b38");l.d(e,"f",(function(){return i["a"]}));var u="/goodscoupon/admin";function r(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/lists.html"),method:"get",params:t})}function s(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/receive.html"),method:"post",data:t})}function d(t,e){return Object(a["a"])({url:"".concat(u,"/goodscoupon/deleteMemberCoupon.html?coupon_id=").concat(e),method:"post",data:t})}function c(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/export.html"),method:"post",data:t,responseType:"blob"})}function f(t){return Object(a["a"])({url:"".concat(u,"/goodsCouponRule/list.html"),method:"get",params:t})}function p(t){return Object(a["a"])({url:"".concat(u,"/goodsCouponRule/add.html"),method:"post",data:t})}function m(t){return Object(a["a"])({url:"".concat(u,"/goodsCouponRule/detailList.html"),method:"get",params:t})}function b(t){return Object(a["a"])({url:"".concat(u,"/goodsCouponRule/stopped.html"),method:"post",data:t})}function v(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/shutDown.html"),method:"post",data:t})}function _(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/sendPageData.html"),method:"get",params:t})}function h(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/sendToSelectedMember.html"),method:"post",data:t})}function g(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/goodsData.html"),method:"get",params:t})}function y(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/deleteGoods.html"),method:"post",data:t})}function O(t){return Object(a["a"])({url:"".concat(u,"/goodscoupon/addGoods.html"),method:"post",data:t})}function j(t){return Object(a["a"])({url:"/admin_plus/AddonGoodscoupon/detail",method:"post",data:t})}function w(t){return Object(a["a"])({url:"/admin_plus/AddonGoodscoupon/lists",method:"post",data:t})}function k(t){return Object(a["a"])({url:"/admin_plus/AddonGoodsCouponRule/detail",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/admin_plus/AddonGoodscoupon/sendPage",method:"post",data:t})}function S(t){return Object(a["a"])({url:"/goodscoupon/admin/goodsCouponRule/selectGoodsCoupon.html",method:"post",data:t})}function T(t){return Object(a["a"])({url:"/goodscoupon/admin/goodsCouponRule/tokenGetTempList.html",method:"post",data:t})}},6308:function(t,e,l){"use strict";l.r(e);var a=function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"app-container"},[t.showSearch?l("div",{staticClass:"filter-container"},[l("el-form",{attrs:{inline:"","label-width":"120px"}},[l("el-form-item",{staticClass:"mobile",attrs:{label:"手机号："}},[l("el-input",{attrs:{size:"small",readonly:"",placeholder:"请选择"},model:{value:t.form.mobile,callback:function(e){t.$set(t.form,"mobile",e)},expression:"form.mobile"}}),t._v(" "),l("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.openDialog}},[t._v("多用户搜索")])],1),t._v(" "),l("el-form-item",{staticClass:"mobile",attrs:{label:"用户类型："}},[l("el-select",{attrs:{placeholder:"全部"},model:{value:t.form.userType,callback:function(e){t.$set(t.form,"userType",e)},expression:"form.userType"}},t._l(t.userTypeOpts,(function(t,e){return l("el-option",{key:e,attrs:{value:t.value,label:t.label}})})),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"是否已领："}},[l("el-select",{attrs:{placeholder:"全部"},model:{value:t.form.userType,callback:function(e){t.$set(t.form,"userType",e)},expression:"form.userType"}},t._l(t.isReceivedOpts,(function(t,e){return l("el-option",{key:e,attrs:{value:t.value,label:t.label}})})),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"注册时间："}},[l("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.form.regTime,callback:function(e){t.$set(t.form,"regTime",e)},expression:"form.regTime"}})],1)],1),t._v(" "),l("div",{staticClass:"users"},[l("span",[t._v("用户在近")]),t._v(" "),l("el-input",{staticStyle:{width:"10%"},attrs:{size:"small",placeholder:"请输入天数"},model:{value:t.form.toDay,callback:function(e){t.$set(t.form,"toDay",e)},expression:"form.toDay"}}),t._v(" "),l("span",[t._v("天内，")]),t._v(" "),l("el-select",{staticStyle:{width:"20%"},attrs:{size:"small",placeholder:"全部"},model:{value:t.form.userBehavior,callback:function(e){t.$set(t.form,"userBehavior",e)},expression:"form.userBehavior"}},t._l(t.userBehaviorOpts,(function(t,e){return l("el-option",{key:e,attrs:{value:t.value,label:t.label}})})),1),t._v(" "),l("el-input",{staticStyle:{width:"10%"},attrs:{size:"small",placeholder:"请输入次数"},model:{value:t.form.number,callback:function(e){t.$set(t.form,"number",e)},expression:"form.number"}}),t._v(" "),l("span",[t._v("次")])],1),t._v(" "),l("div",{staticClass:"flex-b-c buttons"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(e){return t.handleQuery()}}},[t._v("搜索")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:t.handleReset}},[t._v("重置")])],1)],1):t._e(),t._v(" "),t.info.count?l("div",{staticClass:"filter-container"},[l("div",{staticClass:"issuing"},[l("el-button",{attrs:{type:"primary"},on:{click:t.faceSelect}},[t._v("向选中用户发券")]),t._v(" "),l("p",[t._v("当前可发券数量 "+t._s(t.goodsCouponTypeId)+", 已选中待发券用户 "+t._s(t.selectNum)+" 人")])],1),t._v(" "),l("div",{staticClass:"issuing"},[l("el-button",{attrs:{type:"primary"},on:{click:t.searchSelect}},[t._v("向筛选用户发券")]),t._v(" "),l("p",[t._v("当前可发券数量 "+t._s(t.goodsCouponTypeId)+", 筛选待发券用户 "+t._s(this.options.total)+" 人")])],1)]):t._e(),t._v(" "),l("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-list"},[l("div",{staticClass:"btns"},[l("el-button",{attrs:{plain:"",size:"small"},on:{click:t.allSelect}},[t._v("全部选中")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small"},on:{click:t.allCancel}},[t._v("全部取消("+t._s(t.selectNum)+")")])],1),t._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:t.showSearch,options:t.options,columns:t.columns,data:t.list},on:{toggleSearch:t.toggleSearch,"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},onSearch:t.getTableList},scopedSlots:t._u([{key:"action",fn:function(e){var a=e.row;return[l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.selectOrCancel(a)}}},[t._v(t._s(a.isActive?"取消选择":"选择"))])]}}])})],1),t._v(" "),[l("el-dialog",{attrs:{title:"多用户搜索",visible:t.dialogVisible,top:"100px",width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[l("div",{staticClass:"user_search"},[l("p",[t._v("可输入多个手机号搜索，每个手机号以换行分隔。如手机号非平台用户将无法搜索到；如果重复手机号则视为一个用户 ")]),t._v(" "),l("el-input",{attrs:{type:"textarea",autosize:{minRows:25}},model:{value:t.dialogMobile,callback:function(e){t.dialogMobile=e},expression:"dialogMobile"}})],1),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.onSave()}}},[t._v("保 存")]),t._v(" "),l("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关 闭")])],1)])]],2)},n=[],o=l("15fd"),i=l("5530"),u=(l("ac6a"),l("28a5"),l("c71b")),r=l("2868"),s=l("6229"),d=["regTime"],c={data:function(){return{form:{},userTypeOpts:u["Q"],isReceivedOpts:u["p"],userBehaviorOpts:u["P"],dialogVisible:!1,dialogMobile:"",options:{page:1,page_size:10,total:0},info:{},loading:!1,list:[],showSearch:!0,columns:r["g"],selects:[],goodsCouponTypeId:0}},computed:{selectNum:function(){var t=this,e=0;return this.selects=[],this.list.filter((function(l){l.isActive&&(t.selects.push(l),e++)})),e}},mounted:function(){},methods:{onInit:function(){var t=this,e=this.$route.query.goodscoupon_type_id;Object(s["q"])({goodscoupon_type_id:e}).then((function(e){var l=e.data;t.goodsCouponTypeId=l.goodsCouponTypeId}))},openDialog:function(){this.dialogVisible=!0,this.form.mobile||(this.dialogMobile="")},onSave:function(){this.form.mobile=this.dialogMobile.split("\n").join(","),this.dialogVisible=!1},faceSelect:function(){var t=this;if(this.selectNum>0){var e=[];return this.selects.forEach((function(t){console.log(t),e.push(t.member_id)})),void Object(s["s"])(this.$params({member_id:e,goodscoupon_type_id:this.$route.query.goodscoupon_type_id})).then((function(e){var l=e.data;t.$message.success("已发送：".concat(l.send_count,"个用户")),t.selects=[],t.list.forEach((function(t){t.isActive=!1}))}))}this.$message.warning("请选择用户")},searchSelect:function(){var t=this;this.list.length>0?Object(s["s"])(Object(i["a"])(Object(i["a"])({},this.form),{},{goodscoupon_type_id:this.$route.query.goodscoupon_type_id})).then((function(e){var l=e.data;t.$message.success("已发送：".concat(l.send_count,"个用户"))})):this.$message.warning("请先进行搜索!")},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(t){var e=this,l=this.form,a=l.regTime,n=Object(o["a"])(l,d);a&&(n.start_time=this.$format(a[0],"{y}-{m}-{d}"),n.end_time=this.$format(a[1],"{y}-{m}-{d}")),Object(s["r"])(Object(i["a"])(Object(i["a"])({},t),n)).then((function(t){var l=t.data,a=l.goodsCouponTypeInfo,n=l.count,o=l.list;console.log(a),e.info=a,e.options.total=n,e.list=o,e.loading=!1,e.onInit()}))},handleQuery:function(t){this.loading=!0;var e=t||{page:1,page_size:this.options.page_size},l=this.$route.query.goodscoupon_type_id;l&&(e.goodscoupon_type_id=l),this.getTableList(e)},handleReset:function(){this.form={}},allSelect:function(){var t=this;this.list.forEach((function(e,l){e.isActive=!0,t.$set(t.list,l,e)}))},allCancel:function(){var t=this;this.list.forEach((function(e,l){e.isActive=!1,t.$set(t.list,l,e)}))},selectOrCancel:function(t){var e=this;this.list.forEach((function(l,a){t.member_id==l.member_id&&(l.isActive=!l.isActive,e.$set(e.list,a,l))}))}}},f=c,p=(l("e838"),l("2877")),m=Object(p["a"])(f,a,n,!1,null,"1d567c83",null);e["default"]=m.exports},"6dab":function(t,e,l){"use strict";l.d(e,"g",(function(){return n})),l.d(e,"i",(function(){return o})),l.d(e,"a",(function(){return i})),l.d(e,"h",(function(){return u})),l.d(e,"f",(function(){return r})),l.d(e,"d",(function(){return s})),l.d(e,"c",(function(){return d})),l.d(e,"e",(function(){return c})),l.d(e,"b",(function(){return f}));var a=l("b775");function n(t){return Object(a["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/topic/admin/topic/add.html",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:t})}},c71b:function(t,e,l){"use strict";l.d(e,"a",(function(){return a})),l.d(e,"i",(function(){return n})),l.d(e,"H",(function(){return o})),l.d(e,"f",(function(){return i})),l.d(e,"A",(function(){return u})),l.d(e,"x",(function(){return r})),l.d(e,"e",(function(){return s})),l.d(e,"w",(function(){return d})),l.d(e,"c",(function(){return c})),l.d(e,"O",(function(){return f})),l.d(e,"j",(function(){return p})),l.d(e,"k",(function(){return m})),l.d(e,"l",(function(){return b})),l.d(e,"T",(function(){return v})),l.d(e,"d",(function(){return _})),l.d(e,"Q",(function(){return h})),l.d(e,"p",(function(){return g})),l.d(e,"P",(function(){return y})),l.d(e,"m",(function(){return O})),l.d(e,"I",(function(){return j})),l.d(e,"L",(function(){return w})),l.d(e,"N",(function(){return k})),l.d(e,"M",(function(){return C})),l.d(e,"S",(function(){return S})),l.d(e,"s",(function(){return T})),l.d(e,"B",(function(){return x})),l.d(e,"z",(function(){return I})),l.d(e,"K",(function(){return D})),l.d(e,"C",(function(){return $})),l.d(e,"h",(function(){return G})),l.d(e,"g",(function(){return z})),l.d(e,"o",(function(){return A})),l.d(e,"G",(function(){return R})),l.d(e,"J",(function(){return P})),l.d(e,"v",(function(){return E})),l.d(e,"F",(function(){return L})),l.d(e,"r",(function(){return M})),l.d(e,"b",(function(){return N})),l.d(e,"q",(function(){return q})),l.d(e,"R",(function(){return V})),l.d(e,"u",(function(){return B})),l.d(e,"t",(function(){return Q})),l.d(e,"D",(function(){return J})),l.d(e,"E",(function(){return F})),l.d(e,"y",(function(){return H})),l.d(e,"n",(function(){return K}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],o=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],u=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],m=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],b=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],_=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],h=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],O=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],j=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],w=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],k=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],S=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],T=[{value:"0",label:"参团"},{value:"1",label:"团长"}],x=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],I=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],D=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],$=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],G=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],z=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],A=[{value:"1",label:"是"},{value:"2",label:"否"}],R=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],P=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],E=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],L=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],M=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],q=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],V=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],B=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],Q=[{label:"按订单编号",value:"order_no"}],J=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],F=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],H=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],K=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e838:function(t,e,l){"use strict";l("5c80")}}]);