(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1aea995f"],{"367d":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[e.showSearch?l("div",{staticClass:"filter-container"},[l("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),l("div",{staticClass:"flex-b-c buttons"},[l("el-button",{attrs:{type:"primary",loading:e.loading,icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v("搜索")]),e._v(" "),l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),l("div",{staticClass:"table-list"},[l("div",{staticClass:"btns"},[l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-plus"},on:{click:function(t){return e.goSkip("/member/store/list/add")}}},[e._v("添加店铺")])],1),e._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{toggleSearch:e.toggleSearch,"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},onSearch:e.handleQuery},scopedSlots:e._u([{key:"create_time",fn:function(t){var l=t.row;return[e._v("\n                "+e._s(e._f("parseTime")(l.create_time))+"\n            ")]}},{key:"vip_open_time",fn:function(t){var l=t.row;return[e._v("\n                "+e._s(e._f("parseTime")(l.vip_open_time))+"\n            ")]}},{key:"vip_expired_time",fn:function(t){var l=t.row;return[e._v("\n                "+e._s(e._f("parseTime")(l.vip_expired_time))+"\n            ")]}},{key:"shop_status",fn:function(t){var l=t.row;return[e._v("\n                "+e._s(e.getShopStatus(l.shop_status))+"\n            ")]}},{key:"action",fn:function(t){var a=t.row;return[l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleFreeze(a.site_id,a.shop_status)}}},[e._v(e._s(e.getState(a.shop_status)))]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/member/store/list/details",{site_id:a.site_id})}}},[e._v("店主详情")]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/member/store/list/edit",{site_id:a.site_id})}}},[e._v("编辑")]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleBind(a.tag_id,a.bing_status)}}},[e._v(e._s(e.getBindStatus(a.bing_status)))])]}}])})],1)])},n=[],i=l("5530"),o=l("b885"),u=l("7991"),r=l("6b2c"),s=l("c71b"),d=l("e87b"),c={components:{FormQuery:o["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{StoreOwnerLevelOpts:s["a"],bindStatusOpts:s["i"],shopStatusOpts:s["H"]},form:{bing_status:"-1"},formConfig:r["d"],loading:!1,columns:u["d"],list:[],options:{pageNo:1,pageSize:10,total:0}}},mounted:function(){this.handleQuery()},methods:{getBindStatus:function(e){return 2==e?"弱绑定":1==e?"强绑定":void 0},getState:function(e){return 2==e?"解冻":1==e?"冻结":void 0},handleFreeze:function(e,t){var l=this,a="确定要把该店铺进行冻结吗？冻结后店主不可操作店铺（只能查看）。",n=2;2==t&&(a="确定要把该店铺进行解冻吗？解冻后店主恢复操作店铺。",n=1),this.$confirm(a,"信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(d["d"])({site_id:e,shop_status:n}).then((function(e){console.log(e),l.$message({type:"success",message:"冻结店主成功!"}),l.handleQuery()}))})).catch((function(e){}))},handleBind:function(){var e=this;this.$confirm("确定要强绑定吗？","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$message({type:"success",message:"删除成功!"})})).catch((function(e){}))},getShopStatus:function(e){return 0==e?"已过期":1==e?"正常":2==e?"冻结":void 0},goSkip:function(e,t){this.$router.push({path:e,query:t})},updateOrder:function(){this.$message({message:"更新标签排序成功",type:"success"})},setEnterorise:function(e){return 1==e?"是":"否"},handleQuery:function(e){this.loading=!0;var t=e||{page:1,page_size:this.options.page_size};this.getTableList(t)},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(e){var t=this;this.list=[],Object(d["g"])(Object(i["a"])(Object(i["a"])({},e),this.form)).then((function(e){var l=e.data,a=l.count,n=l.list;t.options.total=a,t.list=n,t.loading=!1}))}}},p=c,b=l("2877"),f=Object(b["a"])(p,a,n,!1,null,"0081dcb8",null);t["default"]=f.exports},"3f5e":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return i})),l.d(t,"a",(function(){return o}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,l){"use strict";var a=l("a18c"),n={inserted:function(e,t,l){var n=t.value,i=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;i.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},i=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(i)),n.install=i;t["a"]=n},6396:function(e,t,l){"use strict";l.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,l){var o=i(),u=e-o,r=20,s=0;t="undefined"===typeof t?500:t;var d=function e(){s+=r;var i=Math.easeInOutQuad(s,o,u,t);n(i),s<t?a(e):l&&"function"===typeof l&&l()};d()}},6724:function(e,t,l){"use strict";l("8d41");var a={bind:function(e,t){e.addEventListener("click",(function(l){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),i=n.ele;if(i){i.style.position="relative",i.style.overflow="hidden";var o=i.getBoundingClientRect(),u=i.querySelector(".waves-ripple");switch(u?u.className="waves-ripple":(u=document.createElement("span"),u.className="waves-ripple",u.style.height=u.style.width=Math.max(o.width,o.height)+"px",i.appendChild(u)),n.type){case"center":u.style.top=o.height/2-u.offsetHeight/2+"px",u.style.left=o.width/2-u.offsetWidth/2+"px";break;default:u.style.top=(l.pageY-o.top-u.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",u.style.left=(l.pageX-o.left-u.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return u.style.backgroundColor=n.color,u.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;t["a"]=a},"6b2c":function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"d",(function(){return n})),l.d(t,"c",(function(){return i})),l.d(t,"b",(function(){return o}));var a=[{type:"input",label:"会员Id",model:"member_id",placeholder:"请输入会员ID"},{type:"input",label:"手机号",model:"mobile",placeholder:"请输入手机号"},{type:"input",label:"当前锁定店铺",model:"shop_member_name",placeholder:"请输入当前锁定店铺"},{type:"select",label:"用户标签",model:"tag_ids",options:{name:"userTagsOptions"}},{type:"input",label:"注册推荐人",model:"parent_mobile",placeholder:"请输入注册推荐人"},{type:"select",label:"组别",model:"group_id",options:{name:"userGroupOptions"}},{type:"select",label:"状态",model:"state",options:{name:"memberStatusOpt"}},{type:"select",label:"企微匹配状态",model:"is_shopping_status",options:{name:"mateStatusOpt"}},{type:"select",label:"账号状态",model:"status",options:{name:"accountStatusOpt"}},{type:"time",label:"注册时间",model:"created_time"}],n=[{type:"input",label:"店主ID",model:"shop_id",placeholder:"请输入店主ID"},{type:"input",label:"店主名称",model:"search_text",placeholder:"请输入店主名称"},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"select",label:"店铺状态",model:"shop_status",placeholder:"请输入店铺状态",options:{name:"shopStatusOpts"}},{type:"select",label:"绑定状态",model:"bing_status",placeholder:"请输入绑定状态",options:{name:"bindStatusOpts"}},{type:"select",label:"店主等级",model:"vip_level_name",placeholder:"请输入店主等级",options:{name:"StoreOwnerLevelOpts"}},{type:"date",label:"付费时间",model:"vip_open_time"},{type:"date",label:"付费到期时间",model:"vip_expired_time"},{type:"time",label:"入驻时间",model:"create_time"},{type:"time",label:"到期时间",model:"expired_time"}],i=[{type:"input",label:"代理商名称",model:"enterprise_name",disabled:!0},{type:"select",label:"店铺导师",model:"mentor_id",placeholder:"请选择",options:{name:"mentorOpts"}},{type:"select",label:"店铺会长",model:"president_id",placeholder:"请选择",options:{name:"presidentOpts"}},{type:"input",label:"店主名称",model:"site_name",placeholder:"请输入2-12位中英文"},{type:"input",label:"店主等级",model:"vip_level_name",disabled:!0},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"input",label:"店主微信号",model:"ww",placeholder:"请输入店主微信号"},{type:"date",label:"到期时间",model:"expire_time",placeholder:"请输入到期时间",dateType:"datetime"},{type:"input",label:"店主账号",model:"username",placeholder:"请输入店主账号",disabled:!0},{type:"input",label:"修改登录密码",model:"password",placeholder:"请输入登录密码"},{type:"radio",label:"是否是会长",model:"is_president",options:{name:"persiRadio"}}],o=[{type:"time",timeType:"datetimerange",label:"下单时间",model:"create_time"},{type:"time",timeType:"datetimerange",label:"处理时间",model:"dispose_time"},{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"退款单号",model:"refund_no",placeholder:"请输入退款单号"},{type:"select",label:"订单状态",model:"order_status",options:{name:"orderStateOptions"}},{type:"select",label:"退款状态",model:"refund_status",options:{name:"refundStateOptions"}}]},7991:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"c",(function(){return n})),l.d(t,"e",(function(){return i})),l.d(t,"f",(function(){return o})),l.d(t,"b",(function(){return u})),l.d(t,"d",(function(){return r}));var a=[{faild:"member_id",title:"ID"},{faild:"headimg",title:"头像",slot:"headimg"},{faild:"mobile",title:"手机号码"},{faild:"nickname",title:"微信昵称"},{faild:"site_name",title:"当前锁定店铺"},{faild:"pid",title:"注册推荐人"},{faild:"tags",title:"用户标签",slot:"tags"},{faild:"is_shopping_status",title:"状态",slot:"is_shopping_status"},{faild:"status",title:"账号状态",slot:"status"},{faild:"reg_time",title:"注册时间",slot:"reg_time"},{faild:"xm_group_name",title:"组别"},{title:"操作",slot:"action"}],n=[{faild:"site_id",title:"店铺ID"},{faild:"site_name",title:"店铺名称"},{faild:"lock_time",title:"锁定时间",slot:"lock_time"},{faild:"unlock_time",title:"解锁时间",slot:"unlock_time"}],i=[{faild:"tag_group_id",title:"ID"},{faild:"group_name",title:"标签组"},{faild:"tags_count",title:"下属标签数"},{title:"操作",slot:"action"}],o=[{faild:"tag_id",title:"ID"},{faild:"tag_name",title:"标签名"},{faild:"belongGroup",title:"所属组别",slot:"belongGroup"},{faild:"is_sync_enterprise_wx",title:"同步企微",slot:"is_sync_enterprise_wx"},{faild:"member_tag_count",title:"关联用户数"},{faild:"order",title:"排序",slot:"order"},{faild:"execute_status",title:"自动规则",slot:"execute_status"},{title:"操作",slot:"action"}],u=[{faild:"order_no",title:"订单编号"},{faild:"site_name",title:"店铺名称"},{faild:"order_name",title:"商品名称"},{faild:"order_money",title:"订单金额"},{faild:"pay_money",title:"实际支付金额"},{faild:"order_status_name",title:"订单状态"},{faild:"create_time",title:"下单时间",slot:"create_time"},{title:"操作",slot:"action"}],r=[{faild:"site_id",title:"店主ID"},{faild:"site_name",title:"店铺名称"},{faild:"mobile",title:"联系电话"},{faild:"vip_level_name",title:"店铺等级"},{faild:"create_time",title:"注册时间",slot:"create_time"},{faild:"vip_open_time",title:"付费（入驻）时间",slot:"vip_open_time"},{faild:"vip_expired_time",title:"付费到期时间",slot:"vip_expired_time"},{faild:"group_num",title:"团队会员总人数（包含0元店主）"},{faild:"group_vip_rate",title:"团队付费人数占比"},{faild:"p_site_name",title:"推荐人"},{faild:"upgrade_money",title:"店主升级金额"},{faild:"shop_status",title:"店铺状态",slot:"shop_status"},{faild:"money_sum",title:"店铺交易总额"},{faild:"mentor_name",title:"店铺导师"},{faild:"president_name",title:"店铺会长"},{title:"操作",slot:"action",width:"230"}]},"8d41":function(e,t,l){},b885:function(e,t,l){"use strict";var a=l("e780");l.d(t,"d",(function(){return a["a"]}));var n=l("ad41");l.d(t,"c",(function(){return n["a"]}));var i=l("0476");l.d(t,"g",(function(){return i["a"]}));var o=l("6eb0");l.d(t,"a",(function(){return o["a"]}));var u=l("c87f");l.d(t,"f",(function(){return u["a"]}));var r=l("333d");l.d(t,"e",(function(){return r["a"]}));var s=l("05be");l.d(t,"b",(function(){return s["a"]}));l("9040");var d=l("4381");l.d(t,"h",(function(){return d["a"]}));var c=l("6724");l.d(t,"i",(function(){return c["a"]}))},c40e:function(e,t,l){"use strict";l.d(t,"e",(function(){return n})),l.d(t,"d",(function(){return i})),l.d(t,"f",(function(){return o})),l.d(t,"c",(function(){return u})),l.d(t,"a",(function(){return r})),l.d(t,"g",(function(){return s})),l.d(t,"b",(function(){return d}));var a=l("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"H",(function(){return i})),l.d(t,"f",(function(){return o})),l.d(t,"A",(function(){return u})),l.d(t,"x",(function(){return r})),l.d(t,"e",(function(){return s})),l.d(t,"w",(function(){return d})),l.d(t,"c",(function(){return c})),l.d(t,"O",(function(){return p})),l.d(t,"j",(function(){return b})),l.d(t,"k",(function(){return f})),l.d(t,"l",(function(){return m})),l.d(t,"T",(function(){return v})),l.d(t,"d",(function(){return h})),l.d(t,"Q",(function(){return _})),l.d(t,"p",(function(){return g})),l.d(t,"P",(function(){return y})),l.d(t,"m",(function(){return w})),l.d(t,"I",(function(){return O})),l.d(t,"L",(function(){return S})),l.d(t,"N",(function(){return x})),l.d(t,"M",(function(){return k})),l.d(t,"S",(function(){return j})),l.d(t,"s",(function(){return T})),l.d(t,"B",(function(){return C})),l.d(t,"z",(function(){return I})),l.d(t,"K",(function(){return B})),l.d(t,"C",(function(){return N})),l.d(t,"h",(function(){return z})),l.d(t,"g",(function(){return D})),l.d(t,"o",(function(){return L})),l.d(t,"G",(function(){return R})),l.d(t,"J",(function(){return q})),l.d(t,"v",(function(){return A})),l.d(t,"F",(function(){return E})),l.d(t,"r",(function(){return Q})),l.d(t,"b",(function(){return F})),l.d(t,"q",(function(){return $})),l.d(t,"R",(function(){return H})),l.d(t,"u",(function(){return M})),l.d(t,"t",(function(){return V})),l.d(t,"D",(function(){return W})),l.d(t,"E",(function(){return X})),l.d(t,"y",(function(){return G})),l.d(t,"n",(function(){return P}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],i=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],o=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],u=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],p=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],b=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],S=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],k=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],j=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],T=[{value:"0",label:"参团"},{value:"1",label:"团长"}],C=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],I=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],B=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],N=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],z=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],D=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],L=[{value:"1",label:"是"},{value:"2",label:"否"}],R=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],q=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],A=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],E=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],Q=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],F=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],$=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],H=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],M=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],V=[{label:"按订单编号",value:"order_no"}],W=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],X=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],G=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],P=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e87b:function(e,t,l){"use strict";l.d(t,"g",(function(){return n})),l.d(t,"d",(function(){return i})),l.d(t,"e",(function(){return o})),l.d(t,"h",(function(){return u})),l.d(t,"c",(function(){return r})),l.d(t,"b",(function(){return s})),l.d(t,"f",(function(){return d})),l.d(t,"a",(function(){return c}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/shop/lists.html",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/admin/shop/change_status.html",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/admin/shop/editShop.html",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/admin_plus/shop/showShop",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/admin_plus/shop/editShop",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/admin_plus/shop/addShop",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/admin/agent/getSurplusIntegralByOpenShop",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/admin/shop/addShop",method:"post",data:e})}},fe67:function(e,t,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);