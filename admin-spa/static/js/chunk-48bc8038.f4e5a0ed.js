(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-48bc8038"],{"7a35":function(t,a,s){"use strict";s("b83f")},9406:function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"pd-32"},[s("div",{staticClass:"flex-c mb-40"},[s("div",[s("span",{staticClass:"mr-4"},[t._v("经营总览")]),t._v(" "),s("span",{staticClass:"time mr-4 fs-12"},[t._v("(更新时间："+t._s(t.update_time)+")")])])]),t._v(" "),s("div",{staticClass:"flex-b-c mb-12"},[s("div",{staticClass:"title"},[t._v("订单销量")]),t._v(" "),s("div",{staticClass:"link",on:{click:function(a){return t.toSkip("/analysis/sales")}}},[t._v("销售转化分析 "),s("i",{staticClass:"el-icon-arrow-right"})])]),t._v(" "),s("div",{staticClass:"row flex-c"},[s("div",{staticClass:"data-item"},[s("div",[t._v("今日销售总额（元）")]),t._v(" "),s("span",[t._v(t._s(t.allData.orderSaleAmount))])]),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",[t._v("待发货订单")]),t._v(" "),s("span",{staticClass:"mb-16"},[t._v(t._s(t.allData.waitSendSum))]),t._v(" "),s("router-link",{staticClass:"todispose",attrs:{to:"/order/orderList?order_type=1"}},[t._v("立即发货")])],1),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",{staticClass:"flex-c"},[t._v("售后待审核订单\n      ")]),t._v(" "),s("span",{staticClass:"mb-16"},[t._v(t._s(t.allData.waitVerifySumAfterSale))]),t._v(" "),s("router-link",{staticClass:"todispose",attrs:{to:"/order/refundApply"}},[t._v("去处理")])],1)],1),t._v(" "),s("div",{staticClass:"flex-b-c mb-12"},[s("div",{staticClass:"title"},[t._v("用户访问")]),t._v(" "),s("div",{staticClass:"link",on:{click:function(a){return t.toSkip("/analysis/user")}}},[t._v("用户数据分析 "),s("i",{staticClass:"el-icon-arrow-right"})])]),t._v(" "),s("div",{staticClass:"row flex-c"},[s("div",{staticClass:"data-item"},[s("div",[t._v("今日累计访问次数(PV)")]),t._v(" "),s("span",[t._v(t._s(t.allData.memberData.pv))])]),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",[t._v("今日累计访问人数(UV)")]),t._v(" "),s("span",[t._v(t._s(t.allData.memberData.uv))])]),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",[t._v("今日新增注册用户")]),t._v(" "),s("span",[t._v(t._s(t.allData.memberData.reg_nums))])])],1),t._v(" "),s("div",{staticClass:"flex-b-c mb-12"},[s("div",{staticClass:"title"},[t._v("商品情况")]),t._v(" "),s("div",{staticClass:"link",on:{click:function(a){return t.toSkip("/analysis/commodity")}}},[t._v("商品/类目分析 "),s("i",{staticClass:"el-icon-arrow-right"})])]),t._v(" "),s("div",{staticClass:"row flex-c"},[s("div",{staticClass:"data-item"},[s("div",[t._v("商品总数SPU")]),t._v(" "),s("span",[t._v(t._s(t.allData.goods.spu_total))])]),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",[t._v("上架商品数SPU")]),t._v(" "),s("span",[t._v(t._s(t.allData.goods.spu_online))])]),t._v(" "),s("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),s("div",{staticClass:"data-item"},[s("div",[t._v("上架SKU")]),t._v(" "),s("span",[t._v(t._s(t.allData.goods.sku_online))])])],1),t._v(" "),t._e()],2)},e=[function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"flex-b-c mb-12"},[s("div",{staticClass:"title"},[t._v("客户跟进")]),t._v(" "),s("div",{staticClass:"link"},[t._v("\n        线索跟进情况分析 "),s("i",{staticClass:"el-icon-arrow-right"})])])}],n=s("c7eb"),r=(s("96cf"),s("1da1")),c=s("ef83"),l=s("ed08"),v={name:"Dashboard",data:function(){return{allData:{memberData:{},goods:{},clue:{}},update_time:""}},created:function(){var t=Object(r["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getData();case 2:case"end":return t.stop()}}),t,this)})));function a(){return t.apply(this,arguments)}return a}(),methods:{toSkip:function(t){this.$router.push(t)},getData:function(){var t=Object(r["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["c"])({});case 3:a=t.sent,0==a.code&&(this.allData=a.data,this.update_time=Object(l["d"])(a.timestamp)),t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,this,[[0,7]])})));function a(){return t.apply(this,arguments)}return a}()}},d=v,o=(s("7a35"),s("2877")),_=Object(o["a"])(d,i,e,!1,null,"63a54d70",null);a["default"]=_.exports},b83f:function(t,a,s){},ef83:function(t,a,s){"use strict";s.d(a,"c",(function(){return e})),s.d(a,"e",(function(){return n})),s.d(a,"d",(function(){return r})),s.d(a,"b",(function(){return c})),s.d(a,"a",(function(){return l}));var i=s("b775");function e(t){return Object(i["a"])({url:"/admin_plus/index/index ",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/admin/Analyse/salesAnalyse",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/admin/Analyse/getMemberAnalyse",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/admin/Analyse/commodityCategory.html",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/admin_plus/Analyse/commodityCategory",method:"get",params:t})}}}]);