(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-684f5028"],{"15fd":function(t,e,a){"use strict";function r(t,e){if(null==t)return{};var a,r,o={},n=Object.keys(t);for(r=0;r<n.length;r++)a=n[r],e.indexOf(a)>=0||(o[a]=t[a]);return o}function o(t,e){if(null==t)return{};var a,o,n=r(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++)a=i[o],e.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(t,a)&&(n[a]=t[a])}return n}a.d(e,"a",(function(){return o}))},4239:function(t,e,a){"use strict";a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return n})),a.d(e,"f",(function(){return i})),a.d(e,"a",(function(){return s})),a.d(e,"c",(function(){return c})),a.d(e,"b",(function(){return l}));var r=a("b775");function o(t){return Object(r["a"])({url:"/admin/goodsbrand/lists",method:"get",params:t})}function n(t){return Object(r["a"])({url:"/admin/goodsbrand/modifySort",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/admin/goodsbrand/addBrand.html",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/admin_plus/Goodsbrand/editbrand",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/admin/goodsbrand/editBrand.html",method:"post",data:t})}},4849:function(t,e,a){},e5c5:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[t._v("分类信息")]),t._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",rules:t.rules,model:t.form}},[a("el-form-item",{attrs:{label:"分类名称：",prop:"category_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入分类名称"},model:{value:t.form.category_name,callback:function(e){t.$set(t.form,"category_name",e)},expression:"form.category_name"}}),t._v(" "),a("span",{staticClass:"tips"},[t._v("分类名称最长不超过30个字符")])],1),t._v(" "),a("el-form-item",{attrs:{label:"前端显示简称：",prop:"short_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入简称"},model:{value:t.form.short_name,callback:function(e){t.$set(t.form,"short_name",e)},expression:"form.short_name"}}),t._v(" "),a("span",{staticClass:"tips"},[t._v("分类名过长设置简称方便显示，在小程序前端")])],1),t._v(" "),a("el-form-item",{attrs:{label:"上级分类："}},[a("span",[t._v(t._s(t.classifyName)+"  "),t.can_edit_parent&&"append"!=t.$route.query.eventType?a("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(e){t.dialogVisible=!0}}},[t._v("选择分类")]):t._e()],1),t._v(" "),!t.$route.query.eventType&&t.can_edit_parent?a("span",{staticClass:"tips"},[t._v("如果选择上级分类，那么新增的分类则为被选择上级分类的子分类，不选择上级分类默认为顶级分类")]):t._e(),t._v(" "),"edit"==t.$route.query.eventType?[1==t.oldCategoryDict.level&&0==t.can_edit_parent?a("span",{staticClass:"tips"},[t._v("注意：顶级分类并且有3级子类不能修改")]):2==t.oldCategoryDict.level?a("span",{staticClass:"tips"},[t._v("注意：二级分类可以修改一级分类")]):3==t.oldCategoryDict.level?a("span",{staticClass:"tips"},[t._v("注意：三级分类可以修改二级分类")]):t._e()]:t._e()],2),t._v(" "),a("el-form-item",{attrs:{label:"分类图标："}},[a("label",{staticClass:"upload"},[t.form.image?a("el-image",{attrs:{src:t.form.image,fit:"contain"}}):a("span",[a("i",{staticClass:"el-icon-upload"}),t._v("\n                    点击上传\n                ")]),t._v(" "),a("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{type:"file"},on:{change:t.onChange}})],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("建议图片尺寸：200px * 100px。图片格式：jpg、png、jpeg。")])]),t._v(" "),a("el-form-item",{attrs:{label:"是否显示："}},[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:t.form.is_show,callback:function(e){t.$set(t.form,"is_show",e)},expression:"form.is_show"}}),t._v(" "),a("span",{staticClass:"tips"},[t._v("用于控制前台是否展示")])],1),t._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{staticStyle:{width:"100px"},attrs:{type:"number"},model:{value:t.form.sort,callback:function(e){t.$set(t.form,"sort",e)},expression:"form.sort"}}),t._v(" "),a("span",{staticClass:"tips"},[t._v("排序值必须为整数")])],1),t._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例："}},[a("el-input",{staticStyle:{width:"100px"},attrs:{type:"number"},model:{value:t.form.reward_company_rate,callback:function(e){t.$set(t.form,"reward_company_rate",e)},expression:"form.reward_company_rate"}}),t._v(" %\n            "),a("span",{staticClass:"tips"},[t._v("为0.1 - 99.9的数,仅支持一位小数点")])],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small",loading:t.loading},on:{click:t.onSave}},[t._v("保存")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"选择商品分类",visible:t.dialogVisible,width:"500px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{attrs:{"label-width":"130px"}},[t.$route.query.eventType&&1!=t.oldCategoryDict.level&&2!=t.oldCategoryDict.level?t._e():a("el-form-item",{attrs:{label:"一级分类："}},[a("el-select",{on:{change:function(e){return t.changeLevel(t.form.category_id_1,1)}},model:{value:t.form.category_id_1,callback:function(e){t.$set(t.form,"category_id_1",e)},expression:"form.category_id_1"}},t._l(t.categoryOpt1,(function(t,e){return a("el-option",{key:e,attrs:{label:t.category_name,value:t.category_id}})})),1)],1),t._v(" "),t.$route.query.eventType&&3!=t.oldCategoryDict.level?t._e():a("el-form-item",{attrs:{label:"二级分类："}},[a("el-select",{on:{change:function(e){return t.changeLevel(t.form.category_id_2,2)}},model:{value:t.form.category_id_2,callback:function(e){t.$set(t.form,"category_id_2",e)},expression:"form.category_id_2"}},t._l(t.categoryOpt2,(function(t,e){return a("el-option",{key:e,attrs:{label:t.category_name,value:t.category_id}})})),1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:t.onDialog}},[t._v("确 定")])],1)],1)],1)},o=[],n=(a("7514"),a("c7eb")),i=a("15fd"),s=(a("96cf"),a("1da1")),c=a("b775"),l=a("4239");function d(t){return Object(c["a"])({url:"/admin/goodscategory/getCategoryList.html",method:"post",data:t})}function u(t){return Object(c["a"])({url:"/admin/goodscategory/addCategory.html",method:"post",data:t})}function f(t){return Object(c["a"])({url:"/admin_plus/Goodscategory/editcategory",method:"post",data:t})}function _(t){return Object(c["a"])({url:"/admin/goodscategory/editCategory",method:"post",data:t})}var m=a("ed08"),p=["category_name","short_name","image"],g={data:function(){return{brand_id:null,loading:!1,can_edit_parent:1,oldCategoryDict:{},form:{sort:0,pid:0,level:1,category_id_1:0},classifyName:"顶级分类",rules:{category_name:[{required:!0,message:"分类名称不能为空",trigger:"blur"}],short_name:[{required:!0,message:"前端显示简称不能为空",trigger:"blur"}],reward_company_rate:[{required:!0,message:"公司返佣比例不能为空",trigger:"blur"}]},dialogVisible:!1,categoryOpt1:[],categoryOpt2:[]}},created:function(){var t=Object(s["a"])(Object(n["a"])().mark((function t(){var e,a,r,o,s;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=this.$route.query,a=e.category_id,r=e.eventType,t.next=3,this.onInit();case 3:if(!a){t.next=18;break}return t.next=6,f({category_id:a});case 6:if(o=t.sent,this.oldCategoryDict=Object(m["a"])(o.data.goods_category_info),this.can_edit_parent=o.data.can_edit_parent,"edit"!=r){t.next=17;break}if(0!=o.data.goods_category_info.pid?(this.form.pid=o.data.goods_category_info.pid,this.form.level=o.data.goods_category_info.level,this.classifyName=o.data.goods_category_parent_info.category_name):(this.form.pid=0,this.form.level=1),0==o.data.goods_category_info.pid){t.next=14;break}return t.next=14,this.onInit(o.data.goods_category_info.pid);case 14:0==o.data.goods_category_info.category_id_2&&(o.data.goods_category_info.category_id_2=""),this.form=o.data.goods_category_info,1==this.form.level?(this.form.category_id_1=0,this.form.category_id_2=null):2==this.form.level&&(this.form.category_id_2=null);case 17:"append"==r&&(s=o.data.goods_category_info,s.category_name,s.short_name,s.image,Object(i["a"])(s,p),this.form.pid=o.data.goods_category_info.category_id,this.form.level=o.data.goods_category_info.level+1,this.classifyName=o.data.goods_category_info.category_name);case 18:if("edit"!=r){t.next=22;break}return this.form.category_id=a,this.$route.meta.title="编辑分类",t.abrupt("return");case 22:if("append"!=r){t.next=26;break}return this.form.category_id=a,this.$route.meta.title="添加子类",t.abrupt("return");case 26:this.$route.meta.title="新增分类";case 27:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{onInit:function(){var t=Object(s["a"])(Object(n["a"])().mark((function t(e){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=e||0,t.next=3,d({pid:e});case 3:a=t.sent,0==a.code&&(0==e&&a.data.push({category_name:"顶级分类",category_id:0}),this["categoryOpt".concat(0==e?"1":"2")]=a.data);case 5:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),changeLevel:function(){var t=Object(s["a"])(Object(n["a"])().mark((function t(e,a){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.form.pid=e,this.form.level=0==e?a:a+1,1!=a){t.next=5;break}return t.next=5,this.onInit(e);case 5:case"end":return t.stop()}}),t,this)})));function e(e,a){return t.apply(this,arguments)}return e}(),onDialog:function(){var t=this;if(this.dialogVisible=!1,this.form.category_id_2)return this.categoryOpt2.find((function(e){e.category_id==t.form.category_id_2&&(t.classifyName=e.category_name)})),void(this.form.pid=this.form.category_id_2);this.form.pid=this.form.category_id_1,this.categoryOpt1.find((function(e){e.category_id==t.form.category_id_1&&(t.classifyName=e.category_name)}))},onChange:function(){var t=Object(s["a"])(Object(n["a"])().mark((function t(e){var a,r,o,i;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=this.$loading({lock:!0,text:"上传中"}),r=e.target.files[0],o=new FormData,o.append("file",r),t.prev=4,t.next=7,Object(l["f"])(o);case 7:i=t.sent,this.form.image=i.data.pic_path,a.close(),this.$message.success(i.message),this.$forceUpdate(),t.next=16;break;case 14:t.prev=14,t.t0=t["catch"](4);case 16:a.close();case 17:case"end":return t.stop()}}),t,this,[[4,14]])})));function e(e){return t.apply(this,arguments)}return e}(),onSave:function(){var t=this;this.$refs.form.validate((function(e){if(e){t.loading=!0;var a=t.$route.query.eventType,r=u;"edit"==a&&(r=_),r(t.form).then((function(e){t.$message.success("提交成功"),t.loading=!1,t.$store.dispatch("delView",{path:t.$route.path}).then((function(e){e.visitedViews;t.$router.push("/goods/spu")}))}))}}))}}},y=g,h=(a("e6c8"),a("2877")),v=Object(h["a"])(y,r,o,!1,null,"67152c56",null);e["default"]=v.exports},e6c8:function(t,e,a){"use strict";a("4849")}}]);