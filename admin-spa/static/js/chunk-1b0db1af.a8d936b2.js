(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1b0db1af"],{"3f5e":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return u})),l.d(t,"a",(function(){return i}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,l){"use strict";var a=l("a18c"),n={inserted:function(e,t,l){var n=t.value,u=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;u.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},u=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(u)),n.install=u;t["a"]=n},6396:function(e,t,l){"use strict";l.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function u(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,l){var i=u(),o=e-i,r=20,d=0;t="undefined"===typeof t?500:t;var c=function e(){d+=r;var u=Math.easeInOutQuad(d,i,o,t);n(u),d<t?a(e):l&&"function"===typeof l&&l()};c()}},6724:function(e,t,l){"use strict";l("8d41");var a={bind:function(e,t){e.addEventListener("click",(function(l){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),u=n.ele;if(u){u.style.position="relative",u.style.overflow="hidden";var i=u.getBoundingClientRect(),o=u.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(i.width,i.height)+"px",u.appendChild(o)),n.type){case"center":o.style.top=i.height/2-o.offsetHeight/2+"px",o.style.left=i.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(l.pageY-i.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(l.pageX-i.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=n.color,o.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;t["a"]=a},"8d41":function(e,t,l){},9597:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[e.showSearch?l("div",{staticClass:"filter-container"},[l("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),l("div",{staticClass:"flex-b-c buttons"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v("搜索")]),e._v(" "),l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{toggleSearch:e.toggleSearch,"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},onSearch:e.getTableList},scopedSlots:e._u([{key:"pay_type_name",fn:function(t){var a=t.row;return[l("p",[e._v(e._s(a.pay_type_name)+" "),l("br"),e._v(" "+e._s(a.app_type_name))])]}},{key:"refund_action_time",fn:function(t){var l=t.row;return[e._v("\n            "+e._s(e._f("parseTime")(l.refund_action_time))+"\n        ")]}},{key:"action_action_time",fn:function(t){var l=t.row;return[e._v("\n            "+e._s(e._f("parseTime")(l.action_action_time))+"\n        ")]}},{key:"action",fn:function(t){var a=t.row;return[l("el-button",{class:{danger:e.getStatus(a.refund_status)},attrs:{type:"text"},on:{click:function(t){return e.goSkip("/order/returnDetails",{order_goods_id:a.order_goods_id})}}},[e._v("\n                "+e._s(e.getStatus(a.refund_status)?"去处理":"查看")+"\n            ")])]}}])})],1)},n=[],u=l("5530"),i=l("b885"),o=l("c57b"),r=l("c71b"),d=l("e585"),c=l("ed08"),s={components:{FormQuery:i["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{orderStatusOpts:r["x"],afterSalesOpts:r["e"]},form:{},formConfig:[{type:"time",timeType:"datetimerange",label:"下单时间",model:"order_time"},{type:"time",timeType:"datetimerange",label:"处理时间",model:"handle_time"},{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"退款单号",model:"refund_no",placeholder:"请输入退款单号"},{type:"select",label:"订单状态",model:"order_status",placeholder:"请选择",options:{name:"orderStatusOpts"}},{type:"select",label:"售后状态",model:"refund_status",placeholder:"请选择",options:{name:"afterSalesOpts"}}],loading:!1,columns:o["f"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){this.getTableList()},methods:{getStatus:function(e){return 1==e||4==e||6==e||11==e||5==e},goSkip:function(e,t){this.$router.push({path:e,query:t})},handleQuery:function(){this.options={page:1,page_size:10,total:0},this.getTableList()},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(e){var t=this;this.loading=!0,this.list=[],e&&e&&(this.options.page=e.page,this.options.page_size=e.page_size);var l=Object(u["a"])(Object(u["a"])(Object(u["a"])({},this.options),this.form),{},{start_time:this.form.order_time?Object(c["d"])(this.form.order_time[0]):"",end_time:this.form.order_time?Object(c["d"])(this.form.order_time[1]):"",start_handle_time:this.form.handle_time?Object(c["d"])(this.form.handle_time[0]):"",end_handle_time:this.form.handle_time?Object(c["d"])(this.form.handle_time[1]):""});Object(d["p"])(l).then((function(e){0==e.code&&(t.options.total=e.data.count,t.list=e.data.list,t.loading=!1)}))}}},f=s,b=l("2877"),m=Object(b["a"])(f,a,n,!1,null,"5fafc290",null);t["default"]=m.exports},b885:function(e,t,l){"use strict";var a=l("e780");l.d(t,"d",(function(){return a["a"]}));var n=l("ad41");l.d(t,"c",(function(){return n["a"]}));var u=l("0476");l.d(t,"g",(function(){return u["a"]}));var i=l("6eb0");l.d(t,"a",(function(){return i["a"]}));var o=l("c87f");l.d(t,"f",(function(){return o["a"]}));var r=l("333d");l.d(t,"e",(function(){return r["a"]}));var d=l("05be");l.d(t,"b",(function(){return d["a"]}));l("9040");var c=l("4381");l.d(t,"h",(function(){return c["a"]}));var s=l("6724");l.d(t,"i",(function(){return s["a"]}))},c40e:function(e,t,l){"use strict";l.d(t,"e",(function(){return n})),l.d(t,"d",(function(){return u})),l.d(t,"f",(function(){return i})),l.d(t,"c",(function(){return o})),l.d(t,"a",(function(){return r})),l.d(t,"g",(function(){return d})),l.d(t,"b",(function(){return c}));var a=l("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c57b:function(e,t,l){"use strict";l.d(t,"f",(function(){return a})),l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return u})),l.d(t,"d",(function(){return i})),l.d(t,"e",(function(){return o})),l.d(t,"g",(function(){return r})),l.d(t,"a",(function(){return d}));var a=[{faild:"order_no",title:"订单号"},{faild:"refund_no",title:"退款单号"},{faild:"goods_id",title:"商品ID"},{faild:"goods_name",title:"商品名称"},{faild:"member_id",title:"会员ID"},{faild:"mobile",title:"会员手机号"},{faild:"pay_type_name",title:"支付方式",slot:"pay_type_name"},{faild:"refund_real_money",title:"退款金额"},{faild:"order_status_name",title:"订单状态"},{faild:"refund_status_name",title:"售后状态"},{faild:"refund_action_time",title:"申请时间",slot:"refund_action_time"},{faild:"action_action_time",title:"处理时间",slot:"action_action_time"},{faild:"action_username",title:"操作人"},{title:"操作",slot:"action"}],n=[{faild:"goods_id",title:"商品ID"},{faild:"goods_name",title:"商品名称"},{faild:"sku_name",title:"规格属性"},{faild:"goods_money",title:"商品单价"},{faild:"num",title:"数量"},{faild:"goodscoupon_money",title:"运费"},{faild:"promotion_money",title:"优惠明细",slot:"promotion_money"},{faild:"refund_status_name",title:"状态"}],u=[{faild:"order_no",title:"订单号"},{faild:"refund_no",title:"换货单号"},{faild:"goods_id",title:"商品ID"},{faild:"goods_name",title:"商品名称"},{faild:"member_id",title:"会员ID"},{faild:"mobile",title:"会员手机号"},{faild:"order_status_name",title:"订单状态"},{faild:"refund_status_name",title:"售后状态"},{faild:"refund_action_time",title:"申请时间",slot:"refund_action_time"},{faild:"action_action_time",title:"处理时间",slot:"action_action_time"},{faild:"action_username",title:"操作人"},{title:"操作",slot:"action",faild:"action"}],i=[{type:"check",slot:!0,width:"3%",title:"选择框",id:1},{title:"商品名称",faild:"goods_name",type:6,width:"22%",id:2},{faild:["price","num"],title:"单价/数量",type:1,width:"7%",id:3},{faild:"goodscoupon_money",title:"总优惠",width:"7%",id:4},{faild:"combo_cheap_price",title:"运费",width:"7%",id:5},{title:"实付金额",width:"7%",slot:!0,type:6,id:6},{title:"流水号",slot:!0,type:2,width:"13%",id:7},{title:"收货人信息",slot:!0,type:3,width:"13%",id:8},{title:"订单状态",slot:!0,type:4,width:"7%",id:9},{title:"操作",slot:!0,type:5,width:"14%",id:10}],o=[{faild:"sku_name",title:"商品"},{faild:"sku_no",title:"SPU/SKU"},{faild:"goods_id",title:"商品ID"},{faild:"price",title:"价格"},{faild:"num",title:"数量"},{faild:"real_goods_money",title:"小计(元)"}],r=[{faild:"order_no",title:"订单号"},{faild:"name",title:"收货人",slot:"name"},{faild:"mobile",title:"收货人手机",slot:"mobile"},{faild:"address",title:"收货地址",slot:"address",width:290},{faild:"supply_shop_name",title:"供应商"},{faild:"expressList",title:"物流配送",slot:"expressList",width:400}],d=[{faild:"goods_id",title:"商品ID"},{faild:"goods_name",title:"商品名称"},{faild:"sku_name",title:"规格属性"},{faild:"price",title:"商品单价"},{faild:"num",title:"数量"},{faild:"promotion_money",title:"运费"},{faild:"goodscoupon_money",title:"优惠明细",slot:"seckill"},{faild:"refund_status_name",title:"状态"},{title:"操作",slot:"action",faild:"action"}]},c71b:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"H",(function(){return u})),l.d(t,"f",(function(){return i})),l.d(t,"A",(function(){return o})),l.d(t,"x",(function(){return r})),l.d(t,"e",(function(){return d})),l.d(t,"w",(function(){return c})),l.d(t,"c",(function(){return s})),l.d(t,"O",(function(){return f})),l.d(t,"j",(function(){return b})),l.d(t,"k",(function(){return m})),l.d(t,"l",(function(){return v})),l.d(t,"T",(function(){return p})),l.d(t,"d",(function(){return h})),l.d(t,"Q",(function(){return _})),l.d(t,"p",(function(){return g})),l.d(t,"P",(function(){return y})),l.d(t,"m",(function(){return w})),l.d(t,"I",(function(){return O})),l.d(t,"L",(function(){return j})),l.d(t,"N",(function(){return S})),l.d(t,"M",(function(){return k})),l.d(t,"S",(function(){return x})),l.d(t,"s",(function(){return C})),l.d(t,"B",(function(){return T})),l.d(t,"z",(function(){return R})),l.d(t,"K",(function(){return L})),l.d(t,"C",(function(){return D})),l.d(t,"h",(function(){return I})),l.d(t,"g",(function(){return q})),l.d(t,"o",(function(){return A})),l.d(t,"G",(function(){return N})),l.d(t,"J",(function(){return E})),l.d(t,"v",(function(){return z})),l.d(t,"F",(function(){return M})),l.d(t,"r",(function(){return Q})),l.d(t,"b",(function(){return P})),l.d(t,"q",(function(){return F})),l.d(t,"R",(function(){return H})),l.d(t,"u",(function(){return V})),l.d(t,"t",(function(){return W})),l.d(t,"D",(function(){return X})),l.d(t,"E",(function(){return B})),l.d(t,"y",(function(){return G})),l.d(t,"n",(function(){return J}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],d=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],s=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],b=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],m=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],v=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],p=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],S=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],k=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],x=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],C=[{value:"0",label:"参团"},{value:"1",label:"团长"}],T=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],R=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],L=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],D=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],I=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],q=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],A=[{value:"1",label:"是"},{value:"2",label:"否"}],N=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],E=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],z=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],M=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],Q=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],P=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],F=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],H=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],V=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],W=[{label:"按订单编号",value:"order_no"}],X=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],B=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],G=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],J=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e585:function(e,t,l){"use strict";l.d(t,"m",(function(){return n})),l.d(t,"g",(function(){return u})),l.d(t,"n",(function(){return i})),l.d(t,"p",(function(){return o})),l.d(t,"o",(function(){return r})),l.d(t,"a",(function(){return d})),l.d(t,"c",(function(){return c})),l.d(t,"k",(function(){return s})),l.d(t,"b",(function(){return f})),l.d(t,"h",(function(){return b})),l.d(t,"i",(function(){return m})),l.d(t,"j",(function(){return v})),l.d(t,"f",(function(){return p})),l.d(t,"q",(function(){return h})),l.d(t,"e",(function(){return _})),l.d(t,"d",(function(){return g})),l.d(t,"l",(function(){return y}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/refund/refundList",method:"get",params:e})}function u(e){return Object(a["a"])({url:"/admin/refund/getOrderChainStatus",method:"post",data:e})}function i(){return Object(a["a"])({url:"/admin/refund/refundReview",method:"post",data:data})}function o(e){return Object(a["a"])({url:"/admin/refund/returnRefundList",method:"get",params:e})}function r(e){return Object(a["a"])({url:"/admin_plus/refund/returnRefundDetail",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/admin/refund/agreeReturnMoney.html",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/admin/refund/checkSupplyChainOrder.html",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/admin/Express/logistics.html",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/admin/refund/agreeReturnRefundReview.html",method:"post",data:e})}function b(e){return Object(a["a"])({url:"/admin/address/getProvince.html",method:"post",data:e})}function m(e){return Object(a["a"])({url:"/admin/address/getcity.html",method:"post",data:e})}function v(e){return Object(a["a"])({url:"/admin/address/getdistrict.html",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/admin/express/expressCompany.html",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/admin/refund/sendAgain.html",method:"post",data:e})}function _(e){return Object(a["a"])({url:"/admin/refund/exchangeGoodsList",method:"get",params:e})}function g(e){return Object(a["a"])({url:"/admin_plus/Refund/exchangeGoodsDetail",method:"post",data:e})}function y(e){return Object(a["a"])({url:"/admin_plus/refund/refundDetail",method:"post",data:e})}},fe67:function(e,t,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);