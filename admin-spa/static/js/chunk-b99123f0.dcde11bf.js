(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b99123f0"],{"3f5e":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i}));var l=a("b775");function n(e){return Object(l["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(l["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(l["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,a){"use strict";var l=a("a18c"),n={inserted:function(e,t,a){var n=t.value,o=l["a"].app._route.meta&&l["a"].app._route.meta.permissions;o.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},o=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(o)),n.install=o;t["a"]=n},6396:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,a,l){return e/=l/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var l=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),s=e-i,u=20,r=0;t="undefined"===typeof t?500:t;var c=function e(){r+=u;var o=Math.easeInOutQuad(r,i,s,t);n(o),r<t?l(e):a&&"function"===typeof a&&a()};c()}},6724:function(e,t,a){"use strict";a("8d41");var l={bind:function(e,t){e.addEventListener("click",(function(a){var l=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),o=n.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var i=o.getBoundingClientRect(),s=o.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":(s=document.createElement("span"),s.className="waves-ripple",s.style.height=s.style.width=Math.max(i.width,i.height)+"px",o.appendChild(s)),n.type){case"center":s.style.top=i.height/2-s.offsetHeight/2+"px",s.style.left=i.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(a.pageY-i.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(a.pageX-i.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=n.color,s.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(n)),l.install=n;t["a"]=l},"8d41":function(e,t,a){},b64e:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[e.showSearch?a("div",{staticClass:"filter-container"},[a("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),a("div",{staticClass:"flex-b-c buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(t){return e.handleQuery()}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),a("div",{staticClass:"table-list"},[a("div",{staticClass:"btns"},[e._v("\n            批量操作：\n            "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.tieUp()}}},[e._v("关联分类")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.comRebate()}}},[e._v("公司返佣比例")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.shopRebate()}}},[e._v("店主返佣比例")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.offShelf()}}},[e._v("批量下架")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.onShelf()}}},[e._v("批量上架")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:function(t){return e.liveRoom()}}},[e._v("同步到直播间")]),e._v(" "),a("el-button",{attrs:{type:"danger",plain:"",size:"small"},on:{click:function(t){return e.goSkip("/goods/quality/warning")}}},[e._v("异常商品")]),e._v(" "),a("el-button",{attrs:{type:"danger",plain:"",size:"small"},on:{click:function(t){return e.check()}}},[e._v("检测全站异常商品")])],1),e._v(" "),a("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"oTbale",staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{"expand-change":e.expandChange,selection:e.handleSelectionChange,toggleSearch:e.toggleSearch,"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},"update:options":function(t){e.options=t},onSearch:e.getTableList},scopedSlots:e._u([{key:"expand",fn:function(t){var l=t.row;return[a("div",{staticClass:"detail"},e._l(l.children,(function(t,n){return a("div",{key:n},[a("img",{directives:[{name:"loading",rawName:"v-loading",value:!l.children,expression:"!row.children"}],attrs:{src:t.sku_image,alt:""},on:{click:function(a){return e.setImage(t.sku_image)}}}),e._v(" "),a("div",[a("h4",[e._v(e._s(t.sku_name))]),e._v(" "),a("div",[a("span",[e._v("sku_id："+e._s(t.sku_id))]),e._v(" "),a("span",[e._v("sku："+e._s(t.sku_no))])]),e._v(" "),a("div",[a("span",[e._v("供应商价格：￥"+e._s(t.cost_price))]),e._v(" "),a("span",[e._v("供货价：￥"+e._s(t.price))]),e._v(" "),a("span",[e._v("原价：￥"+e._s(t.market_price))])]),e._v(" "),a("div",[a("span",[e._v("库存："+e._s(t.stock))]),e._v(" "),a("span",[e._v("销量："+e._s(t.sale_num))])])])])})),0)]}},{key:"goods_name",fn:function(t){var l=t.row;return[a("div",{staticClass:"name_info"},[a("img",{attrs:{src:e.getImage(l.goods_image),alt:""},on:{click:function(t){return e.setImage(l.goods_image)}}}),e._v(" "),a("div",[a("span",[e._v(e._s(l.goods_name))]),e._v(" "),a("span",[e._v("SPU："+e._s(l.sn))])])])]}},{key:"site_name",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v("供应商："+e._s(l.site_name))]),e._v(" "),a("span",[e._v("分类："+e._s(l.category_name))]),e._v(" "),a("span",[e._v("品牌名："+e._s(l.brand_name))])])]}},{key:"reward_company_rate",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v("比例："+e._s(l.reward_company_rate)+"%")]),e._v(" "),a("span",[e._v("收益：￥"+e._s((l.cost_price*l.reward_company_rate/100).toFixed(2)))]),e._v(" "),a("span",[e._v("供应商价："+e._s(l.cost_price.toFixed(2)))])])]}},{key:"price",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v("供货价："+e._s(l.price))]),e._v(" "),a("span",[e._v("原价："+e._s(l.market_price)+" ")]),e._v(" "),a("span",[e._v("销售价："+e._s(l.sale_price))])])]}},{key:"reward_shop_rate",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v("比例："+e._s(l.reward_shop_rate)+"%")]),e._v(" "),a("span",[e._v("收益：￥"+e._s(l.reward_shop)+" ")])])]}},{key:"goods_stock",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v("库存："+e._s(l.goods_stock))]),e._v(" "),a("span",[e._v("销量："+e._s(l.sale_num))]),e._v(" "),a("span",[e._v("虚拟销量："+e._s(l.virtual_sale_num))])])]}},{key:"create_time",fn:function(t){var l=t.row;return[a("div",{staticClass:"info"},[a("span",[e._v(e._s(e._f("getStatus")(l.goods_state,e.formopts.shelvesOpt)))]),e._v(" "),a("span",[e._v(e._s(e._f("parseTime")(l.create_time)))])])]}},{key:"action",fn:function(t){var l=t.row,n=l.goods_id,o=l.goods_state;return[1==o?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.offShelf(n)}}},[e._v("下架")]):a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.onShelf(n)}}},[e._v("上架")]),e._v(" "),0==o?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/goods/quality/edit",{goods_id:n})}}},[e._v("编辑")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/goods/quality/details",{goods_id:n})}}},[e._v("详情")])]}}])}),e._v(" "),a("o-image",{ref:"image",attrs:{list:e.images}}),e._v(" "),a("o-classify",{ref:"classify",on:{getClassify:e.getClassify},model:{value:e.form.classify,callback:function(t){e.$set(e.form,"classify",t)},expression:"form.classify"}}),e._v(" "),a("el-dialog",{attrs:{title:"公司返佣设置：（当前已选中"+e.tableSels.length+"个商品）",visible:e.compVisible,width:"40%"},on:{"update:visible":function(t){e.compVisible=t}}},[a("el-form",{attrs:{"label-width":"150px"}},[a("el-form-item",{attrs:{label:"公司返佣比例"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:e.rebateRate,callback:function(t){e.rebateRate=t},expression:"rebateRate"}}),e._v("\n                    %\n                ")],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.compVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.compSubmit}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"店主返佣设置：（当前已选中"+e.tableSels.length+"个商品）",visible:e.shopVisible,width:"40%"},on:{"update:visible":function(t){e.shopVisible=t}}},[a("el-form",{attrs:{"label-width":"150px"}},[a("el-form-item",{attrs:{label:"店主返佣比例"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:e.rebateRate,callback:function(t){e.rebateRate=t},expression:"rebateRate"}}),e._v("\n                    %\n                ")],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.shopVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.shopSubmit}},[e._v("确 定")])],1)],1)],1)])},n=[],o=a("5530"),i=(a("28a5"),a("b885")),s=a("966c"),u=(a("327e"),a("c71b")),r=a("e720"),c=(a("8975"),a("d74f")),d={components:{FormQuery:i["d"]},data:function(){return{images:[],showSearch:!0,compVisible:!1,shopVisible:!1,rebateRate:"",baseConfig:{labelWidth:"120px"},tableSels:[],formopts:{isOpt:u["o"],shelvesOpt:u["G"],tagOpt:u["J"],brandOpt:[],addCouponClassify:[]},form:{},formConfig:r["b"],loading:!1,columns:s["c"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){var e=this;Object(c["i"])({level:1,pid:0}).then((function(t){var a=t.data;e.formopts.addCouponClassify=a})),Object(c["b"])({page_size:"Infinity"}).then((function(t){var a=t.data.list;e.formopts.brandOpt=a})),this.handleQuery()},methods:{getClassify:function(e){var t=this,a=e.id;Object(c["f"])({category_id:a}).then((function(e){var l=e.code,n=e.message;if(l<0)t.$message.error(n);else{var o=[];t.tableSels.map((function(e){o.push(e.goods_id)})),Object(c["c"])({goods_ids:o.join(","),category_name:t.form.classify,category_id:a}).then((function(e){t.handleQuery(),t.$message.success("关联分类成功"),t.$refs.classify.hide()}))}}))},expandChange:function(e){var t=this,a=e.goods_id;Object(c["k"])({goods_id:a}).then((function(e){var l=e.data;t.list.map((function(e,n){e.goods_id===a&&(e.children=l,t.$set(t.list,n,e))})),t.$forceUpdate()}))},setImage:function(e){this.images=e.split(","),this.$refs.image.init()},tieUp:function(){this.tableSels.length>0?this.$refs.classify.init():this.$message.info("请选择需要操作的数据")},comRebate:function(){this.tableSels.length>0?this.compVisible=!0:this.$message.info("请选择需要操作的数据")},compSubmit:function(){var e=this;if(this.rebateRate){var t=[];return this.tableSels.map((function(e){t.push(e.goods_id)})),void Object(c["d"])({goods_ids:t.join(","),goods_count:this.tableSels.length,reward_company_rate:this.rebateRate}).then((function(t){e.$message.success("修改公司返佣比例成功"),e.compVisible=!1,e.handleQuery()}))}this.$message.error("公司返佣比例不能为空")},offShelf:function(e){var t=this;this.tableSels.length>0||e?this.$confirm("确定要下架商品吗?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=[];t.tableSels.map((function(e){a.push(e.goods_id)})),Object(c["h"])({goods_state:0,goods_ids:e||a.join(",")}).then((function(e){t.$message.success("下架商品成功"),t.handleQuery()}))})).catch((function(){})):this.$message.info("请选择需要操作的数据")},shopRebate:function(){this.tableSels.length>0?this.shopVisible=!0:this.$message.info("请选择需要操作的数据")},shopSubmit:function(){var e=this;if(this.rebateRate){var t=[];return this.tableSels.map((function(e){t.push(e.goods_id)})),void Object(c["e"])({goods_ids:t.join(","),goods_count:this.tableSels.length,reward_shop_rate:this.rebateRate}).then((function(t){e.$message.success("修改店主返佣比例成功"),e.shopVisible=!1,e.handleQuery()}))}this.$message.error("店主返佣比例不能为空")},onShelf:function(e){var t=this;this.tableSels.length>0||e?this.$confirm("确定要上架商品吗?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=[];t.tableSels.map((function(e){a.push(e.goods_id)})),Object(c["h"])({goods_state:1,goods_ids:e||a.join(",")}).then((function(e){t.$message.success("上架商品成功"),t.handleQuery()}))})).catch((function(){})):this.$message.info("请选择需要操作的数据")},liveRoom:function(){var e=this;this.tableSels.length>0?this.$confirm("确定要上传到直播间吗?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=[];e.tableSels.map((function(e){t.push(e.goods_id)}));var a=e.$loading({lock:!0,text:"正在检测同步到直播间，请不要刷新页面...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Object(c["n"])({goods_ids:t.join(",")}).then((function(t){var l=t.message;e.$message.success(l),e.handleQuery(),a.close()}))})).catch((function(){})):this.$message.info("请选择需要操作的数据")},check:function(){var e=this;this.$alert("检测到异常的商品将被强制下架","提示",{confirmButtonText:"确定",type:"warning",callback:function(t){var a=e.$loading({lock:!0,text:"正在检测异常商品，请不要刷新页面...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Object(c["a"])({}).then((function(t){a.close(),e.handleQuery()}))}})},getImage:function(e){var t=e.split(",");return t[0]},del:function(){var e=this;this.$confirm("确定删除该数据吗?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$message({type:"success",message:"删除成功!"})})).catch((function(e){}))},handleSelectionChange:function(e){console.log(e),this.tableSels=e},edit:function(e){var t=this;this.$prompt("请输入标签组名称","编辑标签组",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:e.group_name}).then((function(a){var l=a.value;console.log(l),e.group_name=l,t.$message({message:"修改标签组名称成功",type:"success"})})).catch((function(e){}))},executeRule:function(){var e=this;this.$confirm("确定删确定该规则吗?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$message({type:"success",message:"删除成功!"})})).catch((function(e){}))},goSkip:function(e,t){this.$router.push({path:e,query:t})},updateOrder:function(){this.$message({message:"更新标签排序成功",type:"success"})},setEnterorise:function(e){return 1==e?"是":"否"},handleReset:function(){},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(e){var t=this;Object(c["l"])(Object(o["a"])(Object(o["a"])({},e),this.form)).then((function(e){var a=e.data,l=a.count,n=a.list;t.options.total=l,t.list=n,t.loading=!1}))},handleQuery:function(e){this.loading=!0,this.list=[];var t=e||{page:1,page_size:this.options.page_size};this.getTableList(t)}}},v=d,b=(a("e21b"),a("2877")),f=Object(b["a"])(v,l,n,!1,null,"05d60d78",null);t["default"]=f.exports},b885:function(e,t,a){"use strict";var l=a("e780");a.d(t,"d",(function(){return l["a"]}));var n=a("ad41");a.d(t,"c",(function(){return n["a"]}));var o=a("0476");a.d(t,"g",(function(){return o["a"]}));var i=a("6eb0");a.d(t,"a",(function(){return i["a"]}));var s=a("c87f");a.d(t,"f",(function(){return s["a"]}));var u=a("333d");a.d(t,"e",(function(){return u["a"]}));var r=a("05be");a.d(t,"b",(function(){return r["a"]}));a("9040");var c=a("4381");a.d(t,"h",(function(){return c["a"]}));var d=a("6724");a.d(t,"i",(function(){return d["a"]}))},c40e:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return u})),a.d(t,"g",(function(){return r})),a.d(t,"b",(function(){return c}));var l=a("b775");function n(e){return Object(l["a"])({url:"/goods/product/state/",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"i",(function(){return n})),a.d(t,"H",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"A",(function(){return s})),a.d(t,"x",(function(){return u})),a.d(t,"e",(function(){return r})),a.d(t,"w",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"O",(function(){return v})),a.d(t,"j",(function(){return b})),a.d(t,"k",(function(){return f})),a.d(t,"l",(function(){return p})),a.d(t,"T",(function(){return m})),a.d(t,"d",(function(){return h})),a.d(t,"Q",(function(){return _})),a.d(t,"p",(function(){return g})),a.d(t,"P",(function(){return y})),a.d(t,"m",(function(){return w})),a.d(t,"I",(function(){return k})),a.d(t,"L",(function(){return S})),a.d(t,"N",(function(){return x})),a.d(t,"M",(function(){return O})),a.d(t,"S",(function(){return C})),a.d(t,"s",(function(){return $})),a.d(t,"B",(function(){return j})),a.d(t,"z",(function(){return T})),a.d(t,"K",(function(){return R})),a.d(t,"C",(function(){return V})),a.d(t,"h",(function(){return z})),a.d(t,"g",(function(){return B})),a.d(t,"o",(function(){return Q})),a.d(t,"G",(function(){return U})),a.d(t,"J",(function(){return I})),a.d(t,"v",(function(){return q})),a.d(t,"F",(function(){return N})),a.d(t,"r",(function(){return P})),a.d(t,"b",(function(){return A})),a.d(t,"q",(function(){return E})),a.d(t,"R",(function(){return L})),a.d(t,"u",(function(){return F})),a.d(t,"t",(function(){return D})),a.d(t,"D",(function(){return K})),a.d(t,"E",(function(){return M})),a.d(t,"y",(function(){return H})),a.d(t,"n",(function(){return W}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],o=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],s=[{label:"是",code:1},{label:"否",code:0}],u=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],r=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],v=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],b=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],p=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],S=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],O=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],C=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],$=[{value:"0",label:"参团"},{value:"1",label:"团长"}],j=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],T=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],R=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],V=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],z=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],B=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],Q=[{value:"1",label:"是"},{value:"2",label:"否"}],U=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],I=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],q=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],N=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],P=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],A=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],E=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],L=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],F=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],D=[{label:"按订单编号",value:"order_no"}],K=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],M=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],H=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],W=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},cb69:function(e,t,a){},e21b:function(e,t,a){"use strict";a("cb69")},e720:function(e,t,a){"use strict";a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return o}));var l=[{type:"input",label:"商品ID",model:"goods_id",placeholder:"请输入商品ID"},{type:"input",label:"商品名称",model:"search_text",placeholder:"请输入商品名称"},{type:"input",label:"SPU/SKU",model:"spu",placeholder:"请输入SPU/SKU"},{type:"input",label:"供应商名称",model:"supplier_name",placeholder:"请输入供应商名称"},{type:"casder",label:"商品分类",model:"category_id",placeholder:"请选择",options:{name:"addCouponClassify"},props:{label:"category_name",value:"category_id"}},{type:"select",label:"是否任务商品",model:"is_league",placeholder:"请输入是否任务商品",options:{name:"isOpt"}},{type:"select",label:"商品品牌",model:"goods_brand",placeholder:"请输入商品品牌",options:{name:"brandOpt",value:"brand_id",label:"brand_name"}},{type:"select",label:"商品状态",model:"goods_state",placeholder:"请输入商品状态",options:{name:"shelvesOpt"}},{type:"select",label:"商品标签",model:"goods_tag_id",placeholder:"请输入商品标签",options:{name:"tagOpt"}},{type:"select",label:"是否直播商品",model:"is_broadcast",placeholder:"请输入是否直播商品",options:{name:"isOpt"}}],n=[{type:"input",label:"SPU/SKU",model:"sku_no",placeholder:"请输入供应链SPU/SKU"},{type:"input",label:"商品名称",model:"keyword",placeholder:"商品名称/SKU"},{type:"select",label:"原品牌",model:"brand_id",placeholder:"全部",options:{name:"originalData",label:"name",value:"cate_id"}},{type:"time",label:"下单时间",model:"upTime"}],o=[{type:"input",label:"商品名称",model:"search_text",placeholder:"请输入商品名称"},{type:"input",label:"供应链SPU",model:"spu_no",placeholder:"请输入供应链SPU"},{type:"select",label:"异常类型",model:"abnormal_type",options:{name:"warningOpt"}},{type:"time",label:"异常时间",model:"date",timeType:"datetimerange"}]},fe67:function(e,t,a){e.exports=a.p+"static/img/login_bg.e491666c.png"}}]);