(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69c0e61b"],{"3cc8":function(t,n,o){"use strict";o("ba8b")},6229:function(t,n,o){"use strict";o.d(n,"n",(function(){return u})),o.d(n,"o",(function(){return s})),o.d(n,"h",(function(){return d})),o.d(n,"j",(function(){return l})),o.d(n,"m",(function(){return p})),o.d(n,"e",(function(){return f})),o.d(n,"i",(function(){return m})),o.d(n,"u",(function(){return h})),o.d(n,"t",(function(){return b})),o.d(n,"r",(function(){return g})),o.d(n,"s",(function(){return _})),o.d(n,"l",(function(){return v})),o.d(n,"g",(function(){return k})),o.d(n,"d",(function(){return y})),o.d(n,"b",(function(){return O})),o.d(n,"c",(function(){return j})),o.d(n,"a",(function(){return S})),o.d(n,"q",(function(){return w})),o.d(n,"p",(function(){return C})),o.d(n,"v",(function(){return x}));var e=o("b775"),a=o("6dab");o.d(n,"w",(function(){return a["i"]}));var r=o("d74f");o.d(n,"k",(function(){return r["i"]}));var i=o("3b38");o.d(n,"f",(function(){return i["a"]}));var c="/goodscoupon/admin";function u(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/lists.html"),method:"get",params:t})}function s(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/receive.html"),method:"post",data:t})}function d(t,n){return Object(e["a"])({url:"".concat(c,"/goodscoupon/deleteMemberCoupon.html?coupon_id=").concat(n),method:"post",data:t})}function l(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/export.html"),method:"post",data:t,responseType:"blob"})}function p(t){return Object(e["a"])({url:"".concat(c,"/goodsCouponRule/list.html"),method:"get",params:t})}function f(t){return Object(e["a"])({url:"".concat(c,"/goodsCouponRule/add.html"),method:"post",data:t})}function m(t){return Object(e["a"])({url:"".concat(c,"/goodsCouponRule/detailList.html"),method:"get",params:t})}function h(t){return Object(e["a"])({url:"".concat(c,"/goodsCouponRule/stopped.html"),method:"post",data:t})}function b(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/shutDown.html"),method:"post",data:t})}function g(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/sendPageData.html"),method:"get",params:t})}function _(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/sendToSelectedMember.html"),method:"post",data:t})}function v(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/goodsData.html"),method:"get",params:t})}function k(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/deleteGoods.html"),method:"post",data:t})}function y(t){return Object(e["a"])({url:"".concat(c,"/goodscoupon/addGoods.html"),method:"post",data:t})}function O(t){return Object(e["a"])({url:"/admin_plus/AddonGoodscoupon/detail",method:"post",data:t})}function j(t){return Object(e["a"])({url:"/admin_plus/AddonGoodscoupon/lists",method:"post",data:t})}function S(t){return Object(e["a"])({url:"/admin_plus/AddonGoodsCouponRule/detail",method:"post",data:t})}function w(t){return Object(e["a"])({url:"/admin_plus/AddonGoodscoupon/sendPage",method:"post",data:t})}function C(t){return Object(e["a"])({url:"/goodscoupon/admin/goodsCouponRule/selectGoodsCoupon.html",method:"post",data:t})}function x(t){return Object(e["a"])({url:"/goodscoupon/admin/goodsCouponRule/tokenGetTempList.html",method:"post",data:t})}},"6dab":function(t,n,o){"use strict";o.d(n,"g",(function(){return a})),o.d(n,"i",(function(){return r})),o.d(n,"a",(function(){return i})),o.d(n,"h",(function(){return c})),o.d(n,"f",(function(){return u})),o.d(n,"d",(function(){return s})),o.d(n,"c",(function(){return d})),o.d(n,"e",(function(){return l})),o.d(n,"b",(function(){return p}));var e=o("b775");function a(t){return Object(e["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:t})}function r(t){return Object(e["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function i(t){return Object(e["a"])({url:"/topic/admin/topic/add.html",method:"post",data:t})}function c(t){return Object(e["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:t})}function u(t){return Object(e["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:t})}function s(t){return Object(e["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:t})}function d(t){return Object(e["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:t})}function l(t){return Object(e["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:t})}function p(t){return Object(e["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:t})}},"9c95":function(t,n,o){"use strict";o.r(n);var e=function(){var t=this,n=t.$createElement,o=t._self._c||n;return o("div",{staticClass:"app-container"},[t.showSearch?o("div",{staticClass:"filter-container"},[o("formQuery",{staticClass:"mb-20",attrs:{baseConfig:t.baseConfig,config:t.formConfig,options:t.formopts},model:{value:t.form,callback:function(n){t.form=n},expression:"form"}}),t._v(" "),o("div",{staticClass:"flex-b-c buttons"},[o("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(n){return t.handleQuery()}}},[t._v("搜索")]),t._v(" "),o("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:t.handleReset}},[t._v("重置")])],1)],1):t._e(),t._v(" "),o("div",{staticClass:"middle"},[o("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleClick},model:{value:t.tabsIndex,callback:function(n){t.tabsIndex=n},expression:"tabsIndex"}},t._l(t.formopts.activeStateOpt,(function(t,n){return o("el-tab-pane",{key:n,attrs:{label:t.label,name:t.value}})})),1)],1),t._v(" "),o("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-list"},[o("div",{staticClass:"btns"},[o("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(n){return t.goSkip("/market/platform/coupon/add")}}},[t._v("添加优惠券")]),t._v(" "),o("el-button",{attrs:{plain:"",size:"small"},on:{click:function(n){return t.goSkip("/market/platform/coupon/record")}}},[t._v("全部领取记录")]),t._v(" "),o("el-button",{attrs:{plain:"",size:"small"},on:{click:function(n){return t.goSkip("/market/platform/coupon/autorule")}}},[t._v("自动派券规则")])],1),t._v(" "),o("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:t.showSearch,options:t.options,columns:t.couponListColumns,data:t.list},on:{toggleSearch:t.toggleSearch,"update:showSearch":function(n){t.showSearch=n},"update:show-search":function(n){t.showSearch=n},onSearch:t.getTableList},scopedSlots:t._u([{key:"money",fn:function(n){var o=n.row;return[t._v("\n                满"+t._s(o.at_least)+"减"+t._s(o.money)+"\n            ")]}},{key:"max_fetch",fn:function(n){var o=n.row;return[t._v("\n                "+t._s(o.max_fetch)+"张/人\n            ")]}},{key:"end_time",fn:function(n){var o=n.row;return[t._v("\n                失效期："+t._s(t._f("parseTime")(o.end_time))+"\n            ")]}},{key:"start_time",fn:function(n){var o=n.row;return[t._v("\n                "+t._s(t._f("parseTime")(o.start_time))+"\n            ")]}},{key:"over_time",fn:function(n){var o=n.row;return[t._v("\n                "+t._s(t._f("parseTime")(o.over_time))+"\n            ")]}},{key:"use_scenario",fn:function(n){var o=n.row;return[t._v("\n                "+t._s(t.getUseScenario(o.use_scenario))+"\n            ")]}},{key:"privacy_status",fn:function(n){var o=n.row;return[t._v("\n                "+t._s(t.getProvacyStatus(o.privacy_status))+"\n            ")]}},{key:"action",fn:function(n){var e=n.row,a=e.goodscoupon_type_id,r=e.use_scenario,i=e.status,c=e.goodscoupon_name;return[o("div",{staticClass:"actions"},[o("el-button",{attrs:{type:"text"},on:{click:function(n){return t.goSkip("/market/platform/coupon/issuing",{goodscoupon_type_id:a})}}},[t._v("主动发券")]),t._v(" "),o("el-button",{attrs:{type:"text"},on:{click:function(n){t.dialogVisible=!0}}},[t._v("复制路径")]),t._v(" "),o("el-button",{attrs:{type:"text"},on:{click:function(n){return t.goSkip("/market/platform/coupon/record",{goodscoupon_type_id:a,goodscoupon_name:c})}}},[t._v("领取记录")]),t._v(" "),3==r||4==r?o("el-button",{attrs:{type:"text"},on:{click:function(n){return t.goSkip("/market/platform/coupon/goods",{goodscoupon_type_id:a})}}},[t._v("商品管理")]):t._e(),t._v(" "),1==i?o("el-button",{attrs:{type:"text"},on:{click:function(n){return t.early(a)}}},[t._v("提前结束")]):t._e(),t._v(" "),1==i||2==i?o("el-button",{attrs:{type:"text"},on:{click:function(n){return t.goSkip("/market/platform/coupon/details",{goodscoupon_type_id:a})}}},[t._v("详情")]):t._e()],1)]}}])}),t._v(" "),o("el-dialog",{attrs:{title:"复制路径",visible:t.dialogVisible,width:"60%"},on:{"update:visible":function(n){t.dialogVisible=n}}},[o("div",{staticClass:"clipboard"},[o("span",[t._v("小程序路径: "+t._s(t.miniProgramPath)+" ")]),t._v(" "),o("el-button",{attrs:{plain:"",size:"mini"},on:{click:function(n){return t.copy("miniProgramPath")}}},[t._v("复制")])],1),t._v(" "),o("br"),t._v(" "),o("div",{staticClass:"clipboard"},[t._v("\n                h5路径: "+t._s(t.h5Path)+"\n                "),o("el-button",{attrs:{plain:"",size:"mini"},on:{click:function(n){return t.copy("h5Path")}}},[t._v("复制")])],1)])],1)])},a=[],r=o("5530"),i=(o("7f7f"),o("7514"),o("c71b")),c=o("7f88"),u=o("b885"),s=o("2868"),d=o("6229"),l={components:{FormQuery:u["d"]},data:function(){return{dialogVisible:!1,baseConfig:{labelWidth:"120px"},form:{},formopts:{activeStateOpt:i["c"],useScopeOpt:i["O"]},couponListColumns:s["d"],formConfig:c["a"],showSearch:!0,tabsIndex:0,options:{page:1,page_size:10,total:0},loading:!1,list:[],miniProgramPath:"",h5Path:""}},mounted:function(){this.handleQuery(),this.onInit()},methods:{onInit:function(){var t=this;Object(d["c"])().then((function(n){var o=n.h5Path,e=n.miniProgramPath;t.h5Path=o,t.miniProgramPath=e}))},early:function(t){var n=this;this.$confirm("提前结束活动后，用户将无法再领取该活动优惠券，已被领取的优惠券也自动过期，无法使用，请确定是否提前结束。","信息",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0}).then((function(){Object(d["t"])({goodscoupon_type_id:t}).then((function(t){n.$message.success("提前结束活动成功"),n.handleQuery()}))})).catch((function(){}))},copy:function(t){this.$message.success("复制成功"),navigator.clipboard.writeText(this[t])},goSkip:function(t,n){this.$router.push({path:t,query:n})},getUseScenario:function(t){var n="";return i["O"].find((function(o){t==o.value&&(n=o.label)})),n},getProvacyStatus:function(t){return 0==t?"内部券":"公开券"},toggleSearch:function(){this.showSearch=!this.showSearch},handleClick:function(t){console.log(t),t.name>0?this.form.status=t.name:delete this.form.status,this.tabsIndex=t.index,this.handleQuery()},handleReset:function(){this.form={}},getTableList:function(t){var n=this;Object(d["n"])(Object(r["a"])(Object(r["a"])({},t),this.form)).then((function(t){var o=t.data,e=o.count,a=o.list;n.options.total=e,n.list=a,n.loading=!1}))},handleQuery:function(t){this.loading=!0;var n=t||{page:1,page_size:this.options.page_size};this.getTableList(n)}}},p=l,f=(o("3cc8"),o("2877")),m=Object(f["a"])(p,e,a,!1,null,"5cc755c0",null);n["default"]=m.exports},ba8b:function(t,n,o){}}]);