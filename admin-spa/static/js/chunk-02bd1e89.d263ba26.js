(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-02bd1e89"],{2868:function(t,e,l){"use strict";l.d(e,"d",(function(){return a})),l.d(e,"i",(function(){return i})),l.d(e,"a",(function(){return n})),l.d(e,"b",(function(){return u})),l.d(e,"c",(function(){return o})),l.d(e,"g",(function(){return s})),l.d(e,"f",(function(){return r})),l.d(e,"e",(function(){return d})),l.d(e,"j",(function(){return _})),l.d(e,"k",(function(){return c})),l.d(e,"l",(function(){return f})),l.d(e,"n",(function(){return p})),l.d(e,"p",(function(){return v})),l.d(e,"o",(function(){return b})),l.d(e,"h",(function(){return m})),l.d(e,"m",(function(){return h}));var a=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],i=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],n=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],u=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],o=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],s=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],r=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],d=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],_=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],c=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],f=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],p=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],v=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],b=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],m=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],h=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"3b38":function(t,e,l){"use strict";l.d(e,"n",(function(){return i})),l.d(e,"m",(function(){return n})),l.d(e,"p",(function(){return u})),l.d(e,"l",(function(){return o})),l.d(e,"c",(function(){return s})),l.d(e,"g",(function(){return r})),l.d(e,"f",(function(){return d})),l.d(e,"e",(function(){return _})),l.d(e,"j",(function(){return c})),l.d(e,"k",(function(){return f})),l.d(e,"i",(function(){return p})),l.d(e,"h",(function(){return v})),l.d(e,"a",(function(){return b})),l.d(e,"o",(function(){return m})),l.d(e,"b",(function(){return h})),l.d(e,"q",(function(){return g})),l.d(e,"d",(function(){return y}));var a=l("b775");function i(t){return Object(a["a"])({url:"/admin/pintuan/data.html",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/admin/pintuan/store.html",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:t})}function r(t){return Object(a["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:t})}function _(t){return Object(a["a"])({url:"/admin/pintuan/change_status",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/admin/pintuan/editStock",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/admin/pintuan/editSort",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:t})}function m(t){return Object(a["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:t})}function h(t){return Object(a["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/admin/pintuan/update.html",method:"post",data:t})}function y(t){return Object(a["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:t})}},"87e5":function(t,e,l){},"9a1c":function(t,e,l){"use strict";var a=function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"sales-data",class:{active:t.failds.length>6}},t._l(t.failds,(function(e,a){return l("div",{key:a,style:{width:"calc(100% / "+(t.failds.length>12?parseInt(t.failds.length/2):6)+")"}},[l("div",[t._v("\n            "+t._s(e.title)+"\n            "),e.subTitle?l("el-tooltip",{attrs:{content:e.subTitle,placement:"top-start"}},[l("i",{staticClass:"el-icon-question"})]):t._e(),t._v(" "),e.path?l("el-button",{attrs:{size:"mini",type:"danger",plain:""},on:{click:function(l){return t.goSkip(e.path)}}},[t._v("去看看")]):t._e()],1),t._v(" "),l("i",[t._v(t._s(t.data[e.faild[0]]))]),t._v(" "),1==e.type?l("span",{class:{active:t.getSize(e.faild)}},[t._v("\n            "+t._s(t.getSizeText(e.faild))+"\n            "),l("i",{class:t.getSizeIcon(e.faild)}),t._v(" "),l("span",[t._v(t._s(t.data[e.faild[1]])+"%")])]):t._e(),t._v(" "),2==e.type?l("span",[t._v("\n            活跃会员"),l("span",[t._v(t._s(t.data[e.faild[1]]))]),t._v("人\n        ")]):t._e(),t._v(" "),3==e.type?l("span",[t._v("\n            新增店铺"),l("span",[t._v("+"+t._s(t.data[e.faild[1]]))])]):t._e()])})),0)},i=[],n=l("3835"),u={sales:[{title:"销售总额（元）",subTitle:"平台生产全部订单的总金额，包含未支付订单金额",faild:["order_sale_amount","order_sale_amount_float_pro"],type:1},{title:"订单量",subTitle:"包含未支付订单数量",faild:["order_nums","order_nums_float_pro"],type:1},{title:"下单人数",subTitle:"包含未支付订单下单人数",faild:["persons_num","persons_num_float_pro"],type:1},{title:"支付总额（元）",subTitle:"已支付订单总金额",faild:["order_pay_amount","order_pay_amount_float_pro"],type:1},{title:"退款总额（元）",faild:["refund_amount","refund_amount_float_pro"],type:1},{title:"转化率",subTitle:"转化率＝订单量÷UV",faild:["change","change_float_pro"],type:1}],users:[{title:"会员总数",faild:["member_nums","member_nums_float_pro"],type:2},{title:"新增会员数",faild:["add_member_nums","add_member_nums_float_pro"],type:1},{title:"店铺总数",faild:["shop_nums","shop_nums_float_pro"],type:3},{title:"访客数（UV）",faild:["uv","uv_float_pro"],type:1},{title:"访问量（PV）",subTitle:"全部小程及H5序页面访问次数",faild:["pv","pv_float_pro"],type:1},{title:"客单价",subTitle:"客单价=销售额÷成交顾客数",faild:["unit_price","unit_price_float_pro"],type:1}],commodity:[{title:"商品总数",faild:["spu_total"]},{title:"上架商品数",subTitle:"全部已上架状态商品数量（含供应链同步商品）",faild:["spu_online"]},{title:"供应链选品上架",faild:["online"]},{title:"供应链异常下架",faild:["offline"],path:"/goods/quality/warning"},{title:"合作品牌数",subTitle:"已上架商品涉及的品牌数量",faild:["brand_num"]},{title:"合作供应商",subTitle:"已上架商品涉及的供应商数量",faild:["supplier_num"]}],teamwork:[{title:"开团人次",faild:["group_nums"]},{title:"成团人次",faild:["win_group_nums"]},{title:"开团人数",subTitle:"即该活动开团的用户数量，单个用户多次开团算一个用户",faild:["group_people_nums"]},{title:"参团人次",faild:["join_group_nums"]},{title:"参团人数",subTitle:"即该活动中参加别人发起拼团的人数，单个用户多次参团算一个用户",faild:["join_group_people_nums"]},{title:"活动拉新人数",subTitle:"该活动中首次参团，此前无任何活动参团记录，无普通商品下单记录的新用户",faild:["invite_new_nums"]},{title:"拼团支付金额",subTitle:"该活动中开团或参团支付的所有金额（含未中奖退款）",faild:["pay_money"]},{title:"成团率",subTitle:"成团人次/开团人次*100%",faild:["join_group_pro"]},{title:"中奖商品订单数",subTitle:"拼团活动中奖者发放商品，系统会为每个中奖者生成一个商品订单",faild:["win_order_nums"]},{title:"商品订单总金额",subTitle:"拼团中奖者的参团金额不退回，作为购买商品的交易金额。",faild:["win_order_money"]},{title:"分享次数",subTitle:"用户点击拼团分享按钮的次数（含拼团商品详情页、邀请参团页）",faild:["share_nums"]},{title:"分享人数",subTitle:"点击拼团分享按钮的人数（含拼团商品详情页、邀请参团页）",faild:["share_people_nums"]},{title:"打开分享次数",subTitle:"分享拼团链接被打开的次数（含拼团商品详情页、邀请参团页）",faild:["open_share_nums"]},{title:"打开分享人数",subTitle:"打开分享拼团链接的人数（含拼团商品详情页、邀请参团页）",faild:["open_share_people_nums"]}],reward:[{title:"中奖奖励",faild:["pintuan_winning_award"]},{title:"未中奖奖励",faild:["pintuan_not_winning_award"]},{title:"邀约成团奖励",faild:["pintuan_invite_award"]},{title:"邀请新人参团奖励",faild:["pintuan_invite_new_member_award"]},{title:"开团人奖励",faild:["head_award"]},{title:"开团人所属店主奖励",faild:["pintuan_award"]},{title:"开团人所属经理奖励",faild:["head_group_award"]},{title:"中奖人所属店主奖励",faild:["win_shop_award"]},{title:"中奖人所属经理奖励",faild:["win_manager_award"]}]},o={data:function(){return{sales:{},failds:[]}},props:["dataFailds","data"],mounted:function(){this.failds=u[this.dataFailds]},methods:{goSkip:function(t){this.$router.push(t)},getSize:function(t){var e=Object(n["a"])(t,2),l=(e[0],e[1]);return this.data[l]<0},getSizeText:function(t){var e=Object(n["a"])(t,2),l=(e[0],e[1]);return this.data[l]<0?"下降":"上升"},getRate:function(t){var e=Object(n["a"])(t,2);e[0],e[1]},getSizeIcon:function(t){var e=Object(n["a"])(t,2),l=(e[0],e[1]);return this.data[l]<0?"el-icon-caret-bottom":"el-icon-caret-top"}}},s=o,r=(l("ef00"),l("2877")),d=Object(r["a"])(s,a,i,!1,null,"69c89e7e",null);e["a"]=d.exports},a394:function(t,e,l){"use strict";l("87e5")},c71b:function(t,e,l){"use strict";l.d(e,"a",(function(){return a})),l.d(e,"i",(function(){return i})),l.d(e,"H",(function(){return n})),l.d(e,"f",(function(){return u})),l.d(e,"A",(function(){return o})),l.d(e,"x",(function(){return s})),l.d(e,"e",(function(){return r})),l.d(e,"w",(function(){return d})),l.d(e,"c",(function(){return _})),l.d(e,"O",(function(){return c})),l.d(e,"j",(function(){return f})),l.d(e,"k",(function(){return p})),l.d(e,"l",(function(){return v})),l.d(e,"T",(function(){return b})),l.d(e,"d",(function(){return m})),l.d(e,"Q",(function(){return h})),l.d(e,"p",(function(){return g})),l.d(e,"P",(function(){return y})),l.d(e,"m",(function(){return w})),l.d(e,"I",(function(){return k})),l.d(e,"L",(function(){return j})),l.d(e,"N",(function(){return O})),l.d(e,"M",(function(){return T})),l.d(e,"S",(function(){return D})),l.d(e,"s",(function(){return I})),l.d(e,"B",(function(){return x})),l.d(e,"z",(function(){return S})),l.d(e,"K",(function(){return C})),l.d(e,"C",(function(){return z})),l.d(e,"h",(function(){return q})),l.d(e,"g",(function(){return G})),l.d(e,"o",(function(){return L})),l.d(e,"G",(function(){return $})),l.d(e,"J",(function(){return P})),l.d(e,"v",(function(){return N})),l.d(e,"F",(function(){return E})),l.d(e,"r",(function(){return F})),l.d(e,"b",(function(){return V})),l.d(e,"q",(function(){return A})),l.d(e,"R",(function(){return Q})),l.d(e,"u",(function(){return J})),l.d(e,"t",(function(){return R})),l.d(e,"D",(function(){return B})),l.d(e,"E",(function(){return H})),l.d(e,"y",(function(){return U})),l.d(e,"n",(function(){return K}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],i=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],n=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],u=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],s=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],r=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],_=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],c=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],f=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],p=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],v=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],b=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],m=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],h=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],O=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],T=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],D=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],I=[{value:"0",label:"参团"},{value:"1",label:"团长"}],x=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],S=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],C=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],z=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],q=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],G=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],L=[{value:"1",label:"是"},{value:"2",label:"否"}],$=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],P=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],N=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],E=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],F=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],V=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],A=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],Q=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],J=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],R=[{label:"按订单编号",value:"order_no"}],B=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],H=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],U=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],K=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},d6f7:function(t,e,l){"use strict";l.r(e);var a=function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[l("div",{staticClass:"details"},[l("div",{staticClass:"edit_title"},[t._v("拼团数据")]),t._v(" "),l("el-form",{attrs:{inline:"","label-width":"120px"}},[l("el-form-item",{attrs:{label:"活动名称："}},[t._v("\n                "+t._s(t.pintuan.pintuan_name)+"\n            ")]),t._v(" "),l("el-form-item",{attrs:{label:"活动状态："}},[t._v("\n                "+t._s(t.pintuan.status_text)+"\n            ")]),t._v(" "),l("el-form-item",{attrs:{label:"活动时间："}},[t._v("\n                "+t._s(t.pintuan.start_time)+" 至 "+t._s(t.pintuan.end_time)+"\n            ")]),t._v(" "),l("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"统计日期："}},[l("el-date-picker",{attrs:{size:"small",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.totalTime,callback:function(e){t.totalTime=e},expression:"totalTime"}}),t._v(" "),l("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.handleQuery()}}},[t._v("统计")])],1)],1)],1),t._v(" "),l("div",{staticClass:"details"},[l("div",{staticClass:"edit_title"},[t._v("\n            拼团数据统计\n            "),l("span",[t._v("2023-11-20之前的拼团活动数据部分未能统计，显示为0，请注意")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small"},on:{click:function(e){return t.goSkip("/market/teamwork/openGroup",{pintuan_name:t.pintuan.pintuan_name})}}},[t._v("查看开团列表")])],1),t._v(" "),l("sales-data",{staticClass:"sales",attrs:{data:t.statisticsData,dataFailds:"teamwork"}})],1),t._v(" "),l("div",{staticClass:"status details areas"},[l("span",[t._v("机器人开团数："),l("b",[t._v(t._s(t.robotData.robot_open_nums))])]),t._v(" "),l("span",[t._v("机器开团成团数："),l("b",[t._v(t._s(t.robotData.robot_group_nums))])]),t._v(" "),l("span",[t._v("机器开团成团率："),l("b",[t._v(t._s(t.robotData.robot_group_pro)+" %")])]),t._v(" "),l("span",[t._v("真人参团数："),l("b",[t._v(t._s(t.robotData.member_join_nums))])]),t._v(" "),l("span",[t._v("机器人开/参团金额："),l("b",[t._v(t._s(t.robotData.robot_tuan_money)+"（机器人不中奖，均已退回机器人账户）")])])]),t._v(" "),l("div",{staticClass:"details"},[l("div",{staticClass:"edit_title"},[t._v("\n            奖励数据统计\n            "),l("span",[t._v("累计发放奖励"+t._s(t.statisticsData.pintuan_award)+"元")]),t._v(" "),l("el-button",{attrs:{plain:"",size:"small"},on:{click:function(e){return t.goSkip("/market/teamwork/add")}}},[t._v("查看活动配置")])],1),t._v(" "),l("div",{staticClass:"status "},[l("sales-data",{staticClass:"sales reward",attrs:{data:t.statisticsData,dataFailds:"reward"}})],1)]),t._v(" "),l("div",{staticClass:"details table"},[l("div",{staticClass:"edit_title"},[t._v("\n            活动商品数据（共添加 "),l("i",[t._v(" "+t._s(t.pintuan.goods_nums)+" ")]),t._v(" 个活动商品，当前上架商品 "),l("i",[t._v(" "+t._s(t.pintuan.up_goods_nums)+" ")]),t._v(" 个）\n        ")]),t._v(" "),l("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-list"},[l("div",{staticClass:"btns"},[l("el-button",{attrs:{plain:"",size:"small"},on:{click:t.exportGoodsData}},[t._v("导出商品数据")])],1),t._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{hideRefresh:!0,options:t.options,columns:t.columns,data:t.list},on:{onSearch:t.getTableList},scopedSlots:t._u([{key:"action",fn:function(e){e.row;return[l("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/market/teamwork/goods")}}},[t._v("编辑商品")])]}}])})],1)])])},i=[],n=l("5530"),u=(l("28a5"),l("9a1c")),o=l("2868"),s=l("c71b"),r=l("3b38"),d={components:{salesData:u["a"]},data:function(){return{totalTime:[],list:[],loading:!1,columns:o["m"],options:{page:1,page_size:10,total:0},teamwordOpt:s["L"],pintuan:{},robotData:{},statisticsData:{},query:this.$route.query}},mounted:function(){this.onInit(),this.handleQuery()},methods:{onInit:function(){var t=this;this.loading=!0,Object(r["o"])({pintuan_id:this.query.pintuan_id}).then((function(e){var l=e.data,a=l.pintuan,i=l.robotData,n=l.statisticsData;t.pintuan=a,t.robotData=i,t.statisticsData=n,t.loading=!1}))},exportGoodsData:function(){Object(r["d"])({pintuan_id:this.query.pintuan_id}).then((function(t){var e=t.data,l=e.path.split("/");console.log(l.at(-1));var a=document.createElement("a");a.download=l.at(-1),a.href=e,a.click()}))},goSkip:function(t,e){this.$router.push({path:t,query:Object(n["a"])(Object(n["a"])({},this.$route.query),e)})},getTableList:function(t){var e=this;Object(r["g"])(Object(n["a"])(Object(n["a"])({},t),{},{pintuan_id:this.query.pintuan_id})).then((function(t){var l=t.data,a=l.count,i=l.list;e.options.total=a,e.list=i,e.loading=!1}))},handleQuery:function(t){console.log(t),this.loading=!0;var e=t||{page:1,page_size:this.options.page_size};this.totalTime&&(e.start_date=this.$format(this.totalTime[0],"{y}-{m}-{d}"),e.end_date=this.$format(this.totalTime[1],"{y}-{m}-{d}")),this.getTableList(e)}}},_=d,c=(l("a394"),l("2877")),f=Object(c["a"])(_,a,i,!1,null,"00d85b84",null);e["default"]=f.exports},ef00:function(t,e,l){"use strict";l("fa61")},fa61:function(t,e,l){}}]);