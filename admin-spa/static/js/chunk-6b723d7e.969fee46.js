(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b723d7e"],{"0872":function(e,t,a){"use strict";a("7b46")},"2f21":function(e,t,a){"use strict";var o=a("79e5");e.exports=function(e,t){return!!e&&o((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},"33a6":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"选择商品分类",visible:e.dialogVisible,top:"100px",width:"810px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"classify"},[a("div",{staticClass:"fist-level"},e._l(e.fistLevels,(function(t,o){return a("span",{key:o,class:{danger:t.category_name==e.titles[0]},on:{click:function(a){e.getClassify("seconddarys",2,t.category_id),e.teriarys=[],e.setTitle(t,0)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"second-level"},e._l(e.seconddarys,(function(t,o){return a("span",{key:o,class:{danger:t.category_name==e.titles[1]},on:{click:function(a){e.getClassify("teriarys",3,t.category_id),e.setTitle(t,1)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"teriary-level"},e._l(e.teriarys,(function(t,o){return a("span",{key:o,class:{danger:t.category_name==e.titles[2]},on:{click:function(a){return e.setTitle(t,2)}}},[e._v("\n                "+e._s(t.category_name)+"\n            ")])})),0)]),e._v(" "),a("div",{staticClass:"current"},[e._v("\n        您当前选择的是：\n        "+e._s(e.titles[0])+" "),e.titles[1]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[1])+" "),e.titles[2]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[2])+"\n    ")]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submit}},[e._v("保 存")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("关 闭")])],1)])},r=[],l=(a("28a5"),a("d74f")),i={data:function(){return{dialogVisible:!1,fistLevels:[],seconddarys:[],teriarys:[],titles:[],cateIds:[]}},props:["value"],mounted:function(){this.value&&(this.titles=this.value.split("/")),this.getClassify()},methods:{init:function(e){this.dialogVisible=!0,e&&(this.getClassify("seconddarys",2,e[0]),this.getClassify("teriarys",3,e[1]),this.cateIds=e)},getClassify:function(e,t,a){var o=this,r={level:t||1,pid:a||0};Object(l["i"])(r).then((function(t){var a=t.data;o[e||"fistLevels"]=a}))},setTitle:function(e,t){var a=e.category_name,o=e.category_id;this.titles[t]=a,this.cateIds[t]=o,t<2&&this.titles.splice(t+1,this.titles.length),this.$forceUpdate()},submit:function(){this.$emit("onClassify",this.cateIds),this.$emit("input",this.titles.join("/")),this.dialogVisible=!1}}},n=i,s=(a("564d"),a("2877")),u=Object(s["a"])(n,o,r,!1,null,"260665e1",null);t["a"]=u.exports},"3f5e":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return i}));var o=a("b775");function r(e){return Object(o["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function l(e){return Object(o["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(o["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,a){"use strict";var o=a("a18c"),r={inserted:function(e,t,a){var r=t.value,l=o["a"].app._route.meta&&o["a"].app._route.meta.permissions;l.indexOf(r)<0&&e.parentNode&&e.parentNode.removeChild(e)}},l=function(e){e.directive("permission",r)};window.Vue&&(window["permission"]=r,Vue.use(l)),r.install=l;t["a"]=r},"55dd":function(e,t,a){"use strict";var o=a("5ca1"),r=a("d8e8"),l=a("4bf8"),i=a("79e5"),n=[].sort,s=[1,2,3];o(o.P+o.F*(i((function(){s.sort(void 0)}))||!i((function(){s.sort(null)}))||!a("2f21")(n)),"Array",{sort:function(e){return void 0===e?n.call(l(this)):n.call(l(this),r(e))}})},"564d":function(e,t,a){"use strict";a("c16a")},"5d78":function(e,t,a){"use strict";a("a7d2")},6396:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,a,o){return e/=o/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=l(),n=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=s;var l=Math.easeInOutQuad(u,i,n,t);r(l),u<t?o(e):a&&"function"===typeof a&&a()};c()}},6724:function(e,t,a){"use strict";a("8d41");var o={bind:function(e,t){e.addEventListener("click",(function(a){var o=Object.assign({},t.value),r=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},o),l=r.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var i=l.getBoundingClientRect(),n=l.querySelector(".waves-ripple");switch(n?n.className="waves-ripple":(n=document.createElement("span"),n.className="waves-ripple",n.style.height=n.style.width=Math.max(i.width,i.height)+"px",l.appendChild(n)),r.type){case"center":n.style.top=i.height/2-n.offsetHeight/2+"px",n.style.left=i.width/2-n.offsetWidth/2+"px";break;default:n.style.top=(a.pageY-i.top-n.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",n.style.left=(a.pageX-i.left-n.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return n.style.backgroundColor=r.color,n.className="waves-ripple z-active",!1}}),!1)}},r=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(r)),o.install=r;t["a"]=o},"7b46":function(e,t,a){},"8d41":function(e,t,a){},9969:function(e,t,a){"use strict";a("f0a4")},a216:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("div",{staticClass:"tabs"},[e._l(e.tabs,(function(t,o){var r=t.title,l=t.code;return a("span",{key:o,class:{active:e.tabIndex==l}},[e._v(e._s(r))])})),e._v(" "),a("div",[e.tabIndex>1?a("el-button",{attrs:{size:"mini",plain:""},on:{click:function(t){return e.switchTabs(e.tabIndex-1)}}},[e._v("上一步")]):e._e(),e._v(" "),e.tabIndex<3?a("el-button",{attrs:{size:"mini",plain:"",type:"danger"},on:{click:function(t){return e.switchTabs(e.tabIndex+1,!0)}}},[e._v("下一步")]):e._e(),e._v(" "),3==e.tabIndex?a("el-button",{attrs:{size:"mini",loading:e.loading,type:"danger"},on:{click:e.onSave}},[e._v("保存")]):e._e()],1)],2),e._v(" "),Object.keys(e.details).length?a("div",{staticClass:"cont"},[a("o-info",{directives:[{name:"show",rawName:"v-show",value:1==e.tabIndex,expression:"tabIndex == 1"}],ref:"edit_1",attrs:{details:e.details},model:{value:e.infoForm,callback:function(t){e.infoForm=t},expression:"infoForm"}}),e._v(" "),a("o-stock",{directives:[{name:"show",rawName:"v-show",value:2==e.tabIndex,expression:"tabIndex == 2"}],ref:"edit_2",attrs:{details:e.details},model:{value:e.stockForm,callback:function(t){e.stockForm=t},expression:"stockForm"}}),e._v(" "),a("o-details",{directives:[{name:"show",rawName:"v-show",value:3==e.tabIndex,expression:"tabIndex == 3 "}],ref:"edit_3",attrs:{details:e.details},model:{value:e.detailsForm,callback:function(t){e.detailsForm=t},expression:"detailsForm"}})],1):e._e()])},r=[],l=(a("28a5"),a("5530")),i=a("c7eb"),n=(a("96cf"),a("1da1")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[e._v("基础信息")]),e._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",sytle:"width:60%",rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"原商品名称："}},[e._v("\n            "+e._s(e.info.supply_goods_name)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"原商品编码："}},[e._v("\n            "+e._s(e.info.supply_pro_no)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"商品名称：",prop:"goods_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入商品名称"},model:{value:e.form.goods_name,callback:function(t){e.$set(e.form,"goods_name",t)},expression:"form.goods_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品分类：",prop:"category_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入商品分类",readonly:""},model:{value:e.form.category_name,callback:function(t){e.$set(e.form,"category_name",t)},expression:"form.category_name"}},[a("el-button",{attrs:{slot:"append"},on:{click:e.favour},slot:"append"},[e._v("选择")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"促销语："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入促销语"},model:{value:e.form.introduction,callback:function(t){e.$set(e.form,"introduction",t)},expression:"form.introduction"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"关键词："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入关键词"},model:{value:e.form.keywords,callback:function(t){e.$set(e.form,"keywords",t)},expression:"form.keywords"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入虚拟销量",type:"number"},model:{value:e.form.virtual_sale_num,callback:function(t){e.$set(e.form,"virtual_sale_num",t)},expression:"form.virtual_sale_num"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"单位："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入单位",disabled:""},model:{value:e.form.unit,callback:function(t){e.$set(e.form,"unit",t)},expression:"form.unit"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品品牌："}},[a("el-select",{model:{value:e.form.brand_id,callback:function(t){e.$set(e.form,"brand_id",t)},expression:"form.brand_id"}},e._l(e.details.brand_list,(function(e,t){return a("el-option",{key:t,attrs:{value:e.brand_id,label:e.brand_name}})})),1)],1),e._v(" "),e.details.is_install_supply?a("el-form-item",{attrs:{label:"供应商：",prop:"supplier_name"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入供应商",disabled:""},model:{value:e.form.supplier_name,callback:function(t){e.$set(e.form,"supplier_name",t)},expression:"form.supplier_name"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"商品标签："}},[a("el-radio-group",{on:{change:e.handleChange},model:{value:e.form.tag,callback:function(t){e.$set(e.form,"tag",t)},expression:"form.tag"}},e._l(e.details.tag_list,(function(t,o){return a("el-radio",{key:o,attrs:{label:t.id}},[e._v(e._s(t.tag_name))])})),1),e._v(" "),a("div",{staticClass:"danger"},[e._v("迈豆专区的商品不进行原店主分佣方式分佣，只按照新的迈豆专区佣金分配方式分佣")])],1),e._v(" "),a("el-form-item",{attrs:{label:"微信支付方式："}},[a("el-radio-group",{model:{value:e.form.use_pay_type,callback:function(t){e.$set(e.form,"use_pay_type",t)},expression:"form.use_pay_type"}},e._l(e.payList,(function(t,o){return a("el-radio",{key:o,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1),e._v(" "),a("div",{staticClass:"danger"},[e._v("拼团不受此设置控制，默认使用汇付天下")])],1),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("其他信息")]),e._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入单位",type:"number"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否上架：",prop:"goods_state"}},[a("el-radio-group",{model:{value:e.form.goods_state,callback:function(t){e.$set(e.form,"goods_state",t)},expression:"form.goods_state"}},[a("el-radio",{attrs:{label:1}},[e._v("立即上架")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("暂不上架")])],1)],1)],1),e._v(" "),a("classify-dialog",{ref:"classify",on:{onClassify:e.onClassify},model:{value:e.form.category_name,callback:function(t){e.$set(e.form,"category_name",t)},expression:"form.category_name"}})],1):e._e()},u=[],c=(a("7514"),a("55dd"),a("c71b")),d=a("33a6"),p={components:{classifyDialog:d["a"]},data:function(){return{form:{},brandOpt:[],payList:c["y"],rules:{goods_name:[{required:!0,message:"商品名称不能为空",trigger:"blur"},{max:60,message:"商品名称不能超过60个字符",trigger:"blur"}],introduction:[{max:100,message:"促销语不能超过100个字符",trigger:"blur"}],category_name:[{required:!0,message:"商品分类不能为空",trigger:"change"}],supplier_name:[{required:!0,message:"供应商不能为空",trigger:"change"}],goods_state:[{required:!0,message:"是否上架不能为空",trigger:"change"}]}}},computed:{info:function(){if(this.details.goods_info){var e=this.details.goods_info,t=e.goods_spec_format,a=e.goods_id,o=e.commission_rate,r=e.batch_operation_sku,l=e.supplier_id,i=e.goods_state,n=e.sort,s=e.category_id,u=e.category_id_1,c=e.category_id_2,d=e.category_id_3,p=e.use_pay_type,m=e.brand_name,f=e.brand_id,_=e.unit,v=e.virtual_sale_num,b=e.keywords,g=e.supply_goods_name,h=e.supply_pro_no,y=e.site_name,k=e.goods_name,w=e.introduction,x=e.category_name;return this.form={tag:this.details.goods_tag.id||"",supplier_name:y,introduction:w,category_name:x,goods_name:k,supply_goods_name:g,supply_pro_no:h,keywords:b,virtual_sale_num:v,unit:_,brand_id:f,brand_name:m,use_pay_type:p,sort:n,goods_state:i,category_id:s,category_id_1:u,category_id_2:c,category_id_3:d,supplier_id:l,batch_operation_sku:r||"",commission_rate:o,goods_id:a,goods_spec_format:t||""},this.details.goods_info}return{}}},props:{details:{type:Object,default:function(){}}},methods:{handleChange:function(){this.$forceUpdate()},favour:function(){this.$refs.classify.init([this.info.category_id_1,this.info.category_id_2,this.info.category_id_3])},changeStatus:function(e){var t="";return c["n"].find((function(a){a.value==e&&(t=a.label)})),t},getTags:function(e){var t="";return this.details.tag_list&&this.details.tag_list.find((function(a){if(a.id==e)return t=a.tag_name})),t},changePay:function(e){var t="";return c["y"].find((function(a){a.value==e&&(t=a.label)})),t},setVaild:function(e){var t=this;console.log(this.$refs.form),this.$refs.form.validate((function(a){a&&(t.$emit("input",t.form),e())}))},onClassify:function(e){var t=this;console.log(e),e.map((function(e,a){t.form["category_id_".concat(a+1)]=e})),this.form.category_id=e[e.length-1]}}},m=p,f=(a("bfa4"),a("2877")),_=Object(f["a"])(m,s,u,!1,null,"14b9e1fd",null),v=_.exports,b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[e._v("价格库存")]),e._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",model:e.form,rules:e.rules}},[a("el-form-item",{attrs:{label:"启用多规格："}},[a("el-switch",{attrs:{disabled:""},model:{value:e.form.spec_type,callback:function(t){e.$set(e.form,"spec_type",t)},expression:"form.spec_type"}})],1),e._v(" "),e.form.spec_type?[e._l(e.goods_spec_format,(function(t,o){return a("el-form-item",{key:o,attrs:{label:"规格项"+(o+1)}},[a("el-input",{staticStyle:{width:"150px"},attrs:{disabled:!0},model:{value:t.spec_name,callback:function(a){e.$set(t,"spec_name",a)},expression:"item.spec_name"}}),e._v(" "),a("div",e._l(t.value,(function(t,o){return a("el-tag",{key:o,staticStyle:{"margin-right":"10px"},attrs:{type:"info"}},[t.image?a("el-image",{staticStyle:{width:"20px",height:"20px"},attrs:{src:t.image}}):e._e(),e._v(e._s(t.spec_value_name))],1)})),1)],1)})),e._v(" "),a("el-form-item",{attrs:{label:"批量操作："}},[e.batchEditVisible?a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:e.batchEditPlaceholder},model:{value:e.batchEditValue,callback:function(t){e.batchEditValue=t},expression:"batchEditValue"}}),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.toBatchEditConfirm}},[e._v("确定")]),e._v(" "),a("el-button",{on:{click:e.toBatchEditCancel}},[e._v("取消")])],1):a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("spec_name","副标题")}}},[e._v("副标题")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("cost_price","供应商价格")}}},[e._v("供应商价格")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("price","供货价")}}},[e._v("供货价")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("market_price","原价")}}},[e._v("原价")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("sales_price","销售价")}}},[e._v("销售价")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("stock","库存")}}},[e._v("库存")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){e.toBatchEditShow("weight","重量(kg)")}}},[e._v("重量(kg)")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){e.toBatchEditShow("volume","体积(m³)")}}},[e._v("体积(m³)")]),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.toBatchEditShow("sku_no","商品编码")}}},[e._v("商品编码")])],1),e._v(" "),a("el-table",{attrs:{border:"",data:e.form.goods_sku_data,"header-cell-class-name":e.handleColumnAsterisk,"span-method":e.objectSpanMethod}},[a("el-table-column",{attrs:{label:"商品规格"}},[e._l(e.skuColumns.filter((function(e){return e.is_spec})),(function(t,o){return[a("el-table-column",{attrs:{label:t.label,prop:t.prop},scopedSlots:e._u([{key:"default",fn:function(o){return["text"==t.type?a("span",[e._v(e._s(o.row[t.prop]))]):e._e()]}}],null,!0)})]}))],2),e._v(" "),e._l(e.skuColumns.filter((function(e){return!e.is_spec})),(function(t,o){return[a("el-table-column",{attrs:{label:t.label,prop:t.prop},scopedSlots:e._u([{key:"default",fn:function(o){return["input"==t.type?a("el-input",{attrs:{placeholder:t.label,disabled:t.disabled},on:{input:function(t){return e.changeEvent(o.row,o.$index,t)}},model:{value:o.row[t.prop],callback:function(a){e.$set(o.row,t.prop,a)},expression:"scope.row[item.prop]"}}):"text"==t.type?a("span",[e._v(e._s(o.row[t.prop]))]):"image"==t.type?[a("el-image",{attrs:{src:o.row[t.prop],fit:"contain"}})]:"select"==t.type?a("el-checkbox",{on:{change:function(t){return e.checkboxChange(o.row,o.$index)}},model:{value:o.row[t.prop],callback:function(a){e.$set(o.row,t.prop,a)},expression:"scope.row[item.prop]"}}):e._e()]}}],null,!0)})]}))],2)],1)]:[a("el-form-item",{attrs:{label:"销售价：",prop:"sales_price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入销售价"},model:{value:e.form.sales_price,callback:function(t){e.$set(e.form,"sales_price",t)},expression:"form.sales_price"}}),e._v(" "),a("span",[e._v("元")])],1),e._v(" "),a("el-form-item",{attrs:{label:"店主返佣比例：",prop:"reward_shop_rate"}},[a("el-input",{staticStyle:{width:"150px"},attrs:{disabled:""},model:{value:e.form.reward_shop_rate,callback:function(t){e.$set(e.form,"reward_shop_rate",t)},expression:"form.reward_shop_rate"}}),e._v(" % "),a("span",[e._v("(自动计算=(销售价/供货价-1)*100)")])],1),e._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例：",prop:"reward_company_rate"}},[a("el-input",{staticStyle:{width:"150px"},attrs:{disabled:""},model:{value:e.form.reward_company_rate,callback:function(t){e.$set(e.form,"reward_company_rate",t)},expression:"form.reward_company_rate"}}),e._v("% "),a("span",[e._v("(自动计算=(供货价/供应商价格-1)*100)")])],1),e._v(" "),a("el-form-item",{attrs:{label:"供应商价格：",prop:"cost_price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入供应商价格",disabled:""},model:{value:e.form.cost_price,callback:function(t){e.$set(e.form,"cost_price",t)},expression:"form.cost_price"}}),e._v("元\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"供货价：",prop:"price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入供货价"},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}}),e._v("\n          元\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"原价：",prop:"market_price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入原价"},model:{value:e.form.market_price,callback:function(t){e.$set(e.form,"market_price",t)},expression:"form.market_price"}}),e._v("\n          元\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"重量：",prop:"weight"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入重量",disabled:""},model:{value:e.form.weight,callback:function(t){e.$set(e.form,"weight",t)},expression:"form.weight"}}),e._v("kg\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"体积：",prop:"volume"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入体积",disabled:""},model:{value:e.form.volume,callback:function(t){e.$set(e.form,"volume",t)},expression:"form.volume"}}),e._v("m3\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"商品编码：",prop:"sku_no"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入商品编码"},model:{value:e.form.sku_no,callback:function(t){e.$set(e.form,"sku_no",t)},expression:"form.sku_no"}})],1)],e._v(" "),a("el-form-item",{attrs:{label:"总库存：",prop:"goods_stock"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入总库存",type:"number",disabled:e.form.spec_type},model:{value:e.form.goods_stock,callback:function(t){e.$set(e.form,"goods_stock",t)},expression:"form.goods_stock"}}),e._v(" / 件\n      ")],1),e._v(" "),a("el-form-item",{attrs:{label:"库存预警：",prop:"goods_stock_alarm"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入总库存",type:"number"},model:{value:e.form.goods_stock_alarm,callback:function(t){e.$set(e.form,"goods_stock_alarm",t)},expression:"form.goods_stock_alarm"}}),e._v(" / 件\n        "),a("span",[e._v(" (设置最低库存预警值。当库存低于预警值时商家中心商品列表页库存列红字提醒，0为不预警。)")])],1),e._v(" "),a("el-form-item",{attrs:{label:"是否免邮："}},[e._v("\n        "+e._s(1==e.form.is_free_shipping?"是":"否")+"\n      ")])],2)],1):e._e()},g=[],h=a("b885"),y=(a("8338"),a("ed08")),k={number:/^\d{0,10}$/,digit:/^\d{0,10}(.?\d{0,2})$/},w={reward_shop_rate:function(e,t,a){isNaN(t)?a(new Error("[比例]格式输入错误")):a()},reward_company_rate:function(e,t,a){isNaN(t)?a(new Error("请输入公司返佣比例")):t<0||0==t||0==t?a(new Error("返佣比例输入错误")):a()},price:function(e,t,a){isNaN(t)||!k.digit.test(t)?a(new Error("[供货价]格式输入错误")):a()},market_price:function(e){return function(t,a,o){isNaN(a)||!k.digit.test(a)?o(new Error("[原价]格式输入错误")):100*a<=100*(e.form.price-0)?o(new Error("[原价]必须大于供货价")):o()}},cost_price:function(e,t,a){isNaN(t)||!k.digit.test(t)?a(new Error("[供应商价格]格式输入错误")):a()},weight:function(e,t,a){t.length>0&&(isNaN(t)||!k.digit.test(t))?a(new Error("[重量(kg)]格式输入错误")):a()},volume:function(e,t,a){t.length>0&&(isNaN(t)||!k.digit.test(t))?a(new Error("[体积(m³)]格式输入错误")):a()},goods_stock:function(e,t,a){isNaN(t)||!k.number.test(t)?a(new Error("[库存]格式输入错误")):a()},goods_stock_alarm:function(e){return function(t,a,o){if(a.length>0){var r=parseInt(e.form.goods_stock);isNaN(a)||!k.number.test(a)?o(new Error("[库存预警]格式输入错误")):0!=parseInt(a)&&parseInt(a)===r?o(new Error("[库存预警]不能等于库存数量")):parseInt(a)>r?o(new Error("[库存预警]不能超过库存数量")):o()}else o()}},sku_price:function(e,t,a){return 0==t.length?(a(new Error("请输入供货价")),"请输入供货价"):isNaN(t)||!k.digit.test(t)?(a(new Error("[供货价]格式输入错误")),"[供货价]格式输入错误"):(a(),"")},sku_market_price:function(e,t,a){return 0==t.length?(a(new Error("请输入原价")),"请输入原价"):isNaN(t)||!k.digit.test(t)?(a(new Error("[原价]格式输入错误")),"[原价]格式输入错误"):(a(),"")},sku_cost_price:function(e,t,a){return 0==t.length?(a(new Error("请输入供应商价格")),"请输入供应商价格"):isNaN(t)||!k.digit.test(t)?(a(new Error("[供应商价格]格式输入错误")),"[供应商价格]格式输入错误"):(a(),"")},sku_stock:function(e,t,a){return 0==t.length?(a(new Error("请输入库存")),"请输入库存"):isNaN(t)||!k.number.test(t)?(a(new Error("[库存]格式输入错误")),"[库存]格式输入错误"):(a(),"")},sku_weight:function(e,t,a){return t.length>0?isNaN(t)||!k.digit.test(t)?(a(new Error("[重量(kg)]格式输入错误")),"[重量(kg)]格式输入错误"):void 0:(a(),"")},sku_volume:function(e,t,a){return t.length>0?isNaN(t)||!k.digit.test(t)?(a(new Error("[体积(m³)]格式输入错误")),"[体积(m³)]格式输入错误"):void 0:(a(),"")}},x={components:{TableCustom:h["g"]},data:function(){return{form:{spec_type:!1},loading:!1,batchEditVisible:!1,batchEditValue:"",batchEditField:"",batchEditPlaceholder:"",supplyChainSkuColumns:[{prop:"sku_image",label:"sku图片",type:"image",star:!0},{prop:"spec_name",label:"副标题",type:"input",star:!0,disabled:!0},{prop:"cost_price",label:"供应商价格",type:"input",star:!0,disabled:!0,verifyFunc:w.sku_cost_price},{prop:"price",label:"供货价",type:"input",star:!0,verifyFunc:w.sku_price},{prop:"market_price",label:"原价",type:"input",star:!0,verifyFunc:w.sku_market_price},{prop:"sales_price",label:"销售价",type:"input",star:!0},{prop:"stock",label:"库存",type:"input",star:!0,disabled:!0,verifyFunc:w.sku_stock},{prop:"reward_shop_rate",label:"店主返佣比例",type:"input",star:!0,disabled:!0},{prop:"reward_company_rate",label:"公司返佣比例",type:"input",star:!0,disabled:!0},{prop:"weight",label:"重量(kg)",type:"input",star:!0,disabled:!0,verifyFunc:w.sku_weight},{prop:"volume",label:"体积(m³)",type:"input",star:!0,disabled:!0,verifyFunc:w.sku_volume},{prop:"sku_no",label:"SKU编码",type:"input",disabled:!0},{prop:"goods_state_select",label:"上架/下架",type:"select"}],skuColumns:[],spec_matrix:[],goods_spec_format:[],rules:{sales_price:[{required:!0,message:"销售价不能为空",trigger:"blur"}],reward_shop_rate:[{required:!0,message:"请输入店主返佣比例",trigger:"blur"},{validator:w.reward_shop_rate,trigger:"blur"}],reward_company_rate:[{validator:w.reward_company_rate,trigger:"blur"}],price:[{required:!0,message:"供货价不能为空",trigger:"blur"},{validator:w.price,trigger:"blur"}],market_price:[{required:!0,message:"原价不能为空",trigger:"blur"},{validator:w.market_price(this),trigger:"blur"}],cost_price:[{required:!0,message:"请输入供应商价格",trigger:"blur"},{validator:w.cost_price,trigger:"blur"}],weight:[{validator:w.weight,trigger:"blur"}],volume:[{validator:w.volume,trigger:"blur"}],goods_stock:[{required:!0,message:"总库存不能为空",trigger:"blur"},{validator:w.goods_stock,trigger:"blur"}],goods_stock_alarm:[{validator:w.goods_stock_alarm(this),trigger:"blur"}]}}},props:{details:{type:Object,default:function(){}}},computed:{RewardShopRate:function(){return this.form.reward_shop_rate=(100*(this.form.sales_price/this.form.price-1)).toFixed(4),this.form.reward_shop_rate},RewardCompanyRate:function(){return this.form.reward_company_rate=(100*(this.form.price/this.form.cost_price-1)).toFixed(4),this.form.reward_company_rate}},created:function(){this.init()},methods:{init:function(){var e=this.details.goods_info,t=e.sku_list,a=e.cost_price,o=e.goods_stock,r=e.goods_stock_alarm,l=e.goods_spec_format,i=e.price,n=e.reward_shop_rate,s=e.market_price,u=e.is_free_shipping,c=e.reward_company_rate,d=t[0],p=d.volume,m=d.weight,f=d.sku_no;this.form={spec_type:!!l,sales_price:parseFloat(i*(1+n/100)).toFixed(2),price:i,market_price:s,volume:p,weight:m,sku_no:f,goods_stock:o,goods_stock_alarm:r,is_free_shipping:u,cost_price:a,reward_shop_rate:n,reward_company_rate:c,goods_sku_data:t[0]};var _={};for(var v in t[0])_["edit_".concat(v)]=t[0][v];this.form.spec_type&&(this.goods_spec_format=JSON.parse(this.details.goods_info.goods_spec_format),this.handleGoodsSpecDict(),this.skuColumns=this.supplyChainSkuColumns,this.handleColumns(),this.form.goods_sku_data=t.map((function(e,t){return e.sku_spec_format=JSON.parse(e.sku_spec_format),e.goods_spec_format=JSON.parse(e.goods_spec_format).map((function(t){var a=t.value.find((function(e){return e.selected}));return a&&(e["spec_id_".concat(a.spec_id)]=a.spec_value_name),t})),e.goods_state_select=!!e.goods_state,e.sales_price=parseFloat(e.price*(1+e.reward_shop_rate/100)).toFixed(2),e})))},handleColumns:function(){var e=this,t=Object(y["a"])(this.goods_spec_format).reverse();t.map((function(t,a){e.skuColumns.unshift({prop:"spec_id_".concat(t.spec_id),label:t.spec_name,type:"text",is_spec:!0})}))},handleGoodsSpecDict:function(){var e=this,t=1,a=[];this.goods_spec_format.map((function(e){t*=e.value.length}));for(var o=0;o<t;o++){for(var r=[],l=function(t){var a=1;e.goods_spec_format.map((function(e,o){o>t&&(a*=e.value.length)})),r[t]=o%a==0?a:0},i=0;i<this.goods_spec_format.length;i++)l(i);a[o]=r}this.spec_matrix=a},handleColumnAsterisk:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;if(2==t.rowSpan&&1==t.colSpan){var a=this.skuColumns.filter((function(e){return!(e.prop!=t.property||!e.star)}));if(a.length>0)return"starclass"}},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(this.spec_matrix[t]&&this.spec_matrix[t][a]>=0){var o=this.spec_matrix[t][a],r=1;return o>0?{rowspan:o,colspan:r}:{rowspan:0,colspan:0}}},toBatchEditShow:function(e,t){this.batchEditPlaceholder=t,this.batchEditField=e,this.batchEditVisible=!0},toBatchEditCancel:function(){this.batchEditPlaceholder="",this.batchEditField="",this.batchEditValue="",this.batchEditVisible=!1},toBatchEditConfirm:function(){var e=this,t={number:/^\d{0,10}$/,digit:/^\d{0,10}(.?\d{0,2})$/};if(0!=this.batchEditValue.length){var a="";if(this.batchEditField)switch(this.batchEditField){case"market_price":case"price":case"sales_price":case"cost_price":case"weight":case"volume":a=t.digit;break;case"stock":a=t.number;break}!a||a.test(this.batchEditValue)?(this.form.goods_sku_data=this.form.goods_sku_data.map((function(t){return t[e.batchEditField]=e.batchEditValue,t})),this.batchEditPlaceholder="",this.batchEditField="",this.batchEditValue="",this.batchEditVisible=!1):this.$message.error("["+this.batchEditPlaceholder+"]格式输入错误")}else this.$message.error("请输入"+this.batchEditPlaceholder)},isVisible:function(e){return"boolean"!==typeof e||e},changeEvent:function(e,t,a){this.$set(this.form.goods_sku_data,t,e)},checkboxChange:function(e,t){this.$set(this.form.goods_sku_data,t,e)},showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)},setVaild:function(e){var t=this;this.$refs.form.validate((function(a){if(a){if(t.form.spec_type){t.form.goods_sku_data.map((function(e){return e.goods_state=e.goods_state_select?1:0,e}));for(var o=0;o<t.form.goods_sku_data.length;o++){if(!t.form.goods_sku_data[o].sku_image)return void t.$message.error("请上传SKU商品图片");for(var r=0;r<t.skuColumns.length;r++)if(t.skuColumns[r].verifyFunc&&t.form.goods_sku_data[o].hasOwnProperty(t.skuColumns[r].prop)){var i=t.skuColumns[r].verifyFunc({},t.form.goods_sku_data[o][t.skuColumns[r].prop],(function(e){}));if(i)return void t.$message.error(i)}}}var n=Object(l["a"])({},t.form);t.$emit("input",n),e()}}))}}},E=x,C=(a("9969"),Object(f["a"])(E,b,g,!1,null,"fca7565e",null)),$=C.exports,S=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.info.goods_content?a("div",{staticClass:"prod"},[a("div",{staticClass:"edit_title"},[e._v("商品简图 "),a("el-button",{attrs:{type:"danger",plain:""},on:{click:e.uploadImage}},[e._v("上传图片")])],1),e._v(" "),a("div",{staticClass:"images"},e._l(e.form.goods_image,(function(t,o){return a("div",{key:o},[a("span",[a("i",{staticClass:"el-icon-view",on:{click:function(t){return e.showImage(o)}}}),e._v(" "),a("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.removeImage(o)}}})]),e._v(" "),a("el-image",{attrs:{src:t,fit:"cover"}})],1)})),0),e._v(" "),a("p",[e._v("第一张图片将作为商品主图,支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。")]),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("商品详情")]),e._v(" "),a("quill-editor",{ref:"quillEditor",attrs:{options:e.editorOption},model:{value:e.form.goods_content,callback:function(t){e.$set(e.form,"goods_content",t)},expression:"form.goods_content"}}),e._v(" "),a("o-image",{ref:"image",attrs:{list:e.form.goods_image}}),e._v(" "),a("e-image",{ref:"eimage",on:{onChooseImages:e.chooseImages}})],1):e._e()},F=[],N=a("6a95"),O={components:{EImage:N["a"]},data:function(){return{imgs:[],list:["https://fs.jiufuwangluo.com/uploads/supply/product/20210316/a7c026593368583afc23549d3218f48d.jpg"],editorOption:{modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6]}],[{color:[]},{background:[]}],["clean"],["image","video"]]},placeholder:"请输入正文"},form:{}}},computed:{info:function(){if(this.details.goods_info){var e=this.details.goods_info,t=e.goods_content,a=e.goods_image;return this.form={goods_content:t,goods_image:a.split(",")},this.details.goods_info}return{}}},props:{details:{type:Object,default:function(){}}},methods:{uploadImage:function(){this.$refs.eimage.init()},chooseImages:function(e){console.log(e);var t=e.map((function(e){return e.pic_path}));this.form.goods_image=this.form.goods_image.concat(t)},showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)},removeImage:function(e){console.log(this.form.goods_image,e),this.form.goods_image.splice(e,1)},onSubmit:function(e){var t=this.form,a=t.goods_image,o=t.goods_content;a.length<1?this.$message.error("请上传商品主图"):o?o.length<5||o.length>1e4?this.$message.error("商品描述字符数应在5～10000之间"):(this.$emit("input",{goods_image:a.join(","),goods_content:o}),e()):this.$message.error("请填写商品详情")}}},I=O,j=(a("5d78"),Object(f["a"])(I,S,F,!1,null,"214f2527",null)),V=j.exports,q=a("d74f"),T={components:{OInfo:v,ODetails:V,OStock:$},data:function(){return{tabIndex:1,tabs:[{title:"基础信息",code:1},{title:"价格库存",code:2},{title:"商品详情",code:3}],details:{},infoForm:{},stockForm:{},detailsForm:{},loading:!1}},created:function(){var e=Object(n["a"])(Object(i["a"])().mark((function e(){var t,a;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loading=!0,e.prev=1,t=this.$route.query.goods_id,e.next=5,Object(q["m"])({goods_id:t});case 5:a=e.sent,0==a.code&&(this.details=a.data),e.next=11;break;case 9:e.prev=9,e.t0=e["catch"](1);case 11:this.loading=!1;case 12:case"end":return e.stop()}}),e,this,[[1,9]])})));function t(){return e.apply(this,arguments)}return t}(),mounted:function(){},methods:{switchTabs:function(e,t){var a=this;t?this.$refs["edit_".concat(e-1)].setVaild((function(){a.tabIndex=e})):this.tabIndex=e},onSave:function(){var e=this;this.$refs.edit_3.onSubmit((function(){e.loading=!0;var t=Object(l["a"])(Object(l["a"])(Object(l["a"])({},e.detailsForm),e.stockForm),e.infoForm);if(e.stockForm.spec_type)t.goods_sku_data=JSON.stringify(t.goods_sku_data);else{var a=JSON.stringify([{sku_id:e.stockForm.goods_sku_data.sku_id,sku_name:e.infoForm.goods_name,spec_name:"",sku_no:e.stockForm.sku_no,sku_spec_format:"",price:e.stockForm.price,market_price:e.stockForm.market_price,cost_price:e.stockForm.cost_price,stock:e.stockForm.goods_stock,weight:e.stockForm.weight,volume:e.stockForm.volume,sku_image:e.detailsForm.goods_image.split(",")[0],sku_images:e.detailsForm.goods_image,reward_shop_rate:e.stockForm.reward_shop_rate,reward_company_rate:e.stockForm.reward_company_rate}]);t.goods_sku_data=a}Object(q["g"])(t).then((function(t){0==t.code?(e.loading=!1,e.$message.success("编辑商品成功"),e.$router.push("/goods/quality/solid")):(e.loading=!1,e.$message.error(t.message))}))}))}}},B=T,P=(a("0872"),Object(f["a"])(B,o,r,!1,null,"5910322c",null));t["default"]=P.exports},a7d2:function(e,t,a){},a8dc:function(e,t,a){},b885:function(e,t,a){"use strict";var o=a("e780");a.d(t,"d",(function(){return o["a"]}));var r=a("ad41");a.d(t,"c",(function(){return r["a"]}));var l=a("0476");a.d(t,"g",(function(){return l["a"]}));var i=a("6eb0");a.d(t,"a",(function(){return i["a"]}));var n=a("c87f");a.d(t,"f",(function(){return n["a"]}));var s=a("333d");a.d(t,"e",(function(){return s["a"]}));var u=a("05be");a.d(t,"b",(function(){return u["a"]}));a("9040");var c=a("4381");a.d(t,"h",(function(){return c["a"]}));var d=a("6724");a.d(t,"i",(function(){return d["a"]}))},bfa4:function(e,t,a){"use strict";a("a8dc")},c16a:function(e,t,a){},c40e:function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"d",(function(){return l})),a.d(t,"f",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"g",(function(){return u})),a.d(t,"b",(function(){return c}));var o=a("b775");function r(e){return Object(o["a"])({url:"/goods/product/state/",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}function n(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"i",(function(){return r})),a.d(t,"H",(function(){return l})),a.d(t,"f",(function(){return i})),a.d(t,"A",(function(){return n})),a.d(t,"x",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"w",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"O",(function(){return p})),a.d(t,"j",(function(){return m})),a.d(t,"k",(function(){return f})),a.d(t,"l",(function(){return _})),a.d(t,"T",(function(){return v})),a.d(t,"d",(function(){return b})),a.d(t,"Q",(function(){return g})),a.d(t,"p",(function(){return h})),a.d(t,"P",(function(){return y})),a.d(t,"m",(function(){return k})),a.d(t,"I",(function(){return w})),a.d(t,"L",(function(){return x})),a.d(t,"N",(function(){return E})),a.d(t,"M",(function(){return C})),a.d(t,"S",(function(){return $})),a.d(t,"s",(function(){return S})),a.d(t,"B",(function(){return F})),a.d(t,"z",(function(){return N})),a.d(t,"K",(function(){return O})),a.d(t,"C",(function(){return I})),a.d(t,"h",(function(){return j})),a.d(t,"g",(function(){return V})),a.d(t,"o",(function(){return q})),a.d(t,"G",(function(){return T})),a.d(t,"J",(function(){return B})),a.d(t,"v",(function(){return P})),a.d(t,"F",(function(){return A})),a.d(t,"r",(function(){return L})),a.d(t,"b",(function(){return R})),a.d(t,"q",(function(){return z})),a.d(t,"R",(function(){return M})),a.d(t,"u",(function(){return J})),a.d(t,"t",(function(){return D})),a.d(t,"D",(function(){return H})),a.d(t,"E",(function(){return X})),a.d(t,"y",(function(){return U})),a.d(t,"n",(function(){return W}));var o=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],r=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],l=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],n=[{label:"是",code:1},{label:"否",code:0}],s=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],u=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],p=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],m=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],_=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],b=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],h=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],k=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],w=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],x=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],E=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],$=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],S=[{value:"0",label:"参团"},{value:"1",label:"团长"}],F=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],N=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],O=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],I=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],j=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],V=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],q=[{value:"1",label:"是"},{value:"2",label:"否"}],T=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],B=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],P=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],A=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],L=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],R=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],z=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],M=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],J=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],D=[{label:"按订单编号",value:"order_no"}],H=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],X=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],U=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],W=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},f0a4:function(e,t,a){},fe67:function(e,t,a){e.exports=a.p+"static/img/login_bg.e491666c.png"}}]);