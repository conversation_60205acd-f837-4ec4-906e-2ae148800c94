(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-04a6d0a6"],{"1bda":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("div",{staticClass:"tabs"},[e._l(e.tabs,(function(t,l){var o=t.title,i=t.code;return a("span",{key:l,class:{active:e.tabIndex==i}},[e._v(e._s(o))])})),e._v(" "),a("div",[e.tabIndex>1?a("el-button",{attrs:{size:"mini",plain:""},on:{click:function(t){return e.switchTabs(e.tabIndex-1)}}},[e._v("上一步")]):e._e(),e._v(" "),e.tabIndex<3?a("el-button",{attrs:{size:"mini",plain:"",type:"danger"},on:{click:function(t){return e.switchTabs(e.tabIndex+1,!0)}}},[e._v("下一步")]):e._e(),e._v(" "),3==e.tabIndex?a("el-button",{attrs:{size:"mini",loading:e.loading,type:"danger"},on:{click:e.onSave}},[e._v("保存")]):e._e()],1)],2),e._v(" "),a("div",{staticClass:"cont"},[1==e.tabIndex?a("o-info",{ref:"edit_1",attrs:{details:e.details},model:{value:e.infoForm,callback:function(t){e.infoForm=t},expression:"infoForm"}}):e._e(),e._v(" "),2==e.tabIndex?a("o-stock",{ref:"edit_2",attrs:{details:e.details},model:{value:e.stockForm,callback:function(t){e.stockForm=t},expression:"stockForm"}}):e._e(),e._v(" "),3==e.tabIndex?a("o-details",{ref:"edit_3",attrs:{details:e.details},model:{value:e.detailsForm,callback:function(t){e.detailsForm=t},expression:"detailsForm"}}):e._e()],1)])},o=[],i=a("5530"),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"edit_title"},[e._v("基础信息")]),e._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",sytle:"width:60%",rules:e.rules,model:e.form}},[a("el-form-item",{attrs:{label:"商品名称：",prop:"goods_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入商品名称"},model:{value:e.form.goods_name,callback:function(t){e.$set(e.form,"goods_name",t)},expression:"form.goods_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品分类：",prop:"category_name"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入商品分类",readonly:""},model:{value:e.form.category_name,callback:function(t){e.$set(e.form,"category_name",t)},expression:"form.category_name"}},[a("el-button",{attrs:{slot:"append"},on:{click:e.favour},slot:"append"},[e._v("选择")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"促销语："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入促销语"},model:{value:e.form.introduction,callback:function(t){e.$set(e.form,"introduction",t)},expression:"form.introduction"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"关键词："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入关键词"},model:{value:e.form.keywords,callback:function(t){e.$set(e.form,"keywords",t)},expression:"form.keywords"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入虚拟销量",type:"number"},model:{value:e.form.virtual_sale_num,callback:function(t){e.$set(e.form,"virtual_sale_num",t)},expression:"form.virtual_sale_num"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"单位："}},[a("el-input",{staticStyle:{width:"500px"},attrs:{placeholder:"请输入单位"},model:{value:e.form.unit,callback:function(t){e.$set(e.form,"unit",t)},expression:"form.unit"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品品牌："}},[a("el-select",{model:{value:e.form.brand_id,callback:function(t){e.$set(e.form,"brand_id",t)},expression:"form.brand_id"}},e._l(e.brandOpt,(function(e,t){return a("el-option",{key:t,attrs:{value:e.brand_id,label:e.brand_name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"供应商：",prop:"supplier_id"}},[a("el-select",{attrs:{filterable:""},model:{value:e.form.supplier_id,callback:function(t){e.$set(e.form,"supplier_id",t)},expression:"form.supplier_id"}},e._l(e.supplierOpt,(function(e,t){return a("el-option",{key:t,attrs:{value:e.supplier_id,label:e.title}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"商品标签："}},[a("el-radio-group",{model:{value:e.form.tag,callback:function(t){e.$set(e.form,"tag",t)},expression:"form.tag"}},e._l(e.tagOpt,(function(t,l){return a("el-radio",{key:l,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1),e._v(" "),a("div",{staticClass:"danger"},[e._v("迈豆专区的商品不进行原店主分佣方式分佣，只按照新的迈豆专区佣金分配方式分佣")])],1),e._v(" "),a("el-form-item",{attrs:{label:"微信支付方式："}},[a("el-radio-group",{model:{value:e.form.use_pay_type,callback:function(t){e.$set(e.form,"use_pay_type",t)},expression:"form.use_pay_type"}},e._l(e.payList,(function(t,l){return a("el-radio",{key:l,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1),e._v(" "),a("div",{staticClass:"danger"},[e._v("拼团不受此设置控制，默认使用汇付天下")])],1),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("其他信息")]),e._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入单位",type:"number"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否上架：",prop:"goods_state"}},[a("el-radio-group",{model:{value:e.form.goods_state,callback:function(t){e.$set(e.form,"goods_state",t)},expression:"form.goods_state"}},[a("el-radio",{attrs:{label:1}},[e._v("立即上架")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("暂不上架")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("仓库中")])],1)],1)],1),e._v(" "),a("classify-dialog",{ref:"classify",on:{onClassify:e.onClassify},model:{value:e.form.category_name,callback:function(t){e.$set(e.form,"category_name",t)},expression:"form.category_name"}})],1)},r=[],s=(a("7514"),a("55dd"),a("c71b")),u=a("33a6"),c=a("ec01"),d={components:{classifyDialog:u["a"]},data:function(){return{form:{},brandOpt:[],payList:s["y"],tagOpt:s["J"],rules:{goods_name:[{required:!0,message:"商品名称不能为空",trigger:"blur"}],category_name:[{required:!0,message:"商品分类不能为空",trigger:"change"}],supplier_id:[{required:!0,message:"供应商不能为空",trigger:"change"}],goods_state:[{required:!0,message:"是否上架不能为空",trigger:"change"}]},supplierOpt:[]}},computed:{info:function(){if(this.details.goods_info){var e=this.details.goods_info,t=e.goods_spec_format,a=e.goods_id,l=e.commission_rate,o=e.batch_operation_sku,i=e.supplier_id,n=e.goods_state,r=e.sort,s=e.category_id,u=e.category_id_1,c=e.category_id_2,d=e.category_id_3,f=e.use_pay_type,_=e.brand_name,m=e.brand_id,v=e.unit,b=e.virtual_sale_num,p=e.keywords,g=e.supply_goods_name,h=e.supply_pro_no,y=e.site_name,k=e.goods_name,x=e.introduction,w=e.category_name;return this.form={tag:this.details.goods_tag.id,supplier_name:y,introduction:x,category_name:w,goods_name:k,supply_goods_name:g,supply_pro_no:h,keywords:p,virtual_sale_num:b,unit:v,brand_id:m,brand_name:_,use_pay_type:f,sort:r,goods_state:n,category_id:s,category_id_1:u,category_id_2:c,category_id_3:d,supplier_id:i,batch_operation_sku:o||"",commission_rate:l,goods_id:a,goods_spec_format:t||""},this.details.goods_info}return{}}},props:{details:{type:Object,default:function(){}}},mounted:function(){this.onInit()},methods:{onInit:function(){var e=this;Object(c["a"])({page_size:1/0}).then((function(t){e.brandOpt=t.data.list})),Object(c["b"])().then((function(t){console.log(t),e.supplierOpt=t.data}))},handleChange:function(){this.$forceUpdate()},favour:function(){this.$refs.classify.init([this.info.category_id_1,this.info.category_id_2,this.info.category_id_3])},changeStatus:function(e){var t="";return s["n"].find((function(a){a.value==e&&(t=a.label)})),t},getTags:function(e){var t="";return this.details.tag_list&&this.details.tag_list.find((function(a){if(a.id==e)return t=a.tag_name})),t},changePay:function(e){var t="";return s["y"].find((function(a){a.value==e&&(t=a.label)})),t},setVaild:function(e){var t=this;console.log(this.$refs.form),this.$refs.form.validate((function(a){a&&(t.$emit("input",t.form),e())}))},onClassify:function(e){var t=this;console.log(e),e.map((function(e,a){t.form["category_id_".concat(a+1)]=e}))}}},f=d,_=(a("43e0"),a("2877")),m=Object(_["a"])(f,n,r,!1,null,"4e7fa50c",null),v=m.exports,b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[e._v("价格库存")]),e._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",model:e.form,rules:e.rules}},[a("el-form-item",{attrs:{label:"启用多规格："}},[a("el-switch",{attrs:{disabled:""},model:{value:e.form.spec_type,callback:function(t){e.$set(e.form,"spec_type",t)},expression:"form.spec_type"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"销售价：",prop:"sales_price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入销售价"},model:{value:e.form.sales_price,callback:function(t){e.$set(e.form,"sales_price",t)},expression:"form.sales_price"}}),e._v(" "),a("span",[e._v("元")])],1),e._v(" "),a("el-form-item",{attrs:{label:"店主返佣比例："}},[e._v("\n            "+e._s(e.RewardShopRate)+" % "),a("span",[e._v("(自动计算=(销售价/供货价-1)*100)")])]),e._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例："}},[e._v("\n            "+e._s(e.info.reward_company_rate)+" % "),a("span",[e._v("(自动计算=(供货价/供应商价格-1)*100)")])]),e._v(" "),a("el-form-item",{attrs:{label:"供应商价格："}},[e._v("\n            "+e._s(e.info.cost_price)+" 元\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"供货价：",prop:"price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入供货价"},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}}),e._v("\n            元\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"原价：",prop:"market_price"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入原价"},model:{value:e.form.market_price,callback:function(t){e.$set(e.form,"market_price",t)},expression:"form.market_price"}}),e._v("\n            元\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"重量："}},[e._v("\n            "+e._s(e.info.weight)+" kg\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"体积："}},[e._v("\n            "+e._s(e.info.volume)+" m3\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"商品编码："}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入商品编码"},model:{value:e.form.sku_no,callback:function(t){e.$set(e.form,"sku_no",t)},expression:"form.sku_no"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"总库存：",prop:"goods_stock"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入总库存",type:"number"},model:{value:e.form.goods_stock,callback:function(t){e.$set(e.form,"goods_stock",t)},expression:"form.goods_stock"}}),e._v(" / 件\n        ")],1),e._v(" "),a("el-form-item",{attrs:{label:"库存预警："}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入总库存",type:"number"},model:{value:e.form.goods_stock_alarm,callback:function(t){e.$set(e.form,"goods_stock_alarm",t)},expression:"form.goods_stock_alarm"}}),e._v(" / 件\n            "),a("span",[e._v(" (设置最低库存预警值。当库存低于预警值时商家中心商品列表页库存列红字提醒，0为不预警。)")])],1),e._v(" "),a("el-form-item",{attrs:{label:"是否免邮："}},[e._v("\n            "+e._s(1==e.form.is_free_shipping?"是":"否")+"\n        ")])],1)],1):e._e()},p=[],g={data:function(){return{form:{},rules:{sales_price:[{required:!0,message:"销售价不能为空",trigger:"blur"}],price:[{required:!0,message:"供货价不能为空",trigger:"blur"}],market_price:[{required:!0,message:"原价不能为空",trigger:"blur"}],goods_stock:[{required:!0,message:"总库存不能为空",trigger:"blur"}]}}},computed:{info:function(){if(this.details.goods_info){var e=this.details.goods_info,t=e.sku_list,a=e.cost_price,l=e.goods_stock,o=e.goods_stock_alarm,i=e.verify_state,n=e.price,r=e.reward_shop_rate,s=e.market_price,u=e.is_free_shipping,c=t[0],d=c.volume,f=c.weight,_=c.sku_no;this.form={spec_type:i,sales_price:parseFloat(n*(1+r/100)).toFixed(2),price:n,market_price:s,volume:d,weight:f,sku_no:_,goods_stock:l,goods_stock_alarm:o,is_free_shipping:u,cost_price:a,goods_sku_data:JSON.stringify(t)};var m={};for(var v in t[0])m["edit_".concat(v)]=t[0][v];return console.log(m),t[0]}return{}},RewardShopRate:function(){return this.form.reward_shop_rate=(100*(this.form.sales_price/this.form.price-1)).toFixed(4),this.form.reward_shop_rate},RewardCompanyRate:function(){return this.form.reward_company_rate=(100*(this.form.sales_price/this.form.cost_price-1)).toFixed(4),this.form.reward_company_rate}},props:{details:{type:Object,default:function(){}}},methods:{showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)},setVaild:function(e){var t=this;this.$refs.form.validate((function(a){a&&(t.$emit("input",t.form),e())}))}}},h=g,y=(a("6daf"),Object(_["a"])(h,b,p,!1,null,"7af7b9c4",null)),k=y.exports,x=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.info.goods_content?a("div",{staticClass:"prod"},[a("div",{staticClass:"edit_title"},[e._v("商品简图 "),a("el-button",{attrs:{type:"danger",plain:""},on:{click:e.uploadImage}},[e._v("上传图片")])],1),e._v(" "),a("div",{staticClass:"images"},e._l(e.form.goods_image,(function(t,l){return a("div",{key:l},[a("span",[a("i",{staticClass:"el-icon-view",on:{click:function(t){return e.showImage(l)}}}),e._v(" "),a("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.removeImage(l)}}})]),e._v(" "),a("el-image",{attrs:{src:t,fit:"cover"}})],1)})),0),e._v(" "),a("p",[e._v("第一张图片将作为商品主图,支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。")]),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("商品详情")]),e._v(" "),a("quill-editor",{ref:"quillEditor",attrs:{options:e.editorOption},model:{value:e.form.goods_content,callback:function(t){e.$set(e.form,"goods_content",t)},expression:"form.goods_content"}}),e._v(" "),a("o-image",{ref:"image",attrs:{list:e.form.goods_image}}),e._v(" "),a("e-image",{ref:"eimage",on:{onChooseImages:e.chooseImages}})],1):e._e()},w=[],$=(a("28a5"),a("6a95")),C={components:{EImage:$["a"]},data:function(){return{imgs:[],list:["https://fs.jiufuwangluo.com/uploads/supply/product/20210316/a7c026593368583afc23549d3218f48d.jpg"],editorOption:{modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6]}],[{color:[]},{background:[]}],["clean"],["image","video"]]},placeholder:"请输入正文"},form:{}}},computed:{info:function(){if(this.details.goods_info){var e=this.details.goods_info,t=e.goods_content,a=e.goods_image;return this.form={goods_content:t,goods_image:a.split(",")},this.details.goods_info}return{}}},props:{details:{type:Object,default:function(){}}},methods:{uploadImage:function(){this.$refs.eimage.init()},chooseImages:function(e){console.log(e);var t=e.map((function(e){return e.pic_path}));this.form.goods_image=this.form.goods_image.concat(t)},showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)},removeImage:function(e){console.log(this.form.goods_image,e),this.form.goods_image.splice(e,1)},onSubmit:function(e){var t=this.form,a=t.goods_image,l=t.goods_content;this.$emit("input",{goods_image:a.join(","),goods_content:l}),e()}}},O=C,I=(a("800d"),Object(_["a"])(O,x,w,!1,null,"60c0fbc8",null)),j=I.exports,S={components:{OInfo:v,ODetails:j,OStock:k},data:function(){return{tabIndex:1,tabs:[{title:"基础信息",code:1},{title:"价格库存",code:2},{title:"商品详情",code:3}],details:{},infoForm:{},stockForm:{},detailsForm:{},loading:!1}},mounted:function(){var e=this,t=this.$route.query.goods_id;t&&Object(c["c"])({goods_id:t}).then((function(t){var a=t.data;e.details=a}))},methods:{switchTabs:function(e,t){var a=this;t?this.$refs["edit_".concat(e-1)].setVaild((function(){a.tabIndex=e})):this.tabIndex=e},onSave:function(){var e=this;this.loading=!0,this.$refs.edit_3.onSubmit((function(){var t=Object(i["a"])(Object(i["a"])(Object(i["a"])({},e.detailsForm),e.stockForm),e.infoForm);editGoods(t).then((function(t){e.loading=!1,e.$message.success("编辑商品成功"),e.$router.push("/goods/quality/solid")}))}))}}},F=S,q=(a("c9e6"),Object(_["a"])(F,l,o,!1,null,"2202ef8f",null));t["default"]=q.exports},"27fc":function(e,t,a){},"2f21":function(e,t,a){"use strict";var l=a("79e5");e.exports=function(e,t){return!!e&&l((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},"33a6":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"选择商品分类",visible:e.dialogVisible,top:"100px",width:"810px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"classify"},[a("div",{staticClass:"fist-level"},e._l(e.fistLevels,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[0]},on:{click:function(a){e.getClassify("seconddarys",2,t.category_id),e.teriarys=[],e.setTitle(t,0)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"second-level"},e._l(e.seconddarys,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[1]},on:{click:function(a){e.getClassify("teriarys",3,t.category_id),e.setTitle(t,1)}}},[e._v("\n                "+e._s(t.category_name)+"\n                "),t.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"teriary-level"},e._l(e.teriarys,(function(t,l){return a("span",{key:l,class:{danger:t.category_name==e.titles[2]},on:{click:function(a){return e.setTitle(t,2)}}},[e._v("\n                "+e._s(t.category_name)+"\n            ")])})),0)]),e._v(" "),a("div",{staticClass:"current"},[e._v("\n        您当前选择的是：\n        "+e._s(e.titles[0])+" "),e.titles[1]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[1])+" "),e.titles[2]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[2])+"\n    ")]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submit}},[e._v("保 存")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("关 闭")])],1)])},o=[],i=(a("28a5"),a("d74f")),n={data:function(){return{dialogVisible:!1,fistLevels:[],seconddarys:[],teriarys:[],titles:[],cateIds:[]}},props:["value"],mounted:function(){this.value&&(this.titles=this.value.split("/")),this.getClassify()},methods:{init:function(e){this.dialogVisible=!0,e&&(this.getClassify("seconddarys",2,e[0]),this.getClassify("teriarys",3,e[1]),this.cateIds=e)},getClassify:function(e,t,a){var l=this,o={level:t||1,pid:a||0};Object(i["i"])(o).then((function(t){var a=t.data;l[e||"fistLevels"]=a}))},setTitle:function(e,t){var a=e.category_name,l=e.category_id;this.titles[t]=a,this.cateIds[t]=l,t<2&&this.titles.splice(t+1,this.titles.length),this.$forceUpdate()},submit:function(){this.$emit("onClassify",this.cateIds),this.$emit("input",this.titles.join("/")),this.dialogVisible=!1}}},r=n,s=(a("564d"),a("2877")),u=Object(s["a"])(r,l,o,!1,null,"260665e1",null);t["a"]=u.exports},"3f5e":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var l=a("b775");function o(e){return Object(l["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(l["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function n(e){return Object(l["a"])({url:"/admin/Album/Album",method:"post",data:e})}},"43e0":function(e,t,a){"use strict";a("ba88")},4872:function(e,t,a){},"55dd":function(e,t,a){"use strict";var l=a("5ca1"),o=a("d8e8"),i=a("4bf8"),n=a("79e5"),r=[].sort,s=[1,2,3];l(l.P+l.F*(n((function(){s.sort(void 0)}))||!n((function(){s.sort(null)}))||!a("2f21")(r)),"Array",{sort:function(e){return void 0===e?r.call(i(this)):r.call(i(this),o(e))}})},"564d":function(e,t,a){"use strict";a("c16a")},"6daf":function(e,t,a){"use strict";a("4872")},"800d":function(e,t,a){"use strict";a("f956")},ba88:function(e,t,a){},c16a:function(e,t,a){},c71b:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"i",(function(){return o})),a.d(t,"H",(function(){return i})),a.d(t,"f",(function(){return n})),a.d(t,"A",(function(){return r})),a.d(t,"x",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"w",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"O",(function(){return f})),a.d(t,"j",(function(){return _})),a.d(t,"k",(function(){return m})),a.d(t,"l",(function(){return v})),a.d(t,"T",(function(){return b})),a.d(t,"d",(function(){return p})),a.d(t,"Q",(function(){return g})),a.d(t,"p",(function(){return h})),a.d(t,"P",(function(){return y})),a.d(t,"m",(function(){return k})),a.d(t,"I",(function(){return x})),a.d(t,"L",(function(){return w})),a.d(t,"N",(function(){return $})),a.d(t,"M",(function(){return C})),a.d(t,"S",(function(){return O})),a.d(t,"s",(function(){return I})),a.d(t,"B",(function(){return j})),a.d(t,"z",(function(){return S})),a.d(t,"K",(function(){return F})),a.d(t,"C",(function(){return q})),a.d(t,"h",(function(){return T})),a.d(t,"g",(function(){return R})),a.d(t,"o",(function(){return L})),a.d(t,"G",(function(){return V})),a.d(t,"J",(function(){return E})),a.d(t,"v",(function(){return z})),a.d(t,"F",(function(){return A})),a.d(t,"r",(function(){return P})),a.d(t,"b",(function(){return D})),a.d(t,"q",(function(){return J})),a.d(t,"R",(function(){return N})),a.d(t,"u",(function(){return M})),a.d(t,"t",(function(){return X})),a.d(t,"D",(function(){return G})),a.d(t,"E",(function(){return H})),a.d(t,"y",(function(){return B})),a.d(t,"n",(function(){return U}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],o=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],i=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],n=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],r=[{label:"是",code:1},{label:"否",code:0}],s=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],u=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],_=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],m=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],v=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],b=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],p=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],h=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],k=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],x=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],w=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],$=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],O=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],I=[{value:"0",label:"参团"},{value:"1",label:"团长"}],j=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],S=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],F=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],q=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],T=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],R=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],L=[{value:"1",label:"是"},{value:"2",label:"否"}],V=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],E=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],z=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],A=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],P=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],D=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],J=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],N=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],M=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],X=[{label:"按订单编号",value:"order_no"}],G=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],H=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],B=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],U=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},c9e6:function(e,t,a){"use strict";a("27fc")},ec01:function(e,t,a){"use strict";a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return n}));var l=a("b775"),o=a("d74f");function i(e){return Object(l["a"])({url:"/admin_plus/Goods/show",method:"post",data:e})}function n(e){return Object(l["a"])({url:"/admin/goods/getSupplierPageList.html",method:"post",data:e})}a.d(t,"a",(function(){return o["b"]}))},f956:function(e,t,a){}}]);