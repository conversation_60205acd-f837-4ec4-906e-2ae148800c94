(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8b54f59a"],{"267f":function(l,e,a){"use strict";a("b83a")},"5e5d":function(l,e,a){},a673:function(l,e,a){"use strict";a.r(e);var t=function(){var l=this,e=l.$createElement,a=l._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"tabs"},l._l(l.tabs,(function(e,t){var n=e.title,u=e.code;return a("span",{key:t,class:{active:l.tabIndex==u},on:{click:function(e){return l.switchTabs(u)}}},[l._v(l._s(n))])})),0),l._v(" "),a("div",{staticClass:"cont"},[1==l.tabIndex?a("o-info",{attrs:{details:l.details}}):l._e(),l._v(" "),2==l.tabIndex?a("o-stock",{attrs:{details:l.details}}):l._e(),l._v(" "),3==l.tabIndex?a("o-details",{attrs:{details:l.details}}):l._e()],1)])},n=[],u=function(){var l=this,e=l.$createElement,a=l._self._c||e;return l.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[l._v("基础信息")]),l._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"原商品名称："}},[l._v("\n            "+l._s(l.info.supply_goods_name)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"原商品编码："}},[l._v("\n            "+l._s(l.info.supply_pro_no)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"商品名称："}},[l._v("\n            "+l._s(l.info.goods_name)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"商品分类："}},[l._v("\n            "+l._s(l.info.category_name)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"促销语："}},[l._v("\n            "+l._s(l.info.introduction)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"关键词："}},[l._v("\n            "+l._s(l.info.keywords)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[l._v("\n            "+l._s(l.info.virtual_sale_num)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"商品品牌："}},[l._v("\n            "+l._s(l.info.brand_name)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"供应商："}},[l._v("\n            "+l._s(l.info.site_name)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"迈豆专区："}},[l._v("\n            "+l._s(l.getTags(l.details.goods_tag.id))+" \n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"微信支付方式："}},[l._v("\n            "+l._s(l.changePay(l.info.use_pay_type))+"\n        ")])],1),l._v(" "),a("div",{staticClass:"edit_title"},[l._v("其他信息")]),l._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"排序："}},[l._v("\n            "+l._s(l.info.sort)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"是否上架："}},[l._v("\n            "+l._s(l.changeStatus(l.info.goods_state))+"\n        ")])],1)],1):l._e()},i=[],o=(a("7514"),a("c71b")),s={data:function(){return{}},computed:{info:function(){return this.details.goods_info?this.details.goods_info:{}}},props:{details:{type:Object,default:function(){}}},methods:{changeStatus:function(l){var e="";return o["n"].find((function(a){a.value==l&&(e=a.label)})),e},getTags:function(l){var e="";return this.details.tag_list&&this.details.tag_list.find((function(a){if(a.id==l)return e=a.tag_name})),e},changePay:function(l){var e="";return o["y"].find((function(a){a.value==l&&(e=a.label)})),e}}},r=s,v=a("2877"),b=Object(v["a"])(r,u,i,!1,null,null,null),f=b.exports,d=function(){var l=this,e=l.$createElement,a=l._self._c||e;return l.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[l._v("价格库存")]),l._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"启用多规格："}},[l._v("\n            "+l._s(1==l.info.verify_state?"关闭":"启用")+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"销售价："}},[l._v("\n            "+l._s(l.info.price)+" 元\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"店主返佣比例："}},[l._v("\n            "+l._s(l.info.reward_shop_rate)+" % "),a("span",[l._v("(自动计算=(销售价/供货价-1)*100)")])]),l._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例："}},[l._v("\n            "+l._s(l.info.reward_company_rate)+" % "),a("span",[l._v("(自动计算=(供货价/供应商价格-1)*100)")])]),l._v(" "),a("el-form-item",{attrs:{label:"供应商价格："}},[l._v("\n            "+l._s(l.info.cost_price)+" 元\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"供货价："}},[l._v("\n            "+l._s(l.info.price)+" 元\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"原价："}},[l._v("\n            "+l._s(l.info.market_price)+" 元\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"重量："}},[l._v("\n            "+l._s(l.info.weight)+" kg\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"体积："}},[l._v("\n            "+l._s(l.info.volume)+" m3\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"商品编码："}},[l._v("\n            "+l._s(l.info.sku_no)+"\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"总库存："}},[l._v("\n            "+l._s(l.info.stock)+"/件\n        ")]),l._v(" "),a("el-form-item",{attrs:{label:"库存预警："}},[l._v("\n            "+l._s(l.details.goods_info.goods_stock_alarm)+"/件\n            "),a("span",[l._v(" (设置最低库存预警值。当库存低于预警值时商家中心商品列表页库存列红字提醒，0为不预警。)")])]),l._v(" "),a("el-form-item",{attrs:{label:"是否免邮："}},[l._v("\n            "+l._s(1==l.details.goods_info.is_free_shipping?"是":"否")+"\n        ")])],1)],1):l._e()},_=[],c={data:function(){return{}},computed:{info:function(){return this.details.goods_info?this.details.goods_info.sku_list[0]:{}}},props:{details:{type:Object,default:function(){}}},methods:{showImage:function(l){this.imgs=this.images,this.$refs.image.init(l)}}},m=c,p=Object(v["a"])(m,d,_,!1,null,null,null),g=p.exports,h=function(){var l=this,e=l.$createElement,a=l._self._c||e;return a("div",{staticClass:"prod"},[a("div",{staticClass:"edit_title"},[l._v("商品简图")]),l._v(" "),a("div",{staticClass:"images"},l._l(l.images,(function(e,t){return a("div",{key:t},[a("span",[a("i",{staticClass:"el-icon-view",on:{click:function(e){return l.showImage(t)}}})]),l._v(" "),a("el-image",{attrs:{src:e,fit:"cover"}})],1)})),0),l._v(" "),a("p",[l._v("第一张图片将作为商品主图,支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。")]),l._v(" "),a("div",{staticClass:"edit_title"},[l._v("商品详情")]),l._v(" "),a("div",{domProps:{innerHTML:l._s(l.info.goods_content)}}),l._v(" "),a("o-image",{ref:"image",attrs:{list:l.images}})],1)},w=[],y=(a("28a5"),{data:function(){return{imgs:[],list:["https://fs.jiufuwangluo.com/uploads/supply/product/20210316/a7c026593368583afc23549d3218f48d.jpg"]}},computed:{info:function(){return this.details.goods_info?this.details.goods_info:{}},images:function(){return this.details.goods_info?this.details.goods_info.goods_image.split(","):[]}},props:{details:{type:Object,default:function(){}}},methods:{showImage:function(l){this.imgs=this.images,this.$refs.image.init(l)}}}),k=y,x=(a("267f"),Object(v["a"])(k,h,w,!1,null,"41d8e4be",null)),I=x.exports,C=a("d74f"),j={components:{OInfo:f,ODetails:I,OStock:g},data:function(){return{tabIndex:1,tabs:[{title:"基础信息",code:1},{title:"价格库存",code:2},{title:"商品详情",code:3}],details:{}}},mounted:function(){var l=this,e=this.$route.query.goods_id;Object(C["m"])({goods_id:e}).then((function(e){var a=e.data;l.details=a}))},methods:{switchTabs:function(l){this.tabIndex=l}}},O=j,P=(a("f95d"),Object(v["a"])(O,t,n,!1,null,"82ba72a4",null));e["default"]=P.exports},b83a:function(l,e,a){},c71b:function(l,e,a){"use strict";a.d(e,"a",(function(){return t})),a.d(e,"i",(function(){return n})),a.d(e,"H",(function(){return u})),a.d(e,"f",(function(){return i})),a.d(e,"A",(function(){return o})),a.d(e,"x",(function(){return s})),a.d(e,"e",(function(){return r})),a.d(e,"w",(function(){return v})),a.d(e,"c",(function(){return b})),a.d(e,"O",(function(){return f})),a.d(e,"j",(function(){return d})),a.d(e,"k",(function(){return _})),a.d(e,"l",(function(){return c})),a.d(e,"T",(function(){return m})),a.d(e,"d",(function(){return p})),a.d(e,"Q",(function(){return g})),a.d(e,"p",(function(){return h})),a.d(e,"P",(function(){return w})),a.d(e,"m",(function(){return y})),a.d(e,"I",(function(){return k})),a.d(e,"L",(function(){return x})),a.d(e,"N",(function(){return I})),a.d(e,"M",(function(){return C})),a.d(e,"S",(function(){return j})),a.d(e,"s",(function(){return O})),a.d(e,"B",(function(){return P})),a.d(e,"z",(function(){return $})),a.d(e,"K",(function(){return E})),a.d(e,"C",(function(){return T})),a.d(e,"h",(function(){return A})),a.d(e,"g",(function(){return D})),a.d(e,"o",(function(){return S})),a.d(e,"G",(function(){return J})),a.d(e,"J",(function(){return L})),a.d(e,"v",(function(){return M})),a.d(e,"F",(function(){return q})),a.d(e,"r",(function(){return B})),a.d(e,"b",(function(){return H})),a.d(e,"q",(function(){return N})),a.d(e,"R",(function(){return z})),a.d(e,"u",(function(){return F})),a.d(e,"t",(function(){return G})),a.d(e,"D",(function(){return K})),a.d(e,"E",(function(){return Q})),a.d(e,"y",(function(){return R})),a.d(e,"n",(function(){return V}));var t=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],s=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],r=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],v=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],b=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],d=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],_=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],c=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],p=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],h=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],w=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],y=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],x=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],I=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],j=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],O=[{value:"0",label:"参团"},{value:"1",label:"团长"}],P=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],$=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],E=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],T=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],A=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],D=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],S=[{value:"1",label:"是"},{value:"2",label:"否"}],J=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],L=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],M=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],q=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],B=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],H=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],z=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],F=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],G=[{label:"按订单编号",value:"order_no"}],K=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],Q=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],R=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],V=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},f95d:function(l,e,a){"use strict";a("5e5d")}}]);