(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1cb94b11"],{2868:function(t,n,e){"use strict";e.d(n,"d",(function(){return i})),e.d(n,"i",(function(){return o})),e.d(n,"a",(function(){return a})),e.d(n,"b",(function(){return l})),e.d(n,"c",(function(){return d})),e.d(n,"g",(function(){return u})),e.d(n,"f",(function(){return r})),e.d(n,"e",(function(){return s})),e.d(n,"j",(function(){return c})),e.d(n,"k",(function(){return f})),e.d(n,"l",(function(){return m})),e.d(n,"n",(function(){return p})),e.d(n,"p",(function(){return _})),e.d(n,"o",(function(){return h})),e.d(n,"h",(function(){return g})),e.d(n,"m",(function(){return b}));var i=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],o=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],a=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],l=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],d=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],u=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],r=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],s=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],c=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],f=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],m=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],p=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],_=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],h=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],g=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],b=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"2dc5":function(t,n,e){"use strict";e.r(n);var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"details"},[e("div",{staticClass:"edit_title"},[t._v("自动派发规则详情")]),t._v(" "),e("div",{staticClass:"user-info"},[e("div",[e("span",[t._v("规则名称：")]),t._v("\n            "+t._s(t.details.rule_name)+"\n        ")]),t._v(" "),e("div",[e("span",[t._v("已派发总数量：")]),t._v("\n            "+t._s(t.details.send_count)+"\n        ")]),t._v(" "),e("div",[e("span",[t._v("开始执行时间：")]),t._v("\n            "+t._s(t.details.start_time)+"\n        ")]),t._v(" "),e("div",[e("span",[t._v("结束执行时间：")]),t._v("\n            "+t._s(t.details.stop_time)+"\n        ")]),t._v(" "),e("div",[e("span",[t._v("状态：")]),t._v("\n            "+t._s(t.details.status)+"\n        ")]),t._v(" "),e("div",[e("span",[t._v("执行规则：")]),t._v(" "),t.tagRule?e("i",[t._v(t._s(t.tagRule))]):e("i",[t._v("用户首单支付成功后发放，支付金额需满"+t._s(t.details.money)+"元")])]),t._v(" "),t._m(0)]),t._v(" "),e("o-table",{attrs:{isPage:!1,hideRefresh:!0,columns:t.columns,data:t.list},scopedSlots:t._u([{key:"single_count",fn:function(n){var e=n.row;return[t._v("\n            "+t._s(e.single_count)+"\n        ")]}}])})],1)},o=[function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",[e("span",[t._v("派发优惠券：")]),t._v(" "),e("p",[t._v("当用户触发执行规则时，系统自动向该用户派发以下优惠券")])])}],a=e("2868"),l=e("6229"),d=a["a"].splice(0,a["a"].length-1);console.log(d);var u={data:function(){return{width:"",isShowImage:!1,list:[],columns:d,details:{},tagRule:""}},mounted:function(){this.getTableList(),this.onInit()},methods:{onInit:function(){var t=this,n=this.$route.query.rule_id;Object(l["a"])({rule_id:n}).then((function(n){var e=n.data;console.log(e),t.details=e.rule,t.tagRule=e.tag_rule}))},showImage:function(){this.isShowImage=!this.isShowImage},getTableList:function(t){var n=this;Object(l["i"])({rule_id:this.$route.query.rule_id}).then((function(t){var e=t.data.list;n.list=e,n.loading=!1}))}}},r=u,s=(e("7806"),e("2877")),c=Object(s["a"])(r,i,o,!1,null,"4d089b3a",null);n["default"]=c.exports},"3b38":function(t,n,e){"use strict";e.d(n,"n",(function(){return o})),e.d(n,"m",(function(){return a})),e.d(n,"p",(function(){return l})),e.d(n,"l",(function(){return d})),e.d(n,"c",(function(){return u})),e.d(n,"g",(function(){return r})),e.d(n,"f",(function(){return s})),e.d(n,"e",(function(){return c})),e.d(n,"j",(function(){return f})),e.d(n,"k",(function(){return m})),e.d(n,"i",(function(){return p})),e.d(n,"h",(function(){return _})),e.d(n,"a",(function(){return h})),e.d(n,"o",(function(){return g})),e.d(n,"b",(function(){return b})),e.d(n,"q",(function(){return v})),e.d(n,"d",(function(){return j}));var i=e("b775");function o(t){return Object(i["a"])({url:"/admin/pintuan/data.html",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/admin/pintuan/store.html",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:t})}function u(t){return Object(i["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/admin/pintuan/change_status",method:"post",data:t})}function f(t){return Object(i["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:t})}function m(t){return Object(i["a"])({url:"/admin/pintuan/editStock",method:"post",data:t})}function p(t){return Object(i["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:t})}function _(t){return Object(i["a"])({url:"/admin/pintuan/editSort",method:"post",data:t})}function h(t){return Object(i["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:t})}function g(t){return Object(i["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:t})}function b(t){return Object(i["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:t})}function v(t){return Object(i["a"])({url:"/admin/pintuan/update.html",method:"post",data:t})}function j(t){return Object(i["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:t})}},"3d13":function(t,n,e){},6229:function(t,n,e){"use strict";e.d(n,"n",(function(){return u})),e.d(n,"o",(function(){return r})),e.d(n,"h",(function(){return s})),e.d(n,"j",(function(){return c})),e.d(n,"m",(function(){return f})),e.d(n,"e",(function(){return m})),e.d(n,"i",(function(){return p})),e.d(n,"u",(function(){return _})),e.d(n,"t",(function(){return h})),e.d(n,"r",(function(){return g})),e.d(n,"s",(function(){return b})),e.d(n,"l",(function(){return v})),e.d(n,"g",(function(){return j})),e.d(n,"d",(function(){return O})),e.d(n,"b",(function(){return y})),e.d(n,"c",(function(){return w})),e.d(n,"a",(function(){return k})),e.d(n,"q",(function(){return I})),e.d(n,"p",(function(){return D})),e.d(n,"v",(function(){return G}));var i=e("b775"),o=e("6dab");e.d(n,"w",(function(){return o["i"]}));var a=e("d74f");e.d(n,"k",(function(){return a["i"]}));var l=e("3b38");e.d(n,"f",(function(){return l["a"]}));var d="/goodscoupon/admin";function u(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/lists.html"),method:"get",params:t})}function r(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/receive.html"),method:"post",data:t})}function s(t,n){return Object(i["a"])({url:"".concat(d,"/goodscoupon/deleteMemberCoupon.html?coupon_id=").concat(n),method:"post",data:t})}function c(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/export.html"),method:"post",data:t,responseType:"blob"})}function f(t){return Object(i["a"])({url:"".concat(d,"/goodsCouponRule/list.html"),method:"get",params:t})}function m(t){return Object(i["a"])({url:"".concat(d,"/goodsCouponRule/add.html"),method:"post",data:t})}function p(t){return Object(i["a"])({url:"".concat(d,"/goodsCouponRule/detailList.html"),method:"get",params:t})}function _(t){return Object(i["a"])({url:"".concat(d,"/goodsCouponRule/stopped.html"),method:"post",data:t})}function h(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/shutDown.html"),method:"post",data:t})}function g(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/sendPageData.html"),method:"get",params:t})}function b(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/sendToSelectedMember.html"),method:"post",data:t})}function v(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/goodsData.html"),method:"get",params:t})}function j(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/deleteGoods.html"),method:"post",data:t})}function O(t){return Object(i["a"])({url:"".concat(d,"/goodscoupon/addGoods.html"),method:"post",data:t})}function y(t){return Object(i["a"])({url:"/admin_plus/AddonGoodscoupon/detail",method:"post",data:t})}function w(t){return Object(i["a"])({url:"/admin_plus/AddonGoodscoupon/lists",method:"post",data:t})}function k(t){return Object(i["a"])({url:"/admin_plus/AddonGoodsCouponRule/detail",method:"post",data:t})}function I(t){return Object(i["a"])({url:"/admin_plus/AddonGoodscoupon/sendPage",method:"post",data:t})}function D(t){return Object(i["a"])({url:"/goodscoupon/admin/goodsCouponRule/selectGoodsCoupon.html",method:"post",data:t})}function G(t){return Object(i["a"])({url:"/goodscoupon/admin/goodsCouponRule/tokenGetTempList.html",method:"post",data:t})}},"6dab":function(t,n,e){"use strict";e.d(n,"g",(function(){return o})),e.d(n,"i",(function(){return a})),e.d(n,"a",(function(){return l})),e.d(n,"h",(function(){return d})),e.d(n,"f",(function(){return u})),e.d(n,"d",(function(){return r})),e.d(n,"c",(function(){return s})),e.d(n,"e",(function(){return c})),e.d(n,"b",(function(){return f}));var i=e("b775");function o(t){return Object(i["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/topic/admin/topic/add.html",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:t})}function u(t){return Object(i["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:t})}function f(t){return Object(i["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:t})}},7806:function(t,n,e){"use strict";e("3d13")}}]);