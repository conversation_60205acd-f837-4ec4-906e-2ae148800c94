(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4a83c14e"],{"0630":function(t,e,r){},"0bbe":function(t,e,r){"use strict";r.d(e,"k",(function(){return n})),r.d(e,"a",(function(){return i})),r.d(e,"c",(function(){return d})),r.d(e,"g",(function(){return s})),r.d(e,"j",(function(){return o})),r.d(e,"d",(function(){return _})),r.d(e,"e",(function(){return l})),r.d(e,"n",(function(){return u})),r.d(e,"b",(function(){return c})),r.d(e,"f",(function(){return m})),r.d(e,"h",(function(){return v})),r.d(e,"o",(function(){return p})),r.d(e,"l",(function(){return f})),r.d(e,"i",(function(){return h})),r.d(e,"m",(function(){return b}));var a=r("b775");function n(t){return Object(a["a"])({url:"/admin/order/lists.html",method:"get",params:t})}function i(t){return Object(a["a"])({url:"/admin_plus/Order/lists",method:"get",params:t})}function d(t){return Object(a["a"])({url:"/admin/order/exportOrderGoods.html",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/admin/upload/file.html",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/admin/order/importOrderDelivery.html",method:"post",data:t})}function _(t){return Object(a["a"])({url:"/admin/order/exportSupplierOrders.html",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/admin/order/exportSupplierRecord.html",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/admin/Order/orderHandSync.html",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/admin_plus/Order/detail",method:"post",data:t})}function m(t){return Object(a["a"])({url:"/admin/express/expressCompany.html",method:"get",params:t})}function v(t){return Object(a["a"])({url:"/shop/address/getAreaList.html",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/admin/order/sellerRemark.html",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/admin/order/orderComplain.html",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/admin/order/getOrderInfo",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/order/delivery",method:"post",data:t})}},"4d75":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"details order"},[r("div",{staticClass:"info"},[r("div",[r("div",{staticClass:"edit_title"},[t._v("退款订单信息")]),t._v(" "),r("p",[t._v("订单编号："+t._s(t.details.order_no))]),t._v(" "),r("p",[t._v("交易号："+t._s(t.details.out_trade_no))]),t._v(" "),r("p",[t._v("供应链单号："+t._s(t.details.supply_order_no))]),t._v(" "),r("p",[t._v("下单时间："+t._s(t._f("parseTime")(t.details.create_time)))]),t._v(" "),r("p",[t._v("付款时间："+t._s(t._f("parseTime")(t.details.pay_time)))]),t._v(" "),r("p",[t._v("付款方式："+t._s(t.details.pay_type_name))]),t._v(" "),r("p",[t._v("订单总金额："+t._s(t.details.goods_money))]),t._v(" "),r("p",[t._v("订单总运费：0.00")]),t._v(" "),r("p",[t._v("订单总优惠："+t._s((100*t.details.goods_money-100*t.details.balance_money)/100))]),t._v(" "),r("p",[t._v("实付金额："+t._s(t.details.balance_money))]),t._v(" "),r("p",[t._v("会员ID："+t._s(t.details.member_id))]),t._v(" "),r("p",[t._v("会员手机号："+t._s(t.details.mobile))]),t._v(" "),r("p",[t._v("店铺名称："+t._s(t.details.site_name))]),t._v(" "),r("p",[t._v("供应商："+t._s(t.details.supply_shop_name))])])]),t._v(" "),r("div",{staticClass:"remark_info"},[r("div",[r("div",{staticClass:"edit_title"},[t._v("收货信息")]),t._v(" "),r("p",[t._v("收货人："+t._s(t.details.name))]),t._v(" "),r("p",[t._v("联系电话："+t._s(t.details.mobile))]),t._v(" "),r("p",[t._v("收货地址："+t._s(t.details.full_address)+t._s(t.details.address))])]),t._v(" "),r("div",[r("div",{staticClass:"edit_title"},[t._v("留言备注")]),t._v(" "),r("p",[t._v("买家留言：-")]),t._v(" "),r("p",[t._v("商家备注："+t._s(t.details.remark||"-"))]),t._v(" "),r("p",[t._v("管理员备注："+t._s(t.details.admin_remark||"-"))])])]),t._v(" "),r("div",{staticClass:"handle"},[r("div",[r("div",{staticClass:"edit_title"},[t._v("订单状态信息")]),t._v(" "),r("p",[t._v("订单状态："+t._s(t.details.order_status_name))]),t._v(" "),-2==t.details.order_status?r("span",{staticClass:"danger"},[t._v("商品已经申请售后， 请先处理售后订单")]):t._e(),t._v(" "),t._l(t.details.package_list,(function(e,a){return r("div",{key:a},[r("div",{staticClass:"edit_title"},[t._v(t._s(e.package_name))]),t._v(" "),r("p",[t._v("快递公司："+t._s(e.express_company_name))]),t._v(" "),r("p",[t._v("快递单号："+t._s(e.delivery_no))]),t._v(" "),r("p",[t._v("快递备注："+t._s(t.details.logistics_remark||"-"))])])}))],2),t._v(" "),t._m(0)])]),t._v(" "),r("div",{staticClass:"details"},[r("o-table",{attrs:{columns:t.columns,hideRefresh:!0,isPage:!1,data:t.details.order_goods}})],1)])},n=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"tips"},[r("span",[t._v("温馨提醒")]),t._v(" "),r("p",[t._v("如果未发货，请点击同意退款给买家。")]),t._v(" "),r("p",[t._v("如果实际已发货，请主动与买家联系。")]),t._v(" "),r("p",[t._v("如果订单整体退款后，优惠券和余额会退还给买家。")])])}],i=r("d2e6"),d=r("c57b"),s=r("b885"),o=r("0bbe"),_=(r("8975"),{data:function(){return{details:{},logsData:i["c"],dialogVisible:!1,loading:!1,columns:d["e"],list:[{sku_name:"香蕉咬咬胶",sku_no:"香蕉咬咬胶",goods_id:"745",price:"45.00",num:1,real_goods_money:"45.00"}],form:{},baseConfig:{labelWidth:"120px"},formConfig:[{type:"input",label:"退款金额（元）",model:"je",method:"number",placeholder:"请输入退款金额（元）"},{type:"input",label:"运费（元）",model:"yf",method:"number",placeholder:"请输入运费（元）"}]}},components:{FormQuery:s["d"]},mounted:function(){this.init()},methods:{init:function(){var t=this,e=this.$route.query.id;Object(o["b"])({order_id:e}).then((function(e){var r=e.data.order_detail;t.details=r}))}}}),l=_,u=(r("96fb"),r("2877")),c=Object(u["a"])(l,a,n,!1,null,"95ce56b0",null);e["default"]=c.exports},"96fb":function(t,e,r){"use strict";r("0630")}}]);