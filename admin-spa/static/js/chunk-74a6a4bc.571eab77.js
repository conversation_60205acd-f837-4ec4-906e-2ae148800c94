(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74a6a4bc"],{"0234":function(e,t,n){"use strict";n("73c3")},1945:function(e,t,n){"use strict";n("5f9c")},"253e":function(e,t,n){},"322c":function(e,t,n){},3529:function(e,t,n){},"5f9c":function(e,t,n){},"73c3":function(e,t,n){},"748f":function(e,t,n){},"76a6":function(e,t,n){"use strict";n("3529")},"792f":function(e,t,n){"use strict";n("80bf")},"7a83":function(e,t,n){"use strict";n("c5d5")},"80bf":function(e,t,n){},"8e4c":function(e,t,n){"use strict";n("cd66")},"92d7":function(e,t,n){},"93bf":function(e,t,n){
/*!
* screenfull
* v4.2.0 - 2019-04-01
* (c) Sindre Sorhus; MIT License
*/
(function(){"use strict";var t="undefined"!==typeof window&&"undefined"!==typeof window.document?window.document:{},n=e.exports,i="undefined"!==typeof Element&&"ALLOW_KEYBOARD_INPUT"in Element,r=function(){for(var e,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,r=n.length,s={};i<r;i++)if(e=n[i],e&&e[1]in t){for(i=0;i<e.length;i++)s[n[0][i]]=e[i];return s}return!1}(),s={change:r.fullscreenchange,error:r.fullscreenerror},a={request:function(e){return new Promise(function(n){var s=r.requestFullscreen,a=function(){this.off("change",a),n()}.bind(this);e=e||t.documentElement,/ Version\/5\.1(?:\.\d+)? Safari\//.test(navigator.userAgent)?e[s]():e[s](i?Element.ALLOW_KEYBOARD_INPUT:{}),this.on("change",a)}.bind(this))},exit:function(){return new Promise(function(e){if(this.isFullscreen){var n=function(){this.off("change",n),e()}.bind(this);t[r.exitFullscreen](),this.on("change",n)}else e()}.bind(this))},toggle:function(e){return this.isFullscreen?this.exit():this.request(e)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,n){var i=s[e];i&&t.addEventListener(i,n,!1)},off:function(e,n){var i=s[e];i&&t.removeEventListener(i,n,!1)},raw:r};r?(Object.defineProperties(a,{isFullscreen:{get:function(){return Boolean(t[r.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[r.fullscreenElement]}},enabled:{enumerable:!0,get:function(){return Boolean(t[r.fullscreenEnabled])}}}),n?(e.exports=a,e.exports.default=a):window.screenfull=a):n?e.exports=!1:window.screenfull=!1})()},9702:function(e,t,n){"use strict";n("322c")},a46a:function(e,t,n){"use strict";n("748f")},adca:function(e,t,n){"use strict";n("253e")},bd11:function(e,t){e.exports=m,e.exports.parse=s,e.exports.compile=a,e.exports.tokensToFunction=l,e.exports.tokensToRegExp=p;var n="/",i="./",r=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");function s(e,t){var s,a=[],l=0,u=0,d="",f=t&&t.delimiter||n,h=t&&t.delimiters||i,p=!1;while(null!==(s=r.exec(e))){var m=s[0],v=s[1],g=s.index;if(d+=e.slice(u,g),u=g+m.length,v)d+=v[1],p=!0;else{var b="",w=e[u],_=s[2],x=s[3],k=s[4],$=s[5];if(!p&&d.length){var y=d.length-1;h.indexOf(d[y])>-1&&(b=d[y],d=d.slice(0,y))}d&&(a.push(d),d="",p=!1);var C=""!==b&&void 0!==w&&w!==b,E="+"===$||"*"===$,O="?"===$||"*"===$,S=b||f,T=x||k;a.push({name:_||l++,prefix:b,delimiter:S,optional:O,repeat:E,partial:C,pattern:T?c(T):"[^"+o(S)+"]+?"})}}return(d||u<e.length)&&a.push(d+e.substr(u)),a}function a(e,t){return l(s(e,t))}function l(e){for(var t=new Array(e.length),n=0;n<e.length;n++)"object"===typeof e[n]&&(t[n]=new RegExp("^(?:"+e[n].pattern+")$"));return function(n,i){for(var r="",s=i&&i.encode||encodeURIComponent,a=0;a<e.length;a++){var l=e[a];if("string"!==typeof l){var o,c=n?n[l.name]:void 0;if(Array.isArray(c)){if(!l.repeat)throw new TypeError('Expected "'+l.name+'" to not repeat, but got array');if(0===c.length){if(l.optional)continue;throw new TypeError('Expected "'+l.name+'" to not be empty')}for(var u=0;u<c.length;u++){if(o=s(c[u],l),!t[a].test(o))throw new TypeError('Expected all "'+l.name+'" to match "'+l.pattern+'"');r+=(0===u?l.prefix:l.delimiter)+o}}else if("string"!==typeof c&&"number"!==typeof c&&"boolean"!==typeof c){if(!l.optional)throw new TypeError('Expected "'+l.name+'" to be '+(l.repeat?"an array":"a string"));l.partial&&(r+=l.prefix)}else{if(o=s(String(c),l),!t[a].test(o))throw new TypeError('Expected "'+l.name+'" to match "'+l.pattern+'", but got "'+o+'"');r+=l.prefix+o}}else r+=l}return r}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function c(e){return e.replace(/([=!:$/()])/g,"\\$1")}function u(e){return e&&e.sensitive?"":"i"}function d(e,t){if(!t)return e;var n=e.source.match(/\((?!\?)/g);if(n)for(var i=0;i<n.length;i++)t.push({name:i,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,pattern:null});return e}function f(e,t,n){for(var i=[],r=0;r<e.length;r++)i.push(m(e[r],t,n).source);return new RegExp("(?:"+i.join("|")+")",u(n))}function h(e,t,n){return p(s(e,n),t,n)}function p(e,t,r){r=r||{};for(var s=r.strict,a=!1!==r.start,l=!1!==r.end,c=o(r.delimiter||n),d=r.delimiters||i,f=[].concat(r.endsWith||[]).map(o).concat("$").join("|"),h=a?"^":"",p=0===e.length,m=0;m<e.length;m++){var v=e[m];if("string"===typeof v)h+=o(v),p=m===e.length-1&&d.indexOf(v[v.length-1])>-1;else{var g=v.repeat?"(?:"+v.pattern+")(?:"+o(v.delimiter)+"(?:"+v.pattern+"))*":v.pattern;t&&t.push(v),v.optional?v.partial?h+=o(v.prefix)+"("+g+")?":h+="(?:"+o(v.prefix)+"("+g+"))?":h+=o(v.prefix)+"("+g+")"}}return l?(s||(h+="(?:"+c+")?"),h+="$"===f?"$":"(?="+f+")"):(s||(h+="(?:"+c+"(?="+f+"))?"),p||(h+="(?="+c+"|"+f+")")),new RegExp(h,u(r))}function m(e,t,n){return e instanceof RegExp?d(e,t):Array.isArray(e)?f(e,t,n):h(e,t,n)}},c5d5:function(e,t,n){},cd66:function(e,t,n){},d21a:function(e,t,n){"use strict";n("92d7")},d9ce:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-wrapper",class:e.classObj},["mobile"===e.device&&e.sidebar.opened?n("div",{staticClass:"drawer-bg",on:{click:e.handleClickOutside}}):e._e(),e._v(" "),n("sidebar",{staticClass:"sidebar-container"}),e._v(" "),n("div",{staticClass:"main-container"},[n("navbar"),e._v(" "),n("tags-view"),e._v(" "),n("app-main")],1)],1)},r=[],s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"navbar"},[n("hamburger",{staticClass:"hamburger-container",attrs:{"toggle-click":e.toggleSideBar,"is-active":e.sidebar.opened}}),e._v(" "),n("breadcrumb",{staticClass:"breadcrumb-container"}),e._v(" "),n("div",{staticClass:"right-menu"},["mobile"!==e.device?[n("error-log",{staticClass:"errLog-container right-menu-item"}),e._v(" "),n("el-tooltip",{attrs:{content:"全屏",effect:"dark",placement:"bottom"}},[n("screenfull",{staticClass:"screenfull right-menu-item"})],1)]:e._e(),e._v(" "),n("el-dropdown",{staticClass:"avatar-container right-menu-item",attrs:{trigger:"click"}},[n("div",{staticClass:"avatar-wrapper"},[n("img",{staticClass:"user-avatar",attrs:{src:e.avatar+"?imageView2/1/w/80/h/80",alt:""}}),e._v(" "),n("i",{staticClass:"el-icon-caret-bottom"})]),e._v(" "),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("router-link",{attrs:{to:"/"}},[n("el-dropdown-item",[e._v("\n            "+e._s(e.username)+"\n          ")]),e._v(" "),n("el-dropdown-item",{attrs:{divided:""}},[e._v(" 首页 ")])],1),e._v(" "),n("el-dropdown-item",{attrs:{divided:""}},[n("span",{staticStyle:{display:"block"},on:{click:e.logout}},[e._v("退出登录")])])],1)],1)],2)],1)},a=[],l=n("5530"),o=n("2f62"),c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-breadcrumb",{staticClass:"app-breadcrumb",attrs:{separator:"/"}},[n("transition-group",{attrs:{name:"breadcrumb"}},e._l(e.levelList,(function(t,i){return n("el-breadcrumb-item",{key:t.path},[t.meta.title?["noredirect"===t.redirect||i===e.levelList.length-1?n("span",{staticClass:"no-redirect"},[e._v(e._s(t.meta.title))]):n("a",{on:{click:function(n){return n.preventDefault(),e.handleLink(t)}}},[e._v(e._s(t.meta.title))])]:e._e()],2)})),1)],1)},u=[];n("7f7f");function d(e){var t=this.$te("route."+e);if(t){var n=this.$t("route."+e);return n}return e}var f,h,p=n("bd11"),m=n.n(p),v={data:function(){return{levelList:null}},watch:{$route:function(){this.getBreadcrumb()}},created:function(){this.getBreadcrumb()},methods:{generateTitle:d,getBreadcrumb:function(){var e=this.$route.matched.filter((function(e){if(e.name)return!0})),t=e[0];t&&t.name.trim().toLocaleLowerCase()!=="Dashboard".toLocaleLowerCase()&&(e=[{path:"/dashboard",meta:{title:"首页"}}].concat(e)),this.levelList=e},pathCompile:function(e){var t=this.$route.params,n=m.a.compile(e);return n(t)},handleLink:function(e){var t=e.redirect,n=e.path;t?this.$router.push(t):this.$router.push(this.pathCompile(n))}}},g=v,b=(n("0234"),n("2877")),w=Object(b["a"])(g,c,u,!1,null,"ce04ec4e",null),_=w.exports,x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("svg",{staticClass:"hamburger",class:{"is-active":e.isActive},attrs:{t:"1492500959545",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1691","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"64",height:"64"},on:{click:e.toggleClick}},[n("path",{attrs:{d:"M966.8023 568.849776 57.196677 568.849776c-31.397081 0-56.850799-25.452695-56.850799-56.850799l0 0c0-31.397081 25.452695-56.849776 56.850799-56.849776l909.605623 0c31.397081 0 56.849776 25.452695 56.849776 56.849776l0 0C1023.653099 543.397081 998.200404 568.849776 966.8023 568.849776z","p-id":"1692"}}),e._v(" "),n("path",{attrs:{d:"M966.8023 881.527125 57.196677 881.527125c-31.397081 0-56.850799-25.452695-56.850799-56.849776l0 0c0-31.397081 25.452695-56.849776 56.850799-56.849776l909.605623 0c31.397081 0 56.849776 25.452695 56.849776 56.849776l0 0C1023.653099 856.07443 998.200404 881.527125 966.8023 881.527125z","p-id":"1693"}}),e._v(" "),n("path",{attrs:{d:"M966.8023 256.17345 57.196677 256.17345c-31.397081 0-56.850799-25.452695-56.850799-56.849776l0 0c0-31.397081 25.452695-56.850799 56.850799-56.850799l909.605623 0c31.397081 0 56.849776 25.452695 56.849776 56.850799l0 0C1023.653099 230.720755 998.200404 256.17345 966.8023 256.17345z","p-id":"1694"}})])])},k=[],$={name:"Hamburger",props:{isActive:{type:Boolean,default:!1},toggleClick:{type:Function,default:null}}},y=$,C=(n("9702"),Object(b["a"])(y,x,k,!1,null,"1d409a8a",null)),E=C.exports,O=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.errorLogs.length>0?n("div",[n("el-badge",{staticStyle:{"line-height":"30px"},attrs:{"is-dot":!0},nativeOn:{click:function(t){e.dialogTableVisible=!0}}},[n("el-button",{staticClass:"bug-btn",attrs:{size:"small",type:"danger"}},[n("svg",{staticClass:"bug-svg",attrs:{t:"1492682037685",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1863","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"128",height:"128"}},[n("path",{attrs:{d:"M969.142857 548.571429q0 14.848-10.861714 25.709714t-25.709714 10.861714l-128 0q0 97.718857-38.290286 165.705143l118.857143 119.442286q10.861714 10.861714 10.861714 25.709714t-10.861714 25.709714q-10.276571 10.861714-25.709714 10.861714t-25.709714-10.861714l-113.152-112.566857q-2.852571 2.852571-8.557714 7.424t-23.990857 16.274286-37.156571 20.845714-46.848 16.566857-55.442286 7.424l0-512-73.142857 0 0 512q-29.147429 0-58.002286-7.716571t-49.700571-18.870857-37.705143-22.272-24.868571-18.578286l-8.557714-8.009143-104.557714 118.272q-11.446857 11.995429-27.428571 11.995429-13.714286 0-24.576-9.142857-10.861714-10.276571-11.702857-25.417143t8.850286-26.587429l115.419429-129.718857q-33.133714-65.133714-33.133714-156.562286l-128 0q-14.848 0-25.709714-10.861714t-10.861714-25.709714 10.861714-25.709714 25.709714-10.861714l128 0 0-168.009143-98.852571-98.852571q-10.861714-10.861714-10.861714-25.709714t10.861714-25.709714 25.709714-10.861714 25.709714 10.861714l98.852571 98.852571 482.304 0 98.852571-98.852571q10.861714-10.861714 25.709714-10.861714t25.709714 10.861714 10.861714 25.709714-10.861714 25.709714l-98.852571 98.852571 0 168.009143 128 0q14.848 0 25.709714 10.861714t10.861714 25.709714zM694.857143 219.428571l-365.714286 0q0-75.995429 53.430857-129.426286t129.426286-53.430857 129.426286 53.430857 53.430857 129.426286z","p-id":"1864"}})])])],1),e._v(" "),n("el-dialog",{attrs:{visible:e.dialogTableVisible,title:"Error Log",width:"80%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[n("el-table",{attrs:{data:e.errorLogs,border:""}},[n("el-table-column",{attrs:{label:"Message"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[n("span",{staticClass:"message-title"},[e._v("Msg:")]),e._v(" "),n("el-tag",{attrs:{type:"danger"}},[e._v(e._s(t.row.err.message))])],1),e._v(" "),n("br"),e._v(" "),n("div",[n("span",{staticClass:"message-title",staticStyle:{"padding-right":"10px"}},[e._v("Info: ")]),e._v(" "),n("el-tag",{attrs:{type:"warning"}},[e._v(e._s(t.row.vm.$vnode.tag)+" error in "+e._s(t.row.info))])],1),e._v(" "),n("br"),e._v(" "),n("div",[n("span",{staticClass:"message-title",staticStyle:{"padding-right":"16px"}},[e._v("Url: ")]),e._v(" "),n("el-tag",{attrs:{type:"success"}},[e._v(e._s(t.row.url))])],1)]}}],null,!1,3429815996)}),e._v(" "),n("el-table-column",{attrs:{label:"Stack"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.err.stack)+"\n        ")]}}],null,!1,1726869048)})],1)],1)],1):e._e()},S=[],T={name:"ErrorLog",data:function(){return{dialogTableVisible:!1}},computed:{errorLogs:function(){return this.$store.getters.errorLogs}}},L=T,j=(n("7a83"),Object(b["a"])(L,O,S,!1,null,"a5939d82",null)),F=j.exports,M=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("svg",{staticClass:"screenfull-svg",attrs:{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",width:"32",height:"32",t:"1508738709248","p-id":"2069"},on:{click:e.click}},[n("path",{attrs:{d:"M333.493443 428.647617 428.322206 333.832158 262.572184 168.045297 366.707916 64.444754 64.09683 64.444754 63.853283 366.570793 167.283957 262.460644Z","p-id":"2070"}}),e._v(" "),n("path",{attrs:{d:"M854.845439 760.133334 688.61037 593.95864 593.805144 688.764889 759.554142 854.56096 655.44604 958.161503 958.055079 958.161503 958.274066 656.035464Z","p-id":"2071"}}),e._v(" "),n("path",{attrs:{d:"M688.535669 428.550403 854.31025 262.801405 957.935352 366.921787 957.935352 64.34754 655.809313 64.081481 759.919463 167.535691 593.70793 333.731874Z","p-id":"2072"}}),e._v(" "),n("path",{attrs:{d:"M333.590658 594.033341 167.8171 759.804852 64.218604 655.67219 64.218604 958.270996 366.342596 958.502263 262.234493 855.071589 428.421466 688.86108Z","p-id":"2073"}})])])},V=[],q=(n("c5f6"),n("93bf")),A=n.n(q),P={name:"Screenfull",props:{width:{type:Number,default:22},height:{type:Number,default:22},fill:{type:String,default:"#48576a"}},data:function(){return{isFullscreen:!1}},methods:{click:function(){if(!A.a.enabled)return this.$message({message:"you browser can not work",type:"warning"}),!1;A.a.toggle()}}},B=P,z=(n("d21a"),Object(b["a"])(B,M,V,!1,null,"dc6c2dc8",null)),R=z.exports,D={components:{Breadcrumb:_,Hamburger:E,ErrorLog:F,Screenfull:R},computed:Object(l["a"])({},Object(o["b"])(["sidebar","username","avatar","device"])),methods:{toggleSideBar:function(){this.$store.dispatch("toggleSideBar")},logout:function(){var e=this;this.$store.dispatch("LogOut").then((function(){sessionStorage.removeItem("isLogin"),e.$router.push("/login")}))}}},I=D,N=(n("adca"),Object(b["a"])(I,s,a,!1,null,"018d2360",null)),W=N.exports,H=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-scrollbar",{attrs:{"wrap-class":"scrollbar-wrapper"}},[e._v("\n  "+e._s(e.openeds)+"\n  "),n("el-menu",{attrs:{"show-timeout":200,"default-openeds":e.openeds,"default-active":e.$route.path,collapse:e.isCollapse,mode:"vertical","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#4685FD"}},e._l(e.permission_routers,(function(e){return n("sidebar-item",{key:e.path,attrs:{item:e,"base-path":e.path}})})),1)],1)},U=[],Y=(n("2909"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return!e.item.hidden&&e.item.children?n("div",{staticClass:"menu-wrapper"},[!e.hasOneShowingChild(e.item.children,e.item)||e.onlyOneChild.children&&!e.onlyOneChild.noShowingChildren||e.item.alwaysShow?n("el-submenu",{ref:"submenu",attrs:{index:e.resolvePath(e.item.path)}},[n("template",{slot:"title"},[e.item.meta?n("item",{attrs:{icon:e.item.meta.icon,title:e.item.meta.title}}):e._e()],1),e._v(" "),e._l(e.item.children,(function(t){return[t.hidden?e._e():[t.children&&t.children.length>0?n("sidebar-item",{key:t.path,staticClass:"nest-menu",attrs:{"is-nest":!0,item:t,"base-path":e.resolvePath(t.path)}}):n("app-link",{key:t.name,attrs:{to:e.resolvePath(t.path)}},[n("el-menu-item",{attrs:{index:e.resolvePath(t.path)}},[t.meta?n("item",{attrs:{icon:t.meta.icon,title:t.meta.title}}):e._e()],1)],1)]]}))],2):[n("app-link",{attrs:{to:e.resolvePath(e.onlyOneChild.path)}},[n("el-menu-item",{class:{"submenu-title-noDropdown":!e.isNest},attrs:{index:e.resolvePath(e.onlyOneChild.path)}},[e.onlyOneChild.meta?n("item",{attrs:{icon:e.onlyOneChild.meta.icon||e.item.meta.icon,title:e.onlyOneChild.meta.title}}):e._e()],1)],1)]],2):e._e()}),Z=[],J=n("df7c"),K=n.n(J),X=n("ed08"),G={name:"MenuItem",functional:!0,props:{icon:{type:String,default:""},title:{type:String,default:""}},render:function(e,t){var n=t.props,i=n.icon,r=n.title,s=[];return i&&s.push(e("svg-icon",{attrs:{"icon-class":i}})),r&&s.push(e("span",{slot:"title"},[r])),s}},Q=G,ee=Object(b["a"])(Q,f,h,!1,null,null,null),te=ee.exports,ne=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("component",e._b({},"component",e.linkProps(e.to),!1),[e._t("default")],2)},ie=[],re={props:{to:{type:String,required:!0}},methods:{isExternalLink:function(e){return Object(X["c"])(e)},linkProps:function(e){return this.isExternalLink(e)?{is:"a",href:e,target:"_blank",rel:"noopener"}:{is:"router-link",to:e}}}},se=re,ae=Object(b["a"])(se,ne,ie,!1,null,null,null),le=ae.exports,oe={computed:{device:function(){return this.$store.state.app.device}},mounted:function(){this.fixBugIniOS()},methods:{fixBugIniOS:function(){var e=this,t=this.$refs.submenu;if(t){var n=t.handleMouseleave;t.handleMouseleave=function(t){"mobile"!==e.device&&n(t)}}}}},ce={name:"SidebarItem",components:{Item:te,AppLink:le},mixins:[oe],props:{item:{type:Object,required:!0},isNest:{type:Boolean,default:!1},basePath:{type:String,default:""}},data:function(){return{onlyOneChild:null}},methods:{hasOneShowingChild:function(e,t){var n=this,i=e.filter((function(e){return!e.hidden&&(n.onlyOneChild=e,!0)}));return 1===i.length||0===i.length&&(this.onlyOneChild=Object(l["a"])(Object(l["a"])({},t),{},{path:"",noShowingChildren:!0}),!0)},resolvePath:function(e){return this.isExternalLink(e)?e:K.a.resolve(this.basePath,e)},isExternalLink:function(e){return Object(X["c"])(e)}}},ue=ce,de=Object(b["a"])(ue,Y,Z,!1,null,null,null),fe=de.exports;var he={components:{SidebarItem:fe},computed:Object(l["a"])(Object(l["a"])({},Object(o["b"])(["permission_routers","sidebar"])),{},{isCollapse:function(){return!this.sidebar.opened}}),data:function(){return{openeds:["/goods","/goods/quality/quality","/order","/member","/member/tagManage/tagManage"]}},mounted:function(){}},pe=he,me=(n("792f"),Object(b["a"])(pe,H,U,!1,null,"68f8e8db",null)),ve=me.exports,ge=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tags-view-container"},[n("scroll-pane",{ref:"scrollPane",staticClass:"tags-view-wrapper"},e._l(e.visitedViews,(function(t){return n("router-link",{key:t.path,ref:"tag",refInFor:!0,staticClass:"tags-view-item",class:e.isActive(t)?"active":"",attrs:{to:{path:t.path,query:t.query,fullPath:t.fullPath},tag:"span"},nativeOn:{mouseup:function(n){return"button"in n&&1!==n.button?null:e.closeSelectedTag(t)},contextmenu:function(n){return n.preventDefault(),e.openMenu(t,n)}}},[e._v("\n      "+e._s(t.meta.title)+"\n      "),n("span",{staticClass:"el-icon-close",on:{click:function(n){return n.preventDefault(),n.stopPropagation(),e.closeSelectedTag(t)}}})])})),1),e._v(" "),n("ul",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"contextmenu",style:{left:e.left+"px",top:e.top+"px"}},[n("li",{on:{click:function(t){return e.refreshSelectedTag(e.selectedTag)}}},[e._v("刷新")]),e._v(" "),n("li",{on:{click:function(t){return e.closeSelectedTag(e.selectedTag)}}},[e._v("关闭")]),e._v(" "),n("li",{on:{click:e.closeOthersTags}},[e._v("关闭其它")]),e._v(" "),n("li",{on:{click:e.closeAllTags}},[e._v("关闭所有")])])],1)},be=[],we=(n("a481"),n("b85c")),_e=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-scrollbar",{ref:"scrollContainer",staticClass:"scroll-container",attrs:{vertical:!1},nativeOn:{wheel:function(t){return t.preventDefault(),e.handleScroll(t)}}},[e._t("default")],2)},xe=[],ke=4,$e={name:"ScrollPane",data:function(){return{left:0}},methods:{handleScroll:function(e){var t=e.wheelDelta||40*-e.deltaY,n=this.$refs.scrollContainer.$refs.wrap;n.scrollLeft=n.scrollLeft+t/4},moveToTarget:function(e){var t=this.$refs.scrollContainer.$el,n=t.offsetWidth,i=this.$refs.scrollContainer.$refs.wrap,r=this.$parent.$refs.tag,s=null,a=null,l=null,o=null;r.length>0&&(s=r[0],a=r[r.length-1]);for(var c=0;c<r.length;c++)if(r[c]===e){0===c?o=r[c].length>1&&r[c+1]:c===r.length-1?l=r[c].length>1&&r[c-1]:(l=r[c-1],o=r[c+1]);break}if(s===e)i.scrollLeft=0;else if(a===e)i.scrollLeft=i.scrollWidth-n;else{var u=o.$el.offsetLeft+o.$el.offsetWidth+ke,d=l.$el.offsetLeft-ke;u>i.scrollLeft+n?i.scrollLeft=u-n:d<i.scrollLeft&&(i.scrollLeft=d)}}}},ye=$e,Ce=(n("1945"),Object(b["a"])(ye,_e,xe,!1,null,"2c036037",null)),Ee=Ce.exports,Oe={components:{ScrollPane:Ee},data:function(){return{visible:!1,top:0,left:0,selectedTag:{}}},computed:{visitedViews:function(){return console.log(this.$store.state.tagsView.visitedViews),this.$store.state.tagsView.visitedViews}},watch:{$route:function(){this.addViewTags(),this.moveToCurrentTag()},visible:function(e){e?document.body.addEventListener("click",this.closeMenu):document.body.removeEventListener("click",this.closeMenu)}},mounted:function(){this.addViewTags()},methods:{generateTitle:d,isActive:function(e){return e.path===this.$route.path},addViewTags:function(){var e=this.$route.meta;return e&&this.$store.dispatch("addView",this.$route),!1},moveToCurrentTag:function(){var e=this,t=this.$refs.tag;this.$nextTick((function(){var n,i=Object(we["a"])(t);try{for(i.s();!(n=i.n()).done;){var r=n.value;if(r.to.path===e.$route.path){e.$refs.scrollPane.moveToTarget(r),r.to.fullPath!==e.$route.fullPath&&e.$store.dispatch("updateVisitedView",e.$route);break}}}catch(s){i.e(s)}finally{i.f()}}))},refreshSelectedTag:function(e){var t=this;this.$store.dispatch("delCachedView",e).then((function(){var n=e.fullPath;t.$nextTick((function(){t.$router.replace({path:"/redirect"+n})}))}))},closeSelectedTag:function(e){var t=this;this.$store.dispatch("delView",e).then((function(n){var i=n.visitedViews;if(console.log(e),t.isActive(e)){var r=i.slice(-1)[0];r?t.$router.push(r):t.$router.push("/")}}))},closeOthersTags:function(){var e=this;this.$router.push(this.selectedTag),this.$store.dispatch("delOthersViews",this.selectedTag).then((function(){e.moveToCurrentTag()}))},closeAllTags:function(){this.$store.dispatch("delAllViews"),this.$router.push("/")},openMenu:function(e,t){var n=105,i=this.$el.getBoundingClientRect().left,r=this.$el.offsetWidth,s=r-n,a=t.clientX-i+15;this.left=a>s?s:a,this.top=t.clientY,this.visible=!0,this.selectedTag=e},closeMenu:function(){this.visible=!1}}},Se=Oe,Te=(n("8e4c"),n("a46a"),Object(b["a"])(Se,ge,be,!1,null,"da30df5c",null)),Le=Te.exports,je=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"app-main"},[n("transition",{attrs:{name:"fade-transform",mode:"out-in"}},[n("keep-alive",{attrs:{include:e.cachedViews}},[n("router-view",{key:e.key})],1)],1)],1)},Fe=[],Me={name:"AppMain",computed:{cachedViews:function(){return this.$store.state.tagsView.cachedViews},key:function(){return this.$route.fullPath}}},Ve=Me,qe=(n("fb86"),Object(b["a"])(Ve,je,Fe,!1,null,"5160aaa1",null)),Ae=qe.exports,Pe=n("4360"),Be=document,ze=Be.body,Re=1024,De=3,Ie={watch:{$route:function(e){"mobile"===this.device&&this.sidebar.opened&&Pe["a"].dispatch("closeSideBar",{withoutAnimation:!1})}},beforeMount:function(){window.addEventListener("resize",this.resizeHandler)},mounted:function(){var e=this.isMobile();e&&(Pe["a"].dispatch("toggleDevice","mobile"),Pe["a"].dispatch("closeSideBar",{withoutAnimation:!0}))},methods:{isMobile:function(){var e=ze.getBoundingClientRect();return e.width-De<Re},resizeHandler:function(){if(!document.hidden){var e=this.isMobile();Pe["a"].dispatch("toggleDevice",e?"mobile":"desktop"),e&&Pe["a"].dispatch("closeSideBar",{withoutAnimation:!0})}}}},Ne={name:"Layout",components:{Navbar:W,Sidebar:ve,AppMain:Ae,TagsView:Le},mixins:[Ie],computed:{sidebar:function(){return this.$store.state.app.sidebar},device:function(){return this.$store.state.app.device},classObj:function(){return{hideSidebar:!this.sidebar.opened,openSidebar:this.sidebar.opened,withoutAnimation:this.sidebar.withoutAnimation,mobile:"mobile"===this.device}}},methods:{handleClickOutside:function(){this.$store.dispatch("closeSideBar",{withoutAnimation:!1})}}},We=Ne,He=(n("76a6"),Object(b["a"])(We,i,r,!1,null,"91f0c9d2",null));t["default"]=He.exports},da63:function(e,t,n){},fb86:function(e,t,n){"use strict";n("da63")}}]);