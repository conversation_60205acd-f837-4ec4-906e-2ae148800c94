(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-29825e1e"],{1:function(e,t){},"1b62":function(e,t,l){"use strict";l.d(t,"a",(function(){return n}));l("ac6a");var a=l("ed08"),n={data:function(){return{loading:!0,total:0,form:{page:1,pageSize:10},initForm:{},list:[]}},mounted:function(){this.initForm=Object(a["a"])(this.form)},methods:{handleQuery:function(){this.form.page=1,this.getTableList()},handleReset:function(){this.form=Object(a["a"])(this.initForm),this.getTableList()}}}},2:function(e,t){},"349e":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("div",{staticClass:"filter-container"},[l("formQuery",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.options},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),l("div",{staticClass:"flex-b-c"},[l("buttomCustom",{attrs:{config:e.btnConfig},on:{handleQuery:e.handleQuery,handleReset:e.handleReset}}),e._v(" "),l("rightToolbar",{attrs:{showSearch:e.showSearch,columns:e.columns},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getTableList}})],1)],1),e._v(" "),l("tableCustom",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tableCustom",attrs:{columns:e.columns,tableData:e.list},scopedSlots:e._u([{key:"refund_apply_money",fn:function(t){var l=t.row;return[e._v("\n      "+e._s(l.refund_apply_money)+" "+e._s("MAIDOU"==l.pay_type?"迈豆":"")+"\n    ")]}},{key:"pay_type_name",fn:function(t){var a=t.row;return[l("p",[e._v(e._s(a.pay_type_name)+" "),l("br"),e._v(" "+e._s(a.app_type_name))])]}},{key:"refund_action_time",fn:function(t){var a=t.row;return[l("p",[e._v(e._s(e._f("parseTime")(a.refund_action_time)))])]}},{key:"action_action_time",fn:function(t){var a=t.row;return[l("p",[e._v(e._s(e._f("parseTime")(a.action_action_time)))])]}},{key:"event",fn:function(t){var a=t.row;return[l("buttonSingle",{attrs:{permission:"view",text:"详情",icon:"el-icon-view"},on:{click:function(t){return e.handleDetails(a)}}}),e._v(" "),1==a.refund_status?l("buttonSingle",{attrs:{permission:"edit",text:"退款审批",icon:"el-icon-edit"},on:{click:function(t){return e.handleRefundApproval(a)}}}):e._e()]}}])}),e._v(" "),l("div",{staticClass:"tr"},[l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>e.form.pageSize,expression:"total > form.pageSize"}],attrs:{total:e.total,page:e.form.page,limit:e.form.pageSize},on:{"update:page":function(t){return e.$set(e.form,"page",t)},"update:limit":function(t){return e.$set(e.form,"pageSize",t)},pagination:e.getTableList}})],1),e._v(" "),l("approval",{attrs:{visible:e.isApproval,row:e.row,"op-type":"refundApply_RefundApproval"},on:{"update:visible":function(t){e.isApproval=t},OnRefundApplyRefundReview:e.getTableList}})],1)},n=[],o=l("5530"),i=l("b885"),r=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{title:"售后申请",visible:e.dialogVisible,top:"100px",width:"600px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[l("formCustom",{key:e.refresh,ref:"formCustom",attrs:{config:e.formConfig,baseConfig:e.baseConfig},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("buttomCustom",{key:e.refresh,attrs:{config:e.btnConfig,marginRight:"20px"},on:{handleReject:e.handleReject,handleSubmit:e.handleSubmit}})],1)],1)},u=[],s=(l("6762"),l("b775"));function d(e){return Object(s["a"])({url:"/goods/product/state/",method:"post"})}function c(e){return Object(s["a"])({url:"/goods/product/state/",method:"post"})}var p=l("ed08"),m=l("e585"),f=(l("322d"),{components:{FormCustom:i["c"],ButtomCustom:i["a"]},props:{visible:{type:Boolean,default:!1},row:{type:Object,default:function(){}},opType:{type:String}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{dialogVisible:function(e){e&&"refundApply_RefundApproval"==this.opType&&this.getOrderChainStatusInfo()}},data:function(){return{isLoading:!0,refresh:!1,form:{},baseConfig:{textareaWidth:"90%",isBtn:!1},formConfig:[{type:"text",label:"供应链申请状态",model:"supply_chain_status_text",show:{model:"supply_chain_status_text",value:""}},{type:"text",label:"退款单号",model:"refund_no"},{type:"text",label:"申请时间",model:"refund_action_time"},{type:"text",label:"申请理由",model:"refund_remark"},{type:"textarea",label:"审核备注",model:"remark",max:100,placeholder:"请输入审核备注"},{type:"text",label:"分销商优惠",model:"promotion_money"},{type:"text",label:"折扣优惠",model:"multiple_discount_money"},{type:"text",label:"优惠券",model:"goodscoupon_money"},{type:"text",label:"秒杀活动",model:"seckill_discount_money"},{type:"text",label:"实付金额",model:"real_pay_money"},{type:"text",label:"退款金额（元）",model:"refund_apply_money"}],btnConfig:[{type:"danger",text:"驳回",plain:!0,size:"small",event:"handleReject"},{type:"primary",size:"small",text:"通过",event:"handleSubmit"}],supply_chain_status:""}},created:function(){},methods:{initData:function(){this.supply_chain_status="",this.form={},this.isLoading=!0},getOrderChainStatusInfo:function(){var e=this;this.initData(),console.log("this.row.supply_refund_id",this.row.supply_refund_id);for(var t=["promotion_money","multiple_discount_money","goodscoupon_money","seckill_discount_money"],l=0;l<this.formConfig.length;l++)this.form[this.formConfig[l].model]=this.row[this.formConfig[l].model],t.includes(this.formConfig[l].model)&&parseFloat(this.row[this.formConfig[l].model])<=0&&(this.formConfig[l].show={model:this.formConfig[l].model,value:""}),"real_pay_money"==this.formConfig[l].model&&"MAIDOU"==this.row.pay_type&&(this.formConfig[l].label="实付迈豆"),"refund_apply_money"==this.formConfig[l].model&&"MAIDOU"==this.row.pay_type&&(this.formConfig[l].label="退款迈豆"),"supply_chain_status_text"==this.formConfig[l].model&&(this.row.supply_refund_id>0?(this.formConfig[l].show={},this.form[this.formConfig[l].model]="正在同步供应链状态，请稍候..."):this.formConfig[l].show={model:"supply_chain_status_text",value:""});this.row.supply_refund_id>0?Object(m["g"])({order_id:this.row.order_id,order_goods_id:this.row.order_goods_id}).then((function(t){var l="";t instanceof Object&&0==t.code?(l=t.data.status_text,e.supply_chain_status=t.data.status):l="同步供应链状态失败！";for(var a=0;a<e.formConfig.length;a++)if("supply_chain_status_text"==e.formConfig[a].model){e.form[e.formConfig[a].model]=l;break}e.refresh=!e.refresh,e.isLoading=!1})):this.isLoading=!1},getDeliverInfo:function(){var e=this;d({refundNo:this.refundNo}).then((function(t){200===t.code&&(e.form=t.data)}))},handleSubmit:function(){var e=this;this.$refs.formCustom.$refs.formCustom.validate((function(t){if(!t)return!1;e.confirmDeliver()}))},confirmDeliver:function(){var e=this;if("refundApply_RefundApproval"==this.opType){if(this.row.supply_refund_id&&-1!=this.supply_chain_status&&"complete"!==this.supply_chain_status)return this.$message({message:{apply:"供应链未审核，不能通过申请",reject:"供应链已驳回，无法通过申请"}[this.supply_chain_status]||this.supply_chain_status,type:"error"}),!1;var t="元";"MAIDOU"==this.row.pay_type&&(t="迈豆"),this.$confirm("审核通过后，将把退款金额"+this.row.refund_apply_money+t+"原路退回至支付账户，确定审核通过？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(m["n"])({order_goods_id:e.row.order_goods_id,admin_remark:e.form.admin_remark,review_type:"setPass"}).then((function(t){e.$message({message:t.message}),0==t.code&&e.$emit("OnRefundApplyRefundReview")}))}))}else this.ids&&0!==this.ids.length||this.$message({message:"请选择要售后的商品",type:"info"}),c(this.form).then((function(t){200===t.code&&(e.$message({message:"发货成功",type:"success"}),e.dialogVisible=!1)}))},handleReject:function(){var e=this;if("refundApply_RefundApproval"==this.opType){if(this.row.supply_refund_id&&-1!=this.supply_chain_status&&-1===["reject","apply"].indexOf(this.supply_chain_status))return this.$message({message:{complete:"供应链已同意，无法驳回"}[this.supply_chain_status]||this.supply_chain_status,type:"error"}),!1;this.$confirm("审核驳回后，表示不通过用户的订单退款申请，确定审核驳回？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(m["n"])({order_goods_id:e.row.order_goods_id,admin_remark:e.form.admin_remark,review_type:"setFail"}).then((function(t){e.$message({message:t.message}),0==t.code&&e.$emit("OnRefundApplyRefundReview")}))}))}},handleClose:function(){this.dialogVisible=!1}}}),b=f,h=l("2877"),v=Object(h["a"])(b,r,u,!1,null,null,null),_=v.exports;l("96eb");var y=l("1b62"),g=l("6b2c"),w=l("c71b"),O={name:"orderList",components:{FormQuery:i["d"],ButtomCustom:i["a"],TableCustom:i["g"],Pagination:i["e"],RightToolbar:i["f"],ButtonSingle:i["b"],approval:_},directives:{permission:i["h"]},mixins:[y["a"]],data:function(){return{showSearch:!0,isApproval:!1,refundNo:null,row:{},form:{order_status:"",refund_status:""},options:{orderStateOptions:w["D"],refundStateOptions:w["E"]},baseConfig:{labelWidth:"120px"},formConfig:g["b"],btnConfig:[{type:"primary",text:"搜索",icon:"el-icon-search",event:"handleQuery"},{type:"",text:"重置",icon:"el-icon-refresh",event:"handleReset"}],columns:[{key:0,prop:"order_no",label:"订单号",visible:!0,minWidth:"180"},{key:1,prop:"refund_no",label:"退款单号",visible:!0,minWidth:"180"},{key:2,prop:"goods_id",label:"商品ID",visible:!0,minWidth:"100"},{key:2,prop:"goods_name",label:"商品名称",visible:!0,minWidth:"100"},{key:3,prop:"member_id",label:"会员ID",visible:!0,minWidth:"100"},{key:4,prop:"mobile",label:"会员手机号",visible:!0,minWidth:"120"},{key:5,prop:"pay_type_name",label:"支付方式",slot:!0,visible:!0,minWidth:"100"},{key:6,prop:"refund_apply_money",label:"退款金额",slot:!0,visible:!0,minWidth:"100"},{key:7,prop:"refund_remark",label:"申请原因",visible:!0,minWidth:"200"},{key:8,prop:"order_status_name",label:"订单状态",visible:!0,minWidth:"100"},{key:8,prop:"refund_status_name",label:"退款状态",visible:!0,minWidth:"100"},{key:8,prop:"refund_action_time",label:"申请时间",slot:!0,visible:!0,minWidth:"100"},{key:8,prop:"action_action_time",label:"处理时间",slot:!0,visible:!0,minWidth:"100"},{key:8,prop:"action_username",label:"操作人",visible:!0,minWidth:"100"},{action:!0,label:"操作",minWidth:"100"}]}},created:function(){this.getTableList()},methods:{getTableList:function(){var e=this,t=Object(o["a"])({page_size:this.form.pageSize,start_time:this.form.create_time?Object(p["d"])(this.form.create_time[0]):"",end_time:this.form.create_time?Object(p["d"])(this.form.create_time[1]):"",start_handle_time:this.form.dispose_time?Object(p["d"])(this.form.dispose_time[0]):"",end_handle_time:this.form.dispose_time?Object(p["d"])(this.form.dispose_time[1]):""},this.form);this.loading=!0,Object(m["m"])(t).then((function(t){0==t.code&&(e.total=t.data.page_count,e.list=t.data.list,e.loading=!1)})).catch((function(e){}))},handleDetails:function(e){this.$router.push({path:"/order/refundDetails",query:{order_goods_id:e.order_goods_id}})},handleRefundApproval:function(e){this.refundNo=e.refund,e.refund_action_time=Object(p["d"])(e.refund_action_time),this.row=e,this.isApproval=!0}}},C=O,x=Object(h["a"])(C,a,n,!1,null,null,null);t["default"]=x.exports},"3f5e":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return o})),l.d(t,"a",(function(){return i}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,l){"use strict";var a=l("a18c"),n={inserted:function(e,t,l){var n=t.value,o=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;o.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},o=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(o)),n.install=o;t["a"]=n},6396:function(e,t,l){"use strict";l.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,l){var i=o(),r=e-i,u=20,s=0;t="undefined"===typeof t?500:t;var d=function e(){s+=u;var o=Math.easeInOutQuad(s,i,r,t);n(o),s<t?a(e):l&&"function"===typeof l&&l()};d()}},6724:function(e,t,l){"use strict";l("8d41");var a={bind:function(e,t){e.addEventListener("click",(function(l){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),o=n.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var i=o.getBoundingClientRect(),r=o.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(i.width,i.height)+"px",o.appendChild(r)),n.type){case"center":r.style.top=i.height/2-r.offsetHeight/2+"px",r.style.left=i.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(l.pageY-i.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(l.pageX-i.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;t["a"]=a},"6b2c":function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"d",(function(){return n})),l.d(t,"c",(function(){return o})),l.d(t,"b",(function(){return i}));var a=[{type:"input",label:"会员Id",model:"member_id",placeholder:"请输入会员ID"},{type:"input",label:"手机号",model:"mobile",placeholder:"请输入手机号"},{type:"input",label:"当前锁定店铺",model:"shop_member_name",placeholder:"请输入当前锁定店铺"},{type:"select",label:"用户标签",model:"tag_ids",options:{name:"userTagsOptions"}},{type:"input",label:"注册推荐人",model:"parent_mobile",placeholder:"请输入注册推荐人"},{type:"select",label:"组别",model:"group_id",options:{name:"userGroupOptions"}},{type:"select",label:"状态",model:"state",options:{name:"memberStatusOpt"}},{type:"select",label:"企微匹配状态",model:"is_shopping_status",options:{name:"mateStatusOpt"}},{type:"select",label:"账号状态",model:"status",options:{name:"accountStatusOpt"}},{type:"time",label:"注册时间",model:"created_time"}],n=[{type:"input",label:"店主ID",model:"shop_id",placeholder:"请输入店主ID"},{type:"input",label:"店主名称",model:"search_text",placeholder:"请输入店主名称"},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"select",label:"店铺状态",model:"shop_status",placeholder:"请输入店铺状态",options:{name:"shopStatusOpts"}},{type:"select",label:"绑定状态",model:"bing_status",placeholder:"请输入绑定状态",options:{name:"bindStatusOpts"}},{type:"select",label:"店主等级",model:"vip_level_name",placeholder:"请输入店主等级",options:{name:"StoreOwnerLevelOpts"}},{type:"date",label:"付费时间",model:"vip_open_time"},{type:"date",label:"付费到期时间",model:"vip_expired_time"},{type:"time",label:"入驻时间",model:"create_time"},{type:"time",label:"到期时间",model:"expired_time"}],o=[{type:"input",label:"代理商名称",model:"enterprise_name",disabled:!0},{type:"select",label:"店铺导师",model:"mentor_id",placeholder:"请选择",options:{name:"mentorOpts"}},{type:"select",label:"店铺会长",model:"president_id",placeholder:"请选择",options:{name:"presidentOpts"}},{type:"input",label:"店主名称",model:"site_name",placeholder:"请输入2-12位中英文"},{type:"input",label:"店主等级",model:"vip_level_name",disabled:!0},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"input",label:"店主微信号",model:"ww",placeholder:"请输入店主微信号"},{type:"date",label:"到期时间",model:"expire_time",placeholder:"请输入到期时间",dateType:"datetime"},{type:"input",label:"店主账号",model:"username",placeholder:"请输入店主账号",disabled:!0},{type:"input",label:"修改登录密码",model:"password",placeholder:"请输入登录密码"},{type:"radio",label:"是否是会长",model:"is_president",options:{name:"persiRadio"}}],i=[{type:"time",timeType:"datetimerange",label:"下单时间",model:"create_time"},{type:"time",timeType:"datetimerange",label:"处理时间",model:"dispose_time"},{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"退款单号",model:"refund_no",placeholder:"请输入退款单号"},{type:"select",label:"订单状态",model:"order_status",options:{name:"orderStateOptions"}},{type:"select",label:"退款状态",model:"refund_status",options:{name:"refundStateOptions"}}]},"8d41":function(e,t,l){},b885:function(e,t,l){"use strict";var a=l("e780");l.d(t,"d",(function(){return a["a"]}));var n=l("ad41");l.d(t,"c",(function(){return n["a"]}));var o=l("0476");l.d(t,"g",(function(){return o["a"]}));var i=l("6eb0");l.d(t,"a",(function(){return i["a"]}));var r=l("c87f");l.d(t,"f",(function(){return r["a"]}));var u=l("333d");l.d(t,"e",(function(){return u["a"]}));var s=l("05be");l.d(t,"b",(function(){return s["a"]}));l("9040");var d=l("4381");l.d(t,"h",(function(){return d["a"]}));var c=l("6724");l.d(t,"i",(function(){return c["a"]}))},c40e:function(e,t,l){"use strict";l.d(t,"e",(function(){return n})),l.d(t,"d",(function(){return o})),l.d(t,"f",(function(){return i})),l.d(t,"c",(function(){return r})),l.d(t,"a",(function(){return u})),l.d(t,"g",(function(){return s})),l.d(t,"b",(function(){return d}));var a=l("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"H",(function(){return o})),l.d(t,"f",(function(){return i})),l.d(t,"A",(function(){return r})),l.d(t,"x",(function(){return u})),l.d(t,"e",(function(){return s})),l.d(t,"w",(function(){return d})),l.d(t,"c",(function(){return c})),l.d(t,"O",(function(){return p})),l.d(t,"j",(function(){return m})),l.d(t,"k",(function(){return f})),l.d(t,"l",(function(){return b})),l.d(t,"T",(function(){return h})),l.d(t,"d",(function(){return v})),l.d(t,"Q",(function(){return _})),l.d(t,"p",(function(){return y})),l.d(t,"P",(function(){return g})),l.d(t,"m",(function(){return w})),l.d(t,"I",(function(){return O})),l.d(t,"L",(function(){return C})),l.d(t,"N",(function(){return x})),l.d(t,"M",(function(){return j})),l.d(t,"S",(function(){return k})),l.d(t,"s",(function(){return R})),l.d(t,"B",(function(){return S})),l.d(t,"z",(function(){return T})),l.d(t,"K",(function(){return A})),l.d(t,"C",(function(){return D})),l.d(t,"h",(function(){return L})),l.d(t,"g",(function(){return W})),l.d(t,"o",(function(){return I})),l.d(t,"G",(function(){return N})),l.d(t,"J",(function(){return $})),l.d(t,"v",(function(){return q})),l.d(t,"F",(function(){return z})),l.d(t,"r",(function(){return B})),l.d(t,"b",(function(){return E})),l.d(t,"q",(function(){return F})),l.d(t,"R",(function(){return M})),l.d(t,"u",(function(){return V})),l.d(t,"t",(function(){return Q})),l.d(t,"D",(function(){return P})),l.d(t,"E",(function(){return H})),l.d(t,"y",(function(){return X})),l.d(t,"n",(function(){return G}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],o=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],r=[{label:"是",code:1},{label:"否",code:0}],u=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],p=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],m=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],b=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],h=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],v=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],y=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],g=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],C=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],j=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],k=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],R=[{value:"0",label:"参团"},{value:"1",label:"团长"}],S=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],T=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],A=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],D=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],L=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],W=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],I=[{value:"1",label:"是"},{value:"2",label:"否"}],N=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],$=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],q=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],z=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],B=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],E=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],F=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],M=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],V=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],Q=[{label:"按订单编号",value:"order_no"}],P=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],H=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],X=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],G=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e585:function(e,t,l){"use strict";l.d(t,"m",(function(){return n})),l.d(t,"g",(function(){return o})),l.d(t,"n",(function(){return i})),l.d(t,"p",(function(){return r})),l.d(t,"o",(function(){return u})),l.d(t,"a",(function(){return s})),l.d(t,"c",(function(){return d})),l.d(t,"k",(function(){return c})),l.d(t,"b",(function(){return p})),l.d(t,"h",(function(){return m})),l.d(t,"i",(function(){return f})),l.d(t,"j",(function(){return b})),l.d(t,"f",(function(){return h})),l.d(t,"q",(function(){return v})),l.d(t,"e",(function(){return _})),l.d(t,"d",(function(){return y})),l.d(t,"l",(function(){return g}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/refund/refundList",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/admin/refund/getOrderChainStatus",method:"post",data:e})}function i(){return Object(a["a"])({url:"/admin/refund/refundReview",method:"post",data:data})}function r(e){return Object(a["a"])({url:"/admin/refund/returnRefundList",method:"get",params:e})}function u(e){return Object(a["a"])({url:"/admin_plus/refund/returnRefundDetail",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/admin/refund/agreeReturnMoney.html",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/admin/refund/checkSupplyChainOrder.html",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/admin/Express/logistics.html",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/admin/refund/agreeReturnRefundReview.html",method:"post",data:e})}function m(e){return Object(a["a"])({url:"/admin/address/getProvince.html",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/admin/address/getcity.html",method:"post",data:e})}function b(e){return Object(a["a"])({url:"/admin/address/getdistrict.html",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/admin/express/expressCompany.html",method:"post",data:e})}function v(e){return Object(a["a"])({url:"/admin/refund/sendAgain.html",method:"post",data:e})}function _(e){return Object(a["a"])({url:"/admin/refund/exchangeGoodsList",method:"get",params:e})}function y(e){return Object(a["a"])({url:"/admin_plus/Refund/exchangeGoodsDetail",method:"post",data:e})}function g(e){return Object(a["a"])({url:"/admin_plus/refund/refundDetail",method:"post",data:e})}},fe67:function(e,t,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);