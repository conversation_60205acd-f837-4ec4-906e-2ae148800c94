(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-73b95a53"],{"28a5f":function(e,l,a){"use strict";a("7201")},"2a37":function(e,l,a){"use strict";a("b41e")},7201:function(e,l,a){},"9b36":function(e,l,a){"use strict";a.r(l);var t=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"tabs"},e._l(e.tabs,(function(l,t){var n=l.title,u=l.code;return a("span",{key:t,class:{active:e.tabIndex==u},on:{click:function(l){return e.switchTabs(u)}}},[e._v(e._s(n))])})),0),e._v(" "),a("div",{staticClass:"cont"},[1==e.tabIndex?a("o-info",{attrs:{details:e.details}}):e._e(),e._v(" "),2==e.tabIndex?a("o-stock",{attrs:{details:e.details}}):e._e(),e._v(" "),3==e.tabIndex?a("o-details",{attrs:{details:e.details}}):e._e()],1)])},n=[],u=function(){var e=this,l=e.$createElement,a=e._self._c||l;return e.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[e._v("基础信息")]),e._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"商品名称："}},[e._v("\n            "+e._s(e.info.goods_name)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"商品分类："}},[e._v("\n            "+e._s(e.info.category_name)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"促销语："}},[e._v("\n            "+e._s(e.info.introduction)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"关键词："}},[e._v("\n            "+e._s(e.info.keywords)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[e._v("\n            "+e._s(e.info.virtual_sale_num)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"单位："}},[e._v("\n            "+e._s(e.info.unit)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"商品品牌："}},[e._v("\n            "+e._s(e.info.brand_name)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"供应商："}},[e._v("\n            "+e._s(e.info.site_name)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"迈豆专区："}},[e._v("\n            "+e._s(e.getTags(e.details.goods_tag.id))+" \n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"微信支付方式："}},[e._v("\n            "+e._s(e.changePay(e.info.use_pay_type))+"\n        ")])],1),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("其他信息")]),e._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"排序："}},[e._v("\n            "+e._s(e.info.sort)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"是否上架："}},[e._v("\n            "+e._s(e.changeStatus(e.info.goods_state))+"\n        ")])],1)],1):e._e()},i=[],o=(a("7514"),a("c71b")),r={data:function(){return{}},computed:{info:function(){return this.details.goods_info?this.details.goods_info:{}}},props:{details:{type:Object,default:function(){}}},methods:{changeStatus:function(e){var l="";return o["n"].find((function(a){a.value==e&&(l=a.label)})),l},getTags:function(e){var l="";return this.details.tag_list&&this.details.tag_list.find((function(a){if(a.id==e)return l=a.tag_name})),l},changePay:function(e){var l="";return o["y"].find((function(a){a.value==e&&(l=a.label)})),l}}},s=r,v=a("2877"),b=Object(v["a"])(s,u,i,!1,null,null,null),d=b.exports,f=function(){var e=this,l=e.$createElement,a=e._self._c||l;return e.details.goods_info?a("div",[a("div",{staticClass:"edit_title"},[e._v("价格库存")]),e._v(" "),a("el-form",{attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"启用多规格："}},[e._v("\n            "+e._s(1==e.details.goods_info.verify_state?"关闭":"启用")+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"销售价："}},[e._v("\n            "+e._s(e.info.price)+" 元\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"店主返佣比例："}},[e._v("\n            "+e._s(e.info.reward_shop_rate)+" % "),a("span",[e._v("(自动计算=(销售价/供货价-1)*100)")])]),e._v(" "),a("el-form-item",{attrs:{label:"公司返佣比例："}},[e._v("\n            "+e._s(e.info.reward_company_rate)+" % "),a("span",[e._v("(自动计算=(供货价/供应商价格-1)*100)")])]),e._v(" "),a("el-form-item",{attrs:{label:"供应商价格："}},[e._v("\n            "+e._s(e.info.cost_price)+" 元\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"供货价："}},[e._v("\n            "+e._s(e.info.price)+" 元\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"原价："}},[e._v("\n            "+e._s(e.info.market_price)+" 元\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"重量："}},[e._v("\n            "+e._s(e.info.weight)+" kg\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"体积："}},[e._v("\n            "+e._s(e.info.volume)+" m3\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"商品编码："}},[e._v("\n            "+e._s(e.info.sku_no)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"总库存："}},[e._v("\n            "+e._s(e.info.stock)+"/件\n        ")]),e._v(" "),a("el-form-item",{attrs:{label:"库存预警："}},[e._v("\n            "+e._s(e.details.goods_info.goods_stock_alarm)+"/件\n            "),a("span",[e._v(" (设置最低库存预警值。当库存低于预警值时商家中心商品列表页库存列红字提醒，0为不预警。)")])]),e._v(" "),a("el-form-item",{attrs:{label:"是否免邮："}},[e._v("\n            "+e._s(1==e.details.goods_info.is_free_shipping?"是":"否")+"\n        ")])],1)],1):e._e()},c=[],_={data:function(){return{}},computed:{info:function(){return this.details.goods_info?this.details.goods_info.sku_list[0]:{}}},props:{details:{type:Object,default:function(){}}},methods:{showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)}}},m=_,p=Object(v["a"])(m,f,c,!1,null,null,null),g=p.exports,h=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("div",{staticClass:"prod"},[a("div",{staticClass:"edit_title"},[e._v("商品简图")]),e._v(" "),a("div",{staticClass:"images"},e._l(e.images,(function(l,t){return a("div",{key:t},[a("span",[a("i",{staticClass:"el-icon-view",on:{click:function(l){return e.showImage(t)}}})]),e._v(" "),a("el-image",{attrs:{src:l,fit:"cover"}})],1)})),0),e._v(" "),a("p",[e._v("第一张图片将作为商品主图,支持同时上传多张图片,多张图片之间可随意调整位置；支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片，上传后的图片将会自动保存在图片空间的默认分类中。")]),e._v(" "),a("div",{staticClass:"edit_title"},[e._v("商品详情")]),e._v(" "),a("div",{domProps:{innerHTML:e._s(e.info.goods_content)}}),e._v(" "),a("o-image",{ref:"image",attrs:{list:e.images}})],1)},w=[],y=(a("28a5"),{data:function(){return{imgs:[],list:["https://fs.jiufuwangluo.com/uploads/supply/product/20210316/a7c026593368583afc23549d3218f48d.jpg"]}},computed:{info:function(){return this.details.goods_info?this.details.goods_info:{}},images:function(){return this.details.goods_info?this.details.goods_info.goods_image.split(","):[]}},props:{details:{type:Object,default:function(){}}},methods:{showImage:function(e){this.imgs=this.images,this.$refs.image.init(e)}}}),k=y,x=(a("2a37"),Object(v["a"])(k,h,w,!1,null,"171a4200",null)),j=x.exports,I=a("ec01"),O={components:{OInfo:d,ODetails:j,OStock:g},data:function(){return{tabIndex:1,tabs:[{title:"基础信息",code:1},{title:"价格库存",code:2},{title:"商品详情",code:3}],details:{}}},mounted:function(){var e=this,l=this.$route.query.goods_id;Object(I["c"])({goods_id:l}).then((function(l){var a=l.data;e.details=a}))},methods:{switchTabs:function(e){this.tabIndex=e}}},C=O,P=(a("28a5f"),Object(v["a"])(C,t,n,!1,null,"706a9e6e",null));l["default"]=P.exports},b41e:function(e,l,a){},c71b:function(e,l,a){"use strict";a.d(l,"a",(function(){return t})),a.d(l,"i",(function(){return n})),a.d(l,"H",(function(){return u})),a.d(l,"f",(function(){return i})),a.d(l,"A",(function(){return o})),a.d(l,"x",(function(){return r})),a.d(l,"e",(function(){return s})),a.d(l,"w",(function(){return v})),a.d(l,"c",(function(){return b})),a.d(l,"O",(function(){return d})),a.d(l,"j",(function(){return f})),a.d(l,"k",(function(){return c})),a.d(l,"l",(function(){return _})),a.d(l,"T",(function(){return m})),a.d(l,"d",(function(){return p})),a.d(l,"Q",(function(){return g})),a.d(l,"p",(function(){return h})),a.d(l,"P",(function(){return w})),a.d(l,"m",(function(){return y})),a.d(l,"I",(function(){return k})),a.d(l,"L",(function(){return x})),a.d(l,"N",(function(){return j})),a.d(l,"M",(function(){return I})),a.d(l,"S",(function(){return O})),a.d(l,"s",(function(){return C})),a.d(l,"B",(function(){return P})),a.d(l,"z",(function(){return $})),a.d(l,"K",(function(){return E})),a.d(l,"C",(function(){return T})),a.d(l,"h",(function(){return S})),a.d(l,"g",(function(){return A})),a.d(l,"o",(function(){return D})),a.d(l,"G",(function(){return L})),a.d(l,"J",(function(){return J})),a.d(l,"v",(function(){return M})),a.d(l,"F",(function(){return q})),a.d(l,"r",(function(){return B})),a.d(l,"b",(function(){return G})),a.d(l,"q",(function(){return H})),a.d(l,"R",(function(){return N})),a.d(l,"u",(function(){return z})),a.d(l,"t",(function(){return F})),a.d(l,"D",(function(){return K})),a.d(l,"E",(function(){return Q})),a.d(l,"y",(function(){return R})),a.d(l,"n",(function(){return V}));var t=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],v=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],b=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],d=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],f=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],c=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],_=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],p=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],h=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],w=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],y=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],x=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],j=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],I=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],O=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],C=[{value:"0",label:"参团"},{value:"1",label:"团长"}],P=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],$=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],E=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],T=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],S=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],A=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],D=[{value:"1",label:"是"},{value:"2",label:"否"}],L=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],J=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],M=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],q=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],B=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],G=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],H=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],N=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],z=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],F=[{label:"按订单编号",value:"order_no"}],K=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],Q=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],R=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],V=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},ec01:function(e,l,a){"use strict";a.d(l,"c",(function(){return u})),a.d(l,"b",(function(){return i}));var t=a("b775"),n=a("d74f");function u(e){return Object(t["a"])({url:"/admin_plus/Goods/show",method:"post",data:e})}function i(e){return Object(t["a"])({url:"/admin/goods/getSupplierPageList.html",method:"post",data:e})}a.d(l,"a",(function(){return n["b"]}))}}]);