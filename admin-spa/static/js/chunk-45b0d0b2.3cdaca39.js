(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-45b0d0b2"],{"3a5a":function(e,a,t){},"3f5e":function(e,a,t){"use strict";t.d(a,"b",(function(){return n})),t.d(a,"c",(function(){return r})),t.d(a,"a",(function(){return u}));var l=t("b775");function n(e){return Object(l["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function r(e){return Object(l["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(l["a"])({url:"/admin/Album/Album",method:"post",data:e})}},"57f5":function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"details"},[t("div",{staticClass:"edit_title"},[e._v("Banner信息")]),e._v(" "),t("el-form",{ref:"formRef",attrs:{"label-width":"150px",rules:e.rules,model:e.form}},[t("el-form-item",{attrs:{label:"父级："}},[t("el-select",{attrs:{placeholder:"请选择"},on:{change:e.changeSign},model:{value:e.form.parent_sign,callback:function(a){e.$set(e.form,"parent_sign",a)},expression:"form.parent_sign"}},e._l(e.pageData.parent,(function(e,a){return t("el-option",{key:a,attrs:{label:e,value:a}})})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"广告位置："}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.sign,callback:function(a){e.$set(e.form,"sign",a)},expression:"form.sign"}},e._l(e.positionList,(function(e,a){return t("el-option",{key:a,attrs:{label:e,value:a}})})),1)],1),e._v(" "),t("el-form-item",{staticStyle:{width:"50%"},attrs:{label:"banner名称：",prop:"banner_name"}},[t("el-input",{attrs:{placeholder:"请输入banner名称"},model:{value:e.form.banner_name,callback:function(a){e.$set(e.form,"banner_name",a)},expression:"form.banner_name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"banner图片："}},[t("label",{staticClass:"upload"},[0==e.images.length?t("span",[t("i",{staticClass:"el-icon-upload"}),e._v("\n                    点击上传\n                ")]):t("el-image",{attrs:{src:e.images[0],fit:"contain"}}),e._v(" "),t("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{type:"file"},on:{change:e.onChange}})],1),e._v(" "),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.preview}},[e._v("预览")]),e._v(" "),t("p",{staticClass:"tips"},[e._v("选择文件，允许的文件类型：png，jpg，jpeg文档且不超过5mb，格式：banner-宽702px*高228px；弹窗-宽620px*高800px")])],1),e._v(" "),t("el-form-item",{attrs:{label:"开始时间："}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.start_time,callback:function(a){e.$set(e.form,"start_time",a)},expression:"form.start_time"}}),e._v(" "),t("p",{staticClass:"tips"},[e._v("为空即代表永远有效")])],1),e._v(" "),t("el-form-item",{attrs:{label:"结束时间："}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.end_time,callback:function(a){e.$set(e.form,"end_time",a)},expression:"form.end_time"}}),e._v(" "),t("p",{staticClass:"tips"},[e._v("为空即代表永远有效")])],1),e._v(" "),t("el-form-item",{attrs:{label:"banner链接："}},[t("el-input",{staticStyle:{width:"50%"},attrs:{placeholder:"请输入banner链接："},model:{value:e.form.banner_url,callback:function(a){e.$set(e.form,"banner_url",a)},expression:"form.banner_url"}}),e._v(" "),t("p",{staticClass:"tips"},[e._v("{shop_id}将被替换成店铺id")])],1),e._v(" "),t("el-form-item",{attrs:{label:"用户限制："}},[t("el-checkbox-group",{model:{value:e.form.show_group,callback:function(a){e.$set(e.form,"show_group",a)},expression:"form.show_group"}},e._l(e.bannerLimitOpt,(function(a,l){return t("el-checkbox",{key:l,staticClass:"select-tabs",attrs:{label:a.value}},[e._v("\n                    "+e._s(a.label)+"\n                    "),"tag_mem"==a.value?t("div",{staticClass:"select-tabs-tags",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.$refs.tabs.init()}}},[0==e.selectTags.length?t("span",[e._v("请选择标签")]):e._l(e.selectTags,(function(a,l){return t("el-tag",{key:l},[e._v(e._s(a.tag_name))])})),e._v(" "),t("i",{staticClass:"el-icon-caret-bottom select-tabs-tags-icon"})],2):e._e()])})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:""}},[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.toSave}},[e._v("保存")])],1)],1),e._v(" "),t("o-image",{ref:"image",attrs:{list:e.images}}),e._v(" "),t("tabs",{ref:"tabs",attrs:{chooseTags:e.selectTags,tag_status:e.form.tag_status},on:{"update:tag_status":function(a){return e.$set(e.form,"tag_status",a)},onSelectTags:e.toSelectTags},model:{value:e.form.tag_ids,callback:function(a){e.$set(e.form,"tag_ids",a)},expression:"form.tag_ids"}})],1)},n=[],r=t("5530"),u=(t("28a5"),t("c7eb")),i=(t("96cf"),t("1da1")),s=(t("e1a7"),t("c71b")),o=t("f2b4"),c=(t("7cea"),t("8dd4")),b=t("3f5e"),m={components:{tabs:o["a"]},data:function(){return{id:null,allPositionDict:{},positionList:{},selectTags:[],form:{banner_name:"",show_group:[],tag_ids:[],tag_status:0},bannerLimitOpt:s["g"],rules:{banner_name:[{required:!0,message:"banner名称",trigger:"blur"}]},images:[],pageData:{parent:{},position:{}}}},created:function(){var e=Object(i["a"])(Object(u["a"])().mark((function e(){return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.id=this.$route.query.id||null,this.getBannerPositionData(),e.next=4,this.getData();case 4:case"end":return e.stop()}}),e,this)})));function a(){return e.apply(this,arguments)}return a}(),methods:{getData:function(){var e=Object(i["a"])(Object(u["a"])().mark((function e(){var a,t=this;return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,a=null,!this.id){e.next=8;break}return e.next=5,Object(c["b"])({id:this.id});case 5:a=e.sent,e.next=11;break;case 8:return e.next=10,Object(c["a"])({});case 10:a=e.sent;case 11:0==a.code&&(this.pageData=a.data,this.id&&(this.form.parent_sign=a.data.info.parent_sign,this.form.banner_name=a.data.info.banner_name,this.form.image_url=a.data.info.image_url,this.images=[a.data.info.image_url],this.form.start_time=a.data.info.start_time,this.form.end_time=a.data.info.end_time,this.form.banner_url=a.data.info.banner_url,this.form.show_group=a.data.info.show_group.map((function(e){return"tag_one_mem"==e&&(e="tag_mem"),e})),this.form.tag_status=a.data.tag_status,this.form.tag_ids=a.data.tag_ids.split(",").map((function(e){return parseInt(e)})),this.$nextTick(Object(i["a"])(Object(u["a"])().mark((function e(){return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.changeSign(t.form.parent_sign),t.form.sign=a.data.info.sign,!t.form.tag_ids.length){e.next=6;break}return e.next=5,t.$refs.tabs.getActiveTags(t.form.tag_ids);case 5:t.selectTags=e.sent;case 6:case"end":return e.stop()}}),e)})))),this.$forceUpdate())),e.next=16;break;case 14:e.prev=14,e.t0=e["catch"](0);case 16:case"end":return e.stop()}}),e,this,[[0,14]])})));function a(){return e.apply(this,arguments)}return a}(),changeSign:function(e){delete this.form["sign"],this.positionList=e?this.allPositionDict[e]:[]},getBannerPositionData:function(){var e=Object(i["a"])(Object(u["a"])().mark((function e(){var a;return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(c["k"])({});case 3:a=e.sent,0==a.code&&(this.allPositionDict=a.data.position),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function a(){return e.apply(this,arguments)}return a}(),selecTabs:function(){this.$refs.tabs.init()},preview:function(){this.$refs.image.init()},onChange:function(){var e=Object(i["a"])(Object(u["a"])().mark((function e(a){var t,l,n,r;return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$loading({lock:!0,text:"上传中"}),l=a.target.files[0],n=new FormData,n.append("file",l),e.prev=4,e.next=7,Object(b["b"])(n);case 7:r=e.sent,this.$set(this.images,0,r.data.pic_path),this.form.image_url=r.data.pic_path,t.close(),this.$message(r.message),e.next=16;break;case 14:e.prev=14,e.t0=e["catch"](4);case 16:t.close();case 17:case"end":return e.stop()}}),e,this,[[4,14]])})));function a(a){return e.apply(this,arguments)}return a}(),toSelectTags:function(e){this.selectTags=e},toSave:function(){var e=this;this.$confirm("确定要提交数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0}).then((function(){e.$refs.formRef.validate((function(a){if(a){if(!e.images.length)return e.$message.error("上传banner图片！"),!1;var t=Object(r["a"])(Object(r["a"])({},e.form),{},{image_url:e.images[0],tag_ids:e.form.tag_ids.join(",")});e.id?(t.id=e.id,Object(c["h"])(t).then((function(a){e.$message(a.message),0==a.code&&e.$router.back()}))):Object(c["f"])(t).then((function(a){e.$message(a.message),0==a.code&&e.$router.back()}))}}))}))}}},d=m,v=(t("ef05"),t("2877")),f=Object(v["a"])(d,l,n,!1,null,"3008d372",null);a["default"]=f.exports},9481:function(e,a,t){"use strict";t.d(a,"i",(function(){return n})),t.d(a,"a",(function(){return r})),t.d(a,"g",(function(){return u})),t.d(a,"e",(function(){return i})),t.d(a,"d",(function(){return s})),t.d(a,"l",(function(){return o})),t.d(a,"f",(function(){return c})),t.d(a,"h",(function(){return b})),t.d(a,"j",(function(){return m})),t.d(a,"k",(function(){return d})),t.d(a,"c",(function(){return v})),t.d(a,"b",(function(){return f}));var l=t("b775");function n(e){return Object(l["a"])({url:"/admin/MemberTags/groupList.html",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/admin/memberTags/addTagGroup.html",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/admin/memberTags/editTagGroup.html",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/admin/memberTags/delTagGroup.html",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/admin/MemberTags/dataList.html",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/admin/memberTags/delTag",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/admin/MemberTags/editSort.html",method:"post",data:e})}function b(e){return Object(l["a"])({url:"/admin/MemberTags/executeRule.html",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/admin/memberTags/tagRulesList.html",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/admin/memberTags/tagValueAdd.html",method:"post",data:e})}function v(e){return Object(l["a"])({url:"/admin_plus/memberTags/tagValueAdd",method:"get",params:e})}function f(e){return Object(l["a"])({url:"/admin_plus/Member/tags",method:"get",params:e})}},c71b:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"i",(function(){return n})),t.d(a,"H",(function(){return r})),t.d(a,"f",(function(){return u})),t.d(a,"A",(function(){return i})),t.d(a,"x",(function(){return s})),t.d(a,"e",(function(){return o})),t.d(a,"w",(function(){return c})),t.d(a,"c",(function(){return b})),t.d(a,"O",(function(){return m})),t.d(a,"j",(function(){return d})),t.d(a,"k",(function(){return v})),t.d(a,"l",(function(){return f})),t.d(a,"T",(function(){return p})),t.d(a,"d",(function(){return g})),t.d(a,"Q",(function(){return h})),t.d(a,"p",(function(){return _})),t.d(a,"P",(function(){return j})),t.d(a,"m",(function(){return O})),t.d(a,"I",(function(){return k})),t.d(a,"L",(function(){return w})),t.d(a,"N",(function(){return x})),t.d(a,"M",(function(){return y})),t.d(a,"S",(function(){return T})),t.d(a,"s",(function(){return $})),t.d(a,"B",(function(){return C})),t.d(a,"z",(function(){return D})),t.d(a,"K",(function(){return L})),t.d(a,"C",(function(){return M})),t.d(a,"h",(function(){return S})),t.d(a,"g",(function(){return A})),t.d(a,"o",(function(){return P})),t.d(a,"G",(function(){return R})),t.d(a,"J",(function(){return q})),t.d(a,"v",(function(){return B})),t.d(a,"F",(function(){return H})),t.d(a,"r",(function(){return I})),t.d(a,"b",(function(){return G})),t.d(a,"q",(function(){return N})),t.d(a,"R",(function(){return X})),t.d(a,"u",(function(){return z})),t.d(a,"t",(function(){return E})),t.d(a,"D",(function(){return J})),t.d(a,"E",(function(){return V})),t.d(a,"y",(function(){return F})),t.d(a,"n",(function(){return W}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],r=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],u=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],i=[{label:"是",code:1},{label:"否",code:0}],s=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],o=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],b=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],m=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],d=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],v=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],f=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],p=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],g=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],h=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],_=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],j=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],O=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],k=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],w=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],y=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],T=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],$=[{value:"0",label:"参团"},{value:"1",label:"团长"}],C=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],D=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],L=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],M=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],S=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],A=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],P=[{value:"1",label:"是"},{value:"2",label:"否"}],R=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],q=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],B=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],H=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],I=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],G=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],X=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],z=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],E=[{label:"按订单编号",value:"order_no"}],J=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],V=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],F=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],W=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},ef05:function(e,a,t){"use strict";t("3a5a")}}]);