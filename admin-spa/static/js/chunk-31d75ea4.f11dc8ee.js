(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-31d75ea4"],{1045:function(e,t,l){"use strict";l.d(t,"b",(function(){return a})),l.d(t,"c",(function(){return n})),l.d(t,"a",(function(){return u}));var a=[{type:"select",label:"状态",model:"status",placeholder:"全部",options:{name:"promotionStatusOpt"}},{type:"input",label:"页面名称",model:"name",placeholder:"请输入页面名称"},{type:"input",label:"推广渠道标识",model:"identification",placeholder:"请输入推广渠道标识"}],n=[{type:"input",label:"热门关键字",model:"keyword",placeholder:"请输入热门关键字"}],u=[{type:"input",label:"ID",model:"id",placeholder:"请输入"},{type:"input",label:"广告图名称",model:"banner_name",placeholder:"请输入广告图名称"},{type:"select",label:"状态",model:"state",placeholder:"全部",options:{name:"bannerStatusOpt"}}]},"28fb":function(e,t,l){},"3f5e":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return u})),l.d(t,"a",(function(){return o}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},"3fba":function(e,t,l){"use strict";l.d(t,"c",(function(){return a})),l.d(t,"b",(function(){return n})),l.d(t,"d",(function(){return u})),l.d(t,"a",(function(){return o}));var a=[{faild:"id",title:"ID"},{faild:"name",title:"页面名称"},{faild:"identification",title:"推广渠道标识"},{faild:"create_time",title:"创建时间",slot:"create_time"},{title:"操作",slot:"action"}],n=[{faild:"id",title:"ID"},{faild:"name",title:"姓名"},{faild:"mobile",title:"手机号码"},{faild:"create_time",title:"报名时间",slot:"create_time"}],u=[{faild:"id",title:"ID",width:50},{faild:"word",title:"热门关键字"},{faild:"create_time",title:"创建时间",slot:"create_time"},{faild:"update_time",title:"修改时间",slot:"update_time"},{faild:"sort",title:"排序(值越大前端显示越前)"},{title:"操作",slot:"action"}],o=[{faild:"id",title:"ID",width:50},{title:"广告标题",faild:"banner_name"},{title:"起止时间",faild:"time_txt",slot:"time_txt"},{title:"广告位置",faild:"position"},{title:"平台状态",faild:"state",slot:"state"},{title:"运营组可用",slot:"oper",faild:"oper"},{title:"操作",slot:"action",width:160}]},4381:function(e,t,l){"use strict";var a=l("a18c"),n={inserted:function(e,t,l){var n=t.value,u=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;u.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},u=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(u)),n.install=u;t["a"]=n},6160:function(e,t,l){"use strict";l("28fb")},6396:function(e,t,l){"use strict";l.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function u(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,l){var o=u(),i=e-o,r=20,c=0;t="undefined"===typeof t?500:t;var d=function e(){c+=r;var u=Math.easeInOutQuad(c,o,i,t);n(u),c<t?a(e):l&&"function"===typeof l&&l()};d()}},6724:function(e,t,l){"use strict";l("8d41");var a={bind:function(e,t){e.addEventListener("click",(function(l){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),u=n.ele;if(u){u.style.position="relative",u.style.overflow="hidden";var o=u.getBoundingClientRect(),i=u.querySelector(".waves-ripple");switch(i?i.className="waves-ripple":(i=document.createElement("span"),i.className="waves-ripple",i.style.height=i.style.width=Math.max(o.width,o.height)+"px",u.appendChild(i)),n.type){case"center":i.style.top=o.height/2-i.offsetHeight/2+"px",i.style.left=o.width/2-i.offsetWidth/2+"px";break;default:i.style.top=(l.pageY-o.top-i.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",i.style.left=(l.pageX-o.left-i.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return i.style.backgroundColor=n.color,i.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;t["a"]=a},"8d41":function(e,t,l){},"9a9b":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[e.showSearch?l("div",{staticClass:"filter-container"},[l("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}}),e._v(" "),l("div",{staticClass:"flex-b-c buttons"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v("搜索")]),e._v(" "),l("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),l("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-list"},[l("div",{staticClass:"btns"},[l("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(t){return e.goSkip("/shop/promotion/add")}}},[e._v("新增推广页面")])],1),e._v(" "),l("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{toggleSearch:e.toggleSearch,"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},onSearch:e.getTableList},scopedSlots:e._u([{key:"create_time",fn:function(t){var l=t.row;return[e._v("\n              "+e._s(e._f("parseTime")(l.create_time))+"\n            ")]}},{key:"action",fn:function(t){var a=t.row;return[l("div",{staticClass:"actions"},[l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/shop/promotion/add",{id:a.id})}}},[e._v("编辑")]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.offShelf(a)}}},[e._v(e._s(1==a.status?"下架":"上架"))]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.copy(a)}}},[e._v("复制链接")]),e._v(" "),l("el-button",{attrs:{type:"text"},on:{click:function(t){return e.goSkip("/shop/promotion/entry",{id:a.id})}}},[e._v("查看报名")])],1)]}}])})],1)])},n=[],u=l("5530"),o=l("b885"),i=l("3fba"),r=(l("e1a7"),l("c71b")),c=l("1045"),d=(l("ed08"),l("8dd4")),s=l("d223"),b={computed:{row:function(){return s["a"]}},components:{FormQuery:o["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{promotionStatusOpt:r["C"]},form:{},formConfig:c["b"],loading:!1,columns:i["c"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){this.getTableList()},methods:{copy:function(e){this.$message.success("复制成功"),navigator.clipboard.writeText("".concat(location.origin,"/promotion_page/index/index?id=").concat(e.id))},offShelf:function(e){var t=this;this.$confirm("确定要".concat(1==e.status?"下架":"上架","该数据吗？"),"信息",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0}).then((function(){Object(d["q"])({id:e.id,status:1==e.status?0:1}).then((function(e){0==e.code&&(t.$message.success(e.message),setTimeout((function(){t.getTableList()}),1e3))}))})).catch((function(){}))},goSkip:function(e,t){this.$router.push({path:e,query:t})},handleQuery:function(){this.options={page:1,page_size:10,total:0},this.getTableList()},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(e){var t=this;this.loading=!0,this.list=[],e&&e&&(this.options.page=e.page,this.options.page_size=e.page_size);var l=Object(u["a"])(Object(u["a"])({},this.options),this.form);Object(d["s"])(l).then((function(e){0==e.code&&(t.options.total=e.data.count,t.list=e.data.list,t.loading=!1)}))}}},v=b,f=(l("6160"),l("2877")),p=Object(f["a"])(v,a,n,!1,null,"41b7f1a4",null);t["default"]=p.exports},b885:function(e,t,l){"use strict";var a=l("e780");l.d(t,"d",(function(){return a["a"]}));var n=l("ad41");l.d(t,"c",(function(){return n["a"]}));var u=l("0476");l.d(t,"g",(function(){return u["a"]}));var o=l("6eb0");l.d(t,"a",(function(){return o["a"]}));var i=l("c87f");l.d(t,"f",(function(){return i["a"]}));var r=l("333d");l.d(t,"e",(function(){return r["a"]}));var c=l("05be");l.d(t,"b",(function(){return c["a"]}));l("9040");var d=l("4381");l.d(t,"h",(function(){return d["a"]}));var s=l("6724");l.d(t,"i",(function(){return s["a"]}))},c40e:function(e,t,l){"use strict";l.d(t,"e",(function(){return n})),l.d(t,"d",(function(){return u})),l.d(t,"f",(function(){return o})),l.d(t,"c",(function(){return i})),l.d(t,"a",(function(){return r})),l.d(t,"g",(function(){return c})),l.d(t,"b",(function(){return d}));var a=l("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"H",(function(){return u})),l.d(t,"f",(function(){return o})),l.d(t,"A",(function(){return i})),l.d(t,"x",(function(){return r})),l.d(t,"e",(function(){return c})),l.d(t,"w",(function(){return d})),l.d(t,"c",(function(){return s})),l.d(t,"O",(function(){return b})),l.d(t,"j",(function(){return v})),l.d(t,"k",(function(){return f})),l.d(t,"l",(function(){return p})),l.d(t,"T",(function(){return m})),l.d(t,"d",(function(){return h})),l.d(t,"Q",(function(){return g})),l.d(t,"p",(function(){return w})),l.d(t,"P",(function(){return y})),l.d(t,"m",(function(){return _})),l.d(t,"I",(function(){return x})),l.d(t,"L",(function(){return S})),l.d(t,"N",(function(){return C})),l.d(t,"M",(function(){return O})),l.d(t,"S",(function(){return k})),l.d(t,"s",(function(){return T})),l.d(t,"B",(function(){return j})),l.d(t,"z",(function(){return L})),l.d(t,"K",(function(){return N})),l.d(t,"C",(function(){return q})),l.d(t,"h",(function(){return A})),l.d(t,"g",(function(){return I})),l.d(t,"o",(function(){return z})),l.d(t,"G",(function(){return R})),l.d(t,"J",(function(){return E})),l.d(t,"v",(function(){return D})),l.d(t,"F",(function(){return Q})),l.d(t,"r",(function(){return M})),l.d(t,"b",(function(){return B})),l.d(t,"q",(function(){return F})),l.d(t,"R",(function(){return H})),l.d(t,"u",(function(){return V})),l.d(t,"t",(function(){return W})),l.d(t,"D",(function(){return X})),l.d(t,"E",(function(){return $})),l.d(t,"y",(function(){return P})),l.d(t,"n",(function(){return J}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],o=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],i=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],c=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],s=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],b=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],v=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],p=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],w=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],_=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],x=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],S=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],C=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],O=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],k=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],T=[{value:"0",label:"参团"},{value:"1",label:"团长"}],j=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],L=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],N=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],q=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],A=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],I=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],z=[{value:"1",label:"是"},{value:"2",label:"否"}],R=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],E=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],D=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],Q=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],M=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],B=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],F=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],H=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],V=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],W=[{label:"按订单编号",value:"order_no"}],X=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],$=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],P=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],J=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},fe67:function(e,t,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);