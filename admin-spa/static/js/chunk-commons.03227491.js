(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"0476":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-table",{ref:"tableCustom",attrs:{data:e.tableData,"row-key":"id","row-key":e.row<PERSON>ey},on:{"selection-change":e.handleSelectionChange}},[e._l(e.columns,(function(t){return["selection"===t.type?e._t("selection"):"expand"===t.type?i("el-table-column",{key:t.label,attrs:{type:"expand",width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._t("expand",null,{row:Object.assign({},t.row,{$index:t.$index})})]}}],null,!0)}):e._e(),e._v(" "),e.isVisible(t.visible)&&!t.action?i("el-table-column",{key:t.label,attrs:{label:t.label,prop:t.prop,"min-width":t.minWidth,sortable:t.sortable,width:t.width,align:t.align},scopedSlots:e._u([{key:"default",fn:function(a){return[t.slot?e._t(t.prop,null,{row:Object.assign({},a.row,{$index:a.$index})}):i("span",[e._v(e._s(a.row[t.prop]))])]}}],null,!0)}):e._e(),e._v(" "),t.action?i("el-table-column",{key:t.label,attrs:{label:t.label,fixed:"right",align:"center","min-width":t.minWidth},scopedSlots:e._u([{key:"default",fn:function(t){return[e._t("event",null,{row:Object.assign({},t.row,{$index:t.$index})})]}}],null,!0)}):e._e()]}))],2)},l=[],s={props:{columns:{required:!0,type:Array,default:function(){return[]}},tableData:{required:!0,type:Array,default:function(){return[]}},rowKey:{type:String}},methods:{handleSelectionChange:function(e){this.$emit("handleSelectionChange",e)},isVisible:function(e){return"boolean"!==typeof e||e}}},n=s,o=i("2877"),r=Object(o["a"])(n,a,l,!1,null,null,null);t["a"]=r.exports},"056e":function(e,t,i){},"05be":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.permission?[i("el-button",{directives:[{name:"permission",rawName:"v-permission",value:e.permission,expression:"permission"},{name:"waves",rawName:"v-waves"}],attrs:{type:e.type,size:e.size,icon:e.icon},on:{click:e.emitClick}},[e._v(e._s(e.text))])]:i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],attrs:{type:e.type,size:e.size,icon:e.icon},on:{click:e.emitClick}},[e._v(e._s(e.text))])],2)},l=[],s=i("4381"),n=i("6724"),o={directives:{permission:s["a"],waves:n["a"]},props:{permission:String,type:{type:String,default:"text"},size:{type:String,default:""},icon:{type:String,default:""},text:{type:String,default:""}},methods:{emitClick:function(){this.$emit("click")}}},r=o,c=i("2877"),u=Object(c["a"])(r,a,l,!1,null,null,null);t["a"]=u.exports},"06cf":function(e,t,i){"use strict";i("2bd9")},"07b2":function(e,t,i){},"0860":function(e,t,i){"use strict";i("bbba")},"0ce5":function(e,t,i){"use strict";i("66bb")},"212a":function(e,t,i){},"27bc":function(e,t,i){},"2bd9":function(e,t,i){},"333d":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[i("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},l=[],s=(i("c5f6"),i("6396")),n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:8},pageSizes:{type:Array,default:function(){return[8,16,32]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&Object(s["a"])(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&Object(s["a"])(0,800)}}},o=n,r=(i("bd1e"),i("2877")),c=Object(r["a"])(o,a,l,!1,null,"b80f601a",null);t["a"]=c.exports},"347fe":function(e,t,i){"use strict";i("fce7")},"3ebd":function(e,t,i){"use strict";i("056e")},"40a6":function(e,t,i){"use strict";i("27bc")},"4a76":function(e,t,i){"use strict";i("96d7")},"565f":function(e,t,i){},"571c":function(e,t,i){"use strict";i("6468")},6468:function(e,t,i){},"66bb":function(e,t,i){},"6a95":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{staticClass:"dialog",attrs:{title:"图片管理",visible:e.dialogVisible,top:"100px",width:"835px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",{staticClass:"image-list"},[i("el-form",[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.uploadVisible=!0}}},[e._v("上传图片")]),e._v(" "),i("el-input",{staticClass:"input-with-select",staticStyle:{width:"40%"},attrs:{placeholder:"请输入内容"},model:{value:e.pic_name,callback:function(t){e.pic_name=t},expression:"pic_name"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.toSearch},slot:"append"})],1)],1),e._v(" "),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table"},e._l(e.images,(function(t,a){return i("div",{key:a,class:{active:t.isActive},on:{click:function(i){return e.setImages(t,a)}}},[i("el-image",{attrs:{lazy:!0,src:t.pic_path,fit:"cover"}}),e._v(" "),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.pic_name,placement:"top"}},[i("span",[e._v(e._s(t.pic_name))])]),e._v(" "),i("i",{staticClass:"el-icon-finished"})],1)})),0)],1),e._v(" "),i("el-pagination",{attrs:{"page-size":18,background:"",layout:"prev, pager, next","current-page":e.page,total:e.total},on:{"current-change":e.getImages,"update:currentPage":function(t){e.page=t},"update:current-page":function(t){e.page=t}}}),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.toConfirm}},[e._v("确 定")])],1),e._v(" "),i("el-dialog",{attrs:{title:"本地上传",visible:e.uploadVisible,modal:!1},on:{"update:visible":function(t){e.uploadVisible=t}}},[i("div",{staticClass:"upload-list"},[i("span",{staticClass:"upload-list-name"},[e._v("本地图片")]),e._v(" "),i("div",{staticClass:"upload-list-inner"},[i("el-upload",{ref:"fileUploadRef",attrs:{"list-type":"picture-card",action:"","auto-upload":!1,"on-change":e.handle_change,"on-remove":e.handle_remove,"on-success":e.handle_success,"http-request":e.myUploadFile}},[i("i",{staticClass:"el-icon-plus"})])],1)]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.uploadVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.toUpload}},[e._v("确 定")])],1)])],1)},l=[],s=i("c7eb"),n=(i("96cf"),i("1da1")),o=i("3f5e"),r=(i("4f6c"),{data:function(){return{dialogVisible:!1,pic_name:"",images:[],page:1,page_size:18,loading:!1,total:0,uploadVisible:!1,selectImageList:[],fileList:[]}},mounted:function(){},methods:{init:function(){this.getImages(1),this.dialogVisible=!0,this.selectImageList=[]},getImages:function(e){var t=this;this.loading=!0,this.images=[],this.page=e,Object(o["a"])({page:this.page,limit:this.page_size,pic_name:this.pic_name}).then((function(e){0==e.code&&(t.images=e.data.list,t.total=e.data.count),t.loading=!1}))},toSearch:function(){this.images=[],this.total=0,this.getImages(1)},handle_change:function(e,t){this.fileList=t},handle_remove:function(e,t){this.fileList=t},handle_success:function(e,t,i){this.toSearch()},myUploadFile:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(t){var i,a,l,n;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=this.$loading({lock:!0,text:"上传中"}),a=t.file,l=new FormData,l.append("file",a),e.prev=4,e.next=7,Object(o["c"])(l);case 7:n=e.sent,i.close(),this.$message(n.message),e.next=14;break;case 12:e.prev=12,e.t0=e["catch"](4);case 14:i.close();case 15:case"end":return e.stop()}}),e,this,[[4,12]])})));function t(t){return e.apply(this,arguments)}return t}(),toUpload:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(this.fileList.length<=0)){e.next=3;break}return this.$message.error("请先选择上传文件"),e.abrupt("return");case 3:this.$refs.fileUploadRef.submit();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleClose:function(){},setImages:function(e,t){if(e.isActive=!e.isActive,e.isActive){var i=this.selectImageList.filter((function(t){return e.pic_id==t.pic_id}));i.length||this.selectImageList.push(e)}else this.selectImageList=this.selectImageList.filter((function(e){return e.isActive}));this.$forceUpdate()},toConfirm:function(){this.$emit("onChooseImages",this.selectImageList),this.dialogVisible=!1}}}),c=r,u=(i("e884"),i("2877")),d=Object(u["a"])(c,a,l,!1,null,"4de782be",null);t["a"]=d.exports},"6eb0":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",e._l(e.config,(function(t,a){return i("div",{key:a,staticClass:"btn-warp",style:{marginRight:e.marginRight},on:{click:function(i){return e.callFn(t.event)}}},[t.permission?[i("el-button",{directives:[{name:"permission",rawName:"v-permission",value:t.permission,expression:"item.permission"},{name:"waves",rawName:"v-waves"}],attrs:{type:t.type,size:t.size||"mini",icon:t.icon,disabled:t.disabled,plain:t.plain}},[e._v(e._s(t.text))])]:i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],attrs:{type:t.type,size:t.size||"mini",icon:t.icon,disabled:t.disabled,plain:t.plain}},[e._v(e._s(t.text))])],2)})),0)},l=[],s=i("4381"),n=i("6724"),o={directives:{waves:n["a"],permission:s["a"]},props:{config:{type:Array,default:function(){return[]}},row:{type:Object,default:function(){}},marginRight:{type:String,default:"10px"}},methods:{callFn:function(e){this.$emit("callFn",e),this.$emit(e)}}},r=o,c=(i("4a76"),i("2877")),u=Object(c["a"])(r,a,l,!1,null,"1744d387",null);t["a"]=u.exports},8564:function(e,t,i){},"8cbc":function(e,t,i){"use strict";i("212a")},9040:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divder"},[e._v(e._s(e.title))])},l=[],s={props:{title:{type:String,default:""}}},n=s,o=(i("0ce5"),i("2877")),r=Object(o["a"])(n,a,l,!1,null,"b519469c",null);t["a"]=r.exports},"96d7":function(e,t,i){},ad41:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"formCustom",attrs:{model:e.form,rules:e.rules,inline:e.baseConfig.inline||!1,"label-width":e.baseConfig.labelWidth||"120px"}},[e._l(e.config,(function(t,a){return i("div",{key:a,staticClass:"item"},[t.slot?e._t(t.prop,null,{row:t}):[t.title?i("dividerTitle",{attrs:{title:t.title}}):e._e(),e._v(" "),"input"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",required:t.required,prop:t.model}},[i("el-input",{style:{width:e.baseConfig.inputWidth||"300px"},attrs:{placeholder:t.placeholder,clearable:!1,disabled:e.disabled(t),minlength:t.min,maxlength:t.max},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}}),e._v(" "),i("span",[e._v(e._s(t.remark))])],1):"select"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("el-select",{style:{width:e.baseConfig.selectWidth||"300px"},attrs:{placeholder:t.placeholder||"全部",multiple:t.multiple||!1,clearable:!1,disabled:e.disabled(t)},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}},e._l(e.options[t.options.name],(function(e,a){return i("el-option",{key:a,attrs:{label:t.label||e[t.options.label],value:e.value||e[t.options.value]}})})),1),e._v(" "),i("span",[e._v(e._s(t.remark))])],1):"textarea"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("div",{style:{width:e.baseConfig.textareaWidth||"50%"}},[i("el-input",{attrs:{type:"textarea",placeholder:t.placeholder,clearable:!1,disabled:e.disabled(t),maxlength:t.max,rows:"6"},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1)]):"cascader"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("el-cascader",{style:{width:e.baseConfig.selectWidth||"300px"},attrs:{options:e.options[t.options.name],disabled:e.disabled(t),props:t.options.props,"change-on-select":t.options.changeOnSelect},on:{"active-item-change":t.options.activeItemChange},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}}),e._v(" "),i("span",[e._v(e._s(t.remark))])],1):"number"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":"",disabled:e.disabled(t)},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}}),e._v(" "),i("span",[e._v(e._s(t.remark))])],1):"radio"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("el-radio-group",{attrs:{disabled:e.disabled(t)},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}},e._l(e.options[t.options.name],(function(a,l){return i("el-radio",{key:l,attrs:{label:a.label||a[t.options.label]}},[e._v(e._s(a.value||a[t.options.value]))])})),1),e._v(" "),i("div",[e._v(" "+e._s(t.remark))])],1):"switch"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("el-switch",{attrs:{disabled:e.disabled(t)},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}}),e._v(" "),i("span",[e._v(e._s(t.remark))])],1):"img"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,required:t.required}},[i("uploadImg",{attrs:{model:t.model,numshow:t.max||1,uptext:t.text||"添加主图","up-height":t.height||120,upWith:t.width||120,imglist:e.form[t.model],isPreview:t.isPreview,disabled:e.disabled(t)},on:{change:e.getImgUrls},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):"editor"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":",prop:t.model,disabled:!0,required:t.required}},[i("Editor",{ref:"editor",refInFor:!0,attrs:{value:e.form[t.model],height:300,disabled:e.disabled(t)}})],1):"sku"===t.type?[e.form.specification?i("GoodsSku",{staticClass:"goods-sku",attrs:{disabled:e.disabled(t)},model:{value:e.form.skuData,callback:function(t){e.$set(e.form,"skuData",t)},expression:"form.skuData"}}):e._e()]:"text"===t.type&&e.ifControl(t)?i("el-form-item",{attrs:{label:t.label+":"}},[i("span",[e._v(e._s(e.form[t.model]))])]):e._e()]],2)})),e._v(" "),e.isBtn(e.baseConfig.isBtn)?i("div",{staticClass:"btn-fixed"},[e.baseConfig.disabled?i("buttomCustom",{attrs:{config:e.btnConfig2},on:{callFn:e.callFn}}):i("buttomCustom",{attrs:{config:e.btnConfig,marginRight:"20px"},on:{callFn:e.callFn}})],1):e._e()],2)},l=[],s=i("9040"),n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{style:e.myStyle},[i("div",{staticClass:" isblock"},[e.isPreview||e.disabled?[i("div",{directives:[{name:"show",rawName:"v-show",value:!e.isopens,expression:"!isopens"}],staticClass:"avatar-uploader column flex-c-c",style:{width:e.upWith+"px",height:e.upHeight+"px",borderRadius:e.upBradius+"px"},on:{click:e.openImgStore}},[i("i",{staticClass:"el-icon-plus avatar-uploader-icon-sf"},[e._v(e._s(e.uptext))]),e._v(" "),i("div",{staticClass:"avatar-uploader-icon-sf"},[e._v("（"+e._s(e.zlist.length)+"/"+e._s(e.numshow)+"）")])])]:[i("div",{directives:[{name:"show",rawName:"v-show",value:e.zlist.length!=e.numshow,expression:"zlist.length != numshow"}],staticClass:"isleft"},[i("el-upload",{ref:"fileUploadRef",staticClass:"avatar-uploader",style:{width:e.upWith+"px",height:e.upHeight+"px",borderRadius:e.upBradius+"px",lineHeight:e.upHeight+"px"},attrs:{action:e.updatehttp,"show-file-list":!1,accept:".jpg, .jpeg, .png, .gif, .bmp, .JPG, .JPEG, .PBG, .GIF, .BMP",headers:e.headers,data:e.updataimg,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload,"http-request":e.handleToUpload}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.isopens,expression:"isopens"}]},[i("i",{staticClass:"el-icon-loading"}),e._v("\n            上传中...\n          ")]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!e.isopens,expression:"!isopens"}]},[i("i",{staticClass:"el-icon-plus avatar-uploader-icon-sf"},[e._v(e._s(e.uptext))]),e._v(" "),i("div",{staticClass:"avatar-uploader-icon-sf"},[e._v("（"+e._s(e.zlist.length)+"/"+e._s(e.numshow)+"）")])])])],1)],e._v(" "),i("draggable",{staticClass:"dira",attrs:{animation:100,disabled:e.disabled},model:{value:e.zlist,callback:function(t){e.zlist=t},expression:"zlist"}},e._l(e.zlist,(function(t,a){return i("div",{key:a,staticClass:" isblocks"},[i("div",{staticClass:"ress"},[i("el-image",{ref:"preview",refInFor:!0,style:{width:e.upWith+"px",height:e.upHeight+"px",borderRadius:e.upBradius+"px"},attrs:{"preview-src-list":e.zlist,src:t}}),e._v(" "),i("div",{staticClass:"imgs_prews"},[i("div",{staticClass:"imgs_prew",style:{width:e.upWith+"px",height:e.upHeight+"px",borderRadius:e.upBradius+"px"}},[i("i",{staticClass:"el-icon-view",on:{click:function(t){return e.ylimg(a)}}}),e._v(" "),i("span"),e._v(" "),i("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.deleteimg(a)}}})])])],1)])})),0)],2),e._v(" "),e.isPreview||e.disabled?i("imgStore",{attrs:{visible:e.visible,maxNum:e.numshow},on:{"update:visible":function(t){e.visible=t},imgs:e.getImgsUrl}}):e._e()],1)},o=[],r=(i("6762"),i("2fdb"),i("c5f6"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"管理图片",visible:e.show,width:"70%"},on:{"update:visible":function(t){e.show=t}}},[a("div",{staticClass:"flex"},[a("div",{staticClass:"column flex"},[a("div",{staticClass:"flex-b-c"},[a("span",{staticClass:"title",on:{click:e.handleAddGroup}},[e._v("添加分组")]),e._v(" "),a("span",{staticClass:"title",on:{click:e.handleGroupManage}},[e._v(e._s(!0===e.isManage?"关闭":"管理分组"))])]),e._v(" "),a("div",{staticClass:"tree-wrap"},[a("el-tree",{ref:"tree",attrs:{data:e.tree,"node-key":"id","default-expand-all":"","allow-drop":e.allowDrop,"allow-drag":e.allowDrag,"expand-on-click-node":e.isExpand},on:{"node-drag-start":e.handleDragStart,"node-drag-enter":e.handleDragEnter,"node-drag-leave":e.handleDragLeave,"node-drag-over":e.handleDragOver,"node-drag-end":e.handleDragEnd,"node-drop":e.handleDrop,"node-click":e.nodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node,l=t.data;return a("span",{staticClass:"custom-tree-node"},[[a("span",{staticClass:"mr-10 fs-12"},[e._v("\n                "+e._s(l.label)+"\n              ")]),e._v(" "),e.isManage&&l.children?a("span",{staticClass:"btn",on:{click:function(t){return e.handleAppend(l)}}},[e._v("\n                插入\n              ")]):e._e(),e._v(" "),e.isManage?a("span",{staticClass:"btn",on:{click:function(t){return e.handleEditGroup(i,l)}}},[e._v("\n                编辑\n              ")]):e._e(),e._v(" "),e.isManage?a("span",{staticClass:"btn red",attrs:{slot:"reference"},on:{click:function(t){return e.handleRemoveGroup(i,l)}},slot:"reference"},[e._v("\n                删除\n              ")]):e._e()]],2)}}])})],1)]),e._v(" "),a("div",{staticClass:"right-wrap flex-1"},[a("div",[a("div",{staticClass:"head  flex-b-c"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){e.isUploadImg=!0}}},[e._v("上传图片")]),e._v(" "),a("el-input",{attrs:{placeholder:"请输入图片名称搜索"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.searchGoodsImg},slot:"append"})],1)],1),e._v(" "),a("div",{staticClass:"img-list flex"},e._l(e.imgList,(function(t,l){return a("div",{key:l,staticClass:"item",on:{click:function(i){return e.handlecheckImg(t,l)}}},[a("img",{attrs:{src:i("fe67")}}),e._v(" "),t.check?[a("div",{staticClass:"triangle"}),e._v(" "),a("i",{staticClass:"el-icon-check"})]:e._e()],2)})),0)]),e._v(" "),a("pagination",{attrs:{total:e.total,page:e.queryImgParams.page,limit:e.queryImgParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryImgParams,"page",t)},"update:limit":function(t){return e.$set(e.queryImgParams,"pageSize",t)},pagination:e.getGoodsImgs}})],1)]),e._v(" "),a("span",{attrs:{slot:"footer"},slot:"footer"},[a("buttomCustom",{attrs:{config:e.btnConfig},on:{handleCancel:function(t){e.show=!1},handleSubmit:e.handleSubmit}})],1),e._v(" "),a("group",{attrs:{visible:e.isGroupShow,data:e.nodeData,parentId:e.parentId},on:{"update:visible":function(t){e.isGroupShow=t},callFn:e.callFn}}),e._v(" "),a("uploadImgDialog",{ref:"uploadImgDialog",attrs:{visible:e.isUploadImg,async:!0},on:{"update:visible":function(t){e.isUploadImg=t},callFn:e.uploadGoodsImg}})],1)}),c=[],u=(i("7f7f"),i("20d6"),i("ac6a"),i("6eb0")),d=i("333d"),p=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"分组管理",visible:e.dialogVisible,"append-to-body":"",width:"30%",top:"200px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,rules:e.rules}},[i("el-form-item",{attrs:{label:"分组名称：",prop:"name"}},[i("el-input",{attrs:{placeholder:"请输入分组名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),i("buttomCustom",{staticClass:"buttomCustom",attrs:{config:e.btnConfig},on:{handleCancel:function(t){e.visible=!1},handleSubmit:e.handleSubmit}})],1)},m=[],f=i("c40e"),h={components:{ButtomCustom:u["a"]},props:{visible:{type:Boolean,default:!1},data:{type:Object,default:function(){}},parentId:{type:[Number,String],default:null}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{data:function(e){this.form.parentId=this.parentId,this.form.id=e.id,this.form.name=e.label}},data:function(){return{form:{name:"",parentId:this.parentId},rules:{name:[{required:!0,message:"请输入分组名称",trigger:"blur"}]},btnConfig:[{plain:!0,text:"取 消",event:"handleCancel"},{type:"primary",text:"确 认",event:"handleSubmit"}]}},methods:{handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){return t&&Object(f["f"])(e.from).then((function(t){200===t.code&&(e.dialogVisible=!1,e.$emit("callFn",e.form),e.$message({message:"修改成功",type:"success"}))})),!1}))}}},b=h,g=(i("fd6b"),i("2877")),v=Object(g["a"])(b,p,m,!1,null,"65e78e5c",null),_=v.exports,k=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"管理图片",visible:e.dialogVisible,"append-to-body":"",width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",{staticClass:"component-upload-image"},[i("el-upload",{ref:"imageUpload",class:{hide:this.fileList.length>=this.limit},attrs:{multiple:"",action:e.uploadImgUrl,"list-type":"picture-card","on-success":e.handleUploadSuccess,"before-upload":e.handleBeforeUpload,limit:e.limit,"on-error":e.handleUploadError,"on-exceed":e.handleExceed,"on-remove":e.handleDelete,"show-file-list":!0,headers:e.headers,"file-list":e.fileList,"on-preview":e.handlePictureCardPreview}},[i("i",{staticClass:"el-icon-plus"})]),e._v(" "),e.showTip?i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("\n      请上传\n      "),e.fileSize?[e._v(" 大小不超过 "),i("b",{staticStyle:{color:"#f56c6c"}},[e._v(e._s(e.fileSize)+"MB")])]:e._e(),e._v(" "),e.fileType?[e._v(" 格式为 "),i("b",{staticStyle:{color:"#f56c6c"}},[e._v(e._s(e.fileType.join("/")))])]:e._e(),e._v("\n      的文件\n    ")],2):e._e(),e._v(" "),i("div",{staticClass:"tr"},[i("buttomCustom",{attrs:{config:e.btnConfig},on:{handleCancel:function(t){e.visible=!1},handleSubmit:e.handleSubmit}})],1)],1),e._v(" "),i("el-dialog",{attrs:{visible:e.dialogVisible,title:"预览",width:"800","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("img",{staticStyle:{display:"block","max-width":"100%",margin:"0 auto"},attrs:{src:e.dialogImageUrl}})])],1)},y=[],w=(i("a481"),i("28a5"),i("5f87")),x={components:{ButtomCustom:u["a"]},props:{visible:{type:Boolean,default:!1},async:{type:Boolean,default:!1},value:[String,Object,Array],limit:{type:Number,default:5},fileSize:{type:Number,default:5},fileType:{type:Array,default:function(){return["png","jpg","jpeg"]}},isShowTip:{type:Boolean,default:!0}},data:function(){return{number:0,uploadList:[],dialogImageUrl:"",dialogVisible:!1,hideUpload:!1,baseUrl:"/",uploadImgUrl:"//common/upload",headers:{Authorization:"Bearer "+Object(w["a"])()},fileList:[],btnConfig:[{plain:!0,text:"取 消",event:"handleCancel"},{type:"primary",text:"确 认",event:"handleSubmit"}]}},watch:{value:{handler:function(e){var t=this;if(!e)return this.fileList=[],[];var i=Array.isArray(e)?e:this.value.split(",");this.fileList=i.map((function(e){return"string"===typeof e&&(e=-1===e.indexOf(t.baseUrl)?{name:t.baseUrl+e,url:t.baseUrl+e}:{name:e,url:e}),e}))},deep:!0,immediate:!0}},computed:{showTip:function(){return this.isShowTip&&(this.fileType||this.fileSize)},dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},methods:{handleBeforeUpload:function(e){var t=!1;if(this.fileType.length){var i="";e.name.lastIndexOf(".")>-1&&(i=e.name.slice(e.name.lastIndexOf(".")+1)),t=this.fileType.some((function(t){return e.type.indexOf(t)>-1||!!(i&&i.indexOf(t)>-1)}))}else t=e.type.indexOf("image")>-1;if(!t)return this.$message({message:"文件格式不正确, 请上传".concat(this.fileType.join("/"),"图片格式文件!"),type:"info"}),!1;if(this.fileSize){var a=e.size/1024/1024<this.fileSize;if(!a)return this.$message({message:"上传头像图片大小不能超过 ".concat(this.fileSize," MB!"),type:"info"}),!1}this.number++},handleExceed:function(){this.$message({message:"上传文件数量不能超过 ".concat(this.limit," 个!"),type:"info"})},handleUploadSuccess:function(e,t){200===e.code?(this.uploadList.push({name:e.fileName,url:e.fileName}),this.uploadedSuccessfully()):(this.number--,this.$message({message:e.msg,type:"info"}),this.$refs.imageUpload.handleRemove(t),this.uploadedSuccessfully())},handleDelete:function(e){var t=this.fileList.map((function(e){return e.name})).indexOf(e.name);t>-1&&(this.fileList.splice(t,1),this.$emit("input",this.listToString(this.fileList)))},handleUploadError:function(){this.$message({message:"上传图片失败，请重试",type:"info"})},uploadedSuccessfully:function(){this.number>0&&this.uploadList.length===this.number&&(this.fileList=this.fileList.concat(this.uploadList),this.uploadList=[],this.number=0,this.$emit("input",this.listToString(this.fileList)))},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},listToString:function(e,t){var i="";for(var a in t=t||",",e)e[a].url&&(i+=e[a].url.replace(this.baseUrl,"")+t);return""!=i?i.substr(0,i.length-1):""},handleSubmit:function(){this.async||(this.dialogVisible=!1),this.$emit("imgs",this.listToString(this.fileList))},handleCancel:function(){this.dialogVisible=!1}}},S=x,C=(i("06cf"),Object(g["a"])(S,k,y,!1,null,"4bf353b5",null)),$=C.exports,O={components:{ButtomCustom:u["a"],Pagination:d["a"],group:_,UploadImgDialog:$},props:{visible:{type:Boolean,default:!1},maxNum:{type:[Number,String],default:3}},computed:{show:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{visible:function(e){this.show=e}},data:function(){return{isManage:!1,isUploadImg:!1,isGroupShow:!1,isExpand:!0,parentId:null,total:0,keyword:"",queryImgParams:{page:1,pageSize:10},iPopover:!1,nodeData:{},imgList:[],btnConfig:[{plain:!0,text:"取 消",event:"handleCancel"},{type:"primary",text:"确 认",event:"handleSubmit"}],tree:[],defaultProps:{children:"children",label:"label"},dataCopy:[],ids:[],urls:[]}},created:function(){this.getGoodsImgs()},methods:{getImgs:function(e){},handleGroupManage:function(){this.dataCopy=JSON.parse(JSON.stringify(this.data)),this.isManage=!this.isManage,this.isExpand=!this.isExpand},handleCancelGroup:function(){this.tree=this.dataCopy,this.isManage=!1,this.ids=[]},handleSubmit:function(){this.visible=!1,this.$emit("imgs",this.urls),this.ids=[]},getGoodsImgsGroup:function(){var e=this;Object(f["d"])().then((function(t){200===t.code&&(e.tree=t.data)}))},getGoodsImgs:function(){var e=this;Object(f["c"])().then((function(t){200===t.code&&(e.total=t.count,t.data.forEach((function(t){t.check=e.ids.includes(t.id)})),e.imgList=t.data)}))},searchGoodsImg:function(){this.queryImgParams.page=1,this.getGoodsImgs()},handleAppend:function(e){var t=this;Object(f["a"])({parent:e.id,id:e.children.length++,label:"新节点"}).then((function(i){if(200===i.code){var a=e.children.length+2,l={id:a,label:"新节点"};e.children||t.$set(e,"children",[]),e.children.push(l)}}))},handleEditGroup:function(e,t){this.parentId=this.$refs.tree.getNode(e).parent.data.id,this.nodeData=t,this.isGroupShow=!0},handleRemoveGroup:function(e,t){var i=this,a=e.parent,l=a.data.children||a.data,s=l.findIndex((function(e){return e.id===t.id}));l.splice(s,1),this.$confirm("此操作将永久删除该分组, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=e.parent,l=a.data.children||a.data,s=l.findIndex((function(e){return e.id===t.id}));l.splice(s,1),i.isManage=!1,i.$message({type:"success",message:"已删除"})}))},handleAddGroup:function(){var e=this,t=this.data.length+2;Object(f["a"])({parent:null,id:t,label:"新节点"}).then((function(i){if(200===i.code){var a={id:t,label:"新节点",children:[]};e.data.push(a),e.$message({message:"添加分组成功",type:"success"})}}))},callFn:function(e){var t=e.parentId,i=e.id,a=e.name;if(t)e:for(var l=0;l<this.data.length;l++)for(var s=this.data[l],n=0;n<s.children.length;n++){var o=s.children[n];if(o.id==i){o.label=a;break e}}else this.data.some((function(e,t){e.id!==i||(e.label=a)}))},handlecheckImg:function(e,t){if(this.ids.length>=Number(this.maxNum))this.$message({message:"最大上传数量为".concat(this.maxNum,"张"),type:"info"});else{if(this.imgList[t].check=!e.check,this.imgList[t].check)return this.ids.push(e.id),void this.urls.push(e.image);var i=this.ids.findIndex((function(t){return t.id===e.id}));this.ids.splice(i,1),this.urls.splice(i,1)}},nodeClick:function(e,t){var i=this.$refs.tree.getNode(t).parent.data.id;i||(this.params.parentId=i),this.params.id=e.id,this.getGoodsImgs()},uploadGoodsImg:function(e){var t=this;Object(f["g"])({imgs:e}).then((function(e){200===e.code&&(t.$message({type:"success",message:"上传成功"}),t.$refs.uploadImgDialog.handleCancel())}))},handleDragStart:function(e,t){console.log("drag start",e)},handleDragEnter:function(e,t,i){console.log("tree drag enter: ",t.label)},handleDragLeave:function(e,t,i){console.log("tree drag leave: ",t.label)},handleDragOver:function(e,t,i){console.log("tree drag over: ",t.label)},handleDragEnd:function(e,t,i,a){console.log("tree drag end: ",t&&t.label,i)},handleDrop:function(e,t,i,a){console.log("tree drop: ",t.label,i)},allowDrop:function(e,t,i){return"二级 3-1"!==t.data.label||"inner"!==i},allowDrag:function(e){return-1===e.data.label.indexOf("三级 3-2-2")},handleCancel:function(){}}},I=O,z=(i("de58"),Object(g["a"])(I,r,c,!1,null,"77e3c3b8",null)),j=z.exports,D=i("1980"),P=i.n(D),L=i("3f5e"),T={components:{ImgStore:j,draggable:P.a},props:{model:{type:String,default:""},isPreview:{type:Boolean,default:!1},numshow:{type:String,default:"3"},uptext:{type:String,default:"上传图片"},upWith:{type:Number,default:100},upHeight:{type:Number,default:100},upBradius:{type:Number,default:0},utypeTexe:{type:String,default:"WxCourse"},utype:{type:Number,default:1},isflag:{type:Boolean,default:!1},imglist:{type:Array,default:function(){return[]}},disabled:{type:Boolean,default:!1}},computed:{myStyle:function(){return{"--upWith":this.upWith+"px","--upHeight":this.upHeight+"px","--upBradius":this.upBradius+"px"}}},data:function(){return{visible:!1,updatehttp:"",headers:{},updataimg:{},isopens:!1,isok:!1,payVoucherDialog:!1,ishow:!0,zlist:[]}},watch:{zlist:function(){this.$emit("change",this.model,this.zlist)}},created:function(){this.zlist=this.imglist},methods:{handleToUpload:function(e){var t=this,i=e.file,a=new FormData;a.append("file",i),this.isopens=!0,Object(L["b"])(a).then((function(e){t.zlist.push(e.data.pic_path),t.isopens=!1}))},handleAvatarSuccess:function(e,t){this.isok},topprogressicon:function(e,t,i){this.isok&&(this.isopens=!0)},beforeAvatarUpload:function(e){if(e.type.includes("image")){var t=e.size/1024/1024<10;t?(this.isok=!0,this.updataimg.fileKey=this.utypeTexe):(this.$message.error("上传图片大小不能超过 10MB!"),this.isok=!1)}},deleteimg:function(e){this.$delete(this.zlist,e)},ylimg:function(e){this.$refs.preview[e].showViewer=!0},getImgsUrl:function(e){this.zlist=e,this.$emit("change",this.model,e)},openImgStore:function(){this.disabled||(this.visible=!0)}}},F=T,U=(i("8cbc"),Object(g["a"])(F,n,o,!1,null,"e36d7810",null)),V=U.exports,B=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",["url"==this.type?i("el-upload",{ref:"upload",staticStyle:{display:"none"},attrs:{action:e.uploadUrl,"before-upload":e.handleBeforeUpload,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,name:"file","show-file-list":!1,headers:e.headers}}):e._e(),e._v(" "),i("div",{ref:"editor",staticClass:"editor",style:e.styles})],1)},A=[],E=i("9339"),N=i.n(E),R=(i("a753"),i("8096"),i("14e1"),{name:"Editor",props:{value:{type:String,default:""},height:{type:Number,default:null},minHeight:{type:Number,default:null},readOnly:{type:Boolean,default:!1},fileSize:{type:Number,default:5},type:{type:String,default:"url"},disabled:{type:Boolean,default:!1}},data:function(){return{uploadUrl:"//common/upload",headers:{Authorization:"Bearer "+Object(w["a"])()},Quill:null,currentValue:"",options:{theme:"snow",bounds:document.body,debug:"warn",modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{align:[]}],["clean"],["link","image","video"]]},placeholder:"请输入内容",readOnly:this.readOnly}}},computed:{styles:function(){var e={};return this.minHeight&&(e.minHeight="".concat(this.minHeight,"px")),this.height&&(e.height="".concat(this.height,"px")),e}},watch:{value:{handler:function(e){e!==this.currentValue&&(this.currentValue=null===e?"":e,this.Quill&&this.Quill.pasteHTML(this.currentValue))},immediate:!0}},mounted:function(){var e=this;this.$nextTick((function(){e.Quill.enable(!e.disabled)})),this.init()},beforeDestroy:function(){this.Quill=null},methods:{init:function(){var e=this,t=this.$refs.editor;if(this.Quill=new N.a(t,this.options),this.Quill.options.readOnly=!0,"url"==this.type){var i=this.Quill.getModule("toolbar");i.addHandler("image",(function(t){t?e.$refs.upload.$children[0].$refs.input.click():e.quill.format("image",!1)}))}this.Quill.pasteHTML(this.currentValue),this.Quill.on("text-change",(function(t,i,a){var l=e.$refs.editor.children[0].innerHTML,s=e.Quill.getText(),n=e.Quill;e.currentValue=l,e.$emit("input",l),e.$emit("on-change",{html:l,text:s,quill:n})})),this.Quill.on("text-change",(function(t,i,a){e.$emit("on-text-change",t,i,a)})),this.Quill.on("selection-change",(function(t,i,a){e.$emit("on-selection-change",t,i,a)})),this.Quill.on("editor-change",(function(t){for(var i=arguments.length,a=new Array(i>1?i-1:0),l=1;l<i;l++)a[l-1]=arguments[l];e.$emit.apply(e,["on-editor-change",t].concat(a))}))},handleBeforeUpload:function(e){var t=["image/jpeg","image/jpg","image/png","image/svg"],i=t.includes(e.type);if(!i)return this.$message.error("图片格式错误!"),!1;if(this.fileSize){var a=e.size/1024/1024<this.fileSize;if(!a)return this.$message.error("上传文件大小不能超过 ".concat(this.fileSize," MB!")),!1}return!0},handleUploadSuccess:function(e,t){if(200==e.code){var i=this.Quill,a=i.getSelection().index;i.insertEmbed(a,"image","/"+e.fileName),i.setSelection(a+1)}else this.$message.error("图片插入失败")},handleUploadError:function(){this.$message.error("图片插入失败")}}}),q=R,G=(i("40a6"),Object(g["a"])(q,B,A,!1,null,null,null)),M=G.exports,W=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"sku-list"},[[i("div",{staticClass:"sku-list-head"},[i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.addSkuRow}},[e._v("添加规格")])],1),e._v(" "),e._l(e.skuData.attrList,(function(t,a){return i("div",{key:a,staticClass:"sku-list-item"},[i("div",{staticClass:"sku-list-item-main"},[i("div",{staticClass:"sku-list-item__layout"},[i("span",{staticClass:"span"},[e._v("规格名")]),e._v(" "),i("el-input",{staticClass:"input",attrs:{size:"small"},model:{value:t.attrName,callback:function(i){e.$set(t,"attrName",i)},expression:"item.attrName"}})],1),e._v(" "),i("div",{staticClass:"sku-list-item__layout"},[i("span",{staticClass:"span"},[e._v("规格值")]),e._v(" "),i("div",{staticClass:"sku-list-item-tags"},[e._l(t.attrValue,(function(t,l){return i("el-tag",{key:l,staticClass:"sku-list-item-tag",attrs:{closable:""},on:{close:function(t){return e.removeSkuAttr(a)}}},[e._v(e._s(t.attrValue))])})),e._v(" "),i("el-button",{attrs:{size:"small",icon:"el-icon-plus"},on:{click:function(t){return e.addSkuAttr(a)}}},[e._v("添加")])],2)])]),e._v(" "),i("el-button",{staticClass:"sku-list-item-removeBtn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.removeSkuRow(a)}}},[e._v("删除规格")])],1)}))],e._v(" "),i("div",{staticClass:"sku-wrap"},[i("el-table",{staticClass:"allSkuForm",attrs:{data:[e.allSkuForm],"header-cell-style":{background:"#fff"}}},[i("el-table-column",{attrs:{label:"SKU图片"}}),e._v(" "),i("el-table-column",{attrs:{label:"副标题"}}),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"供应商价格",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.costPrice,callback:function(t){e.$set(e.allSkuForm,"costPrice",t)},expression:"allSkuForm.costPrice"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("costPrice")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"供货价",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.price,callback:function(t){e.$set(e.allSkuForm,"price",t)},expression:"allSkuForm.price"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("price")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"销售价",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.salePrice,callback:function(t){e.$set(e.allSkuForm,"salePrice",t)},expression:"allSkuForm.salePrice"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("salePrice")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"原价",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.marketPrice,callback:function(t){e.$set(e.allSkuForm,"marketPrice",t)},expression:"allSkuForm.marketPrice"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("marketPrice")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"库存",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.goodsStock,callback:function(t){e.$set(e.allSkuForm,"goodsStock",t)},expression:"allSkuForm.goodsStock"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("goodsStock")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"店主返佣比例",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.rewardShopRate,callback:function(t){e.$set(e.allSkuForm,"rewardShopRate",t)},expression:"allSkuForm.rewardShopRate"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("rewardShopRate")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"公司返佣比例",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.rewardCompanyRate,callback:function(t){e.$set(e.allSkuForm,"rewardCompanyRate",t)},expression:"allSkuForm.rewardCompanyRate"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("rewardCompanyRate")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"重量",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.weight,callback:function(t){e.$set(e.allSkuForm,"weight",t)},expression:"allSkuForm.weight"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("weight")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"体积",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.wevolumeight,callback:function(t){e.$set(e.allSkuForm,"wevolumeight",t)},expression:"allSkuForm.wevolumeight"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("wevolumeight")}}},[e._v("设置")])],1)]),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"SKU编码",width:"160"}},[i("div",{staticClass:"flex-c"},[i("el-input",{attrs:{disabled:e.disabled},model:{value:e.allSkuForm.goodCode,callback:function(t){e.$set(e.allSkuForm,"goodCode",t)},expression:"allSkuForm.goodCode"}}),e._v(" "),i("span",{on:{click:function(t){return e.setItemSpce("goodCode")}}},[e._v("设置")])],1)]),e._v(" "),e.disabled?e._e():i("el-table-column",{attrs:{label:"操作",width:"100"}},[i("el-button",{attrs:{type:"text",size:"small"},on:{click:e.setAllSpce}},[e._v("全局替换")])],1)],1),e._v(" "),i("el-table",{staticClass:"sku-list",attrs:{border:"",data:e.skuData.skuList}},[i("el-table-column",{attrs:{label:"SKU图片",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("oss-upload",{attrs:{disabled:e.disabled,"on-success":function(i,a){return e.onUploadImgSuccess(i,a,t.row)},dir:"erp/goods"}},[t.row.icon?i("img",{staticClass:"goods-img",attrs:{src:t.row.icon}}):e.disabled?e._e():i("el-button",{attrs:{type:"text",size:"small"}},[e._v("上传图片")])],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"副标题",prop:"attrPath"}}),e._v(" "),i("el-table-column",{attrs:{label:"供应商价格",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.costPrice,callback:function(i){e.$set(t.row,"costPrice",i)},expression:"scope.row.costPrice"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"供货价",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.price,callback:function(i){e.$set(t.row,"price",i)},expression:"scope.row.price"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"销售价",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.salePrice,callback:function(i){e.$set(t.row,"salePrice",i)},expression:"scope.row.salePrice"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"原价",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.marketPrice,callback:function(i){e.$set(t.row,"marketPrice",i)},expression:"scope.row.marketPrice"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"库存",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.goodsStock,callback:function(i){e.$set(t.row,"goodsStock",i)},expression:"scope.row.goodsStock"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"店主返佣比例",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.rewardShopRate,callback:function(i){e.$set(t.row,"rewardShopRate",i)},expression:"scope.row.rewardShopRate"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"公司返佣比例",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.rewardCompanyRate,callback:function(i){e.$set(t.row,"rewardCompanyRate",i)},expression:"scope.row.rewardCompanyRate"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"重量kg",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.weight,callback:function(i){e.$set(t.row,"weight",i)},expression:"scope.row.weight"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"体积m3",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.wevolumeight,callback:function(i){e.$set(t.row,"wevolumeight",i)},expression:"scope.row.wevolumeight"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"SKU编码",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{attrs:{readonly:e.disabled,disabled:e.disabled},model:{value:t.row.goodCode,callback:function(i){e.$set(t.row,"goodCode",i)},expression:"scope.row.goodCode"}})]}}])}),e._v(" "),e.disabled?e._e():i("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.removeSkuAttr(0)}}},[e._v("删除")]),e._v(" "),t.$index>0?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleUp(t.$index)}}},[e._v("上移")]):e._e(),e._v(" "),t.$index<e.skuData.skuList.length-1?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleDown(t.$index)}}},[e._v("下移")]):e._e()]}}],null,!1,1787957673)})],1)],1)],2)},H=[],Q=i("5530"),K=(i("7514"),i("2909")),J=i("ade3"),X={model:{prop:"skuData",event:"change"},props:{skuData:{type:Object,default:function(){return{}}},disabled:{type:Boolean,default:!1}},data:function(){return{allSkuForm:Object(J["a"])(Object(J["a"])(Object(J["a"])(Object(J["a"])({costPrice:"",price:"",salePrice:"",marketPrice:"",goodsStock:"",rewardShopRate:""},"rewardShopRate",""),"weight",""),"wevolumeight",""),"goodCode","")}},watch:{"skuData.attrList":{handler:function(){this.disabled||this.$set(this.skuData,"skuList",this.getTable())},deep:!0,immediate:!0}},methods:{handleUp:function(e){var t=this.skuData.skuList;t.splice.apply(t,[e-1,1].concat(Object(K["a"])(t.splice(e,1,t[e-1]))))},handleDown:function(e){var t=this.skuData.skuList;t.splice.apply(t,[e,1].concat(Object(K["a"])(t.splice(e+1,1,t[e]))))},addSkuRow:function(e){this.skuData.attrList.push({attrName:"",attrValue:[]}),this.$emit("change",this.skuData)},removeSkuRow:function(e){this.skuData.attrList.splice(e,1),this.$emit("change",this.skuData)},removeSkuAttr:function(e,t){this.skuData.attrList[e].attrValue.splice(t,1),this.$emit("change",this.skuData)},addSkuAttr:function(e){var t=this;this.$prompt("请输入规格值","添加规格值",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S+/,inputErrorMessage:"规格值不能为空",closeOnClickModal:!1}).then((function(i){var a=i.value;t.skuData.attrList[e].attrValue.push({attrValue:a}),t.$emit("change",t.skuData)}))},onUploadImgSuccess:function(e,t,i){t&&(i.icon=t,this.$emit("change",this.skuData))},getTable:function(){var e=this,t=[],i=[],a=[],l=(this.skuData.attrList||[]).filter((function(e){return""!=e.attrName&&e.attrValue.length>0}));if(!l||0==l.length)return[];function s(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,l=0;l<i[t].length;l++)t<i.length-1?(e[t]=i[t][l],s(e,t+1)):a.push([].concat(Object(K["a"])(e),[i[t][l]]))}return l.forEach((function(e){i.push(e.attrValue)})),s([],0),a.forEach((function(i,a){var s,n,o="";i.forEach((function(e,t){o+="".concat(l[t].attrName,":").concat(e.attrValue)})),s=e.skuData.initSkulist.find((function(e){return o.includes(e.attrPath)}))||{},n=e.skuData.skuList.length>0&&e.skuData.skuList[a]?e.skuData.skuList[a]:{costPrice:"",price:"",salePrice:"",marketPrice:"",goodsStock:"",rewardShopRate:"",rewardCompanyRate:"",weight:"",wevolumeight:"",goodCode:"",icon:null},n.attrPath=o,n=Object(Q["a"])(Object(Q["a"])({},n),s),t.push(n)})),t},setItemSpce:function(e){var t=this;0!=this.skuData.skuList.length&&(this.skuData.skuList.forEach((function(i){i[e]=t.allSkuForm[e]})),this.$emit("change",this.skuData))},setAllSpce:function(){if(0!=this.skuData.skuList.length){var e=this.allSkuForm;this.skuData.skuList.forEach((function(t){t.costPrice=e.costPrice,t.price=e.price,t.salePrice=e.salePrice,t.marketPrice=e.marketPrice,t.goodsStock=e.goodsStock,t.rewardShopRate=e.rewardShopRate,t.rewardCompanyRate=e.rewardCompanyRate,t.weight=e.weight,t.wevolumeight=e.wevolumeight,t.goodCode=e.goodCode})),this.$emit("change",this.skuData)}}}},Y=X,Z=(i("347fe"),Object(g["a"])(Y,W,H,!1,null,"339808f9",null)),ee=Z.exports,te={components:{DividerTitle:s["a"],uploadImg:V,Editor:M,ButtomCustom:u["a"],GoodsSku:ee},model:{prop:"form",event:"change"},props:{baseConfig:{type:Object,default:function(){}},config:{type:Array,default:function(){return[]}},form:{type:Object,default:function(){}},rules:{type:Object,default:function(){}},options:{type:Object,default:function(){}},btnConfig:{type:Array,default:function(){return[{text:"返回",size:"small",event:"handleBack"},{type:"primary",size:"small",text:"保存",event:"handleSubmit"}]}}},data:function(){return{btnConfig2:[{text:"返回",size:"small",event:"handleBack"}]}},methods:{callFn:function(e){var t=this.$refs.formCustom;this.$emit(e,t)},getImgUrls:function(e,t){this.form[e]=t},disabled:function(e){return this.baseConfig.disabled||e.disabled},isBtn:function(e){return"boolean"!==typeof e||e},ifControl:function(e){e.show=e.show||{};var t=e.show,i=t.model,a=t.value;return!i||this.form[i]===a}}},ie=te,ae=(i("3ebd"),Object(g["a"])(ie,a,l,!1,null,"c18a983c",null));t["a"]=ae.exports},afdb:function(e,t,i){"use strict";i("e31f")},bbba:function(e,t,i){},bd1e:function(e,t,i){"use strict";i("ce3c")},c87f:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"top-right-btn",style:e.style},[i("el-row",[e.search?i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.showSearch?"隐藏搜索":"显示搜索",placement:"top"}},[i("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-search"},on:{click:function(t){return e.toggleSearch()}}})],1):e._e(),e._v(" "),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"刷新",placement:"top"}},[i("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-refresh"},on:{click:function(t){return e.refresh()}}})],1),e._v(" "),e.columns?i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"显隐列",placement:"top"}},[i("el-button",{attrs:{size:"mini",circle:"",icon:"el-icon-menu"},on:{click:function(t){return e.showColumn()}}})],1):e._e()],1),e._v(" "),i("el-dialog",{attrs:{title:e.title,visible:e.open,"append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[i("el-transfer",{attrs:{titles:["显示","隐藏"],data:e.filterColumns},on:{change:e.dataChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)],1)},l=[],s=(i("6762"),i("2fdb"),i("c5f6"),{name:"RightToolbar",data:function(){return{value:[],title:"显示/隐藏",open:!1,filterColumns:[]}},props:{showSearch:{type:Boolean,default:!0},columns:{type:Array},search:{type:Boolean,default:!0},gutter:{type:Number,default:10}},computed:{style:function(){var e={};return this.gutter&&(e.marginRight="".concat(this.gutter/2,"px")),e}},created:function(){for(var e in this.filterColumns=this.columns.filter((function(e){return e.label})),this.filterColumns)!1===this.filterColumns[e].visible&&this.value.push(parseInt(e))},methods:{toggleSearch:function(){this.$emit("update:showSearch",!this.showSearch)},refresh:function(){this.$emit("queryTable")},dataChange:function(e){for(var t in console.log(e,"data"),this.columns){var i=this.columns[t].key;this.columns[t].visible=!e.includes(i)}},showColumn:function(){this.open=!0}}}),n=s,o=(i("afdb"),i("2877")),r=Object(o["a"])(n,a,l,!1,null,"4908ef8a",null);t["a"]=r.exports},ce3c:function(e,t,i){},de58:function(e,t,i){"use strict";i("565f")},e31f:function(e,t,i){},e780:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"editForm",attrs:{model:e.form,rules:e.rules,inline:e.inline,"label-width":e.baseConfig.labelWidth||"94px"}},e._l(e.config,(function(t,a){return i("div",{key:a,staticClass:"item"},["input"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-input",{style:{width:e.baseConfig.inputWidth},attrs:{type:t.method||"text",size:"small",disabled:t.disabled,placeholder:t.placeholder,clearable:!1,max:t.max},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}},[t.optFaild?i("el-select",{style:{width:t.optWidth||"100px"},attrs:{slot:"prepend",size:"small"},slot:"prepend",model:{value:e.form[t.optFaild],callback:function(i){e.$set(e.form,t.optFaild,i)},expression:"form[item.optFaild]"}},e._l(e.options[t.options.name],(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):e._e()],1)],1):e._e(),e._v(" "),"select"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-select",{style:{width:e.baseConfig.selectWidth||"188px"},attrs:{size:"small",disabled:t.disabled,placeholder:t.placeholder||"全部",multiple:t.multiple||!1,clearable:!1},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}},e._l(e.options[t.options.name],(function(e,a){return i("el-option",{key:a,attrs:{label:e.label||e[t.options.label],value:e.value||e[t.options.value]}})})),1)],1):e._e(),e._v(" "),"cascader"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-cascader",{attrs:{disabled:t.disabled,size:"small",options:e.options[t.options.name],label:t.options.label,value:t.options.value},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"time"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-date-picker",{attrs:{disabled:t.disabled,size:"small",type:t.timeType||"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:!1},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"quickTime"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-date-picker",{attrs:{disabled:t.disabled,size:"small",type:t.timeType||"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,clearable:!1},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"date"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-date-picker",{attrs:{size:"small",disabled:t.disabled,type:t.dateType||"date",placeholder:t.placeholder||"选择日期"},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"textarea"===t.type?i("el-form-item",{style:{width:e.baseConfig.inputWidth},attrs:{prop:t.model,label:t.label+":"}},[i("el-input",{attrs:{type:"textarea",disabled:t.disabled,size:"small",placeholder:t.placeholder},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"casder"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-cascader",{attrs:{disabled:t.disabled,props:t.props||{},options:e.options[t.options.name]},on:{"active-item-change":function(i){t.fun||e.activeItemChange}},model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}})],1):e._e(),e._v(" "),"radio"===t.type?i("el-form-item",{attrs:{prop:t.model,label:t.label+":"}},[i("el-radio-group",{model:{value:e.form[t.model],callback:function(i){e.$set(e.form,t.model,i)},expression:"form[item.model]"}},e._l(e.options[t.options.name],(function(t,a){return i("el-radio",{key:a,attrs:{label:t.code}},[e._v(e._s(t.label))])})),1)],1):e._e()],1)})),0)},l=[],s={model:{prop:"form",event:"change"},props:{inline:{type:Boolean,default:!0},baseConfig:{type:Object,default:function(){return{inline:!0}}},config:{type:Array,default:function(){return[]}},form:{type:Object,default:function(){}},options:{type:Object,default:function(){}},rules:{type:Object,default:function(){}}},mounted:function(){var e=this;this.$nextTick((function(){e.$emit("validate",e.$refs.editForm.validate)}))},data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-6048e5),e.$emit("pick",[i,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-2592e6),e.$emit("pick",[i,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-7776e6),e.$emit("pick",[i,t])}}]}}},method:{ruleValidate:function(){console.log(1231312)},activeItemChange:function(){}}},n=s,o=(i("0860"),i("2877")),r=Object(o["a"])(n,a,l,!1,null,"2c18f4b0",null);t["a"]=r.exports},e884:function(e,t,i){"use strict";i("8564")},f2b4:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{staticClass:"tag_label",attrs:{title:"选择标签",visible:e.dialogTagVisible},on:{"update:visible":function(t){e.dialogTagVisible=t}}},[i("el-form",{attrs:{"label-position":"top"}},[i("el-form-item",{attrs:{label:"筛选条件："}},[i("el-radio-group",{on:{change:e.change_tag_status},model:{value:e.tmp_tag_status,callback:function(t){e.tmp_tag_status=t},expression:"tmp_tag_status"}},[i("el-radio",{attrs:{label:0}},[e._v("以下标签满足其一")]),e._v(" "),i("el-radio",{attrs:{label:1}},[e._v("以下标签同时满足")])],1)],1),e._v(" "),e._l(e.tagsList,(function(t,a){var l=t.group_name,s=t.tag_group_id,n=t.tags;return i("el-form-item",{key:a,attrs:{label:l+":"}},[n.length>0?i("div",{staticClass:"tags"},e._l(n,(function(t,a){var l=t.tag_id,n=t.tag_name,o=t.isActive;return i("span",{class:{active:o},on:{click:function(t){return e.setLable(l,s)}}},[e._v(e._s(n))])})),0):i("div",[e._v("\n                无标签\n            ")])])}))],2),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"text"},on:{click:e.skip}},[e._v("标签管理")]),e._v(" "),i("el-button",{on:{click:function(t){e.dialogTagVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.toConfirm}},[e._v("确 定")])],1)],1)},l=[],s=i("c7eb"),n=(i("96cf"),i("1da1")),o=i("9481"),r={data:function(){return{dialogTagVisible:!1,tagGroupData:[],tagsList:[],tabs:[],selectTags:[],tmp_tag_status:0}},props:["value","tag_status"],created:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getData();case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{getData:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(){var t;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["b"])({});case 3:t=e.sent,0==t.code&&(this.tagGroupData=t.data),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),init:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.dialogTagVisible=!0,this.tmp_tag_status=this.tag_status||0,e.next=4,this.getTagsList();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getTagsList:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(){var t=this;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.tabs=this.value,this.selectTags=[],this.tagsList=this.tagGroupData.map((function(e){return e.tags=e.tags.map((function(e){return t.value.indexOf(e.tag_id)>=0?(e.isActive=!0,t.selectTags.push(e)):e.isActive=!1,e})),e}));case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getActiveTags:function(){var e=Object(n["a"])(Object(s["a"])().mark((function e(t){var i,a=this;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getData();case 2:return i=[],this.tagGroupData.map((function(e){return e.tags.map((function(e){return a.value.indexOf(e.tag_id)>=0&&i.push(e),e})),e})),e.abrupt("return",i);case 5:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),skip:function(){this.$router.push("/member/tagManage/tagValue")},onSubmit:function(){this.$emit("input",this.tabs),this.$emit("onSelectTags",this.selectTags)},change_tag_status:function(e){this.$emit("update:tag_status",e)},setLable:function(e,t){var i=this;this.tagsList.map((function(a,l){return a.tag_group_id==t&&a.tags.map((function(t){return t.tag_id==e&&(t.isActive=!t.isActive,t.isActive?i.tabs.push(e):i.tabs=i.tabs.filter((function(t){return t!=e})),t.isActive?i.selectTags.push(t):i.selectTags=i.selectTags.filter((function(e){return e.tag_id!=t.tag_id}))),t})),a})),this.$forceUpdate()},toConfirm:function(){this.onSubmit(),this.dialogTagVisible=!1}}},c=r,u=(i("571c"),i("2877")),d=Object(u["a"])(c,a,l,!1,null,"ed68c54e",null);t["a"]=d.exports},fce7:function(e,t,i){},fd6b:function(e,t,i){"use strict";i("07b2")}}]);