(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f8719c24"],{"15fd":function(e,a,t){"use strict";function l(e,a){if(null==e)return{};var t,l,n={},r=Object.keys(e);for(l=0;l<r.length;l++)t=r[l],a.indexOf(t)>=0||(n[t]=e[t]);return n}function n(e,a){if(null==e)return{};var t,n,r=l(e,a);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],a.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}t.d(a,"a",(function(){return n}))},"3b38":function(e,a,t){"use strict";t.d(a,"n",(function(){return n})),t.d(a,"m",(function(){return r})),t.d(a,"p",(function(){return i})),t.d(a,"l",(function(){return o})),t.d(a,"c",(function(){return u})),t.d(a,"g",(function(){return s})),t.d(a,"f",(function(){return _})),t.d(a,"e",(function(){return d})),t.d(a,"j",(function(){return m})),t.d(a,"k",(function(){return p})),t.d(a,"i",(function(){return c})),t.d(a,"h",(function(){return v})),t.d(a,"a",(function(){return b})),t.d(a,"o",(function(){return f})),t.d(a,"b",(function(){return w})),t.d(a,"q",(function(){return y})),t.d(a,"d",(function(){return h}));var l=t("b775");function n(e){return Object(l["a"])({url:"/admin/pintuan/data.html",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/admin/pintuan/store.html",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:e})}function _(e){return Object(l["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:e})}function d(e){return Object(l["a"])({url:"/admin/pintuan/change_status",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:e})}function p(e){return Object(l["a"])({url:"/admin/pintuan/editStock",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:e})}function v(e){return Object(l["a"])({url:"/admin/pintuan/editSort",method:"post",data:e})}function b(e){return Object(l["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:e})}function f(e){return Object(l["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:e})}function w(e){return Object(l["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:e})}function y(e){return Object(l["a"])({url:"/admin/pintuan/update.html",method:"post",data:e})}function h(e){return Object(l["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:e})}},"5ab6":function(e,a,t){},c602:function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"details"},[t("div",{staticClass:"edit_title"},[e._v(e._s(e.$route.query.pintuan_id?"编辑":"添加")+"专题活动")]),e._v(" "),t("el-form",{ref:"form",attrs:{model:e.form,"label-position":"right",inline:!1,rules:e.rules,"label-width":"180px"}},[t("el-form-item",{staticStyle:{width:"50%"},attrs:{prop:"pintuan_name",label:"活动名称："}},[t("el-input",{attrs:{size:"small",placeholder:"请输入优惠券名称",disabled:e.is_preview},model:{value:e.form.pintuan_name,callback:function(a){e.$set(e.form,"pintuan_name",a)},expression:"form.pintuan_name"}})],1),e._v(" "),t("el-form-item",{staticStyle:{width:"50%"},attrs:{prop:"valid_date",label:"活动时间："}},[t("el-date-picker",{attrs:{size:"small",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:e.is_preview},model:{value:e.form.valid_date,callback:function(a){e.$set(e.form,"valid_date",a)},expression:"form.valid_date"}}),e._v(" "),t("p",{staticClass:"tips"},[e._v("结束时间不能小于开始时间，也不能小于当前时间")])],1),e._v(" "),t("el-form-item",{attrs:{label:"活动类型："}},[t("el-radio-group",{attrs:{disabled:e.is_preview},model:{value:e.form.promotion_type,callback:function(a){e.$set(e.form,"promotion_type",a)},expression:"form.promotion_type"}},[t("el-radio",{attrs:{label:"business"}},[e._v("拼商团")]),e._v(" "),t("el-radio",{attrs:{label:"new"}},[e._v("新人团")])],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"团长中奖限制："}},[t("el-radio-group",{attrs:{disabled:e.is_preview},model:{value:e.form.leader_win,callback:function(a){e.$set(e.form,"leader_win",a)},expression:"form.leader_win"}},[t("el-radio",{attrs:{label:3}},[e._v("团长必不中奖")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("团长必中奖")]),e._v(" "),t("el-radio",{attrs:{label:1}},[e._v("不限制")])],1)],1),e._v(" "),t("el-form-item",{staticStyle:{width:"50%"},attrs:{prop:"winning_num",label:"中奖名额："}},[t("el-input",{staticClass:"offered",attrs:{size:"small",type:"number",placeholder:"请输入优惠券名称",disabled:e.is_preview},model:{value:e.form.winning_num,callback:function(a){e.$set(e.form,"winning_num",a)},expression:"form.winning_num"}}),e._v(" "),t("p",{staticClass:"tips"},[e._v("中奖名额是每个已经成功成团的中奖名额")])],1),e._v(" "),t("el-form-item",{attrs:{label:"机器人开团："}},[t("div",{staticClass:"open"},[t("el-radio-group",{attrs:{disabled:e.is_preview},model:{value:e.form.use_robot,callback:function(a){e.$set(e.form,"use_robot",a)},expression:"form.use_robot"}},[t("el-radio",{attrs:{label:0}},[e._v("关闭")]),e._v(" "),t("el-radio",{attrs:{label:1}},[e._v("启用")])],1),e._v(" "),t("span",{staticClass:"tips"},[e._v("，每个团派")]),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{disabled:1!=e.form.use_robot||e.is_preview,type:"number"},model:{value:e.form.robot_nums,callback:function(a){e.$set(e.form,"robot_nums",a)},expression:"form.robot_nums"}}),e._v(" "),t("span",{staticClass:"tips"},[e._v("个机器人参团")])],1)]),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"中奖奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.winning_award_money_type,callback:function(a){e.$set(e.form,"winning_award_money_type",a)},expression:"form.winning_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.winning_award_money_pct,callback:function(a){e.$set(e.form,"winning_award_money_pct",a)},expression:"form.winning_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"未中奖奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.not_winning_money_type,callback:function(a){e.$set(e.form,"not_winning_money_type",a)},expression:"form.not_winning_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.not_winning_award_money_pct,callback:function(a){e.$set(e.form,"not_winning_award_money_pct",a)},expression:"form.not_winning_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"店主奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.shop_award_money_type,callback:function(a){e.$set(e.form,"shop_award_money_type",a)},expression:"form.shop_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.shop_award_money_pct,callback:function(a){e.$set(e.form,"shop_award_money_pct",a)},expression:"form.shop_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"邀约成团奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.invite_award_money_type,callback:function(a){e.$set(e.form,"invite_award_money_type",a)},expression:"form.invite_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.invite_award_money_pct,callback:function(a){e.$set(e.form,"invite_award_money_pct",a)},expression:"form.invite_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"开团人所属经理奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.head_group_award_money_type,callback:function(a){e.$set(e.form,"head_group_award_money_type",a)},expression:"form.head_group_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.head_group_award_money_pct,callback:function(a){e.$set(e.form,"head_group_award_money_pct",a)},expression:"form.head_group_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"开团人奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.head_award_money_type,callback:function(a){e.$set(e.form,"head_award_money_type",a)},expression:"form.head_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.head_award_money_pct,callback:function(a){e.$set(e.form,"head_award_money_pct",a)},expression:"form.head_award_money_pct"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"邀请新人参团奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.invite_new_award_money_type,callback:function(a){e.$set(e.form,"invite_new_award_money_type",a)},expression:"form.invite_new_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.invite_new_award_money,callback:function(a){e.$set(e.form,"invite_new_award_money",a)},expression:"form.invite_new_award_money"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"中奖人所属店主奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.win_shop_award_money_type,callback:function(a){e.$set(e.form,"win_shop_award_money_type",a)},expression:"form.win_shop_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.win_shop_award_money,callback:function(a){e.$set(e.form,"win_shop_award_money",a)},expression:"form.win_shop_award_money"}})],1),e._v(" "),t("el-form-item",{staticClass:"offered",attrs:{label:"中奖人所属经理奖励："}},[t("span",{staticClass:"tips"},[e._v("奖励零钱: 奖励数量为")]),e._v(" "),t("el-select",{attrs:{placeholder:"请选择",disabled:e.is_preview},model:{value:e.form.win_manager_award_money_type,callback:function(a){e.$set(e.form,"win_manager_award_money_type",a)},expression:"form.win_manager_award_money_type"}},e._l(e.formopts.teamwordRewardOpt,(function(e,a){return t("el-option",{key:a,attrs:{value:e.value,label:e.label}})})),1),e._v(" "),t("el-input",{staticClass:"offered",staticStyle:{width:"100px",margin:"0 10px"},attrs:{type:"number",disabled:e.is_preview},model:{value:e.form.win_manager_award_money,callback:function(a){e.$set(e.form,"win_manager_award_money",a)},expression:"form.win_manager_award_money"}})],1),e._v(" "),t("el-form-item",{staticStyle:{width:"50%"},attrs:{prop:"pintuan_num",label:"成团人数："}},[t("el-input",{staticClass:"offered",attrs:{size:"small",type:"number",placeholder:"请输入成团人数",disabled:e.is_preview},model:{value:e.form.pintuan_num,callback:function(a){e.$set(e.form,"pintuan_num",a)},expression:"form.pintuan_num"}})],1)],1),e._v(" "),e.is_preview?e._e():t("div",{staticClass:"edit_buttons"},[t("el-button",{attrs:{type:"primary",size:"small",loading:e.loading},on:{click:e.onSave}},[e._v("保存")]),e._v(" "),t("el-button",{attrs:{plain:"",size:"small"},on:{click:e.skip}},[e._v("返回")])],1)],1)},n=[],r=t("15fd"),i=(t("28a5"),t("c71b")),o=t("3b38"),u=["valid_date"],s={data:function(){return{ruleForm:{},baseConfig:{labelWidth:"120px",inline:!1,inputWidth:"50%"},dialogTagVisible:!1,formopts:{teamwordRewardOpt:i["M"]},rules:{pintuan_name:[{required:!0,message:"活动名称不能为空",trigger:"blur"}],valid_date:[{required:!0,message:"时间不能为空",trigger:"change"}],winning_num:[{required:!0,message:"中奖名额",trigger:"blur"}]},form:{promotion_type:"business",leader_win:1,use_robot:0,winning_award_money_type:"2",not_winning_money_type:"2",shop_award_money_type:"2",invite_award_money_type:"2",head_group_award_money_type:"2",head_award_money_type:"2",invite_new_award_money_type:"2",win_shop_award_money_type:"2",win_manager_award_money_type:"2"},formConfig:[],loading:!1,is_preview:!1}},created:function(){this.is_preview=!!this.$route.query.is_preview},mounted:function(){this.init()},methods:{init:function(){var e=this,a=this.$route.query.pintuan_id;a&&Object(o["b"])({pintuan_id:a}).then((function(t){var l=t.data.pintuan,n=l.award_config,r=n.win_manager_award,i=n.win_shop_award,o=n.invite_new_award,u=n.head_award,s=n.head_group_award,_=n.invite_award,d=n.winning_award,m=n.shop_award,p=n.not_winning_award,c=l.pintuan_num,v=l.robot_nums,b=l.use_robot,f=l.valid_date,w=l.pintuan_name,y=l.promotion_type,h=l.leader_win,g=l.winning_num;e.form={valid_date:f.split(" - "),pintuan_name:w,promotion_type:y,leader_win:h,winning_num:g,use_robot:b,robot_nums:v,winning_award_money_type:d.money_type,winning_award_money_pct:d.money_pct,not_winning_money_type:p.money_type,not_winning_award_money_pct:p.money_pct,shop_award_money_type:m.money_type,shop_award_money_pct:m.money_pct,invite_award_money_type:_.money_type,invite_award_money_pct:_.money_pct,head_group_award_money_type:s.money_type,head_group_award_money_pct:s.money,head_award_money_type:u.money_type,head_award_money_pct:u.money,invite_new_award_money_type:o.money_type,invite_new_award_money:o.money,win_shop_award_money_type:i.money_type,win_shop_award_money:i.money,win_manager_award_money_type:r.money_type,win_manager_award_money:r.money,pintuan_num:c,pintuan_id:a}}))},onSave:function(){var e=this;this.loading=!0;var a=this.$route.query.pintuan_id,t=o["p"];a&&(t=o["q"]),this.$refs.form.validate((function(a){if(a){var l=e.form,n=l.valid_date,i=Object(r["a"])(l,u);return i.valid_date=" ".concat(e.$format(n[0])," - ").concat(e.$format(n[1])," "),void t(i).then((function(a){var t=a.message,l=a.code;l<0?e.$message.error(t):(e.loading=!1,e.$message.success("新增拼团活动成功"),e.$store.dispatch("delView",{path:e.$route.path}).then((function(a){a.visitedViews;e.skip()})))}))}}))},skip:function(){this.$router.push("/market/teamwork")}}},_=s,d=(t("c72b"),t("2877")),m=Object(d["a"])(_,l,n,!1,null,"8fa28560",null);a["default"]=m.exports},c71b:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"i",(function(){return n})),t.d(a,"H",(function(){return r})),t.d(a,"f",(function(){return i})),t.d(a,"A",(function(){return o})),t.d(a,"x",(function(){return u})),t.d(a,"e",(function(){return s})),t.d(a,"w",(function(){return _})),t.d(a,"c",(function(){return d})),t.d(a,"O",(function(){return m})),t.d(a,"j",(function(){return p})),t.d(a,"k",(function(){return c})),t.d(a,"l",(function(){return v})),t.d(a,"T",(function(){return b})),t.d(a,"d",(function(){return f})),t.d(a,"Q",(function(){return w})),t.d(a,"p",(function(){return y})),t.d(a,"P",(function(){return h})),t.d(a,"m",(function(){return g})),t.d(a,"I",(function(){return x})),t.d(a,"L",(function(){return k})),t.d(a,"N",(function(){return O})),t.d(a,"M",(function(){return C})),t.d(a,"S",(function(){return $})),t.d(a,"s",(function(){return j})),t.d(a,"B",(function(){return S})),t.d(a,"z",(function(){return R})),t.d(a,"K",(function(){return q})),t.d(a,"C",(function(){return P})),t.d(a,"h",(function(){return z})),t.d(a,"g",(function(){return G})),t.d(a,"o",(function(){return I})),t.d(a,"G",(function(){return V})),t.d(a,"J",(function(){return A})),t.d(a,"v",(function(){return D})),t.d(a,"F",(function(){return E})),t.d(a,"r",(function(){return L})),t.d(a,"b",(function(){return J})),t.d(a,"q",(function(){return N})),t.d(a,"R",(function(){return B})),t.d(a,"u",(function(){return F})),t.d(a,"t",(function(){return M})),t.d(a,"D",(function(){return T})),t.d(a,"E",(function(){return W})),t.d(a,"y",(function(){return H})),t.d(a,"n",(function(){return K}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],r=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],i=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],u=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],_=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],d=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],m=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],c=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],v=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],b=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],f=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],w=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],y=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],h=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],g=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],x=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],k=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],O=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],$=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],j=[{value:"0",label:"参团"},{value:"1",label:"团长"}],S=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],R=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],q=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],P=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],z=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],G=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],I=[{value:"1",label:"是"},{value:"2",label:"否"}],V=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],A=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],D=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],E=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],L=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],J=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],B=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],F=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],M=[{label:"按订单编号",value:"order_no"}],T=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],W=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],H=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],K=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},c72b:function(e,a,t){"use strict";t("5ab6")}}]);