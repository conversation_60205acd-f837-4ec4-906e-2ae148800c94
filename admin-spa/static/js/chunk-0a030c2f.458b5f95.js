(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a030c2f"],{"3f5e":function(e,a,l){"use strict";l.d(a,"b",(function(){return n})),l.d(a,"c",(function(){return u})),l.d(a,"a",(function(){return r}));var t=l("b775");function n(e){return Object(t["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(t["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function r(e){return Object(t["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,a,l){"use strict";var t=l("a18c"),n={inserted:function(e,a,l){var n=a.value,u=t["a"].app._route.meta&&t["a"].app._route.meta.permissions;u.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},u=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(u)),n.install=u;a["a"]=n},6396:function(e,a,l){"use strict";l.d(a,"a",(function(){return r})),Math.easeInOutQuad=function(e,a,l,t){return e/=t/2,e<1?l/2*e*e+a:(e--,-l/2*(e*(e-2)-1)+a)};var t=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function u(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(e,a,l){var r=u(),o=e-r,i=20,c=0;a="undefined"===typeof a?500:a;var d=function e(){c+=i;var u=Math.easeInOutQuad(c,r,o,a);n(u),c<a?t(e):l&&"function"===typeof l&&l()};d()}},6724:function(e,a,l){"use strict";l("8d41");var t={bind:function(e,a){e.addEventListener("click",(function(l){var t=Object.assign({},a.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},t),u=n.ele;if(u){u.style.position="relative",u.style.overflow="hidden";var r=u.getBoundingClientRect(),o=u.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(r.width,r.height)+"px",u.appendChild(o)),n.type){case"center":o.style.top=r.height/2-o.offsetHeight/2+"px",o.style.left=r.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(l.pageY-r.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(l.pageX-r.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=n.color,o.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",t)};window.Vue&&(window.waves=t,Vue.use(n)),t.install=n;a["a"]=t},"8d41":function(e,a,l){},"931b":function(e,a,l){"use strict";l("bb1c")},b885:function(e,a,l){"use strict";var t=l("e780");l.d(a,"d",(function(){return t["a"]}));var n=l("ad41");l.d(a,"c",(function(){return n["a"]}));var u=l("0476");l.d(a,"g",(function(){return u["a"]}));var r=l("6eb0");l.d(a,"a",(function(){return r["a"]}));var o=l("c87f");l.d(a,"f",(function(){return o["a"]}));var i=l("333d");l.d(a,"e",(function(){return i["a"]}));var c=l("05be");l.d(a,"b",(function(){return c["a"]}));l("9040");var d=l("4381");l.d(a,"h",(function(){return d["a"]}));var s=l("6724");l.d(a,"i",(function(){return s["a"]}))},bb1c:function(e,a,l){},c40e:function(e,a,l){"use strict";l.d(a,"e",(function(){return n})),l.d(a,"d",(function(){return u})),l.d(a,"f",(function(){return r})),l.d(a,"c",(function(){return o})),l.d(a,"a",(function(){return i})),l.d(a,"g",(function(){return c})),l.d(a,"b",(function(){return d}));var t=l("b775");function n(e){return Object(t["a"])({url:"/goods/product/state/",method:"post",data:e})}function u(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}function c(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(t["a"])({url:"/goods/product/page",method:"post",data:e})}},c52c:function(e,a,l){"use strict";l.r(a);var t=function(){var e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"app-container"},[l("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[l("div",{staticClass:"edit_title"},[e._v("店铺信息")]),e._v(" "),l("formQuery",{ref:"inForm",staticClass:"mb-20",attrs:{inline:!1,rules:e.rules,baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},on:{validate:e.setValidate},model:{value:e.form,callback:function(a){e.form=a},expression:"form"}}),e._v(" "),l("div",{staticClass:"edit_buttons"},[l("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleSave}},[e._v("保存")]),e._v(" "),l("el-button",{attrs:{plain:"",size:"small"},on:{click:e.skip}},[e._v("返回")])],1)],1)])},n=[],u=(l("28a5"),l("a481"),l("7f7f"),l("c7eb")),r=(l("96cf"),l("1da1")),o=l("7cea"),i=l("b885"),c=(l("c71b"),l("e87b")),d={components:{FormQuery:i["d"]},data:function(){return{loading:!1,ruleForm:{},baseConfig:{labelWidth:"120px",inline:!1,inputWidth:"50%"},formopts:{agentOpts:[],presidentOpts:[],mentorOpts:[]},rules:{agent_id:[{required:!0,message:"代理商名称不能为空",trigger:"change"}],president_id:[{required:!0,message:"会长不能为空",trigger:"change"}],mentor_id:[{required:!0,message:"导师不能为空",trigger:"change"}],mobiles:[{required:!0,message:"店主手机号码不能为空",trigger:"blur"}]},form:{},formConfig:[{type:"select",label:"代理商名称",model:"agent_id",placeholder:"请选择",options:{name:"agentOpts"},tips:function(){return"剩余".concat(o["a"].surplus_integral,"积分，可开").concat(o["a"].can_open_shop_num,"家店铺")}},{type:"select",label:"会长",model:"president_id",placeholder:"请选择",options:{name:"presidentOpts"}},{type:"select",label:"导师",model:"mentor_id",placeholder:"请选择",options:{name:"mentorOpts"}},{type:"textarea",label:"店主手机号码",model:"mobiles",placeholder:"请输入店主手机号，多个手机号用英文逗号隔开"}]}},created:function(){var e=Object(r["a"])(Object(u["a"])().mark((function e(){return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getData();case 2:case"end":return e.stop()}}),e,this)})));function a(){return e.apply(this,arguments)}return a}(),mounted:function(){},methods:{skip:function(){this.$router.push("/member/store/list")},getData:function(){var e=Object(r["a"])(Object(u["a"])().mark((function e(){var a;return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(c["b"])({});case 3:a=e.sent,0==a.code&&(this.formopts.mentorOpts=a.data.mentor_list.map((function(e){return{label:e.name,value:e.id}})),this.formopts.presidentOpts=a.data.president_list.map((function(e){return{label:e.username,value:e.id}})),this.formopts.agentOpts=a.data.agent_list.map((function(e){return{label:e.enterprise_name,value:e.site_id}}))),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])})));function a(){return e.apply(this,arguments)}return a}(),setValidate:function(e){this.validate=e},handleSave:function(){var e=this;this.validate((function(a){if(a){for(var l=e.form.mobiles.replace(/(^,)|(,$)/g,""),t=l.split(","),n=0;n<t.length;n++)if(!/^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(t[n]))return void e.$message.error("手机号码错误 ".concat(t[n]));var u={agent_id:e.form.agent_id,mobiles:l};Object(c["f"])(u).then((function(a){0==a.code?a.data.open_shop_integral<=a.data.surplus_integral?e.$confirm("确认注册店铺后，扣除"+a.data.open_shop_integral+"积分","提示",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0}).then((function(){e.loading=!0,Object(c["a"])(e.form).then((function(a){if(e.loading=!1,-2==a.code){var l=a.data.join(",");e.$message.error("已注册的手机号 ".concat(l))}else 0==a.code?(e.$message.success("添加成功"),setTimeout((function(){e.skip()}),1e3)):e.$message.error(a.message)}))})):e.$message.error("当前积分不足，请先充值"):e.$message.error(a.message)}))}}))}}},s=d,b=(l("931b"),l("2877")),v=Object(b["a"])(s,t,n,!1,null,"292952e2",null);a["default"]=v.exports},c71b:function(e,a,l){"use strict";l.d(a,"a",(function(){return t})),l.d(a,"i",(function(){return n})),l.d(a,"H",(function(){return u})),l.d(a,"f",(function(){return r})),l.d(a,"A",(function(){return o})),l.d(a,"x",(function(){return i})),l.d(a,"e",(function(){return c})),l.d(a,"w",(function(){return d})),l.d(a,"c",(function(){return s})),l.d(a,"O",(function(){return b})),l.d(a,"j",(function(){return v})),l.d(a,"k",(function(){return f})),l.d(a,"l",(function(){return p})),l.d(a,"T",(function(){return m})),l.d(a,"d",(function(){return h})),l.d(a,"Q",(function(){return g})),l.d(a,"p",(function(){return w})),l.d(a,"P",(function(){return _})),l.d(a,"m",(function(){return O})),l.d(a,"I",(function(){return y})),l.d(a,"L",(function(){return j})),l.d(a,"N",(function(){return x})),l.d(a,"M",(function(){return C})),l.d(a,"S",(function(){return k})),l.d(a,"s",(function(){return T})),l.d(a,"B",(function(){return q})),l.d(a,"z",(function(){return A})),l.d(a,"K",(function(){return N})),l.d(a,"C",(function(){return S})),l.d(a,"h",(function(){return $})),l.d(a,"g",(function(){return E})),l.d(a,"o",(function(){return R})),l.d(a,"G",(function(){return F})),l.d(a,"J",(function(){return I})),l.d(a,"v",(function(){return L})),l.d(a,"F",(function(){return V})),l.d(a,"r",(function(){return B})),l.d(a,"b",(function(){return M})),l.d(a,"q",(function(){return W})),l.d(a,"R",(function(){return z})),l.d(a,"u",(function(){return D})),l.d(a,"t",(function(){return H})),l.d(a,"D",(function(){return Q})),l.d(a,"E",(function(){return X})),l.d(a,"y",(function(){return P})),l.d(a,"n",(function(){return J}));var t=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],r=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],i=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],c=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],s=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],b=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],v=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],p=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],m=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],w=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],_=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],O=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],y=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],k=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],T=[{value:"0",label:"参团"},{value:"1",label:"团长"}],q=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],A=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],N=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],S=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],$=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],E=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],R=[{value:"1",label:"是"},{value:"2",label:"否"}],F=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],I=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],L=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],V=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],B=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],M=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],W=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],z=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],D=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],H=[{label:"按订单编号",value:"order_no"}],Q=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],X=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],P=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],J=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e87b:function(e,a,l){"use strict";l.d(a,"g",(function(){return n})),l.d(a,"d",(function(){return u})),l.d(a,"e",(function(){return r})),l.d(a,"h",(function(){return o})),l.d(a,"c",(function(){return i})),l.d(a,"b",(function(){return c})),l.d(a,"f",(function(){return d})),l.d(a,"a",(function(){return s}));var t=l("b775");function n(e){return Object(t["a"])({url:"/admin/shop/lists.html",method:"get",params:e})}function u(e){return Object(t["a"])({url:"/admin/shop/change_status.html",method:"post",data:e})}function r(e){return Object(t["a"])({url:"/admin/shop/editShop.html",method:"post",data:e})}function o(e){return Object(t["a"])({url:"/admin_plus/shop/showShop",method:"post",data:e})}function i(e){return Object(t["a"])({url:"/admin_plus/shop/editShop",method:"post",data:e})}function c(e){return Object(t["a"])({url:"/admin_plus/shop/addShop",method:"post",data:e})}function d(e){return Object(t["a"])({url:"/admin/agent/getSurplusIntegralByOpenShop",method:"post",data:e})}function s(e){return Object(t["a"])({url:"/admin/shop/addShop",method:"post",data:e})}},fe67:function(e,a,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);