(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7fa104c5"],{"227e":function(t,e,a){"use strict";a("22c7")},"22c7":function(t,e,a){},"6bef":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"app-container"},[a("span",{staticClass:"update-time"},[t._v("\n        更新时间："+t._s(t._f("parseTime")(new Date))+"\n        "),a("i",{staticClass:"el-icon-refresh-right",class:{active:t.fullscreenLoading},attrs:{title:"刷新"},on:{click:t.startLoad}})]),t._v(" "),a("div",{staticClass:"analy-card"},[a("span",[t._v("商品数据")]),t._v(" "),a("sales-data",{attrs:{data:t.goodsData,dataFailds:"commodity"}})],1),t._v(" "),a("div",{staticClass:"analy-card"},[a("span",[t._v("\n            商品/类目销售情况\n            "),a("span",t._l(t.tabs,(function(e,i){var n=e.code,l=e.label,s=e.columns;return a("span",{key:i,class:{active:t.tabIndex==n},on:{click:function(e){return t.switchTabs(n,s)}}},[t._v(t._s(l))])})),0)]),t._v(" "),a("o-table",{key:t.tableKey,ref:"table",attrs:{options:t.options,columns:t.columns,data:t.list},on:{onSearch:t.getList}})],1)])},n=[],l=a("5530"),s=(a("a481"),a("c7eb")),o=(a("96cf"),a("1da1")),r=a("9a1c"),u=a("34a6"),d=[{faild:"category_1",title:"一级类目"},{faild:"category_2",title:"二级类目"},{faild:"category_3",title:"三级类目"},{faild:"user_view",title:"UV",sortable:!0},{faild:"click_num",title:"PV",sortable:!0},{faild:"order_member",title:"购买人数",sortable:!0},{faild:"order_num",title:"支付订单数量",sortable:!0},{faild:"trans_rate",title:"转化率(订单数量/UV)",sortable:!0},{faild:"goods_money",title:"销售金额(元)",sortable:!0},{faild:"cost_money",title:"商品成本(元)",sortable:!0}],c=[{title:"商品ID",sortable:!0,faild:"goods_id"},{title:"商品名称",faild:"goods_name"},{title:"UV",sortable:!0,faild:"user_view"},{title:"PV",sortable:!0,faild:"click_num"},{title:"购买人数",sortable:!0,faild:"order_member"},{title:"支付订单数量",sortable:!0,faild:"order_num"},{title:"转化率(订单数量/UV)",sortable:!0,faild:"trans_rate"},{title:"销售金额(元)",sortable:!0,faild:"goods_money"},{title:"商品成本(元)",sortable:!0,faild:"cost_money"}],_=a("ef83"),f={data:function(){return{fullscreenLoading:!1,tabs:[{code:"1",label:"按单品",columns:c},{code:"2",label:"按类目",columns:d}],goodsData:{},columns:c,tabIndex:1,options:{pageNo:1,pageSize:10,total:0},list:[],singleData:[],cateData:[],sort:null,tableKey:!1}},components:{salesData:r["a"],OTable:u["a"]},mounted:function(){var t=Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getData();case 2:this.getList();case 3:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{getData:function(){var t=Object(o["a"])(Object(s["a"])().mark((function t(){var e;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(_["a"])({});case 3:e=t.sent,0==e.code&&(this.goodsData={spu_total:e.data.goods.spu_total,spu_online:e.data.goods.spu_online,online:e.data.supply_goods.online,offline:e.data.supply_goods.offline,brand_num:e.data.brand_num,supplier_num:e.data.supplier_num}),t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),getList:function(t,e){var a=this;this.fullscreenLoading=!0;var i={};if(e){var n=e.order.replace("ending","");i.field=e.prop,i.order=n}else this.$refs.table.clearSort();t&&(this.options.pageNo=t.pageNo,this.options.pageSize=t.pageSize),Object(_["b"])(Object(l["a"])({page:this.options.pageNo,page_size:this.options.pageSize,type:this.tabIndex},i)).then((function(t){var e=t.data,i=e.count,n=e.list;e.statistics;a.options.total=i,a.list=n,a.fullscreenLoading=!1}))},switchTabs:function(t,e){this.tabIndex=t,this.columns=e,this.getList(),this.tableKey=!this.tableKey},startLoad:function(){var t=Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getData();case 2:this.getList();case 3:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}},p=f,m=(a("227e"),a("2877")),b=Object(m["a"])(p,i,n,!1,null,"079b9cd3",null);e["default"]=b.exports},"9a1c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sales-data",class:{active:t.failds.length>6}},t._l(t.failds,(function(e,i){return a("div",{key:i,style:{width:"calc(100% / "+(t.failds.length>12?parseInt(t.failds.length/2):6)+")"}},[a("div",[t._v("\n            "+t._s(e.title)+"\n            "),e.subTitle?a("el-tooltip",{attrs:{content:e.subTitle,placement:"top-start"}},[a("i",{staticClass:"el-icon-question"})]):t._e(),t._v(" "),e.path?a("el-button",{attrs:{size:"mini",type:"danger",plain:""},on:{click:function(a){return t.goSkip(e.path)}}},[t._v("去看看")]):t._e()],1),t._v(" "),a("i",[t._v(t._s(t.data[e.faild[0]]))]),t._v(" "),1==e.type?a("span",{class:{active:t.getSize(e.faild)}},[t._v("\n            "+t._s(t.getSizeText(e.faild))+"\n            "),a("i",{class:t.getSizeIcon(e.faild)}),t._v(" "),a("span",[t._v(t._s(t.data[e.faild[1]])+"%")])]):t._e(),t._v(" "),2==e.type?a("span",[t._v("\n            活跃会员"),a("span",[t._v(t._s(t.data[e.faild[1]]))]),t._v("人\n        ")]):t._e(),t._v(" "),3==e.type?a("span",[t._v("\n            新增店铺"),a("span",[t._v("+"+t._s(t.data[e.faild[1]]))])]):t._e()])})),0)},n=[],l=a("3835"),s={sales:[{title:"销售总额（元）",subTitle:"平台生产全部订单的总金额，包含未支付订单金额",faild:["order_sale_amount","order_sale_amount_float_pro"],type:1},{title:"订单量",subTitle:"包含未支付订单数量",faild:["order_nums","order_nums_float_pro"],type:1},{title:"下单人数",subTitle:"包含未支付订单下单人数",faild:["persons_num","persons_num_float_pro"],type:1},{title:"支付总额（元）",subTitle:"已支付订单总金额",faild:["order_pay_amount","order_pay_amount_float_pro"],type:1},{title:"退款总额（元）",faild:["refund_amount","refund_amount_float_pro"],type:1},{title:"转化率",subTitle:"转化率＝订单量÷UV",faild:["change","change_float_pro"],type:1}],users:[{title:"会员总数",faild:["member_nums","member_nums_float_pro"],type:2},{title:"新增会员数",faild:["add_member_nums","add_member_nums_float_pro"],type:1},{title:"店铺总数",faild:["shop_nums","shop_nums_float_pro"],type:3},{title:"访客数（UV）",faild:["uv","uv_float_pro"],type:1},{title:"访问量（PV）",subTitle:"全部小程及H5序页面访问次数",faild:["pv","pv_float_pro"],type:1},{title:"客单价",subTitle:"客单价=销售额÷成交顾客数",faild:["unit_price","unit_price_float_pro"],type:1}],commodity:[{title:"商品总数",faild:["spu_total"]},{title:"上架商品数",subTitle:"全部已上架状态商品数量（含供应链同步商品）",faild:["spu_online"]},{title:"供应链选品上架",faild:["online"]},{title:"供应链异常下架",faild:["offline"],path:"/goods/quality/warning"},{title:"合作品牌数",subTitle:"已上架商品涉及的品牌数量",faild:["brand_num"]},{title:"合作供应商",subTitle:"已上架商品涉及的供应商数量",faild:["supplier_num"]}],teamwork:[{title:"开团人次",faild:["group_nums"]},{title:"成团人次",faild:["win_group_nums"]},{title:"开团人数",subTitle:"即该活动开团的用户数量，单个用户多次开团算一个用户",faild:["group_people_nums"]},{title:"参团人次",faild:["join_group_nums"]},{title:"参团人数",subTitle:"即该活动中参加别人发起拼团的人数，单个用户多次参团算一个用户",faild:["join_group_people_nums"]},{title:"活动拉新人数",subTitle:"该活动中首次参团，此前无任何活动参团记录，无普通商品下单记录的新用户",faild:["invite_new_nums"]},{title:"拼团支付金额",subTitle:"该活动中开团或参团支付的所有金额（含未中奖退款）",faild:["pay_money"]},{title:"成团率",subTitle:"成团人次/开团人次*100%",faild:["join_group_pro"]},{title:"中奖商品订单数",subTitle:"拼团活动中奖者发放商品，系统会为每个中奖者生成一个商品订单",faild:["win_order_nums"]},{title:"商品订单总金额",subTitle:"拼团中奖者的参团金额不退回，作为购买商品的交易金额。",faild:["win_order_money"]},{title:"分享次数",subTitle:"用户点击拼团分享按钮的次数（含拼团商品详情页、邀请参团页）",faild:["share_nums"]},{title:"分享人数",subTitle:"点击拼团分享按钮的人数（含拼团商品详情页、邀请参团页）",faild:["share_people_nums"]},{title:"打开分享次数",subTitle:"分享拼团链接被打开的次数（含拼团商品详情页、邀请参团页）",faild:["open_share_nums"]},{title:"打开分享人数",subTitle:"打开分享拼团链接的人数（含拼团商品详情页、邀请参团页）",faild:["open_share_people_nums"]}],reward:[{title:"中奖奖励",faild:["pintuan_winning_award"]},{title:"未中奖奖励",faild:["pintuan_not_winning_award"]},{title:"邀约成团奖励",faild:["pintuan_invite_award"]},{title:"邀请新人参团奖励",faild:["pintuan_invite_new_member_award"]},{title:"开团人奖励",faild:["head_award"]},{title:"开团人所属店主奖励",faild:["pintuan_award"]},{title:"开团人所属经理奖励",faild:["head_group_award"]},{title:"中奖人所属店主奖励",faild:["win_shop_award"]},{title:"中奖人所属经理奖励",faild:["win_manager_award"]}]},o={data:function(){return{sales:{},failds:[]}},props:["dataFailds","data"],mounted:function(){this.failds=s[this.dataFailds]},methods:{goSkip:function(t){this.$router.push(t)},getSize:function(t){var e=Object(l["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0},getSizeText:function(t){var e=Object(l["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0?"下降":"上升"},getRate:function(t){var e=Object(l["a"])(t,2);e[0],e[1]},getSizeIcon:function(t){var e=Object(l["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0?"el-icon-caret-bottom":"el-icon-caret-top"}}},r=o,u=(a("ef00"),a("2877")),d=Object(u["a"])(r,i,n,!1,null,"69c89e7e",null);e["a"]=d.exports},ef00:function(t,e,a){"use strict";a("fa61")},ef83:function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"e",(function(){return l})),a.d(e,"d",(function(){return s})),a.d(e,"b",(function(){return o})),a.d(e,"a",(function(){return r}));var i=a("b775");function n(t){return Object(i["a"])({url:"/admin_plus/index/index ",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/admin/Analyse/salesAnalyse",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin/Analyse/getMemberAnalyse",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/admin/Analyse/commodityCategory.html",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/admin_plus/Analyse/commodityCategory",method:"get",params:t})}},fa61:function(t,e,a){}}]);