(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a6565896"],{2540:function(t,s,a){"use strict";a("e0f1")},"26fc":function(t,s,a){t.exports=a.p+"static/img/404_cloud.0f4bc32b.png"},a36b:function(t,s,a){t.exports=a.p+"static/img/404.a57b6f31.png"},e0f1:function(t,s,a){},fde5:function(t,s,a){"use strict";a.r(s);var i=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"wscn-http404-container"},[a("div",{staticClass:"wscn-http404"},[t._m(0),t._v(" "),a("div",{staticClass:"bullshit"},[a("div",{staticClass:"bullshit__oops"},[t._v("OOPS!")]),t._v(" "),t._m(1),t._v(" "),a("div",{staticClass:"bullshit__headline"},[t._v(t._s(t.message))]),t._v(" "),a("div",{staticClass:"bullshit__info"},[t._v("请检查您输入的网址是否正确，请点击以下按钮返回主页或者发送错误报告")]),t._v(" "),a("a",{staticClass:"bullshit__return-home",attrs:{href:""}},[t._v("返回首页")])])])])},c=[function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"pic-404"},[i("img",{staticClass:"pic-404__parent",attrs:{src:a("a36b"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child left",attrs:{src:a("26fc"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child mid",attrs:{src:a("26fc"),alt:"404"}}),t._v(" "),i("img",{staticClass:"pic-404__child right",attrs:{src:a("26fc"),alt:"404"}})])},function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"bullshit__info"},[t._v("版权所有\n        "),a("a",{staticClass:"link-type",attrs:{href:"https://wallstreetcn.com",target:"_blank"}},[t._v("华尔街见闻")])])}],e={name:"Page404",computed:{message:function(){return"网管说这个页面你不能进......"}}},l=e,n=(a("2540"),a("2877")),_=Object(n["a"])(l,i,c,!1,null,"6dc9a718",null);s["default"]=_.exports}}]);