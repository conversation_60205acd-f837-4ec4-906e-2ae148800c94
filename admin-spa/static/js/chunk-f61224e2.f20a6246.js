(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f61224e2"],{4929:function(e,s,t){"use strict";t("e814")},"507d":function(e,s,t){"use strict";t.r(s);var n=function(){var e=this,s=e.$createElement,t=e._self._c||s;return t("div",{staticClass:"app-container"},[t("div",[t("el-form",{attrs:{model:e.ruleForm,rules:e.rules,"label-width":"140px"}},[t("el-form-item",{attrs:{label:"所属标签组",prop:"tag_group_id"}},[t("el-select",{attrs:{disabled:e.isEdit,placeholder:"请选择"},model:{value:e.ruleForm.tag_group_id,callback:function(s){e.$set(e.ruleForm,"tag_group_id",s)},expression:"ruleForm.tag_group_id"}},e._l(e.tabsOptions,(function(e,s){return t("el-option",{key:s,attrs:{label:e.group_name,value:e.tag_group_id}})})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"标签",prop:"tag_name"}},[t("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:"请输入标签"},model:{value:e.ruleForm.tag_name,callback:function(s){e.$set(e.ruleForm,"tag_name",s)},expression:"ruleForm.tag_name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"是否与企微同步"}},[t("el-radio-group",{attrs:{disabled:e.isEdit},model:{value:e.ruleForm.is_sync_enterprise_wx,callback:function(s){e.$set(e.ruleForm,"is_sync_enterprise_wx",s)},expression:"ruleForm.is_sync_enterprise_wx"}},[t("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),t("el-radio",{attrs:{label:0}},[e._v("否")])],1),e._v(" "),t("div",{staticClass:"tips"},[e._v("\n                    请注意：该选项确定后将无法变更。如选择与企微同步，在创建标签后，该标签将同步至企业微信；任一用户被打上该标签时，系统也需要同步到企业微信对应的用户信息中；如选择不同步，则该标签仅在先迈端可见；\n                ")])],1),e._v(" "),t("el-form-item",{attrs:{label:"是否与SCRM同步"}},[t("el-radio-group",{attrs:{disabled:e.isEdit},model:{value:e.ruleForm.is_sync_scrm,callback:function(s){e.$set(e.ruleForm,"is_sync_scrm",s)},expression:"ruleForm.is_sync_scrm"}},[t("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),t("el-radio",{attrs:{label:0}},[e._v("否")])],1),e._v(" "),t("div",{staticClass:"tips"},[e._v("\n                    请注意：该选项确定后将无法变更。如选择与企微同步，在创建标签后，该标签将同步至scrm；任一用户被打上该标签时，系统也需要同步到scrm的用户信息中；如选择不同步，则该标签仅在先迈端可见；\n                ")])],1),e._v(" "),t("el-form-item",{attrs:{label:"自动打标签规则"}},[t("div",{staticClass:"switch"},[t("el-switch",{model:{value:e.autoForm.bool,callback:function(s){e.$set(e.autoForm,"bool",s)},expression:"autoForm.bool"}}),e._v("\n                    "+e._s(e.autoForm.bool?"已启用":"未启用")+"\n                ")],1),e._v(" "),e.autoForm.bool?t("conduct",{attrs:{"rule-data":e.autoGroup},model:{value:e.autoForm.form,callback:function(s){e.$set(e.autoForm,"form",s)},expression:"autoForm.form"}}):e._e()],1),e._v(" "),t("el-form-item",{attrs:{label:"自动取消标签规则"}},[t("div",{staticClass:"switch"},[t("el-switch",{model:{value:e.autoCancelForm.bool,callback:function(s){e.$set(e.autoCancelForm,"bool",s)},expression:"autoCancelForm.bool"}}),e._v("\n                    "+e._s(e.autoCancelForm.bool?"已启用":"未启用")+"\n                ")],1),e._v(" "),e.autoCancelForm.bool?t("conduct",{attrs:{"rule-data":e.autoCancelGroup},model:{value:e.autoCancelForm.form,callback:function(s){e.$set(e.autoCancelForm,"form",s)},expression:"autoCancelForm.form"}}):e._e()],1),e._v(" "),t("el-form-item",{attrs:{label:" "}},[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.onSave}},[e._v("保存")]),e._v(" "),t("el-button",{attrs:{plain:"",size:"small"}},[e._v("返回")])],1)],1)],1)])},a=[],o=t("c7eb"),c=(t("96cf"),t("1da1")),i=function(){var e=this,s=e.$createElement,t=e._self._c||s;return t("div",[t("div",{staticClass:"tips"},[e._v("1.当系统检测到用户达到下列勾选的全部规则后，自动为该用户打上标签。")]),e._v(" "),t("div",{staticClass:"tips"},[e._v("2.下列规则中“近X天”如设置为0，则表示仅统计当天数据。")]),e._v(" "),t("div",{staticClass:"tips"},[e._v("3.下列规则中关于“在A到B之间”的区间设置，均包含A和B")]),e._v(" "),t("span",{staticClass:"title"},[e._v(" 用户注册消费行为")]),e._v(" "),t("div",{staticClass:"conduct"},[t("div",[t("div",{staticClass:"checkbox",class:{active:e.conds.rule_key.indexOf("member_new")>=0},on:{click:function(s){return e.check("member_new")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("当前用户为")]),e._v(" "),t("el-select",{staticStyle:{width:"100px"},attrs:{size:"small"},model:{value:e.conds.member_new[0],callback:function(s){e.$set(e.conds.member_new,0,s)},expression:"conds.member_new[0]"}},[t("el-option",{attrs:{value:1,label:"新用户"}}),e._v(" "),t("el-option",{attrs:{value:2,label:"老用户"}})],1)],1),e._v(" "),t("div",[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_enterprise_sex")>=0},on:{click:function(s){return e.check("member_enterprise_sex")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("小程序注册用户性别信息为")]),e._v(" "),t("el-select",{staticStyle:{width:"100px"},attrs:{size:"small"},model:{value:e.conds.member_enterprise_sex[0],callback:function(s){e.$set(e.conds.member_enterprise_sex,0,s)},expression:"conds.member_enterprise_sex[0]"}},[t("el-option",{attrs:{value:1,label:"男"}}),e._v(" "),t("el-option",{attrs:{value:2,label:"女"}}),e._v(" "),t("el-option",{attrs:{value:0,label:"未知"}})],1)],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_register_days")>=0},on:{click:function(s){return e.check("member_register_days")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("用户当前注册天数在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_register_days[0],callback:function(s){e.$set(e.conds.member_register_days,0,s)},expression:"conds.member_register_days[0]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_register_days[1],callback:function(s){e.$set(e.conds.member_register_days,1,s)},expression:"conds.member_register_days[1]"}}),e._v(" "),t("span",[e._v("天之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_register_time")>=0},on:{click:function(s){return e.check("member_register_time")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("用户注册时间在")]),e._v(" "),t("el-date-picker",{attrs:{size:"small",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:function(s){return e.timeChange(s,"member_register_time")}},model:{value:e.conds.member_register_time,callback:function(s){e.$set(e.conds,"member_register_time",s)},expression:"conds.member_register_time"}})],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_consume_money")>=0},on:{click:function(s){return e.check("member_consume_money")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_consume_money[0],callback:function(s){e.$set(e.conds.member_consume_money,0,s)},expression:"conds.member_consume_money[0]"}}),e._v(" "),t("span",[e._v("天累计消费金额在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_consume_money[1],callback:function(s){e.$set(e.conds.member_consume_money,1,s)},expression:"conds.member_consume_money[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_consume_money[2],callback:function(s){e.$set(e.conds.member_consume_money,2,s)},expression:"conds.member_consume_money[2]"}}),e._v(" "),t("span",[e._v("元之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_pay_order_num")>=0},on:{click:function(s){return e.check("member_pay_order_num")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_pay_order_num[0],callback:function(s){e.$set(e.conds.member_pay_order_num,0,s)},expression:"conds.member_pay_order_num[0]"}}),e._v(" "),t("span",[e._v("天已支付订单数在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_pay_order_num[1],callback:function(s){e.$set(e.conds.member_pay_order_num,1,s)},expression:"conds.member_pay_order_num[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_pay_order_num[2],callback:function(s){e.$set(e.conds.member_pay_order_num,2,s)},expression:"conds.member_pay_order_num[2]"}}),e._v(" "),t("span",[e._v("笔之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_last_pay_time")>=0},on:{click:function(s){return e.check("member_last_pay_time")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("最近一次支付距今时间在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_last_pay_time[0],callback:function(s){e.$set(e.conds.member_last_pay_time,0,s)},expression:"conds.member_last_pay_time[0]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_last_pay_time[1],callback:function(s){e.$set(e.conds.member_last_pay_time,1,s)},expression:"conds.member_last_pay_time[1]"}}),e._v(" "),t("span",[e._v("天之间")])],1),e._v(" "),t("div",[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_belong_group")>=0},on:{click:function(s){return e.check("member_belong_group")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("用户所属组别")]),e._v(" "),t("el-select",{staticStyle:{width:"100px"},attrs:{size:"small"},model:{value:e.conds.member_belong_group[0],callback:function(s){e.$set(e.conds.member_belong_group,0,s)},expression:"conds.member_belong_group[0]"}},e._l(e.group,(function(e,s){return t("el-option",{key:s,attrs:{value:e.value,label:e.name}})})),1)],1),e._v(" "),t("div",{staticClass:"special"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_follow_enterprise_wechat")>=0},on:{click:function(s){return e.check("member_follow_enterprise_wechat")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("el-select",{staticStyle:{width:"100px"},attrs:{size:"small"},model:{value:e.conds.member_follow_enterprise_wechat[0],callback:function(s){e.$set(e.conds.member_follow_enterprise_wechat,0,s)},expression:"conds.member_follow_enterprise_wechat[0]"}},[t("el-option",{attrs:{value:1,label:"已关注"}}),e._v(" "),t("el-option",{attrs:{value:0,label:"未关注"}})],1),e._v(" "),t("span",[e._v("企业微信的小程序注册用户")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("member_no_order")>=0},on:{click:function(s){return e.check("member_no_order")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.member_no_order[0],callback:function(s){e.$set(e.conds.member_no_order,0,s)},expression:"conds.member_no_order[0]"}}),e._v(" "),t("span",[e._v("天用户没有新增订单记录（已支付）")])],1)]),e._v(" "),t("span",{staticClass:"title"},[e._v(" 店主分销及众包行为")]),e._v(" "),t("div",{staticClass:"conduct"},[t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("shop_consume_money")>=0},on:{click:function(s){return e.check("shop_consume_money")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_consume_money[0],callback:function(s){e.$set(e.conds.shop_consume_money,0,s)},expression:"conds.shop_consume_money[0]"}}),e._v(" "),t("span",[e._v("天店铺累计销售金额在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_consume_money[1],callback:function(s){e.$set(e.conds.shop_consume_money,1,s)},expression:"conds.shop_consume_money[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_consume_money[2],callback:function(s){e.$set(e.conds.shop_consume_money,2,s)},expression:"conds.shop_consume_money[2]"}}),e._v(" "),t("span",[e._v("元之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("shop_order_num")>=0},on:{click:function(s){return e.check("shop_order_num")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_order_num[0],callback:function(s){e.$set(e.conds.shop_order_num,0,s)},expression:"conds.shop_order_num[0]"}}),e._v(" "),t("span",[e._v("天店铺累计订单笔数在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_order_num[1],callback:function(s){e.$set(e.conds.shop_order_num,1,s)},expression:"conds.shop_order_num[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_order_num[2],callback:function(s){e.$set(e.conds.shop_order_num,2,s)},expression:"conds.shop_order_num[2]"}}),e._v(" "),t("span",[e._v("笔之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("shop_reward")>=0},on:{click:function(s){return e.check("shop_reward")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_reward[0],callback:function(s){e.$set(e.conds.shop_reward,0,s)},expression:"conds.shop_reward[0]"}}),e._v(" "),t("span",[e._v("天店铺累计佣金收益在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_reward[1],callback:function(s){e.$set(e.conds.shop_reward,1,s)},expression:"conds.shop_reward[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_reward[2],callback:function(s){e.$set(e.conds.shop_reward,2,s)},expression:"conds.shop_reward[2]"}}),e._v(" "),t("span",[e._v("元之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("shop_pay_member_num")>=0},on:{click:function(s){return e.check("shop_pay_member_num")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_pay_member_num[0],callback:function(s){e.$set(e.conds.shop_pay_member_num,0,s)},expression:"conds.shop_pay_member_num[0]"}}),e._v(" "),t("span",[e._v("天店铺下单用户数量在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_pay_member_num[1],callback:function(s){e.$set(e.conds.shop_pay_member_num,1,s)},expression:"conds.shop_pay_member_num[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_pay_member_num[2],callback:function(s){e.$set(e.conds.shop_pay_member_num,2,s)},expression:"conds.shop_pay_member_num[2]"}}),e._v(" "),t("span",[e._v("人之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("shop_no_order")>=0},on:{click:function(s){return e.check("shop_no_order")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.shop_no_order[0],callback:function(s){e.$set(e.conds.shop_no_order,0,s)},expression:"conds.shop_no_order[0]"}}),e._v(" "),t("span",[e._v("天店铺没有新增订单记录（已支付）")])],1)]),e._v(" "),t("span",{staticClass:"title"},[e._v(" 加盟行为")]),e._v(" "),t("div",{staticClass:"conduct"},[t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_join_num")>=0},on:{click:function(s){return e.check("league_join_num")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_join_num[0],callback:function(s){e.$set(e.conds.league_join_num,0,s)},expression:"conds.league_join_num[0]"}}),e._v(" "),t("span",[e._v("天新增加盟笔数在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_join_num[1],callback:function(s){e.$set(e.conds.league_join_num,1,s)},expression:"conds.league_join_num[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_join_num[2],callback:function(s){e.$set(e.conds.league_join_num,2,s)},expression:"conds.league_join_num[2]"}}),e._v(" "),t("span",[e._v("笔之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_initial_fee")>=0},on:{click:function(s){return e.check("league_initial_fee")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_initial_fee[0],callback:function(s){e.$set(e.conds.league_initial_fee,0,s)},expression:"conds.league_initial_fee[0]"}}),e._v(" "),t("span",[e._v("天当前用户加盟保证金总额在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_initial_fee[1],callback:function(s){e.$set(e.conds.league_initial_fee,1,s)},expression:"conds.league_initial_fee[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_initial_fee[2],callback:function(s){e.$set(e.conds.league_initial_fee,2,s)},expression:"conds.league_initial_fee[2]"}}),e._v(" "),t("span",[e._v("元之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_first_join_days")>=0},on:{click:function(s){return e.check("league_first_join_days")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("当前距离用户首次加盟时间在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_first_join_days[0],callback:function(s){e.$set(e.conds.league_first_join_days,0,s)},expression:"conds.league_first_join_days[0]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_first_join_days[1],callback:function(s){e.$set(e.conds.league_first_join_days,1,s)},expression:"conds.league_first_join_days[1]"}}),e._v(" "),t("span",[e._v("天之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_invite_num")>=0},on:{click:function(s){return e.check("league_invite_num")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_invite_num[0],callback:function(s){e.$set(e.conds.league_invite_num,0,s)},expression:"conds.league_invite_num[0]"}}),e._v(" "),t("span",[e._v("天成功推荐加盟的人数在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_invite_num[1],callback:function(s){e.$set(e.conds.league_invite_num,1,s)},expression:"conds.league_invite_num[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_invite_num[2],callback:function(s){e.$set(e.conds.league_invite_num,2,s)},expression:"conds.league_invite_num[2]"}}),e._v(" "),t("span",[e._v("人之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_quit_times")>=0},on:{click:function(s){return e.check("league_quit_times")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_times[0],callback:function(s){e.$set(e.conds.league_quit_times,0,s)},expression:"conds.league_quit_times[0]"}}),e._v(" "),t("span",[e._v("天新增的提前退出记录在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_times[1],callback:function(s){e.$set(e.conds.league_quit_times,1,s)},expression:"conds.league_quit_times[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_times[2],callback:function(s){e.$set(e.conds.league_quit_times,2,s)},expression:"conds.league_quit_times[2]"}}),e._v(" "),t("span",[e._v("次之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_quit_prices")>=0},on:{click:function(s){return e.check("league_quit_prices")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_prices[0],callback:function(s){e.$set(e.conds.league_quit_prices,0,s)},expression:"conds.league_quit_prices[0]"}}),e._v(" "),t("span",[e._v("天新增的提前退出金额在")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_prices[1],callback:function(s){e.$set(e.conds.league_quit_prices,1,s)},expression:"conds.league_quit_prices[1]"}}),e._v(" "),t("span",[e._v("到")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_quit_prices[2],callback:function(s){e.$set(e.conds.league_quit_prices,2,s)},expression:"conds.league_quit_prices[2]"}}),e._v(" "),t("span",[e._v("元之间")])],1),e._v(" "),t("div",{staticClass:"input"},[t("div",{staticClass:" checkbox",class:{active:e.conds.rule_key.indexOf("league_no_new")>=0},on:{click:function(s){return e.check("league_no_new")}}},[t("i",{staticClass:"el-icon-check"})]),e._v(" "),t("span",[e._v("近")]),e._v(" "),t("el-input",{attrs:{type:"number",size:"small"},model:{value:e.conds.league_no_new[0],callback:function(s){e.$set(e.conds.league_no_new,0,s)},expression:"conds.league_no_new[0]"}}),e._v(" "),t("span",[e._v("天没有新增加盟成功记录")])],1)])])},l=[],r=(t("7514"),{props:["ruleData"],data:function(){return{times:[],group:[],conds:{rule_key:[],member_new:[1],member_enterprise_sex:[1],member_register_days:[0,0],member_register_time:[],member_consume_money:[0,0,0],member_pay_order_num:[0,0,0],member_last_pay_time:[0,0],member_belong_group:[1],member_follow_enterprise_wechat:[1],member_no_order:[0],shop_consume_money:[0,0,0],shop_order_num:[0,0,0],shop_reward:[0,0,0],shop_pay_member_num:[0,0,0],shop_no_order:[0],league_join_num:[0,0,0],league_initial_fee:[0,0,0],league_first_join_days:[0,0],league_invite_num:[0,0,0],league_quit_times:[0,0,0],league_quit_prices:[0,0,0],league_no_new:[0]},firstLoad:!0}},created:function(){this.getGroupList(),this.parsePaddingData()},mounted:function(){this.onChange()},methods:{getSelectDict:function(e,s){for(var t=JSON.parse(this.ruleData[e].rules[s].rule_value)[0].content,n="",a=0;a<t.length;a++)t[a].selected&&(n=t[a].value);return{select_list:t,selected_value:n}},getGroupList:function(){var e=this.getSelectDict(0,7),s=e.select_list;this.group=s},parsePaddingData:function(){var e=this;this.ruleData.map((function(s){s.rules.map((function(s){s.checked&&e.conds.rule_key.push(s.key);var t=JSON.parse(s.rule_value);t.map((function(t,n){if("select"==t.type){var a=t.content.find((function(e){return e.selected}));a&&(e.conds[s.key][n]=a.value)}else("input"==t.type||"datetime"==t.type)&&(e.conds[s.key][n]=t.content)}))}))}))},timeChange:function(e,s){this.conds.member_register_time=[this.$format(e[0]),this.$format(e[1])],this.conds.rule_key.indexOf(s)>=0&&this.onChange()},onChange:function(){this.$emit("input",this.conds)},check:function(e){var s=this.conds.rule_key.indexOf(e);s>=0?this.conds.rule_key.splice(s,1):(this.conds.rule_key.push(e),this.firstLoad&&(this.$emit("input",this.conds),this.firstLoad=!1))}}}),_=r,u=(t("b436"),t("2877")),m=Object(u["a"])(_,i,l,!1,null,"2f96d00d",null),d=m.exports,p=t("9481"),v={components:{conduct:d},data:function(){return{tag_id:null,ruleForm:{rule_type:["auto","auto_cancel"],is_sync_enterprise_wx:0,is_sync_scrm:0},tabsOptions:[],autoForm:{bool:!1,form:{},prefix:"auto"},autoCancelForm:{bool:!1,form:{},prefix:"auto_cancel"},isEdit:!1,rules:{tag_group_id:[{required:!0,message:"请选择所属标签组",trigger:"change"}],tag_name:[{required:!0,message:"请输入标签名称",trigger:"blur"}]},autoGroup:[],autoCancelGroup:[]}},created:function(){var e=Object(c["a"])(Object(o["a"])().mark((function e(){var s;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.tag_id=this.$route.query.tag_id||null,e.prev=1,e.next=4,Object(p["j"])({tag_id:this.tag_id});case 4:s=e.sent,0==s.code&&(this.autoGroup=s.data.auto,this.autoCancelGroup=s.data.auto_cancel),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,this,[[1,8]])})));function s(){return e.apply(this,arguments)}return s}(),mounted:function(){var e=Object(c["a"])(Object(o["a"])().mark((function e(){var s,t,n,a=this;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Object(p["i"])({page_size:1e4}).then((function(e){var s=e.data.list;a.tabsOptions=s})),s=this.$route.query,s.belongGroup,t=s.tag_id,s.is_sync_enterprise_wx,s.is_sync_scrm,s.tag_group_id,s.tag_name,s.auto_cancel_tag,s.auto_tag,!t){e.next=13;break}return this.isEdit=!0,e.prev=4,e.next=7,Object(p["c"])({tag_id:t});case 7:n=e.sent,0==n.code&&(this.autoCancelForm.bool=!!n.data.tag_info.auto_cancel_tag,this.autoForm.bool=!!n.data.tag_info.auto_tag,this.ruleForm={is_sync_enterprise_wx:n.data.tag_info.is_sync_enterprise_wx,is_sync_scrm:n.data.tag_info.is_sync_scrm,tag_group_id:n.data.tag_info.tag_group_id,tag_name:n.data.tag_info.tag_name,tag_id:n.data.tag_info.tag_id,rule_type:["auto","auto_cancel"]}),e.next=13;break;case 11:e.prev=11,e.t0=e["catch"](4);case 13:case"end":return e.stop()}}),e,this,[[4,11]])})));function s(){return e.apply(this,arguments)}return s}(),methods:{onSave:function(){var e=this;this.autoForm.bool&&(this.ruleForm.auto_rule_key=this.autoForm.form.rule_key.map((function(s){return e.ruleForm["auto_".concat(s)]=e.autoForm.form[s],s})),this.ruleForm.autoRule="auto"),this.autoCancelForm.bool&&(this.ruleForm.auto_cancel_rule_key=this.autoCancelForm.form.rule_key.map((function(s){return e.autoCancelForm.bool&&(e.ruleForm["auto_cancel_".concat(s)]=e.autoCancelForm.form[s]),s})),this.ruleForm.autoCancelRule="auto_cancel"),this.tag_id&&(this.ruleForm.tag_id=this.tag_id),Object(p["k"])(this.ruleForm).then((function(s){0==s.code?(e.$message({type:"success",message:"操作标签规则成功"}),setTimeout((function(){e.$router.push("/member/tagManage/tagValue")}),1e3)):e.$message.error(s.message)}))}}},b=v,h=(t("4929"),Object(u["a"])(b,n,a,!1,null,"45901678",null));s["default"]=h.exports},9481:function(e,s,t){"use strict";t.d(s,"i",(function(){return a})),t.d(s,"a",(function(){return o})),t.d(s,"g",(function(){return c})),t.d(s,"e",(function(){return i})),t.d(s,"d",(function(){return l})),t.d(s,"l",(function(){return r})),t.d(s,"f",(function(){return _})),t.d(s,"h",(function(){return u})),t.d(s,"j",(function(){return m})),t.d(s,"k",(function(){return d})),t.d(s,"c",(function(){return p})),t.d(s,"b",(function(){return v}));var n=t("b775");function a(e){return Object(n["a"])({url:"/admin/MemberTags/groupList.html",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/admin/memberTags/addTagGroup.html",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/admin/memberTags/editTagGroup.html",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/admin/memberTags/delTagGroup.html",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/admin/MemberTags/dataList.html",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/admin/memberTags/delTag",method:"post",data:e})}function _(e){return Object(n["a"])({url:"/admin/MemberTags/editSort.html",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/admin/MemberTags/executeRule.html",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/admin/memberTags/tagRulesList.html",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/admin/memberTags/tagValueAdd.html",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/admin_plus/memberTags/tagValueAdd",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/admin_plus/Member/tags",method:"get",params:e})}},a950:function(e,s,t){},b436:function(e,s,t){"use strict";t("a950")},e814:function(e,s,t){}}]);