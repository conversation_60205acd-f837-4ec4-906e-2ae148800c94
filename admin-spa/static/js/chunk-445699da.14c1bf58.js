(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-445699da"],{1155:function(t,e,a){},2892:function(t,e,a){"use strict";a("b778")},"2e70":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"radio"},[t._v("\n    统计时间范围：\n    "),a("el-radio-group",{on:{input:t.onInput},model:{value:t.statisTime,callback:function(e){t.statisTime=e},expression:"statisTime"}},t._l(t.statis,(function(e,i){return a("el-radio",{key:i,attrs:{label:e.code}},[t._v(t._s(e.label))])})),1),t._v(" "),2==t.statisTime?a("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.onChange},model:{value:t.radioTime,callback:function(e){t.radioTime=e},expression:"radioTime"}}):t._e(),t._v(" "),a("i",{staticClass:"el-icon-refresh-right",class:{active:t.fullLoading},attrs:{title:"刷新"},on:{click:t.startLoad}})],1)},n=[],s={data:function(){return{statis:[{code:0,label:"昨日数据统计"},{code:1,label:"今日数据统计"},{code:2,label:"自定义时间段"}],statisTime:0,radioTime:[]}},props:["fullLoading"],mounted:function(){this.onInput(0)},methods:{onInput:function(t){if(t<2){var e=new Date;e.setDate(e.getDate()-1),console.log(this.$format(e,"{y}-{m}-{d}")),this.radioTime=["".concat(this.$format(e,"{y}-{m}-{d}")," 00:00:00"),"".concat(this.$format(e,"{y}-{m}-{d}")," 23:59:59")]}this.$emit("radioResult",this.radioTime)},onChange:function(t){console.log(t),this.$emit("radioResult",[this.$format(t[0]),this.$format(t[1])])},startLoad:function(){this.$emit("refresh")}}},l=s,r=(a("6290"),a("2877")),o=Object(r["a"])(l,i,n,!1,null,"7cd3a70d",null);e["a"]=o.exports},6290:function(t,e,a){"use strict";a("7fdb")},"6adb":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"app-container"},[a("radio",{attrs:{fullLoading:t.fullscreenLoading},on:{radioResult:t.radioResult,refresh:t.startLoad}}),t._v(" "),a("div",{staticClass:"analy-card"},[a("span",[t._v("销售数据")]),t._v(" "),a("sales-data",{attrs:{data:t.saData,dataFailds:"sales"}})],1),t._v(" "),a("div",{staticClass:"analy-card charts"},[a("span",[t._v("\n                每日订单动态\n")]),t._v(" "),a("div",[t.fullscreenLoading?t._e():a("chart-line",{attrs:{showYname:"true",legend:"true",xAxis:t.orderXAxis,yAxis:t.orderYAxis}})],1)]),t._v(" "),a("div",{staticClass:"analy-card charts"},[a("span",[t._v("\n                新加购物车\n")]),t._v(" "),a("div",[t.fullscreenLoading?t._e():a("chart-line",{attrs:{xAxis:t.cartXAxis,yAxis:t.cartYAxis}})],1)])],1)},n=[],s=a("9a1c"),l=a("96f1"),r=a("2e70"),o=a("ef83"),d={name:"Sales",components:{salesData:s["a"],chartLine:l["a"],radio:r["a"]},data:function(){return{fullscreenLoading:!1,saData:{},cartXAxis:[],cartYAxis:[],orderXAxis:[],orderYAxis:[],times:[]}},mounted:function(){},methods:{radioResult:function(t){var e=this;this.fullscreenLoading=!0,Object(o["e"])({start_time:t[0],end_time:t[1]}).then((function(a){var i=a.data,n=i.add_cart_nums,s=i.order_dynamic,l=i.sale_data;e.cartXAxis=n.date,e.cartYAxis=[{"订单数":n.data}],e.orderXAxis=s.date,e.orderYAxis=[{"订单数":s.data},{"销售金额":s.sale_data}],e.saData=l,e.fullscreenLoading=!1,e.times=t}))},startLoad:function(){this.radioResult(this.times)}}},u=d,c=(a("6d20"),a("2877")),f=Object(c["a"])(u,i,n,!1,null,"20d0748b",null);e["default"]=f.exports},"6d20":function(t,e,a){"use strict";a("1155")},"7fdb":function(t,e,a){},"96f1":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{ref:"line",staticClass:"line"})])},n=[],s=(a("ac6a"),a("a481"),a("6b54"),a("313e")),l=a.n(s),r={props:["e_id","xAxis","yAxis","legend","showYname"],mounted:function(){var t=this;this.createCharts(),window.addEventListener("resize",(function(){t.resetDraw()}))},data:function(){return{_chart:"",showLine:!1,_options:{}}},methods:{createCharts:function(){var t=this,e=[],a={color:["#5470C6","#EE6666"],tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:this.xAxis},yAxis:[],series:[]};function i(t){var e=t.toString();return parseInt(e.substr(0,1))+1+e.slice(1).replace(/\d/gi,"0")}if(this.yAxis.forEach((function(n,s){for(var l in n){var r={type:"line",data:n[l],name:l,smooth:!0,emphasis:{focus:"series"}};1==s&&(r.yAxisIndex=1,r.areaStyle={color:"rgb(255 95 95 / 30%)"}),0==s&&(r.areaStyle={color:"rgb(24 144 255 / 30%)"}),a.series.push(r),e.push(l);var o=i(Math.max.apply(null,n[l])),d={type:"value",max:o,interval:o/5};t.showYname&&(d.name=l),a.yAxis.push(d)}})),a.series.length){console.log(a.series,a.yAxis);var n=this.$refs.line;this._chart=l.a.init(n),this.legend&&(a.legend={data:e}),this._chart.setOption(a)}},resetDraw:function(){this._chart.resize()}}},o=r,d=(a("2892"),a("2877")),u=Object(d["a"])(o,i,n,!1,null,"7e7a88d4",null);e["a"]=u.exports},"9a1c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sales-data",class:{active:t.failds.length>6}},t._l(t.failds,(function(e,i){return a("div",{key:i,style:{width:"calc(100% / "+(t.failds.length>12?parseInt(t.failds.length/2):6)+")"}},[a("div",[t._v("\n            "+t._s(e.title)+"\n            "),e.subTitle?a("el-tooltip",{attrs:{content:e.subTitle,placement:"top-start"}},[a("i",{staticClass:"el-icon-question"})]):t._e(),t._v(" "),e.path?a("el-button",{attrs:{size:"mini",type:"danger",plain:""},on:{click:function(a){return t.goSkip(e.path)}}},[t._v("去看看")]):t._e()],1),t._v(" "),a("i",[t._v(t._s(t.data[e.faild[0]]))]),t._v(" "),1==e.type?a("span",{class:{active:t.getSize(e.faild)}},[t._v("\n            "+t._s(t.getSizeText(e.faild))+"\n            "),a("i",{class:t.getSizeIcon(e.faild)}),t._v(" "),a("span",[t._v(t._s(t.data[e.faild[1]])+"%")])]):t._e(),t._v(" "),2==e.type?a("span",[t._v("\n            活跃会员"),a("span",[t._v(t._s(t.data[e.faild[1]]))]),t._v("人\n        ")]):t._e(),t._v(" "),3==e.type?a("span",[t._v("\n            新增店铺"),a("span",[t._v("+"+t._s(t.data[e.faild[1]]))])]):t._e()])})),0)},n=[],s=a("3835"),l={sales:[{title:"销售总额（元）",subTitle:"平台生产全部订单的总金额，包含未支付订单金额",faild:["order_sale_amount","order_sale_amount_float_pro"],type:1},{title:"订单量",subTitle:"包含未支付订单数量",faild:["order_nums","order_nums_float_pro"],type:1},{title:"下单人数",subTitle:"包含未支付订单下单人数",faild:["persons_num","persons_num_float_pro"],type:1},{title:"支付总额（元）",subTitle:"已支付订单总金额",faild:["order_pay_amount","order_pay_amount_float_pro"],type:1},{title:"退款总额（元）",faild:["refund_amount","refund_amount_float_pro"],type:1},{title:"转化率",subTitle:"转化率＝订单量÷UV",faild:["change","change_float_pro"],type:1}],users:[{title:"会员总数",faild:["member_nums","member_nums_float_pro"],type:2},{title:"新增会员数",faild:["add_member_nums","add_member_nums_float_pro"],type:1},{title:"店铺总数",faild:["shop_nums","shop_nums_float_pro"],type:3},{title:"访客数（UV）",faild:["uv","uv_float_pro"],type:1},{title:"访问量（PV）",subTitle:"全部小程及H5序页面访问次数",faild:["pv","pv_float_pro"],type:1},{title:"客单价",subTitle:"客单价=销售额÷成交顾客数",faild:["unit_price","unit_price_float_pro"],type:1}],commodity:[{title:"商品总数",faild:["spu_total"]},{title:"上架商品数",subTitle:"全部已上架状态商品数量（含供应链同步商品）",faild:["spu_online"]},{title:"供应链选品上架",faild:["online"]},{title:"供应链异常下架",faild:["offline"],path:"/goods/quality/warning"},{title:"合作品牌数",subTitle:"已上架商品涉及的品牌数量",faild:["brand_num"]},{title:"合作供应商",subTitle:"已上架商品涉及的供应商数量",faild:["supplier_num"]}],teamwork:[{title:"开团人次",faild:["group_nums"]},{title:"成团人次",faild:["win_group_nums"]},{title:"开团人数",subTitle:"即该活动开团的用户数量，单个用户多次开团算一个用户",faild:["group_people_nums"]},{title:"参团人次",faild:["join_group_nums"]},{title:"参团人数",subTitle:"即该活动中参加别人发起拼团的人数，单个用户多次参团算一个用户",faild:["join_group_people_nums"]},{title:"活动拉新人数",subTitle:"该活动中首次参团，此前无任何活动参团记录，无普通商品下单记录的新用户",faild:["invite_new_nums"]},{title:"拼团支付金额",subTitle:"该活动中开团或参团支付的所有金额（含未中奖退款）",faild:["pay_money"]},{title:"成团率",subTitle:"成团人次/开团人次*100%",faild:["join_group_pro"]},{title:"中奖商品订单数",subTitle:"拼团活动中奖者发放商品，系统会为每个中奖者生成一个商品订单",faild:["win_order_nums"]},{title:"商品订单总金额",subTitle:"拼团中奖者的参团金额不退回，作为购买商品的交易金额。",faild:["win_order_money"]},{title:"分享次数",subTitle:"用户点击拼团分享按钮的次数（含拼团商品详情页、邀请参团页）",faild:["share_nums"]},{title:"分享人数",subTitle:"点击拼团分享按钮的人数（含拼团商品详情页、邀请参团页）",faild:["share_people_nums"]},{title:"打开分享次数",subTitle:"分享拼团链接被打开的次数（含拼团商品详情页、邀请参团页）",faild:["open_share_nums"]},{title:"打开分享人数",subTitle:"打开分享拼团链接的人数（含拼团商品详情页、邀请参团页）",faild:["open_share_people_nums"]}],reward:[{title:"中奖奖励",faild:["pintuan_winning_award"]},{title:"未中奖奖励",faild:["pintuan_not_winning_award"]},{title:"邀约成团奖励",faild:["pintuan_invite_award"]},{title:"邀请新人参团奖励",faild:["pintuan_invite_new_member_award"]},{title:"开团人奖励",faild:["head_award"]},{title:"开团人所属店主奖励",faild:["pintuan_award"]},{title:"开团人所属经理奖励",faild:["head_group_award"]},{title:"中奖人所属店主奖励",faild:["win_shop_award"]},{title:"中奖人所属经理奖励",faild:["win_manager_award"]}]},r={data:function(){return{sales:{},failds:[]}},props:["dataFailds","data"],mounted:function(){this.failds=l[this.dataFailds]},methods:{goSkip:function(t){this.$router.push(t)},getSize:function(t){var e=Object(s["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0},getSizeText:function(t){var e=Object(s["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0?"下降":"上升"},getRate:function(t){var e=Object(s["a"])(t,2);e[0],e[1]},getSizeIcon:function(t){var e=Object(s["a"])(t,2),a=(e[0],e[1]);return this.data[a]<0?"el-icon-caret-bottom":"el-icon-caret-top"}}},o=r,d=(a("ef00"),a("2877")),u=Object(d["a"])(o,i,n,!1,null,"69c89e7e",null);e["a"]=u.exports},b778:function(t,e,a){},ef00:function(t,e,a){"use strict";a("fa61")},ef83:function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return l})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return o}));var i=a("b775");function n(t){return Object(i["a"])({url:"/admin_plus/index/index ",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin/Analyse/salesAnalyse",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/admin/Analyse/getMemberAnalyse",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/admin/Analyse/commodityCategory.html",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/admin_plus/Analyse/commodityCategory",method:"get",params:t})}},fa61:function(t,e,a){}}]);