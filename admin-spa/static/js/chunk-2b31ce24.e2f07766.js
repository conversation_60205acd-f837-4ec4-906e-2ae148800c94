(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2b31ce24"],{"3f5e":function(e,l,t){"use strict";t.d(l,"b",(function(){return n})),t.d(l,"c",(function(){return u})),t.d(l,"a",(function(){return o}));var a=t("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,l,t){"use strict";var a=t("a18c"),n={inserted:function(e,l,t){var n=l.value,u=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;u.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},u=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(u)),n.install=u;l["a"]=n},6396:function(e,l,t){"use strict";t.d(l,"a",(function(){return o})),Math.easeInOutQuad=function(e,l,t,a){return e/=a/2,e<1?t/2*e*e+l:(e--,-t/2*(e*(e-2)-1)+l)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function u(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,l,t){var o=u(),r=e-o,i=20,s=0;l="undefined"===typeof l?500:l;var d=function e(){s+=i;var u=Math.easeInOutQuad(s,o,r,l);n(u),s<l?a(e):t&&"function"===typeof t&&t()};d()}},6724:function(e,l,t){"use strict";t("8d41");var a={bind:function(e,l){e.addEventListener("click",(function(t){var a=Object.assign({},l.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),u=n.ele;if(u){u.style.position="relative",u.style.overflow="hidden";var o=u.getBoundingClientRect(),r=u.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",u.appendChild(r)),n.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(t.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(t.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;l["a"]=a},"6b2c":function(e,l,t){"use strict";t.d(l,"a",(function(){return a})),t.d(l,"d",(function(){return n})),t.d(l,"c",(function(){return u})),t.d(l,"b",(function(){return o}));var a=[{type:"input",label:"会员Id",model:"member_id",placeholder:"请输入会员ID"},{type:"input",label:"手机号",model:"mobile",placeholder:"请输入手机号"},{type:"input",label:"当前锁定店铺",model:"shop_member_name",placeholder:"请输入当前锁定店铺"},{type:"select",label:"用户标签",model:"tag_ids",options:{name:"userTagsOptions"}},{type:"input",label:"注册推荐人",model:"parent_mobile",placeholder:"请输入注册推荐人"},{type:"select",label:"组别",model:"group_id",options:{name:"userGroupOptions"}},{type:"select",label:"状态",model:"state",options:{name:"memberStatusOpt"}},{type:"select",label:"企微匹配状态",model:"is_shopping_status",options:{name:"mateStatusOpt"}},{type:"select",label:"账号状态",model:"status",options:{name:"accountStatusOpt"}},{type:"time",label:"注册时间",model:"created_time"}],n=[{type:"input",label:"店主ID",model:"shop_id",placeholder:"请输入店主ID"},{type:"input",label:"店主名称",model:"search_text",placeholder:"请输入店主名称"},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"select",label:"店铺状态",model:"shop_status",placeholder:"请输入店铺状态",options:{name:"shopStatusOpts"}},{type:"select",label:"绑定状态",model:"bing_status",placeholder:"请输入绑定状态",options:{name:"bindStatusOpts"}},{type:"select",label:"店主等级",model:"vip_level_name",placeholder:"请输入店主等级",options:{name:"StoreOwnerLevelOpts"}},{type:"date",label:"付费时间",model:"vip_open_time"},{type:"date",label:"付费到期时间",model:"vip_expired_time"},{type:"time",label:"入驻时间",model:"create_time"},{type:"time",label:"到期时间",model:"expired_time"}],u=[{type:"input",label:"代理商名称",model:"enterprise_name",disabled:!0},{type:"select",label:"店铺导师",model:"mentor_id",placeholder:"请选择",options:{name:"mentorOpts"}},{type:"select",label:"店铺会长",model:"president_id",placeholder:"请选择",options:{name:"presidentOpts"}},{type:"input",label:"店主名称",model:"site_name",placeholder:"请输入2-12位中英文"},{type:"input",label:"店主等级",model:"vip_level_name",disabled:!0},{type:"input",label:"联系电话",model:"mobile",placeholder:"请输入联系电话"},{type:"input",label:"店主微信号",model:"ww",placeholder:"请输入店主微信号"},{type:"date",label:"到期时间",model:"expire_time",placeholder:"请输入到期时间",dateType:"datetime"},{type:"input",label:"店主账号",model:"username",placeholder:"请输入店主账号",disabled:!0},{type:"input",label:"修改登录密码",model:"password",placeholder:"请输入登录密码"},{type:"radio",label:"是否是会长",model:"is_president",options:{name:"persiRadio"}}],o=[{type:"time",timeType:"datetimerange",label:"下单时间",model:"create_time"},{type:"time",timeType:"datetimerange",label:"处理时间",model:"dispose_time"},{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"退款单号",model:"refund_no",placeholder:"请输入退款单号"},{type:"select",label:"订单状态",model:"order_status",options:{name:"orderStateOptions"}},{type:"select",label:"退款状态",model:"refund_status",options:{name:"refundStateOptions"}}]},"76f2":function(e,l,t){"use strict";t.r(l);var a=function(){var e=this,l=e.$createElement,t=e._self._c||l;return t("div",{staticClass:"app-container"},[e.showSearch?t("div",{staticClass:"filter-container"},[t("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(l){e.form=l},expression:"form"}}),e._v(" "),t("div",{staticClass:"flex-b-c buttons"},[t("el-button",{attrs:{type:"primary",loading:e.loading,icon:"el-icon-search",size:"small"},on:{click:function(l){return e.handleQuery()}}},[e._v("搜索")]),e._v(" "),t("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-list"},[t("div",{staticClass:"btns"},[t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:e.batchExport}},[e._v("批量导出")])],1),e._v(" "),t("o-table",{staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{toggleSearch:e.toggleSearch,"update:showSearch":function(l){e.showSearch=l},"update:show-search":function(l){e.showSearch=l},onSearch:e.handleQuery},scopedSlots:e._u([{key:"headimg",fn:function(e){var l=e.row;return[t("img",{staticClass:"headimg",attrs:{src:l.headimg}})]}},{key:"tags",fn:function(l){var a=l.row;return[a.tags.length>0?t("span",[e._v(e._s(a.tags.join(",")))]):t("span",[e._v("无任何标签")])]}},{key:"status",fn:function(l){var t=l.row;return[e._v("\n                "+e._s(e.getStatus(t.status))+"\n            ")]}},{key:"is_shopping_status",fn:function(l){var t=l.row;return[e._v("\n                "+e._s(e.geShoppingStatus(t.is_shopping_status))+"\n            ")]}},{key:"reg_time",fn:function(l){var t=l.row;return[e._v("\n                "+e._s(e._f("parseTime")(t.reg_time))+"\n            ")]}},{key:"action",fn:function(l){var a=l.row;return[t("el-button",{attrs:{type:"text"},on:{click:function(l){return e.details({member_id:a.member_id})}}},[e._v("详情")])]}}])})],1)])},n=[],u=t("5530"),o=t("15fd"),r=t("b885"),i=t("7991"),s=t("5315"),d=t("6b2c"),c=t("c71b"),b=["created_time"],p={name:"orderList",components:{FormQuery:r["d"]},data:function(){return{showSearch:!0,isApproval:!1,orderNo:"",options:{page:1,page_size:10,total:0},form:{is_shopping_status:"-1",status:"-1"},loading:!1,list:[],formopts:{refundStateOptions:c["F"],userTagsOptions:[],userGroupOptions:[],orderStateOptions:c["v"],memberStatusOpt:c["r"],accountStatusOpt:c["b"],mateStatusOpt:c["q"]},baseConfig:{labelWidth:"120px"},formConfig:d["a"],columns:i["a"]}},created:function(){this.getTableList()},methods:{toggleSearch:function(){this.showSearch=!this.showSearch},details:function(e){this.$router.push({path:"/member/memberList/details",query:e})},getStatus:function(e){return 0==e?"正常":1==e?"已注销":void 0},geShoppingStatus:function(e){return 1==e?"正常购物":"无"},batchExport:function(){var e=this,l=this.form,t=l.created_time,a=l.params;t&&(a.reg_time_start=t[0],a.reg_time_end=t[1]),Object(s["d"])(a).then((function(l){var t=new FileReader;t.readAsText(l),t.onload=function(){try{var t=JSON.parse(e.result);e.$message.error("导出失败，"+t.message)}catch(o){e.batchExportLoading=!1;var a=new Blob([l],{type:"application/vnd.ms-excel;charset=UTF-8"}),n=document.createElement("a"),u=window.URL.createObjectURL(a);n.href=u,n.download="用户列表".concat((new Date).getTime(),".xlsx"),n.click(),window.URL.revokeObjectURL(u)}}}))},getTableList:function(e){var l=this,t=this.form,a=t.created_time,n=Object(o["a"])(t,b);a&&(n.reg_time_start=a[0],n.reg_time_end=a[1]),Object(s["f"])(Object(u["a"])(Object(u["a"])({},e),n)).then((function(e){var t=e.data,a=t.count,n=t.list;l.options.total=a,l.list=n,l.loading=!1}))},handleQuery:function(e){this.loading=!0,this.list=[];var l=e||{page:1,page_size:this.options.page_size};this.getTableList(l)},handleReset:function(){},handleDetails:function(e){this.$router.push({path:"/order/exchangeGoodsDetails",query:{orderNo:e.orderNo}})},handleRefundApproval:function(e){this.orderNo=e.refund,this.row=e,this.isApproval=!0},handleExport:function(){}}},v=p,m=(t("eb27"),t("2877")),f=Object(m["a"])(v,a,n,!1,null,null,null);l["default"]=f.exports},"8d41":function(e,l,t){},b885:function(e,l,t){"use strict";var a=t("e780");t.d(l,"d",(function(){return a["a"]}));var n=t("ad41");t.d(l,"c",(function(){return n["a"]}));var u=t("0476");t.d(l,"g",(function(){return u["a"]}));var o=t("6eb0");t.d(l,"a",(function(){return o["a"]}));var r=t("c87f");t.d(l,"f",(function(){return r["a"]}));var i=t("333d");t.d(l,"e",(function(){return i["a"]}));var s=t("05be");t.d(l,"b",(function(){return s["a"]}));t("9040");var d=t("4381");t.d(l,"h",(function(){return d["a"]}));var c=t("6724");t.d(l,"i",(function(){return c["a"]}))},c40e:function(e,l,t){"use strict";t.d(l,"e",(function(){return n})),t.d(l,"d",(function(){return u})),t.d(l,"f",(function(){return o})),t.d(l,"c",(function(){return r})),t.d(l,"a",(function(){return i})),t.d(l,"g",(function(){return s})),t.d(l,"b",(function(){return d}));var a=t("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,l,t){"use strict";t.d(l,"a",(function(){return a})),t.d(l,"i",(function(){return n})),t.d(l,"H",(function(){return u})),t.d(l,"f",(function(){return o})),t.d(l,"A",(function(){return r})),t.d(l,"x",(function(){return i})),t.d(l,"e",(function(){return s})),t.d(l,"w",(function(){return d})),t.d(l,"c",(function(){return c})),t.d(l,"O",(function(){return b})),t.d(l,"j",(function(){return p})),t.d(l,"k",(function(){return v})),t.d(l,"l",(function(){return m})),t.d(l,"T",(function(){return f})),t.d(l,"d",(function(){return h})),t.d(l,"Q",(function(){return g})),t.d(l,"p",(function(){return _})),t.d(l,"P",(function(){return y})),t.d(l,"m",(function(){return w})),t.d(l,"I",(function(){return O})),t.d(l,"L",(function(){return S})),t.d(l,"N",(function(){return x})),t.d(l,"M",(function(){return T})),t.d(l,"S",(function(){return j})),t.d(l,"s",(function(){return k})),t.d(l,"B",(function(){return C})),t.d(l,"z",(function(){return L})),t.d(l,"K",(function(){return R})),t.d(l,"C",(function(){return N})),t.d(l,"h",(function(){return A})),t.d(l,"g",(function(){return E})),t.d(l,"o",(function(){return q})),t.d(l,"G",(function(){return I})),t.d(l,"J",(function(){return z})),t.d(l,"v",(function(){return D})),t.d(l,"F",(function(){return F})),t.d(l,"r",(function(){return Q})),t.d(l,"b",(function(){return M})),t.d(l,"q",(function(){return H})),t.d(l,"R",(function(){return U})),t.d(l,"u",(function(){return V})),t.d(l,"t",(function(){return W})),t.d(l,"D",(function(){return X})),t.d(l,"E",(function(){return B})),t.d(l,"y",(function(){return G})),t.d(l,"n",(function(){return J}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],o=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],r=[{label:"是",code:1},{label:"否",code:0}],i=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],d=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],b=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],v=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],f=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],_=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],S=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],x=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],T=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],j=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],k=[{value:"0",label:"参团"},{value:"1",label:"团长"}],C=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],L=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],R=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],N=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],A=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],E=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],q=[{value:"1",label:"是"},{value:"2",label:"否"}],I=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],z=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],D=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],F=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],Q=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],M=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],H=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],U=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],V=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],W=[{label:"按订单编号",value:"order_no"}],X=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],B=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],G=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],J=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},eb27:function(e,l,t){"use strict";t("f82a")},f82a:function(e,l,t){},fe67:function(e,l,t){e.exports=t.p+"static/img/login_bg.e491666c.png"}}]);