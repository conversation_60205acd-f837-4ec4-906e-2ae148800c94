(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34a9758c"],{3870:function(e,l,a){"use strict";a.r(l);var t=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("div",{staticClass:"app-container"},[e.showSearch?a("div",{staticClass:"filter-container"},[a("formQuery",{staticClass:"mb-20",attrs:{baseConfig:e.baseConfig,config:e.formConfig,options:e.formopts},model:{value:e.form,callback:function(l){e.form=l},expression:"form"}}),e._v(" "),a("div",{staticClass:"flex-b-c buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),e._v(" "),a("o-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:e.showSearch,options:e.options,columns:e.columns,data:e.list},on:{toggleSearch:e.toggleSearch,"update:showSearch":function(l){e.showSearch=l},"update:show-search":function(l){e.showSearch=l},onSearch:e.getTableList},scopedSlots:e._u([{key:"refund_action_time",fn:function(l){var a=l.row;return[e._v("\n            "+e._s(e._f("parseTime")(a.refund_action_time))+"\n        ")]}},{key:"action_action_time",fn:function(l){var a=l.row;return[e._v("\n            "+e._s(e._f("parseTime")(a.action_action_time))+"\n        ")]}},{key:"action",fn:function(l){var t=l.row,n=t.order_goods_id,u=t.refund_status;return[a("el-button",{class:{danger:e.getStatus(u)},attrs:{type:"text"},on:{click:function(l){return e.goSkip("/order/exchangeGoodsDetails",{order_goods_id:n})}}},[e._v("\n                "+e._s(e.getStatus(u)?"去处理":"查看")+"\n            ")])]}}])})],1)},n=[],u=a("5530"),r=a("b885"),o=a("c57b"),i=(a("d2e6"),a("c71b")),d=a("ed08"),b=a("e585"),c={components:{FormQuery:r["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{orderStatusOpts:i["x"],afterSalesOpts:i["e"]},form:{},formConfig:[{type:"input",label:"订单号",model:"order_no",placeholder:"请输入订单号"},{type:"input",label:"换货单号",model:"refund_no",placeholder:"请输入退款单号"},{type:"select",label:"订单状态",model:"shop_status",placeholder:"请选择",options:{name:"orderStatusOpts"}},{type:"select",label:"售后状态",model:"bing_status",placeholder:"请选择",options:{name:"afterSalesOpts"}},{type:"time",label:"下单时间",model:"order_time"},{type:"time",label:"处理时间",model:"handle_time"}],loading:!1,columns:o["c"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){this.getTableList()},methods:{getStatus:function(e){return 6==e||1==e},goSkip:function(e,l){this.$router.push({path:e,query:l})},handleQuery:function(){this.options={page:1,page_size:10,total:0},this.getTableList()},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(e){var l=this;this.loading=!0,this.list=[],e&&e&&(this.options.page=e.page,this.options.page_size=e.page_size);var a=Object(u["a"])(Object(u["a"])(Object(u["a"])({},this.options),this.form),{},{start_time:this.form.order_time?Object(d["d"])(this.form.order_time[0]):"",end_time:this.form.order_time?Object(d["d"])(this.form.order_time[1]):"",start_handle_time:this.form.handle_time?Object(d["d"])(this.form.handle_time[0]):"",end_handle_time:this.form.handle_time?Object(d["d"])(this.form.handle_time[1]):""});Object(b["e"])(a).then((function(e){0==e.code&&(l.options.total=e.data.count,l.list=e.data.list,l.loading=!1)}))}}},s=c,v=a("2877"),f=Object(v["a"])(s,t,n,!1,null,"f87a4586",null);l["default"]=f.exports},c71b:function(e,l,a){"use strict";a.d(l,"a",(function(){return t})),a.d(l,"i",(function(){return n})),a.d(l,"H",(function(){return u})),a.d(l,"f",(function(){return r})),a.d(l,"A",(function(){return o})),a.d(l,"x",(function(){return i})),a.d(l,"e",(function(){return d})),a.d(l,"w",(function(){return b})),a.d(l,"c",(function(){return c})),a.d(l,"O",(function(){return s})),a.d(l,"j",(function(){return v})),a.d(l,"k",(function(){return f})),a.d(l,"l",(function(){return m})),a.d(l,"T",(function(){return h})),a.d(l,"d",(function(){return p})),a.d(l,"Q",(function(){return _})),a.d(l,"p",(function(){return g})),a.d(l,"P",(function(){return O})),a.d(l,"m",(function(){return j})),a.d(l,"I",(function(){return y})),a.d(l,"L",(function(){return S})),a.d(l,"N",(function(){return w})),a.d(l,"M",(function(){return k})),a.d(l,"S",(function(){return C})),a.d(l,"s",(function(){return x})),a.d(l,"B",(function(){return R})),a.d(l,"z",(function(){return L})),a.d(l,"K",(function(){return z})),a.d(l,"C",(function(){return D})),a.d(l,"h",(function(){return T})),a.d(l,"g",(function(){return A})),a.d(l,"o",(function(){return P})),a.d(l,"G",(function(){return Q})),a.d(l,"J",(function(){return E})),a.d(l,"v",(function(){return G})),a.d(l,"F",(function(){return I})),a.d(l,"r",(function(){return q})),a.d(l,"b",(function(){return J})),a.d(l,"q",(function(){return N})),a.d(l,"R",(function(){return B})),a.d(l,"u",(function(){return F})),a.d(l,"t",(function(){return M})),a.d(l,"D",(function(){return $})),a.d(l,"E",(function(){return H})),a.d(l,"y",(function(){return K})),a.d(l,"n",(function(){return V}));var t=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],u=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],r=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],i=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],d=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],b=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],s=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],v=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],h=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],p=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],O=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],j=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],y=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],S=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],w=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],k=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],C=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],x=[{value:"0",label:"参团"},{value:"1",label:"团长"}],R=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],L=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],z=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],D=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],T=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],A=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],P=[{value:"1",label:"是"},{value:"2",label:"否"}],Q=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],E=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],G=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],I=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],q=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],J=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],N=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],B=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],F=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],M=[{label:"按订单编号",value:"order_no"}],$=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],H=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],K=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],V=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},e585:function(e,l,a){"use strict";a.d(l,"m",(function(){return n})),a.d(l,"g",(function(){return u})),a.d(l,"n",(function(){return r})),a.d(l,"p",(function(){return o})),a.d(l,"o",(function(){return i})),a.d(l,"a",(function(){return d})),a.d(l,"c",(function(){return b})),a.d(l,"k",(function(){return c})),a.d(l,"b",(function(){return s})),a.d(l,"h",(function(){return v})),a.d(l,"i",(function(){return f})),a.d(l,"j",(function(){return m})),a.d(l,"f",(function(){return h})),a.d(l,"q",(function(){return p})),a.d(l,"e",(function(){return _})),a.d(l,"d",(function(){return g})),a.d(l,"l",(function(){return O}));var t=a("b775");function n(e){return Object(t["a"])({url:"/admin/refund/refundList",method:"get",params:e})}function u(e){return Object(t["a"])({url:"/admin/refund/getOrderChainStatus",method:"post",data:e})}function r(){return Object(t["a"])({url:"/admin/refund/refundReview",method:"post",data:data})}function o(e){return Object(t["a"])({url:"/admin/refund/returnRefundList",method:"get",params:e})}function i(e){return Object(t["a"])({url:"/admin_plus/refund/returnRefundDetail",method:"post",data:e})}function d(e){return Object(t["a"])({url:"/admin/refund/agreeReturnMoney.html",method:"post",data:e})}function b(e){return Object(t["a"])({url:"/admin/refund/checkSupplyChainOrder.html",method:"post",data:e})}function c(e){return Object(t["a"])({url:"/admin/Express/logistics.html",method:"post",data:e})}function s(e){return Object(t["a"])({url:"/admin/refund/agreeReturnRefundReview.html",method:"post",data:e})}function v(e){return Object(t["a"])({url:"/admin/address/getProvince.html",method:"post",data:e})}function f(e){return Object(t["a"])({url:"/admin/address/getcity.html",method:"post",data:e})}function m(e){return Object(t["a"])({url:"/admin/address/getdistrict.html",method:"post",data:e})}function h(e){return Object(t["a"])({url:"/admin/express/expressCompany.html",method:"post",data:e})}function p(e){return Object(t["a"])({url:"/admin/refund/sendAgain.html",method:"post",data:e})}function _(e){return Object(t["a"])({url:"/admin/refund/exchangeGoodsList",method:"get",params:e})}function g(e){return Object(t["a"])({url:"/admin_plus/Refund/exchangeGoodsDetail",method:"post",data:e})}function O(e){return Object(t["a"])({url:"/admin_plus/refund/refundDetail",method:"post",data:e})}}}]);