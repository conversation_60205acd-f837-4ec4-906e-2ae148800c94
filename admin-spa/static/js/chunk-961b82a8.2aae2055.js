(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-961b82a8"],{2868:function(e,t,l){"use strict";l.d(t,"d",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"a",(function(){return i})),l.d(t,"b",(function(){return u})),l.d(t,"c",(function(){return o})),l.d(t,"g",(function(){return r})),l.d(t,"f",(function(){return d})),l.d(t,"e",(function(){return s})),l.d(t,"j",(function(){return c})),l.d(t,"k",(function(){return f})),l.d(t,"l",(function(){return p})),l.d(t,"n",(function(){return b})),l.d(t,"p",(function(){return m})),l.d(t,"o",(function(){return v})),l.d(t,"h",(function(){return _})),l.d(t,"m",(function(){return h}));var a=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],n=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],i=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],u=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],o=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],r=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],d=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],s=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],c=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],f=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],p=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],b=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],m=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],v=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],_=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],h=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"3b38":function(e,t,l){"use strict";l.d(t,"n",(function(){return n})),l.d(t,"m",(function(){return i})),l.d(t,"p",(function(){return u})),l.d(t,"l",(function(){return o})),l.d(t,"c",(function(){return r})),l.d(t,"g",(function(){return d})),l.d(t,"f",(function(){return s})),l.d(t,"e",(function(){return c})),l.d(t,"j",(function(){return f})),l.d(t,"k",(function(){return p})),l.d(t,"i",(function(){return b})),l.d(t,"h",(function(){return m})),l.d(t,"a",(function(){return v})),l.d(t,"o",(function(){return _})),l.d(t,"b",(function(){return h})),l.d(t,"q",(function(){return g})),l.d(t,"d",(function(){return y}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/pintuan/data.html",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:e})}function u(e){return Object(a["a"])({url:"/admin/pintuan/store.html",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:e})}function c(e){return Object(a["a"])({url:"/admin/pintuan/change_status",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/admin/pintuan/editStock",method:"post",data:e})}function b(e){return Object(a["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:e})}function m(e){return Object(a["a"])({url:"/admin/pintuan/editSort",method:"post",data:e})}function v(e){return Object(a["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:e})}function _(e){return Object(a["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:e})}function h(e){return Object(a["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:e})}function g(e){return Object(a["a"])({url:"/admin/pintuan/update.html",method:"post",data:e})}function y(e){return Object(a["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:e})}},"3f5e":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return i})),l.d(t,"a",(function(){return u}));var a=l("b775");function n(e){return Object(a["a"])({url:"/admin/upload/upload",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function i(e){return Object(a["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:e,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(e){return Object(a["a"])({url:"/admin/Album/Album",method:"post",data:e})}},4381:function(e,t,l){"use strict";var a=l("a18c"),n={inserted:function(e,t,l){var n=t.value,i=a["a"].app._route.meta&&a["a"].app._route.meta.permissions;i.indexOf(n)<0&&e.parentNode&&e.parentNode.removeChild(e)}},i=function(e){e.directive("permission",n)};window.Vue&&(window["permission"]=n,Vue.use(i)),n.install=i;t["a"]=n},6396:function(e,t,l){"use strict";l.d(t,"a",(function(){return u})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function u(e,t,l){var u=i(),o=e-u,r=20,d=0;t="undefined"===typeof t?500:t;var s=function e(){d+=r;var i=Math.easeInOutQuad(d,u,o,t);n(i),d<t?a(e):l&&"function"===typeof l&&l()};s()}},6724:function(e,t,l){"use strict";l("8d41");var a={bind:function(e,t){e.addEventListener("click",(function(l){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),i=n.ele;if(i){i.style.position="relative",i.style.overflow="hidden";var u=i.getBoundingClientRect(),o=i.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(u.width,u.height)+"px",i.appendChild(o)),n.type){case"center":o.style.top=u.height/2-o.offsetHeight/2+"px",o.style.left=u.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(l.pageY-u.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(l.pageX-u.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=n.color,o.className="waves-ripple z-active",!1}}),!1)}},n=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(n)),a.install=n;t["a"]=a},"7f88":function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"b",(function(){return n})),l.d(t,"c",(function(){return i}));var a=[{type:"input",label:"优惠券名称",model:"goodscoupon_name",placeholder:"请输入优惠券名称"},{type:"select",label:"使用范围",model:"use_scenario",placeholder:"请选择",options:{name:"useScopeOpt"}}],n=[{type:"input",label:"活动ID",model:"goodscoupon_type_id",placeholder:"请输入活动ID"},{type:"input",label:"活动名称",model:"goodscoupon_name",placeholder:"请输入活动名称"},{type:"input",label:"领取用户名称",model:"username",placeholder:"请输入用户昵称"},{type:"input",label:"用户手机号",model:"mobile",placeholder:"请输入用户手机号"},{type:"select",label:"优惠券状态",model:"state",placeholder:"请选择",options:{name:"couponStateOpt"}},{type:"select",label:"优惠券类型",model:"privacy_status",placeholder:"请选择",options:{name:"couponTypeOpt"}},{type:"quickTime",label:"下单时间",model:"use_time"},{type:"quickTime",label:"下单时间",model:"fetch_time"}],i=[{type:"input",label:"搜索内容",model:"username",placeholder:"请输入搜索内容",optFaild:"user_type",options:{name:"orderSearchOpt"}},{type:"input",label:"订单编号",model:"order",placeholder:"请输入订单编号",optFaild:"order_type",options:{name:"orderIdOpt"}},{type:"input",label:"活动名称",model:"pintuan_name",placeholder:"请输入活动名称"},{type:"input",label:"商品名称",model:"goods_name",placeholder:"请输入商品名称"},{type:"input",label:"团ID",model:"group_id",placeholder:"请输入团ID"},{type:"select",label:"中奖状态",model:"win_status",placeholder:"全部",options:{name:"winningLotteryOpt"}},{type:"select",label:"参团类型",model:"is_header",placeholder:"全部",options:{name:"offeredTypeOpt"}},{type:"quickTime",label:"支付时间",model:"pay_time"}]},"8d41":function(e,t,l){},b885:function(e,t,l){"use strict";var a=l("e780");l.d(t,"d",(function(){return a["a"]}));var n=l("ad41");l.d(t,"c",(function(){return n["a"]}));var i=l("0476");l.d(t,"g",(function(){return i["a"]}));var u=l("6eb0");l.d(t,"a",(function(){return u["a"]}));var o=l("c87f");l.d(t,"f",(function(){return o["a"]}));var r=l("333d");l.d(t,"e",(function(){return r["a"]}));var d=l("05be");l.d(t,"b",(function(){return d["a"]}));l("9040");var s=l("4381");l.d(t,"h",(function(){return s["a"]}));var c=l("6724");l.d(t,"i",(function(){return c["a"]}))},c40e:function(e,t,l){"use strict";l.d(t,"e",(function(){return n})),l.d(t,"d",(function(){return i})),l.d(t,"f",(function(){return u})),l.d(t,"c",(function(){return o})),l.d(t,"a",(function(){return r})),l.d(t,"g",(function(){return d})),l.d(t,"b",(function(){return s}));var a=l("b775");function n(e){return Object(a["a"])({url:"/goods/product/state/",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/goods/product/page",method:"post",data:e})}},c71b:function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),l.d(t,"i",(function(){return n})),l.d(t,"H",(function(){return i})),l.d(t,"f",(function(){return u})),l.d(t,"A",(function(){return o})),l.d(t,"x",(function(){return r})),l.d(t,"e",(function(){return d})),l.d(t,"w",(function(){return s})),l.d(t,"c",(function(){return c})),l.d(t,"O",(function(){return f})),l.d(t,"j",(function(){return p})),l.d(t,"k",(function(){return b})),l.d(t,"l",(function(){return m})),l.d(t,"T",(function(){return v})),l.d(t,"d",(function(){return _})),l.d(t,"Q",(function(){return h})),l.d(t,"p",(function(){return g})),l.d(t,"P",(function(){return y})),l.d(t,"m",(function(){return w})),l.d(t,"I",(function(){return O})),l.d(t,"L",(function(){return j})),l.d(t,"N",(function(){return k})),l.d(t,"M",(function(){return I})),l.d(t,"S",(function(){return D})),l.d(t,"s",(function(){return T})),l.d(t,"B",(function(){return x})),l.d(t,"z",(function(){return q})),l.d(t,"K",(function(){return N})),l.d(t,"C",(function(){return A})),l.d(t,"h",(function(){return L})),l.d(t,"g",(function(){return S})),l.d(t,"o",(function(){return C})),l.d(t,"G",(function(){return E})),l.d(t,"J",(function(){return R})),l.d(t,"v",(function(){return G})),l.d(t,"F",(function(){return F})),l.d(t,"r",(function(){return M})),l.d(t,"b",(function(){return P})),l.d(t,"q",(function(){return V})),l.d(t,"R",(function(){return H})),l.d(t,"u",(function(){return X})),l.d(t,"t",(function(){return W})),l.d(t,"D",(function(){return z})),l.d(t,"E",(function(){return B})),l.d(t,"y",(function(){return J})),l.d(t,"n",(function(){return Q}));var a=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],n=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],i=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],u=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],o=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],d=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],s=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],c=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],b=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],_=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],h=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],w=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],O=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],k=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],I=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],D=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],T=[{value:"0",label:"参团"},{value:"1",label:"团长"}],x=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],q=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],N=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],A=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],L=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],S=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],C=[{value:"1",label:"是"},{value:"2",label:"否"}],E=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],R=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],G=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],F=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],M=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],P=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],V=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],H=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],X=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],W=[{label:"按订单编号",value:"order_no"}],z=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],B=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],J=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],Q=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},fe67:function(e,t,l){e.exports=l.p+"static/img/login_bg.e491666c.png"}}]);