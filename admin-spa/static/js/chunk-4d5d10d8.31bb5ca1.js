(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d5d10d8"],{4239:function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"f",(function(){return o})),a.d(e,"a",(function(){return s})),a.d(e,"c",(function(){return d})),a.d(e,"b",(function(){return u}));var n=a("b775");function r(t){return Object(n["a"])({url:"/admin/goodsbrand/lists",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/admin/goodsbrand/modifySort",method:"post",data:t})}function o(t){return Object(n["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/admin/goodsbrand/addBrand.html",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/admin_plus/Goodsbrand/editbrand",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/admin/goodsbrand/editBrand.html",method:"post",data:t})}},a27a:function(t,e,a){},bf14:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[t._v("品牌信息")]),t._v(" "),a("el-form",{ref:"form",attrs:{"label-width":"200px",rules:t.rules,model:t.form}},[a("el-form-item",{attrs:{label:"品牌名称：",prop:"brand_name"}},[a("el-input",{staticStyle:{width:"500px"},model:{value:t.form.brand_name,callback:function(e){t.$set(t.form,"brand_name",e)},expression:"form.brand_name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"品牌名称："}},[a("label",{staticClass:"upload"},[t.form.image_url?a("el-image",{attrs:{src:t.form.image_url,fit:"contain"}}):a("span",[a("i",{staticClass:"el-icon-upload"}),t._v("\n                    点击上传\n                ")]),t._v(" "),a("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{type:"file"},on:{change:t.onChange}})],1),t._v(" "),a("span",{staticClass:"tips"},[t._v("建议图片尺寸：200px * 100px。")]),t._v(" "),a("span",{staticClass:"tips"},[t._v("图片格式：jpg、png、jpeg。")])]),t._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{staticStyle:{width:"100px"},attrs:{type:"number"},model:{value:t.form.sort,callback:function(e){t.$set(t.form,"sort",e)},expression:"form.sort"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small",loading:t.loading},on:{click:t.onSave}},[t._v("保存")])],1)],1)],1)},r=[],i=a("c7eb"),o=(a("96cf"),a("1da1")),s=a("4239"),d={data:function(){return{brand_id:null,loading:!1,form:{sort:0},rules:{brand_name:[{required:!0,message:"请输入品牌名称",trigger:"blur"}]}}},created:function(){var t=this.$route.query.brand_id;if(t)return this.onInit(t),this.form.brand_id=t,void(this.$route.meta.title="编辑品牌");this.$route.meta.title="新增品牌"},methods:{onInit:function(t){var e=this;console.log(t),Object(s["c"])({brand_id:t}).then((function(t){var a=t.data.brand_info;e.form=a}))},onChange:function(){var t=Object(o["a"])(Object(i["a"])().mark((function t(e){var a,n,r,o;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=this.$loading({lock:!0,text:"上传中"}),n=e.target.files[0],r=new FormData,r.append("file",n),t.prev=4,t.next=7,Object(s["f"])(r);case 7:o=t.sent,this.form.image_url=o.data.pic_path,a.close(),this.$message.success(o.message),t.next=15;break;case 13:t.prev=13,t.t0=t["catch"](4);case 15:a.close();case 16:case"end":return t.stop()}}),t,this,[[4,13]])})));function e(e){return t.apply(this,arguments)}return e}(),onSave:function(){var t=this;this.$refs.form.validate((function(e){if(e){var a=t.$route.query.brand_id,n=s["a"];a&&(n=s["b"]),n(t.form).then((function(e){t.$message.success("提交成功"),t.$store.dispatch("delView",{path:t.$route.path}).then((function(e){e.visitedViews;t.$router.push("/goods/brand")}))}))}}))}}},u=d,c=(a("f01c"),a("2877")),l=Object(c["a"])(u,n,r,!1,null,"112b375e",null);e["default"]=l.exports},f01c:function(t,e,a){"use strict";a("a27a")}}]);