(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0eb26618"],{"0781":function(e,t,n){"use strict";n("d8f1")},d8f1:function(e,t,n){},e585:function(e,t,n){"use strict";n.d(t,"m",(function(){return r})),n.d(t,"g",(function(){return s})),n.d(t,"n",(function(){return a})),n.d(t,"p",(function(){return d})),n.d(t,"o",(function(){return o})),n.d(t,"a",(function(){return _})),n.d(t,"c",(function(){return l})),n.d(t,"k",(function(){return u})),n.d(t,"b",(function(){return c})),n.d(t,"h",(function(){return v})),n.d(t,"i",(function(){return f})),n.d(t,"j",(function(){return m})),n.d(t,"f",(function(){return p})),n.d(t,"q",(function(){return h})),n.d(t,"e",(function(){return b})),n.d(t,"d",(function(){return y})),n.d(t,"l",(function(){return g}));var i=n("b775");function r(e){return Object(i["a"])({url:"/admin/refund/refundList",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/admin/refund/getOrderChainStatus",method:"post",data:e})}function a(){return Object(i["a"])({url:"/admin/refund/refundReview",method:"post",data:data})}function d(e){return Object(i["a"])({url:"/admin/refund/returnRefundList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/admin_plus/refund/returnRefundDetail",method:"post",data:e})}function _(e){return Object(i["a"])({url:"/admin/refund/agreeReturnMoney.html",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/admin/refund/checkSupplyChainOrder.html",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/admin/Express/logistics.html",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/admin/refund/agreeReturnRefundReview.html",method:"post",data:e})}function v(e){return Object(i["a"])({url:"/admin/address/getProvince.html",method:"post",data:e})}function f(e){return Object(i["a"])({url:"/admin/address/getcity.html",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/admin/address/getdistrict.html",method:"post",data:e})}function p(e){return Object(i["a"])({url:"/admin/express/expressCompany.html",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/admin/refund/sendAgain.html",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/admin/refund/exchangeGoodsList",method:"get",params:e})}function y(e){return Object(i["a"])({url:"/admin_plus/Refund/exchangeGoodsDetail",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/admin_plus/refund/refundDetail",method:"post",data:e})}},fca0:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"details order"},[n("div",{staticClass:"info"},[n("div",{staticClass:"edit_title"},[e._v("换货订单信息")]),e._v(" "),n("div",[e._v("订单编号："+e._s(e.details.order_no))]),e._v(" "),n("div",[e._v("申请时间："+e._s(e._f("parseTime")(e.details.refund_action_time||0)))]),e._v(" "),n("div",[e._v("买家发货时间："+e._s(e._f("parseTime")(e.afterSaleInfo.buyer_send_time||0)))]),e._v(" "),n("div",[e._v("卖家收货时间："+e._s(e._f("parseTime")(e.afterSaleInfo.seller_receive_time||0)))]),e._v(" "),n("div",[e._v("退款单号："+e._s(e.details.refund_no))]),e._v(" "),n("div",[e._v("付款方式："+e._s(e.orderInfo.app_type_name)+"  "+e._s(e.orderInfo.pay_type_name))]),e._v(" "),n("div",[e._v("会员ID："+e._s(e.orderInfo.member_id))]),e._v(" "),n("div",[e._v("会员手机号："+e._s(e.orderInfo.member_mobile))]),e._v(" "),n("div",[e._v("店铺名称："+e._s(e.orderInfo.site_name))]),e._v(" "),n("div",[e._v("供应商："+e._s(e.orderInfo.supply_shop_name))])]),e._v(" "),n("div",{staticClass:"handle"},[n("div",[n("div",{staticClass:"edit_title"},[e._v("处理换货信息")]),e._v(" "),n("p",[e._v("售后状态："+e._s(e.details.refund_status_name)),n("span",[e._v(e._s(e.details.additional_status_name))])]),e._v(" "),e.details.goods_status_name?n("p",[e._v("商品状态："+e._s(e.details.goods_status_name))]):e._e(),e._v(" "),e.afterSaleInfo.refund_recept_delivery_no?n("div",{staticClass:"logistics"},[n("div",{staticClass:"edit_title"},[e._v("退货物流")]),e._v(" "),n("p",[e._v("快递公司："+e._s(e.afterSaleInfo.refund_recept_delivery_name))]),e._v(" "),n("p",[e._v("快递单号："+e._s(e.afterSaleInfo.refund_recept_delivery_no)+" "),n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.seeLogistics()}}},[e._v("查看物流")])],1)]):e._e(),e._v(" "),6==e.details.refund_status?n("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.sendGoods(0)}}},[e._v("发货")]):e._e(),e._v(" "),8==e.details.refund_status?n("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.sendGoods(1)}}},[e._v("修改物流信息")]):e._e()],1),e._v(" "),e._m(0)])]),e._v(" "),n("div",{staticClass:"details goods_info"},[n("div",[n("div",{staticClass:"edit_title"},[e._v("售后商品")]),e._v(" "),n("div",{staticClass:"info"},[n("img",{attrs:{src:e.getImage(e.details.sku_image)}}),e._v(" "),n("div",[n("span",[e._v(e._s(e.details.sku_name))]),e._v(" "),n("span",[e._v("购买数量： "+e._s(e.details.num))]),e._v(" "),n("span",[e._v("退货数量： "+e._s(e.afterSaleInfo.refund_num))])])])]),e._v(" "),n("div",[n("div",{staticClass:"edit_title"},[e._v("售后信息")]),e._v(" "),n("div",{staticClass:"service"},[e._m(1),e._v(" "),n("p",[e._v("退款金额："),n("span",[e._v("￥"+e._s(e.details.refund_real_money))])]),e._v(" "),n("p",[e._v("联系方式："),n("i",[e._v(e._s(e.details.refund_phone))])]),e._v(" "),n("p",[e._v("退款原因："),n("i",[e._v(e._s(e.details.refund_reason))])]),e._v(" "),n("p",[e._v("补充描述："),n("i",[e._v(" "+e._s(e.details.refund_remark))])]),e._v(" "),n("span",[e._v("上传凭证："),e.afterSaleInfo.upload_vouchers?n("el-image",{attrs:{src:e.afterSaleInfo.upload_vouchers}}):e._e()],1),e._v(" "),n("p",[e._v("接收退货地址："),n("i",[e._v(e._s(e.details.refund_address))])])])]),e._v(" "),n("div",[n("div",{staticClass:"edit_title"},[e._v("购买信息")]),e._v(" "),n("div",{staticClass:"service"},[n("p",[e._v("商品单价："),n("span",[e._v("￥"+e._s(e.details.real_goods_money))]),e._v("x1件")]),e._v(" "),n("p",[e._v("分销商优惠："),n("span",[e._v("-￥"+e._s(e.details.promotion_money))])]),e._v(" "),n("p",[e._v("实付金额："),n("i",[e._v("￥"+e._s(e.details.real_pay_money))])]),e._v(" "),n("p",[e._v("配送状态："),n("i",[e._v(e._s(e.details.delivery_status_name)+" ")])]),e._v(" "),n("p",[e._v("订单编号："),n("el-link",{attrs:{type:"primary",underline:!1}},[e._v(e._s(e.details.order_no))])],1)])])]),e._v(" "),n("div",{staticClass:"details"},[n("div",{staticClass:"edit_title"},[e._v("维权日志")]),e._v(" "),n("div",{staticClass:"log"},e._l(e.details.refund_log_list,(function(t,i){return n("div",{key:i,class:{active:3!=t.action_way}},[n("span",[e._v(e._s(e.getLogs(t.action_way)))]),e._v(" "),n("div",[n("span",[e._v("操作人："+e._s(t.username))]),e._v(" "),n("div",[n("span",[e._v("操作内容："+e._s(t.action))]),e._v(" "),n("span",[e._v(e._s(e._f("parseTime")(t.action_time)))])])])])})),0)]),e._v(" "),n("el-dialog",{attrs:{title:"发货/修改物流信息",visible:e.sendVisible,width:"828px"},on:{"update:visible":function(t){e.sendVisible=t}}},[n("div",{staticClass:"receive"},[n("span",[e._v("请填写发货物流信息")]),e._v(" "),n("el-form",{attrs:{"label-width":"160px"}},[n("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"退换收件人："}},[n("el-input",{model:{value:e.returnForm.refund_name,callback:function(t){e.$set(e.returnForm,"refund_name",t)},expression:"returnForm.refund_name"}})],1),e._v(" "),n("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"联系电话："}},[n("el-input",{model:{value:e.returnForm.refund_phone,callback:function(t){e.$set(e.returnForm,"refund_phone",t)},expression:"returnForm.refund_phone"}})],1),e._v(" "),n("el-form-item",{staticClass:"supplier",attrs:{label:"收货地址："}},[n("el-select",{attrs:{placeholder:"省份"},model:{value:e.returnForm.province_id,callback:function(t){e.$set(e.returnForm,"province_id",t)},expression:"returnForm.province_id"}},e._l(e.provList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-select",{attrs:{placeholder:"市区"},model:{value:e.returnForm.city_id,callback:function(t){e.$set(e.returnForm,"city_id",t)},expression:"returnForm.city_id"}},e._l(e.cityList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-select",{attrs:{placeholder:"县"},model:{value:e.returnForm.district_id,callback:function(t){e.$set(e.returnForm,"district_id",t)},expression:"returnForm.district_id"}},e._l(e.areaList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-input",{staticClass:"address",attrs:{placeholder:"请输入详细地址，以方便买家联系"},model:{value:e.returnForm.refund_address,callback:function(t){e.$set(e.returnForm,"refund_address",t)},expression:"returnForm.refund_address"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"物流公司："}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.returnForm.company_id,callback:function(t){e.$set(e.returnForm,"company_id",t)},expression:"returnForm.company_id"}},e._l(e.expressList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.company_name,value:e.company_id}})})),1)],1),e._v(" "),n("el-form-item",{staticStyle:{width:"60%"},attrs:{label:"快递单号："}},[n("el-input",{model:{value:e.returnForm.delivery_no,callback:function(t){e.$set(e.returnForm,"delivery_no",t)},expression:"returnForm.delivery_no"}})],1)],1)],1),e._v(" "),n("span",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1,e.returnForm={}}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary",loading:e.btnLoad},on:{click:e.onComfirm}},[e._v("确 定")])],1)]),e._v(" "),n("el-dialog",{attrs:{title:"物流信息",visible:e.logisticsVisible},on:{"update:visible":function(t){e.logisticsVisible=t}}},[n("div",{staticClass:"logistics-info"},[n("img",{attrs:{src:e.getImage(e.delivery.company&&e.delivery.company.logo)}}),e._v(" "),n("div",[n("span",[e._v("快递编号：12584525478852")]),e._v(" "),n("span",[e._v("快递公司：顺丰速运")])])]),e._v(" "),n("p",[e._v(e._s(e.delivery.reason))])])],1)},r=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tips"},[n("span",[e._v("温馨提醒")]),e._v(" "),n("p",[e._v("如果未发货，请点击同意退款给买家。")]),e._v(" "),n("p",[e._v("如果实际已发货，请主动与买家联系。")]),e._v(" "),n("p",[e._v("如果订单整体退款后，优惠券和余额会退还给买家。")])])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("p",[e._v("售后类型："),n("span",[e._v("换货")])])}],s=n("5530"),a=n("d2e6"),d=n("c57b"),o=n("b885"),_=n("e585"),l=(n("8975"),{data:function(){return{details:{},exLogsData:a["a"],dialogVisible:!1,loading:!1,columns:d["b"],list:[],afterSaleInfo:{},orderInfo:{},supplyAddress:{},provList:[],cityList:[],areaList:[],sendVisible:!1,expressList:[],returnForm:{},btnLoad:!1,logisticsVisible:!1,delivery:{}}},components:{FormQuery:o["d"]},mounted:function(){this.onInit(),this.getProvince(),this.getExpress()},methods:{getImage:function(e){return e?e.indexOf("http")>=0?e:"/api/".concat(e):""},seeLogistics:function(){var e=this;Object(_["k"])({delivery_no:this.details.refund_delivery_no,company_id:this.details.refund_delivery_company_id}).then((function(t){var n=t.data;e.logisticsVisible=!0,e.delivery=n}))},editLogis:function(){},getLogs:function(e){return 3==e?"平":2==e?"商":1==e?"买":void 0},getExpress:function(){var e=this;Object(_["f"])().then((function(t){var n=t.data;e.expressList=n.list}))},getProvince:function(){var e=this;Object(_["h"])().then((function(t){e.provList=t}))},onComfirm:function(){var e=this;this.btnLoad=!0,this.$confirm("提交后，商品将会快递到该地址，确定要提交吗？","信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){e.loading=!0;var t=e.$route.query.order_goods_id;Object(_["q"])(Object(s["a"])({order_goods_id:t},e.returnForm)).then((function(t){e.btnLoad=!1,e.$message.success("发货/修改物流信息成功"),e.sendVisible=!1,e.onInit()}))})).catch((function(){}))},sendGoods:function(e){this.sendVisible=!0;var t=this.afterSaleInfo,n=t.refund_recept_name,i=t.refund_recept_phone,r=t.refund_recept_city_id,s=t.refund_recept_district_id,a=t.refund_recept_province_id,d=t.refund_recept_short_address,o=t.refund_recept_delivery_company_id,_=t.refund_recept_delivery_no;this.returnForm={refund_name:n,refund_phone:i,province_id:a,city_id:r,district_id:s,refund_address:d,company_id:o,delivery_no:_,edit:e},this.getCity(),this.getArea()},getCity:function(e){var t=this,n=e||this.orderInfo.province_id;Object(_["i"])({province_id:n}).then((function(n){t.cityList=n,e||(t.areaList=[])}))},getArea:function(e){var t=this,n=e||this.orderInfo.city_id;Object(_["j"])({city_id:n}).then((function(e){t.areaList=e}))},receive:function(){this.btnLoad=!0},onInit:function(){var e=this,t=this.$route.query.order_goods_id;Object(_["d"])({order_goods_id:t}).then((function(t){var n=t.data,i=n.detail,r=n.afterSaleInfo,s=n.order_info,a=n.supplyAddress;e.details=i||{},e.afterSaleInfo=r||{},e.orderInfo=s||{},e.supplyAddress=a||{}}))}}}),u=l,c=(n("0781"),n("2877")),v=Object(c["a"])(u,i,r,!1,null,"fa7812e8",null);t["default"]=v.exports}}]);