(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d3297ff"],{2868:function(t,e,n){"use strict";n.d(e,"d",(function(){return l})),n.d(e,"i",(function(){return a})),n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"g",(function(){return r})),n.d(e,"f",(function(){return d})),n.d(e,"e",(function(){return c})),n.d(e,"j",(function(){return s})),n.d(e,"k",(function(){return f})),n.d(e,"l",(function(){return p})),n.d(e,"n",(function(){return m})),n.d(e,"p",(function(){return b})),n.d(e,"o",(function(){return v})),n.d(e,"h",(function(){return h})),n.d(e,"m",(function(){return _}));var l=[{faild:"goodscoupon_type_id",title:"ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"money",title:"优惠政策",slot:"money"},{faild:"count",title:"发放总数量"},{faild:"max_fetch",title:"领取上限",slot:"max_fetch"},{faild:"end_time",title:"领取有效期",slot:"end_time"},{faild:"start_time",title:"活动开始时间",slot:"start_time"},{faild:"over_time",title:"活动结束时间",slot:"over_time"},{faild:"use_scenario",title:"使用范围",slot:"use_scenario"},{faild:"status_name",title:"状态"},{faild:"privacy_status",title:"公开状态",slot:"privacy_status"},{title:"操作",slot:"action",faild:"action",width:"250"}],a=[{faild:"goodscoupon_type_id",title:"活动ID"},{faild:"nickname",title:"领取用户名"},{faild:"mobile",title:"用户手机号"},{faild:"goodscoupon_name",title:"活动名称"},{faild:"privacy_status",title:"券类",slot:"privacy_status"},{faild:"money",title:"优惠金额"},{faild:"state",title:"优惠券状态",slot:"state"},{faild:"fetch_time",title:"领取时间",slot:"fetch_time"},{faild:"get_type",title:"获取方式",slot:"get_type"},{faild:"use_time",title:"使用时间",slot:"use_time"},{faild:"order_money",title:"关联订单金"},{faild:"order_no",title:"关联订单号",slot:"order_no"},{title:"操作",slot:"action",faild:"action"}],o=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"single_count",title:"每个用户派发张数",slot:"single_count"},{title:"操作",slot:"action"}],u=[{faild:"goodscoupon_type_id",title:"券ID"},{faild:"goodscoupon_name",title:"优惠券名称"},{faild:"count",title:"剩余券数量"},{faild:"over_time",title:"活动结束"},{title:"操作",slot:"action"}],i=[{faild:"rule_id",title:"ID"},{faild:"rule_name",title:"规则名称"},{faild:"send_count",title:"已派发数量"},{faild:"start_time",title:"开始执行"},{faild:"stop_time",title:"停止执行"},{faild:"status_name",title:"状态"},{title:"操作",slot:"action",width:200}],r=[{faild:"member_id",title:"ID"},{faild:"mobile",title:"用户手机号"},{faild:"site_name",title:"当前锁定店铺"},{faild:"parent_name",title:"注册推荐人"},{faild:"reg_time",title:"注册时间"},{title:"操作",slot:"action"}],d=[{type:"selection"},{faild:"id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"销售价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{title:"操作",slot:"action"}],c=[{type:"selection"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"goods_stock",title:"库存",width:150}],s=[{type:"selection"},{faild:"sku_name",title:"商品",slot:"sku_name"},{faild:"stock",title:"库存",width:150}],f=[{faild:"topic_name",title:"专题名称"},{faild:"start_time",title:"开始时间",slot:"start_time"},{fiald:"end_time",title:"结束时间",slot:"end_time"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],p=[{faild:"goods_name",title:"商品",slot:"goods_name",width:"200"},{faild:"reward_shop",title:"店主佣金",slot:"reward_shop"},{faild:"sale_price",title:"商店价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"reward_shop_rate",title:"店主佣金比例(%)"},{faild:"goods_stock",title:"库存"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"goods_state",title:"商品状态",slot:"goods_state"},{faild:"verify_state",title:"活动商品状态",slot:"verify_state"},{title:"操作",slot:"action"}],m=[{faild:"pintuan_id",title:"活动ID"},{faild:"pintuan_name",title:"活动名称"},{faild:"promotion_type",title:"活动类型",slot:"promotion_type"},{faild:"valid_date",title:"活动时间"},{faild:"robot_nums",title:"成团人数"},{faild:"goods_num",title:"商品数量",sortable:!0},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action",width:350}],b=[{faild:"order_no",title:"订单编号"},{faild:"member_id",title:"用户ID"},{faild:"sku_name",title:"商品名称"},{faild:"pay_type",title:"支付方式",slot:"pay_type"},{faild:"pay_time",title:"支付时间",slot:"pay_time"},{faild:"pintuan_name",title:"活动名称"},{faild:"group_id",title:"团ID",sortable:!0},{faild:"is_header",title:"参团类型",slot:"is_header"},{faild:"mobile",title:"用户手机号码"},{faild:"pintuan_status",title:"拼团状态",slot:"pintuan_status"},{faild:"win_status",title:"中奖状态",slot:"win_status"},{faild:"inviter_mobile",title:"邀请人号码"},{title:"操作",slot:"action",fixed:"right"}],v=[{faild:"pintuan_id",title:"ID"},{faild:"goods_name",title:"商品",slot:"goods_name"},{faild:"sale_price",title:"商品价格",slot:"sale_price"},{faild:"cost_price",title:"成本价",slot:"cost_price"},{faild:"pintuan_price",title:"拼团价格(可编辑)",slot:"pintuan_price",width:120},{faild:"stock",title:"库存",slot:"stock",width:120},{faild:"virtual_order_num",title:"虚拟开团次数",slot:"virtual_order_num"},{faild:"group_nums",title:"开团次数"},{faild:"group_success_nums",title:"成团次数"},{faild:"sale_num",title:"实际销量"},{faild:"sort",title:"排序",slot:"sort"},{faild:"status",title:"状态",slot:"status"},{title:"操作",slot:"action"}],h=[{faild:"group_id",title:"拼团ID",sortable:!0},{faild:"goods_name",title:"商品名称"},{faild:"stock",title:"剩余活动库存"},{faild:"mobile",title:"开团用户手机号码"},{faild:"pintuan_num",title:"参团人数"},{faild:"pintuan_count",title:"当前参团人数",slot:"pintuan_count"},{title:"操作",slot:"action"}],_=[{title:"序号",type:"index"},{faild:"goods_name",title:"商品名称"},{faild:"group_nums",title:"开团人次"},{faild:"join_group_nums",title:"参团人次"},{faild:"win_order_nums",title:"中奖订单数"},{faild:"win_order_money",title:"商品订单金额"},{faild:"share_nums",title:"分享次数"},{faild:"share_people_nums",title:"分享人数"},{faild:"open_share_nums",title:"打开分享次数"},{faild:"open_share_people_nums",title:"打开分享人数"},{faild:"status_text",title:"当前商品状态"},{faild:"sale_time",title:"在售时长"},{faild:"last_up_time",title:"最后上架时间"},{faild:"last_down_time",title:"最后下架时间"},{title:"操作",slot:"action"}]},"342a":function(t,e,n){"use strict";n.r(e);var l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[t.showSearch?n("div",{staticClass:"filter-container"},[n("formQuery",{staticClass:"mb-20",attrs:{baseConfig:t.baseConfig,config:t.formConfig,options:t.formopts},model:{value:t.form,callback:function(e){t.form=e},expression:"form"}}),t._v(" "),n("div",{staticClass:"flex-b-c buttons"},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(e){return t.handleQuery()}}},[t._v("搜索")]),t._v(" "),n("el-button",{attrs:{plain:"",size:"small",icon:"el-icon-refresh"},on:{click:t.handleReset}},[t._v("重置")])],1)],1):t._e(),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-list"},[n("div",{staticClass:"btns"},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.goSkip("/addrule")}}},[t._v("添加自动派券规则")])],1),t._v(" "),n("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{isSearch:!0,showSearch:t.showSearch,options:t.options,columns:t.columns,data:t.list},on:{toggleSearch:t.toggleSearch,"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},onSearch:t.getTableList},scopedSlots:t._u([{key:"refund_action_time",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(t._f("parseTime")(n.refund_action_time))+"\n            ")]}},{key:"action_action_time",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(t._f("parseTime")(n.action_action_time))+"\n            ")]}},{key:"action",fn:function(e){var l=e.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/autorule/details",{rule_id:l.rule_id})}}},[t._v("查看")]),t._v(" "),1==l.status||2==l.status?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.stopEarly(l)}}},[t._v("提前停止")]):t._e(),t._v(" "),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.goSkip("/record",{rule_id:l.rule_id})}}},[t._v("派发记录")])]}}])})],1)])},a=[],o=n("5530"),u=n("b885"),i=n("2868"),r=n("c71b"),d=n("6229"),c="/market/platform/coupon",s={components:{FormQuery:u["d"]},data:function(){return{showSearch:!0,baseConfig:{labelWidth:"120px"},formopts:{activeStatusOpt:r["d"]},form:{},formConfig:[{type:"input",label:"ID",model:"rule_id",placeholder:"请输入ID"},{type:"input",label:"规则名称",model:"rule_name",placeholder:"请输入规则名称"},{type:"select",label:"活动状态",model:"status",placeholder:"请选择",options:{name:"activeStatusOpt"}}],loading:!1,columns:i["c"],list:[],options:{page:1,page_size:10,total:0}}},mounted:function(){this.handleQuery()},methods:{stopEarly:function(t){var e=this,n=t.rule_id;this.$confirm('点击提前停止并确定后，"停止执行"的时间需变更为点击"确定"的时间',"信息",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0}).then((function(){Object(d["u"])({rule_id:n}).then((function(t){e.$message.success("提前停止自动派券规则成功"),e.handleQuery()}))})).catch((function(){}))},goSkip:function(t,e){t="".concat(c).concat(t),this.$router.push({path:t,query:e})},handleReset:function(){this.form={}},toggleSearch:function(){this.showSearch=!this.showSearch},getTableList:function(t){var e=this;Object(d["m"])(Object(o["a"])(Object(o["a"])({},t),this.form)).then((function(t){var n=t.data,l=n.count,a=n.list;e.options.total=l,e.list=a,e.loading=!1}))},handleQuery:function(t){this.loading=!0;var e=t||{page:1,page_size:this.options.page_size};this.getTableList(e)}}},f=s,p=n("2877"),m=Object(p["a"])(f,l,a,!1,null,"55f3f302",null);e["default"]=m.exports},"3b38":function(t,e,n){"use strict";n.d(e,"n",(function(){return a})),n.d(e,"m",(function(){return o})),n.d(e,"p",(function(){return u})),n.d(e,"l",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"g",(function(){return d})),n.d(e,"f",(function(){return c})),n.d(e,"e",(function(){return s})),n.d(e,"j",(function(){return f})),n.d(e,"k",(function(){return p})),n.d(e,"i",(function(){return m})),n.d(e,"h",(function(){return b})),n.d(e,"a",(function(){return v})),n.d(e,"o",(function(){return h})),n.d(e,"b",(function(){return _})),n.d(e,"q",(function(){return g})),n.d(e,"d",(function(){return y}));var l=n("b775");function a(t){return Object(l["a"])({url:"/admin/pintuan/data.html",method:"get",params:t})}function o(t){return Object(l["a"])({url:"/admin/pintuanGroupOrder/orderLists.html",method:"get",params:t})}function u(t){return Object(l["a"])({url:"/admin/pintuan/store.html",method:"post",data:t})}function i(t){return Object(l["a"])({url:"/admin/pintuanGroupOrder/groupLists.html",method:"post",data:t})}function r(t){return Object(l["a"])({url:"/admin/pintuanGroupOrder/endPintuanGroup.html",method:"post",data:t})}function d(t){return Object(l["a"])({url:"/admin/pintuan/goodsStatistics.html",method:"post",data:t})}function c(t){return Object(l["a"])({url:"/admin/pintuan/goodsData.html",method:"get",params:t})}function s(t){return Object(l["a"])({url:"/admin/pintuan/change_status",method:"post",data:t})}function f(t){return Object(l["a"])({url:"/admin/pintuan/updateGoods",method:"post",data:t})}function p(t){return Object(l["a"])({url:"/admin/pintuan/editStock",method:"post",data:t})}function m(t){return Object(l["a"])({url:"/admin/pintuan/editVirtualOrderNum",method:"post",data:t})}function b(t){return Object(l["a"])({url:"/admin/pintuan/editSort",method:"post",data:t})}function v(t){return Object(l["a"])({url:"/admin/goods/admin_goodsselect.html",method:"get",params:t})}function h(t){return Object(l["a"])({url:"/admin_plus/pintuan/statistics.html",method:"get",params:t})}function _(t){return Object(l["a"])({url:"/admin_plus/Pintuan/edit",method:"post",data:t})}function g(t){return Object(l["a"])({url:"/admin/pintuan/update.html",method:"post",data:t})}function y(t){return Object(l["a"])({url:"/admin/pintuan/exportGoodsStatistics",method:"post",data:t})}},"3f5e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return u}));var l=n("b775");function a(t){return Object(l["a"])({url:"/admin/upload/upload",method:"post",data:t,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function o(t){return Object(l["a"])({url:"/admin/upload/openAlbumNoType",method:"post",data:t,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"multipart/form-data"}})}function u(t){return Object(l["a"])({url:"/admin/Album/Album",method:"post",data:t})}},4381:function(t,e,n){"use strict";var l=n("a18c"),a={inserted:function(t,e,n){var a=e.value,o=l["a"].app._route.meta&&l["a"].app._route.meta.permissions;o.indexOf(a)<0&&t.parentNode&&t.parentNode.removeChild(t)}},o=function(t){t.directive("permission",a)};window.Vue&&(window["permission"]=a,Vue.use(o)),a.install=o;e["a"]=a},6229:function(t,e,n){"use strict";n.d(e,"n",(function(){return r})),n.d(e,"o",(function(){return d})),n.d(e,"h",(function(){return c})),n.d(e,"j",(function(){return s})),n.d(e,"m",(function(){return f})),n.d(e,"e",(function(){return p})),n.d(e,"i",(function(){return m})),n.d(e,"u",(function(){return b})),n.d(e,"t",(function(){return v})),n.d(e,"r",(function(){return h})),n.d(e,"s",(function(){return _})),n.d(e,"l",(function(){return g})),n.d(e,"g",(function(){return y})),n.d(e,"d",(function(){return O})),n.d(e,"b",(function(){return w})),n.d(e,"c",(function(){return j})),n.d(e,"a",(function(){return k})),n.d(e,"q",(function(){return C})),n.d(e,"p",(function(){return S})),n.d(e,"v",(function(){return x}));var l=n("b775"),a=n("6dab");n.d(e,"w",(function(){return a["i"]}));var o=n("d74f");n.d(e,"k",(function(){return o["i"]}));var u=n("3b38");n.d(e,"f",(function(){return u["a"]}));var i="/goodscoupon/admin";function r(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/lists.html"),method:"get",params:t})}function d(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/receive.html"),method:"post",data:t})}function c(t,e){return Object(l["a"])({url:"".concat(i,"/goodscoupon/deleteMemberCoupon.html?coupon_id=").concat(e),method:"post",data:t})}function s(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/export.html"),method:"post",data:t,responseType:"blob"})}function f(t){return Object(l["a"])({url:"".concat(i,"/goodsCouponRule/list.html"),method:"get",params:t})}function p(t){return Object(l["a"])({url:"".concat(i,"/goodsCouponRule/add.html"),method:"post",data:t})}function m(t){return Object(l["a"])({url:"".concat(i,"/goodsCouponRule/detailList.html"),method:"get",params:t})}function b(t){return Object(l["a"])({url:"".concat(i,"/goodsCouponRule/stopped.html"),method:"post",data:t})}function v(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/shutDown.html"),method:"post",data:t})}function h(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/sendPageData.html"),method:"get",params:t})}function _(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/sendToSelectedMember.html"),method:"post",data:t})}function g(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/goodsData.html"),method:"get",params:t})}function y(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/deleteGoods.html"),method:"post",data:t})}function O(t){return Object(l["a"])({url:"".concat(i,"/goodscoupon/addGoods.html"),method:"post",data:t})}function w(t){return Object(l["a"])({url:"/admin_plus/AddonGoodscoupon/detail",method:"post",data:t})}function j(t){return Object(l["a"])({url:"/admin_plus/AddonGoodscoupon/lists",method:"post",data:t})}function k(t){return Object(l["a"])({url:"/admin_plus/AddonGoodsCouponRule/detail",method:"post",data:t})}function C(t){return Object(l["a"])({url:"/admin_plus/AddonGoodscoupon/sendPage",method:"post",data:t})}function S(t){return Object(l["a"])({url:"/goodscoupon/admin/goodsCouponRule/selectGoodsCoupon.html",method:"post",data:t})}function x(t){return Object(l["a"])({url:"/goodscoupon/admin/goodsCouponRule/tokenGetTempList.html",method:"post",data:t})}},6396:function(t,e,n){"use strict";n.d(e,"a",(function(){return u})),Math.easeInOutQuad=function(t,e,n,l){return t/=l/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var l=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function u(t,e,n){var u=o(),i=t-u,r=20,d=0;e="undefined"===typeof e?500:e;var c=function t(){d+=r;var o=Math.easeInOutQuad(d,u,i,e);a(o),d<e?l(t):n&&"function"===typeof n&&n()};c()}},6724:function(t,e,n){"use strict";n("8d41");var l={bind:function(t,e){t.addEventListener("click",(function(n){var l=Object.assign({},e.value),a=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),o=a.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var u=o.getBoundingClientRect(),i=o.querySelector(".waves-ripple");switch(i?i.className="waves-ripple":(i=document.createElement("span"),i.className="waves-ripple",i.style.height=i.style.width=Math.max(u.width,u.height)+"px",o.appendChild(i)),a.type){case"center":i.style.top=u.height/2-i.offsetHeight/2+"px",i.style.left=u.width/2-i.offsetWidth/2+"px";break;default:i.style.top=(n.pageY-u.top-i.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",i.style.left=(n.pageX-u.left-i.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return i.style.backgroundColor=a.color,i.className="waves-ripple z-active",!1}}),!1)}},a=function(t){t.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(a)),l.install=a;e["a"]=l},"6dab":function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"i",(function(){return o})),n.d(e,"a",(function(){return u})),n.d(e,"h",(function(){return i})),n.d(e,"f",(function(){return r})),n.d(e,"d",(function(){return d})),n.d(e,"c",(function(){return c})),n.d(e,"e",(function(){return s})),n.d(e,"b",(function(){return f}));var l=n("b775");function a(t){return Object(l["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:t})}function o(t){return Object(l["a"])({url:"/admin/upload/upload.html",method:"post",data:t})}function u(t){return Object(l["a"])({url:"/topic/admin/topic/add.html",method:"post",data:t})}function i(t){return Object(l["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:t})}function r(t){return Object(l["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:t})}function d(t){return Object(l["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:t})}function c(t){return Object(l["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:t})}function s(t){return Object(l["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:t})}function f(t){return Object(l["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:t})}},"8d41":function(t,e,n){},b885:function(t,e,n){"use strict";var l=n("e780");n.d(e,"d",(function(){return l["a"]}));var a=n("ad41");n.d(e,"c",(function(){return a["a"]}));var o=n("0476");n.d(e,"g",(function(){return o["a"]}));var u=n("6eb0");n.d(e,"a",(function(){return u["a"]}));var i=n("c87f");n.d(e,"f",(function(){return i["a"]}));var r=n("333d");n.d(e,"e",(function(){return r["a"]}));var d=n("05be");n.d(e,"b",(function(){return d["a"]}));n("9040");var c=n("4381");n.d(e,"h",(function(){return c["a"]}));var s=n("6724");n.d(e,"i",(function(){return s["a"]}))},c40e:function(t,e,n){"use strict";n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return o})),n.d(e,"f",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return r})),n.d(e,"g",(function(){return d})),n.d(e,"b",(function(){return c}));var l=n("b775");function a(t){return Object(l["a"])({url:"/goods/product/state/",method:"post",data:t})}function o(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}function u(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}function i(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}function r(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}function d(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}function c(t){return Object(l["a"])({url:"/goods/product/page",method:"post",data:t})}},c71b:function(t,e,n){"use strict";n.d(e,"a",(function(){return l})),n.d(e,"i",(function(){return a})),n.d(e,"H",(function(){return o})),n.d(e,"f",(function(){return u})),n.d(e,"A",(function(){return i})),n.d(e,"x",(function(){return r})),n.d(e,"e",(function(){return d})),n.d(e,"w",(function(){return c})),n.d(e,"c",(function(){return s})),n.d(e,"O",(function(){return f})),n.d(e,"j",(function(){return p})),n.d(e,"k",(function(){return m})),n.d(e,"l",(function(){return b})),n.d(e,"T",(function(){return v})),n.d(e,"d",(function(){return h})),n.d(e,"Q",(function(){return _})),n.d(e,"p",(function(){return g})),n.d(e,"P",(function(){return y})),n.d(e,"m",(function(){return O})),n.d(e,"I",(function(){return w})),n.d(e,"L",(function(){return j})),n.d(e,"N",(function(){return k})),n.d(e,"M",(function(){return C})),n.d(e,"S",(function(){return S})),n.d(e,"s",(function(){return x})),n.d(e,"B",(function(){return T})),n.d(e,"z",(function(){return D})),n.d(e,"K",(function(){return I})),n.d(e,"C",(function(){return G})),n.d(e,"h",(function(){return R})),n.d(e,"g",(function(){return A})),n.d(e,"o",(function(){return L})),n.d(e,"G",(function(){return N})),n.d(e,"J",(function(){return q})),n.d(e,"v",(function(){return E})),n.d(e,"F",(function(){return z})),n.d(e,"r",(function(){return Q})),n.d(e,"b",(function(){return M})),n.d(e,"q",(function(){return P})),n.d(e,"R",(function(){return V})),n.d(e,"u",(function(){return B})),n.d(e,"t",(function(){return F})),n.d(e,"D",(function(){return H})),n.d(e,"E",(function(){return W})),n.d(e,"y",(function(){return X})),n.d(e,"n",(function(){return $}));var l=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],a=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],o=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],u=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],i=[{label:"是",code:1},{label:"否",code:0}],r=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],d=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],s=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],f=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],p=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],m=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],b=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],v=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],h=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],_=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],g=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],O=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],w=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],j=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],k=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],C=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],S=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],x=[{value:"0",label:"参团"},{value:"1",label:"团长"}],T=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],D=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],I=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],G=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],R=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],A=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],L=[{value:"1",label:"是"},{value:"2",label:"否"}],N=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],q=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],E=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],z=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],Q=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],M=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],P=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],V=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],B=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],F=[{label:"按订单编号",value:"order_no"}],H=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],W=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],X=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],$=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]},fe67:function(t,e,n){t.exports=n.p+"static/img/login_bg.e491666c.png"}}]);