(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3ef2e746"],{5715:function(e,t,a){},ade9:function(e,t,a){"use strict";a("5715")},daba:function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return s})),a.d(t,"f",(function(){return d}));var i=a("b775");function r(e){return Object(i["a"])({url:"/admin/config/award.html",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/admin_plus/config/award.html",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/admin/memberwithdraw/config.html",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/admin_plus/Memberwithdraw/config",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/admin_plus/Order/config.html",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/admin/Order/config.html",method:"post",data:e})}},e08a:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"oper_tips details"},[a("el-collapse",{on:{change:e.handleChange},model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"操作提示",name:"1"}},[a("ul",[a("li",[e._v("设置好相关的奖励, 达到条件时系统会自动发送对应的奖励到用户账户上")])])])],1)],1),e._v(" "),a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[e._v("奖励设置")]),e._v(" "),a("el-form",{ref:"form",staticStyle:{"max-width":"600px"},attrs:{"label-width":"150px",model:e.form,rules:e.rules}},[a("el-form-item",{attrs:{label:"拉新奖励：",prop:"invite_new_register_award"}},[a("div",[a("el-input",{staticClass:"input-with-select",attrs:{placeholder:"请输入内容"},model:{value:e.form.invite_new_register_award,callback:function(t){e.$set(e.form,"invite_new_register_award",t)},expression:"form.invite_new_register_award"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.form.invite_new_register_award_type,callback:function(t){e.$set(e.form,"invite_new_register_award_type",t)},expression:"form.invite_new_register_award_type"}},[a("el-option",{attrs:{label:"余额",value:"2"}})],1)],1)],1),e._v(" "),a("span",{staticClass:"tips"},[e._v("推荐用户注册后获得奖励")])]),e._v(" "),a("el-form-item",{attrs:{label:"邀请规则："}},[a("quill-editor",{ref:"quillEditor",attrs:{options:e.editorOption},model:{value:e.form.invite_rule,callback:function(t){e.$set(e.form,"invite_rule",t)},expression:"form.invite_rule"}})],1),e._v(" "),a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary",size:"small",loading:e.loading},on:{click:e.onSave}},[e._v("保存")])],1)],1)],1)])},r=[],n=a("daba"),o={data:function(){return{form:{},activeNames:1,rules:{invite_new_register_award:[{required:!0,message:"拉新奖励不能为空",trigger:"blur"}]},content:"",editorOption:{modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6]}],[{color:[]},{background:[]}],["clean"],["image","video"]]},placeholder:"请输入正文"},loading:!1}},mounted:function(){var e=this;Object(n["d"])().then((function(t){var a=t.data.config.data;e.form=a.value}))},methods:{handleChange:function(){},onSave:function(){var e=this;this.loading=!0,console.log(this.form),this.$refs.form.validate((function(t){t?Object(n["c"])(e.form).then((function(t){console.log(t),e.$message.success("奖励设置成功"),e.loading=!1})):e.loading=!1}))}}},l=o,s=(a("ade9"),a("2877")),d=Object(s["a"])(l,i,r,!1,null,"ba38e69c",null);t["default"]=d.exports}}]);