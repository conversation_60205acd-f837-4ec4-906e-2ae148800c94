(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-28449051"],{"15fd":function(e,l,a){"use strict";function t(e,l){if(null==e)return{};var a,t,i={},n=Object.keys(e);for(t=0;t<n.length;t++)a=n[t],l.indexOf(a)>=0||(i[a]=e[a]);return i}function i(e,l){if(null==e)return{};var a,i,n=t(e,l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)a=r[i],l.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}a.d(l,"a",(function(){return i}))},"33a6":function(e,l,a){"use strict";var t=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("el-dialog",{attrs:{title:"选择商品分类",visible:e.dialogVisible,top:"100px",width:"810px"},on:{"update:visible":function(l){e.dialogVisible=l}}},[a("div",{staticClass:"classify"},[a("div",{staticClass:"fist-level"},e._l(e.fistLevels,(function(l,t){return a("span",{key:t,class:{danger:l.category_name==e.titles[0]},on:{click:function(a){e.getClassify("seconddarys",2,l.category_id),e.teriarys=[],e.setTitle(l,0)}}},[e._v("\n                "+e._s(l.category_name)+"\n                "),l.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"second-level"},e._l(e.seconddarys,(function(l,t){return a("span",{key:t,class:{danger:l.category_name==e.titles[1]},on:{click:function(a){e.getClassify("teriarys",3,l.category_id),e.setTitle(l,1)}}},[e._v("\n                "+e._s(l.category_name)+"\n                "),l.child_count>0?a("i",{staticClass:"el-icon-arrow-right"}):e._e()])})),0),e._v(" "),a("div",{staticClass:"teriary-level"},e._l(e.teriarys,(function(l,t){return a("span",{key:t,class:{danger:l.category_name==e.titles[2]},on:{click:function(a){return e.setTitle(l,2)}}},[e._v("\n                "+e._s(l.category_name)+"\n            ")])})),0)]),e._v(" "),a("div",{staticClass:"current"},[e._v("\n        您当前选择的是：\n        "+e._s(e.titles[0])+" "),e.titles[1]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[1])+" "),e.titles[2]?a("i",{staticClass:"el-icon-arrow-right"}):e._e(),e._v("\n        "+e._s(e.titles[2])+"\n    ")]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submit}},[e._v("保 存")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(l){e.dialogVisible=!1}}},[e._v("关 闭")])],1)])},i=[],n=(a("28a5"),a("d74f")),r={data:function(){return{dialogVisible:!1,fistLevels:[],seconddarys:[],teriarys:[],titles:[],cateIds:[]}},props:["value"],mounted:function(){this.value&&(this.titles=this.value.split("/")),this.getClassify()},methods:{init:function(e){this.dialogVisible=!0,e&&(this.getClassify("seconddarys",2,e[0]),this.getClassify("teriarys",3,e[1]),this.cateIds=e)},getClassify:function(e,l,a){var t=this,i={level:l||1,pid:a||0};Object(n["i"])(i).then((function(l){var a=l.data;t[e||"fistLevels"]=a}))},setTitle:function(e,l){var a=e.category_name,t=e.category_id;this.titles[l]=a,this.cateIds[l]=t,l<2&&this.titles.splice(l+1,this.titles.length),this.$forceUpdate()},submit:function(){this.$emit("onClassify",this.cateIds),this.$emit("input",this.titles.join("/")),this.dialogVisible=!1}}},u=r,o=(a("564d"),a("2877")),s=Object(o["a"])(u,t,i,!1,null,"260665e1",null);l["a"]=s.exports},"33c9":function(e,l,a){"use strict";a("bfab")},"564d":function(e,l,a){"use strict";a("c16a")},"6dab":function(e,l,a){"use strict";a.d(l,"g",(function(){return i})),a.d(l,"i",(function(){return n})),a.d(l,"a",(function(){return r})),a.d(l,"h",(function(){return u})),a.d(l,"f",(function(){return o})),a.d(l,"d",(function(){return s})),a.d(l,"c",(function(){return c})),a.d(l,"e",(function(){return v})),a.d(l,"b",(function(){return d}));var t=a("b775");function i(e){return Object(t["a"])({url:"/topic/admin/topic/lists.html",method:"get",params:e})}function n(e){return Object(t["a"])({url:"/admin/upload/upload.html",method:"post",data:e})}function r(e){return Object(t["a"])({url:"/topic/admin/topic/add.html",method:"post",data:e})}function u(e){return Object(t["a"])({url:"/topic/admin/topic/delete.html",method:"post",data:e})}function o(e){return Object(t["a"])({url:"/topic/admin/topic/goods.html",method:"get",params:e})}function s(e){return Object(t["a"])({url:"/topic/admin/topic/downInvalidGoods.html",method:"post",data:e})}function c(e){return Object(t["a"])({url:"/topic/admin/topic/change_status.html",method:"post",data:e})}function v(e){return Object(t["a"])({url:"/topic/admin/topic/editSort.html",method:"post",data:e})}function d(e){return Object(t["a"])({url:"/topic/admin/topic/addTopicGoods.html",method:"post",data:e})}},"9b97":function(e,l,a){"use strict";a.r(l);var t=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("div",{staticClass:"details"},[a("div",{staticClass:"edit_title"},[e._v("添加专题活动")]),e._v(" "),a("el-form",{ref:"formRef",attrs:{model:e.form,"label-position":"right",inline:!1,rules:e.rules,"label-width":"140px"}},[a("el-form-item",{staticStyle:{width:"50%"},attrs:{prop:"topic_name",label:"活动标题："}},[a("el-input",{attrs:{size:"small",placeholder:"请输入活动标题名称"},model:{value:e.form.topic_name,callback:function(l){e.$set(e.form,"topic_name",l)},expression:"form.topic_name"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"privacy_status",label:"活动时间类型："}},[a("el-radio-group",{model:{value:e.form.topic_type,callback:function(l){e.$set(e.form,"topic_type",l)},expression:"form.topic_type"}},[a("el-radio",{attrs:{label:"short_time"}},[e._v("限时")]),e._v(" "),a("el-radio",{attrs:{label:"long_time"}},[e._v("长期有效")])],1)],1),e._v(" "),"short_time"==e.form.topic_type?a("el-form-item",{attrs:{label:"开始时间：",prop:"at_least"}},[a("el-date-picker",{attrs:{size:"small",type:"datetime",placeholder:"选择日期时间"},model:{value:e.form.start_time,callback:function(l){e.$set(e.form,"start_time",l)},expression:"form.start_time"}})],1):e._e(),e._v(" "),"short_time"==e.form.topic_type?a("el-form-item",{attrs:{label:"结束时间：",prop:"at_least"}},[a("el-date-picker",{attrs:{size:"small",type:"datetime",placeholder:"选择日期时间"},model:{value:e.form.end_time,callback:function(l){e.$set(e.form,"end_time",l)},expression:"form.end_time"}}),e._v(" "),a("p",{staticClass:"tips"},[e._v("结束时间不能小于开始时间，也不能小于当前时间")])],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"是否完成加盟任务："}},[a("el-radio-group",{model:{value:e.form.is_league,callback:function(l){e.$set(e.form,"is_league",l)},expression:"form.is_league"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:"0"}},[e._v("否")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"横幅图片：",prop:"at_least"}},[a("label",{directives:[{name:"loading",rawName:"v-loading",value:e.picLoad,expression:"picLoad"}],staticClass:"upload-demo"},[a("i",{staticClass:"el-icon-upload"}),e._v(" "),a("span",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),e._v(" "),e.form.topic_adv?a("el-image",{attrs:{src:e.form.topic_adv,fit:"cover"}}):e._e(),e._v(" "),a("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{type:"file",accept:"image/*"},on:{change:e.uploadChange}})],1),e._v(" "),a("div",{staticClass:"tips"},[e._v("\n                图片宽度>=750px，高度自适应\n            ")])]),e._v(" "),a("el-form-item",{attrs:{label:"背景主色调："}},[a("el-color-picker",{attrs:{"show-alpha":""},model:{value:e.form.bg_color,callback:function(l){e.$set(e.form,"bg_color",l)},expression:"form.bg_color"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"商品列表模板："}},[a("el-radio-group",{staticClass:"radio",model:{value:e.form.temp,callback:function(l){e.$set(e.form,"temp",l)},expression:"form.temp"}},[a("div",[a("img",{attrs:{src:"https://youpin-dev.jiufuwangluo.com/public/static/img/topic_template_1.png"}}),e._v(" "),a("el-radio",{attrs:{label:"1"}},[e._v("模板1")])],1),e._v(" "),a("div",[a("img",{attrs:{src:"https://youpin-dev.jiufuwangluo.com/public/static/img/topic_template_2.png"}}),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("模板2")])],1)])],1),e._v(" "),a("el-form-item",{attrs:{label:"活动内容：",prop:"remark"}},[a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",autosize:{minRows:4}},model:{value:e.form.remark,callback:function(l){e.$set(e.form,"remark",l)},expression:"form.remark"}})],1)],1),e._v(" "),a("div",{staticClass:"edit_buttons"},[a("el-button",{attrs:{type:"primary",loading:e.loading,size:"small"},on:{click:e.onSave}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{plain:"",size:"small"},on:{click:e.skip}},[e._v("返回")])],1)],1)},i=[],n=a("15fd"),r=a("c71b"),u=a("33a6"),o=a("7cea"),s=a("6dab"),c=["start_time","end_time"],v={components:{classifyDialog:u["a"]},data:function(){return{ruleForm:{},picLoad:!1,loading:!1,baseConfig:{labelWidth:"120px",inline:!1,inputWidth:"50%"},sucUpload:function(e,l,a){console.log(e,l,a)},tagsList:o["c"].map((function(e){return e.children=o["d"].filter((function(l){if(e.tag_group_id==l.tag_group_id)return l})),e})),dialogTagVisible:!1,formopts:{couponTypeOpt:r["k"],zoneOpt:r["T"]},rules:{topic_name:[{required:!0,message:"活动标题不能为空",trigger:"blur"}],start_time:[{required:!0,message:"开始时间不能为空",trigger:"change"}],end_time:[{required:!0,message:"结束时间不能为空",trigger:"change"}],remark:[{required:!0,message:"活动内容不能为空",trigger:"change"}]},form:{topic_type:"long_time",is_league:"0",temp:"1"},formConfig:[]}},methods:{uploadChange:function(e){var l=this;this.picLoad=!0;var a=e.target.files[0];Object(s["i"])({file:a}).then((function(e){var a=e.data.pic_path;l.form.topic_adv=a,l.picLoad=!1}))},onSave:function(){var e=this;this.$refs.formRef.validate((function(l){if(e.loading=!0,l){var a=e.form,t=a.start_time,i=a.end_time,r=Object(n["a"])(a,c);return r.start_time=new Date(t).getTime()/1e3,r.end_time=new Date(i).getTime()/1e3,void Object(s["a"])(r).then((function(l){e.loading=!1,e.$store.dispatch("delView",{path:e.$route.path}).then((function(l){l.visitedViews;e.skip()}))}))}e.loading=!0}))},skip:function(){this.$router.push("/market/platform/subject")}}},d=v,b=(a("33c9"),a("2877")),f=Object(b["a"])(d,t,i,!1,null,"5409cb9a",null);l["default"]=f.exports},bfab:function(e,l,a){},c16a:function(e,l,a){},c71b:function(e,l,a){"use strict";a.d(l,"a",(function(){return t})),a.d(l,"i",(function(){return i})),a.d(l,"H",(function(){return n})),a.d(l,"f",(function(){return r})),a.d(l,"A",(function(){return u})),a.d(l,"x",(function(){return o})),a.d(l,"e",(function(){return s})),a.d(l,"w",(function(){return c})),a.d(l,"c",(function(){return v})),a.d(l,"O",(function(){return d})),a.d(l,"j",(function(){return b})),a.d(l,"k",(function(){return f})),a.d(l,"l",(function(){return m})),a.d(l,"T",(function(){return p})),a.d(l,"d",(function(){return _})),a.d(l,"Q",(function(){return g})),a.d(l,"p",(function(){return h})),a.d(l,"P",(function(){return y})),a.d(l,"m",(function(){return k})),a.d(l,"I",(function(){return w})),a.d(l,"L",(function(){return C})),a.d(l,"N",(function(){return O})),a.d(l,"M",(function(){return j})),a.d(l,"S",(function(){return x})),a.d(l,"s",(function(){return $})),a.d(l,"B",(function(){return L})),a.d(l,"z",(function(){return T})),a.d(l,"K",(function(){return z})),a.d(l,"C",(function(){return I})),a.d(l,"h",(function(){return V})),a.d(l,"g",(function(){return S})),a.d(l,"o",(function(){return D})),a.d(l,"G",(function(){return P})),a.d(l,"J",(function(){return q})),a.d(l,"v",(function(){return E})),a.d(l,"F",(function(){return A})),a.d(l,"r",(function(){return N})),a.d(l,"b",(function(){return R})),a.d(l,"q",(function(){return G})),a.d(l,"R",(function(){return J})),a.d(l,"u",(function(){return B})),a.d(l,"t",(function(){return F})),a.d(l,"D",(function(){return U})),a.d(l,"E",(function(){return W})),a.d(l,"y",(function(){return H})),a.d(l,"n",(function(){return K}));var t=[{label:"全部",value:"-1"},{label:"免费店主",value:"0"},{label:"先迈店主",value:"1"},{label:"集市店主",value:"2"},{label:"卖场店主",value:"3"},{label:"商城店主",value:"4"},{label:"平台店主",value:"5"}],i=[{label:"全部",value:"-1"},{label:"弱绑定",value:"1"},{label:"强绑定",value:"2"}],n=[{label:"全部",value:""},{label:"正常",value:"1"},{label:"已过期",value:"0"},{label:"冻结",value:"2"}],r=[{label:"李雪含测试十一月六日",value:"366"},{label:"ceshi",value:"107"}],u=[{label:"是",code:1},{label:"否",code:0}],o=[{value:"",label:"全部"},{value:"3",label:"已发货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"}],s=[{value:"",label:"全部"},{value:"1",label:"待审核"},{value:"-1",label:"驳回申请"},{value:"-2",label:"已撤销"},{value:"3",label:"已退款"},{value:"4",label:"待退货"},{value:"5",label:"待收货"},{value:"6",label:"待退款"},{value:"9",label:"未收到退货"},{value:"10",label:"买家超时未发货"}],c=[{value:"all",label:"全部"},{value:"0",label:"待支付"},{value:"1",label:"待发货"},{value:"3",label:"已发货"},{value:"4",label:"已收货"},{value:"10",label:"已完成"},{value:"-1",label:"已关闭"},{value:"-2",label:"申请退款中"},{value:"-3",label:"已退款"},{value:"-4",label:"已换货"}],v=[{label:"全部",value:"0"},{label:"进行中",value:"1"},{label:"已结束",value:"2"},{label:"未开始",value:"3"}],d=[{label:"全部",value:""},{label:"全场通用",value:"1"},{label:"指定类目可用",value:"2"},{label:"指定商品可用",value:"3"},{label:"指定商品不可用",value:"4"}],b=[{label:"全部",value:"0"},{label:"已领取",value:"1"},{label:"已使用",value:"2"},{label:"已过期",value:"3"}],f=[{label:"全部",value:""},{label:"内部券",value:"1"},{label:"公开券",value:"0"}],m=[{label:"订单",value:"1"},{label:"直接领取",value:"2"},{label:"后台发放",value:"4"},{label:"规则自动派发",value:"5"}],p=[{label:"ceshi 123",value:"8"},{label:"APP赏金任务专区",value:"13"},{label:"哈哈",value:"65"},{label:"测试加盟",value:"70"},{label:"拼团专区",value:"71"},{label:"测试123456",value:"72"}],_=[{value:"0",label:"全部"},{value:"1",label:"未开始"},{value:"2",label:"进行中"},{value:"3",label:"已停止"}],g=[{value:"1",label:"普通用户"},{value:"2",label:"平台店主(VIP)"},{value:"3",label:"平台董事"},{value:"4",label:"平台经理"}],h=[{value:"1",label:"未领券"},{value:"2",label:"已领券"}],y=[{value:"1",label:"打开小程序"},{value:"2",label:"购买商品"},{value:"3",label:"无购买商品"}],k=[{value:"1",label:"按商品名称"},{value:"2",label:"按商品ID"}],w=[{value:"2",label:"进行中"},{value:"3",label:"已结束"}],C=[{value:"1",label:"进行中"},{value:"2",label:"已结束"}],O=[{value:"new",label:"新人团"},{value:"business",label:"拼商团"}],j=[{value:"1",label:"用户所支付的金额%"},{value:"2",label:"固定金额"}],x=[{value:"0",label:"未中奖"},{value:"1",label:"已中奖"}],$=[{value:"0",label:"参团"},{value:"1",label:"团长"}],L=[{value:"1",label:"拼团失败"},{value:"2",label:"组团中"},{value:"3",label:"拼团成功"}],T=[{value:"BALANCE",label:"余额支付"},{value:"adapay",label:"汇付天下-微信支付"}],z=[{value:"",label:"全部"},{value:"1",label:"上架中"},{value:"2",label:"已下架"},{value:"3",label:"已售罄"}],I=[{value:"",label:"全部"},{value:"0",label:"下架"},{value:"1",label:"上架"}],V=[{value:"",label:"全部"},{value:"0",label:"禁用"},{value:"1",label:"启用"}],S=[{label:"仅新用户可见（指无购买记录的用户）",value:"new_mem"},{label:"仅老用户可见",value:"old_mem"},{label:"仅分销商可见",value:"shop_mem"},{label:"指定标签用户可见",value:"tag_mem"},{label:"所有人可见",value:""}],D=[{value:"1",label:"是"},{value:"2",label:"否"}],P=[{label:"已下架",value:"0"},{label:"已上架",value:"1"}],q=[{label:"迈豆专区",value:"1"},{label:"信任专享",value:"2"},{label:"优选单品",value:"3"}],E=[{label:"待发货",vlaue:"1"},{label:"已关闭",vlaue:"2"}],A=[{label:"申请退款中",vlaue:"1"},{label:"已退款",vlaue:"2"},{label:"退款驳回",vlaue:"3"}],N=[{value:"0",label:"禁止购物"},{value:"1",label:"正常"}],R=[{value:"-1",label:"全部"},{value:"0",label:"已注销"},{value:"1",label:"正常"}],G=[{value:"-1",label:"全部"},{value:"0",label:"未匹配"},{value:"1",label:"已匹配"}],J=[{value:"1",label:"价格变动"},{value:"2",label:"商品下架"},{value:"3",label:"商品信息变动"}],B=[{label:"用户手机号",value:"mobile"},{label:"按用户ID",value:"member_id"}],F=[{label:"按订单编号",value:"order_no"}],U=[{label:"全部",value:""},{label:"待发货",value:"1"},{label:"已关闭",value:"-1"}],W=[{label:"全部",value:""},{label:"申请退款中",value:"1"},{label:"已退款",value:"3"},{label:"退款驳回",value:"-1"}],H=[{value:"adapay",label:"汇付天下"},{value:"newpay",label:"新生支付"},{value:"unionpay",label:"通联支付"},{value:"cpcnpay",label:"中金支付"}],K=[{value:"1",label:"立刻上架"},{value:"0",label:"暂不上架"}]}}]);