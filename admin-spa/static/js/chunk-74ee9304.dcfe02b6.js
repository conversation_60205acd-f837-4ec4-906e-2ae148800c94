(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74ee9304"],{"0431":function(e,t,n){"use strict";n("41e3")},"0fd5":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.loading,expression:"loading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{"element-loading-text":"拼命加载中","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[n("div",{staticClass:"details order"},[n("div",{staticClass:"info"},[n("div",{staticClass:"edit_title"},[e._v("退款订单信息")]),e._v(" "),n("div",[e._v("订单编号："+e._s(e.orderInfo.order_no))]),e._v(" "),n("div",[e._v("申请时间："+e._s(e._f("parseTime")(e.details.refund_action_time)))]),e._v(" "),n("div",[e._v("买家发货时间："+e._s(e._f("parseTime")(e.afterSaleInfo.buyer_send_time||0)))]),e._v(" "),n("div",[e._v("卖家收货时间："+e._s(e._f("parseTime")(e.afterSaleInfo.seller_receive_time||0)))]),e._v(" "),n("div",[e._v("退款单号："+e._s(e.details.refund_no))]),e._v(" "),n("div",[e._v("付款方式："+e._s(e.orderInfo.app_type_name)+"  "+e._s(e.orderInfo.pay_type_name))]),e._v(" "),n("div",[e._v("会员ID："+e._s(e.orderInfo.member_id))]),e._v(" "),n("div",[e._v("会员手机号："+e._s(e.orderInfo.member_mobile))]),e._v(" "),n("div",[e._v("店铺名称："+e._s(e.orderInfo.site_name))]),e._v(" "),n("div",[e._v("供应商："+e._s(e.orderInfo.supply_shop_name))])]),e._v(" "),n("div",{staticClass:"handle"},[n("div",[n("div",{staticClass:"edit_title"},[e._v("处理退款信息")]),e._v(" "),n("p",[e._v("售后状态："+e._s(e.details.refund_status_name)),n("span",[e._v(e._s(e.details.additional_status_name))])]),e._v(" "),e.details.goods_status_name?n("p",[e._v("商品状态："+e._s(e.details.goods_status_name))]):e._e(),e._v(" "),5==e.details.refund_status?n("div",{staticClass:"logistics"},[n("div",{staticClass:"edit_title"},[e._v("退货物流")]),e._v(" "),n("p",[e._v("快递公司："+e._s(e.details.refund_delivery_name))]),e._v(" "),n("p",[e._v("快递单号："+e._s(e.details.refund_delivery_no)+" "),n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.seeLogistics()}}},[e._v("查看物流")])],1),e._v(" "),e.afterSaleInfo.apply_end_time||e.afterSaleInfo.seller_not_received_time?n("p",{staticClass:"danger applyEnd"},[e._v("截止至 "+e._s(e._f("parseTime")(e.afterSaleInfo.apply_end_time||e.afterSaleInfo.seller_not_received_time||0))+" 默认同意")]):e._e()]):e._e(),e._v(" "),6==e.details.refund_status||11==e.details.refund_status||4==e.details.refund_type&&1==e.details.refund_status?n("el-button",{attrs:{type:"danger"},on:{click:function(t){e.dialogVisible=!0}}},[e._v("同意退款")]):1==e.details.refund_status&&2!=e.details.refund_type?n("div",{staticClass:"buttons"},[n("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.onReturnGoods(1)}}},[e._v("同意退货")]),e._v(" "),n("el-button",{attrs:{plain:""},on:{click:function(t){return e.onReturnGoods(2)}}},[e._v("驳回申请")])],1):5==e.details.refund_status?n("div",{staticClass:"buttons"},[n("el-button",{attrs:{type:"danger"},on:{click:e.onReturnGoods}},[e._v("接收退货")]),e._v(" "),n("el-button",{attrs:{plain:""},on:{click:e.onReturnGoods}},[e._v("未收到退货")])],1):e._e()],1),e._v(" "),e._m(0)])]),e._v(" "),n("div",{staticClass:"details goods_info"},[n("div",[n("div",{staticClass:"edit_title"},[e._v("售后商品")]),e._v(" "),n("div",{staticClass:"info"},[n("img",{attrs:{src:e.getImage(e.details.sku_image)}}),e._v(" "),n("div",[n("span",[e._v(e._s(e.details.sku_name))]),e._v(" "),n("span",[e._v("购买数量： "+e._s(e.details.num))]),e._v(" "),n("span",[e._v("退货数量： "+e._s(e.afterSaleInfo.refund_num))])])])]),e._v(" "),n("div",[n("div",{staticClass:"edit_title"},[e._v("售后信息")]),e._v(" "),n("div",{staticClass:"service"},[e._m(1),e._v(" "),n("p",[e._v("退款金额："),n("span",[e._v("￥"+e._s(e.details.refund_real_money))])]),e._v(" "),n("p",[e._v("联系方式："),n("i",[e._v(e._s(e.details.refund_phone))])]),e._v(" "),n("p",[e._v("退款原因："),n("i",[e._v(e._s(e.details.refund_reason))])]),e._v(" "),n("p",[e._v("补充描述："),n("i",[e._v(" "+e._s(e.details.refund_remark))])]),e._v(" "),n("span",[e._v("上传凭证："),e.afterSaleInfo.upload_vouchers?n("el-image",{attrs:{src:e.afterSaleInfo.upload_vouchers}}):e._e()],1),e._v(" "),n("p",[e._v("接收退货地址："),n("i",[e._v(e._s(e.details.refund_address))])])])]),e._v(" "),n("div",[n("div",{staticClass:"edit_title"},[e._v("购买信息")]),e._v(" "),n("div",{staticClass:"service"},[n("p",[e._v("商品单价："),n("span",[e._v("￥"+e._s(e.details.real_goods_money))]),e._v("x1件")]),e._v(" "),n("p",[e._v("分销商优惠："),n("span",[e._v("-￥"+e._s(e.details.promotion_money))])]),e._v(" "),n("p",[e._v("实付金额："),n("i",[e._v("￥"+e._s(e.details.real_pay_money))])]),e._v(" "),n("p",[e._v("配送状态："),n("i",[e._v(e._s(e.details.delivery_status_name)+" ")])]),e._v(" "),n("p",[e._v("订单编号："),n("el-link",{attrs:{type:"primary",underline:!1}},[e._v(e._s(e.details.order_no))])],1)])])]),e._v(" "),n("div",{staticClass:"details"},[n("div",{staticClass:"edit_title"},[e._v("维权日志")]),e._v(" "),n("div",{staticClass:"log"},e._l(e.details.refund_log_list,(function(t,i){return n("div",{key:i,class:{active:3!=t.action_way}},[n("span",[e._v(e._s(e.getLogs(t.action_way)))]),e._v(" "),n("div",[n("span",[e._v("操作人："+e._s(t.username))]),e._v(" "),n("div",[n("span",[e._v("操作内容："+e._s(t.action))]),e._v(" "),n("span",[e._v(e._s(e._f("parseTime")(t.action_time)))])])])])})),0)]),e._v(" "),n("el-dialog",{attrs:{title:"同意退款",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("div",{staticClass:"table-list"},[n("div",{staticClass:"btns"},[n("span",[e._v("问题商品")])]),e._v(" "),n("o-table",{staticClass:"o-table",attrs:{columns:e.columns,hideRefresh:!0,isPage:!1,data:[this.details]},scopedSlots:e._u([{key:"promotion_money",fn:function(t){var i=t.row;return[n("span",{staticClass:"danger"},[e._v("分销商优惠：￥"+e._s(i.promotion_money))])]}}])}),e._v(" "),n("formQuery",{attrs:{baseConfig:e.baseConfig,config:e.formConfig},model:{value:e.form,callback:function(t){e.form=t},expression:"form"}})],1),e._v(" "),n("span",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1,e.form={}}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary",loading:e.btnLoad},on:{click:e.onSubmit}},[e._v("确 定")])],1)]),e._v(" "),n("el-dialog",{attrs:{title:"物流信息",visible:e.logisticsVisible},on:{"update:visible":function(t){e.logisticsVisible=t}}},[n("div",{staticClass:"logistics-info"},[n("img",{attrs:{src:e.getImage(e.delivery.company&&e.delivery.company.logo)}}),e._v(" "),n("div",[n("span",[e._v("快递编号：12584525478852")]),e._v(" "),n("span",[e._v("快递公司：顺丰速运")])])]),e._v(" "),n("p",[e._v(e._s(e.delivery.reason))])]),e._v(" "),n("el-dialog",{attrs:{title:"同意退货",visible:e.returnVisible,width:"828px"},on:{"update:visible":function(t){e.returnVisible=t}}},[n("div",{staticClass:"receive"},[n("span",[e._v("请填写接收退货的收货地址信息")]),e._v(" "),n("el-form",{attrs:{"label-width":"160px"}},[n("el-form-item",{attrs:{label:"退换收件人："}},[n("el-input",{model:{value:e.returnForm.refund_name,callback:function(t){e.$set(e.returnForm,"refund_name",t)},expression:"returnForm.refund_name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"联系电话："}},[n("el-input",{model:{value:e.returnForm.refund_phone,callback:function(t){e.$set(e.returnForm,"refund_phone",t)},expression:"returnForm.refund_phone"}})],1),e._v(" "),n("el-form-item",{staticClass:"supplier",attrs:{label:"供应商地址："}},[n("el-select",{attrs:{placeholder:"省份"},model:{value:e.returnForm.province_id,callback:function(t){e.$set(e.returnForm,"province_id",t)},expression:"returnForm.province_id"}},e._l(e.provinceList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-select",{attrs:{placeholder:"市区"},model:{value:e.returnForm.city_id,callback:function(t){e.$set(e.returnForm,"city_id",t)},expression:"returnForm.city_id"}},e._l(e.cityList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-select",{attrs:{placeholder:"县"},model:{value:e.returnForm.district_id,callback:function(t){e.$set(e.returnForm,"district_id",t)},expression:"returnForm.district_id"}},e._l(e.areaList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1),e._v(" "),n("el-input",{staticClass:"address",attrs:{placeholder:"请输入详细地址，以方便买家联系"},model:{value:e.returnForm.supplier_address,callback:function(t){e.$set(e.returnForm,"supplier_address",t)},expression:"returnForm.supplier_address"}})],1)],1)],1),e._v(" "),n("span",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1,e.returnForm={}}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary",loading:e.btnLoad},on:{click:e.onComfirm}},[e._v("确 定")])],1)]),e._v(" "),n("remark",{ref:"remark",attrs:{title:"驳回申请",max:100},on:{setRemark:e.setRemark},model:{value:e.remark,callback:function(t){e.remark=t},expression:"remark"}})],1)},r=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tips"},[n("span",[e._v("温馨提醒")]),e._v(" "),n("p",[e._v("如果未发货，请点击同意退款给买家。")]),e._v(" "),n("p",[e._v("如果实际已发货，请主动与买家联系。")]),e._v(" "),n("p",[e._v("如果订单整体退款后，优惠券和余额会退还给买家。")])])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("p",[e._v("售后类型："),n("span",[e._v("退货退款")])])}],s=n("5530"),a=n("d2e6"),o=n("c57b"),d=n("b885"),l=n("e585"),u=(n("8975"),n("61e0")),_={data:function(){return{details:{},logsData:a["c"],dialogVisible:!1,loading:!1,columns:o["b"],list:[],form:{},afterSaleInfo:{},baseConfig:{labelWidth:"120px"},remark:"",returnForm:{},formConfig:[{type:"input",label:"退款金额（元）",model:"refund_money",method:"number",placeholder:"请输入退款金额（元）"},{type:"input",label:"运费（元）",model:"refund_delivery_money",method:"number",placeholder:"请输入运费（元）"}],orderInfo:{},btnLoad:!1,logisticsVisible:!1,delivery:{},returnVisible:!1,provinceList:{},cityList:{},areaList:{},supplyAddress:{}}},components:{FormQuery:d["d"],remark:u["a"]},mounted:function(){this.onInit(),this.getProvince()},methods:{getImage:function(e){return e?e.indexOf("http")>=0?e:"/api/".concat(e):""},getLogs:function(e){return 3==e?"平":2==e?"商":1==e?"买":void 0},seeLogistics:function(){var e=this;Object(l["k"])({delivery_no:this.details.refund_delivery_no,company_id:this.details.refund_delivery_company_id}).then((function(t){var n=t.data;e.logisticsVisible=!0,e.delivery=n}))},onComfirm:function(){var e=this;this.$confirm("提交后，商品将会退回到该地址，确定要提交吗？","信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){e.loading=!0;var t=e.$route.query.order_goods_id;Object(l["b"])(Object(s["a"])({order_goods_id:t},e.returnForm)).then((function(t){e.$message.success("同意退货成功"),e.returnVisible=!1,e.onInit()}))})).catch((function(){}))},setRemark:function(e){var t=this;this.$confirm("提交后，将驳回该订单商品的售后申请，确定要提交吗？","信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.loading=!0;var n=t.$route.query.order_goods_id;Object(l["b"])({order_goods_id:n,refund_refuse_reason:e}).then((function(e){t.$message.success("驳回申请成功"),t.onInit()}))})).catch((function(){}))},getProvince:function(){var e=this;Object(l["h"])().then((function(t){e.provinceList=t,e.cityList=[]}))},getCity:function(e){var t=this,n=e||this.orderInfo.province_id;Object(l["i"])({province_id:n}).then((function(n){t.cityList=n,e||(t.areaList=[])}))},getArea:function(e){var t=this,n=e||this.orderInfo.city_id;Object(l["j"])({city_id:n}).then((function(e){t.areaList=e}))},onReturnGoods:function(e){var t=this,n=this.$route.query.order_goods_id;Object(l["c"])({order_goods_id:n}).then((function(n){var i=n.code,r=n.message;if(i<0)t.$message.error(r);else{if(1==e){t.returnVisible=!0,t.getCity(),t.getArea();var s=t.orderInfo,a=s.city_id,o=s.district_id,d=s.province_id;t.returnForm={city_id:a,district_id:o,province_id:d,refund_name:t.supplyAddress.supplier_contact,refund_phone:t.supplyAddress.supplier_phone,supplier_address:t.supplyAddress.supplier_address}}2==e&&t.$refs.remark.init()}}))},onInit:function(){var e=this;this.loading=!0;var t=this.$route.query.order_goods_id;Object(l["o"])({order_goods_id:t}).then((function(t){var n=t.data,i=n.afterSaleInfo,r=n.detail,s=n.order_info,a=n.supplyAddress;e.details=r,e.orderInfo=s,e.afterSaleInfo=i||{},e.loading=!1,e.supplyAddress=a||{}}))},onSubmit:function(){var e=this;this.btnLoad=!0,this.$confirm("提交后，将会把问题商品的退款状态变更为【已退款】，并将退款金额".concat(this.form.refund_money,"元原路退回给支付账户，确定要提交吗？"),"信息",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){var t=e.$route.query.order_goods_id;Object(l["a"])(Object(s["a"])({order_goods_id:t,pay_type:e.orderInfo.pay_type},e.form)).then((function(t){var n=t.code,i=t.message;e.btnLoad=!1,n<0?e.$message.error(i):(e.$message.success("退款成功"),e.dialogVisible=!1,e.onInit())}))})).catch((function(){}))}}},c=_,v=(n("f719"),n("2877")),f=Object(v["a"])(c,i,r,!1,null,"1e1f2055",null);t["default"]=f.exports},"33c3":function(e,t,n){},"41e3":function(e,t,n){},"61e0":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{title:e.title,visible:e.storeDialog,"append-to-body":""},on:{"update:visible":function(t){e.storeDialog=t}}},[n("div",["驳回申请"==e.title?n("span",{staticClass:"reason danger"},[e._v("请填写驳回申请的原因")]):e._e(),e._v(" "),n("el-input",{attrs:{type:"textarea","show-word-limit":"",autosize:{minRows:6},placeholder:"备注内容",maxlength:e.max||"200"},on:{input:e.onInput},model:{value:e.remark,callback:function(t){e.remark=t},expression:"remark"}}),e._v(" "),n("p",[e._v("不超过"+e._s(e.max||200)+"个字")])],1),e._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.storeDialog=!1}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确 定")])],1)])},r=[],s={data:function(){return{remark:"",storeDialog:!1,info:{}}},props:["value","title","max"],methods:{init:function(e,t){this.info=e,this.remark=t,this.storeDialog=!0},onInput:function(e){this.$emit("input",e)},onSubmit:function(){var e=this;this.$alert("保存后该信息将同步更新到用户端订单详情中，请确定。","提示",{callback:function(t){if(e.remark)return e.$emit("setRemark",e.remark,e.info),void(e.storeDialog=!1);e.$message.error("商家备注内容不能为空")}})}}},a=s,o=(n("0431"),n("2877")),d=Object(o["a"])(a,i,r,!1,null,"b3b74f70",null);t["a"]=d.exports},e585:function(e,t,n){"use strict";n.d(t,"m",(function(){return r})),n.d(t,"g",(function(){return s})),n.d(t,"n",(function(){return a})),n.d(t,"p",(function(){return o})),n.d(t,"o",(function(){return d})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"k",(function(){return _})),n.d(t,"b",(function(){return c})),n.d(t,"h",(function(){return v})),n.d(t,"i",(function(){return f})),n.d(t,"j",(function(){return m})),n.d(t,"f",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"e",(function(){return g})),n.d(t,"d",(function(){return h})),n.d(t,"l",(function(){return y}));var i=n("b775");function r(e){return Object(i["a"])({url:"/admin/refund/refundList",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/admin/refund/getOrderChainStatus",method:"post",data:e})}function a(){return Object(i["a"])({url:"/admin/refund/refundReview",method:"post",data:data})}function o(e){return Object(i["a"])({url:"/admin/refund/returnRefundList",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/admin_plus/refund/returnRefundDetail",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/admin/refund/agreeReturnMoney.html",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/admin/refund/checkSupplyChainOrder.html",method:"post",data:e})}function _(e){return Object(i["a"])({url:"/admin/Express/logistics.html",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/admin/refund/agreeReturnRefundReview.html",method:"post",data:e})}function v(e){return Object(i["a"])({url:"/admin/address/getProvince.html",method:"post",data:e})}function f(e){return Object(i["a"])({url:"/admin/address/getcity.html",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/admin/address/getdistrict.html",method:"post",data:e})}function p(e){return Object(i["a"])({url:"/admin/express/expressCompany.html",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/admin/refund/sendAgain.html",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/admin/refund/exchangeGoodsList",method:"get",params:e})}function h(e){return Object(i["a"])({url:"/admin_plus/Refund/exchangeGoodsDetail",method:"post",data:e})}function y(e){return Object(i["a"])({url:"/admin_plus/refund/refundDetail",method:"post",data:e})}},f719:function(e,t,n){"use strict";n("33c3")}}]);