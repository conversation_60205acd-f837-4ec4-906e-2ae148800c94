(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-770e4a02"],{"2ade":function(t,e,n){"use strict";n("5a6c")},"5a6c":function(t,e,n){},9208:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"tabs"},t._l(t.tabs,(function(e,i){var a=e.title,s=e.code;return n("span",{key:i,class:{active:t.tabIndex==s},on:{click:function(e){return t.switchTabs(s)}}},[t._v(t._s(a))])})),0),t._v(" "),n("div",{staticClass:"cont"},[1==t.tabIndex?n("o-info"):t._e(),t._v(" "),2==t.tabIndex?n("o-order"):t._e()],1)])},a=[],s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}]},[n("div",{staticClass:"user-info"},[n("div",[n("span",[t._v("ID：")]),t._v("\n            "+t._s(t.userInfo.member_id)+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("头像：")]),t._v(" "),n("div",{staticClass:"thumb"},[n("img",{attrs:{src:t.userInfo.headimg,alt:""}})])]),t._v(" "),n("div",[n("span",[t._v("手机号码：")]),t._v("\n            "+t._s(t.userInfo.mobile)+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("微信昵称：")]),t._v("\n            "+t._s(t.userInfo.nickname)+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("注册时间：")]),t._v("\n            "+t._s(t._f("parseTime")(t.userInfo.reg_time))+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("标签：")]),t._v(" "),t.userInfo.tags&&t.userInfo.tags.length>0?n("div",t._l(t.userInfo.tags,(function(e,i){return n("span",{key:i},[t._v(t._s(e.tag_name))])})),0):n("div",[t._v("暂无标签")]),t._v(" "),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.editTags(!0,null)}}},[t._v("编辑")])],1),t._v(" "),n("div",[n("span",[t._v("可用迈豆：")]),t._v("\n            "+t._s(t.userInfo.canuse_maidou||0)+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("冻结迈豆：")]),t._v("\n            "+t._s(t.userInfo.nouser_maidou||0)+"\n        ")]),t._v(" "),n("div",[n("span",[t._v("推荐注册人：")]),t._v(" "),n("div",{staticClass:"recomm"},[n("div",[t._v("昵称："+t._s(t.userInfo.nickname))]),t._v(" "),n("div",[t._v("手机号："+t._s(t.userInfo.mobile))])])]),t._v(" "),n("div",[n("span",[t._v("状态：")]),t._v(" "),n("el-radio-group",{model:{value:t.userInfo.is_shopping_status,callback:function(e){t.$set(t.userInfo,"is_shopping_status",e)},expression:"userInfo.is_shopping_status"}},[n("el-radio",{attrs:{label:1}},[t._v("正常购物")]),t._v(" "),n("el-radio",{attrs:{label:0}},[t._v("禁止购物")])],1)],1),t._v(" "),n("div",[n("span",[t._v("当前锁定店铺：")]),t._v(" "),n("div",[t._v("\n                "+t._s(t.userInfo.site_name)+"\n            ")]),t._v(" "),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.getTableList()}}},[t._v("店铺记录")])],1),t._v(" "),n("div",[n("span"),t._v(" "),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleSave}},[t._v("保存")]),t._v(" "),n("el-button",{attrs:{plain:"",size:"small"},on:{click:function(e){return t.$router.back()}}},[t._v("返回")])],1)]),t._v(" "),n("el-dialog",{attrs:{title:"店铺记录",visible:t.dialogTableVisible},on:{"update:visible":function(e){t.dialogTableVisible=e}}},[n("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{options:t.options,columns:t.columns,data:t.list},on:{onSearch:t.getTableList},scopedSlots:t._u([{key:"lock_time",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(t._f("parseTime")(n.lock_time))+"\n            ")]}},{key:"unlock_time",fn:function(e){var n=e.row;return[t._v("\n                "+t._s(t._f("parseTime")(n.unlock_time))+"\n            ")]}}])})],1),t._v(" "),n("el-dialog",{attrs:{title:"选择标签",visible:t.dialogTagVisible},on:{"update:visible":function(e){t.dialogTagVisible=e}}},[n("el-form",{attrs:{"label-position":"top"}},t._l(t.tagsList,(function(e,i){var a=e.group_name,s=e.tag_group_id,o=e.tags;return n("el-form-item",{key:i,attrs:{label:a+":"}},[o.length>0?n("div",{staticClass:"tags"},t._l(o,(function(e,i){var a=e.tag_id,o=e.tag_name,r=e.isActive;return n("span",{class:{active:r},on:{click:function(e){return t.setLable(a,s)}}},[t._v(t._s(o))])})),0):n("div",[t._v("\n                    无标签\n                ")])])})),1),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"text"},on:{click:t.skip}},[t._v("标签管理")]),t._v(" "),n("el-button",{on:{click:function(e){t.dialogTagVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.successChoose}},[t._v("确 定")])],1)],1)],1)},o=[],r=n("c7eb"),c=(n("96cf"),n("1da1")),u=(n("7514"),n("15fd")),l=n("7cea"),d=n("7991"),_=n("5315"),v=(n("daba"),["total"]),f={data:function(){return{member_id:null,pageLoading:!1,userInfo:{tags:[]},tagsList:[],radio:0,dialogTableVisible:!1,dialogTagVisible:!1,list:[],loading:!1,options:{page:1,page_size:50,total:0},columns:d["c"]}},mounted:function(){this.onInit()},methods:{handleSave:function(){var t=this,e={member_id:this.userInfo.member_id,is_shopping_status:this.userInfo.is_shopping_status};Object(_["c"])(e).then((function(e){console.log(e),t.$message.success("更新资料成功")}))},getTableList:function(t){var e=this;this.loading=!0,t&&(this.options.page=t.page,this.options.page_size=t.page_size);var n=this.options,i=(n.total,Object(u["a"])(n,v));i.member_id=this.userInfo.member_id,Object(_["e"])(i).then((function(t){var n=t.data,i=n.count,a=n.list;e.list=a,e.options.total=i,e.loading=!1,e.dialogTableVisible=!0}))},getLabels:function(t){return l["d"].filter((function(e){if(t==e.tag_group_id)return e}))},setLable:function(t,e){this.tagsList.find((function(n,i){n.tag_group_id==e&&n.tags.find((function(e){e.tag_id==t&&(e.isActive=!e.isActive)}))})),this.$forceUpdate()},successChoose:function(){var t=Object(c["a"])(Object(r["a"])().mark((function t(){var e,n,i;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=[],this.tagsList.map((function(t){e=e.concat(t.tags.filter((function(t){return t.isActive})))})),this.userInfo.tags=e,this.dialogTagVisible=!1,this.pageLoading=!0,n={member_id:this.member_id,tag_ids:this.userInfo.tags.map((function(t){return t.tag_id})).join(",")},t.prev=6,t.next=9,Object(_["b"])(n);case 9:i=t.sent,this.pageLoading=!1,0==i.code?(this.$message.success("编辑成功"),this.onInit()):this.$message.error(i.message),t.next=16;break;case 14:t.prev=14,t.t0=t["catch"](6);case 16:case"end":return t.stop()}}),t,this,[[6,14]])})));function e(){return t.apply(this,arguments)}return e}(),onInit:function(){var t=Object(c["a"])(Object(r["a"])().mark((function t(){var e;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.member_id=this.$route.query.member_id||null,t.prev=1,t.next=4,Object(_["a"])({member_id:this.member_id});case 4:e=t.sent,0==e.code&&(this.userInfo=e.data.member_info.data,this.userInfo.tags=e.data.tags,this.editTags(!1,e.data.tags_data)),t.next=10;break;case 8:t.prev=8,t.t0=t["catch"](1);case 10:case"end":return t.stop()}}),t,this,[[1,8]])})));function e(){return t.apply(this,arguments)}return e}(),skip:function(){this.$router.push("/member/tagManage/tagValue")},editTags:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1?arguments[1]:void 0;n=n||this.tagsList,this.tagsList=n.map((function(e){return e.tags.map((function(e){return t.userInfo.tags.filter((function(t){return t.tag_id==e.tag_id})).length?e.isActive=!0:e.isActive=!1,e})),e})),this.dialogTagVisible=e}}},g=f,p=(n("b3d0"),n("2877")),m=Object(p["a"])(g,s,o,!1,null,"23dcb8bf",null),b=m.exports,h=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("o-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"o-table",attrs:{options:t.options,columns:t.columns,data:t.list},on:{onSearch:t.getTableList},scopedSlots:t._u([{key:"create_time",fn:function(e){var n=e.row;return[t._v("\n        "+t._s(t._f("parseTime")(n.create_time))+"\n    ")]}},{key:"action",fn:function(e){var i=e.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.toOrderDetail({id:i.order_id})}}},[t._v("详情")])]}}])})},I=[],k=["total"],O={data:function(){return{loading:!1,options:{pageNo:1,pageSize:10,total:l["b"].length},columns:d["b"],list:[],showSearch:!1}},mounted:function(){this.getTableList()},methods:{getTableList:function(t){var e=this;t&&(this.options.page=t.page,this.options.page_size=t.page_size);var n=this.options,i=(n.total,Object(u["a"])(n,k));this.list=[],i.member_id=this.$route.query.member_id,Object(_["g"])(i).then((function(t){var n=t.data,i=n.count,a=n.list;e.list=a,e.options.total=i,e.loading=!1}))},toOrderDetail:function(t){this.$router.push({path:"/order/orderDetails",query:t})}}},w=O,T=Object(p["a"])(w,h,I,!1,null,null,null),j=T.exports,x={components:{OInfo:b,OOrder:j},data:function(){return{tabIndex:1,tabs:[{title:"基础信息",code:1},{title:"订单管理",code:2}]}},methods:{switchTabs:function(t){this.tabIndex=t}}},y=x,L=(n("2ade"),Object(p["a"])(y,i,a,!1,null,"397caf96",null));e["default"]=L.exports},a461:function(t,e,n){},b3d0:function(t,e,n){"use strict";n("a461")},daba:function(t,e,n){"use strict";n.d(e,"c",(function(){return a})),n.d(e,"d",(function(){return s})),n.d(e,"e",(function(){return o})),n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return u}));var i=n("b775");function a(t){return Object(i["a"])({url:"/admin/config/award.html",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin_plus/config/award.html",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/admin/memberwithdraw/config.html",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/admin_plus/Memberwithdraw/config",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/admin_plus/Order/config.html",method:"post",data:t})}function u(t){return Object(i["a"])({url:"/admin/Order/config.html",method:"post",data:t})}}}]);