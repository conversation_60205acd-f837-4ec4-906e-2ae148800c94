// #ifdef H5
:root{
	--custom-brand-color:rgba(246, 93, 114, 1);
	--custom-brand-color-90:rgba(246, 93, 114, 0.9);
	--custom-brand-color-80:rgba(246, 93, 114, 0.8);
	--custom-brand-color-70:rgba(246, 93, 114, 0.7);
	--custom-brand-color-60:rgba(246, 93, 114, 0.6);
	--custom-brand-color-50:rgba(246, 93, 114, 0.5);
	--custom-brand-color-40:rgba(246, 93, 114, 0.4);
	--custom-brand-color-30:rgba(246, 93, 114, 0.3);
	--custom-brand-color-20:rgba(246, 93, 114, 0.2);
	--custom-brand-color-10:rgba(246, 93, 114, 0.1);
}
// #endif

// #ifdef MP-WEIXIN
page{
	--custom-brand-color:rgba(246, 93, 114, 1);
	--custom-brand-color-90:rgba(246, 93, 114, 0.9);
	--custom-brand-color-80:rgba(246, 93, 114, 0.8);
	--custom-brand-color-70:rgba(246, 93, 114, 0.7);
	--custom-brand-color-60:rgba(246, 93, 114, 0.6);
	--custom-brand-color-50:rgba(246, 93, 114, 0.5);
	--custom-brand-color-40:rgba(246, 93, 114, 0.4);
	--custom-brand-color-30:rgba(246, 93, 114, 0.3);
	--custom-brand-color-20:rgba(246, 93, 114, 0.2);
	--custom-brand-color-10:rgba(246, 93, 114, 0.1);
}
// #endif

//文字颜色
.ns-text-color {
	color: var(--custom-brand-color) !important;
}

//边框
.ns-border-color {
	border-color: var(--custom-brand-color) !important;
	&-top{
		border-top-color: var(--custom-brand-color) !important;
	}
	&-bottom{
		border-bottom-color: var(--custom-brand-color) !important;
	}
	&-right{
		border-right-color: var(--custom-brand-color) !important;
	}
	&-left{
		border-left-color: var(--custom-brand-color) !important;
	}
}

//背景色
.ns-bg-color {
	background-color: var(--custom-brand-color) !important;
}


//按钮
button {
	margin: 0 60rpx;
	font-size: $ns-font-size-base;
	border-radius: 20px;
	line-height: 2.7;
	&[type='primary'] {
		background-color: var(--custom-brand-color) !important;

		// &.button-hover {
		// 	background-color: darken(var(--custom-brand-color), 5%) !important;
		// }
		&[plain] {
			background-color: transparent !important;
			color: var(--custom-brand-color) !important;
			border-color: var(--custom-brand-color) !important;
		}
		&[disabled] {
			// color: rgba(0, 0, 0, 0.2) !important;
			// border-color: rgba(0, 0, 0, 0.2) !important;
			background: $ns-bg-color-gray !important;
			color: $ns-text-color-gray;
		}
		&.btn-disabled {
			background: $ns-bg-color-gray !important;
			color: $ns-text-color-gray !important;
		}
	}
	&.btn-disabled {
		background: $ns-bg-color-gray !important;
		color: $ns-text-color-gray !important;
	}
	&[type='warn'] {
		background: #ffffff;
		border: 1rpx solid var(--custom-brand-color) !important;
		color: var(--custom-brand-color);
		&[plain] {
			background-color: transparent !important;
			color: var(--custom-brand-color) !important;
			border-color: var(--custom-brand-color) !important;
		}
		&[disabled] {
			border: 1rpx solid $ns-border-color-gray !important;
			color: $ns-text-color-gray;
		}
		&.btn-disabled {
			border: 1rpx solid $ns-border-color-gray !important;
			color: $ns-text-color-gray;
		}
	}
	&[size='mini'] {
		margin: 0 !important;
	}
}

// 复选框
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
	color: var(--custom-brand-color) !important;
}

// 开关
uni-switch .uni-switch-input.uni-switch-input-checked {
	background-color: var(--custom-brand-color) !important;
	border-color: var(--custom-brand-color) !important;
}

// 单选
uni-radio .uni-radio-input-checked {
	background-color: var(--custom-brand-color) !important;
	border-color: var(--custom-brand-color) !important;
}

// 滑块
uni-slider .uni-slider-track {
	background-color: var(--custom-brand-color) !important;
}

.uni-tag--primary {
	color: #fff !important;
	background-color: var(--custom-brand-color) !important;
	border-color: var(--custom-brand-color) !important;
}

.uni-tag--primary.uni-tag--inverted {
	color: var(--custom-brand-color) !important;
	background-color: #fff !important;
	border-color: var(--custom-brand-color) !important;
}

//商品详情，优惠券弹出层，项
.goods-coupon-popup-layer .coupon-body .item {
	background-color: var(--custom-brand-color-40) !important;
	view {
		color: var(--custom-brand-color-10) !important;
	}
}

// 商品详情，sku选中
.sku-layer .body-item .sku-list-wrap {
	.items {
		background-color: #f5f5f5 !important;
		&.selected {
			background: var(--custom-brand-color)!important;
			color: #fff !important;
			border-color:var(--custom-brand-color) !important;
		}
		&.disabled {
			color: #898989 !important;
			cursor: not-allowed !important;
			pointer-events: none !important;
			opacity: 0.5 !important;
			box-shadow: none !important;
			filter: grayscale(100%);
		}
	}
}

// 商品详情，限时折扣
.goods-detail .goods-discount {
	background: linear-gradient(to bottom, #fef391, #fbe253);
	.price-info {
		background: linear-gradient(to right, var(--custom-brand-color), var(--custom-brand-color-10)) !important;
	}
}

// 秒杀商品详情
.goods-detail .seckill-wrap {
	background: linear-gradient(to right, var(--custom-brand-color), var(--custom-brand-color-20)) !important;
}

.goods-detail .goods-module-wrap .original-price .seckill-save-price {
	background: var(--custom-brand-color-40) !important;
	color: var(--custom-brand-color) !important;
}

// 拼团商品详情
.goods-detail .goods-pintuan {
	background: linear-gradient(to bottom, #fef391, #fbe253);
	.price-info {
		background: linear-gradient(to right, var(--custom-brand-color), var(--custom-brand-color-10)) !important;
	}
}

// 专题商品详情
.goods-detail .topic-wrap {
	background: linear-gradient(to right, var(--custom-brand-color), var(--custom-brand-color-30)) !important;
}
.goods-detail .goods-module-wrap .original-price .topic-save-price {
	background: var(--custom-brand-color-40) !important;
	color: var(--custom-brand-color) !important;
}

// 团购商品详情
.goods-detail .goods-groupbuy {
	background: linear-gradient(to bottom, #fef391, #fbe253);
	.price-info {
		background: linear-gradient(to right, var(--custom-brand-color), var(--custom-brand-color-10)) !important;
	}
}

//团购列表颜色渐变
.gradual-change {
	background: linear-gradient(45deg, var(--custom-brand-color-100), var(--custom-brand-color-60)) !important;
}

//测试统一按钮
.ns-btn-default-all {
	width: 100%;
	height: 70rpx;
	background: var(--custom-brand-color);
	border-radius: 70rpx;
	text-align: center;
	line-height: 70rpx;
	color: #ffffff;
	font-size: $ns-font-size-base;
}
.ns-btn-default-all.gray {
	background: $ns-bg-color-gray;
	color: $ns-text-color-gray;
}
.ns-btn-default-all.free {
	width: 100%;
	background: #ffffff;
	color: var(--custom-brand-color);
	border: 1rpx solid var(--custom-brand-color);
	font-size: $ns-font-size-base;
	box-sizing: border-box;
}
.ns-btn-default-all.free.gray {
	background: #ffffff;
	color: $ns-text-color-gray;
	border: 1rpx solid $ns-border-color-gray;
}
.ns-btn-default-mine {
	display: inline-block;
	height: 60rpx;
	border-radius: 60rpx;
	line-height: 60rpx;
	padding: 0 30rpx;
	box-sizing: border-box;
	color: #ffffff;
	background: var(--custom-brand-color);
}
.ns-btn-default-mine.gray {
	background: $ns-bg-color-gray;
	color: $ns-text-color-gray;
}
.ns-btn-default-mine.free {
	background: #ffffff;
	color: var(--custom-brand-color);
	border: 1rpx solid var(--custom-brand-color);
	font-size: $ns-font-size-base;
	box-sizing: border-box;
}
.ns-btn-default-mine.free.gray {
	background: #ffffff;
	color: $ns-text-color-gray;
	border: 1rpx solid $ns-border-color-gray;
}

// //订单列表按钮样式
.order-box-btn {
	display: inline-block;
	line-height: 56rpx;
	padding: 0 30rpx;
	font-size: $ns-font-size-base;
	color: $ns-text-color-black;
	border: 1rpx solid #999;
	box-sizing: border-box;
	border-radius: 60rpx;
	margin-left: $ns-margin;
	&.order-pay {
		background: var(--custom-brand-color);
		color: #fff;
		border-color: #fff;
	}
}
	.ns-text-before::after,.ns-text-before::before{
			color: var(--custom-brand-color) !important;
		}
	.ns-bg-before::after{
		background: var(--custom-brand-color) !important;
	}
	.ns-bg-before::before{
		background: var(--custom-brand-color) !important;
	}

