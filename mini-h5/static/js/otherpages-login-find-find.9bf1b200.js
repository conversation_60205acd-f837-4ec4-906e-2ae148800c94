(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-login-find-find"],{2866:function(e,t,r){"use strict";var a=r("4701"),n=r.n(a);n.a},4701:function(e,t,r){var a=r("c84b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=r("967d").default;n("52d7f89a",a,!0,{sourceMap:!1,shadowMode:!1})},"80b3":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{class:e.themeStyle},[r("v-uni-view",{staticClass:"find"},[r("v-uni-view",{staticClass:"find-form"},[r("v-uni-view",{staticClass:"form-input"},[r("v-uni-text",{staticClass:"form-input-label"},[e._v("手机号")]),r("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",maxlength:"17",placeholder:e.$lang("accountPlaceholder")},model:{value:e.formData.mobileOrEmail,callback:function(t){e.$set(e.formData,"mobileOrEmail",t)},expression:"formData.mobileOrEmail"}})],1),r("v-uni-view",{staticClass:"form-input align-type"},[r("v-uni-text",{staticClass:"form-input-label"},[e._v("验证码")]),r("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",maxlength:"4",placeholder:e.$lang("captchaPlaceholder")},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}}),r("v-uni-view",{staticClass:"dynacode ns-text-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendDynaCode.apply(void 0,arguments)}}},[e._v(e._s(e.codeText))])],1),r("v-uni-view",{staticClass:"form-input"},[r("v-uni-text",{staticClass:"form-input-label"},[e._v("新密码")]),r("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",maxlength:"30",password:"true",placeholder:e.$lang("passwordPlaceholder")},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1),r("v-uni-view",{staticClass:"form-input"},[r("v-uni-text",{staticClass:"form-input-label"},[e._v("确认密码")]),r("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",maxlength:"30",password:"true",placeholder:e.$lang("rePasswordPlaceholder")},model:{value:e.formData.rePassword,callback:function(t){e.$set(e.formData,"rePassword",t)},expression:"formData.rePassword"}})],1),r("v-uni-button",{staticClass:"find-btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save.apply(void 0,arguments)}}},[e._v(e._s(e.$lang("save")))])],1)],1)],1)},n=[]},8469:function(e,t,r){r("23f4"),r("7d2f"),r("5c47"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){for(var r=0;r<t.length;r++){if(!t[r].checkType)return!0;if(!t[r].name)return!0;if(!t[r].errorMsg)return!0;if(!e[t[r].name])return this.error=t[r].errorMsg,!1;switch(t[r].checkType){case"custom":if("function"==typeof t[r].validate&&!t[r].validate(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"string":a=new RegExp("^.{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[r].name]))return this.error=t[r].errorMsg,!1;var n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"same":if(e[t[r].name]!=t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"notsame":if(e[t[r].name]==t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"phoneno":a=/^[1](([3][0-9])|([4][1,4-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"reg":a=new RegExp(t[r].checkRule);if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"in":if(-1==t[r].checkRule.indexOf(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"notnull":if(0==e[t[r].name]||void 0==e[t[r].name]||null==e[t[r].name]||e[t[r].name].length<1)return this.error=t[r].errorMsg,!1;break;case"lengthMin":if(e[t[r].name].length<t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"lengthMax":if(e[t[r].name].length>t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"bank_account":a=/^([1-9]{1})(\d{15}|\d{18})$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"idCard":a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},a4bd:function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("2634")),i=a(r("2fdc"));r("aa9c"),r("5ef2"),r("5c47"),r("a1c1");var s=a(r("8469")),o=a(r("7c8d")),c={data:function(){return{findMode:"mobile",codeText:"获取验证码",seconds:120,timer:null,formData:{mobileOrEmail:"",password:"",rePassword:"",dynacode:"",captcha:""},stepShow:0,isSend:!1,captcha:{id:"",img:""},registerConfig:{}}},onLoad:function(){},onShow:function(){this.$langConfig.refresh(),this.getRegisterConfig()},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},methods:{navigateBack:function(){1==this.stepShow?this.stepShow=0:0==this.stepShow&&this.$util.redirectTo("/pages/login/login/login","","","reLaunch")},vertify:function(){var e=this.registerConfig,t=[{name:"mobileOrEmail",checkType:"required",errorMsg:"请输入手机号码"},{name:"dynacode",checkType:"required",errorMsg:this.$lang("dynacodePlaceholder")},{name:"password",checkType:"required",errorMsg:"请输入密码"}];if(e.pwd_len>0&&t.push({name:"password",checkType:"lengthMin",checkRule:e.pwd_len,errorMsg:"密码长度不能小于"+e.pwd_len+"位"}),""!=e.pwd_complexity){var r="密码需包含",a="";-1!=e.pwd_complexity.indexOf("number")&&(a+="(?=.*?[0-9])",r+="数字"),-1!=e.pwd_complexity.indexOf("letter")&&(a+="(?=.*?[a-z])",r+="、小写字母"),-1!=e.pwd_complexity.indexOf("upper_case")&&(a+="(?=.*?[A-Z])",r+="、大写字母"),-1!=e.pwd_complexity.indexOf("symbol")&&(a+="(?=.*?[#?!@$%^&*-])",r+="、特殊字符"),t.push({name:"password",checkType:"reg",checkRule:a,errorMsg:r})}var n=s.default.check(this.formData,t);return n?this.formData.password==this.formData.rePassword||(this.$util.showToast({title:"两次密码不一致"}),!1):(this.$util.showToast({title:s.default.error}),!1)},getCaptcha:function(){var e=this;this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},sendDynaCode:function(){var e=this;return(0,i.default)((0,n.default)().mark((function t(){var r,a;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isSend){t.next=2;break}return t.abrupt("return");case 2:e.isSend=!0,r=o.default.sendMobileCodeUrl,a={type:10},a[e.findMode]=e.formData.mobileOrEmail,e.$api.sendRequest({url:r,data:a,success:function(t){t.code>=0?120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.codeText="已发送("+e.seconds+"s)"}),1e3),e.$util.showToast({title:"发送验证码成功"})):(e.$util.showToast({title:t.message}),e.isSend=!1)},fail:function(t){e.isSend=!1}});case 6:case"end":return t.stop()}}),t)})))()},save:function(){var e=this;if(this.vertify()){var t=o.default.retrievePasswordUrl,r={code:this.formData.dynacode,password:this.formData.password,rePassword:this.formData.rePassword};r[this.findMode]=this.formData.mobileOrEmail,uni.showLoading({title:"保存中..."}),this.$api.sendRequest({url:t,data:r,success:function(){var t=(0,i.default)((0,n.default)().mark((function t(r){return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(uni.hideLoading(),-10010!=r.code){t.next=8;break}return e.isShow=!1,uni.showModal({title:"提示",content:r.message,showCancel:!1}),t.next=6,e.$util.clearUserInfo();case 6:t.next=9;break;case 8:0==r.code?(e.$util.showToast({title:r.message}),setTimeout((function(){e.$util.redirectTo("/pages/login/login/login",{},"redirectTo")}),1e3)):(e.isSend=!1,e.$util.showToast({title:r.message}));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})}},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value)}})}},watch:{seconds:function(e){0==e&&(this.seconds=120,this.codeText="获取验证码",this.isSend=!1,clearInterval(this.timer))}}};t.default=c},b88d:function(e,t,r){"use strict";r.r(t);var a=r("80b3"),n=r("f669");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("2866");var s=r("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"50e084f5",null,!1,a["a"],void 0);t["default"]=o.exports},c84b:function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-50e084f5]{width:100%;text-align:center}uni-page-body[data-v-50e084f5]{background:#fff!important}body.?%PAGE?%[data-v-50e084f5]{background:#fff!important}.captcha[data-v-50e084f5]{width:%?170?%;height:%?50?%}.find-head[data-v-50e084f5]{\r\n  /* 自定义导航 */}.find-head .head-nav[data-v-50e084f5]{width:100%;height:0}.find-head .head-content[data-v-50e084f5]{position:relative;width:%?750?%;height:%?540?%;background-size:contain}.find-head .head-content .head-return[data-v-50e084f5]{padding-left:%?30?%;height:%?90?%;line-height:%?90?%;color:#fff;font-size:%?32?%}.find-head .head-content .head-return uni-text[data-v-50e084f5]{display:inline-block;margin-right:%?10?%}.find-form[data-v-50e084f5]{padding:%?0?% %?40?% 0}.find-form .form-input[data-v-50e084f5]{margin-top:%?60?%;height:%?60?%;border-bottom:%?2?% solid #e5e5e5;display:flex;align-items:center}.find-form .form-input-label[data-v-50e084f5]{font-size:%?32?%;font-weight:500;color:#333;width:%?150?%}.find-form .form-input uni-input[data-v-50e084f5]{padding:0;font-size:%?28?%}.find-form .find-btn[data-v-50e084f5]{margin:%?100?% 0 0;border-radius:%?40?%;color:#fff}.forget-section[data-v-50e084f5]{display:flex;flex-direction:row-reverse;justify-content:space-between;margin-top:%?10?%;height:%?70?%;line-height:%?70?%}.align-type[data-v-50e084f5]{display:flex}',""]),e.exports=t},f669:function(e,t,r){"use strict";r.r(t);var a=r("a4bd"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a}}]);