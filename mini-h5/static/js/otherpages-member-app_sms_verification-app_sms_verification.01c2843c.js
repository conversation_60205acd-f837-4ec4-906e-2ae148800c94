(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-app_sms_verification-app_sms_verification"],{"11d4":function(t,e,n){var i=n("299a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("da3cc4c2",i,!0,{sourceMap:!1,shadowMode:!1})},"299a":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-4999a761]{width:100%;text-align:center}@-webkit-keyframes rotate-data-v-4999a761{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotate-data-v-4999a761{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.main[data-v-4999a761]{width:100%;height:100vh;overflow-y:scroll;-webkit-overflow-scrolling:touch;background-color:var(--custom-brand-color)}.main-content[data-v-4999a761]{width:100%;height:100%;position:relative;box-sizing:border-box}.main-content-img[data-v-4999a761]{width:%?394?%;height:%?276?%;position:absolute;left:%?302?%;top:%?0?%}.main-content-title[data-v-4999a761]{font-size:%?46?%;font-weight:400;letter-spacing:0;line-height:%?55.2?%;color:#fff;padding-left:%?56?%;box-sizing:border-box}.main-content-subtitle[data-v-4999a761]{font-size:%?24?%;font-weight:400;letter-spacing:0;line-height:%?40?%;color:#fff;margin-top:%?10?%;padding-left:%?56?%;box-sizing:border-box}.main-content-box[data-v-4999a761]{width:%?690?%;margin:0 auto;margin-top:%?50?%;background-color:#fff;position:relative;z-index:2;border-radius:%?18?%;padding:%?38?% %?24?% %?76?% %?24?%;box-sizing:border-box}.main-content-box-text[data-v-4999a761]{font-size:%?28?%;font-weight:400;letter-spacing:0;line-height:%?40?%;color:#383838;margin-bottom:%?10?%}.main-content-box-tip[data-v-4999a761]{font-size:%?28?%;font-weight:400;letter-spacing:0;line-height:%?36?%;color:var(--custom-brand-color);padding-bottom:%?22?%;box-sizing:border-box;border-bottom:%?2?% solid #f5f5f5}.main-content-box-number[data-v-4999a761]{display:flex;justify-content:space-around;align-items:center;margin-top:%?98?%}.main-content-box-number-one[data-v-4999a761]{width:%?92?%;height:%?92?%;border-radius:50%;border:%?4?% dashed #e5e5e5;display:flex;justify-content:center;align-items:center;font-size:%?36?%;font-weight:400;letter-spacing:0;color:#000}.main-content-box-op[data-v-4999a761]{width:%?630?%;height:%?80?%;border-radius:%?40?%;background:var(--custom-brand-color);margin:0 auto;margin-top:%?98?%;font-size:%?28?%;font-weight:400;letter-spacing:0;color:#fff;display:flex;justify-content:center;align-items:center;position:relative}.main-content-box-op-icon[data-v-4999a761]{position:absolute;top:%?-32?%;right:%?16?%}.main-content-box-op-icon-animation[data-v-4999a761]{-webkit-animation:rotate-data-v-4999a761 1s linear infinite;animation:rotate-data-v-4999a761 1s linear infinite}.main-content-box-back[data-v-4999a761]{width:%?630?%;height:%?80?%;border-radius:%?40?%;background:#fff;border:%?2?% solid var(--custom-brand-color);margin:0 auto;margin-top:%?52?%;font-size:%?28?%;font-weight:400;letter-spacing:0;line-height:%?40?%;color:var(--custom-brand-color);display:flex;justify-content:center;align-items:center}[data-v-4999a761] .uni-navbar{position:fixed;z-index:999;left:0;top:0}[data-v-4999a761] .uni-navbar__header-btns-left{width:auto!important;padding-left:%?52?%!important;box-sizing:border-box}',""]),t.exports=e},"2a00":function(t,e,n){"use strict";var i=n("11d4"),a=n.n(i);a.a},"2d01":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},3094:function(t,e,n){"use strict";n.r(e);var i=n("f005"),a=n("58f6");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("2a00");var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"4999a761",null,!1,i["a"],void 0);e["default"]=r.exports},"58f6":function(t,e,n){"use strict";n.r(e);var i=n("63ce"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"63ce":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),o=i(n("5de6")),s=i(n("2fdc")),r=i(n("2d01")),c=i(n("d817")),d=i(n("e77e")),u=i(n("7c8d")),l=i(n("de74")),m={components:{UniIcons:l.default,UniNavBar:c.default},mixins:[r.default],name:"app_sms_verification",data:function(){return{nav_height:0,paddingTop:0,imgTop:0,sms_code:["","","",""],is_timer:!1,timer_obj:null,init_time:60,time_count:0,sms_phone:"",is_send_api:!1}},onLoad:function(){this.nav_height=44+uni.getSystemInfoSync()["statusBarHeight"],this.paddingTop="calc(".concat(this.nav_height,"px + 50rpx)"),this.imgTop="calc(".concat(this.nav_height,"px)")},onShow:function(){this.$langConfig.refresh()},onUnload:function(){clearInterval(this.timer_obj)},methods:{smsTip:function(){this.is_timer&&this.$util.showToast({title:"请不要频繁操作 （".concat(this.time_count,"S）")})},getphonenumber:function(t){var e=this;return(0,s.default)((0,a.default)().mark((function n(){var i,s,r,c,l,m,f;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,d.default.getPhoneNumber(t);case 2:return i=n.sent,s=(0,o.default)(i,2),r=s[0],c=s[1],l=c.encryptedData,m=c.iv,uni.showLoading({title:"加载中",mask:!0}),e.is_send_api=!0,n.prev=10,n.next=13,e.$api.sendRequest({url:u.default.getAuthCodeUrl,async:!1,data:{code:r,encrypteData:l,iv:m}});case 13:f=n.sent,uni.hideLoading(),e.is_send_api=!1,0!=f.code?e.$util.showToast({title:f.message}):1!=f.data.is_success?e.$util.showToast({title:f.data.re_msg.message}):(e.sms_code=f.data.send_code.split(""),e.sms_phone=f.data.mobile,e.sendSms()),n.next=21;break;case 19:n.prev=19,n.t0=n["catch"](10);case 21:case"end":return n.stop()}}),n,null,[[10,19]])})))()},sendSms:function(){var t=this;this.is_timer?this.$util.showToast({title:"请不要频繁操作 （".concat(this.time_count,"S）")}):(this.is_timer=!0,this.time_count=this.init_time,this.$util.copy(this.sms_code.join(""),(function(){t.$util.showToast({title:"已复制验证码"})})),this.timer_obj=setInterval((function(){t.time_count--,t.time_count<=0&&(clearInterval(t.timer_obj),t.is_timer=!1,t.time_count=t.init_time)}),1e3))}}};e.default=m},f005:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uniNavBar:n("d817").default,uniIcons:n("de74").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"main",class:t.themeStyle,style:[t.themeColorVar]},[n("v-uni-view",{staticClass:"main-content",style:{paddingTop:t.paddingTop}},[n("uni-nav-bar",{attrs:{border:!1,fixed:!0,"status-bar":!0,"background-color":"transparent",color:"#fff","left-text":"先迈网短信验证"}}),n("v-uni-image",{staticClass:"main-content-img",style:{top:t.imgTop},attrs:{src:t.$util.img("public/static/youpin/member/ren.png")}}),n("v-uni-view",{staticClass:"main-content-title"},[t._v("上先迈 超划算")]),n("v-uni-view",{staticClass:"main-content-subtitle"},[t._v("千万臻选，好物不停")]),n("v-uni-view",{staticClass:"main-content-box"},[n("v-uni-view",{staticClass:"main-content-box-text"},[t._v("授权手机号获取验证码，验证码5分钟内有效，失效可重新获取。")]),n("v-uni-view",{staticClass:"main-content-box-tip"},[t._v("注意：授权的手机号与APP端注册或登陆的手机号需保持一致，否则验证码无法使用。")]),n("v-uni-view",{staticClass:"main-content-box-number"},t._l(t.sms_code,(function(e,i){return n("v-uni-view",{key:i,staticClass:"main-content-box-number-one"},[t._v(t._s(e||"?"))])})),1),t.is_timer?n("v-uni-view",{staticClass:"main-content-box-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.smsTip.apply(void 0,arguments)}}},[t._v(t._s("授权手机号："+t.sms_phone)),n("uni-icons",{staticClass:"main-content-box-op-icon",class:{"main-content-box-op-icon-animation":t.is_send_api},style:{top:"-6rpx"},attrs:{type:"refreshempty",size:"28",color:"#fff"}})],1):n("v-uni-button",{staticClass:"main-content-box-op",attrs:{"open-type":"getPhoneNumber"},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)}}},[t._v(t._s(t.sms_phone?"授权手机号："+t.sms_phone:"授权手机号")),t.sms_phone?n("uni-icons",{staticClass:"main-content-box-op-icon",class:{"main-content-box-op-icon-animation":t.is_send_api},attrs:{type:"refreshempty",size:"28",color:"#fff"}}):t._e()],1),n("v-uni-button",{staticClass:"main-content-box-back",attrs:{"open-type":"launchApp","app-parameter":"wechat",binderror:"launchAppError"}},[t._v(t._s(t.sms_code.filter((function(t){return t})).length?"复制验证码，并返回先迈网APP":"返回先迈网APP端"))])],1)],1)],1)},o=[]}}]);