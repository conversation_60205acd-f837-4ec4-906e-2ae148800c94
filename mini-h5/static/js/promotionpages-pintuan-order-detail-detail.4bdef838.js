(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-pintuan-order-detail-detail"],{"0149":function(t,a,e){"use strict";var i=e("3149"),o=e.n(i);o.a},"016e":function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var o=i(e("39d8")),n=i(e("2634")),r=i(e("2fdc")),s=(i(e("cbf3")),{methods:{orderClose:function(t,a){var e=this,i=t.out_trade_no;uni.showModal({title:"提示",content:"确定要取消该订单吗？",cancelText:"我再想想",confirmText:"确定",success:function(o){o.confirm&&e.$api.sendRequest({url:"/api/PintuanOrder/close",data:{id:t.id},success:function(t){if(t.code>=0)e.createBuriedPoint("out_trade_no",i,250),e.$util.showToast({title:"取消订单成功！",success:function(){"function"==typeof a&&a()}});else{if(-11==t.code)return e.$util.showToast({title:t.message}),setTimeout((function(){"function"==typeof a&&a()}),1500),!1;e.$util.showToast({title:t.message})}}})}})},orderPay:function(t,a){var e=this;return(0,r.default)((0,n.default)().mark((function i(){var o;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return o="",o=t.group_id?"join_pintuan_before":"open_pintuan_before",i.prev=2,i.next=5,e.$util.subscribeMessage({source:"out_trade_no",source_id:t.out_trade_no,scene_type:o});case 5:i.next=9;break;case 7:i.prev=7,i.t0=i["catch"](2);case 9:e.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:t.out_trade_no,pay_type:"adapay"},success:function(i){if(i.code>=0)uni.hideLoading(),e.$util.wechatPay(i.data.pay_type,"adapay"==i.data.pay_type?i.data.payment:i.data.pay_info,(function(i){e.createBuriedPoint("out_trade_no",t.out_trade_no,11),"function"==typeof a&&a()}),(function(a){e.createBuriedPoint("out_trade_no",t.out_trade_no,9001),uni.hideLoading()}),(function(t){uni.hideLoading()}),e);else{if(-11==i.code)return e.$util.showToast({title:i.message}),setTimeout((function(){"function"==typeof a&&a()}),1500),!1;i.message?e.$util.showToast({title:i.message}):uni.hideLoading()}},fail:function(t){e.$util.showToast({title:"request:fail"})}});case 10:case"end":return i.stop()}}),i,null,[[2,7]])})))()},createBuriedPoint:function(t,a,e){var i;this.$buriedPoint.orderStatus((i={},(0,o.default)(i,t,a),(0,o.default)(i,"status",e),(0,o.default)(i,"orderType",!0),i))}}});a.default=s},"0954":function(t,a,e){var i=e("add2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("35a46fb8",i,!0,{sourceMap:!1,shadowMode:!1})},"0b48":function(t,a,e){"use strict";e.r(a);var i=e("b5ce"),o=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=o.a},1097:function(t,a,e){"use strict";e.r(a);var i=e("b8bc"),o=e("73c9");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(n);e("70cc"),e("6420"),e("c604");var r=e("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"7b0ba466",null,!1,i["a"],void 0);a["default"]=s.exports},"2d01":function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;a.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2e91":function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-46873122]{width:100%;text-align:center}.yd-popup[data-v-46873122]{background:rgba(0,0,0,.4);width:100%;height:100%;z-index:998;position:fixed;top:0;left:0}.yd-popup .share-tip[data-v-46873122]{width:100%;height:%?447?%;display:block}',""]),t.exports=a},"2f73":function(t,a,e){"use strict";e.r(a);var i=e("e71a"),o=e("0b48");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(n);e("0149");var r=e("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"46873122",null,!1,i["a"],void 0);a["default"]=s.exports},3149:function(t,a,e){var i=e("2e91");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("31685291",i,!0,{sourceMap:!1,shadowMode:!1})},"31c3":function(t,a,e){"use strict";e.r(a);var i=e("ae57"),o=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=o.a},3745:function(t,a,e){"use strict";var i=e("764d"),o=e.n(i);o.a},5552:function(t,a,e){var i=e("7a28");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("3cc69f81",i,!0,{sourceMap:!1,shadowMode:!1})},"5ab1":function(t,a,e){"use strict";e.r(a);var i=e("78f85"),o=e("fe2a");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(n);e("3745");var r=e("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"d6071282",null,!1,i["a"],void 0);a["default"]=s.exports},6024:function(t,a,e){var i=e("7e30");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("7de6e9c3",i,!0,{sourceMap:!1,shadowMode:!1})},6420:function(t,a,e){"use strict";var i=e("0954"),o=e.n(i);o.a},"70cc":function(t,a,e){"use strict";var i=e("6024"),o=e.n(i);o.a},"73c9":function(t,a,e){"use strict";e.r(a);var i=e("945b"),o=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=o.a},"764d":function(t,a,e){var i=e("b81d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("36126566",i,!0,{sourceMap:!1,shadowMode:!1})},"78f85":function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),e("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),e("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),e("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"7a28":function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7b0ba466]{width:100%;text-align:center}.my-popup[data-v-7b0ba466]{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:2;display:flex;align-items:center;justify-content:center}.popup-mask[data-v-7b0ba466]{position:absolute;top:0;left:0;width:100%;height:100vh;background-color:rgba(0,0,0,.75)}.popup-box[data-v-7b0ba466]{width:%?620?%;z-index:3}.popup-dialog[data-v-7b0ba466]{overflow:hidden;height:%?506?%;background:#fff;border-radius:%?20?%;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-7b0ba466]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body uni-textarea[data-v-7b0ba466]{width:%?556?%;padding:%?15?%;box-sizing:border-box;margin:0 auto;border:1px solid #ccc;height:%?260?%;border-radius:%?8?%}.popup-dialog .popup-dialog-footer[data-v-7b0ba466]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-7b0ba466]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-7b0ba466]{color:#f2270c;background:#fff;border:%?1?% solid #f2270c}.popup-dialog .popup-dialog-footer .button.red[data-v-7b0ba466]{color:#fff;background:#f2270c}.heightvh[data-v-7b0ba466]{overflow-y:hidden}.refund-apply-money[data-v-7b0ba466]{display:flex;align-items:center;justify-content:space-between;padding:0 %?24?%;margin:0 %?24?%;margin-bottom:%?20?%;height:%?100?%;border-radius:%?20?%;background-color:#fff}.refund-apply-money .name[data-v-7b0ba466]{font-weight:700;color:#333;font-size:%?28?%}.refund-apply-money .money[data-v-7b0ba466]{font-weight:700;color:#f2270c;font-size:%?36?%}.refund-apply-money .money .text[data-v-7b0ba466]{font-weight:400;font-size:%?26?%}',""]),t.exports=a},"7e30":function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7b0ba466]{width:100%;text-align:center}.align-right[data-v-7b0ba466]{text-align:right}.detail-container[data-v-7b0ba466]{height:100vh}.detail-container .height-box[data-v-7b0ba466]{display:block;padding-bottom:%?100?%;padding-bottom:calc(%?100?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?100?% + env(safe-area-inset-bottom))}.status-wrap[data-v-7b0ba466]{position:relative;height:%?264?%;box-sizing:border-box;background-repeat:no-repeat;background-size:100% 100%}.status-wrap .status-bg[data-v-7b0ba466]{position:absolute;left:0;top:0;width:%?750?%;height:%?264?%}.status-wrap.status-success-or-fail[data-v-7b0ba466]{height:%?674?%}.status-wrap.status-success-or-fail .status-bg[data-v-7b0ba466]{height:%?674?%}.status-wrap.status-success-or-fail .rewards[data-v-7b0ba466]{position:absolute;bottom:%?134?%;left:50%;margin-left:%?-351?%;z-index:1}.status-wrap.status-success-or-fail .rewards .bg-rewards[data-v-7b0ba466]{width:%?702?%;height:%?293?%;background-repeat:no-repeat;background-size:100% 100%;display:flex;align-items:center;justify-content:center;text-align:center}.status-wrap.status-success-or-fail .rewards .bg-rewards-center[data-v-7b0ba466]{width:100%;height:100%;display:flex;align-items:center;text-align:center;justify-content:center}.status-wrap.status-success-or-fail .rewards .bg-rewards-scroll[data-v-7b0ba466]{width:%?660?%;height:100%;margin-left:%?18?%;box-sizing:border-box;display:flex;align-items:center;text-align:center;overflow-x:auto}.status-wrap.status-success-or-fail .rewards .bg-rewards uni-view[data-v-7b0ba466]{font-size:%?26?%;color:#666}.status-wrap.status-success-or-fail .rewards .bg-rewards .rewards-wrap[data-v-7b0ba466]{font-size:0}.status-wrap.status-success-or-fail .rewards .bg-rewards .rewards-img[data-v-7b0ba466]{width:%?210?%;height:%?160?%}.status-wrap.status-success-or-fail .rewards .bg-rewards .sku_image[data-v-7b0ba466]{width:%?210?%;height:%?210?%}.status-wrap.status-underway[data-v-7b0ba466]{height:%?413?%;display:flex;justify-content:center;padding-top:%?20?%}.status-wrap.status-underway .status-bg[data-v-7b0ba466]{height:%?413?%}.status-wrap.status-underway .underway[data-v-7b0ba466]{position:relative;z-index:1;display:flex;flex-direction:column;justify-content:space-between;align-items:center;width:%?702?%;height:%?288?%;box-sizing:border-box;padding:%?22?% 0;border-radius:%?20?%;background-color:#fff}.status-wrap.status-underway .underway .underway-title[data-v-7b0ba466]{font-size:%?28?%;display:flex}.status-wrap.status-underway .underway .underway-title uni-text[data-v-7b0ba466]{color:#f84346}.status-wrap.status-underway .underway .underway-title .pintuan-countdown[data-v-7b0ba466] .custom uni-view:nth-child(odd){background-color:#f84346;color:#fff;min-width:%?46?%;padding:0 %?4?%;box-sizing:border-box;height:%?40?%;line-height:%?40?%;color:#fff;border-radius:%?6?%;font-size:%?28?%;text-align:center;overflow:hidden;margin:0 %?5?%}.status-wrap.status-underway .underway .member-list[data-v-7b0ba466]{display:flex;flex-wrap:wrap}.status-wrap.status-underway .underway .member-list .img-wrap[data-v-7b0ba466]{position:relative}.status-wrap.status-underway .underway .member-list .head-tag[data-v-7b0ba466]{position:absolute;right:0;top:%?-10?%;width:%?60?%;height:%?30?%;line-height:%?30?%;font-size:%?20?%;color:#fff;background-color:#fe0b0b;border-radius:%?50?%;text-align:center;z-index:1}.status-wrap.status-underway .underway .member-list .member-list-img[data-v-7b0ba466], .status-wrap.status-underway .underway .member-list .has-diff-num[data-v-7b0ba466]{box-sizing:border-box;margin:0 %?23?%;margin:0 %?16?%;width:%?80?%;height:%?80?%;border-radius:50%;border:%?2?% solid red}.status-wrap.status-underway .underway .member-list .has-diff-num[data-v-7b0ba466]{border:none}.status-wrap.status-underway .underway .member-list .has-diff-num .img[data-v-7b0ba466]{width:%?80?%;height:%?80?%}.status-wrap .status-box[data-v-7b0ba466]{text-align:center;color:#fff}.status-wrap .status-box[data-v-7b0ba466]{min-height:%?180?%;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative}.status-wrap .status-name-box[data-v-7b0ba466]{display:flex;align-items:center;justify-content:center}.status-wrap .status-name[data-v-7b0ba466]{font-size:%?40?%;font-weight:700;color:#fff}.status-wrap .desc[data-v-7b0ba466]{margin-left:%?20?%;font-size:%?28?%}.status-wrap .strong[data-v-7b0ba466]{font-weight:700;font-size:%?36?%}.status-wrap .operation-group[data-v-7b0ba466]{text-align:center;padding-top:%?20?%}.status-wrap .operation-group .operation-btn[data-v-7b0ba466]{line-height:1;padding:%?16?% %?50?%;display:inline-block;border-radius:%?32?%;background:#fff;box-shadow:0 0 %?14?% hsla(0,0%,62%,.6)}.address-wrap.active[data-v-7b0ba466]{display:flex;align-items:center}.address-wrap[data-v-7b0ba466]{margin:0 %?24?%;margin-bottom:%?20?%;margin-top:%?-84?%;min-height:%?168?%;border-radius:%?20?%;background-color:#fff;position:relative;z-index:10}.address-wrap .icon[data-v-7b0ba466]{width:%?101?%;display:flex;align-items:center;justify-content:center}.address-wrap .icon uni-image[data-v-7b0ba466]{width:%?48?%;height:%?48?%}.address-wrap .address-info[data-v-7b0ba466]{flex-grow:1}.address-wrap .address-info .info[data-v-7b0ba466]{display:flex;font-size:%?30?%;font-weight:700;margin-bottom:%?20?%}.address-wrap .address-info .info .info-name[data-v-7b0ba466]{max-width:%?380?%;margin-right:%?32?%;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;word-break:break-all;font-weight:700}.address-wrap .address-info .detail[data-v-7b0ba466]{line-height:1.3;color:#343434;width:%?508?%}.address-wrap .cell-more[data-v-7b0ba466]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?10?%}.address-wrap .cell-more .iconfont[data-v-7b0ba466]{color:#999}.pickup-info[data-v-7b0ba466]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative;padding:%?20?% %?30?%}.pickup-info .pickup-point-info .name[data-v-7b0ba466]{display:flex;height:%?50?%;align-items:flex-end;margin-bottom:10px}.pickup-info .pickup-point-info .name uni-text[data-v-7b0ba466]{line-height:1}.pickup-info .pickup-point-info .name uni-text.mark[data-v-7b0ba466]{font-size:%?20?%;padding:1px %?10?%;border:.5px solid #fff;border-radius:%?4?%;margin-left:%?10?%}.pickup-info .pickup-point-info .address[data-v-7b0ba466],\r\n.pickup-info .pickup-point-info .time[data-v-7b0ba466],\r\n.pickup-info .pickup-point-info .contact[data-v-7b0ba466]{font-size:%?26?%;line-height:1;margin-top:%?16?%}.pickup-info .pickup-point-info .address .iconfont[data-v-7b0ba466],\r\n.pickup-info .pickup-point-info .time .iconfont[data-v-7b0ba466],\r\n.pickup-info .pickup-point-info .contact .iconfont[data-v-7b0ba466]{color:#999;font-size:%?26?%;line-height:1;margin-right:%?10?%}.pickup-info .hr[data-v-7b0ba466]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.pickup-info .pickup-code-info .info[data-v-7b0ba466]{text-align:center}.pickup-info .pickup-code-info .code[data-v-7b0ba466]{margin:0 auto;width:%?160?%;height:%?160?%}.pickup-info .pickup-code-info .code uni-image[data-v-7b0ba466]{width:100%;height:100%}.verify-code-wrap[data-v-7b0ba466]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.verify-code-wrap .wrap[data-v-7b0ba466]{line-height:%?40?%}.verify-code-wrap .wrap .copy[data-v-7b0ba466]{font-size:%?20?%;display:inline-block;color:#666;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?10?%;border-radius:%?18?%}.verify-code-wrap .hr[data-v-7b0ba466]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.verify-code-wrap .code[data-v-7b0ba466]{margin:0 auto;width:%?200?%;height:%?200?%}.verify-code-wrap .code uni-image[data-v-7b0ba466]{width:100%;height:100%}.site-wrap[data-v-7b0ba466]{margin:0 %?24?%;padding:0 %?24?%;border-radius:%?20?% %?20?% 0 0;background:#fff;position:relative}.site-wrap .site-header[data-v-7b0ba466]{display:flex;align-items:center;height:%?98?%}.site-wrap .site-header .icondianpu[data-v-7b0ba466]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?30?%}.site-wrap .site-body .goods-wrap[data-v-7b0ba466]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-7b0ba466]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-7b0ba466]{width:%?180?%;height:%?180?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-7b0ba466]{width:100%;height:100%;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-7b0ba466]{flex:1;position:relative;max-width:calc(100% - %?200?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-7b0ba466]{padding-top:%?5?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%}.site-wrap .site-body .goods-wrap .goods-info .goods-name .pintuan-tag[data-v-7b0ba466]{margin-right:%?2?%;background-color:#fc4027;color:#fff;font-size:%?24?%;border-radius:%?8?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-7b0ba466]{width:100%;line-height:1.3;display:flex;justify-content:space-between;align-items:center}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-7b0ba466]{color:#9a9a9a;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price[data-v-7b0ba466]{text-align:right;font-size:%?28?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price .unit[data-v-7b0ba466]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-operation[data-v-7b0ba466]{text-align:right;padding:%?20?% 0}.site-wrap .site-body .goods-operation .disinline[data-v-7b0ba466]{display:inline-block}.site-wrap .site-body .goods-operation .operation-btn[data-v-7b0ba466]{line-height:1;padding:%?14?% %?20?%;color:#333;display:inline-block;border-radius:%?28?%;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?10?%}.order-cell[data-v-7b0ba466]{display:flex;margin:%?35?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-7b0ba466]{text-align:left}.order-cell .box[data-v-7b0ba466]{flex:1;padding:0 %?20?%;line-height:inherit}.order-cell .box .textarea[data-v-7b0ba466]{height:%?40?%}.order-cell .iconfont[data-v-7b0ba466]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-7b0ba466]{padding:0}.order-cell .order-pay uni-text[data-v-7b0ba466]{display:inline-block;margin-left:%?6?%}.order-summary[data-v-7b0ba466]{margin:%?20?% %?24?%;padding:%?1?% %?24?%;margin-bottom:%?54?%;border-radius:%?20?%;background-color:#fff}.order-summary .order-cell[data-v-7b0ba466]{font-size:%?26?%}.order-summary .order-cell .tit[data-v-7b0ba466]{width:%?170?%}.order-summary .order-cell .box[data-v-7b0ba466]{display:flex;align-items:center}.order-summary .order-cell .copy[data-v-7b0ba466]{font-size:%?26?%;display:inline-block;color:#f2280d;line-height:1;margin-left:%?20?%}.order-summary .hr[data-v-7b0ba466]{width:calc(100% - %?190?%);margin-left:%?190?%;height:1px;background:#f7f7f7}.order-money[data-v-7b0ba466]{margin:0 %?24?%;padding:%?1?% %?24?%;background-color:#fff}.order-money .order-cell[data-v-7b0ba466]{font-size:%?26?%}.order-money .order-cell .order-cell-right[data-v-7b0ba466]{font-size:%?26?%;color:#9a9a9a}.order-money .order-cell .order-cell-right .pay-money[data-v-7b0ba466]{color:#f2280d}.order-money .order-cell .box[data-v-7b0ba466]{padding:0}.order-money .order-cell .box .operator[data-v-7b0ba466]{font-size:%?24?%;margin-right:%?6?%}.kefu[data-v-7b0ba466]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.kefu > uni-view[data-v-7b0ba466]{display:flex;justify-content:center;align-items:center}.kefu > uni-view .iconfont[data-v-7b0ba466]{font-weight:700;margin-right:%?10?%;font-size:%?28?%;line-height:1}.kefu uni-button[data-v-7b0ba466]{width:100%;height:100%;position:absolute;border:none;z-index:1;padding:0;margin:0;background:none}.kefu uni-button[data-v-7b0ba466]::after{border:none!important}.order-operation[data-v-7b0ba466]{display:flex;justify-content:flex-end;padding-bottom:%?36?%;margin:0 %?24?%;background:#fff;border-radius:0 0 %?20?% %?20?%}.order-operation .order-box-btn[data-v-7b0ba466]{margin-right:%?20?%;margin-left:0}.desc-box[data-v-7b0ba466], .desc-box .desc[data-v-7b0ba466]{display:flex;align-items:center;justify-content:center;color:#fff}.icon-pay-clock[data-v-7b0ba466]{width:%?38?%;height:%?38?%;margin-right:%?15?%}.site-name[data-v-7b0ba466]{font-size:%?28?%;font-weight:700}.order-box-btn[data-v-7b0ba466]{min-width:%?160?%;padding:0 %?16?%;box-sizing:border-box;display:flex;height:%?64?%;line-height:%?64?%!important;text-align:center;align-items:center;justify-content:center;border-color:#ccc!important;color:#666!important;font-size:%?26?%}.order-box-btn.order-pay[data-v-7b0ba466]{background:#f2280c!important;border-color:#f2280c!important;color:#fff!important}.order-box-btn-wrap[data-v-7b0ba466]{display:inline-block}.underway-inviter-btn[data-v-7b0ba466]{width:%?460?%;height:%?60?%;line-height:%?60?%;background:#f84346;border-radius:%?50?%;text-align:center;color:#fff;font-size:%?26?%}.underway-inviter-btn .share-btn[data-v-7b0ba466]{background-color:initial;color:#fff;font-size:%?26?%;margin:0;padding:0;line-height:%?60?%}.redic_pay[data-v-7b0ba466]{width:%?80?%;height:%?80?%;border-radius:50%;position:absolute;left:%?16?%;bottom:%?16?%}',""]),t.exports=a},"945b":function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("f7a5"),e("e838"),e("c9b5"),e("bf0f"),e("ab80"),e("e966");var o=i(e("2634")),n=i(e("2fdc")),r=i(e("5ab1")),s=i(e("016e")),d=i(e("2d01")),u=i(e("85bf")),c=i(e("2f73")),l=e("4b89"),p={components:{uniCountDown:r.default,diyShareNavigateH5:c.default},data:function(){return{isIphoneX:!1,orderId:0,orderData:{action:[]},refundRemark:"",keyHeight:0,bg_image:"",awardList:[],isOnXianMaiApp:l.isOnXianMaiApp}},onLoad:function(t){t.order_id&&(this.orderId=t.order_id)},onShow:function(){var t=this;return(0,n.default)((0,o.default)().mark((function a(){return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.$langConfig.refresh(),a.next=3,u.default.wait_staticLogin_success();case 3:t.isIphoneX=t.$util.uniappIsIPhoneX(),uni.getStorageSync("token")?t.getOrderData():t.$util.redirectTo("/pages/login/login/login",{back:"/promotionpages/pintuan/order/detail/detail?order_id="+t.orderId});case 5:case"end":return a.stop()}}),a)})))()},mixins:[d.default,s.default],methods:{getOrderData:function(){var t=this;this.$api.sendRequest({url:"/api/Pintuan/orderDetail",data:{id:this.orderId},success:function(a){uni.stopPullDownRefresh(),a.code>=0?(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.orderData=a.data,t.bg_image=a.data.bg_image,t.awardList=a.data.awardList,t.orderData.close_time&&(t.orderData.discountTimeMachine=t.$util.countDown(t.orderData.close_time)),t.orderData.distance_time&&(t.orderData.distance_time=1e3*Math.abs(t.orderData.distance_time)),t.setWechatShare()):t.$util.showToast({title:"未获取到订单信息!！",success:function(){}})},fail:function(a){uni.stopPullDownRefresh(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getSharePageParams:function(){var t=this.$util.unifySharePageParams("/promotionpages/pintuan/share/share","先迈商城","邀请拼团抢".concat(this.orderData.sku_name),{group_id:this.orderData.group_id},this.$util.img(this.orderData.sku_image));return t},setWechatShare:function(){var t=this,a=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+a.link.slice(1);a.link=e,this.$util.publicShare(a,(function(){u.default.pintuanShareActionReport({pintuan_id:t.orderData.pintuan_id,goods_id:t.orderData.goods_id})}))},toShare:function(){var t=this,a=this.getSharePageParams();this.$refs.shareNavigateH5.open(a,(function(){t.isOnXianMaiApp&&u.default.pintuanShareActionReport({pintuan_id:t.orderData.pintuan_id,goods_id:t.orderData.goods_id})}))},onPullDownRefresh:function(){this.getOrderData()},operation:function(t,a){var e=this,i=t.action;switch(i){case"orderPay":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderPay(this.orderData,(function(){e.getOrderData()}));break;case"orderClose":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderClose(a,(function(){e.getOrderData()}));break}},imageError:function(){this.orderData.sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},awardImageError:function(){this.orderData.awardInfo.sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},toShopDetail:function(t){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:t})},to_copy_order_no:function(){this.$util.copy(this.orderData.order_no)}},onShareAppMessage:function(t){var a=this.getSharePageParams();return u.default.pintuanShareActionReport({pintuan_id:this.orderData.pintuan_id,goods_id:this.orderData.goods_id}),this.$buriedPoint.pageShare(a.link,a.imageUrl,a.desc,!0,{goods_id:this.orderData.goods_id})},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)},timeStr:function(t){var a=parseInt(t/3600).toString(),e=parseInt(t%3600/60).toString();return 1==e.length&&(e="0"+e),1==a.length&&(a="0"+a),a+":"+e},moneyFormat:function(t){return parseFloat(t).toFixed(2)}}};a.default=p},ac31:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",[e("v-uni-view",{staticClass:"custom flex-start-center"},[t.timeData.day>=1?e("v-uni-view",{staticClass:"day"},[t._v(t._s(t.timeData.day))]):t._e(),t.timeData.day>=1?e("v-uni-view",{staticClass:"day-symbol"},[t._v(t._s(t.showColon||t.showDaySymbol?"天":":"))]):t._e(),e("v-uni-view",{staticClass:"hour"},[t._v(t._s(t._f("fillWithZero")(t.timeData.hour)))]),e("v-uni-view",{staticClass:"hour-symbol"},[t._v(t._s(t.showColon?"时":":"))]),e("v-uni-view",{staticClass:"minute"},[t._v(t._s(t._f("fillWithZero")(t.timeData.minute)))]),e("v-uni-view",{staticClass:"minute-symbol"},[t._v(t._s(t.showColon?"分":":"))]),e("v-uni-view",{staticClass:"second"},[t._v(t._s(t._f("fillWithZero")(t.timeData.second)))]),e("v-uni-view",{staticClass:"second-symbol"},[t._v(t._s(t.showColon?"秒":""))])],1)],1)},o=[]},add2:function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,"[data-v-7b0ba466] .uni-page{overflow:hidden}.countdown .clockrun[data-v-7b0ba466] .uni-countdown{display:flex;justify-content:center;align-items:center;height:%?64?%;padding:0}.countdown .clockrun[data-v-7b0ba466] .uni-countdown__number{background:#000;\n\t/* // #690b08 */padding:0;margin:0;border:none;font-weight:700;font-size:%?36?%}.countdown .clockrun[data-v-7b0ba466] .uni-countdown__splitor{padding:0;color:#000;font-weight:700;font-size:%?36?%}.countdown .clockrun[data-v-7b0ba466] .uni-countdown__splitor.day{width:auto;font-weight:700;font-size:%?36?%}[data-v-7b0ba466] .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%}",""]),t.exports=a},ae57:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("64aa"),e("c9b5"),e("bf0f"),e("ab80");var i={props:{time:{type:Number,default:0},autoStart:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},showDaySymbol:{type:Boolean,default:!1}},data:function(){return{timer:null,timeData:{remain:0,day:0,hour:0,minute:0,second:0}}},watch:{time:function(){this.reset()}},filters:{fillWithZero:function(t){var a=t.toString().length;while(a<2)t="0"+t,a++;return t}},methods:{updateTimeData:function(){var t=this.timeData.remain;this.timeData.day=Math.floor(t/1e3/60/60/24),this.timeData.hour=Math.floor(t/1e3/60/60%24),this.timeData.minute=Math.floor(t/1e3/60%60),this.timeData.second=Math.floor(t/1e3%60)},reset:function(){var t=this;this.timer&&this.timer.stop(),this.timeData.remain=this.time,this.updateTimeData(),this.timer=new this.$util.AdjustingInterval((function(){t.timeData.remain-=1e3,t.timeData.remain<=0?(t.timeData.day="00",t.timeData.hour="00",t.timeData.minute="00",t.timeData.second="00"):t.updateTimeData()}),1e3,this.time/1e3,(function(){t.$emit("finish")})),this.autoStart&&this.timer.start()},start:function(){this.timer||this.timer.start()}},mounted:function(){this.reset()},beforeDestroy:function(){}};a.default=i},b5ce:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=e("4b89"),o={name:"diy-share-navigate-h5",data:function(){return{isShow:!1,isWeiXin:this.$util.isWeiXin(),isOnXianMaiApp:!1}},methods:{maskClose:function(t){0==t.target.offsetTop&&(this.isShow=!1)},open:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0;if(this.isOnXianMaiApp){var e={title:t.desc?this.$util.shareTitleAddNickname(t.desc):"",desc:t.desc?t.desc:"",webpageUrl:t.webpageUrl?t.webpageUrl:"",thumbImage:t.imageUrl?this.$util.imageCdnResize(t.imageUrl,{image_process:"resize,w_300","x-oss-process":"image/resize,w_300"}):"",path:t.link};(0,i.shareMiniProgramSchemeGo)(e),a&&"function"==typeof a&&a()}else this.isShow=!0,a&&"function"==typeof a&&a()},close:function(){this.isShow=!1}},created:function(){this.isOnXianMaiApp=i.isOnXianMaiApp}};a.default=o},b81d:function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-d6071282]{width:100%;text-align:center}.uni-countdown[data-v-d6071282]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-d6071282]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?28?%}.uni-countdown__splitor.day[data-v-d6071282]{line-height:%?50?%}.uni-countdown__number[data-v-d6071282]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=a},b8bc:function(t,a,e){"use strict";e.d(a,"b",(function(){return o})),e.d(a,"c",(function(){return n})),e.d(a,"a",(function(){return i}));var i={uniCountDown:e("5ab1").default,countdownTimer:e("d3b8").default,loadingCover:e("5510").default,diyShareNavigateH5:e("2f73").default},o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"detail-container",class:{themeStyle:t.themeStyle,"safe-area":t.isIphoneX}},[e("v-uni-view",{staticClass:"status-wrap",class:{"status-success-or-fail":1==t.orderData.pintuan_status||3==t.orderData.pintuan_status,"status-underway":2==t.orderData.pintuan_status}},[e("v-uni-view",{staticClass:"bg"},[e("v-uni-image",{staticClass:"status-bg",attrs:{src:t.bg_image}})],1),0==t.orderData.pintuan_status||-1==t.orderData.pintuan_status?e("v-uni-view",{staticClass:"status-box"},[e("v-uni-view",{staticClass:"status-name-box"},[e("v-uni-image",{staticClass:"icon-pay-clock",attrs:{src:t.$util.img("public/static/youpin/order/icon-pay-clock.png")}}),"待支付"==t.orderData.status_desc?e("v-uni-text",{staticClass:"status-name"},[t._v("等待买家付款")]):e("v-uni-text",{staticClass:"status-name"},[t._v(t._s(t.orderData.status_desc))])],1),0==t.orderData.pintuan_status?[e("v-uni-view",{staticClass:"desc-box"},[e("v-uni-text",{staticClass:"desc"},[t._v("需支付："),e("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",{staticClass:"ns-font-size-lg strong"},[t._v(t._s(t.orderData.pay_money))])],1),e("v-uni-view",{staticClass:"desc"},[e("v-uni-text",[t._v("剩余：")]),e("v-uni-view",{staticClass:"countdown"},[e("v-uni-view",{staticClass:"clockrun"},[e("uni-count-down",{attrs:{day:t.orderData.discountTimeMachine.d,hour:t.orderData.discountTimeMachine.h,minute:t.orderData.discountTimeMachine.i,second:t.orderData.discountTimeMachine.s,color:"#FFFFFF",splitorColor:"#FFFFFF","background-color":"transparent"}})],1)],1)],1)],1)]:t._e()],2):t._e(),2==t.orderData.pintuan_status?e("v-uni-view",{staticClass:"underway"},[e("v-uni-view",{staticClass:"underway-title"},[t._v("拼团中 (还差"),e("v-uni-text",[t._v(t._s(t.orderData.diff_num)+"人")]),t._v(")，剩余"),e("countdown-timer",{staticClass:"pintuan-countdown",attrs:{autoStart:!0,time:t.orderData.distance_time,showColon:!0,format:"dd天hh:mm:ss"},on:{finish:function(a){arguments[0]=a=t.$handleEvent(a),t.getOrderData.apply(void 0,arguments)}}})],1),e("v-uni-navigator",{staticClass:"member-list",attrs:{url:"/promotionpages/pintuan/join_member/join_member?group_id="+t.orderData.group_id}},[t._l(t.orderData.member_list,(function(a,i,o){return e("v-uni-view",{key:o},[i<4?e("v-uni-view",{staticClass:"img-wrap"},[a.member_id==t.orderData.head_id?e("v-uni-view",{staticClass:"head-tag"},[t._v("团长")]):t._e(),e("v-uni-image",{staticClass:"member-list-img",attrs:{src:t.$util.img(a.member_img),mode:"aspectFill"},on:{error:function(a){arguments[0]=a=t.$handleEvent(a)}}}),0==a.pintuan_status?e("v-uni-image",{staticClass:"redic_pay",attrs:{src:t.$util.img("public/static/youpin/order/pintuan/radicpay.png")}}):t._e()],1):t._e()],1)})),e("v-uni-view",{staticClass:"has-diff-num"},[e("v-uni-image",{staticClass:"img",attrs:{src:t.$util.img("public/static/youpin/pintuan/rule-icon1.png")}})],1),t.orderData.diff_num>1?e("v-uni-view",{staticClass:"has-diff-num"},[e("v-uni-image",{staticClass:"img",attrs:{src:t.$util.img("public/static/youpin/pintuan/more-icon.png")}})],1):t._e()],2),e("v-uni-view",{staticClass:"underway-inviter-btn"},[e("v-uni-button",{staticClass:"share-btn",attrs:{"data-group_id":t.orderData.group_id,"data-sku_name":t.orderData.sku_name,"data-sku_image":t.orderData.sku_image},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toShare.apply(void 0,arguments)}}},[t._v("邀请好友拼团")])],1)],1):t._e(),1==t.orderData.pintuan_status||3==t.orderData.pintuan_status||-2==t.orderData.pintuan_status?[e("v-uni-view",{staticClass:"rewards"},[e("v-uni-view",{staticClass:"bg-rewards",style:"background-image:url("+t.$util.img("public/static/youpin/order/pintuan/bg-border.png")+");"},[e("v-uni-view",{class:t.awardList.length>3?"bg-rewards-scroll":"bg-rewards-center"},[t._l(t.awardList,(function(a,i){return[a.content?e("v-uni-view",[e("v-uni-image",{staticClass:"rewards-img",attrs:{src:a.image}}),e("v-uni-view",[t._v(t._s(a.content))])],1):e("v-uni-view",{key:i+"_0",staticClass:"rewards-wrap"},[e("v-uni-image",{staticClass:"sku_image",attrs:{src:a.image,mode:"aspectFill"},on:{error:function(a){arguments[0]=a=t.$handleEvent(a),t.awardImageError()}}})],1)]}))],2)],1)],1)]:t._e()],2),e("v-uni-view",{staticClass:"address-wrap active"},[e("v-uni-view",{staticClass:"icon"},[e("v-uni-image",{staticClass:"icon-pay-clock",attrs:{src:t.$util.img("public/static/youpin/order/icon-no-pay-address.png")}})],1),e("v-uni-view",{staticClass:"address-info"},[e("v-uni-view",{staticClass:"info"},[e("v-uni-view",{staticClass:"info-name"},[t._v(t._s(t.orderData.name))]),e("v-uni-view",[t._v(t._s(t.orderData.mobile||t.orderData.telephone))])],1),e("v-uni-view",{staticClass:"detail"},[e("v-uni-text",[t._v(t._s(t.orderData.full_address)+" "+t._s(t.orderData.address))])],1)],1)],1),e("v-uni-view",{staticClass:"site-wrap"},[e("v-uni-view",{staticClass:"site-header"},[e("v-uni-text",{staticClass:"site-name"},[t._v("订单号："+t._s(t.orderData.order_no))])],1),e("v-uni-view",{staticClass:"site-body"},[e("v-uni-view",{staticClass:"goods-wrap"},[e("v-uni-view",{staticClass:"goods-img",attrs:{"hover-class":"none"}},[e("v-uni-image",{attrs:{src:t.$util.img(t.orderData.sku_image),mode:"aspectFill"},on:{error:function(a){arguments[0]=a=t.$handleEvent(a),t.imageError()}}})],1),e("v-uni-view",{staticClass:"goods-info"},[e("v-uni-view",{staticClass:"goods-name",attrs:{"hover-class":"none"}},[e("v-uni-text",{staticClass:"pintuan-tag"},[t._v("拼团")]),t._v(t._s(t.orderData.sku_name))],1),e("v-uni-view",{staticClass:"goods-sub-section"},[e("v-uni-view",[t._v(t._s(t.orderData.spec_name))]),e("v-uni-view",[t._v("x1")])],1),e("v-uni-view",{staticClass:"goods-price"},[e("v-uni-text",{staticClass:"unit"},[t._v("¥")]),e("v-uni-text",[t._v(t._s(t.orderData.pay_money))])],1)],1)],1)],1)],1),e("v-uni-view",{staticClass:"order-money"},[e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("共1件商品")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"order-cell-right"},[e("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("¥")]),t._v(t._s(t.orderData.pay_money))],1)],1)],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("运费")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"order-cell-right"},[e("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("¥")]),e("v-uni-text",[t._v("0.00")])],1)],1)],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("实付金额")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"order-cell-right"},[e("v-uni-text",{staticClass:"pay-money"},[t._v("¥")]),e("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.pay_money))])],1)],1)],1)],1),e("v-uni-view",[t.orderData.action.length>0?e("v-uni-view",{staticClass:"order-operation"},t._l(t.orderData.action,(function(a,i){return"inviterMember"!=a.action?e("v-uni-view",{key:i,staticClass:"order-box-btn",class:{"order-pay":"orderPay"==a.action},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.operation(a,t.orderData)}}},[t._v(t._s(a.title))]):t._e()})),1):t._e()],1),e("v-uni-view",{staticClass:"order-summary"},[t.orderData.order_no?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("订单号：")]),e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.order_no))]),e("v-uni-view",{staticClass:"copy",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.to_copy_order_no.apply(void 0,arguments)}}},[t._v("复制")])],1)],1):t._e(),t.orderData.out_trade_no?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("支付单号：")]),e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.out_trade_no))])],1)],1):t._e(),t.orderData.add_time?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("下单时间：")]),e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.add_time)))])],1)],1):t._e(),t.orderData.pay_time?[e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("支付时间：")]),e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.pay_time)))])],1)],1),t.orderData.app_type_name?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("支付方式：")]),e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.pay_type_name))])],1)],1):t._e()]:t._e()],2),e("loading-cover",{ref:"loadingCover"}),e("diy-share-navigate-h5",{ref:"shareNavigateH5"})],1)},n=[]},c604:function(t,a,e){"use strict";var i=e("5552"),o=e.n(i);o.a},d3b8:function(t,a,e){"use strict";e.r(a);var i=e("ac31"),o=e("31c3");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(n);var r=e("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);a["default"]=s.exports},e71a:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],staticClass:"yd-popup",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.maskClose.apply(void 0,arguments)}}},[t.isWeiXin?e("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/weixin-share-tip.png")}}):e("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/browser-share-tip.png")}})],1)},o=[]},ecc6:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},created:function(t){var a=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){a.seconds--,a.seconds<0?a.timeUp():a.countDown()}),1e3)},watch:{day:function(t){var a=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){a.seconds--,a.seconds<0?a.timeUp():a.countDown()}),1e3)},hour:function(t){var a=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){a.seconds--,a.seconds<0?a.timeUp():a.countDown()}),1e3)},minute:function(t){var a=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){a.seconds--,a.seconds<0?a.timeUp():a.countDown()}),1e3)},second:function(t){var a=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){a.seconds--,a.seconds<0?a.timeUp():a.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,a,e,i){return 60*t*60*24+60*a*60+60*e+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,a=0,e=0,i=0,o=0;t>0?(a=Math.floor(t/86400),e=Math.floor(t/3600)-24*a,i=Math.floor(t/60)-24*a*60-60*e,o=Math.floor(t)-24*a*60*60-60*e*60-60*i):this.timeUp(),a<10&&(a="0"+a),e<10&&(e="0"+e),i<10&&(i="0"+i),o<10&&(o="0"+o),this.d=a,this.h=e,this.i=i,this.s=o}}};a.default=i},fe2a:function(t,a,e){"use strict";e.r(a);var i=e("ecc6"),o=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=o.a}}]);