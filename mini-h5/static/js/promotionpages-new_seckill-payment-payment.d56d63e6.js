(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-new_seckill-payment-payment"],{1195:function(e,t,a){var i=a("f408");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("61b25e3b",i,!0,{sourceMap:!1,shadowMode:!1})},"1c3e":function(e,t,a){"use strict";var i=a("68ed"),o=a.n(i);o.a},"287e":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var i={uniIcons:a("de74").default,uniPopup:a("5e99").default,mypOne:a("c8ec").default,loadingCover:a("5510").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"order-container",class:e.themeStyle,style:[e.themeColorVar]},[e.isOnXianMaiApp?a("v-uni-view",{staticClass:"nav bg-white",style:{height:e.navHeight+"px"}},[a("v-uni-view",{staticClass:"nav-title"},[a("v-uni-image",{staticClass:"back",attrs:{src:e.$util.img("public/static/youpin/order/back.png"),mode:"aspectFit"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup()}}}),a("v-uni-text",[e._v("确认订单")])],1)],1):e._e(),a("v-uni-view",{staticClass:"wrapper",style:{marginTop:e.navHeight+"px"}},[a("v-uni-view",{staticClass:"address-wrap"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/order/icon-no-pay-address.png"),mode:""}})],1),a("v-uni-view",{staticClass:"address-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getChooseAddress.apply(void 0,arguments)}}},[e.orderPaymentData.member_address?[a("v-uni-view",{staticClass:"info"},[a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.name))]),a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.mobile))])],1),a("v-uni-view",{staticClass:"detail"},[a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.full_address)+" "+e._s(e.orderPaymentData.member_address.address))])],1)]:[a("v-uni-view",{staticClass:"address-empty"},[a("v-uni-text",[e._v("选择收货地址")])],1)],a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont iconright"})],1)],2)],1),e.orderPaymentData.goodsInfo?a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-header",staticStyle:{visibility:"hidden"}},[a("v-uni-view",{staticClass:"iconfont icondianpu"}),a("v-uni-text",{staticClass:"site-name"},[e._v(e._s(e.orderPaymentData.goodsInfo.site_name))])],1),a("v-uni-view",{staticClass:"site-body"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-navigator",{staticClass:"goods-img",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.orderPaymentData.goodsInfo.sku_id}},[a("v-uni-image",{attrs:{src:e.$util.img(e.orderPaymentData.goodsInfo.sku_image),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(e.orderPaymentData)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-navigator",{staticClass:"goods-name",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.orderPaymentData.goodsInfo.sku_id}},[e._v(e._s(e.orderPaymentData.goodsInfo.goods_name))]),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",[e._v(e._s(e.orderPaymentData.goodsInfo.spec_name))])],1),a("v-uni-view",[a("v-uni-text",[e._v("x "+e._s(e.orderPaymentData.seckillBuy.num))])],1)],1),a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-text",[a("v-uni-text",{staticClass:"unit"},[e._v("￥")]),a("v-uni-text",{staticClass:"price"},[e._v(e._s(e.orderPaymentData.seckillBuy.seckill_price))])],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[parseFloat(e.orderPaymentData.delivery_money)>0?a("v-uni-text",{},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.delivery_money)))])],1):a("v-uni-text",[e._v("包邮")])],1)],1)],1),a("v-uni-view",{staticClass:"site-footer"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("订单备注")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-input",{staticClass:"ns-font-size-base",attrs:{type:"text",value:"",placeholder:"请填对本次交易的说明","placeholder-style":"{color:#CCCCCC}",maxlength:"50"},model:{value:e.buyer_message,callback:function(t){e.buyer_message=t},expression:"buyer_message"}})],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"box align-right order-pay"},[e._v("共 "+e._s(e.orderPaymentData.seckillBuy.num)+" 件商品"),a("v-uni-text",[e._v("微信支付\t\t\t\t\t\t\t\t小计：￥"),a("v-uni-text",{staticClass:"pay-money"},[e._v(e._s(e.orderPaymentData.seckillBuy.goods_money))])],1)],1)],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"site-wrap payment-methods"},e._l(e.otherPaymentMethods,(function(t){return a("v-uni-view",{staticClass:"item",class:{disable:t.disable},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changePayment(t)}}},[a("v-uni-image",{staticClass:"icon",attrs:{src:t.icon}}),a("v-uni-view",{staticClass:"title"},[a("v-uni-text",{staticClass:"text"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"desc"},[e._v(e._s(t.desc))])],1),a("uni-icons",{staticClass:"checkbox",attrs:{type:"checkbox-filled",color:e.paymentMethod==t.key?"var(--custom-brand-color)":"#ccc",size:"18"}})],1)})),1),a("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[a("v-uni-view",{staticClass:"order-settlement-info"},[a("v-uni-text",[e._v("实付金额：")]),a("v-uni-text",{staticClass:"text-color"},[e._v(e._s(e.$lang("common.currencySymbol"))),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e.orderPaymentData.seckillBuy.goods_money))])],1)],1),a("v-uni-view",{staticClass:"submit-btn"},[a("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showGoWeixin.apply(void 0,arguments)}}},[e._v(e._s(1==e.orderCreateData.is_balance?"余额支付":"微信支付"))])],1)],1)],1),a("uni-popup",{ref:"popup",staticClass:"my-popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("提示")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[e._v("是否放弃本次付款？")]),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-view",{staticClass:"button white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toBack()}}},[e._v("放弃")]),a("v-uni-button",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup()}}},[e._v("继续付款")])],1)],1)],1),a("uni-popup",{ref:"popupToList",staticClass:"my-popup-dialog",attrs:{"mask-click":!1}},[a("v-uni-view",{staticClass:"popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("提示")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[e._v("订单超过2小时未支付将被取消，请尽快完成支付哦！~")]),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-button",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toWaitPayList()}}},[e._v("我知道了")])],1)],1)],1),a("uni-popup",{ref:"payPassword",attrs:{custom:!0}},[a("v-uni-view",{staticClass:"pay-password"},[0==e.orderPaymentData.member_account.is_pay_password?[a("v-uni-view",{staticClass:"title"},[e._v("为了您的账户安全,请先设置您的支付密码")]),a("v-uni-view",{staticClass:"tips"},[e._v('可到"个人中心-设置-支付密码设置"中设置')]),a("v-uni-view",{staticClass:"btn ns-bg-color ns-border-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("立即设置")]),a("v-uni-view",{staticClass:"btn white ns-border-color ns-text-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.noSet.apply(void 0,arguments)}}},[e._v("暂不设置")])]:[a("v-uni-view",{staticClass:"popup-title"},[a("v-uni-image",{staticClass:"cha_close",attrs:{src:e.$util.img("public/static/youpin/maidou/cha.png"),mode:""},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close_pay()}}}),a("v-uni-view",{staticClass:"title"},[e._v("请输入支付密码")])],1),a("v-uni-view",{staticClass:"money-box"},[a("v-uni-view",{staticClass:"total-fee"},[e._v("总金额￥"+e._s(e._f("moneyFormat")(e.orderPaymentData.seckillBuy.goods_money)))]),a("v-uni-view",{staticClass:"balance"},[e._v("(当前余额￥"+e._s(e.orderPaymentData.member_account.balance_money)+")")])],1),a("v-uni-view",{staticClass:"password-wrap"},[a("myp-one",{ref:"input",attrs:{maxlength:6,"is-pwd":!0,"auto-focus":e.isFocus,type:"box"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.input.apply(void 0,arguments)}}}),e.errMsg?a("v-uni-view",{staticClass:" ns-text-color ns-font-size-sm forget-password error-tips"},[e._v(e._s(e.errMsg))]):e._e(),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"ns-text-color ns-font-size-sm forget-password",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("忘记密码")])],1)],1)]],2)],1),a("uni-popup",{ref:"popupGoWeixin",staticClass:"my-popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("提示")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[e._v("需要跳转到微信APP，使用先迈小程序支付订单金额")]),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-button",{staticClass:"button white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.popupGoWeixin.close()}}},[e._v("取消")]),a("v-uni-view",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.comfirmGoWeixin.apply(void 0,arguments)}}},[e._v("去支付")])],1)],1)],1),a("loading-cover",{ref:"loadingCover"})],1)},n=[]},"2d01":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"327f":function(e,t,a){"use strict";var i=a("a971"),o=a.n(i);o.a},"37ff":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},o=[]},4210:function(e,t,a){"use strict";var i=a("e133"),o=a.n(i);o.a},"61c4":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"code-box"},[a("v-uni-view",{staticClass:"flex-box"},[a("v-uni-input",{staticClass:"hide-input",attrs:{value:e.inputValue,type:"number",focus:e.autoFocus,maxlength:e.maxlength},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.getVal.apply(void 0,arguments)}}}),e._l(e.ranges,(function(t,i){return[a("v-uni-view",{key:i+"_0",class:["item",{active:e.codeIndex===t,middle:"middle"===e.type,bottom:"bottom"===e.type,box:"box"===e.type}]},["middle"!==e.type?a("v-uni-view",{staticClass:"line"}):e._e(),"middle"===e.type&&e.codeIndex<=t?a("v-uni-view",{staticClass:"bottom-line"}):e._e(),e.isPwd&&e.codeArr.length>=t?[a("v-uni-text",{staticClass:"dot"},[e._v("●")])]:[a("v-uni-text",{staticClass:"number"},[e._v(e._s(e.codeArr[i]?e.codeArr[i]:""))])]],2)]}))],2)],1)},o=[]},"68ed":function(e,t,a){var i=a("ebf2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("0242835c",i,!0,{sourceMap:!1,shadowMode:!1})},"6a1d":function(e,t,a){"use strict";var i=a("1195"),o=a.n(i);o.a},"84e97":function(e,t,a){"use strict";var i=a("e4fe"),o=a.n(i);o.a},"8f68":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},9008:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d4b5"),a("5c47"),a("0506"),a("8f71"),a("bf0f"),a("e838");var o=i(a("2634")),n=i(a("2fdc")),s=(i(a("cbf3")),i(a("7c8d"))),r={data:function(){return{isSub:!1,suk_id:"",seckill_id:"",buyer_message:"",orderPaymentData:{member_account:{balance:0,is_pay_password:0},is_balance:0,seckillBuy:{seckill_price:"",goods_money:""}},seckillOrderCreateData:{},isFocus:!1,orderCreateData:{is_balance:0,pay_password:""},errMsg:"",paymentMethod:"WECHAT",push_data:{},isSubscribed:!1}},methods:{selectAddress:function(){this.$util.redirectTo("/otherpages/member/address/address",{back:"/promotionpages/new_seckill/payment/payment"})},getAddressList:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var a,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=[],t.next=3,e.$api.sendRequest({url:"/api/memberaddress/page",async:!1,data:{page:1,page_size:20}});case 3:return i=t.sent,0==i.code&&(a=i.data.list),t.abrupt("return",a);case 6:case"end":return t.stop()}}),t)})))()},getOrderPaymentData:function(){var e=this;if(this.seckillOrderCreateData=uni.getStorageSync("seckillOrderCreateData"),this.seckillOrderCreateData){var t=uni.getStorageSync("member_address");t&&(this.seckillOrderCreateData.member_address=t.id),this.$api.sendRequest({url:this.$apiUrl.getSeckillPayment,data:this.seckillOrderCreateData,success:function(t){if(t.code>=0){e.orderPaymentData=t.data;var a=uni.getStorageSync("member_address");a?a.id==e.orderPaymentData.member_address.id?uni.setStorageSync("member_address",e.orderPaymentData.member_address):e.orderPaymentData.member_address=a:uni.setStorageSync("member_address",e.orderPaymentData.member_address),e.$buriedPoint.submitOrderContent({sku_ids:[t.data.goodsInfo.sku_id],num:[t.data.seckillBuy.num],pages:1}),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:t.message,success:function(){setTimeout((function(){e.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}),1500)}})},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})}else this.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){console.log("!this.seckillOrderCreateData"),console.log(e.seckillOrderCreateData),e.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}),1500)}})},orderCreate:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var a,i,s;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.verify()){t.next=21;break}if(!e.isSub){t.next=3;break}return t.abrupt("return");case 3:if(e.isSub=!0,e.isSubscribed){t.next=16;break}return a="",a="WECHAT"==e.paymentMethod?"order_pay_before":"other_pay_before",t.prev=7,t.next=10,e.$util.subscribeMessage({source:"order",source_id:"",scene_type:a},!0);case 10:e.push_data=t.sent,e.isSubscribed=!0,t.next=16;break;case 14:t.prev=14,t.t0=t["catch"](7);case 16:uni.showLoading({mask:!0,title:"加载中"}),i=uni.getStorageSync("member_address"),s={sku_id:e.seckillOrderCreateData.sku_id,seckill_id:e.seckillOrderCreateData.seckill_id,member_address:JSON.stringify(i.id),buyer_message:JSON.stringify(e.buyer_message),num:e.seckillOrderCreateData.num},Object.assign(s,e.orderCreateData),e.$api.sendRequest({url:e.$apiUrl.getSeckillSubmitorder,data:s,success:function(){var t=(0,n.default)((0,o.default)().mark((function t(a){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!(a.code>=0)){t.next=9;break}return e.createBuriedPoint(a.data.out_trade_no,3),e.push_data.source_id=a.data.order_ids,t.next=5,e.$util.subscribeMessageMethod(e.push_data);case 5:1==a.data.is_free?(e.createBuriedPoint(a.data.out_trade_no,11),e.$util.redirectTo("/pages/pay/result/result?order_ids="+a.data.order_ids,{code:a.data.out_trade_no},"redirectTo"),uni.removeStorage({key:"seckillOrderCreateData",success:function(){}})):e.orderPayPay(a.data.out_trade_no,a.data.order_ids),uni.removeStorageSync("member_address"),t.next=13;break;case 9:e.isSub=!1,uni.hideLoading(),e.$refs.payPassword&&(e.isFocus=!1,e.$refs.payPassword.close()),10==a.data.error_code||12==a.data.error_code?uni.showModal({title:"订单未创建",content:a.message,confirmText:"去设置",success:function(t){t.confirm&&e.selectAddress()}}):e.$util.showToast({title:a.message});case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),fail:function(t){uni.hideLoading(),e.isSub=!1}});case 21:case"end":return t.stop()}}),t,null,[[7,14]])})))()},orderPayPay:function(e,t){var a=this;uni.showLoading({mask:!0,title:"加载中"});this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:e,pay_type:"adapay"},success:function(t){uni.hideLoading(),t.code>=0?a.$util.wechatPay(t.data.pay_type,"adapay"==t.data.pay_type?t.data.payment:t.data.pay_info,(function(t){a.createBuriedPoint(e,11),uni.hideLoading(),a.$util.redirectTo("/pages/order/list/list?status=all",{},"redirectTo"),uni.removeStorage({key:"seckillOrderCreateData",success:function(){}})}),(function(t){a.createBuriedPoint(e,9001),uni.hideLoading(),a.$refs.popupToList.open()}),(function(e){setTimeout((function(){a.$util.redirectTo("/pages/order/list/list",{},"redirectTo")}),2e3)})):(console.log(t),t.message?a.$util.showToast({title:t.message}):uni.hideLoading(),a.$refs.popupToList&&a.$refs.popupToList.open())},fail:function(e){a.$util.showToast({title:"request:fail"})}})},createBuriedPoint:function(e,t){this.$buriedPoint.orderStatus({out_trade_no:e,status:t})},verify:function(){var e=this;if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!/^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/.test(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}return 0!=this.orderPaymentData.is_virtual||this.orderPaymentData.member_address?("BALANCE"==this.paymentMethod&&(this.orderCreateData.is_balance=1),1!=this.orderCreateData.is_balance||""!=this.orderCreateData.pay_password||(setTimeout((function(){e.$refs.input&&e.$refs.input.clear()}),0),this.openPasswordPopup(),!1)):(this.$util.showToast({title:"请先选择您的收货地址"}),!1)},init:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var a,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.$langConfig.refresh(),uni.getStorageSync("token")){t.next=5;break}e.$util.redirectTo("/otherpages/shop/home/<USER>"),t.next=13;break;case 5:if(a=uni.getStorageSync("member_address"),!a){t.next=11;break}return t.next=9,e.getAddressList();case 9:i=t.sent,i.length?i.filter((function(e){return a.id==e.id})).length<1&&uni.removeStorageSync("member_address"):uni.removeStorageSync("member_address");case 11:e.orderPaymentData={member_account:{balance:0},is_balance:0,seckillBuy:{seckill_price:"",goods_money:""}},e.orderCreateData={is_balance:0,pay_password:""},e.isSub||e.getOrderPaymentData();case 13:case"end":return t.stop()}}),t)})))()},changePayment:function(e){"BALANCE"==e.key?this.orderCreateData.is_balance=e.disable||1==this.orderCreateData.is_balance?0:1:this.orderCreateData.is_balance=0,e.disable||(this.paymentMethod=e.key),this.$forceUpdate()},openPasswordPopup:function(){var e=this;this.$refs.payPassword.open(),setTimeout((function(){e.isFocus=!0}),500)},showGoWeixin:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(uni.getStorageSync("member_address")){t.next=3;break}return e.$util.showToast({title:"请先选择地址！"}),t.abrupt("return",!1);case 3:if("h5"!=e.$util.getPlatform()||"WECHAT"!=e.paymentMethod||!e.isOnXianMaiApp){t.next=7;break}e.$refs.popupGoWeixin.open(),t.next=9;break;case 7:return t.next=9,e.orderCreate();case 9:case"end":return t.stop()}}),t)})))()},comfirmGoWeixin:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$refs.popupGoWeixin.close(),t.next=3,e.orderCreate();case 3:case"end":return t.stop()}}),t)})))()},setPayPassword:function(){this.$refs.payPassword.close(),this.$util.redirectTo("/otherpages/member/setting/setting_password",{back:"/promotionpages/pintuan/payment/payment",phonenum:this.orderPaymentData.member_account.mobile})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(e){var t=this;this.errMsg="",6==e.length&&(uni.showLoading({title:"加载中...",mask:!0}),this.$api.sendRequest({url:s.default.checkpaypasswordUrl,data:{pay_password:e},success:function(a){a.code>=0?(t.orderCreateData.pay_password=e,t.orderCreate()):(uni.hideLoading(),t.errMsg=a.message)},fail:function(e){uni.hideLoading()}}))}},onLoad:function(e){e&&e.seckill_id&&(this.seckill_id=e.seckill_id)},onShow:function(){var e=this;if(this.ischoiceWechatAdder)var t=setInterval((function(){e.postWechatAdder&&(console.log("this.choiceWechatAdderError",e.choiceWechatAdderError),e.choiceWechatAdderError&&e.choiceWechatAdderError&&(e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.$util.showToast({title:"获取微信地址失败，请手动添加地址",success:function(){setTimeout((function(){e.selectAddress()}),1500)}})),e.ischoiceWechatAdder=!1,e.postWechatAdder=!1,e.choiceWechatAdderError=!1,clearInterval(t),e.init())}),100);else this.init()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=r},9127:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"92c0":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var i={name:"mypOneInput",props:{value:{type:String,default:""},maxlength:{type:Number,default:4},autoFocus:{type:Boolean,default:!1},isPwd:{type:Boolean,default:!1},type:{type:String,default:"bottom"}},watch:{maxlength:{immediate:!0,handler:function(e){this.ranges=6===e?[1,2,3,4,5,6]:[1,2,3,4]}},value:{immediate:!0,handler:function(e){e!==this.inputValue&&(this.inputValue=e,this.toMakeAndCheck(e))}}},data:function(){return{inputValue:"",codeIndex:1,codeArr:[],ranges:[1,2,3,4]}},methods:{getVal:function(e){var t=e.detail.value;this.inputValue=t,this.$emit("input",t),this.toMakeAndCheck(t)},toMakeAndCheck:function(e){var t=e.split("");this.codeIndex=t.length+1,this.codeArr=t,this.codeIndex>Number(this.maxlength)&&this.$emit("finish",this.codeArr.join(""))},set:function(e){this.inputValue=e,this.toMakeAndCheck(e)},clear:function(){this.inputValue="",this.codeArr=[],this.codeIndex=1}}};t.default=i},"9ef8":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5d77fca1]{width:100%;text-align:center}[data-v-5d77fca1] .uni-navbar--border{border-bottom-width:0}',""]),e.exports=t},a3af:function(e,t,a){"use strict";a.r(t);var i=a("92c0"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},a971:function(e,t,a){var i=a("8f68");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("2d38abe0",i,!0,{sourceMap:!1,shadowMode:!1})},b156:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@-webkit-keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}@keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}.code-box[data-v-31a1a5ae]{text-align:center}.flex-box[data-v-31a1a5ae]{display:flex;justify-content:center;flex-wrap:wrap;position:relative}.flex-box .hide-input[data-v-31a1a5ae]{position:absolute;top:0;left:-100%;width:200%;height:100%;text-align:left;z-index:9;opacity:1}.flex-box .item[data-v-31a1a5ae]{position:relative;flex:1;margin-right:%?18?%;font-size:%?70?%;font-weight:700;color:#333;line-height:%?100?%}.flex-box .item[data-v-31a1a5ae]::before{content:"";padding-top:100%;display:block}.flex-box .item[data-v-31a1a5ae]:last-child{margin-right:0}.flex-box .middle[data-v-31a1a5ae]{border:none}.flex-box .box[data-v-31a1a5ae]{box-sizing:border-box;border:%?2?% solid #ccc;border-width:%?2?% 0 %?2?% %?2?%;margin-right:0}.flex-box .box[data-v-31a1a5ae]:first-of-type{border-top-left-radius:%?8?%;border-bottom-left-radius:%?8?%}.flex-box .box[data-v-31a1a5ae]:last-child{border-right:%?2?% solid #ccc;border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.flex-box .bottom[data-v-31a1a5ae]{box-sizing:border-box;border-bottom:1px solid #ddd}.flex-box .active[data-v-31a1a5ae]{border-color:#ddd}.flex-box .active .line[data-v-31a1a5ae]{display:block}.flex-box .line[data-v-31a1a5ae]{display:none;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?2?%;height:%?40?%;background:#333;-webkit-animation:twinkling-data-v-31a1a5ae 1s infinite ease;animation:twinkling-data-v-31a1a5ae 1s infinite ease}.flex-box .dot[data-v-31a1a5ae],\n.flex-box .number[data-v-31a1a5ae]{font-size:%?44?%;line-height:%?40?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.flex-box .bottom-line[data-v-31a1a5ae]{height:4px;background:#000;width:80%;position:absolute;border-radius:2px;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}',""]),e.exports=t},b517:function(e,t,a){"use strict";a.r(t);var i=a("c2f1"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},b8ea:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var o=i(a("9127")),n={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:o.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=n},c2f1:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var o=i(a("9008")),n=i(a("5e99")),s=i(a("2d01")),r=a("4b89"),d=i(a("de74")),c={components:{UniIcons:d.default,uniPopup:n.default},data:function(){return{time:null,navHeight:0,choiceWechatAdderError:!1,ischoiceWechatAdder:!1,postWechatAdder:!1,isOnXianMaiApp:r.isOnXianMaiApp}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle},otherPaymentMethods:function(){return[{icon:this.$util.img("public/static/youpin/paySuccess-icon.png"),name:"微信支付",desc:"",key:"WECHAT",disable:!1},{icon:this.$util.img("public/static/youpin/balance.png"),name:"余额支付",desc:"(当前余额￥".concat(this.orderPaymentData.member_account.balance_money,"）"),key:"BALANCE",disable:Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.seckillBuy.goods_money)}]}},onLoad:function(){var e=this;uni.getSystemInfo({success:function(t){var a=t.statusBarHeight+46;e.navHeight=a,e.isOnXianMaiApp||(e.navHeight=0)},fail:function(e){console.log(e)}})},onShow:function(){},mixins:[o.default,s.default],methods:{toShopDetail:function(e){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:e})},openPopup:function(){this.$refs.popup.open()},closePopup:function(){this.$refs.popup.close()},openCouponInstructionsPopup:function(){this.$refs.couponInstructions.open()},closeCouponInstructionsPopup:function(){this.$refs.couponInstructions.close()},closePopupCoupon:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},close_pay:function(){this.$refs.payPassword.close(),this.errMsg=""},toBack:function(){this.closePopup(),uni.navigateBack()},toWaitPayList:function(){this.$refs.popupToList.close(),this.$util.redirectTo("/pages/order/list/list?status=waitpay",{},"redirectTo"),uni.removeStorage({key:"orderCreateData",success:function(){}})},getChooseAddress:function(){this.orderPaymentData.member_address,this.selectAddress()},saveAddress:function(e){var t=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:e,success:function(e){e.code>=0||(t.$util.showToast({title:e.message}),t.choiceWechatAdderError=!0)},complete:function(){t.postWechatAdder=!0}})},imageError:function(){this.orderPaymentData.goodsInfo.sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),a=t.title,i=t.link,o=t.imageUrl;t.query;return this.$buriedPoint.pageShare(i,o,a)}};t.default=c},c490:function(e,t,a){"use strict";a.r(t);var i=a("287e"),o=a("b517");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("84e97"),a("1c3e"),a("6a1d");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"5d77fca1",null,!1,i["a"],void 0);t["default"]=r.exports},c8ec:function(e,t,a){"use strict";a.r(t);var i=a("61c4"),o=a("a3af");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("4210");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"31a1a5ae",null,!1,i["a"],void 0);t["default"]=r.exports},de74:function(e,t,a){"use strict";a.r(t);var i=a("37ff"),o=a("fefc");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("327f");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"65e5b6d2",null,!1,i["a"],void 0);t["default"]=r.exports},e133:function(e,t,a){var i=a("b156");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("356f4cc6",i,!0,{sourceMap:!1,shadowMode:!1})},e4fe:function(e,t,a){var i=a("9ef8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("ba5aaf06",i,!0,{sourceMap:!1,shadowMode:!1})},ebf2:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5d77fca1]{width:100%;text-align:center}[data-v-5d77fca1] .my-popup-dialog .uni-popup__wrapper-box{max-width:%?540?%;width:%?540?%;border-radius:%?20?%}[data-v-5d77fca1] .coupon-instructions .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%;border-radius:%?20?%}.popup-dialog[data-v-5d77fca1]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-5d77fca1]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-5d77fca1]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-5d77fca1]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-5d77fca1]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-5d77fca1]{color:var(--custom-brand-color);background:#fff;border:%?1?% solid var(--custom-brand-color)}.popup-dialog .popup-dialog-footer .button.red[data-v-5d77fca1]{color:#fff;background:var(--custom-brand-color)}',""]),e.exports=t},f408:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5d77fca1]{width:100%;text-align:center}.uni-list-cell[data-v-5d77fca1]{display:flex;justify-content:space-between}.align-right[data-v-5d77fca1]{text-align:right}.inline[data-v-5d77fca1]{display:inline!important}.order-container[data-v-5d77fca1]{padding-bottom:%?120?%}.order-container.safe-area[data-v-5d77fca1]{padding-bottom:%?188?%}.address-wrap[data-v-5d77fca1]{background:#fff;position:relative;min-height:%?100?%;max-height:%?140?%;display:flex;align-items:center}.address-wrap .icon[data-v-5d77fca1]{width:%?94?%;height:%?100?%;display:flex;justify-content:center;align-items:center}.address-wrap .icon uni-image[data-v-5d77fca1]{width:%?48?%;height:%?48?%}.address-wrap .address-info[data-v-5d77fca1]{width:%?656?%}.address-wrap .address-info .info[data-v-5d77fca1]{display:flex;padding-top:%?10?%}.address-wrap .address-info .info uni-text[data-v-5d77fca1]{overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;word-break:break-all;font-weight:700}.address-wrap .address-info .info uni-text[data-v-5d77fca1]:first-of-type{max-width:%?380?%;margin-right:%?32?%}.address-wrap .address-info .detail[data-v-5d77fca1]{width:%?508?%;padding-bottom:%?17?%;line-height:1.3;font-size:%?26?%}.address-wrap .address-empty[data-v-5d77fca1]{line-height:%?100?%;color:#999;font-size:%?26?%}.address-wrap .cell-more[data-v-5d77fca1]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?20?%}.address-wrap .cell-more .iconfont[data-v-5d77fca1]{color:#999}.mobile-wrap[data-v-5d77fca1]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.mobile-wrap .form-group .form-item[data-v-5d77fca1]{display:flex;line-height:%?50?%}.mobile-wrap .form-group .form-item .text[data-v-5d77fca1]{display:inline-block;line-height:%?50?%;padding-right:%?10?%}.mobile-wrap .form-group .form-item .placeholder[data-v-5d77fca1]{line-height:%?50?%}.mobile-wrap .form-group .form-item .input[data-v-5d77fca1]{flex:1;height:%?50?%;line-height:%?50?%}.order-cell[data-v-5d77fca1]{display:flex;margin:%?48?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-5d77fca1]{text-align:left}.order-cell .box[data-v-5d77fca1]{flex:1;line-height:inherit}.order-cell .box .textarea[data-v-5d77fca1]{height:%?40?%}.order-cell .iconfont[data-v-5d77fca1]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-5d77fca1]{padding:0;font-size:%?26?%;color:#333}.order-cell .order-pay uni-text[data-v-5d77fca1]{display:inline-block;margin-left:%?20?%}.order-cell .order-pay .pay-money[data-v-5d77fca1]{font-size:%?32?%;font-weight:700;margin-left:%?2?%}.site-wrap[data-v-5d77fca1]{margin:%?24?%;padding:0 %?24?%;border-radius:%?20?%;background:#fff;position:relative}.site-wrap .site-header[data-v-5d77fca1]{display:flex;align-items:center;height:%?88?%}.site-wrap .site-header .icondianpu[data-v-5d77fca1]{display:inline-block;line-height:1;margin-right:%?17?%;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap[data-v-5d77fca1]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-5d77fca1]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-5d77fca1]{width:%?180?%;height:%?180?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-5d77fca1]{width:100%;height:100%;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-5d77fca1]{flex:1;position:relative;max-width:calc(100% - %?200?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-5d77fca1]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;margin-bottom:%?30?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-5d77fca1]{width:100%;line-height:1.3;display:flex;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-5d77fca1]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-5d77fca1]{color:#999;font-size:%?24?%;line-height:1.3}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-5d77fca1]:last-of-type{text-align:right}.site-wrap .site-body .goods-wrap .goods-info .goods-price[data-v-5d77fca1]{color:#333;text-align:right;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price .price[data-v-5d77fca1]{font-size:%?28?%}.site-wrap .site-footer[data-v-5d77fca1]{padding-bottom:%?30?%}.site-wrap .site-footer .order-cell .tit[data-v-5d77fca1]{width:%?180?%}.site-wrap .site-footer .order-cell .store-promotion-box[data-v-5d77fca1]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.site-wrap .site-footer .order-cell .box uni-input[data-v-5d77fca1]{text-align:right;font-size:%?26?%}.site-wrap .site-footer .order-cell .box.text-overflow[data-v-5d77fca1]{max-width:calc(100% - %?180?%)}.site-wrap .site-footer .order-cell.my-coupon[data-v-5d77fca1]{padding:%?26?% 0;margin:0;text-align:right}.site-wrap .site-footer .order-cell.my-coupon .box.text-overflow[data-v-5d77fca1]{max-width:unset}.site-wrap .site-footer .order-cell[data-v-5d77fca1]:last-of-type{margin-bottom:0}.order-checkout[data-v-5d77fca1]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.order-checkout .order-cell .iconyuan_checkbox[data-v-5d77fca1],\r\n.order-checkout .order-cell .iconyuan_checked[data-v-5d77fca1]{font-size:%?38?%}.order-money .order-cell .box[data-v-5d77fca1]{padding:0}.order-money .order-cell .box .operator[data-v-5d77fca1]{font-size:%?24?%;margin-right:%?6?%}.order-submit[data-v-5d77fca1]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?98?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);display:flex;align-items:center}.order-submit.bottom-safe-area[data-v-5d77fca1]{padding-bottom:0!important;padding-bottom:constant(safe-area-inset-bottom)!important;padding-bottom:env(safe-area-inset-bottom)!important}.order-submit .order-settlement-info[data-v-5d77fca1]{flex:1;height:%?98?%;line-height:%?98?%;padding-left:%?25?%;font-size:%?28?%}.order-submit .order-settlement-info .money[data-v-5d77fca1]{font-size:%?48?%}.order-submit .submit-btn[data-v-5d77fca1]{height:%?80?%;margin-right:%?24?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-5d77fca1]{padding:0;width:%?200?%;background-color:var(--custom-brand-color)!important;height:%?80?%;line-height:%?80?%;font-size:%?28?%;border-radius:%?40?%}.popup[data-v-5d77fca1]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-5d77fca1]{height:%?90?%;display:flex;align-items:center;padding:0 %?30?%}.popup .popup-header > uni-view[data-v-5d77fca1]{flex:1;line-height:1}.popup .popup-header .tit[data-v-5d77fca1]{font-size:%?32?%;font-weight:600}.popup .popup-header .vice-tit[data-v-5d77fca1]{margin-right:%?20?%}.popup .popup-body[data-v-5d77fca1]{height:calc(100% - %?210?%);height:calc(100% - %?210?% - constant(safe-area-inset-bottom));height:calc(100% - %?210?% - env(safe-area-inset-bottom))}.popup .popup-footer[data-v-5d77fca1]{padding-bottom:0!important;padding-bottom:constant(safe-area-inset-bottom)!important;padding-bottom:env(safe-area-inset-bottom)!important}.popup .popup-footer .confirm-btn[data-v-5d77fca1]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?40?%;border-radius:%?40?%}.pay-password[data-v-5d77fca1]{width:80vw;background:#fff;box-sizing:border-box;border-radius:%?10?%;overflow:hidden;padding:%?60?% %?40?%;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.pay-password .popup-title[data-v-5d77fca1]{display:flex;align-items:center}.pay-password .popup-title .title[data-v-5d77fca1]{font-size:%?28?%;text-align:center;width:calc(100% - %?40?%);text-align:center}.pay-password .error-tips[data-v-5d77fca1]{text-align:center;width:100%}.pay-password .money-box[data-v-5d77fca1]{margin-top:%?50?%}.pay-password .money-box .total-fee[data-v-5d77fca1]{text-align:center;font-size:%?48?%;font-weight:700;color:#333}.pay-password .money-box .balance[data-v-5d77fca1]{font-size:%?24?%;color:#999;text-align:center}.pay-password .cha_close[data-v-5d77fca1]{width:%?30?%;height:%?30?%}.pay-password .tips[data-v-5d77fca1]{font-size:%?24?%;color:#999;text-align:center}.pay-password .btn[data-v-5d77fca1]{width:60%;margin:0 auto;margin-top:%?30?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;color:#fff;text-align:center;border:1px solid #fff}.pay-password .btn.white[data-v-5d77fca1]{margin-top:%?20?%;background-color:#fff!important}.pay-password .password-wrap[data-v-5d77fca1]{padding-top:%?20?%;width:90%;margin:0 auto}.pay-password .password-wrap .forget-password[data-v-5d77fca1]{margin-top:%?20?%;display:inline-block}.text-color[data-v-5d77fca1]{color:var(--custom-brand-color)}.text-color .money[data-v-5d77fca1]{font-weight:700;font-size:%?48?%}.nav[data-v-5d77fca1]{width:100%;overflow:hidden;position:fixed;top:0;left:0;z-index:10;background-color:#fff}.nav-title[data-v-5d77fca1]{width:100%;height:%?88?%;line-height:%?88?%;text-align:center;position:absolute;bottom:0;left:0;z-index:10}.nav .back[data-v-5d77fca1]{width:%?42?%;height:%?70?%;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?0?%;padding:0 %?24?%}.wrapper[data-v-5d77fca1]{padding-top:%?24?%}.coupon-instructions-close[data-v-5d77fca1]{display:flex;justify-content:flex-end;align-items:center}.coupon-instructions-btn[data-v-5d77fca1]{margin-right:%?20?%;color:#999;font-size:%?24?%}.coupon-close[data-v-5d77fca1]{color:#a0a1a7}.coupon-default[data-v-5d77fca1]{display:flex;align-items:center;justify-content:center;height:100%}.coupon-default uni-view[data-v-5d77fca1]{color:#999}.payment-methods .item[data-v-5d77fca1]{display:flex;justify-content:space-between;align-items:center;padding:%?10?% 0}.payment-methods .item.disable .title uni-text[data-v-5d77fca1]{color:#ccc}.payment-methods .item .icon[data-v-5d77fca1]{width:%?48?%;height:%?48?%}.payment-methods .item .title[data-v-5d77fca1]{flex:1;font-size:%?26?%;margin:0 %?10?%}.payment-methods .item .title .desc[data-v-5d77fca1]{color:var(--custom-brand-color);margin-left:%?10?%}',""]),e.exports=t},fefc:function(e,t,a){"use strict";a.r(t);var i=a("b8ea"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a}}]);