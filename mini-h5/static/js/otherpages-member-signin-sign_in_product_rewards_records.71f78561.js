(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-signin-sign_in_product_rewards_records"],{"0149":function(t,e,i){"use strict";var a=i("3149"),o=i.n(a);o.a},"0b48":function(t,e,i){"use strict";i.r(e);var a=i("b5ce"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"0fc48":function(t,e,i){var a=i("5c3d5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("84a3a7fe",a,!0,{sourceMap:!1,shadowMode:!1})},"2d01":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2e91":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-46873122]{width:100%;text-align:center}.yd-popup[data-v-46873122]{background:rgba(0,0,0,.4);width:100%;height:100%;z-index:998;position:fixed;top:0;left:0}.yd-popup .share-tip[data-v-46873122]{width:100%;height:%?447?%;display:block}',""]),t.exports=e},"2f73":function(t,e,i){"use strict";i.r(e);var a=i("e71a"),o=i("0b48");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("0149");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"46873122",null,!1,a["a"],void 0);e["default"]=r.exports},3149:function(t,e,i){var a=i("2e91");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("31685291",a,!0,{sourceMap:!1,shadowMode:!1})},3250:function(t,e,i){"use strict";var a=i("0fc48"),o=i.n(a);o.a},"327f":function(t,e,i){"use strict";var a=i("a971"),o=i.n(a);o.a},"37ff":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},o=[]},3905:function(t,e,i){var a=i("d468");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("0c5a1ad8",a,!0,{sourceMap:!1,shadowMode:!1})},4448:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={nsEmpty:i("dc6c").default,uniPopup:i("5e99").default,loadingCover:i("5510").default,diyShareNavigateH5:i("2f73").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"records",class:t.themeStyle,style:[t.themeColorVar]},[t.list.length?i("v-uni-view",{staticClass:"records-list"},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"records-list-one"},[i("v-uni-view",{staticClass:"records-list-one-top"},[i("v-uni-view",{staticClass:"records-list-one-top-info"},[i("v-uni-view",{staticClass:"records-list-one-top-info-left"},[i("v-uni-text",{staticClass:"records-list-one-top-info-left-label"},[t._v(t._s(e.round)+t._s(e.title))]),i("v-uni-text",{staticClass:"records-list-one-top-info-left-date"},[t._v(t._s(e.complete_date_start)+" 至 "+t._s(e.complete_date_end))])],1),i("v-uni-view",{staticClass:"records-list-one-top-info-right"},[t._v(t._s(e.is_send_reward?"奖励已发放":"待领取"))])],1),0==e.complete_status?i("v-uni-view",{staticClass:"records-list-one-top-desc"},[t._v("还需邀请"),i("v-uni-text",{staticClass:"records-list-one-top-desc-text"},[t._v(t._s(e.residue_invite_nums))]),t._v("位"),i("v-uni-text",{staticClass:"records-list-one-top-desc-text"},[t._v(t._s("new"==e.invite_limit?"新好友":"好友"))]),t._v("签到")],1):t._e()],1),1==e.complete_status?i("v-uni-view",{staticClass:"records-list-one-bottom"},[i("v-uni-view",{staticClass:"records-list-one-bottom-left"},[i("v-uni-text",{staticClass:"records-list-one-bottom-left-text"},[t._v("兑换码：")]),i("v-uni-text",{staticClass:"records-list-one-bottom-left-code"},[t._v(t._s(e.code))]),i("v-uni-image",{staticClass:"records-list-one-bottom-left-img",attrs:{src:t.$util.img("public/static/youpin/member/signin/copy.png")},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.copy(e.code)}}})],1),i("v-uni-view",{staticClass:"records-list-one-bottom-right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getAward(a)}}},[i("v-uni-text",{staticClass:"records-list-one-bottom-right-op"},[t._v("联系客服")])],1)],1):t._e(),2==e.complete_status?i("v-uni-view",{staticClass:"records-list-one-bottom"},[i("v-uni-view",{staticClass:"records-list-one-bottom-left"},[i("v-uni-text",{staticClass:"records-list-one-bottom-left-text"},[t._v("优惠券：")]),i("v-uni-text",{staticClass:"records-list-one-bottom-left-code"},[t._v(t._s(e.goodscoupon_name))])],1),i("v-uni-view",{staticClass:"records-list-one-bottom-right"},[e.is_old_data?t._e():i("v-uni-text",{staticClass:"records-list-one-bottom-right-op",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toGoodList(e.goodscoupon_type_id)}}},[t._v("去使用")])],1)],1):t._e(),3==e.complete_status?i("v-uni-view",{staticClass:"records-list-one-bottom"},[i("v-uni-view",{staticClass:"records-list-one-bottom-left"},[i("v-uni-text",{staticClass:"records-list-one-bottom-left-text"},[t._v("已兑换：")]),i("v-uni-text",{staticClass:"records-list-one-bottom-left-code"},[t._v(t._s(e.goodscoupon_goods))])],1),i("v-uni-view",{staticClass:"records-list-one-bottom-right"})],1):t._e(),0==e.complete_status?i("v-uni-view",{staticClass:"records-list-one-bottom"},[i("v-uni-view",{staticClass:"records-list-one-bottom-left"},[e.need_invite_nums>6?t._l(Array(6),(function(a,o){return i("v-uni-image",{key:o,staticClass:"records-list-one-bottom-left-head",attrs:{src:5==o?t.$util.img("public/static/youpin/member/signin/ellipsis.png"):e.invite_member[o]?e.invite_member[o]:t.$util.img("public/static/youpin/member/signin/add.png")}})})):t._l(Array(e.need_invite_nums),(function(a,o){return i("v-uni-image",{key:o,staticClass:"records-list-one-bottom-left-head",attrs:{src:e.invite_member[o]?e.invite_member[o]:t.$util.img("public/static/youpin/member/signin/add.png")}})}))],2),i("v-uni-view",{staticClass:"records-list-one-bottom-right"},[i("v-uni-button",{staticClass:"records-list-one-bottom-right-op",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.openSharePopup(e)}}},[t._v("邀请助力")])],1)],1):t._e()],1)})),1):[i("ns-empty",{attrs:{text:"暂无完成的签到任务",isIndex:!1,entrance:"signIn"}})],i("uni-popup",{ref:"signWechatPopup",staticClass:"sign-wechat-popup",attrs:{"mask-click":!1}},[i("v-uni-view",{staticClass:"sign-wechat"},[i("v-uni-view",{staticClass:"sign-wechat-top"},[i("v-uni-image",{staticClass:"sign-wechat-head",attrs:{src:t.$util.img(t.customerService.headpic)}}),i("v-uni-view",{staticClass:"sign-wechat-name"},[t._v(t._s(t.customerService.name))]),i("v-uni-view",{staticClass:"sign-wechat-tip"},[t._v("您好，长按或扫描下方二维码加我哟")]),i("v-uni-image",{staticClass:"sign-wechat-qrcode",attrs:{src:t.$util.img(t.customerService.qrcode),"show-menu-by-longpress":!0}})],1),i("v-uni-image",{staticClass:"sign-wechat-center",attrs:{src:t.$util.img("public/static/youpin/member/signin/partition.png"),mode:"widthFix"}}),i("v-uni-view",{staticClass:"sign-wechat-bottom"},[i("v-uni-view",{staticClass:"sign-wechat-bottom-info"},[i("v-uni-text",{staticClass:"sign-wechat-bottom-info-code"},[t._v("兑换码："+t._s(t.chooseData.code))]),i("v-uni-text",{staticClass:"sign-wechat-bottom-info-copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.chooseData.code)}}},[t._v("复制")])],1)],1),i("v-uni-image",{staticClass:"sign-wechat-close",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-close.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.signWechatPopup.close()}}})],1)],1),i("loading-cover",{ref:"loadingCover"}),i("diy-share-navigate-h5",{ref:"shareNavigateH5"})],2)},s=[]},"5c3d5":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-13a7296c]{width:100%;text-align:center}.sign-success-popup[data-v-13a7296c] .uni-popup__wrapper-box{overflow-y:unset!important}.sign-wechat-popup[data-v-13a7296c] .uni-popup__wrapper-box{overflow-y:unset!important}.sign-wechat-popup[data-v-13a7296c] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:transparent}.records-list[data-v-13a7296c]{padding-top:%?20?%;box-sizing:border-box}.records-list-one[data-v-13a7296c]{width:%?690?%;border-radius:%?20?%;background:#fff;padding:%?32?% %?20?%;box-sizing:border-box;margin:0 auto}.records-list-one[data-v-13a7296c]:not(:first-child){margin-top:%?20?%}.records-list-one-top[data-v-13a7296c]{padding-bottom:%?14?%;box-sizing:border-box;border-bottom:2px dashed #e5e5e5}.records-list-one-top-info[data-v-13a7296c]{display:flex;justify-content:space-between;align-items:flex-start}.records-list-one-top-info-left[data-v-13a7296c]{display:flex;flex-direction:column}.records-list-one-top-info-left-label[data-v-13a7296c]{font-size:%?32?%;font-weight:400;line-height:%?40?%;color:#383838}.records-list-one-top-info-left-date[data-v-13a7296c]{font-size:%?26?%;font-weight:400;line-height:%?40?%;color:grey;margin-top:%?18?%}.records-list-one-top-info-right[data-v-13a7296c]{font-size:%?26?%;font-weight:400;line-height:%?40?%;color:var(--custom-brand-color)}.records-list-one-top-desc[data-v-13a7296c]{margin-top:%?10?%;font-size:%?26?%;font-weight:400;color:grey}.records-list-one-top-desc-text[data-v-13a7296c]{color:var(--custom-brand-color)}.records-list-one-bottom[data-v-13a7296c]{display:flex;justify-content:space-between;align-items:center;padding-top:%?20?%;box-sizing:border-box}.records-list-one-bottom-left[data-v-13a7296c]{display:flex;align-items:center}.records-list-one-bottom-left-text[data-v-13a7296c]{font-size:%?28?%;font-weight:400;color:grey}.records-list-one-bottom-left-code[data-v-13a7296c]{font-size:%?28?%;font-weight:400;color:var(--custom-brand-color)}.records-list-one-bottom-left-img[data-v-13a7296c]{width:%?44?%;height:%?44?%;margin-left:%?10?%}.records-list-one-bottom-left-head[data-v-13a7296c]{width:%?56?%;height:%?56?%;border-radius:50%;margin-right:%?7?%}.records-list-one-bottom-right-op[data-v-13a7296c]{display:inline-block;margin:0;padding:0 %?24?%;box-sizing:border-box;height:%?56?%;line-height:%?56?%;border-radius:%?200?%;background:var(--custom-brand-color);font-size:%?28?%;font-weight:400;color:#fff}.sign-wechat[data-v-13a7296c]{width:%?600?%;border-radius:%?40?%}.sign-wechat-top[data-v-13a7296c]{height:%?680?%;border-radius:%?40?% %?40?% 0 0;background:#fff;display:flex;flex-direction:column;align-items:center;padding-top:%?62?%;box-sizing:border-box;position:relative}.sign-wechat-center[data-v-13a7296c]{width:100%;height:auto;display:block}.sign-wechat-bottom[data-v-13a7296c]{width:100%;background:#fff;border-radius:0 0 %?40?% %?40?%;text-align:center;padding-bottom:%?24?%;box-sizing:border-box}.sign-wechat-bottom-info[data-v-13a7296c]{display:inline-flex;align-items:center;height:%?70?%;border-radius:%?200?%;background:#f5f5f5;padding:0 %?24?%;box-sizing:border-box}.sign-wechat-bottom-info-code[data-v-13a7296c]{font-size:%?32?%;font-weight:400;color:#000}.sign-wechat-bottom-info-copy[data-v-13a7296c]{font-size:%?28?%;font-weight:400;color:var(--custom-brand-color);margin-left:%?32?%}.sign-wechat-head[data-v-13a7296c]{width:%?160?%;height:%?160?%;border-radius:50%}.sign-wechat-name[data-v-13a7296c]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:#383838;margin-top:%?26?%}.sign-wechat-tip[data-v-13a7296c]{font-size:%?28?%;font-weight:400;line-height:%?32.82?%;color:#a6a6a6;margin-top:%?14?%}.sign-wechat-qrcode[data-v-13a7296c]{width:%?320?%;height:%?320?%;margin-top:%?40?%}.sign-wechat-close[data-v-13a7296c]{width:%?48?%;height:%?48?%;position:absolute;left:50%;bottom:%?-80?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}',""]),t.exports=e},6175:function(t,e,i){"use strict";i.r(e);var a=i("4448"),o=i("90f5");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("70c4"),i("3250");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"13a7296c",null,!1,a["a"],void 0);e["default"]=r.exports},"70c4":function(t,e,i){"use strict";var a=i("3905"),o=i.n(a);o.a},"8f68":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},"90f5":function(t,e,i){"use strict";i.r(e);var a=i("d6d1"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},9127:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},a971:function(t,e,i){var a=i("8f68");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("2d38abe0",a,!0,{sourceMap:!1,shadowMode:!1})},b5ce:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),o={name:"diy-share-navigate-h5",data:function(){return{isShow:!1,isWeiXin:this.$util.isWeiXin(),isOnXianMaiApp:!1}},methods:{maskClose:function(t){0==t.target.offsetTop&&(this.isShow=!1)},open:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(this.isOnXianMaiApp){var i={title:t.desc?this.$util.shareTitleAddNickname(t.desc):"",desc:t.desc?t.desc:"",webpageUrl:t.webpageUrl?t.webpageUrl:"",thumbImage:t.imageUrl?this.$util.imageCdnResize(t.imageUrl,{image_process:"resize,w_300","x-oss-process":"image/resize,w_300"}):"",path:t.link};(0,a.shareMiniProgramSchemeGo)(i),e&&"function"==typeof e&&e()}else this.isShow=!0,e&&"function"==typeof e&&e()},close:function(){this.isShow=!1}},created:function(){this.isOnXianMaiApp=a.isOnXianMaiApp}};e.default=o},b8ea:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var o=a(i("9127")),s={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:o.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=s},d468:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"uni-page-body[data-v-13a7296c]{background:#fafafa}body.?%PAGE?%[data-v-13a7296c]{background:#fafafa}",""]),t.exports=e},d6d1:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("f7a5");var o=a(i("2634")),s=a(i("2fdc")),n=a(i("5e99")),r=a(i("2d01")),c=a(i("de74")),l={components:{uniPopup:n.default,UniIcons:c.default},data:function(){return{list:[],sign_activity_id:null,complete_id:null,customerService:{},chooseData:{},share_title:"",share_img:""}},onLoad:function(t){var e=this;return(0,s.default)((0,o.default)().mark((function i(){return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return e.sign_activity_id=t.sign_activity_id||0,i.next=3,e.getData();case 3:case"end":return i.stop()}}),i)})))()},computed:{},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"签到达标记录"})},onReady:function(){},mixins:[r.default],methods:{getData:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.signinCompleteLogsUrl,async:!1,data:{sign_activity_id:t.sign_activity_id}});case 3:i=e.sent,0==i.code&&(t.list=i.data.map((function(t,e){return t.round="第".concat(i.data.length-e,"轮"),t}))),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](0);case 10:case"end":return e.stop()}}),e,null,[[0,8]])})))()},getAward:function(t){this.list[t].is_send_reward||(this.chooseData=this.list[t],this.customerService=this.list[t].cs_info,this.$refs.signWechatPopup.open())},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/member/signin/sign_in_product_rewards",this.share_title||"一起来签到","",{sign_activity_id:this.sign_activity_id,complete_id:this.complete_id},this.$util.img(this.share_img))},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},openSharePopup:function(t){this.complete_id=t.id,this.share_title=t.share_title,this.share_img=t.share_img;var e=this.getSharePageParams();this.setWechatShare(),this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(e)}},onShareAppMessage:function(t){t.target.dataset.complete_id&&(this.complete_id=t.target.dataset.complete_id),t.target.dataset.share_title&&(this.share_title=t.target.dataset.share_title),t.target.dataset.share_img&&(this.share_img=t.target.dataset.share_img);var e=this.getSharePageParams(),i=e.title,a=e.link,o=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,o,i)}};e.default=l},de74:function(t,e,i){"use strict";i.r(e);var a=i("37ff"),o=i("fefc");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("327f");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"65e5b6d2",null,!1,a["a"],void 0);e["default"]=r.exports},e71a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],staticClass:"yd-popup",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.maskClose.apply(void 0,arguments)}}},[t.isWeiXin?i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/weixin-share-tip.png")}}):i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/browser-share-tip.png")}})],1)},o=[]},fefc:function(t,e,i){"use strict";i.r(e);var a=i("b8ea"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a}}]);