(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-join-redeem_mall-redeem_mall"],{"030f":function(e,t,o){"use strict";o("6a54");var n=o("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("d4b5");var a=n(o("2634")),i=n(o("2fdc")),r=o("4b89"),s=n(o("d817")),c=n(o("2d01")),l=n(o("85bf")),d={name:"redeem_mall",components:{UniNavBar:s.default},mixins:[c.default],data:function(){return{title:"贡献值兑换",member_points:0,page:1,page_size:10,loading:!1,finished:!1,datalist:[],one_data:{},isOnXianMaiApp:r.isOnXianMaiApp}},onLoad:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$langConfig.refresh(),t.next=3,l.default.wait_staticLogin_success();case 3:return t.next=5,e.getDataList();case 5:case"end":return t.stop()}}),t)})))()},onReachBottom:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.finished&&!e.loading){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.getDataList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDataList:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){var o,n;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.finished&&!e.loading){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,t.prev=3,t.next=6,e.$api.sendRequest({url:e.$apiUrl.exchangeListUrl,async:!1,data:{page:e.page,page_size:e.page_size}});case 6:if(o=t.sent,e.$refs.loadingCover&&e.$refs.loadingCover.hide(),0!=o.code)e.loading=!1,e.finished=!0,e.$util.showToast({title:o.message});else if(0==o.code){for(e.member_points=o.data.member_points,n=0;n<o.data.list.length;n++)e.$set(e.datalist,e.datalist.length,o.data.list[n]);e.loading=!1,e.datalist.length>=o.data.count&&(e.finished=!0),e.page=e.page+1}t.next=17;break;case 11:t.prev=11,t.t0=t["catch"](3),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.loading=!1,e.finished=!0,e.$util.showToast({title:t.t0.message});case 17:case"end":return t.stop()}}),t,null,[[3,11]])})))()},imageError:function(e,t){e.goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},toContributionRecords:function(){console.log("aaaaa"),this.$util.redirectTo("/otherpages/member/join/contribution_records/contribution_records")},toGoodsCoupon:function(e){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:e.goodscoupon_type_id})},toExchange:function(e){this.one_data=JSON.parse(JSON.stringify(e)),this.one_data.is_allow=this.member_points>=this.one_data.points,this.$refs.exchangePopupRef.open()},closeExchangePopup:function(){this.$refs.exchangePopupRef.close()},resetData:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!1,e.finished=!1,e.page=1,e.datalist=[],t.next=6,e.getDataList();case 6:case"end":return t.stop()}}),t)})))()},confirmExchange:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){var o;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.one_data.is_allow){t.next=3;break}return e.$util.showToast({title:"当前你的贡献值余额不足，无法兑换该物品"}),t.abrupt("return");case 3:return uni.showLoading({title:"兑换中",mask:!0}),t.prev=4,t.next=7,e.$api.sendRequest({url:e.$apiUrl.exchangeUrl,async:!1,data:{exchange_goods_id:e.one_data.exchange_goods_id}});case 7:if(o=t.sent,uni.hideLoading(),0==o.code){t.next=13;break}e.$util.showToast({title:o.message}),t.next=17;break;case 13:return e.$refs.exchangePopupRef.close(),e.$refs.goodsCouponPopupRef.open(),t.next=17,e.resetData();case 17:t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](4),uni.hideLoading(),e.$util.showToast({title:t.t0.message});case 23:case"end":return t.stop()}}),t,null,[[4,19]])})))()},toUse:function(){this.isOnXianMaiApp?(0,r.launchMiniProgram)(this.one_data.wx_url):window.location.href=this.one_data.wx_url}}};t.default=d},"2b09":function(e,t,o){var n=o("c7cf");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=o("967d").default;a("5756ae8c",n,!0,{sourceMap:!1,shadowMode:!1})},"2d01":function(e,t,o){"use strict";o("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"658a":function(e,t,o){"use strict";o.r(t);var n=o("030f"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(i);t["default"]=a.a},a046:function(e,t,o){"use strict";o.r(t);var n=o("b51b"),a=o("658a");for(var i in a)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(i);o("eb5f");var r=o("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"35ce4377",null,!1,n["a"],void 0);t["default"]=s.exports},b51b:function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"c",(function(){return i})),o.d(t,"a",(function(){return n}));var n={uniNavBar:o("d817").default,uniPopup:o("5e99").default,loadingCover:o("5510").default},a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-view",{staticClass:"redeem-mall",style:[e.themeColorVar],attrs:{"data-theme":e.themeStyle}},[o("uni-nav-bar",{attrs:{"left-icon":"back",border:!1,fixed:!0,statusBar:!0,backgroundColor:"transparent",leftText:e.title,color:"#fff"},on:{clickLeft:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.goBack.apply(void 0,arguments)}},scopedSlots:e._u([{key:"right",fn:function(){return[o("v-uni-view",{staticClass:"nav-points",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toContributionRecords.apply(void 0,arguments)}}},[e._v("贡献值:"),o("v-uni-text",{staticClass:"nav-points-text"},[e._v(e._s(e.member_points||0))])],1)]},proxy:!0}])}),o("v-uni-view",{staticClass:"bg-img",style:{backgroundImage:"url("+e.$util.img("public/static/youpin/member/contribution/beijing.png")+")"}}),o("v-uni-view",{staticClass:"container"},[o("v-uni-view",{staticClass:"container-list"},e._l(e.datalist,(function(t,n){return o("v-uni-view",{key:n,staticClass:"container-list-one"},[o("v-uni-view",{staticClass:"container-list-one-info",on:{click:function(o){arguments[0]=o=e.$handleEvent(o),e.toGoodsCoupon(t)}}},[o("v-uni-view",{staticClass:"container-list-one-info-top"},[o("v-uni-view",{staticClass:"container-list-one-info-top-title"},[e._v("满"+e._s(Number(t.at_least))+"减"+e._s(Number(t.money)))]),o("v-uni-view",{staticClass:"container-list-one-info-top-desc"},[e._v(e._s(t.use_scenario_text))])],1),o("v-uni-view",{staticClass:"container-list-one-info-bottom"},[o("v-uni-text",{staticClass:"container-list-one-info-bottom-left-circle"}),o("v-uni-text",{staticClass:"container-list-one-info-bottom-right-circle"}),e._l(t.use_goods,(function(t,n){return o("v-uni-image",{key:n,staticClass:"container-list-one-info-bottom-img",attrs:{src:e.$util.img(t.goods_image),mode:"aspectFill"},on:{error:function(o){arguments[0]=o=e.$handleEvent(o),e.imageError(t,n)}}})}))],2)],1),o("v-uni-view",{staticClass:"container-list-one-op"},[o("v-uni-text",{staticClass:"container-list-one-op-num"},[e._v(e._s(t.points)+" 贡献值")]),o("v-uni-button",{staticClass:"container-list-one-op-button",attrs:{size:"mini"},on:{click:function(o){arguments[0]=o=e.$handleEvent(o),e.toExchange(t)}}},[e._v("兑换")])],1)],1)})),1)],1),o("uni-popup",{ref:"exchangePopupRef",attrs:{type:"center",maskClick:!1}},[o("v-uni-view",{staticClass:"exchange-popup"},[o("v-uni-view",{staticClass:"exchange-popup-top"},[o("v-uni-view",{staticClass:"exchange-popup-top-title"},[e._v("满"+e._s(Number(e.one_data.at_least))+"减"+e._s(Number(e.one_data.money)))]),o("v-uni-view",{staticClass:"exchange-popup-top-desc"},[e._v(e._s(e.one_data.use_scenario_text))])],1),o("v-uni-view",{staticClass:"exchange-popup-bottom"},[o("v-uni-view",{staticClass:"exchange-popup-bottom-desc"},[e._v(e._s(e.one_data.is_allow?"当前你的余额为"+e.member_points+"，确定要消耗"+e.one_data.points+"贡献值兑换该商品？":"当前你的贡献值余额不足，无法兑换该物品"))]),o("v-uni-view",{staticClass:"exchange-popup-bottom-op"},[e.one_data.is_allow?[o("v-uni-button",{staticClass:"exchange-popup-bottom-op-button exchange-popup-bottom-op-cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeExchangePopup.apply(void 0,arguments)}}},[e._v("取消")]),o("v-uni-button",{staticClass:"exchange-popup-bottom-op-button exchange-popup-bottom-op-submit",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmExchange.apply(void 0,arguments)}}},[e._v("兑换")])]:o("v-uni-button",{staticClass:"exchange-popup-bottom-op-button exchange-popup-bottom-op-cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeExchangePopup.apply(void 0,arguments)}}},[e._v("返回")])],2)],1)],1)],1),o("uni-popup",{ref:"goodsCouponPopupRef",attrs:{type:"center",maskClick:!1}},[o("v-uni-view",{staticClass:"to-popup"},[o("v-uni-view",{staticClass:"to-popup-title"},[e._v("兑换成功，请去先迈商城小程序查看并使用该优惠券")]),o("v-uni-view",{staticClass:"to-popup-bottom-op"},[o("v-uni-button",{staticClass:"to-popup-bottom-op-button to-popup-bottom-op-cancel",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.goodsCouponPopupRef.close()}}},[e._v("返回")]),o("v-uni-button",{staticClass:"to-popup-bottom-op-button to-popup-bottom-op-submit",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toUse.apply(void 0,arguments)}}},[e._v("去使用")])],1)],1)],1),o("loading-cover",{ref:"loadingCover"})],1)},i=[]},c7cf:function(e,t,o){var n=o("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-35ce4377]{width:100%;text-align:center}[data-v-35ce4377] .uni-navbar__header-btns-left{width:%?200?%!important}[data-v-35ce4377] .uni-navbar__header-btns{width:%?200?%!important}.redeem-mall[data-v-35ce4377]{position:relative;min-height:100vh;background:linear-gradient(180deg,var(--custom-brand-color),var(--custom-brand-color-20));box-sizing:border-box}.redeem-mall .bg-img[data-v-35ce4377]{width:%?750?%;height:%?422?%;background-repeat:no-repeat;background-size:100% 100%;position:absolute;top:0;left:0}.redeem-mall .nav-points[data-v-35ce4377]{font-size:%?24?%;height:44px;line-height:44px;padding-top:%?2?%;box-sizing:border-box;color:#fff}.redeem-mall .nav-points-text[data-v-35ce4377]{font-weight:700}.redeem-mall .container[data-v-35ce4377]{width:%?702?%;margin:0 auto;position:relative}.redeem-mall .container-list[data-v-35ce4377]{width:100%;display:flex;justify-content:space-between;flex-wrap:wrap}.redeem-mall .container-list-one[data-v-35ce4377]{width:%?343?%;background-color:#fff;border-radius:%?12?%;padding:%?12?%;box-sizing:border-box;margin-bottom:%?20?%}.redeem-mall .container-list-one-info[data-v-35ce4377]{background:linear-gradient(158.43158deg,var(--custom-brand-color-70) -7%,var(--custom-brand-color) 60%);border-radius:%?10?%;padding-top:%?32?%;padding-bottom:%?20?%;box-sizing:border-box}.redeem-mall .container-list-one-info-top[data-v-35ce4377]{box-sizing:border-box;padding-bottom:%?20?%;position:relative}.redeem-mall .container-list-one-info-top-title[data-v-35ce4377]{font-size:%?36?%;font-weight:700;color:#ffec25;margin:0;text-align:center}.redeem-mall .container-list-one-info-top-desc[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:#fff;margin:0;text-align:center;margin-top:%?5?%}.redeem-mall .container-list-one-info-bottom[data-v-35ce4377]{display:flex;justify-content:center;align-items:center;padding-top:%?20?%;box-sizing:border-box;border-top:%?2?% dashed hsla(0,0%,100%,.6);position:relative;height:%?100?%}.redeem-mall .container-list-one-info-bottom-left-circle[data-v-35ce4377]{width:%?12?%;height:%?20?%;background:#fff;border-radius:0 %?10?% %?10?% 0;position:absolute;left:0;top:%?-12?%}.redeem-mall .container-list-one-info-bottom-right-circle[data-v-35ce4377]{width:%?12?%;height:%?20?%;background:#fff;border-radius:%?12?% 0 0 %?12?%;position:absolute;right:0;top:%?-12?%}.redeem-mall .container-list-one-info-bottom-img[data-v-35ce4377]{width:%?80?%;height:%?80?%;border-radius:%?10?%}.redeem-mall .container-list-one-info-bottom-img[data-v-35ce4377]:not(:first-child){margin-left:%?10?%}.redeem-mall .container-list-one-op[data-v-35ce4377]{display:flex;justify-content:space-between;align-items:center;margin-top:%?20?%;padding-left:%?20?%;box-sizing:border-box}.redeem-mall .container-list-one-op-num[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:var(--custom-brand-color)}.redeem-mall .container-list-one-op-button[data-v-35ce4377]{width:%?100?%;color:#fff;font-size:%?26?%;font-weight:400;background:var(--custom-brand-color);border:none;height:%?50?%;line-height:%?50?%;padding:0;box-sizing:border-box}.redeem-mall .exchange-popup[data-v-35ce4377]{width:%?450?%;box-sizing:border-box}.redeem-mall .exchange-popup-father[data-v-35ce4377]{background:transparent}.redeem-mall .exchange-popup-top[data-v-35ce4377]{background:var(--custom-brand-color);padding-top:%?32?%;padding-bottom:%?16?%;box-sizing:border-box}.redeem-mall .exchange-popup-top-title[data-v-35ce4377]{font-size:%?32?%;font-weight:700;color:#ffec25;margin:0;text-align:center}.redeem-mall .exchange-popup-top-desc[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:#fff;text-align:center;margin:0;margin-top:%?10?%}.redeem-mall .exchange-popup-bottom[data-v-35ce4377]{background-color:#fff;padding:%?24?%;box-sizing:border-box}.redeem-mall .exchange-popup-bottom-desc[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:#383838;margin:0;text-align:center}.redeem-mall .exchange-popup-bottom-op[data-v-35ce4377]{display:flex;justify-content:center;margin-top:%?40?%}.redeem-mall .exchange-popup-bottom-op-button[data-v-35ce4377]{display:flex;justify-content:center;align-items:center;width:%?190?%;height:%?64?%;margin:0;padding:0;background:linear-gradient(141.63deg,var(--custom-brand-color),var(--custom-brand-color-70));border-radius:%?28?%;font-size:%?24?%;font-weight:500;color:#fff;border:none}.redeem-mall .exchange-popup-bottom-op-cancel[data-v-35ce4377]{background:var(--custom-brand-color-20);color:var(--custom-brand-color)}.redeem-mall .exchange-popup-bottom-op-submit[data-v-35ce4377]{margin-left:%?10?%}.redeem-mall .to-popup[data-v-35ce4377]{width:%?450?%;box-sizing:border-box;padding:%?32?%}.redeem-mall .to-popup-title[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:#333;text-align:center}.redeem-mall .to-popup-bottom[data-v-35ce4377]{background-color:#fff;padding:%?24?%;box-sizing:border-box}.redeem-mall .to-popup-bottom-desc[data-v-35ce4377]{font-size:%?28?%;font-weight:400;color:#383838;margin:0;text-align:center}.redeem-mall .to-popup-bottom-op[data-v-35ce4377]{display:flex;justify-content:center;margin-top:%?40?%}.redeem-mall .to-popup-bottom-op-button[data-v-35ce4377]{display:flex;justify-content:center;align-items:center;margin:0;padding:0;width:%?190?%;height:%?64?%;background:linear-gradient(141.63deg,var(--custom-brand-color),var(--custom-brand-color-70));border-radius:%?28?%;font-size:%?24?%;font-weight:500;color:#fff;border:none}.redeem-mall .to-popup-bottom-op-cancel[data-v-35ce4377]{background:var(--custom-brand-color-20);color:var(--custom-brand-color)}.redeem-mall .to-popup-bottom-op-submit[data-v-35ce4377]{margin-left:%?10?%}',""]),e.exports=t},eb5f:function(e,t,o){"use strict";var n=o("2b09"),a=o.n(n);a.a}}]);