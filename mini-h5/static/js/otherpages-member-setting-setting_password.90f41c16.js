(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-setting-setting_password"],{"2d01":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},4607:function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0ebf8474]{width:100%;text-align:center}.form[data-v-0ebf8474]{background:#fff;margin-top:%?20?%;padding:0 %?10?%}.phone[data-v-0ebf8474]{display:flex;align-items:center;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.phone .phonenum[data-v-0ebf8474]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.phone .pnum[data-v-0ebf8474]{font-size:%?30?%;color:#333}.phonecode[data-v-0ebf8474]{display:flex;align-items:center;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.phonecode .phonenum[data-v-0ebf8474]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.phonecode uni-input[data-v-0ebf8474]{border:none;width:40%}.phonecode .getphonecode[data-v-0ebf8474]{width:%?145.5?%;height:%?45.5?%;text-align:center;border:%?1?% solid var(--custom-brand-color);color:var(--custom-brand-color);font-size:%?24?%;border-radius:%?10?%;line-height:%?44?%;margin-left:%?30?%}.paycode[data-v-0ebf8474]{display:flex;align-items:center;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.paycode .phonenum[data-v-0ebf8474]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.paycode uni-input[data-v-0ebf8474]{border:none;width:60%}.comfirmcode[data-v-0ebf8474]{display:flex;align-items:center;height:%?100?%;margin:%?20?%}.comfirmcode .phonenum[data-v-0ebf8474]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.comfirmcode uni-input[data-v-0ebf8474]{border:none;width:40%}.comfirm[data-v-0ebf8474]{width:%?654?%;height:%?80?%;background:var(--custom-brand-color);text-align:center;line-height:%?80?%;font-size:%?32?%;color:#fff;border-radius:%?50?%;margin:%?50?% auto 0}',""]),e.exports=t},"7bbc":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{class:e.themeStyle,style:[e.themeColorVar]},[n("v-uni-view",{staticClass:"form"},[n("v-uni-view",{staticClass:"phone"},[n("v-uni-text",{staticClass:"phonenum"},[e._v("手机号")]),n("v-uni-text",{staticClass:"pnum"},[e._v(e._s(e.phonenum))])],1),n("v-uni-view",{staticClass:"phonecode"},[n("v-uni-text",{staticClass:"phonenum"},[e._v("验证码")]),n("v-uni-input",{attrs:{type:"text",value:"",placeholder:"请输入验证码"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.codeInput.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"getphonecode"},[1==e.showText?n("v-uni-text",{staticClass:"getcode",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getYzCode()}}},[e._v("获取验证码")]):n("v-uni-text",{staticClass:"getcode",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getYzCode()}}},[e._v(e._s(e.second)+"s")])],1)],1),n("v-uni-view",{staticClass:"paycode"},[n("v-uni-text",{staticClass:"phonenum"},[e._v("支付密码")]),n("v-uni-input",{attrs:{type:"number",value:"",placeholder:"请输入六位的支付密码",maxlength:"6"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"comfirmcode"},[n("v-uni-text",{staticClass:"phonenum"},[e._v("确认密码")]),n("v-uni-input",{attrs:{type:"number",value:"",placeholder:"请重新输入密码",maxlength:"6"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.getInput.apply(void 0,arguments)}}})],1)],1),n("v-uni-view",{staticClass:"comfirm",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPassword()}}},[n("v-uni-text",[e._v("提交")])],1)],1)},o=[]},"9b4c":function(e,t,n){"use strict";n.r(t);var i=n("fa1e"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);t["default"]=o.a},b3ae:function(e,t,n){var i=n("4607");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=n("967d").default;o("267e3738",i,!0,{sourceMap:!1,shadowMode:!1})},e30b:function(e,t,n){"use strict";n.r(t);var i=n("7bbc"),o=n("9b4c");for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);n("e591");var s=n("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"0ebf8474",null,!1,i["a"],void 0);t["default"]=r.exports},e591:function(e,t,n){"use strict";var i=n("b3ae"),o=n.n(i);o.a},fa1e:function(e,t,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("23f4"),n("7d2f"),n("5c47"),n("9c4e"),n("ab80"),n("0506"),n("f7a5");var o=i(n("2d01")),a={components:{},mixins:[o.default],data:function(){return{phonenum:"",password:"",passwordgain:"",code:"",second:60,showText:!0,codeType:4}},onLoad:function(e){console.log(e.phonenum),this.phonenum=e.phonenum},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"支付密码设置"})},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},watch:{},methods:{getYzCode:function(){var e=this;this.$api.sendRequest({url:this.$apiUrl.sendMobileCodeUrl,data:{mobile:this.phonenum,type:this.codeType},success:function(t){var n=t.message;if(0==t.code){e.showText=!1;var i=setInterval((function(){var t=--e.second;e.second=t<10?"0"+t:t}),1e3);setTimeout((function(){clearInterval(i),e.second=60,e.showText=!0}),6e4)}else e.$util.showToast({title:n})},fail:function(){}})},codeInput:function(e){this.code=e.target.value},onInput:function(e){var t=e.target.value,n=t.charAt(t.length-1),i=new RegExp("^[0-9]*$");if(!i.test(n))return t.slice(0,-1);this.password=e.target.value},getInput:function(e){var t=e.target.value,n=t.charAt(t.length-1),i=new RegExp("^[0-9]*$");if(!i.test(n))return t.slice(0,-1);this.passwordgain=e.target.value},setPassword:function(){var e=this;this.$api.sendRequest({url:this.$apiUrl.changePayPassword,data:{mobile:this.phonenum,code:this.code,new_password:this.password,confirm_password:this.passwordgain,type:this.codeType},success:function(t){var n=t.message;0==t.code?(e.$util.showToast({title:"设置成功"}),setTimeout((function(){e.$util.goBack()}),1e3)):e.$util.showToast({title:n})},fail:function(){}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),n=t.title,i=t.link,o=t.imageUrl;t.query;return this.$buriedPoint.pageShare(i,o,n)}};t.default=a}}]);