(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-diy-diy-diy~otherpages-shop-app-home-app-home~otherpages-shop-home-home~promotionpages-ta~32aa736f"],{"0e86":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={}},"2b8a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"uv-waterfall"},[n("v-uni-view",{staticClass:"uv-waterfall__gap_left",style:[t.gapLeftStyle]}),t.columnNum>=1?[n("v-uni-view",{staticClass:"uv-waterfall__column",attrs:{id:"uv-waterfall-1"}},[t._t("list1")],2)]:t._e(),t.columnNum>=2?[n("v-uni-view",{staticClass:"uv-waterfall__gap_center",style:[t.gapCenterStyle]}),n("v-uni-view",{staticClass:"uv-waterfall__column",attrs:{id:"uv-waterfall-2"}},[t._t("list2")],2)]:t._e(),t.columnNum>=3?[n("v-uni-view",{staticClass:"uv-waterfall__gap_center",style:[t.gapCenterStyle]}),n("v-uni-view",{staticClass:"uv-waterfall__column",attrs:{id:"uv-waterfall-3"}},[t._t("list3")],2)]:t._e(),t.columnNum>=4?[n("v-uni-view",{staticClass:"uv-waterfall__gap_center",style:[t.gapCenterStyle]}),n("v-uni-view",{staticClass:"uv-waterfall__column",attrs:{id:"uv-waterfall-4"}},[t._t("list4")],2)]:t._e(),t.columnNum>=5?[n("v-uni-view",{staticClass:"uv-waterfall__gap_center",style:[t.gapCenterStyle]}),n("v-uni-view",{staticClass:"uv-waterfall__column",attrs:{id:"uv-waterfall-5"}},[t._t("list5")],2)]:t._e(),n("v-uni-view",{staticClass:"uv-waterfall__gap_right",style:[t.gapRightStyle]})],2)},a=[]},"2d01":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2fd2":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-12eeb4b9]{width:100%;text-align:center}uni-view[data-v-12eeb4b9], uni-scroll-view[data-v-12eeb4b9], uni-swiper-item[data-v-12eeb4b9]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.uv-waterfall[data-v-12eeb4b9]{display:flex;flex-direction:row;align-items:flex-start}.uv-waterfall__column[data-v-12eeb4b9]{display:flex;flex-direction:column;flex:1;height:auto}',""]),t.exports=e},3639:function(t,e,n){n("bf0f"),n("18f7"),n("d0af"),n("de6c"),n("6a54"),n("9a2c");var i=n("bdbb")["default"];function a(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(a=function(t){return t?n:e})(t)}t.exports=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!==typeof t)return{default:t};var n=a(e);if(n&&n.has(t))return n.get(t);var r={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in t)if("default"!==l&&Object.prototype.hasOwnProperty.call(t,l)){var o=u?Object.getOwnPropertyDescriptor(t,l):null;o&&(o.get||o.set)?Object.defineProperty(r,l,o):r[l]=t[l]}return r["default"]=t,n&&n.set(t,r),r},t.exports.__esModule=!0,t.exports["default"]=t.exports},4269:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("5de6")),r=i(n("2634")),u=i(n("9b1b")),l=i(n("2fdc"));n("c223"),n("f7a5"),n("aa9c"),n("dd2b"),n("8f71"),n("bf0f"),n("fd3c"),n("aa77"),n("bd06");var o=i(n("0e86")),s=i(n("5b75")),c=i(n("5e6b")),f={name:"uv-waterfall",mixins:[o.default,s.default,c.default],data:function(){return{list1:[],list2:[],list3:[],list4:[],list5:[],tempList:[]}},computed:{copyValue:function(){return this.$uv.deepClone(this.value)},columnNum:function(){return this.columnCount<=0?0:this.columnCount>=5?5:this.columnCount},gapLeftStyle:function(){var t={};return t.width=this.$uv.addUnit(this.leftGap),t},gapRightStyle:function(){var t={};return t.width=this.$uv.addUnit(this.rightGap),t},gapCenterStyle:function(){var t={};return t.width=this.$uv.addUnit(this.columnGap),t},nvueWaterfallStyle:function(){var t={};return 0!=this.width&&(t.width=this.$uv.addUnit(this.width)),0!=this.height&&(t.height=this.$uv.addUnit(this.height)),t.width||(t.width=this.$uv.addUnit(this.$uv.sys().windowWidth,"px")),t.height||(t.height=this.$uv.addUnit(this.$uv.sys().windowHeight,"px")),this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}},watch:{copyValue:function(t,e){if(0!=t.length){var n=Array.isArray(e)&&e.length>0?e.length:0;this.tempList=this.tempList.concat(this.$uv.deepClone(t.slice(n))),this.splitData()}}},mounted:function(){this.tempList=this.$uv.deepClone(this.copyValue),this.splitData()},methods:{scrolltolower:function(t){var e=this;this.$uv.sleep(30).then((function(){e.$emit("scrolltolower")}))},splitData:function(){var t=this;return(0,l.default)((0,r.default)().mark((function e(){var n,i,a,l,o,s,c;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=[],i={},t.tempList.length){e.next=4;break}return e.abrupt("return");case 4:a=1;case 5:if(!(a<=t.columnNum)){e.next=13;break}return e.next=8,t.$uvGetRect("#uv-waterfall-".concat(a));case 8:l=e.sent,n.push((0,u.default)((0,u.default)({},l),{},{name:a}));case 10:a++,e.next=5;break;case 13:if(o=t.tempList[0],o){e.next=16;break}return e.abrupt("return");case 16:if(s=t.getMin(n),o.width=s.width,t["list".concat(s.name)].push(o),i.name="list".concat(s.name),i.value=o,t.$emit("changeList",i),t.tempList.splice(0,1),!t.tempList.length){e.next=30;break}return c=t.addTime,e.next=27,t.$uv.sleep(c);case 27:t.splitData(),e.next=31;break;case 30:t.$emit("finish");case 31:case"end":return e.stop()}}),e)})))()},getMin:function(t){var e=this,n=null,i=t.filter((function(t){return 0==t.height}));if(i.length){var r=[];t.map((function(t,n){r.push({len:e["list".concat(n+1)].length,item:t})}));var u=Math.min.apply(Math,r.map((function(t){return t.len})));try{var l=r.find((function(t){return t.len==u&&0==t.item.height})),o=l.item;n=o}catch(v){var s=r.find((function(t){return 0==t.item.height})),c=s.item;n=c}}else{var f=Math.min.apply(Math,t.map((function(t){return t.height}))),d=t.filter((function(t){return t.height==f})),p=(0,a.default)(d,1),h=p[0];n=h}return n},clear:function(){var t=this;return(0,l.default)((0,r.default)().mark((function e(){var n;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(n=0;n<t.columnCount;n++)t["list".concat(n+1)]=[];return t.$emit("input",[]),t.tempList=[],e.next=5,t.$uv.sleep(300);case 5:t.$emit("clear");case 6:case"end":return e.stop()}}),e)})))()},remove:function(t){for(var e=this,n=-1,i=1;i<=this.columnCount;i++)n=this["list".concat(i)].findIndex((function(n){return n[e.idKey]==t})),-1!=n&&this["list".concat(i)].splice(n,1);n=this.value.findIndex((function(n){return n[e.idKey]==t})),-1!=n&&this.$emit("input",this.value.splice(n,1)),this.$emit("remove",t)}}};e.default=f},5637:function(t,e,n){"use strict";n.r(e);var i=n("4269"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"5b75":function(t,e,n){"use strict";n("6a54");var i=n("3639").default,a=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("fd3c"),n("dc8a"),n("bf0f"),n("5c47"),n("5ef2"),n("aa9c"),n("dd2b");var r=a(n("9b1b")),u=i(n("d6c8")),l=i(n("5e6f")),o=a(n("e616")),s=a(n("73d1")),c=a(n("6d9a")),f={props:{customStyle:{type:[Object,String],default:function(){return{}}},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:function(){return{}},onLoad:function(){this.$uv.getRect=this.$uvGetRect},created:function(){this.$uv.getRect=this.$uvGetRect},computed:{$uv:function(){var t,e,n;return(0,r.default)((0,r.default)({},u),{},{test:l,route:o.default,debounce:s.default,throttle:c.default,unit:null===(t=uni)||void 0===t||null===(e=t.$uv)||void 0===e||null===(n=e.config)||void 0===n?void 0:n.unit})},bem:function(){return function(t,e,n){var i=this,a="uv-".concat(t,"--"),r={};return e&&e.map((function(t){r[a+i[t]]=!0})),n&&n.map((function(t){i[t]?r[a+t]=i[t]:delete r[a+t]})),Object.keys(r)}}},methods:{openPage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"url",e=this[t];e&&uni[this.linkType]({url:e})},$uvGetRect:function(t,e){var n=this;return new Promise((function(i){uni.createSelectorQuery().in(n)[e?"selectAll":"select"](t).boundingClientRect((function(t){e&&Array.isArray(t)&&t.length&&i(t),!e&&t&&i(t)})).exec()}))},getParentData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((function(e){t.parentData[e]=t.parent[e]}))},preventEvent:function(t){t&&"function"===typeof t.stopPropagation&&t.stopPropagation()},noop:function(t){this.preventEvent(t)}},onReachBottom:function(){uni.$emit("uvOnReachBottom")},beforeDestroy:function(){var t=this;if(this.parent&&l.array(this.parent.children)){var e=this.parent.children;e.map((function(n,i){n===t&&e.splice(i,1)}))}},unmounted:function(){var t=this;if(this.parent&&l.array(this.parent.children)){var e=this.parent.children;e.map((function(n,i){n===t&&e.splice(i,1)}))}}};e.default=f},"5e6b":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var a,r,u=i(n("9b1b")),l={props:(0,u.default)({value:{type:Array,default:function(){return[]}},idKey:{type:String,default:"id"},addTime:{type:Number,default:200},columnCount:{type:[Number,String],default:2},columnGap:{type:[Number,String],default:20},leftGap:{type:[Number,String],default:0},rightGap:{type:[Number,String],default:0},showScrollbar:{type:[Boolean],default:!1},columnWidth:{type:[Number,String],default:"auto"},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""}},null===(a=uni.$uv)||void 0===a||null===(r=a.props)||void 0===r?void 0:r.waterfall)};e.default=l},"6d9a":function(t,e,n){"use strict";var i;n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];n?i||(i=!0,"function"===typeof t&&t(),setTimeout((function(){i=!1}),e)):i||(i=!0,setTimeout((function(){i=!1,"function"===typeof t&&t()}),e))};e.default=a},"73d1":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=null;var a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(null!==i&&clearTimeout(i),n){var a=!i;i=setTimeout((function(){i=null}),e),a&&"function"===typeof t&&t()}else i=setTimeout((function(){"function"===typeof t&&t()}),e)};e.default=a},"76a6":function(t,e,n){"use strict";var i=n("d7e5"),a=n.n(i);a.a},c7e0:function(t,e,n){"use strict";n.r(e);var i=n("2b8a"),a=n("5637");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("76a6");var u=n("828b"),l=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"12eeb4b9",null,!1,i["a"],void 0);e["default"]=l.exports},d7e5:function(t,e,n){var i=n("2fd2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("dbe180a4",i,!0,{sourceMap:!1,shadowMode:!1})},e616:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("0506"),n("bf0f");var a=i(n("2634")),r=i(n("2fdc")),u=i(n("80b1")),l=i(n("efe5")),o=n("d6c8"),s=function(){function t(){(0,u.default)(this,t),this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}return(0,l.default)(t,[{key:"addRootPath",value:function(t){return"/"===t[0]?t:"/".concat(t)}},{key:"mixinParam",value:function(t,e){t=t&&this.addRootPath(t);var n="";return/.*\/.*\?.*=.*/.test(t)?(n=(0,o.queryParams)(e,!1),t+"&".concat(n)):(n=(0,o.queryParams)(e),t+n)}},{key:"route",value:function(){var t=(0,r.default)((0,a.default)().mark((function t(){var e,n,i,r,u=arguments;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=u.length>0&&void 0!==u[0]?u[0]:{},n=u.length>1&&void 0!==u[1]?u[1]:{},i={},"string"===typeof e?(i.url=this.mixinParam(e,n),i.type="navigateTo"):(i=(0,o.deepMerge)(this.config,e),i.url=this.mixinParam(e.url,e.params)),i.url!==(0,o.page)()){t.next=6;break}return t.abrupt("return");case 6:if(n.intercept&&(i.intercept=n.intercept),i.params=n,i=(0,o.deepMerge)(this.config,i),"function"!==typeof i.intercept){t.next=16;break}return t.next=12,new Promise((function(t,e){i.intercept(i,t)}));case 12:r=t.sent,r&&this.openPage(i),t.next=17;break;case 16:this.openPage(i);case 17:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}()},{key:"openPage",value:function(t){var e=t.url,n=(t.type,t.delta),i=t.animationType,a=t.animationDuration,r=t.events;"navigateTo"!=t.type&&"to"!=t.type||uni.navigateTo({url:e,animationType:i,animationDuration:a,events:r}),"redirectTo"!=t.type&&"redirect"!=t.type||uni.redirectTo({url:e}),"switchTab"!=t.type&&"tab"!=t.type||uni.switchTab({url:e}),"reLaunch"!=t.type&&"launch"!=t.type||uni.reLaunch({url:e}),"navigateBack"!=t.type&&"back"!=t.type||uni.navigateBack({delta:n})}}]),t}(),c=(new s).route;e.default=c}}]);