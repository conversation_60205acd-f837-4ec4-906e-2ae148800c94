(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-articlemessage-detail-detail"],{"0781":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-79d704f8]{width:100%;text-align:center}.article-detail[data-v-79d704f8]{background-color:#fff;box-sizing:border-box;padding:%?24?%}.article-detail .title[data-v-79d704f8]{font-size:%?36?%;color:#333;line-height:normal;font-weight:700}.article-detail .base-msg[data-v-79d704f8]{width:100%;display:flex;justify-content:space-between;align-items:center;margin:%?20?% %?0?%;font-size:%?26?%}.article-detail .base-msg uni-view[data-v-79d704f8]{color:#999;line-height:normal}.product-recommend[data-v-79d704f8]{margin-top:%?20?%;background-color:#fff;box-sizing:border-box;padding:%?20?% %?24?%}.product-recommend .product-title[data-v-79d704f8]{font-size:%?28?%;line-height:normal;margin-bottom:%?20?%;color:#333;font-weight:700}.product-recommend .product-list[data-v-79d704f8]{width:100%;height:%?160?%;display:flex;justify-content:space-between;margin-bottom:%?20?%;border-radius:%?10?%;box-sizing:border-box;padding:%?20?% %?30?% %?20?% %?20?%;background-color:#f5f5f5}.product-recommend .product-list .product-info[data-v-79d704f8]{display:flex}.product-recommend .product-list .product-info .img[data-v-79d704f8]{width:%?120?%;height:%?120?%;border-radius:%?4?%;margin-right:%?20?%}.product-recommend .product-list .product-info .product-info-item .name[data-v-79d704f8]{width:%?410?%;line-height:normal;margin-bottom:%?36?%}.product-recommend .product-list .product-info .product-info-item .price[data-v-79d704f8]{font-size:%?32?%;color:#f33}.product-recommend .product-list .butImg[data-v-79d704f8]{width:%?48?%;height:%?48?%;margin-top:%?36?%}.product-recommend .emptyImg[data-v-79d704f8]{width:100%}.product-recommend .emptyTips[data-v-79d704f8]{text-align:center}.article-but[data-v-79d704f8]{width:100%;background-color:#fff;border:%?1?% solid #eee;position:fixed;left:0;bottom:0;z-index:1;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.article-but .article-but-box[data-v-79d704f8]{width:100%;height:%?98?%;box-sizing:border-box;padding:%?0?% %?50?%;display:flex;justify-content:space-around;text-align:center}.article-but .article-but-box .but-item[data-v-79d704f8]{width:%?130?%;font-size:%?20?%;padding-top:%?10?%;position:relative}.article-but .article-but-box .but-item .shareBut[data-v-79d704f8]{width:%?130?%;height:%?85?%;line-height:%?42?%;margin:0;border:0;padding:0}.article-but .article-but-box .but-item .touchImg[data-v-79d704f8]{width:%?48?%;height:%?48?%}.article-but .article-but-box .but-item .anim[data-v-79d704f8]{-webkit-animation:movescale-data-v-79d704f8 1s normal;animation:movescale-data-v-79d704f8 1s normal}@-webkit-keyframes movescale-data-v-79d704f8{0%{-webkit-transform:scale(1.2);transform:scale(1.2)}50%{-webkit-transform:scale(1.5);transform:scale(1.5)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes movescale-data-v-79d704f8{0%{-webkit-transform:scale(1.2);transform:scale(1.2)}50%{-webkit-transform:scale(1.5);transform:scale(1.5)}100%{-webkit-transform:scale(1);transform:scale(1)}}.article-but .article-but-box .but-item .nums[data-v-79d704f8]{width:%?20?%;height:%?20?%;text-align:center;line-height:%?20?%;color:#fff;font-size:%?16?%;background-color:#f33;border-radius:50%;position:absolute;top:%?10?%;right:%?35?%}.article-but .article-but-box .but-item .txt[data-v-79d704f8]{font-size:%?20?%;line-height:0}.to-top[data-v-79d704f8]{width:%?144?%;height:%?152?%;position:fixed;right:0;bottom:%?85?%;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.mphtml[data-v-79d704f8] img{display:flex}',""]),t.exports=e},"0ab5":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3af2c4e0]{width:100%;text-align:center}.popup-dialog[data-v-3af2c4e0]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-3af2c4e0]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-3af2c4e0]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-3af2c4e0]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-3af2c4e0]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-3af2c4e0]{color:#f2270c;background:#fff;border:%?1?% solid #f2270c}.popup-dialog .popup-dialog-footer .button.red[data-v-3af2c4e0]{color:#fff;background:#f2270c}',""]),t.exports=e},1554:function(t,e,i){var a=i("0ab5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("a23ceca2",a,!0,{sourceMap:!1,shadowMode:!1})},"1f7d":function(t,e,i){"use strict";i.r(e);var a=i("21b5"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"21b5":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47"),i("2c10"),i("bf0f"),i("2797"),i("a1c1"),i("5ef2"),i("4626"),i("f7a5");var o=a(i("fcf3")),n=a(i("2634")),s=a(i("2fdc")),r=a(i("3b27")),c=a(i("85bf")),d=a(i("5283")),u=a(i("7c8d")),l=(a(i("57bc")),a(i("172f"))),f={mixins:[r.default,d.default],components:{mphtml:l.default},data:function(){return{list:[],isShopOwner:0,token:"",shop_id:null,id:"",butList:[{img:this.$util.img("public/static/youpin/share.png"),name:"分享好友",type:"share"},{img:this.$util.img("public/static/youpin/noLike.png"),name:"点赞",type:"like"},{img:this.$util.img("public/static/youpin/cart.png"),name:"达人推荐",type:"recommend"}],productList:[],goodsSkuDetail:{},animShow:!1,productShowStatus:!0,info:{},content:""}},onLoad:function(t){this.id=t.id},onPullDownRefresh:function(){this.getArticleContent()},onShow:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,c.default.wait_staticLogin_success();case 3:t.shop_id=uni.getStorageSync("shop_id"),t.token=uni.getStorageSync("token"),t.getArticleContent(),uni.getStorageSync("is_register")&&(t.$util.toShowCouponPopup(t),uni.removeStorageSync("is_register"));case 7:case"end":return e.stop()}}),e)})))()},methods:{getArticleContent:function(){var t=this;this.$api.sendRequest({url:u.default.usershareexperienceContent,data:{id:this.id},success:function(e){if(0==e.code)if(1==e.data.status){t.isShopOwner=e.data.is_shop_owner,t.info=e.data;var i=e.data.content.match(/<img.*?(?:>|\/>)/gi);i&&i.length>0&&i.forEach((function(t){var i=t.match(/src=[\'\"]?([^\'\"]*)[\'\"]?/i)[1];e.data.content=e.data.content.replace(i,i+"?image_process=resize,s_600")})),e.data.content.indexOf("text-indent: 28px;")>0&&(e.data.content=e.data.content.replace(/text-indent: 28px;/gi,"")),t.content=e.data.content,1==e.data.is_like&&(t.animShow=!0,t.butList[1].img=t.$util.img("public/static/youpin/redLike.png"),t.butList[1].name="已点赞"),e.data.goods&&e.data.goods.length>0&&e.data.goods.forEach((function(t){t.goods_detail.is_shop_owner=t.is_shop_owner,t.goods_detail.goods_spec_format&&(t.goods_detail.goods_spec_format=JSON.parse(t.goods_detail.goods_spec_format)),t.tags&&t.tags.length>0?t.showType=t.tags[0].key:t.showType="normal"})),t.productList=e.data.goods,e.data.goods&&e.data.goods.length<=0&&(t.productShowStatus=!1),t.$api.sendRequest({url:u.default.usershareexperienceAddView,data:{id:t.id}}),t.setWechatShare()}else uni.showToast({title:"该分享体验已过期，请查看其它分享",duration:2e3,icon:"none"}),setTimeout((function(){t.$util.redirectTo("/promotionpages/articlemessage/list/list",{},"redirectTo")}),1500)}})},refreshGoodsSkuDetail:function(t){Object.assign(this.goodsSkuDetail,t)},changeSkuShow:function(t,e){if(this.token)if(0!=uni.getStorageSync("is_shopping_status")){var i=this.productList[t];i.goods_detail.show_price=1==this.isShopOwner?i.discount_price:i.retail_price,i.goods_detail.numControl=!0,this.goodsSkuDetail=i.goods_detail,["newhand","seckill","pintuan","maidou"].includes(i.showType)?this.toProductDetail(i):i.goods_detail.goods_spec_format&&"object"==(0,o.default)(i.goods_detail.goods_spec_format)&&i.goods_detail.goods_spec_format.length>0?this.$refs.goodsSku.show("choose_spec",(function(){})):this.joinCart(i)}else this.$refs.diywrangDialog.open();else this.$util.toShowLoginPopup(this,null,"/promotionpages/articlemessage/detail/detail?id=".concat(this.id))},joinCart:function(t){var e=this;this.$refs.goodsSku.type="join_cart",this.$refs.goodsSku.confirm(null,!1),this.$refs.goodsSku.callback=function(i){i&&(e.$buriedPoint.purchaseGoods({id:t.sku_id,action_type:0,action_num:[1],is_goods_page:1}),uni.showModal({content:"该商品已加入购物车",cancelText:"再看看",cancelColor:"#999",confirmText:"去结算",confirmColor:"#000",success:function(t){t.confirm&&e.$util.redirectTo("/pages/goods/cart/cart",{},"reLaunch")}}))}},toProductDetail:function(t){var e=t.applet_url;e&&this.$util.redirectTo(e)},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},getSharePageParams:function(){var t=this.$util.unifySharePageParams("/promotionpages/articlemessage/detail/detail","先迈商城",this.info.title,{id:this.id},this.$util.img("public/static/youpin/articlemessage.jpg"));return t},touchButton:function(t,e){var i=this;if("share"==t)this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(this.getSharePageParams()),this.getTransmit();else if("like"==t){if(!this.token)return void this.$util.toShowLoginPopup(this,null,"/promotionpages/articlemessage/detail/detail?id=".concat(this.id));this.$api.sendRequest({url:u.default.usershareexperienceLike,data:{id:this.id},success:function(t){0==t.code&&(i.animShow=!i.animShow,i.butList[1].name=i.animShow?"已点赞":"点赞",i.butList[1].img=i.animShow?i.$util.img("public/static/youpin/redLike.png"):i.$util.img("public/static/youpin/noLike.png"))}})}else if("recommend"==t){uni.createSelectorQuery().select("#product-recommend").boundingClientRect((function(t){uni.createSelectorQuery().select(".article-detail").boundingClientRect((function(e){uni.pageScrollTo({duration:500,scrollTop:Math.abs(t.top-e.top)})})).exec()})).exec()}},getTransmit:function(){this.$api.sendRequest({url:u.default.usershareexperienceTransmit,data:{id:this.id}})},imageError:function(t){this.productList[t].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}},onShareAppMessage:function(){this.getTransmit();var t=this.getSharePageParams();return this.$buriedPoint.pageShare(t.link,t.imageUrl,t.desc)},onShareTimeline:function(t){this.getTransmit();var e=this.getSharePageParams();return{title:e.desc,imageUrl:e.imageUrl,query:e.query,success:function(t){},fail:function(t){}}}};e.default=f},2328:function(t,e,i){"use strict";i.r(e);var a=i("2650"),o=i("83b7");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("35f0");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"3af2c4e0",null,!1,a["a"],void 0);e["default"]=r.exports},2650:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("5e99").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("uni-popup",{ref:"popupBan",attrs:{maskClick:!1}},[i("v-uni-view",{staticClass:"popup-dialog"},[i("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),i("v-uni-view",{staticClass:"popup-dialog-body"},[t._v("哎哟~系统好像出了点问题，暂时不能支付，请联系客服。")]),i("v-uni-view",{staticClass:"popup-dialog-footer"},[i("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.popupBan.close()}}},[t._v("知道了")])],1)],1)],1)],1)},n=[]},"327f":function(t,e,i){"use strict";var a=i("a971"),o=i.n(a);o.a},"35f0":function(t,e,i){"use strict";var a=i("1554"),o=i.n(a);o.a},"37ff":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},o=[]},"3ea9":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{}},methods:{open:function(){this.$refs.popupBan.open()}}}},5283:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("5c47"),i("5ef2"),i("aa9c"),i("c223");var o=a(i("2634")),n=a(i("2fdc")),s={data:function(){return{systemInfo:null,scrollPosition:0,isEmit:!0,postendSkuId:[],tmpSkuId:[]}},onLoad:function(){this.systemInfo=uni.getSystemInfoSync()},onReady:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.againDealWith();case 2:case"end":return e.stop()}}),e)})))()},onPageScroll:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function i(){return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.scrollTouch(t);case 2:case"end":return i.stop()}}),i)})))()},methods:{againDealWith:function(){var t=arguments,e=this;return(0,n.default)((0,o.default)().mark((function i(){var a;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:a=t.length>0&&void 0!==t[0]&&t[0],a&&(e.postendSkuId=[]),setTimeout((0,n.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getExposeGoods();case 2:case"end":return t.stop()}}),t)}))),2e3);case 3:case"end":return i.stop()}}),i)})))()},scrollTouch:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function i(){var a;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=t.scrollTop,!(Math.abs(a-e.scrollPosition)>50&&e.isEmit)){i.next=7;break}return e.isEmit=!1,e.scrollPosition=a,i.next=6,e.getExposeGoods();case 6:e.isEmit=!0;case 7:case"end":return i.stop()}}),i)})))()},checkNoneInScreen:function(t){return t.top>-1&&t.top<=this.systemInfo.screenHeight&&t.left>-1&&t.left<=this.systemInfo.screenWidth},getExposeGoods:function(){var t=this,e=uni.createSelectorQuery();return new Promise((function(i,a){e.selectAll(".expose_goods_index").boundingClientRect((function(e){for(var a=0;a<e.length;a++)if(t.checkNoneInScreen(e[a])){var o=e[a].dataset["expose_goods_sku"];-1==t.postendSkuId.indexOf(o)&&-1==t.tmpSkuId.indexOf(o)&&t.tmpSkuId.push(o)}t.toExposes(),i()})).exec()}))},toExposes:function(){for(var t=[],e=0;e<this.tmpSkuId.length;e++)t.push({sku_id:this.tmpSkuId[e]});this.postendSkuId=this.postendSkuId.concat(this.tmpSkuId),this.$buriedPoint.exposeGoods(t,"sku_id"),this.tmpSkuId=[]}}};e.default=s},"6ae3":function(t,e,i){"use strict";var a=i("b056"),o=i.n(a);o.a},7089:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={nsGoodsSku:i("4d57").default,diyShareNavigateH5:i("2f73").default,diyWrongDialog:i("2328").default,ydAuthPopup:i("161f").default,nsLogin:i("4f5a").default,uniCouponPop:i("8765").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"article-detail"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(t.info.title))]),i("v-uni-view",{staticClass:"base-msg"},[t.info.show_view_num?i("v-uni-view",[t._v("浏览："+t._s(t.info.show_view_num))]):t._e(),i("v-uni-view",[t._v(t._s(t.info.create_time))])],1),t.content?i("mphtml",{staticClass:"mphtml",attrs:{content:t.content}}):t._e()],1),i("v-uni-view",{staticClass:"product-recommend"},[i("v-uni-view",{staticClass:"product-title",attrs:{id:"product-recommend"}},[t._v("达人推荐")]),t._l(t.productList,(function(e,a){return t.productShowStatus?i("v-uni-view",{key:a,staticClass:"product-list",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"product-info"},[i("v-uni-image",{staticClass:"img expose_goods_index",attrs:{"data-expose_goods_sku":e.sku_id,src:e.goods_image},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}}),i("v-uni-view",{staticClass:"product-info-item"},[i("v-uni-view",{staticClass:"name overtext-hidden-one"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticStyle:{"font-size":"24rpx"}},[t._v("￥")]),t._v(t._s(e.retail_price))],1)],1)],1),"seckill"!==e.showType&&"pintuan"!==e.showType?i("v-uni-image",{staticClass:"butImg",attrs:{src:t.$util.img("public/static/youpin/redCart.png")},on:{click:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.changeSkuShow(a,e)}}}):t._e(),"seckill"==e.showType?i("v-uni-image",{staticClass:"butImg",attrs:{src:t.$util.img("public/static/youpin/seckill.png")},on:{click:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.changeSkuShow(a,e)}}}):t._e(),"pintuan"==e.showType?i("v-uni-image",{staticClass:"butImg",attrs:{src:t.$util.img("public/static/youpin/pintuan.png")},on:{click:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.changeSkuShow(a,e)}}}):t._e()],1):t._e()})),t.productShowStatus?t._e():i("v-uni-view",[i("v-uni-image",{staticClass:"emptyImg",attrs:{src:t.$util.img("public/static/youpin/emptyCart.jpg"),mode:""}}),i("v-uni-view",{staticClass:"emptyTips"},[t._v("推荐商品已下架")])],1)],2),i("v-uni-view",{staticStyle:{width:"100%",height:"200rpx"}}),i("v-uni-view",{staticClass:"article-but"},[i("v-uni-view",{staticClass:"article-but-box"},t._l(t.butList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"but-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.touchButton(e.type,a)}}},["share"==e.type?[i("v-uni-image",{staticClass:"touchImg",attrs:{src:e.img,mode:"widthFix"}}),"recommend"==e.type?i("v-uni-view",{staticClass:"nums"},[t._v(t._s(t.productList.length))]):t._e(),i("v-uni-view",{staticClass:"txt"},[t._v(t._s(e.name))])]:t._e(),"share"!==e.type?[i("v-uni-image",{staticClass:"touchImg",class:{anim:"like"==e.type&&t.animShow},attrs:{src:e.img,mode:"widthFix"}}),"recommend"==e.type?i("v-uni-view",{staticClass:"nums"},[t._v(t._s(t.productList.length))]):t._e(),i("v-uni-view",{staticClass:"txt"},[t._v(t._s(e.name))])]:t._e()],2)})),1)],1),i("ns-goods-sku",{ref:"goodsSku",attrs:{"goods-detail":t.goodsSkuDetail,entrance:"choose_spec"},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshGoodsSkuDetail.apply(void 0,arguments)}}}),i("diy-share-navigate-h5",{ref:"shareNavigateH5"}),i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:t.showTop,expression:"showTop"}],staticClass:"to-top",attrs:{src:t.$util.img("public/static/youpin/to-top.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToTopNative.apply(void 0,arguments)}}}),i("diy-wrong-dialog",{ref:"diywrangDialog"}),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"}),i("uni-coupon-pop",{ref:"couponPop"})],1)},n=[]},"83b7":function(t,e,i){"use strict";i.r(e);var a=i("3ea9"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"8f68":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},9127:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},a971:function(t,e,i){var a=i("8f68");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("2d38abe0",a,!0,{sourceMap:!1,shadowMode:!1})},b056:function(t,e,i){var a=i("0781");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("137fc828",a,!0,{sourceMap:!1,shadowMode:!1})},b8ea:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var o=a(i("9127")),n={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:o.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=n},ba50:function(t,e,i){"use strict";i.r(e);var a=i("7089"),o=i("1f7d");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("6ae3");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"79d704f8",null,!1,a["a"],void 0);e["default"]=r.exports},de74:function(t,e,i){"use strict";i.r(e);var a=i("37ff"),o=i("fefc");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("327f");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"65e5b6d2",null,!1,a["a"],void 0);e["default"]=r.exports},fefc:function(t,e,i){"use strict";i.r(e);var a=i("b8ea"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a}}]);