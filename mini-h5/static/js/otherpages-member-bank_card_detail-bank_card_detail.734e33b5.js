(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-bank_card_detail-bank_card_detail"],{"16f6":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("5de6")),i=a(r("2634")),s=a(r("2fdc"));r("fd3c"),r("bf0f"),r("2797"),r("aa9c");var o={props:{defaultRegions:{type:Array}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];3===e.length&&(e.join(""),t.join(""))},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var e=this.pickedArr.map((function(e){return e.map((function(e){return e.label}))}));return e}},pickedArr:function(){return this.isInitMultiArray?[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{handleColumnChange:function(e){var t=this;return(0,s.default)((0,i.default)().mark((function r(){var a,n;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.isInitMultiArray=!1,a=e.detail.column,n=e.detail.value,t.multiIndex[a]=n,r.t0=a,r.next=0===r.t0?7:1===r.t0?14:2===r.t0?18:19;break;case 7:return r.next=9,t.getAreasAsync(t.pickerValueArray[0][t.multiIndex[a]].value);case 9:return t.cityArr=r.sent,r.next=12,t.getAreasAsync(t.cityArr[0].value);case 12:return t.districtArr=r.sent,r.abrupt("break",19);case 14:return r.next=16,t.getAreasAsync(t.cityArr[t.multiIndex[a]].value);case 16:return t.districtArr=r.sent,r.abrupt("break",19);case 18:return r.abrupt("break",19);case 19:case"end":return r.stop()}}),r)})))()},handleValueChange:function(e){var t=(0,n.default)(e.detail.value,3),r=t[0],a=t[1],i=t[2],s=(0,n.default)(this.pickedArr,3),o=s[0],c=s[1],u=s[2],l=[o[r],c[a],u[i]];this.$emit("getRegions",l)},handleDefaultRegions:function(){var e=this;return(0,s.default)((0,i.default)().mark((function t(){var r,a,n,s;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isLoadDefaultAreas){t.next=2;break}return t.abrupt("return");case 2:e.isInitMultiArray=!1,r=0;case 4:if(!(r<e.defaultRegions.length)){t.next=37;break}a=0;case 6:if(!(a<e.pickerValueArray[r].length)){t.next=34;break}if(e.defaultRegions[r]!=e.pickerValueArray[r][a].value){t.next=31;break}return e.$set(e.multiIndex,0,a),t.next=11,e.getAreasAsync(e.pickerValueArray[r][a].value);case 11:e.cityArr=t.sent,n=0;case 13:if(!(n<e.cityArr.length)){t.next=31;break}if(e.defaultRegions[1]!=e.cityArr[n].value){t.next=28;break}return e.$set(e.multiIndex,1,n),t.next=18,e.getAreasAsync(e.cityArr[n].value);case 18:e.districtArr=t.sent,s=0;case 20:if(!(s<e.districtArr.length)){t.next=28;break}if(e.defaultRegions[2]!=e.districtArr[s].value){t.next=25;break}return e.$set(e.multiIndex,2,s),e.handleValueChange({detail:{value:[a,n,s]}}),t.abrupt("return");case 25:s++,t.next=20;break;case 28:n++,t.next=13;break;case 31:a++,t.next=6;break;case 34:r++,t.next=4;break;case 37:case"end":return t.stop()}}),t)})))()},getDefaultAreas:function(e,t){var r=this;this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(){var e=(0,s.default)((0,i.default)().mark((function e(a){var n,s;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=a.code){e.next=16;break}if(n=[],s=void 0,a.data.forEach((function(e,r){void 0!=t&&(0==t.level&&void 0!=t.province_id?s=t.province_id:1==t.level&&void 0!=t.city_id?s=t.city_id:2==t.level&&void 0!=t.district_id&&(s=t.district_id)),void 0==s&&0==r&&(s=e.id),n.push({value:e.id,label:e.name})})),r.pickerValueArray[t.level]=n,!(t.level+1<3)){e.next=10;break}t.level++,r.getDefaultAreas(s,t),e.next=16;break;case 10:return r.cityArr=r.pickerValueArray[1],r.districtArr=r.pickerValueArray[2],r.isInitMultiArray=!0,r.isLoadDefaultAreas=!0,e.next=16,r.handleDefaultRegions();case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},getAreasAsync:function(e){var t=this;return(0,s.default)((0,i.default)().mark((function r(){var a,n;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$api.sendRequest({url:"/api/address/lists",data:{pid:e},async:!1});case 2:if(a=r.sent,0!=a.code){r.next=7;break}return n=[],a.data.forEach((function(e,t){n.push({value:e.id,label:e.name})})),r.abrupt("return",n);case 7:case"end":return r.stop()}}),r)})))()},getAreas:function(e,t){this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var r=[];e.data.forEach((function(e,t){r.push({value:e.id,label:e.name})})),t&&t(r)}}})}}};t.default=o},"2d01":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"55fb":function(e,t,r){"use strict";r.r(t);var a=r("16f6"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},"5c85":function(e,t,r){"use strict";var a=r("d887"),n=r.n(a);n.a},6436:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return a}));var a={pickRegions:r("d89a").default},n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{class:e.themeStyle,style:[e.themeColorVar]},[r("v-uni-view",{staticClass:"top-tips"},[e._v("注意：请绑定本人的储蓄卡，不支持绑定信用卡")]),r("v-uni-view",{staticClass:"form"},[r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("持卡人")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",disabled:!0,placeholder:"请填写持卡人","placeholder-class":"placeholder"},model:{value:e.formObj.realname,callback:function(t){e.$set(e.formObj,"realname",t)},expression:"formObj.realname"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("证件号码")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",disabled:!0,placeholder:"请填写证件号码","placeholder-class":"placeholder"},model:{value:e.formObj.IdCard,callback:function(t){e.$set(e.formObj,"IdCard",t)},expression:"formObj.IdCard"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("银行名称")]),r("v-uni-view",{staticClass:"item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectBank.apply(void 0,arguments)}}},[e.formObj.branch_bank_name?r("v-uni-view",{staticClass:"bank-name"},[e._v(e._s(e.formObj.branch_bank_name)),r("v-uni-text",{staticClass:"iconfont iconright placeholder"})],1):r("v-uni-view",{staticClass:"picker"},[e._v("请选择"),r("v-uni-text",{staticClass:"iconfont iconright placeholder"})],1)],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("银行卡号")]),r("v-uni-view",{staticClass:"item"},[e.formObj.cardNoFormat&&!e.isFocus?r("v-uni-view",{staticClass:"input",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isFocus=!0}}},[e._v(e._s(e.formObj.cardNoFormat))]):r("v-uni-input",{attrs:{type:"number",focus:e.isFocus,placeholder:"请填写银行卡号","placeholder-class":"placeholder"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.isFocus=!0},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.cardBlur.apply(void 0,arguments)}},model:{value:e.formObj.bank_account,callback:function(t){e.$set(e.formObj,"bank_account",t)},expression:"formObj.bank_account"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("开户省市")]),r("pick-regions",{staticClass:"picker",attrs:{"default-regions":e.defaultRegions},on:{getRegions:function(t){arguments[0]=t=e.$handleEvent(t),e.handleGetRegions.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"item"},[e.formObj.full_address?r("v-uni-view",{staticClass:"bank-name"},[e._v(e._s(e.formObj.full_address)),r("v-uni-text",{staticClass:"iconfont iconright placeholder"})],1):r("v-uni-view",{staticClass:"picker"},[e._v("请选择"),r("v-uni-text",{staticClass:"iconfont iconright placeholder"})],1)],1)],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("开户行支行")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",placeholder:"请填写开户行支行名称","placeholder-class":"placeholder"},model:{value:e.formObj.bankname,callback:function(t){e.$set(e.formObj,"bankname",t)},expression:"formObj.bankname"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("手机号")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"number",placeholder:"请填写银行预留手机号","placeholder-class":"placeholder"},model:{value:e.formObj.mobile,callback:function(t){e.$set(e.formObj,"mobile",t)},expression:"formObj.mobile"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("验证码")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",placeholder:"请输入验证码","placeholder-class":"placeholder",maxlength:"4"},model:{value:e.formObj.code,callback:function(t){e.$set(e.formObj,"code",t)},expression:"formObj.code"}}),r("v-uni-view",{staticClass:"verification-code",class:{disabled:e.countDownNum>0},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),!(e.countDownNum>0)&&e.getCode()}}},[e._v(e._s(e.countDownNum>0?e.countDownNum+"后重发":"获取验证码"))])],1)],1)],1),r("v-uni-button",{staticClass:"submit",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submit.apply(void 0,arguments)}}},[e._v("立即绑定")])],1)},i=[]},"7e79":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("dc8a"),r("5c47"),r("a1c1"),r("0c26"),r("bf0f"),r("2797"),r("c223"),r("8f71");var n=a(r("2634")),i=a(r("2fdc")),s=a(r("d89a")),o=a(r("8469")),c=a(r("7c8d")),u=a(r("2d01")),l={components:{pickRegions:s.default},mixins:[u.default],data:function(){return{countDownNum:0,bank_account_id:0,formObj:{realname:"",mobile:"",bank_account:"",cardNoFormat:"",branch_bank_name:"",code:"",full_address:"",bankname:"",IdCard:""},isFocus:!1,addressValue:"",defaultRegions:[],bank_card_list_code:{}}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},onLoad:function(e){var t=this;return(0,i.default)((0,n.default)().mark((function r(){var a,i,s;return(0,n.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.id){r.next=4;break}return t.bank_account_id=e.id,r.next=4,t.getBankCard();case 4:return a=uni.getStorageSync("bank_card_list_code"),a&&Object.keys(a).length>0&&a.countDownNum>0&&(t.bank_card_list_code=a,uni.removeStorageSync("bank_card_list_code"),t.formObj.mobile==a.mobile&&(t.formObj.code=a.code,t.countDownNum=t.bank_card_list_code.countDownNum,i=setInterval((function(){t.countDownNum-=1,t.countDownNum||clearInterval(i)}),1e3))),uni.removeStorageSync("bank"),s=uni.getStorageSync("memberAuth"),s&&(t.formObj.realname=s.auth_card_name,t.formObj.IdCard=t.$util.addStar(s.auth_card_no,4,4)),r.next=11,t.getMemberAuthenticationInfo();case 11:case"end":return r.stop()}}),r)})))()},onShow:function(){this.$langConfig.refresh(),uni.getStorageSync("bank")&&(this.formObj.branch_bank_name=uni.getStorageSync("bank").bank_name),this.bank_account_id&&uni.setNavigationBarTitle({title:"编辑银行卡"})},methods:{getBankCard:function(){var e=this;return(0,i.default)((0,n.default)().mark((function t(){var r;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:c.default.memberBankUrl,async:!1});case 2:r=t.sent,r.data&&(e.formObj=Object.assign(e.formObj,r.data),e.formObj.bankname=r.data.branch_branch_bank_name,e.formObj.full_address=r.data.province+" "+r.data.city,e.inputChange());case 4:case"end":return t.stop()}}),t)})))()},getMemberAuthenticationInfo:function(){var e=this;return(0,i.default)((0,n.default)().mark((function t(){var r;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:c.default.memberAuthenticationInfo,async:!1});case 2:r=t.sent,r.data&&(e.formObj.realname=r.data.auth_card_name,e.formObj.IdCard=e.$util.addStar(r.data.auth_card_no,4,4));case 4:case"end":return t.stop()}}),t)})))()},inputChange:function(e){this.formObj.bank_account=e?e.detail.value:this.formObj.bank_account,this.formObj.cardNoFormat=String(e?e.detail.value:this.formObj.bank_account).replace(/\D/g,"").replace(/....(?!$)/g,"$& ")},cardBlur:function(){var e=this;return(0,i.default)((0,n.default)().mark((function t(){var r;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isFocus=!1,!e.formObj.bank_account){t.next=11;break}return t.prev=2,t.next=5,e.$api.sendRequest({url:c.default.bankCardInfoUrl,data:{card_no:e.formObj.bank_account},async:!1});case 5:r=t.sent,0==r.code&&r.data&&Object.keys(r.data).length>0&&(uni.setStorageSync("bank",r.data),e.formObj.branch_bank_name=r.data.bank_name),t.next=11;break;case 9:t.prev=9,t.t0=t["catch"](2);case 11:case"end":return t.stop()}}),t,null,[[2,9]])})))()},selectBank:function(){this.$util.redirectTo("/otherpages/member/bank_list/bank_list",{back:"/otherpages/member/bank_card_detail/bank_card_detail"})},handleGetRegions:function(e){this.formObj.full_address="",this.formObj.full_address+=void 0!=e[0]?e[0].label:"",this.formObj.full_address+=void 0!=e[1]?" "+e[1].label:"",this.formObj.full_address+=void 0!=e[2]?" "+e[2].label:"",this.addressValue="",this.addressValue+=void 0!=e[0]?e[0].value:"",this.addressValue+=void 0!=e[1]?" "+e[1].value:"",this.addressValue+=void 0!=e[2]?" "+e[2].value:""},vertify:function(e){this.formObj.branch_bank_name=this.formObj.branch_bank_name.trim(),this.formObj.bank_account=this.formObj.bank_account.trim(),this.formObj.mobile=this.formObj.mobile.trim();var t=[],r=[{name:"branch_bank_name",checkType:"required",errorMsg:"请选择银行名称"},{name:"bank_account",checkType:"required",errorMsg:"请输入提现账号"},{name:"bank_account",checkType:"bank_account",errorMsg:"请输入正确的提现账号"},{name:"full_address",checkType:"required",errorMsg:"请选择开户省市"},{name:"bankname",checkType:"required",errorMsg:"请输入开户支行名称"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"code",checkType:"required",errorMsg:"请输入验证码"}];e?e.forEach((function(e){t=t.concat(r.filter((function(t){return t.name==e})))})):t=r;var a=o.default.check(this.formObj,t);return!!a||(this.$util.showToast({title:o.default.error}),this.flag=!1,!1)},getCode:function(){var e=this,t=this.vertify(["mobile"]);t&&this.$api.sendRequest({url:c.default.sendMobileCodeUrl,data:{mobile:this.formObj.mobile},success:function(t){if(0==t.code){e.countDownNum=120;var r=setInterval((function(){e.countDownNum-=1,e.countDownNum||clearInterval(r)}),1e3)}else e.$util.showToast({title:t.message})}})},submit:function(){var e=this,t=this.vertify();if(t){var r=this.bank_account_id?c.default.editBankUrl:c.default.addBankUrl,a=this.addressValue.split(" "),n={};n={realname:this.formObj.realname,mobile:this.formObj.mobile,branch_bank_name:this.formObj.branch_bank_name,bank_account:this.formObj.bank_account,code:this.formObj.code,province_id:a[0]?a[0]:this.formObj.province_id,city_id:a[1]?a[1]:this.formObj.city_id,branch_branch_bank_name:this.formObj.bankname},this.bank_account_id&&(n.bank_account_id=this.bank_account_id),this.$api.sendRequest({url:r,data:n,success:function(t){0==t.code?(e.$util.showToast({title:"绑定成功",icon:"success"}),setTimeout((function(){e.$util.redirectTo("/otherpages/member/bank_card_list/bank_card_list",{},"redirectTo")}),1e3)):e.$util.showToast({title:t.message})}})}},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),r=t.title,a=t.link,n=t.imageUrl;t.query;return this.$buriedPoint.pageShare(a,n,r)}};t.default=l},8469:function(e,t,r){r("23f4"),r("7d2f"),r("5c47"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){for(var r=0;r<t.length;r++){if(!t[r].checkType)return!0;if(!t[r].name)return!0;if(!t[r].errorMsg)return!0;if(!e[t[r].name])return this.error=t[r].errorMsg,!1;switch(t[r].checkType){case"custom":if("function"==typeof t[r].validate&&!t[r].validate(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"string":a=new RegExp("^.{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[r].name]))return this.error=t[r].errorMsg,!1;var n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"same":if(e[t[r].name]!=t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"notsame":if(e[t[r].name]==t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"phoneno":a=/^[1](([3][0-9])|([4][1,4-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"reg":a=new RegExp(t[r].checkRule);if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"in":if(-1==t[r].checkRule.indexOf(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"notnull":if(0==e[t[r].name]||void 0==e[t[r].name]||null==e[t[r].name]||e[t[r].name].length<1)return this.error=t[r].errorMsg,!1;break;case"lengthMin":if(e[t[r].name].length<t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"lengthMax":if(e[t[r].name].length>t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"bank_account":a=/^([1-9]{1})(\d{15}|\d{18})$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"idCard":a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"92e0":function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-64b57231]{width:100%;text-align:center}.top-tips[data-v-64b57231]{background:var(--custom-brand-color-10);padding:0 %?25?%;line-height:%?64?%;font-size:%?24?%;color:var(--custom-brand-color)}.form[data-v-64b57231]{background:#fff;width:%?710?%;margin:0 auto;margin-top:%?20?%;border-radius:%?20?%;padding:0 %?30?%;box-sizing:border-box}.form .form-item[data-v-64b57231]{display:flex;align-items:center;justify-content:space-between;height:%?116?%;position:relative;box-sizing:border-box}.form .form-item[data-v-64b57231]:not(:first-child){border-top:%?2?% solid #f5f5f5}.form .form-item .label[data-v-64b57231]{width:%?190?%;font-size:%?30?%;font-weight:400;line-height:%?52?%;color:#383838}.form .form-item .item[data-v-64b57231]{width:calc(100vw - %?190?% - %?100?%);position:absolute;line-height:%?116?%;top:0;right:%?26?%;font-size:%?30?%}.form .form-item .item uni-input[data-v-64b57231]{line-height:%?116?%;height:%?116?%;position:absolute;width:100%;top:0;left:0;z-index:2;font-size:%?30?%}.form .form-item .item .cardNo[data-v-64b57231]{opacity:0}.form .form-item .item .input[data-v-64b57231]{width:100%;line-height:%?116?%;height:%?116?%}.form .form-item .item .picker[data-v-64b57231]{color:#9a9a9a;text-align:right;line-height:%?100?%}.form .form-item .item .bank-name[data-v-64b57231]{display:flex;justify-content:space-between;height:%?100?%;line-height:%?100?%;font-size:%?30?%}.form .form-item .item .verification-code[data-v-64b57231]{position:absolute;right:0;top:%?26?%;z-index:999;width:%?148?%;height:%?48?%;border:1px solid var(--custom-brand-color);border-radius:24px;line-height:%?48?%;text-align:center;color:var(--custom-brand-color);font-size:%?24?%}.form .form-item .item .verification-code.disabled[data-v-64b57231]{border:1px solid #eee;color:#bcbcbc}.form .placeholder[data-v-64b57231]{font-size:%?30?%;color:#ccc}.submit[data-v-64b57231]{margin-top:%?80?%;line-height:%?80?%}[data-v-64b57231] .pick-regions div:last-child{height:%?100?%}[data-v-64b57231] .pick-regions div:last-child .bank-name{margin-left:%?24?%}',""]),e.exports=t},"95b4":function(e,t,r){"use strict";r.r(t);var a=r("6436"),n=r("c2a2");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("5c85");var s=r("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"64b57231",null,!1,a["a"],void 0);t["default"]=o.exports},c2a2:function(e,t,r){"use strict";r.r(t);var a=r("7e79"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},d2ca:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"pick-regions"},[r("v-uni-picker",{attrs:{mode:"multiSelector",value:e.multiIndex,range:e.multiArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleValueChange.apply(void 0,arguments)},columnchange:function(t){arguments[0]=t=e.$handleEvent(t),e.handleColumnChange.apply(void 0,arguments)}}},[e._t("default")],2)],1)},n=[]},d887:function(e,t,r){var a=r("92e0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=r("967d").default;n("1e9b03d5",a,!0,{sourceMap:!1,shadowMode:!1})},d89a:function(e,t,r){"use strict";r.r(t);var a=r("d2ca"),n=r("55fb");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);var s=r("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=o.exports}}]);