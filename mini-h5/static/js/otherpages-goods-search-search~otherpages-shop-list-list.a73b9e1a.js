(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-goods-search-search~otherpages-shop-list-list"],{"1c72":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("2634")),r=i(a("2fdc"));a("64aa"),a("fd3c"),a("5c47"),a("dfcf"),a("c223"),a("4626"),a("5ac7"),a("aa9c");var o=i(a("de74")),s=i(a("7c8d")),c={name:"diy-associate-search",components:{UniIcons:o.default},props:{value:{type:String,default:""},placeholder:{type:String,default:"搜索商品"},focus:{type:Boolean,default:!1},confirmType:{type:String,default:"search"},associateTop:{type:[Number,String],default:0}},data:function(){return{width:"440rpx",height:"64rpx",input_width:"330rpx",input_value:"",associate_history:{},to_input_value:null,search_report_list:[]}},watch:{value:{handler:function(e){this.input_value=e,this.focus=!e}},immediate:!0},created:function(){this.input_value=this.value,this.to_input_value=this.debounce(this.toInput,300)},methods:{debounce:function(e,t){var a;return function(){var i=this,n=arguments;clearTimeout(a),a=setTimeout((function(){e.apply(i,n)}),t)}},toInput:function(e){var t=e.detail.value;t&&(this.associate_history.hasOwnProperty(t)||this.toAssociate())},toAssociate:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var a;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$api.sendRequest({url:s.default.searchGuessUrl,data:{search:e.input_value},async:!1});case 3:a=t.sent,0==a.code&&e.$set(e.associate_history,e.input_value,a.data.map((function(t){return t.keyword_list=t.keyword_text.split(e.input_value),t.is_click=!1,t}))),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.log(t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},toClear:function(){this.input_value="",this.$emit("clear")},toConfirm:function(){this.focus=!1,this.input_value&&(this.$emit("confirm",{type:"user_input",value:this.input_value}),this.searchReport("search",this.input_value,this.input_value))},clickAssociate:function(e){var t=this;return(0,r.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.focus=!1,t.$emit("confirm",{type:"associate",value:t.associate_history[t.input_value][e]}),a.next=4,t.hitReport(t.input_value,e);case 4:return a.next=6,t.searchReport("guess",t.associate_history[t.input_value][e].keyword_text,t.input_value);case 6:t.input_value=t.associate_history[t.input_value][e].keyword_text;case 7:case"end":return a.stop()}}),a)})))()},toFocus:function(e){this.focus=!0,this.input_value&&(this.associate_history.hasOwnProperty(this.input_value)||this.toAssociate())},hitReport:function(e,t){var a=this;return(0,r.default)((0,n.default)().mark((function i(){var r;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!a.associate_history[e][t].is_click){i.next=2;break}return i.abrupt("return");case 2:return a.associate_history[e][t].is_click=!0,r=a.associate_history[e][t].keyword_text,i.prev=4,i.next=7,a.$api.sendRequest({url:s.default.searchHitUrl,data:{search_text:e,hit_text:r},async:!1});case 7:i.sent,i.next=12;break;case 10:i.prev=10,i.t0=i["catch"](4);case 12:case"end":return i.stop()}}),i,null,[[4,10]])})))()},searchReport:function(){var e=arguments,t=this;return(0,r.default)((0,n.default)().mark((function a(){var i,r,o,c;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=e.length>0&&void 0!==e[0]?e[0]:"",r=e.length>1?e[1]:void 0,o=e.length>2?e[2]:void 0,c="".concat(i,"-").concat(r),!t.search_report_list.includes(c)){a.next=6;break}return a.abrupt("return");case 6:return t.search_report_list.push(c),a.prev=7,a.next=10,t.$api.sendRequest({url:s.default.searchReportUrl,data:{source:i,search_text:r,source_search_text:o},async:!1});case 10:a.sent,a.next=15;break;case 13:a.prev=13,a.t0=a["catch"](7);case 15:case"end":return a.stop()}}),a,null,[[7,13]])})))()}}};t.default=c},3164:function(e,t,a){"use strict";a.r(t);var i=a("1c72"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"327f":function(e,t,a){"use strict";var i=a("a971"),n=a.n(i);n.a},"37ff":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},n=[]},"5fdb":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7c6365dc]{width:100%;text-align:center}.search-top-wrap-input[data-v-7c6365dc]{border-radius:%?40?% 0 0 %?40?%;background:#f7f7f7;padding-left:%?24?%;font-size:%?32?%;font-weight:400;line-height:%?37.26?%;color:#000;z-index:1}.search-top-wrap-input-placeholder[data-v-7c6365dc]{font-size:%?26?%;font-weight:400;line-height:%?30.26?%;color:#a6a6a6}.search-top-wrap-wrapper[data-v-7c6365dc]{position:relative;border-radius:%?40?%;background:#f7f7f7}.search-top-wrap-wrapper .iconguanbi[data-v-7c6365dc]{color:#c4c4c4;font-size:%?32?%;position:absolute;right:%?100?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:10}.search-top-wrap-wrapper .iconsousuo[data-v-7c6365dc]{margin:0;font-size:%?32?%;color:#fff;background:var(--custom-brand-color);width:%?80?%;height:%?52?%;border-radius:%?40?%;display:flex;justify-content:center;align-items:center;position:absolute;right:%?6?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:10}.search-top-wrap-associate[data-v-7c6365dc]{position:fixed;left:0;width:100%;background-color:#fff;padding:0 %?20?%;box-sizing:border-box;padding-bottom:%?40?%;overflow-y:scroll}.search-top-wrap-associate-one[data-v-7c6365dc]{display:flex;justify-content:space-between;align-items:center;height:%?88?%;border-bottom:%?2?% solid #fafafa;box-sizing:border-box}.search-top-wrap-associate-one-left[data-v-7c6365dc]{font-size:%?28?%;font-weight:400;line-height:%?88?%;color:#383838}.search-top-wrap-associate-one-left-select[data-v-7c6365dc]{color:var(--custom-brand-color)}.search-top-wrap-associate-one-right-icon[data-v-7c6365dc]{display:block;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}',""]),e.exports=t},"8f68":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},9127:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},a971:function(e,t,a){var i=a("8f68");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("2d38abe0",i,!0,{sourceMap:!1,shadowMode:!1})},aaba:function(e,t,a){"use strict";var i=a("d133"),n=a.n(i);n.a},b8ea:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var n=i(a("9127")),r={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:n.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=r},bc40:function(e,t,a){"use strict";a.r(t);var i=a("cbf5"),n=a("3164");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("aaba");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"7c6365dc",null,!1,i["a"],void 0);t["default"]=s.exports},cbf5:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={uniIcons:a("de74").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"search-top-wrap"},[a("v-uni-view",{staticClass:"search-top-wrap-wrapper",style:{height:e.height,width:e.width}},[a("v-uni-input",{staticClass:"search-top-wrap-input",style:{height:e.height,width:e.input_width},attrs:{type:"text",maxlength:"50","placeholder-class":"search-top-wrap-input-placeholder",placeholder:e.placeholder,focus:e.focus,"confirm-type":e.confirmType},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.to_input_value.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toConfirm.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.toFocus.apply(void 0,arguments)}},model:{value:e.input_value,callback:function(t){e.input_value=t},expression:"input_value"}}),e.input_value?a("v-uni-text",{staticClass:"iconfont iconsousuo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toConfirm.apply(void 0,arguments)}}}):e._e()],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.focus&&e.input_value&&e.associate_history[e.input_value]&&e.associate_history[e.input_value].length,expression:"focus && input_value && associate_history[input_value] && associate_history[input_value].length"}],staticClass:"search-top-wrap-associate",style:{top:e.associateTop,height:"calc(100vh - "+e.associateTop+")"}},e._l(e.associate_history[e.input_value],(function(t,i){return a("v-uni-view",{key:i,staticClass:"search-top-wrap-associate-one",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickAssociate(i)}}},[a("v-uni-view",{staticClass:"search-top-wrap-associate-one-left"},e._l(t.keyword_list,(function(i,n){return a("v-uni-text",{key:i.stat_id},[e._v(e._s(i)),n<t.keyword_list.length-1?a("v-uni-text",{staticClass:"search-top-wrap-associate-one-left-select"},[e._v(e._s(e.input_value))]):e._e()],1)})),1),a("v-uni-view",{staticClass:"search-top-wrap-associate-one-right"},[a("uni-icons",{staticClass:"search-top-wrap-associate-one-right-icon",attrs:{type:"arrowthinright",size:"32rpx",color:"rgba(196, 196, 196, 1)"}})],1)],1)})),1)],1)},r=[]},d133:function(e,t,a){var i=a("5fdb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("f7eb71ca",i,!0,{sourceMap:!1,shadowMode:!1})},de74:function(e,t,a){"use strict";a.r(t);var i=a("37ff"),n=a("fefc");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("327f");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"65e5b6d2",null,!1,i["a"],void 0);t["default"]=s.exports},fefc:function(e,t,a){"use strict";a.r(t);var i=a("b8ea"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a}}]);