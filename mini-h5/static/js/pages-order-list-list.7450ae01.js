(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-list-list"],{"0d02":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(o("2634")),a=i(o("2fdc")),n={data:function(){return{list:[],img:["public/static/youpin/coupon_bg_1.png","public/static/youpin/coupon_bg_2.png","public/static/youpin/coupon_bg_3.png","public/static/youpin/coupon_bg_4.png"],boxClass:["box1","box2","box3","box4"]}},onLoad:function(){this.$util.toShowCouponPopup(this)},methods:{open:function(){this.listInfo()},listInfo:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 3:o=e.sent,o.data.length&&(t.list=o.data,t.$refs.coupon.open()),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})}}};e.default=n},"1df9":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-666e9b38]{width:100%;text-align:center}.order-container[data-v-666e9b38]{width:100vw;height:100vh}.order-nav[data-v-666e9b38]{width:calc(100vw - %?120?%);height:%?100?%;flex-direction:row;white-space:nowrap;background:#fff;position:fixed;left:0;z-index:998;overflow-x:auto;border-bottom:%?2?% solid #f5f5f5;box-sizing:border-box}.order-nav .uni-tab-item[data-v-666e9b38]{display:inline-flex;align-items:center;flex-wrap:nowrap;padding-left:%?17?%;padding-right:%?17?%;height:100%}.order-nav .uni-tab-item-title[data-v-666e9b38]{color:#555;font-size:%?30?%;display:block;height:%?64?%;line-height:%?64?%;padding:0 %?10?%;flex-wrap:nowrap;white-space:nowrap}.order-nav .uni-tab-item-title .line[data-v-666e9b38]{width:%?36?%;height:%?6?%;background:transparent;border-radius:%?3?%;margin:0 auto}.order-nav .uni-tab-item-title-active[data-v-666e9b38]{display:block;height:%?64?%;padding:0 %?10?%;position:relative}.order-nav .uni-tab-item-title-active .line[data-v-666e9b38]{width:%?24?%;height:%?24?%;overflow:hidden;background:transparent;border-bottom-left-radius:%?28?%;border-bottom-right-radius:%?0?%;border-left:%?6?% solid var(--custom-brand-color);border-bottom:%?6?% solid var(--custom-brand-color);position:absolute;left:50%;bottom:%?-10?%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.order-nav[data-v-666e9b38] ::-webkit-scrollbar{width:0;height:0;color:transparent}.uni-tab-filter[data-v-666e9b38]{position:fixed;top:0;right:0;display:flex;align-items:center;box-sizing:border-box;background-color:initial;z-index:9999;color:#555;font-size:%?30?%;flex-wrap:nowrap;white-space:nowrap}.uni-tab-filter .line[data-v-666e9b38]{width:%?36?%;height:%?6?%;background:transparent;border-radius:%?3?%;margin:0 auto}.uni-tab-filter-fill[data-v-666e9b38]{display:inline-block;height:%?98?%;width:%?30?%;background:linear-gradient(90deg,hsla(0,0%,80%,0),#fff 60%)}.uni-tab-filter-info[data-v-666e9b38]{background-color:#fff;box-sizing:border-box;display:inline-flex!important;align-items:center;height:%?98?%;padding-left:0!important;padding-bottom:%?4?%;padding-right:%?24?%;padding-top:%?6?%}.uni-tab-filter-info-title[data-v-666e9b38]{display:flex!important;align-items:center;padding:0!important;font-size:%?30?%}.uni-tab-filter-info-title-icon[data-v-666e9b38]{width:%?32?%;height:%?32?%;margin-bottom:%?2?%}.uni-tab-filter-info-title-filter[data-v-666e9b38]{color:var(--custom-brand-color)!important}.order-item[data-v-666e9b38]{margin:%?20?% %?20?%;padding:%?20?% %?20?%;border-radius:%?20?%;background:#fff;position:relative}.order-item .order-header[data-v-666e9b38]{display:flex;align-items:center;position:relative;justify-content:space-between}.order-item .order-header.waitpay .iconyuan_checked[data-v-666e9b38],\r\n.order-item .order-header.waitpay .iconyuan_checkbox[data-v-666e9b38]{font-size:%?36?%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.order-item .order-header.waitpay .iconyuan_checkbox[data-v-666e9b38]{color:#a6a6a6}.order-item .order-header .icondianpu[data-v-666e9b38]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?30?%;color:#333}.order-item .order-header .status-name[data-v-666e9b38]{text-align:right}.order-item .order-body[data-v-666e9b38]{margin-top:%?20?%}.order-item .order-body .goods-wrap[data-v-666e9b38]{margin-bottom:%?20?%;display:flex;position:relative}.order-item .order-body .goods-wrap[data-v-666e9b38]:last-of-type{margin-bottom:0}.order-item .order-body .goods-wrap .goods-img[data-v-666e9b38]{width:%?200?%;height:%?200?%;margin-right:%?20?%}.order-item .order-body .goods-wrap .goods-img uni-image[data-v-666e9b38]{width:100%;height:100%;border-radius:%?20?%}.order-item .order-body .goods-wrap .goods-info[data-v-666e9b38]{flex:1;position:relative;padding:%?28?% 0 0 0;max-width:calc(100% - %?200?%)}.order-item .order-body .goods-wrap .goods-info-bottom[data-v-666e9b38]{display:flex;align-items:center;justify-content:space-between;margin-top:%?10?%}.order-item .order-body .goods-wrap .goods-info .goods-name[data-v-666e9b38]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838;min-height:%?85?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-666e9b38]{font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838;display:flex;justify-content:space-between}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-666e9b38]:last-child{margin-left:%?10?%}.order-item .order-body .goods-wrap .goods-info .goods-price[data-v-666e9b38]{color:#383838;font-size:%?32?%;text-align:right}.order-item .order-body .goods-wrap .goods-info .unit[data-v-666e9b38]{font-weight:400;font-size:%?26?%;margin-right:%?2?%}.order-item .order-footer .order-base-info[data-v-666e9b38]{display:flex}.order-item .order-footer .order-base-info .total[data-v-666e9b38]{text-align:right;padding-top:%?20?%;flex:1}.order-item .order-footer .order-base-info .total > uni-text[data-v-666e9b38]{line-height:1;margin-left:%?10?%}.order-item .order-footer .order-base-info .total uni-text[data-v-666e9b38]{font-size:%?26?%;color:#343434}.order-item .order-footer .order-base-info .total uni-text uni-text[data-v-666e9b38]:last-of-type{margin-left:0}.order-item .order-footer .order-base-info .total uni-text.strong[data-v-666e9b38]{font-weight:700;font-size:%?32?%}.order-item .order-footer .order-operation[data-v-666e9b38]{display:flex;justify-content:space-between;align-items:center;text-align:right;padding-top:%?20?%;position:relative}.order-item .order-footer .order-operation-left[data-v-666e9b38]{display:flex;align-items:center;font-size:%?26?%;font-weight:400;height:%?38?%;line-height:%?38?%}.order-item .order-footer .order-operation-right[data-v-666e9b38]{display:flex;align-items:center}.order-item .order-footer .order-operation .operation-btn[data-v-666e9b38]{line-height:1;padding:%?20?% %?26?%;color:#333;display:inline-block;border-radius:%?32?%;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?10?%}.empty[data-v-666e9b38]{padding-top:%?200?%;text-align:center}.empty .empty-image[data-v-666e9b38]{width:%?180?%;height:%?180?%}.order-batch-operation[data-v-666e9b38]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);text-align:right}.order-batch-operation.bottom-safe-area[data-v-666e9b38]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-batch-operation .operation-btn[data-v-666e9b38]{height:%?68?%;line-height:%?68?%;background:#fff;padding:0 %?40?%;display:inline-block;text-align:center;margin:%?16?% %?20?% %?16?% 0;border-radius:%?40?%;border:.5px solid #fff}.order-batch-operation .operation-btn.white[data-v-666e9b38]{height:%?68?%;line-height:%?68?%;color:#333;border:.5px solid #999;background:#fff}.high-text-color[data-v-666e9b38]{color:var(--custom-brand-color)!important}.order-box-btn[data-v-666e9b38]{min-width:%?160?%;padding:0 %?16?%;box-sizing:border-box;display:flex;height:%?64?%;line-height:%?64?%;align-items:center;justify-content:center;border-color:#ccc;color:#666;font-size:%?26?%}.order-box-btn.order-pay[data-v-666e9b38]{background:var(--custom-brand-color)!important;border-color:var(--custom-brand-color);color:#fff}.site-name-box[data-v-666e9b38]{display:flex;align-items:center}.site-name-box .site-name[data-v-666e9b38]{font-size:%?26?%;font-weight:400;color:#383838;display:flex;align-items:center}.site-name-box .site-name-copy[data-v-666e9b38]{width:%?32?%;height:%?32?%;margin-left:%?16?%}.order-filter-pop[data-v-666e9b38] .uni-popup__wrapper-box{border-radius:0!important}.order-filter-pop-form[data-v-666e9b38]{padding:0 %?40?%;padding-bottom:%?52?%;box-sizing:border-box}.order-filter-pop-form-row[data-v-666e9b38]{border-bottom:%?2?% solid #f5f5f5;display:flex;align-items:center;height:%?108?%}.order-filter-pop-form-row-label[data-v-666e9b38]{font-size:%?30?%;font-weight:400;line-height:%?34.92?%;color:#383838;margin-right:%?38?%;min-width:%?120?%}.order-filter-pop-form-row-date[data-v-666e9b38]{display:flex;align-items:center}.order-filter-pop-form-row-date-select[data-v-666e9b38]{line-height:%?34.92?%}.order-filter-pop-form-row-date-one[data-v-666e9b38]{width:%?200?%;display:inline-block;font-size:%?30?%;font-weight:400;line-height:%?34.92?%}.order-filter-pop-form-row-date-one-placeholder[data-v-666e9b38]{color:#e5e5e5}.order-filter-pop-form-row-date-separator[data-v-666e9b38]{font-size:%?32?%;font-weight:400;line-height:%?37.26?%;color:#383838;padding:0 %?32?%;box-sizing:border-box}.order-filter-pop-form-row-value-placeholder[data-v-666e9b38]{font-size:%?30?%;font-weight:400;line-height:%?34.92?%;color:#e5e5e5}.order-filter-pop-form-op[data-v-666e9b38]{display:flex;justify-content:center;align-items:center;margin-top:%?42?%}.order-filter-pop-form-op-reset[data-v-666e9b38]{width:%?220?%;height:%?68?%;border-radius:%?100?%;background:#fff;border:%?2?% solid #e5e5e5;font-size:%?30?%;font-weight:400;line-height:%?34.92?%;color:#383838;margin-right:%?50?%;display:flex;justify-content:center;align-items:center}.order-filter-pop-form-op-search[data-v-666e9b38]{width:%?220?%;height:%?68?%;border-radius:%?100?%;background:var(--custom-brand-color);font-size:%?30?%;font-weight:400;line-height:%?34.92?%;color:#fff;display:flex;justify-content:center;align-items:center}\r\n/* 标题栏 */.custom[data-v-666e9b38]{background:#fff;display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%}.custom .iconfont[data-v-666e9b38]{font-size:%?40?%;color:#333;font-weight:700;position:absolute;left:%?20?%}.custom .custom-navbar[data-v-666e9b38]{display:flex;width:%?360?%;align-items:center}.custom .custom-navbar .navbar-item[data-v-666e9b38]{height:%?60?%;line-height:%?60?%;width:100%;text-align:center;color:#333;font-size:%?30?%}.page-title[data-v-666e9b38]{width:%?360?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;text-align:center}',""]),t.exports=e},"2d01":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"313a":function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={uniPopup:o("5e99").default},r=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("uni-popup",{ref:"coupon",attrs:{custom:!0,"mask-click":!1}},[t.list.length?o("v-uni-view",{staticClass:"coupon-model",class:t.list.length<4?t.boxClass[t.list.length-1]:t.boxClass[3],style:{"background-image":"url("+(t.list.length<4?t.$util.img(t.img[t.list.length-1]):t.$util.img(t.img[3]))+")"}},[o("v-uni-view",{staticClass:"coupon-header"},[o("v-uni-view",{staticClass:"title"},[t._v("恭喜您获得以下优惠券")]),o("v-uni-view",{staticClass:"tip"},[t._v("马上去使用吧！")])],1),o("v-uni-view",{staticClass:"coupon-box"},t._l(t.list,(function(e,i){return o("v-uni-view",{key:i,staticClass:"coupon-list",style:{"background-image":"url("+t.$util.img("public/static/youpin/coupon_border.png")+")"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodList(e)}}},[o("v-uni-view",{staticClass:"left"},[o("v-uni-view",{staticClass:"info"},[o("v-uni-view",[t._v("¥")]),e.money<100?[0==e.money.split(".")[1]?o("v-uni-view",[t._v(t._s(e.money.split(".")[0]))]):t._e(),e.money.split(".")[1]>0?o("v-uni-view",[t._v(t._s(e.money.split(".")[0])+"."),o("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])]):t._e()]:o("v-uni-view",{staticClass:"money-thousand"},[t._v(t._s(e.money.split(".")[0])+"."),o("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])])],2)],1),o("v-uni-view",{staticClass:"right"},[o("v-uni-view",[o("v-uni-view",{staticClass:"name"},[t._v(t._s(e.desc))]),o("v-uni-view",{staticClass:"time h5-time"},[t._v("有效期至"+t._s(e.end_time))])],1),o("v-uni-view",{staticClass:"btn"},[o("v-uni-view",{staticClass:"h5-btn"},[t._v("去使用")])],1)],1)],1)})),1)],1):t._e(),o("v-uni-image",{staticClass:"pop-ad-info-close",attrs:{src:t.$util.img("public/static/youpin/icon-close-overlay.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.coupon.close()}}})],1)],1)},a=[]},3745:function(t,e,o){"use strict";var i=o("764d"),r=o.n(i);r.a},"44a2":function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={uniNavBar:o("d817").default,uniCountDown:o("5ab1").default,nsEmpty:o("dc6c").default,uniPopup:o("5e99").default,loadingCover:o("5510").default,ydAuthPopup:o("161f").default,nsLogin:o("4f5a").default,uniCouponPop:o("8765").default},r=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"order-container",class:t.themeStyle,style:[t.themeColorVar]},[t.isOnXianMaiApp?o("uni-nav-bar",{attrs:{"left-icon":"back",border:!1},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}},[[o("v-uni-view",{staticClass:"page-title"},[t._v("我的订单")])]],2):t._e(),o("v-uni-scroll-view",{staticClass:"order-nav",style:{top:t.statusBarHeight+t.navHeight+"px"},attrs:{id:"tab-bar","scroll-x":!0,"show-scrollbar":!1,"scroll-into-view":t.scrollInto}},t._l(t.statusList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"uni-tab-item",attrs:{id:e.id,"data-current":i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap.apply(void 0,arguments)}}},[o("v-uni-view",{staticClass:"uni-tab-item-title",class:e.status==t.orderStatus?"uni-tab-item-title-active high-text-color":""},[t._v(t._s(e.name)),o("v-uni-view",{staticClass:"line"})],1)],1)})),1),o("v-uni-view",{staticClass:"uni-tab-filter",style:{top:t.statusBarHeight+t.navHeight+"px"}},[o("v-uni-view",{staticClass:"uni-tab-filter-fill"}),o("v-uni-view",{staticClass:"uni-tab-item uni-tab-filter-info",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toShowFilter.apply(void 0,arguments)}}},[o("v-uni-view",{staticClass:"uni-tab-item-title uni-tab-filter-info-title",class:{"uni-tab-filter-info-title-filter":t.isClickFilter}},[o("v-uni-image",{staticClass:"uni-tab-filter-info-title-icon",attrs:{src:t.orderFilterBg}}),t._v("筛选")],1)],1)],1),o("mescroll-uni",{ref:"mescroll",class:{"empty-content":t.orderList.length<1},attrs:{top:100+2*(t.navHeight+t.statusBarHeight)+"rpx"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length>0?o("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"order-item"},[o("v-uni-view",{staticClass:"order-header",class:{waitpay:"waitpay"==t.orderStatus&&0==e.order_status}},[o("v-uni-view",{staticClass:"site-name-box"},[o("v-uni-view",{staticClass:"site-name"},[t._v("订单号："+t._s(e.order_no)),o("v-uni-image",{staticClass:"site-name-copy",attrs:{src:t.$util.img("/public/static/youpin/order/copy-two.png")},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.to_copy_order_no(e.order_no)}}})],1)],1),o("v-uni-text",{staticClass:"status-name high-text-color"},[t._v(t._s(e.order_status_name))])],1),o("v-uni-view",{staticClass:"order-body",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},t._l(e.order_goods,(function(r,a){return o("v-uni-view",{key:a,staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(r.sku_image),mode:"aspectFit","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i,a)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(r.goods_name))]),o("v-uni-view",{staticClass:"goods-info-bottom"},[o("v-uni-view",{staticClass:"goods-info-bottom-left"},[1==e.is_maidou_pay?o("v-uni-view",{staticClass:"goods-price"},[o("v-uni-text",[t._v(t._s(r.price)+"迈豆")])],1):o("v-uni-view",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",[t._v(t._s(r.price))])],1)],1),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-view",[t._v(t._s(r.spec_name))]),o("v-uni-view",[t._v("x"+t._s(r.num))])],1)],1)],1)],1)})),1),o("v-uni-view",{staticClass:"order-footer"},[o("v-uni-view",{staticClass:"order-base-info"},[o("v-uni-view",{staticClass:"total"},[o("v-uni-text",[t._v("共"+t._s(e.goods_num)+"件商品")]),o("v-uni-text",[t._v("总计："),1==e.is_maidou_pay?[o("v-uni-text",{staticClass:"strong"},[t._v(t._s(e.pay_money)+"迈豆")])]:[o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"strong"},[t._v(t._s("BALANCE"==e.pay_type?e.balance_money:e.pay_money))])]],2)],1)],1),e.action.length>0?o("v-uni-view",{staticClass:"order-operation"},[e.action&&e.action.filter((function(t){return"orderPay"==t.action})).length?o("v-uni-view",{staticClass:"order-operation-left"},[o("v-uni-view",{staticClass:"countdown"},[o("v-uni-view",{staticClass:"clockrun"},[o("uni-count-down",{attrs:{day:e.discountTimeMachine.d,hour:e.discountTimeMachine.h,minute:e.discountTimeMachine.i,second:e.discountTimeMachine.s,color:"var(--custom-brand-color)",splitorColor:"var(--custom-brand-color)","background-color":"transparent"}})],1)],1),o("v-uni-text",[t._v("后取消订单")])],1):o("v-uni-view"),o("v-uni-view",{staticClass:"order-operation-right"},t._l(e.action,(function(i,r){return o("v-uni-view",{key:r,staticClass:"order-box-btn",class:{"order-pay":"orderPay"==i.action||"memberTakeDelivery"==i.action},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(i,e)}}},[t._v(t._s(i.title))])})),1)],1):t._e()],1)],1)})),1):o("v-uni-view",[o("ns-empty",{attrs:{isIndex:!1,text:t.text,entrance:"orderList",fixed:!1}})],1)],1)],2),o("uni-popup",{ref:"orderFilter",staticClass:"order-filter-pop",attrs:{type:"top",top:100+2*(t.navHeight+t.statusBarHeight)+"rpx"}},[o("v-uni-view",{staticClass:"order-filter-pop-form"},[o("v-uni-view",{staticClass:"order-filter-pop-form-row"},[o("v-uni-text",{staticClass:"order-filter-pop-form-row-label"},[t._v("商品名字")]),o("v-uni-input",{staticClass:"order-filter-pop-form-row-value",attrs:{type:"text","placeholder-class":"order-filter-pop-form-row-value-placeholder",placeholder:"请输入商品名称"},model:{value:t.goods_name,callback:function(e){t.goods_name=e},expression:"goods_name"}})],1),o("v-uni-view",{staticClass:"order-filter-pop-form-row"},[o("v-uni-text",{staticClass:"order-filter-pop-form-row-label"},[t._v("订单号")]),o("v-uni-input",{staticClass:"order-filter-pop-form-row-value",attrs:{type:"text","placeholder-class":"order-filter-pop-form-row-value-placeholder",placeholder:"请输入订单号"},model:{value:t.order_no,callback:function(e){t.order_no=e},expression:"order_no"}})],1),o("v-uni-view",{staticClass:"order-filter-pop-form-row"},[o("v-uni-text",{staticClass:"order-filter-pop-form-row-label"},[t._v("日期")]),o("v-uni-view",{staticClass:"order-filter-pop-form-row-date"},[o("v-uni-picker",{staticClass:"order-filter-pop-form-row-date-select",attrs:{mode:"date",value:t.date_start},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.startDateChange.apply(void 0,arguments)}}},[o("v-uni-text",{staticClass:"order-filter-pop-form-row-date-one",class:{"order-filter-pop-form-row-date-one-placeholder":!t.date_start}},[t._v(t._s(t.date_start?t.date_start:"开始日期"))])],1),o("v-uni-text",{staticClass:"order-filter-pop-form-row-date-separator"},[t._v("-")]),o("v-uni-picker",{staticClass:"order-filter-pop-form-row-date-select",attrs:{mode:"date",value:t.date_end},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.endDateChange.apply(void 0,arguments)}}},[o("v-uni-text",{staticClass:"order-filter-pop-form-row-date-one",class:{"order-filter-pop-form-row-date-one-placeholder":!t.date_end}},[t._v(t._s(t.date_end?t.date_end:"结束日期"))])],1)],1)],1),o("v-uni-view",{staticClass:"order-filter-pop-form-op"},[o("v-uni-text",{staticClass:"order-filter-pop-form-op-reset",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toResetOrder.apply(void 0,arguments)}}},[t._v("重置")]),o("v-uni-text",{staticClass:"order-filter-pop-form-op-search",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toFilterOrder.apply(void 0,arguments)}}},[t._v("搜索")])],1)],1)],1),o("loading-cover",{ref:"loadingCover"}),o("yd-auth-popup",{ref:"ydauth"}),o("ns-login",{ref:"login"}),o("uni-coupon-pop",{ref:"couponPop"})],1)},a=[]},"46e8":function(t,e,o){var i=o("c881");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=o("967d").default;r("f73cf6f6",i,!0,{sourceMap:!1,shadowMode:!1})},"50b4":function(t,e,o){"use strict";o.r(e);var i=o("0d02"),r=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"51e3":function(t,e,o){"use strict";o.r(e);var i=o("ee25"),r=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},5579:function(t,e,o){"use strict";var i=o("bfc4"),r=o.n(i);r.a},"5ab1":function(t,e,o){"use strict";o.r(e);var i=o("78f85"),r=o("fe2a");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);o("3745");var n=o("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,"d6071282",null,!1,i["a"],void 0);e["default"]=s.exports},"764d":function(t,e,o){var i=o("b81d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=o("967d").default;r("36126566",i,!0,{sourceMap:!1,shadowMode:!1})},7759:function(t,e,o){"use strict";o.r(e);var i=o("44a2"),r=o("51e3");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);o("9e4c"),o("5579");var n=o("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,"666e9b38",null,!1,i["a"],void 0);e["default"]=s.exports},"78f85":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},r=[]},8416:function(t,e,o){"use strict";var i=o("46e8"),r=o.n(i);r.a},8765:function(t,e,o){"use strict";o.r(e);var i=o("313a"),r=o("50b4");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);o("8416");var n=o("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,"7ca63514",null,!1,i["a"],void 0);e["default"]=s.exports},"88e4":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(o("39d8")),a=i(o("2634")),n=i(o("2fdc")),s=(i(o("cbf3")),{methods:{orderPay:function(t,e){var o=this;return(0,n.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,o.$util.subscribeMessage({source:"order",source_id:t.order_id,scene_type:"order_pay_before"});case 2:uni.showLoading({mask:!0,title:"加载中"}),0==t.adjust_money?o.$api.sendRequest({url:"/api/order/pay",data:{order_ids:t.order_id},success:function(i){if(i.code>=0)o.createBuriedPoint("out_trade_no",i.data,3),o.orderPayPay(i.data,t,(function(t){"function"==typeof e&&e(t)}));else{if(-11==i.code)return uni.hideLoading(),"function"==typeof e&&e(i),!1;o.$util.showToast({title:i.message}),uni.hideLoading()}}}):uni.showModal({title:"提示",content:"商家已将支付金额调整为"+t.pay_money+"元，是否继续支付？",success:function(i){i.confirm&&o.$api.sendRequest({url:"/api/order/pay",data:{order_ids:t.order_id},success:function(i){if(i.code>=0)o.createBuriedPoint("out_trade_no",i.data,3),o.orderPayPay(i.data,t,(function(t){"function"==typeof e&&e(t)}));else{if(-11==i.code)return uni.hideLoading(),"function"==typeof e&&e(i),!1;o.$util.showToast({title:i.message}),uni.hideLoading()}}})}});case 4:case"end":return i.stop()}}),i)})))()},orderClose:function(t,e){var o=this;uni.showModal({title:"提示",content:"确定要取消该订单吗？",cancelText:"我再想想",confirmText:"确定",success:function(i){i.confirm&&o.$api.sendRequest({url:"/api/order/close",data:{order_id:t},success:function(i){if(i.code>=0)o.createBuriedPoint("order_id",t,250),o.$util.showToast({title:"取消订单成功！",success:function(){"function"==typeof e&&e()}});else{if(-11==i.code)return o.$util.showToast({title:i.message}),setTimeout((function(){"function"==typeof e&&e()}),1500),!1;o.$util.showToast({title:i.message})}}})}})},orderDelivery:function(t,e){var o=this;uni.showModal({title:"提示",content:"你已经收到货物了吗？",confirmText:"已收到",cancelText:"未收到",success:function(i){i.confirm&&o.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t},success:function(t){"function"==typeof e&&e()}})}})},orderPayPay:function(t,e,o){var i=this;e.order_id;this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:t,pay_type:"adapay"},success:function(e){e.code>=0?(uni.hideLoading(),i.$util.wechatPay(e.data.pay_type,"adapay"==e.data.pay_type?e.data.payment:e.data.pay_info,(function(e){i.createBuriedPoint("out_trade_no",t,11),i.$util.redirectTo("/pages/order/list/list?status=all",{},"redirectTo")}),(function(e){i.createBuriedPoint("out_trade_no",t,9001),uni.hideLoading()}),(function(t){uni.hideLoading()}),i)):(console.log(e),e.message?i.$util.showToast({title:e.message}):uni.hideLoading()),"function"==typeof o&&o(e)},fail:function(t){i.$util.showToast({title:"request:fail"})}})},createBuriedPoint:function(t,e,o){var i;this.$buriedPoint.orderStatus((i={},(0,r.default)(i,t,e),(0,r.default)(i,"status",o),i))}}});e.default=s},"8f36":function(t,e,o){var i=o("1df9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=o("967d").default;r("5061e855",i,!0,{sourceMap:!1,shadowMode:!1})},"9e4c":function(t,e,o){"use strict";var i=o("8f36"),r=o.n(i);r.a},b81d:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-d6071282]{width:100%;text-align:center}.uni-countdown[data-v-d6071282]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-d6071282]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?28?%}.uni-countdown__splitor.day[data-v-d6071282]{line-height:%?50?%}.uni-countdown__number[data-v-d6071282]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},bfc4:function(t,e,o){var i=o("f148");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=o("967d").default;r("068feb48",i,!0,{sourceMap:!1,shadowMode:!1})},c881:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7ca63514]{width:100%;text-align:center}.coupon-model[data-v-7ca63514]{display:flex;flex-direction:column;position:relative;z-index:111;width:%?620?%;background-size:cover;background-position:50%}.coupon-model .coupon-header[data-v-7ca63514]{background-size:cover;background-position:50%;margin-bottom:%?117?%}.coupon-model .title[data-v-7ca63514]{font-size:%?40?%;line-height:%?52?%;background-image:-webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:%?124?% 0 %?14?%;text-align:center;font-weight:700}.coupon-model .tip[data-v-7ca63514]{font-size:%?30?%;line-height:%?32?%;background-image:-webkit-linear-gradient(0deg,#fc5a50,#ff561a 46.75293%,#ff2637);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-align:center}.coupon-model .coupon-box[data-v-7ca63514]{flex:1;padding:0 %?54?% 0;background-size:100% 100%;background-position:50%;position:relative;margin-bottom:28px;overflow-y:auto}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]{display:flex;background-size:cover;background-position:50%;height:%?120?%;margin-bottom:%?20?%;position:relative;z-index:11}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]:last-child{margin-bottom:0}.coupon-model .coupon-box .coupon-list .left[data-v-7ca63514]{width:70px;display:flex;align-items:center;justify-content:center}.coupon-model .coupon-box .coupon-list .left .info[data-v-7ca63514]{display:flex;align-items:baseline}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:first-child{display:inline-block;font-size:%?26?%;color:#eb0000}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:last-child{display:inline-block;font-size:%?48?%;color:#eb0000;line-height:%?80?%}.coupon-model .coupon-box .coupon-list .left .point-class[data-v-7ca63514]{font-size:%?35?%}.coupon-model .coupon-box .coupon-list .left .money-thousand[data-v-7ca63514]{font-size:%?41?%!important}.coupon-model .coupon-box .coupon-list .left .money-thousand > span[data-v-7ca63514]{font-size:%?29?%}.coupon-model .coupon-box .coupon-list .right[data-v-7ca63514]{width:%?238?%;flex:1;display:flex;align-items:center;position:relative;margin-left:%?18?%}.coupon-model .coupon-box .coupon-list .right > uni-view[data-v-7ca63514]:first-child{flex:1;overflow:hidden;height:100%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .name[data-v-7ca63514]{font-size:%?24?%;line-height:%?36?%;padding:%?24?% 0 %?8?%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .time[data-v-7ca63514]{font-size:%?18?%;line-height:%?30?%;color:#999;white-space:nowrap}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .h5-time[data-v-7ca63514]{display:flex;width:119px;margin-left:-13px;-webkit-transform:scale(.78);transform:scale(.78)}.coupon-model .coupon-box .coupon-list .right .btn[data-v-7ca63514]{display:flex;justify-content:center;align-items:center;width:%?94?%;height:%?38?%;background:linear-gradient(90deg,#ffab37,#fff594);border-radius:19px;font-size:%?24?%;color:#822d02;margin:0 %?10?% 0 0}.coupon-model .coupon-box .coupon-list .right .h5-btn[data-v-7ca63514]{-webkit-transform:scale(.8);transform:scale(.8)}.coupon-model .coupon_bg[data-v-7ca63514]{position:absolute;top:%?270?%;width:100%;height:%?153?%;z-index:-1}.coupon-model .coupon_footer[data-v-7ca63514]{width:100%;height:%?51?%}.box1[data-v-7ca63514]{height:%?565?%}.box2[data-v-7ca63514]{height:%?660?%}.box3[data-v-7ca63514]{height:%?800?%}.box4[data-v-7ca63514]{height:%?850?%}.pop-ad-info-close[data-v-7ca63514]{width:%?88?%;height:%?88?%;display:block;margin:%?60?% auto 0}',""]),t.exports=e},ecc6:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},created:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,o,i){return 60*t*60*24+60*e*60+60*o+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,o=0,i=0,r=0;t>0?(e=Math.floor(t/86400),o=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*o,r=Math.floor(t)-24*e*60*60-60*o*60-60*i):this.timeUp(),e<10&&(e="0"+e),o<10&&(o="0"+o),i<10&&(i="0"+i),r<10&&(r="0"+r),this.d=e,this.h=o,this.i=i,this.s=r}}};e.default=i},ee25:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(o("2634")),a=i(o("2fdc"));o("f7a5"),o("c223"),o("bf0f"),o("2797"),o("aa9c"),o("e966"),o("dd2b"),o("c9b5"),o("ab80");i(o("7c8d"));var n=i(o("5ab1")),s=i(o("88e4")),d=i(o("85bf")),l=i(o("f8de")),c=i(o("2d01")),u=o("4b89"),p={components:{uniCountDown:n.default},data:function(){return{scrollInto:"",orderStatus:"all",statusList:[],orderList:[],contentText:{},mergePayOrder:[],text:"暂无订单信息",statusBarHeight:0,navHeight:0,isOnXianMaiApp:u.isOnXianMaiApp,isFirstLoad:!0,isClickFilter:!1,order_no:"",goods_name:"",date_start:"",date_end:"",orderFilterBg:""}},mixins:[s.default,l.default,c.default],onLoad:function(t){var e=this;t.status&&(this.orderStatus=t.status),uni.getSystemInfo({success:function(t){e.navHeight=t.statusBarHeight+46-wx.getSystemInfoSync()["statusBarHeight"],e.isOnXianMaiApp||(e.navHeight=0)},fail:function(t){console.log(t)}});var o=this.$util.colorToHex(this.$store.state.themeColorVar["--custom-brand-color"]).slice(1);this.orderFilterBg=encodeURI(this.$util.img("api/website/svgChangeFillColor?svg_name=order-filter&color=".concat(o)))},onShow:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.statusBarHeight=wx.getSystemInfoSync()["statusBarHeight"],t.$langConfig.refresh(),e.next=4,d.default.wait_staticLogin_success();case 4:t.getOrderStatus(),uni.getStorageSync("token")?t.$refs.mescroll&&!t.isFirstLoad&&t.$refs.mescroll.refresh():(o="/pages/order/list/list?status=".concat(t.orderStatus),t.$util.toShowLoginPopup(t,null,o)),uni.getStorageSync("is_register")&&(t.$util.toShowCouponPopup(t),uni.removeStorageSync("is_register"));case 7:case"end":return e.stop()}}),e)})))()},methods:{ontabtap:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,"all"==this.orderStatus?this.text="您还暂无相关订单":"waitpay"==this.orderStatus?this.text="您还暂无待付款订单":"waitsend"==this.orderStatus?this.text="您还暂无待发货订单":"waitconfirm"==this.orderStatus?this.text="您还暂无待收货订单":this.text="您还暂无已完成订单",""==this.orderStatus&&(this.mergePayOrder=[]),this.$refs.mescroll.refresh()},getListData:function(t){var e=this;return(0,a.default)((0,r.default)().mark((function o(){var i;return(0,r.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,d.default.wait_staticLogin_success();case 2:i={page:t.num,page_size:t.size,order_status:e.orderStatus},e.isClickFilter&&Object.assign(i,{order_no:e.order_no,date_start:e.date_start,date_end:e.date_end,goods_name:e.goods_name}),e.$api.sendRequest({url:"/api/order/lists",data:i,success:function(o){var i=[],r=o.message;0==o.code&&o.data?i=o.data.list:(uni.getStorageSync("token")||-10009!=o.code)&&e.$util.showToast({title:r}),t.endSuccess(i.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(i);for(var a=0;a<e.orderList.length;a++)("number"==typeof e.orderList[a].rest_pay_time||e.orderList[a].rest_pay_time)&&(e.orderList[a].discountTimeMachine=e.$util.countDown(e.orderList[a].rest_pay_time));e.isFirstLoad=!1,e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}});case 5:case"end":return o.stop()}}),o)})))()},getOrderStatus:function(){this.statusList=[{status:"all",name:this.$lang("all"),id:"status_0"},{status:"waitpay",name:this.$lang("waitPay"),id:"status_1"},{status:"waitsend",name:this.$lang("readyDelivery"),id:"status_2"},{status:"waitconfirm",name:this.$lang("waitDelivery"),id:"status_3"},{status:"waitrate",name:this.$lang("waitEvaluate"),id:"status_4"}]},operation:function(t,e){var o=this,i=t.action;this.status;switch(i){case"orderPay":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderPay(e,(function(t){if(-11==t.code){o.$util.showToast({title:t.message});var e=o;e.orderStatus="all",setTimeout((function(){e.$refs.mescroll.refresh()}),1500)}}));break;case"orderClose":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderClose(e.order_id,(function(){o.orderStatus="all",o.$refs.mescroll.refresh()}));break;case"memberTakeDelivery":this.orderDelivery(e.order_id,(function(){o.$refs.mescroll.refresh()}));break;case"trace":e.package_list&&1==e.package_list.length?this.$util.redirectTo("/pages/order/logistics/logistics",{order_id:e.order_id}):e.package_list&&e.package_list.length>1&&this.$util.redirectTo("/otherpages/order/parcel/parcel",{order_id:e.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/otherpages/order/evaluate/evaluate",{order_id:e.order_id});break;case"orderBuy":this.orderBuy(e);break}},orderBuy:function(t){var e=this;return(0,a.default)((0,r.default)().mark((function o(){var i;return(0,r.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(i=[],t.order_goods.forEach((function(t){return i.push(t.sku_id)})),8!=t.order_create_type){o.next=7;break}return e.$util.redirectTo("/promotionpages/pintuan/detail/detail",{id:t.pintuan_goods_id}),o.abrupt("return");case 7:return e.$util.redirectTo("/pages/goods/detail/detail",{sku_id:i[0]}),o.abrupt("return");case 9:case"end":return o.stop()}}),o)})))()},to_copy_order_no:function(t){this.$util.copy(t)},orderDetail:function(t){switch(t.order_type?parseInt(t.order_type):1){case 2:this.$util.redirectTo("/pages/order/detail_pickup/detail_pickup",{order_id:t.order_id});break;case 3:this.$util.redirectTo("/pages/order/detail_local_delivery/detail_local_delivery",{order_id:t.order_id});break;case 4:this.$util.redirectTo("/pages/order/detail_virtual/detail_virtual",{order_id:t.order_id});break;default:this.$util.redirectTo("/pages/order/detail/detail",{order_id:t.order_id});break}},selectOrder:function(t){-1!=this.$util.inArray(t,this.mergePayOrder)?this.mergePayOrder.splice(this.$util.inArray(t,this.mergePayOrder),1):this.mergePayOrder.push(t)},mergePay:function(){var t=this;this.mergePayOrder.length&&this.$api.sendRequest({url:"/api/order/pay",data:{order_ids:this.mergePayOrder.toString()},success:function(e){e.code>=0?t.$util.redirectTo("/pages/pay/index/index",{code:e.data}):t.$util.showToast({title:e.message})}})},imageError:function(t,e){this.orderList[t]&&this.orderList[t].order_goods&&(this.orderList[t].order_goods[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate())},toShopDetail:function(t){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:t})},navigateBack:function(){uni.navigateBack()},navigateCycle:function(){this.$util.redirectTo("/pages/order/manage_cycle/manage_cycle")},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))},toShowFilter:function(){this.$refs.orderFilter.open()},startDateChange:function(t){this.date_start=t.detail.value},endDateChange:function(t){this.date_end=t.detail.value},toResetOrder:function(){this.date_start="",this.date_end="",this.order_no="",this.goods_name="",this.isClickFilter=!1,this.$refs.orderFilter.close(),this.$refs.mescroll.refresh()},toFilterOrder:function(){this.isClickFilter=!0,this.$refs.orderFilter.close(),this.$refs.mescroll.refresh()}},onShareAppMessage:function(t){var e=this.getSharePageParams(),o=e.title,i=e.link,r=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,r,o)},computed:{mpOrderList:function(){if(this.orderList[this.status])return this.orderList[this.status].list||[]},themeStyle:function(){return"theme-"+this.$store.state.themeStyle}}};e.default=p},f148:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,"[data-v-666e9b38] .uni-page{overflow:hidden}[data-v-666e9b38] .mescroll-upwarp{padding-bottom:%?100?%}.empty-content[data-v-666e9b38] .mescroll-uni{background-color:#fff}.countdown .clockrun[data-v-666e9b38] .uni-countdown{display:flex;justify-content:center;align-items:center;padding:0;height:%?38?%;line-height:%?38?%}.countdown .clockrun[data-v-666e9b38] .uni-countdown__number{background:#000;\n\t\t/* // #690b08 */padding:0;margin:0;border:none;font-size:%?26?%;height:%?38?%;line-height:%?38?%}.countdown .clockrun[data-v-666e9b38] .uni-countdown__splitor{padding:0;font-size:%?26?%;height:%?38?%;line-height:%?38?%}.countdown .clockrun[data-v-666e9b38] .uni-countdown__splitor.day{width:auto;font-size:%?26?%;height:%?38?%;line-height:%?38?%}[data-v-666e9b38] .mescroll-upwarp{padding:0!important;margin-bottom:0;min-height:0;line-height:0}[data-v-666e9b38] .mescroll-uni{height:auto!important}",""]),t.exports=e},f8de:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o("4b89"),r={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){i.isOnXianMaiApp&&this.appCurrentPages<=1?(0,i.goClosePage)("0"):uni.navigateBack()}}};e.default=r},fe2a:function(t,e,o){"use strict";o.r(e);var i=o("ecc6"),r=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a}}]);