(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-web-web"],{"0ec1":function(e,t,a){"use strict";a.r(t);var n=a("8d9a"),r=a("6aa1");for(var c in r)["default"].indexOf(c)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(c);a("892f");var i=a("828b"),s=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"dea44d64",null,!1,n["a"],void 0);t["default"]=s.exports},"153f":function(e,t,a){var n=a("e0d1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("967d").default;r("d96ef5fc",n,!0,{sourceMap:!1,shadowMode:!1})},"6aa1":function(e,t,a){"use strict";a.r(t);var n=a("8de5"),r=a.n(n);for(var c in n)["default"].indexOf(c)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(c);t["default"]=r.a},"892f":function(e,t,a){"use strict";var n=a("153f"),r=a.n(n);r.a},"8d9a":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return c})),a.d(t,"a",(function(){return n}));var n={ydAuthPopup:a("161f").default,nsLogin:a("4f5a").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[e.src?a("v-uni-web-view",{attrs:{src:e.src},on:{message:function(t){arguments[0]=t=e.$handleEvent(t),e.postMessage.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.loadpage.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.binderror.apply(void 0,arguments)}}}):e._e(),a("yd-auth-popup",{ref:"ydauth"}),a("ns-login",{ref:"login"})],1)},c=[]},"8de5":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("fd3c"),a("dc8a"),a("c223");var r=n(a("2634")),c=n(a("2fdc")),i=a("d64b"),s=n(a("7c8d")),o=n(a("85bf")),u={data:function(){return{src:"",tmpSrc:"",encodesrc:"",shareData:{}}},onLoad:function(e){var t=this;return(0,c.default)((0,r.default)().mark((function a(){var n,c,u,d,f,l;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,o.default.wait_staticLogin_success();case 2:if(e.scene&&(0,i.scenePare)(!1,e),t.tmpSrc=e.src,!e.encodesrc){a.next=14;break}return a.prev=5,a.next=8,t.$api.sendRequest({url:s.default.decryptDataUrl,async:!1,data:{key:e.encodesrc}});case 8:n=a.sent,0==n.code&&(t.tmpSrc=n.data.decrypt_data),a.next=14;break;case 12:a.prev=12,a.t0=a["catch"](5);case 14:if(c=uni.getStorageSync("token"),!c){a.next=24;break}return a.prev=16,a.next=19,o.default.checkToken();case 19:a.next=24;break;case 21:a.prev=21,a.t1=a["catch"](16),c="";case 24:u=t.tmpSrc;try{u=decodeURIComponent(u),d=u.split("?"),f={},-1!=u.indexOf("/spa/app")&&c&&(f["mini_token"]=c),l=Object.keys(f).map((function(e){return"".concat(e,"=").concat(f[e])})).join("&"),d.length>1?d[1]+="&".concat(l):d[1]=l,u=d.join("?")}catch(r){}t.src=u;case 27:case"end":return a.stop()}}),a,null,[[5,12],[16,21]])})))()},onShow:function(){return(0,c.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},methods:{navigateBack:function(){uni.navigateBack({delta:1})},postMessage:function(e){var t=e.detail.data.length>0?e.detail.data[e.detail.data.length-1]:{};this.shareData=t},loadpage:function(e){},binderror:function(e){},getMemberInfo:function(){var e=this;return(0,c.default)((0,r.default)().mark((function t(){var a,n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={},t.prev=1,t.next=4,e.$api.sendRequest({url:"/api/member/info",async:!1});case 4:n=t.sent,n.code>=0&&n.data&&(a=n.data,a.phone=a.mobile,a.mobile&&(a.mobile=a.mobile.substr(0,3)+"****"+a.mobile.substr(7))),t.next=10;break;case 8:t.prev=8,t.t0=t["catch"](1);case 10:return t.abrupt("return",a);case 11:case"end":return t.stop()}}),t,null,[[1,8]])})))()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/web/web",this.shareData.title?this.shareData.title:"先迈商城","",{src:this.shareData.link?decodeURIComponent(this.shareData.link):decodeURIComponent(this.tmpSrc)},this.shareData.imgUrl?this.shareData.imgUrl:"")}},onShareAppMessage:function(e){var t=this.getSharePageParams(),a=t.title,n=t.link,r=t.imageUrl;t.query;return this.$buriedPoint.pageShare(n,r,a)}};t.default=u},e0d1:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-dea44d64]{width:100%;text-align:center}.navigate-back[data-v-dea44d64]{position:absolute;top:%?34?%;left:%?34?%;z-index:5;font-size:%?32?%}',""]),e.exports=t}}]);