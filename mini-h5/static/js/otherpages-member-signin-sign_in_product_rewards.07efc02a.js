(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-signin-sign_in_product_rewards"],{"0149":function(t,e,i){"use strict";var a=i("3149"),n=i.n(a);n.a},"0b48":function(t,e,i){"use strict";i.r(e);var a=i("b5ce"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"2d01":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2e91":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-46873122]{width:100%;text-align:center}.yd-popup[data-v-46873122]{background:rgba(0,0,0,.4);width:100%;height:100%;z-index:998;position:fixed;top:0;left:0}.yd-popup .share-tip[data-v-46873122]{width:100%;height:%?447?%;display:block}',""]),t.exports=e},"2efc":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",{staticClass:"count component"},[this._v(this._s(this.displayValue))])},n=[]},"2f73":function(t,e,i){"use strict";i.r(e);var a=i("e71a"),n=i("0b48");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("0149");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"46873122",null,!1,a["a"],void 0);e["default"]=o.exports},3149:function(t,e,i){var a=i("2e91");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("31685291",a,!0,{sourceMap:!1,shadowMode:!1})},"31c3":function(t,e,i){"use strict";i.r(e);var a=i("ae57"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"3ecf":function(t,e,i){var a=i("fb10");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("520a22bb",a,!0,{sourceMap:!1,shadowMode:!1})},"48a0":function(t,e,i){"use strict";i.r(e);var a=i("a6e0"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"4d82":function(t,e,i){"use strict";i.r(e);var a=i("bf93"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"528d":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-38dab624]{width:100%;text-align:center}.rolling-order[data-v-38dab624]{position:fixed;display:flex;align-items:center;background-color:rgba(0,0,0,.6);padding:0 %?20?% 0 %?6?%;height:%?60?%;line-height:%?60?%;box-sizing:border-box;border-radius:%?40?%;opacity:0}.rolling-order-head[data-v-38dab624]{width:%?50?%;height:%?50?%;border-radius:50%}.rolling-order-text[data-v-38dab624]{font-size:%?24?%;line-height:1.5;color:#fff;margin-left:%?10?%}',""]),t.exports=e},"570c":function(t,e,i){"use strict";i.r(e);var a=i("b9d2"),n=i("48a0");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("fc31");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"78bf10da",null,!1,a["a"],void 0);e["default"]=o.exports},"629c":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"[data-v-0175af82] .uni-navbar{position:fixed;z-index:999}",""]),t.exports=e},"6e6c":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0175af82]{width:100%;text-align:center}.product-rewards[data-v-0175af82]{padding-bottom:%?176?%;box-sizing:border-box;background:#f0f4f7;min-height:100vh}.product-rewards-bg[data-v-0175af82]{width:100%;background-repeat:no-repeat;background-position:0 0;background-size:100% auto;padding-top:%?298?%;box-sizing:border-box}.product-rewards-header[data-v-0175af82]{padding-left:%?44?%;box-sizing:border-box}.product-rewards-header-one[data-v-0175af82]{font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:#222;display:flex;align-items:center}.product-rewards-header-one-number[data-v-0175af82]{font-size:%?72?%;font-weight:700;line-height:%?72?%;color:var(--custom-brand-color);margin-right:%?6?%}.product-rewards-header-two[data-v-0175af82]{font-size:%?28?%;font-weight:400;letter-spacing:%?0.4?%;line-height:%?32.6?%;color:#222}.product-rewards-sign[data-v-0175af82]{position:relative;width:%?710?%;background-color:#fff;margin:0 auto;margin-top:%?74?%;border-radius:%?60?%;padding:%?38?% %?28?% %?30?% %?28?%;box-sizing:border-box}.product-rewards-sign-header[data-v-0175af82]{display:flex;justify-content:space-between}.product-rewards-sign-header-left[data-v-0175af82]{display:flex}.product-rewards-sign-header-left-name[data-v-0175af82]{font-size:%?40?%;font-weight:700;line-height:%?48?%;color:#222}.product-rewards-sign-header-left-result[data-v-0175af82]{font-size:%?26?%;font-weight:400;line-height:%?30.26?%;color:#383838;align-self:flex-end;margin-left:%?18?%}.product-rewards-sign-header-left-result-date[data-v-0175af82]{font-size:%?36?%;font-weight:700;line-height:%?42.2?%;color:var(--custom-brand-color);margin-right:%?4?%}.product-rewards-sign-header-right[data-v-0175af82]{display:flex;justify-content:center;align-items:center;width:%?248?%;height:%?80?%;border-radius:0 %?50?% 0 %?84?%;background:#f0f4f7;position:absolute;top:0;right:0}.product-rewards-sign-header-right-text[data-v-0175af82]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#222;margin-left:%?4?%;display:flex;align-items:center}.product-rewards-sign-header-right-text .iconfont[data-v-0175af82]{margin-left:%?10?%}.product-rewards-sign-scroll[data-v-0175af82]{height:%?446?%;margin-top:%?66?%}.product-rewards-sign-scroll-one[data-v-0175af82]{height:%?220?%}.product-rewards-sign-list[data-v-0175af82]{width:100%}.product-rewards-sign-list-not[data-v-0175af82]{display:flex;justify-content:center}.product-rewards-sign-list-center[data-v-0175af82]{display:flex;justify-content:center}.product-rewards-sign-list-one[data-v-0175af82]{display:inline-flex;flex-direction:column;align-items:center;width:%?138?%;vertical-align:top;margin-bottom:%?40?%;margin-right:%?34?%}.product-rewards-sign-list-one[data-v-0175af82]:nth-child(4n+4){margin-right:0}.product-rewards-sign-list-one-content[data-v-0175af82]{width:%?138?%;height:%?194?%;border-radius:%?20?%;background:#f0f4f7;display:flex;justify-content:center;align-items:center;flex-direction:column}.product-rewards-sign-list-one-content-signed[data-v-0175af82]{background:var(--custom-brand-color)}.product-rewards-sign-list-one-content-text[data-v-0175af82]{font-size:%?26?%;font-weight:400;letter-spacing:%?0.4?%;line-height:%?30.48?%;color:grey;margin-bottom:%?18?%}.product-rewards-sign-list-one-content-text-today[data-v-0175af82]{color:#383838}.product-rewards-sign-list-one-content-text-signed[data-v-0175af82]{color:#fff}.product-rewards-sign-list-one-content-day[data-v-0175af82]{font-size:%?28?%;font-weight:400;letter-spacing:%?0.4?%;line-height:%?32.82?%;margin-top:%?18?%;color:grey}.product-rewards-sign-list-one-content-day-today[data-v-0175af82]{color:#383838}.product-rewards-sign-list-one-content-day-signed[data-v-0175af82]{color:#fff}.product-rewards-sign-list-one-content-img[data-v-0175af82]{width:%?48?%;height:%?49.85?%;margin-top:%?10?%}.product-rewards-sign-list-one-content-not[data-v-0175af82]{display:flex;flex-direction:column;align-items:center;justify-content:center}.product-rewards-sign-list-one-content-yes[data-v-0175af82]{width:%?46.67?%;height:%?46.67?%}.product-rewards-sign-list-one-content-miss[data-v-0175af82]{width:%?46.67?%;height:%?46.67?%}.product-rewards-sign-list-one-miss[data-v-0175af82]{background:#f0f0f0}.product-rewards-sign-invite[data-v-0175af82]{display:flex;align-items:center;flex-wrap:wrap}.product-rewards-sign-invite-one[data-v-0175af82]{width:%?138?%;height:%?194?%;border-radius:%?20?%;background:#f0f4f7;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-bottom:%?40?%;margin-right:%?34?%}.product-rewards-sign-invite-one[data-v-0175af82]:nth-child(4n+4){margin-right:0}.product-rewards-sign-invite-one-has[data-v-0175af82]{background-color:var(--custom-brand-color)}.product-rewards-sign-invite-one-head[data-v-0175af82]{width:%?88?%;height:%?88?%;border-radius:50%;background:var(--custom-brand-color-10);display:flex;justify-content:center;align-items:center;margin-bottom:%?22?%}.product-rewards-sign-invite-one-head-avatar[data-v-0175af82]{width:%?48?%;height:%?55?%}.product-rewards-sign-invite-one-head-inner[data-v-0175af82]{width:100%;height:100%;border-radius:50%}.product-rewards-sign-invite-one-head-has[data-v-0175af82]{background-color:#fff;padding:%?2?%;box-sizing:border-box}.product-rewards-sign-invite-one-text[data-v-0175af82]{font-size:%?28?%;font-weight:400;letter-spacing:%?0.4?%;line-height:%?32.82?%;color:grey}.product-rewards-sign-invite-one-text-has[data-v-0175af82]{color:#fff}.product-rewards-sign-op[data-v-0175af82]{display:flex;flex-direction:column;align-items:center;margin-top:%?14?%}.product-rewards-sign-op-time[data-v-0175af82]{display:flex;align-items:center;margin-bottom:%?22?%}.product-rewards-sign-op-time-clock[data-v-0175af82]{width:%?32?%;height:%?32?%;margin-right:%?2?%}.product-rewards-sign-op-time-name[data-v-0175af82]{font-size:%?28?%;font-weight:700;line-height:%?32.82?%;color:#222}.product-rewards-sign-op-time[data-v-0175af82] .flex-start-center > uni-view{font-size:%?28?%;font-weight:700;line-height:32.82prx;color:#222}.product-rewards-sign-op-time[data-v-0175af82] .flex-start-center > uni-view:nth-child(odd){color:var(--custom-brand-color)}.product-rewards-sign-op-but[data-v-0175af82]{width:%?664?%;height:%?90?%;line-height:%?90?%;opacity:1;border-radius:200px;background:var(--custom-brand-color);text-align:center;font-size:%?32?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.product-rewards-sign-op-but-icon[data-v-0175af82]{margin-right:%?14?%}.product-rewards-sign-op-but-col[data-v-0175af82]{color:#ffeb3b}.product-rewards-sign-op-but-yes[data-v-0175af82]{background:var(--custom-brand-color-50)}.product-rewards-sign-op-but-disabled[data-v-0175af82]{background:var(--custom-brand-color-50)}.product-rewards-sign-tip[data-v-0175af82]{font-size:%?26?%;font-weight:400;line-height:%?37.5?%;color:grey;text-align:center;margin-top:%?20?%}.product-rewards-sign-tip-number[data-v-0175af82]{font-size:%?32?%;font-weight:700;color:var(--custom-brand-color);margin:0 %?10?%}.product-rewards-gift[data-v-0175af82]{width:%?710?%;margin:0 auto;margin-top:%?20?%;border-radius:%?76?%;box-sizing:border-box;padding-bottom:%?32?%}.product-rewards-gift-header[data-v-0175af82]{height:%?258?%;padding:0 %?26?%;padding-top:%?42?%;box-sizing:border-box;background-repeat:no-repeat;background-size:100% 100%}.product-rewards-gift-header-inner[data-v-0175af82]{display:flex;align-items:center}.product-rewards-gift-content[data-v-0175af82]{box-sizing:border-box;background-color:#fff;margin-top:%?-94?%;padding-bottom:%?22?%;border-radius:0 0 %?76?% %?76?%}.product-rewards-gift-left[data-v-0175af82]{display:flex;align-items:center;margin-right:%?10?%}.product-rewards-gift-left-text[data-v-0175af82]{font-size:%?40?%;font-weight:700;line-height:%?48?%;color:#222;margin-left:%?12?%}.product-rewards-gift-right[data-v-0175af82]{display:flex;align-items:center}.product-rewards-gift-right-badge[data-v-0175af82]{background:var(--custom-brand-color);color:#fff;border-radius:50%;margin-right:%?13?%}.product-rewards-gift-right-badge[data-v-0175af82] .uni-badge--default-inverted{color:#fff}.product-rewards-gift-right-more[data-v-0175af82]{line-height:1.3}.product-rewards-gift-tabs[data-v-0175af82]{height:%?293?%;box-sizing:border-box;padding:0 %?32?%;padding-top:%?17?%;white-space:nowrap}.product-rewards-gift-tabs-one[data-v-0175af82]{display:inline-flex;flex-direction:column;align-items:center;width:%?198?%;padding-bottom:%?12?%;border-radius:%?20?%;background:var(--custom-brand-color-20);padding-top:%?6?%;box-sizing:border-box}.product-rewards-gift-tabs-one-top[data-v-0175af82]{width:%?186?%;height:%?188?%;border-radius:%?14?%;position:relative}.product-rewards-gift-tabs-one-top-img[data-v-0175af82]{width:100%;height:100%;border-radius:%?14?%}.product-rewards-gift-tabs-one-top-text[data-v-0175af82]{width:100%;height:%?40?%;border-radius:0 0 %?14?% %?14?%;background:rgba(0,0,0,.5);font-size:%?22?%;font-weight:400;line-height:%?25.78?%;color:#fff;display:flex;justify-content:center;align-items:center;position:absolute;left:0;bottom:0}.product-rewards-gift-tabs-one-text[data-v-0175af82]{font-size:%?30?%;font-weight:400;line-height:%?35.16?%;color:#383838;margin-top:%?20?%}.product-rewards-gift-tabs-one-text-active[data-v-0175af82]{color:#fff}.product-rewards-gift-tabs-one-text-number[data-v-0175af82]{color:var(--custom-brand-color)}.product-rewards-gift-tabs-one-text-number-active[data-v-0175af82]{color:#fff}.product-rewards-gift-tabs-one-active[data-v-0175af82]{background-color:var(--custom-brand-color)}.product-rewards-gift-tabs-one[data-v-0175af82]:not(:first-child){margin-left:%?20?%}.product-rewards-gift-goods[data-v-0175af82]{margin:0 auto;box-sizing:border-box;padding:0 %?26?%}.product-rewards-gift-goods-one[data-v-0175af82]{display:flex;justify-content:space-between;align-items:center;margin-top:%?32?%;border-radius:%?20?%;background:#fafafa;padding:%?10?% %?16?% %?10?% %?10?%;box-sizing:border-box}.product-rewards-gift-goods-one-img[data-v-0175af82]{border-radius:%?16?%;width:%?160?%;height:%?160?%}.product-rewards-gift-goods-one-center[data-v-0175af82]{width:%?468?%;margin-left:%?16?%}.product-rewards-gift-goods-one-center-name[data-v-0175af82]{font-size:%?32?%;font-weight:700;line-height:%?40?%;color:#383838;text-overflow:ellipsis;overflow:hidden;word-break:break-all;white-space:nowrap}.product-rewards-gift-goods-one-center-subname[data-v-0175af82]{font-size:%?26?%;font-weight:400;line-height:%?30.48?%;color:grey}.product-rewards-gift-goods-one-center-price[data-v-0175af82]{display:flex;justify-content:space-between;align-items:center}.product-rewards-gift-goods-one-center-price-left-symbol[data-v-0175af82]{font-size:%?24?%;font-weight:400;color:#383838}.product-rewards-gift-goods-one-center-price-left-text[data-v-0175af82]{font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:#383838;-webkit-text-decoration-line:line-through;text-decoration-line:line-through}.product-rewards-gift-goods-one-center-price-right[data-v-0175af82]{display:flex;justify-content:center;align-items:center;background-color:var(--custom-brand-color);border-radius:%?8?%;width:%?176?%;height:%?44?%}.product-rewards-gift-goods-one-center-price-right-one[data-v-0175af82]{font-size:%?20?%;font-weight:400;line-height:%?23.44?%;text-align:center;color:#fff}.product-rewards-gift-goods-one-center-price-right-two[data-v-0175af82]{font-size:%?28?%;font-weight:400;line-height:%?37.5?%;color:#fff;color:#fff;text-align:center;margin-left:%?8?%}.product-rewards-gift-goods-one-center-price-right-two-symbol[data-v-0175af82]{font-size:%?20?%}.product-rewards-gift-bottom[data-v-0175af82]{width:%?670?%;height:%?90?%;border-radius:%?100?%;background:#fafafa;font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#222;display:flex;justify-content:center;align-items:center;margin:0 auto;margin-top:%?22?%}.product-rewards-explain[data-v-0175af82]{width:%?710?%;background-color:#fff;margin:0 auto;border-radius:%?60?%;padding:%?30?% %?20?%;padding-top:%?56?%;box-sizing:border-box;margin-top:%?20?%}.product-rewards-explain-title[data-v-0175af82]{font-size:%?40?%;font-weight:700;line-height:%?48?%;color:#222}.product-rewards-explain-content[data-v-0175af82]{font-size:%?28?%;font-weight:400;line-height:%?48?%;color:#383838;margin-top:%?32?%;display:block}.sign-success-popup[data-v-0175af82] .uni-popup__wrapper-box{overflow-y:unset!important}.sign-wechat-popup[data-v-0175af82] .uni-popup__wrapper-box{overflow-y:unset!important}.sign-wechat-popup[data-v-0175af82] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:transparent}.sign-success[data-v-0175af82]{width:%?600?%;height:%?600?%;border-radius:%?40?%;background:#fff;position:relative;padding-top:%?224?%;box-sizing:border-box}.sign-success-img[data-v-0175af82]{width:%?260?%;height:%?260?%;position:absolute;left:50%;top:%?-74?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.sign-success-title[data-v-0175af82]{font-size:%?48?%;font-weight:400;line-height:%?57.6?%;color:var(--custom-brand-color);text-align:center}.sign-success-tip[data-v-0175af82]{font-size:%?26?%;font-weight:400;line-height:%?40?%;color:#a6a6a6;width:%?354?%;margin:0 auto;text-align:center;margin-top:%?38?%}.sign-success-op[data-v-0175af82]{width:%?480?%;height:%?90?%;line-height:%?90?%;background:var(--custom-brand-color);font-size:%?32?%;font-weight:400;color:#fff;text-align:center;display:block;border-radius:%?200?%;margin:0 auto;margin-top:%?48?%}.sign-award[data-v-0175af82]{width:%?600?%;height:%?600?%;border-radius:%?40?%;background:#fff;position:relative;padding-top:%?224?%;box-sizing:border-box}.sign-award-img[data-v-0175af82]{width:%?260?%;height:%?260?%;position:absolute;left:50%;top:%?-74?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.sign-award-title[data-v-0175af82]{font-size:%?48?%;font-weight:400;line-height:%?57.6?%;color:var(--custom-brand-color);text-align:center;width:%?336?%;margin:0 auto}.sign-award-tip[data-v-0175af82]{font-size:%?26?%;font-weight:400;line-height:%?40?%;color:#a6a6a6;width:%?354?%;margin:0 auto;text-align:center;margin-top:%?38?%}.sign-award-op[data-v-0175af82]{width:%?480?%;height:%?90?%;line-height:%?90?%;background:var(--custom-brand-color);font-size:%?32?%;font-weight:400;color:#fff;text-align:center;display:block;border-radius:%?200?%;margin:0 auto;margin-top:%?48?%}.sign-award-close[data-v-0175af82]{width:%?48?%;height:%?48?%;position:absolute;left:50%;bottom:%?-80?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.sign-wechat[data-v-0175af82]{width:%?600?%;border-radius:%?40?%}.sign-wechat-top[data-v-0175af82]{height:%?680?%;border-radius:%?40?% %?40?% 0 0;background:#fff;display:flex;flex-direction:column;align-items:center;padding-top:%?62?%;box-sizing:border-box;position:relative}.sign-wechat-center[data-v-0175af82]{width:100%;height:auto;display:block}.sign-wechat-bottom[data-v-0175af82]{width:100%;background:#fff;border-radius:0 0 %?40?% %?40?%;text-align:center;padding-bottom:%?24?%;box-sizing:border-box}.sign-wechat-bottom-info[data-v-0175af82]{display:inline-flex;align-items:center;height:%?70?%;border-radius:%?200?%;background:#f5f5f5;padding:0 %?24?%;box-sizing:border-box}.sign-wechat-bottom-info-code[data-v-0175af82]{font-size:%?32?%;font-weight:400;color:#000}.sign-wechat-bottom-info-copy[data-v-0175af82]{font-size:%?28?%;font-weight:400;color:var(--custom-brand-color);margin-left:%?32?%}.sign-wechat-head[data-v-0175af82]{width:%?160?%;height:%?160?%;border-radius:50%}.sign-wechat-name[data-v-0175af82]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:#383838;margin-top:%?26?%}.sign-wechat-tip[data-v-0175af82]{font-size:%?28?%;font-weight:400;line-height:%?32.82?%;color:#a6a6a6;margin-top:%?14?%}.sign-wechat-qrcode[data-v-0175af82]{width:%?320?%;height:%?320?%;margin-top:%?40?%}.sign-wechat-close[data-v-0175af82]{width:%?48?%;height:%?48?%;position:absolute;left:50%;bottom:%?-80?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.pop-ad[data-v-0175af82] .uni-popup__wrapper-box{background:transparent!important;max-width:100%!important;max-height:100%!important;border-radius:0!important}.pop-ad[data-v-0175af82] .uni-popup__wrapper.center{justify-content:flex-start;align-items:flex-start}.pop-ad-info[data-v-0175af82]{background:transparent;width:100vw;height:100vh}.pop-ad-info-video[data-v-0175af82]{width:100vw;height:100vh}.pop-ad-info-img[data-v-0175af82]{width:100vw;height:100vh}.pop-ad-info-close[data-v-0175af82]{width:%?88?%;height:%?88?%;display:block}.pop-ad-time[data-v-0175af82]{position:absolute;left:%?20?%;top:%?20?%;width:%?240?%;line-height:%?48?%;height:%?48?%;background-color:rgba(0,0,0,.5);border:1px solid hsla(0,0%,100%,.2);font-size:%?24?%;border-radius:%?98?%;color:#fff;display:flex;align-items:center;justify-content:center}.pop-ad-time-one[data-v-0175af82]{text-align:center}.pop-ad-unmute[data-v-0175af82]{position:absolute;left:%?300?%;top:%?20?%;width:%?160?%;line-height:%?48?%;height:%?48?%;background-color:rgba(0,0,0,.5);border:1px solid hsla(0,0%,100%,.2);font-size:%?24?%;border-radius:%?98?%;color:#fff;display:flex;align-items:center;justify-content:center}.pop-help[data-v-0175af82] .uni-popup{z-index:800}.pop-help[data-v-0175af82] .uni-popup__wrapper-box{overflow-y:unset!important;border-radius:%?40?%!important;background-color:initial}.pop-help-info[data-v-0175af82]{width:%?600?%;height:%?720?%}.pop-help-info-header[data-v-0175af82]{height:%?284?%;width:100%;position:relative;padding-top:%?50?%;box-sizing:border-box}.pop-help-info-header-bg[data-v-0175af82]{width:100%;height:%?284?%;position:absolute;left:0;top:0}.pop-help-info-header-user[data-v-0175af82]{display:flex;flex-direction:column;align-items:center;position:relative}.pop-help-info-header-user-avatar[data-v-0175af82]{width:%?126?%;height:%?126?%;border-radius:%?100?%;border:%?4?% solid #fff}.pop-help-info-header-user-name[data-v-0175af82]{font-size:%?32?%;font-weight:400;line-height:%?37.26?%;color:#fff;margin-top:%?18?%}.pop-help-info-tip[data-v-0175af82]{width:%?458?%;height:%?60?%;margin:0 auto;margin-top:%?80?%;display:block}.pop-help-info-desc[data-v-0175af82]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#b5b5b5;text-align:center;margin-top:%?26?%}.pop-help-info-op[data-v-0175af82]{width:%?516?%;height:%?90?%;border-radius:%?200?%;background:linear-gradient(196.15deg,var(--custom-brand-color),var(--custom-brand-color-80));font-size:%?32?%;font-weight:400;line-height:%?37.26?%;color:#fff;display:flex;justify-content:center;align-items:center;margin:0 auto;margin-top:%?102?%}.pop-help-info-op-disabled[data-v-0175af82]{opacity:.5;background:#666}.pop-help-info-close[data-v-0175af82]{width:%?48?%;height:%?48?%;position:absolute;left:50%;bottom:%?-80?%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}',""]),t.exports=e},"6f6c":function(t,e,i){var a=i("629c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f10a6734",a,!0,{sourceMap:!1,shadowMode:!1})},8047:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("e838"),i("5c47"),i("0506"),i("a1c1");var a=i("cc2c"),n={props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator:function(t){return t>=0}},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0}},data:function(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown:function(){return this.startVal>this.endVal}},watch:{startVal:function(){this.autoplay&&this.start()},endVal:function(){this.autoplay&&this.start()}},mounted:function(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{easingFn:function(t,e,i,a){return i*(1-Math.pow(2,-10*t/a))*1024/1023+e},start:function(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=(0,a.requestAnimationFrame)(this.count)},pauseResume:function(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause:function(){(0,a.cancelAnimationFrame)(this.rAF)},resume:function(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,(0,a.requestAnimationFrame)(this.count)},reset:function(){this.startTime=null,(0,a.cancelAnimationFrame)(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count:function(t){this.startTime||(this.startTime=t),this.timestamp=t;var e=t-this.startTime;this.remaining=this.localDuration-e,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(e,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(e,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(e/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(e/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),e<this.localDuration?this.rAF=(0,a.requestAnimationFrame)(this.count):this.$emit("callback")},isNumber:function(t){return!isNaN(parseFloat(t))},formatNumber:function(t){t=t.toFixed(this.decimals),t+="";var e=t.split("."),i=e[0],a=e.length>1?this.decimal+e[1]:"",n=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))while(n.test(i))i=i.replace(n,"$1"+this.separator+"$2");return this.prefix+i+a+this.suffix}},destroyed:function(){(0,a.cancelAnimationFrame)(this.rAF)}};e.default=n},"84c7":function(t,e,i){"use strict";i.r(e);var a=i("2efc"),n=i("dfc1");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"f3446868",null,!1,a["a"],void 0);e["default"]=o.exports},"8f89":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={countdownTimer:i("d3b8").default,uniIcons:i("de74").default,uniBadge:i("570c").default,uniPopup:i("5e99").default,diyFloatingRollingOrder:i("f9a5").default,ydAuthPopup:i("161f").default,nsLogin:i("4f5a").default,loadingCover:i("5510").default,diyShareNavigateH5:i("2f73").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"product-rewards",class:t.themeStyle,style:[t.themeColorVar]},[i("v-uni-view",{staticClass:"product-rewards-bg",style:{backgroundImage:"url("+t.$util.img(t.productRewards_bg)+")"}},[i("v-uni-view",{staticClass:"product-rewards-header"},[i("v-uni-view",{staticClass:"product-rewards-header-one"},[i("v-uni-view",{staticClass:"product-rewards-header-one-number"},[i("count-to",{attrs:{startVal:0,endVal:t.receive_nums}})],1),t._v("人")],1),i("v-uni-view",{staticClass:"product-rewards-header-two"},[t._v("已领取签到礼品")])],1),i("v-uni-view",{staticClass:"product-rewards-sign"},[i("v-uni-view",{staticClass:"product-rewards-sign-header"},[i("v-uni-view",{staticClass:"product-rewards-sign-header-left"},[i("v-uni-view",{staticClass:"product-rewards-sign-header-left-name"},[t._v("每日签到")]),t.list.length?i("v-uni-view",{staticClass:"product-rewards-sign-header-left-result"},[t._v("已连续签到："),i("v-uni-text",{staticClass:"product-rewards-sign-header-left-result-date"},[t._v(t._s(t.continuous_nums))]),t._v("天")],1):t._e()],1),i("v-uni-view",{staticClass:"product-rewards-sign-header-right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRecord.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"product-rewards-sign-header-right-text"},[t._v("签到记录"),i("v-uni-text",{staticClass:"iconfont icongengduo3"})],1)],1)],1),i("v-uni-scroll-view",{staticClass:"product-rewards-sign-scroll",class:{"product-rewards-sign-scroll-one":t.show_one_row},attrs:{"scroll-y":!0,"scroll-top":t.scroll_height,"scroll-with-animation":!0}},[i("v-uni-view",{staticClass:"product-rewards-sign-list",class:{"product-rewards-sign-list-not":t.list.length<4}},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-rewards-sign-list-one"},[i("v-uni-view",{staticClass:"product-rewards-sign-list-one-content",class:{"product-rewards-sign-list-one-miss":0==e.status,"product-rewards-sign-list-one-content-signed":3==e.status,"product-rewards-sign-list-one-content-today":e.is_now_date&&3!=e.status}},[i("v-uni-text",{staticClass:"product-rewards-sign-list-one-content-text",class:{"product-rewards-sign-list-one-content-text-today":e.is_now_date,"product-rewards-sign-list-one-content-text-signed":3==e.status}},[t._v(t._s(e.is_now_date?"今天":e.continuous_index>0?"第"+e.continuous_index+"天":" "))]),1==e.status?[i("v-uni-image",{staticClass:"product-rewards-sign-list-one-content-img",attrs:{src:t.$util.img("public/static/youpin/member/signin/product-rewards-mai.png")}})]:t._e(),2==e.status?[i("v-uni-image",{staticClass:"product-rewards-sign-list-one-content-img",attrs:{src:t.$util.img("public/static/youpin/member/signin/product-rewards-gift.png")}})]:t._e(),3==e.status?[i("v-uni-image",{staticClass:"product-rewards-sign-list-one-content-yes",attrs:{src:t.$util.img("public/static/youpin/member/signin/product-rewards-yes.png")}})]:t._e(),0==e.status?[i("v-uni-image",{staticClass:"product-rewards-sign-list-one-content-miss",attrs:{src:t.$util.img("public/static/youpin/member/signin/product-rewards-miss.png")}})]:t._e(),i("v-uni-view",{staticClass:"product-rewards-sign-list-one-content-day",class:{"product-rewards-sign-list-one-content-day-today":e.is_now_date,"product-rewards-sign-list-one-content-day-signed":3==e.status}},[t._v(t._s(3==e.status?"已签":e.is_now_date?"未签":e.date))])],2)],1)})),1),t.invite_list.length?i("v-uni-view",{staticClass:"product-rewards-sign-invite"},t._l(t.invite_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-rewards-sign-invite-one",class:{"product-rewards-sign-invite-one-has":e}},[i("v-uni-view",{staticClass:"product-rewards-sign-invite-one-head",class:{"product-rewards-sign-invite-one-head-has":e}},[i("v-uni-image",e?{staticClass:"product-rewards-sign-invite-one-head-inner",attrs:{src:e},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.avatarError(a)}}}:{staticClass:"product-rewards-sign-invite-one-head-avatar",attrs:{src:t.avatarBg}})],1),i("v-uni-text",{staticClass:"product-rewards-sign-invite-one-text",class:{"product-rewards-sign-invite-one-text-has":e}},[t._v(t._s(e?"已邀":"待邀"))])],1)})),1):t._e()],1),i("v-uni-view",{staticClass:"product-rewards-sign-op"},[i("v-uni-view",{staticClass:"product-rewards-sign-op-time"},[0==t.status?i("v-uni-text",{staticClass:"product-rewards-sign-op-time-name"},[t._v("活动未开始")]):10==t.status?[i("v-uni-image",{staticClass:"product-rewards-sign-op-time-clock",attrs:{src:t.$util.img("public/static/youpin/member/signin/clock.png")}}),i("v-uni-text",{staticClass:"product-rewards-sign-op-time-name"},[t._v("活动截止剩余：")]),i("countdown-timer",{ref:"countdown",attrs:{time:t.activity_time,autoStart:!0,"show-colon":!0},on:{finish:function(e){arguments[0]=e=t.$handleEvent(e),t.onFinish.apply(void 0,arguments)}}})]:i("v-uni-text",{staticClass:"product-rewards-sign-op-time-name"},[t._v("活动已结束")])],2),4==t.signInStatus?[i("v-uni-button",{staticClass:"product-rewards-sign-op-but",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("分享"),i("v-uni-text",{staticClass:"product-rewards-sign-op-but-col"},[t._v(t._s(t.residue_invite_nums))]),t._v("位"),i("v-uni-text",{staticClass:"product-rewards-sign-op-but-col"},[t._v(t._s("new"==t.invite_limit?"新好友":"好友"))]),t._v("签到，获取兑换码")],1)]:[i("v-uni-button",{staticClass:"product-rewards-sign-op-but",class:{"product-rewards-sign-op-but-yes":1==t.signInStatus,"product-rewards-sign-op-but-disabled":10!=t.status},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toSignIn.apply(void 0,arguments)}}},[3==t.signInStatus?i("uni-icons",{staticClass:"product-rewards-sign-op-but-icon",attrs:{type:"weixin",size:"20",color:"#fff"}}):t._e(),t._v(t._s(t.signInStatusText))],1)]],2),3==t.signInStatus?i("v-uni-view",{staticClass:"product-rewards-sign-tip"},[t._v("恭喜你完成签到任务，快找客服领券吧")]):4==t.signInStatus?i("v-uni-view",{staticClass:"product-rewards-sign-tip"},[t._v("你已获得奖励资格，完成助力即可挑选奖品")]):i("v-uni-view",{staticClass:"product-rewards-sign-tip"},[t._v("连续签到"),i("v-uni-text",{staticClass:"product-rewards-sign-tip-number"},[t._v(t._s(t.complete_sign_nums))]),t._v("天，可获得丰厚奖品")],1)],1),i("v-uni-view",{staticClass:"product-rewards-gift"},[i("v-uni-view",{staticClass:"product-rewards-gift-header",style:{backgroundImage:"url("+t.$util.img("public/static/youpin/member/signin/header-sign-award.png")+")"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRecord.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"product-rewards-gift-header-inner"},[i("v-uni-view",{staticClass:"product-rewards-gift-left"},[i("v-uni-text",{staticClass:"product-rewards-gift-left-text"},[t._v("我的礼品")])],1),i("v-uni-view",{staticClass:"product-rewards-gift-right"},[i("uni-badge",{staticClass:"product-rewards-gift-right-badge",attrs:{inverted:!0,text:t.complete_nums}}),i("uni-icons",{staticClass:"product-rewards-gift-right-more",attrs:{type:"forward",size:"18",color:"rgba(56, 56, 56, 1)"}})],1)],1)],1),i("v-uni-view",{staticClass:"product-rewards-gift-content"},[i("v-uni-scroll-view",{staticClass:"product-rewards-gift-tabs",attrs:{"scroll-x":!0,"scroll-with-animation":!0,"scroll-into-view":t.scroll_into_id}},t._l(t.rounds_use_goods,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-rewards-gift-tabs-one",class:{"product-rewards-gift-tabs-one-active":a==t.rounds_index},attrs:{id:"sign_product_rewards_gift_tabs_"+a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs(a)}}},[i("v-uni-view",{staticClass:"product-rewards-gift-tabs-one-top"},[i("v-uni-image",{staticClass:"product-rewards-gift-tabs-one-top-img",attrs:{src:t.$util.img(e.goods_image),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.rounds_use_goods,a)}}}),i("v-uni-view",{staticClass:"product-rewards-gift-tabs-one-top-text"},[t._v("共"+t._s(e.goods.length)+"件可选")])],1),i("v-uni-view",{staticClass:"product-rewards-gift-tabs-one-text",class:{"product-rewards-gift-tabs-one-text-active":a==t.rounds_index}},[e.is_current_round?i("v-uni-text",[t._v("本轮奖品")]):i("v-uni-text",[t._v("第"),i("v-uni-text",{staticClass:"product-rewards-gift-tabs-one-text-number",class:{"product-rewards-gift-tabs-one-text-number-active":a==t.rounds_index}},[t._v(t._s(e.round))]),t._v("轮奖品")],1)],1)],1)})),1),i("v-uni-scroll-view",{staticClass:"product-rewards-gift-goods",attrs:{"scroll-y":!0}},t._l(t.show_use_goods,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-rewards-gift-goods-one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-image",{staticClass:"product-rewards-gift-goods-one-img",attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.show_use_goods,a)}}}),i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center"},[i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center-subname"},[t._v(t._s(e.introduction))]),i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center-price"},[i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center-price-left"},[i("v-uni-text",{staticClass:"product-rewards-gift-goods-one-center-price-left-symbol"},[t._v("￥")]),i("v-uni-text",{staticClass:"product-rewards-gift-goods-one-center-price-left-text"},[t._v(t._s(e.market_price))])],1),i("v-uni-view",{staticClass:"product-rewards-gift-goods-one-center-price-right"},[i("v-uni-text",{staticClass:"product-rewards-gift-goods-one-center-price-right-one"},[t._v("体验价")]),i("v-uni-text",{staticClass:"product-rewards-gift-goods-one-center-price-right-two"},[i("v-uni-text",{staticClass:"product-rewards-gift-goods-one-center-price-right-two-symbol"},[t._v("￥")]),t._v(t._s(e.experiential_price))],1)],1)],1)],1)],1)})),1),t.show_more?i("v-uni-view",{staticClass:"product-rewards-gift-bottom",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShowMore.apply(void 0,arguments)}}},[t._v("查看更多可以选商品"),i("uni-icons",{attrs:{type:"forward",size:"28rpx",color:"rgba(34, 34, 34, 1)"}})],1):t._e()],1)],1),i("v-uni-view",{staticClass:"product-rewards-explain"},[i("v-uni-view",{staticClass:"product-rewards-explain-title"},[t._v("活动说明")]),i("v-uni-text",{staticClass:"product-rewards-explain-content"},[t._v(t._s(t.rule))])],1)],1),i("uni-popup",{ref:"signSuccessPopup",staticClass:"sign-success-popup",attrs:{"mask-click":!1}},[i("v-uni-view",{staticClass:"sign-success"},[i("v-uni-image",{staticClass:"sign-success-img",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-success.png")}}),i("v-uni-view",{staticClass:"sign-success-title"},[t._v(t._s(t.complete_id?t.is_help?"助力成功，万分感谢！":"很遗憾，助力不成功":"签到成功"))]),i("v-uni-view",{staticClass:"sign-success-tip"},[t._v("连续签到"+t._s(t.complete_sign_nums)+"天，可获得丰厚奖品明天一定要来哦")]),i("v-uni-text",{staticClass:"sign-success-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.signSuccessPopup.close()}}},[t._v("我知道了")])],1)],1),i("uni-popup",{ref:"signAwardPopup",staticClass:"sign-success-popup",attrs:{"mask-click":!1}},[i("v-uni-view",{staticClass:"sign-award"},[i("v-uni-image",{staticClass:"sign-award-img",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-award.png")}}),i("v-uni-view",{staticClass:"sign-award-title"},[t._v("恭喜你奖励已收入囊中")]),i("v-uni-view",{staticClass:"sign-award-tip"},[t._v(t._s(4==t.signInStatus?t.signInStatusText:"请添加客服微信免费领取"))]),4==t.signInStatus?[i("v-uni-button",{staticClass:"sign-award-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("去分享")])]:i("v-uni-text",{staticClass:"sign-award-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toContact.apply(void 0,arguments)}}},[t._v("去添加客服")]),i("v-uni-image",{staticClass:"sign-award-close",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-close.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.signAwardPopup.close()}}})],2)],1),i("uni-popup",{ref:"signWechatPopup",staticClass:"sign-wechat-popup",attrs:{"mask-click":!1}},[i("v-uni-view",{staticClass:"sign-wechat"},[i("v-uni-view",{staticClass:"sign-wechat-top"},[i("v-uni-image",{staticClass:"sign-wechat-head",attrs:{src:t.$util.img(t.customerService.headpic)}}),i("v-uni-view",{staticClass:"sign-wechat-name"},[t._v(t._s(t.customerService.name))]),i("v-uni-view",{staticClass:"sign-wechat-tip"},[t._v("您好，长按或扫描下方二维码加我哟")]),i("v-uni-image",{staticClass:"sign-wechat-qrcode",attrs:{src:t.$util.img(t.customerService.qrcode),"show-menu-by-longpress":!0}})],1),i("v-uni-image",{staticClass:"sign-wechat-center",attrs:{src:t.$util.img("public/static/youpin/member/signin/partition.png"),mode:"widthFix"}}),i("v-uni-view",{staticClass:"sign-wechat-bottom"},[i("v-uni-view",{staticClass:"sign-wechat-bottom-info"},[i("v-uni-text",{staticClass:"sign-wechat-bottom-info-code"},[t._v("兑换码："+t._s(t.complete_logs.length?t.complete_logs[0].code:""))]),i("v-uni-text",{staticClass:"sign-wechat-bottom-info-copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.complete_logs.length?t.complete_logs[0].code:"")}}},[t._v("复制")])],1)],1),i("v-uni-image",{staticClass:"sign-wechat-close",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-close.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.signWechatPopup.close()}}})],1)],1),i("v-uni-view",{staticClass:"PopWindow",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"signInPop",staticClass:"pop-ad",attrs:{type:"center",maskClick:!1}},[i("v-uni-view",{staticClass:"pop-ad-info"},[1==t.adType?[t.videoShow?i("v-uni-video",{staticClass:"pop-ad-info-video",attrs:{id:"adVideo",src:t.$util.img(t.advertise),objectFit:"contain",autoplay:!0,muted:t.muted,controls:!1,"initial-time":t.initial_time},on:{ended:function(e){arguments[0]=e=t.$handleEvent(e),t.videoEnd.apply(void 0,arguments)},loadedmetadata:function(e){arguments[0]=e=t.$handleEvent(e),t.loadedmetadata.apply(void 0,arguments)},play:function(e){arguments[0]=e=t.$handleEvent(e),t.videoPlay.apply(void 0,arguments)},timeupdate:function(e){arguments[0]=e=t.$handleEvent(e),t.timeUpdate.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPop(t.advertise_link)}}}):t._e()]:t._e(),t.fullScreenMode?i("v-uni-image",{directives:[{name:"else",rawName:"v-else"}],staticClass:"pop-ad-info-img",attrs:{src:t.$util.img(t.advertise),mode:t.fullScreenMode},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPop(t.advertise_link)}}}):t._e(),i("v-uni-view",{staticClass:"pop-ad-time",style:"top: "+t.navHeight+"px;"},[t.timing>0?i("v-uni-text",{staticClass:"pop-ad-time-one"},[t._v("浏览"+t._s(t.timing/1e3)+"秒完成签到")]):i("v-uni-text",{staticClass:"pop-ad-time-one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.videoEnd.apply(void 0,arguments)}}},[t._v("关闭")])],1),t.adType?i("v-uni-text",{staticClass:"pop-ad-unmute",style:"top: "+t.navHeight+"px;",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeMuted.apply(void 0,arguments)}}},[t._v(t._s(t.muted?"取消静音":"静音"))]):t._e()],2)],1)],1),i("uni-popup",{ref:"helpPop",staticClass:"pop-help",attrs:{type:"center",maskClick:!1}},[i("v-uni-view",{staticClass:"pop-help-info"},[i("v-uni-view",{staticClass:"pop-help-info-header"},[i("v-uni-image",{staticClass:"pop-help-info-header-bg",attrs:{src:t.$util.img("public/static/youpin/member/signin/help_header.png?v=2")}}),i("v-uni-view",{staticClass:"pop-help-info-header-user"},[i("v-uni-image",{staticClass:"pop-help-info-header-user-avatar",attrs:{src:t.$util.img(t.complete_headimg)}}),i("v-uni-text",{staticClass:"pop-help-info-header-user-name"},[t._v(t._s(t.complete_nickname))])],1)],1),i("v-uni-image",{staticClass:"pop-help-info-tip",attrs:{src:t.$util.img("public/static/youpin/member/signin/help_sigin.png")}}),i("v-uni-view",{staticClass:"pop-help-info-desc"},[t._v("老铁，需要你的一臂之力")]),i("v-uni-view",{staticClass:"pop-help-info-op",class:{"pop-help-info-op-disabled":1==t.signInStatus||3==t.signInStatus||4==t.signInStatus||10!=t.status},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toHelp.apply(void 0,arguments)}}},[t._v(t._s(1==t.signInStatus||3==t.signInStatus||4==t.signInStatus||10!=t.status?"无法助力":"助力签到"))]),i("v-uni-image",{staticClass:"pop-help-info-close",attrs:{src:t.$util.img("public/static/youpin/member/signin/sign-close.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.helpPop.close()}}})],1)],1),i("diy-floating-rolling-order",{attrs:{top:"h5"==t.$util.getPlatform()?"100rpx":"180rpx","position-type":"sign_detail"}}),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"}),i("diy-share-navigate-h5",{ref:"shareNavigateH5"})],1)},s=[]},"9a7f":function(t,e,i){var a=i("528d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("d3405a38",a,!0,{sourceMap:!1,shadowMode:!1})},a6e0:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"UniBadge",props:{type:{type:String,default:"default"},inverted:{type:Boolean,default:!1},text:{type:[String,Number],default:""},size:{type:String,default:"normal"}},data:function(){return{badgeStyle:""}},watch:{text:function(){this.setStyle()}},mounted:function(){this.setStyle()},methods:{setStyle:function(){this.badgeStyle="width: ".concat(8*String(this.text).length+12,"px")},onClick:function(){this.$emit("click")}}};e.default=a},ac31:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"custom flex-start-center"},[t.timeData.day>=1?i("v-uni-view",{staticClass:"day"},[t._v(t._s(t.timeData.day))]):t._e(),t.timeData.day>=1?i("v-uni-view",{staticClass:"day-symbol"},[t._v(t._s(t.showColon||t.showDaySymbol?"天":":"))]):t._e(),i("v-uni-view",{staticClass:"hour"},[t._v(t._s(t._f("fillWithZero")(t.timeData.hour)))]),i("v-uni-view",{staticClass:"hour-symbol"},[t._v(t._s(t.showColon?"时":":"))]),i("v-uni-view",{staticClass:"minute"},[t._v(t._s(t._f("fillWithZero")(t.timeData.minute)))]),i("v-uni-view",{staticClass:"minute-symbol"},[t._v(t._s(t.showColon?"分":":"))]),i("v-uni-view",{staticClass:"second"},[t._v(t._s(t._f("fillWithZero")(t.timeData.second)))]),i("v-uni-view",{staticClass:"second-symbol"},[t._v(t._s(t.showColon?"秒":""))])],1)],1)},n=[]},ae57:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("c9b5"),i("bf0f"),i("ab80");var a={props:{time:{type:Number,default:0},autoStart:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},showDaySymbol:{type:Boolean,default:!1}},data:function(){return{timer:null,timeData:{remain:0,day:0,hour:0,minute:0,second:0}}},watch:{time:function(){this.reset()}},filters:{fillWithZero:function(t){var e=t.toString().length;while(e<2)t="0"+t,e++;return t}},methods:{updateTimeData:function(){var t=this.timeData.remain;this.timeData.day=Math.floor(t/1e3/60/60/24),this.timeData.hour=Math.floor(t/1e3/60/60%24),this.timeData.minute=Math.floor(t/1e3/60%60),this.timeData.second=Math.floor(t/1e3%60)},reset:function(){var t=this;this.timer&&this.timer.stop(),this.timeData.remain=this.time,this.updateTimeData(),this.timer=new this.$util.AdjustingInterval((function(){t.timeData.remain-=1e3,t.timeData.remain<=0?(t.timeData.day="00",t.timeData.hour="00",t.timeData.minute="00",t.timeData.second="00"):t.updateTimeData()}),1e3,this.time/1e3,(function(){t.$emit("finish")})),this.autoStart&&this.timer.start()},start:function(){this.timer||this.timer.start()}},mounted:function(){this.reset()},beforeDestroy:function(){}};e.default=a},b373:function(t,e,i){"use strict";var a=i("9a7f"),n=i.n(a);n.a},b5ce:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),n={name:"diy-share-navigate-h5",data:function(){return{isShow:!1,isWeiXin:this.$util.isWeiXin(),isOnXianMaiApp:!1}},methods:{maskClose:function(t){0==t.target.offsetTop&&(this.isShow=!1)},open:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(this.isOnXianMaiApp){var i={title:t.desc?this.$util.shareTitleAddNickname(t.desc):"",desc:t.desc?t.desc:"",webpageUrl:t.webpageUrl?t.webpageUrl:"",thumbImage:t.imageUrl?this.$util.imageCdnResize(t.imageUrl,{image_process:"resize,w_300","x-oss-process":"image/resize,w_300"}):"",path:t.link};(0,a.shareMiniProgramSchemeGo)(i),e&&"function"==typeof e&&e()}else this.isShow=!0,e&&"function"==typeof e&&e()},close:function(){this.isShow=!1}},created:function(){this.isOnXianMaiApp=a.isOnXianMaiApp}};e.default=n},b9d2:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.text?i("v-uni-text",{staticClass:"uni-badge",class:t.inverted?"uni-badge--"+t.type+" uni-badge--"+t.size+" uni-badge--"+t.type+"-inverted":"uni-badge--"+t.type+" uni-badge--"+t.size,style:t.badgeStyle,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick()}}},[t._v(t._s(t.text))]):t._e()},n=[]},bf01:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.activeData.message?i("v-uni-view",{staticClass:"rolling-order",style:t.style,attrs:{animation:t.animationData}},[i("v-uni-image",{staticClass:"rolling-order-head",attrs:{src:t.activeData.headimg?t.$util.img(t.activeData.headimg):t.$util.getDefaultImage().default_headimg,mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.activeData.headimg=t.$util.getDefaultImage().default_headimg}}}),i("v-uni-text",{staticClass:"rolling-order-text"},[t._v(t._s(t.activeData.message))])],1):t._e()},n=[]},bf93:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),s=a(i("2fdc"));i("64aa"),i("c223");var r={name:"diy-floating-rolling-order",props:{top:{type:[String],default:function(){return"280rpx"}},left:{type:[String],default:function(){return"24rpx"}},zIndex:{type:Number,default:function(){return 888}},intervalsTime:{type:Number,default:function(){return 3e3}},positionType:{type:String,default:function(){return"index_banner"}},sleepStart:{type:Number,default:function(){return 4e3}}},data:function(){return{ordersList:[],activeIndex:0,activeData:{},animationData:{},animation:null,duration:1e3,timeOutOne:null,timeOutTwo:null}},computed:{style:function(){return"top: ".concat(this.top,";left: ").concat(this.left,";z-index:").concat(this.zIndex,";")}},created:function(){},mounted:function(){var t=this;this.sleepStart?setTimeout((function(){t.switchMessage()}),this.sleepStart):this.switchMessage()},beforeDestroy:function(){clearTimeout(this.timeOutOne),clearTimeout(this.timeOutTwo)},methods:{contentAnimation:function(){var t=this;this.activeData=this.ordersList[this.activeIndex],this.animation=uni.createAnimation({duration:this.duration,timingFunction:"linear"}),this.animation.opacity(1).step(),this.animationData=this.animation.export(),this.timeOutOne=setTimeout((0,s.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.animation.opacity(0).step(),t.animationData=t.animation.export(),t.activeIndex+=1,!(t.activeIndex>=t.ordersList.length)){e.next=6;break}return e.next=6,t.getData();case 6:t.timeOutTwo=setTimeout((function(){t.contentAnimation()}),t.duration+t.intervalsTime);case 7:case"end":return e.stop()}}),e)}))),this.duration+this.intervalsTime)},switchMessage:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getData();case 2:if(!(t.ordersList.length<1)){e.next=4;break}return e.abrupt("return");case 4:t.contentAnimation();case 5:case"end":return e.stop()}}),e)})))()},getData:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.activeIndex=0,e.prev=1,e.next=4,t.$api.sendRequest({url:"sign_detail"==t.positionType?t.$apiUrl.getSignDriftMessageUrl:t.$apiUrl.driftMessageUrl,async:!1,data:{position:t.positionType}});case 4:i=e.sent,0==i.code&&(t.ordersList=i.data),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,null,[[1,8]])})))()}}};e.default=r},cc2c:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.requestAnimationFrame=e.cancelAnimationFrame=void 0;var a,n,s=0;"webkit moz ms o".split(" ");e.requestAnimationFrame=a,e.cancelAnimationFrame=n,e.requestAnimationFrame=a=function(t){var e=(new Date).getTime(),i=Math.max(0,16-(e-s)),a=setTimeout((function(){t(e+i)}),i);return s=e+i,a},e.cancelAnimationFrame=n=function(t){clearTimeout(t)}},ce1d:function(t,e,i){"use strict";var a=i("6f6c"),n=i.n(a);n.a},d3b8:function(t,e,i){"use strict";i.r(e);var a=i("ac31"),n=i("31c3");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports},dfc1:function(t,e,i){"use strict";i.r(e);var a=i("8047"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},e52a:function(t,e,i){"use strict";i.r(e);var a=i("fa23"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},e611:function(t,e,i){var a=i("6e6c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("094219c9",a,!0,{sourceMap:!1,shadowMode:!1})},e71a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],staticClass:"yd-popup",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.maskClose.apply(void 0,arguments)}}},[t.isWeiXin?i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/weixin-share-tip.png")}}):i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/browser-share-tip.png")}})],1)},n=[]},eee2:function(t,e,i){"use strict";var a=i("e611"),n=i.n(a);n.a},f8de:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),n={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){a.isOnXianMaiApp&&this.appCurrentPages<=1?(0,a.goClosePage)("0"):uni.navigateBack()}}};e.default=n},f9a5:function(t,e,i){"use strict";i.r(e);var a=i("bf01"),n=i("4d82");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("b373");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"38dab624",null,!1,a["a"],void 0);e["default"]=o.exports},fa23:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("f7a5"),i("e966"),i("c223"),i("fd3c"),i("aa77"),i("bf0f"),i("e838");var n=a(i("2634")),s=a(i("2fdc")),r=a(i("5e99")),o=a(i("2d01")),d=a(i("de74")),c=a(i("d817")),u=a(i("f8de")),l=a(i("85bf")),p=a(i("d3b8")),g=a(i("570c")),f=a(i("f9a5")),h=i("4b89"),v=a(i("84c7")),m={components:{UniBadge:g.default,uniPopup:r.default,UniIcons:d.default,uniNavBar:c.default,countdownTimer:p.default,diyFloatingRollingOrder:f.default,countTo:v.default},mixins:[o.default,u.default],data:function(){return{sign_activity_id:null,status:10,productRewards_bg:"",share_img:"",share_title:"",rule:"",complete_nums:null,complete_sign_nums:null,continuous_nums:null,advertise:null,advertise_link:null,is_sign:!1,complete_id:null,list:[],rounds_use_goods:[],complete_logs:[],residue_invite_nums:0,invite_list:[],customerService:{name:"",qrcode:""},now_day_data:{},signInStatus:0,fullScreenMode:"",navHeight:0,timing:0,videoShow:!1,videoTime:0,videoOver:!1,timingObj:null,adType:0,video_real_time:0,initial_time:0,muted:!0,isClick:!1,activity_time:1e4,receive_nums:0,isOnXianMaiApp:h.isOnXianMaiApp,complete_headimg:"",complete_nickname:"",invite_limit:"none",is_help:!1,is_click_sign:!1,show_use_goods:[],rounds_index:0,page_show_goods:10,show_more:!1,avatarBg:"",scroll_height:0,show_one_row:!1,scroll_into_id:""}},onLoad:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function i(){var a,s,r;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return e.sign_activity_id=t.sign_activity_id||0,e.complete_id=t.complete_id||0,e.navHeight=20,i.next=5,l.default.wait_staticLogin_success();case 5:try{a=e.$util.colorToHex(e.$store.state.themeColorVar["--custom-brand-color"]).slice(1),e.avatarBg=encodeURI(e.$util.img("api/website/svgChangeFillColor?svg_name=avatar&color=".concat(a)))}catch(n){}return i.next=8,e.getData();case 8:return s=e.$util.deepClone(e.getSharePageParams()),r=window.location.origin+e.$router.options.base+s.link.slice(1),s.link=r,i.next=13,e.$util.publicShare(s);case 13:e.setWechatShare();case 14:case"end":return i.stop()}}),i)})))()},computed:{signInStatusText:function(){switch(this.signInStatus){case 0:return"签到";case 1:return"今天已签到";case 2:return"重新开始签到";case 3:return"加客服微信领取";case 4:return"分享".concat(this.residue_invite_nums,"位好友签到，获取兑换码");default:return""}}},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"签到"})},onUnload:function(){clearTimeout(this.timingObj)},methods:{goBack:function(){this.$util.goBack()},toHelp:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=!(1==t.signInStatus||3==t.signInStatus||4==t.signInStatus||10!=t.status),!i){e.next=6;break}return e.next=4,t.toSignIn();case 4:e.next=7;break;case 6:t.$util.showToast({title:"无法助力！"});case 7:case"end":return e.stop()}}),e)})))()},toSignIn:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(10==t.status){e.next=2;break}return e.abrupt("return");case 2:if(t.$buriedPoint.diyReportCustomPageInteractionEvent({diy_action_type:"click",diy_page_name:"签到",diy_page_title:"签到",diy_page_identifier:parseInt(t.sign_activity_id),diy_click_text:"签到",diy_click_link:"",diy_click_image:""}),!t.isClick){e.next=5;break}return e.abrupt("return");case 5:if(t.isClick=!0,uni.getStorageSync("token")){e.next=10;break}return t.$util.toShowLoginPopup(t,null,"/otherpages/member/signin/sign_in_product_rewards?sign_activity_id=".concat(t.sign_activity_id,"&complete_id=").concat(t.complete_id)),t.isClick=!1,e.abrupt("return");case 10:if(t.$refs.helpPop.close(),0!=t.signInStatus&&2!=t.signInStatus){e.next=24;break}return e.next=14,t.signInActive();case 14:if(i=e.sent,i){e.next=18;break}return t.isClick=!1,e.abrupt("return");case 18:return e.next=20,t.getPopData();case 20:return e.next=22,t.getData();case 22:e.next=25;break;case 24:3==t.signInStatus&&t.$refs.signWechatPopup.open();case 25:t.isClick=!1;case 26:case"end":return e.stop()}}),e)})))()},toContact:function(){this.$buriedPoint.diyReportCustomPageInteractionEvent({diy_action_type:"click",diy_page_name:"签到",diy_page_title:"签到",diy_page_identifier:parseInt(this.sign_activity_id),diy_click_text:"去添加客服",diy_click_link:"",diy_click_image:""}),this.signInStatus=3,this.$refs.signAwardPopup.close(),this.$refs.signWechatPopup.open()},toRecord:function(){this.$buriedPoint.diyReportCustomPageInteractionEvent({diy_action_type:"click",diy_page_name:"签到",diy_page_title:"签到",diy_page_identifier:parseInt(this.sign_activity_id),diy_click_text:"签到记录",diy_click_link:"/otherpages/member/signin/sign_in_product_rewards_records?sign_activity_id=".concat(this.sign_activity_id),diy_click_image:""}),this.$util.diyRedirectTo({wap_url:"/otherpages/member/signin/sign_in_product_rewards_records?sign_activity_id=".concat(this.sign_activity_id)})},getData:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={sign_activity_id:t.sign_activity_id},t.complete_id&&(i["complete_id"]=t.complete_id),e.prev=2,e.next=5,t.$api.sendRequest({url:t.$apiUrl.signinDetailUrl,async:!1,data:i});case 5:a=e.sent,0==a.code&&(t.status=a.data.status,t.activity_time=a.data.residue_time>0?a.data.residue_time:0,t.productRewards_bg=a.data.default_img,t.share_img=a.data.share_img,t.share_title=a.data.share_title,t.rule=a.data.rule,t.complete_sign_nums=a.data.complete_sign_nums,t.continuous_nums=a.data.continuous_nums,t.advertise=a.data.advertise,t.advertise_link=a.data.advertise_link,t.timing=1e3*a.data.look_time,t.adType="image"==a.data.advertise_type?0:1,t.customerService=a.data.complete_logs&&a.data.complete_logs.length?a.data.complete_logs[0].cs_info:{},t.complete_logs=a.data.complete_logs,t.residue_invite_nums=a.data.complete_logs&&a.data.complete_logs.length?a.data.complete_logs[0].residue_invite_nums:0,t.invite_list=a.data.invite_list,t.complete_nums=a.data.complete_nums,t.invite_limit=a.data.invite_limit,t.complete_headimg=a.data.complete_headimg,t.complete_nickname=a.data.complete_nickname?a.data.complete_nickname.length>4?a.data.complete_nickname.slice(0,3)+"...":a.data.complete_nickname:"",t.receive_nums=parseInt(a.data.receive_nums),t.rounds_use_goods=a.data.rounds_use_goods.map((function(e,i){return e.is_current_round&&(t.rounds_index=i),i==t.rounds_index&&(e.goods.length>t.page_show_goods?(t.show_more=!0,t.show_use_goods=e.goods.slice(0,t.page_show_goods)):(t.show_more=!1,t.show_use_goods=e.goods)),e.goods_image=e.goods.length>0?e.goods[0].goods_image:t.$util.getDefaultImage().default_goods_img,e})),s=0,t.list=a.data.show_date.map((function(t,e){return 1==t.sign_status?t.status=3:-1==t.sign_status?t.status=0:t.is_reward?t.status=2:t.status=1,t.is_now_date&&(t.date="今天",s=e),t})),t.now_day_data=a.data.show_date.find((function(t){return t.is_now_date})),1==t.now_day_data.sign_status?(t.is_sign=!0,t.now_day_data.is_reward?t.complete_logs.length&&0==t.complete_logs[0].complete_status?t.signInStatus=4:t.signInStatus=3:t.signInStatus=1):-1==t.now_day_data.sign_status?t.signInStatus=2:t.signInStatus=0,a.data.complete_logs&&a.data.complete_logs.length&&0==a.data.complete_logs[0].complete_status&&(t.signInStatus=4,0==t.now_day_data.sign_status&&(t.list=[])),t.list.length>0&&t.invite_list.length>0?t.show_one_row=!1:(t.list.length<=4||t.invite_list.length<=4)&&(t.show_one_row=!0),setTimeout((function(){if(s>3&&t.list.length>4){var e=uni.getSystemInfoSync();t.scroll_height=234*e.screenWidth/750*parseInt(t.list.length/4)}}),500),setTimeout((function(){t.scroll_into_id="sign_product_rewards_gift_tabs_"+t.rounds_index}),500)),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.complete_id&&!t.is_click_sign&&t.$refs.helpPop.open(),e.next=15;break;case 11:e.prev=11,e.t0=e["catch"](2),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.$util.showToast({title:e.t0.message});case 15:case"end":return e.stop()}}),e,null,[[2,11]])})))()},signInActive:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={sign_activity_id:t.sign_activity_id},t.complete_id&&(i.complete_id=t.complete_id),e.prev=2,e.next=5,t.$api.sendRequest({url:t.$apiUrl.signinUserUrl,async:!1,data:i});case 5:if(a=e.sent,t.is_click_sign=!0,-20002!=a.code){e.next=11;break}return e.abrupt("return",!1);case 11:if(0==a.code){e.next=16;break}return t.$util.showToast({title:a.message}),e.abrupt("return",!1);case 16:return t.is_help=a.data.is_help,e.abrupt("return",!0);case 18:e.next=23;break;case 20:return e.prev=20,e.t0=e["catch"](2),e.abrupt("return",!1);case 23:case"end":return e.stop()}}),e,null,[[2,20]])})))()},getPopData:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(1!=t.adType){e.next=6;break}t.videoShow=!0,t.$buriedPoint.diyReportAdEvent({diy_ad_location_type:"sign",diy_material_path:t.advertise,diy_ad_type:"video",diy_ad_id:parseInt(t.sign_activity_id),diy_action_type:"display"}),t.$refs.signInPop&&t.$refs.signInPop.open(),e.next=21;break;case 6:return e.prev=6,e.next=9,t.$util.getImageInfo(t.advertise);case 9:return i=e.sent,e.next=12,uni.getWindowInfo();case 12:a=e.sent,parseFloat((a.windowHeight/a.windowWidth).toFixed(2))>parseFloat((i.height/i.width).toFixed(2))?t.fullScreenMode="heightFix":t.fullScreenMode="widthFix",e.next=18;break;case 16:e.prev=16,e.t0=e["catch"](6);case 18:t.$buriedPoint.diyReportAdEvent({diy_ad_location_type:"sign",diy_material_path:t.advertise,diy_ad_type:"image",diy_ad_id:parseInt(t.sign_activity_id),diy_action_type:"display"}),t.$refs.signInPop&&t.$refs.signInPop.open(),t.adCountDown();case 21:case"end":return e.stop()}}),e,null,[[6,16]])})))()},loadedmetadata:function(t){this.videoTime=1e3*parseInt(t.detail.duration)},videoPlay:function(){this.adCountDown()},videoEnd:function(){this.videoOver=!0,this.timing=0,this.adCountDown()},changeMuted:function(){this.muted=!this.muted},timeUpdate:function(t){t.detail.duration;var e=parseInt(t.detail.currentTime);if(0==this.video_real_time)var i=parseInt(this.initial_time)+parseInt(this.video_real_time);else i=parseInt(this.video_real_time);if(e>i&&e-i>3){var a=wx.createVideoContext("adVideo");a.seek(this.video_real_time)}this.video_real_time=e},adCountDown:function(){var t=this;this.timing<0?1==this.adType?this.videoOver&&(this.$refs.signInPop&&this.$refs.signInPop.close(),this.now_day_data.is_reward?this.$refs.signAwardPopup.open():this.$refs.signSuccessPopup.open()):(this.$refs.signInPop&&this.$refs.signInPop.close(),this.now_day_data.is_reward?this.$refs.signAwardPopup.open():this.$refs.signSuccessPopup.open()):this.timingObj=setTimeout((function(){t.timing=t.timing-1e3,t.adCountDown()}),1e3)},toPop:function(t){this.$buriedPoint.diyReportAdEvent({diy_ad_location_type:"sign",diy_material_path:this.advertise,diy_ad_type:0==this.adType?"image":"video",diy_target_page:t,diy_ad_id:parseInt(this.sign_activity_id),diy_action_type:"click"}),t&&this.$util.diyRedirectTo({wap_url:t})},onFinish:function(){this.status=100,this.activity_time=0},getSharePageParams:function(){var t=this.complete_logs.length&&this.complete_logs[0].id?this.complete_logs[0].id:0;return this.$util.unifySharePageParams("/otherpages/member/signin/sign_in_product_rewards",this.share_title||"一起来签到","",{sign_activity_id:this.sign_activity_id,complete_id:t},this.share_img?this.$util.img(this.share_img):this.$util.img(this.productRewards_bg))},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},openSharePopup:function(t){var e=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(e)},imageError:function(t,e){t[e].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},changeTabs:function(t){this.rounds_index!=t&&(this.rounds_index=t,this.show_use_goods=this.rounds_use_goods[t].goods.length>this.page_show_goods?this.rounds_use_goods[t].goods.slice(0,this.page_show_goods):this.rounds_use_goods[t].goods,this.show_more=this.rounds_use_goods[t].goods.length>this.page_show_goods)},changeShowMore:function(){this.show_more=!1,this.show_use_goods=this.rounds_use_goods[this.rounds_index].goods},avatarError:function(t){this.invite_list[t]=this.$util.getDefaultImage().default_headimg,this.$forceUpdate()}},onShareAppMessage:function(t){var e=this.getSharePageParams(),i=e.title,a=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,n,i)},onShareTimeline:function(t){var e=this.getSharePageParams(),i=e.title,a=e.imageUrl,n=e.query;return{title:i,imageUrl:a,query:n,success:function(t){},fail:function(t){}}}};e.default=m},fb10:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-78bf10da]{width:100%;text-align:center}.uni-badge[data-v-78bf10da]{display:flex;box-sizing:border-box;overflow:hidden;justify-content:center;flex-direction:row;height:20px;line-height:20px;color:#333;border-radius:100px;background-color:#f1f1f1;background-color:initial;text-align:center;font-family:Helvetica Neue,Helvetica,sans-serif;font-size:12px;padding:0 6px}.uni-badge--inverted[data-v-78bf10da]{padding:0 5px 0 0;color:#f1f1f1}.uni-badge--default[data-v-78bf10da]{color:#333;background-color:#f1f1f1}.uni-badge--default-inverted[data-v-78bf10da]{color:#999;background-color:initial}.uni-badge--primary[data-v-78bf10da]{color:#fff;background-color:#007aff}.uni-badge--primary-inverted[data-v-78bf10da]{color:#007aff;background-color:initial}.uni-badge--success[data-v-78bf10da]{color:#fff;background-color:#4cd964}.uni-badge--success-inverted[data-v-78bf10da]{color:#4cd964;background-color:initial}.uni-badge--warning[data-v-78bf10da]{color:#fff;background-color:#f0ad4e}.uni-badge--warning-inverted[data-v-78bf10da]{color:#f0ad4e;background-color:initial}.uni-badge--error[data-v-78bf10da]{color:#fff;background-color:#dd524d}.uni-badge--error-inverted[data-v-78bf10da]{color:#dd524d;background-color:initial}.uni-badge--small[data-v-78bf10da]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},fc31:function(t,e,i){"use strict";var a=i("3ecf"),n=i.n(a);n.a},ff6f:function(t,e,i){"use strict";i.r(e);var a=i("8f89"),n=i("e52a");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("ce1d"),i("eee2");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"0175af82",null,!1,a["a"],void 0);e["default"]=o.exports}}]);