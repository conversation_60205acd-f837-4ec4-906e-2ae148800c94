(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-live-list-liveList"],{"0584":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-00f31f96]{width:100%;text-align:center}[data-v-00f31f96] .subscribe--live-player-subscribe__btn{border:1px solid #f33;border-radius:%?30?%}.live-list-tag[data-v-00f31f96]{width:100%;height:%?88?%;position:fixed;top:0;left:0;z-index:2;display:flex;align-items:center;justify-content:space-around;font-size:%?36?%;font-family:PingFang SC;font-weight:500;line-height:%?50?%;color:#222;background:#fff;opacity:1}.live-list-tag .tag-item[data-v-00f31f96]{width:100%;display:flex;flex-direction:column;justify-content:center;align-items:center}.live-list-tag .tag-bottom-line[data-v-00f31f96]{width:%?40?%;height:%?6?%;background:#f33;opacity:0;border-radius:2px}.live-list-tag .tag-bottom-line-active[data-v-00f31f96]{opacity:1}.live-content[data-v-00f31f96]{width:100%;height:500px}.live-list-wonderful[data-v-00f31f96]{flex:1;padding:0 %?24?%;background:#f5f5f5}.live-list-wonderful .live-list[data-v-00f31f96]{display:flex;background-color:#fff;padding:%?24?%;margin-bottom:%?24?%;border-radius:%?20?%}.live-list-wonderful .live-list[data-v-00f31f96]:last-of-type{margin-bottom:unset}.live-list-wonderful .live-list .left[data-v-00f31f96]{position:relative;width:%?372?%;height:%?372?%}.live-list-wonderful .live-list .left .live-img[data-v-00f31f96]{width:100%;height:100%;border-radius:%?20?%}.live-list-wonderful .live-list .left .info[data-v-00f31f96]{position:absolute;top:%?12?%;left:%?12?%;height:%?36?%;display:flex;align-items:center;background-color:rgba(0,0,0,.5);border-radius:%?18?%}.live-list-wonderful .live-list .left .info > uni-view[data-v-00f31f96]:first-child{background:linear-gradient(180deg,#ec624d,#f33);border-radius:18px;font-size:%?20?%;color:#fff;padding:0 %?8?%;display:flex;align-items:center;box-sizing:border-box;padding:%?2?% %?10?%}.live-list-wonderful .live-list .left .info > uni-view:first-child img[data-v-00f31f96]{width:%?20?%;height:%?20?%;margin-right:%?4?%}.live-list-wonderful .live-list .right[data-v-00f31f96]{display:flex;flex-direction:column;flex:1;padding-left:%?24?%}.live-list-wonderful .live-list .right .title[data-v-00f31f96]{line-height:%?44?%;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;min-height:%?88?%}.live-list-wonderful .live-list .right .wonderful-info[data-v-00f31f96]{display:flex;margin-top:%?40?%;align-items:center}.live-list-wonderful .live-list .right .wonderful-info img[data-v-00f31f96]{width:%?44?%;height:%?44?%;border-radius:50%;margin-right:%?4?%}.live-list-wonderful .live-list .right .wonderful-info uni-view[data-v-00f31f96]{font-size:%?24?%;color:#666;line-height:%?30?%}.live-list-wonderful .live-list .right .goods[data-v-00f31f96]{display:flex;margin-top:%?14?%;width:%?258?%;overflow-x:scroll}.live-list-wonderful .live-list .right .goods img[data-v-00f31f96]{flex-shrink:0;width:%?120?%;height:%?120?%;border-radius:%?10?%;margin-right:%?20?%}.live-list-wonderful .live-list .right .goods img[data-v-00f31f96]:last-child{margin-right:%?20?%}.live-list-wonderful .live-list .right .btn[data-v-00f31f96]{display:flex;align-items:flex-end;flex-direction:row-reverse;flex:1}.live-list-wonderful .live-list .right .btn uni-view[data-v-00f31f96]{display:flex;justify-content:center;align-items:center;width:%?126?%;height:%?48?%;background-color:#f33;border-radius:%?48?%;color:#fff}.live-list-wonderful .live-list .right .btn uni-view img[data-v-00f31f96]{width:%?32?%;height:%?32?%;margin-right:%?4?%}.live-list-notice[data-v-00f31f96]{width:100%;display:flex;flex-wrap:wrap;justify-content:space-between;box-sizing:border-box;padding:0 %?24?%;padding-top:%?10?%}.live-list-notice .live-item-notice[data-v-00f31f96]{width:%?342?%;background:#fff;border-radius:%?20?%;margin-top:%?20?%}.live-list-notice .live-item-notice .notice-box[data-v-00f31f96]{width:%?342?%;height:%?342?%;position:relative}.live-list-notice .live-item-notice .notice-status[data-v-00f31f96]{background-color:rgba(0,0,0,.5);border-radius:9px;position:absolute;top:%?20?%;left:%?16?%;z-index:99;display:flex}.live-list-notice .live-item-notice .notice-status .status[data-v-00f31f96]{background:linear-gradient(180deg,#ec624d,#f33);border-radius:%?18?%;font-size:%?20?%;font-family:PingFang SC;font-weight:400;color:#fff;display:flex;align-items:center;box-sizing:border-box;padding:%?2?% %?10?%;justify-content:space-around}.live-list-notice .live-item-notice .notice-status .status .status-img[data-v-00f31f96]{width:%?24?%;height:%?24?%;display:block;margin-right:%?4?%}.live-list-notice .live-item-notice .notice-status .time[data-v-00f31f96]{font-size:%?22?%;font-family:PingFang SC;font-weight:400;line-height:%?38?%;color:#fff;margin:0 %?8?%}.live-list-notice .live-item-notice .notice-cover[data-v-00f31f96]{width:%?342?%;height:%?342?%;display:block;border-radius:%?20?%}.live-list-notice .live-item-notice .notice-info[data-v-00f31f96]{width:100%;box-sizing:border-box;padding:0 %?16?%;padding-bottom:%?20?%}.live-list-notice .live-item-notice .notice-info .notice-title[data-v-00f31f96]{width:100%;max-height:%?74?%;font-size:%?26?%;font-family:PingFang SC;font-weight:600;line-height:%?38?%;color:#333;margin-top:%?8?%}.live-list-notice .live-item-notice .notice-info .anchor-info[data-v-00f31f96]{width:100%;margin-top:%?20?%;display:flex;align-items:center;justify-content:space-between}.live-list-notice .live-item-notice .notice-info .anchor-info .head-portrait[data-v-00f31f96]{width:%?44?%;height:%?44?%;display:block;margin-right:%?8?%;border-radius:50%;border:1px solid #000}.live-list-notice .live-item-notice .notice-info .anchor-info .name-info[data-v-00f31f96]{font-size:%?24?%;font-family:PingFang SC;font-weight:400;line-height:%?38?%;color:#666}.live-list-notice .live-item-notice .notice-info .anchor-info .name-info .name[data-v-00f31f96]{margin-left:%?8?%}.live-list-notice .live-item-notice .notice-info .subscribe[data-v-00f31f96]{width:100%;display:flex;justify-content:flex-end}.botton-btn[data-v-00f31f96]{width:100%;height:%?120?%;background:#fff;position:fixed;bottom:0;left:0;z-index:2;display:flex;align-items:center;justify-content:center}.botton-btn .btn[data-v-00f31f96]{width:%?642?%;height:%?84?%;background:#f33;border-radius:%?44?%;text-align:center;line-height:%?84?%;font-size:%?30?%;font-family:PingFang SC;font-weight:400;color:#fff;border:unset}',""]),t.exports=e},"0878":function(t,e,i){"use strict";i.r(e);var a=i("cf6d"),n=i("8a60");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("3b11");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"00f31f96",null,!1,a["a"],void 0);e["default"]=r.exports},1063:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"[data-v-f23bcaae] .uni-popup__wrapper.bottom{background:none!important}[data-v-f23bcaae] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0}",""]),t.exports=e},"18f7e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7aa735bd]{width:100%;text-align:center}.share-popup .share-title[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-title[data-v-7aa735bd]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center}.share-popup .share-content[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content[data-v-7aa735bd]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;margin-bottom:%?22?%}.share-popup .share-content .share-box[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-7aa735bd]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-7aa735bd]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-7aa735bd]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-7aa735bd]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-7aa735bd]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd]{color:#07c160}.share-popup .share-footer[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer[data-v-7aa735bd]{width:%?672?%;height:%?80?%;line-height:%?80?%;text-align:center;color:#fff;border-radius:%?40?%;background:var(--custom-brand-color);margin:0 auto}.share-popup .share-footer-padding[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer-padding[data-v-7aa735bd]{margin-bottom:%?40?%}.canvas[data-v-7aa735bd]{width:%?620?%;height:%?917?%;margin:0 auto;margin-top:%?70?%;display:block;overflow:hidden}.poster[data-v-7aa735bd]{display:flex;justify-content:center}.canvas1[data-v-7aa735bd]{top:100vh}@-webkit-keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-7aa735bd]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-7aa735bd]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-7aa735bd]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-7aa735bd]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-7aa735bd]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .6s linear normal infinite;animation:spin-data-v-7aa735bd .6s linear normal infinite}.loading-anim .in[data-v-7aa735bd]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .8s linear infinite;animation:spin-data-v-7aa735bd .8s linear infinite}.loading-anim .mid[data-v-7aa735bd]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-7aa735bd .6s linear infinite;animation:spin-data-v-7aa735bd .6s linear infinite}',""]),t.exports=e},"22ba":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-f23bcaae]{width:100%;text-align:center}.share-popup .share-title[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-title[data-v-f23bcaae]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center;background:#f5f5f5}.share-popup .share-content[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content[data-v-f23bcaae]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;background:#f5f5f5}.share-popup .share-content .share-box[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-f23bcaae]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-f23bcaae]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-f23bcaae]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-f23bcaae]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-f23bcaae]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-f23bcaae],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-f23bcaae]{color:#07c160}.share-popup .share-footer[data-v-f23bcaae],\r\n.uni-popup__wrapper-box .share-footer[data-v-f23bcaae]{height:%?88?%;line-height:%?88?%;border-top:%?2?% #f5f5f5 solid;text-align:center;color:#666}.canvas[data-v-f23bcaae]{width:%?620?%;height:%?917?%;margin:0 auto;margin-top:%?70?%;display:block;overflow:hidden}.poster[data-v-f23bcaae]{display:flex;justify-content:center}.canvas1[data-v-f23bcaae]{top:100vh}@-webkit-keyframes spin-data-v-f23bcaae{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-f23bcaae{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-f23bcaae]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-f23bcaae]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-f23bcaae]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-f23bcaae]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-f23bcaae]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-f23bcaae .6s linear normal infinite;animation:spin-data-v-f23bcaae .6s linear normal infinite}.loading-anim .in[data-v-f23bcaae]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-f23bcaae .8s linear infinite;animation:spin-data-v-f23bcaae .8s linear infinite}.loading-anim .mid[data-v-f23bcaae]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-f23bcaae .6s linear infinite;animation:spin-data-v-f23bcaae .6s linear infinite}',""]),t.exports=e},"2e65":function(t,e,i){"use strict";i.r(e);var a=i("bb0b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"3b11":function(t,e,i){"use strict";var a=i("94d5"),n=i.n(a);n.a},"4d48":function(t,e,i){"use strict";var a=i("7f85"),n=i.n(a);n.a},"4d52":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223"),i("f7a5");var n=a(i("2634")),o=a(i("2fdc")),s=a(i("b874")),r=a(i("82c2")),l=a(i("014b")),h=i("d64b"),d=a(i("85bf")),c={components:{sharePage:s.default,sharePopup:r.default,diyShare:l.default},data:function(){return{isIndex:!1,showEmpty:!1,siteId:0,tagActive:0,canvasOptions:{width:"634",height:"832",borderRadius:"20rpx"},sharePopupOptions:[],shareImgPath:[],isShowCanvas:!1,roomInfo:[],liveStatus:{101:"进行中",102:"未开始",103:"回看",104:"禁播",105:"暂停",106:"异常",107:"已过期"},liveStatusBgc:{101:"ongoing",102:"begin",103:"back",104:"bear",105:"stop",106:"alarm",107:""},curLiveInfo:"",subscribeInfo:{roomid:"",customParams:{}},sharetype:2}},onLoad:function(t){t.site_id&&(this.siteId=t.site_id)},onShow:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$langConfig.refresh(),d.default.wait_staticLogin_success();case 2:case"end":return e.stop()}}),e)})))()},onShareAppMessage:function(t){if("menu"==t.from||"button"==t.from&&2===this.sharetype){var e=this.getSharePageParams(),i=e.title,a=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,n,i)}if("button"==t.from&&1===this.sharetype){var o=this.getSharePageParams(),s=this.curLiveInfo,r=s.name,l=this.$util.img(s.share_img),h="/pages/live-player-plugin?"+o.query+"&room_id="+s.roomid;return this.sharetype=2,this.$buriedPoint.pageShare(h,l,r)}},watch:{tagActive:function(){this.roomInfo=[],this.$refs.mescroll&&this.$refs.mescroll.refresh()}},methods:{onSubscribe:function(t){console.log("房间号：",t.detail.room_id),console.log("订阅用户openid",t.detail.openid),console.log("是否订阅",t.detail.is_subscribe)},subscribe:function(t){console.log("roomid",t);var e=requirePlugin("live-player-plugin"),i=t;e.getSubscribeStatus({room_id:i}).then((function(t){console.log("房间号：",t.room_id),console.log("订阅用户openid",t.openid),console.log("是否订阅",t.is_subscribe)})).catch((function(t){console.log("get subscribe status",t)}))},tagChangeFunc:function(t){this.tagActive=t},getListData:function(t){var e=this;this.showEmpty=!1;var i={page:t.num,page_size:t.size,type:this.tagActive};this.siteId&&(i.site_id=this.siteId),this.$api.sendRequest({url:"/broadcast/api/live/roomList",data:i,success:function(i){e.showEmpty=!0;var a=[],n=i.message;0==i.code&&i.data?(a=i.data.list,t.endSuccess(a.length),1==t.num&&(e.roomInfo=[]),e.roomInfo=e.roomInfo.concat(a),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.showToast({title:n})},fail:function(i){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},entryRoom:function(t){},imageError:function(t){this.roomInfo[t].feeds_img=this.$util.getDefaultImage().default_goods_img},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/live/list/liveList","直播间福利多多，只等你来","",{},this.$util.img("public/static/youpin/Live_fan.jpg"))},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},openSharePopup:function(t){this.sharetype=1;var e=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(e)},newCommQrcode:function(t){var e=this;this.curLiveInfo=t;var i={room_id:t.roomid,shop_id:uni.getStorageSync("shop_id")};uni.getStorageSync("member_id")&&(i.recommend_member_id=uni.getStorageSync("member_id"));var a=(0,h.query_to_scene)(i),n=uni.getStorageSync("userInfo");""==uni.getStorageSync("token")&&(n=""),this.isShowCanvas=!1,this.$api.sendRequest({url:"/api/Website/newCommQrcode",data:{path:"pages/live-player-plugin",scene:a},success:function(i){if(0==i.code)if(""==n){var a={};e.$api.sendRequest({url:"/api/member/info",success:function(n){a=0==n.code&&""!=uni.getStorageSync("token")?{headimg:n.data.headimg,nickname:n.data.nickname}:{headimg:"https://youpin-dev.jiufuwangluo.com:8443//upload/default/default_img/head.png",nickname:"请登录"},e.drawCanvas(i.data.qrcodeUrl,t,a),setTimeout((function(){e.$refs.sharePopup&&e.$refs.sharePopup.open()}),0)}})}else e.drawCanvas(i.data.qrcodeUrl,t,n),setTimeout((function(){e.$refs.sharePopup&&e.$refs.sharePopup.open()}),0);else e.$util.showToast({title:i.message})}})},drawCanvas:function(t,e,i){this.sharePopupOptions=[{background:"#fff",x:0,y:0,width:634,height:832,type:"image"},{path:this.$util.img(e.share_img),x:0,y:0,width:634,height:507,type:"image"},{path:i.headimg,radius:36,x:40,y:567,width:56,height:56,type:"image"},{text:i.nickname,size:28,color:"#333",fontWeight:"bold",x:130,y:582,type:"text"},{text:e.name,size:26,color:"#999",x:130,y:630,width:310,lineNum:2,lineHeight:34,type:"text"},{path:t,x:466,y:536,width:128,height:128,type:"image"},{background:"#F8F8F8",x:0,y:692,width:634,height:140,type:"image"},{path:this.$util.img("public/static/youpin/qrcodetips.png"),x:40,y:710,width:554,height:106,type:"image"}],this.isShowCanvas=!0},roomFun:function(t){103==t.live_status?this.$util.redirectTo("/otherpages/live/end/end?room_id=".concat(t.roomid)):this.playerLive(t.roomid)},playerLive:function(t){var e=this.$util.livePlayerPageUrl(t,!1);this.$util.redirectTo(e)}}};e.default=c},5005:function(t,e,i){var a=i("22ba");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f12a655a",a,!0,{sourceMap:!1,shadowMode:!1})},6497:function(t,e,i){"use strict";i.r(e);var a=i("b3aed"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"7d2f8":function(t,e,i){var a=i("18f7e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("00c0b360",a,!0,{sourceMap:!1,shadowMode:!1})},"7f85":function(t,e,i){var a=i("1063");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("96184e44",a,!0,{sourceMap:!1,shadowMode:!1})},"82c2":function(t,e,i){"use strict";i.r(e);var a=i("c01b"),n=i("2e65");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("a24d");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"7aa735bd",null,!1,a["a"],void 0);e["default"]=r.exports},"8a60":function(t,e,i){"use strict";i.r(e);var a=i("4d52"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"91bf":function(t,e,i){"use strict";var a=i("5005"),n=i.n(a);n.a},"94d5":function(t,e,i){var a=i("0584");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("a5e747ca",a,!0,{sourceMap:!1,shadowMode:!1})},a24d:function(t,e,i){"use strict";var a=i("7d2f8"),n=i.n(a);n.a},a53b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("5e99").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"sharePopup",staticClass:"share-popup",attrs:{type:"bottom"}},[i("template",{attrs:{slot:"container"},slot:"container"},[t.imagePath?t._e():i("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width+"px",height:t.canvasOptions.height+"px",borderRadius:t.canvasOptions.borderRadius},attrs:{"canvas-id":"myCanvas"}}),i("v-uni-view",{staticClass:"poster"},[t.imagePath?i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:!t.isShowLoading,expression:"!isShowLoading"}],staticClass:"canvas",style:{width:t.canvasOptions.width+"rpx",height:t.canvasOptions.height+"rpx",borderRadius:t.canvasOptions.borderRadius},attrs:{src:t.imagePath,mode:""}}):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowLoading,expression:"isShowLoading"}],staticClass:"loading-layer"},[i("v-uni-view",{staticClass:"loading-anim"},[i("v-uni-view",{staticClass:"box item"},[i("v-uni-view",{staticClass:"border out item ns-border-color-top ns-border-color-left"})],1)],1)],1)],1),i("v-uni-view",[i("v-uni-view",{staticClass:"share-title"},[t._v("分享到")]),i("v-uni-view",{staticClass:"share-content"},[i("v-uni-view",{staticClass:"share-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveImage.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/goods/save-image.png"),mode:""}}),i("v-uni-text",[t._v("保存图片")])],1)],1)],1),i("v-uni-view",{staticClass:"share-footer",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSharePopup.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("取消分享")])],1)],1)],2)],1)],1)},o=[]},b3aed:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0}}},data:function(){return{ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,height:0,windowHeight:0,windowWidth:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.ctx=uni.createCanvasContext("myCanvas",this)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var i=0;i<this.drawPathQueue.length;i++)for(var a=0;a<this.drawPathQueue.length;a++){var n=this.drawPathQueue[a];if(n.index===i){if("text"===n.type){if(this.ctx.setFillStyle(n.color||"#000"),this.ctx.setFontSize(n.size*this.myPx),n.textBaseline){var o=n.textBaselineColor||"#999999";this.ctx.strokeStyle=o,this.ctx.moveTo(n.x,n.y-5),this.ctx.lineTo(this.ctx.measureText(n.text).width+20,n.y-5),this.ctx.stroke(),this.ctx.textBaseline=n.textBaseline}if(n.width&&n.text){for(var s=n.text.split(""),r="",l=[],h=n.width,d=n.lineNum||1,c=n.lineHeight||20,u=0;u<s.length;u++)this.ctx.measureText(r).width<h&&this.ctx.measureText(r+s[u]).width<=h?(r+=s[u],u==s.length-1&&l.push(r)):(l.push(r),r=s[u]);if(d<=l.length)for(var f=0;f<d;f++)n.fontWeight?(this.ctx.fillText(l[f],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(l[f],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(l[f],n.x*this.myPx,n.y*this.myPx),n.y=n.y+c;else for(var p=0;p<l.length;p++)n.fontWeight?(this.ctx.fillText(l[p],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(l[p],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(l[p],n.x*this.myPx,n.y*this.myPx),n.y=n.y+c}else n.fontWeight?(this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(n.text,n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx);this.counter--}"image"===n.type&&(n.path?this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx):(this.ctx.fillStyle=n.background,this.ctx.fillRect(n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx)),this.counter--)}}},counter:function(t,e){var i=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){i.imagePath=t.savedFilePath,i.isShowLoading=!1}})},fail:function(t){console.log("err",t)}},i)}),100))}},methods:{open:function(){this.$refs.sharePopup.open(),this.imagePath||(this.isShowLoading=!0,this.generateImg())},closeSharePopup:function(){this.$refs.sharePopup.close()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var i=t.drawQueue[e];if(i.index=e,"text"===i.type)return t.drawPathQueue.push(i),"continue";i.path?uni.getImageInfo({src:i.path,success:function(e){i.path=e.path,t.drawPathQueue.push(i)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(i)},i=0;i<this.drawQueue.length;i++)e(i)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})}}};e.default=a},b874:function(t,e,i){"use strict";i.r(e);var a=i("a53b"),n=i("6497");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("4d48"),i("91bf");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"f23bcaae",null,!1,a["a"],void 0);e["default"]=r.exports},bb0b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0}},shareType:{type:String}},data:function(){return{isIPhoneX:!1,ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,height:0,windowHeight:0,windowWidth:0,bottomPadding:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.getSafeArea(),this.ctx=uni.createCanvasContext("myCanvas",this)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var i=0;i<this.drawPathQueue.length;i++)for(var a=0;a<this.drawPathQueue.length;a++){var n=this.drawPathQueue[a];if(n.index===i){if("text"===n.type){if(this.ctx.setFillStyle(n.color||"#000"),this.ctx.setFontSize(n.size*this.myPx),n.textBaseline){var o=n.textBaselineColor||"#999999";this.ctx.strokeStyle=o,this.ctx.moveTo(n.x,n.y-5),this.ctx.lineTo(this.ctx.measureText(n.text).width+20,n.y-5),this.ctx.stroke(),this.ctx.textBaseline=n.textBaseline}if(n.width&&n.text){for(var s=n.text.split(""),r="",l=[],h=n.width,d=n.lineNum||1,c=n.lineHeight||20,u=0;u<s.length;u++)this.ctx.measureText(r).width<h&&this.ctx.measureText(r+s[u]).width<=h?(r+=s[u],u==s.length-1&&l.push(r)):(l.push(r),r=s[u]);if(d<=l.length)for(var f=0;f<d;f++)n.fontWeight?(this.ctx.fillText(l[f],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(l[f],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(l[f],n.x*this.myPx,n.y*this.myPx),n.y=n.y+c;else for(var p=0;p<l.length;p++)n.fontWeight?(this.ctx.fillText(l[p],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(l[p],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(l[p],n.x*this.myPx,n.y*this.myPx),n.y=n.y+c}else n.fontWeight?(this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(n.text,n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx);this.counter--}if("image"===n.type){if(n.path)if(n.radius){var v=2*n.radius,g=n.x+n.radius,m=n.y+n.radius;this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(g,m,n.radius,0,2*Math.PI),this.ctx.clip(),this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,v*this.myPx,v*this.myPx),this.ctx.restore()}else this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx);else this.ctx.fillStyle=n.background,this.ctx.fillRect(n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx);this.counter--}}}},counter:function(t,e){var i=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){i.imagePath=t.savedFilePath,i.isShowLoading=!1}})},fail:function(t){console.log("err",t)}},i)}),100))}},methods:{open:function(){this.isIPhoneX=this.$util.isIPhoneX(),this.$refs.sharePopup.open(),this.imagePath||(this.isShowLoading=!0,this.generateImg())},closeSharePopup:function(){this.$refs.sharePopup.close()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var i=t.drawQueue[e];if(i.index=e,"text"===i.type)return t.drawPathQueue.push(i),"continue";i.path?uni.getImageInfo({src:i.path,success:function(e){i.path=e.path,t.drawPathQueue.push(i)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(i)},i=0;i<this.drawQueue.length;i++)e(i)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})},getSafeArea:function(){var t=uni.getSystemInfoSync();this.bottomPadding=t.screenHeight-t.safeArea.bottom}}};e.default=a},c01b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("5e99").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"sharePopup",staticClass:"share-popup",attrs:{type:"bottom","bottom-radius":!0}},[i("template",{attrs:{slot:"container"},slot:"container"},[t.imagePath?t._e():i("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width+"px",height:t.canvasOptions.height+"px",borderRadius:t.canvasOptions.borderRadius},attrs:{"canvas-id":"myCanvas"}}),i("v-uni-view",{staticClass:"poster",style:{"margin-top":t.isIPhoneX?"80rpx":""}},[t.imagePath?i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:!t.isShowLoading,expression:"!isShowLoading"}],staticClass:"canvas",style:{width:t.canvasOptions.width+"rpx",height:t.canvasOptions.height+"rpx",borderRadius:t.canvasOptions.borderRadius},attrs:{src:t.imagePath,mode:""}}):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowLoading,expression:"isShowLoading"}],staticClass:"loading-layer"},[i("v-uni-view",{staticClass:"loading-anim"},[i("v-uni-view",{staticClass:"box item"},[i("v-uni-view",{staticClass:"border out item ns-border-color-top ns-border-color-left"})],1)],1)],1)],1),i("v-uni-view",[i("v-uni-view",{staticClass:"share-title"},[t._v("分享到")]),i("v-uni-view",{staticClass:"share-content"},[i("v-uni-view",{staticClass:"share-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveImage.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/goods/save-image.png"),mode:""}}),i("v-uni-text",[t._v("保存图片")])],1)],1)],1),i("v-uni-view",{staticClass:"share-footer",class:{"share-footer-padding":t.bottomPadding<1},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSharePopup.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("取消分享")])],1)],1)],2)],1)],1)},o=[]},cf6d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={nsEmpty:i("dc6c").default,sharePopup:i("82c2").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"live-view"},[i("v-uni-view",{staticClass:"live-list-tag"},[i("v-uni-view",{staticClass:"tag-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tagChangeFunc(0)}}},[i("v-uni-view",[t._v("精彩直播")]),i("v-uni-view",{staticClass:"tag-bottom-line",class:0===t.tagActive?"tag-bottom-line-active":""})],1),i("v-uni-view",{staticClass:"tag-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tagChangeFunc(1)}}},[i("v-uni-view",[t._v("直播预告")]),i("v-uni-view",{staticClass:"tag-bottom-line",class:1===t.tagActive?"tag-bottom-line-active":""})],1)],1),i("v-uni-view",{staticClass:"live-content"},[i("ns-empty",{attrs:{text:"直播仅支持在微信小程序内查看",isIndex:t.isIndex,entrance:"live"}})],1),t.isShowCanvas?i("share-popup",{ref:"sharePopup",attrs:{canvasOptions:t.canvasOptions,sharePopupOptions:t.sharePopupOptions}}):t._e(),t.roomInfo.length?i("v-uni-view",{staticClass:"botton-btn"},[i("v-uni-button",{staticClass:"btn",attrs:{plain:!0,"open-type":"share"}},[i("v-uni-text",[t._v("转发直播间")])],1)],1):t._e()],1)},o=[]}}]);