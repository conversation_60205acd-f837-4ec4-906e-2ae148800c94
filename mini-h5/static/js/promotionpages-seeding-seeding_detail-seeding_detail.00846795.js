(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-seeding-seeding_detail-seeding_detail"],{"0a79":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uniIcons:i("de74").default,uniPopup:i("5e99").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"bottom-nav",class:{"bottom-nav-dark":"dark"==t.theme}},[t.dataInfo.goods&&t.dataInfo.goods.length?i("v-uni-view",{staticClass:"bottom-nav-left",class:{"bottom-nav-left-dark":"dark"==t.theme},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.goodsListPopup.open()}}},[i("v-uni-image",{staticClass:"bottom-nav-left-icon",attrs:{src:t.$util.img("public/static/youpin/goods-add.png")}}),t._v("商品("+t._s(t.dataInfo.goods&&t.dataInfo.goods.length||0)+")")],1):i("v-uni-view"),i("v-uni-view",{staticClass:"bottom-nav-right"},[i("v-uni-view",{staticClass:"bottom-nav-right-one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeLike.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:t.dataInfo.is_like?"heart-filled":"heart",size:"20",color:"dark"==t.theme?t.dataInfo.is_like?"var(--custom-brand-color)":"#fff":t.dataInfo.is_like?"var(--custom-brand-color)":""}}),i("v-uni-text",{staticClass:"bottom-nav-right-one-num",class:{"bottom-nav-right-one-num-dark":"dark"==t.theme}},[t._v(t._s(t.dataInfo.like_num||"点赞"))])],1),i("v-uni-button",{staticClass:"bottom-nav-right-one",class:{"bottom-nav-right-one-dark":"dark"==t.theme},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"redo",size:"20",color:"dark"==t.theme?"#fff":""}}),i("v-uni-text",{staticClass:"bottom-nav-right-one-num",class:{"bottom-nav-right-one-num-dark":"dark"==t.theme}},[t._v("分享")])],1)],1)],1),i("uni-popup",{ref:"goodsListPopup",staticClass:"goods-list-pop-father",class:{"goods-list-pop-father-dark":"dark"==t.theme},attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"goods-list-pop",class:{"goods-list-pop-dark":"dark"==t.theme}},[i("v-uni-view",{staticClass:"goods-list-pop-header",class:{"goods-list-pop-header-dark":"dark"==t.theme}},[i("v-uni-text",{staticClass:"goods-list-pop-header-left",class:{"goods-list-pop-header-left-dark":"dark"==t.theme}},[t._v("商品推荐")]),i("v-uni-text",{staticClass:"iconfont icondelete goods-list-pop-header-right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.goodsListPopup.close()}}})],1),i("v-uni-view",{staticClass:"goods-list-pop-list"},t._l(t.dataInfo.goods,(function(e,n){return i("v-uni-view",{staticClass:"goods-list-pop-list-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"goods-list-pop-list-item-left"},[i("v-uni-image",{staticClass:"goods-list-pop-list-item-img",attrs:{src:e.goods_image,alt:""},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.errorFun(t.dataInfo.goods,n)}}}),i("v-uni-view",{staticClass:"goods-list-pop-list-item-info"},[i("v-uni-view",{staticClass:"goods-list-pop-list-item-info-text overtext-hidden-one"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"goods-list-pop-list-item-info-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1)],1)],1),i("v-uni-text",{staticClass:"goods-list-pop-list-item-buy"},[t._v("购买")])],1)})),1)],1)],1)],1)},o=[]},1372:function(t,e,i){var n=i("61f8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("719152fc",n,!0,{sourceMap:!1,shadowMode:!1})},"15d5":function(t,e,i){var n=i("7b934");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("e5409c1c",n,!0,{sourceMap:!1,shadowMode:!1})},1925:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"videoPlayer"},[i("v-uni-video",{staticClass:"video",attrs:{id:"myVideo_"+t.video.id,controls:!1,src:t.video.share_resource,loop:!0,autoplay:t.autoplay,poster:t.video.image},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}})],1)},a=[]},1926:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"listright"},[i("v-uni-view",{staticClass:"item"},[i("v-uni-image",{staticClass:"item-img",attrs:{src:t.showData.is_like?t.$util.img("public/static/youpin/video-like-red.png"):t.$util.img("public/static/youpin/video-like.png"),alt:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeLike.apply(void 0,arguments)}}}),i("v-uni-text",{staticClass:"item-text"},[t._v(t._s(t.showData.like_num?t.showData.like_num:"点赞"))])],1),i("v-uni-view",{staticClass:"item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toComment.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"item-img",attrs:{src:t.$util.img("public/static/youpin/video-comment.png"),alt:""}}),i("v-uni-text",{staticClass:"item-text"},[t._v(t._s(t.showData.comment_num?t.showData.comment_num:"评论"))])],1),i("v-uni-view",{staticClass:"item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toShare.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"item-img",attrs:{src:t.$util.img("public/static/youpin/video-share.png"),alt:""}}),i("v-uni-text",{staticClass:"item-text"},[t._v("分享")])],1)],1)},a=[]},"1b2a":function(t,e,i){var n=i("dbf6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("3732c34c",n,!0,{sourceMap:!1,shadowMode:!1})},"235d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"comment"},[t.comment_counts?i("v-uni-view",{staticClass:"comment-count"},[t._v("共"+t._s(t.comment_counts)+"条评论")]):t._e(),i("v-uni-view",{staticClass:"comment-list"},[t._l(t.list,(function(e,n){return i("v-uni-view",{key:e.experience_comment_id,staticClass:"comment-list-item clearfix"},[i("v-uni-view",{staticClass:"comment-list-item-one"},[i("v-uni-image",{staticClass:"comment-list-item-one-head",attrs:{src:e.headimg},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toSeedingHome(e.member_id)}}}),i("v-uni-view",{staticClass:"comment-list-item-one-info"},[i("v-uni-view",{staticClass:"comment-list-item-one-info-name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toSeedingHome(e.member_id)}}},[t._v(t._s(e.nickname)),e.is_author?i("v-uni-text",{staticClass:"comment-list-item-one-info-name-inner"},[t._v("作者")]):t._e()],1),i("v-uni-view",{staticClass:"comment-list-item-one-info-content"},[t._v(t._s(e.comment_content))]),i("v-uni-view",{staticClass:"comment-list-item-one-info-date"},[t._v(t._s(e.time)),i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toReply(n,null)}}},[t._v("回复")])],1)],1)],1),e.reply.length>0?[e.isShow?t._l(e.reply,(function(e,a){return i("v-uni-view",{key:e.experience_comment_id,staticClass:"comment-list-item-two"},[i("v-uni-image",{staticClass:"comment-list-item-two-head",attrs:{src:e.member_headimg},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toSeedingHome(e.member_id)}}}),i("v-uni-view",{staticClass:"comment-list-item-two-info"},[i("v-uni-view",{staticClass:"comment-list-item-two-info-name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toSeedingHome(e.member_id)}}},[t._v(t._s(e.member_nickname)),e.is_author?i("v-uni-text",{staticClass:"comment-list-item-two-info-name-inner"},[t._v("作者")]):t._e()],1),e.reply_nickname?i("v-uni-view",{staticClass:"comment-list-item-two-info-content"},[t._v("回复"),i("v-uni-text",[t._v(t._s(e.reply_nickname)+":")]),t._v(t._s(e.comment_content))],1):i("v-uni-view",{staticClass:"comment-list-item-two-info-content"},[t._v(t._s(e.comment_content))]),i("v-uni-view",{staticClass:"comment-list-item-two-info-date"},[t._v(t._s(e.time)),i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toReply(n,a)}}},[t._v("回复")])],1)],1)],1)})):t._e(),e.isShow?i("v-uni-view",{staticClass:"comment-list-item-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.unfold(n,!1)}}},[t._v("收起"),i("v-uni-text",{staticClass:"iconfont iconiconangledown"})],1):i("v-uni-view",{staticClass:"comment-list-item-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.unfold(n,!0)}}},[t._v("展开"+t._s(e.reply.length)+"条回复"),i("v-uni-text",{staticClass:"iconfont iconiconangledown"})],1)]:t._e(),i("v-uni-view",{staticClass:"comment-list-item-separate"})],2)})),t.page<=t.page_count?i("v-uni-view",{staticClass:"comment-list-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[t._v(t._s(t.loadMore?"加载中...":"加载更多"))]):t._e()],2),i("v-uni-view",{staticClass:"comment-input",style:t.commentInput.isFixed?"position: fixed;left: 0;bottom: "+t.commentInput.bottom+";":""},[i("v-uni-view",{staticClass:"comment-input-text",style:t.like.is_show?"":"width:100%;",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toComment.apply(void 0,arguments)}}},[t._v("留下你的精彩评论吧")]),t.like.is_show?i("v-uni-view",{staticClass:"comment-input-like",style:t.like.is_like?"color:#FF3333;":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeLike.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"comment-input-like-img",attrs:{src:t.like.is_like?t.$util.img("public/static/youpin/two-like-yes.png"):t.$util.img("public/static/youpin/two-like-no.png")}}),i("v-uni-text",[t._v(t._s(t.like.like_num))])],1):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowPopup,expression:"isShowPopup"}],staticClass:"comment-popup",on:{click:function(e){if(e.target!==e.currentTarget)return null;arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"comment-popup-inner",style:{bottom:t.keyHeight}},[t.isShowPopup?i("v-uni-input",{ref:"commonetRef",staticClass:"comment-popup-inner-input",attrs:{type:"text",placeholder:"留下你的精彩评论吧","placeholder-class":"comment-popup-inner-input-placeholder",focus:t.focus,"cursor-spacing":t.cursorSpacing},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.inputFocus.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.inputBlur.apply(void 0,arguments)}},model:{value:t.commentContent,callback:function(e){t.commentContent=e},expression:"commentContent"}}):t._e(),i("v-uni-image",{staticClass:"comment-popup-inner-post",attrs:{src:t.$util.img("public/static/youpin/post.png")},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.postComment.apply(void 0,arguments)}}})],1)],1)],1)},a=[]},"29b2":function(t,e,i){"use strict";var n=i("fe57"),a=i.n(n);a.a},"2d01":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},31490:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-image",{staticClass:"mescroll-totop",class:[t.value?"mescroll-totop-in":"mescroll-totop-out"],attrs:{src:t.$util.img("public/static/youpin/to-top.png"),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toTopClick.apply(void 0,arguments)}}})},a=[]},3455:function(t,e,i){"use strict";i.r(e);var n=i("1925"),a=i("3833");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("f97a");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"2da196b0",null,!1,n["a"],void 0);e["default"]=r.exports},3525:function(t,e,i){"use strict";i.r(e);var n=i("a6ff"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"37cd":function(t,e,i){"use strict";i.r(e);var n=i("31490"),a=i("a157");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("e0b7");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"0d800a55",null,!1,n["a"],void 0);e["default"]=r.exports},3833:function(t,e,i){"use strict";i.r(e);var n=i("f0ae"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"3b04":function(t,e,i){"use strict";i.r(e);var n=i("8465"),a=i("3525");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("29b2");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"589ee183",null,!1,n["a"],void 0);e["default"]=r.exports},"3b27":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{showTop:!1,scrollTop:0,oldLocation:0}},methods:{scrollToTopNative:function(){uni.pageScrollTo({duration:200,scrollTop:0})}},onReachBottom:function(){},onPageScroll:function(t){this.oldLocation=t.scrollTop,t.scrollTop>200?!this.showTop&&(this.showTop=!0):this.showTop&&(this.showTop=!1),t.scrollTop>100?!this.isShowDetailTab&&(this.isShowDetailTab=!0):this.isShowDetailTab&&(this.isShowDetailTab=!1)}};e.default=n},"41aa":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5c9fa887]{width:100%;text-align:center}uni-page-body[data-v-5c9fa887]{background-color:#fff}body.?%PAGE?%[data-v-5c9fa887]{background-color:#fff}',""]),t.exports=e},"43e5":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("2634")),o=n(i("2fdc"));i("64aa"),i("c223"),i("0c26"),i("3efd");var s=n(i("7c8d")),r={name:"commentPreview",props:{commentInput:{type:Object,default:function(){return{isFixed:!0,bottom:0}}},articleId:{type:[String,Number],required:!0},like:{type:Object,default:function(){return{is_show:!0,is_like:!1,like_num:0}}}},watch:{comment_counts:function(t){this.$emit("updateCommentCounts",t)}},data:function(){return{inputWrapHeight:"0px",isShowPopup:!1,focus:!1,reply_comment_id:null,reply_index:[],commentContent:"",systemInfo:{},page_size:10,page:1,page_count:1,list:[],comment_counts:0,loadMore:!1,keyHeight:0,cursorSpacing:0}},created:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=uni.getSystemInfoSync(),n=i.screenWidth,t.cursorSpacing=18/(n/375),e.next=4,t.getData();case 4:case"end":return e.stop()}}),e)})))()},methods:{closePopup:function(){this.isShowPopup=!1,this.focus=!1},changeLike:function(){this.$emit("changeLike",!this.like.is_like)},toComment:function(){this.reply_comment_id=null,this.reply_index=[],this.isShowPopup=!0,this.focus=!0},toReply:function(t,e){var i="",n="";null!=e?(i=this.list[t].reply[e].member_nickname,n=this.list[t].reply[e].experience_comment_id):(i=this.list[t].nickname,n=this.list[t].experience_comment_id),this.reply_index=[t,e],this.placeholder="回复 @".concat(i,": "),this.reply_comment_id=n,this.isShowPopup=!0,this.focus=!0},inputFocus:function(t){this.isShowPopup=!0,this.focus=!0,t.detail&&t.detail.height&&(this.keyHeight=t.detail.height)},inputBlur:function(){this.inputWrapHeight=0,this.focus=!1,this.keyHeight=0},getData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(t.page>t.page_count)){e.next=2;break}return e.abrupt("return");case 2:return t.loadMore=!0,e.next=5,t.$api.sendRequest({url:s.default.commentListUrl,async:!1,data:{page:t.page,user_share_experience_id:t.articleId}});case 5:i=e.sent,0==i.code&&(t.comment_counts=i.data.comment_counts,i.data&&Array.isArray(i.data.data)&&(t.page_count=i.data.last_page,t.list=t.list.concat(i.data.data),t.page+=1)),t.loadMore=!1;case 8:case"end":return e.stop()}}),e)})))()},publishComment:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(""!=t.commentContent.trim()){e.next=3;break}return t.$util.showToast({title:"请输入评论！",mask:!0}),e.abrupt("return");case 3:return e.next=5,t.$api.sendRequest({url:s.default.publishCommentUrl,async:!1,data:{user_share_experience_id:t.articleId,content:t.commentContent}});case 5:i=e.sent,0==i.code?(t.$util.showToast({title:"发表评论成功",mask:!0}),t.commentContent="",t.list.unshift(i.data[0]),t.comment_counts+=1):t.$util.showToast({title:i.message,mask:!0});case 7:case"end":return e.stop()}}),e)})))()},commentReply:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function i(){var n,o;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(""!=e.commentContent.trim()){i.next=3;break}return e.$util.showToast({title:"请输入评论！",mask:!0}),i.abrupt("return");case 3:return i.next=5,e.$api.sendRequest({url:s.default.commentReplyUrl,async:!1,data:{experience_comment_id:t,content:e.commentContent}});case 5:n=i.sent,0==n.code?(e.$util.showToast({title:"回复评论成功",mask:!0}),e.commentContent="",e.reply_index.length>1&&(o=e.list[e.reply_index[0]],o.reply=o.reply.concat(n.data),e.$set(e.list,e.reply_index[0],o),e.comment_counts+=1)):e.$util.showToast({title:n.message,mask:!0});case 7:case"end":return i.stop()}}),i)})))()},postComment:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isShowPopup=!1,t.focus=!1,uni.getStorageSync("token")){e.next=5;break}return t.$emit("toLogin"),e.abrupt("return");case 5:if(null!=t.reply_comment_id){e.next=10;break}return e.next=8,t.publishComment();case 8:e.next=12;break;case 10:return e.next=12,t.commentReply(t.reply_comment_id);case 12:case"end":return e.stop()}}),e)})))()},unfold:function(t,e){var i=this.list[t];i.isShow=e,this.$set(this.list,t,i)},toSeedingHome:function(t){this.$util.redirectTo("/promotionpages/seeding/seeding_home_page/seeding_home_page",{other_mid:t})}}};e.default=r},47344:function(t,e,i){"use strict";i.r(e);var n=i("5b4e"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"4a6b":function(t,e,i){"use strict";var n=i("56b2"),a=i.n(n);a.a},"4f21":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a=n(i("3455")),o=n(i("3b04")),s=n(i("d631")),r=n(i("cba4")),c=n(i("de74")),l={props:["list"],components:{UniIcons:c.default,videoPlayer:a.default,listLeft:o.default,listRight:s.default,commentPreview:r.default},data:function(){return{videos:[],pageStatrY:0,pageEndY:0,page:0,current_index:0,page_count:3,showCommnetPopup:!1}},watch:{list:function(){}},created:function(){this.getThreeElements(this.list,this.page)},methods:{getThreeElements:function(t,e){for(var i=t.length,n=[],a=0;a<this.page_count;a++){var o=(e+a)%i;n.push(t[o])}this.videos=n},closeComment:function(){this.showCommnetPopup=!1},changeClick:function(){this.$refs.right[0].change()},changeplay:function(t){clearTimeout(null),this.current_index=t.detail.current;try{for(var e=0;e<this.$refs.player.length;e++)this.$refs.player[e].pause()}catch(i){console.log(i)}this.pageStatrY<this.pageEndY?(this.page<=0?this.page=this.list.length-1:this.page=this.page-1,this.current_index==this.page_count-1&&this.getThreeElements(this.list,this.page),console.log("向上滑动",this.page),this.pageStatrY=0,this.pageEndY=0,this.$emit("previousVideo",this.page)):(this.page>=this.list.length-1?this.page=0:this.page=this.page+1,0==this.current_index&&this.getThreeElements(this.list,this.page),console.log("向下滑动",this.page),this.pageStatrY=0,this.pageEndY=0,this.$emit("nextVideo",this.page))},touchStart:function(t){this.pageStatrY=t.changedTouches[0].pageY},touchEnd:function(t){this.pageEndY=t.changedTouches[0].pageY},toComment:function(){this.showCommnetPopup=!0},changeLike:function(){this.$emit("changeLike")},toShare:function(){this.$emit("toShare")},toLogin:function(){this.$emit("toLogin")},update_comment_counts:function(t){var e=this.videos[this.page];e.comment_num=t}}};e.default=l},"56b2":function(t,e,i){var n=i("9cee");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("67512630",n,!0,{sourceMap:!1,shadowMode:!1})},"5b4e":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("de74")),o=n(i("5e99")),s={name:"bottom-nav",props:{dataInfo:{type:Object,default:function(){return{}}},theme:{type:String,default:"white"}},data:function(){return{}},components:{UniPopup:o.default,UniIcons:a.default},created:function(){console.log("dataInfo",this.dataInfo)},methods:{openSharePopup:function(){this.$emit("toShare")},changeLike:function(t){this.$emit("changeLike")},errorFun:function(t,e){t[e]&&(t[e].goods_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()}}};e.default=s},"5c80":function(t,e,i){"use strict";var n=i("c66e"),a=i.n(n);a.a},"61f8":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-2da196b0]{width:100%;text-align:center}.videoPlayer[data-v-2da196b0]{height:100%;width:100%;background-color:#000}.video[data-v-2da196b0]{height:calc(100% - env(safe-area-inset-bottom));width:100%}',""]),t.exports=e},"714b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{showData:{type:Object}},data:function(){return{show:!0}},methods:{toComment:function(){this.$emit("toComment")},changeLike:function(){this.$emit("changeLike")},toShare:function(){this.$emit("toShare")}}};e.default=n},7390:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5c9fa887]{width:100%;text-align:center}\r\n/* 标题栏 */.custom[data-v-5c9fa887]{background:#fff;display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;z-index:1}.custom .iconfont[data-v-5c9fa887]{font-size:%?40?%;color:#333;font-weight:700;position:absolute;left:%?20?%}.custom .custom-navbar[data-v-5c9fa887]{display:flex;width:%?360?%;align-items:center}.custom .custom-navbar .navbar-item[data-v-5c9fa887]{height:%?60?%;line-height:%?60?%;width:100%;text-align:center;color:#333;font-size:%?30?%}.video-preview[data-v-5c9fa887]{box-sizing:border-box}.image-preview[data-v-5c9fa887]{box-sizing:border-box;padding-bottom:calc(%?126?% + env(safe-area-inset-bottom))}.image-preview-header[data-v-5c9fa887]{position:relative}.image-preview-header .swiper[data-v-5c9fa887]{background:hsla(0,0%,86.3%,.39)}.image-preview-header .swiper-item[data-v-5c9fa887]{display:flex;justify-content:center;align-items:center}.image-preview-header .swiper-image[data-v-5c9fa887]{width:100%;height:100%}.image-preview-header-index[data-v-5c9fa887]{width:%?88?%;height:%?40?%;border-radius:%?40?%;background:rgba(0,0,0,.5);font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:#fff;display:flex;justify-content:center;align-items:center;box-sizing:border-box;position:absolute;right:%?14?%;bottom:%?14?%}.image-preview-article[data-v-5c9fa887]{padding:%?16?% %?24?% %?20?% %?24?%;box-sizing:border-box;position:relative}.image-preview-article-share[data-v-5c9fa887]{margin:0;padding:0;width:%?120?%;height:%?48?%;background:linear-gradient(90deg,var(--custom-brand-color-80),var(--custom-brand-color) 100%,#f33 0);border-radius:%?24?% 0 0 %?24?%;display:flex;align-items:center;justify-content:center;position:absolute;top:%?16?%;right:0}.image-preview-article-share-img[data-v-5c9fa887]{width:%?32?%;height:%?30?%;margin-right:%?8?%}.image-preview-article-share-text[data-v-5c9fa887]{font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#fff;margin-top:%?4?%}.image-preview-article-title[data-v-5c9fa887]{font-size:%?32?%;font-weight:600;line-height:%?38?%;color:#333;margin-top:%?32?%}.image-preview-article-info[data-v-5c9fa887]{display:flex;align-items:center}.image-preview-article-info-left[data-v-5c9fa887]{display:flex;align-items:center}.image-preview-article-info-image[data-v-5c9fa887]{width:%?64?%;height:%?64?%;background:hsla(0,0%,86.3%,.39);border-radius:50%;border:%?2?% solid #fff;margin-right:%?8?%}.image-preview-article-info-name[data-v-5c9fa887]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:#383838;margin-right:%?20?%}.image-preview-article-info-date[data-v-5c9fa887]{font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#999}.image-preview-article-desc[data-v-5c9fa887]{font-size:%?30?%;font-weight:400;line-height:%?40?%;color:#333;margin-top:%?12?%}.image-preview-product[data-v-5c9fa887]{box-sizing:border-box;margin-bottom:%?40?%;width:100%;padding:0 %?20?%}.image-preview-product-header[data-v-5c9fa887]{font-size:%?32?%;font-weight:700;line-height:%?46.34?%;color:#383838;display:flex;align-items:center}.image-preview-product-header-icon[data-v-5c9fa887]{width:%?32?%;height:%?32?%;margin-right:%?14?%}.image-preview-product-item[data-v-5c9fa887]{width:100%;display:flex;align-items:center;height:%?144?%;border-radius:%?20?%;background:#f7f7f7;position:relative;margin-top:%?20?%}.image-preview-product-item-img[data-v-5c9fa887]{width:%?120?%;height:%?120?%;border-radius:%?20?%;display:block}.image-preview-product-item-info[data-v-5c9fa887]{height:100%;width:%?380?%;margin-left:%?20?%;display:flex;flex-direction:column;justify-content:center}.image-preview-product-item-info-text[data-v-5c9fa887]{font-size:%?30?%;font-weight:400;line-height:%?43.44?%;color:#222}.image-preview-product-item-info-price[data-v-5c9fa887]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:var(--custom-brand-color);margin-top:%?14?%}.image-preview-product-item-info-price uni-text[data-v-5c9fa887]{font-size:%?24?%}.image-preview-product-item-buy[data-v-5c9fa887]{width:%?180?%;height:%?64?%;border-radius:100px;background:var(--custom-brand-color);display:flex;justify-content:center;align-items:center;font-size:%?32?%;font-weight:400;line-height:%?32?%;color:#fff}.image-preview-product-item-card[data-v-5c9fa887]{width:%?48?%;height:%?48?%}.image-preview-product-item-card-one[data-v-5c9fa887]{position:absolute;right:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.image-preview-separate[data-v-5c9fa887]{width:%?702?%;height:1px;background-color:hsla(0,0%,93.3%,.8);margin:0 auto}.comment-popup[data-v-5c9fa887]{width:100vw;height:100vh;background:rgba(0,0,0,.39);position:fixed;left:0;top:0}.comment-popup-content[data-v-5c9fa887]{width:100%;height:70vh;position:absolute;left:0;bottom:0;overflow-y:auto}.page-title[data-v-5c9fa887]{width:%?360?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;text-align:center}.mphtml[data-v-5c9fa887] img{display:flex}[data-v-5c9fa887] .mescroll-totop{bottom:%?150?%!important}.share-list[data-v-5c9fa887]{width:%?450?%}.share-list-one[data-v-5c9fa887]{margin:0;border-radius:0;border-bottom:1px solid #eee;background-color:#fff;height:%?90?%;display:flex;align-items:center;padding-left:%?50?%}.share-list-one-icon[data-v-5c9fa887]{width:%?45?%;height:%?45?%;margin-right:%?20?%}',""]),t.exports=e},"7b934":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-71c009d5]{width:100%;text-align:center}.listright[data-v-71c009d5]{width:%?112?%;margin:0 auto}.listright .item[data-v-71c009d5]{margin:0;width:100%;display:flex;flex-direction:column;align-items:center;background-color:initial}.listright .item[data-v-71c009d5]:not(:first-child){margin-top:%?20?%}.listright .item-img[data-v-71c009d5]{width:%?112?%;height:%?100?%}.listright .item-text[data-v-71c009d5]{font-size:%?24?%;font-weight:400;line-height:%?38?%;color:#fff;text-shadow:0 %?3?% %?6?% rgba(0,0,0,.16)}',""]),t.exports=e},"7d21":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"videoList"},[i("v-uni-view",{staticClass:"swiper-box"},[i("v-uni-swiper",{staticClass:"swiper",attrs:{vertical:!0,circular:!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeplay.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}},t._l(t.videos,(function(e,n){return i("v-uni-swiper-item",{key:e.id,attrs:{"data-id":e.id}},[i("v-uni-view",{staticClass:"swiper-item",staticStyle:{color:"#000000"}},[i("video-player",{key:e.id,ref:"player",refInFor:!0,attrs:{video:e,index:n},on:{changeClick:function(e){arguments[0]=e=t.$handleEvent(e),t.changeClick.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"listleftbox"},[i("list-left",{attrs:{"show-data":e}})],1),i("v-uni-view",{staticClass:"listrightbox"})],1)})),1)],1),t.showCommnetPopup?i("v-uni-view",{ref:"commentPopup",staticClass:"comment-popup"},[i("v-uni-view",{staticClass:"comment-popup-content",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[t.videos[t.page].id?i("comment-preview",{attrs:{"article-id":t.videos[t.page].id,like:{is_show:!1}},on:{updateCommentCounts:function(e){arguments[0]=e=t.$handleEvent(e),t.update_comment_counts.apply(void 0,arguments)},toLogin:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)}}}):t._e(),i("v-uni-image",{staticClass:"comment-popup-content-close",attrs:{src:t.$util.img("public/static/youpin/clase-x.png"),alt:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeComment.apply(void 0,arguments)}}})],1)],1):t._e()],1)},a=[]},"7fcc":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{value:!0}},methods:{toTopClick:function(){this.$emit("toTop")}}}},8465:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uniPopup:i("5e99").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"listLeft"},[i("v-uni-view",{staticClass:"user",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toSeedingHome(t.showData.member_id)}}},[i("v-uni-image",{staticClass:"user-head",attrs:{src:t.showData.headimg}}),i("v-uni-text",{staticClass:"user-name"},[t._v(t._s(t.showData.nickname))])],1),i("v-uni-view",{staticClass:"info"},[""!=t.showData.title?i("v-uni-view",{staticClass:"info-title"},[t._v(t._s(t.showData.title))]):t._e(),i("v-uni-view",{staticClass:"info-row"},[i("v-uni-scroll-view",{staticStyle:{"max-height":"380rpx"},attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"info-desc",class:{"info-desc-hide":t.is_hide}},[t._v(t._s(t.showData.content))])],1),i("v-uni-view",{staticClass:"info-more"},[t.show_hide?i("v-uni-text",{staticClass:"info-more-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeHide.apply(void 0,arguments)}}},[t._v(t._s(t.hide_text))]):t._e()],1)],1)],1),(t.showData.goods&&t.showData.goods.length,t._e()),i("uni-popup",{ref:"videoDescRef",staticClass:"video-desc-father",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"video-desc"},[i("v-uni-view",{staticClass:"video-desc-header"},[i("v-uni-image",{staticClass:"video-desc-header-img",attrs:{src:t.$util.img(t.showData.headimg)}}),i("v-uni-text",{staticClass:"video-desc-header-name"},[t._v(t._s(t.showData.nickname))])],1),i("v-uni-view",{staticClass:"video-desc-content"},[t._v(t._s(t.showData.content))]),i("v-uni-view",{staticClass:"video-desc-op"},[i("v-uni-text",{staticClass:"video-desc-op-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.videoDescRef.close()}}},[t._v("收起")])],1)],1)],1)],1)},o=[]},8590:function(t,e,i){"use strict";var n=i("15d5"),a=i.n(n);a.a},"8ce4":function(t,e,i){"use strict";i.r(e);var n=i("0a79"),a=i("47344");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("4a6b");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"702aec7c",null,!1,n["a"],void 0);e["default"]=r.exports},"958b":function(t,e,i){"use strict";var n=i("1b2a"),a=i.n(n);a.a},"959c":function(t,e,i){"use strict";i.r(e);var n=i("4f21"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"9cee":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-702aec7c]{width:100%;text-align:center}.bottom-nav[data-v-702aec7c]{width:100%;height:calc(%?126?% + env(safe-area-inset-bottom));padding:0 %?20?%;padding-bottom:env(safe-area-inset-bottom);border-top:%?2?% solid #f5f5f5;box-sizing:border-box;position:fixed;left:0;bottom:0;background-color:#fff;display:flex;justify-content:space-between;align-items:center}.bottom-nav-dark[data-v-702aec7c]{border-top:%?2?% solid #000;background-color:#000}.bottom-nav-left[data-v-702aec7c]{width:%?194?%;height:%?64?%;border-radius:%?100?%;background:var(--custom-brand-color-10);font-size:%?28?%;font-weight:400;line-height:%?40?%;color:var(--custom-brand-color);display:flex;justify-content:center;align-items:center}.bottom-nav-left-dark[data-v-702aec7c]{background-color:hsla(0,0%,100%,.1);color:#fff}.bottom-nav-left-icon[data-v-702aec7c]{width:%?32?%;height:%?32?%;margin-right:%?12?%}.bottom-nav-right[data-v-702aec7c]{display:flex;align-items:center}.bottom-nav-right-one[data-v-702aec7c]{display:flex;align-items:center;background-color:initial;margin:0;padding:0;box-sizing:border-box}.bottom-nav-right-one[data-v-702aec7c]:not(:first-child){margin-left:%?30?%}.bottom-nav-right-one-dark[data-v-702aec7c]{color:#fff}.bottom-nav-right-one-num[data-v-702aec7c]{font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838;margin-left:%?6?%}.bottom-nav-right-one-num-dark[data-v-702aec7c]{color:#fff}.goods-list-pop-father[data-v-702aec7c] .uni-popup__mask{bottom:unset;height:calc(100vh - %?126?% - env(safe-area-inset-bottom))}.goods-list-pop-father[data-v-702aec7c] .uni-popup__wrapper.uni-bottom{border-radius:%?40?% %?40?% 0 0;bottom:calc(%?126?% + env(safe-area-inset-bottom))}.goods-list-pop-father-dark[data-v-702aec7c] .uni-popup__wrapper.uni-bottom{background:transparent}.goods-list-pop-father[data-v-702aec7c] .uni-popup__wrapper-box{border-radius:%?40?% %?40?% 0 0!important;background:transparent!important}.goods-list-pop-father[data-v-702aec7c] .bottom{padding-bottom:0!important}.goods-list-pop[data-v-702aec7c]{padding:0 %?20?% %?26?% %?20?%;box-sizing:border-box;position:relative}.goods-list-pop-dark[data-v-702aec7c]{background-color:rgba(0,0,0,.5)}.goods-list-pop-header[data-v-702aec7c]{display:flex;justify-content:space-between;align-items:center;box-sizing:border-box;position:-webkit-sticky;position:sticky;left:0;top:0;height:%?98?%;padding-top:%?26?%;z-index:1;background-color:#fff;border-radius:%?40?% %?40?% 0 0}.goods-list-pop-header-dark[data-v-702aec7c]{background:transparent}.goods-list-pop-header-left[data-v-702aec7c]{font-size:%?32?%;font-weight:700;line-height:%?46.34?%;color:#383838}.goods-list-pop-header-left-dark[data-v-702aec7c]{color:#fff}.goods-list-pop-header-right[data-v-702aec7c]{font-size:%?40?%;color:#e5e5e5}.goods-list-pop-list-item[data-v-702aec7c]{width:100%;display:flex;justify-content:space-between;align-items:center;height:%?144?%;border-radius:%?20?%;background:#f7f7f7;position:relative;margin-top:%?20?%;padding:0 %?10?%;box-sizing:border-box}.goods-list-pop-list-item-left[data-v-702aec7c]{display:flex;align-items:center}.goods-list-pop-list-item-img[data-v-702aec7c]{width:%?120?%;height:%?120?%;border-radius:%?20?%;display:block}.goods-list-pop-list-item-info[data-v-702aec7c]{height:100%;width:%?380?%;margin-left:%?20?%;display:flex;flex-direction:column;justify-content:center}.goods-list-pop-list-item-info-text[data-v-702aec7c]{font-size:%?30?%;font-weight:400;line-height:%?43.44?%;color:#222}.goods-list-pop-list-item-info-price[data-v-702aec7c]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:var(--custom-brand-color);margin-top:%?14?%}.goods-list-pop-list-item-info-price uni-text[data-v-702aec7c]{font-size:%?24?%}.goods-list-pop-list-item-buy[data-v-702aec7c]{width:%?160?%;height:%?64?%;border-radius:100px;background:var(--custom-brand-color);display:flex;justify-content:center;align-items:center;font-size:%?32?%;font-weight:400;line-height:%?32?%;color:#fff}.goods-list-pop-list-item-card[data-v-702aec7c]{width:%?48?%;height:%?48?%}.goods-list-pop-list-item-card-one[data-v-702aec7c]{position:absolute;right:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}',""]),t.exports=e},"9ecd":function(t,e,i){"use strict";var n=i("ac0c"),a=i.n(n);a.a},a157:function(t,e,i){"use strict";i.r(e);var n=i("7fcc"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},a256:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uniNavBar:i("d817").default,uniPopup:i("5e99").default,diyShareNavigateH5:i("2f73").default,ydAuthPopup:i("161f").default,nsLogin:i("4f5a").default,loadingCover:i("5510").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{style:[t.themeColorVar]},[0==t.type?i("v-uni-view",{staticClass:"video-preview"},[t.isOnXianMaiApp?i("v-uni-view",{staticClass:"custom",style:{paddingTop:t.statusBarHeight+20+"px",height:t.navHeight+"px",backgroundColor:"transparent"}},[i("v-uni-view",{staticClass:"iconfont iconback_light",staticStyle:{color:"white"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"custom-navbar"},[i("v-uni-view",{staticClass:"navbar-item"})],1)],1):t._e(),t.list.length?[i("video-list",{attrs:{list:t.list},on:{nextVideo:function(e){arguments[0]=e=t.$handleEvent(e),t.nextVideo.apply(void 0,arguments)},previousVideo:function(e){arguments[0]=e=t.$handleEvent(e),t.previousVideo.apply(void 0,arguments)},changeLike:function(e){arguments[0]=e=t.$handleEvent(e),t.changeLike.apply(void 0,arguments)},toShare:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)},toLogin:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)}}})]:t._e()],2):t._e(),1==t.type?i("v-uni-view",{staticClass:"image-preview",style:{paddingTop:t.statusBarHeight+t.navHeight+"px"}},[t.isOnXianMaiApp?i("uni-nav-bar",{attrs:{"left-icon":"back",border:!1},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}}):t._e(),t.info.share_resource&&t.info.share_resource.length>0?i("v-uni-view",{staticClass:"image-preview-header"},[i("v-uni-swiper",{staticClass:"swiper",style:{height:t.listHeight+"px"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.info.share_resource,(function(e,n){return i("v-uni-swiper-item",{key:n,staticClass:"swiper-item"},[i("v-uni-image",{ref:"proImg",refInFor:!0,staticClass:"swiper-image",attrs:{src:e,alt:"",mode:"scaleToFill"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLoad(e,n)}}})],1)})),1),i("v-uni-text",{staticClass:"image-preview-header-index"},[t._v(t._s(t.swiperIndex+1)+"/"+t._s(t.info.share_resource.length))])],1):t._e(),i("v-uni-view",{staticClass:"image-preview-article"},[i("v-uni-view",{staticClass:"image-preview-article-info"},[i("v-uni-view",{staticClass:"image-preview-article-info-left",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toSeedingHome(t.info.member_id)}}},[i("v-uni-image",{staticClass:"image-preview-article-info-image",attrs:{src:t.info.headimg}}),i("v-uni-text",{staticClass:"image-preview-article-info-name"},[t._v(t._s(t.info.nickname))])],1)],1),""!=t.info.title?i("v-uni-view",{staticClass:"image-preview-article-title"},[t._v(t._s(t.info.title))]):t._e(),i("v-uni-view",{staticClass:"image-preview-article-desc"},[t.info.content?i("mphtml",{ref:"mphtml",staticClass:"mphtml",attrs:{content:t.info.content}}):t._e()],1)],1),t.info.goods&&t.info.goods.length>0?[i("v-uni-view",{staticClass:"image-preview-product"},[i("v-uni-view",{staticClass:"image-preview-product-header"},[i("v-uni-image",{staticClass:"image-preview-product-header-icon",attrs:{src:t.$util.img("public/static/youpin/goods-add.png")}}),t._v("推荐商品")],1),t._l(t.info.goods,(function(e,n){return i("v-uni-view",{staticClass:"image-preview-product-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-image",{staticClass:"image-preview-product-item-img",attrs:{src:e.goods_image,alt:""},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.errorFun(t.info.goods,n)}}}),i("v-uni-view",{staticClass:"image-preview-product-item-info"},[i("v-uni-view",{staticClass:"image-preview-product-item-info-text overtext-hidden-one"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"image-preview-product-item-info-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1)],1),i("v-uni-text",{staticClass:"image-preview-product-item-buy"},[t._v("购买")])],1)}))],2)]:t._e(),i("v-uni-view",{staticClass:"image-preview-separate"})],2):t._e(),i("uni-popup",{ref:"downloadSharePopup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"share-list"},[i("v-uni-button",{staticClass:"share-list-one",attrs:{"open-type":"share"}},[i("v-uni-image",{staticClass:"share-list-one-icon",attrs:{src:t.$util.img("public/static/youpin/member/icon-wechat.png")}}),t._v("分享给微信好友")],1),i("v-uni-button",{staticClass:"share-list-one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDownload.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-list-one-icon",attrs:{src:t.$util.img("public/static/youpin/download.png")}}),t._v("保存图片/视频到手机")],1),i("v-uni-button",{staticClass:"share-list-one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copywriting.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-list-one-icon",attrs:{src:t.$util.img("public/static/youpin/copy.png")}}),t._v("复制标题与正文文案")],1)],1)],1),Object.keys(t.info).length?[i("bottom-nav",{attrs:{dataInfo:t.info,theme:0==t.type?"dark":"white"},on:{changeLike:function(e){arguments[0]=e=t.$handleEvent(e),t.changeLike.apply(void 0,arguments)},toShare:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}})]:t._e(),t.showTop?i("to-top",{on:{toTop:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToTopNative()}}}):t._e(),i("diy-share-navigate-h5",{ref:"shareNavigateH5"}),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"})],2)},o=[]},a4f6:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("dc69"),i("c223"),i("8f71"),i("bf0f"),i("f7a5");var a=n(i("2634")),o=n(i("2fdc")),s=n(i("b5cc")),r=n(i("cba4")),c=n(i("85bf")),l=n(i("7c8d")),d=n(i("172f")),u=n(i("3b27")),f=n(i("37cd")),p=n(i("2f73")),h=n(i("f8de")),m=n(i("2d01")),v=n(i("8ce4")),g=n(i("de74")),b=n(i("5e99")),w=i("4b89"),x={name:"seeding_detail",components:{UniPopup:b.default,UniIcons:g.default,videoList:s.default,commentPreview:r.default,mphtml:d.default,toTop:f.default,diyShareNavigateH5:p.default,bottomNav:v.default},mixins:[u.default,h.default,m.default],data:function(){return{oldId:null,id:null,type:1,statusBarHeight:0,navHeight:0,listHeight:0,imgHeights:[],info:{},showCommnetPopup:!1,swiperIndex:0,page_size:10,page:1,page_count:1,list:[],videIndex:0,isOnXianMaiApp:w.isOnXianMaiApp}},onLoad:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,c.default.wait_staticLogin_success();case 2:e.id=t.id,e.oldId=t.id,e.pageviews();case 5:case"end":return i.stop()}}),i)})))()},onShow:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.default.wait_staticLogin_success();case 2:return e.next=4,t.getData();case 4:if(0!=t.type){e.next=7;break}return e.next=7,t.getVideoList();case 7:t.$refs.loadingCover&&t.$refs.loadingCover.hide();case 8:case"end":return e.stop()}}),e)})))()},methods:{toLogin:function(){uni.getStorageSync("token")||this.$util.toShowLoginPopup(this,null,"/promotionpages/seeding/seeding_detail/seeding_detail?id=".concat(this.oldId))},previousVideo:function(t){this.videIndex=t,this.id=this.list[t].id,this.info=this.list[t],this.pageviews()},nextVideo:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.videIndex=t,e.id=e.list[t].id,e.info=e.list[t],e.pageviews(),!(e.videIndex+1+4>=e.list.length)){i.next=7;break}return i.next=7,e.getVideoList();case 7:case"end":return i.stop()}}),i)})))()},navigateBack:function(){var t=getCurrentPages().reverse();t.length<2?this.$util.redirectTo("/promotionpages/seeding/seeding-list/seeding-list",{},"reLaunch"):uni.navigateBack()},getData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:l.default.usershareexperienceContent,async:!1,data:{id:t.id}});case 2:i=e.sent,0==i.code&&(t.info=i.data,t.imgHeights=new Array(t.info.share_resource.length),3==t.info.content_type?t.type=1:4==t.info.content_type&&(t.type=0));case 4:case"end":return e.stop()}}),e)})))()},getVideoList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(t.page>t.page_count)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$api.sendRequest({url:l.default.usershareexperienceVideoListUrl,async:!1,data:{id:t.id,page:t.page,page_size:t.page_size}});case 4:i=e.sent,0==i.code&&i.data&&Array.isArray(i.data.list)&&(t.page_count=i.data.page_count,t.list=t.list.concat(i.data.list),t.page+=1);case 6:case"end":return e.stop()}}),e)})))()},changeSwiper:function(t){this.swiperIndex=t.detail.current,this.listHeight=this.imgHeights[this.swiperIndex]},imgLoad:function(t,e){this.systemInfo=wx.getSystemInfoSync();var i=this.systemInfo.screenWidth,n=i/t.detail.width;this.imgHeights[e]=t.detail.height*n,e==this.swiperIndex&&(this.listHeight=this.imgHeights[e])},changeLike:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n,o,s;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(uni.getStorageSync("token")){t.next=3;break}return e.toLogin(),t.abrupt("return");case 3:return t.next=5,e.$api.sendRequest({url:l.default.usershareexperienceLike,async:!1,data:{id:e.id}});case 5:i=t.sent,0==i.code?(n=i.data.like_num,e.info.like_num=n,e.info.is_like?e.info.is_like=!1:e.info.is_like=!0,1==e.type||(o=e.list.filter((function(t){return t.id==e.id})),o.length>0&&(s=o[0],s.like_num=n,s.is_like?s.is_like=!1:s.is_like=!0))):e.$util.showToast({title:i.message,mask:!0});case 7:case"end":return t.stop()}}),t)})))()},getSharePageParams:function(){var t="",e="";0==this.type?(t=this.list[this.videIndex].image,e=""==this.list[this.videIndex].title?this.list[this.videIndex].content:this.list[this.videIndex].title):(t=this.info.share_resource[0],e=""==this.info.title?this.info.content:this.info.title);var i=this.$util.unifySharePageParams("/promotionpages/seeding/seeding_detail/seeding_detail","先迈商城",e,{id:this.id},t);return i},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},openSharePopup:function(){var t=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(t)},closeSharePopup:function(){this.$refs.sharePopup&&this.$refs.sharePopup.close()},toSeedingHome:function(t){this.$util.redirectTo("/promotionpages/seeding/seeding_home_page/seeding_home_page",{other_mid:t})},pageviews:function(){this.$api.sendRequest({url:l.default.usershareexperienceAddView,data:{id:this.id}})},getTransmit:function(){this.$api.sendRequest({url:l.default.usershareexperienceTransmit,data:{id:this.id}})},downloadFile:function(t){return new Promise((function(e,i){uni.downloadFile({url:t,success:function(t){var i=t.tempFilePath;e(i)},fail:function(t){i(t)}})}))},saveFile:function(t){var e=this;return new Promise((function(i,n){uni.getSetting({success:function(a){var o=a.authSetting;o.hasOwnProperty("scope.writePhotosAlbum")?o["scope.writePhotosAlbum"]?uni.saveImageToPhotosAlbum({filePath:t,success:function(t){return i(t)},fail:function(t){n(t)}}):e.$util.showToast({title:"未授权保存文件到相册的权限，需要授权才能保存",success:function(t){uni.openSetting({success:function(){n("")},fail:function(t){n(t)}})},fail:function(t){n(t)}}):uni.authorize({scope:"scope.writePhotosAlbum",success:function(e){uni.saveImageToPhotosAlbum({filePath:t,success:function(t){return i(t)},fail:function(t){n(t)}})},fail:function(t){n(t)}})},fail:function(t){n(t)}})}))},saveToPhotoAlbum:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function i(){var n,o;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:i.prev=0,uni.showLoading({title:"保存中",mask:!0}),n=0;case 3:if(!(n<t.length)){i.next=12;break}return i.next=6,e.downloadFile(t[n]);case 6:return o=i.sent,i.next=9,e.saveFile(o);case 9:n++,i.next=3;break;case 12:uni.hideLoading(),e.$util.showToast({title:"保存文件完成"}),i.next=20;break;case 16:i.prev=16,i.t0=i["catch"](0),uni.hideLoading(),e.$util.showToast({title:i.t0});case 20:case"end":return i.stop()}}),i,null,[[0,16]])})))()},toDownload:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$refs.downloadSharePopup.close(),1!=t.type){e.next=10;break}if(!(t.info.share_resource&&t.info.share_resource.length>0)){e.next=7;break}return e.next=5,t.saveToPhotoAlbum(t.info.share_resource);case 5:e.next=8;break;case 7:t.$util.showToast({title:"没图片可以保存的"});case 8:e.next=15;break;case 10:if(0!=t.type){e.next=15;break}if(i=t.list.filter((function(e){return e.id==t.id})),!(i.length>0)){e.next=15;break}return e.next=15,t.saveToPhotoAlbum([i[0].share_resource]);case 15:case"end":return e.stop()}}),e)})))()},copywriting:function(){var t=this;this.$refs.downloadSharePopup.close();var e="";if(1==this.type)e=this.$refs.mphtml.getText(this.$refs.mphtml.nodes),this.info.title&&(e=this.info.title+"\r\n"+e);else if(0==this.type){var i=this.list.filter((function(e){return e.id==t.id}));i.length>0&&(e=i[0].content,i[0].title&&(e=i[0].title+"\r\n"+e))}this.$util.copy(e)},errorFun:function(t,e){t[e]&&(t[e].goods_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()}},onShareAppMessage:function(t){this.getTransmit();var e=this.getSharePageParams(),i=(e.title,e.link),n=e.imageUrl,a=e.desc;return this.$buriedPoint.pageShare(i,n,a)}};e.default=x},a6ff:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var a=n(i("5e99")),o={components:{UniPopup:a.default},props:{showData:{type:Object}},data:function(){return{show_hide:!1,is_hide:!0,hide_text:"展开",textHeight:0}},mounted:function(){this.getSize()},methods:{toSeedingHome:function(t){this.$util.redirectTo("/promotionpages/seeding/seeding_home_page/seeding_home_page",{other_mid:t})},changeHide:function(){this.is_hide=!this.is_hide,this.is_hide?this.hide_text="展开":this.hide_text="收起"},getSize:function(){var t=this,e=uni.getSystemInfoSync(),i=e.screenWidth,n=uni.createSelectorQuery().in(this);n.select(".info-desc").boundingClientRect((function(e){t.textHeight=e.height,e.height>=i/375*35&&(t.show_hide=!0)})).exec()}}};e.default=o},ac0c:function(t,e,i){var n=i("d7d6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("c9625806",n,!0,{sourceMap:!1,shadowMode:!1})},b5cc:function(t,e,i){"use strict";i.r(e);var n=i("7d21"),a=i("959c");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("958b");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"4c02bd5b",null,!1,n["a"],void 0);e["default"]=r.exports},b6a7:function(t,e,i){"use strict";i.r(e);var n=i("43e5"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},c2d3:function(t,e,i){"use strict";i.r(e);var n=i("a256"),a=i("f009");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("5c80"),i("dcd3");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"5c9fa887",null,!1,n["a"],void 0);e["default"]=r.exports},c66e:function(t,e,i){var n=i("41aa");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("16a3abee",n,!0,{sourceMap:!1,shadowMode:!1})},cba4:function(t,e,i){"use strict";i.r(e);var n=i("235d"),a=i("b6a7");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("9ecd");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"fcbce028",null,!1,n["a"],void 0);e["default"]=r.exports},d214:function(t,e,i){var n=i("d9fd");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("471b92d1",n,!0,{sourceMap:!1,shadowMode:!1})},d5b7:function(t,e,i){var n=i("7390");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("84edc666",n,!0,{sourceMap:!1,shadowMode:!1})},d631:function(t,e,i){"use strict";i.r(e);var n=i("1926"),a=i("fc1f");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("8590");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"71c009d5",null,!1,n["a"],void 0);e["default"]=r.exports},d7d6:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-fcbce028]{width:100%;text-align:center}.comment[data-v-fcbce028]{padding-left:%?24?%;box-sizing:border-box;padding-bottom:%?100?%;background-color:#fff;padding-top:%?40?%}.comment-count[data-v-fcbce028]{font-size:%?26?%;font-weight:400;line-height:%?42?%;color:#666}.comment-list[data-v-fcbce028]{padding-bottom:%?46?%;box-sizing:border-box}.comment-list-item[data-v-fcbce028]{margin-top:%?24?%}.comment-list-item-one[data-v-fcbce028]{display:flex;justify-content:space-between}.comment-list-item-one-head[data-v-fcbce028]{width:%?68?%;height:%?68?%;border-radius:50%}.comment-list-item-one-info[data-v-fcbce028]{width:%?642?%}.comment-list-item-one-info-name[data-v-fcbce028]{font-size:%?26?%;font-weight:400;line-height:%?42?%;color:#999;display:flex;align-items:center}.comment-list-item-one-info-name-inner[data-v-fcbce028]{width:%?64?%;height:%?36?%;line-height:%?36?%;background:#f33;border-radius:%?20?%;font-size:%?22?%;font-weight:400;color:#fff;margin-left:%?6?%;display:block;text-align:center}.comment-list-item-one-info-content[data-v-fcbce028]{font-size:%?28?%;font-weight:400;line-height:%?42?%;color:#333;margin-top:%?4?%;word-break:break-word;padding-right:%?24?%;box-sizing:border-box}.comment-list-item-one-info-date[data-v-fcbce028]{display:flex;align-items:center;font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#999;margin-top:%?4?%}.comment-list-item-one-info-date uni-text[data-v-fcbce028]{font-size:%?22?%;font-weight:400;line-height:%?42?%;color:#7096d8;margin-left:%?6?%}.comment-list-item-two[data-v-fcbce028]{display:flex;width:%?642?%;margin-top:%?24?%;float:right}.comment-list-item-two-head[data-v-fcbce028]{width:%?44?%;height:%?44?%;border-radius:50%}.comment-list-item-two-info[data-v-fcbce028]{margin-left:%?10?%}.comment-list-item-two-info-name[data-v-fcbce028]{font-size:%?26?%;font-weight:400;line-height:%?42?%;color:#999;display:flex;align-items:center}.comment-list-item-two-info-name-inner[data-v-fcbce028]{width:%?64?%;height:%?36?%;line-height:%?36?%;background:#f33;border-radius:%?20?%;font-size:%?22?%;font-weight:400;color:#fff;margin-left:%?6?%;display:block;text-align:center}.comment-list-item-two-info-content[data-v-fcbce028]{font-size:%?28?%;font-weight:400;line-height:%?42?%;color:#333;margin-top:%?4?%;padding-right:%?24?%;box-sizing:border-box}.comment-list-item-two-info-content uni-text[data-v-fcbce028]{color:#999;margin:0 %?10?%}.comment-list-item-two-info-date[data-v-fcbce028]{display:flex;align-items:center;font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#999;margin-top:%?4?%}.comment-list-item-two-info-date uni-text[data-v-fcbce028]{font-size:%?22?%;font-weight:400;line-height:%?42?%;color:#7096d8;margin-left:%?6?%}.comment-list-item-more[data-v-fcbce028]{font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#0b055e;float:right;width:%?642?%;display:flex;align-items:center;margin-top:%?24?%}.comment-list-item-separate[data-v-fcbce028]{width:%?642?%;height:1px;background-color:#eee;float:right;margin-top:%?16?%}.comment-list-more[data-v-fcbce028]{font-size:%?26?%;font-weight:400;line-height:%?42?%;color:#999;text-align:center;margin-top:%?24?%}.comment-input[data-v-fcbce028]{padding:%?8?% 0;padding-left:%?24?%;padding-right:%?24?%;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between;width:100vw;background-color:#fff}.comment-input-text[data-v-fcbce028]{font-size:%?32?%;font-weight:400;line-height:%?42?%;color:#666;width:%?560?%;height:%?84?%;background:#f6f6f6;border-radius:%?42?%;display:flex;align-items:center;padding-left:%?24?%;box-sizing:border-box}.comment-input-like[data-v-fcbce028]{font-size:%?28?%;font-weight:600;line-height:%?40?%;color:#333;margin-left:%?16?%;display:flex;align-items:center}.comment-input-like-img[data-v-fcbce028]{width:%?56?%;height:%?56?%;margin-right:%?4?%}.comment-input-like .iconfont[data-v-fcbce028]{font-size:%?48?%;margin-right:%?8?%}.comment-popup[data-v-fcbce028]{width:100vw;height:100vh;background:rgba(0,0,0,.39);position:fixed;left:0;top:0;z-index:10}.comment-popup-inner[data-v-fcbce028]{position:fixed;left:0;bottom:0;width:%?750?%;height:%?120?%;background:#fff;display:flex;justify-content:center;align-items:center}.comment-popup-inner-input[data-v-fcbce028]{width:%?606?%;height:%?84?%;background:#f6f6f6;border-radius:%?42?%;margin-right:%?24?%;padding-left:%?24?%;box-sizing:border-box}.comment-popup-inner-input-placeholder[data-v-fcbce028]{font-size:%?32?%;font-weight:400;color:#666}.comment-popup-inner-post[data-v-fcbce028]{width:%?72?%;height:%?72?%}',""]),t.exports=e},d9fd:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* 回到顶部的按钮 */.mescroll-totop[data-v-0d800a55]{z-index:9990;position:fixed!important; /* 加上important避免编译到H5,在多mescroll中定位失效 */right:%?0?%!important;bottom:%?272?%!important;width:%?144?%;height:%?146?%;border-radius:50%;opacity:0;transition:opacity .5s; /* 过渡 */margin-bottom:var(--window-bottom) /* css变量 */}\r\n/* 适配 iPhoneX */.mescroll-safe-bottom[data-v-0d800a55]{margin-bottom:calc(var(--window-bottom) + constant(safe-area-inset-bottom)); /* window-bottom + 适配 iPhoneX */margin-bottom:calc(var(--window-bottom) + env(safe-area-inset-bottom))}\r\n/* 显示 -- 淡入 */.mescroll-totop-in[data-v-0d800a55]{opacity:1}\r\n/* 隐藏 -- 淡出且不接收事件*/.mescroll-totop-out[data-v-0d800a55]{opacity:0;pointer-events:none}",""]),t.exports=e},dbf6:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-4c02bd5b]{width:100%;text-align:center}.videoList[data-v-4c02bd5b]{height:calc(100vh - %?126?% - env(safe-area-inset-bottom));width:100%}.swiper-box[data-v-4c02bd5b]{height:100%;width:100%}.swiper[data-v-4c02bd5b]{height:100%;width:100%}.swiper-item[data-v-4c02bd5b]{height:100%;width:100%;z-index:19}.title[data-v-4c02bd5b]{color:#fff}[data-v-4c02bd5b] .listleftbox{z-index:6;-webkit-transform:translateZ(1000px);transform:translateZ(1000px);position:absolute;bottom:0;left:0;box-sizing:border-box}[data-v-4c02bd5b] .listrightbox{z-index:6;position:absolute;bottom:%?420?%;right:%?18?%;color:#fff;-webkit-transform:translateZ(1000px);transform:translateZ(1000px)}.comment-popup[data-v-4c02bd5b]{width:100vw;height:100vh;background:rgba(0,0,0,.39);position:fixed;left:0;top:0}.comment-popup-content[data-v-4c02bd5b]{width:100%;height:70vh;position:absolute;left:0;bottom:0;overflow-y:auto;background-color:#fff;border-radius:%?20?% %?20?% 0 0}.comment-popup-content-close[data-v-4c02bd5b]{position:absolute;right:%?24?%;top:%?24?%;width:%?32?%;height:%?32?%}',""]),t.exports=e},dcd3:function(t,e,i){"use strict";var n=i("d5b7"),a=i.n(n);a.a},e0b7:function(t,e,i){"use strict";var n=i("d214"),a=i.n(n);a.a},e6e0:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-589ee183]{width:100%;text-align:center}.listLeft[data-v-589ee183]{width:100vw;min-height:%?1000?%;background:linear-gradient(180deg,hsla(0,0%,100%,0),rgba(0,0,0,.3));padding-bottom:%?32?%;box-sizing:border-box;display:flex;flex-direction:column;justify-content:flex-end}.listLeft .user[data-v-589ee183]{display:flex;align-items:center;padding-left:%?24?%;box-sizing:border-box}.listLeft .user-head[data-v-589ee183]{width:%?44?%;height:%?44?%;background:hsla(0,0%,86.3%,.39);border-radius:50%}.listLeft .user-name[data-v-589ee183]{font-size:%?32?%;font-weight:400;line-height:%?42?%;color:#fff;margin-left:%?14?%}.listLeft .info[data-v-589ee183]{margin-top:%?36?%;padding-left:%?24?%;box-sizing:border-box;width:%?680?%}.listLeft .info-title[data-v-589ee183]{font-size:%?32?%;font-weight:500;line-height:%?38?%;color:#fff;margin-bottom:%?12?%}.listLeft .info-row[data-v-589ee183]{display:flex;align-items:flex-end}.listLeft .info-desc[data-v-589ee183]{font-size:%?28?%;font-weight:400;line-height:%?38?%;color:#fff;box-sizing:border-box;word-break:break-all}.listLeft .info-desc-hide[data-v-589ee183]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.listLeft .info-more[data-v-589ee183]{text-align:right}.listLeft .info-more-text[data-v-589ee183]{width:%?60?%;height:%?36?%;background:hsla(0,0%,100%,.39);border-radius:%?4?%;font-size:%?26?%;font-weight:400;line-height:%?38?%;color:#fff;text-align:center;display:inline-block}.listLeft .product[data-v-589ee183]{padding-left:%?8?%;box-sizing:border-box;margin-top:%?40?%;overflow-x:auto;display:flex;flex-wrap:nowrap}.listLeft .product-item[data-v-589ee183]{margin-left:%?16?%;display:flex;align-items:center;height:%?120?%;background:#fff;border-radius:%?8?%;padding:%?12?%;box-sizing:border-box;position:relative}.listLeft .product-item-img[data-v-589ee183]{width:%?96?%;height:%?96?%;border-radius:%?4?%}.listLeft .product-item-info[data-v-589ee183]{display:flex;flex-direction:column;justify-content:space-between;height:100%;width:%?386?%;margin-left:%?12?%}.listLeft .product-item-info-text[data-v-589ee183]{width:%?386?%;font-size:%?28?%;font-weight:400;line-height:%?42?%;color:#333}.listLeft .product-item-info-price[data-v-589ee183]{font-size:%?32?%;font-weight:400;line-height:%?42?%;color:#f33}.listLeft .product-item-info-price uni-text[data-v-589ee183]{font-size:%?24?%}.listLeft .product-item-card[data-v-589ee183]{width:%?48?%;height:%?48?%}.listLeft .product-item-card-one[data-v-589ee183]{position:absolute;right:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.video-desc[data-v-589ee183]{padding:%?32?%;box-sizing:border-box;background:rgba(0,0,0,.8)}.video-desc-header[data-v-589ee183]{display:flex;align-items:center}.video-desc-header-img[data-v-589ee183]{width:%?64?%;height:%?64?%;border-radius:50%;box-sizing:border-box;border:%?2?% solid #fff}.video-desc-header-name[data-v-589ee183]{font-size:%?32?%;font-weight:700;line-height:%?37.5?%;color:#fff;margin-left:%?12?%}.video-desc-content[data-v-589ee183]{font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#fff}.video-desc-op[data-v-589ee183]{display:flex;justify-content:flex-end;margin-top:%?20?%}.video-desc-op-text[data-v-589ee183]{font-size:%?30?%;font-weight:400;line-height:%?40?%;color:#e5e5e5}.video-desc-father[data-v-589ee183] .uni-popup__wrapper-box{border-radius:0!important}.video-desc-father[data-v-589ee183] .bottom{padding-bottom:0!important}',""]),t.exports=e},f009:function(t,e,i){"use strict";i.r(e);var n=i("a4f6"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f0ae:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:["video","index"],data:function(){return{play:!1,dblClick:!1,autoplay:!1}},mounted:function(){this.videoContext=uni.createVideoContext("myVideo_".concat(this.video.id),this),this.atuo()},methods:{click:function(){!1===this.play?this.playThis():this.pause()},player:function(){this.videoContext.play(),this.play=!0},pause:function(){this.videoContext.pause(),this.play=!1},playThis:function(){this.videoContext.play(),this.play=!0},atuo:function(){this.index}},created:function(){}};e.default=n},f8de:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("4b89"),a={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){n.isOnXianMaiApp&&this.appCurrentPages<=1?(0,n.goClosePage)("0"):uni.navigateBack()}}};e.default=a},f97a:function(t,e,i){"use strict";var n=i("1372"),a=i.n(n);a.a},fc1f:function(t,e,i){"use strict";i.r(e);var n=i("714b"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},fe57:function(t,e,i){var n=i("e6e0");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("8085d3f0",n,!0,{sourceMap:!1,shadowMode:!1})}}]);