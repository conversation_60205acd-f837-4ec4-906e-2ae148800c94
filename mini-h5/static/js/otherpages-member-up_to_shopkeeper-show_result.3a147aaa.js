(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-up_to_shopkeeper-show_result"],{"0587":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={components:{},data:function(){return{applyData:""}},onLoad:function(t){this.getData()},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"升级成为店主"})},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},watch:{},methods:{getData:function(){var t=this;this.$api.sendRequest({url:this.$apiUrl.upgradeShop,data:{},success:function(e){var a=e.message;0==e.code&&e.data?t.applyData=e.data.apply_info:t.$util.showToast({title:a})},fail:function(){}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),a=e.title,n=e.link,i=e.imageUrl;e.query;return this.$buriedPoint.pageShare(n,i,a)}};e.default=n},"0dcd":function(t,e,a){"use strict";a.r(e);var n=a("0587"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"47cc":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{class:t.themeStyle},[a("v-uni-view",{staticClass:"banner"},[a("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/maidou/wait.png"),mode:""}}),a("v-uni-text",[t._v("申请成功，等待审核")])],1),a("v-uni-view",{staticClass:"form"},[a("v-uni-view",{staticClass:"phone"},[a("v-uni-text",{staticClass:"phonenum"},[t._v("真实姓名")]),a("v-uni-text",{staticClass:"pnum"},[t._v(t._s(t.applyData.real_name))])],1),a("v-uni-view",{staticClass:"phone"},[a("v-uni-text",{staticClass:"phonenum"},[t._v("微信号")]),a("v-uni-text",{staticClass:"pnum"},[t._v(t._s(t.applyData.wechat_no))])],1),a("v-uni-view",{staticClass:"phone"},[a("v-uni-text",{staticClass:"phonenum"},[t._v("手机号码")]),a("v-uni-text",{staticClass:"pnum"},[t._v(t._s(t.applyData.mobile))])],1)],1)],1)},i=[]},"7a70e":function(t,e,a){"use strict";a.r(e);var n=a("47cc"),i=a("0dcd");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("b2e4");var r=a("828b"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"1b906342",null,!1,n["a"],void 0);e["default"]=o.exports},"858d":function(t,e,a){var n=a("b427");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("41af5051",n,!0,{sourceMap:!1,shadowMode:!1})},b2e4:function(t,e,a){"use strict";var n=a("858d"),i=a.n(n);i.a},b427:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-1b906342]{width:100%;text-align:center}uni-page-body[data-v-1b906342]{background:#fff}body.?%PAGE?%[data-v-1b906342]{background:#fff}.banner[data-v-1b906342]{margin-top:%?100?%;display:flex;align-items:center;width:100%;flex-direction:column}.banner uni-image[data-v-1b906342]{width:%?148?%;height:%?148?%}.banner uni-text[data-v-1b906342]{font-size:%?40?%;color:#000;margin-top:%?30?%}.form[data-v-1b906342]{background:#fff;margin-top:%?80?%;padding:0 %?10?%}.phone[data-v-1b906342]{display:flex;align-items:center;justify-content:space-between;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.phone .phonenum[data-v-1b906342]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.phone .pnum[data-v-1b906342]{font-size:%?30?%;color:#333}',""]),t.exports=e}}]);