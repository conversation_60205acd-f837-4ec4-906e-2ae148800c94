(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-order-return_and_exchange-refund_progress"],{"0a00":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return o}));var o={uniPopup:n("5e99").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"view"},[n("v-uni-view",{staticClass:"content",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.open.apply(void 0,arguments)}}},[t.company_name?n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.company_name))]):n("v-uni-text",{staticClass:"placeholder"},[t._v("请选择物流公司")]),n("v-uni-text",{staticClass:"iconfont iconiconangledown icon"})],1),n("v-uni-view",{staticClass:"popup"},[n("uni-popup",{ref:"popup-select-upoff",staticClass:"uni-popup-father",attrs:{type:"bottom"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"uni-popup-popup"},[n("v-uni-view",{staticClass:"popup-header"},[n("v-uni-view",{staticClass:"popup-close"},[n("v-uni-text",{staticClass:"iconfont iconguanbi iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeOp.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"popup-title"},[n("v-uni-text",[t._v("请选择物流公司")])],1)],1),n("v-uni-view",{staticClass:"popup-content"},[n("v-uni-view",{staticClass:"popup-content-list"},[n("v-uni-view",{staticClass:"main-inner",staticStyle:{"min-height":"calc(100% + 1px)"}},t._l(t.express_company_list,(function(e,o){return n("v-uni-view",{key:o,staticClass:"popup-content-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.select(e)}}},[n("v-uni-view",{staticClass:"item-reason"},[t._v(t._s(e.company_name))]),n("v-uni-view",{staticClass:"item-select-icon"},[e.company_id==t.currentId?n("v-uni-text",{staticClass:"icon iconfont iconyuan_checked"}):n("v-uni-text",{staticClass:"icon iconfont iconyuan_checkbox"})],1)],1)})),1)],1)],1)],1)],1)],1)],1)},s=[]},"0a66":function(t,e,n){"use strict";n.r(e);var o=n("31e4"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},"1819c":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("dd2b");var i=o(n("b7c7")),s={props:{dataList:{type:Array,default:function(){return{}}}},computed:{DATALIST_SPLICE:function(){var t=(0,i.default)(this.dataList);return t.splice(0,1),t}},data:function(){return{margin:122}},methods:{getKey:function(t){return t+Math.random()}}};e.default=s},2171:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-b41f5b88]{width:100%;text-align:center}.uni-input-placeholder[data-v-b41f5b88],\r\n.placeholderByweixin-input[data-v-b41f5b88]{font-size:%?28?%;font-weight:400;color:#ccc}[data-v-b41f5b88] .uni-input-input{font-size:%?28?%;font-weight:400;color:#666}.refund_progress[data-v-b41f5b88]{width:100%;box-sizing:border-box;padding-bottom:%?100?%}.refund_progress .refund_progress-header[data-v-b41f5b88]{height:%?264?%;background:linear-gradient(-90deg,var(--custom-brand-color),var(--custom-brand-color-80));display:flex;align-items:center;justify-content:center}.refund_progress .refund_progress-header .header__progress[data-v-b41f5b88]{width:90%;height:%?200?%;background:#fff;border-radius:%?20?%;box-sizing:border-box;padding:%?30?% %?31?%}.refund_progress .refund_progress-header .header__progress .header__progress-title[data-v-b41f5b88]{font-size:%?32?%;font-weight:700;color:#333}.refund_progress .refund_progress-content[data-v-b41f5b88]{width:100%;box-sizing:border-box;padding:%?20?% %?24?%}.refund_progress .refund_progress-content .refund_progress-situation[data-v-b41f5b88]{width:100%;background:#fff;border-radius:%?20?%;box-sizing:border-box;padding:%?29?% %?22?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box[data-v-b41f5b88]{margin-bottom:%?61?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box[data-v-b41f5b88]:last-of-type{margin-bottom:unset}.refund_progress .refund_progress-content .refund_progress-situation .bottom_unset[data-v-b41f5b88]{margin-bottom:unset}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-title[data-v-b41f5b88]{font-size:%?28?%;font-weight:700;color:#333;margin-bottom:%?31?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content[data-v-b41f5b88]{width:100%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .receiving__info-box[data-v-b41f5b88]{width:100%;display:flex}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .receiving__info-box .info__box-text[data-v-b41f5b88]{width:%?159?%;font-size:%?28?%;font-weight:400;color:#666}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .receiving__info-box .info__box-value[data-v-b41f5b88]{flex:1;font-size:%?28?%;font-weight:400;color:#333}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .receiving__info-box .address-box[data-v-b41f5b88]{display:flex;align-items:flex-end}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .receiving__info-box .address-box .value__copy[data-v-b41f5b88]{width:%?90?%;height:%?44?%;font-size:%?22?%;font-weight:400;color:var(--custom-brand-color);text-align:center;line-height:%?44?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .content__receiving-info[data-v-b41f5b88]{width:100%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .content__receiving-info .receiving__info-form[data-v-b41f5b88]{width:100%;border-top:1px solid #eee;box-sizing:border-box;padding-top:%?30?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .content__receiving-info .receiving__info-form .info__form-input[data-v-b41f5b88]{width:100%;height:%?68?%;background:#fff;border:1px solid #ccc;border-radius:4px;display:flex;align-items:center;margin-bottom:%?20?%}.refund_progress .refund_progress-content .refund_progress-situation .situation__box-content .content__receiving-info .receiving__info-form .info__form-input .form__input[data-v-b41f5b88]{width:100%;height:100%;box-sizing:border-box;padding:0 %?19?%}.refund_progress .refund_progress-content .refund_progress-info[data-v-b41f5b88]{width:100%;margin-top:%?20?%;background:#fff;border-radius:%?20?%;box-sizing:border-box;padding:%?30?% %?21?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-title[data-v-b41f5b88]{font-size:%?32?%;font-weight:700;color:#333;margin-bottom:%?16?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods[data-v-b41f5b88]{width:100%;display:flex}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods .goods__img[data-v-b41f5b88]{width:%?180?%;height:%?180?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods .goods__img img[data-v-b41f5b88]{width:%?180?%;height:%?180?%;display:block;border-radius:%?20?%;border:1px solid #f2f2f2}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods .goods__info[data-v-b41f5b88]{margin-left:%?20?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods .goods__info .goods_info-text[data-v-b41f5b88]{font-size:%?28?%;font-weight:400;color:#333;line-height:42px}.refund_progress .refund_progress-content .refund_progress-info .info__box-goods .goods__info .goods__info-tips[data-v-b41f5b88]{font-size:%?24?%;font-weight:400;color:#999}.refund_progress .refund_progress-content .refund_progress-info .info__box-content[data-v-b41f5b88]{width:100%;margin-top:%?63?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box[data-v-b41f5b88]{width:100%;display:flex;margin-bottom:%?19?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box[data-v-b41f5b88]:last-of-type{margin-bottom:unset}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .content__box-text[data-v-b41f5b88]{width:%?191?%;font-size:%?28?%;font-weight:400;color:#666}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .content__box-value[data-v-b41f5b88]{flex:4;font-size:%?28?%;font-weight:400;color:#333}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .isred[data-v-b41f5b88]{font-size:%?28?%;font-weight:400;color:var(--custom-brand-color)}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .imgbox[data-v-b41f5b88]{display:flex}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .imgbox img[data-v-b41f5b88]{width:%?130?%;height:%?130?%;display:block;border:1px solid #f2f2f2;margin-right:%?20?%}.refund_progress .refund_progress-content .refund_progress-info .info__box-content .content__box .imgbox img[data-v-b41f5b88]:last-of-type{margin-right:unset}.refund_progress .refund_progress-btns[data-v-b41f5b88]{width:100%;height:%?98?%;background:#fff;display:flex;align-items:center;justify-content:flex-end;box-sizing:border-box;padding-right:%?24?%;position:fixed;bottom:0}.refund_progress .refund_progress-btns .default[data-v-b41f5b88]{border:1px solid #ccc;border-radius:%?32?%;padding:%?10?% %?28?%;font-size:%?26?%;font-weight:400;color:#666}.refund_progress .refund_progress-btns .submit[data-v-b41f5b88]{padding:%?10?% %?28?%;font-size:%?26?%;font-weight:400;color:#fff;background:linear-gradient(0deg,var(--custom-brand-color),var(--custom-brand-color-80));border-radius:%?32?%}.refund_progress .refund_progress-btns .btns[data-v-b41f5b88]{margin-right:%?20?%}.refund_progress .refund_progress-btns .btns[data-v-b41f5b88]:last-of-type{margin-right:unset}.uni-popup-popup[data-v-b41f5b88]{width:%?455?%;height:%?315?%;display:flex;flex-direction:column;justify-content:space-between;align-items:center;border-radius:%?15?%}.uni-popup-popup .uni-popup-box[data-v-b41f5b88]{width:100%;box-sizing:border-box;display:flex;flex-direction:column;flex:1;justify-content:center}.uni-popup-popup .uni-popup-box .uni-popup-title[data-v-b41f5b88]{width:100%;text-align:center;font-size:%?32?%;font-weight:700;color:#333;margin-bottom:%?30?%}.uni-popup-popup .uni-popup-box .uni-popup-content[data-v-b41f5b88]{width:100%;text-align:center;font-size:%?28?%;font-weight:400;color:#666}.uni-popup-popup .uni-popup-btns[data-v-b41f5b88]{width:100%;display:flex;border-top:1px solid #f2f2f2}.uni-popup-popup .uni-popup-btns .btn[data-v-b41f5b88]{flex:1;text-align:center;padding:%?15?% 0}.uni-popup-popup .uni-popup-btns .right[data-v-b41f5b88]{border-right:1px solid #f2f2f2}.overHiden[data-v-b41f5b88]{position:fixed;top:0;bottom:0;width:100%;height:100%;overflow:hidden}.overHidenH5[data-v-b41f5b88]{position:fixed;top:%?0?%;bottom:0;width:100%;height:100%;overflow:hidden}.progress-info[data-v-b41f5b88]{display:flex}.progress-info .progress-btn[data-v-b41f5b88]{display:flex;align-items:center;justify-content:center;width:%?120?%;height:%?48?%;font-size:%?24?%;color:#666;border:1px solid #ccc;border-radius:%?24?%;margin-left:%?12?%}',""]),t.exports=e},2440:function(t,e,n){"use strict";n.r(e);var o=n("2fba"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},"26ab":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-59a24327]{width:100%;text-align:center}.uni-popup-father[data-v-59a24327] .uni-popup__wrapper{background:transparent!important}.uni-popup-father[data-v-59a24327] .uni-popup__wrapper .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0!important}.view[data-v-59a24327]{width:100%;height:%?68?%;background:#fff;border:1px solid #ccc;border-radius:4px}.view .content[data-v-59a24327]{height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box;padding:0 %?19?%;font-size:%?28?%;font-weight:400;color:#666}.view .content .placeholder[data-v-59a24327]{font-size:%?28?%;font-weight:400;color:#ccc}.view .uni-popup-popup[data-v-59a24327]{width:100%;box-sizing:border-box;padding:%?28?% %?30?%}.view .uni-popup-popup .popup-header[data-v-59a24327]{width:100%}.view .uni-popup-popup .popup-header .popup-close[data-v-59a24327]{width:100%;text-align:end;color:#ccc}.view .uni-popup-popup .popup-header .popup-title[data-v-59a24327]{width:100%;text-align:center;font-size:%?32?%;font-weight:700;color:#333}.view .uni-popup-popup .popup-content[data-v-59a24327]{width:100%;height:60vh;overflow:auto;-webkit-overflow-scrolling:touch}.view .uni-popup-popup .popup-content .popup-content-list[data-v-59a24327]{width:100%}.view .uni-popup-popup .popup-content .popup-content-list .popup-content-item[data-v-59a24327]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:%?10?% 0;border-bottom:1px solid #eee}.view .uni-popup-popup .popup-content .popup-content-list .popup-content-item .item-reason[data-v-59a24327]{font-size:%?28?%;font-weight:400;color:#333}.view .uni-popup-popup .popup-content .popup-content-list .popup-content-item .item-select-icon[data-v-59a24327]{font-size:%?40?%;color:#999}.view .uni-popup-popup .popup-content .popup-content-list .popup-content-item .item-select-icon .iconyuan_checked[data-v-59a24327]{color:var(--custom-brand-color)}.view .uni-popup-popup .popup-content .popup-content-list .popup-content-item .item-select-icon .icon[data-v-59a24327]{font-size:%?44?%}',""]),t.exports=e},"2d01":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2fba":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa"),n("fd3c"),n("5c47"),n("a1c1");var i=o(n("2d01")),s={mixins:[i.default],data:function(){return{dataInfo:{},popupType:1,popupTitle:"",order_goods_id:"",form:{refund_delivery_no:"",refund_delivery_company_id:"",refund_delivery_name:""},express_company_list:[],stepList:[],isCompanyInfoEdit:!1,overHiden:!1,loadFinsh:!1}},onLoad:function(t){this.order_goods_id=t.order_goods_id},onShow:function(){this.order_goods_id&&this.orderDetail()},computed:{STATUS_1:function(){return 1===Number(this.dataInfo.refund_status)},STATUS_2:function(){return 2===Number(this.dataInfo.refund_status)},STATUS_3:function(){return 3===Number(this.dataInfo.refund_status)},STATUS_4:function(){return 4===Number(this.dataInfo.refund_status)},STATUS_5:function(){return 5===Number(this.dataInfo.refund_status)},STATUS_6:function(){return 6===Number(this.dataInfo.refund_status)},STATUS_7:function(){return 7===Number(this.dataInfo.refund_status)},STATUS_8:function(){return 8===Number(this.dataInfo.refund_status)},STATUS_9:function(){return 9===Number(this.dataInfo.refund_status)},STATUS_10:function(){return 10===Number(this.dataInfo.refund_status)},STATUS_00:function(){return-1===Number(this.dataInfo.refund_status)},REFUND_TYPE_1:function(){return Number(4===this.dataInfo.refund_type)},REFUND_TYPE_2:function(){return Number(2===this.dataInfo.refund_type)},REFUND_TYPE_3:function(){return Number(3===this.dataInfo.refund_type)},COMPANY_NAME:function(){var t,e=this;return this.express_company_list.map((function(n){n.company_id==e.dataInfo.refund_delivery_company_id&&(t=n.company_name)})),t}},methods:{open:function(){1==this.popupType?this.popupTitle="确认撤销申请吗":this.popupTitle="确认收货吗",this.$refs["popup-enter-upoff"].open()},close:function(){this.$refs["popup-enter-upoff"].close()},enter:function(){switch(this.popupType){case 1:this.canleApply();break;case 2:this.confirmReceipt();break}},handleCanleEdit:function(){this.isCompanyInfoEdit=!0,this.form.refund_delivery_company_id=this.dataInfo.refund_delivery_company_id,this.form.refund_delivery_no=this.dataInfo.refund_delivery_no},handleEditApply:function(){this.$util.redirectTo("/otherpages/order/return_and_exchange/refund_form",{order_goods_id:this.dataInfo.order_goods_id,num:this.dataInfo.num,type:this.dataInfo.refund_type},"redirectTo")},afreshApply:function(){this.$util.redirectTo("/otherpages/order/return_and_exchange/select_service",{order_goods_id:this.dataInfo.order_goods_id,num:this.dataInfo.num},"redirectTo")},handleConfirm:function(){this.popupType=2,this.open()},confirmReceipt:function(){var t=this;this.close(),uni.showLoading({title:"加载中",mask:!0}),this.$api.sendRequest({url:this.$apiUrl.confirm,data:{order_goods_id:this.order_goods_id},success:function(e){0===e.code?t.$util.showToast({title:"已确认收货",mask:!0,success:function(){setTimeout((function(){t.$nextTick((function(){t.orderDetail()}))}),1500)}}):t.$util.showToast({title:e.message})},complete:function(){uni.hideLoading()}})},handleCanleApply:function(){this.popupType=1,this.open()},canleApply:function(){var t=this;this.close(),uni.showLoading({title:"加载中",mask:!0}),this.$api.sendRequest({url:this.$apiUrl.cancel,data:{order_goods_id:this.order_goods_id},success:function(e){uni.hideLoading(),0===e.code?t.$util.showToast({title:"已提交",mask:!0,success:function(){setTimeout((function(){t.$util.redirectTo("/pages/order/detail/detail",{order_id:t.dataInfo.order_id},"reLaunch")}),1500)}}):t.$util.showToast({title:e.message})},complete:function(){uni.hideLoading()}})},orderDetail:function(){var t=this;this.$api.sendRequest({url:this.$apiUrl.detail,data:{order_goods_id:this.order_goods_id},success:function(e){0===e.code?(t.dataInfo=e.data,t.express_company_list=e.data.express_company_list,t.stepList=e.data.steps.content,9==e.data.refund_status&&e.data.refund_delivery_company_id&&(t.form.refund_delivery_no=e.data.refund_delivery_no,t.form.refund_delivery_name=e.data.refund_delivery_name,t.form.refund_delivery_company_id=e.data.refund_delivery_company_id),uni.setNavigationBarTitle({title:4==t.dataInfo.refund_type?"申请仅退款":2==t.dataInfo.refund_type?"申请退货退款":"申请换货"})):t.$util.showToast({title:e.message}),t.loadFinsh=!0},complete:function(){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},submitCompanyInfo:function(){var t=this;return""==this.form.refund_delivery_no?(this.$util.showToast({title:"请填写快递单号"}),!1):this.form.refund_delivery_company_id?(uni.showLoading({title:"加载中",mask:!0}),void this.$api.sendRequest({url:this.$apiUrl.delivery,data:{order_goods_id:this.order_goods_id,refund_delivery_no:this.form.refund_delivery_no,refund_delivery_company_id:this.form.refund_delivery_company_id,refund_delivery_name:this.form.refund_delivery_name},success:function(e){uni.hideLoading(),0===e.code?t.$util.showToast({title:"已提交",mask:!0,success:function(e){setTimeout((function(){t.$nextTick((function(){t.orderDetail(),t.isCompanyInfoEdit=!1}))}),1500)}}):-10001===e.code&&(t.$util.showToast({title:e.message,mask:!0}),setTimeout((function(){t.$nextTick((function(){t.orderDetail(),t.isCompanyInfoEdit=!1}))}),2e3))}})):(this.$util.showToast({title:"请选择物流公司"}),!1)},companySelect:function(t){this.form.refund_delivery_name=t.company_name,this.form.refund_delivery_company_id=t.company_id},progressDetailFun:function(t,e){var n={company_id:t,delivery_no:e,refund:1};this.$util.redirectTo("/pages/order/logistics/logistics",n)},preViewImage:function(t,e){t.length&&uni.previewImage({urls:t,current:e})},checkstatusFunc:function(t,e){var n=this;uni.showLoading(),this.$api.sendRequest({url:this.$apiUrl.checkstatus,data:{order_goods_id:this.order_goods_id,action:e},success:function(e){uni.hideLoading(),0===e.code?t():(n.$util.showToast({title:e.message,mask:!0}),setTimeout((function(){n.$nextTick((function(){n.orderDetail()}))}),2e3))}})},copyText:function(t){this.$util.copy(t)},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))},openModel:function(){this.overHiden=!0},closeModel:function(){this.overHiden=!1}},onShareAppMessage:function(t){var e=this.getSharePageParams(),n=e.title,o=e.link,i=e.imageUrl;e.query;return this.$buriedPoint.pageShare(o,i,n)},watch:{"form.refund_delivery_no":{handler:function(t){var e=this;this.$nextTick((function(){e.form.refund_delivery_no=t.replace(/[\W]/g,"")}))}}}};e.default=s},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},"31e4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa"),n("fd3c");var o={props:{refund_delivery_company_id:{type:[String,Number],default:""},express_company_list:{type:Array,default:function(){return[]}}},watch:{refund_delivery_company_id:{immediate:!0,handler:function(t){var e=this;t&&this.express_company_list.map((function(n){t==n.company_id&&(e.company_name=n.company_name,e.$emit("companySelect",n))}))}}},data:function(){return{currentId:"",company_name:""}},methods:{open:function(){this.$refs["popup-select-upoff"].open(),this.$emit("openModel")},closeOp:function(){this.$refs["popup-select-upoff"].close()},change:function(t){t.show||this.$emit("closeModel")},select:function(t){this.company_name=t.company_name,this.currentId=t.company_id,this.$emit("companySelect",t),this.$refs["popup-select-upoff"].close()}}};e.default=o},3472:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return o}));var o={uniStep:n("6062").default,uniSelect:n("72c2").default,uniPopup:n("5e99").default,loadingCover:n("5510").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"refund_progress",class:t.overHiden?"weapp"==t.$util.getPlatform()?"overHiden":"overHidenH5":"",style:[t.themeColorVar]},[t.loadFinsh?[n("v-uni-view",{staticClass:"refund_progress-header"},[n("v-uni-view",{staticClass:"header__progress"},[n("v-uni-view",{staticClass:"header__progress-title"},[t._v(t._s(t.dataInfo.steps.title))]),n("v-uni-view",{staticClass:"header__progress-step"},[n("uni-step",{attrs:{dataList:t.stepList}})],1)],1)],1),n("v-uni-view",{staticClass:"refund_progress-content"},[t.STATUS_6&&t.REFUND_TYPE_3||t.STATUS_1||t.STATUS_10?t._e():n("v-uni-view",{staticClass:"refund_progress-situation"},[n("v-uni-view",{staticClass:"situation__box",class:t.STATUS_3||t.STATUS_00||t.STATUS_6?"bottom_unset":""},[t.STATUS_4?n("v-uni-view",{staticClass:"situation__box-title"},[t._v("商家已同意退货，请寄回商品并填写物流单号")]):t._e(),t.STATUS_9?n("v-uni-view",{staticClass:"situation__box-title"},[t._v("商家未收到退货,请检查物流信息重新提交")]):t._e(),(t.STATUS_8||t.STATUS_3)&&t.REFUND_TYPE_3?n("v-uni-view",{staticClass:"situation__box-title"},[t._v("商家已重新发货，请注意接收快递")]):t._e(),n("v-uni-view",{staticClass:"situation__box-content"},[(t.STATUS_8||t.STATUS_3)&&t.REFUND_TYPE_3?n("v-uni-view",{staticClass:"content-receiving"},[n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("物流公司")]),n("v-uni-view",{staticClass:"info__box-value"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_recept_delivery_name))])],1),n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("快递单号")]),n("v-uni-view",{staticClass:"info__box-value progress-info"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_recept_delivery_no)),n("v-uni-view",{staticClass:"progress-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.progressDetailFun(t.dataInfo.order_goods_aftersale.refund_recept_delivery_company_id,t.dataInfo.order_goods_aftersale.refund_recept_delivery_no)}}},[t._v("查看物流")])],1)],1)],1):t._e(),t.STATUS_9||t.STATUS_4||t.STATUS_5?n("v-uni-view",{staticClass:"content__receiving-info"},[n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("收件人")]),n("v-uni-view",{staticClass:"info__box-value"},[t._v(t._s(t.dataInfo.refund_name))])],1),n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("联系电话")]),n("v-uni-view",{staticClass:"info__box-value"},[t._v(t._s(t.dataInfo.refund_phone))])],1),n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("退货地址")]),n("v-uni-view",{staticClass:"info__box-value address-box"},[n("v-uni-view",{staticClass:"value__address",staticStyle:{flex:"1"}},[t._v(t._s(t.dataInfo.refund_address))]),n("v-uni-view",{staticClass:"value__copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copyText(t.dataInfo.refund_name+","+t.dataInfo.refund_phone+","+t.dataInfo.refund_address)}}},[t._v("复制")])],1)],1),t.STATUS_9||t.STATUS_4||t.isCompanyInfoEdit?n("v-uni-view",{staticClass:"receiving__info-form"},[n("v-uni-view",{staticClass:"info__form-input"},[n("v-uni-input",{staticClass:"form__input",attrs:{type:"number",placeholder:"请填写快递单号","placeholder-style":"color: #cccccc"},model:{value:t.form.refund_delivery_no,callback:function(e){t.$set(t.form,"refund_delivery_no",e)},expression:"form.refund_delivery_no"}})],1),n("uni-select",{attrs:{refund_delivery_company_id:t.form.refund_delivery_company_id,express_company_list:t.express_company_list},on:{openModel:function(e){arguments[0]=e=t.$handleEvent(e),t.openModel.apply(void 0,arguments)},closeModel:function(e){arguments[0]=e=t.$handleEvent(e),t.closeModel.apply(void 0,arguments)},"update:refund_delivery_company_id":function(e){arguments[0]=e=t.$handleEvent(e),t.$set(t.form,"refund_delivery_company_id",e)},companySelect:function(e){arguments[0]=e=t.$handleEvent(e),t.companySelect.apply(void 0,arguments)}}})],1):t._e(),t.STATUS_5&&!t.isCompanyInfoEdit?[n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("快递单号")]),n("v-uni-view",{staticClass:"info__box-value progress-info"},[n("v-uni-view",[t._v(t._s(t.dataInfo.refund_delivery_no))]),n("v-uni-view",{staticClass:"progress-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.progressDetailFun(t.dataInfo.refund_delivery_company_id,t.dataInfo.refund_delivery_no)}}},[t._v("查看物流")])],1)],1),n("v-uni-view",{staticClass:"receiving__info-box"},[n("v-uni-view",{staticClass:"info__box-text"},[t._v("物流公司")]),n("v-uni-view",{staticClass:"info__box-value"},[t._v(t._s(t.COMPANY_NAME))])],1)]:t._e()],2):t._e()],1)],1),t.STATUS_6?n("v-uni-view",{staticClass:"situation__box"},[n("v-uni-view",{staticClass:"situation__box-title"},[t._v("商家已同意，待平台审核退款")]),n("v-uni-view",{staticClass:"situation__box-content"},[n("v-uni-view",{staticClass:"content-receiving"},[t._v("平台会在2个工作日内审核退款，退款金额将按原路\n              退回支付账号，如有疑问也可通过客服咨询。")])],1)],1):t._e(),(t.STATUS_6||t.STATUS_7)&&String(t.dataInfo.admin_remark).length?n("v-uni-view",{staticClass:"situation__box"},[n("v-uni-view",{staticClass:"situation__box-title"},[t._v("商家备注")]),n("v-uni-view",{staticClass:"situation__box-content"},[n("v-uni-view",{staticClass:"content-receiving"},[t._v(t._s(t.dataInfo.admin_remark))])],1)],1):t._e(),t.STATUS_00?n("v-uni-view",{staticClass:"situation__box"},[n("v-uni-view",{staticClass:"situation__box-title"},[t._v("以下是驳回原因，您可重新申请或请平台介入")]),n("v-uni-view",{staticClass:"situation__box-content"},[n("v-uni-view",{staticClass:"content-receiving"},[t._v(t._s(t.dataInfo.refund_refuse_reason))])],1)],1):t._e(),t.STATUS_3&&t.REFUND_TYPE_2?n("v-uni-view",{staticClass:"situation__box"},[n("v-uni-view",{staticClass:"situation__box-title"},[t._v("平台已退款，请留意退款信息")]),n("v-uni-view",{staticClass:"situation__box-content"},[n("v-uni-view",{staticClass:"content-receiving"},[t._v("退款金额已按原路退回支付账号，请留意退款信息，\n              如有疑问也可通过客服咨询。")])],1)],1):t._e()],1),n("v-uni-view",{staticClass:"refund_progress-info"},[n("v-uni-view",{staticClass:"info__box-title"},[t._v(t._s(t.REFUND_TYPE_1?"退款":t.REFUND_TYPE_2?"退货":"换货")+"信息")]),n("v-uni-view",{staticClass:"info__box-goods"},[n("v-uni-view",{staticClass:"goods__img"},[n("img",{attrs:{src:t.$util.img(t.dataInfo.sku_image),alt:""}})]),n("v-uni-view",{staticClass:"goods__info"},[n("v-uni-view",{staticClass:"goods__info-text"},[t._v(t._s(t.dataInfo.sku_name))]),n("v-uni-view",{staticClass:"goods__info-tips"},[t._v(t._s(t.dataInfo.spec_name))])],1)],1),n("v-uni-view",{staticClass:"info__box-content"},[n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v(t._s(t.REFUND_TYPE_1||t.REFUND_TYPE_2?"退款":"换货")+"编号")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.refund_no||"无"))])],1),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("申请原因")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.refund_reason||"无"))])],1),t.REFUND_TYPE_1?n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("货物状态")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.goods_status_name||"无"))])],1):t._e(),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("购买数量")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.num||"无"))])],1),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("申请数量")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_num||"无"))])],1),t.REFUND_TYPE_3?t._e():n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v(t._s("0"==String(t.dataInfo.is_maidou_pay)?"退款金额":"退回迈豆"))]),n("v-uni-view",{staticClass:"content__box-value isred"},[t._v(t._s("0"==String(t.dataInfo.is_maidou_pay)?"￥"+t.dataInfo.refund_apply_money:t.dataInfo.refund_apply_money+"迈豆")+t._s("0"==String(t.dataInfo.is_maidou_pay)?parseFloat(t.dataInfo.refund_delivery_money)>0?"(包含运费"+t.dataInfo.refund_delivery_money+")":"（不包含运费）":""))])],1),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("申请时间")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.apply_time||"无"))])],1),t.REFUND_TYPE_3?n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("收货地址")]),n("v-uni-view",{staticClass:"content__box-value"},[n("v-uni-view",{staticClass:"value_name"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_recept_name))]),n("v-uni-view",{staticClass:"value_iphone"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_recept_phone))]),n("v-uni-view",{staticClass:"value_address"},[t._v(t._s(t.dataInfo.order_goods_aftersale.refund_recept_address))])],1)],1):t._e(),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("补充描述")]),n("v-uni-view",{staticClass:"content__box-value"},[t._v(t._s(t.dataInfo.refund_remark||"无"))])],1),n("v-uni-view",{staticClass:"content__box"},[n("v-uni-view",{staticClass:"content__box-text"},[t._v("上传凭证")]),t.dataInfo.order_goods_aftersale.upload_vouchers.length?n("v-uni-view",{staticClass:"content__box-value imgbox"},t._l(t.dataInfo.order_goods_aftersale.upload_vouchers,(function(e,o){return n("img",{key:o,attrs:{src:e,alt:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.preViewImage(t.dataInfo.order_goods_aftersale.upload_vouchers,o)}}})})),0):n("v-uni-view",{staticClass:"content__box-value"},[t._v("无")])],1)],1)],1)],1),n("v-uni-view",{staticClass:"refund_progress-btns"},[n("v-uni-view",{staticClass:"btns default",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.getCustomerService()}}},[t._v("客服介入")]),t.STATUS_1?n("v-uni-view",{staticClass:"btns default",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checkstatusFunc(t.handleCanleApply,"cancel")}}},[t._v("撤销申请")]):t._e(),t.STATUS_1?n("v-uni-view",{staticClass:"btns submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checkstatusFunc(t.handleEditApply,"apply")}}},[t._v("修改申请")]):t._e(),t.STATUS_10||t.STATUS_00?n("v-uni-view",{staticClass:"btns submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.afreshApply.apply(void 0,arguments)}}},[t._v("重新申请")]):t._e(),t.isCompanyInfoEdit?n("v-uni-view",{staticClass:"btns default",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.isCompanyInfoEdit=!1}}},[t._v("取消修改")]):t._e(),t.STATUS_4||t.STATUS_5&&t.isCompanyInfoEdit||t.STATUS_9?n("v-uni-view",{staticClass:"btns submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitCompanyInfo.apply(void 0,arguments)}}},[t._v("提交物流信息")]):t._e(),t.STATUS_5&&!t.isCompanyInfoEdit?n("v-uni-view",{staticClass:"btns submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checkstatusFunc(t.handleCanleEdit,"edit_delivery")}}},[t._v("修改物流信息")]):t._e(),t.STATUS_8?n("v-uni-view",{staticClass:"btns submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleConfirm.apply(void 0,arguments)}}},[t._v("确认收货")]):t._e()],1)]:t._e(),n("uni-popup",{ref:"popup-enter-upoff",staticClass:"uni-popup-father",attrs:{type:"center"}},[n("v-uni-view",{staticClass:"uni-popup-popup"},[n("v-uni-view",{staticClass:"uni-popup-box"},[n("v-uni-view",{staticClass:"uni-popup-title"},[t._v("提示")]),n("v-uni-view",{staticClass:"uni-popup-content"},[t._v(t._s(t.popupTitle))])],1),n("v-uni-view",{staticClass:"uni-popup-btns"},[n("v-uni-view",{staticClass:"btn right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}},[t._v("取消")]),n("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.enter.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)],1),n("loading-cover",{ref:"loadingCover"})],2)},s=[]},"35a5":function(t,e,n){"use strict";var o=n("de0a"),i=n.n(o);i.a},"461c":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-163c2366]{width:100%;text-align:center}.step__content[data-v-163c2366]{width:100%;box-sizing:border-box;padding-top:%?10?%}.step__content .step__progress[data-v-163c2366]{width:100%;display:flex;box-sizing:border-box;padding:0 %?56?%}.step__content .step__progress .step__progress-box[data-v-163c2366]{padding:%?10?% 0;display:flex;justify-content:center;position:relative}.step__content .step__progress .step__progress-box .step__progress-spot[data-v-163c2366]{width:%?14?%;height:%?14?%;background:#fff;border-radius:50%;border:%?4?% solid var(--custom-brand-color);position:absolute;top:0}.step__content .step__progress .step__progress-box .isCurrent-spot[data-v-163c2366]{background:var(--custom-brand-color)}.step__content .step__progress .step__progress-box .noArrived-spot[data-v-163c2366]{width:%?14?%;height:%?14?%;background:#ccc;border:%?4?% solid #ccc}.step__content .step__progress .step__progress-box .step__progress-line[data-v-163c2366]{width:%?124?%;height:%?3?%;background:#ccc;border-radius:2px}.step__content .step__progress .step__progress-box .isArrived-line[data-v-163c2366]{background:var(--custom-brand-color)}.step__content .step__text[data-v-163c2366]{width:100%;display:flex;justify-content:space-around;margin-top:%?10?%}.step__content .step__text .step__text-name[data-v-163c2366]{font-size:%?24?%!important;font-weight:400;color:#333}',""]),t.exports=e},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,o.default)(t)};var o=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},6062:function(t,e,n){"use strict";n.r(e);var o=n("fd34"),i=n("ee985");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("c851");var r=n("828b"),a=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"163c2366",null,!1,o["a"],void 0);e["default"]=a.exports},"72c2":function(t,e,n){"use strict";n.r(e);var o=n("0a00"),i=n("0a66");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("d630");var r=n("828b"),a=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"59a24327",null,!1,o["a"],void 0);e["default"]=a.exports},ae48:function(t,e,n){var o=n("26ab");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("1865f564",o,!0,{sourceMap:!1,shadowMode:!1})},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,o.default)(t)||(0,i.default)(t)||(0,s.default)(t)||(0,r.default)()};var o=a(n("4733")),i=a(n("d14d")),s=a(n("5d6b")),r=a(n("30f7"));function a(t){return t&&t.__esModule?t:{default:t}}},c851:function(t,e,n){"use strict";var o=n("e567"),i=n.n(o);i.a},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},d630:function(t,e,n){"use strict";var o=n("ae48"),i=n.n(o);i.a},de0a:function(t,e,n){var o=n("2171");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("49e48ab4",o,!0,{sourceMap:!1,shadowMode:!1})},de25:function(t,e,n){"use strict";n.r(e);var o=n("3472"),i=n("2440");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("35a5");var r=n("828b"),a=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"b41f5b88",null,!1,o["a"],void 0);e["default"]=a.exports},e567:function(t,e,n){var o=n("461c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("25605377",o,!0,{sourceMap:!1,shadowMode:!1})},ee985:function(t,e,n){"use strict";n.r(e);var o=n("1819c"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},fd34:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"step__content"},[n("v-uni-view",{staticClass:"step__progress"},[n("v-uni-view",{staticClass:"step__progress-box"},[t._l(t.dataList,(function(e,o){return n("v-uni-view",{key:o,staticClass:"step__progress-spot",class:"white"==e.status?"":"black"==e.status?"isCurrent-spot":"noArrived-spot",style:"left: "+t.margin*o+"rpx"})})),t._l(t.DATALIST_SPLICE,(function(e,o){return n("v-uni-view",{key:t.getKey(o),staticClass:"step__progress-line",class:"gray"!=e.status?"isArrived-line":"",style:"left: "+t.margin*(o+1)+"rpx"})}))],2)],1),n("v-uni-view",{staticClass:"step__text"},t._l(t.dataList,(function(e,o){return n("v-uni-view",{key:o,staticClass:"step__text-name"},[t._v(t._s(e.status_text))])})),1)],1)},i=[]}}]);