(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-join-contribution_list-contribution_list"],{"0a80":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("f7a5");var a=n(i("2634")),o=n(i("2fdc")),r=i("4b89"),s=n(i("d817")),l=n(i("2d01")),c=n(i("de74")),u=n(i("2f73")),d=n(i("172f")),v=n(i("85bf")),h={name:"contribution_list",components:{UniNavBar:s.default,UniIcons:c.default,diyShareNavigateH5:u.default,mpHtml:d.default},mixins:[l.default],data:function(){return{title:"我的贡献值",is_show_page:!1,shop_id:null,shareTitle:"",shareImg:"",is_permissions:!0,rule_html:"",league_1:{point:0,auto_complete_nums:0,wait_complete_task:[]},isOnXianMaiApp:r.isOnXianMaiApp}},onLoad:function(){},onShow:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,v.default.wait_staticLogin_success();case 3:return t.shop_id=uni.getStorageSync("shop_id"),e.next=6,t.getDiyInfo();case 6:return e.next=8,t.getData();case 8:return i=t.$util.deepClone(t.getSharePageParams()),n=window.location.origin+t.$router.options.base+i.link.slice(1),i.link=n,e.next=13,t.$util.publicShare(i);case 13:case"end":return e.stop()}}),e)})))()},methods:{getData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.xmLeaguePointsUrl,async:!1,data:{}});case 3:i=e.sent,t.$refs.loadingCover&&t.$refs.loadingCover.hide(),-20001==i.code?uni.showModal({content:"该活动仅限受邀内测用户参与，暂未对外开放。",showCancel:!1,confirmText:"知道了",confirmColor:"var(--custom-brand-color)",success:function(t){t.confirm&&this.$util.goBack()}}):0==i.code?(t.league_1=Object.assign(t.league_1,i.data.league_1),t.league_1.enable&&(t.is_permissions=!0),t.is_show_page=!0):t.$util.showToast(i.message),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.$util.showToast(e.t0.message);case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()},getDiyInfo:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:"/api/diyview/info",async:!1,data:{name:"DIYVIEW_SHOP_INDEX",site_id:t.shop_id||0}});case 2:i=e.sent,i.data&&(n=JSON.parse(i.data.value),t.shareTitle=n.global.shareTitle,t.shareImg=n.global.shareImg);case 4:case"end":return e.stop()}}),e)})))()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))},openSharePopup:function(){var t=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(t)},showRuleDetail:function(){this.rule_html=this.league_1.rule_content,this.$refs.rulePopupRef.open()},to_tip:function(){this.$refs.tipPopup.open()},toDetail:function(){this.$util.redirectTo("/otherpages/member/join/contribution_records/contribution_records")},toRedeemMall:function(){this.$util.redirectTo("/otherpages/member/join/redeem_mall/redeem_mall")}},onShareAppMessage:function(t){var e=this.getSharePageParams(),i=e.title,n=e.link,a=e.imageUrl;e.query;return this.$buriedPoint.pageShare(n,a,i)},onShareTimeline:function(t){var e=this.getSharePageParams(),i=e.title,n=e.imageUrl,a=e.query;return{title:i,imageUrl:n,query:a,success:function(t){},fail:function(t){}}}};e.default=h},"1c85":function(t,e,i){"use strict";i.r(e);var n=i("0a80"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"2d01":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"580e":function(t,e,i){"use strict";i.r(e);var n=i("ac60"),a=i("1c85");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("7d374");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"5a11163c",null,!1,n["a"],void 0);e["default"]=s.exports},"58d3":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5a11163c]{width:100%;text-align:center}[data-v-5a11163c] .uni-navbar__header-btns-left{width:%?200?%!important}.contribution-list[data-v-5a11163c]{position:relative;min-height:100vh;background:linear-gradient(180deg,var(--custom-brand-color),var(--custom-brand-color-20));box-sizing:border-box}.contribution-list .bg-img[data-v-5a11163c]{width:%?750?%;height:%?422?%;background-repeat:no-repeat;background-size:100% 100%;position:absolute;top:0;left:0}.contribution-list .container[data-v-5a11163c]{position:relative}.contribution-list .container .month-value[data-v-5a11163c]{width:%?702?%;height:%?250?%;background:#fff;border-radius:%?16?%;margin:0 auto;display:flex;align-items:center;justify-content:space-between;padding:0 %?64?%;box-sizing:border-box}.contribution-list .container .month-value-left-title[data-v-5a11163c]{font-size:%?32?%;font-weight:400;line-height:%?40?%;color:rgba(56,56,56,.8);display:flex;align-items:center}.contribution-list .container .month-value-left-title[data-v-5a11163c] .uni-icons{margin-left:%?12?%}.contribution-list .container .month-value-left-value[data-v-5a11163c]{font-size:%?70?%;font-weight:700;line-height:%?50?%;color:var(--custom-brand-color);margin-top:%?32?%}.contribution-list .container .month-value-left-value-unit[data-v-5a11163c]{font-size:%?32?%;font-weight:400;line-height:%?40?%;margin-left:%?10?%}.contribution-list .container .month-value-right[data-v-5a11163c]{display:flex;flex-direction:column;align-items:center}.contribution-list .container .month-value-right-detail[data-v-5a11163c]{display:block;font-size:%?28?%;font-weight:400;height:%?60?%;line-height:%?60?%;color:var(--custom-brand-color);width:%?160?%;text-align:center;background-color:var(--custom-brand-color-10);border-radius:%?30?%}.contribution-list .container .month-value-right-redeem[data-v-5a11163c]{display:block;font-size:%?28?%;font-weight:400;width:%?160?%;height:%?60?%;line-height:%?60?%;color:#fff;background:linear-gradient(335deg,var(--custom-brand-color),var(--custom-brand-color-60));border-radius:%?30?%;text-align:center;margin-top:%?24?%}.contribution-list .container .earning-method[data-v-5a11163c]{width:%?702?%;background:#fff;border-radius:%?16?%;margin:0 auto;margin-top:%?24?%;padding:0 %?24?%;box-sizing:border-box}.contribution-list .container .earning-method-header[data-v-5a11163c]{display:flex;align-items:center;box-sizing:border-box;height:%?88?%}.contribution-list .container .earning-method-header-icon[data-v-5a11163c]{width:%?40?%;height:%?40?%;margin-right:%?12?%}.contribution-list .container .earning-method-header-title[data-v-5a11163c]{font-size:%?32?%;font-weight:400;line-height:%?40?%;color:rgba(56,56,56,.8)}.contribution-list .container .earning-method-list[data-v-5a11163c]{padding-bottom:%?54?%;box-sizing:border-box}.contribution-list .container .earning-method-list-one[data-v-5a11163c]{display:flex;justify-content:space-between;align-items:center}.contribution-list .container .earning-method-list-one[data-v-5a11163c]:not(:first-child){margin-top:%?54?%}.contribution-list .container .earning-method-list-one-left-title[data-v-5a11163c]{font-size:%?32?%;font-weight:700;color:#383838;margin:0}.contribution-list .container .earning-method-list-one-left-desc[data-v-5a11163c]{margin:0;margin-top:%?14?%;font-size:%?24?%;font-weight:400;color:grey}.contribution-list .container .earning-method-list-one-left-desc uni-text[data-v-5a11163c]{color:var(--custom-brand-color)}.contribution-list .container .earning-method-list-one-right-op[data-v-5a11163c]{width:%?164?%;height:%?72?%;border-radius:%?100?%;background:linear-gradient(180deg,var(--custom-brand-color),var(--custom-brand-color-70));font-size:%?30?%;font-weight:400;letter-spacing:%?4?%;color:#fff;display:flex;justify-content:center;align-items:center;margin:0}.contribution-list .container .not-tip[data-v-5a11163c]{margin:0 auto;width:%?702?%;min-height:%?364?%;background-color:#fff;border-radius:%?20?%;padding:%?100?% %?24?% %?60?% %?24?%;box-sizing:border-box;margin-top:%?20?%;display:flex;flex-direction:column;align-items:center;position:relative}.contribution-list .container .not-tip-icon[data-v-5a11163c]{width:%?240?%;height:%?240?%}.contribution-list .container .not-tip-text[data-v-5a11163c]{font-size:%?32?%;font-weight:400;color:grey;width:%?310?%;text-align:center;margin-top:%?24?%}.contribution-list .rule[data-v-5a11163c]{width:100vw;position:fixed;left:0;bottom:0;background-color:#fff;height:calc(%?76?% + env(safe-area-inset-bottom));padding:0 %?24?%;padding-bottom:env(safe-area-inset-bottom);box-sizing:border-box;display:flex;justify-content:space-between;align-items:center;font-size:%?30?%;font-weight:400}.contribution-list .rule-left[data-v-5a11163c]{color:var(--custom-brand-color)}.contribution-list .rule-right[data-v-5a11163c]{display:flex;justify-content:center;align-items:center;color:var(--custom-brand-color)}.contribution-list .rule-right-img[data-v-5a11163c]{width:%?28?%;height:%?28?%;margin-left:%?10?%}.contribution-list .rule-detail[data-v-5a11163c]{padding:%?60?% %?24?% 0 %?24?%;box-sizing:border-box;white-space:pre-line}.contribution-list .rule-close[data-v-5a11163c]{width:%?24?%;height:%?24?%;position:absolute;right:%?32?%;top:%?24?%}.contribution-list .tip-popup-content[data-v-5a11163c]{padding:%?40?% %?24?% %?60?% %?24?%;box-sizing:border-box;display:flex;flex-direction:column;align-items:center}.contribution-list .tip-popup-content-title[data-v-5a11163c]{font-size:%?32?%;font-weight:700;color:#383838;margin-bottom:%?24?%;text-align:center}.contribution-list .tip-popup-content-desc[data-v-5a11163c]{font-size:%?28?%;font-weight:400;color:#383838;line-height:%?40?%;margin-bottom:%?30?%;text-align:center}.contribution-list .tip-popup-content-op[data-v-5a11163c]{width:%?400?%;height:%?80?%;border-radius:%?40?%;background:var(--custom-brand-color);font-size:%?32?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}',""]),t.exports=e},"7d374":function(t,e,i){"use strict";var n=i("dac3"),a=i.n(n);a.a},ac60:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uniNavBar:i("d817").default,uniIcons:i("de74").default,uniPopup:i("5e99").default,mpHtml:i("172f").default,diyShareNavigateH5:i("2f73").default,loadingCover:i("5510").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"contribution-list",style:[t.themeColorVar],attrs:{"data-theme":t.themeStyle}},[i("uni-nav-bar",{attrs:{"left-icon":"back",border:!1,fixed:!0,statusBar:!0,backgroundColor:"transparent",leftText:t.title,color:"#fff"},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.goBack.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"bg-img",style:{backgroundImage:"url("+t.$util.img("public/static/youpin/member/contribution/beijing.png")+")"}}),t.is_show_page?[i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticClass:"month-value"},[i("v-uni-view",{staticClass:"month-value-left"},[i("v-uni-view",{staticClass:"month-value-left-title"},[t._v("本月贡献值"),i("uni-icons",{attrs:{type:"info-filled",size:"16",color:"rgba(0,0,0,0.3)"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.to_tip.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"month-value-left-value"},[t._v(t._s(t.league_1.point)),i("v-uni-text",{staticClass:"month-value-left-value-unit"},[t._v("分")])],1)],1),i("v-uni-view",{staticClass:"month-value-right"},[i("v-uni-text",{staticClass:"month-value-right-detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail.apply(void 0,arguments)}}},[t._v("查看明细")]),i("v-uni-text",{staticClass:"month-value-right-redeem",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRedeemMall.apply(void 0,arguments)}}},[t._v("兑换商城")])],1)],1),t.is_permissions?[t.league_1.rules&&t.league_1.rules.length>0?i("v-uni-view",{staticClass:"earning-method"},[i("v-uni-view",{staticClass:"earning-method-header"},[i("v-uni-image",{staticClass:"earning-method-header-icon",attrs:{src:t.$util.img("public/static/youpin/member/contribution/point-shop.png"),alt:""}}),i("v-uni-text",{staticClass:"earning-method-header-title"},[t._v("赚取贡献值方式")])],1),i("v-uni-view",{staticClass:"earning-method-list"},[t._l(t.league_1.rules,(function(e,n){return["recommend_register"==e.rule_key&&1==e.enable?i("v-uni-view",{staticClass:"earning-method-list-one"},[i("v-uni-view",{staticClass:"earning-method-list-one-left"},[i("v-uni-view",{staticClass:"earning-method-list-one-left-title"},[t._v("邀请新好友注册")]),i("v-uni-view",{staticClass:"earning-method-list-one-left-desc"},[t._v("每邀请一位新用户即可获得"),i("v-uni-text",[t._v(t._s(e.rule_val))]),t._v("贡献值")],1)],1),i("v-uni-view",{staticClass:"earning-method-list-one-right"},["weapp"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",attrs:{"open-type":"share"}},[t._v("邀请")]):t._e(),"h5"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("邀请")]):t._e()],1)],1):t._e(),"add_shop_fans"==e.rule_key&&1==e.enable?i("v-uni-view",{staticClass:"earning-method-list-one"},[i("v-uni-view",{staticClass:"earning-method-list-one-left"},[i("v-uni-view",{staticClass:"earning-method-list-one-left-title"},[t._v("店铺绑定粉丝增长")]),i("v-uni-view",{staticClass:"earning-method-list-one-left-desc"},[t._v("店铺每新增一位粉丝可获得"),i("v-uni-text",[t._v(t._s(e.rule_val))]),t._v("贡献值")],1)],1),i("v-uni-view",{staticClass:"earning-method-list-one-right"},["weapp"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",attrs:{"open-type":"share"}},[t._v("分享")]):t._e(),"h5"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("分享")]):t._e()],1)],1):t._e(),"recommend_browse"==e.rule_key&&1==e.enable?i("v-uni-view",{staticClass:"earning-method-list-one"},[i("v-uni-view",{staticClass:"earning-method-list-one-left"},[i("v-uni-view",{staticClass:"earning-method-list-one-left-title"},[t._v("好友商品浏览数增长")]),i("v-uni-view",{staticClass:"earning-method-list-one-left-desc"},[t._v("好友每"),i("v-uni-text",[t._v(t._s(e.rule_nums))]),t._v("人浏览可得"),i("v-uni-text",[t._v(t._s(e.rule_val))]),t._v("贡献值")],1)],1),i("v-uni-view",{staticClass:"earning-method-list-one-right"},["weapp"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",attrs:{"open-type":"share"}},[t._v("分享")]):t._e(),"h5"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("分享")]):t._e()],1)],1):t._e(),"shop_fans_browse"==e.rule_key&&1==e.enable?i("v-uni-view",{staticClass:"earning-method-list-one"},[i("v-uni-view",{staticClass:"earning-method-list-one-left"},[i("v-uni-view",{staticClass:"earning-method-list-one-left-title"},[t._v("粉丝商品浏览数增长")]),i("v-uni-view",{staticClass:"earning-method-list-one-left-desc"},[t._v("店铺粉丝每"),i("v-uni-text",[t._v(t._s(e.rule_nums))]),t._v("人浏览可得"),i("v-uni-text",[t._v(t._s(e.rule_val))]),t._v("贡献值")],1)],1),i("v-uni-view",{staticClass:"earning-method-list-one-right"},["weapp"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",attrs:{"open-type":"share"}},[t._v("分享")]):t._e(),"h5"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("分享")]):t._e()],1)],1):t._e(),"recommend_shop"==e.rule_key&&1==e.enable?i("v-uni-view",{staticClass:"earning-method-list-one"},[i("v-uni-view",{staticClass:"earning-method-list-one-left"},[i("v-uni-view",{staticClass:"earning-method-list-one-left-title"},[t._v("好友升级店主")]),i("v-uni-view",{staticClass:"earning-method-list-one-left-desc"},[t._v("每位直推好友升级可获得"),i("v-uni-text",[t._v(t._s(e.rule_val))]),t._v("贡献值")],1)],1),i("v-uni-view",{staticClass:"earning-method-list-one-right"},["weapp"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",attrs:{"open-type":"share"}},[t._v("邀请")]):t._e(),"h5"==t.$util.getPlatform()?i("v-uni-button",{staticClass:"earning-method-list-one-right-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[t._v("邀请")]):t._e()],1)],1):t._e()]}))],2)],1):i("v-uni-view",{staticClass:"not-tip"},[i("v-uni-image",{staticClass:"not-tip-icon",attrs:{src:t.$util.img("public/static/youpin/member/contribution/not_config.png"),alt:""}}),i("v-uni-view",{staticClass:"not-tip-text"},[t._v("暂未配置贡献值方式")])],1)]:[i("v-uni-view",{staticClass:"not-tip"},[i("v-uni-image",{staticClass:"not-tip-icon",attrs:{src:t.$util.img("public/static/youpin/member/contribution/not_permissions.png"),alt:""}}),i("v-uni-view",{staticClass:"not-tip-text"},[t._v("您的活动权限已关闭无法获得新的贡献值")])],1)]],2),i("v-uni-view",{staticClass:"rule",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showRuleDetail.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"rule-left"},[t._v("赚贡献值，自动完成加盟任务")]),i("v-uni-view",{staticClass:"rule-right"},[t._v("查看详情"),i("v-uni-image",{staticClass:"rule-right-img",attrs:{src:t.$util.img("public/static/youpin/member/contribution/rule-more.png"),alt:""}})],1)],1)]:t._e(),i("uni-popup",{ref:"rulePopupRef",attrs:{type:"bottom","bottom-radius":!0}},[i("mp-html",{staticClass:"rule-detail",attrs:{content:t.rule_html,"preview-img":!0}}),i("v-uni-image",{staticClass:"rule-close",attrs:{src:t.$util.img("public/static/youpin/clase-x.png"),alt:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.rulePopupRef.close()}}})],1),i("uni-popup",{ref:"tipPopup",staticClass:"tip-popup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"tip-popup-content"},[i("v-uni-view",{staticClass:"tip-popup-content-desc"},[t._v("用户获得贡献值时，将优先用于完成任意加盟单任务一。如未使用，也可以用于兑换优惠券等其他活动。")]),i("v-uni-view",{staticClass:"tip-popup-content-desc"},[t._v("贡献值本月有效，下月1号零点之前贡献值自动失效。")]),i("v-uni-button",{staticClass:"tip-popup-content-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.tipPopup.close()}}},[t._v("我知道了")])],1)],1),i("diy-share-navigate-h5",{ref:"shareNavigateH5"}),i("loading-cover",{ref:"loadingCover"})],2)},o=[]},dac3:function(t,e,i){var n=i("58d3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("b09eac04",n,!0,{sourceMap:!1,shadowMode:!1})}}]);