(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-questionnaire-qform-qform"],{"18ab":function(t,e,i){"use strict";var a=i("87cc"),n=i.n(a);n.a},2637:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniNavBar:i("d817").default,uniPopup:i("5e99").default,loadingCover:i("5510").default,ydAuthPopup:i("161f").default,nsLogin:i("4f5a").default,diyShareNavigateH5:i("2f73").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:t.themeStyle},[t.isOnXianMaiApp?i("uni-nav-bar",{attrs:{"left-icon":"back",border:!1},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[[i("v-uni-view",{staticClass:"page-title"},[t._v(t._s(t.title))])]],2):t._e(),t.isShowPage?i("v-uni-view",{staticClass:"container"},[i("v-uni-image",{staticClass:"header-image",attrs:{src:t.image,mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.rulePopupRef.open()}}}),i("v-uni-view",{staticClass:"container-form"},[t._l(t.list,(function(e,a){return["text"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-input",{staticClass:"uni-input container-form-item-input",attrs:{type:"text",placeholder:e.describe,"placeholder-class":"container-form-item-input-placeholder"},model:{value:e.value,callback:function(i){t.$set(e,"value",i)},expression:"item.value"}})],1):t._e(),"number"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-input",{staticClass:"uni-input container-form-item-input",attrs:{type:"number",placeholder:e.describe,"placeholder-class":"container-form-item-input-placeholder"},model:{value:e.value,callback:function(i){t.$set(e,"value",i)},expression:"item.value"}})],1):t._e(),"radio"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-radio-group",{on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.radioChange(e,a)}}},[e.select_data.filter((function(t){return!!t.image})).length>0?i("v-uni-view",{staticClass:"container-form-item-radio-img"},t._l(e.select_data,(function(e,n){return i("v-uni-view",{key:n,staticClass:"container-form-item-radio-img-one"},[i("v-uni-image",{staticClass:"container-form-item-radio-img-one-img",attrs:{src:e.image,mode:"aspectFit"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRadioChange(a,n)}}}),i("v-uni-view",{staticClass:"container-form-item-radio-img-one-bottom"},[i("v-uni-view",{staticClass:"container-form-item-radio-img-one-bottom-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRadioChange(a,n)}}},[t._v(t._s(e.value))]),i("v-uni-radio",{attrs:{value:e.value,borderColor:"rgba(196, 196, 196, 1)",color:"rgba(246, 93, 114, 1)",checked:e.is_select}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-form-item-radio-text"},t._l(e.select_data,(function(e,a){return i("v-uni-view",{key:a,staticClass:"container-form-item-radio-text-one"},[i("v-uni-radio",{staticClass:"container-form-item-radio-text-one-control",attrs:{value:e.value,borderColor:"rgba(196, 196, 196, 1)",color:"rgba(246, 93, 114, 1)",checked:e.is_select}},[i("v-uni-text",{staticClass:"container-form-item-radio-text-one-text"},[t._v(t._s(e.value))])],1)],1)})),1)],1)],1):t._e(),"checkbox"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-checkbox-group",{on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.checkboxChange(e,a)}}},[e.select_data.filter((function(t){return!!t.image})).length>0?i("v-uni-view",{staticClass:"container-form-item-radio-img"},t._l(e.select_data,(function(e,n){return i("v-uni-view",{key:n,staticClass:"container-form-item-radio-img-one"},[i("v-uni-image",{staticClass:"container-form-item-radio-img-one-img",attrs:{src:e.image,mode:"aspectFit"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCheckboxChange(a,n)}}}),i("v-uni-view",{staticClass:"container-form-item-radio-img-one-bottom"},[i("v-uni-view",{staticClass:"container-form-item-radio-img-one-bottom-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCheckboxChange(a,n)}}},[t._v(t._s(e.value))]),i("v-uni-checkbox",{attrs:{value:e.value,borderColor:"rgba(196, 196, 196, 1)",activeBackgroundColor:"rgba(246, 93, 114, 1)",activeBorderColor:"rgba(246, 93, 114, 1)",checked:e.is_select}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-form-item-radio-text"},t._l(e.select_data,(function(e,a){return i("v-uni-view",{key:a,staticClass:"container-form-item-radio-text-one"},[i("v-uni-checkbox",{staticClass:"container-form-item-radio-text-one-control",attrs:{value:e.value,borderColor:"rgba(196, 196, 196, 1)",activeBackgroundColor:"rgba(246, 93, 114, 1)",activeBorderColor:"rgba(246, 93, 114, 1)",checked:e.is_select}},[i("v-uni-text",{staticClass:"container-form-item-radio-text-one-text"},[t._v(t._s(e.value))])],1)],1)})),1)],1)],1):t._e(),"textarea"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-textarea",{staticClass:"uni-input container-form-item-textarea",attrs:{type:"text",placeholder:e.describe,"placeholder-class":"container-form-item-textarea-placeholder"},model:{value:e.value,callback:function(i){t.$set(e,"value",i)},expression:"item.value"}})],1):t._e(),"datetime"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-picker",{attrs:{mode:"time",value:e.value},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindTimeChange(e,a)}}},[i("v-uni-view",{staticClass:"uni-input container-form-item-datetime",class:{"container-form-item-datetime-placeholder":!e.value},attrs:{type:"number"}},[t._v(t._s(e.value?e.value:e.describe))])],1)],1):t._e(),"date"==e.type?i("v-uni-view",{staticClass:"container-form-item"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.name)),1==e.is_require?i("v-uni-view",{staticClass:"required"},[i("v-uni-text",[t._v("*")]),t._v("（必填）")],1):t._e()],1),i("v-uni-picker",{attrs:{mode:"date",value:e.value},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindDateChange(e,a)}}},[i("v-uni-view",{staticClass:"uni-input container-form-item-date",class:{"container-form-item-date-placeholder":!e.value},attrs:{type:"number"}},[t._v(t._s(e.value?e.value:e.describe))])],1)],1):t._e()]}))],2),t.bottom_image?i("v-uni-view",{staticClass:"container-qrcode",style:{"background-image":"url("+this.$util.img("public/static/youpin/questionnaire/qrcode_bg.png")+")"}},[i("v-uni-image",{staticClass:"container-qrcode-img",attrs:{src:t.bottom_image,"show-menu-by-longpress":!0}}),i("v-uni-view",{staticClass:"container-qrcode-text"},[t._v("长按识别二维码有惊喜")])],1):t._e(),i("v-uni-view",{staticClass:"container-footer"},[i("v-uni-view",{staticClass:"container-footer-left"},[i("v-uni-button",{staticClass:"container-footer-left-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backToHome.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"container-footer-left-op-img",attrs:{src:t.$util.img("public/static/youpin/questionnaire/home.png")}}),i("v-uni-text",{staticClass:"container-footer-left-op-text"},[t._v("首页")])],1),i("v-uni-button",{staticClass:"container-footer-left-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toShare.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"container-footer-left-op-img",attrs:{src:t.$util.img("public/static/youpin/questionnaire/share.png")}}),i("v-uni-text",{staticClass:"container-footer-left-op-text"},[t._v("分享")])],1)],1),i("v-uni-view",{staticClass:"container-footer-right"},[i("v-uni-button",{staticClass:"container-footer-right-op container-footer-right-records",attrs:{type:"warn"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRecords.apply(void 0,arguments)}}},[t._v("查看记录")]),i("v-uni-button",{staticClass:"container-footer-right-op container-footer-right-submit",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitData.apply(void 0,arguments)}}},[t._v("提交")])],1)],1)],1):t._e(),i("uni-popup",{ref:"exitConfirmationRef",attrs:{maskClick:!1}},[i("v-uni-view",{staticClass:"exit-confirmation"},[i("v-uni-view",{staticClass:"exit-confirmation-title"},[t._v("确认退出？")]),i("v-uni-view",{staticClass:"exit-confirmation-desc"},[t._v("您参与的问卷活动暂未完成提交是否确认退出？")]),i("v-uni-view",{staticClass:"exit-confirmation-op"},[i("v-uni-text",{staticClass:"exit-confirmation-op-cancel",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmationCancel.apply(void 0,arguments)}}},[t._v("取消")]),i("v-uni-text",{staticClass:"exit-confirmation-op-back",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toBack.apply(void 0,arguments)}}},[t._v("退出")])],1)],1)],1),i("uni-popup",{ref:"tipPopupRef",attrs:{maskClick:!1}},[i("v-uni-view",{staticClass:"tip-popup"},[i("v-uni-view",{staticClass:"tip-popup-title"},[t._v(t._s("invalid"==t.tip_type?"活动已过期":"不符合参与要求"))]),i("v-uni-view",{staticClass:"tip-popup-desc"},[t._v(t._s("invalid"==t.tip_type?"该活动到期结束或分享链接已失效":"您未符合本活动的参与条件，无法填写问卷。"))]),i("v-uni-image",{staticClass:"tip-popup-img",attrs:{src:t.$util.img("public/static/youpin/questionnaire/invalid.png")}}),i("v-uni-view",{staticClass:"tip-popup-back",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backToHome.apply(void 0,arguments)}}},[t._v("返回首页")])],1)],1),i("uni-popup",{ref:"resultPopupRef",attrs:{maskClick:!1}},[i("v-uni-view",{staticClass:"result-popup"},[i("v-uni-view",{staticClass:"result-popup-title"},[t._v("提交成功")]),i("v-uni-view",{staticClass:"result-popup-desc"},[t._v("感谢您的参与")]),i("v-uni-image",{staticClass:"result-popup-img",attrs:{src:t.bottom_image||t.$util.img("public/static/youpin/questionnaire/questionnaire.png"),"show-menu-by-longpress":!0}}),i("v-uni-view",{staticClass:"result-popup-tip"},[t._v("长按识别二维码")]),i("v-uni-view",{staticClass:"result-popup-op"},[i("v-uni-text",{staticClass:"result-popup-op-left",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backToHome.apply(void 0,arguments)}}},[t._v("返回首页")]),i("v-uni-text",{staticClass:"result-popup-op-right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRecords.apply(void 0,arguments)}}},[t._v("查看记录")])],1)],1)],1),i("uni-popup",{ref:"rulePopupRef",attrs:{type:"bottom","bottom-radius":!0}},[i("v-uni-view",{staticClass:"rule"},[i("v-uni-view",{staticClass:"rule-header"},[i("v-uni-text",{staticClass:"rule-header-text"},[t._v("活动规则")]),i("v-uni-text",{staticClass:"iconfont iconroundclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.rulePopupRef.close()}}})],1),i("mphtml",{attrs:{content:t.content,"preview-img":!0}})],1)],1),i("loading-cover",{ref:"loadingCover"}),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"}),i("diy-share-navigate-h5",{ref:"shareNavigateH5"})],1)},o=[]},4196:function(t,e,i){"use strict";i.r(e);var a=i("2637"),n=i("e2de");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("18ab"),i("e453"),i("42bc");var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"089ca442",null,!1,a["a"],void 0);e["default"]=s.exports},"42bc":function(t,e,i){"use strict";var a=i("adfd"),n=i.n(a);n.a},"84ec":function(t,e,i){var a=i("a4fb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("2261db75",a,!0,{sourceMap:!1,shadowMode:!1})},"87cc":function(t,e,i){var a=i("d04c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("7b2ead6a",a,!0,{sourceMap:!1,shadowMode:!1})},a4fb:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-089ca442]{width:100%;text-align:center}[data-v-089ca442] .uni-picker-action-confirm{color:#f65d72}.weui-btn_primary[data-v-089ca442]{background-color:#ff0}',""]),t.exports=e},adfd:function(t,e,i){var a=i("f035");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("323a62d4",a,!0,{sourceMap:!1,shadowMode:!1})},d04c:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"[data-v-089ca442] .uni-navbar{position:fixed;z-index:999}",""]),t.exports=e},e2de:function(t,e,i){"use strict";i.r(e);var a=i("f829"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},e453:function(t,e,i){"use strict";var a=i("84ec"),n=i.n(a);n.a},f035:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-089ca442]{width:100%;text-align:center}[data-v-089ca442] .uni-popup__wrapper-box{border-radius:%?40?%!important}[data-v-089ca442] .uni-radio-input-checked{background-color:#f65d72!important}[data-v-089ca442] .uni-checkbox-input.uni-checkbox-input-checked{color:#fff!important}[data-v-089ca442] uni-checkbox .wx-checkbox-input.wx-checkbox-input-checked{color:#fff;background-color:#f65d72!important;border-color:#f65d72!important}.header-image[data-v-089ca442]{width:%?750?%;height:auto;min-height:%?216?%}.container[data-v-089ca442]{min-height:100vh;padding-bottom:calc(%?220?% + env(safe-area-inset-bottom));box-sizing:border-box;background:#fafafa}.container-form[data-v-089ca442]{width:%?712?%;border-radius:%?20?%;margin:0 auto;margin-top:%?-216?%;position:relative;padding-top:%?56?%;box-sizing:border-box}.container-form-item[data-v-089ca442]{padding:%?40?% %?24?%;background-color:#fff;border-radius:%?20?%}.container-form-item[data-v-089ca442]:not(:first-child){box-sizing:border-box;margin-top:%?18?%}.container-form-item .title[data-v-089ca442]{font-size:%?32?%;font-weight:700;color:#1a1a1a;display:flex;align-items:center;margin-bottom:%?20?%}.container-form-item .title .required[data-v-089ca442]{font-size:%?28?%;font-weight:400;color:grey;margin-left:%?10?%}.container-form-item .title .required uni-text[data-v-089ca442]{color:#f65d72}.container-form-item-input[data-v-089ca442]{height:%?96?%;border-radius:%?20?%;background:#fafafa;padding:0 %?20?%;box-sizing:border-box}.container-form-item-input-placeholder[data-v-089ca442]{font-size:%?32?%;font-weight:400;color:#a6a6a6}.container-form-item-datetime[data-v-089ca442]{height:%?96?%;border-radius:%?20?%;background:#fafafa;padding:0 %?20?%;box-sizing:border-box;display:flex;align-items:center}.container-form-item-datetime-placeholder[data-v-089ca442]{font-size:%?32?%;font-weight:400;color:#a6a6a6}.container-form-item-date[data-v-089ca442]{height:%?96?%;border-radius:%?20?%;background:#fafafa;padding:0 %?20?%;box-sizing:border-box;display:flex;align-items:center}.container-form-item-date-placeholder[data-v-089ca442]{font-size:%?32?%;font-weight:400;color:#a6a6a6}.container-form-item-radio-text-one[data-v-089ca442]{margin-top:%?28?%;font-size:%?32?%;font-weight:400;color:#383838;display:flex;align-items:center}.container-form-item-radio-text-one-control[data-v-089ca442]{width:100%}.container-form-item-radio-text-one-text[data-v-089ca442]{margin-left:%?22?%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;word-break:break-all;display:inline-block;vertical-align:bottom;width:%?580?%}.container-form-item-radio-img[data-v-089ca442]{display:flex;flex-wrap:wrap;justify-content:space-between}.container-form-item-radio-img-one[data-v-089ca442]{width:%?320?%;height:%?480?%;background:#fafafa;border-radius:%?20?%;margin-bottom:%?24?%}.container-form-item-radio-img-one-img[data-v-089ca442]{width:%?320?%;height:%?320?%;border-radius:%?20?% %?20?% 0 0}.container-form-item-radio-img-one-bottom[data-v-089ca442]{display:flex;flex-direction:column;align-items:center;padding:0 %?20?%}.container-form-item-radio-img-one-bottom-text[data-v-089ca442]{margin-bottom:%?12?%;height:%?64?%;font-size:%?32?%;font-weight:400;color:#383838;line-height:%?32?%;text-align:center;width:100%;word-break:break-all;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.container-form-item-textarea[data-v-089ca442]{width:100%;border-radius:%?20?%;background:#fafafa;padding:%?35?% %?24?%;box-sizing:border-box}.container-form-item-textarea-placeholder[data-v-089ca442]{font-size:%?30?%;font-weight:400;color:#a6a6a6}.container-qrcode[data-v-089ca442]{width:%?712?%;height:%?293?%;border-radius:%?20?%;background-repeat:no-repeat;background-size:100% 100%;margin:0 auto;margin-top:%?20?%;display:flex;align-items:center;padding-left:%?32?%;box-sizing:border-box}.container-qrcode-img[data-v-089ca442]{width:%?241?%;height:%?241?%}.container-qrcode-text[data-v-089ca442]{width:%?220?%;font-weight:400;color:#383838;margin-left:%?40?%}.container-footer[data-v-089ca442]{background-color:#fff;display:flex;justify-content:space-between;align-items:center;width:100vw;padding:%?20?%;box-sizing:border-box;padding-bottom:calc(%?20?% + env(safe-area-inset-bottom));position:fixed;left:0;bottom:0;z-index:10}.container-footer-left[data-v-089ca442]{display:flex;align-items:center}.container-footer-left-op[data-v-089ca442]{display:flex;flex-direction:column;align-items:center;background-color:initial;margin:0;padding:0}.container-footer-left-op[data-v-089ca442]:last-child{margin-left:%?34?%}.container-footer-left-op-img[data-v-089ca442]{width:%?44?%;height:%?44?%}.container-footer-left-op-text[data-v-089ca442]{font-size:%?24?%;font-weight:400;color:#000;display:block}.container-footer-right[data-v-089ca442]{display:flex;align-items:center}.container-footer-right-op[data-v-089ca442]{width:%?260?%;height:%?72?%;border-radius:%?100?%;display:flex;justify-content:center;align-items:center;margin:0;padding:0}.container-footer-right-op[data-v-089ca442]:last-child{margin-left:%?12?%}.container-footer-right-records[data-v-089ca442]{color:#f65d72;background-color:rgba(246,93,114,.1);border-color:rgba(246,93,114,.1)!important}.container-footer-right-submit[data-v-089ca442]{color:#fff;background-color:#f65d72!important}.exit-confirmation[data-v-089ca442]{width:%?601?%;height:%?457?%;border-radius:%?40?%;background:linear-gradient(0deg,hsla(0,0%,100%,0),#ffebed);padding:%?40?%;box-sizing:border-box}.exit-confirmation-title[data-v-089ca442]{font-size:%?40?%;font-weight:700;color:#383838;text-align:center;margin-top:%?60?%}.exit-confirmation-desc[data-v-089ca442]{font-size:%?28?%;font-weight:400;color:grey;width:%?393?%;text-align:center;margin:0 auto;margin-top:%?30?%}.exit-confirmation-op[data-v-089ca442]{display:flex;align-items:center;justify-content:space-between;margin-top:%?40?%}.exit-confirmation-op-cancel[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:rgba(246,93,114,.1);font-size:%?28?%;color:#f65d72;display:flex;justify-content:center;align-items:center}.exit-confirmation-op-back[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:#f65d72;font-size:%?28?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.tip-popup[data-v-089ca442]{width:%?601?%;border-radius:%?40?%;background:linear-gradient(0deg,hsla(0,0%,100%,0),#ffebed);padding:%?40?%;box-sizing:border-box}.tip-popup-title[data-v-089ca442]{font-size:%?40?%;font-weight:700;color:#383838;margin-top:%?46?%;text-align:center}.tip-popup-desc[data-v-089ca442]{font-size:%?28?%;font-weight:400;color:grey;text-align:center}.tip-popup-img[data-v-089ca442]{width:%?301?%;height:%?249?%;display:block;margin:0 auto;margin-top:%?68?%}.tip-popup-op[data-v-089ca442]{display:flex;justify-content:space-between;align-items:center;margin-top:%?60?%}.tip-popup-op-left[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:rgba(246,93,114,.1);font-size:%?28?%;font-weight:400;color:#f65d72;display:flex;justify-content:center;align-items:center}.tip-popup-op-right[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:#f65d72;font-size:%?28?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.tip-popup-back[data-v-089ca442]{width:%?521?%;height:%?72?%;border-radius:%?200?%;background:#f65d72;font-size:%?28?%;font-weight:400;color:#fff;margin:0 auto;margin-top:%?60?%;display:flex;justify-content:center;align-items:center}.result-popup[data-v-089ca442]{width:%?601?%;border-radius:%?40?%;background:linear-gradient(0deg,hsla(0,0%,100%,0),#ffebed);padding:%?40?%;box-sizing:border-box}.result-popup-title[data-v-089ca442]{font-size:%?40?%;font-weight:700;color:#383838;margin-top:%?46?%;text-align:center}.result-popup-desc[data-v-089ca442]{font-size:%?28?%;font-weight:400;color:grey;text-align:center}.result-popup-img[data-v-089ca442]{width:%?281?%;height:%?281?%;display:block;margin:0 auto;margin-top:%?30?%}.result-popup-tip[data-v-089ca442]{font-size:%?28?%;font-weight:400;color:grey;text-align:center}.result-popup-op[data-v-089ca442]{display:flex;justify-content:space-between;align-items:center;margin-top:%?40?%}.result-popup-op-left[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:rgba(246,93,114,.1);font-size:%?28?%;font-weight:400;color:#f65d72;display:flex;justify-content:center;align-items:center}.result-popup-op-right[data-v-089ca442]{width:%?248?%;height:%?72?%;border-radius:%?200?%;background:#f65d72;font-size:%?28?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.page-title[data-v-089ca442]{width:%?360?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;text-align:center;color:#fff}.rule[data-v-089ca442]{width:100vw;padding:0 %?40?% %?40?% %?40?%;box-sizing:border-box}.rule-header[data-v-089ca442]{display:flex;justify-content:space-between;align-items:center;height:%?80?%;background-color:#fff;box-sizing:border-box;position:-webkit-sticky;position:sticky;top:0;z-index:1}.rule-header-text[data-v-089ca442]{font-size:%?36?%;font-weight:700;color:#383838}.rule-header .iconfont[data-v-089ca442]{font-size:%?32?%;color:#c2c2c2}',""]),t.exports=e},f829:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("4626"),i("5ac7"),i("8f71"),i("bf0f"),i("aa9c"),i("d4b5"),i("f7a5");var n=a(i("2634")),o=a(i("2fdc")),r=a(i("85bf")),s=a(i("5e99")),c=a(i("f8de")),l=a(i("2f73")),u=a(i("d817")),d=a(i("7c8d")),f=a(i("172f")),p=i("4b89"),v={name:"qform",components:{uniPopup:s.default,diyShareNavigateH5:l.default,uniNavBar:u.default,mphtml:f.default},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},mixins:[c.default],data:function(){return{title:"",share_title:"",questionnaire_id:null,submit_id:null,isShowPage:!1,content:"",list:[],image:"",bottom_image:"",tip_type:"invalid",isOnXianMaiApp:p.isOnXianMaiApp}},onLoad:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,r.default.wait_staticLogin_success();case 2:return e.questionnaire_id=t.questionnaire_id||null,e.submit_id=t.submit_id||null,i.next=6,e.getData();case 6:case"end":return i.stop()}}),i)})))()},onShow:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r.default.wait_staticLogin_success();case 2:t.checkLogin();case 3:case"end":return e.stop()}}),e)})))()},onReady:function(){this.setWechatShare()},methods:{toBack:function(){this.appGoBack()},goBack:function(){this.checkInputHasContent()?this.$refs.exitConfirmationRef.open():this.toBack()},confirmationCancel:function(){this.$refs.exitConfirmationRef.close()},getData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i,a,o,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,i={questionnaire_id:t.questionnaire_id},a=d.default.questionnaireActivityFormUrl,o=uni.getStorageSync("recommend_member_id"),o&&(i.share_member_id=o),t.submit_id&&(i.submit_id=t.submit_id,a=d.default.questionnaireActivityEditDetailUrl),e.next=8,t.$api.sendRequest({url:a,async:!1,data:i});case 8:r=e.sent,t.$refs.loadingCover&&t.$refs.loadingCover.hide(),-20002==r.code||(-20003==r.code?(t.tip_type="invalid",t.$refs.tipPopupRef.open()):-20004==r.code?(t.tip_type="ineligible",t.$refs.tipPopupRef.open()):0==r.code?(t.share_title=r.data.share_title,t.image=r.data.image,t.bottom_image=r.data.bottom_image,t.list=r.data.column_data,t.content=r.data.content,uni.setNavigationBarTitle({title:r.data.name}),t.isShowPage=!0):t.$util.showToast({title:r.message})),e.next=15;break;case 13:e.prev=13,e.t0=e["catch"](0);case 15:case"end":return e.stop()}}),e,null,[[0,13]])})))()},radioChange:function(t,e){var i=this.list[e];i.select_data=i.select_data.map((function(e){return e.value==t.detail.value?e.is_select=!0:e.is_select=!1,e})),this.list[e]=i},checkboxChange:function(t,e){var i=this.list[e];i.select_data=i.select_data.map((function(e){return t.detail.value.includes(e.value)?e.is_select=!0:e.is_select=!1,e})),this.list[e]=i},onCheckboxChange:function(t,e){var i=this.list[t];i.select_data=i.select_data.map((function(t,i){return e==i&&(t.is_select=!t.is_select),t})),this.list[t]=i},onRadioChange:function(t,e){var i=this.list[t];i.select_data=i.select_data.map((function(t,i){return t.is_select=e==i,t})),this.list[t]=i},bindDateChange:function(t,e){var i=this.list[e];i.value=t.detail.value,this.$set(this.list,e,i)},bindTimeChange:function(t,e){var i=this.list[e];i.value=t.detail.value,this.$set(this.list,e,i)},checkInputHasContent:function(){var t=this.list.filter((function(t){return t.value instanceof Array?t.value.length>1:!!t.value}));return t.length>0},verifyData:function(){var t=this.list.filter((function(t){return 1==t.is_require&&(["radio","checkbox"].includes(t.type)?t.select_data.filter((function(t){return t.is_select}))<1:!t.value)}));return t.length<1},submitData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i,a,o,r;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.checkLogin()){e.next=2;break}return e.abrupt("return",!1);case 2:if(t.verifyData()){e.next=5;break}return t.$util.showToast({title:"有必填项未填写完成！"}),e.abrupt("return",!1);case 5:return uni.showLoading({title:"提交中..."}),i=d.default.questionnaireActivitySubmitUrl,a={questionnaire_id:t.questionnaire_id,data:[]},o=uni.getStorageSync("recommend_member_id"),o&&(a.share_member_id=o),t.submit_id&&(a.submit_id=t.submit_id,i=d.default.questionnaireActivityEditUrl),t.list.map((function(t){var e="";return e=["radio","checkbox"].includes(t.type)?t.select_data.filter((function(t){return t.is_select})).map((function(t){return t.value})).join("/"):t.value,a.data.push({name:t.name,value:e}),t})),a.data=JSON.stringify(a.data),e.prev=13,e.next=16,t.$api.sendRequest({url:i,async:!1,data:a});case 16:if(r=e.sent,uni.hideLoading(),-20002!=r.code){e.next=21;break}e.next=27;break;case 21:if(0==r.code){e.next=26;break}return t.$util.showToast({title:r.message}),e.abrupt("return",!1);case 26:t.$refs.resultPopupRef.open();case 27:e.next=31;break;case 29:e.prev=29,e.t0=e["catch"](13);case 31:case"end":return e.stop()}}),e,null,[[13,29]])})))()},toRecords:function(){if(this.$refs.resultPopupRef.close(),!this.checkLogin())return!1;this.$util.redirectTo("/promotionpages/questionnaire/records/records?questionnaire_id=".concat(this.questionnaire_id))},notAuthentication:function(){this.$refs.tipPopupRef.close()},toAuthentication:function(){this.$util.redirectTo("/otherpages/member/real_name_authentication/real_name_authentication")},backToHome:function(){this.$util.redirectTo("/otherpages/shop/home/<USER>",{},"reLaunch")},checkLogin:function(){return!!uni.getStorageSync("token")||(this.toLogin(),!1)},toLogin:function(){var t="/promotionpages/questionnaire/qform/qform?questionnaire_id=".concat(this.questionnaire_id);this.$util.toShowLoginPopup(this,null,t)},getSharePageParams:function(){var t={questionnaire_id:this.questionnaire_id};return this.$util.unifySharePageParams("/promotionpages/questionnaire/qform/qform",this.share_title||"先迈商城","",t,this.image)},setWechatShare:function(){var t=this.$util.deepClone(this.getSharePageParams()),e=window.location.origin+this.$router.options.base+t.link.slice(1);t.link=e,this.$util.publicShare(t)},toShare:function(){var t=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(t)}},onShareAppMessage:function(t){var e=this.getSharePageParams(),i=e.title,a=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,n,i)}};e.default=v},f8de:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),n={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){a.isOnXianMaiApp&&this.appCurrentPages<=1?(0,a.goClosePage)("0"):uni.navigateBack()}}};e.default=n}}]);