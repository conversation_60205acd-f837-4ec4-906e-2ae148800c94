(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-live-list-liveList~promotionpages-pintuan-share-share"],{"014b":function(t,e,a){"use strict";a.r(e);var i=a("9d90"),n=a("c08b");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("91e0"),a("a9cf");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"847fc110",null,!1,i["a"],void 0);e["default"]=s.exports},"5c37":function(t,e,a){var i=a("7858");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("730aaec3",i,!0,{sourceMap:!1,shadowMode:!1})},7858:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-847fc110]{width:100%;text-align:center}.share-popup .share-title[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-title[data-v-847fc110]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center;background:#f5f5f5}.share-popup .share-content[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content[data-v-847fc110]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;background:#f5f5f5}.share-popup .share-content .share-box[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-847fc110]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-847fc110]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-847fc110]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-847fc110]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-847fc110]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-847fc110],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-847fc110]{color:#07c160}.share-popup .share-footer[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-footer[data-v-847fc110]{height:%?88?%;line-height:%?88?%;border-top:%?2?% #f5f5f5 solid;text-align:center;color:#666}.canvas[data-v-847fc110]{width:%?620?%;height:%?917?%;margin:0 auto;overflow:hidden;position:fixed;left:100%}.poster[data-v-847fc110]{display:flex;justify-content:center}@-webkit-keyframes spin-data-v-847fc110{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-847fc110{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-847fc110]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-847fc110]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-847fc110]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-847fc110]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-847fc110]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-847fc110 .6s linear normal infinite;animation:spin-data-v-847fc110 .6s linear normal infinite}.loading-anim .in[data-v-847fc110]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-847fc110 .8s linear infinite;animation:spin-data-v-847fc110 .8s linear infinite}.loading-anim .mid[data-v-847fc110]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-847fc110 .6s linear infinite;animation:spin-data-v-847fc110 .6s linear infinite}',""]),t.exports=e},"908b":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-847fc110] .uni-popup__wrapper.bottom{background:none!important}[data-v-847fc110] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0}",""]),t.exports=e},"91e0":function(t,e,a){"use strict";var i=a("9404"),n=a.n(i);n.a},9404:function(t,e,a){var i=a("908b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("e5810390",i,!0,{sourceMap:!1,shadowMode:!1})},"9d90":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[t.imagePath?t._e():a("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width*t.canvasOptions.scale+"px",height:t.canvasOptions.height*t.canvasOptions.scale+"px",borderRadius:t.canvasOptions.borderRadius*t.canvasOptions.scale},attrs:{"canvas-id":"myCanvas"}})],1)},n=[]},a9cf:function(t,e,a){"use strict";var i=a("5c37"),n=a.n(i);n.a},be88:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("aa9c");var i={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0,scale:1}}},data:function(){return{ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,ctx_share:null,height:0,windowHeight:0,windowWidth:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.ctx=uni.createCanvasContext("myCanvas",this),this.ctx.scale(this.canvasOptions.scale,this.canvasOptions.scale)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var a=0;a<this.drawPathQueue.length;a++)for(var i=0;i<this.drawPathQueue.length;i++){var n=this.drawPathQueue[i];if(n.index===a){if("text"===n.type){if(this.ctx.setFillStyle(n.color||"#000"),this.ctx.setFontSize(n.size*this.myPx),n.textBaseline){var o=n.textBaselineColor||"#999999";this.ctx.strokeStyle=o,this.ctx.moveTo(n.x-4,n.y-8),this.ctx.lineTo(n.x+this.ctx.measureText(n.text).width+4,n.y-8),this.ctx.stroke(),this.ctx.textBaseline=n.textBaseline}if(n.width&&n.text){for(var r=n.text.split(""),s="",h=[],c=n.width,p=n.lineNum||1,u=n.lineHeight||20,l=0;l<r.length;l++)this.ctx.measureText(s).width<c&&this.ctx.measureText(s+r[l]).width<=c?(s+=r[l],l==r.length-1&&h.push(s)):(h.push(s),s=r[l]);if(p<=h.length)for(var f=0;f<p;f++)n.fontWeight?(this.ctx.fillText(h[f],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(h[f],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(h[f],n.x*this.myPx,n.y*this.myPx),n.y=n.y+u;else for(var d=0;d<h.length;d++)n.fontWeight?(this.ctx.fillText(h[d],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(h[d],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(h[d],n.x*this.myPx,n.y*this.myPx),n.y=n.y+u}else n.fontWeight?(this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(n.text,n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx);this.counter--}"image"===n.type&&(n.path?this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx):(this.ctx.fillStyle=n.background,this.ctx.fillRect(n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx)),this.counter--)}}},counter:function(t,e){var a=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",destWidth:a.canvasOptions.width,destHeight:a.canvasOptions.height,success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){a.imagePath=t.savedFilePath,a.$emit("childByValue",t.savedFilePath)}})},fail:function(t){console.log("err",t)}},a)}),100))}},methods:{open:function(){this.imagePath||this.generateImg()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var a=t.drawQueue[e];if(a.index=e,"text"===a.type)return t.drawPathQueue.push(a),"continue";console.log("current.path====",a.path),a.path?uni.getImageInfo({src:a.path,success:function(e){a.path=e.path,t.drawPathQueue.push(a)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(a)},a=0;a<this.drawQueue.length;a++)e(a)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})}}};e.default=i},c08b:function(t,e,a){"use strict";a.r(e);var i=a("be88"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a}}]);