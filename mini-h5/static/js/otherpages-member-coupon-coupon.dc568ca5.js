(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-coupon-coupon"],{"1ea8":function(t,e,o){"use strict";o.r(e);var i=o("e3dd"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"2d01":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"520e":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6fafaf02]{width:100%;text-align:center}.coupoon[data-v-6fafaf02]{position:relative}.status-box[data-v-6fafaf02]{position:fixed;left:0;right:0;top:0;display:flex;height:%?100?%;background-color:#fff;color:#333;font-weight:400;z-index:1}.status-box .status-list[data-v-6fafaf02]{position:relative;display:flex;align-items:center;justify-content:center;flex:1}.status-box .active[data-v-6fafaf02]{font-weight:700}.status-box .active[data-v-6fafaf02]::after{position:absolute;bottom:%?20?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);content:"";width:%?36?%;height:%?8?%;background-color:var(--custom-brand-color);border-radius:%?6?%}.coupon-box[data-v-6fafaf02]{padding:%?120?% %?20?% %?110?%}.coupon-box .coupon-list[data-v-6fafaf02]{display:flex;height:%?202?%;background-size:%?702?% %?200?%;background-position:50%;background-repeat:no-repeat;margin-bottom:%?20?%}.coupon-box .coupon-list .coupon-left[data-v-6fafaf02]{display:flex;justify-content:center;align-items:center;flex-shrink:0;width:%?218?%}.coupon-box .coupon-list .coupon-left > uni-view > uni-text[data-v-6fafaf02]:first-child{font-size:%?28?%;font-weight:700}.coupon-box .coupon-list .coupon-left > uni-view > uni-text[data-v-6fafaf02]:last-child{font-size:%?52?%;font-weight:700}.coupon-box .coupon-list .coupon-right[data-v-6fafaf02]{position:relative;flex:1;padding:%?22?% %?24?% %?28?%}.coupon-box .coupon-list .coupon-right .coupon-name[data-v-6fafaf02]{font-size:%?30?%;line-height:%?42?%;color:#333;font-weight:700}.coupon-box .coupon-list .coupon-right .coupon-time[data-v-6fafaf02]{position:absolute;bottom:%?64?%;color:#999;font-size:%?22?%;line-height:%?42?%}.coupon-box .coupon-list .coupon-right .use-btn[data-v-6fafaf02]{background:var(--custom-brand-color);text-align:center;position:absolute;width:%?120?%;height:%?48?%;color:#fff;font-size:%?24?%;line-height:%?48?%;border-radius:%?24?%;right:%?24?%;bottom:%?16?%}.coupon-box .coupon-list .coupon-right .disabled[data-v-6fafaf02]{border:1px solid #ccc;background-color:#fff;color:#999}.coupon-box .coupon-list .coupon-right uni-image[data-v-6fafaf02]{position:absolute;right:%?16?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?40?%;height:%?40?%}.empty-box[data-v-6fafaf02]{display:flex;flex-direction:column;align-items:center}.empty-box uni-image[data-v-6fafaf02]{width:%?402?%;height:%?282?%;margin:%?208?% 0 %?42?%}.empty-box .empty-info[data-v-6fafaf02]{display:flex;align-items:center;justify-content:center;height:100%;font-size:%?32?%;line-height:%?44?%}',""]),t.exports=e},"666f":function(t,e,o){var i=o("520e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("591befbe",i,!0,{sourceMap:!1,shadowMode:!1})},"6a5a":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{page:1,page_size:10,showLoading:!1,scrollLoading:!1}},methods:{hideLoading:function(){this.showLoading=!1}},onReachBottom:function(){if(this.scrollLoading)return!1;this.showLoading=!0,this.getInit()}}},b440:function(t,e,o){"use strict";o.r(e);var i=o("f602"),n=o("1ea8");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("c62c");var s=o("828b"),u=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"6fafaf02",null,!1,i["a"],void 0);e["default"]=u.exports},c62c:function(t,e,o){"use strict";var i=o("666f"),n=o.n(i);n.a},e3dd:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("e966"),o("c223");var n=i(o("6a5a")),a=i(o("2d01")),s={mixins:[n.default,a.default],data:function(){return{list:[],statusList:[{status:"1",name:"已领取",btn:"去使用"},{status:"2",name:"已使用",btn:"已使用"},{status:"3",name:"已过期",btn:"已过期"}],selectIndex:"1",barHeight:0}},onLoad:function(t){this.selectIndex=t.selectIndex||"1"},onShow:function(){this.reset()},methods:{reset:function(){uni.showLoading({title:"加载中"}),this.page=1,this.list=[],this.scrollLoading=!1,this.getInit()},getInit:function(){var t=this;this.$api.sendRequest({url:this.$apiUrl.memberpage,data:{page:this.page,page_size:this.page_size,state:parseInt(this.selectIndex),token:uni.getStorageSync("token")},success:function(e){if(uni.hideLoading(),0==e.code){var o=e.data.list;o.length?(t.page++,t.list=t.list.concat(o),o.length<t.page_size&&(t.scrollLoading=!0)):t.scrollLoading=!0}t.hideLoading()},fail:function(t){uni.hideLoading()}})},toGoodList:function(t){if(3==t.state)return!1;this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),o=e.title,i=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,n,o)}};e.default=s},f602:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={nsLoading:o("6b09").default},n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"coupon",style:[t.themeColorVar]},[o("v-uni-view",{staticClass:"status-box"},t._l(t.statusList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"status-list",class:t.selectIndex==e.status?"active":"",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.selectIndex=e.status,t.reset()}}},[t._v(t._s(e.name))])})),1),t.list.length?[o("v-uni-view",{staticClass:"coupon-box"},[t._l(t.list,(function(e,i){return o("v-uni-view",{key:i,staticClass:"coupon-list",style:{"background-image":"url("+(1==e.state?t.$util.img("public/static/youpin/coupon_ysy.png"):t.$util.img("public/static/youpin/coupon_no.png"))+")"}},[o("v-uni-view",{staticClass:"coupon-left"},[o("v-uni-view",[o("v-uni-text",{style:{color:1==e.state?"var(--custom-brand-color)":"#fff"}},[t._v("￥")]),o("v-uni-text",{style:{color:1==e.state?"var(--custom-brand-color)":"#fff"}},[t._v(t._s(parseFloat(e.money)))])],1)],1),o("v-uni-view",{staticClass:"coupon-right"},[o("v-uni-view",{staticClass:"coupon-name"},[t._v(t._s(e.desc))]),o("v-uni-view",{staticClass:"coupon-time"},[t._v("有效期："+t._s(t.$util.timeStampTurnTime(e.end_time)))]),o("v-uni-view",{staticClass:"use-btn",class:1==e.state?"":"disabled",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodList(e)}}},[t._v(t._s(t.statusList[parseInt(e.state)-1].btn))])],1)],1)})),t.scrollLoading?o("v-uni-view",{staticClass:"empty-list-text"},[t._v("没有更多内容")]):t._e(),t.showLoading?o("ns-loading"):t._e()],2)]:o("v-uni-view",{staticClass:"empty-box"},[o("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/empty_coupon.png"),mode:""}}),o("v-uni-view",{staticClass:"empty-info"},[t._v("暂无可用优惠券")])],1)],2)},a=[]}}]);