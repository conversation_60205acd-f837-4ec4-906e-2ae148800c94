(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-up_to_shopkeeper-form_up_to_shopkeeper"],{1739:function(e,t,o){"use strict";var i=o("d92f"),n=o.n(i);n.a},"340f":function(e,t,o){"use strict";o.r(t);var i=o("ece1"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(a);t["default"]=n.a},"44e1":function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){return i}));var i={uniPopup:o("5e99").default},n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-view",{class:e.themeStyle},[o("v-uni-view",{staticClass:"form"},[o("v-uni-view",{staticClass:"paycode"},[o("v-uni-text",{staticClass:"phonenum"},[e._v("真实姓名")]),o("v-uni-input",{attrs:{type:"text",value:"",placeholder:"请输入真实姓名"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)}}})],1),o("v-uni-view",{staticClass:"comfirmcode"},[o("v-uni-text",{staticClass:"phonenum"},[e._v("微信号")]),o("v-uni-input",{attrs:{type:"text",value:"",placeholder:"请输入微信号"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.getInput.apply(void 0,arguments)}}})],1),o("v-uni-view",{staticClass:"phone"},[o("v-uni-text",{staticClass:"phonenum"},[e._v("手机号码")]),o("v-uni-text",{staticClass:"pnum"},[e._v(e._s(e.phonenum))])],1)],1),o("v-uni-view",{staticClass:"bottom"},[o("v-uni-view",{staticClass:"comfirm_one",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup()}}},[e._v("申请成为店主")])],1),o("uni-popup",{ref:"popup",staticClass:"my-popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("确定申请")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[e._v("确认是否升级为店主？")]),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-view",{staticClass:"button white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup()}}},[e._v("取消")]),o("v-uni-button",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.shure()}}},[e._v("确定")])],1)],1)],1)],1)},a=[]},bb4f:function(e,t,o){"use strict";o.r(t);var i=o("44e1"),n=o("340f");for(var a in n)["default"].indexOf(a)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(a);o("1739");var p=o("828b"),r=Object(p["a"])(n["default"],i["b"],i["c"],!1,null,"c04e033e",null,!1,i["a"],void 0);t["default"]=r.exports},d2bc:function(e,t,o){var i=o("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-c04e033e]{width:100%;text-align:center}.form[data-v-c04e033e]{background:#fff;margin-top:%?20?%;padding:0 %?10?%}.phone[data-v-c04e033e]{display:flex;align-items:center;height:%?100?%;margin:%?20?%}.phone .phonenum[data-v-c04e033e]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.phone .pnum[data-v-c04e033e]{font-size:%?30?%;color:#333}.phonecode[data-v-c04e033e]{display:flex;align-items:center;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.phonecode .phonenum[data-v-c04e033e]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.phonecode uni-input[data-v-c04e033e]{border:none;width:40%}.phonecode .getphonecode[data-v-c04e033e]{width:%?144?%;height:%?44?%;text-align:center;border:%?1?% solid #f2270c;color:#f2270c;font-size:%?24?%;border-radius:%?10?%;line-height:%?44?%;margin-left:%?30?%}.paycode[data-v-c04e033e]{display:flex;align-items:center;border-bottom:%?1?% solid #eee;height:%?100?%;margin:%?20?%}.paycode .phonenum[data-v-c04e033e]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.paycode uni-input[data-v-c04e033e]{border:none;width:60%}.comfirmcode[data-v-c04e033e]{display:flex;align-items:center;height:%?100?%;margin:%?20?%;border-bottom:%?1?% solid #eee}.comfirmcode .phonenum[data-v-c04e033e]{font-size:%?32?%;color:#333;display:inline-block;width:%?180?%}.comfirmcode uni-input[data-v-c04e033e]{border:none;width:40%}.comfirm[data-v-c04e033e]{width:%?654?%;height:%?80?%;background:#f2270c;text-align:center;line-height:%?80?%;font-size:%?32?%;color:#fff;border-radius:%?50?%;margin:%?50?% auto 0}.bottom[data-v-c04e033e]{width:100%;height:%?100?%;display:flex;align-items:center;justify-content:center;background:#fff;position:fixed;bottom:0}.bottom .comfirm_one[data-v-c04e033e]{width:%?690?%;height:%?80?%;text-align:center;border-radius:%?50?%;line-height:%?80?%;background:#f2270c;color:#fff;font-size:%?32?%}[data-v-c04e033e] .my-popup-dialog .uni-popup__wrapper-box{max-width:%?540?%;width:%?540?%;border-radius:%?20?%}[data-v-c04e033e] .coupon-instructions .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%;border-radius:%?20?%}.popup-dialog[data-v-c04e033e]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-c04e033e]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-c04e033e]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-c04e033e]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-c04e033e]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-c04e033e]{color:#f2270c;background:#fff;border:%?1?% solid #f2270c}.popup-dialog .popup-dialog-footer .button.red[data-v-c04e033e]{color:#fff;background:#f2270c}.coupon-instructions-popup-body[data-v-c04e033e]{width:%?560?%;height:%?540?%;margin:0 auto}.coupon-instructions .popup-dialog .popup-dialog-body[data-v-c04e033e]{padding:0}.coupon-instructions .popup-dialog .popup-dialog-footer .button[data-v-c04e033e]{width:%?480?%}',""]),e.exports=t},d92f:function(e,t,o){var i=o("d2bc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=o("967d").default;n("5bf5f5c9",i,!0,{sourceMap:!1,shadowMode:!1})},ece1:function(e,t,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i(o("5e99"));var n={components:{},data:function(){return{phonenum:"",realname:"",wxnum:""}},onLoad:function(e){this.phonenum=uni.getStorageSync("phone")},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"升级成为店主"})},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},watch:{},methods:{openPopup:function(){this.$refs.popup.open()},closePopup:function(){this.$refs.popup.close()},shure:function(){this.$refs.popup.close(),console.log(this.realname),console.log(this.wxnum),this.applyKeeper()},onInput:function(e){this.realname=e.target.value},getInput:function(e){this.wxnum=e.target.value},applyKeeper:function(){var e=this;this.$api.sendRequest({url:this.$apiUrl.applyUpgradeShop,data:{mobile:this.phonenum,real_name:this.realname,wechat_no:this.wxnum},success:function(t){var o=t.message;0==t.code?e.$util.redirectTo("/otherpages/member/up_to_shopkeeper/show_result"):e.$util.showToast({title:o})},fail:function(){}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),o=t.title,i=t.link,n=t.imageUrl;t.query;return this.$buriedPoint.pageShare(i,n,o)}};t.default=n}}]);