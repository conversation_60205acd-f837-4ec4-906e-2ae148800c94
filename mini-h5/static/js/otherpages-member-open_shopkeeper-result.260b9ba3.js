(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-open_shopkeeper-result"],{4438:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container",class:t.themeStyle},[1==t.vipData.state?[i("v-uni-view",{staticClass:"image-wrap"},[i("v-uni-image",{staticClass:"result-image",attrs:{src:t.$util.img("public/static/youpin/order/shopkeeper_wait.png"),mode:""}})],1),i("v-uni-view",{staticClass:"msg"},[t._v("等待审核")]),i("v-uni-view",{staticClass:"hint"},[t._v("请截图发送至推荐人,加快审核进度")])]:t._e(),2==t.vipData.state?[i("v-uni-view",{staticClass:"image-wrap"},[i("v-uni-image",{staticClass:"result-image",attrs:{src:t.$util.img("public/static/youpin/order/shopkeeper_success.png"),mode:""}})],1),i("v-uni-view",{staticClass:"msg"},[t._v("开店成功")])]:t._e(),3==t.vipData.state?[i("v-uni-view",{staticClass:"image-wrap"},[i("v-uni-image",{staticClass:"result-image",attrs:{src:t.$util.img("public/static/youpin/order/shopkeeper_fail.png"),mode:""}})],1),i("v-uni-view",{staticClass:"msg"},[t._v("审核不通过")])]:t._e(),i("v-uni-view",{staticClass:"vip-info"},[i("v-uni-view",{staticClass:"vip-info-input flex-space-between"},[i("v-uni-view",[t._v("手机号码")]),i("v-uni-view",[t._v(t._s(t.vipData.username?t.vipData.username:"-"))])],1),i("v-uni-view",{staticClass:"vip-info-input flex-space-between"},[i("v-uni-view",[t._v(t._s(2==t.vipData.state?"开店时间":"申请时间"))]),i("v-uni-view",[t._v(t._s(t.vipData.create_time?t.vipData.create_time:"-"))])],1)],1),2==t.vipData.state?[i("v-uni-button",{staticClass:"copy-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.vipData.download_link)}}},[t._v("复制先迈商城APP下载链接")]),i("v-uni-view",{staticClass:"hint text-center ns-margin-top"},[t._v("提示：复制链接使用浏览器打开，下载最新先迈商城APP")])]:t._e()],2)},n=[]},"461f":function(t,e,i){var a=i("60fa");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("2875c679",a,!0,{sourceMap:!1,shadowMode:!1})},"4bca":function(t,e,i){"use strict";i.r(e);var a=i("54c59"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"54c59":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={components:{},data:function(){return{mobile:null,vipData:{}}},onLoad:function(t){t&&t.mobile?(this.mobile=t.mobile,this.getVipUserInfo(t.mobile)):this.$util.showToast({title:"缺少参数！"})},onShow:function(){},computed:{},methods:{getVipUserInfo:function(t){var e=this;this.$api.sendRequest({url:this.$apiUrl.memberCheckShopInfoByMobile,data:{mobile:t},success:function(t){t.code>=0&&(e.vipData=t.data)},fail:function(t){}})},copyUrl:function(){},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),i=e.title,a=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,n,i)}}},"60fa":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-bf173e4a]{width:100%;text-align:center}.container[data-v-bf173e4a]{width:100vw;height:100vh;background:#fff}.container .image-wrap[data-v-bf173e4a]{display:flex;justify-content:center;padding:%?140?% 0 %?36?% 0}.container .image-wrap .result-image[data-v-bf173e4a]{width:%?120?%;height:%?120?%}.container .msg[data-v-bf173e4a]{text-align:center;font-size:%?40?%;line-height:1;margin-bottom:%?30?%}.container .hint[data-v-bf173e4a]{text-align:center;font-size:%?24?%;color:#999}.container .vip-info[data-v-bf173e4a]{width:%?670?%;margin:%?100?% auto 0}.container .vip-info .vip-info-input[data-v-bf173e4a]{padding:%?24?% 0;font-size:%?32?%;color:#333;border-bottom:1px solid #eee}.container .copy-btn[data-v-bf173e4a]{width:%?670?%;margin:0 auto;margin-top:%?80?%;height:%?80?%;line-height:%?80?%;font-size:%?30?%;color:#f2280c;border-radius:%?4?%;background-color:#fff!important;text-align:center;border:1px solid #f2280c}',""]),t.exports=e},"95bf":function(t,e,i){"use strict";i.r(e);var a=i("4438"),n=i("4bca");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("c0e0");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"bf173e4a",null,!1,a["a"],void 0);e["default"]=o.exports},c0e0:function(t,e,i){"use strict";var a=i("461f"),n=i.n(a);n.a}}]);