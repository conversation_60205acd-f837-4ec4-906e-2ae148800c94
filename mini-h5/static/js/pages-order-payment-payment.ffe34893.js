(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-payment-payment"],{"2d01":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"31dd":function(t,e,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa"),o("e966");var i=a(o("fcd5")),n=a(o("5e99")),s=a(o("c8ec")),r=a(o("2d01")),d=o("4b89"),u=a(o("de74")),l={components:{UniIcons:u.default,uniPopup:n.default,mypOne:s.default},data:function(){return{timeTip:"选择配送时间",time:null,navHeight:0,couponTitData:[{name:"可用优惠券",status:1},{name:"不可使用优惠券",status:0}],currentTab:1,couponInstructionsContent:"",choiceWechatAdderError:!1,ischoiceWechatAdder:!1,postWechatAdder:!1,maidou:{},paymentMethod:"WECHAT",isOnXianMaiApp:d.isOnXianMaiApp}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle},paymentMethods:function(){return[{icon:this.$util.img("public/static/youpin/paySuccess-icon.png"),name:"微信支付",desc:"",key:"WECHAT",disable:!1},{icon:this.$util.img("public/static/youpin/balance.png"),name:"余额支付",desc:"(当前余额￥".concat(this.orderPaymentData.member_account.balance_money,"）"),key:"BALANCE",disable:1==this.orderCreateData.is_balance?Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.balance_money):Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.pay_money)},{icon:this.$util.img("public/static/youpin/maidou.png"),name:"迈豆支付",desc:"(当前迈豆".concat(this.maidou.canuse_maidou,"）"),key:"MAIDOU",disable:1!=this.maidou.maidou_pay}]},otherPaymentMethods:function(){return[{icon:this.$util.img("public/static/youpin/paySuccess-icon.png"),name:"微信支付",desc:"",key:"WECHAT",disable:!1},{icon:this.$util.img("public/static/youpin/balance.png"),name:"余额支付",desc:"(当前余额￥".concat(this.orderPaymentData.member_account.balance_money,"）"),key:"BALANCE",disable:1==this.orderCreateData.is_balance?Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.balance_money):Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.pay_money)}]}},onLoad:function(){var t=this;uni.getSystemInfo({success:function(e){var o=e.statusBarHeight+46;t.navHeight=o,t.isOnXianMaiApp||(t.navHeight=0)},fail:function(t){console.log(t)}})},onShow:function(){},mixins:[i.default,r.default],methods:{cartNumChange:function(t,e){var o=this;if(uni.showLoading({title:"加载中",mask:!0}),this.modifyFlag=!0,this.orderPaymentData.cart_ids){var a=this.orderPaymentData.shop_goods_list[e.siteIndex].goods_list[e.goodsIndex];t<1&&(t=1),this.$api.sendRequest({url:"/api/cart/edit",data:{num:t,cart_id:a.cart_id},success:function(i){if(i.code>=0){var n=a.num;o.orderPaymentData.shop_goods_list[e.siteIndex].goods_list[e.goodsIndex].num=parseInt(t),o.getOrderPaymentData(1),o.$buriedPoint.purchaseGoods({id:a.sku_id,action_type:n<t?0:1,action_num:[n<t?t-n:n-t],is_goods_page:0})}else o.$util.showToast({title:i.message})}})}else this.orderCreateData.num=t,this.getOrderPaymentData(1)},bindTimeChange:function(t){this.timeTip=""+t.target.value,this.time=t.target.value,this.orderCreateData.delivery[t.currentTarget.dataset.siteid].buyer_ask_delivery_time=this.time},toShopDetail:function(t){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:t})},openPopup:function(){this.$refs.popup.open()},closePopup:function(){this.$refs.popup.close()},openCouponInstructionsPopup:function(){this.$refs.couponInstructions.open()},closeCouponInstructionsPopup:function(){this.$refs.couponInstructions.close()},close_pay:function(){this.errMsg="",this.$refs.payPassword.close()},closePopupCoupon:function(t){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[t].close()},toBack:function(){this.closePopup(),uni.navigateBack()},toWaitPayList:function(){this.$refs.popupToList.close(),this.$util.redirectTo("/pages/order/list/list?status=waitpay",{},"redirectTo"),uni.removeStorage({key:"orderCreateData",success:function(){}})},getChooseAddress:function(){this.orderPaymentData.member_address,this.selectAddress()},saveAddress:function(t){var e=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:t,success:function(t){t.code>=0||(e.$util.showToast({title:t.message}),e.choiceWechatAdderError=!0)},complete:function(){e.postWechatAdder=!0}})}}};e.default=l},"327f":function(t,e,o){"use strict";var a=o("a971"),i=o.n(a);i.a},"37ff":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},i=[]},4210:function(t,e,o){"use strict";var a=o("e133"),i=o.n(a);i.a},"45b2":function(t,e,o){var a=o("7fd9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("635bedd2",a,!0,{sourceMap:!1,shadowMode:!1})},5281:function(t,e,o){"use strict";o.r(e);var a=o("31dd"),i=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},5761:function(t,e,o){"use strict";var a=o("7e56"),i=o.n(a);i.a},"5a67":function(t,e,o){"use strict";o.r(e);var a=o("f68c"),i=o("5281");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);o("7f96"),o("7f24"),o("5761");var s=o("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"3d499d70",null,!1,a["a"],void 0);e["default"]=r.exports},"5e48":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"uni-numbox",class:{small:"small"==t.size}},[o("v-uni-view",{staticClass:"uni-numbox__minus",class:{"uni-numbox--disabled":t.inputValue<=t.min||t.disabled,small:"small"==t.size},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("minus")}}},[t._v("-")]),o("v-uni-input",{staticClass:"uni-numbox__value",class:{small:"small"==t.size},attrs:{disabled:t.disabled,maxlength:String(this.max).length+1,type:"number"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t._onInput.apply(void 0,arguments)}},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}}),o("v-uni-view",{staticClass:"uni-numbox__plus",class:{"uni-numbox--disabled":t.inputValue>=t.max||t.disabled,small:"small"==t.size},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("plus")}}},[t._v("+")])],1)},i=[]},"61c4":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"code-box"},[o("v-uni-view",{staticClass:"flex-box"},[o("v-uni-input",{staticClass:"hide-input",attrs:{value:t.inputValue,type:"number",focus:t.autoFocus,maxlength:t.maxlength},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.getVal.apply(void 0,arguments)}}}),t._l(t.ranges,(function(e,a){return[o("v-uni-view",{key:a+"_0",class:["item",{active:t.codeIndex===e,middle:"middle"===t.type,bottom:"bottom"===t.type,box:"box"===t.type}]},["middle"!==t.type?o("v-uni-view",{staticClass:"line"}):t._e(),"middle"===t.type&&t.codeIndex<=e?o("v-uni-view",{staticClass:"bottom-line"}):t._e(),t.isPwd&&t.codeArr.length>=e?[o("v-uni-text",{staticClass:"dot"},[t._v("●")])]:[o("v-uni-text",{staticClass:"number"},[t._v(t._s(t.codeArr[a]?t.codeArr[a]:""))])]],2)]}))],2)],1)},i=[]},"61fe":function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'.uni-numbox[data-v-41203276]{display:inline-flex;flex-direction:row;justify-content:flex-start;height:%?70?%;position:relative}.uni-numbox.small[data-v-41203276]{height:%?52?%}.uni-numbox[data-v-41203276]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border:1px solid #eee;border-radius:%?12?%;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox__minus[data-v-41203276],\r\n.uni-numbox__plus[data-v-41203276]{margin:0;background-color:#f8f8f8;width:%?70?%;font-size:%?40?%;height:100%;line-height:%?70?%;text-align:center;display:inline-flex;align-items:center;justify-content:center;color:#333;position:relative}.uni-numbox__value[data-v-41203276]{position:relative;background-color:#fff;width:%?80?%;height:100%;text-align:center;padding:0}.uni-numbox__minus.small[data-v-41203276],\r\n.uni-numbox__plus.small[data-v-41203276]{width:%?50?%;line-height:%?50?%}.uni-numbox__value.small[data-v-41203276]{width:%?60?%;font-size:%?28?%}.uni-numbox__value[data-v-41203276]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-style:solid;border-color:#eee;border-left-width:1px;border-right-width:1px;border-top-width:0;border-bottom-width:0;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox--disabled[data-v-41203276]{color:silver}',""]),t.exports=e},"66de":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa"),o("5c47"),o("0506");var a={name:"UniNumberBox",props:{type:{type:String},value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},modifyFlag:{type:Boolean,default:!1},size:{type:String,default:"default"},index:{type:Number,default:-1},minTips:{type:String,default:"最少购买一件哦"},maxTips:{type:String,default:"该商品不能购买更多哦！"}},data:function(){return{inputValue:0,oldNumber:0}},watch:{value:function(t){this.inputValue=+t}},created:function(){this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled&&!this.modifyFlag){var e=this._getDecimalScale(),o=this.inputValue*e,a=this.step*e;if("minus"===t?o-=a:"plus"===t&&(o+=a),o<this.min||o>this.max)return"cart"==this.type&&o<this.min&&this.$util.showToast({title:this.minTips}),void("cart"==this.type&&o>this.max&&this.$util.showToast({title:this.maxTips}));this.inputValue=o/e,this.$emit("change",this.inputValue)}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onInput:function(t){var e=this;setTimeout((function(){var o=t.detail.value;/(^[1-9]\d*$)/.test(o)||(o=e.min),!o||e.max<1?e.inputValue=1:(o=+o,o>e.max?o=e.max:o<e.min&&(o=e.min)),e.inputValue=o,e.oldNumber!==e.inputValue&&(e.$emit("change",e.inputValue),e.oldNumber=e.inputValue)}),0)}}};e.default=a},"7e56":function(t,e,o){var a=o("f35b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("6a6b33d4",a,!0,{sourceMap:!1,shadowMode:!1})},"7f24":function(t,e,o){"use strict";var a=o("baac"),i=o.n(a);i.a},"7f96":function(t,e,o){"use strict";var a=o("45b2"),i=o.n(a);i.a},"7fd9":function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3d499d70]{width:100%;text-align:center}[data-v-3d499d70] .uni-navbar--border{border-bottom-width:0}',""]),t.exports=e},8092:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3d499d70]{width:100%;text-align:center}[data-v-3d499d70] .my-popup-dialog .uni-popup__wrapper-box{max-width:%?540?%;width:%?540?%;border-radius:%?20?%}[data-v-3d499d70] .coupon-instructions .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%;border-radius:%?20?%}.popup-dialog[data-v-3d499d70]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-3d499d70]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-3d499d70]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-3d499d70]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-3d499d70]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-3d499d70]{color:var(--custom-brand-color);background:#fff;border:%?1?% solid var(--custom-brand-color)}.popup-dialog .popup-dialog-footer .button.red[data-v-3d499d70]{color:#fff;background:var(--custom-brand-color)}.coupon-instructions-popup-body[data-v-3d499d70]{width:%?560?%;height:%?540?%;margin:0 auto}.coupon-instructions .popup-dialog .popup-dialog-body[data-v-3d499d70]{padding:0}.coupon-instructions .popup-dialog .popup-dialog-footer .button[data-v-3d499d70]{width:%?480?%}.cha_close[data-v-3d499d70]{width:%?30?%;height:%?30?%}.pay_money[data-v-3d499d70]{font-size:%?36?%!important}',""]),t.exports=e},"8f68":function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},9127:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"92c0":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var a={name:"mypOneInput",props:{value:{type:String,default:""},maxlength:{type:Number,default:4},autoFocus:{type:Boolean,default:!1},isPwd:{type:Boolean,default:!1},type:{type:String,default:"bottom"}},watch:{maxlength:{immediate:!0,handler:function(t){this.ranges=6===t?[1,2,3,4,5,6]:[1,2,3,4]}},value:{immediate:!0,handler:function(t){t!==this.inputValue&&(this.inputValue=t,this.toMakeAndCheck(t))}}},data:function(){return{inputValue:"",codeIndex:1,codeArr:[],ranges:[1,2,3,4]}},methods:{getVal:function(t){var e=t.detail.value;this.inputValue=e,this.$emit("input",e),this.toMakeAndCheck(e)},toMakeAndCheck:function(t){var e=t.split("");this.codeIndex=e.length+1,this.codeArr=e,this.codeIndex>Number(this.maxlength)&&this.$emit("finish",this.codeArr.join(""))},set:function(t){this.inputValue=t,this.toMakeAndCheck(t)},clear:function(){this.inputValue="",this.codeArr=[],this.codeIndex=1}}};e.default=a},a3af:function(t,e,o){"use strict";o.r(e);var a=o("92c0"),i=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},a8f1:function(t,e,o){var a=o("61fe");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("4e4f523b",a,!0,{sourceMap:!1,shadowMode:!1})},a971:function(t,e,o){var a=o("8f68");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("2d38abe0",a,!0,{sourceMap:!1,shadowMode:!1})},b156:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@-webkit-keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}@keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}.code-box[data-v-31a1a5ae]{text-align:center}.flex-box[data-v-31a1a5ae]{display:flex;justify-content:center;flex-wrap:wrap;position:relative}.flex-box .hide-input[data-v-31a1a5ae]{position:absolute;top:0;left:-100%;width:200%;height:100%;text-align:left;z-index:9;opacity:1}.flex-box .item[data-v-31a1a5ae]{position:relative;flex:1;margin-right:%?18?%;font-size:%?70?%;font-weight:700;color:#333;line-height:%?100?%}.flex-box .item[data-v-31a1a5ae]::before{content:"";padding-top:100%;display:block}.flex-box .item[data-v-31a1a5ae]:last-child{margin-right:0}.flex-box .middle[data-v-31a1a5ae]{border:none}.flex-box .box[data-v-31a1a5ae]{box-sizing:border-box;border:%?2?% solid #ccc;border-width:%?2?% 0 %?2?% %?2?%;margin-right:0}.flex-box .box[data-v-31a1a5ae]:first-of-type{border-top-left-radius:%?8?%;border-bottom-left-radius:%?8?%}.flex-box .box[data-v-31a1a5ae]:last-child{border-right:%?2?% solid #ccc;border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.flex-box .bottom[data-v-31a1a5ae]{box-sizing:border-box;border-bottom:1px solid #ddd}.flex-box .active[data-v-31a1a5ae]{border-color:#ddd}.flex-box .active .line[data-v-31a1a5ae]{display:block}.flex-box .line[data-v-31a1a5ae]{display:none;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?2?%;height:%?40?%;background:#333;-webkit-animation:twinkling-data-v-31a1a5ae 1s infinite ease;animation:twinkling-data-v-31a1a5ae 1s infinite ease}.flex-box .dot[data-v-31a1a5ae],\n.flex-box .number[data-v-31a1a5ae]{font-size:%?44?%;line-height:%?40?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.flex-box .bottom-line[data-v-31a1a5ae]{height:4px;background:#000;width:80%;position:absolute;border-radius:2px;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}',""]),t.exports=e},b8ea:function(t,e,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var i=a(o("9127")),n={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:i.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=n},baac:function(t,e,o){var a=o("8092");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("66e390d3",a,!0,{sourceMap:!1,shadowMode:!1})},c8ec:function(t,e,o){"use strict";o.r(e);var a=o("61c4"),i=o("a3af");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);o("4210");var s=o("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"31a1a5ae",null,!1,a["a"],void 0);e["default"]=r.exports},dadc:function(t,e,o){"use strict";o.r(e);var a=o("5e48"),i=o("f393");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);o("e651");var s=o("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"41203276",null,!1,a["a"],void 0);e["default"]=r.exports},de74:function(t,e,o){"use strict";o.r(e);var a=o("37ff"),i=o("fefc");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);o("327f");var s=o("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"65e5b6d2",null,!1,a["a"],void 0);e["default"]=r.exports},e133:function(t,e,o){var a=o("b156");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("356f4cc6",a,!0,{sourceMap:!1,shadowMode:!1})},e651:function(t,e,o){"use strict";var a=o("a8f1"),i=o.n(a);i.a},f35b:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3d499d70]{width:100%;text-align:center}.uni-list-cell[data-v-3d499d70]{display:flex;justify-content:space-between}.align-right[data-v-3d499d70]{text-align:right}.inline[data-v-3d499d70]{display:inline!important}.order-container[data-v-3d499d70]{padding-bottom:%?120?%}.order-container.safe-area[data-v-3d499d70]{padding-bottom:%?188?%}.address-wrap[data-v-3d499d70]{background:#fff;position:relative;min-height:%?100?%;max-height:%?140?%;display:flex;align-items:center}.address-wrap .icon[data-v-3d499d70]{width:%?94?%;height:%?100?%;display:flex;justify-content:center;align-items:center}.address-wrap .icon uni-image[data-v-3d499d70]{width:%?48?%;height:%?48?%}.address-wrap .address-info[data-v-3d499d70]{width:%?656?%}.address-wrap .address-info .info[data-v-3d499d70]{display:flex;padding-top:%?10?%}.address-wrap .address-info .info uni-text[data-v-3d499d70]{overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;word-break:break-all;font-weight:700}.address-wrap .address-info .info uni-text[data-v-3d499d70]:first-of-type{max-width:%?380?%;margin-right:%?32?%}.address-wrap .address-info .detail[data-v-3d499d70]{width:%?508?%;padding-bottom:%?17?%;line-height:1.3;font-size:%?26?%}.address-wrap .address-empty[data-v-3d499d70]{line-height:%?100?%;color:#999;font-size:%?26?%}.address-wrap .cell-more[data-v-3d499d70]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?20?%}.address-wrap .cell-more .iconfont[data-v-3d499d70]{color:#999}.mobile-wrap[data-v-3d499d70]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.mobile-wrap .form-group .form-item[data-v-3d499d70]{display:flex;line-height:%?50?%}.mobile-wrap .form-group .form-item .text[data-v-3d499d70]{display:inline-block;line-height:%?50?%;padding-right:%?10?%}.mobile-wrap .form-group .form-item .placeholder[data-v-3d499d70]{line-height:%?50?%}.mobile-wrap .form-group .form-item .input[data-v-3d499d70]{flex:1;height:%?50?%;line-height:%?50?%}.order-cell[data-v-3d499d70]{display:flex;margin:%?48?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-3d499d70]{text-align:left}.order-cell .box[data-v-3d499d70]{flex:1;line-height:inherit}.order-cell .box .textarea[data-v-3d499d70]{height:%?40?%}.order-cell .iconfont[data-v-3d499d70]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-3d499d70]{padding:0;font-size:%?26?%;color:#333}.order-cell .order-pay uni-text[data-v-3d499d70]{display:inline-block;margin-left:%?20?%}.order-cell .order-pay .pay-money[data-v-3d499d70]{font-size:%?32?%;font-weight:700;margin-left:%?2?%}.order-discount .tit[data-v-3d499d70]{width:inherit!important}.order-discount .tit uni-text[data-v-3d499d70]{margin-left:%?10?%;color:grey}.site-wrap[data-v-3d499d70]{margin:%?24?%;padding:0 %?24?%;border-radius:%?20?%;background:#fff;position:relative}.site-wrap .site-header[data-v-3d499d70]{display:flex;align-items:center;height:%?88?%}.site-wrap .site-header .icondianpu[data-v-3d499d70]{display:inline-block;line-height:1;margin-right:%?17?%;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap[data-v-3d499d70]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-3d499d70]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-3d499d70]{width:%?180?%;height:%?180?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-3d499d70]{width:100%;height:100%;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-3d499d70]{flex:1;position:relative;max-width:calc(100% - %?200?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-price-box[data-v-3d499d70]{display:flex;align-items:center;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-price-box .uni-numbox[data-v-3d499d70]{border-radius:%?36?%;overflow:hidden;border:%?2?% solid #ccc;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0)}.site-wrap .site-body .goods-wrap .goods-info .goods-price-box .uni-numbox .uni-numbox__minus[data-v-3d499d70],\r\n.site-wrap .site-body .goods-wrap .goods-info .goods-price-box .uni-numbox .uni-numbox__plus[data-v-3d499d70]{background:none!important;border-radius:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price-box .uni-numbox .uni-numbox__value[data-v-3d499d70]{border-left:%?2?% solid #ccc;border-right:%?2?% solid #ccc;width:%?80?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price-box .goods-number[data-v-3d499d70]{padding:%?0?% %?14?%;border:1px solid #ccc;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-3d499d70]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;margin-bottom:%?30?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-3d499d70]{width:100%;line-height:1.3;display:flex;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-3d499d70]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-3d499d70]{color:#999;font-size:%?24?%;line-height:1.3}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-3d499d70]:last-of-type{text-align:right}.site-wrap .site-body .goods-wrap .goods-info .goods-price[data-v-3d499d70]{color:var(--custom-brand-color);font-size:%?24?%;font-weight:600}.site-wrap .site-body .goods-wrap .goods-info .goods-price .price[data-v-3d499d70]{font-size:%?32?%}.site-wrap .site-body .goods-wrap .goods-info .share-discount[data-v-3d499d70]{display:inline-block;height:14px;line-height:14px;background:linear-gradient(90deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:2px;font-size:9px;font-weight:500;color:#fff;padding:1px 2px}.site-wrap .site-footer[data-v-3d499d70]{padding-bottom:%?30?%}.site-wrap .site-footer .order-cell .tit[data-v-3d499d70]{width:%?180?%}.site-wrap .site-footer .order-cell .store-promotion-box[data-v-3d499d70]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.site-wrap .site-footer .order-cell .box uni-input[data-v-3d499d70]{text-align:right;font-size:%?26?%}.site-wrap .site-footer .order-cell .box.text-overflow[data-v-3d499d70]{max-width:calc(100% - %?180?%)}.site-wrap .site-footer .order-cell.my-coupon[data-v-3d499d70]{padding:%?26?% 0;margin:0;text-align:right}.site-wrap .site-footer .order-cell.my-coupon .box.text-overflow[data-v-3d499d70]{max-width:unset}.site-wrap .site-footer .order-cell[data-v-3d499d70]:last-of-type{margin-bottom:0}.order-checkout[data-v-3d499d70]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.order-checkout .order-cell .iconyuan_checkbox[data-v-3d499d70],\r\n.order-checkout .order-cell .iconyuan_checked[data-v-3d499d70]{font-size:%?38?%}.order-money .order-cell .box[data-v-3d499d70]{padding:0}.order-money .order-cell .box .operator[data-v-3d499d70]{font-size:%?24?%;margin-right:%?6?%}.order-submit[data-v-3d499d70]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?98?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);display:flex;align-items:center}.order-submit.bottom-safe-area[data-v-3d499d70]{padding-bottom:0!important;padding-bottom:constant(safe-area-inset-bottom)!important;padding-bottom:env(safe-area-inset-bottom)!important}.order-submit .order-settlement-info[data-v-3d499d70]{flex:1;padding-left:%?25?%;font-size:%?28?%}.order-submit .order-settlement-info .money[data-v-3d499d70]{font-size:%?48?%}.order-submit .order-settlement-info .total-discount[data-v-3d499d70]{font-size:%?22?%;color:var(--custom-brand-color)}.order-submit .order-settlement-info .money-info[data-v-3d499d70]{line-height:normal}.order-submit .submit-btn[data-v-3d499d70]{height:%?80?%;margin-right:%?24?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-3d499d70]{padding:0;width:%?200?%;background-color:var(--custom-brand-color)!important;height:%?80?%;line-height:%?80?%;font-size:%?28?%;border-radius:%?40?%}.popup[data-v-3d499d70]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-3d499d70]{height:%?120?%;display:flex;align-items:center;padding:0 %?40?%}.popup .popup-header > uni-view[data-v-3d499d70]{flex:1;line-height:1}.popup .popup-header .tit[data-v-3d499d70]{font-size:%?32?%;font-weight:600}.popup .popup-header .vice-tit[data-v-3d499d70]{margin-right:%?20?%}.popup .u-padding-30[data-v-3d499d70]{padding-bottom:%?30?%!important}.popup .popup-footer[data-v-3d499d70]{display:flex;align-items:center;justify-content:center;height:%?100?%}.popup .popup-footer .confirm-btn[data-v-3d499d70]{display:flex;align-items:center;justify-content:center;width:%?654?%;height:%?80?%;color:#fff;text-align:center;border-radius:%?40?%;background:linear-gradient(0deg,var(--custom-brand-color),var(--custom-brand-color-80))}.popup .popup-footer .disabled[data-v-3d499d70]{background:#ccc}.invoice-popup[data-v-3d499d70]{height:65vh}.invoice-popup .popup-body .invoice-cell[data-v-3d499d70]{margin:0 %?30?%;padding:%?30?% 0;border-top:1px solid #f5f5f5}.invoice-popup .popup-body .invoice-cell[data-v-3d499d70]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-3d499d70]{font-size:%?28?%;font-weight:600}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-3d499d70]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-3d499d70]{display:inline-block;line-height:1;font-size:%?28?%;padding:%?16?% %?40?%;background:#eee;border:1px solid #eee;border-radius:%?32?%;margin:0 %?20?% %?20?% 0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(1), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(2), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(3){margin-bottom:0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.disabled[data-v-3d499d70]{color:#aaa}.invoice-popup .popup-body .invoice-cell .form-group[data-v-3d499d70]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item[data-v-3d499d70]{display:flex;line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .text[data-v-3d499d70]{display:inline-block;width:%?200?%;line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .placeholder[data-v-3d499d70]{line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .input[data-v-3d499d70]{flex:1;height:%?50?%;line-height:%?50?%}.coupon-popup-father[data-v-3d499d70] .uni-popup__wrapper{background:transparent!important}.coupon-popup-father[data-v-3d499d70] .uni-popup__wrapper .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0!important;max-height:%?1108?%!important}.coupon-popup[data-v-3d499d70]{display:flex;flex-direction:column;border-radius:%?25?% %?25?% 0 0;overflow:hidden}.coupon-popup .popup-body[data-v-3d499d70]{height:%?798?%}.coupon-popup .uni-tab-item[data-v-3d499d70]{display:inline-block;flex-wrap:nowrap;padding-left:%?24?%;padding-right:%?24?%}.coupon-popup .uni-tab-item-title[data-v-3d499d70]{color:#333;font-size:%?26?%;display:block;height:%?50?%;line-height:%?50?%;padding:0 %?10?%;flex-wrap:nowrap;white-space:nowrap}.coupon-popup .uni-tab-item-title .line[data-v-3d499d70]{width:%?36?%;height:%?8?%;background:transparent;border-radius:%?6?%;margin:0 auto}.coupon-popup .uni-tab-item-title-active[data-v-3d499d70]{display:block;height:%?50?%;font-weight:700;padding:0 %?10?%}.coupon-popup .uni-tab-item-title-active .line[data-v-3d499d70]{background:var(--custom-brand-color)}.coupon-popup .popup-body[data-v-3d499d70]{background:#f5f5f5}.coupon-popup .coupon-item[data-v-3d499d70]{position:relative;margin:%?24?%;font-size:0}.coupon-popup .coupon-item .coupon_ysy[data-v-3d499d70]{width:%?702?%;height:%?200?%}.coupon-popup .coupon-item > .iconfont[data-v-3d499d70]{font-size:%?40?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.coupon-popup .coupon-item > .iconyuan_checkbox[data-v-3d499d70]{color:#a6a6a6}.coupon-popup .coupon-item .circular[data-v-3d499d70]{position:absolute;top:50%;left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background:#f5f5f5;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item .coupon-info[data-v-3d499d70]{height:%?200?%;display:flex;width:%?702?%;position:absolute;top:0}.coupon-popup .coupon-item .coupon-info .coupon-money[data-v-3d499d70]{width:%?218?%;height:%?200?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?24?%}.coupon-popup .coupon-item .coupon-info .coupon-money uni-text[data-v-3d499d70]{font-size:%?50?%;line-height:1}.coupon-popup .coupon-item .coupon-info .coupon-money .ns-font-size-sm-left[data-v-3d499d70]{font-size:%?24?%!important;margin-top:%?14?%;margin-left:%?4?%}.coupon-popup .coupon-item .coupon-info .coupon-money .ns-font-size-sm-right[data-v-3d499d70]{font-size:%?24?%!important;margin-top:%?14?%;margin-right:%?4?%}.coupon-popup .coupon-item .coupon-info .info[data-v-3d499d70]{flex:1;max-width:calc(100% - %?240?%)}.coupon-popup .coupon-item .coupon-info .info .coupon-name[data-v-3d499d70]{margin-top:%?18?%;margin-bottom:%?50?%;font-weight:700;color:#333}.coupon-popup .coupon-item .coupon-info .info .coupon-time[data-v-3d499d70]{color:#999;font-size:%?22?%}.coupon-popup .coupon-item .coupon-info .info uni-view[data-v-3d499d70]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.promotion-popup[data-v-3d499d70]{height:65vh}.promotion-popup .order-cell[data-v-3d499d70]{margin:%?20?% %?30?%;display:flex;flex-direction:column;align-items:flex-start}.promotion-popup .order-cell .tit[data-v-3d499d70]{line-height:%?60?%}.promotion-popup .order-cell .box[data-v-3d499d70]{padding:0}.delivery-popup[data-v-3d499d70]{height:80vh}.delivery-popup .delivery-cell[data-v-3d499d70]{margin:0 %?30?%;padding:%?30?% 0;border-top:1px solid #f5f5f5}.delivery-popup .delivery-cell[data-v-3d499d70]:first-of-type{border-top:none}.delivery-popup .delivery-cell .tit[data-v-3d499d70]{font-size:%?28?%;font-weight:600}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-3d499d70]{display:inline-block;line-height:1;font-size:%?28?%;padding:%?16?% %?40?%;background:#eee;border:1px solid #eee;border-radius:%?32?%;margin:0 %?20?% %?20?% 0}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(1), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(2), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-3d499d70]:nth-of-type(3){margin-bottom:0}.delivery-popup .delivery-cell .option-grpup .option-item.disabled[data-v-3d499d70]{color:#aaa}.delivery-popup .delivery-cont[data-v-3d499d70]{height:calc(100% - %?180?%);overflow-y:scroll}.delivery-popup .delivery-cont .pickup-point[data-v-3d499d70]{padding:%?20?% 0;border-top:1px solid #f5f5f5}.delivery-popup .delivery-cont .pickup-point .name[data-v-3d499d70]{display:flex}.delivery-popup .delivery-cont .pickup-point .name .icon[data-v-3d499d70]{flex:1;text-align:right}.delivery-popup .delivery-cont .pickup-point .name .icon .iconfont[data-v-3d499d70]{line-height:1}.delivery-popup .delivery-cont .pickup-point[data-v-3d499d70]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-cont .pickup-point .info[data-v-3d499d70]{line-height:1.2}.delivery-popup .delivery-cont .pickup-point .info .ns-text-color-gray[data-v-3d499d70]:last-of-type{margin-left:%?10?%}.pay-password[data-v-3d499d70]{width:80vw;background:#fff;box-sizing:border-box;border-radius:%?10?%;overflow:hidden;padding:%?30?% %?40?%;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.pay-password .popup-title[data-v-3d499d70]{display:flex;align-items:center}.pay-password .popup-title .title[data-v-3d499d70]{font-size:%?28?%;text-align:center;width:calc(100% - %?40?%);text-align:center}.pay-password .error-tips[data-v-3d499d70]{text-align:center;width:100%}.pay-password .money-box[data-v-3d499d70]{margin-top:%?50?%}.pay-password .money-box .total-fee[data-v-3d499d70]{text-align:center;font-size:%?48?%;font-weight:700;color:#333}.pay-password .money-box .balance[data-v-3d499d70]{font-size:%?24?%;color:#999;text-align:center}.pay-password .tips[data-v-3d499d70]{font-size:%?24?%;color:#999;text-align:center}.pay-password .btn[data-v-3d499d70]{width:60%;margin:0 auto;margin-top:%?30?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;color:#fff;text-align:center;border:1px solid #fff}.pay-password .btn.white[data-v-3d499d70]{margin-top:%?20?%;background-color:#fff!important}.pay-password .password-wrap[data-v-3d499d70]{padding-top:%?20?%;width:90%;margin:0 auto}.pay-password .password-wrap .forget-password[data-v-3d499d70]{margin-top:%?20?%;display:inline-block}.text-color[data-v-3d499d70]{color:var(--custom-brand-color)}.text-color .money[data-v-3d499d70]{font-weight:700;font-size:%?36?%!important}.nav[data-v-3d499d70]{width:100%;overflow:hidden;position:fixed;top:0;left:0;z-index:10;background-color:#fff}.nav-title[data-v-3d499d70]{width:100%;height:%?88?%;line-height:%?88?%;text-align:center;position:absolute;bottom:0;left:0;z-index:10}.nav .back[data-v-3d499d70]{width:%?42?%;height:%?70?%;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?0?%;padding:0 %?24?%}.wrapper[data-v-3d499d70]{padding-top:%?24?%}.coupon-instructions-close[data-v-3d499d70]{display:flex;justify-content:flex-end;align-items:center}.coupon-instructions-btn[data-v-3d499d70]{margin-right:%?20?%;color:#999;font-size:%?24?%}.coupon-close[data-v-3d499d70]{color:#a0a1a7}.coupon-default[data-v-3d499d70]{display:flex;align-items:center;justify-content:center;height:100%}.coupon-default uni-view[data-v-3d499d70]{color:#999}.payment-methods .item[data-v-3d499d70]{display:flex;justify-content:space-between;align-items:center;padding:%?10?% 0}.payment-methods .item.disable .title uni-text[data-v-3d499d70]{color:#ccc}.payment-methods .item .icon[data-v-3d499d70]{width:%?48?%;height:%?48?%}.payment-methods .item .title[data-v-3d499d70]{flex:1;font-size:%?26?%;margin:0 %?10?%}.payment-methods .item .title .desc[data-v-3d499d70]{color:var(--custom-brand-color);margin-left:%?10?%}.empty-box[data-v-3d499d70]{display:flex;flex-direction:column;align-items:center}.empty-box uni-image[data-v-3d499d70]{width:%?402?%;height:%?282?%;margin:%?208?% 0 %?42?%}.empty-info[data-v-3d499d70]{display:flex;align-items:center;justify-content:center;height:100%;font-size:%?32?%;line-height:%?44?%}.coupon-box[data-v-3d499d70]{padding:%?20?% %?20?%}.coupon-box .coupon-list[data-v-3d499d70]{display:flex;height:%?200?%;background-size:%?702?% %?200?%;background-position:50%;background-repeat:no-repeat;margin-bottom:%?20?%}.coupon-box .coupon-list .coupon-left[data-v-3d499d70]{display:flex;justify-content:center;align-items:center;flex-shrink:0;width:%?218?%}.coupon-box .coupon-list .coupon-left > uni-view > uni-text[data-v-3d499d70]:first-child{font-size:%?28?%;font-weight:700}.coupon-box .coupon-list .coupon-left > uni-view > uni-text[data-v-3d499d70]:last-child{font-size:%?52?%;font-weight:700}.coupon-box .coupon-list .coupon-right[data-v-3d499d70]{position:relative;flex:1;padding:%?22?% %?24?% %?28?%}.coupon-box .coupon-list .coupon-right .coupon-name[data-v-3d499d70]{font-size:%?30?%;line-height:%?42?%;color:#333;font-weight:700}.coupon-box .coupon-list .coupon-right .coupon-time[data-v-3d499d70]{position:absolute;bottom:%?64?%;color:#999;font-size:%?22?%;line-height:%?42?%}.coupon-box .coupon-list .coupon-right uni-image[data-v-3d499d70]{position:absolute;right:%?16?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?40?%;height:%?40?%}',""]),t.exports=e},f393:function(t,e,o){"use strict";o.r(e);var a=o("66de"),i=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},f68c:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){return a}));var a={uniNumberBox:o("dadc").default,uniIcons:o("de74").default,uniPopup:o("5e99").default,mypOne:o("c8ec").default,loadingCover:o("5510").default},i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"order-container",class:t.themeStyle,style:[t.themeColorVar]},[t.isOnXianMaiApp?o("v-uni-view",{staticClass:"nav bg-white",style:{height:t.navHeight+"px"}},[o("v-uni-view",{staticClass:"nav-title"},[o("v-uni-image",{staticClass:"back",attrs:{src:t.$util.img("public/static/youpin/order/back.png"),mode:"aspectFit"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPopup()}}}),o("v-uni-text",[t._v("确认订单")])],1)],1):t._e(),o("v-uni-view",{staticClass:"wrapper",style:{marginTop:t.navHeight+"px"}},[o("v-uni-view",{staticClass:"address-wrap"},[o("v-uni-view",{staticClass:"icon"},[o("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/order/icon-no-pay-address.png"),mode:""}})],1),o("v-uni-view",{staticClass:"address-info",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getChooseAddress.apply(void 0,arguments)}}},[t.orderPaymentData.member_address?[o("v-uni-view",{staticClass:"info"},[o("v-uni-text",[t._v(t._s(t.orderPaymentData.member_address.name))]),o("v-uni-text",[t._v(t._s(t.orderPaymentData.member_address.mobile))])],1),o("v-uni-view",{staticClass:"detail"},[o("v-uni-text",[t._v(t._s(t.orderPaymentData.member_address.full_address)+" "+t._s(t.orderPaymentData.member_address.address))])],1)]:[o("v-uni-view",{staticClass:"address-empty"},[o("v-uni-text",[t._v("选择收货地址")])],1)],o("v-uni-view",{staticClass:"cell-more"},[o("v-uni-view",{staticClass:"iconfont iconright"})],1)],2)],1),t.orderPaymentData.shop_goods_list?t._l(t.orderPaymentData.shop_goods_list,(function(e,a){return o("v-uni-view",{key:a,staticClass:"site-wrap"},[o("v-uni-view",{staticClass:"site-header",staticStyle:{visibility:"hidden"}},[o("v-uni-view",{staticClass:"iconfont icondianpu"}),o("v-uni-text",{staticClass:"site-name"},[t._v(t._s(e.site_name))])],1),o("v-uni-view",{staticClass:"site-body"},t._l(e.goods_list,(function(e,i){return o("v-uni-view",{key:i,staticClass:"goods-wrap"},[o("v-uni-navigator",{staticClass:"goods-img",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,i)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-navigator",{staticClass:"goods-name",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[t._v(t._s(e.sku_name))]),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-view",[o("v-uni-text",[t._v(t._s(e.spec_name))])],1)],1),o("v-uni-view",{staticClass:"goods-price-box"},[o("v-uni-view",{staticClass:"box-left"},[parseFloat(e.multiple_discount_money)>0?o("v-uni-view",{staticClass:"share-discount"},[t._v("享多件折扣")]):t._e(),o("v-uni-view",{staticClass:"goods-price"},[o("v-uni-text",[o("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price"},[t._v(t._s(e.price))])],1)],1)],1),o("v-uni-view",{staticClass:"box-right"},[t.limitType?o("v-uni-view",{staticClass:"goods-number"},[t._v("x"+t._s(t.orderCreateData.combo_id?e.num:"1"))]):o("uni-number-box",{attrs:{min:1,type:"cart",max:e.stock,value:e.num,size:"small",modifyFlag:t.modifyFlag},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.cartNumChange(e,{siteIndex:a,goodsIndex:i})}}})],1)],1)],1)],1)})),1),o("v-uni-view",{staticClass:"order-money"},[o("v-uni-view",{staticClass:"order-cell"},[o("v-uni-text",{staticClass:"tit"},[t._v("运费")]),o("v-uni-view",{staticClass:"box align-right"},[parseFloat(e.delivery_money)>0?o("v-uni-text",{},[o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",[t._v(t._s(t._f("moneyFormat")(e.delivery_money)))])],1):o("v-uni-text",[t._v("包邮")])],1)],1)],1),o("v-uni-view",{staticClass:"site-footer"},[o("v-uni-view",{staticClass:"order-cell"},[o("v-uni-text",{staticClass:"tit"},[t._v("订单备注")]),o("v-uni-view",{staticClass:"box"},[o("v-uni-input",{staticClass:"ns-font-size-base",attrs:{type:"text",value:"",placeholder:"请填对本次交易的说明","placeholder-style":"{color:#CCCCCC}",maxlength:"50"},model:{value:t.orderCreateData.buyer_message[e.site_id],callback:function(o){t.$set(t.orderCreateData.buyer_message,e.site_id,o)},expression:"orderCreateData.buyer_message[siteItem.site_id]"}})],1)],1),e.promotion_money&&parseFloat(e.promotion_money)?o("v-uni-view",{staticClass:"order-cell"},[o("v-uni-text",{staticClass:"tit"},[t._v("分销商优惠")]),o("v-uni-view",{staticClass:"box align-right"},[o("v-uni-text",{staticStyle:{color:"var(--custom-brand-color)"}},[o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",[t._v(t._s(e.promotion_money))])],1)],1)],1):t._e(),1!=t.maidou.maidou_tag&&parseFloat(e.multiple_discount_money)?o("v-uni-view",{staticClass:"order-cell order-discount"},[o("v-uni-view",{staticClass:"tit"},[t._v("多件折扣")]),o("v-uni-view",{staticClass:"box align-right"},[o("v-uni-text",{staticStyle:{color:"var(--custom-brand-color)"}},[o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("-¥")]),o("v-uni-text",[t._v(t._s(e.multiple_discount_money))])],1)],1)],1):t._e(),t.orderCreateData.combo_id&&e.combo_cheap_price&&parseFloat(e.combo_cheap_price)?o("v-uni-view",{staticClass:"order-cell"},[o("v-uni-text",{staticClass:"tit"},[t._v("组合优惠")]),o("v-uni-view",{staticClass:"box align-right"},[o("v-uni-text",{staticStyle:{color:"var(--custom-brand-color)"}},[o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",[t._v(t._s(e.combo_cheap_price))])],1)],1)],1):t._e(),o("v-uni-view",{staticClass:"order-cell"},[o("v-uni-view",{staticClass:"box align-right order-pay"},[t._v("共"+t._s(e.goods_num)+"件商品"),o("v-uni-text",[t._v("小计："+t._s(t.$lang("common.currencySymbol"))),o("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(parseFloat(t.orderPaymentData.pay_money)>0?parseFloat(e.pay_money):e.balance_money))])],1)],1)],1)],1)],1)})):t._e(),1!=t.maidou.maidou_tag&&t.couponShow?o("v-uni-view",{staticClass:"site-wrap"},[o("v-uni-view",{staticClass:"site-footer",staticStyle:{"padding-bottom":"0"}},[o("v-uni-view",{staticClass:"order-cell my-coupon"},[o("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),o("v-uni-view",{staticClass:"box text-overflow",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSiteCoupon()}}},[0!=t.orderPaymentData.goodscoupon_id?o("v-uni-text",{staticClass:"ns-text-color-gray"},[o("v-uni-text",{staticClass:"inline"},[t._v("满减优惠")]),o("v-uni-text",{staticClass:"ns-text-color inline"},[t._v(t._s(t.orderPaymentData.goodscoupon_money)+" 元")])],1):t.useCoupon?o("v-uni-text",{staticClass:"ns-text-color-gray"},[t._v("有可用优惠券")]):o("v-uni-text",{staticClass:"ns-text-color-gray"},[t._v("无可用优惠券")])],1),o("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],1):t._e(),1==t.maidou.maidou_tag?o("v-uni-view",{staticClass:"site-wrap payment-methods"},t._l(t.paymentMethods,(function(e){return o("v-uni-view",{staticClass:"item",class:{disable:e.disable},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.changePayment(e)}}},[o("v-uni-image",{staticClass:"icon",attrs:{src:e.icon}}),o("v-uni-view",{staticClass:"title"},[o("v-uni-text",{staticClass:"text"},[t._v(t._s(e.name))]),o("v-uni-text",{staticClass:"desc"},[t._v(t._s(e.desc))])],1),o("uni-icons",{staticClass:"checkbox",attrs:{type:"checkbox-filled",color:t.paymentMethod==e.key?"var(--custom-brand-color)":"#ccc",size:"18"}})],1)})),1):o("v-uni-view",{staticClass:"site-wrap payment-methods"},t._l(t.otherPaymentMethods,(function(e){return o("v-uni-view",{staticClass:"item",class:{disable:e.disable},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.changePayment(e)}}},[o("v-uni-image",{staticClass:"icon",attrs:{src:e.icon}}),o("v-uni-view",{staticClass:"title"},[o("v-uni-text",{staticClass:"text"},[t._v(t._s(e.name))]),o("v-uni-text",{staticClass:"desc"},[t._v(t._s(e.desc))])],1),o("uni-icons",{staticClass:"checkbox",attrs:{type:"checkbox-filled",color:t.paymentMethod==e.key?"var(--custom-brand-color)":"#ccc",size:"18"}})],1)})),1),o("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[o("v-uni-view",{staticClass:"order-settlement-info"},[o("v-uni-view",{staticClass:"money-info"},[o("v-uni-text",[t._v("实付金额：")]),"WECHAT"==t.paymentMethod||"BALANCE"==t.paymentMethod?o("v-uni-text",{staticClass:"text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))),o("v-uni-text",{staticClass:"money pay_money"},[t._v(t._s(Number(t.orderPaymentData.pay_money)||Number(t.orderPaymentData.balance_money)))])],1):o("v-uni-text",{staticClass:"text-color"},[o("v-uni-text",{staticClass:"money pay_money"},[t._v(t._s(t.maidou.maidou_pay_num)+"迈豆")])],1)],1),1!=t.maidou.maidou_tag&&parseFloat(t.orderPaymentData.total_account_money)>0?o("v-uni-view",{staticClass:"total-discount"},[t._v("合计已优惠 ¥"+t._s(t.orderPaymentData.total_account_money))]):t._e()],1),o("v-uni-view",{staticClass:"submit-btn"},[o("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showGoWeixin.apply(void 0,arguments)}}},[t._v("确认支付")])],1)],1)],2),o("uni-popup",{ref:"couponPopup",staticClass:"coupon-popup-father",attrs:{type:"bottom"}},[o("v-uni-view",{staticClass:"coupon-popup popup"},[o("v-uni-view",{staticClass:"popup-header"},[o("v-uni-view",[o("v-uni-text",{staticClass:"tit"},[t._v("优惠券")])],1),o("v-uni-view",{staticClass:"coupon-instructions-close"},[o("v-uni-view",{staticClass:"align-right"},[o("v-uni-text",{staticClass:"iconfont iconguanbi coupon-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopupCoupon("couponPopup")}}})],1)],1)],1),o("v-uni-view",{staticClass:"flex-center ns-padding-bottom u-padding-30"},t._l(t.couponTitData,(function(e,a){return o("v-uni-view",{key:a,staticClass:"uni-tab-item",attrs:{"data-current":a}},[o("v-uni-view",{staticClass:"uni-tab-item-title",class:e.status==t.currentTab?"uni-tab-item-title-active high-text-color":"",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.ontabtapCoupon(e)}}},[t._v(t._s(e.name)),o("v-uni-view",{staticClass:"line"})],1)],1)})),1),o("v-uni-scroll-view",{staticClass:"popup-body",attrs:{"scroll-y":"true"}},[0==t.myCoupon.length?o("v-uni-view",{staticClass:"empty-box"},[o("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/empty_coupon.png"),mode:""}}),o("v-uni-view",{staticClass:"empty-info"},[t._v("暂无可用优惠券")])],1):t._e(),[o("v-uni-view",{staticClass:"coupon-box"},t._l(t.myCoupon,(function(e,a){return o("v-uni-view",{key:a,staticClass:"coupon-list",style:{"background-image":"url("+(t.currentTab?t.$util.img("public/static/youpin/coupon_ysy.png"):t.$util.img("public/static/youpin/coupon_no.png"))+")"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.selectCoupon(e)}}},[o("v-uni-view",{staticClass:"coupon-left"},[o("v-uni-view",[o("v-uni-text",{style:{color:t.currentTab?"var(--custom-brand-color)":"#fff"}},[t._v("￥")]),o("v-uni-text",{style:{color:t.currentTab?"var(--custom-brand-color)":"#fff"}},[t._v(t._s(parseFloat(e.money)))])],1)],1),o("v-uni-view",{staticClass:"coupon-right"},[o("v-uni-view",{staticClass:"coupon-name"},[t._v(t._s(e.goodscoupon_title))]),o("v-uni-view",{staticClass:"coupon-time"},[t._v("有效期："+t._s(e.end_time))]),t.currentTab?o("v-uni-image",{attrs:{src:t.selectCouponId==e.goodscoupon_id?t.$util.img("public/static/youpin/get.png"):t.$util.img("public/static/youpin/select.png"),mode:""}}):t._e()],1)],1)})),1)]],2),o("v-uni-view",{staticClass:"popup-footer"},[o("v-uni-view",{staticClass:"confirm-btn",class:0==t.currentTab?"disabled":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.popupConfirm("couponPopup")}}},[t._v("确定")])],1)],1)],1),o("uni-popup",{ref:"popupCoupon",staticClass:"my-popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[t._v("选择优惠券部分商品将无法参与“多件折扣”，是否使用优惠券？")]),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-view",{staticClass:"button white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.comfirmCoupon.apply(void 0,arguments)}}},[t._v("确定")]),o("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.comfirmCouponCancel.apply(void 0,arguments)}}},[t._v("取消")])],1)],1)],1),o("uni-popup",{ref:"popup",staticClass:"my-popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[t._v("是否放弃本次付款？")]),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-view",{staticClass:"button white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toBack()}}},[t._v("放弃")]),o("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup()}}},[t._v("继续付款")])],1)],1)],1),o("uni-popup",{ref:"popupToList",staticClass:"my-popup-dialog",attrs:{"mask-click":!1}},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[t._v(t._s(t.limit_pay_tips))]),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toWaitPayList()}}},[t._v("我知道了")])],1)],1)],1),o("uni-popup",{ref:"couponInstructions",staticClass:"coupon-instructions"},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("优惠券使用说明")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[o("v-uni-scroll-view",{staticClass:"coupon-instructions-popup-body",attrs:{"scroll-y":"true"}},[o("v-uni-rich-text",{attrs:{nodes:t.couponInstructionsContent}})],1)],1),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeCouponInstructionsPopup()}}},[t._v("我知道了")])],1)],1)],1),o("uni-popup",{ref:"payPassword",attrs:{custom:!0}},[o("v-uni-view",{staticClass:"pay-password"},[0==t.orderPaymentData.member_account.is_pay_password?[o("v-uni-view",{staticClass:"title"},[t._v("为了您的账户安全,请先设置您的支付密码")]),o("v-uni-view",{staticClass:"tips"},[t._v('可到"个人中心-设置-支付密码设置"中设置')]),o("v-uni-view",{staticClass:"btn ns-bg-color ns-border-color",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setPayPassword.apply(void 0,arguments)}}},[t._v("立即设置")]),o("v-uni-view",{staticClass:"btn white ns-border-color ns-text-color",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.noSet.apply(void 0,arguments)}}},[t._v("暂不设置")])]:[o("v-uni-view",{staticClass:"popup-title"},[o("v-uni-image",{staticClass:"cha_close",attrs:{src:t.$util.img("public/static/youpin/maidou/cha.png"),mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close_pay()}}}),o("v-uni-view",{staticClass:"title"},[t._v("请输入支付密码")])],1),1!=t.maidou.maidou_tag||"BALANCE"==t.paymentMethod?o("v-uni-view",{staticClass:"money-box"},[o("v-uni-view",{staticClass:"total-fee"},[t._v("总金额￥"+t._s(Number(t.orderPaymentData.pay_money)||Number(t.orderPaymentData.balance_money)))]),o("v-uni-view",{staticClass:"balance"},[t._v("(当前余额￥"+t._s(t.orderPaymentData.member_account.balance_money)+")")])],1):t._e(),"MAIDOU"==t.paymentMethod?o("v-uni-view",{staticClass:"money-box"},[o("v-uni-view",{staticClass:"total-fee"},[t._v("总金额"+t._s(t.maidou.maidou_pay_num)+"迈豆")]),o("v-uni-view",{staticClass:"balance"},[t._v("(当前余额"+t._s(t.maidou.canuse_maidou)+"迈豆)")])],1):t._e(),o("v-uni-view",{staticClass:"password-wrap"},[o("myp-one",{ref:"input",attrs:{maxlength:6,"is-pwd":!0,"auto-focus":t.isFocus,type:"box"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.input.apply(void 0,arguments)}}}),t.errMsg?o("v-uni-view",{staticClass:" ns-text-color ns-font-size-sm forget-password error-tips"},[t._v(t._s(t.errMsg))]):t._e(),o("v-uni-view",{staticClass:"align-right"},[o("v-uni-text",{staticClass:"ns-text-color ns-font-size-sm forget-password",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setPayPassword.apply(void 0,arguments)}}},[t._v("忘记密码")])],1)],1)]],2)],1),o("uni-popup",{ref:"popupGoWeixin",staticClass:"my-popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog"},[o("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),o("v-uni-view",{staticClass:"popup-dialog-body"},[t._v("需要跳转到微信APP，使用先迈小程序支付订单金额")]),o("v-uni-view",{staticClass:"popup-dialog-footer"},[o("v-uni-button",{staticClass:"button white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.popupGoWeixin.close()}}},[t._v("取消")]),o("v-uni-view",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.comfirmGoWeixin.apply(void 0,arguments)}}},[t._v("去支付")])],1)],1)],1),o("loading-cover",{ref:"loadingCover"})],1)},n=[]},fcd5:function(t,e,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("22b6"),o("bf0f"),o("2797"),o("5ef2"),o("5c47"),o("dfcf"),o("aa9c"),o("d4b5"),o("fd3c"),o("c9b5"),o("ab80"),o("dc8a"),o("e838"),o("0506"),o("8f71");var i=a(o("2634")),n=a(o("2fdc")),s=(a(o("cbf3")),a(o("cd3a")),a(o("5aed")),a(o("7c8d"))),r=a(o("85bf")),d={data:function(){return{orderCreateData:{is_balance:0,pay_password:"",platform_coupon_id:0,buyer_message:{}},errMsg:"",orderPaymentData:{member_account:{balance:0,is_pay_password:0},platform_coupon_list:[]},isSub:!1,sitePromotion:[],siteDelivery:{site_id:0,data:[]},siteCoupon:{site_id:0,data:[]},shopCoupon:{},myCoupon:[],couponInfo:{},couponUpdate:!0,isFocus:!1,tempData:null,couponShow:!1,useCoupon:!1,selectCouponId:"",discount_remind:0,selectCouponMoney:"0.00",selectCouponHaveChoose:!1,selectPlatCouponId:0,selectPlatCouponMoney:"0.00",limit_pay_tips:"",limitType:!1,activity_id:"",activity_type:"share",push_data:{},isSubscribed:!1,modifyFlag:!1,isFireLoad:!0}},methods:{openPopup:function(t){"PlatcouponPopup"==t&&(this.selectPlatCouponId=this.orderPaymentData.platform_coupon_id,this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_money),this.$refs[t].open()},closePopup:function(t){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[t].close()},selectAddress:function(){this.isFireLoad=!0,this.$util.redirectTo("/otherpages/member/address/address",{back:"/pages/order/payment/payment"})},getAddressList:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){var o,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=[],e.next=3,t.$api.sendRequest({url:"/api/memberaddress/page",async:!1,data:{page:1,page_size:20}});case 3:return a=e.sent,0==a.code&&(o=a.data.list),e.abrupt("return",o);case 6:case"end":return e.stop()}}),e)})))()},getOrderPaymentData:function(t){var e=this;if(this.orderCreateData){var o=this.$store.state.share_member_id;o&&(this.orderCreateData.share_member_id=o),this.selectCouponId&&(this.orderCreateData.coupon_id=this.selectCouponId),this.activity_id&&(this.orderCreateData.activity_id=this.activity_id,this.orderCreateData.activity_type=this.activity_type);var a=uni.getStorageSync("member_address");a&&(this.orderCreateData.member_address_id=a.id),this.$api.sendRequest({url:"/api/ordercreate/payment",data:this.orderCreateData,success:function(o){if(o.code>=0){e.orderPaymentData=o.data,e.limit_pay_tips=o.data.limit_pay_tips,a?a.id!=e.orderPaymentData.member_address.id&&uni.setStorageSync("member_address",e.orderPaymentData.member_address):uni.setStorageSync("member_address",e.orderPaymentData.member_address),o.data.coupon_list&&o.data.coupon_list.coupon_id&&!e.selectCouponHaveChoose?(e.orderCreateData.coupon_id=o.data.coupon_list.coupon_id,e.selectCouponId=o.data.coupon_list.coupon_id):(e.orderCreateData.coupon_id=e.selectCouponId,e.selectCouponHaveChoose=!0),e.handlePaymentData(t);var i=Object.values(o.data.shop_goods_list);i.length>0&&i.forEach((function(t){var o=t.goods_list_str;e.splitfn(o,-1!==o.indexOf(";")?";":":")})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.$util.showToast({title:o.message||"未获取到创建订单所需数据!！",success:function(){uni.removeStorageSync("member_address")}})},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()},complete:function(){uni.hideLoading(),e.$nextTick((function(){e.modifyFlag=!1}))}})}else uni.showModal({title:"提示",content:"订单信息已过期，请到订单列表查看最新状态",confirmText:"查看订单",showCancel:!1,success:function(t){e.$util.redirectTo("/pages/order/list/list?status=all",{},"redirectTo")},fail:function(t){}})},splitfn:function(t,e){var o=[],a=[];";"==e?t.split(e).forEach((function(t){t.split(":").forEach((function(t,e){e%2?a.push(t):o.push(t)}))})):(o=[t.split(":")[0]],a=[t.split(":")[1]]),o.length>0&&a.length>0&&this.$buriedPoint.submitOrderContent({sku_ids:o,num:a,pages:1})},getOrderCouponList:function(){var t=this,e=[],o=[],a=[],i=JSON.parse(JSON.stringify(this.orderPaymentData));for(var n in i.shop_goods_list)a.push(i.shop_goods_list[n]);for(var s=0;s<a.length;s++)a[s].goods_list.map((function(t){e.push(t.sku_id),o.push(t.num)}));this.$api.sendRequest({url:this.$apiUrl.orderCoupon,data:{sku_ids:e.toString(),sku_nums:o.toString()},success:function(e){0==e.code&&(t.shopCoupon=e.data)},fali:function(t){}})},ontabtapCoupon:function(t){uni.showLoading({title:"加载中"}),this.currentTab=t.status,1==this.currentTab?this.myCoupon=this.couponInfo.use:0==this.currentTab&&(this.myCoupon=this.couponInfo.no_use),setTimeout((function(){uni.hideLoading()}),100)},handlePaymentData:function(t){var e=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.buyer_message={},this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="";var o=uni.getStorageSync("member_address");o&&(this.orderCreateData.member_address_id=o.id);var a=this.orderPaymentData,i=(new Date).getHours().toString(),n=(new Date).getMinutes().toString();1==i.length&&(i="0"+i),1==n.length&&(n="0"+n);var s=i+":"+n;Object.keys(a.shop_goods_list).forEach((function(t,o){var i=a.shop_goods_list[t];e.orderCreateData.delivery[t]={},i.local_config&&(i.local_config.info&&1==i.local_config.info.time_is_open?(e.orderCreateData.delivery[t].showTimeBar=!0,e.orderCreateData.delivery[t].buyer_ask_delivery_time=s):e.orderCreateData.delivery[t].showTimeBar=!1),void 0!=i.express_type[0]&&(e.orderCreateData.delivery[t].delivery_type=i.express_type[0].name,e.orderCreateData.delivery[t].delivery_type_name=i.express_type[0].title,e.orderCreateData.delivery[t].store_id=0,e.orderCreateData.delivery[t].store_id=0,"store"==i.express_type[0].name&&void 0!=i.express_type[0].store_list[0]&&(e.orderCreateData.delivery[t].store_id=i.express_type[0].store_list[0].store_id)),e.orderCreateData.coupon[t]={},i.coupon_list&&void 0!=i.coupon_list[0]&&(e.orderCreateData.coupon[t].coupon_id=i.coupon_list[0].coupon_id,e.selectCouponId=i.coupon_list[0].coupon_id,e.orderCreateData.coupon[t].coupon_money=i.coupon_list[0].money,e.selectCouponMoney=i.coupon_list[0].coupon_id),e.orderCreateData.buyer_message[t]=""})),this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={mobile:""}),this.orderPaymentData.platform_coupon_list&&this.orderPaymentData.platform_coupon_list.length>0&&(this.orderPaymentData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.orderCreateData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.orderPaymentData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money,this.orderCreateData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money,this.selectPlatCouponId=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_list[0].money),Object.assign(this.orderPaymentData,this.orderCreateData),t?this.orderCouponList():this.orderCalculate()},orderCouponList:function(){var t=this,e=this.$util.deepClone(this.orderCreateData);e.delivery=JSON.stringify(e.delivery),e.coupon=JSON.stringify(e.coupon),e.buyer_message=JSON.stringify(e.buyer_message),e.goodscoupon_id=this.selectCouponId?this.selectCouponId:0,this.$api.sendRequest({url:"/api/ordercreate/calculate",data:e,success:function(e){if(e.code>=0){var o=e.data.goods_coupons.use;if(t.selectCouponId&&o.length){var a=!1;o.forEach((function(e){e.goodscoupon_id==t.selectCouponId&&(a=!0)})),t.selectCouponId=a?t.selectCouponId:0}else t.selectCouponId=0;t.orderCalculate()}}})},orderCalculate:function(){var t=this,e=this.$util.deepClone(this.orderCreateData);e.delivery=JSON.stringify(e.delivery),e.coupon=JSON.stringify(e.coupon),e.buyer_message=JSON.stringify(e.buyer_message),e.goodscoupon_id=this.selectCouponId?this.selectCouponId:0,this.$api.sendRequest({url:"/api/ordercreate/calculate",data:e,success:function(e){if(e.code>=0){t.couponInfo=e.data.goods_coupons;var o=0;if(Object.keys(e.data.shop_goods_list).forEach((function(a,i){var n=e.data.shop_goods_list[a];if(parseFloat(n.multiple_discount_money)>0)return o=1,t.orderPaymentData.total_account_money_show=1,t.myCoupon=t.couponInfo.use,!1})),e.data.goods_coupons&&""==t.selectCouponId&&t.couponUpdate&&!o&&t.currentTab&&(t.myCoupon=t.couponInfo.use,t.myCoupon.length))return t.selectCouponId=t.myCoupon[0].goodscoupon_id,t.couponUpdate=!1,t.orderCalculate(),!1;(e.data.goods_coupons.no_use.length||e.data.goods_coupons.use.length)&&(t.couponShow=!0),t.useCoupon=!!e.data.goods_coupons.use.length,1==t.currentTab?t.myCoupon=t.couponInfo.use:0==t.currentTab&&(t.myCoupon=t.couponInfo.no_use),t.orderPaymentData.delivery_money=e.data.delivery_money,t.orderPaymentData.coupon_money=e.data.coupon_money,t.orderPaymentData.invoice_money=e.data.invoice_money,t.orderPaymentData.promotion_money=e.data.promotion_money,t.orderPaymentData.order_money=e.data.order_money,t.orderPaymentData.balance_money=e.data.balance_money,t.orderPaymentData.pay_money=e.data.pay_money,t.orderPaymentData.goods_money=e.data.goods_money,t.orderPaymentData.goodscoupon_id=e.data.goodscoupon_id,t.orderPaymentData.goodscoupon_money=e.data.goodscoupon_money,t.orderPaymentData.total_account_money=e.data.total_account_money,t.member_account=e.data.member_account,t.maidou={maidou_tag:e.data.maidou_tag,maidou_pay_num:e.data.maidou_pay_num,maidou_pay:e.data.maidou_pay,canuse_maidou:e.data.member_account.canuse_maidou},Object.keys(e.data.shop_goods_list).forEach((function(o,a){t.$set(t.orderPaymentData.shop_goods_list,o,e.data.shop_goods_list[o])}))}else t.$util.showToast({title:e.message})}})},orderCreate:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){var o,a,s,r;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.verify()){e.next=29;break}if(!t.isSub){e.next=3;break}return e.abrupt("return");case 3:if(t.isSub=!0,t.isSubscribed){e.next=16;break}return o="",o="WECHAT"==t.paymentMethod?"order_pay_before":"other_pay_before",e.prev=7,e.next=10,t.$util.subscribeMessage({source:"order",source_id:"",scene_type:o},!0);case 10:t.push_data=e.sent,t.isSubscribed=!0,e.next=16;break;case 14:e.prev=14,e.t0=e["catch"](7);case 16:uni.showLoading({mask:!0,title:"加载中"}),a=uni.getStorageSync("member_address"),s=t.$util.deepClone(t.orderCreateData),r=t.$store.state.share_member_id,r&&(s.share_member_id=r),t.orderPaymentData.goodscoupon_id&&(s.goodscoupon_id=t.orderPaymentData.goodscoupon_id),s.delivery=JSON.stringify(s.delivery),s.coupon=JSON.stringify(s.coupon),s.member_address=JSON.stringify(a.id),s.buyer_message=JSON.stringify(s.buyer_message),s.pay_type=t.paymentMethod,t.activity_id&&(s.activity_id=t.activity_id,s.activity_type=t.activity_type),t.$api.sendRequest({url:"/api/ordercreate/create",data:s,success:function(){var e=(0,n.default)((0,i.default)().mark((function e(o){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(o.code>=0)){e.next=11;break}return uni.removeStorage({key:"orderCreateData",success:function(){}}),"MAIDOU"!=t.paymentMethod&&t.createBuriedPoint(o.data.out_trade_no,3),t.push_data.source_id=o.data.order_ids,e.next=6,t.$util.subscribeMessageMethod(t.push_data);case 6:uni.hideLoading(),1==o.data.is_free?("MAIDOU"!=t.paymentMethod&&t.createBuriedPoint(o.data.out_trade_no,11),t.$util.redirectTo("/pages/pay/result/result?order_ids="+o.data.order_ids,{code:o.data.out_trade_no},"redirectTo")):t.orderPayPay(o.data.out_trade_no,o.data.order_ids),uni.removeStorageSync("member_address"),e.next=15;break;case 11:t.isSub=!1,uni.hideLoading(),t.$refs.payPassword&&(t.isFocus=!1,t.$refs.payPassword.close()),10==o.data.error_code||12==o.data.error_code?uni.showModal({title:"订单未创建",content:o.message,confirmText:"去设置",success:function(e){e.confirm&&t.selectAddress()}}):t.$util.showToast({title:o.message});case 15:t.getCartNumber();case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fail:function(e){uni.hideLoading(),t.isSub=!1}});case 29:case"end":return e.stop()}}),e,null,[[7,14]])})))()},orderPayPay:function(t,e){var o=this;uni.showLoading({mask:!0,title:"加载中"});this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:t,pay_type:"adapay"},success:function(e){if(uni.hideLoading(),e.code>=0){o.$util.wechatPay(e.data.pay_type,"adapay"==e.data.pay_type?e.data.payment:e.data.pay_info,(function(e){"MAIDOU"!=o.paymentMethod&&o.createBuriedPoint(t,11),uni.hideLoading(),o.$util.redirectTo("/pages/order/list/list?status=all",{},"redirectTo")}),(function(e){"MAIDOU"!=o.paymentMethod&&o.createBuriedPoint(t,9001),uni.hideLoading(),o.$refs.popupToList.open()}),(function(t){setTimeout((function(){o.$util.redirectTo("/pages/order/list/list",{},"redirectTo")}),2e3)}));var a=uni.getStorageSync("activity_id");if(a){var i=[],n=o.orderPaymentData.shop_goods_list;n=JSON.parse(JSON.stringify(n)),n=Object.values(n),Object.keys(n).forEach((function(t){for(var e=0;e<n[t].goods_list.length;e++)i.push(n[t].goods_list[e].sku_id)})),console.log("sku_id_arr"),console.log(i);for(var s=0;s<i.length;s++)delete a[i[s]];"{}"!=JSON.stringify(a)?uni.setStorageSync("activity_id",a):(uni.removeStorageSync("activity_id"),console.log("activity_id"),console.log(a))}}else console.log(e),e.message?o.$util.showToast({title:e.message}):uni.hideLoading(),o.$refs.popupToList&&o.$refs.popupToList.open()},fail:function(t){o.$util.showToast({title:"request:fail"})}})},createBuriedPoint:function(t,e){this.$buriedPoint.orderStatus({out_trade_no:t,status:e})},verify:function(){var t=this;if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!/^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/.test(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if(!this.orderPaymentData.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;var e=!0;for(var o in this.orderCreateData.delivery){if("{}"==JSON.stringify(this.orderCreateData.delivery[o])){e=!1,this.$util.showToast({title:'店铺"'+this.orderPaymentData.shop_goods_list[o].site_name+'"未设置配送方式'});break}if("store"==this.orderCreateData.delivery[o].delivery_type&&0==this.orderCreateData.delivery[o].store_id){e=!1,this.$util.showToast({title:'店铺"'+this.orderPaymentData.shop_goods_list[o].site_name+'"没有可提货的门店,请选择其他配送方式'});break}}if(!e)return!1}return"BALANCE"==this.paymentMethod&&(this.orderCreateData.is_balance=1),1!=this.orderCreateData.is_balance&&"MAIDOU"!=this.paymentMethod||""!=this.orderCreateData.pay_password||(setTimeout((function(){t.$refs.input&&t.$refs.input.clear()}),0),this.openPasswordPopup(),!1)},openSitePromotion:function(t){this.sitePromotion=t,this.$refs.sitePromotionPopup.open()},openSiteDelivery:function(t,e){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.siteDelivery.site_id=t,this.siteDelivery.data=e,this.$refs.deliveryPopup.open()},selectDeliveryType:function(t){this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type=t.name,this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type_name=t.title,"store"==t.name&&void 0!=t.store_list[0]&&(this.orderCreateData.delivery[this.siteDelivery.site_id].store_id=t.store_list[0].store_id),Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},selectPickupPoint:function(t){this.orderCreateData.delivery[this.siteDelivery.site_id].store_id=t,Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},openSiteCoupon:function(){this.$refs.couponPopup.open()},selectCoupon:function(t){t.is_use&&(this.selectCouponId!=t.goodscoupon_id?(this.selectCouponId=t.goodscoupon_id,this.discount_remind=t.discount_remind):(this.selectCouponId="",this.discount_remind=0),Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate())},popupConfirm:function(t,e){return 0!=this.currentTab&&(this.discount_remind?(this.$refs["popupCoupon"].open(),!1):(this.$refs[t].close(),this.selectCouponHaveChoose=!0,void this.orderCalculate()))},comfirmCoupon:function(){this.$refs["popupCoupon"].close(),this.$refs["couponPopup"].close(),this.selectCouponHaveChoose=!0,this.orderCalculate()},comfirmCouponCancel:function(){this.$refs["popupCoupon"].close(),this.selectCouponId="",this.discount_remind=0},imageError:function(t,e){this.orderPaymentData.shop_goods_list[t].goods_list[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},getCartNumber:function(){uni.getStorageSync("token")&&this.$store.dispatch("getCartNumber")},openPasswordPopup:function(){var t=this;this.$refs.payPassword.open(),setTimeout((function(){t.isFocus=!0}),500)},showGoWeixin:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(uni.getStorageSync("member_address")){e.next=3;break}return t.$util.showToast({title:"请先选择地址！"}),e.abrupt("return",!1);case 3:if("h5"!=t.$util.getPlatform()||"WECHAT"!=t.paymentMethod||!t.isOnXianMaiApp){e.next=7;break}t.$refs.popupGoWeixin.open(),e.next=9;break;case 7:return e.next=9,t.orderCreate();case 9:case"end":return e.stop()}}),e)})))()},comfirmGoWeixin:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$refs.popupGoWeixin.close(),e.next=3,t.orderCreate();case 3:case"end":return e.stop()}}),e)})))()},setPayPassword:function(){this.$refs.payPassword.close(),this.$util.redirectTo("/otherpages/member/setting/setting_password",{back:"/pages/order/payment/payment",phonenum:this.orderPaymentData.member_account.mobile})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(t){var e=this;this.errMsg="",6==t.length&&(uni.showLoading({title:"加载中...",mask:!0}),this.$api.sendRequest({url:s.default.checkpaypasswordUrl,data:{pay_password:t},success:function(o){o.code>=0?(e.orderCreateData.pay_password=t,e.orderCreate()):(uni.hideLoading(),e.errMsg=o.message)},fail:function(t){uni.hideLoading()}}))},init:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){var o,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$langConfig.refresh(),uni.getStorageSync("token")){e.next=5;break}t.$util.redirectTo("/otherpages/shop/home/<USER>"),e.next=15;break;case 5:if(o=uni.getStorageSync("member_address"),!o){e.next=11;break}return e.next=9,t.getAddressList();case 9:a=e.sent,a.length?a.filter((function(t){return o.id==t.id})).length<1&&uni.removeStorageSync("member_address"):uni.removeStorageSync("member_address");case 11:t.orderPaymentData={member_account:{balance:0}},t.orderCreateData={is_balance:0,pay_password:"",coupon_id:t.selectCouponId,buyer_message:{}},t.isSub||(t.orderCreateData=uni.getStorageSync("orderCreateData"),t.getOrderPaymentData());case 15:case"end":return e.stop()}}),e)})))()},changePayment:function(t){"BALANCE"==t.key?this.orderCreateData.is_balance=t.disable||1==this.orderCreateData.is_balance?0:1:this.orderCreateData.is_balance=0,t.disable||(this.paymentMethod=t.key),this.$forceUpdate()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onLoad:function(t){var e=this;return(0,n.default)((0,i.default)().mark((function o(){var a,n;return(0,i.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,uni.getStorageSync("orderCreateData").limitType;case 2:e.limitType=o.sent,t.sku_id&&t.num&&(a={sku_id:t.sku_id,num:t.num},uni.setStorageSync("orderCreateData",a)),n=uni.getStorageSync("activity_id"),n&&(e.activity_id=JSON.stringify(n));case 6:case"end":return o.stop()}}),o)})))()},onShow:function(){var t=this;return(0,n.default)((0,i.default)().mark((function e(){var o;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r.default.wait_staticLogin_success();case 2:t.ischoiceWechatAdder?o=setInterval((function(){t.postWechatAdder&&(console.log("this.choiceWechatAdderError",t.choiceWechatAdderError),t.choiceWechatAdderError&&t.choiceWechatAdderError&&(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.$util.showToast({title:"获取微信地址失败，请手动添加地址",success:function(){setTimeout((function(){t.selectAddress()}),1500)}})),t.ischoiceWechatAdder=!1,t.postWechatAdder=!1,t.choiceWechatAdderError=!1,clearInterval(o),t.init())}),100):t.init();case 3:case"end":return e.stop()}}),e)})))()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},onShareAppMessage:function(t){var e=this.getSharePageParams(),o=e.title,a=e.link,i=e.imageUrl;e.query;return this.$buriedPoint.pageShare(a,i,o)},filters:{moneyFormat:function(t){return parseFloat(t).toFixed(2)},promotion:function(t){var e="";return t&&Object.keys(t).forEach((function(o){e+=t[o].content+"　"})),e}}};e.default=d},fefc:function(t,e,o){"use strict";o.r(e);var a=o("b8ea"),i=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a}}]);