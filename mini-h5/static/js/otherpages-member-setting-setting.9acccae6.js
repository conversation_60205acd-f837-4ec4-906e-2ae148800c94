(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-setting-setting"],{"0d02":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o(n("2634")),a=o(n("2fdc")),c={data:function(){return{list:[],img:["public/static/youpin/coupon_bg_1.png","public/static/youpin/coupon_bg_2.png","public/static/youpin/coupon_bg_3.png","public/static/youpin/coupon_bg_4.png"],boxClass:["box1","box2","box3","box4"]}},onLoad:function(){this.$util.toShowCouponPopup(this)},methods:{open:function(){this.listInfo()},listInfo:function(){var t=this;return(0,a.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 3:n=e.sent,n.data.length&&(t.list=n.data,t.$refs.coupon.open()),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})}}};e.default=c},"278d":function(t,e,n){"use strict";n.r(e);var o=n("b815"),i=n("4f5d");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("fa66");var c=n("828b"),s=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,"77d95a10",null,!1,o["a"],void 0);e["default"]=s.exports},"2d01":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"313a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={uniPopup:n("5e99").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("uni-popup",{ref:"coupon",attrs:{custom:!0,"mask-click":!1}},[t.list.length?n("v-uni-view",{staticClass:"coupon-model",class:t.list.length<4?t.boxClass[t.list.length-1]:t.boxClass[3],style:{"background-image":"url("+(t.list.length<4?t.$util.img(t.img[t.list.length-1]):t.$util.img(t.img[3]))+")"}},[n("v-uni-view",{staticClass:"coupon-header"},[n("v-uni-view",{staticClass:"title"},[t._v("恭喜您获得以下优惠券")]),n("v-uni-view",{staticClass:"tip"},[t._v("马上去使用吧！")])],1),n("v-uni-view",{staticClass:"coupon-box"},t._l(t.list,(function(e,o){return n("v-uni-view",{key:o,staticClass:"coupon-list",style:{"background-image":"url("+t.$util.img("public/static/youpin/coupon_border.png")+")"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toGoodList(e)}}},[n("v-uni-view",{staticClass:"left"},[n("v-uni-view",{staticClass:"info"},[n("v-uni-view",[t._v("¥")]),e.money<100?[0==e.money.split(".")[1]?n("v-uni-view",[t._v(t._s(e.money.split(".")[0]))]):t._e(),e.money.split(".")[1]>0?n("v-uni-view",[t._v(t._s(e.money.split(".")[0])+"."),n("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])]):t._e()]:n("v-uni-view",{staticClass:"money-thousand"},[t._v(t._s(e.money.split(".")[0])+"."),n("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])])],2)],1),n("v-uni-view",{staticClass:"right"},[n("v-uni-view",[n("v-uni-view",{staticClass:"name"},[t._v(t._s(e.desc))]),n("v-uni-view",{staticClass:"time h5-time"},[t._v("有效期至"+t._s(e.end_time))])],1),n("v-uni-view",{staticClass:"btn"},[n("v-uni-view",{staticClass:"h5-btn"},[t._v("去使用")])],1)],1)],1)})),1)],1):t._e(),n("v-uni-image",{staticClass:"pop-ad-info-close",attrs:{src:t.$util.img("public/static/youpin/icon-close-overlay.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.coupon.close()}}})],1)],1)},a=[]},"46e8":function(t,e,n){var o=n("c881");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("f73cf6f6",o,!0,{sourceMap:!1,shadowMode:!1})},"4f5d":function(t,e,n){"use strict";n.r(e);var o=n("8f20"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},"50b4":function(t,e,n){"use strict";n.r(e);var o=n("0d02"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},8416:function(t,e,n){"use strict";var o=n("46e8"),i=n.n(o);i.a},8765:function(t,e,n){"use strict";n.r(e);var o=n("313a"),i=n("50b4");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("8416");var c=n("828b"),s=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,"7ca63514",null,!1,o["a"],void 0);e["default"]=s.exports},"8f20":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o(n("2634")),a=o(n("2fdc")),c=o(n("2d01")),s={components:{},mixins:[c.default],data:function(){return{phonenum:"",token:""}},onLoad:function(t){this.phonenum=t.phonenum},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"设置"}),this.token=uni.getStorageSync("token"),uni.getStorageSync("is_register")&&(this.$util.toShowCouponPopup(this),uni.removeStorageSync("is_register"))},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},watch:{},methods:{tosetPassword:function(){this.$util.redirectTo("/otherpages/member/setting/setting_password?phonenum=".concat(this.phonenum))},changeAccount:function(){this.$refs.changeAccount.open()},getphonenumber:function(t){var e=this;return(0,a.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$refs.changeAccount.close(),t.next=3,e.$util.clearUserInfo();case 3:e.$util.toShowLoginPopup(e,null,"/otherpages/shop/home/<USER>");case 4:case"end":return t.stop()}}),t)})))()},logout:function(){var t=this;uni.showModal({title:"提示",content:"是否退出登录",confirmText:"确定",cancelText:"取消",success:function(){var e=(0,a.default)((0,i.default)().mark((function e(n){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!n.confirm){e.next=4;break}return e.next=3,t.$util.clearUserInfo();case 3:t.$util.redirectTo("/otherpages/shop/home/<USER>",{},"redirectTo");case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),n=e.title,o=e.link,i=e.imageUrl;e.query;return this.$buriedPoint.pageShare(o,i,n)}};e.default=s},b815:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={uniPopup:n("5e99").default,ydAuthPopup:n("161f").default,uniCouponPop:n("8765").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{class:t.themeStyle,style:[t.themeColorVar]},[n("v-uni-view",{staticClass:"setting",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tosetPassword()}}},[n("v-uni-text",{staticClass:"zhifu"},[t._v(t._s(t.$lang("zhifu")))]),n("v-uni-text",{staticClass:"right"},[t._v(">")])],1),t.token?n("v-uni-view",{staticClass:"setting",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.logout.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"zhifu"},[t._v("退出登录")]),n("v-uni-text",{staticClass:"right"},[t._v(">")])],1):t._e(),n("uni-popup",{ref:"changeAccount",staticClass:"pop-ad",attrs:{type:"center",maskClick:!1}},[n("v-uni-view",{staticClass:"account"},[n("h3",[t._v("提示")]),n("p",[t._v("是否退出登录切换其他账号")]),n("v-uni-view",{staticClass:"account-op"},[n("v-uni-button",{staticClass:"confirm",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)}}},[t._v("确定")]),n("v-uni-button",{staticClass:"cancel",attrs:{type:"button"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.changeAccount.close()}}},[t._v("取消")])],1)],1)],1),n("yd-auth-popup",{ref:"ydauth"}),n("uni-coupon-pop",{ref:"couponPop"})],1)},a=[]},c1f5:function(t,e,n){var o=n("ded4");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("61e70694",o,!0,{sourceMap:!1,shadowMode:!1})},c881:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7ca63514]{width:100%;text-align:center}.coupon-model[data-v-7ca63514]{display:flex;flex-direction:column;position:relative;z-index:111;width:%?620?%;background-size:cover;background-position:50%}.coupon-model .coupon-header[data-v-7ca63514]{background-size:cover;background-position:50%;margin-bottom:%?117?%}.coupon-model .title[data-v-7ca63514]{font-size:%?40?%;line-height:%?52?%;background-image:-webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:%?124?% 0 %?14?%;text-align:center;font-weight:700}.coupon-model .tip[data-v-7ca63514]{font-size:%?30?%;line-height:%?32?%;background-image:-webkit-linear-gradient(0deg,#fc5a50,#ff561a 46.75293%,#ff2637);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-align:center}.coupon-model .coupon-box[data-v-7ca63514]{flex:1;padding:0 %?54?% 0;background-size:100% 100%;background-position:50%;position:relative;margin-bottom:28px;overflow-y:auto}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]{display:flex;background-size:cover;background-position:50%;height:%?120?%;margin-bottom:%?20?%;position:relative;z-index:11}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]:last-child{margin-bottom:0}.coupon-model .coupon-box .coupon-list .left[data-v-7ca63514]{width:70px;display:flex;align-items:center;justify-content:center}.coupon-model .coupon-box .coupon-list .left .info[data-v-7ca63514]{display:flex;align-items:baseline}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:first-child{display:inline-block;font-size:%?26?%;color:#eb0000}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:last-child{display:inline-block;font-size:%?48?%;color:#eb0000;line-height:%?80?%}.coupon-model .coupon-box .coupon-list .left .point-class[data-v-7ca63514]{font-size:%?35?%}.coupon-model .coupon-box .coupon-list .left .money-thousand[data-v-7ca63514]{font-size:%?41?%!important}.coupon-model .coupon-box .coupon-list .left .money-thousand > span[data-v-7ca63514]{font-size:%?29?%}.coupon-model .coupon-box .coupon-list .right[data-v-7ca63514]{width:%?238?%;flex:1;display:flex;align-items:center;position:relative;margin-left:%?18?%}.coupon-model .coupon-box .coupon-list .right > uni-view[data-v-7ca63514]:first-child{flex:1;overflow:hidden;height:100%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .name[data-v-7ca63514]{font-size:%?24?%;line-height:%?36?%;padding:%?24?% 0 %?8?%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .time[data-v-7ca63514]{font-size:%?18?%;line-height:%?30?%;color:#999;white-space:nowrap}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .h5-time[data-v-7ca63514]{display:flex;width:119px;margin-left:-13px;-webkit-transform:scale(.78);transform:scale(.78)}.coupon-model .coupon-box .coupon-list .right .btn[data-v-7ca63514]{display:flex;justify-content:center;align-items:center;width:%?94?%;height:%?38?%;background:linear-gradient(90deg,#ffab37,#fff594);border-radius:19px;font-size:%?24?%;color:#822d02;margin:0 %?10?% 0 0}.coupon-model .coupon-box .coupon-list .right .h5-btn[data-v-7ca63514]{-webkit-transform:scale(.8);transform:scale(.8)}.coupon-model .coupon_bg[data-v-7ca63514]{position:absolute;top:%?270?%;width:100%;height:%?153?%;z-index:-1}.coupon-model .coupon_footer[data-v-7ca63514]{width:100%;height:%?51?%}.box1[data-v-7ca63514]{height:%?565?%}.box2[data-v-7ca63514]{height:%?660?%}.box3[data-v-7ca63514]{height:%?800?%}.box4[data-v-7ca63514]{height:%?850?%}.pop-ad-info-close[data-v-7ca63514]{width:%?88?%;height:%?88?%;display:block;margin:%?60?% auto 0}',""]),t.exports=e},ded4:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-77d95a10]{width:100%;text-align:center}.setting[data-v-77d95a10]{background:#fff;padding:%?30?%;display:flex;align-items:center;justify-content:space-between;margin-top:%?30?%}.setting .zhifu[data-v-77d95a10]{font-size:%?32?%;color:#333}.setting .right[data-v-77d95a10]{font-size:%?32?%;color:#ccc}.account[data-v-77d95a10]{width:%?540?%;background:#fff;border-radius:%?20?%}.account h3[data-v-77d95a10]{margin:0;font-size:%?36?%;font-weight:700;color:#333;text-align:center;margin-top:%?35?%}.account p[data-v-77d95a10]{margin:0;font-size:%?28?%;font-weight:500;color:#666;text-align:center;margin-top:%?36?%}.account-op[data-v-77d95a10]{display:flex;justify-content:center;border-top:%?2?% solid #eee;box-sizing:border-box;margin-top:%?39?%}.account-op uni-button[data-v-77d95a10]{width:50%;font-size:1rem;font-weight:500;box-sizing:border-box;border-radius:0;margin:0;height:100%;background:transparent}.account-op uni-button[data-v-77d95a10]:first-child{border-right:%?2?% solid #eee}.account-op .confirm[data-v-77d95a10]{color:var(--custom-brand-color)}.account-op .cancel[data-v-77d95a10]{color:#ccc}',""]),t.exports=e},fa66:function(t,e,n){"use strict";var o=n("c1f5"),i=n.n(o);i.a}}]);