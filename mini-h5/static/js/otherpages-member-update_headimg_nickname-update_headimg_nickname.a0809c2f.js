(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-update_headimg_nickname-update_headimg_nickname"],{"10ec":function(e,t,a){"use strict";var n=a("f9fe"),i=a.n(n);i.a},"2d01":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"327f":function(e,t,a){"use strict";var n=a("a971"),i=a.n(n);i.a},"37ff":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},i=[]},"514c":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-4b38ad6a]{width:100%;text-align:center}.main[data-v-4b38ad6a]{width:100vw;height:100vh;display:flex;flex-direction:column;align-items:center;background-color:#fff;padding-top:%?100?%;box-sizing:border-box}.avatar-wrapper[data-v-4b38ad6a]{width:%?180?%;height:%?180?%;padding:0;margin:0;border-radius:0;position:relative}.avatar-wrapper-tier[data-v-4b38ad6a]{width:100%;height:100%;position:absolute;left:0;top:0;background-color:rgba(0,0,0,.5);display:flex;justify-content:center;align-items:center}.avatar-wrapper-tier-active[data-v-4b38ad6a]{background-color:rgba(0,0,0,.1)}.avatar[data-v-4b38ad6a]{width:100%;height:100%}.nickname[data-v-4b38ad6a]{display:flex;align-items:center;width:%?590?%;border-bottom:1px solid #eee;padding:0 %?20?%;padding-bottom:%?20?%;box-sizing:border-box;margin-top:%?50?%;font-size:%?32?%}.nickname-label[data-v-4b38ad6a]{font-weight:500;color:#333}.nickname-input[data-v-4b38ad6a]{width:%?400?%;margin-left:%?40?%}.op[data-v-4b38ad6a]{width:%?450?%;height:%?70?%;background:var(--custom-brand-color);border-radius:%?50?%;font-size:%?32?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center;margin-top:%?50?%}',""]),e.exports=t},"79a5":function(e,t,a){"use strict";a.r(t);var n=a("98fc"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"7d37":function(e,t,a){"use strict";a.r(t);var n=a("e264"),i=a("79a5");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("10ec");var o=a("828b"),l=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"4b38ad6a",null,!1,n["a"],void 0);t["default"]=l.exports},"8f68":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},9127:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"98fc":function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2");var i=n(a("2634")),r=n(a("2fdc")),o=n(a("85bf")),l=n(a("de74")),s=n(a("7c8d")),c=n(a("2d01")),d={components:{UniIcons:l.default},mixins:[c.default],data:function(){return{avatarUrl:"",nickname:"",memberInfo:{},isActive:!1}},onShow:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,o.default.wait_staticLogin_success();case 2:uni.getStorageSync("userInfo")&&(e.memberInfo=uni.getStorageSync("userInfo"),e.avatarUrl=e.memberInfo.headimg,e.nickname=e.memberInfo.nickname);case 3:case"end":return t.stop()}}),t)})))()},methods:{onChooseAvatar:function(e){var t=e.detail.avatarUrl;this.avatarUrl=t},editMemberInfo:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){var a,n;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(uni.showLoading({title:"保存中。。。",mask:!0}),a={nickname:e.nickname},-1!=e.avatarUrl.indexOf("//tmp")){t.next=18;break}return a["headimg_url"]=e.avatarUrl,t.prev=4,t.next=7,e.$api.sendRequest({url:s.default.editMemberInfoUrl,async:!1,data:a});case 7:n=t.sent,uni.hideLoading(),e.$util.showToast({title:n.message}),t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](4),uni.hideLoading(),e.$util.showToast({title:t.t0.message});case 16:t.next=20;break;case 18:a["token"]=uni.getStorageSync("token"),uni.uploadFile({url:e.$config.baseUrl+s.default.editMemberInfoUrl,filePath:e.avatarUrl,name:"headimg_file",formData:a,success:function(t){uni.hideLoading();var a=JSON.parse(t.data);e.$util.showToast({title:a.message})},fail:function(t){uni.hideLoading(),e.$util.showToast({title:t})}});case 20:case"end":return t.stop()}}),t,null,[[4,12]])})))()},changeClass:function(){var e=this;this.isActive=!0,setTimeout((function(){e.isActive=!1}),500)},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),a=t.title,n=t.link,i=t.imageUrl;t.query;return this.$buriedPoint.pageShare(n,i,a)}};t.default=d},a971:function(e,t,a){var n=a("8f68");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("2d38abe0",n,!0,{sourceMap:!1,shadowMode:!1})},b8ea:function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var i=n(a("9127")),r={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:i.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=r},de74:function(e,t,a){"use strict";a.r(t);var n=a("37ff"),i=a("fefc");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("327f");var o=a("828b"),l=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"65e5b6d2",null,!1,n["a"],void 0);t["default"]=l.exports},e264:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"main",style:[e.themeColorVar]},[a("v-uni-button",{staticClass:"avatar-wrapper",attrs:{"open-type":"chooseAvatar"},on:{chooseavatar:function(t){arguments[0]=t=e.$handleEvent(t),e.onChooseAvatar.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeClass.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"avatar",attrs:{src:e.avatarUrl}})],1),a("v-uni-view",{staticClass:"nickname"},[a("v-uni-text",{staticClass:"nickname-label"},[e._v("昵称：")]),a("v-uni-input",{staticClass:"nickname-input",attrs:{type:"nickname",placeholder:"请输入昵称"},model:{value:e.nickname,callback:function(t){e.nickname=t},expression:"nickname"}})],1),a("v-uni-button",{staticClass:"op",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.editMemberInfo.apply(void 0,arguments)}}},[e._v("保存")])],1)},i=[]},f9fe:function(e,t,a){var n=a("514c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("210ac5f6",n,!0,{sourceMap:!1,shadowMode:!1})},fefc:function(e,t,a){"use strict";a.r(t);var n=a("b8ea"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a}}]);