(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-pintuan-order-list-list"],{"016e":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("39d8")),a=i(o("2634")),s=i(o("2fdc")),r=(i(o("cbf3")),{methods:{orderClose:function(t,e){var o=this,i=t.out_trade_no;uni.showModal({title:"提示",content:"确定要取消该订单吗？",cancelText:"我再想想",confirmText:"确定",success:function(n){n.confirm&&o.$api.sendRequest({url:"/api/PintuanOrder/close",data:{id:t.id},success:function(t){if(t.code>=0)o.createBuriedPoint("out_trade_no",i,250),o.$util.showToast({title:"取消订单成功！",success:function(){"function"==typeof e&&e()}});else{if(-11==t.code)return o.$util.showToast({title:t.message}),setTimeout((function(){"function"==typeof e&&e()}),1500),!1;o.$util.showToast({title:t.message})}}})}})},orderPay:function(t,e){var o=this;return(0,s.default)((0,a.default)().mark((function i(){var n;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n="",n=t.group_id?"join_pintuan_before":"open_pintuan_before",i.prev=2,i.next=5,o.$util.subscribeMessage({source:"out_trade_no",source_id:t.out_trade_no,scene_type:n});case 5:i.next=9;break;case 7:i.prev=7,i.t0=i["catch"](2);case 9:o.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:t.out_trade_no,pay_type:"adapay"},success:function(i){if(i.code>=0)uni.hideLoading(),o.$util.wechatPay(i.data.pay_type,"adapay"==i.data.pay_type?i.data.payment:i.data.pay_info,(function(i){o.createBuriedPoint("out_trade_no",t.out_trade_no,11),"function"==typeof e&&e()}),(function(e){o.createBuriedPoint("out_trade_no",t.out_trade_no,9001),uni.hideLoading()}),(function(t){uni.hideLoading()}),o);else{if(-11==i.code)return o.$util.showToast({title:i.message}),setTimeout((function(){"function"==typeof e&&e()}),1500),!1;i.message?o.$util.showToast({title:i.message}):uni.hideLoading()}},fail:function(t){o.$util.showToast({title:"request:fail"})}});case 10:case"end":return i.stop()}}),i,null,[[2,7]])})))()},createBuriedPoint:function(t,e,o){var i;this.$buriedPoint.orderStatus((i={},(0,n.default)(i,t,e),(0,n.default)(i,"status",o),(0,n.default)(i,"orderType",!0),i))}}});e.default=r},"0d02":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("2634")),a=i(o("2fdc")),s={data:function(){return{list:[],img:["public/static/youpin/coupon_bg_1.png","public/static/youpin/coupon_bg_2.png","public/static/youpin/coupon_bg_3.png","public/static/youpin/coupon_bg_4.png"],boxClass:["box1","box2","box3","box4"]}},onLoad:function(){this.$util.toShowCouponPopup(this)},methods:{open:function(){this.listInfo()},listInfo:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var o;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 3:o=e.sent,o.data.length&&(t.list=o.data,t.$refs.coupon.open()),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})}}};e.default=s},"313a":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={uniPopup:o("5e99").default},n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("uni-popup",{ref:"coupon",attrs:{custom:!0,"mask-click":!1}},[t.list.length?o("v-uni-view",{staticClass:"coupon-model",class:t.list.length<4?t.boxClass[t.list.length-1]:t.boxClass[3],style:{"background-image":"url("+(t.list.length<4?t.$util.img(t.img[t.list.length-1]):t.$util.img(t.img[3]))+")"}},[o("v-uni-view",{staticClass:"coupon-header"},[o("v-uni-view",{staticClass:"title"},[t._v("恭喜您获得以下优惠券")]),o("v-uni-view",{staticClass:"tip"},[t._v("马上去使用吧！")])],1),o("v-uni-view",{staticClass:"coupon-box"},t._l(t.list,(function(e,i){return o("v-uni-view",{key:i,staticClass:"coupon-list",style:{"background-image":"url("+t.$util.img("public/static/youpin/coupon_border.png")+")"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodList(e)}}},[o("v-uni-view",{staticClass:"left"},[o("v-uni-view",{staticClass:"info"},[o("v-uni-view",[t._v("¥")]),e.money<100?[0==e.money.split(".")[1]?o("v-uni-view",[t._v(t._s(e.money.split(".")[0]))]):t._e(),e.money.split(".")[1]>0?o("v-uni-view",[t._v(t._s(e.money.split(".")[0])+"."),o("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])]):t._e()]:o("v-uni-view",{staticClass:"money-thousand"},[t._v(t._s(e.money.split(".")[0])+"."),o("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])])],2)],1),o("v-uni-view",{staticClass:"right"},[o("v-uni-view",[o("v-uni-view",{staticClass:"name"},[t._v(t._s(e.desc))]),o("v-uni-view",{staticClass:"time h5-time"},[t._v("有效期至"+t._s(e.end_time))])],1),o("v-uni-view",{staticClass:"btn"},[o("v-uni-view",{staticClass:"h5-btn"},[t._v("去使用")])],1)],1)],1)})),1)],1):t._e(),o("v-uni-image",{staticClass:"pop-ad-info-close",attrs:{src:t.$util.img("public/static/youpin/icon-close-overlay.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.coupon.close()}}})],1)],1)},a=[]},3745:function(t,e,o){"use strict";var i=o("764d"),n=o.n(i);n.a},"3f8b":function(t,e,o){"use strict";o.r(e);var i=o("8e23"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"46e8":function(t,e,o){var i=o("c881");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("f73cf6f6",i,!0,{sourceMap:!1,shadowMode:!1})},4778:function(t,e,o){"use strict";o.r(e);var i=o("69fe"),n=o("3f8b");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("e3eb"),o("e82b");var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"f18b6ffe",null,!1,i["a"],void 0);e["default"]=r.exports},"4ee5":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,"[data-v-f18b6ffe] .uni-page{overflow:hidden}[data-v-f18b6ffe] .mescroll-upwarp{padding-bottom:%?100?%}.countdown .clockrun[data-v-f18b6ffe] .uni-countdown{display:flex;justify-content:center;align-items:center;height:%?64?%;padding:0}.countdown .clockrun[data-v-f18b6ffe] .uni-countdown__number{background:#000;\n\t\t/* // #690b08 */padding:0;margin:0;border:none}.countdown .clockrun[data-v-f18b6ffe] .uni-countdown__splitor{padding:0;color:#000}.countdown .clockrun[data-v-f18b6ffe] .uni-countdown__splitor.day{width:auto}[data-v-f18b6ffe] .mescroll-upwarp{padding:0!important;margin-bottom:0;min-height:0;line-height:0}.inviter-diff-num[data-v-f18b6ffe]{color:#fff}.order-list[data-v-f18b6ffe]{padding-bottom:%?100?%}",""]),t.exports=e},"50b4":function(t,e,o){"use strict";o.r(e);var i=o("0d02"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"5ab1":function(t,e,o){"use strict";o.r(e);var i=o("78f85"),n=o("fe2a");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("3745");var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"d6071282",null,!1,i["a"],void 0);e["default"]=r.exports},"69fe":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={uniCountDown:o("5ab1").default,nsEmpty:o("dc6c").default,loadingCover:o("5510").default,ydAuthPopup:o("161f").default,nsLogin:o("4f5a").default,uniCouponPop:o("8765").default},n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"order-container",class:t.themeStyle},[o("v-uni-scroll-view",{staticClass:"order-nav",attrs:{id:"tab-bar","scroll-x":!0,"show-scrollbar":!1,"scroll-into-view":t.scrollInto}},t._l(t.statusList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"uni-tab-item",attrs:{id:e.id,"data-current":i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap.apply(void 0,arguments)}}},[o("v-uni-view",{staticClass:"uni-tab-item-title",class:e.status==t.orderStatus?"uni-tab-item-title-active high-text-color":""},[t._v(t._s(e.name)),o("v-uni-view",{staticClass:"line"})],1)],1)})),1),o("mescroll-uni",{ref:"mescroll",attrs:{top:2*(t.navHeight+t.statusBarHeight)+"rpx"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length>0?o("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"order-item"},[o("v-uni-view",{staticClass:"order-header",class:{waitpay:"waitpay"==t.orderStatus&&0==e.order_status}},[o("v-uni-view",{staticClass:"site-name-box"},[o("v-uni-view",{staticClass:"site-name"},[t._v("订单号："+t._s(e.order_no))])],1),o("v-uni-text",{staticClass:"status-name high-text-color"},[t._v(t._s(e.status_desc))])],1),o("v-uni-view",{staticClass:"order-body",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[o("v-uni-view",{staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-view",[t._v(t._s(e.spec_name))]),o("v-uni-view",[t._v("x1")])],1),o("v-uni-view",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"unit"},[t._v("¥")]),o("v-uni-text",[t._v(t._s(e.pay_money))])],1)],1)],1)],1),o("v-uni-view",{staticClass:"order-footer"},[o("v-uni-view",{staticClass:"order-base-info"},[o("v-uni-view",{staticClass:"total"},[o("v-uni-text",[t._v("共1件商品")]),o("v-uni-text",[t._v("总计："),o("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("¥")]),o("v-uni-text",{staticClass:"strong"},[t._v(t._s(e.pay_money))])],1)],1)],1),o("v-uni-view",{staticClass:"order-operation"},[o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[t._v("订单详情")]),2==e.pintuan_status&&e.diff_num>0?o("v-uni-view",{staticClass:"order-box-btn order-pay"},[o("v-uni-view",{staticClass:"inviter-diff-num"},[o("v-uni-button",{staticClass:"share-btn",attrs:{"data-group_id":e.group_id,"data-sku_name":e.sku_name,"data-sku_image":e.sku_image,"data-pay_money":e.pay_money},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toPintuanDetail(e)}}},[t._v("差"+t._s(e.diff_num)+"人 邀请好友")])],1)],1):t._e(),t._l(e.action,(function(i,n){return"inviterMember"!=i.action?o("v-uni-view",{key:n,staticClass:"order-box-btn",class:{"order-pay":"orderPay"==i.action},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(i,e)}}},[t._v(t._s(i.title)),"orderPay"==i.action?o("v-uni-view",{staticClass:"countdown"},[o("v-uni-view",{staticClass:"clockrun"},[o("uni-count-down",{attrs:{day:e.discountTimeMachine.d,hour:e.discountTimeMachine.h,minute:e.discountTimeMachine.i,second:e.discountTimeMachine.s,color:"#FFFFFF",splitorColor:"#FFFFFF","background-color":"#F2280C"}})],1)],1):t._e()],1):t._e()})),1==e.pintuan_status||3==e.pintuan_status?o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toPintuanDetail(e)}}},[t._v("拼团详情")]):t._e()],2)],1)],1)})),1):o("v-uni-view",[o("ns-empty",{attrs:{isIndex:!1,text:t.text}})],1)],1)],2),o("loading-cover",{ref:"loadingCover"}),o("yd-auth-popup",{ref:"ydauth"}),o("ns-login",{ref:"login"}),o("uni-coupon-pop",{ref:"couponPop"})],1)},a=[]},"764d":function(t,e,o){var i=o("b81d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("36126566",i,!0,{sourceMap:!1,shadowMode:!1})},"78f85":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),o("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},n=[]},8416:function(t,e,o){"use strict";var i=o("46e8"),n=o.n(i);n.a},8581:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-f18b6ffe]{width:100%;text-align:center}.order-container[data-v-f18b6ffe]{width:100vw;height:100vh}.order-nav[data-v-f18b6ffe]{width:100vw;height:%?70?%;flex-direction:row;white-space:nowrap;background:#fff;padding:%?15?% 0;position:fixed;left:0;z-index:998}.order-nav .uni-tab-item[data-v-f18b6ffe]{display:inline-block;flex-wrap:nowrap;padding:0 %?24?%}.order-nav .uni-tab-item-title[data-v-f18b6ffe]{color:#555;font-size:%?30?%;display:block;height:%?64?%;line-height:%?64?%;padding:0 %?10?%;flex-wrap:nowrap;white-space:nowrap}.order-nav .uni-tab-item-title .line[data-v-f18b6ffe]{width:%?36?%;height:%?6?%;background:transparent;border-radius:%?3?%;margin:0 auto}.order-nav .uni-tab-item-title-active[data-v-f18b6ffe]{display:block;height:%?64?%;padding:0 %?10?%}.order-nav .uni-tab-item-title-active .line[data-v-f18b6ffe]{background:#f2280c}.order-nav[data-v-f18b6ffe] ::-webkit-scrollbar{width:0;height:0;color:transparent}.order-item[data-v-f18b6ffe]{margin:%?20?% %?24?%;padding:%?28?% %?24?%;border-radius:%?20?%;background:#fff;position:relative}.order-item .order-header[data-v-f18b6ffe]{display:flex;align-items:center;position:relative;justify-content:space-between}.order-item .order-header.waitpay .iconyuan_checked[data-v-f18b6ffe],\r\n.order-item .order-header.waitpay .iconyuan_checkbox[data-v-f18b6ffe]{font-size:%?36?%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.order-item .order-header.waitpay .iconyuan_checkbox[data-v-f18b6ffe]{color:#a6a6a6}.order-item .order-header .icondianpu[data-v-f18b6ffe]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?30?%;color:#333}.order-item .order-header .status-name[data-v-f18b6ffe]{text-align:right}.order-item .order-body .goods-wrap[data-v-f18b6ffe]{margin-bottom:%?20?%;display:flex;position:relative}.order-item .order-body .goods-wrap[data-v-f18b6ffe]:last-of-type{margin-bottom:0}.order-item .order-body .goods-wrap .goods-img[data-v-f18b6ffe]{width:%?180?%;height:%?180?%;padding:%?28?% 0 0 0;margin-right:%?20?%}.order-item .order-body .goods-wrap .goods-img uni-image[data-v-f18b6ffe]{width:100%;height:100%;border-radius:%?20?%}.order-item .order-body .goods-wrap .goods-info[data-v-f18b6ffe]{flex:1;position:relative;padding:%?28?% 0 0 0;max-width:calc(100% - %?200?%)}.order-item .order-body .goods-wrap .goods-info .goods-name[data-v-f18b6ffe]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-f18b6ffe]{width:100%;line-height:1.3;display:flex;justify-content:space-between}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-f18b6ffe]{color:#9a9a9a;font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-price[data-v-f18b6ffe]{color:#343434;font-size:%?28?%;text-align:right}.order-item .order-body .goods-wrap .goods-info .unit[data-v-f18b6ffe]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.order-item .order-footer .order-base-info[data-v-f18b6ffe]{display:flex}.order-item .order-footer .order-base-info .total[data-v-f18b6ffe]{text-align:right;padding-top:%?20?%;flex:1}.order-item .order-footer .order-base-info .total > uni-text[data-v-f18b6ffe]{line-height:1;margin-left:%?10?%}.order-item .order-footer .order-base-info .total uni-text[data-v-f18b6ffe]{font-size:%?26?%;color:#343434}.order-item .order-footer .order-base-info .total uni-text uni-text[data-v-f18b6ffe]:last-of-type{margin-left:0}.order-item .order-footer .order-base-info .total uni-text.strong[data-v-f18b6ffe]{font-weight:700;font-size:%?32?%}.order-item .order-footer .order-operation[data-v-f18b6ffe]{display:flex;justify-content:flex-end;text-align:right;padding-top:%?20?%}.order-item .order-footer .order-operation .operation-btn[data-v-f18b6ffe]{line-height:1;padding:%?20?% %?26?%;color:#333;display:inline-block;border-radius:%?32?%;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?10?%}.empty[data-v-f18b6ffe]{padding-top:%?200?%;text-align:center}.empty .empty-image[data-v-f18b6ffe]{width:%?180?%;height:%?180?%}.order-batch-operation[data-v-f18b6ffe]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);text-align:right}.order-batch-operation.bottom-safe-area[data-v-f18b6ffe]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-batch-operation .operation-btn[data-v-f18b6ffe]{height:%?68?%;line-height:%?68?%;background:#fff;padding:0 %?40?%;display:inline-block;text-align:center;margin:%?16?% %?20?% %?16?% 0;border-radius:%?40?%;border:.5px solid #fff}.order-batch-operation .operation-btn.white[data-v-f18b6ffe]{height:%?68?%;line-height:%?68?%;color:#333;border:.5px solid #999;background:#fff}.high-text-color[data-v-f18b6ffe]{color:#f2280c!important}.order-box-btn[data-v-f18b6ffe]{min-width:%?160?%;padding:0 %?16?%;box-sizing:border-box;display:flex;height:%?64?%;line-height:%?64?%;align-items:center;justify-content:center;border-color:#ccc;color:#666;font-size:%?26?%}.order-box-btn.order-pay[data-v-f18b6ffe]{background:#f2280c!important;color:#fff}.order-box-btn .share-btn[data-v-f18b6ffe]{background-color:initial;color:#fff;font-size:%?26?%;margin:0;padding:0;line-height:normal}.site-name-box[data-v-f18b6ffe]{display:flex;align-items:center}.site-name-box .site-name[data-v-f18b6ffe]{width:%?530?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;font-weight:700}\r\n/* 标题栏 */.custom[data-v-f18b6ffe]{background:#fff;display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%}.custom .iconfont[data-v-f18b6ffe]{font-size:%?40?%;color:#333;font-weight:700;position:absolute;left:%?20?%}.custom .custom-navbar[data-v-f18b6ffe]{display:flex;border-radius:%?30?%;background:#fff4f4;width:%?360?%;align-items:center}.custom .custom-navbar .navbar-item[data-v-f18b6ffe]{height:%?60?%;line-height:%?60?%;width:50%;text-align:center;color:#f2280c;font-size:%?30?%}.custom .custom-navbar .navbar-item.active[data-v-f18b6ffe]{background:#f2280c;color:#fff;border-radius:%?30?%}',""]),t.exports=e},8765:function(t,e,o){"use strict";o.r(e);var i=o("313a"),n=o("50b4");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("8416");var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"7ca63514",null,!1,i["a"],void 0);e["default"]=r.exports},"8e23":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c223"),o("c9b5"),o("bf0f"),o("ab80");i(o("7c8d"));var n=i(o("5ab1")),a=i(o("016e")),s={components:{uniCountDown:n.default},data:function(){return{scrollInto:"",orderStatus:"all",statusList:[],orderList:[],contentText:{},mergePayOrder:[],text:"您还暂无相关订单",statusBarHeight:0,navHeight:0,group_id:null,isFirstLoad:!0}},mixins:[a.default],onLoad:function(t){var e=this;t.status&&(this.orderStatus=t.status),uni.getSystemInfo({success:function(t){e.navHeight=t.statusBarHeight+46-wx.getSystemInfoSync()["statusBarHeight"]},fail:function(t){console.log(t)}})},onShow:function(){this.$langConfig.refresh(),this.getOrderStatus(),uni.getStorageSync("token")?this.$refs.mescroll&&!this.isFirstLoad&&this.$refs.mescroll.refresh():this.$util.toShowLoginPopup(this,null,"/promotionpages/pintuan/order/list/list?status="+this.orderStatus),uni.getStorageSync("is_register")&&(this.$util.toShowCouponPopup(this),uni.removeStorageSync("is_register"))},methods:{ontabtap:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,"all"==this.orderStatus?this.text="您还暂无相关订单":"0"==this.orderStatus?this.text="您还暂无待付款订单":"2"==this.orderStatus?this.text="您还暂无拼团中订单":"3"==this.orderStatus?this.text="您还暂无拼团成功订单":"-1"==this.orderStatus?this.text="您还暂无已失效订单":"1"==this.orderStatus&&(this.text="您还暂无已失败订单"),""==this.orderStatus&&(this.mergePayOrder=[]),this.$refs.mescroll.refresh()},getListData:function(t){var e=this;this.$api.sendRequest({url:"/api/Pintuan/orderList",data:{page:t.num,page_size:t.size,pintuan_status:this.orderStatus},success:function(o){var i=[],n=o.message;0==o.code&&o.data?i=o.data.list:(uni.getStorageSync("token")||-10009!=o.code)&&e.$util.showToast({title:n}),t.endSuccess(i.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(i),e.$refs.loadingCover&&e.$refs.loadingCover.hide();for(var a=0;a<e.orderList.length;a++)e.orderList[a].close_time&&(e.orderList[a].discountTimeMachine=e.$util.countDown(e.orderList[a].close_time));e.isFirstLoad=!1},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getOrderStatus:function(){this.statusList=[{status:"all",name:this.$lang("all"),id:"all"},{status:"0",name:this.$lang("waitPay"),id:"0"},{status:"2",name:this.$lang("underWay"),id:"2"},{status:"3",name:this.$lang("success"),id:"3"},{status:"1",name:this.$lang("fail"),id:"1"}]},operation:function(t,e){var o=this,i=t.action;this.status;switch(i){case"orderPay":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderPay(e,(function(){o.$refs.mescroll.refresh()}));break;case"orderClose":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderClose(e,(function(){o.$refs.mescroll.refresh()}));break;case"orderDel":this.orderDel(e.order_id,(function(){o.$refs.mescroll.refresh()}));break}},orderDetail:function(t){this.$util.redirectTo("/promotionpages/pintuan/order/detail/detail",{order_id:t.id})},toPintuanDetail:function(t){console.log(t.group_id),this.$util.redirectTo("/promotionpages/pintuan/share/share",{group_id:t.group_id})},mergePay:function(){var t=this;this.mergePayOrder.length&&this.$api.sendRequest({url:"/api/order/pay",data:{order_ids:this.mergePayOrder.toString()},success:function(e){e.code>=0?t.$util.redirectTo("/pages/pay/index/index",{code:e.data}):t.$util.showToast({title:e.message})}})},imageError:function(t){this.orderList[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},navigateBack:function(){this.$util.redirectTo("/pages/member/index/index","","redirectTo")},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),o=e.title,i=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,n,o)},computed:{mpOrderList:function(){if(this.orderList[this.status])return this.orderList[this.status].list||[]},themeStyle:function(){return"theme-"+this.$store.state.themeStyle}}};e.default=s},"9d9a":function(t,e,o){var i=o("4ee5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("040c03ea",i,!0,{sourceMap:!1,shadowMode:!1})},b81d:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-d6071282]{width:100%;text-align:center}.uni-countdown[data-v-d6071282]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-d6071282]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?28?%}.uni-countdown__splitor.day[data-v-d6071282]{line-height:%?50?%}.uni-countdown__number[data-v-d6071282]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},c881:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7ca63514]{width:100%;text-align:center}.coupon-model[data-v-7ca63514]{display:flex;flex-direction:column;position:relative;z-index:111;width:%?620?%;background-size:cover;background-position:50%}.coupon-model .coupon-header[data-v-7ca63514]{background-size:cover;background-position:50%;margin-bottom:%?117?%}.coupon-model .title[data-v-7ca63514]{font-size:%?40?%;line-height:%?52?%;background-image:-webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:%?124?% 0 %?14?%;text-align:center;font-weight:700}.coupon-model .tip[data-v-7ca63514]{font-size:%?30?%;line-height:%?32?%;background-image:-webkit-linear-gradient(0deg,#fc5a50,#ff561a 46.75293%,#ff2637);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-align:center}.coupon-model .coupon-box[data-v-7ca63514]{flex:1;padding:0 %?54?% 0;background-size:100% 100%;background-position:50%;position:relative;margin-bottom:28px;overflow-y:auto}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]{display:flex;background-size:cover;background-position:50%;height:%?120?%;margin-bottom:%?20?%;position:relative;z-index:11}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]:last-child{margin-bottom:0}.coupon-model .coupon-box .coupon-list .left[data-v-7ca63514]{width:70px;display:flex;align-items:center;justify-content:center}.coupon-model .coupon-box .coupon-list .left .info[data-v-7ca63514]{display:flex;align-items:baseline}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:first-child{display:inline-block;font-size:%?26?%;color:#eb0000}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:last-child{display:inline-block;font-size:%?48?%;color:#eb0000;line-height:%?80?%}.coupon-model .coupon-box .coupon-list .left .point-class[data-v-7ca63514]{font-size:%?35?%}.coupon-model .coupon-box .coupon-list .left .money-thousand[data-v-7ca63514]{font-size:%?41?%!important}.coupon-model .coupon-box .coupon-list .left .money-thousand > span[data-v-7ca63514]{font-size:%?29?%}.coupon-model .coupon-box .coupon-list .right[data-v-7ca63514]{width:%?238?%;flex:1;display:flex;align-items:center;position:relative;margin-left:%?18?%}.coupon-model .coupon-box .coupon-list .right > uni-view[data-v-7ca63514]:first-child{flex:1;overflow:hidden;height:100%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .name[data-v-7ca63514]{font-size:%?24?%;line-height:%?36?%;padding:%?24?% 0 %?8?%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .time[data-v-7ca63514]{font-size:%?18?%;line-height:%?30?%;color:#999;white-space:nowrap}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .h5-time[data-v-7ca63514]{display:flex;width:119px;margin-left:-13px;-webkit-transform:scale(.78);transform:scale(.78)}.coupon-model .coupon-box .coupon-list .right .btn[data-v-7ca63514]{display:flex;justify-content:center;align-items:center;width:%?94?%;height:%?38?%;background:linear-gradient(90deg,#ffab37,#fff594);border-radius:19px;font-size:%?24?%;color:#822d02;margin:0 %?10?% 0 0}.coupon-model .coupon-box .coupon-list .right .h5-btn[data-v-7ca63514]{-webkit-transform:scale(.8);transform:scale(.8)}.coupon-model .coupon_bg[data-v-7ca63514]{position:absolute;top:%?270?%;width:100%;height:%?153?%;z-index:-1}.coupon-model .coupon_footer[data-v-7ca63514]{width:100%;height:%?51?%}.box1[data-v-7ca63514]{height:%?565?%}.box2[data-v-7ca63514]{height:%?660?%}.box3[data-v-7ca63514]{height:%?800?%}.box4[data-v-7ca63514]{height:%?850?%}.pop-ad-info-close[data-v-7ca63514]{width:%?88?%;height:%?88?%;display:block;margin:%?60?% auto 0}',""]),t.exports=e},e3eb:function(t,e,o){"use strict";var i=o("e411"),n=o.n(i);n.a},e411:function(t,e,o){var i=o("8581");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("18a049bc",i,!0,{sourceMap:!1,shadowMode:!1})},e82b:function(t,e,o){"use strict";var i=o("9d9a"),n=o.n(i);n.a},ecc6:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},created:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,o,i){return 60*t*60*24+60*e*60+60*o+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,o=0,i=0,n=0;t>0?(e=Math.floor(t/86400),o=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*o,n=Math.floor(t)-24*e*60*60-60*o*60-60*i):this.timeUp(),e<10&&(e="0"+e),o<10&&(o="0"+o),i<10&&(i="0"+i),n<10&&(n="0"+n),this.d=e,this.h=o,this.i=i,this.s=n}}};e.default=i},fe2a:function(t,e,o){"use strict";o.r(e);var i=o("ecc6"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a}}]);