(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-detail-detail"],{"075d":function(t,e,a){"use strict";var i=a("d724"),o=a.n(i);o.a},"1b9d":function(t,e,a){"use strict";var i=a("642c"),o=a.n(i);o.a},"1c82":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uniNavBar:a("d817").default,uniCountDown:a("5ab1").default,uniIcons:a("de74").default,loadingCover:a("5510").default,uniPopup:a("5e99").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"detail-container",class:{themeStyle:t.themeStyle,"safe-area":t.isIphoneX,heightvh:t.myPopupIsShow},style:t.style},[t.isOnXianMaiApp?a("uni-nav-bar",{attrs:{"left-icon":"back",border:!1,fixed:!0,"background-color":"var(--custom-brand-color)",color:"white"},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}},[[a("v-uni-view",{staticClass:"page-title"},[t._v("我的订单")])]],2):t._e(),a("v-uni-view",{staticClass:"status-wrap"},[a("v-uni-view",{staticClass:"status-box"},[a("v-uni-view",{staticClass:"status-name-box"},[a("v-uni-text",{staticClass:"status-name"},[t._v(t._s(t.orderData.order_status_name))])],1),1==t.orderData.order_status?a("v-uni-view",[t._v("订单已支付，请等待发货")]):t._e(),3==t.orderData.order_status?a("v-uni-view",[t._v("订单已发货，请等待收货")]):t._e(),0==t.orderData.order_status?[a("v-uni-view",{staticClass:"desc-box"},[a("v-uni-view",{staticClass:"desc"},[a("v-uni-view",{staticClass:"countdown"},[a("v-uni-view",{staticClass:"clockrun"},[a("uni-count-down",{attrs:{day:t.orderData.discountTimeMachine.d,hour:t.orderData.discountTimeMachine.h,minute:t.orderData.discountTimeMachine.i,second:t.orderData.discountTimeMachine.s,color:"#FFFFFF",splitorColor:"#FFFFFF","background-color":"transparent"}})],1)],1),a("v-uni-text",[t._v("后取消订单")])],1)],1)]:t._e()],2)],1),a("v-uni-view",{staticClass:"address-wrap active"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-text",{staticClass:"icon-pay-clock iconfont icondizhi"})],1),a("v-uni-view",{staticClass:"address-info"},[a("v-uni-view",{staticClass:"info"},[a("v-uni-view",{staticClass:"info-left"},[a("v-uni-view",{staticClass:"info-name"},[t._v(t._s(t.orderData.name))]),a("v-uni-view",{staticClass:"info-mobile"},[t._v(t._s(t.orderData.mobile||t.orderData.telephone))])],1),a("uni-icons",{staticClass:"info-more",attrs:{type:"arrowright",size:"12",color:"rgba(166, 166, 166, 1)"}})],1),a("v-uni-view",{staticClass:"detail"},[a("v-uni-text",[t._v(t._s(t.orderData.full_address)+" "+t._s(t.orderData.address))])],1)],1)],1),2==t.orderData.order_create_type?a("v-uni-view",{staticClass:"refund-apply-money"},[a("v-uni-view",{staticClass:"name"},[t._v("总周期数：")]),a("v-uni-view",{staticClass:"money"},[t._v(t._s(t.orderData.periodInfo.period_count))]),a("v-uni-view",{staticClass:"name"},[t._v("当前周期数：")]),a("v-uni-view",{staticClass:"money"},[t._v(t._s(t.orderData.periodInfo.period))])],1):t._e(),a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-header"},[a("v-uni-text",{staticClass:"site-name"},[t._v("商品信息")])],1),a("v-uni-view",{staticClass:"site-body"},t._l(t.orderData.order_goods,(function(e,i){return a("v-uni-view",{key:i},[a("v-uni-view",{staticClass:"goods-wrap"},[8==t.orderData.order_create_type?a("v-uni-navigator",{staticClass:"goods-img",attrs:{"hover-class":"none",url:"/promotionpages/pintuan/detail/detail?id="+t.orderData.pintuan_goods_id}},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1):a("v-uni-navigator",{staticClass:"goods-img",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[8==t.orderData.order_create_type?a("v-uni-navigator",{staticClass:"goods-name",attrs:{"hover-class":"none",url:"/promotionpages/pintuan/detail/detail?id="+t.orderData.pintuan_goods_id}},[t._v(t._s(e.sku_name))]):a("v-uni-navigator",{staticClass:"goods-name",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[t._v(t._s(e.sku_name))]),a("v-uni-view",{staticClass:"goods-info-row"},[a("v-uni-view",{staticClass:"goods-info-row-left"},[1!=t.orderData.is_maidou_pay?a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t.orderData.periodInfo?t._e():a("v-uni-text",[t._v(t._s(e.price))]),t.orderData.periodInfo?a("v-uni-text",[t._v(t._s(t.orderData.periodInfo.buy_price))]):t._e()],1):t._e(),1==t.orderData.is_maidou_pay?a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-text",[t._v(t._s(e.price)+"迈豆")])],1):t._e()],1),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[t._v(t._s(e.spec_name))]),a("v-uni-view",[t._v("x"+t._s(e.num))])],1)],1)],1)],1),t.orderData.periodInfo||8==t.orderData.order_create_type?t._e():a("v-uni-view",{staticClass:"goods-operation"},[t.orderData.is_enable_refund?[0==e.refund_status?a("v-uni-view",{staticClass:"disinline"},[a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.openPopup(e.order_goods_id,i,0)}}},[t._v("申请退款")])],1):t._e(),1==e.refund_status?a("v-uni-navigator",{staticClass:"order-box-btn-wrap",attrs:{"hover-class":"none",url:"/otherpages/order/refund_detail/refund_detail?order_goods_id="+e.order_goods_id}},[a("v-uni-view",{staticClass:"order-box-btsdn"},[t._v("查看退款")])],1):t._e()]:t._e()],2),a("v-uni-view",{staticClass:"order-refund"},[e.aftersale_block.is_enable_aftersale&&4==t.orderData.order_status?a("v-uni-navigator",{staticClass:"order-box-btn-wrap",attrs:{"hover-class":"none",url:1==e.aftersale_block.status||2==e.aftersale_block.status?"/otherpages/order/return_and_exchange/refund_progress?order_goods_id="+e.order_goods_id:"/otherpages/order/return_and_exchange/select_service?order_goods_id="+e.order_goods_id+"&num="+e.num}},[a("v-uni-view",{staticClass:"order-box-btn"},[t._v(t._s(1==e.aftersale_block.status?"售后中":"退换"))])],1):t._e()],1)],1)})),1)],1),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("共"+t._s(t.orderData.goods_num)+"件商品")]),a("v-uni-view",{staticClass:"box align-right"},[1==t.orderData.is_maidou_pay?a("v-uni-text",{staticClass:"order-cell-right"},[t._v(t._s(t.orderData.goods_money)+" 迈豆")]):t._e(),t.orderData.periodInfo||1==t.orderData.is_maidou_pay?t._e():a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(t.orderData.goods_money))],1),t.orderData.periodInfo?a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(t.orderData.periodInfo.total_price))],1):t._e()],1)],1),t.orderData.periodInfo?t._e():a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.delivery_money))])],1)],1)],1),5==t.orderData.order_create_type?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[t._v("砍价优惠 "+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t._f("moneyFormat")(t.orderData.bargain_promotion)))])],1)],1)],1):t._e(),t.orderData.goodscoupon_id?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"pay-money"},[t._v("-￥"+t._s(t.orderData.goodscoupon_money))])],1)],1)],1):t._e(),t.orderData.promotion_money&&parseFloat(t.orderData.promotion_money)?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("分销商优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm pay-money"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.promotion_money))])],1)],1)],1):t._e(),parseFloat(t.orderData.multiple_discount_money)>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("多件折扣")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm pay-money"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.multiple_discount_money))])],1)],1)],1):t._e(),parseFloat(t.orderData.combo_cheap_price)>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("组合优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm pay-money"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.combo_cheap_price))])],1)],1)],1):t._e(),parseFloat(t.orderData.seckill_discount_money)>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("秒杀优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"order-cell-right"},[a("v-uni-text",{staticClass:"ns-font-size-sm pay-money"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.seckill_discount_money))])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(0!=t.orderData.order_status?"实付金额":""))]),a("v-uni-view",{staticClass:"box align-right"},[1==t.orderData.is_maidou_pay?a("v-uni-text",{staticClass:"order-cell-right"},[0==t.orderData.order_status?a("v-uni-text",{staticClass:"order-cell-right-tip"},[t._v("需付款：")]):t._e(),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.pay_money)+"迈豆")])],1):t._e(),1!=t.orderData.is_maidou_pay?a("v-uni-text",{staticClass:"order-cell-right"},[0==t.orderData.order_status?a("v-uni-text",{staticClass:"order-cell-right-tip"},[t._v("需付款：")]):t._e(),a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t.orderData.periodInfo?t._e():a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s("BALANCE"==t.orderData.pay_type?t.orderData.balance_money:t.orderData.pay_money))]),t.orderData.periodInfo?a("v-uni-text",{staticClass:"pay-money"},[t._v(t._s(t.orderData.periodInfo.total_price))]):t._e()],1):t._e()],1)],1)],1),a("v-uni-view",[a("v-uni-view",{staticClass:"order-operation"},[a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.getCustomerService()}}},[t._v("客服")]),t.orderData.action.length>0?t._l(t.orderData.action,(function(e,i){return"orderBuy"!=e.action?a("v-uni-view",{key:i,staticClass:"order-box-btn",class:{"order-pay":"orderPay"==e.action||"memberTakeDelivery"==e.action},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.operation(e)}}},[t._v(t._s(e.title))]):t._e()})):t._e(),"已完成"!=t.orderData.order_status_name||t.orderData.periodInfo||8==t.orderData.order_create_type?t._e():a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.getCustomerService()}}},[t._v("售后")]),t.orderData.is_order_refund?a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPopup(t.orderData.order_id,null,1)}}},[t._v("整单退款")]):t._e()],2)],1),a("v-uni-view",{staticClass:"order-summary"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.order_no))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.to_copy_order_no.apply(void 0,arguments)}}},[t._v("复制")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("交易号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.out_trade_no))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("下单时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.create_time)))])],1)],1),t.orderData.pay_status>0?[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.pay_time)))])],1)],1),t.orderData.app_type_name?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.app_type_name))])],1)],1):t._e()]:t._e(),t.orderData.delivery_time>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发货时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.delivery_time)))])],1)],1):t._e(),t.orderData.package_list.length>1?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("物流信息：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.package_list.length)+"个包裹")]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/otherpages/order/parcel/parcel?order_id="+t.orderData.order_id)}}},[t._v("查看")])],1)],1):t._e(),1==t.orderData.package_list.length&&t.orderData.package_list[0].express_company_name?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("快递类型：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.package_list.length>0&&t.orderData.package_list[0].express_company_name))])],1)],1):t._e(),1==t.orderData.package_list.length&&t.orderData.package_list[0].delivery_no?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("快递单号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.package_list.length>0&&t.orderData.package_list[0].delivery_no))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.to_copy_code.apply(void 0,arguments)}}},[t._v("复制")])],1)],1):t._e(),t.orderData.logistics_remark?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("物流备注：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.logistics_remark))])],1)],1):t._e(),t.orderData.sign_time>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("确认收货：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.sign_time)))])],1)],1):t._e(),t.orderData.finish_time>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("完成时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.finish_time)))])],1)],1):t._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单备注：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"ns-text-color-black"},[t._v(t._s(t.orderData.buyer_message))])],1)],1),t.orderData.remark?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("商家备注：")]),a("v-uni-view",{staticClass:"box",staticStyle:{"margin-top":"-4rpx"}},[a("v-uni-view",{staticClass:"ns-text-color-black",staticStyle:{"word-break":"break-word"}},[t._v(t._s(t.orderData.remark)),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.remark)}}},[t._v("复制")])],1)],1)],1):t._e()],2),a("loading-cover",{ref:"loadingCover"}),t.myPopupIsShow?a("v-uni-view",{staticClass:"my-popup"},[a("v-uni-view",{staticClass:"popup-mask"}),a("v-uni-view",{staticClass:"popup-box"},[a("v-uni-view",{staticClass:"popup-dialog",style:{marginBottom:t.keyHeight+"px"}},[a("v-uni-form",[a("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("申请退款")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[a("v-uni-textarea",{attrs:{placeholder:"请输入退款理由，最多100字","placeholder-style":"{color:#CCCCCC,font-size:28rpx}","adjust-position":"true",name:"refundRemark"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.textareaFocus.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.textareaBlur.apply(void 0,arguments)}},model:{value:t.refundRemark,callback:function(e){t.refundRemark=e},expression:"refundRemark"}}),t.complete_order_refund?t._e():a("v-uni-view",{staticClass:"popup-dialog-body-tip"},[t._v("注意：部分商品退款不退运费，如需退运费请将该订单全部商品申请退款，取消订单。")])],1),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-view",{staticClass:"button white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("取消")]),a("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.orderGoodRefund.apply(void 0,arguments)}}},[t._v("提交")])],1)],1)],1)],1)],1):t._e(),a("uni-popup",{ref:"selectPopup",staticClass:"select-popup",attrs:{type:"bottom"}},[a("div",{staticClass:"select-box"},[a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addFun(3)}}},[a("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/photo-icon.png"),mode:""}}),a("v-uni-view",[t._v("发图片")])],1),a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addFun(4)}}},[a("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/video-icon.png"),mode:""}}),a("v-uni-view",[t._v("发视频")])],1)],1),a("v-uni-image",{staticClass:"close",attrs:{src:t.$util.img("public/static/youpin/seed-close.png"),mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeFun()}}})],1)],1)},r=[]},"2d01":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},3745:function(t,e,a){"use strict";var i=a("764d"),o=a.n(i);o.a},"47d4":function(t,e,a){"use strict";a.r(e);var i=a("1c82"),o=a("8f99");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("1b9d"),a("075d"),a("4ffb");var n=a("828b"),s=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"0bac068e",null,!1,i["a"],void 0);e["default"]=s.exports},"4ffb":function(t,e,a){"use strict";var i=a("c836"),o=a.n(i);o.a},"56a1":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-0bac068e] .uni-page{overflow:hidden}[data-v-0bac068e] .uni-navbar{position:fixed;left:0;top:0;z-index:1}.countdown[data-v-0bac068e]{margin-right:%?6?%}.countdown .clockrun[data-v-0bac068e] .uni-countdown{display:flex;justify-content:center;align-items:center;height:%?64?%;padding:0;font-size:%?32?%}.countdown .clockrun[data-v-0bac068e] .uni-countdown__number{background:#000;\r\n\t/* // #690b08 */padding:0;margin:0;border:none;font-weight:700;font-size:%?32?%}.countdown .clockrun[data-v-0bac068e] .uni-countdown__splitor{padding:0;color:#000;font-weight:700;font-size:%?32?%}.countdown .clockrun[data-v-0bac068e] .uni-countdown__splitor.day{width:auto;font-weight:700;font-size:%?32?%}[data-v-0bac068e] .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%}",""]),t.exports=e},"5ab1":function(t,e,a){"use strict";a.r(e);var i=a("78f85"),o=a("fe2a");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("3745");var n=a("828b"),s=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"d6071282",null,!1,i["a"],void 0);e["default"]=s.exports},"5d87":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("fd3c"),a("bf0f"),a("2797"),a("aa9c"),a("d4b5"),a("e838"),a("c9b5"),a("ab80"),a("e966");var o=i(a("2634")),r=i(a("2fdc")),n=i(a("5ab1")),s=i(a("88e4")),d=i(a("2d01")),c=i(a("85bf")),l=(i(a("2d01")),i(a("f8de"))),u=i(a("de74")),p=a("4b89"),v={components:{UniIcons:u.default,uniCountDown:n.default},data:function(){return{isIphoneX:!1,orderId:0,orderData:{package_list:[],action:[]},refundRemark:"",keyHeight:0,myPopupIsShow:!1,orderIndex:0,goods_id:"",findGoodArr:[],complete_order_refund:!1,isOnXianMaiApp:p.isOnXianMaiApp,navHeight:44}},onLoad:function(t){t.order_id&&(this.orderId=t.order_id),"h5"!=this.$util.getPlatform()||this.isOnXianMaiApp||(this.navHeight=0)},onShow:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,c.default.wait_staticLogin_success();case 3:t.isIphoneX=t.$util.uniappIsIPhoneX(),uni.getStorageSync("token")?t.getOrderData():t.$util.redirectTo("/otherpages/shop/home/<USER>"),uni.removeStorageSync("selectGood"),uni.removeStorageSync("selectGoodType");case 7:case"end":return e.stop()}}),e)})))()},mixins:[d.default,s.default,l.default],computed:{style:function(){var t="padding-top: ".concat(this.navHeight,"px;");for(var e in this.themeColorVar)t+="".concat(e,":").concat(this.themeColorVar[e],";");return t}},methods:{goBack:function(){this.$util.goBack()},openPopup:function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this.complete_order_refund=1==a,this.orderIndex=e||0,this.myPopupIsShow=!0,uni.setStorageSync("order_goods_id",t)},closePopup:function(){this.myPopupIsShow=!1,this.refundRemark=""},textareaFocus:function(t){this.keyHeight=t.detail.height},textareaBlur:function(t){this.keyHeight=0},orderGoodRefund:function(t){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var a,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=uni.getStorageSync("order_goods_id"),!a){t.next=12;break}if(""!=e.refundRemark){t.next=6;break}e.$util.showToast({title:"请输入申请理由"}),t.next=12;break;case 6:return t.next=8,e.$util.subscribeMessage({source:e.complete_order_refund?"order":"order_goods",source_id:a,scene_type:"order_refund_before"});case 8:uni.showLoading({title:"提交中...",mask:!0}),i={refund_type:1,refund_remark:e.refundRemark},e.complete_order_refund?i.order_id=a:i.order_goods_id=a,e.$api.sendRequest({url:"/api/orderrefund/refund",data:i,success:function(t){uni.hideLoading(),e.$util.showToast({title:t.message}),0==t.code?("迈豆支付"!=e.orderData.app_type_name&&(e.complete_order_refund?e.orderData.order_goods.map((function(t){e.$buriedPoint.refundAll({order_no:t.order_no,sku_id:t.sku_id})})):e.$buriedPoint.refundAll({order_no:e.orderData.order_goods[e.orderIndex].order_no,sku_id:e.orderData.order_goods[e.orderIndex].sku_id})),e.$util.showToast({title:"申请退款已提交",success:function(){setTimeout((function(){e.myPopupIsShow=!1,e.refundRemark="",e.$util.redirectTo("/pages/order/activist/activist")}),1500)}})):e.$util.showToast({title:t.message})},fail:function(t){uni.hideLoading(),e.$util.showToast({title:t.message})}});case 12:case"end":return t.stop()}}),t)})))()},getOrderData:function(){var t=this;this.$api.sendRequest({url:"/api/order/detail",data:{order_id:this.orderId},success:function(e){uni.stopPullDownRefresh(),e.code>=0?(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.orderData=e.data,"number"==typeof t.orderData.reset_pay_time&&(t.orderData.discountTimeMachine=t.$util.countDown(t.orderData.reset_pay_time))):t.$util.showToast({title:"未获取到订单信息!！",success:function(){}})},fail:function(e){uni.stopPullDownRefresh(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},onPullDownRefresh:function(){this.getOrderData()},operation:function(t){var e=this,a=t.action;switch(a){case"orderPay":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderPay(this.orderData,(function(t){if(-11==t.code){e.$util.showToast({title:t.message});var a=e;setTimeout((function(){a.getOrderData()}),1500)}}));break;case"orderClose":t.disabled?this.$util.showToast({title:t.disabled_tips}):this.orderClose(this.orderData.order_id,(function(){e.getOrderData()}));break;case"memberTakeDelivery":this.orderDelivery(this.orderData.order_id,(function(){e.getOrderData()}));break;case"trace":this.orderData.package_list&&1==this.orderData.package_list.length?this.$util.redirectTo("/pages/order/logistics/logistics",{order_id:this.orderData.order_id}):this.orderData.package_list&&this.orderData.package_list.length>1&&this.$util.redirectTo("/otherpages/order/parcel/parcel",{order_id:this.orderData.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/otherpages/order/evaluate/evaluate",{order_id:this.orderData.order_id});break}},imageError:function(t){this.orderData.order_goods[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},toShopDetail:function(t){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:t})},to_copy_order_no:function(){this.$util.copy(this.orderData.order_no)},to_copy_code:function(){this.$util.copy(this.orderData.package_list.length>0&&this.orderData.package_list[0].delivery_no)},findFun:function(t){this.$refs.selectPopup.open(),this.findGoodArr=[{goods_id:t.goods_id,goods_image:this.$util.img(t.sku_image)}]},finishFindFun:function(){var t=this,e=[];this.orderData.order_goods.forEach((function(a){e.push({goods_id:a.goods_id,goods_image:t.$util.img(a.sku_image)})})),this.findGoodArr=e,this.$refs.selectPopup.open()},closeFun:function(){this.$refs.selectPopup.close()},addFun:function(t){uni.setStorageSync("selectGood",JSON.stringify(this.findGoodArr)),uni.setStorageSync("selectGoodType",1),this.$util.redirectTo("/promotionpages/seeding/seeding-add/seeding-add?type=".concat(t,"&goods_id=").concat(this.goods_id)),this.$refs.selectPopup.close()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),a=e.title,i=e.link,o=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,o,a)},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)},timeStr:function(t){var e=parseInt(t/3600).toString(),a=parseInt(t%3600/60).toString();return 1==a.length&&(a="0"+a),1==e.length&&(e="0"+e),e+":"+a},moneyFormat:function(t){return parseFloat(t).toFixed(2)}}};e.default=v},"5dc2":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0bac068e]{width:100%;text-align:center}.align-right[data-v-0bac068e]{text-align:right}.detail-container[data-v-0bac068e]{min-height:100vh;box-sizing:border-box;padding-bottom:calc(%?60?% + env(safe-area-inset-bottom))}.detail-container .height-box[data-v-0bac068e]{display:block;padding-bottom:%?100?%;padding-bottom:calc(%?100?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?100?% + env(safe-area-inset-bottom))}.status-wrap[data-v-0bac068e]{height:%?220?%;box-sizing:border-box;background-color:var(--custom-brand-color);display:flex;flex-direction:column;justify-content:flex-end;padding-bottom:%?136?%}.status-wrap uni-view[data-v-0bac068e]{text-align:center;color:#fff}.status-wrap .status-box[data-v-0bac068e]{display:flex;align-items:center;justify-content:space-between;padding:0 %?20?%;box-sizing:border-box}.status-wrap .status-name-box[data-v-0bac068e]{display:flex;align-items:center;justify-content:center}.status-wrap .status-name[data-v-0bac068e]{font-size:%?48?%;font-weight:700;line-height:%?52?%;color:#fff}.status-wrap .desc[data-v-0bac068e]{margin-left:%?20?%;font-size:%?28?%}.status-wrap .strong[data-v-0bac068e]{font-weight:700;font-size:%?36?%}.status-wrap .operation-group[data-v-0bac068e]{text-align:center;padding-top:%?20?%}.status-wrap .operation-group .operation-btn[data-v-0bac068e]{line-height:1;padding:%?16?% %?50?%;display:inline-block;border-radius:%?32?%;background:#fff;box-shadow:0 0 %?14?% hsla(0,0%,62%,.6)}.address-wrap.active[data-v-0bac068e]{display:flex;align-items:center}.address-wrap[data-v-0bac068e]{margin:0 %?20?%;margin-bottom:%?20?%;margin-top:%?-84?%;min-height:%?168?%;border-radius:%?20?%;background-color:#fff;padding:0 %?30?%}.address-wrap .icon[data-v-0bac068e]{display:flex;align-items:center;justify-content:center}.address-wrap .icon uni-image[data-v-0bac068e]{width:%?48?%;height:%?48?%}.address-wrap .address-info[data-v-0bac068e]{flex-grow:1}.address-wrap .address-info .info[data-v-0bac068e]{display:flex;justify-content:space-between;align-items:center;font-size:%?32?%;font-weight:400;margin-bottom:%?20?%}.address-wrap .address-info .info .info-left[data-v-0bac068e]{display:flex;justify-content:center;align-items:center}.address-wrap .address-info .info .info-name[data-v-0bac068e]{max-width:%?380?%;margin-right:%?32?%;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;word-break:break-all;font-weight:700;font-size:%?32?%}.address-wrap .address-info .info .info-mobile[data-v-0bac068e]{font-size:%?32?%}.address-wrap .address-info .detail[data-v-0bac068e]{font-size:%?26?%;font-weight:400;line-height:%?32?%;color:grey;width:%?508?%}.address-wrap .cell-more[data-v-0bac068e]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?10?%}.address-wrap .cell-more .iconfont[data-v-0bac068e]{color:#999}.pickup-info[data-v-0bac068e]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative;padding:%?20?% %?30?%}.pickup-info .pickup-point-info .name[data-v-0bac068e]{display:flex;height:%?50?%;align-items:flex-end;margin-bottom:10px}.pickup-info .pickup-point-info .name uni-text[data-v-0bac068e]{line-height:1}.pickup-info .pickup-point-info .name uni-text.mark[data-v-0bac068e]{font-size:%?20?%;padding:1px %?10?%;border:.5px solid #fff;border-radius:%?4?%;margin-left:%?10?%}.pickup-info .pickup-point-info .address[data-v-0bac068e],\r\n.pickup-info .pickup-point-info .time[data-v-0bac068e],\r\n.pickup-info .pickup-point-info .contact[data-v-0bac068e]{font-size:%?26?%;line-height:1;margin-top:%?16?%}.pickup-info .pickup-point-info .address .iconfont[data-v-0bac068e],\r\n.pickup-info .pickup-point-info .time .iconfont[data-v-0bac068e],\r\n.pickup-info .pickup-point-info .contact .iconfont[data-v-0bac068e]{color:#999;font-size:%?26?%;line-height:1;margin-right:%?10?%}.pickup-info .hr[data-v-0bac068e]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.pickup-info .pickup-code-info .info[data-v-0bac068e]{text-align:center}.pickup-info .pickup-code-info .code[data-v-0bac068e]{margin:0 auto;width:%?160?%;height:%?160?%}.pickup-info .pickup-code-info .code uni-image[data-v-0bac068e]{width:100%;height:100%}.verify-code-wrap[data-v-0bac068e]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.verify-code-wrap .wrap[data-v-0bac068e]{line-height:%?40?%}.verify-code-wrap .wrap .copy[data-v-0bac068e]{font-size:%?20?%;display:inline-block;color:#666;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?10?%;border-radius:%?18?%}.verify-code-wrap .hr[data-v-0bac068e]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.verify-code-wrap .code[data-v-0bac068e]{margin:0 auto;width:%?200?%;height:%?200?%}.verify-code-wrap .code uni-image[data-v-0bac068e]{width:100%;height:100%}.site-wrap[data-v-0bac068e]{margin:0 %?20?%;padding:0 %?20?%;border-radius:%?20?% %?20?% 0 0;background:#fff;position:relative}.site-wrap .site-header[data-v-0bac068e]{display:flex;align-items:center;height:%?78?%}.site-wrap .site-header .icondianpu[data-v-0bac068e]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?30?%}.site-wrap .site-body .goods-wrap[data-v-0bac068e]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-0bac068e]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-0bac068e]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-0bac068e]{width:100%;height:100%;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-0bac068e]{flex:1;position:relative;max-width:calc(100% - %?200?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-0bac068e]{padding-top:%?5?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838;min-height:%?84?%}.site-wrap .site-body .goods-wrap .goods-info-row[data-v-0bac068e]{display:flex;justify-content:space-between;align-items:center;margin-top:%?30?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-0bac068e]{display:flex;align-items:center}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-0bac068e]{font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-0bac068e]:last-child{margin-left:%?10?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price[data-v-0bac068e]{font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:#383838}.site-wrap .site-body .goods-wrap .goods-info .goods-price .unit[data-v-0bac068e]{font-size:%?24?%;font-weight:400;line-height:%?44?%;color:#383838}.site-wrap .site-body .goods-operation[data-v-0bac068e]{text-align:right;padding:%?20?% 0}.site-wrap .site-body .goods-operation .disinline[data-v-0bac068e]{display:inline-block}.site-wrap .site-body .goods-operation .operation-btn[data-v-0bac068e]{line-height:1;padding:%?14?% %?20?%;color:#333;display:inline-block;border-radius:%?28?%;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?10?%}.order-cell[data-v-0bac068e]{display:flex;margin:%?35?% 0;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-0bac068e]{text-align:left}.order-cell .box[data-v-0bac068e]{flex:1;padding:0 %?20?%;line-height:inherit}.order-cell .box .textarea[data-v-0bac068e]{height:%?40?%}.order-cell .iconfont[data-v-0bac068e]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-0bac068e]{padding:0}.order-cell .order-pay uni-text[data-v-0bac068e]{display:inline-block;margin-left:%?6?%}.order-summary[data-v-0bac068e]{margin:%?20?% %?20?%;padding:%?1?% %?20?%;margin-bottom:%?54?%;border-radius:%?20?%;background-color:#fff}.order-summary .order-cell[data-v-0bac068e]{font-size:%?26?%}.order-summary .order-cell .tit[data-v-0bac068e]{width:%?170?%}.order-summary .order-cell .box[data-v-0bac068e]{display:flex;align-items:center}.order-summary .order-cell .copy[data-v-0bac068e]{font-size:%?26?%;display:inline-block;color:var(--custom-brand-color);line-height:1;margin-left:%?20?%}.order-summary .hr[data-v-0bac068e]{width:calc(100% - %?190?%);margin-left:%?190?%;height:1px;background:#f7f7f7}.order-money[data-v-0bac068e]{margin:0 %?20?%;padding:%?1?% %?20?%;background-color:#fff}.order-money .tit[data-v-0bac068e]{font-size:%?30?%;font-weight:400;line-height:%?64?%;color:grey}.order-money .order-cell[data-v-0bac068e]{font-size:%?26?%}.order-money .order-cell .order-cell-right[data-v-0bac068e]{font-size:%?30?%;font-weight:700;line-height:%?64?%;color:#383838}.order-money .order-cell .order-cell-right .pay-money[data-v-0bac068e]{color:var(--custom-brand-color)}.order-money .order-cell .order-cell-right-tip[data-v-0bac068e]{font-size:%?30?%;font-weight:400;line-height:%?64?%;color:#383838}.order-money .order-cell .box[data-v-0bac068e]{padding:0}.order-money .order-cell .box .operator[data-v-0bac068e]{font-size:%?24?%;margin-right:%?6?%}.kefu[data-v-0bac068e]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.kefu > uni-view[data-v-0bac068e]{display:flex;justify-content:center;align-items:center}.kefu > uni-view .iconfont[data-v-0bac068e]{font-weight:700;margin-right:%?10?%;font-size:%?28?%;line-height:1}.kefu uni-button[data-v-0bac068e]{width:100%;height:100%;position:absolute;border:none;z-index:1;padding:0;margin:0;background:none}.kefu uni-button[data-v-0bac068e]::after{border:none!important}.order-refund[data-v-0bac068e]{display:flex;flex-direction:row-reverse}.order-operation[data-v-0bac068e]{position:fixed;left:0;bottom:0;width:100vw;height:%?126?%;display:flex;justify-content:flex-end;align-items:center;background:#fff}.order-operation .order-box-btn[data-v-0bac068e]{margin-right:%?20?%;margin-left:0}.desc-box[data-v-0bac068e], .desc-box .desc[data-v-0bac068e]{display:flex;align-items:center;justify-content:center;font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:#fff}.icon-pay-clock[data-v-0bac068e]{font-size:%?38?%;line-height:%?44?%;margin-right:%?15?%;margin-top:%?-50?%}.site-name[data-v-0bac068e]{font-size:%?28?%;font-weight:700;border-bottom:%?2?% solid #f5f5f5;width:100%}.order-box-btn[data-v-0bac068e]{min-width:%?160?%;padding:0 %?16?%;box-sizing:border-box;display:flex;height:%?64?%;line-height:%?64?%!important;text-align:center;align-items:center;justify-content:center;border-color:#ccc!important;color:#666!important;font-size:%?26?%}.order-box-btn.order-pay[data-v-0bac068e]{background:var(--custom-brand-color)!important;border-color:var(--custom-brand-color)!important;color:#fff!important}.order-box-btn-wrap[data-v-0bac068e]{display:inline-block}',""]),t.exports=e},"642c":function(t,e,a){var i=a("5dc2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("330b790c",i,!0,{sourceMap:!1,shadowMode:!1})},"764d":function(t,e,a){var i=a("b81d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("36126566",i,!0,{sourceMap:!1,shadowMode:!1})},"78f85":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?a("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?a("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),a("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),a("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),a("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"86f3":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0bac068e]{width:100%;text-align:center}.my-popup[data-v-0bac068e]{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:2;display:flex;align-items:center;justify-content:center}.popup-mask[data-v-0bac068e]{position:absolute;top:0;left:0;width:100%;height:100vh;background-color:rgba(0,0,0,.75)}.popup-box[data-v-0bac068e]{width:%?620?%;z-index:3}.popup-dialog[data-v-0bac068e]{overflow:hidden;background:#fff;border-radius:%?20?%;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-0bac068e]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body uni-textarea[data-v-0bac068e]{width:%?556?%;padding:%?15?%;box-sizing:border-box;margin:0 auto;border:1px solid #ccc;height:%?260?%;border-radius:%?8?%}.popup-dialog .popup-dialog-body-tip[data-v-0bac068e]{color:var(--custom-brand-color);font-size:%?24?%;line-height:1.5;padding:0 %?32?%;box-sizing:border-box;margin-top:%?10?%}.popup-dialog .popup-dialog-footer[data-v-0bac068e]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-0bac068e]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-0bac068e]{color:var(--custom-brand-color);background:#fff;border:%?1?% solid var(--custom-brand-color)}.popup-dialog .popup-dialog-footer .button.red[data-v-0bac068e]{color:#fff;background:var(--custom-brand-color)}.heightvh[data-v-0bac068e]{overflow-y:hidden}.refund-apply-money[data-v-0bac068e]{display:flex;align-items:center;justify-content:space-between;padding:0 %?20?%;margin:0 %?20?%;margin-bottom:%?20?%;height:%?100?%;border-radius:%?20?%;background-color:#fff}.refund-apply-money .name[data-v-0bac068e]{font-weight:700;color:#333;font-size:%?28?%}.refund-apply-money .money[data-v-0bac068e]{font-weight:700;color:var(--custom-brand-color);font-size:%?36?%}.refund-apply-money .money .text[data-v-0bac068e]{font-weight:400;font-size:%?26?%}.select-popup[data-v-0bac068e] .uni-popup__wrapper-box{max-width:none!important;width:auto!important}.select-box[data-v-0bac068e]{position:relative;height:%?580?%;display:flex;align-items:center;justify-content:center}.select-box > uni-view[data-v-0bac068e]:first-child{flex:1;display:flex;flex-direction:column;margin-left:%?110?%}.select-box > uni-view:first-child uni-image[data-v-0bac068e]{width:%?200?%;height:%?200?%}.select-box > uni-view:first-child uni-view[data-v-0bac068e]{width:%?200?%;text-align:center}.select-box > uni-view[data-v-0bac068e]:last-child{flex:1;display:flex;flex-direction:column}.select-box > uni-view:last-child uni-image[data-v-0bac068e]{width:%?200?%;height:%?200?%}.select-box > uni-view:last-child uni-view[data-v-0bac068e]{width:%?200?%;text-align:center}.close[data-v-0bac068e]{position:absolute;width:%?32?%;height:%?32?%;right:%?36?%;top:%?28?%}.page-title[data-v-0bac068e]{width:%?360?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;text-align:center;color:#fff}',""]),t.exports=e},"88e4":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("39d8")),r=i(a("2634")),n=i(a("2fdc")),s=(i(a("cbf3")),{methods:{orderPay:function(t,e){var a=this;return(0,n.default)((0,r.default)().mark((function i(){return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,a.$util.subscribeMessage({source:"order",source_id:t.order_id,scene_type:"order_pay_before"});case 2:uni.showLoading({mask:!0,title:"加载中"}),0==t.adjust_money?a.$api.sendRequest({url:"/api/order/pay",data:{order_ids:t.order_id},success:function(i){if(i.code>=0)a.createBuriedPoint("out_trade_no",i.data,3),a.orderPayPay(i.data,t,(function(t){"function"==typeof e&&e(t)}));else{if(-11==i.code)return uni.hideLoading(),"function"==typeof e&&e(i),!1;a.$util.showToast({title:i.message}),uni.hideLoading()}}}):uni.showModal({title:"提示",content:"商家已将支付金额调整为"+t.pay_money+"元，是否继续支付？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/pay",data:{order_ids:t.order_id},success:function(i){if(i.code>=0)a.createBuriedPoint("out_trade_no",i.data,3),a.orderPayPay(i.data,t,(function(t){"function"==typeof e&&e(t)}));else{if(-11==i.code)return uni.hideLoading(),"function"==typeof e&&e(i),!1;a.$util.showToast({title:i.message}),uni.hideLoading()}}})}});case 4:case"end":return i.stop()}}),i)})))()},orderClose:function(t,e){var a=this;uni.showModal({title:"提示",content:"确定要取消该订单吗？",cancelText:"我再想想",confirmText:"确定",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/close",data:{order_id:t},success:function(i){if(i.code>=0)a.createBuriedPoint("order_id",t,250),a.$util.showToast({title:"取消订单成功！",success:function(){"function"==typeof e&&e()}});else{if(-11==i.code)return a.$util.showToast({title:i.message}),setTimeout((function(){"function"==typeof e&&e()}),1500),!1;a.$util.showToast({title:i.message})}}})}})},orderDelivery:function(t,e){var a=this;uni.showModal({title:"提示",content:"你已经收到货物了吗？",confirmText:"已收到",cancelText:"未收到",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t},success:function(t){"function"==typeof e&&e()}})}})},orderPayPay:function(t,e,a){var i=this;e.order_id;this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:t,pay_type:"adapay"},success:function(e){e.code>=0?(uni.hideLoading(),i.$util.wechatPay(e.data.pay_type,"adapay"==e.data.pay_type?e.data.payment:e.data.pay_info,(function(e){i.createBuriedPoint("out_trade_no",t,11),i.$util.redirectTo("/pages/order/list/list?status=all",{},"redirectTo")}),(function(e){i.createBuriedPoint("out_trade_no",t,9001),uni.hideLoading()}),(function(t){uni.hideLoading()}),i)):(console.log(e),e.message?i.$util.showToast({title:e.message}):uni.hideLoading()),"function"==typeof a&&a(e)},fail:function(t){i.$util.showToast({title:"request:fail"})}})},createBuriedPoint:function(t,e,a){var i;this.$buriedPoint.orderStatus((i={},(0,o.default)(i,t,e),(0,o.default)(i,"status",a),i))}}});e.default=s},"8f99":function(t,e,a){"use strict";a.r(e);var i=a("5d87"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},b81d:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-d6071282]{width:100%;text-align:center}.uni-countdown[data-v-d6071282]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-d6071282]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?28?%}.uni-countdown__splitor.day[data-v-d6071282]{line-height:%?50?%}.uni-countdown__number[data-v-d6071282]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},c836:function(t,e,a){var i=a("86f3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("8c4f50e4",i,!0,{sourceMap:!1,shadowMode:!1})},d724:function(t,e,a){var i=a("56a1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("3826d8c5",i,!0,{sourceMap:!1,shadowMode:!1})},ecc6:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},created:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,a,i){return 60*t*60*24+60*e*60+60*a+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,a=0,i=0,o=0;t>0?(e=Math.floor(t/86400),a=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*a,o=Math.floor(t)-24*e*60*60-60*a*60-60*i):this.timeUp(),e<10&&(e="0"+e),a<10&&(a="0"+a),i<10&&(i="0"+i),o<10&&(o="0"+o),this.d=e,this.h=a,this.i=i,this.s=o}}};e.default=i},f8de:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a("4b89"),o={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){i.isOnXianMaiApp&&this.appCurrentPages<=1?(0,i.goClosePage)("0"):uni.navigateBack()}}};e.default=o},fe2a:function(t,e,a){"use strict";a.r(e);var i=a("ecc6"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a}}]);