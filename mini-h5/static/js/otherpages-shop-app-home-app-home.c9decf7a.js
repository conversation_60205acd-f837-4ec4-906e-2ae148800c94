(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-shop-app-home-app-home"],{"0d02":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),o=i(n("2fdc")),r={data:function(){return{list:[],img:["public/static/youpin/coupon_bg_1.png","public/static/youpin/coupon_bg_2.png","public/static/youpin/coupon_bg_3.png","public/static/youpin/coupon_bg_4.png"],boxClass:["box1","box2","box3","box4"]}},onLoad:function(){this.$util.toShowCouponPopup(this)},methods:{open:function(){this.listInfo()},listInfo:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 3:n=e.sent,n.data.length&&(t.list=n.data,t.$refs.coupon.open()),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})}}};e.default=r},"11d6":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7c2c1f1c]{width:100%;text-align:center}',""]),t.exports=e},"1ed19":function(t,e,n){"use strict";var i=n("8b41"),a=n.n(i);a.a},2033:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),o=i(n("2fdc"));n("f7a5"),n("c223"),n("bf0f"),n("2797"),n("8f71"),n("3efd"),n("e966"),n("aa9c"),n("e838");var r=i(n("7c8d")),s=i(n("85bf")),c=i(n("07ad")),l=i(n("4a67")),d=i(n("ada6")),u=i(n("76ff")),f=i(n("188a")),p=i(n("be30")),h=i(n("b9bf")),g=i(n("45ef")),v=i(n("a0cd")),m=i(n("d636")),b=i(n("97e1")),x=i(n("5e99")),y=i(n("f9a5")),w=i(n("d4f7")),_=i(n("7641")),k=i(n("546f")),C=i(n("64d9")),$=i(n("2325")),S=i(n("0519")),P=i(n("2d01")),A=n("4b89"),T=n("aa3e"),D=n("4b89"),I={components:{diySearch:c.default,diyAdvertisingSwiper:l.default,diyActivityAds:d.default,diyChannelArea:u.default,diyPintuan:f.default,diyMaidou:p.default,diySeckill:h.default,diyRecommendProduct:g.default,diyGoodsList:v.default,diyTagProduct:m.default,diyNotice:b.default,uniPopup:x.default,diyFloatingRollingOrder:y.default,diyProductTopic:w.default,diyNewProductArea:_.default,diyLive:k.default,diySeeding:C.default,diyVideo:$.default,diySlideShow:S.default},mixins:[P.default],data:function(){return{searchObj:null,searchShow:!1,shop_id:null,openBottomNav:!1,isToTop:!1,tags:[],tagsIndex:0,tagsIcon:!1,ads:[],channels:[],recommend_goods:[],pintuanList:[],seckill:{},maidou:{},statusBarHeight:0,navHeight:40,shop_name:"",activeAds:{top1:null,top2:null,bottom1:null,bottom2:null,bottom3:null},exposeStatus:!0,exposeProducts:!1,popData:{},diyData:{},diyDataList:[],bgColor:"",bgUrl:"",tagsBgColor:"",ccFontColor:"",ccActionFontColor:"",openCategoryColumn:!1,shareTitle:"",shareImg:"",products:[],sbgc:"",timing:0,timingObj:null,fullScreenMode:"",topHeight:0,scrollTop:0,isFixedTop:!1,headerColor:"",noticeNum:0,messageSvg:"",homeShareSvg:"",redDotBackgroundColor:"var(--custom-brand-color)",redDotTextColor:"#ffffff"}},methods:{onSubscribe:function(t,e){this.$refs.diyLiveRef[0].changeSubscribe(t.detail.room_id)},getSvg:function(t,e){return e=this.$util.colorToHex(e).slice(1),encodeURI(this.$util.img("api/website/svgChangeFillColor?svg_name=".concat(t,"&color=").concat(e)))},getDiyInfo:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:"/api/diyview/appinfo",async:!1,data:{name:"DIYVIEW_APP_INDEX",site_id:t.shop_id||0}});case 2:n=e.sent,n.data&&(i=JSON.parse(n.data.value),t.diyData=i,t.bgColor=i.global.bgColor||"linear-gradient(0deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 100%)",t.bgUrl=i.global.bgUrl,t.tagsBgColor=i.global.bgColor,t.openCategoryColumn=i.global.openCategoryColumn,t.ccFontColor=i.global.ccFontColor,t.ccActionFontColor=i.global.ccActionFontColor,t.shareTitle=i.global.shareTitle,t.shareImg=i.global.shareImg,t.openBottomNav=!1,t.topHeight=t.openCategoryColumn?t.statusBarHeight+2*t.navHeight:t.statusBarHeight+t.navHeight,i.value.length>0&&i.value.forEach((function(e,n){"SEARCH"==e.type&&(t.searchShow=!0,t.sbgc=e.imageUrl||e.backgroundColor,t.searchObj=e),e.key=Math.floor(10*Math.random())+String(n)})),t.diyDataList=i.value);case 4:case"end":return e.stop()}}),e)})))()},scroll:function(t){var e,n,i,a;(this.scrollTop=t.scrollTop,t.scrollTop>=200&&this.exposeStatus)&&(this.exposeStatus=!1,this.$buriedPoint.exposeGoods(null!==(e=this.pintuanList)&&void 0!==e?e:[],"goods_id"),this.$buriedPoint.exposeGoods(null!==(n=this.maidou.list)&&void 0!==n?n:[],"sku_id"),this.$buriedPoint.exposeGoods(null!==(i=this.seckill.list)&&void 0!==i?i:[],"sku_id"),this.$refs.diyProductTopicRef&&this.$buriedPoint.exposeGoods(null!==(a=this.$refs.diyProductTopicRef[0].dataList)&&void 0!==a?a:[],"sku_id"));t.scrollTop>=1e3&&!this.exposeProducts&&(this.$buriedPoint.exposeGoods(this.products,"sku_id"),this.exposeProducts=!0)},changeTag:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.$refs.loadingCover&&e.$refs.loadingCover.show(),e.tagsIndex=t,e.$refs.mescroll.refresh(),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.$refs.mescroll.toTopClick();case 5:case"end":return n.stop()}}),n)})))()},getAdInfo:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i,o;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=[],n.next=3,e.$api.sendRequest({url:r.default.specialBannerUrl,async:!1,data:{sign:t}});case 3:if(o=n.sent,0==o.code){n.next=6;break}return n.abrupt("return",i);case 6:return i=o.data.list,n.abrupt("return",i);case 8:case"end":return n.stop()}}),n)})))()},getData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i,o,s,c,l,d,u,f;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getAdInfo("index-1");case 2:return n=e.sent,t.ads=n,n&&n.length>0&&t.diyDataList.filter((function(t){return"ImageAds"==t.controller&&"carousel-posters-2"==t.selectedTemplate})).length>0&&(t.isFixedTop=!0),e.next=7,t.getAdInfo("index-2");case 7:i=e.sent,o=0;case 9:if(!(o<i.length)){e.next=26;break}e.t0=i[o].sign,e.next="index-2-1"===e.t0?13:"index-2-2"===e.t0?15:"index-2-3"===e.t0?17:"index-2-4"===e.t0?19:"index-2-5"===e.t0?21:23;break;case 13:return t.activeAds.top1=i[o],e.abrupt("break",23);case 15:return t.activeAds.top2=i[o],e.abrupt("break",23);case 17:return t.activeAds.bottom1=i[o],e.abrupt("break",23);case 19:return t.activeAds.bottom2=i[o],e.abrupt("break",23);case 21:return t.activeAds.bottom3=i[o],e.abrupt("break",23);case 23:o++,e.next=9;break;case 26:if(!(t.diyDataList.filter((function(t){return"Pintuan"==t.controller})).length>0)){e.next=29;break}return e.next=29,t.getPintuanGoodsList();case 29:return e.next=31,t.$api.sendRequest({url:r.default.homeUrl,async:!1,data:{}});case 31:if(s=e.sent,0==s.code){e.next=35;break}return uni.showToast({title:s.message,mask:!0,icon:"none",duration:3e3}),e.abrupt("return");case 35:if(t.tags=s.data.cates,t.tags.unshift({category_name:"首页",category_id:null}),t.tagsIcon=!!s.data.show_cate_icon,c=s.data.channels,10,l=0,d=[],c.length>0)for(l=parseInt(c.length/10)+1,u=0;u<l;u++)u+1==l?(f=c.slice(10*u),f.length>0&&d.push(c.slice(10*u))):d.push(c.slice(10*u,10*(u+1)));s.data.seckill.action_time=s.data.seckill.action_time?1e3*Math.abs(s.data.seckill.action_time):0,t.channels=d,t.recommend_goods=s.data.recommend_goods,t.seckill="Object"==Object.prototype.toString.call(s.data.seckill).slice(8,-1)?s.data.seckill:{},t.maidou=s.data.maidou,t.$refs.loadingCover&&t.$refs.loadingCover.hide();case 49:case"end":return e.stop()}}),e)})))()},getPintuanGoodsList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:r.default.pintuanGoodsList,async:!1,data:{page_size:5}});case 2:if(n=e.sent,0==n.code){e.next=6;break}return uni.showToast({title:n.message,mask:!0,icon:"none",duration:3e3}),e.abrupt("return");case 6:i=n.data.list,t.pintuanList=i;case 8:case"end":return e.stop()}}),e)})))()},toCategory:function(){this.$util.diyCompateRedirectTo({wap_url:"`/otherpages/shop/category/category?shop_id=${this.shop_id}&showNavBar=true`"})},toPop:function(t){this.$refs.homePop&&this.$refs.homePop.close(),this.$buriedPoint.diyReportAdEvent({diy_ad_location_type:"home",diy_material_path:this.popData.image_url,diy_ad_type:"image",diy_target_page:t,diy_ad_id:this.popData.id,diy_action_type:"click"}),this.$util.specialBannerReportByClick(this.popData.id),t&&this.$util.diyCompateRedirectTo({wap_url:t})},goHome:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,s.default.wait_staticLogin_success();case 2:t.optUp.toTop.src=e.$util.img("public/static/youpin/to-top.png"),t.optUp.toTop.width="144rpx",e.$nextTick((function(){0==e.tagsIndex?e.getGoodsList(t):e.$refs.getTabProductRef.getCategoryGoodsList(t,e.tags[e.tagsIndex])}));case 5:case"end":return n.stop()}}),n)})))()},getGoodsList:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i,o;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,e.$api.sendRequest({url:r.default.goodsListUrl,async:!1,data:{shop_id:e.shop_id,page_size:t.size,page:t.num}});case 2:if(i=n.sent,0==i.code){n.next=6;break}return uni.showToast({title:i.message,mask:!0,icon:"none",duration:3e3}),n.abrupt("return");case 6:o=i.data.list,t.endSuccess(o.length),1==t.num&&(e.products=[]),e.products=e.products.concat(o),e.exposeProducts&&e.$buriedPoint.exposeGoods(o,"sku_id");case 11:case"end":return n.stop()}}),n)})))()},listenRefresh:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=t.tagsIndex){e.next=14;break}return e.next=3,s.default.wait_staticLogin_success();case 3:return e.next=5,t.getDiyInfo();case 5:return e.next=7,t.getData();case 7:return n=t.$util.deepClone(t.getSharePageParams()),i=window.location.origin+t.$router.options.base+n.link.slice(1),n.link=i,e.next=12,t.$util.publicShare(n);case 12:return e.next=14,t.getAppNotice();case 14:case"end":return e.stop()}}),e)})))()},imageError:function(t,e){t instanceof Object&&t[e]&&t[e].goods_image&&(t[e].goods_image=this.$util.getDefaultImage().default_goods_img),t instanceof Object&&t[e]&&t[e].image_url&&(t[e].image_url=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()},adCountDown:function(){var t=this;this.timing<0?this.$refs.homePop&&this.$refs.homePop.close():this.timingObj=setTimeout((function(){t.timing=t.timing-1e3,t.adCountDown()}),1e3)},getPopData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i,o,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getAdInfo("index-3");case 2:return n=e.sent,e.next=5,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 5:if(i=e.sent,0!=i.data.code||!i.data.length){e.next=10;break}t.$util.toShowCouponPopup(t),e.next=28;break;case 10:if(!(n.length>0)){e.next=28;break}return t.popData=n[0],e.prev=12,e.next=15,t.$util.getImageInfo(t.popData.image_url);case 15:return o=e.sent,e.next=18,uni.getWindowInfo();case 18:r=e.sent,parseFloat((r.windowHeight/r.windowWidth).toFixed(2))>parseFloat((o.height/o.width).toFixed(2))?t.fullScreenMode="heightFix":t.fullScreenMode="widthFix",e.next=24;break;case 22:e.prev=22,e.t0=e["catch"](12);case 24:t.$refs.homePop&&t.$refs.homePop.open(),t.$buriedPoint.diyReportAdEvent({diy_ad_location_type:"home",diy_material_path:t.popData.image_url,diy_ad_type:"image",diy_ad_id:t.popData.id,diy_action_type:"display"}),t.timing=5e3,t.adCountDown();case 28:case"end":return e.stop()}}),e,null,[[12,22]])})))()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg?v=1"))},headerColorChange:function(t){if("var(--custom-brand-color)"==t?(t=this.$util.generateRGBAColors(this.themeColorVar["--custom-brand-color"]).pop(),this.messageSvg=this.getSvg("message","#ffffff"),this.homeShareSvg=this.getSvg("homeShare","#ffffff"),this.redDotTextColor="var(--custom-brand-color)",this.redDotBackgroundColor="#ffffff"):(this.messageSvg=this.getSvg("message","#545454"),this.homeShareSvg=this.getSvg("homeShare","#545454"),this.redDotBackgroundColor="var(--custom-brand-color)",this.redDotTextColor="#ffffff"),this.headerColor=this.$util.rgbaToRgb(t),this.headerColor&&"h5"==this.$util.getPlatform()){var e=this.$util.colorToHex(this.headerColor);(0,D.changeNavBgColor)(e)}},scrollToY:function(t){this.$refs.mescroll.toScrollPointY(t)},clickBanner:function(t){this.$util.specialBannerReportByClick(t)},gotoapp:function(){(0,A.schemeGo)(T.myMessageUrl)},appshare:function(){var t=this.getSharePageParams(),e=t.title,n=t.link,i=t.imageUrl,a=(t.query,{title:e,desc:e,webpageUrl:"",thumbImage:i,path:n});(0,A.shareMiniProgramSchemeGo)(a)},getAppNotice:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:r.default.getAppNotice,async:!1});case 2:if(n=e.sent,0==n.code){e.next=6;break}return uni.showToast({title:n.message,mask:!0,icon:"none",duration:3e3}),e.abrupt("return");case 6:t.noticeNum=n.data.unread_msg_count;case 7:case"end":return e.stop()}}),e)})))()}},onLoad:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function n(){var i;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.messageSvg=e.getSvg("message","#545454"),e.homeShareSvg=e.getSvg("homeShare","#545454"),uni.setStorageSync("appOrigin",window.location.origin+e.$router.options.base),i=uni.getStorageSync("shop_id"),e.shop_id=i||110,e.startData=t;case 6:case"end":return n.stop()}}),n)})))()},onShow:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$langConfig.refresh(),t.next=3,s.default.wait_staticLogin_success();case 3:case"end":return t.stop()}}),t)})))()},onShareAppMessage:function(t){var e=t.target&&t.target.dataset&&t.target.dataset.shareType;if(e&&"liveRoom"==e){var n=this.$refs.diyLiveRef[0].toShareAppMessage(t),i=n.title,a=n.link,o=n.imageUrl;n.query;return this.$buriedPoint.pageShare(a,o,i)}var r=this.getSharePageParams(),s=r.title,c=r.link,l=r.imageUrl;r.query;return this.$buriedPoint.pageShare(c,l,s)},onShareTimeline:function(t){var e=this.getSharePageParams(),n=e.title,i=e.imageUrl,a=e.query;return{title:n,imageUrl:i,query:a,success:function(t){},fail:function(t){}}}};e.default=I},"275c":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7c2c1f1c]{width:100%;text-align:center}.home[data-v-7c2c1f1c]{width:100%;height:100vh;box-sizing:border-box;position:relative;background-repeat:no-repeat!important;background-size:100% auto!important;overflow-y:hidden}.home--content[data-v-7c2c1f1c]{box-sizing:border-box;padding:0 %?20?%;padding-bottom:calc(%?300?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?300?% + env(safe-area-inset-bottom))}.home--content--header[data-v-7c2c1f1c]{position:fixed;top:%?0?%;left:0;width:100%;box-sizing:border-box;z-index:5;transition:all .5s}.home--content--tags[data-v-7c2c1f1c]{padding:0 %?25?%;display:flex;justify-content:flex-start;align-items:center;background-color:initial}.home--content--tags--list[data-v-7c2c1f1c]{white-space:nowrap}.home--content--tags--list--has[data-v-7c2c1f1c]{width:calc(100% - %?63?%)}.home--content--tags--list uni-view[data-v-7c2c1f1c]{display:inline-block;font-size:%?26?%;font-weight:400;position:relative;margin-right:%?52?%}.home--content--tags--list uni-view uni-text[data-v-7c2c1f1c]{white-space:nowrap}.home--content--tags--list uni-view.active uni-text[data-v-7c2c1f1c]:last-child{width:%?28?%;height:%?28?%;overflow:hidden;background:transparent;border-bottom-left-radius:%?32?%;border-bottom-right-radius:%?0?%;border-left:%?6?% solid;border-bottom:%?6?% solid;position:absolute;left:50%;bottom:%?-16?%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.home--content--tags--more[data-v-7c2c1f1c]{width:%?63?%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:flex-end}.home--content--tags--more uni-image[data-v-7c2c1f1c]{width:%?27?%;height:%?27?%}.home--content--tags--more uni-text[data-v-7c2c1f1c]:first-child{width:%?28?%;height:%?4?%;margin-bottom:%?6?%}.home--content--tags--more uni-text[data-v-7c2c1f1c]:nth-child(2){width:%?14?%;height:%?4?%;margin-bottom:%?6?%}.home--content--tags--more uni-text[data-v-7c2c1f1c]:nth-child(3){width:%?24?%;height:%?4?%}.channel[data-v-7c2c1f1c]{margin-top:%?35?%}.channel--list[data-v-7c2c1f1c]{height:%?350?%}.channel--list--item[data-v-7c2c1f1c]{display:flex;flex-wrap:wrap;justify-content:flex-start;align-items:flex-start}.channel--list--item--channel[data-v-7c2c1f1c]{width:%?138?%;display:flex;flex-direction:column;align-items:center}.channel--list--item--channel uni-image[data-v-7c2c1f1c]{width:%?96?%;height:%?96?%;border-radius:50%}.channel--list--item--channel uni-view[data-v-7c2c1f1c]{font-size:%?24?%;font-weight:500;color:#9a9a9a;margin-top:%?20?%}.channel--dot[data-v-7c2c1f1c]{display:flex;justify-content:center;align-items:center;margin-top:%?15?%}.channel--dot--index[data-v-7c2c1f1c]{width:%?43?%;height:%?6?%;background:#d4d8de;border-radius:%?3?%}.channel--dot--active[data-v-7c2c1f1c]{background:#f2280c}.to-top[data-v-7c2c1f1c]{width:%?144?%;height:%?152?%;position:fixed;right:0;bottom:%?200?%}.nav[data-v-7c2c1f1c]{width:100%;overflow:hidden;position:fixed;top:0;left:0;z-index:10}.nav-title[data-v-7c2c1f1c]{width:100%;text-align:center;position:absolute;left:0;z-index:10;display:flex;align-items:center;padding:0 %?20?%;box-sizing:border-box;justify-content:space-between}.nav-title-left[data-v-7c2c1f1c]{position:relative;display:flex;flex-direction:column;justify-content:center}.nav-title-left-img[data-v-7c2c1f1c]{width:%?44?%;height:%?44?%}.nav-title-left .red-dot[data-v-7c2c1f1c]{min-width:%?20?%;height:%?10?%;padding:%?6?%;line-height:%?0?%;padding-top:%?16?%;font-size:%?20?%;border-radius:50px;text-align:center;color:#fff;background-color:var(--custom-brand-color);position:absolute;top:0;left:%?30?%;z-index:1}.nav-title-center[data-v-7c2c1f1c]{width:%?540?%}.nav-title-right[data-v-7c2c1f1c]{display:flex;flex-direction:column;justify-content:center}.nav-title-right-img[data-v-7c2c1f1c]{width:%?44?%;height:%?44?%}.nav .home-logo[data-v-7c2c1f1c]{height:%?88?%;display:flex;align-items:center;justify-content:flex-start}.nav .home-logo uni-image[data-v-7c2c1f1c]{width:%?190?%;height:auto}.nav .home-logo uni-view[data-v-7c2c1f1c]{display:block;width:calc(100% - %?120?%);overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-left:%?10?%;text-align:left;font-size:%?30?%;color:#666;font-weight:700}[data-v-7c2c1f1c] uni-image.mescroll-totop{right:0!important}[data-v-7c2c1f1c] uni-view.empty{top:40vh}.pop-ad[data-v-7c2c1f1c] .uni-popup__wrapper-box{background:transparent!important;max-width:100%!important;max-height:100%!important;border-radius:0!important}.pop-ad[data-v-7c2c1f1c] .uni-popup__wrapper.center{justify-content:flex-start;align-items:flex-start}.pop-ad-info[data-v-7c2c1f1c]{background:transparent;width:100vw;height:100vh}.pop-ad-info-img[data-v-7c2c1f1c]{width:100vw;height:100vh}.pop-ad-info-close[data-v-7c2c1f1c]{width:%?88?%;height:%?88?%;display:block}.pop-ad-time[data-v-7c2c1f1c]{position:absolute;right:%?60?%;bottom:%?20?%;width:%?160?%;line-height:%?160?%;height:%?48?%;background-color:rgba(0,0,0,.5);border:1px solid hsla(0,0%,100%,.2);font-size:%?24?%;border-radius:%?98?%;color:#fff;display:flex;align-items:center;justify-content:center}.pop-ad-time-one[data-v-7c2c1f1c]{width:45%;text-align:center;height:%?48?%;line-height:%?48?%}.pop-ad-time-op[data-v-7c2c1f1c]{width:45%;text-align:center;height:%?48?%;line-height:%?48?%}[data-v-7c2c1f1c] .uni-scroll-view ::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none;width:0;height:0;color:transparent;background:transparent}[data-v-7c2c1f1c] ::-webkit-scrollbar{display:none;width:0;height:0;color:transparent;background:transparent}',""]),t.exports=e},"313a":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uniPopup:n("5e99").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("uni-popup",{ref:"coupon",attrs:{custom:!0,"mask-click":!1}},[t.list.length?n("v-uni-view",{staticClass:"coupon-model",class:t.list.length<4?t.boxClass[t.list.length-1]:t.boxClass[3],style:{"background-image":"url("+(t.list.length<4?t.$util.img(t.img[t.list.length-1]):t.$util.img(t.img[3]))+")"}},[n("v-uni-view",{staticClass:"coupon-header"},[n("v-uni-view",{staticClass:"title"},[t._v("恭喜您获得以下优惠券")]),n("v-uni-view",{staticClass:"tip"},[t._v("马上去使用吧！")])],1),n("v-uni-view",{staticClass:"coupon-box"},t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"coupon-list",style:{"background-image":"url("+t.$util.img("public/static/youpin/coupon_border.png")+")"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toGoodList(e)}}},[n("v-uni-view",{staticClass:"left"},[n("v-uni-view",{staticClass:"info"},[n("v-uni-view",[t._v("¥")]),e.money<100?[0==e.money.split(".")[1]?n("v-uni-view",[t._v(t._s(e.money.split(".")[0]))]):t._e(),e.money.split(".")[1]>0?n("v-uni-view",[t._v(t._s(e.money.split(".")[0])+"."),n("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])]):t._e()]:n("v-uni-view",{staticClass:"money-thousand"},[t._v(t._s(e.money.split(".")[0])+"."),n("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])])],2)],1),n("v-uni-view",{staticClass:"right"},[n("v-uni-view",[n("v-uni-view",{staticClass:"name"},[t._v(t._s(e.desc))]),n("v-uni-view",{staticClass:"time h5-time"},[t._v("有效期至"+t._s(e.end_time))])],1),n("v-uni-view",{staticClass:"btn"},[n("v-uni-view",{staticClass:"h5-btn"},[t._v("去使用")])],1)],1)],1)})),1)],1):t._e(),n("v-uni-image",{staticClass:"pop-ad-info-close",attrs:{src:t.$util.img("public/static/youpin/icon-close-overlay.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.coupon.close()}}})],1)],1)},o=[]},"327f":function(t,e,n){"use strict";var i=n("a971"),a=n.n(i);a.a},"37ff":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},a=[]},"46e8":function(t,e,n){var i=n("c881");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("f73cf6f6",i,!0,{sourceMap:!1,shadowMode:!1})},"4d82":function(t,e,n){"use strict";n.r(e);var i=n("bf93"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"50b4":function(t,e,n){"use strict";n.r(e);var i=n("0d02"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"528d":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-38dab624]{width:100%;text-align:center}.rolling-order[data-v-38dab624]{position:fixed;display:flex;align-items:center;background-color:rgba(0,0,0,.6);padding:0 %?20?% 0 %?6?%;height:%?60?%;line-height:%?60?%;box-sizing:border-box;border-radius:%?40?%;opacity:0}.rolling-order-head[data-v-38dab624]{width:%?50?%;height:%?50?%;border-radius:50%}.rolling-order-text[data-v-38dab624]{font-size:%?24?%;line-height:1.5;color:#fff;margin-left:%?10?%}',""]),t.exports=e},"5e6f":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.amount=function(t){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(t)},e.array=function(t){if("function"===typeof Array.isArray)return Array.isArray(t);return"[object Array]"===Object.prototype.toString.call(t)},e.carNo=function(t){if(7===t.length)return/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/.test(t);if(8===t.length)return/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/.test(t);return!1},e.chinese=function(t){return/^[\u4e00-\u9fa5]+$/gi.test(t)},e.code=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;return new RegExp("^\\d{".concat(e,"}$")).test(t)},e.contains=function(t,e){return t.indexOf(e)>=0},e.date=function(t){if(!t)return!1;o(t)&&(t=+t);return!/Invalid|NaN/.test(new Date(t).toString())},e.dateISO=function(t){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(t)},e.digits=function(t){return/^\d+$/.test(t)},e.email=function(t){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(t)},e.empty=function(t){switch((0,a.default)(t)){case"undefined":return!0;case"string":if(0==t.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!t)return!0;break;case"number":if(0===t||isNaN(t))return!0;break;case"object":if(null===t||0===t.length)return!0;for(var e in t)return!1;return!0}return!1},e.enOrNum=function(t){return/^[0-9a-zA-Z]*$/g.test(t)},e.func=s,e.idCard=function(t){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(t)},e.image=function(t){var e=t.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(e)},e.jsonString=function(t){if("string"===typeof t)try{var e=JSON.parse(t);return!("object"!==(0,a.default)(e)||!e)}catch(n){return!1}return!1},e.landline=function(t){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(t)},e.letter=function(t){return/^[a-zA-Z]*$/.test(t)},e.mobile=function(t){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(t)},e.number=o,e.object=r,e.promise=function(t){return r(t)&&s(t.then)&&s(t.catch)},e.range=function(t,e){return t>=e[0]&&t<=e[1]},e.rangeLength=function(t,e){return t.length>=e[0]&&t.length<=e[1]},e.regExp=function(t){return t&&"[object RegExp]"===Object.prototype.toString.call(t)},e.string=function(t){return"string"===typeof t},e.url=function(t){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(t)},e.video=function(t){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(t)};var a=i(n("fcf3"));function o(t){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(t)}function r(t){return"[object Object]"===Object.prototype.toString.call(t)}function s(t){return"function"===typeof t}n("5c47"),n("0506"),n("c9b5"),n("bf0f"),n("ab80"),n("5ef2"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e")},8416:function(t,e,n){"use strict";var i=n("46e8"),a=n.n(i);a.a},"856a":function(t,e,n){"use strict";var i=n("ac3f"),a=n.n(i);a.a},8765:function(t,e,n){"use strict";n.r(e);var i=n("313a"),a=n("50b4");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8416");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"7ca63514",null,!1,i["a"],void 0);e["default"]=s.exports},8919:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uniPopup:n("5e99").default,diyFloatingRollingOrder:n("f9a5").default,diyBottomNav:n("dea9").default,loadingCover:n("5510").default,ydAuthPopup:n("161f").default,nsLogin:n("4f5a").default,uniCouponPop:n("8765").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"home",style:[{background:t.bgColor,backgroundImage:"url("+t.bgUrl+")"},t.themeColorVar]},[n("v-uni-view",{staticClass:"nav bg-white",style:{height:t.navHeight+t.statusBarHeight+"px",backgroundColor:t.headerColor},on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[n("v-uni-view",{staticClass:"nav-title",style:{top:t.statusBarHeight+"px",height:t.navHeight+"px",lineHeight:t.navHeight+"px"}},[n("v-uni-view",{staticClass:"nav-title-left"},[n("v-uni-image",{staticClass:"nav-title-left-img",attrs:{src:t.messageSvg,mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gotoapp.apply(void 0,arguments)}}}),t.noticeNum?n("v-uni-text",{staticClass:"red-dot",style:{backgroundColor:t.redDotBackgroundColor,color:t.redDotTextColor}},[t._v(t._s(t.noticeNum))]):t._e()],1),n("v-uni-view",{staticClass:"nav-title-center"},[t.searchShow?n("diy-search",{attrs:{searchObj:t.searchObj,sbgc:t.sbgc}}):t._e()],1),n("v-uni-view",{staticClass:"nav-title-right"},[n("v-uni-image",{staticClass:"nav-title-right-img",attrs:{src:t.homeShareSvg,mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.appshare.apply(void 0,arguments)}}})],1)],1)],1),t.openCategoryColumn?n("v-uni-view",{staticClass:"home--content--header",style:{top:t.navHeight+t.statusBarHeight+"px",backgroundColor:t.headerColor},on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[n("v-uni-view",{staticClass:"home--content--tags",style:{height:t.navHeight+"px"}},[n("v-uni-scroll-view",{staticClass:"home--content--tags--list",class:{"home--content--tags--list--has":t.tagsIcon},style:{height:t.navHeight+"px",lineHeight:t.navHeight+"px"},attrs:{"scroll-x":!0}},t._l(t.tags,(function(e,i){return n("v-uni-view",{key:i,class:{active:i==t.tagsIndex},style:{color:i==t.tagsIndex?t.ccActionFontColor:t.ccFontColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTag(i)}}},[n("v-uni-text",[t._v(t._s(e.category_name))]),n("v-uni-text",{style:{borderColor:t.ccActionFontColor}})],1)})),1),t.tagsIcon?n("v-uni-view",{staticClass:"home--content--tags--more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toCategory.apply(void 0,arguments)}}},[n("v-uni-text",{style:{backgroundColor:t.ccFontColor}}),n("v-uni-text",{style:{backgroundColor:t.ccFontColor}}),n("v-uni-text",{style:{backgroundColor:t.ccFontColor}})],1):t._e()],1)],1):t._e(),n("mescroll-uni",{ref:"mescroll",attrs:{top:t.isFixedTop?"0px":t.topHeight+"px"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.goHome.apply(void 0,arguments)},listenRefresh:function(e){arguments[0]=e=t.$handleEvent(e),t.listenRefresh.apply(void 0,arguments)},scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.scroll.apply(void 0,arguments)}}},[n("template",{attrs:{slot:"list"},slot:"list"},[n("v-uni-view",{staticClass:"home--fill"}),n("v-uni-view",{staticClass:"home--content"},[0==t.tagsIndex?t._l(t.diyDataList,(function(e,i){return n("v-uni-view",{key:e.key},["ImageAds"==e.controller?[n("diy-advertising-swiper",{ref:"diyAdvertisingSwiperRef",refInFor:!0,attrs:{ads:t.ads,imageError:t.imageError,config:e,"top-height":t.topHeight,"scroll-top":t.scrollTop},on:{headerColorChange:function(e){arguments[0]=e=t.$handleEvent(e),t.headerColorChange.apply(void 0,arguments)},clickBanner:function(e){arguments[0]=e=t.$handleEvent(e),t.clickBanner.apply(void 0,arguments)}}})]:t._e(),"GraphicNav"==e.controller?[n("diy-channel-area",{attrs:{channels:e.list,scrollSetting:e.scrollSetting}})]:t._e(),"SlideShow"==e.controller?[n("diy-slide-show",{attrs:{value:e}})]:t._e(),"ActivityAds"==e.controller?[n("diy-activity-ads",{attrs:{value:e}})]:t._e(),"Pintuan"==e.controller?[n("diy-pintuan",{attrs:{value:{backgroundColor:"#ffffff",item:e},dataList:t.pintuanList}})]:t._e(),"Maidou"==e.controller?[n("diy-maidou",{attrs:{value:{item:e},dataList:t.maidou,"site-id":t.shop_id},on:{finish:function(e){arguments[0]=e=t.$handleEvent(e),t.listenRefresh.apply(void 0,arguments)}}})]:t._e(),"Seckill"==e.controller?[n("diy-seckill",{attrs:{value:{item:e},dataList:t.seckill,"site-id":t.shop_id},on:{finish:function(e){arguments[0]=e=t.$handleEvent(e),t.listenRefresh.apply(void 0,arguments)}}})]:t._e(),"LiveInfo"==e.controller?[n("diy-live",{ref:"diyLiveRef",refInFor:!0,attrs:{value:e,"site-id":t.shop_id}},[n("template",{slot:"liveSubscribe"})],2)]:t._e(),"Seeding"==e.controller?[n("diy-seeding",{attrs:{value:{backgroundColor:"#ffffff",item:e}}})]:t._e(),"ProductTopic"==e.controller?[n("diy-product-topic",{ref:"diyProductTopicRef",refInFor:!0,attrs:{config:e,"top-height":t.topHeight,"show-top":t.isFixedTop?t.topHeight:0,"scroll-top":t.scrollTop},on:{scrollToPoint:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToY.apply(void 0,arguments)}}})]:t._e(),"NewProductArea"==e.controller?[n("diy-new-product-area",{attrs:{config:e}})]:t._e(),"ShoperRecommand"==e.controller?[n("diy-recommend-product",{attrs:{recommendGoods:t.recommend_goods,imageError:t.imageError}})]:t._e(),"GoodsList"==e.controller?[n("diy-goods-list",{attrs:{products:t.products}})]:t._e(),"Notice"==e.controller?[n("diy-notice",{attrs:{value:e,"site-id":t.shop_id}})]:t._e(),"Video"==e.controller?void 0:t._e()],2)})):[n("diy-tag-product",{ref:"getTabProductRef"})]],2)],1)],2),n("v-uni-view",{staticClass:"PopWindow",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[n("uni-popup",{ref:"homePop",staticClass:"pop-ad",attrs:{type:"center",maskClick:!1}},[n("v-uni-view",{staticClass:"pop-ad-info"},[t.fullScreenMode?n("v-uni-image",{staticClass:"pop-ad-info-img",attrs:{src:t.$util.img(t.popData.image_url),mode:t.fullScreenMode},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPop(t.popData.banner_url)}}}):t._e(),n("v-uni-view",{staticClass:"pop-ad-time",style:"bottom: "+(t.navHeight+80)+"rpx;",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.homePop.close()}}},[n("v-uni-text",{staticClass:"pop-ad-time-one"},[t._v(t._s(t.timing/1e3)+"s")]),n("v-uni-text",{staticClass:"pop-ad-time-op"},[t._v("跳过")])],1)],1)],1)],1),n("diy-floating-rolling-order",{attrs:{top:"h5"==t.$util.getPlatform()?"190rpx":"280rpx"}}),t.openBottomNav?n("diy-bottom-nav",{attrs:{type:"shop","site-id":t.shop_id}}):t._e(),n("loading-cover",{ref:"loadingCover"}),n("yd-auth-popup",{ref:"ydauth"}),n("ns-login",{ref:"login"}),n("uni-coupon-pop",{ref:"couponPop"})],1)},o=[]},"8b41":function(t,e,n){var i=n("275c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("3082d01e",i,!0,{sourceMap:!1,shadowMode:!1})},"8f68":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},9127:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},"9a7f":function(t,e,n){var i=n("528d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("d3405a38",i,!0,{sourceMap:!1,shadowMode:!1})},a971:function(t,e,n){var i=n("8f68");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("2d38abe0",i,!0,{sourceMap:!1,shadowMode:!1})},ac3f:function(t,e,n){var i=n("11d6");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("619b31dd",i,!0,{sourceMap:!1,shadowMode:!1})},b373:function(t,e,n){"use strict";var i=n("9a7f"),a=n.n(i);a.a},b8ea:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var a=i(n("9127")),o={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:a.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=o},bf01:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.activeData.message?n("v-uni-view",{staticClass:"rolling-order",style:t.style,attrs:{animation:t.animationData}},[n("v-uni-image",{staticClass:"rolling-order-head",attrs:{src:t.activeData.headimg?t.$util.img(t.activeData.headimg):t.$util.getDefaultImage().default_headimg,mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.activeData.headimg=t.$util.getDefaultImage().default_headimg}}}),n("v-uni-text",{staticClass:"rolling-order-text"},[t._v(t._s(t.activeData.message))])],1):t._e()},a=[]},bf93:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),o=i(n("2fdc"));n("64aa"),n("c223");var r={name:"diy-floating-rolling-order",props:{top:{type:[String],default:function(){return"280rpx"}},left:{type:[String],default:function(){return"24rpx"}},zIndex:{type:Number,default:function(){return 888}},intervalsTime:{type:Number,default:function(){return 3e3}},positionType:{type:String,default:function(){return"index_banner"}},sleepStart:{type:Number,default:function(){return 4e3}}},data:function(){return{ordersList:[],activeIndex:0,activeData:{},animationData:{},animation:null,duration:1e3,timeOutOne:null,timeOutTwo:null}},computed:{style:function(){return"top: ".concat(this.top,";left: ").concat(this.left,";z-index:").concat(this.zIndex,";")}},created:function(){},mounted:function(){var t=this;this.sleepStart?setTimeout((function(){t.switchMessage()}),this.sleepStart):this.switchMessage()},beforeDestroy:function(){clearTimeout(this.timeOutOne),clearTimeout(this.timeOutTwo)},methods:{contentAnimation:function(){var t=this;this.activeData=this.ordersList[this.activeIndex],this.animation=uni.createAnimation({duration:this.duration,timingFunction:"linear"}),this.animation.opacity(1).step(),this.animationData=this.animation.export(),this.timeOutOne=setTimeout((0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.animation.opacity(0).step(),t.animationData=t.animation.export(),t.activeIndex+=1,!(t.activeIndex>=t.ordersList.length)){e.next=6;break}return e.next=6,t.getData();case 6:t.timeOutTwo=setTimeout((function(){t.contentAnimation()}),t.duration+t.intervalsTime);case 7:case"end":return e.stop()}}),e)}))),this.duration+this.intervalsTime)},switchMessage:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getData();case 2:if(!(t.ordersList.length<1)){e.next=4;break}return e.abrupt("return");case 4:t.contentAnimation();case 5:case"end":return e.stop()}}),e)})))()},getData:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.activeIndex=0,e.prev=1,e.next=4,t.$api.sendRequest({url:"sign_detail"==t.positionType?t.$apiUrl.getSignDriftMessageUrl:t.$apiUrl.driftMessageUrl,async:!1,data:{position:t.positionType}});case 4:n=e.sent,0==n.code&&(t.ordersList=n.data),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,null,[[1,8]])})))()}}};e.default=r},c881:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7ca63514]{width:100%;text-align:center}.coupon-model[data-v-7ca63514]{display:flex;flex-direction:column;position:relative;z-index:111;width:%?620?%;background-size:cover;background-position:50%}.coupon-model .coupon-header[data-v-7ca63514]{background-size:cover;background-position:50%;margin-bottom:%?117?%}.coupon-model .title[data-v-7ca63514]{font-size:%?40?%;line-height:%?52?%;background-image:-webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:%?124?% 0 %?14?%;text-align:center;font-weight:700}.coupon-model .tip[data-v-7ca63514]{font-size:%?30?%;line-height:%?32?%;background-image:-webkit-linear-gradient(0deg,#fc5a50,#ff561a 46.75293%,#ff2637);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-align:center}.coupon-model .coupon-box[data-v-7ca63514]{flex:1;padding:0 %?54?% 0;background-size:100% 100%;background-position:50%;position:relative;margin-bottom:28px;overflow-y:auto}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]{display:flex;background-size:cover;background-position:50%;height:%?120?%;margin-bottom:%?20?%;position:relative;z-index:11}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]:last-child{margin-bottom:0}.coupon-model .coupon-box .coupon-list .left[data-v-7ca63514]{width:70px;display:flex;align-items:center;justify-content:center}.coupon-model .coupon-box .coupon-list .left .info[data-v-7ca63514]{display:flex;align-items:baseline}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:first-child{display:inline-block;font-size:%?26?%;color:#eb0000}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:last-child{display:inline-block;font-size:%?48?%;color:#eb0000;line-height:%?80?%}.coupon-model .coupon-box .coupon-list .left .point-class[data-v-7ca63514]{font-size:%?35?%}.coupon-model .coupon-box .coupon-list .left .money-thousand[data-v-7ca63514]{font-size:%?41?%!important}.coupon-model .coupon-box .coupon-list .left .money-thousand > span[data-v-7ca63514]{font-size:%?29?%}.coupon-model .coupon-box .coupon-list .right[data-v-7ca63514]{width:%?238?%;flex:1;display:flex;align-items:center;position:relative;margin-left:%?18?%}.coupon-model .coupon-box .coupon-list .right > uni-view[data-v-7ca63514]:first-child{flex:1;overflow:hidden;height:100%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .name[data-v-7ca63514]{font-size:%?24?%;line-height:%?36?%;padding:%?24?% 0 %?8?%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .time[data-v-7ca63514]{font-size:%?18?%;line-height:%?30?%;color:#999;white-space:nowrap}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .h5-time[data-v-7ca63514]{display:flex;width:119px;margin-left:-13px;-webkit-transform:scale(.78);transform:scale(.78)}.coupon-model .coupon-box .coupon-list .right .btn[data-v-7ca63514]{display:flex;justify-content:center;align-items:center;width:%?94?%;height:%?38?%;background:linear-gradient(90deg,#ffab37,#fff594);border-radius:19px;font-size:%?24?%;color:#822d02;margin:0 %?10?% 0 0}.coupon-model .coupon-box .coupon-list .right .h5-btn[data-v-7ca63514]{-webkit-transform:scale(.8);transform:scale(.8)}.coupon-model .coupon_bg[data-v-7ca63514]{position:absolute;top:%?270?%;width:100%;height:%?153?%;z-index:-1}.coupon-model .coupon_footer[data-v-7ca63514]{width:100%;height:%?51?%}.box1[data-v-7ca63514]{height:%?565?%}.box2[data-v-7ca63514]{height:%?660?%}.box3[data-v-7ca63514]{height:%?800?%}.box4[data-v-7ca63514]{height:%?850?%}.pop-ad-info-close[data-v-7ca63514]{width:%?88?%;height:%?88?%;display:block;margin:%?60?% auto 0}',""]),t.exports=e},d046:function(t,e,n){"use strict";n.r(e);var i=n("2033"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},de74:function(t,e,n){"use strict";n.r(e);var i=n("37ff"),a=n("fefc");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("327f");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"65e5b6d2",null,!1,i["a"],void 0);e["default"]=s.exports},ecb1:function(t,e,n){"use strict";n.r(e);var i=n("8919"),a=n("d046");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("856a"),n("1ed19");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"7c2c1f1c",null,!1,i["a"],void 0);e["default"]=s.exports},f9a5:function(t,e,n){"use strict";n.r(e);var i=n("bf01"),a=n("4d82");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("b373");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"38dab624",null,!1,i["a"],void 0);e["default"]=s.exports},fefc:function(t,e,n){"use strict";n.r(e);var i=n("b8ea"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a}}]);