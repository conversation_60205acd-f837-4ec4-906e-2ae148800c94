(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-address-address"],{"1ebc":function(e,t,r){var s=r("c86c");t=s(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-318c7edc]{width:100%;text-align:center}.mescroll-empty[data-v-318c7edc] .uni-scroll-view-content{background-color:#fff}.address-list[data-v-318c7edc]{width:100%;height:100%}.address-list .address-item[data-v-318c7edc]{width:%?710?%;margin:0 auto;display:flex;background-color:#fff;padding:%?30?% 0 %?30?% %?30?%;justify-content:space-between;box-sizing:border-box;margin-top:%?20?%}.address-list .address-item.back[data-v-318c7edc]{padding-left:0}.address-list .address-item.back .address-item-top[data-v-318c7edc]{width:%?545?%}.address-list .address-item.back .address-item-top .address-top-info .address-name[data-v-318c7edc]{max-width:%?260?%}.address-list .address-item .address-default[data-v-318c7edc]{width:%?80?%;display:flex;align-items:center;justify-content:center}.address-list .address-item .address-default .iconfont[data-v-318c7edc]{font-size:%?38?%}.address-list .address-item .address-default .iconfont.high-color[data-v-318c7edc]{color:var(--custom-brand-color)!important}.address-list .address-item .address-default-two[data-v-318c7edc]{align-items:flex-start;box-sizing:border-box}.address-list .address-item .address-default-icon[data-v-318c7edc]{font-size:%?28?%;line-height:%?44?%}.address-list .address-item .address-item-top[data-v-318c7edc]{width:%?600?%;display:flex;flex-direction:column}.address-list .address-item .address-item-top .address-top-info[data-v-318c7edc]{display:flex;font-weight:700;align-items:center}.address-list .address-item .address-item-top .address-top-info .address-name[data-v-318c7edc]{font-size:%?32?%;font-weight:700;line-height:%?40?%;color:#383838;margin-right:%?20?%;max-width:%?300?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}.address-list .address-item .address-item-top .address-top-info .address-tel[data-v-318c7edc]{font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#383838;max-width:%?215?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}.address-list .address-item .address-item-top .address-top-info .tag[data-v-318c7edc]{margin-left:%?20?%;width:%?65.86?%;height:%?32?%;border-radius:%?4?%;background:#fff;border:%?2?% solid var(--custom-brand-color);font-size:%?24?%;font-weight:400;line-height:%?32?%;color:var(--custom-brand-color);display:flex;justify-content:center;align-items:center}.address-list .address-item .address-item-top .address-info[data-v-318c7edc]{font-size:%?26?%;font-weight:400;line-height:%?32?%;color:grey;margin-top:%?10?%}.address-list .address-item .address-item-bottom[data-v-318c7edc]{display:flex;justify-content:space-between}.address-list .address-item .address-item-bottom .address-btn[data-v-318c7edc]{font-size:%?22?%;color:#a9a9a9;display:flex;align-items:center;padding-left:%?36?%;padding-right:%?36?%}.address-list .address-item .address-item-bottom .address-btn .delete[data-v-318c7edc]{padding-left:%?47?%}.address-list .address-item .address-item-bottom .address-btn .edit uni-image[data-v-318c7edc]{width:%?32?%;height:%?32?%}.button-wrap[data-v-318c7edc]{position:fixed;bottom:0;left:0;width:100%;height:%?100?%;border-top:%?1?% solid #eee;background-color:#fff;padding-bottom:env(safe-area-inset-bottom);display:flex;justify-content:space-around;align-items:center;z-index:10}.button-wrap .wei-address[data-v-318c7edc]{display:flex;align-items:center;justify-content:center;font-size:%?28?%;width:%?316?%;height:%?80?%;line-height:%?80?%;background:#fff!important;border:%?1?% solid #9a9a9a;border-radius:%?40?%;box-sizing:border-box;color:#343434}.button-wrap .wei-address uni-image[data-v-318c7edc]{width:%?48?%;height:%?48?%;margin-right:%?9?%}.button-wrap .add-address[data-v-318c7edc]{width:%?316?%;height:%?80?%;line-height:%?80?%;margin:0;padding:0;background:var(--custom-brand-color)!important;border-radius:%?40?%;display:flex;justify-content:center;align-items:center}.button-wrap .add-address-icon[data-v-318c7edc]{width:%?26?%;height:%?26?%;line-height:%?26?%;background:#fff;border-radius:50%;font-size:%?26?%;font-weight:700;color:var(--custom-brand-color);display:flex;justify-content:center;align-items:center;margin-right:%?15?%}.my-address-default[data-v-318c7edc]{overflow-y:hidden;height:calc(100vh - %?252?%);display:flex;justify-content:center;align-items:center;flex-direction:column}.my-address-default uni-image[data-v-318c7edc]{width:%?240?%;height:%?240?%}.my-address-default .text[data-v-318c7edc]{font-size:%?32?%;font-weight:400;line-height:%?37.26?%;color:#a6a6a6;margin-bottom:%?52?%;margin-top:%?64?%}.my-address-default .add-address.button[data-v-318c7edc]{width:%?400?%;height:%?80?%;border-radius:%?40?%;background:var(--custom-brand-color);margin:0;padding:0;font-size:%?32?%;font-weight:400;line-height:%?32?%;color:#fff;display:flex;justify-content:center;align-items:center}',""]),e.exports=t},"2d01":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"2d87":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return s}));var s={loadingCover:r("5510").default},a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{class:e.themeStyle,style:[e.themeColorVar]},[r("v-uni-view",[r("mescroll-uni",{ref:"mescroll",class:{"mescroll-empty":e.addressList.length<1},attrs:{top:e.navHeight},on:{getData:function(t){arguments[0]=t=e.$handleEvent(t),e.getListData.apply(void 0,arguments)}}},[r("template",{attrs:{slot:"list"},slot:"list"},[r("v-uni-view",{staticClass:"address-list"},[e._l(e.addressList,(function(t,s){return r("v-uni-view",{key:s,staticClass:"address-item back"},[e.isBack?r("v-uni-view",{staticClass:"address-default",on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.setShipping(t)}}},[r("v-uni-view",{staticClass:"iconfont",class:e.member_address&&t.id==e.member_address.id?"iconyuan_checked high-color":"iconyuan_checkbox"})],1):e._e(),r("v-uni-view",{staticClass:"address-default address-default-two"},[r("v-uni-text",{staticClass:"address-default-icon iconfont icondizhi"})],1),r("v-uni-view",{staticClass:"address-item-top"},[r("v-uni-view",{staticClass:"address-top-info"},[r("v-uni-view",{staticClass:"address-name"},[e._v(e._s(t.name))]),r("v-uni-view",{staticClass:"address-tel"},[e._v(e._s(t.mobile))]),1==t.is_default?r("v-uni-view",{staticClass:"tag"},[e._v("默认")]):e._e()],1),r("v-uni-view",{staticClass:"address-info"},[e._v(e._s(t.full_address)+e._s(t.address))])],1),r("v-uni-view",{staticClass:"address-item-bottom"},[r("v-uni-view",{staticClass:"address-btn"},[r("v-uni-view",{staticClass:"edit",on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.addAddress("edit",t.id)}}},[r("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/member/icon-edit-address.png"),mode:""}})],1)],1)],1)],1)})),0==e.addressList.length?r("v-uni-view",{staticClass:"my-address-default"},[r("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/member/address-default.png"),mode:""}}),r("v-uni-view",{staticClass:"text"},[e._v("暂无收货地址")]),r("v-uni-button",{staticClass:"add-address button",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addAddress("add")}}},[e._v(e._s(e.$lang("newAddAddress")))])],1):e._e()],2)],1)],2),e.addressList.length>0?r("v-uni-view",{staticClass:"button-wrap"},[e.$util.isWeiXin()?r("v-uni-view",{staticClass:"wei-address",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getChooseAddress.apply(void 0,arguments)}}},[r("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/member/icon-wechat.png"),mode:""}}),e._v(e._s(e.$lang("getAddress")))],1):e._e(),r("v-uni-button",{staticClass:"add-address",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addAddress("add")}}},[r("v-uni-text",{staticClass:"add-address-icon"},[e._v("+")]),e._v(e._s(e.$lang("newAddAddress")))],1)],1):e._e(),r("loading-cover",{ref:"loadingCover"})],1)],1)},i=[]},"318e":function(e,t,r){"use strict";var s=r("990e"),a=r.n(s);a.a},"362f":function(e,t,r){"use strict";r.r(t);var s=r("36cf"),a=r.n(s);for(var i in s)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return s[e]}))}(i);t["default"]=a.a},"36cf":function(e,t,r){"use strict";r("6a54");var s=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=s(r("2634")),i=s(r("2fdc"));r("c223"),r("dc69"),r("5ef2"),r("0c26");var d=s(r("8469")),n=s(r("2d01")),o=r("4b89"),c={mixins:[n.default],data:function(){return{addressList:[],back:"",redirect:"redirectTo",isIndex:!1,isBack:!1,member_address:"",navHeight:0}},onLoad:function(e){e.back&&(this.back=e.back,this.isBack=!0),this.member_address=wx.getStorageSync("member_address"),e.redirect&&(this.redirect=e.redirect),o.isOnXianMaiApp&&(this.navHeight=88)},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},onShow:function(){this.$langConfig.refresh(),this.$refs.mescroll&&this.$refs.mescroll.refresh()},methods:{getListData:function(e){var t=this;this.$api.sendRequest({url:"/api/memberaddress/page",data:{page:e.num,page_size:e.size},success:function(r){var s=[],a=r.message;0==r.code&&r.data?s=r.data.list:t.$util.showToast({title:a}),e.endSuccess(s.length),1==e.num&&(t.addressList=[]),t.addressList=t.addressList.concat(s),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.addressList.length||uni.removeStorageSync("member_address")},fail:function(r){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},addAddress:function(e,t){var r={};"edit"==e&&(r.id=t),this.back&&(r.back=this.back),this.$util.redirectTo("/otherpages/member/address_edit/address_edit",r)},setShipping:function(e){if(""!=this.back){for(var t=!0,r=getCurrentPages().reverse(),s=0;s<r.length;s++)if(-1!=this.back.indexOf(r[s].route)){t=!1,uni.navigateBack({delta:s});break}uni.setStorageSync("member_address",e),t&&this.$util.redirectTo(this.back,{},"redirectTo")}else this.$refs.mescroll.refresh(),this.$util.showToast({title:"修改默认地址成功"})},vertify:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.mobile=e.mobile.trim();var t=[{name:"mobile",checkType:"required",errorMsg:"请在微信地址输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请在微信地址输入正确的手机号"}],r=d.default.check(e,t);return!!r||(this.$util.showToast({title:d.default.error}),!1)},getChooseAddress:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$util.isWeiXin()&&e.wxJS.openAddress((function(t){"openAddress:ok"==t.errMsg?e.vertify({mobile:t.telNumber})&&e.saveAddress({name:t.userName,mobile:t.telNumber,province:t.provinceName,city:t.cityName,district:t.countryName,address:t.detailInfo,full_address:t.provinceName+"-"+t.cityName+"-"+t.countryName}):e.$util.showToast({title:t.errMsg})}));case 1:case"end":return t.stop()}}),t)})))()},saveAddress:function(e){var t=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:e,success:function(e){e.code>=0?t.$refs.mescroll.refresh():t.$util.showToast({title:e.message})}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),r=t.title,s=t.link,a=t.imageUrl;t.query;return this.$buriedPoint.pageShare(s,a,r)}};t.default=c},8469:function(e,t,r){r("23f4"),r("7d2f"),r("5c47"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){for(var r=0;r<t.length;r++){if(!t[r].checkType)return!0;if(!t[r].name)return!0;if(!t[r].errorMsg)return!0;if(!e[t[r].name])return this.error=t[r].errorMsg,!1;switch(t[r].checkType){case"custom":if("function"==typeof t[r].validate&&!t[r].validate(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"required":var s=new RegExp("/[S]+/");if(s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"string":s=new RegExp("^.{"+t[r].checkRule+"}$");if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"int":s=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[r].checkRule+"}$");if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[r].name]))return this.error=t[r].errorMsg,!1;var a=t[r].checkRule.split(",");if(a[0]=Number(a[0]),a[1]=Number(a[1]),e[t[r].name]>a[1]||e[t[r].name]<a[0])return this.error=t[r].errorMsg,!1;break;case"betweenD":s=/^-?[1-9][0-9]?$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;a=t[r].checkRule.split(",");if(a[0]=Number(a[0]),a[1]=Number(a[1]),e[t[r].name]>a[1]||e[t[r].name]<a[0])return this.error=t[r].errorMsg,!1;break;case"betweenF":s=/^-?[0-9][0-9]?.+[0-9]+$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;a=t[r].checkRule.split(",");if(a[0]=Number(a[0]),a[1]=Number(a[1]),e[t[r].name]>a[1]||e[t[r].name]<a[0])return this.error=t[r].errorMsg,!1;break;case"same":if(e[t[r].name]!=t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"notsame":if(e[t[r].name]==t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"email":s=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"phoneno":s=/^[1](([3][0-9])|([4][1,4-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"zipcode":s=/^[0-9]{6}$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"reg":s=new RegExp(t[r].checkRule);if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"in":if(-1==t[r].checkRule.indexOf(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"notnull":if(0==e[t[r].name]||void 0==e[t[r].name]||null==e[t[r].name]||e[t[r].name].length<1)return this.error=t[r].errorMsg,!1;break;case"lengthMin":if(e[t[r].name].length<t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"lengthMax":if(e[t[r].name].length>t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"bank_account":s=/^([1-9]{1})(\d{15}|\d{18})$/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"idCard":s=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!s.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"95ec":function(e,t,r){"use strict";r.r(t);var s=r("2d87"),a=r("362f");for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);r("318e");var d=r("828b"),n=Object(d["a"])(a["default"],s["b"],s["c"],!1,null,"318c7edc",null,!1,s["a"],void 0);t["default"]=n.exports},"990e":function(e,t,r){var s=r("1ebc");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[e.i,s,""]]),s.locals&&(e.exports=s.locals);var a=r("967d").default;a("62c083a4",s,!0,{sourceMap:!1,shadowMode:!1})}}]);