(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-pintuan-detail-detail","otherpages-live-list-liveList~promotionpages-pintuan-share-share"],{"014b":function(t,e,i){"use strict";i.r(e);var a=i("9d90"),o=i("c08b");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("91e0"),i("a9cf");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"847fc110",null,!1,a["a"],void 0);e["default"]=r.exports},"0895":function(t,e,i){var a=i("b332");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("506cb360",a,!0,{sourceMap:!1,shadowMode:!1})},1497:function(t,e,i){"use strict";i.r(e);var a=i("14fb"),o=i("64ea");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("aa42"),i("2982"),i("d40e");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"33b7d3ae",null,!1,a["a"],void 0);e["default"]=r.exports},"14fb":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={uniNavBar:i("d817").default,uniPopup:i("5e99").default,uniCountDown:i("5ab1").default,uniIcons:i("de74").default,sharePopup:i("82c2").default,countdownTimer:i("d3b8").default,diyGoodsDetailMoreGoodies:i("f99f").default,nsGoodsSku:i("4d57").default,nsLogin:i("4f5a").default,loadingCover:i("5510").default,nsGoodsAction:i("9fe9").default,nsGoodsActionIcon:i("8a64").default,nsGoodsActionButton:i("ca20").default,ydAuthPopup:i("161f").default,uniCouponPop:i("8765").default,diyFloatingRollingOrder:i("f9a5").default,diyShare:i("014b").default,diyShareNavigateH5:i("2f73").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:t.themeStyle},[t.isOnXianMaiApp?i("uni-nav-bar",{attrs:{"left-icon":"back",border:!1},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}},[[i("v-uni-view",{staticClass:"page-title"},[t._v(t._s(t.goodsSkuDetail.goods_name))])]],2):t._e(),i("v-uni-view",{staticClass:"goods-detail"},[i("v-uni-view",{staticClass:"goods-container"},[i("v-uni-view",{staticClass:"goods-media"},[i("v-uni-view",{staticClass:"goods-img",class:{show:"img"==t.switchMedia}},[i("v-uni-swiper",{staticClass:"swiper",attrs:{interval:t.swiperInterval,autoplay:t.swiperAutoplay,circular:!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.goodsSkuDetail.sku_images,(function(e,a){return i("v-uni-swiper-item",{key:a},[i("v-uni-view",{staticClass:"item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(a)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperImageError(a)}}})],1)],1)})),1),i("v-uni-view",{staticClass:"img-indicator-dots"},[i("v-uni-text",[t._v(t._s(t.swiperCurrent))]),t.goodsSkuDetail.sku_images?i("v-uni-text",[t._v("/"+t._s(t.goodsSkuDetail.sku_images.length))]):t._e()],1)],1),i("v-uni-view",{staticClass:"goods-video",class:{show:"video"==t.switchMedia}},[i("v-uni-view",{staticClass:"video-img"},[i("v-uni-image",{attrs:{src:t.$util.img(t.goodsSkuDetail.sku_image),mode:""}}),i("v-uni-view",{staticClass:"video-open"},[i("v-uni-view",{staticClass:"iconfont iconarrow-",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openVideo.apply(void 0,arguments)}}})],1)],1)],1),""!=t.goodsSkuDetail.video_url?i("v-uni-view",{staticClass:"media-mode"},[i("v-uni-text",{class:{"ns-bg-color":"video"==t.switchMedia},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchMedia="video"}}},[t._v(t._s(t.$lang("video")))]),i("v-uni-text",{class:{"ns-bg-color":"img"==t.switchMedia},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchMedia="img"}}},[t._v(t._s(t.$lang("image")))])],1):t._e()],1),i("v-uni-view",{staticClass:"videoPopup-box",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"videoPopup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"pop-video"},[i("v-uni-video",{attrs:{src:t.$util.img(t.goodsSkuDetail.video_url),poster:t.$util.img(t.goodsSkuDetail.sku_image),objectFit:"contain"}})],1)],1)],1),0==t.preview&&1==t.goodsSkuDetail.promotion_type&&t.goodsSkuDetail.discountTimeMachine&&t.addonIsExit.discount?i("v-uni-view",{staticClass:"goods-discount"},[i("v-uni-view",{staticClass:"price-info"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"symbol"},[t._v(t._s(t.$lang("common.currencySymbol")))]),i("v-uni-text",[t._v(t._s(t.goodsSkuDetail.discount_price))])],1),i("v-uni-view",{staticClass:"original-price"},[i("v-uni-text",{staticClass:"price"},[t._v(t._s(t.$lang("common.currencySymbol"))+" "+t._s(t.goodsSkuDetail.price))]),i("v-uni-text",{staticClass:"sale-num hide-sales"},[t._v(t._s(t.goodsSkuDetail.sale_num)+t._s(t.goodsSkuDetail.unit)+"已售")])],1)],1),i("v-uni-view",{staticClass:"countdown"},[i("v-uni-view",{staticClass:"txt"},[t._v("距结束仅剩")]),i("v-uni-view",{staticClass:"clockrun"},[i("uni-count-down",{attrs:{day:t.goodsSkuDetail.discountTimeMachine.d,hour:t.goodsSkuDetail.discountTimeMachine.h,minute:t.goodsSkuDetail.discountTimeMachine.i,second:t.goodsSkuDetail.discountTimeMachine.s,color:"#fff",splitorColor:"#000","background-color":"#000"}})],1)],1)],1):t._e(),i("v-uni-view",{staticClass:"group-wrap",staticStyle:{"margin-top":"-20rpx"}},[i("v-uni-view",{staticClass:"pintuan-price"},[i("v-uni-view",{staticClass:"pintuan-price-left"},[i("v-uni-view",{staticClass:"pintuan-price-left-one"},[i("v-uni-view",{staticClass:"pintuan-price-left-one-tag"},[t._v("拼团价")]),i("v-uni-view",{staticClass:"pintuan-price-left-one-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(t.pintuan_info.pintuan_price))],1),i("v-uni-view",{staticClass:"pintuan-price-left-one-unprice"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(t.goodsSkuDetail.market_price))],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-price-right"},[i("v-uni-text",{staticClass:"pintuan-price-right-number"},[t._v(t._s(t.pintuan_info.pintuan_num)+"人团")]),i("v-uni-text",{staticClass:"pintuan-price-right-yet"},[t._v("已开"+t._s(t.pintuan_info.order_num)+"团")])],1)],1),i("v-uni-view",{staticClass:"goods-module-wrap"},[i("v-uni-view",[i("v-uni-view",{staticClass:"sku-name-box",staticStyle:{overflow:"none"}},["new"==t.pintuan_info.promotion_type?i("v-uni-text",{staticClass:"sku-name-tuan"},[t._v(t._s(t.pintuan_info.promotion_type_desc))]):t._e(),i("v-uni-text",{staticClass:"sku-name",staticStyle:{display:"inline","font-size":"30rpx","line-height":"44rpx",position:"relative"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.longpress.apply(void 0,arguments)},longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.longpress.apply(void 0,arguments)}}},[t._v(t._s(t.goodsSkuDetail.goods_name))]),t.copytextShow?i("v-uni-view",{staticClass:"showCopybox"},[i("v-uni-view",{staticClass:"copytext"},[i("v-uni-text",{staticClass:"fuzhi",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.goodsSkuDetail.goods_name,t.copyCallback)}}},[t._v("复制")]),i("v-uni-text",{staticClass:"quxiao",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copytextShow=!1}}},[t._v("取消")])],1)],1):t._e()],1),t.goodsSkuDetail.introduction?i("v-uni-text",{staticClass:"introduction ns-text-color"},[t._v(t._s(t.goodsSkuDetail.introduction))]):t._e()],1),i("v-uni-view",{staticClass:"sku-subsidy hide-sales"},[i("v-uni-text",{staticClass:"sku-subsidy-one"},[t._v("拼团补贴")]),i("v-uni-text",{staticClass:"sku-subsidy-two"},[t._v("￥"+t._s(t.pintuan_info.not_win_money))])],1),t.pintuanList.length?i("v-uni-button",{staticClass:"group-wrap-share",staticStyle:{"font-size":"24rpx"},attrs:{plain:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.morePintuan("invite_politely")}}},[i("v-uni-text",[t._v("分享")]),i("v-uni-text",{staticStyle:{"margin-top":"10rpx"}},[t._v("有礼")])],1):i("v-uni-button",{staticClass:"group-wrap-share",attrs:{plain:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup.apply(void 0,arguments)}}},[i("uni-icons",{staticClass:"group-wrap-share-icon",attrs:{type:"redo",color:"#fff"}}),i("v-uni-text",[t._v("分享")])],1)],1)],1),t.isShowCanvas?i("share-popup",{ref:"sharePopup",attrs:{canvasOptions:t.canvasOptions,sharePopupOptions:t.sharePopupOptions}}):t._e(),t.pintuanList.length>0?i("v-uni-view",{staticClass:"group-wrap"},[i("v-uni-view",{staticClass:"pintuan-list"},[i("v-uni-view",{staticClass:"pintuan-list-title"},[i("v-uni-view",{staticClass:"pintuan-list-title-left"},[i("v-uni-text",{staticClass:"pintuan-list-title-left-tip"},[t._v(t._s(t.pintuan_group_count)+"人正在拼团")])],1),i("v-uni-view",{staticClass:"pintuan-list-title-right"},[i("v-uni-text",{staticClass:"pintuan-list-title-right-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.morePintuan("view_all")}}},[t._v("查看更多"),i("v-uni-text",{staticClass:"iconfont icongengduo3",staticStyle:{color:"#ccc"}})],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-list-info"},[t._l(t.pintuanList,(function(e,a){return[a<3?i("v-uni-view",{key:e.group_id+"_0",staticClass:"pintuan-list-info-one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toPintuanDetail(e)}}},[i("v-uni-view",{staticClass:"pintuan-list-info-one-left"},[i("v-uni-view",{staticClass:"pintuan-list-info-one-left-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.head_member_img)},on:{error:function(e){var i=this;arguments[0]=e=t.$handleEvent(e),function(){i.pintuanList[a].head_member_img=i.$util.getDefaultImage().default_goods_img}.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"pintuan-list-info-one-left-img-desc"},[i("v-uni-text",{staticClass:"pintuan-list-info-one-left-img-desc-text"},[t._v("团长")])],1)],1),i("v-uni-text",{staticClass:"pintuan-list-info-one-left-name"},[t._v(t._s(e.head_nickname))])],1),i("v-uni-view",{staticClass:"pintuan-list-info-one-right"},[i("v-uni-view",{staticClass:"pintuan-list-info-one-right-one"},[i("v-uni-view",{staticClass:"pintuan-list-info-one-right-one-count"},[t._v("还差"),i("v-uni-text",[t._v(t._s(e.diff_num))]),t._v("人拼成")],1),i("v-uni-view",{staticClass:"pintuan-list-info-one-right-one-time"},[t._v("剩余"),i("countdown-timer",{ref:"countdown",refInFor:!0,attrs:{time:1e3*e.distance_time,autoStart:!0},on:{finish:function(i){arguments[0]=i=t.$handleEvent(i),t.onFinish(e.group_id)}}})],1)],1),i("v-uni-button",{staticClass:"pintuan-list-info-one-right-two"},[t._v("去参团")])],1)],1):t._e()]}))],2)],1)],1):t._e(),i("v-uni-view",{staticClass:"group-wrap group-wrap-padding"},[t.goodsSkuDetail.sku_spec_format?i("v-uni-view",{staticClass:"goods-cell selected-sku-spec",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseSkuspecFormat(t.goodsSkuDetail.maidou_tag)}}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"tit"},[t._v("已选择")]),t._l(t.goodsSkuDetail.sku_spec_format,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.spec_name)+"/"+t._s(e.spec_value_name))])}))],2),i("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),t.goodsSkuDetail.goods_attr_format&&t.goodsSkuDetail.goods_attr_format.length>0?i("v-uni-view",{staticClass:"goods-cell",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openAttributePopup()}}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"tit"},[t._v("规格参数")])],1),i("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"attributePopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"goods-attribute-popup-layer"},[i("v-uni-text",{staticClass:"title"},[t._v("规格参数")]),i("v-uni-scroll-view",{staticClass:"goods-attribute-body",attrs:{"scroll-y":!0}},t._l(t.goodsSkuDetail.goods_attr_format,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item ns-border-color-gray"},[i("v-uni-text",{staticClass:"ns-text-color-gray"},[t._v(t._s(e.attr_name))]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(e.attr_value_name))])],1)})),1),i("v-uni-view",{staticClass:"button-box"},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeAttributePopup()}}},[t._v("确定")])],1)],1)],1)],1)],1),i("v-uni-view",{staticClass:"group-wrap"},[i("v-uni-view",{staticClass:"pintuan-method"},[i("v-uni-view",{staticClass:"pintuan-method-title"},[i("v-uni-view",{staticClass:"pintuan-method-title-left"},[i("v-uni-text",{staticClass:"pintuan-method-title-left-tip"},[t._v("拼团奖励")]),i("v-uni-text",{staticClass:"pintuan-method-title-left-desc"},[t._v("返现")])],1),i("v-uni-view",{staticClass:"pintuan-method-title-right"},[i("v-uni-view",{staticClass:"pintuan-method-title-right-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPintuanRule.apply(void 0,arguments)}}},[t._v("了解规则"),i("uni-icons",{attrs:{type:"help-filled",color:"#ccc"}})],1)],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-space"},[i("v-uni-view",{staticClass:"pintuan-flow"},[i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-one.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info"},[i("v-uni-view",[t._v("参与拼团")]),i("v-uni-view",[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.pintuan_num))]),t._v("人成团")],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-two.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info"},[i("v-uni-view",[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.winning_num))]),t._v("人拼中发货")],1),i("v-uni-view",{class:{"hide-sales":t.pintuan_info.pintuan_num-t.pintuan_info.winning_num<1}},[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.pintuan_num-t.pintuan_info.winning_num))]),t._v("人未中退款")],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-three.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info pintuan-flow-one-info-other"},t._l(t.pintuan_info.play_list,(function(e,a){return i("v-uni-view",{key:a},[t._v(t._s(e.reward_str)),i("v-uni-text",{staticClass:"pintuan-flow-one-info-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.money))],1)],1)})),1)],1)],1)],1)],1),(!t.is_audit_mode&&t.buyerList.length,t._e()),0==t.preview&&t.couponList.length?i("v-uni-view",{staticClass:"group-wrap"},[t.manjianList&&t.manjianList.rule_json&&t.addonIsExit.manjian?i("v-uni-view",{staticClass:"goods-cell",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openManjianPopup()}}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"tit"},[t._v("满减")]),i("v-uni-text",[t._v(t._s(t.manjianList.manjian_name)+"...")])],1),i("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),t.addonIsExit.manjian?i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"manjianPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"manjian-popup-layer"},[i("v-uni-text",{staticClass:"title"},[t._v("满减")]),i("v-uni-scroll-view",{staticClass:"manjian-body",attrs:{"scroll-y":!0}},t._l(t.manjianList.rule_json,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item ns-border-color-gray"},[i("v-uni-text",{staticClass:"manjian-icon ns-bg-color"},[t._v("满减")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s("满"+e.money+"减"+e.discount_money))])],1)})),1),i("v-uni-view",{staticClass:"button-box"},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeManjianPopup()}}},[t._v("确定")])],1)],1)],1)],1):t._e(),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.shopInfo.shop_baozh||1==t.shopInfo.shop_qtian||1==t.shopInfo.shop_zhping||1==t.shopInfo.shop_erxiaoshi||1==t.shopInfo.shop_tuihuo||1==t.shopInfo.shop_shiyong||1==t.shopInfo.shop_shiti||1==t.shopInfo.shop_xiaoxie,expression:"\n\t\t\t\t\t\t\tshopInfo.shop_baozh == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_qtian == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_zhping == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_erxiaoshi == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_tuihuo == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_shiyong == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_shiti == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_xiaoxie == 1\n\t\t\t\t\t\t"}],staticClass:"goods-cell service",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openMerchantsServicePopup()}}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("service")))]),1==t.shopInfo.shop_baozh?i("v-uni-text",[t._v("保证服务")]):t._e(),1==t.shopInfo.shop_qtian?i("v-uni-text",[t._v("7天退换")]):t._e(),1==t.shopInfo.shop_zhping?i("v-uni-text",[t._v("正品保障")]):t._e(),1==t.shopInfo.shop_erxiaoshi?i("v-uni-text",[t._v("两小时发货")]):t._e(),1==t.shopInfo.shop_tuihuo?i("v-uni-text",[t._v("退货承诺")]):t._e(),1==t.shopInfo.shop_shiyong?i("v-uni-text",[t._v("试用中心")]):t._e(),1==t.shopInfo.shop_shiti?i("v-uni-text",[t._v("实体验证")]):t._e(),1==t.shopInfo.shop_xiaoxie?i("v-uni-text",[t._v("消协保证")]):t._e()],1),i("v-uni-text",{staticClass:"iconfont iconright"})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.shopInfo.shop_baozh||1==t.shopInfo.shop_qtian||1==t.shopInfo.shop_zhping||1==t.shopInfo.shop_erxiaoshi||1==t.shopInfo.shop_tuihuo||1==t.shopInfo.shop_shiyong||1==t.shopInfo.shop_shiti||1==t.shopInfo.shop_xiaoxie,expression:"\n\t\t\t\t\t\t\tshopInfo.shop_baozh == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_qtian == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_zhping == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_erxiaoshi == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_tuihuo == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_shiyong == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_shiti == 1 ||\n\t\t\t\t\t\t\t\tshopInfo.shop_xiaoxie == 1\n\t\t\t\t\t\t"}],on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"merchantsServicePopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"goods-merchants-service-popup-layer"},[i("v-uni-text",{staticClass:"tax-title ns-text-color-black"},[t._v(t._s(t.$lang("service")))]),i("v-uni-scroll-view",{attrs:{"scroll-y":!0}},[1==t.shopInfo.shop_baozh?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("保证服务")]),i("v-uni-text",{staticClass:"describe"},[t._v("保证服务")])],1)],1):t._e(),1==t.shopInfo.shop_qtian?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("7天退换")]),i("v-uni-text",{staticClass:"describe"},[t._v("满足7天无理由退换货申请的前提下，包邮商品需要买家承担退货邮费，非包邮商品需要买家承担发货和退货邮费")])],1)],1):t._e(),1==t.shopInfo.shop_zhping?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("正品保障")]),i("v-uni-text",{staticClass:"describe"},[t._v("商品支持正品保障服务")])],1)],1):t._e(),1==t.shopInfo.shop_erxiaoshi?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("两小时发货")]),i("v-uni-text",{staticClass:"describe"},[t._v("付款后2小时内发货")])],1)],1):t._e(),1==t.shopInfo.shop_tuihuo?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("退货承诺")]),i("v-uni-text",{staticClass:"describe"},[t._v("退货承诺")])],1)],1):t._e(),1==t.shopInfo.shop_shiyong?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("试用中心")]),i("v-uni-text",{staticClass:"describe"},[t._v("试用中心")])],1)],1):t._e(),1==t.shopInfo.shop_shiti?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("实体验证")]),i("v-uni-text",{staticClass:"describe"},[t._v("实体验证")])],1)],1):t._e(),1==t.shopInfo.shop_xiaoxie?i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"iconfont icondui ns-text-color"}),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-text",{staticClass:"title"},[t._v("消协保证")]),i("v-uni-text",{staticClass:"describe"},[t._v("如有商品质量问题、描述不符或未收到货等，您有权申请退款或退货，来回邮费由卖家承担")])],1)],1):t._e()],1),i("v-uni-view",{staticClass:"button-box"},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeMerchantsServicePopup()}}},[t._v("确定")])],1)],1)],1)],1)],1):t._e(),0==t.preview&&t.bundling.length&&t.bundling[0].bl_name&&t.addonIsExit.bundling?i("v-uni-view",[i("v-uni-view",{staticClass:"group-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openBundlingPopup()}}},[i("v-uni-view",{staticClass:"goods-cell",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openBundlingPopup()}}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"tit"},[t._v("组合套餐")]),i("v-uni-text",[t._v(t._s(t.bundling[0].bl_name))])],1),i("v-uni-text",{staticClass:"iconfont iconright"})],1),i("v-uni-view",{staticClass:"combo-goods-wrap ns-text-color-gray"},[i("v-uni-navigator",{staticClass:"goods ns-border-color-gray",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+t.skuId}},[i("v-uni-image",{attrs:{src:t.$util.img(t.goodsSkuDetail.sku_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}}),i("v-uni-text",[t._v("¥"+t._s(t.goodsSkuDetail.price))])],1),i("v-uni-view",{staticClass:"iconfont iconadd1 ns-text-color-gray"}),t._l(t.bundling[0].bundling_goods,(function(e,a){return[a<3?[i("v-uni-navigator",{staticClass:"goods ns-border-color-gray",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.bundlingImageError(0,a)}}}),i("v-uni-text",[t._v("¥"+t._s(e.price))])],1)]:t._e()]}))],2)],1),i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"bundlingPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"bundling-popup-layer"},[i("v-uni-text",{staticClass:"title"},[t._v("组合套餐")]),i("v-uni-scroll-view",{staticClass:"bundling-body",attrs:{"scroll-y":!0}},[t._l(t.bundling,(function(e,a){return[i("v-uni-scroll-view",{key:a+"_0",attrs:{"scroll-x":!0}},[i("v-uni-view",{staticClass:"item ns-border-color-gray"},[i("v-uni-navigator",{staticClass:"value",attrs:{"hover-class":"none",url:"/promotionpages/combo/detail/detail?bl_id="+e.bl_id}},[i("v-uni-text",[t._v(t._s(e.bl_name)+"：￥"+t._s(e.bl_price))]),i("v-uni-view",{staticClass:"right"},[i("v-uni-text",{staticClass:"ns-text-color"},[t._v("查看")]),i("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),i("v-uni-view",{staticClass:"goods-wrap"},[i("v-uni-navigator",{staticClass:"goods",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+t.skuId}},[i("v-uni-image",{attrs:{src:t.$util.img(t.goodsSkuDetail.sku_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}}),i("v-uni-text",[t._v("¥"+t._s(t.goodsSkuDetail.price))])],1),i("v-uni-view",{staticClass:"iconfont iconadd1 ns-text-color-gray"}),t._l(e.bundling_goods,(function(e,o){return[o<3?[i("v-uni-navigator",{staticClass:"goods",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+e.sku_id}},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.bundlingImageError(a,o)}}}),i("v-uni-text",[t._v("¥"+t._s(e.price))])],1)]:t._e()]}))],2)],1)],1)]}))],2),i("v-uni-view",{staticClass:"button-box"},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeBundlingPopup()}}},[t._v("确定")])],1)],1)],1)],1)],1):t._e(),t.Development?[0==t.preview?i("v-uni-view",{staticClass:"group-wrap"},[i("v-uni-view",{staticClass:"shop-wrap"},[i("v-uni-navigator",{staticClass:"box",attrs:{"hover-class":"none",url:"/otherpages/shop/index/index?site_id="+t.shopInfo.site_id}},[i("v-uni-view",{staticClass:"shop-logo"},[t.shopInfo.avatar?i("v-uni-image",{attrs:{src:t.$util.img(t.shopInfo.avatar),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.shopInfo.avatar=t.$util.getDefaultImage().default_shop_img}}}):i("v-uni-image",{attrs:{src:t.$util.getDefaultImage().default_shop_img,mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"shop-info"},[i("v-uni-text",[t._v(t._s(t.shopInfo.site_name))]),t.shopInfo.seo_description?i("v-uni-view",{staticClass:"description"},[t._v(t._s(t.shopInfo.seo_description))]):t._e()],1)],1),i("v-uni-navigator",{staticClass:"box shop-score",attrs:{"hover-class":"none",url:"/otherpages/shop/index/index?site_id="+t.shopInfo.site_id}},[i("v-uni-text",[t._v("商品描述"+t._s(t.shopInfo.shop_desccredit))]),i("v-uni-text",[t._v("卖家服务"+t._s(t.shopInfo.shop_servicecredit))]),i("v-uni-text",[t._v("发货速度"+t._s(t.shopInfo.shop_deliverycredit))])],1),i("v-uni-view",{staticClass:"box"},[i("v-uni-view",{staticClass:"goods-action"},[i("v-uni-navigator",{staticClass:"ns-text-color ns-border-color",attrs:{"hover-class":"none",url:"/otherpages/shop/list/list?site_id="+t.shopInfo.site_id}},[t._v("全部商品")]),i("v-uni-navigator",{staticClass:"ns-text-color ns-border-color",attrs:{"hover-class":"none",url:"/otherpages/shop/index/index?site_id="+t.shopInfo.site_id}},[t._v("查看店铺")])],1)],1)],1)],1):t._e()]:t._e(),0==t.preview&&t.isShowEvaluate?i("v-uni-view",{staticClass:"group-wrap"},[i("v-uni-view",{staticClass:"goods-evaluate"},[i("v-uni-view",{staticClass:"tit"},[i("v-uni-view",[i("v-uni-text",[t._v("商品评价（"+t._s(t.goodsSkuDetail.evaluate)+"）")])],1),i("v-uni-navigator",{staticClass:"ns-text-color",attrs:{"hover-class":"none",url:"/otherpages/goods/evaluate/evaluate?goods_id="+t.goodsSkuDetail.goods_id}},[i("v-uni-text",[t._v("查看更多")]),i("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),t.goodsEvaluate.content?i("v-uni-view",{staticClass:"evaluate-item"},[i("v-uni-view",{staticClass:"evaluator"},[i("v-uni-view",{staticClass:"evaluator-face"},[t.goodsEvaluate.member_headimg?i("v-uni-image",{attrs:{src:t.$util.img(t.goodsEvaluate.member_headimg),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsEvaluate.member_headimg=t.$util.getDefaultImage().default_headimg}}}):i("v-uni-image",{attrs:{src:t.$util.getDefaultImage().default_headimgsss,mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsEvaluate.member_headimg=t.$util.getDefaultImage().default_headimg}}})],1),i("v-uni-text",{staticClass:"evaluator-name"},[t._v(t._s(t.goodsEvaluate.member_name))])],1),i("v-uni-view",{staticClass:"cont"},[t._v(t._s(t.goodsEvaluate.content))]),t.goodsEvaluate.images?i("v-uni-view",{staticClass:"evaluate-img"},t._l(t.goodsEvaluate.images,(function(e,a){return i("v-uni-view",{key:a,staticClass:"img-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewEvaluate(a,"images")}}},[i("v-uni-image",{attrs:{src:t.$util.img(e),mode:"aspectFit"}})],1)})),1):t._e(),i("v-uni-view",{staticClass:"time"},[i("v-uni-text",[t._v(t._s(t.$util.timeStampTurnTime(t.goodsEvaluate.create_time)))]),i("v-uni-text",[t._v(t._s(t.goodsEvaluate.sku_name))])],1)],1):i("v-uni-view",{staticClass:"evaluate-item-empty"},[t._v("该商品暂无评价哦")])],1)],1):t._e(),i("v-uni-view",{staticClass:"goods-detail-tab"},[t.isShowDetailTab?i("v-uni-view",{staticClass:"detail-tab flex-center",style:"top:"+t.statusBarHeight+"px;"},[i("v-uni-view",{staticClass:"tab-item",class:"productDetail"==t.detailTab?"active ns-bg-before":"",attrs:{"data-id":"productDetail"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPoint.apply(void 0,arguments)}}},[t._v("商品详情")]),t.afterSale?i("v-uni-view",{staticClass:"tab-item",class:"productSale"==t.detailTab?"active ns-bg-before":"",attrs:{"data-id":"productSale"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPoint.apply(void 0,arguments)}}},[t._v("售后保障")]):t._e(),t.service?i("v-uni-view",{staticClass:"tab-item",class:"productServe"==t.detailTab?"active ns-bg-before":"",attrs:{"data-id":"productServe"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toPoint.apply(void 0,arguments)}}},[t._v("服务说明")]):t._e()],1):t._e(),i("v-uni-view",{staticClass:"detail-content"},[i("v-uni-view",{staticClass:"detail-content-item",attrs:{id:"productDetail"}},[i("v-uni-view",{staticClass:"goods-details-title"},[i("v-uni-view"),i("v-uni-view",[t._v("图文详情")]),i("v-uni-view")],1),t.goodsSkuDetail.goods_content?i("v-uni-view",{staticClass:"goods-details"},[i("mphtml",{attrs:{content:t.goodsSkuDetail.goods_content,"preview-img":!0}})],1):i("v-uni-view",{staticClass:"goods-details active"},[t._v("该商家暂无上传相关详情哦！")])],1),i("diy-goods-detail-more-goodies",{attrs:{sku_id:t.skuId}}),t.afterSale?i("v-uni-view",{staticClass:"detail-content-item",attrs:{id:"productSale"}},[i("v-uni-view",{staticClass:"goods-details-title"},[i("v-uni-view"),i("v-uni-view",[t._v("售后保障")]),i("v-uni-view")],1),i("v-uni-view",{staticClass:"goods-details"},[i("v-uni-rich-text",{attrs:{nodes:t.afterSale}})],1)],1):t._e(),t.service?i("v-uni-view",{staticClass:"detail-content-item",attrs:{id:"productServe"}},[i("v-uni-view",{staticClass:"goods-details-title"},[i("v-uni-view"),i("v-uni-view",[t._v("服务说明")]),i("v-uni-view")],1),i("v-uni-view",{staticClass:"goods-details"},[i("v-uni-rich-text",{attrs:{nodes:t.service}})],1)],1):t._e()],1)],1),i("ns-goods-sku",{ref:"goodsSku",attrs:{"goods-detail":t.goodsSkuDetail,"pintuan-info":t.pintuan_info,entrance:t.goodsSkuDetail.maidou_tag},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshGoodsSkuDetail.apply(void 0,arguments)}}}),i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"posterPopup",staticClass:"poster-layer",attrs:{type:"bottom"}},["-1"!=t.poster?[i("v-uni-view",{style:{height:t.posterHeight>0?t.posterHeight+80+"px":""}},[i("v-uni-view",{staticClass:"image-wrap"},[i("v-uni-image",{style:{height:t.posterHeight>0?t.posterHeight+"px":""},attrs:{src:t.$util.img(t.poster)}})],1),i("v-uni-view",{staticClass:"save"},[t._v("长按保存图片")])],1),i("v-uni-view",{staticClass:"close iconfont iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePosterPopup()}}})]:i("v-uni-view",{staticClass:"msg"},[t._v(t._s(t.posterMsg))])],2)],1),i("ns-login",{ref:"login"})],2)],1),i("loading-cover",{ref:"loadingCover"}),i("ns-goods-action",[1==t.goodsSkuDetail.goods_state&&1==t.goodsSkuDetail.verify_state?[i("ns-goods-action-icon",{attrs:{text:"首页",icon:"iconshouye",imgicon:t.$util.img("public/static/youpin/home-icon.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goHome.apply(void 0,arguments)}}}),t.addonIsExit.servicer?i("ns-goods-action-icon",{attrs:{text:"客服",icon:"iconkefu"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.getCustomerService()}}}):t._e(),i("ns-goods-action-icon",{attrs:{text:"购物车",icon:"icongouwuche",imgicon:t.$util.img("public/static/youpin/shop-icon.png"),"corner-mark":t.cartCount>0?t.cartCount+"":""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goCart.apply(void 0,arguments)}}}),t.goodsSkuDetail.stock?[i("ns-goods-action-button",{staticClass:"goods-action-button",class:(t.goodsSkuDetail.is_single_buy,"active1"),attrs:{text:"¥ "+t.goodsSkuDetail.retail_price,"text-price":"单独购买",background:"#FFF3F3",disabledText:"¥ "+t.goodsSkuDetail.retail_price,disabled:!t.goodsSkuDetail.stock},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow.apply(void 0,arguments)}}}),i("ns-goods-action-button",{staticClass:"goods-action-button",class:1==t.goodsSkuDetail.is_single_buy?"active2":"active4",attrs:{text:"¥ "+t.pintuan_info.pintuan_price,"text-price":"开团",disabledText:"¥ "+t.pintuan_info.pintuan_price,disabled:!t.pintuan_info.stock},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.pintuan.apply(void 0,arguments)}}})]:[t.pintuanList.length>0?i("ns-goods-action-button",{staticClass:"goods-action-button active3",attrs:{text:"开团已满，请参团"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.popupProceedPintuan.open()}}}):t._e(),0==t.pintuanList.length?i("ns-goods-action-button",{staticClass:"goods-action-button active4",attrs:{disabledText:"已售罄",disabled:!0}}):t._e()]]:[i("ns-goods-action-button",{staticClass:"goods-action-button active3",attrs:{"disabled-text":"该商品已下架",disabled:!0}})]],2),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"}),i("uni-coupon-pop",{ref:"couponPop"}),i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:t.showTop,expression:"showTop"}],staticClass:"to-top",attrs:{src:t.$util.img("public/static/youpin/to-top.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToTopNative.apply(void 0,arguments)}}}),i("uni-popup",{ref:"popupBan",attrs:{maskClick:!1}},[i("v-uni-view",{staticClass:"popup-dialog"},[i("v-uni-view",{staticClass:"popup-dialog-header"},[t._v("提示")]),i("v-uni-view",{staticClass:"popup-dialog-body"},[t._v("哎哟~系统好像出了点问题，暂时不能支付，请联系客服。")]),i("v-uni-view",{staticClass:"popup-dialog-footer"},[i("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.popupBan.close()}}},[t._v("知道了")])],1)],1)],1),i("uni-popup",{ref:"popupOpenPintuan",staticClass:"choose-pintuan-type-parent",attrs:{type:"bottom",custom:!0}},[i("v-uni-view",{staticClass:"choose-pintuan-type"},[i("v-uni-view",{staticClass:"choose-pintuan-type-title"},[t._v("团购类型：")]),i("v-uni-view",{staticClass:"choose-pintuan-type-list"},[i("v-uni-text",{staticClass:"active"},[t._v(t._s(t.pintuan_info.pintuan_num)+"人团")])],1),i("v-uni-view",{staticClass:"choose-pintuan-type-op"},[i("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePinTuanPopup.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1),i("uni-popup",{ref:"popupProceedPintuan",staticClass:"proceed-pintuan-parent",attrs:{type:"bottom",maskClick:!0}},[i("v-uni-view",{on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),function(){}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"proceed-pintuan-title"},[i("v-uni-text",{staticClass:"proceed-pintuan-title-left"},[t._v("正在拼团")]),i("v-uni-view",{staticClass:"proceed-pintuan-title-right"},[i("v-uni-text",{staticClass:"proceed-pintuan-title-right-number"},[t._v(t._s(t.pintuan_info.pintuan_num)+"人团")]),i("v-uni-text",{staticClass:"proceed-pintuan-title-right-yet"},[t._v("已开"+t._s(t.pintuan_info.order_num)+"团")])],1)],1),i("v-uni-scroll-view",{staticClass:"proceed-pintuan",attrs:{"scroll-y":"true","scroll-into-view":t.popUpPintuanInnerRule}},[i("v-uni-view",{staticClass:"proceed-pintuan-info"},[t._l(t.isShowPintuanMore?t.pintuanList.slice(0,3):t.pintuanList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"proceed-pintuan-info-one"},[i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left"},[i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.head_member_img)},on:{error:function(e){var i=this;arguments[0]=e=t.$handleEvent(e),function(){i.pintuanList[a].head_member_img=i.$util.getDefaultImage().default_goods_img}.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-img-desc"},[i("v-uni-text",[t._v("团长")])],1)],1),i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-info"},[i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-info-name"},[t._v(t._s(e.head_nickname))])],1),i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-name"},[i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-name-text"},[t._v("还差"),i("v-uni-text",[t._v(t._s(e.diff_num))]),t._v("人拼成")],1),i("v-uni-view",{staticClass:"proceed-pintuan-info-one-left-name-time"},[t._v("剩余"),i("countdown-timer",{ref:"proceedCountdown",refInFor:!0,attrs:{time:1e3*e.distance_time,autoStart:!0},on:{finish:function(i){arguments[0]=i=t.$handleEvent(i),t.onFinish(e.group_id)}}})],1)],1)],1),i("v-uni-view",{staticClass:"proceed-pintuan-info-one-right"},[i("v-uni-button",{staticClass:"proceed-pintuan-info-one-right-join",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toPintuanDetail(e)}}},[t._v("参团")]),i("v-uni-button",{staticClass:"proceed-pintuan-info-one-right-share",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toShareJoin(a)}}},[t._v("分享")])],1)],1)})),t.isShowPintuanMore?i("v-uni-view",{staticClass:"proceed-pintuan-info-loading",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPintuanMore.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("查看更多")]),i("uni-icons",{attrs:{type:"arrowdown",color:"rgba(166, 166, 166, 1)",size:"12"}})],1):t._e()],2),i("v-uni-view",{staticClass:"group-wrap"},[i("v-uni-view",{staticClass:"pintuan-method",staticStyle:{"background-color":"transparent"}},[i("v-uni-view",{staticClass:"pintuan-method-title"},[i("v-uni-view",{staticClass:"pintuan-method-title-left"},[i("v-uni-text",{staticClass:"pintuan-method-title-left-tip"},[t._v("拼团奖励")]),i("v-uni-text",{staticClass:"pintuan-method-title-left-desc"},[t._v("返现")])],1),i("v-uni-view",{staticClass:"pintuan-method-title-right"},[i("v-uni-view",{staticClass:"pintuan-method-title-right-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollIntoPintunRule.apply(void 0,arguments)}}},[t._v("了解规则"),i("uni-icons",{attrs:{type:"help-filled",color:"#ccc"}})],1)],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-space",staticStyle:{"background-color":"transparent"}},[i("v-uni-view",{staticClass:"pintuan-flow"},[i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-one.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info"},[i("v-uni-view",[t._v("参与拼团")]),i("v-uni-view",[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.pintuan_num))]),t._v("人成团")],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-two.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info"},[i("v-uni-view",[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.winning_num))]),t._v("人拼中发货")],1),i("v-uni-view",{class:{"hide-sales":t.pintuan_info.pintuan_num-t.pintuan_info.winning_num<1}},[i("v-uni-text",{staticClass:"pintuan-flow-one-info-red"},[t._v(t._s(t.pintuan_info.pintuan_num-t.pintuan_info.winning_num))]),t._v("人未中退款")],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-flow-one"},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-flow-three.png"),alt:""}}),i("v-uni-view",{staticClass:"pintuan-flow-one-info pintuan-flow-one-info-other"},t._l(t.pintuan_info.play_list,(function(e,a){return i("v-uni-view",{key:a},[t._v(t._s(e.reward_str)),i("v-uni-text",{staticClass:"pintuan-flow-one-info-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.money))],1)],1)})),1)],1)],1)],1)],1),i("v-uni-view",{staticClass:"pintuan-rule pintuan-rule-inner"},[i("v-uni-view",{staticClass:"pintuan-rule-info",attrs:{id:"popUpPintuanInnerRule"}},[i("v-uni-rich-text",{attrs:{nodes:t.UnchangeContent(t.pintuan_info.pintuan_rule)}})],1)],1)],1),i("v-uni-view",{staticClass:"proceed-pintuan-op"},[i("ns-goods-action",[1==t.goodsSkuDetail.goods_state&&1==t.goodsSkuDetail.verify_state?[t.goodsSkuDetail.stock?[i("ns-goods-action-button",{staticClass:"goods-action-button",class:(t.goodsSkuDetail.is_single_buy,"active1"),attrs:{background:"#FFF3F3",text:"¥ "+t.pintuan_info.pintuan_price,"text-price":"自己开团",disabledText:"¥ "+t.pintuan_info.pintuan_price,disabled:!t.pintuan_info.stock},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.pintuan.apply(void 0,arguments)}}}),i("ns-goods-action-button",{staticClass:"goods-action-button",class:1==t.goodsSkuDetail.is_single_buy?"active2":"active4",attrs:{text:t.pintuan_info.play_list&&t.pintuan_info.play_list.filter((function(t){return"invite_award"==t.reward_type})).length?"邀请成团奖励¥ "+t.pintuan_info.play_list.filter((function(t){return"invite_award"==t.reward_type}))[0].money:"","text-price":"分享邀请好友",disabledText:t.pintuan_info.play_list&&t.pintuan_info.play_list.filter((function(t){return"invite_award"==t.reward_type})).length?"邀请成团奖励¥ "+t.pintuan_info.play_list.filter((function(t){return"invite_award"==t.reward_type}))[0].money:"",disabled:!t.pintuanList.length},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toShareJoin(0)}}})]:[t.pintuanList.length>0?i("ns-goods-action-button",{staticClass:"goods-action-button active3",attrs:{text:"开团已满，请参团"}}):t._e(),0==t.pintuanList.length?i("ns-goods-action-button",{staticClass:"goods-action-button active4",attrs:{disabledText:"已售罄",disabled:!0}}):t._e()]]:[i("ns-goods-action-button",{staticClass:"goods-action-button active3",attrs:{"disabled-text":"该商品已下架",disabled:!0}})]],2)],1)],1)],1),i("v-uni-swiper",{directives:[{name:"show",rawName:"v-show",value:t.isShowDetailTab,expression:"isShowDetailTab"}],staticClass:"grouping-swiper",attrs:{circular:!0,autoplay:!0,vertical:!0}},t._l(t.pintuanList,(function(e,a){return i("v-uni-swiper-item",{key:a},[i("v-uni-view",{staticClass:"grouping-in-progress"},[i("v-uni-view",{staticClass:"grouping-in-progress-left"},[i("v-uni-image",{staticClass:"grouping-in-progress-left-head",attrs:{src:e.head_member_img?t.$util.img(e.head_member_img):t.$util.getDefaultImage().default_headimg,mode:"aspectFill"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),e.head_member_img=t.$util.getDefaultImage().default_headimg}}}),i("v-uni-view",{staticClass:"grouping-in-progress-left-name"},[i("v-uni-text",[t._v(t._s(e.head_nickname))]),i("v-uni-text",[t._v("正在拼该商品")])],1)],1),i("v-uni-view",{staticClass:"grouping-in-progress-right"},[i("v-uni-button",{staticClass:"grouping-in-progress-right-btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toPintuanDetail(e)}}},[t._v("去参团")])],1)],1)],1)})),1),i("uni-popup",{ref:"popupPintuanRule",staticClass:"pintuan-rule-parent",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"pintuan-rule"},[i("v-uni-view",{staticClass:"sku-close iconfont iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),function(){return t.$refs.popupPintuanRule.close()}.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"pintuan-rule-title"},[t._v("拼团规则说明")]),i("v-uni-scroll-view",{staticClass:"pintuan-rule-info",attrs:{"scroll-y":!0}},[i("v-uni-rich-text",{attrs:{nodes:t.UnchangeContent(t.pintuan_info.pintuan_rule)}})],1)],1)],1),i("uni-popup",{ref:"popupToList",staticClass:"my-popup-dialog"},[i("v-uni-view",{staticClass:"popup-box"},[i("v-uni-view",{staticClass:"popup-box-body"},[t._v(t._s(t.pintuan_info.group_limit))]),i("v-uni-view",{staticClass:"popup-box-footer"},[i("v-uni-button",{staticClass:"red-botton",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.topituanOrderList.apply(void 0,arguments)}}},[t._v("查看我的拼团")])],1)],1)],1),i("diy-floating-rolling-order",{attrs:{positionType:"pintuan_detail",top:"weapp"==t.$util.getPlatform()?t.isShowDetailTab?"260rpx":"104rpx":"h5"==t.$util.getPlatform()?t.isShowDetailTab?"80rpx":"20rpx":"",left:"weapp"==t.$util.getPlatform()?"70rpx":""}}),i("diy-share",{ref:"sharePopup_share",attrs:{canvasOptions:t.canvasOptions_share,sharePopupOptions:t.sharePopupOptions_share},on:{childByValue:function(e){arguments[0]=e=t.$handleEvent(e),t.getShareImg.apply(void 0,arguments)}}}),i("diy-share-navigate-h5",{ref:"shareNavigateH5"})],1)},n=[]},"17d3":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6c61c027]{width:100%;text-align:center}.action-buttom-wrap[data-v-6c61c027]{flex:1;font-size:%?26?%;padding:%?15?% 0;border:none;color:#fff;text-align:center}.action-buttom-wrap.has-second[data-v-6c61c027]{padding:%?5?% 0;height:auto!important;line-height:%?35?%}.action-buttom-wrap.has-second .text-price[data-v-6c61c027]{font-size:%?24?%}.action-buttom-wrap.has-second uni-text[data-v-6c61c027]{display:block}.action-buttom-wrap[data-v-6c61c027]:active{opacity:.8}.action-buttom-wrap.disabled[data-v-6c61c027]{background:#ccc}',""]),t.exports=e},"18f7e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7aa735bd]{width:100%;text-align:center}.share-popup .share-title[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-title[data-v-7aa735bd]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center}.share-popup .share-content[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content[data-v-7aa735bd]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;margin-bottom:%?22?%}.share-popup .share-content .share-box[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-7aa735bd]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-7aa735bd]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-7aa735bd]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-7aa735bd]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-7aa735bd]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd]{color:#07c160}.share-popup .share-footer[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer[data-v-7aa735bd]{width:%?672?%;height:%?80?%;line-height:%?80?%;text-align:center;color:#fff;border-radius:%?40?%;background:var(--custom-brand-color);margin:0 auto}.share-popup .share-footer-padding[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer-padding[data-v-7aa735bd]{margin-bottom:%?40?%}.canvas[data-v-7aa735bd]{width:%?620?%;height:%?917?%;margin:0 auto;margin-top:%?70?%;display:block;overflow:hidden}.poster[data-v-7aa735bd]{display:flex;justify-content:center}.canvas1[data-v-7aa735bd]{top:100vh}@-webkit-keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-7aa735bd]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-7aa735bd]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-7aa735bd]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-7aa735bd]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-7aa735bd]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .6s linear normal infinite;animation:spin-data-v-7aa735bd .6s linear normal infinite}.loading-anim .in[data-v-7aa735bd]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .8s linear infinite;animation:spin-data-v-7aa735bd .8s linear infinite}.loading-anim .mid[data-v-7aa735bd]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-7aa735bd .6s linear infinite;animation:spin-data-v-7aa735bd .6s linear infinite}',""]),t.exports=e},"281a":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("2634")),n=a(i("2fdc"));i("64aa");var s=a(i("7c8d")),r={name:"diy-goods-detail-more-goodies",props:{title:{type:String,default:"更多好物"},sku_id:{type:[Number,String],required:!0}},data:function(){return{list:[]}},watch:{sku_id:function(){var t=(0,n.default)((0,o.default)().mark((function t(e,i){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getData();case 2:case"end":return t.stop()}}),t,this)})));return function(e,i){return t.apply(this,arguments)}}()},mounted:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getData();case 2:case"end":return e.stop()}}),e)})))()},methods:{getData:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:s.default.moreGoodiesUrl,async:!1,data:{sku_id:t.sku_id}});case 3:i=e.sent,0==i.code&&(t.list=i.data),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},imageError:function(t,e){e[t]&&(e[t].goods_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()}}};e.default=r},2982:function(t,e,i){"use strict";var a=i("fd7b"),o=i.n(a);o.a},"2e45":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966"),i("aa9c"),i("d4b5"),i("bf0f"),i("5c47"),i("a1c1"),i("f7a5"),i("c223"),i("8f71"),i("2797"),i("e838"),i("dd2b");var o=a(i("2634")),n=a(i("2fdc")),s=a(i("57bc")),r=a(i("7c8d")),d=a(i("85bf")),u=(i("54b6"),i("d64b")),l={data:function(){return{id:null,skuId:0,inviter_id:0,goodsSkuDetail:{goods_id:0},shopInfo:{logo:"",shop_baozh:0,shop_qtian:0,shop_zhping:0,shop_erxiaoshi:0,shop_tuihuo:0,shop_shiyong:0,shop_shiti:0,shop_xiaoxie:0},cartCount:0,whetherCollection:0,swiperInterval:1,swiperAutoplay:!1,swiperCurrent:1,switchMedia:"img",couponList:[],couponBtnSwitch:!1,token:"",poster:"-1",posterMsg:"",posterHeight:0,manjianList:{},goodsEvaluate:{member_headimg:"",member_name:"",content:"",images:[],create_time:0,sku_name:""},bundling:[{bundling_goods:{bl_name:"",sku_image:""}}],memberId:0,contactData:{title:"",path:"",img:""},detailTab:"productDetail",afterSale:null,service:null,preview:0,isDistributor:!1,shop_name_array:{share_shop_name:"",shop_name:""},tags:[],pintuanList:[],pintuan_info:{},pintuan_group_count:0,pintuan_page:1,pintuan_page_count:1,isPintuanGroup:!1,isShowPintuanMore:!1,popUpPintuanInnerRule:"",sharePopupOptions:[],isShowCanvas:!1,canvasOptions:{width:"560",height:"830",borderRadius:"20rpx"},sharePopupOptions_share:[],canvasOptions_share:{width:"420",height:"336",borderRadius:"10",scale:.5},buyerList:[],statusBarHeight:0}},computed:{share_shop_id:function(){return console.log("share_shop_id1",this.$store.state.share_shop_id),this.$store.state.share_shop_id},is_audit_mode:function(){return this.$store.state.audit_mode}},onLoad:function(t){console.log(t),console.log("option",t),console.log("??????"),t.scene&&(0,u.scenePare)(!1,t),this.id=t.id||0,this.preview=t.preview||0,t.source_member&&uni.setStorageSync("source_member",t.source_member),uni.removeStorageSync("selectGood"),uni.removeStorageSync("selectGoodType")},onShow:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,d.default.wait_staticLogin_success();case 3:return t.inviter_id=uni.getStorageSync("recommend_member_id")||0,e.next=6,t.getGoodsSkuDetail();case 6:return e.next=8,t.getPintuanGroupList(!0);case 8:t.token=uni.getStorageSync("token"),""!=t.token&&0==t.preview&&(t.getCartCount(),t.getMemberId()),0==t.preview&&(t.modifyGoodsInfo(),t.addonIsExit.manjian,t.addonIsExit.bundling);case 11:case"end":return e.stop()}}),e)})))()},onHide:function(){this.couponBtnSwitch=!1,this.$buriedPoint.browseGoods({id:this.goodsSkuDetail.goods_id})},onUnload:function(t){this.$buriedPoint.browseGoods({id:this.goodsSkuDetail.goods_id}),this.$store.dispatch("writeShareMemberId",null)},onShareAppMessage:function(t){var e=parseInt(t.target&&t.target.dataset&&t.target.dataset.join);if("button"==t.from&&e>-1){var i=this.getJoinPintuanShareParams(e);return d.default.pintuanShareActionReport({pintuan_id:this.pintuan_info.pintuan_id,goods_id:this.pintuan_info.goods_id}),this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_group_buying_id:this.pintuanList[e].group_id,diy_action_type:"share",is_goods_page:1}),this.$buriedPoint.pageShare(i.link,i.imageUrl,i.desc)}d.default.goodsShare(this.goodsSkuDetail.goods_id);var a=this.getSharePageParams(),o=this.shareImgPath||a.imageUrl;return d.default.pintuanShareActionReport({pintuan_id:this.pintuan_info.pintuan_id,goods_id:this.pintuan_info.goods_id}),this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_action_type:"share",is_goods_page:1}),this.$buriedPoint.pageShare(a.link,o,this.pintuan_info.share_title,!0,{goods_id:this.goodsSkuDetail.goods_id})},onShareTimeline:function(t){d.default.goodsShare(this.goodsSkuDetail.goods_id);var e=this.getSharePageParams();return{title:e.desc,imageUrl:e.imageUrl,query:e.query,success:function(t){},fail:function(t){}}},methods:{goBack:function(){this.$util.goBack()},buyerFun:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:r.default.usershareexperienceListUrl,async:!1,data:{page_size:100,page:1,goods_id:t.goodsSkuDetail.goods_id}});case 3:i=e.sent,t.buyerList=i.data.list,e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},buyersDetailFun:function(t){2==t.content_type?this.$util.diyRedirectTo({wap_url:t.content_link}):this.$util.redirectTo("/promotionpages/seeding/seeding_detail/seeding_detail?id=".concat(t.id))},buyersMoreFun:function(t){var e=[];e.push({goods_id:this.goodsSkuDetail.goods_id,goods_image:this.$util.img(this.goodsSkuDetail.sku_image)}),uni.setStorageSync("selectGood",JSON.stringify(e)),uni.setStorageSync("selectGoodType",1),t?this.$util.redirectTo("/promotionpages/seeding/seeding-add/seeding-add?goods_id=".concat(this.goodsSkuDetail.goods_id)):this.$util.redirectTo("/promotionpages/seeding/seeding-list/seeding-list?goods_id=".concat(this.goodsSkuDetail.goods_id))},getShareImg:function(t){this.shareImgPath=t},openVideo:function(){this.$refs.videoPopup.open()},getShareInfo:function(){return new Promise((function(t,e){d.default.checkToken().then((function(e){t()}))}))},showPintuanRule:function(){this.$refs.popupPintuanRule.open(),this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_action_type:"understand_rules",is_goods_page:1})},UnchangeContent:function(t){return t?t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/quot;/g,'"').replace(/<img/g,'<img style="max-width: 100%;"'):""},getGoodsSkuDetail:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n,s,d,u,l;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.getShareInfo();case 2:return e.id=t||e.id,a={id:e.id,share_id:e.inviter_id},i.next=6,e.$api.sendRequest({url:r.default.pintuanGoodsDetail,async:!1,data:a});case 6:if(n=i.sent,-10030!=n.code){i.next=9;break}return i.abrupt("return");case 9:if(s=n.data,e.pintuan_info=s.pintuan_info,null==s.goods_sku_detail){i.next=34;break}if(e.goodsSkuDetail=s.goods_sku_detail,e.goodsSkuDetail.preview=e.preview,e.shopInfo=s.shop_info,0==e.skuId&&(e.skuId=e.goodsSkuDetail.sku_id),e.goodsSkuDetail.video_url&&(e.switchMedia="video"),e.goodsSkuDetail.sku_images=e.goodsSkuDetail.sku_images.split(","),e.goodsSkuDetail.unit=e.goodsSkuDetail.unit||"件",e.goodsSkuDetail.show_price=e.goodsSkuDetail.retail_price,e.goodsSkuDetail.sku_spec_format&&(e.goodsSkuDetail.sku_spec_format=JSON.parse(e.goodsSkuDetail.sku_spec_format)),e.goodsSkuDetail.goods_attr_format)for(d=JSON.parse(e.goodsSkuDetail.goods_attr_format),e.goodsSkuDetail.goods_attr_format=JSON.parse(e.goodsSkuDetail.goods_attr_format),e.goodsSkuDetail.goods_attr_format=e.$util.unique(e.goodsSkuDetail.goods_attr_format,"attr_id"),u=0;u<e.goodsSkuDetail.goods_attr_format.length;u++)for(l=0;l<d.length;l++)e.goodsSkuDetail.goods_attr_format[u].attr_id==d[l].attr_id&&e.goodsSkuDetail.goods_attr_format[u].attr_value_id!=d[l].attr_value_id&&(e.goodsSkuDetail.goods_attr_format[u].attr_value_name+="、"+d[l].attr_value_name);return e.goodsSkuDetail.goods_spec_format&&(e.goodsSkuDetail.goods_spec_format=JSON.parse(e.goodsSkuDetail.goods_spec_format)),uni.setNavigationBarTitle({title:e.goodsSkuDetail.goods_name}),e.is_join_activity&&(e.goodsSkuDetail.goods_content='<img src="'.concat(s.shop_info.wechat_qrcode_img,'" style="display: block; width: 100%;"/>')+e.goodsSkuDetail.goods_content),1==e.goodsSkuDetail.promotion_type&&e.addonIsExit.discount&&(e.goodsSkuDetail.end_time-n.timestamp>0?e.goodsSkuDetail.discountTimeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-n.timestamp):e.goodsSkuDetail.promotion_type=0),e.contactData={title:e.goodsSkuDetail.sku_name,path:"/promotionpages/pintuan/detail/detail?id="+e.id,img:e.$util.img(e.goodsSkuDetail.sku_image)},e.getService(),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.setWechatShare(),i.next=32,e.buyerFun();case 32:i.next=35;break;case 34:e.$util.redirectTo("/pages/index/index/index",{},"reLaunch");case 35:case"end":return i.stop()}}),i)})))()},getSharePageParams:function(){var t=this.$util.unifySharePageParams("/promotionpages/pintuan/detail/detail","先迈商城",this.pintuan_info.share_title,{id:this.id},this.$util.img(this.goodsSkuDetail.sku_image));return t},getJoinPintuanShareParams:function(t){var e=this.pintuanList[t];e.goods_image=e.goods_image.split(",")[0];var i=this.$util.unifySharePageParams("/promotionpages/pintuan/share/share","先迈商城","邀请拼团抢".concat(e.goods_name),{group_id:e.group_id},this.$util.img(e.goods_image));return i},toShareJoin:function(t){var e=this;this.$refs.popupProceedPintuan.close();var i=this.getJoinPintuanShareParams(t);this.$refs.shareNavigateH5.open(i,(function(){e.isOnXianMaiApp&&d.default.pintuanShareActionReport({pintuan_id:e.pintuan_info.pintuan_id,goods_id:e.pintuan_info.goods_id})}))},newCommQrcode:function(){var t=this,e=this.getSharePageParams(),i=e.link,a=this.$util.GetRequestQuery(i),o=(0,u.query_to_scene)(a);this.$api.sendRequest({url:"/api/Website/newCommQrcode",data:{path:e.path.slice(1),scene:o},success:function(e){0==e.code?(t.drawCanvas(e.data.qrcodeUrl),t.drawCanvas_share(),setTimeout((function(){t.$refs.sharePopup_share.open()}),1e3)):t.$util.showToast({title:e.message})}})},drawCanvas:function(t){this.sharePopupOptions=[{background:"#fff",x:0,y:0,width:560,height:830,type:"image"},{path:this.$util.img(this.goodsSkuDetail.sku_image),x:0,y:0,width:560,height:560,type:"image"},{text:"￥",size:32,color:"#FF1010",x:20,y:620,type:"text"},{text:this.pintuan_info.pintuan_price,size:52,color:"#FF1010",fontWeight:"bold",x:60,y:620,type:"text"},{text:"￥"+this.goodsSkuDetail.market_price,size:26,color:"#999999",x:30,y:660,type:"text",textBaseline:!0},{text:this.goodsSkuDetail.goods_name,size:26,color:"#333333",x:30,y:700,width:310,lineNum:3,lineHeight:34,type:"text"},{path:t,x:354,y:580,width:180,height:180,type:"image"},{text:"请使用微信扫码",size:20,color:"#999999",x:376,y:786,type:"text"}],this.isShowCanvas=!0},drawCanvas_share:function(){this.sharePopupOptions_share=[{background:"#fff",x:0,y:0,width:420,height:336,type:"image"},{path:this.$util.img("https://www.xianmai88.com/static/youpin/share_bg.png"),x:0,y:0,width:420,height:336,type:"image"},{path:this.$util.img(this.goodsSkuDetail.sku_image)+"?image_process=resize,s_200",x:13,y:90,width:231,height:231,type:"image"},{text:"￥",size:32,color:"#FF1010",x:260,y:160,type:"text"},{text:this.pintuan_info.pintuan_price,size:38,color:"#FF1010",fontWeight:"bold",x:290,y:160,type:"text"},{text:"￥"+this.goodsSkuDetail.market_price,size:26,color:"#999999",x:260,y:200,type:"text",textBaseline:!0}]},getPintuanGroupList:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t){i.next=6;break}if(!(e.pintuan_page>e.pintuan_page_count)){i.next=3;break}return i.abrupt("return");case 3:e.isPintuanGroup=!0,i.next=9;break;case 6:e.pintuanList=[],e.pintuan_page=1,e.pintuan_page_count=1;case 9:return i.next=11,e.$api.sendRequest({url:r.default.pintuanGroupList,async:!1,data:{goods_id:e.goodsSkuDetail.goods_id,page:e.pintuan_page,page_size:20}});case 11:if(a=i.sent,0==a.code){i.next=14;break}return i.abrupt("return");case 14:n=a.data.group_list.list,e.pintuanList=e.pintuanList.concat(n),e.pintuan_group_count=a.data.group_list.count,e.pintuan_page_count=a.data.group_list.page_count,e.pintuan_page+=1,e.pintuanList.length>3&&(e.isShowPintuanMore=!0);case 20:case"end":return i.stop()}}),i)})))()},showPintuanMore:function(){this.isShowPintuanMore=!1},scrollIntoPintunRule:function(){var t=this;this.popUpPintuanInnerRule="popUpPintuanInnerRule",setTimeout((function(){t.popUpPintuanInnerRule=""}),100)},refreshGoodsSkuDetail:function(t){var e=this;Object.assign(this.goodsSkuDetail,t),this.swiperCurrent>this.goodsSkuDetail.sku_images.length&&(this.swiperAutoplay=!0,this.swiperCurrent=1,setTimeout((function(){e.swiperAutoplay=!1}),40))},goHome:function(){this.preview||this.$util.redirectTo("/otherpages/shop/home/<USER>",{},"reLaunch")},checkShoppingStatus:function(){var t=uni.getStorageSync("is_shopping_status");if(0==t)return this.$refs.popupBan.open(),!0},goCart:function(){this.preview||this.$util.redirectTo("/pages/goods/cart/cart",{},"reLaunch")},topituanOrderList:function(){console.log("进来了"),this.$util.redirectTo("/promotionpages/pintuan/order/list/list",{},"redirectTo")},redirectDetailsUrl:function(){var t=uni.getStorageSync("shop_id"),e="/promotionpages/pintuan/detail/detail?id=".concat(this.id,"&shop_id=").concat(t);return e},joinCart:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.token||0!=t.preview){e.next=6;break}return e.next=3,t.redirectDetailsUrl();case 3:return i=e.sent,t.$util.toShowLoginPopup(t,null,i),e.abrupt("return");case 6:return e.next=8,t.checkShoppingStatus();case 8:if(!e.sent){e.next=10;break}return e.abrupt("return",!1);case 10:if(!t.goodsSkuDetail.sku_spec_format){e.next=16;break}return console.log(t.$refs.goodsSku),t.$refs.goodsSku.type="join_cart",t.$refs.goodsSku.callback=function(){t.getCartCount()},t.$refs.goodsSku.confirm(),e.abrupt("return");case 16:t.$refs.goodsSku.show("join_cart",(function(){t.getCartCount()}));case 17:case"end":return e.stop()}}),e)})))()},buyNow:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:t.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(t.id),diy_action_type:"individual_purchase",is_goods_page:1}),t.token||0!=t.preview){e.next=7;break}return e.next=4,t.redirectDetailsUrl();case 4:return i=e.sent,t.$util.toShowLoginPopup(t,null,i),e.abrupt("return");case 7:return e.next=9,t.checkShoppingStatus();case 9:if(!e.sent){e.next=11;break}return e.abrupt("return",!1);case 11:if(!t.goodsSkuDetail.sku_spec_format){e.next=16;break}return t.$refs.goodsSku.type="buy_now",t.$refs.goodsSku.callback=function(){t.getCartCount()},t.$refs.goodsSku.confirm(),e.abrupt("return");case 16:t.$refs.goodsSku.show("buy_now",(function(){t.getCartCount()}));case 17:case"end":return e.stop()}}),e)})))()},swiperChange:function(t){this.swiperCurrent=t.detail.current+1},chooseSkuspecFormat:function(t){var e=this;this.token||0!=this.preview?1==t?this.$refs.goodsSku.show("maidou_spec",(function(){e.getCartCount()})):this.tags&&this.tags.filter((function(t){return"newhand"==t.key})).length>0?this.$refs.goodsSku.show("newhand_spec",(function(){e.getCartCount()})):(this.$refs.goodsSku.IsNotSkip=!1,this.$refs.goodsSku.show("pintuan",(function(){e.getCartCount()}))):this.$util.toShowLoginPopup(this,null,"/promotionpages/pintuan/detail/detail?id="+this.id)},getCoupon:function(){var t=this;this.$api.sendRequest({url:"/coupon/api/coupon/typelists",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){var i=e.data;i&&(i.forEach((function(t){t.useState=0})),t.couponList=i)}})},openCouponPopup:function(){this.$refs.couponPopup.open()},closeCouponPopup:function(){this.$refs.couponPopup.close()},receiveCoupon:function(t,e){var i=this;if(!this.preview&&1!=t.useState&&!this.couponBtnSwitch)if(this.couponBtnSwitch=!0,""!=this.token){var a={site_id:t.site_id,coupon_type_id:t.coupon_type_id,get_type:2};"/coupon/api/coupon/receive",this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:a,success:function(e){e.data;var a=e.message;0==e.code&&(a="领取成功");var o=i.couponList;if(1==e.data.is_exist)for(var n=0;n<o.length;n++)o[n].coupon_type_id==t.coupon_type_id&&(o[n].useState=1);else for(var s=0;s<o.length;s++)o[s].coupon_type_id==t.coupon_type_id&&(o[s].useState=2);i.$util.showToast({title:a}),i.couponBtnSwitch=!1}})}else this.$refs.login.open("/promotionpages/pintuan/detail/detail?id="+this.id)},openMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.open()},closeMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.close()},openAttributePopup:function(){this.$refs.attributePopup.open()},closeAttributePopup:function(){this.$refs.attributePopup.close()},getGoodsEvaluate:function(){var t=this;this.$api.sendRequest({url:"/api/goodsevaluate/firstinfo",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){var i=e.data;i&&(t.goodsEvaluate=i,t.goodsEvaluate.images&&(t.goodsEvaluate.images=t.goodsEvaluate.images.split(",")),1==t.goodsEvaluate.is_anonymous&&(t.goodsEvaluate.member_name=t.goodsEvaluate.member_name.replace(t.goodsEvaluate.member_name.substring(1,t.goodsEvaluate.member_name.length-1),"***")))}})},previewEvaluate:function(t,e){for(var i=[],a=0;a<this.goodsEvaluate[e].length;a++)i.push(this.$util.img(this.goodsEvaluate[e][a]));uni.previewImage({current:t,urls:i})},getWhetherCollection:function(){var t=this;this.$api.sendRequest({url:"/api/goodscollect/iscollect",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){t.whetherCollection=e.data}})},editCollection:function(){var t=this;this.preview||(""!=this.token?0==this.whetherCollection?this.$api.sendRequest({url:"/api/goodscollect/add",data:{sku_id:this.skuId,site_id:this.goodsSkuDetail.site_id,goods_id:this.goodsSkuDetail.goods_id,category_id:this.goodsSkuDetail.category_id,sku_name:this.goodsSkuDetail.sku_name,sku_price:this.goodsSkuDetail.discount_price,sku_image:this.goodsSkuDetail.sku_image},success:function(e){var i=e.data;i>0&&(t.whetherCollection=1,t.$util.showToast({title:"收藏成功"}))}}):this.$api.sendRequest({url:"/api/goodscollect/delete",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){var i=e.data;i>0&&(t.whetherCollection=0,t.$util.showToast({title:"已取消收藏"}))}}):this.$util.toShowLoginPopup(this,null,"/promotionpages/pintuan/detail/detail?sku_id="+this.id))},getCartCount:function(){var t=this;this.preview||this.$store.dispatch("getCartNumber").then((function(e){t.cartCount=e}))},modifyGoodsInfo:function(){if(!this.preview){this.$api.sendRequest({url:"/api/goods/modifyclicks",data:{sku_id:this.skuId,site_id:this.goodsSkuDetail.site_id},success:function(t){}});var t={goods_id:this.goodsSkuDetail.goods_id,sku_id:this.skuId,category_id:this.goodsSkuDetail.category_id,category_id_1:this.goodsSkuDetail.category_id_1,category_id_2:this.goodsSkuDetail.category_id_2,category_id_3:this.goodsSkuDetail.category_id_3,site_id:this.goodsSkuDetail.site_id};this.$store.state.buried_shop_id&&(t.share_shop_id=this.$store.state.buried_shop_id),this.$api.sendRequest({url:"/api/goodsbrowse/add",data:t,success:function(t){}})}},getManjian:function(){var t=this;this.$api.sendRequest({url:"/manjian/api/manjian/info",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){var i=e.data;i&&(t.manjianList=i)}})},openManjianPopup:function(){this.$refs.manjianPopup.open()},closeManjianPopup:function(){this.$refs.manjianPopup.close()},getBundling:function(){var t=this;this.$api.sendRequest({url:"/bundling/api/bundling/lists",data:{sku_id:this.skuId},success:function(e){if(e.data&&e.data.length){var i=e.data;i.forEach((function(e){e.bundling_goods=e.bundling_goods.filter((function(e){return e.sku_id!=t.goodsSkuDetail.sku_id}))})),t.bundling=i}}})},openBundlingPopup:function(){this.$refs.bundlingPopup.open()},closeBundlingPopup:function(){this.$refs.bundlingPopup.close()},openSharePopup:function(){var t=this,e=this.getSharePageParams();this.$refs.shareNavigateH5&&this.$refs.shareNavigateH5.open(e,(function(){t.isOnXianMaiApp&&d.default.pintuanShareActionReport({pintuan_id:t.pintuan_info.pintuan_id,goods_id:t.pintuan_info.goods_id})}))},closeSharePopup:function(){this.$refs.sharePopup.close()},onShareClick:function(){console.log(this.$refs.share_box),this.$refs.share_box.click()},openPosterPopup:function(){var t=this;this.getGoodsPoster(),this.$refs.sharePopup.close(),this.$refs.posterPopup.open(),"-1"!=this.poster&&setTimeout((function(){var e=uni.createSelectorQuery().in(t).select(".poster-layer .image-wrap");e.fields({size:!0},(function(e){var i=e.width,a=parseFloat((740/i).toFixed(2));""!=t.token?t.posterHeight=parseInt(1120/a):t.posterHeight=parseInt(1100/a)})).exec()}),100)},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var t=this,e={sku_id:this.skuId};this.memberId&&(e.source_member=this.memberId),this.$api.sendRequest({url:"/api/goods/poster",data:{page:"/promotionpages/pintuan/detail/detail",qrcode_param:JSON.stringify(e)},success:function(e){0==e.code?t.poster=e.data.path:t.posterMsg=e.message}})},previewMedia:function(t){for(var e=[],i=0;i<this.goodsSkuDetail.sku_images.length;i++)e.push(this.$util.img(this.goodsSkuDetail.sku_images[i]));uni.previewImage({current:t,urls:e})},imageError:function(){this.goodsSkuDetail.sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},swiperImageError:function(t){this.goodsSkuDetail.sku_images[t]=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},bundlingImageError:function(t,e){this.bundling[t].bundling_goods[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},getMemberId:function(){var t=this;this.$api.sendRequest({url:"/api/member/id",success:function(e){e.code>=0&&(t.memberId=e.data)}})},getAfterSale:function(){var t=this;this.$api.sendRequest({url:"/api/goods/aftersale",success:function(e){if(0==e.code&&e.data){e.data.content;e.data.content&&(t.afterSale=(0,s.default)(e.data.content))}}})},getService:function(){var t=this;this.$api.sendRequest({url:r.default.goodsServiceDesc,data:{goods_id:this.goodsSkuDetail.goods_id},success:function(e){if(0==e.code&&e.data){e.data.content;e.data.directions&&(t.service=(0,s.default)(e.data.directions)),e.data.protocol&&(t.afterSale=(0,s.default)(e.data.protocol))}}})},errorShopLogo:function(){this.shopInfo.avatar=this.$util.getDefaultImage().default_shop_img,this.$forceUpdate()},setWechatShare:function(){var t=this,e=this.$util.deepClone(this.getSharePageParams()),i=window.location.origin+this.$router.options.base+e.link.slice(1);e.link=i,this.$util.publicShare(e,(function(){d.default.pintuanShareActionReport({pintuan_id:t.pintuan_info.pintuan_id,goods_id:t.pintuan_info.goods_id})}))},toPoint:function(t){var e=t.target.dataset.id,i=this;uni.createSelectorQuery().select(".goods-detail").boundingClientRect((function(t){uni.createSelectorQuery().select("#"+e).boundingClientRect((function(a){i.detailTab=e,uni.pageScrollTo({duration:500,scrollTop:Math.abs(t.top-a.top)})})).exec()})).exec()},pintuan:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:t.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(t.id),diy_action_type:"start_group",is_goods_page:1}),t.$refs.popupProceedPintuan.close(),uni.getStorageSync("token")){e.next=9;break}return e.next=5,t.redirectDetailsUrl();case 5:i=e.sent,t.$util.toShowLoginPopup(t,null,i),e.next=10;break;case 9:t.pintuan_info.group_limit?t.$refs.popupToList.open():t.$refs.popupOpenPintuan.open();case 10:case"end":return e.stop()}}),e)})))()},closePinTuanPopup:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$refs.popupOpenPintuan.close(),t.token||0!=t.preview){e.next=7;break}return e.next=4,t.redirectDetailsUrl();case 4:return i=e.sent,t.$util.toShowLoginPopup(t,null,i),e.abrupt("return");case 7:return e.next=9,t.checkShoppingStatus();case 9:if(!e.sent){e.next=11;break}return e.abrupt("return",!1);case 11:t.$refs.goodsSku.number=1,t.$refs.goodsSku.IsNotSkip=!0,t.$refs.goodsSku.type="pintuan",t.$refs.goodsSku.confirm();case 15:case"end":return e.stop()}}),e)})))()},onFinish:function(t){for(var e=null,i=0;i<this.pintuanList.length;i++)if(this.pintuanList[i].group_id==t){e=i;break}null!=e&&this.pintuanList.splice(e,1)},morePintuan:function(t){this.$refs.popupProceedPintuan.open(),"view_all"==t&&this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_action_type:"view_all",is_goods_page:1}),"invite_politely"==t&&this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_action_type:"click_invite_politely",is_goods_page:1})},toPintuanDetail:function(t){this.$buriedPoint.diyReportGroupBuyingInteractionEvent({sku_id:this.goodsSkuDetail.sku_id,goods_id:null,diy_group_activity_id:parseInt(this.id),diy_group_buying_id:t.group_id,diy_action_type:"join_group",is_goods_page:1});var e="/promotionpages/pintuan/share/share?group_id=".concat(t.group_id);this.$util.redirectTo(e,{})},proceedPintuanTo:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isPintuanGroup){t.next=4;break}return t.next=3,e.getPintuanGroupList();case 3:e.isPintuanGroup=!1;case 4:case"end":return t.stop()}}),t)})))()}}};e.default=l},"2e65":function(t,e,i){"use strict";i.r(e);var a=i("bb0b"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"31c3":function(t,e,i){"use strict";i.r(e);var a=i("ae57"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"360e":function(t,e,i){"use strict";i.r(e);var a=i("be6f"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},3745:function(t,e,i){"use strict";var a=i("764d"),o=i.n(a);o.a},"49a0":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[t.disabled?i("v-uni-view",{staticClass:"action-buttom-wrap disabled ns-gradient-components-ns-goods-action-list",class:[t.themeStyle,t.textPrice?"has-second":""],staticStyle:{border:"none",color:"#fff"}},[i("v-uni-text",[t._v(t._s(t.disabledText))]),t.textPrice?i("v-uni-text",{staticClass:"text-price"},[t._v(t._s(t.textPrice))]):t._e()],1):i("v-uni-view",{staticClass:"action-buttom-wrap ns-gradient-components-ns-goods-action-list",class:[t.themeStyle,t.backgroundClass,t.textPrice?"has-second":""],style:{background:t.background},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickEvent.apply(void 0,arguments)}}},[i("v-uni-text",[t._v(t._s(t.text))]),t.textPrice?i("v-uni-text",{staticClass:"text-price"},[t._v(t._s(t.textPrice))]):t._e()],1)],1)},o=[]},"4d82":function(t,e,i){"use strict";i.r(e);var a=i("bf93"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"528d":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-38dab624]{width:100%;text-align:center}.rolling-order[data-v-38dab624]{position:fixed;display:flex;align-items:center;background-color:rgba(0,0,0,.6);padding:0 %?20?% 0 %?6?%;height:%?60?%;line-height:%?60?%;box-sizing:border-box;border-radius:%?40?%;opacity:0}.rolling-order-head[data-v-38dab624]{width:%?50?%;height:%?50?%;border-radius:50%}.rolling-order-text[data-v-38dab624]{font-size:%?24?%;line-height:1.5;color:#fff;margin-left:%?10?%}',""]),t.exports=e},"5ab1":function(t,e,i){"use strict";i.r(e);var a=i("78f85"),o=i("fe2a");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("3745");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"d6071282",null,!1,a["a"],void 0);e["default"]=r.exports},"5c37":function(t,e,i){var a=i("7858");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("730aaec3",a,!0,{sourceMap:!1,shadowMode:!1})},"5fa3":function(t,e,i){"use strict";i.r(e);var a=i("281a"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"606f":function(t,e,i){"use strict";var a=i("0895"),o=i.n(a);o.a},6147:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".ns-goods-action[data-v-a49c4d4a]{position:fixed;right:0;bottom:0;left:0;display:flex;align-items:center;background-color:#fff}.ns-goods-action.bottom-safe-area[data-v-a49c4d4a]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}",""]),t.exports=e},"64ea":function(t,e,i){"use strict";i.r(e);var a=i("fb52"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},6531:function(t,e,i){var a=i("6147");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("3a16ed49",a,!0,{sourceMap:!1,shadowMode:!1})},"68d5":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"ns-goods-action-icon",props:{icon:{type:String,default:""},imgicon:{type:String,default:""},text:{type:String,default:""},cornerMark:{type:String,default:""},cornerMarkBg:{type:String,default:""},cornerMarkColor:{type:String,default:"#fff"},openType:{type:String,default:""},sendData:{type:Object,default:function(){return{title:"",path:"",img:""}}}},methods:{clickEvent:function(){this.$emit("click")}}};e.default=a},"748c":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-33b7d3ae]{width:100%;text-align:center}.goods_tag[data-v-33b7d3ae]{padding:%?5?%;background:linear-gradient(270deg,#fe5838,#fb331d);border-radius:%?8?%;font-size:%?20?%;color:#fff;text-align:center;line-height:%?24?%;margin-right:%?10?%}.pintuan-price[data-v-33b7d3ae]{height:%?170?%;padding-bottom:%?58?%;background:rgba(246,93,114,.95);box-sizing:border-box;padding-right:%?25?%;display:flex;justify-content:space-between;align-items:center}.pintuan-price-left[data-v-33b7d3ae]{width:%?440?%;height:%?108?%;padding-left:%?28?%;display:flex;justify-content:center;flex-direction:column}.pintuan-price-left-one[data-v-33b7d3ae]{display:flex;align-items:center}.pintuan-price-left-one-tag[data-v-33b7d3ae]{width:%?80?%;height:%?28?%;line-height:%?28?%;background:#feeeee;border-radius:%?14?%;font-size:%?20?%;font-weight:500;color:#f84346;text-align:center;margin-right:%?12?%}.pintuan-price-left-one-price[data-v-33b7d3ae]{font-size:%?58?%;font-weight:700;color:#fff;line-height:1}.pintuan-price-left-one-price uni-text[data-v-33b7d3ae]{font-size:%?34?%}.pintuan-price-left-one-unprice[data-v-33b7d3ae]{font-size:%?22?%;font-weight:500;text-decoration:line-through;color:#feeeee;align-self:flex-end;margin-left:%?7?%}.pintuan-price-right[data-v-33b7d3ae]{border-radius:40px;background:hsla(0,0%,100%,.1);height:%?32?%;display:flex;align-items:center;padding-right:%?20?%;box-sizing:border-box}.pintuan-price-right-number[data-v-33b7d3ae]{font-size:%?24?%;font-weight:400;line-height:%?32?%;color:#f65d72;padding:0 %?20?%;box-sizing:border-box;border-radius:%?40?%;background:#fff}.pintuan-price-right-yet[data-v-33b7d3ae]{font-size:%?24?%;font-weight:400;line-height:%?32?%;color:#fff;margin-left:%?16?%}.pintuan-list[data-v-33b7d3ae]{background:#fff}.pintuan-list-title[data-v-33b7d3ae]{box-sizing:border-box;height:%?88?%;padding:0 %?24?%;display:flex;justify-content:space-between;align-items:center}.pintuan-list-title-left[data-v-33b7d3ae]{display:flex;align-items:center}.pintuan-list-title-left-fill[data-v-33b7d3ae]{width:%?6?%;height:%?32?%;background:#f84346;border-radius:%?3?%;display:inline-block}.pintuan-list-title-left-tip[data-v-33b7d3ae]{font-size:%?30?%;font-weight:400;color:#383838;margin-left:%?14?%}.pintuan-list-title-right-more[data-v-33b7d3ae]{font-size:%?26?%;font-weight:500;color:#383838}.pintuan-list-info[data-v-33b7d3ae]{padding:0 %?24?%;box-sizing:border-box}.pintuan-list-info-one[data-v-33b7d3ae]{box-sizing:border-box;padding-bottom:%?32?%;display:flex;justify-content:space-between;align-items:center}.pintuan-list-info-one-left[data-v-33b7d3ae]{display:flex;align-items:center}.pintuan-list-info-one-left-img[data-v-33b7d3ae]{width:%?92?%;height:%?92?%;position:relative}.pintuan-list-info-one-left-img uni-image[data-v-33b7d3ae]{width:%?92?%;height:%?92?%;border-radius:50%}.pintuan-list-info-one-left-img-desc[data-v-33b7d3ae]{position:absolute;left:50%;bottom:%?-12?%;-webkit-transform:translateX(-50%);transform:translateX(-50%);background-color:#fff;height:%?32?%;width:%?56?%;border-radius:%?40?%;display:flex;justify-content:center;align-items:center}.pintuan-list-info-one-left-img-desc-text[data-v-33b7d3ae]{width:%?48?%;height:%?24?%;border-radius:%?40?%;background:#f65d72;font-size:%?16?%;font-weight:400;line-height:%?24?%;color:#fff;text-align:center}.pintuan-list-info-one-left-name[data-v-33b7d3ae]{font-size:%?28?%;font-weight:700;color:#333;margin-left:%?15?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:%?260?%}.pintuan-list-info-one-right[data-v-33b7d3ae]{display:flex;align-items:center}.pintuan-list-info-one-right-one[data-v-33b7d3ae]{display:flex;justify-content:center;flex-direction:column}.pintuan-list-info-one-right-one-count[data-v-33b7d3ae]{font-size:%?24?%;color:#383838;font-weight:400}.pintuan-list-info-one-right-one-count uni-text[data-v-33b7d3ae]{color:#f65d72;font-weight:700}.pintuan-list-info-one-right-one-time[data-v-33b7d3ae]{font-size:%?24?%;font-weight:500;color:#666;display:flex;align-items:center}.pintuan-list-info-one-right-two[data-v-33b7d3ae]{width:%?160?%;height:%?72?%;border-radius:%?40?%;background:#f65d72;font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#fff;display:flex;justify-content:center;align-items:center;margin:0;margin-left:%?18?%}.pintuan-list-info-one-right-one-time[data-v-33b7d3ae] .custom uni-view{font-size:%?24?%;font-weight:500;color:#666}.proceed-pintuan-info-one-left-name-time[data-v-33b7d3ae] .custom uni-view{font-size:%?24?%;font-weight:500;color:#666}.pintuan-method[data-v-33b7d3ae]{background:#fff}.pintuan-method-title[data-v-33b7d3ae]{box-sizing:border-box;height:%?88?%;padding:0 %?32?% 0 %?28?%;display:flex;justify-content:space-between;align-items:center}.pintuan-method-title-left[data-v-33b7d3ae]{display:flex;align-items:center}.pintuan-method-title-left-tip[data-v-33b7d3ae]{font-size:%?30?%;font-weight:400;line-height:%?44?%;color:#383838}.pintuan-method-title-left-desc[data-v-33b7d3ae]{width:%?48?%;height:%?24?%;border-radius:%?40?%;background:#f65d72;border:%?2?% solid #fff;font-size:%?16?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.pintuan-method-title-right-more[data-v-33b7d3ae]{font-size:%?26?%;font-weight:500;color:#383838;display:flex;align-items:center}.pintuan-method-title-right-more uni-image[data-v-33b7d3ae]{width:%?30?%;height:%?30?%;margin-left:%?12?%}.pintuan-method-info[data-v-33b7d3ae]{display:flex;align-items:center;height:%?110?%;padding:0 %?40?%;padding-bottom:%?10?%}.pintuan-method-info-node[data-v-33b7d3ae]{display:flex;align-items:center;position:relative}.pintuan-method-info-node-order[data-v-33b7d3ae]{width:%?28?%;height:%?28?%;line-height:%?28?%;background:#ccc;border-radius:50%;font-size:%?20?%;font-weight:700;color:#fff;text-align:center}.pintuan-method-info-node-text[data-v-33b7d3ae]{margin-left:%?8?%;font-size:%?26?%;font-weight:700;color:#333}.pintuan-method-info-node-tip[data-v-33b7d3ae]{position:absolute;left:%?8?%;bottom:%?-28?%;font-size:%?22?%;font-weight:500;color:#666;width:%?160?%}.pintuan-method-info-span[data-v-33b7d3ae]{margin:0 %?50?%;color:#ccc}.choose-pintuan-type[data-v-33b7d3ae]{background:#fff;border-radius:%?30?% %?30?% %?0?% %?0?%;padding-top:%?40?%;box-sizing:border-box}.choose-pintuan-type-title[data-v-33b7d3ae]{font-size:%?36?%;font-weight:700;color:#333;padding-left:%?33?%;box-sizing:border-box}.choose-pintuan-type-list[data-v-33b7d3ae]{display:flex;flex-wrap:wrap;padding-left:%?33?%;box-sizing:border-box;margin-top:%?38?%;min-height:%?212?%}.choose-pintuan-type-list uni-text[data-v-33b7d3ae]{width:%?144?%;height:%?54?%;line-height:%?54?%;color:#333;background:#f2f2f2;border-radius:%?27?%;font-size:%?26?%;font-weight:500;margin-right:%?38?%;text-align:center;box-sizing:border-box}.choose-pintuan-type-list uni-text.active[data-v-33b7d3ae]{border:%?2?% solid #eb655a;color:#f84346;background:#fcedeb}.choose-pintuan-type-op[data-v-33b7d3ae]{height:%?98?%;border-top:%?2?% solid #eee;box-sizing:border-box;display:flex;justify-content:center;align-items:center}.choose-pintuan-type-op uni-button[data-v-33b7d3ae]{width:%?654?%;height:%?80?%;background:#f2270c;border-radius:%?40?%;font-size:%?28?%;font-weight:500;color:#fff}.choose-pintuan-type-parent[data-v-33b7d3ae] .uni-popup__wrapper{border-radius:%?30?% %?30?% %?0?% %?0?%}.proceed-pintuan-parent[data-v-33b7d3ae] .uni-popup__wrapper{border-radius:%?40?% %?40?% %?0?% %?0?%}.proceed-pintuan-parent[data-v-33b7d3ae] .uni-popup__wrapper-box{background-color:initial!important;padding-bottom:%?100?%}.proceed-pintuan[data-v-33b7d3ae]{width:%?750?%;min-height:%?500?%;max-height:%?980?%;margin-top:%?-48?%;background:#fff;border-radius:%?40?%;box-sizing:border-box;position:relative}.proceed-pintuan[data-v-33b7d3ae] .uni-scroll-view{border-radius:%?40?%;box-sizing:border-box}.proceed-pintuan-title[data-v-33b7d3ae]{height:%?148?%;display:flex;justify-content:space-between;align-items:center;padding:%?32?% %?22?% %?72?% %?40?%;box-sizing:border-box;width:100%;z-index:9999;border-radius:%?40?% %?40?% 0 0;background:#f65d72}.proceed-pintuan-title-left[data-v-33b7d3ae]{font-size:%?32?%;font-weight:700;line-height:%?44?%;color:#fff}.proceed-pintuan-title-right[data-v-33b7d3ae]{border-radius:40px;background:hsla(0,0%,100%,.1);height:%?32?%;display:flex;align-items:center;padding-right:%?20?%;box-sizing:border-box}.proceed-pintuan-title-right-number[data-v-33b7d3ae]{font-size:%?24?%;font-weight:400;line-height:%?32?%;color:#f65d72;padding:0 %?20?%;box-sizing:border-box;border-radius:%?40?%;background:#fff}.proceed-pintuan-title-right-yet[data-v-33b7d3ae]{font-size:%?24?%;font-weight:400;line-height:%?32?%;color:#fff;margin-left:%?16?%}.proceed-pintuan-info[data-v-33b7d3ae]{padding:0 %?30?%;padding-bottom:%?24?%;box-sizing:border-box;border-bottom:%?2?% solid #eee}.proceed-pintuan-info-one[data-v-33b7d3ae]{display:flex;justify-content:space-between;align-items:center;box-sizing:border-box;padding:%?30?% 0}.proceed-pintuan-info-one-left[data-v-33b7d3ae]{display:flex;align-items:center}.proceed-pintuan-info-one-left-img[data-v-33b7d3ae]{width:%?92?%;height:%?92?%;position:relative}.proceed-pintuan-info-one-left-img uni-image[data-v-33b7d3ae]{width:%?92?%;height:%?92?%;border-radius:50%}.proceed-pintuan-info-one-left-img-desc[data-v-33b7d3ae]{position:absolute;left:50%;bottom:%?-12?%;-webkit-transform:translateX(-50%);transform:translateX(-50%);background-color:#fff;height:%?32?%;width:%?56?%;border-radius:%?40?%;display:flex;justify-content:center;align-items:center}.proceed-pintuan-info-one-left-img-desc uni-text[data-v-33b7d3ae]{width:%?48?%;height:%?24?%;border-radius:%?40?%;background:#f65d72;font-size:%?16?%;font-weight:400;line-height:%?24?%;color:#fff;text-align:center}.proceed-pintuan-info-one-left-info[data-v-33b7d3ae]{display:flex;flex-direction:column;justify-content:center;margin-left:%?22?%}.proceed-pintuan-info-one-left-info-name[data-v-33b7d3ae]{font-size:%?28?%;font-weight:700;color:#333;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:%?104?%}.proceed-pintuan-info-one-left-name[data-v-33b7d3ae]{display:flex;flex-direction:column;justify-content:center;margin-left:%?20?%}.proceed-pintuan-info-one-left-name-text[data-v-33b7d3ae]{font-size:%?24?%;font-weight:500;color:#333;line-height:1;text-align:right}.proceed-pintuan-info-one-left-name-text uni-text[data-v-33b7d3ae]{color:#f84346}.proceed-pintuan-info-one-left-name-time[data-v-33b7d3ae]{font-size:%?24?%;font-weight:500;color:#666;display:flex;justify-content:flex-end;align-items:center;line-height:1}.proceed-pintuan-info-one-right[data-v-33b7d3ae]{display:flex;justify-content:center;align-items:center}.proceed-pintuan-info-one-right-join[data-v-33b7d3ae]{height:%?72?%;border-radius:%?40?%;background:rgba(246,93,114,.1);border:%?2?% solid #f65d72;font-size:%?28?%;font-weight:400;line-height:%?40?%;color:#f65d72;display:flex;justify-content:center;align-items:center;margin:0}.proceed-pintuan-info-one-right-share[data-v-33b7d3ae]{width:%?120?%;height:%?72?%;border-radius:%?40?%;background:#f65d72;font-size:%?28?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center;margin:0;margin-left:%?14?%}.proceed-pintuan-info-loading[data-v-33b7d3ae]{display:flex;justify-content:center;align-items:center;box-sizing:border-box;margin-top:%?20?%}.proceed-pintuan-info-loading uni-text[data-v-33b7d3ae]{font-size:%?28?%;font-weight:400;line-height:%?44?%;color:#a6a6a6}.proceed-pintuan-op[data-v-33b7d3ae]{display:flex}.proceed-pintuan-op[data-v-33b7d3ae] .ns-goods-action.bottom-safe-area{padding-bottom:%?20?%;padding-bottom:calc(%?20?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?20?% + env(safe-area-inset-bottom))}.proceed-pintuan-op[data-v-33b7d3ae] .action-buttom-wrap.has-second{height:%?80?%!important;display:flex;flex-direction:column;justify-content:center;align-items:center;box-sizing:border-box}@-webkit-keyframes loading-data-v-33b7d3ae{50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-data-v-33b7d3ae{50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.proceed-pintuan-info-one-left-info-time[data-v-33b7d3ae] .custom uni-view{font-size:%?22?%;font-weight:500;color:#999}.pintuan-rule[data-v-33b7d3ae]{height:%?785?%;background:#fff;border-radius:%?20?% %?20?% %?0?% %?0?%;box-sizing:border-box;padding:%?50?% %?40?% %?30?% %?40?%}.pintuan-rule-inner[data-v-33b7d3ae]{height:auto!important}.pintuan-rule-inner .pintuan-rule-info[data-v-33b7d3ae]{height:auto!important}.pintuan-rule-title[data-v-33b7d3ae]{font-size:%?36?%;font-weight:700;color:#333;text-align:center}.pintuan-rule-info[data-v-33b7d3ae]{font-size:%?28?%;font-weight:500;color:#666;height:%?580?%;box-sizing:border-box}.pintuan-rule-op[data-v-33b7d3ae]{width:%?480?%;height:%?68?%;background:#f2270c;border-radius:%?34?%;font-size:%?28?%;font-weight:500;color:#fff;margin:0 auto}.pintuan-space[data-v-33b7d3ae]{background:#fff;display:flex;justify-content:center;padding-bottom:%?20?%;box-sizing:border-box}.pintuan-flow[data-v-33b7d3ae]{display:flex;justify-content:space-between;align-items:flex-start;box-sizing:border-box;width:%?690?%;border-radius:%?10?%;background:#fafafa;padding:%?40?%;position:relative}.pintuan-flow-one[data-v-33b7d3ae]{display:flex;flex-direction:column;justify-content:center;align-items:center;position:relative}.pintuan-flow-one[data-v-33b7d3ae]:nth-child(2){width:%?160?%}.pintuan-flow-one[data-v-33b7d3ae]:nth-child(1):after{content:"";width:%?100?%;border-top:%?2?% dashed #ccc;position:absolute;left:%?106?%;top:%?44?%}.pintuan-flow-one[data-v-33b7d3ae]:nth-child(2):after{content:"";width:%?130?%;border-top:%?2?% dashed #ccc;position:absolute;left:%?140?%;top:%?44?%}.pintuan-flow-one uni-image[data-v-33b7d3ae]{width:%?88?%;height:%?88?%;border-radius:50%;margin-bottom:%?20?%}.pintuan-flow-one-info-other[data-v-33b7d3ae]{display:flex;flex-direction:column;align-items:flex-start}.pintuan-flow-one-info uni-view[data-v-33b7d3ae]{font-size:%?24?%;font-weight:500;color:#333;text-align:center}.pintuan-flow-one-info-red[data-v-33b7d3ae]{color:#f84346}.pintuan-flow-one-info-price[data-v-33b7d3ae]{color:#f84346;font-weight:700}.pintuan-flow-one-info-price uni-text[data-v-33b7d3ae]{font-size:%?24?%}.popup-box[data-v-33b7d3ae]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-box-body[data-v-33b7d3ae]{color:#656565;text-align:center;padding:%?30?% %?30?% 0 %?30?%;width:%?416?%}.popup-box-footer[data-v-33b7d3ae]{margin:0 %?32?%;height:%?140?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-justify-content:space-around;justify-content:space-around}.red-botton[data-v-33b7d3ae]{width:%?235?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0;font-size:%?28?%;color:#fff;background:#f2270c}.iconclose[data-v-33b7d3ae]{position:absolute;top:%?24?%;right:%?24?%;width:%?32?%;height:%?32?%;font-size:%?20?%;background:#999;border-radius:50%;line-height:%?34?%;text-align:center;color:#fff}.pintuan-rule-parent[data-v-33b7d3ae] .uni-popup__wrapper{border-radius:%?30?% %?30?% %?0?% %?0?%}.pintuan-rule[data-v-33b7d3ae] .uni-popup__wrapper{border-radius:%?30?% %?30?% %?0?% %?0?%}.page-title[data-v-33b7d3ae]{width:%?360?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;text-align:center}[data-v-33b7d3ae] .uni-navbar{position:fixed;z-index:999}.grouping-swiper[data-v-33b7d3ae]{position:fixed;left:0;bottom:calc(%?100?% + env(safe-area-inset-bottom));bottom:calc(%?100?% + constant(safe-area-inset-bottom));width:100%;height:%?90?%}.grouping-in-progress[data-v-33b7d3ae]{display:flex;justify-content:space-between;align-items:center;width:100%;background-color:#fded88;padding:%?12?% %?24?%;box-sizing:border-box;height:%?90?%}.grouping-in-progress-left[data-v-33b7d3ae]{display:flex;align-items:center}.grouping-in-progress-left-head[data-v-33b7d3ae]{width:%?66?%;height:%?66?%;border-radius:50%}.grouping-in-progress-left-name[data-v-33b7d3ae]{color:red;font-size:%?24?%;margin-left:%?16?%;display:flex;align-items:center}.grouping-in-progress-left-name uni-text[data-v-33b7d3ae]:first-child{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:%?240?%}.grouping-in-progress-left-name uni-text[data-v-33b7d3ae]:last-child{margin-left:%?16?%}.grouping-in-progress-right-btn[data-v-33b7d3ae]{margin:0;height:%?54?%;line-height:%?54?%;background:#f84346;border-radius:%?27?%;font-size:%?28?%;font-weight:500;color:#fff}.goods-action-button-share[data-v-33b7d3ae]{font-size:%?26?%;color:#fff;background:#f65d72;line-height:%?34?%;height:%?80?%;box-sizing:border-box;margin:0;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?20?%}',""]),t.exports=e},"764d":function(t,e,i){var a=i("b81d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("36126566",a,!0,{sourceMap:!1,shadowMode:!1})},7841:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"action-icon-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickEvent.apply(void 0,arguments)}}},[t.imgicon?i("v-uni-image",{staticClass:"image-icon",attrs:{src:t.imgicon}}):i("v-uni-view",{staticClass:"iconfont",class:t.icon}),i("v-uni-text",[t._v(t._s(t.text))]),t.cornerMark.length?i("v-uni-view",{staticClass:"corner-mark",style:{background:t.cornerMarkBg,color:t.cornerMarkColor}},[t._v(t._s(t.cornerMark))]):t._e()],1)},o=[]},7858:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-847fc110]{width:100%;text-align:center}.share-popup .share-title[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-title[data-v-847fc110]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center;background:#f5f5f5}.share-popup .share-content[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content[data-v-847fc110]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;background:#f5f5f5}.share-popup .share-content .share-box[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-847fc110]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-847fc110]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-847fc110]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-847fc110]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-847fc110]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-847fc110],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-847fc110]{color:#07c160}.share-popup .share-footer[data-v-847fc110],\r\n.uni-popup__wrapper-box .share-footer[data-v-847fc110]{height:%?88?%;line-height:%?88?%;border-top:%?2?% #f5f5f5 solid;text-align:center;color:#666}.canvas[data-v-847fc110]{width:%?620?%;height:%?917?%;margin:0 auto;overflow:hidden;position:fixed;left:100%}.poster[data-v-847fc110]{display:flex;justify-content:center}@-webkit-keyframes spin-data-v-847fc110{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-847fc110{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-847fc110]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-847fc110]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-847fc110]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-847fc110]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-847fc110]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-847fc110 .6s linear normal infinite;animation:spin-data-v-847fc110 .6s linear normal infinite}.loading-anim .in[data-v-847fc110]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-847fc110 .8s linear infinite;animation:spin-data-v-847fc110 .8s linear infinite}.loading-anim .mid[data-v-847fc110]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-847fc110 .6s linear infinite;animation:spin-data-v-847fc110 .6s linear infinite}',""]),t.exports=e},"78f85":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),i("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),i("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),i("v-uni-view",{staticClass:"uni-countdown__number",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"7aef":function(t,e,i){"use strict";var a=i("f1bf"),o=i.n(a);o.a},"7cd6":function(t,e,i){"use strict";i.r(e);var a=i("68d5"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"7d2f8":function(t,e,i){var a=i("18f7e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("00c0b360",a,!0,{sourceMap:!1,shadowMode:!1})},"82c2":function(t,e,i){"use strict";i.r(e);var a=i("c01b"),o=i("2e65");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("a24d");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"7aa735bd",null,!1,a["a"],void 0);e["default"]=r.exports},8319:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"ns-goods-action-button",props:{text:{type:String,default:""},textPrice:{type:String,default:""},background:{type:String,default:""},backgroundClass:{type:String,default:""},disabled:{type:Boolean,default:!1},disabledText:{type:String,default:""}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},methods:{clickEvent:function(){this.$emit("click")}}};e.default=a},"834b":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6c836e7e]{width:100%;text-align:center}.action-icon-wrap[data-v-6c836e7e]{display:flex;flex-direction:column;justify-content:center;height:%?100?%;min-width:%?96?%;text-align:center;position:relative;margin-right:%?6?%}.action-icon-wrap uni-button[data-v-6c836e7e]{width:100%;height:100%;position:absolute;border:none;z-index:1;padding:0;margin:0;background:none}.action-icon-wrap uni-button[data-v-6c836e7e]::after{border:none!important}.action-icon-wrap .iconfont[data-v-6c836e7e]{margin:0 auto %?10?%;color:#323233;line-height:1;font-size:%?32?%}.action-icon-wrap .corner-mark[data-v-6c836e7e]{position:absolute;z-index:5;font-size:%?24?%;top:%?6?%;right:%?6?%;box-sizing:border-box;color:#fff;\r\n  /* \tpadding: 1rpx 6rpx; */line-height:1;border-radius:50%;width:%?30?%;height:%?30?%;display:flex;justify-content:center;align-items:center;background-color:var(--custom-brand-color)}.action-icon-wrap uni-text[data-v-6c836e7e]{font-size:%?24?%;line-height:1}.image-icon[data-v-6c836e7e]{width:%?38?%;height:%?38?%;display:block;margin:0 auto;padding-bottom:%?7?%}',""]),t.exports=e},"8a64":function(t,e,i){"use strict";i.r(e);var a=i("7841"),o=i("7cd6");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("ef2c");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"6c836e7e",null,!1,a["a"],void 0);e["default"]=r.exports},"908b":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"[data-v-847fc110] .uni-popup__wrapper.bottom{background:none!important}[data-v-847fc110] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0}",""]),t.exports=e},"91e0":function(t,e,i){"use strict";var a=i("9404"),o=i.n(a);o.a},9404:function(t,e,i){var a=i("908b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("e5810390",a,!0,{sourceMap:!1,shadowMode:!1})},9489:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.list&&t.list.length>0?i("v-uni-view",{staticClass:"more-goodies"},[t.title?i("v-uni-view",{staticClass:"more-goodies-header"},[i("v-uni-image",{staticClass:"more-goodies-header-img",attrs:{src:t.$util.img("public/static/youpin/goods/more-goodies.png")}}),i("v-uni-text",{staticClass:"more-goodies-header-title"},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"more-goodies-list"},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"more-goodies-list-one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-image",{staticClass:"more-goodies-list-one-img",attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,t.list)}}}),i("v-uni-view",{staticClass:"more-goodies-list-one-info"},[i("v-uni-view",{staticClass:"more-goodies-list-one-info-title"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"more-goodies-list-one-info-two"},[i("v-uni-text",{staticClass:"more-goodies-list-one-info-two-price"},[i("v-uni-text",{staticClass:"more-goodies-list-one-info-two-price"},[t._v("￥")]),t._v(t._s(e.retail_price))],1),i("v-uni-text",{staticClass:"more-goodies-list-one-info-two-drawing"},[t._v("￥"+t._s(e.market_price))])],1)],1)],1)})),1)],1):t._e()},o=[]},"9a7f":function(t,e,i){var a=i("528d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("d3405a38",a,!0,{sourceMap:!1,shadowMode:!1})},"9d90":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[t.imagePath?t._e():i("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width*t.canvasOptions.scale+"px",height:t.canvasOptions.height*t.canvasOptions.scale+"px",borderRadius:t.canvasOptions.borderRadius*t.canvasOptions.scale},attrs:{"canvas-id":"myCanvas"}})],1)},o=[]},"9fe9":function(t,e,i){"use strict";i.r(e);var a=i("f45e"),o=i("360e");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("d475");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"a49c4d4a",null,!1,a["a"],void 0);e["default"]=r.exports},"9ff2":function(t,e,i){"use strict";i.r(e);var a=i("8319"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},a24d:function(t,e,i){"use strict";var a=i("7d2f8"),o=i.n(a);o.a},a9cf:function(t,e,i){"use strict";var a=i("5c37"),o=i.n(a);o.a},aa42:function(t,e,i){"use strict";var a=i("fc1c"),o=i.n(a);o.a},ac31:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"custom flex-start-center"},[t.timeData.day>=1?i("v-uni-view",{staticClass:"day"},[t._v(t._s(t.timeData.day))]):t._e(),t.timeData.day>=1?i("v-uni-view",{staticClass:"day-symbol"},[t._v(t._s(t.showColon||t.showDaySymbol?"天":":"))]):t._e(),i("v-uni-view",{staticClass:"hour"},[t._v(t._s(t._f("fillWithZero")(t.timeData.hour)))]),i("v-uni-view",{staticClass:"hour-symbol"},[t._v(t._s(t.showColon?"时":":"))]),i("v-uni-view",{staticClass:"minute"},[t._v(t._s(t._f("fillWithZero")(t.timeData.minute)))]),i("v-uni-view",{staticClass:"minute-symbol"},[t._v(t._s(t.showColon?"分":":"))]),i("v-uni-view",{staticClass:"second"},[t._v(t._s(t._f("fillWithZero")(t.timeData.second)))]),i("v-uni-view",{staticClass:"second-symbol"},[t._v(t._s(t.showColon?"秒":""))])],1)],1)},o=[]},ae57:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("c9b5"),i("bf0f"),i("ab80");var a={props:{time:{type:Number,default:0},autoStart:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},showDaySymbol:{type:Boolean,default:!1}},data:function(){return{timer:null,timeData:{remain:0,day:0,hour:0,minute:0,second:0}}},watch:{time:function(){this.reset()}},filters:{fillWithZero:function(t){var e=t.toString().length;while(e<2)t="0"+t,e++;return t}},methods:{updateTimeData:function(){var t=this.timeData.remain;this.timeData.day=Math.floor(t/1e3/60/60/24),this.timeData.hour=Math.floor(t/1e3/60/60%24),this.timeData.minute=Math.floor(t/1e3/60%60),this.timeData.second=Math.floor(t/1e3%60)},reset:function(){var t=this;this.timer&&this.timer.stop(),this.timeData.remain=this.time,this.updateTimeData(),this.timer=new this.$util.AdjustingInterval((function(){t.timeData.remain-=1e3,t.timeData.remain<=0?(t.timeData.day="00",t.timeData.hour="00",t.timeData.minute="00",t.timeData.second="00"):t.updateTimeData()}),1e3,this.time/1e3,(function(){t.$emit("finish")})),this.autoStart&&this.timer.start()},start:function(){this.timer||this.timer.start()}},mounted:function(){this.reset()},beforeDestroy:function(){}};e.default=a},b332:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0def30ac]{width:100%;text-align:center}.more-goodies[data-v-0def30ac]{width:100%;padding:0 %?20?%;padding-top:%?20?%;box-sizing:border-box;background-color:#fff;margin-bottom:%?20?%}.more-goodies-header[data-v-0def30ac]{margin-bottom:%?20?%;display:flex;align-items:center}.more-goodies-header-img[data-v-0def30ac]{width:%?32?%;height:%?32?%}.more-goodies-header-title[data-v-0def30ac]{font-size:%?30?%;font-weight:400;letter-spacing:0;line-height:%?44?%;color:#383838;margin-left:%?14?%}.more-goodies-list[data-v-0def30ac]{width:100%;display:flex;justify-content:space-between;flex-wrap:wrap}.more-goodies-list-one[data-v-0def30ac]{width:%?346?%;border-radius:%?20?%;background:#fff;box-shadow:%?0?% %?4?% %?12?% rgba(0,0,0,.05);margin-bottom:%?20?%}.more-goodies-list-one-img[data-v-0def30ac]{width:100%;height:%?346?%;border-radius:%?20?% %?20?% 0 0}.more-goodies-list-one-info[data-v-0def30ac]{padding:%?10?% %?10?% %?20?% %?10?%;box-sizing:border-box}.more-goodies-list-one-info-title[data-v-0def30ac]{font-size:%?28?%;font-weight:400;line-height:%?32?%;color:#383838;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.more-goodies-list-one-info-two[data-v-0def30ac]{display:flex;align-items:baseline;margin-top:%?10?%}.more-goodies-list-one-info-two-price[data-v-0def30ac]{font-size:%?36?%;font-weight:700;line-height:%?42.2?%;color:var(--custom-brand-color)}.more-goodies-list-one-info-two-price-symbal[data-v-0def30ac]{font-size:%?32?%}.more-goodies-list-one-info-two-drawing[data-v-0def30ac]{font-size:%?20?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6;margin-left:%?14?%}',""]),t.exports=e},b373:function(t,e,i){"use strict";var a=i("9a7f"),o=i.n(a);o.a},b81d:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-d6071282]{width:100%;text-align:center}.uni-countdown[data-v-d6071282]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-d6071282]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?28?%}.uni-countdown__splitor.day[data-v-d6071282]{line-height:%?50?%}.uni-countdown__number[data-v-d6071282]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},bb0b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0}},shareType:{type:String}},data:function(){return{isIPhoneX:!1,ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,height:0,windowHeight:0,windowWidth:0,bottomPadding:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.getSafeArea(),this.ctx=uni.createCanvasContext("myCanvas",this)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var i=0;i<this.drawPathQueue.length;i++)for(var a=0;a<this.drawPathQueue.length;a++){var o=this.drawPathQueue[a];if(o.index===i){if("text"===o.type){if(this.ctx.setFillStyle(o.color||"#000"),this.ctx.setFontSize(o.size*this.myPx),o.textBaseline){var n=o.textBaselineColor||"#999999";this.ctx.strokeStyle=n,this.ctx.moveTo(o.x,o.y-5),this.ctx.lineTo(this.ctx.measureText(o.text).width+20,o.y-5),this.ctx.stroke(),this.ctx.textBaseline=o.textBaseline}if(o.width&&o.text){for(var s=o.text.split(""),r="",d=[],u=o.width,l=o.lineNum||1,c=o.lineHeight||20,p=0;p<s.length;p++)this.ctx.measureText(r).width<u&&this.ctx.measureText(r+s[p]).width<=u?(r+=s[p],p==s.length-1&&d.push(r)):(d.push(r),r=s[p]);if(l<=d.length)for(var f=0;f<l;f++)o.fontWeight?(this.ctx.fillText(d[f],o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(d[f],o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(d[f],o.x*this.myPx,o.y*this.myPx),o.y=o.y+c;else for(var h=0;h<d.length;h++)o.fontWeight?(this.ctx.fillText(d[h],o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(d[h],o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(d[h],o.x*this.myPx,o.y*this.myPx),o.y=o.y+c}else o.fontWeight?(this.ctx.fillText(o.text,o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(o.text,o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(o.text,o.x*this.myPx,o.y*this.myPx);this.counter--}if("image"===o.type){if(o.path)if(o.radius){var g=2*o.radius,v=o.x+o.radius,b=o.y+o.radius;this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(v,b,o.radius,0,2*Math.PI),this.ctx.clip(),this.ctx.drawImage(o.path,o.x*this.myPx,o.y*this.myPx,g*this.myPx,g*this.myPx),this.ctx.restore()}else this.ctx.drawImage(o.path,o.x*this.myPx,o.y*this.myPx,o.width*this.myPx,o.height*this.myPx);else this.ctx.fillStyle=o.background,this.ctx.fillRect(o.x*this.myPx,o.y*this.myPx,o.width*this.myPx,o.height*this.myPx);this.counter--}}}},counter:function(t,e){var i=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){i.imagePath=t.savedFilePath,i.isShowLoading=!1}})},fail:function(t){console.log("err",t)}},i)}),100))}},methods:{open:function(){this.isIPhoneX=this.$util.isIPhoneX(),this.$refs.sharePopup.open(),this.imagePath||(this.isShowLoading=!0,this.generateImg())},closeSharePopup:function(){this.$refs.sharePopup.close()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var i=t.drawQueue[e];if(i.index=e,"text"===i.type)return t.drawPathQueue.push(i),"continue";i.path?uni.getImageInfo({src:i.path,success:function(e){i.path=e.path,t.drawPathQueue.push(i)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(i)},i=0;i<this.drawQueue.length;i++)e(i)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})},getSafeArea:function(){var t=uni.getSystemInfoSync();this.bottomPadding=t.screenHeight-t.safeArea.bottom}}};e.default=a},be6f:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"ns-goods-action",props:{safeArea:{type:Boolean,default:!1}}};e.default=a},be88:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0,scale:1}}},data:function(){return{ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,ctx_share:null,height:0,windowHeight:0,windowWidth:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.ctx=uni.createCanvasContext("myCanvas",this),this.ctx.scale(this.canvasOptions.scale,this.canvasOptions.scale)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var i=0;i<this.drawPathQueue.length;i++)for(var a=0;a<this.drawPathQueue.length;a++){var o=this.drawPathQueue[a];if(o.index===i){if("text"===o.type){if(this.ctx.setFillStyle(o.color||"#000"),this.ctx.setFontSize(o.size*this.myPx),o.textBaseline){var n=o.textBaselineColor||"#999999";this.ctx.strokeStyle=n,this.ctx.moveTo(o.x-4,o.y-8),this.ctx.lineTo(o.x+this.ctx.measureText(o.text).width+4,o.y-8),this.ctx.stroke(),this.ctx.textBaseline=o.textBaseline}if(o.width&&o.text){for(var s=o.text.split(""),r="",d=[],u=o.width,l=o.lineNum||1,c=o.lineHeight||20,p=0;p<s.length;p++)this.ctx.measureText(r).width<u&&this.ctx.measureText(r+s[p]).width<=u?(r+=s[p],p==s.length-1&&d.push(r)):(d.push(r),r=s[p]);if(l<=d.length)for(var f=0;f<l;f++)o.fontWeight?(this.ctx.fillText(d[f],o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(d[f],o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(d[f],o.x*this.myPx,o.y*this.myPx),o.y=o.y+c;else for(var h=0;h<d.length;h++)o.fontWeight?(this.ctx.fillText(d[h],o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(d[h],o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(d[h],o.x*this.myPx,o.y*this.myPx),o.y=o.y+c}else o.fontWeight?(this.ctx.fillText(o.text,o.x*this.myPx,o.y*this.myPx+.5),this.ctx.fillText(o.text,o.x*this.myPx+.5,o.y*this.myPx)):this.ctx.fillText(o.text,o.x*this.myPx,o.y*this.myPx);this.counter--}"image"===o.type&&(o.path?this.ctx.drawImage(o.path,o.x*this.myPx,o.y*this.myPx,o.width*this.myPx,o.height*this.myPx):(this.ctx.fillStyle=o.background,this.ctx.fillRect(o.x*this.myPx,o.y*this.myPx,o.width*this.myPx,o.height*this.myPx)),this.counter--)}}},counter:function(t,e){var i=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",destWidth:i.canvasOptions.width,destHeight:i.canvasOptions.height,success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){i.imagePath=t.savedFilePath,i.$emit("childByValue",t.savedFilePath)}})},fail:function(t){console.log("err",t)}},i)}),100))}},methods:{open:function(){this.imagePath||this.generateImg()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var i=t.drawQueue[e];if(i.index=e,"text"===i.type)return t.drawPathQueue.push(i),"continue";console.log("current.path====",i.path),i.path?uni.getImageInfo({src:i.path,success:function(e){i.path=e.path,t.drawPathQueue.push(i)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(i)},i=0;i<this.drawQueue.length;i++)e(i)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})}}};e.default=a},bf01:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.activeData.message?i("v-uni-view",{staticClass:"rolling-order",style:t.style,attrs:{animation:t.animationData}},[i("v-uni-image",{staticClass:"rolling-order-head",attrs:{src:t.activeData.headimg?t.$util.img(t.activeData.headimg):t.$util.getDefaultImage().default_headimg,mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.activeData.headimg=t.$util.getDefaultImage().default_headimg}}}),i("v-uni-text",{staticClass:"rolling-order-text"},[t._v(t._s(t.activeData.message))])],1):t._e()},o=[]},bf93:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("2634")),n=a(i("2fdc"));i("64aa"),i("c223");var s={name:"diy-floating-rolling-order",props:{top:{type:[String],default:function(){return"280rpx"}},left:{type:[String],default:function(){return"24rpx"}},zIndex:{type:Number,default:function(){return 888}},intervalsTime:{type:Number,default:function(){return 3e3}},positionType:{type:String,default:function(){return"index_banner"}},sleepStart:{type:Number,default:function(){return 4e3}}},data:function(){return{ordersList:[],activeIndex:0,activeData:{},animationData:{},animation:null,duration:1e3,timeOutOne:null,timeOutTwo:null}},computed:{style:function(){return"top: ".concat(this.top,";left: ").concat(this.left,";z-index:").concat(this.zIndex,";")}},created:function(){},mounted:function(){var t=this;this.sleepStart?setTimeout((function(){t.switchMessage()}),this.sleepStart):this.switchMessage()},beforeDestroy:function(){clearTimeout(this.timeOutOne),clearTimeout(this.timeOutTwo)},methods:{contentAnimation:function(){var t=this;this.activeData=this.ordersList[this.activeIndex],this.animation=uni.createAnimation({duration:this.duration,timingFunction:"linear"}),this.animation.opacity(1).step(),this.animationData=this.animation.export(),this.timeOutOne=setTimeout((0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.animation.opacity(0).step(),t.animationData=t.animation.export(),t.activeIndex+=1,!(t.activeIndex>=t.ordersList.length)){e.next=6;break}return e.next=6,t.getData();case 6:t.timeOutTwo=setTimeout((function(){t.contentAnimation()}),t.duration+t.intervalsTime);case 7:case"end":return e.stop()}}),e)}))),this.duration+this.intervalsTime)},switchMessage:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getData();case 2:if(!(t.ordersList.length<1)){e.next=4;break}return e.abrupt("return");case 4:t.contentAnimation();case 5:case"end":return e.stop()}}),e)})))()},getData:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.activeIndex=0,e.prev=1,e.next=4,t.$api.sendRequest({url:"sign_detail"==t.positionType?t.$apiUrl.getSignDriftMessageUrl:t.$apiUrl.driftMessageUrl,async:!1,data:{position:t.positionType}});case 4:i=e.sent,0==i.code&&(t.ordersList=i.data),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,null,[[1,8]])})))()}}};e.default=s},c01b:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("5e99").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"sharePopup",staticClass:"share-popup",attrs:{type:"bottom","bottom-radius":!0}},[i("template",{attrs:{slot:"container"},slot:"container"},[t.imagePath?t._e():i("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width+"px",height:t.canvasOptions.height+"px",borderRadius:t.canvasOptions.borderRadius},attrs:{"canvas-id":"myCanvas"}}),i("v-uni-view",{staticClass:"poster",style:{"margin-top":t.isIPhoneX?"80rpx":""}},[t.imagePath?i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:!t.isShowLoading,expression:"!isShowLoading"}],staticClass:"canvas",style:{width:t.canvasOptions.width+"rpx",height:t.canvasOptions.height+"rpx",borderRadius:t.canvasOptions.borderRadius},attrs:{src:t.imagePath,mode:""}}):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowLoading,expression:"isShowLoading"}],staticClass:"loading-layer"},[i("v-uni-view",{staticClass:"loading-anim"},[i("v-uni-view",{staticClass:"box item"},[i("v-uni-view",{staticClass:"border out item ns-border-color-top ns-border-color-left"})],1)],1)],1)],1),i("v-uni-view",[i("v-uni-view",{staticClass:"share-title"},[t._v("分享到")]),i("v-uni-view",{staticClass:"share-content"},[i("v-uni-view",{staticClass:"share-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveImage.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/goods/save-image.png"),mode:""}}),i("v-uni-text",[t._v("保存图片")])],1)],1)],1),i("v-uni-view",{staticClass:"share-footer",class:{"share-footer-padding":t.bottomPadding<1},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSharePopup.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("取消分享")])],1)],1)],2)],1)],1)},n=[]},c08b:function(t,e,i){"use strict";i.r(e);var a=i("be88"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},c4ba:function(t,e,i){var a=i("834b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("71c84cd0",a,!0,{sourceMap:!1,shadowMode:!1})},c64f:function(t,e,i){var a=i("748c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("f309560e",a,!0,{sourceMap:!1,shadowMode:!1})},c95c:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"[data-v-33b7d3ae] .uni-video-cover{background:none}[data-v-33b7d3ae] .uni-video-cover-duration{display:none}[data-v-33b7d3ae] .uni-video-cover-play-button{border-radius:50%;border:%?4?% solid #fff;width:%?120?%;height:%?120?%;background-size:30%}.poster-layer[data-v-33b7d3ae] .uni-popup__wrapper-box{max-height:none!important}[data-v-33b7d3ae] .sku-layer .uni-popup__wrapper-box{overflow-y:initial!important}.goods-discount .countdown .clockrun[data-v-33b7d3ae] .uni-countdown__number{min-width:%?32?%;height:%?32?%;text-align:center;line-height:%?32?%;background:#000;\n\t/* // #690b08 */border-radius:4px;display:inline-block;padding:%?4?%;margin:0;border:none}.goods-discount .countdown .clockrun[data-v-33b7d3ae] .uni-countdown__splitor{width:%?10?%;height:%?32?%;line-height:%?36?%;text-align:center;display:inline-block;color:#000}.goods-discount .countdown .clockrun[data-v-33b7d3ae] .uni-countdown__splitor.day{width:auto}[data-v-33b7d3ae] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{max-height:unset!important}[data-v-33b7d3ae] .goods-action-button.active1{padding-left:10px}[data-v-33b7d3ae] .goods-action-button.active2{padding-right:10px}[data-v-33b7d3ae] .goods-action-button.active3{padding:0 10px}[data-v-33b7d3ae] .goods-action-button.active4{padding:0 10px}[data-v-33b7d3ae] .goods-action-button.active1 .action-buttom-wrap{color:#f2270c;border:1px solid #f2270c;border-radius:%?40?%;box-sizing:border-box;margin-right:%?14?%}[data-v-33b7d3ae] .goods-action-button.active2 .action-buttom-wrap{border-radius:%?40?%;box-sizing:border-box}[data-v-33b7d3ae] .goods-action-button.active3 .action-buttom-wrap{border-radius:36px;margin:%?20?% 0}[data-v-33b7d3ae] .goods-action-button.active4 .action-buttom-wrap{border-radius:36px}\n\n/* 底部分享按钮 */.distributor-share-button[data-v-33b7d3ae]{width:auto!important;height:auto!important;border:none;margin:0;line-height:auto;padding:0}.disabled-share-btn[data-v-33b7d3ae]{background-color:initial!important}.to-top[data-v-33b7d3ae]{width:%?144?%;height:%?152?%;position:fixed;right:0;bottom:%?380?%}",""]),t.exports=e},ca20:function(t,e,i){"use strict";i.r(e);var a=i("49a0"),o=i("9ff2");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("7aef");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"6c61c027",null,!1,a["a"],void 0);e["default"]=r.exports},d3b8:function(t,e,i){"use strict";i.r(e);var a=i("ac31"),o=i("31c3");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=r.exports},d40e:function(t,e,i){"use strict";var a=i("c64f"),o=i.n(a);o.a},d475:function(t,e,i){"use strict";var a=i("6531"),o=i.n(a);o.a},e085:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-33b7d3ae]{width:100%;text-align:center}.go-top[data-v-33b7d3ae]{position:fixed;right:%?30?%;bottom:%?220?%;z-index:1;background:#fff;padding:%?10?%;border:1px solid;border-radius:20px;width:%?57?%;height:%?270?%;text-align:center;font-size:%?24?%}.go-top .goods-share[data-v-33b7d3ae],\r\n.go-top .collection[data-v-33b7d3ae]{margin-bottom:%?10?%;font-size:%?24?%}.go-top .icontop[data-v-33b7d3ae]{font-size:%?40?%}.goods-detail[data-v-33b7d3ae]{height:100%;padding-bottom:%?100?%;padding-bottom:calc(%?100?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?100?% + env(safe-area-inset-bottom))}.goods-media[data-v-33b7d3ae]{width:100%;position:relative;overflow:hidden}.goods-media[data-v-33b7d3ae]:after{padding-top:100%;display:block;content:""}.goods-media .over[data-v-33b7d3ae]{width:%?200?%;height:%?200?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.goods-media .pop-video[data-v-33b7d3ae]{line-height:1}.goods-media .goods-img[data-v-33b7d3ae],\r\n.goods-media .goods-video[data-v-33b7d3ae]{position:absolute;width:100%;height:100%;left:0;top:0;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;transition-timing-function:cubic-bezier(0,0,.25,1);transition-duration:.35s;-webkit-transform:translateZ(0);transform:translateZ(0)}.goods-media .goods-img .video-img[data-v-33b7d3ae],\r\n.goods-media .goods-video .video-img[data-v-33b7d3ae]{width:100%;height:100%;position:relative}.goods-media .goods-img uni-image[data-v-33b7d3ae],\r\n.goods-media .goods-video uni-image[data-v-33b7d3ae]{width:100%;height:100%}.goods-media .goods-img .video-open[data-v-33b7d3ae],\r\n.goods-media .goods-video .video-open[data-v-33b7d3ae]{width:100%;height:100%;position:absolute;left:0;top:0;display:flex;justify-content:center;align-items:center}.goods-media .goods-img .video-open .iconfont[data-v-33b7d3ae],\r\n.goods-media .goods-video .video-open .iconfont[data-v-33b7d3ae]{font-size:%?90?%;color:#fff}.goods-media .goods-img[data-v-33b7d3ae]{-webkit-transform:translateX(100%);transform:translateX(100%)}.goods-media .goods-video[data-v-33b7d3ae]{-webkit-transform:translateX(-100%);transform:translateX(-100%)}.goods-media .goods-img.show[data-v-33b7d3ae],\r\n.goods-media .goods-video.show[data-v-33b7d3ae]{-webkit-transform:translateX(0);transform:translateX(0)}.goods-media .goods-img .swiper[data-v-33b7d3ae]{width:100%;height:100%}.goods-media .goods-img .swiper .item[data-v-33b7d3ae]{width:100%;height:100%}.goods-media .goods-img .swiper uni-image[data-v-33b7d3ae]{width:100%;height:100%}.goods-media .goods-img .img-indicator-dots[data-v-33b7d3ae]{position:absolute;z-index:5;bottom:%?40?%;right:%?40?%;background:hsla(0,0%,39.2%,.6);color:#fff;font-size:%?24?%;line-height:%?40?%;border-radius:%?20?%;padding:0 %?20?%}.goods-media .goods-video uni-video[data-v-33b7d3ae]{width:100%;height:100%}.goods-media .goods-video .uni-video-cover[data-v-33b7d3ae]{background:none}.goods-media .media-mode[data-v-33b7d3ae]{position:absolute;width:100%;z-index:5;bottom:%?40?%;text-align:center;line-height:%?50?%}.goods-media .media-mode uni-text[data-v-33b7d3ae]{background:hsla(0,0%,39.2%,.6);color:#fff;font-size:%?24?%;line-height:%?50?%;border-radius:%?50?%;padding:0 %?30?%;display:inline-block}.goods-media .media-mode uni-text[data-v-33b7d3ae]:last-child{margin-left:%?40?%}.group-wrap[data-v-33b7d3ae]{margin-bottom:%?20?%;position:relative;background-color:#fff}.group-wrap-padding[data-v-33b7d3ae]{padding:0 %?20?%;box-sizing:border-box}uni-button.group-wrap-share[data-v-33b7d3ae]{z-index:99;margin:0;position:fixed;right:%?21?%;bottom:%?280?%;padding:%?8?% 0;line-height:1;box-sizing:border-box;border:none;width:%?98?%;height:%?98?%;background:linear-gradient(0deg,#fd3c3b,#ff6c6a);box-shadow:0 0 %?14?% %?4?% rgba(216,47,41,.31);border-radius:50%;font-size:%?20?%;font-weight:400;color:#fff;display:flex;flex-direction:column;justify-content:center;align-items:center;padding-bottom:%?8?%}uni-button.group-wrap-share .group-wrap-share-icon[data-v-33b7d3ae]{font-size:%?44?%;color:#fff;line-height:1;margin-bottom:%?10?%}.goods-module-wrap[data-v-33b7d3ae]{background-color:#fff;display:flex;justify-content:space-between;align-items:center;border-radius:%?40?% %?40?% 0 0;margin-top:%?-68?%}.goods-module-wrap > uni-view[data-v-33b7d3ae]{width:%?590?%;box-sizing:border-box;padding:%?10?% %?21?%}.goods-module-wrap.discount[data-v-33b7d3ae]{padding-top:%?20?%}.goods-module-wrap .goods-module-wrap-box[data-v-33b7d3ae]{display:flex;justify-content:space-between;align-items:center}.goods-module-wrap .price-symbol[data-v-33b7d3ae]{font-size:%?36?%;position:relative;top:%?4?%;margin-right:%?10?%;font-weight:700}.goods-module-wrap .price[data-v-33b7d3ae]{font-size:%?48?%;position:relative;top:%?4?%;margin-right:%?10?%;font-weight:700}.goods-module-wrap .market-price-symbol[data-v-33b7d3ae]{position:relative;top:%?4?%;text-decoration:line-through;color:#a6a6a6}.goods-module-wrap .market-price[data-v-33b7d3ae]{position:relative;top:%?4?%;margin-right:%?20?%;color:#a6a6a6;text-decoration:line-through}.goods-module-wrap .sku-name[data-v-33b7d3ae]{font-weight:700;font-size:%?32?%}.goods-module-wrap .sku-name-tuan[data-v-33b7d3ae]{width:%?70?%;height:%?32?%;line-height:%?32?%;vertical-align:text-top;text-align:center;background:linear-gradient(90deg,#fb331d,#fe5838);border-radius:%?4?%;font-size:%?20?%;font-weight:500;color:#fff;display:inline-block;box-sizing:border-box;margin-right:%?6?%}.goods-module-wrap .sku-name[data-v-33b7d3ae],\r\n.goods-module-wrap .introduction[data-v-33b7d3ae]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.goods-module-wrap .introduction[data-v-33b7d3ae]{margin-bottom:%?20?%}.goods-module-wrap .sku-subsidy[data-v-33b7d3ae]{display:flex;flex-direction:column;justify-content:center;align-items:center;width:%?130?%;height:%?90?%;background:#fff3f3;border-radius:%?4?%;padding:0;margin-right:%?25?%}.goods-module-wrap .sku-subsidy-one[data-v-33b7d3ae]{font-size:%?24?%;font-weight:500;color:#333}.goods-module-wrap .sku-subsidy-two[data-v-33b7d3ae]{font-size:%?24?%;font-weight:700;color:#f84346}.goods-module-wrap .adds-wrap[data-v-33b7d3ae]{display:flex}.goods-module-wrap .adds-wrap uni-text[data-v-33b7d3ae]{flex:1;font-size:%?24?%;color:#999;text-align:center}.goods-module-wrap .adds-wrap uni-text[data-v-33b7d3ae]:first-of-type{text-align:left}.goods-module-wrap .adds-wrap uni-text[data-v-33b7d3ae]:last-of-type{text-align:right}.goods-module-wrap .adds-wrap uni-text.adds-wrap-volume[data-v-33b7d3ae]{font-size:%?26?%;font-weight:500;color:#999;text-align:left}.goods-cell[data-v-33b7d3ae]{display:flex;align-items:center;background:#fff;height:%?80?%;line-height:%?80?%;justify-content:space-between;box-shadow:0 %?1?% %?3?% 0 hsla(0,0%,93.3%,.8);position:relative;box-sizing:border-box}.goods-cell[data-v-33b7d3ae]:not(:first-child){border-top:%?2?% solid #f5f5f5}.goods-cell .tit[data-v-33b7d3ae]{color:#999;font-size:%?28?%;margin-right:%?10?%}.goods-cell .box[data-v-33b7d3ae]{width:90%;font-size:%?28?%;line-height:inherit;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-cell .iconfont[data-v-33b7d3ae]{color:#bbb;font-size:%?28?%}.goods-cell.service .box[data-v-33b7d3ae]{display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;-webkit-box-pack:center;overflow:hidden;word-break:break-all}.goods-cell.service .box uni-text[data-v-33b7d3ae]::after{content:" · ";display:inline-block}.goods-cell.service .box uni-text[data-v-33b7d3ae]:last-child::after{content:""}.shop-wrap[data-v-33b7d3ae]{padding:%?20?%;background:#fff}.shop-wrap .box[data-v-33b7d3ae]{display:flex}.shop-wrap .shop-logo[data-v-33b7d3ae]{width:%?120?%;height:%?120?%;border-radius:%?10?%;border:%?1?% solid #e7e7e7}.shop-wrap .shop-logo uni-image[data-v-33b7d3ae]{width:100%;height:100%}.shop-wrap .shop-info[data-v-33b7d3ae]{padding-left:%?20?%;flex:1}.shop-wrap .shop-info .description[data-v-33b7d3ae]{color:#a6a6a6}.shop-wrap .shop-score[data-v-33b7d3ae]{margin-top:%?20?%}.shop-wrap .shop-score uni-text[data-v-33b7d3ae]{flex:1;text-align:center;color:#999}.shop-wrap .goods-action[data-v-33b7d3ae]{margin-top:%?20?%;text-align:center;width:100%}.shop-wrap .goods-action uni-navigator[data-v-33b7d3ae]{display:inline-block;line-height:%?40?%;padding:%?2?% %?40?%;border:1px solid #fff;border-radius:%?40?%}.shop-wrap .goods-action uni-navigator[data-v-33b7d3ae]:last-of-type{margin-left:%?30?%}.goods-evaluate[data-v-33b7d3ae]{padding:%?20?%;background:#fff}.goods-evaluate .tit[data-v-33b7d3ae]{padding-bottom:%?20?%;display:flex;align-items:center}.goods-evaluate .tit uni-view[data-v-33b7d3ae]{flex:1;line-height:%?40?%;text-align:left}.goods-evaluate .tit uni-navigator[data-v-33b7d3ae]{text-align:right}.goods-evaluate .tit uni-navigator .iconfont[data-v-33b7d3ae]{font-size:%?24?%}.goods-evaluate .evaluate-item .evaluator[data-v-33b7d3ae]{display:flex;align-items:center}.goods-evaluate .evaluate-item .evaluator .evaluator-face[data-v-33b7d3ae]{width:%?50?%;height:%?50?%;border-radius:50%;overflow:hidden}.goods-evaluate .evaluate-item .evaluator .evaluator-face uni-image[data-v-33b7d3ae]{width:100%;height:100%}.goods-evaluate .evaluate-item .evaluator .evaluator-name[data-v-33b7d3ae]{margin-left:%?20?%;color:#999}.goods-evaluate .evaluate-item .cont[data-v-33b7d3ae]{text-align:justify;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;-webkit-box-pack:center;overflow:hidden;word-break:break-all}.goods-evaluate .evaluate-item .evaluate-img[data-v-33b7d3ae]{display:inline-flex}.goods-evaluate .evaluate-item .evaluate-img .img-box[data-v-33b7d3ae]{width:%?100?%;height:%?100?%;overflow:hidden;margin:0 %?20?% %?20?% 0}.goods-evaluate .evaluate-item .evaluate-img .img-box uni-image[data-v-33b7d3ae]{width:100%;height:100%}.goods-evaluate .evaluate-item .time[data-v-33b7d3ae]{color:#999}.goods-evaluate .evaluate-item .time uni-text[data-v-33b7d3ae]{margin-right:%?20?%}.goods-evaluate .evaluate-item-empty[data-v-33b7d3ae]{width:100%;height:%?130?%;display:flex;justify-content:center;align-items:center;color:#a6a6a6}.goods-action-button[data-v-33b7d3ae]{flex:1}.line[data-v-33b7d3ae]{height:1px}.goods-coupon[data-v-33b7d3ae]{position:relative}.goods-coupon .get-coupon[data-v-33b7d3ae]{border:1px solid #fff;border-radius:%?20?%;display:block;height:%?42?%;width:%?84?%;position:absolute;top:50%;right:%?20?%;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:%?24?%}.goods-coupon-popup-layer[data-v-33b7d3ae]{background:#fff;height:%?800?%}.goods-coupon-popup-layer .tax-title[data-v-33b7d3ae]{text-align:center;font-size:%?32?%;line-height:%?120?%;height:%?120?%;display:block;font-weight:700;position:relative}.goods-coupon-popup-layer .tax-title uni-text[data-v-33b7d3ae]{position:absolute;float:right;right:22px;font-size:%?40?%;font-weight:500}.goods-coupon-popup-layer .coupon-body[data-v-33b7d3ae]{position:absolute;left:0;right:0;height:65%}.goods-coupon-popup-layer .coupon-body .body-item[data-v-33b7d3ae]{width:%?702?%;height:%?130?%;margin:0 auto;border:1px solid #fff;margin-bottom:%?28?%;border-radius:%?10?%;display:flex;justify-content:flex-start;align-items:center;overflow:hidden;padding-right:%?18?%;border:1px solid rgba(0,0,0,.1)!important}.goods-coupon-popup-layer .coupon-body .body-item .item-price[data-v-33b7d3ae]{width:%?240?%;height:100%;border-right:1px dashed rgba(0,0,0,.2);display:flex;flex-direction:column;align-items:center;justify-content:center;line-height:1;color:#000}.goods-coupon-popup-layer .coupon-body .body-item .item-price .price[data-v-33b7d3ae]{font-size:%?28?%;line-height:1}.goods-coupon-popup-layer .coupon-body .body-item .item-price .price .price-num[data-v-33b7d3ae]{font-size:%?48?%}.goods-coupon-popup-layer .coupon-body .body-item .item-price .sub[data-v-33b7d3ae]{font-size:%?20?%;color:#000;line-height:1;margin-top:%?14?%}.goods-coupon-popup-layer .coupon-body .body-item .item-info[data-v-33b7d3ae]{flex:1;height:100%;display:flex;justify-content:space-between;align-items:center}.goods-coupon-popup-layer .coupon-body .body-item .item-info .info-box[data-v-33b7d3ae]{height:100%;display:flex;flex-direction:column;padding-left:%?23?%;box-sizing:border-box;display:flex;flex-direction:column;justify-content:center}.goods-coupon-popup-layer .coupon-body .body-item .item-info .info-box .sub[data-v-33b7d3ae]{font-size:%?24?%;color:#000;line-height:1}.goods-coupon-popup-layer .coupon-body .body-item .item-info .info-box .sub[data-v-33b7d3ae]:nth-child(2){color:#ababab;margin-top:%?18?%}.goods-coupon-popup-layer .coupon-body .body-item .item-info .item-btn[data-v-33b7d3ae]{width:%?90?%;height:%?48?%;color:#fff;border-radius:%?24?%;float:right;display:flex;align-items:center;justify-content:center;font-size:%?24?%;opacity:.7;line-height:1}.goods-coupon-popup-layer .coupon-body .free_div[data-v-33b7d3ae]{height:%?40?%}.goods-coupon-popup-layer .coupon-body .item[data-v-33b7d3ae]{overflow:hidden;margin:0 %?20?% %?20?%;border-radius:%?12?%;display:flex}.goods-coupon-popup-layer .coupon-body .item[data-v-33b7d3ae]:last-child{margin-top:0}.goods-coupon-popup-layer .coupon-body .item .main[data-v-33b7d3ae]{flex:1;padding:%?20?% 0 %?20?% %?20?%}.goods-coupon-popup-layer .coupon-body .item .main .price[data-v-33b7d3ae]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.goods-coupon-popup-layer .coupon-body .item .main .price .money[data-v-33b7d3ae]{font-size:%?48?%;font-weight:700}.goods-coupon-popup-layer .coupon-body .item .main .sub[data-v-33b7d3ae]{font-size:%?24?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.goods-coupon-popup-layer .coupon-body .item .tax-split[data-v-33b7d3ae]{border:1px dotted;position:relative;border-right:0}.goods-coupon-popup-layer .coupon-body .item .tax-split[data-v-33b7d3ae]::before{content:"";position:absolute;width:10px;height:10px;background:#fff;border-radius:50%;left:-5px;top:-5px;z-index:1}.goods-coupon-popup-layer .coupon-body .item .tax-split[data-v-33b7d3ae]::after{content:"";position:absolute;width:10px;height:10px;background:#fff;border-radius:50%;left:-5px;bottom:-5px;z-index:1}.goods-coupon-popup-layer .coupon-body .item .tax-operator[data-v-33b7d3ae]{flex:0.4;text-align:center;height:%?220?%;line-height:%?220?%;font-size:%?28?%}.goods-coupon-popup-layer .button-box[data-v-33b7d3ae]{width:100%;position:absolute;bottom:0;z-index:1;margin-bottom:%?20?%!important}.goods-merchants-service-popup-layer[data-v-33b7d3ae]{background:#fff;height:%?660?%}.goods-merchants-service-popup-layer .tax-title[data-v-33b7d3ae]{text-align:center;font-size:%?32?%;line-height:%?120?%;height:%?120?%;display:block;font-weight:700}.goods-merchants-service-popup-layer uni-scroll-view[data-v-33b7d3ae]{position:absolute;left:0;right:0;height:65%}.goods-merchants-service-popup-layer uni-scroll-view .item[data-v-33b7d3ae]{padding:%?20?% %?40?%;position:relative}.goods-merchants-service-popup-layer uni-scroll-view .item .iconfont[data-v-33b7d3ae]{vertical-align:top;display:inline-block;margin-right:%?20?%;font-size:%?32?%}.goods-merchants-service-popup-layer uni-scroll-view .item .info-wrap[data-v-33b7d3ae]{display:inline-block;vertical-align:middle;width:90%}.goods-merchants-service-popup-layer uni-scroll-view .item .info-wrap .title[data-v-33b7d3ae]{display:block;font-size:%?28?%;font-weight:700}.goods-merchants-service-popup-layer uni-scroll-view .item .info-wrap .describe[data-v-33b7d3ae]{font-size:%?24?%;color:#a6a6a6;display:block;padding:%?10?% 0}.goods-merchants-service-popup-layer .button-box[data-v-33b7d3ae]{width:100%;position:absolute;bottom:0;z-index:1;margin-bottom:%?20?%!important}.goods-attribute-popup-layer[data-v-33b7d3ae]{background:#fff;height:%?660?%}.goods-attribute-popup-layer .title[data-v-33b7d3ae]{font-size:%?32?%;line-height:%?120?%;height:%?120?%;display:block;font-weight:700;padding-left:%?20?%}.goods-attribute-popup-layer .goods-attribute-body[data-v-33b7d3ae]{position:absolute;left:0;right:0;height:60%}.goods-attribute-popup-layer .goods-attribute-body .item[data-v-33b7d3ae]{padding:%?20?%;border-bottom:1px solid}.goods-attribute-popup-layer .goods-attribute-body .item .value[data-v-33b7d3ae]{margin-left:%?20?%}.goods-attribute-popup-layer .goods-attribute-body .item[data-v-33b7d3ae]:last-child{border-bottom:0}.goods-attribute-popup-layer .button-box[data-v-33b7d3ae]{position:absolute;bottom:0;z-index:1;margin-bottom:%?20?%!important;width:100%}.manjian-popup-layer[data-v-33b7d3ae]{background:#fff;height:%?660?%}.manjian-popup-layer .title[data-v-33b7d3ae]{font-size:%?32?%;line-height:%?120?%;height:%?120?%;display:block;font-weight:700;padding-left:%?20?%;text-align:center}.manjian-popup-layer .manjian-body[data-v-33b7d3ae]{position:absolute;left:0;right:0;height:60%}.manjian-popup-layer .manjian-body .item[data-v-33b7d3ae]{padding:%?20?% %?30?%;display:flex;align-items:center}.manjian-popup-layer .manjian-body .item .manjian-icon[data-v-33b7d3ae]{display:inline-block;font-size:%?20?%;color:#fff;border-radius:%?50?%;padding:%?6?% %?10?%;line-height:1}.manjian-popup-layer .manjian-body .item .value[data-v-33b7d3ae]{margin-left:%?20?%}.manjian-popup-layer .manjian-body .item[data-v-33b7d3ae]:last-child{border-bottom:0}.manjian-popup-layer .button-box[data-v-33b7d3ae]{width:100%;position:absolute;bottom:0;z-index:1;margin-bottom:%?20?%!important}.combo-goods-wrap[data-v-33b7d3ae]{display:flex;align-items:center;padding:%?20?%;background:#f7f7f7}.combo-goods-wrap .goods[data-v-33b7d3ae]{width:25%;display:inline-block;border:1px solid;margin-right:5%;position:relative}.combo-goods-wrap .goods[data-v-33b7d3ae]:first-child{margin-right:0}.combo-goods-wrap .goods uni-image[data-v-33b7d3ae]{max-width:100%;display:block;position:relative;width:100%;height:%?160?%}.combo-goods-wrap .goods uni-text[data-v-33b7d3ae]{height:%?40?%;line-height:%?40?%;background:rgba(89,86,86,.7);color:#fff;position:absolute;bottom:0;width:100%;text-align:center;font-size:%?24?%}.combo-goods-wrap .iconfont[data-v-33b7d3ae]{width:%?100?%;text-align:center;font-size:%?50?%;font-weight:700}.bundling-popup-layer[data-v-33b7d3ae]{background:#fff;height:%?660?%}.bundling-popup-layer .title[data-v-33b7d3ae]{font-size:%?32?%;line-height:%?120?%;height:%?120?%;display:block;font-weight:700;padding-left:%?20?%}.bundling-popup-layer .bundling-body[data-v-33b7d3ae]{position:absolute;left:0;right:0;height:60%}.bundling-popup-layer .bundling-body uni-scroll-view[data-v-33b7d3ae]{width:100%;white-space:nowrap;box-sizing:border-box}.bundling-popup-layer .bundling-body uni-scroll-view .item[data-v-33b7d3ae]{padding:%?20?%;border-bottom:1px solid}.bundling-popup-layer .bundling-body uni-scroll-view .item .right[data-v-33b7d3ae]{vertical-align:middle}.bundling-popup-layer .bundling-body uni-scroll-view .item .right uni-text[data-v-33b7d3ae]{vertical-align:middle}.bundling-popup-layer .bundling-body uni-scroll-view .item .right .iconright[data-v-33b7d3ae]{color:#bbb}.bundling-popup-layer .bundling-body uni-scroll-view .item .value[data-v-33b7d3ae]{margin-bottom:%?10?%;display:flex;justify-content:space-between;align-items:center}.bundling-popup-layer .bundling-body uni-scroll-view .item .goods-wrap .iconfont[data-v-33b7d3ae]{width:%?100?%;text-align:center;font-size:%?50?%;font-weight:700;display:inline-block;vertical-align:middle}.bundling-popup-layer .bundling-body uni-scroll-view .item .goods-wrap .goods[data-v-33b7d3ae]{width:25%;display:inline-block;margin-right:%?20?%;position:relative;vertical-align:middle}.bundling-popup-layer .bundling-body uni-scroll-view .item .goods-wrap .goods[data-v-33b7d3ae]:first-child{margin-right:0}.bundling-popup-layer .bundling-body uni-scroll-view .item .goods-wrap .goods uni-image[data-v-33b7d3ae]{max-width:100%;display:block;position:relative;width:100%;height:%?160?%}.bundling-popup-layer .bundling-body uni-scroll-view .item .goods-wrap .goods uni-text[data-v-33b7d3ae]{height:%?40?%;line-height:%?40?%;background:rgba(89,86,86,.7);color:#fff;position:absolute;bottom:0;width:100%;text-align:center;font-size:%?24?%}.bundling-popup-layer .button-box[data-v-33b7d3ae]{width:100%;position:absolute;bottom:0;z-index:1;margin-bottom:%?20?%!important}.goods-detail-tab[data-v-33b7d3ae]{width:100%}.goods-detail-tab .detail-tab[data-v-33b7d3ae]{width:100%;position:fixed;left:0;top:0;z-index:2;background-color:#fff}.goods-detail-tab .detail-tab .tab-item[data-v-33b7d3ae]{height:%?70?%;color:#999;line-height:%?70?%;box-sizing:border-box}.goods-detail-tab .detail-tab .tab-item.active[data-v-33b7d3ae]{position:relative;color:#333;font-weight:700}.goods-detail-tab .detail-tab .tab-item.active[data-v-33b7d3ae]::after{content:"";display:inline-block;width:%?40?%;height:%?8?%;position:absolute;left:50%;bottom:0;-webkit-transform:translateX(-50%);transform:translateX(-50%);border-radius:%?8?%}.goods-detail-tab .detail-tab .tab-item[data-v-33b7d3ae]:not(:first-child){margin-left:%?100?%}.goods-detail-tab .detail-content[data-v-33b7d3ae]{width:100%}.goods-detail-tab .detail-content-item[data-v-33b7d3ae]{background:#fff;box-shadow:0 %?2?% %?3?% 0 hsla(0,0%,93.3%,.8);padding-top:%?40?%}.goods-detail-tab .goods-details-title[data-v-33b7d3ae]{display:flex;justify-content:center;align-items:center}.goods-detail-tab .goods-details-title uni-view[data-v-33b7d3ae]{font-size:%?28?%;font-weight:500;color:#333}.goods-detail-tab .goods-details-title uni-view[data-v-33b7d3ae]:first-child{margin-right:%?14?%;width:%?90?%;height:%?2?%;background-color:#eee}.goods-detail-tab .goods-details-title uni-view[data-v-33b7d3ae]:last-child{margin-left:%?14?%;width:%?90?%;height:%?2?%;background-color:#eee}.goods-detail-tab .goods-details[data-v-33b7d3ae]{padding:%?20?%;margin-bottom:%?20?%;overflow:hidden;box-sizing:border-box}.goods-detail-tab .goods-details.active[data-v-33b7d3ae]{min-height:%?150?%;width:100%;display:flex;justify-content:center;align-items:center;color:#a6a6a6}.poster-layer .generate-poster[data-v-33b7d3ae]{padding:%?40?% 0}.poster-layer .generate-poster .iconfont[data-v-33b7d3ae]{font-size:%?80?%;color:#07c160;line-height:normal}.poster-layer .generate-poster > uni-view[data-v-33b7d3ae]{text-align:center}.poster-layer .generate-poster > uni-view[data-v-33b7d3ae]:last-child{margin-top:%?20?%}.poster-layer .image-wrap[data-v-33b7d3ae]{width:70%;margin:30px auto 20px auto;box-shadow:0 0 16px hsla(0,0%,39.2%,.3)}.poster-layer .image-wrap uni-image[data-v-33b7d3ae]{width:100%;height:100%;height:%?750?%}.poster-layer .msg[data-v-33b7d3ae]{padding:%?40?%}.poster-layer .save[data-v-33b7d3ae]{text-align:center;height:%?80?%;line-height:%?80?%}.poster-layer .close[data-v-33b7d3ae]{position:absolute;top:0;right:%?20?%;width:%?40?%;height:%?80?%;font-size:%?50?%}.share-popup .share-title[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-title[data-v-33b7d3ae]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center}.share-popup .share-content[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content[data-v-33b7d3ae]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%}.share-popup .share-content .share-box[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-33b7d3ae]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-33b7d3ae]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-text[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-33b7d3ae]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-33b7d3ae]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-33b7d3ae],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-33b7d3ae]{color:#07c160}.share-popup .share-footer[data-v-33b7d3ae],\r\n.uni-popup__wrapper-box .share-footer[data-v-33b7d3ae]{height:%?90?%;line-height:%?90?%;border-top:%?2?% #f5f5f5 solid;text-align:center;color:#666}.selected-sku-spec .box uni-text[data-v-33b7d3ae]{margin-right:%?10?%;white-space:nowrap;overflow:hidden;margin-right:10px;text-overflow:ellipsis}.goods-discount[data-v-33b7d3ae]{position:relative;height:%?120?%;overflow:hidden}.goods-discount .price-info[data-v-33b7d3ae]{position:relative;margin-right:%?250?%;height:%?120?%}.goods-discount .price-info .discount-price[data-v-33b7d3ae]{padding:%?12?% 0 0 %?25?%;height:%?60?%;line-height:%?60?%;white-space:nowrap;overflow:hidden;font-size:%?52?%;margin-bottom:%?8?%;color:#fff}.goods-discount .price-info .discount-price .symbol[data-v-33b7d3ae]{margin-right:%?4?%;font-size:%?24?%}.goods-discount .price-info .original-price[data-v-33b7d3ae]{height:%?32?%;line-height:%?32?%;padding-left:%?25?%;font-size:%?24?%;white-space:nowrap;overflow:hidden;color:#fff}.goods-discount .price-info .original-price .price[data-v-33b7d3ae]{text-decoration:line-through;opacity:.85}.goods-discount .price-info .sale-num[data-v-33b7d3ae]{margin-left:%?20?%}.goods-discount .countdown[data-v-33b7d3ae]{position:absolute;right:%?10?%;top:50%;width:%?230?%;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.goods-discount .countdown .txt[data-v-33b7d3ae]{height:%?32?%;text-align:center;line-height:%?32?%;font-size:%?24?%;color:#9e495b}.goods-discount .countdown .clockrun[data-v-33b7d3ae]{margin-top:%?10?%;height:%?40?%;line-height:%?40?%;text-align:center;font-size:%?24?%;color:#fff}.distribution-guest[data-v-33b7d3ae]{position:absolute;top:%?-100?%;left:%?25?%}.popup-dialog[data-v-33b7d3ae]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-33b7d3ae]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-33b7d3ae]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-33b7d3ae]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-33b7d3ae]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-33b7d3ae]{color:#f2270c;background:#fff;border:%?1?% solid #f2270c}.popup-dialog .popup-dialog-footer .button.red[data-v-33b7d3ae]{color:#fff;background:#f2270c}.buyer-info[data-v-33b7d3ae]{width:100%;background-color:#fff;margin-bottom:%?20?%}.buyer-info .buyer-info-title[data-v-33b7d3ae]{display:flex;align-items:center;justify-content:space-between;padding:%?24?% %?24?% %?16?%;font-size:%?28?%;color:#333}.buyer-info .buyer-info-title > uni-view[data-v-33b7d3ae]:first-child{font-weight:700}.buyer-info .buyer-info-title > uni-view[data-v-33b7d3ae]:last-child{color:#666;font-size:%?24?%}.buyer-info .buyers-box[data-v-33b7d3ae]{display:flex;padding:6px 0 12px;overflow-x:auto;z-index:111111;width:%?736?%}.buyer-info .buyers-box .buyers-list[data-v-33b7d3ae]{width:%?400?%;height:%?160?%;padding:0 %?24?%;display:flex;align-items:center;background-color:#f5f5f5;border-radius:%?8?%;margin-left:%?24?%}.buyer-info .buyers-box .buyers-list .buyers-list-left[data-v-33b7d3ae]{flex:1;height:100%}.buyer-info .buyers-box .buyers-list .buyers-list-left .buyers-list-left-top[data-v-33b7d3ae]{display:flex;margin:%?18?% 0 %?10?%}.buyer-info .buyers-box .buyers-list .buyers-list-left .buyers-list-left-top > uni-image[data-v-33b7d3ae]{width:%?48?%;height:%?48?%;border-radius:50%}.buyer-info .buyers-box .buyers-list .buyers-list-left .buyers-list-left-top > uni-view[data-v-33b7d3ae]{width:%?200?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:%?26?%;color:#999;margin-left:%?10?%}.buyer-info .buyers-box .buyers-list .buyers-list-left .buyers-list-left-bottom[data-v-33b7d3ae]{padding-right:%?24?%;font-size:%?28?%;color:#333;line-height:%?32?%;word-break:break-all;text-overflow:ellipsis;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.buyer-info .buyers-box .buyers-list .buyers-list-right[data-v-33b7d3ae]{width:%?120?%;height:%?120?%;border-radius:%?8?%}.buyer-info .buyers-not[data-v-33b7d3ae]{width:100%;padding:6px 0 12px;box-sizing:border-box;display:flex;flex-direction:column;justify-content:center;align-content:center}.buyer-info .buyers-not-tip[data-v-33b7d3ae]{font-size:%?24?%;color:#666;text-align:center}.buyer-info .buyers-not-op[data-v-33b7d3ae]{display:inline-flex;align-items:center;justify-content:center;width:%?180?%;height:%?44?%;line-height:%?44?%;border:1px solid #f33;border-radius:%?44?%;color:#f33;background-color:#fff;font-size:%?24?%;margin:0 auto;margin-top:%?16?%}[data-v-33b7d3ae] ._img{display:block}.ns-text-color[data-v-33b7d3ae]{color:#ff1010!important}.sku-name-box[data-v-33b7d3ae]{width:100%;position:relative}.showCopybox[data-v-33b7d3ae]{position:absolute;top:%?-66?%;left:45%}.showCopybox .copytext[data-v-33b7d3ae]{text-align:center;border-radius:%?10?%;color:#fff;font-size:%?24?%;position:relative}.showCopybox .copytext[data-v-33b7d3ae]::after{content:"";display:block;width:%?20?%;height:%?20?%;background:#1f2022;position:absolute;bottom:%?-16?%;left:%?30?%;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.showCopybox .fuzhi[data-v-33b7d3ae]{border-right:1px solid hsla(0,0%,100%,.7);padding:%?16?% %?24?%;background:#1f2022;border-radius:5px 0 0 5px}.showCopybox .quxiao[data-v-33b7d3ae]{padding:%?16?% %?24?%;background:#1f2022;border-radius:0 5px 5px 0}',""]),t.exports=e},ecc6:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},created:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,i,a){return 60*t*60*24+60*e*60+60*i+a},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,i=0,a=0,o=0;t>0?(e=Math.floor(t/86400),i=Math.floor(t/3600)-24*e,a=Math.floor(t/60)-24*e*60-60*i,o=Math.floor(t)-24*e*60*60-60*i*60-60*a):this.timeUp(),e<10&&(e="0"+e),i<10&&(i="0"+i),a<10&&(a="0"+a),o<10&&(o="0"+o),this.d=e,this.h=i,this.i=a,this.s=o}}};e.default=a},ef2c:function(t,e,i){"use strict";var a=i("c4ba"),o=i.n(a);o.a},f1bf:function(t,e,i){var a=i("17d3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("0d3f6258",a,!0,{sourceMap:!1,shadowMode:!1})},f45e:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"ns-goods-action bottom-safe-area"},[this._t("default")],2)},o=[]},f8de:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),o={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){a.isOnXianMaiApp&&this.appCurrentPages<=1?(0,a.goClosePage)("0"):uni.navigateBack()}}};e.default=o},f99f:function(t,e,i){"use strict";i.r(e);var a=i("9489"),o=i("5fa3");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("606f");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"0def30ac",null,!1,a["a"],void 0);e["default"]=r.exports},f9a5:function(t,e,i){"use strict";i.r(e);var a=i("bf01"),o=i("4d82");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("b373");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"38dab624",null,!1,a["a"],void 0);e["default"]=r.exports},fb52:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("9fe9")),n=a(i("8a64")),s=a(i("ca20")),r=a(i("5e99")),d=a(i("4d57")),u=a(i("c799")),l=a(i("5ab1")),c=a(i("d3b8")),p=a(i("2e45")),f=a(i("3b27")),h=a(i("2d01")),g=a(i("014b")),v=a(i("2f73")),b=a(i("f8de")),m=a(i("d817")),w=a(i("de74")),x=a(i("f9a5")),_=a(i("172f")),y=i("4b89"),k=a(i("f99f")),C={components:{diyGoodsDetailMoreGoodies:k.default,mphtml:_.default,uniIcons:w.default,nsGoodsAction:o.default,nsGoodsActionIcon:n.default,nsGoodsActionButton:s.default,uniPopup:r.default,nsGoodsSku:d.default,nsGoodsRecommend:u.default,uniCountDown:l.default,countdownTimer:c.default,diyShare:g.default,diyShareNavigateH5:v.default,uniNavBar:m.default,diyFloatingRollingOrder:x.default},data:function(){return{isShowEvaluate:!1,isShowDetailTab:!1,isOnXianMaiApp:y.isOnXianMaiApp,copytextShow:!1}},onShow:function(){uni.getStorageSync("is_register")&&(this.$util.toShowCouponPopup(this),uni.removeStorageSync("is_register"))},computed:{Development:function(){return!1},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit}},mixins:[p.default,f.default,h.default,b.default],methods:{longpress:function(){this.copytextShow=!0},copyCallback:function(){this.copytextShow=!1}}};e.default=C},fc1c:function(t,e,i){var a=i("e085");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("0f28f8bb",a,!0,{sourceMap:!1,shadowMode:!1})},fd7b:function(t,e,i){var a=i("c95c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("011c7ef8",a,!0,{sourceMap:!1,shadowMode:!1})},fe2a:function(t,e,i){"use strict";i.r(e);var a=i("ecc6"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a}}]);