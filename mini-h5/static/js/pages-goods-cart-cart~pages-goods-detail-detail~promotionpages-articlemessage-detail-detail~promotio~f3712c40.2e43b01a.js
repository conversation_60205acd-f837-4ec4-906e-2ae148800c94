(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-cart-cart~pages-goods-detail-detail~promotionpages-articlemessage-detail-detail~promotio~f3712c40"],{"0d02":function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(i("2634")),a=o(i("2fdc")),s={data:function(){return{list:[],img:["public/static/youpin/coupon_bg_1.png","public/static/youpin/coupon_bg_2.png","public/static/youpin/coupon_bg_3.png","public/static/youpin/coupon_bg_4.png"],boxClass:["box1","box2","box3","box4"]}},onLoad:function(){this.$util.toShowCouponPopup(this)},methods:{open:function(){this.listInfo()},listInfo:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.use_remind,async:!1});case 3:i=e.sent,i.data.length&&(t.list=i.data,t.$refs.coupon.open()),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toGoodList:function(t){this.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:t.goodscoupon_type_id})}}};e.default=s},1858:function(t,e,i){"use strict";var o=i("4086"),n=i.n(o);n.a},"313a":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniPopup:i("5e99").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("uni-popup",{ref:"coupon",attrs:{custom:!0,"mask-click":!1}},[t.list.length?i("v-uni-view",{staticClass:"coupon-model",class:t.list.length<4?t.boxClass[t.list.length-1]:t.boxClass[3],style:{"background-image":"url("+(t.list.length<4?t.$util.img(t.img[t.list.length-1]):t.$util.img(t.img[3]))+")"}},[i("v-uni-view",{staticClass:"coupon-header"},[i("v-uni-view",{staticClass:"title"},[t._v("恭喜您获得以下优惠券")]),i("v-uni-view",{staticClass:"tip"},[t._v("马上去使用吧！")])],1),i("v-uni-view",{staticClass:"coupon-box"},t._l(t.list,(function(e,o){return i("v-uni-view",{key:o,staticClass:"coupon-list",style:{"background-image":"url("+t.$util.img("public/static/youpin/coupon_border.png")+")"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toGoodList(e)}}},[i("v-uni-view",{staticClass:"left"},[i("v-uni-view",{staticClass:"info"},[i("v-uni-view",[t._v("¥")]),e.money<100?[0==e.money.split(".")[1]?i("v-uni-view",[t._v(t._s(e.money.split(".")[0]))]):t._e(),e.money.split(".")[1]>0?i("v-uni-view",[t._v(t._s(e.money.split(".")[0])+"."),i("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])]):t._e()]:i("v-uni-view",{staticClass:"money-thousand"},[t._v(t._s(e.money.split(".")[0])+"."),i("span",{staticClass:"point-class"},[t._v(t._s(e.money.split(".")[1]))])])],2)],1),i("v-uni-view",{staticClass:"right"},[i("v-uni-view",[i("v-uni-view",{staticClass:"name"},[t._v(t._s(e.desc))]),i("v-uni-view",{staticClass:"time h5-time"},[t._v("有效期至"+t._s(e.end_time))])],1),i("v-uni-view",{staticClass:"btn"},[i("v-uni-view",{staticClass:"h5-btn"},[t._v("去使用")])],1)],1)],1)})),1)],1):t._e(),i("v-uni-image",{staticClass:"pop-ad-info-close",attrs:{src:t.$util.img("public/static/youpin/icon-close-overlay.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.coupon.close()}}})],1)],1)},a=[]},3922:function(t,e,i){"use strict";(function(t){i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966"),i("dc8a");var n=o(i("c3f5")),a=(o(i("57bc")),o(i("7c8d"))),s=o(i("fc35")),r={name:"ns-goods-sku",components:{uniPopup:n.default,nsGoodsDiscount:s.default},props:{goodsDetail:{type:Object,default:null},pintuanInfo:{type:Object,default:null},disabled:{type:Boolean,default:!1},entrance:{type:String,default:""},periodId:{type:String,default:""},isShowNumChoose:{type:Boolean,default:!0},limitBuy:{type:Boolean,default:!1},comboId:{type:String}},data:function(){return{systemInfo:{},number:1,token:"",btnSwitch:!1,type:"",callback:null,skuId:0,pintuanId:0,limitNumber:0,preview:0,minNumber:0,entranceType:this.entrance,IsNotSkip:!0,goodsSkuDetail:{},skuIsDisabledStatus:{},seckill_price:null}},created:function(){if(this.systemInfo=uni.getSystemInfoSync(),this.token=uni.getStorageSync("token"),this.goodsDetail&&this.goodsDetail.goods_spec_format)for(var t=0;t<this.goodsDetail.goods_spec_format.length;t++)for(var e=0;e<this.goodsDetail.goods_spec_format[t].value.length;e++)this.$set(this.skuIsDisabledStatus,this.goodsDetail.goods_spec_format[t].value[e].sku_id,this.goodsDetail.goods_spec_format[t].value[e].disabled)},watch:{goodsDetail:function(t){t&&(this.skuId=t.sku_id,t.numControl&&(this.number=1))}},methods:{discountOpen:function(){this.$refs.discountPopup.open()},popupChange:function(t){t.show||this.$emit("closeModel")},setLimitNumber:function(){"pintuan"==this.type&&this.goodsDetail.pintuan_id?this.limitNumber=this.goodsDetail.buy_num:"groupbuy"==this.type&&this.goodsDetail.groupbuy_id?(this.number=this.goodsDetail.buy_num,this.minNumber=this.goodsDetail.buy_num):"seckill"==this.type?this.limitNumber=this.goodsDetail.buy_num:"combo_buy"==this.type&&(this.limitNumber=this.goodsDetail.buy_nums)},show:function(e,i){var o=this;"choose_spec"==e&&(this.entranceType="choose_spec"),t((function(){o.$refs.skuPopup.open(),o.$emit("openModel"),o.type=e,o.callback=i,o.skuId=o.goodsDetail.sku_id,o.preview=o.goodsDetail.preview||0,o.setLimitNumber(),"seckill"==o.type&&null==o.seckill_price&&(o.seckill_price=o.goodsDetail.show_price)}))},hide:function(){this.$refs.skuPopup.close()},change:function(t,e){if(!this.disabled&&t!=this.skuId){this.btnSwitch=!1,this.skuId=t;for(var i=0;i<this.goodsDetail.goods_spec_format.length;i++)for(var o=this.goodsDetail.goods_spec_format[i],n=0;n<o.value.length;n++)e==this.goodsDetail.goods_spec_format[i].value[n].spec_id&&(this.goodsDetail.goods_spec_format[i].value[n].selected=!1);this.goodsDetail.pintuan_id?this.getPintuanGoodsSkuInfo():this.goodsDetail.groupbuy_id&&this.getGroupbuyGoodsSkuInfo(),"combo_buy"==this.type?this.getComboSkuInfo():this.getGoodsSkuInfo()}},getGoodsSkuInfo:function(){var t=this,e={sku_id:this.skuId},i=this.$store.state.share_member_id;i&&(e.share_member_id=i),this.number>1&&(e.nums=this.number);this.$api.sendRequest({url:"/api/goodssku/info",data:e,success:function(e){var i=e.data;null!=i&&i instanceof Object?(t.goodsSkuDetail=i,t.dealData(),1==t.goodsSkuDetail.promotion_type&&(t.goodsSkuDetail.discountTimeMachine=t.$util.countDown(t.goodsSkuDetail.end_time-e.timestamp)),"seckill"==t.type&&t.seckill_price&&(t.goodsSkuDetail.show_price=t.seckill_price,t.goodsSkuDetail.price=t.seckill_price),t.btnSwitch=!1,t.$emit("refresh",t.goodsSkuDetail)):t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")},fail:function(e){t.btnSwitch=!1,t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}})},getPintuanGoodsSkuInfo:function(){var t=this;this.$api.sendRequest({url:"/pintuan/api/goods/info",data:{sku_id:this.skuId,pintuan_id:this.goodsDetail.pintuan_id},success:function(e){var i=e.data;null!=i?(t.goodsSkuDetail=i,t.dealData(),t.goodsSkuDetail.show_price=t.goodsDetail.group_id>0?t.goodsSkuDetail.promotion_price:t.goodsSkuDetail.pintuan_price,t.goodsSkuDetail.save_price=t.goodsSkuDetail.price-t.goodsSkuDetail.show_price>0?(t.goodsSkuDetail.price-t.goodsSkuDetail.show_price).toFixed(2):0,t.goodsSkuDetail.end_time-e.timestamp>0?t.goodsSkuDetail.timeMachine=t.$util.countDown(t.goodsSkuDetail.end_time-e.timestamp):(t.$util.showToast({title:"活动已结束"}),setTimeout((function(){t.$util.redirectTo("/pages/goods/detail/detail",{sku_id:t.goodsSkuDetail.sku_id},"redirectTo")}),1e3)),t.btnSwitch=!1,t.$emit("refresh",t.goodsSkuDetail)):t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")},fail:function(e){t.btnSwitch=!1,t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}})},getGroupbuyGoodsSkuInfo:function(){var t=this;this.$api.sendRequest({url:"/groupbuy/api/goods/info",data:{sku_id:this.skuId,id:this.goodsDetail.groupbuy_id},success:function(e){var i=e.data;null!=i?(t.goodsSkuDetail=i,t.dealData(),t.goodsSkuDetail.show_price=t.goodsDetail.groupbuy_price,t.goodsSkuDetail.save_price=t.goodsSkuDetail.price-t.goodsSkuDetail.show_price>0?(t.goodsSkuDetail.price-t.goodsSkuDetail.show_price).toFixed(2):0,t.goodsSkuDetail.end_time-e.timestamp>0?t.goodsSkuDetail.timeMachine=t.$util.countDown(t.goodsSkuDetail.end_time-e.timestamp):(t.$util.showToast({title:"活动已结束"}),setTimeout((function(){t.$util.redirectTo("/pages/goods/detail/detail",{sku_id:t.goodsSkuDetail.sku_id},"redirectTo")}),1e3)),t.btnSwitch=!1,t.$emit("refresh",t.goodsSkuDetail)):t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")},fail:function(e){t.btnSwitch=!1,t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}})},getComboSkuInfo:function(){var t=this,e={sku_id:this.skuId,combo_id:this.comboId};this.$api.sendRequest({url:this.$apiUrl.comboSkuInfoUrl,data:e,success:function(e){var i=e.data;null!=i&&i instanceof Object?(t.goodsSkuDetail=i,t.dealData(),t.btnSwitch=!1,t.$emit("refresh",t.goodsSkuDetail)):t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")},fail:function(e){t.btnSwitch=!1,t.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}})},dealData:function(){this.goodsSkuDetail.sku_images=this.goodsSkuDetail.sku_images?this.goodsSkuDetail.sku_images.split(","):[],this.goodsSkuDetail.show_price=this.goodsSkuDetail.discount_price,this.goodsSkuDetail.sku_spec_format&&(this.goodsSkuDetail.sku_spec_format=JSON.parse(this.goodsSkuDetail.sku_spec_format)),this.goodsSkuDetail.goods_spec_format&&(this.goodsSkuDetail.goods_spec_format=JSON.parse(this.goodsSkuDetail.goods_spec_format)),this.$forceUpdate(),this.keyInput(!0)},changeNum:function(t){if(this.isShowNumChoose&&0!=this.goodsDetail.stock){var e=this.goodsDetail.stock,i=1;if("pintuan"==this.type&&this.goodsDetail.pintuan_id?e=this.goodsDetail.buy_num>this.goodsDetail.stock?this.goodsDetail.stock:this.limitNumber:"groupbuy"==this.type&&this.goodsDetail.groupbuy_id?(e=this.goodsDetail.buy_num>this.goodsDetail.stock?this.goodsDetail.stock:this.limitNumber,i=this.limitNumber):"seckill"==this.type&&(e=this.goodsDetail.buy_num>this.goodsDetail.stock?this.goodsDetail.stock:this.limitNumber),"+"==t){if(this.limitBuy)return;if(!(this.number<e))return;this.number++}else if("-"==t){if(this.limitBuy)return;if(!(this.number>i))return;this.number-=1}this.getGoodsSkuInfo()}},blur:function(){var t=this,e=parseInt(this.number);this.number=0,setTimeout((function(){t.number=e,t.getGoodsSkuInfo()}),0)},keyInput:function(t,e){var i=this;setTimeout((function(){var o=i.goodsDetail.stock;0!=i.goodsDetail.stock?(t&&0==i.number.length&&(i.number=1),t&&(i.number<=0||isNaN(i.number))&&(i.number=1),"pintuan"==i.type&&i.goodsDetail.pintuan_id&&i.number>i.limitNumber||"groupbuy"==i.type&&i.goodsDetail.groupbuy_id&&i.number<i.limitNumber||"seckill"==i.type&&i.number>i.limitNumber||"combo_buy"==i.type&&i.number>i.limitNumber?i.number=i.limitNumber:i.number>o&&(i.number=o),t&&(i.number=parseInt(i.number)),e&&e()):i.number=0}),0)},confirm:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.preview?this.$util.showToast({title:"预览商品无法购买"}):""!=this.token?(e&&(this.type=e),this.setLimitNumber(),"seckill"==this.type&&null==this.seckill_price&&(this.seckill_price=this.goodsDetail.show_price),this.keyInput(!0,(function(){if(0!=t.goodsDetail.stock)if(0!=t.number.length&&0!=t.number){if(!t.btnSwitch)if(t.btnSwitch=!0,"join_cart"==t.type)t.$api.sendRequest({url:"/api/cart/add",data:{site_id:t.goodsDetail.site_id,sku_id:t.goodsDetail.sku_id,num:t.number},success:function(e){var o=e.data,n=e.code,a=e.message;o>0?(i&&t.$util.showToast({title:"加入购物车成功"}),t.number=1,t.callback&&t.callback(!0)):n<0&&a&&(t.$util.showToast({title:a}),t.callback&&t.callback(!1)),t.$refs.skuPopup.close(),t.btnSwitch=!1},fail:function(e){t.$refs.skuPopup.close(),t.btnSwitch=!1}});else if("buy_now"==t.type||"maidou_spec"==t.type||"newhand_spec"==t.type){var e={sku_id:t.goodsDetail.sku_id,num:t.number};t.limitBuy&&(e["limitType"]=!0),uni.setStorage({key:"orderCreateData",data:e,success:function(){t.$util.redirectTo("/pages/order/payment/payment"),t.$refs.skuPopup.close(),t.btnSwitch=!1}})}else if("seckill"==t.type){e={sku_id:t.goodsDetail.sku_id,num:t.number,seckill_id:t.goodsDetail.seckill_id};uni.setStorage({key:"seckillOrderCreateData",data:e,success:function(){t.$util.redirectTo("/promotionpages/new_seckill/payment/payment"),t.btnSwitch=!1}})}else if("pintuan"==t.type){e={pintuan_goods_id:t.pintuanInfo.pintuan_goods_id,pintuan_id:t.pintuanInfo.pintuan_id,sku_id:t.goodsDetail.sku_id,goods_id:t.goodsDetail.goods_id,num:t.number};uni.setStorage({key:"pintuanOrderCreateData",data:e,success:function(){t.IsNotSkip&&t.$util.redirectTo("/promotionpages/pintuan/payment/payment"),t.$refs.skuPopup.close(),t.btnSwitch=!1}})}else if("topic"==t.type){e={topic_goods_id:t.goodsDetail.id,num:t.number};uni.setStorage({key:"topicOrderCreateData",data:e,success:function(){t.$util.redirectTo("/promotionpages/topics/payment/payment"),t.btnSwitch=!1}})}else if("groupbuy"==t.type){e={groupbuy_id:t.goodsDetail.groupbuy_id,sku_id:t.skuId,num:t.number};uni.setStorage({key:"groupbuyOrderCreateData",data:e,success:function(){t.$util.redirectTo("/promotionpages/groupbuy/payment/payment"),t.btnSwitch=!1}})}else if("update_cart"==t.type)t.$api.sendRequest({url:a.default.updateCartUrl,data:{cart_id:t.goodsDetail.cart_id,sku_id:t.goodsDetail.sku_id,num:t.number},success:function(e){0==e.code&&t.callback&&t.callback(),t.$refs.skuPopup.close(),t.btnSwitch=!1},fail:function(e){t.$refs.skuPopup.close(),t.btnSwitch=!1}});else if("periodbuy"==t.type){e={sku_id:t.skuId,num:t.number};uni.setStorage({key:"orderCreateData",data:e,success:function(){t.$util.redirectTo("/pages/order/cycle_payment/cycle_payment?period_id=".concat(t.periodId)),t.$refs.skuPopup.close(),t.btnSwitch=!1}})}else if("combo_buy"==t.type){var o={};o=Object.keys(t.goodsSkuDetail).length<1?t.goodsDetail:t.goodsSkuDetail.goods_id==t.goodsDetail.goods_id?t.goodsSkuDetail:t.goodsDetail,t.$emit("refresh",o),t.$refs.skuPopup.close(),t.btnSwitch=!1}}else t.$util.showToast({title:"购买数量不能为0"});else t.$util.showToast({title:"商品已售罄"})}))):this.$util.showToast({title:"请登录",success:function(){}})},closeSkuPopup:function(){this.$refs.skuPopup.close()},imageError:function(){var t=this;setTimeout((function(){t.goodsDetail.sku_image=t.$util.getDefaultImage().default_goods_img,t.$forceUpdate()}),100)},valueImageError:function(t,e){this.goodsDetail.goods_spec_format[t].value[e].image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}}};e.default=r}).call(this,i("ed83")["nextTick"])},"3b27":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={data:function(){return{showTop:!1,scrollTop:0,oldLocation:0}},methods:{scrollToTopNative:function(){uni.pageScrollTo({duration:200,scrollTop:0})}},onReachBottom:function(){},onPageScroll:function(t){this.oldLocation=t.scrollTop,t.scrollTop>200?!this.showTop&&(this.showTop=!0):this.showTop&&(this.showTop=!1),t.scrollTop>100?!this.isShowDetailTab&&(this.isShowDetailTab=!0):this.isShowDetailTab&&(this.isShowDetailTab=!1)}};e.default=o},4086:function(t,e,i){var o=i("68f7");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("7418f950",o,!0,{sourceMap:!1,shadowMode:!1})},"46e8":function(t,e,i){var o=i("c881");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("f73cf6f6",o,!0,{sourceMap:!1,shadowMode:!1})},"4d57":function(t,e,i){"use strict";i.r(e);var o=i("85d2"),n=i("b80d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("ee53");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"59f5e252",null,!1,o["a"],void 0);e["default"]=r.exports},"50b4":function(t,e,i){"use strict";i.r(e);var o=i("0d02"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},5479:function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(i("2634")),a=o(i("2fdc"));i("64aa");var s=o(i("5e99")),r=o(i("de74")),u={name:"ns-goods-discount",components:{UniIcons:r.default,uniPopup:s.default},props:{sku_id:{type:[Number,String],default:""}},data:function(){return{promotion_discount:[],multiple_discounts:[],goodscoupons:[]}},created:function(){},mounted:function(){},methods:{getGoodsSkuDetail:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function i(){var o,a,s;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return e.skuId=t||e.skuId,o={sku_id:e.skuId},i.next=4,e.$api.sendRequest({url:"/api/goodssku/detail",async:!1,data:o});case 4:a=i.sent,s=a.data,null!=s.discount_info&&(e.promotion_discount=s.discount_info.promotion_discount,e.multiple_discounts=s.discount_info.multiple_discounts,e.goodscoupons=s.discount_info.goodscoupons);case 7:case"end":return i.stop()}}),i)})))()},getCoipon:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(uni.getStorageSync("token")){i.next=3;break}return e.$util.toShowLoginPopup(e,null,"/pages/goods/detail/detail?sku_id=".concat(e.sku_id)),i.abrupt("return",!1);case 3:return i.next=5,e.$util.subscribeMessage({source:"goodscoupon",source_id:"",scene_type:"goodscoupon_group"});case 5:uni.showLoading({title:"加载中",mask:!0}),e.$api.sendRequest({url:e.$apiUrl.goodsCouponReceive,data:{token:uni.getStorageSync("token"),goodscoupon_type_id:t.goodscoupon_type_id},success:function(t){uni.hideLoading(),setTimeout((function(){e.$util.showToast({title:0==t.code?"领取成功":t.message,mask:!0,duration:2e3,success:function(t){e.getGoodsSkuDetail(e.sku_id)}})}),100)},fail:function(t){uni.hideLoading()}});case 7:case"end":return i.stop()}}),i)})))()},filterNum:function(t,e){var i=String(Number(t)).split(".");return!!i[e]&&i[e]},change:function(t){t.show||this.$emit("closeModel")},open:function(){this.$refs.discountPopup.open(),this.$emit("openModel")},hide:function(){this.$refs.discountPopup.close()},refresherrefresh:function(){return!1}}};e.default=u},"57bc":function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("5c47"),i("2c10"),i("a1c1"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("aa9c"),i("473f"),i("bf0f"),i("3efd");var n=o(i("93d4")),a=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,s=/^<\/([-A-Za-z0-9_]+)[^>]*>/,r=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,u=h("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),c=h("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),d=h("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),l=h("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),p=h("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),f=h("script,style");function h(t){for(var e={},i=t.split(","),o=0;o<i.length;o++)e[i[o]]=!0;return e}var m=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){return t=t.replace(/\\/g,"").replace(/<img/g,'<img style="width:100% !important;display:block;"'),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,e){return'<img style="width:100% !important;display:block;" src="'+n.default.img(e)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,e){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,e){return t.replace(/"/g,"'")})),t})),t}(t);var e=[],i={node:"root",children:[]};return function(t,e){var i,o,n,h=[],m=t;h.last=function(){return this[this.length-1]};while(t){if(o=!0,h.last()&&f[h.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+h.last()+"[^>]*>"),(function(t,i){return i=i.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(i),""})),b("",h.last());else if(0==t.indexOf("\x3c!--")?(i=t.indexOf("--\x3e"),i>=0&&(e.comment&&e.comment(t.substring(4,i)),t=t.substring(i+3),o=!1)):0==t.indexOf("</")?(n=t.match(s),n&&(t=t.substring(n[0].length),n[0].replace(s,b),o=!1)):0==t.indexOf("<")&&(n=t.match(a),n&&(t=t.substring(n[0].length),n[0].replace(a,v),o=!1)),o){i=t.indexOf("<");var g=i<0?t:t.substring(0,i);t=i<0?"":t.substring(i),e.chars&&e.chars(g)}if(t==m)throw"Parse Error: "+t;m=t}function v(t,i,o,n){if(i=i.toLowerCase(),c[i])while(h.last()&&d[h.last()])b("",h.last());if(l[i]&&h.last()==i&&b("",i),n=u[i]||!!n,n||h.push(i),e.start){var a=[];o.replace(r,(function(t,e){var i=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:p[e]?e:"";a.push({name:e,value:i,escaped:i.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(i,a,n)}}function b(t,i){if(i){for(o=h.length-1;o>=0;o--)if(h[o]==i)break}else var o=0;if(o>=0){for(var n=h.length-1;n>=o;n--)e.end&&e.end(h[n]);h.length=o}}b()}(t,{start:function(t,o,n){var a={name:t};if(0!==o.length&&(a.attrs=function(t){return t.reduce((function(t,e){var i=e.value,o=e.name;return t[o]?t[o]=t[o]+" "+i:t[o]=i,t}),{})}(o)),n){var s=e[0]||i;s.children||(s.children=[]),s.children.push(a)}else e.unshift(a)},end:function(t){var o=e.shift();if(o.name!==t&&console.error("invalid state: mismatch end tag"),0===e.length)i.children.push(o);else{var n=e[0];n.children||(n.children=[]),n.children.push(o)}},chars:function(t){var o={type:"text",text:t};if(0===e.length)i.children.push(o);else{var n=e[0];n.children||(n.children=[]),n.children.push(o)}},comment:function(t){var i={node:"comment",text:t},o=e[0];o.children||(o.children=[]),o.children.push(i)}}),i.children};e.default=m},6546:function(t,e,i){var o=i("e70a");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("1be23796",o,!0,{sourceMap:!1,shadowMode:!1})},"68f7":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";.uni-popup[data-v-499e7720]{position:fixed;top:0;top:0;bottom:0;left:0;right:0;z-index:999;overflow:hidden}.uni-popup__mask[data-v-499e7720]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:998;background:rgba(0,0,0,.4);opacity:0}.uni-popup__mask.ani[data-v-499e7720]{transition:all .3s}.uni-popup__mask.uni-bottom[data-v-499e7720],\r\n.uni-popup__mask.uni-center[data-v-499e7720],\r\n.uni-popup__mask.uni-right[data-v-499e7720],\r\n.uni-popup__mask.uni-left[data-v-499e7720],\r\n.uni-popup__mask.uni-top[data-v-499e7720]{opacity:1}.uni-popup__wrapper[data-v-499e7720]{position:absolute;z-index:999;box-sizing:border-box\r\n/* \tbackground: #ffffff; */}.uni-popup__wrapper.ani[data-v-499e7720]{transition:all .3s}.uni-popup__wrapper.top[data-v-499e7720]{top:0;left:0;width:100%;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.uni-popup__wrapper.bottom[data-v-499e7720]{background:#fff;\r\n\t/* bottom: -30px; */bottom:0;left:0;width:100%;-webkit-transform:translateY(100%);transform:translateY(100%)}.uni-popup__wrapper.right[data-v-499e7720]{bottom:0;left:0;width:100%;-webkit-transform:translateX(100%);transform:translateX(100%)}.uni-popup__wrapper.left[data-v-499e7720]{bottom:0;left:0;width:100%;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.uni-popup__wrapper.center[data-v-499e7720]{width:100%;height:100%;display:flex;justify-content:center;overflow:hidden;align-items:center;-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}.uni-popup__wrapper-box[data-v-499e7720]{position:relative;box-sizing:border-box}.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box[data-v-499e7720]{background:#fff}.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box[data-v-499e7720]{background:transparent;position:relative;max-width:80%;max-height:80%;overflow-y:scroll;border-radius:%?20?%}.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box[data-v-499e7720],\r\n.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box[data-v-499e7720]{width:100%;\r\n\t/* max-height: 500px; */overflow-y:scroll}.uni-popup__wrapper.uni-bottom[data-v-499e7720],\r\n.uni-popup__wrapper.uni-top[data-v-499e7720]{-webkit-transform:translateY(0);transform:translateY(0);border-radius:%?20?% %?20?% 0 0;overflow:hidden}.uni-popup__wrapper.uni-left[data-v-499e7720],\r\n.uni-popup__wrapper.uni-right[data-v-499e7720]{-webkit-transform:translateX(0);transform:translateX(0)}.uni-popup__wrapper.uni-center[data-v-499e7720]{-webkit-transform:scale(1);transform:scale(1);opacity:1}\r\n\r\n/* isIphoneX系列手机底部安全距离 */.bottom[data-v-499e7720]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}',""]),t.exports=e},"77da":function(t,e,i){"use strict";var o=i("6546"),n=i.n(o);n.a},8416:function(t,e,i){"use strict";var o=i("46e8"),n=i.n(o);n.a},"85d2":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniPopup:i("5e99").default,nsGoodsDiscount:i("fc35").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-sku",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"skuPopup",staticClass:"sku-layer",attrs:{type:"bottom"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.popupChange.apply(void 0,arguments)}}},[t.goodsDetail?i("v-uni-view",{staticClass:"sku-info",style:{height:1*t.systemInfo.windowHeight+"px"}},[i("v-uni-view",{staticClass:"header"},[i("v-uni-view",{staticClass:"img-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.goodsDetail.sku_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}})],1),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"goodsName"},[t._v(t._s(t.goodsDetail.sku_name))]),i("v-uni-view",{staticClass:"price-wrap"},["combo_buy"==t.entrance?[i("v-uni-view",{staticClass:"price ns-font-size-lg"},[i("v-uni-text",{staticClass:"prize-icon"},[t._v("￥")]),t._v(t._s(t.goodsDetail.combo_price))],1),i("v-uni-view",{staticClass:"cheap_price"},[t._v("已省"+t._s(t.goodsDetail.cheap_price)+"元")])]:[i("v-uni-view",{staticClass:"price ns-font-size-lg"},[i("v-uni-text",{staticClass:"prize-icon"},[t._v("￥")]),t._v(t._s(t.goodsDetail.show_price))],1),i("v-uni-view",{staticClass:"market_price"},[i("v-uni-text",{staticClass:"prize-icon"},[t._v("￥")]),t._v(t._s(t.goodsDetail.market_price))],1)]],2)],1),i("v-uni-view",{staticClass:"sku-close iconfont iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSkuPopup()}}})],1),"pintuan"!=t.type&&"seckill"!=t.type&&"maidou_spec"!=t.type&&"combo_buy"!=t.type&&t.goodsDetail.discount_content&&t.goodsDetail.discount_content.length>0?i("v-uni-view",{staticClass:"coupon-content"},[i("v-uni-scroll-view",{staticClass:"content-box-scroll",attrs:{"scroll-x":"true"}},[t._l(t.goodsDetail.discount_content,(function(e,o){return[""!=e.describe?i("v-uni-view",{key:o+"_0",staticClass:"content-box-items-red"},[t._v(t._s(e.describe))]):t._e(),i("v-uni-view",{key:o+"_1",staticClass:"content-box-items"},[t._v(t._s(e.content))])]}))],2)],1):t._e(),i("v-uni-view",{staticClass:"body-item"},[i("v-uni-scroll-view",{staticClass:"wrap",attrs:{"scroll-y":!0}},[t._l(t.goodsDetail.goods_spec_format,(function(e,o){return i("v-uni-view",{key:o,staticClass:"sku-list-wrap"},[i("v-uni-text",{staticClass:"title ns-font-size-base"},[t._v(t._s(e.spec_name))]),t._l(e.value,(function(e,n){return i("v-uni-view",{key:n,staticClass:"items ns-border-color-gray ns-bg-color-gray ns-font-size-base",class:{selected:e["selected"]||t.skuId==e.sku_id,disabled:e["disabled"]||!e["selected"]&&t.disabled},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.change(e.sku_id,e.spec_id)}}},[e.image?i("v-uni-image",{attrs:{src:t.$util.img(e.image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.valueImageError(o,n)}}}):t._e(),i("v-uni-text",[t._v(t._s(e.spec_value_name))])],1)}))],2)})),"cart"!=t.entrance?i("v-uni-view",{staticClass:"number-wrap"},[i("v-uni-view",{staticClass:"number-line"},[i("v-uni-text",{staticClass:"title ns-font-size-base"},[t._v("购买数量")]),t.limitNumber>0?i("v-uni-text",{staticClass:"limit-txt ns-font-size-sm"},[t._v("（每人限购"+t._s(t.limitNumber)+"件）")]):t._e(),t.minNumber>0?i("v-uni-text",{staticClass:"limit-txt ns-font-size-sm"},[t._v("（"+t._s(t.minNumber)+"件起购）")]):t._e(),i("v-uni-view",{staticClass:"number"},[i("v-uni-button",{staticClass:"decrease ns-border-color-gray",class:{disabled:t.limitBuy},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeNum("-")}}},[t._v("-")]),i("v-uni-input",{staticClass:"uni-input ns-border-color-gray ns-font-size-sm",attrs:{type:"number",placeholder:"0",disabled:t.limitBuy},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.keyInput(!1)}},model:{value:t.number,callback:function(e){t.number=e},expression:"number"}}),i("v-uni-button",{staticClass:"increase ns-border-color-gray",class:{disabled:t.limitBuy},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeNum("+")}}},[t._v("+")])],1)],1)],1):t._e()],2)],1),"choose_spec"==t.entranceType?i("v-uni-view",{staticClass:"footer"},[i("v-uni-view",{staticClass:"footer-btn"},[t.goodsDetail.stock&&0!=t.goodsDetail.stock&&!t.skuIsDisabledStatus[t.goodsDetail.sku_id]?i("v-uni-button",{staticClass:"btn-cart",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm("join_cart")}}},[t._v("加入购物车")]):i("v-uni-button",{staticClass:"btn-disabled"},[t._v("加入购物车")])],1),i("v-uni-view",{staticClass:"footer-btn"},[t.goodsDetail.stock&&0!=t.goodsDetail.stock&&!t.skuIsDisabledStatus[t.goodsDetail.sku_id]?i("v-uni-button",{staticClass:"btn-confirm",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm("buy_now")}}},[t._v("确定")]):i("v-uni-button",{staticClass:"btn-disabled",attrs:{type:"primary"}},[t._v("确定")])],1)],1):i("v-uni-view",{staticClass:"footer cart-footer",style:{bottom:"cart"==t.entrance?"60rpx":"0rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm()}}},[t.goodsDetail.stock&&0!=t.goodsDetail.stock&&!t.skuIsDisabledStatus[t.goodsDetail.sku_id]?i("v-uni-button",{staticClass:"btn-confirm",attrs:{type:"primary"}},[t._v("确定")]):i("v-uni-button",{staticClass:"btn-disabled",attrs:{type:"primary"}},[t._v("确定")])],1)],1):t._e()],1),i("ns-goods-discount",{ref:"discountPopup"})],1)},a=[]},8765:function(t,e,i){"use strict";i.r(e);var o=i("313a"),n=i("50b4");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("8416");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"7ca63514",null,!1,o["a"],void 0);e["default"]=r.exports},"8afb":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-59f5e252]{width:100%;text-align:center}.sku-layer .sku-info[data-v-59f5e252]{height:75vh!important;position:relative;z-index:999}.sku-layer .sku-info .coupon-content[data-v-59f5e252]{width:100%;box-sizing:border-box;padding:%?0?% %?20?%}.sku-layer .sku-info .coupon-content .content-box-scroll[data-v-59f5e252]{width:100%;white-space:nowrap;height:%?50?%}.sku-layer .sku-info .coupon-content .content-box-scroll uni-view[data-v-59f5e252]{width:-webkit-fit-content;width:fit-content;display:inline-block;height:%?40?%;font-size:%?22?%;box-sizing:border-box;padding:%?4?% %?8?%}.sku-layer .sku-info .coupon-content .content-box-scroll .content-box-items-red[data-v-59f5e252]{color:#fff;background-color:var(--custom-brand-color);margin-right:%?10?%;border-radius:%?10?%}.sku-layer .sku-info .coupon-content .content-box-scroll .content-box-items[data-v-59f5e252]{color:var(--custom-brand-color);background:var(--custom-brand-color-10);border-radius:%?10?%;margin-right:%?10?%;word-break:keep-all;white-space:nowrap}.sku-layer .sku-info .coupon-content .coupon-btn[data-v-59f5e252]{color:var(--custom-brand-color);font-size:%?24?%}.sku-layer .sku-info .coupon-content .coupon-btn .iconright[data-v-59f5e252]{font-size:%?24?%}.sku-layer .sku-info .header[data-v-59f5e252]{padding:%?36?% 0 %?36?% %?236?%;position:relative}.sku-layer .sku-info .header .img-wrap[data-v-59f5e252]{width:%?180?%;height:%?180?%;position:absolute;left:%?20?%;border-radius:%?8?%;overflow:hidden;line-height:%?208?%}.sku-layer .sku-info .header .img-wrap uni-image[data-v-59f5e252]{width:100%;height:100%}.sku-layer .sku-info .main[data-v-59f5e252]{font-size:12px;line-height:%?40?%;padding-right:%?40?%;height:%?180?%;position:relative}.sku-layer .sku-info .main .price-distributor[data-v-59f5e252]{position:absolute;left:0;bottom:%?40?%;display:flex;align-items:center}.sku-layer .sku-info .main .price-distributor uni-text[data-v-59f5e252]:first-child{background:linear-gradient(90deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:%?4?% 0 0 %?4?%;padding:%?0?% %?9?%;box-sizing:border-box;font-size:%?20?%;font-weight:500;color:#fff;text-align:center;height:%?40?%;line-height:%?40?%}.sku-layer .sku-info .main .price-distributor uni-text[data-v-59f5e252]:last-child{background:var(--custom-brand-color-10);border-radius:0 %?4?% %?4?% 0;text-align:center;font-size:.6875rem;font-weight:500;color:var(--custom-brand-color);padding:%?0?% %?9?%;box-sizing:border-box;height:%?40?%;line-height:%?40?%}.sku-layer .sku-info .main .price-wrap[data-v-59f5e252]{position:absolute;bottom:0;display:flex;align-items:baseline}.sku-layer .sku-info .main .goodsName[data-v-59f5e252]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;width:90%}.sku-layer .sku-info .main .price .prize-icon[data-v-59f5e252]{font-size:%?26?%;font-weight:400}.sku-layer .sku-info .main .price[data-v-59f5e252]{word-wrap:break-word;font-weight:700;line-height:%?30?%;color:var(--custom-brand-color)}.sku-layer .sku-info .main .market_price[data-v-59f5e252]{word-wrap:break-word;font-size:%?24?%;text-decoration:line-through;color:#999;margin-left:%?8?%}.sku-layer .sku-info .main .cheap_price[data-v-59f5e252]{font-size:%?22?%;font-weight:400;line-height:%?32?%;color:#fff;display:inline-block;padding:%?2?% %?14?%;box-sizing:border-box;margin-left:%?12?%;background:var(--custom-brand-color);border-radius:%?32?%}.sku-layer .sku-info .main .sku-name[data-v-59f5e252]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;height:%?90?%;overflow:hidden}.sku-layer .sku-info .main .sku-name uni-text[data-v-59f5e252]{margin-right:%?10?%}.sku-layer .sku-info .sku-close[data-v-59f5e252]{position:absolute;top:%?24?%;right:%?24?%;width:%?32?%;height:%?32?%;font-size:%?20?%;background:#999;border-radius:50%;line-height:%?34?%;text-align:center;color:#fff}.sku-layer .body-item[data-v-59f5e252]{padding:0 %?30?%;height:calc(100% - %?330?%);box-sizing:border-box;overflow:scroll}.sku-layer .body-item .wrap[data-v-59f5e252]{height:calc(100% - %?116?%)}.sku-layer .body-item .sku-list-wrap[data-v-59f5e252]{padding-bottom:%?20?%}.sku-layer .body-item .sku-list-wrap .title[data-v-59f5e252]{font-weight:400;padding:%?26?% 0;margin:0;display:block}.sku-layer .body-item .sku-list-wrap .items[data-v-59f5e252]{position:relative;display:inline-block;border:1px solid;padding:0 %?20?%;border-radius:%?27?%;margin:0 %?10?% %?10?% 0;font-size:%?26?%;line-height:%?54?%}.sku-layer .body-item .sku-list-wrap .items.disabled[data-v-59f5e252]{border:1px dashed}.sku-layer .body-item .sku-list-wrap .items uni-image[data-v-59f5e252]{height:%?48?%;width:%?48?%;border-radius:%?4?%;margin-right:%?10?%;display:inline-block;vertical-align:middle}.sku-layer .body-item .number-wrap .number-line[data-v-59f5e252]{padding:%?20?% 0;line-height:%?72?%;border-bottom:1px solid rgba(0,0,0,.1)}.sku-layer .body-item .number-wrap .title[data-v-59f5e252]{font-weight:400}.sku-layer .body-item .number-wrap .number[data-v-59f5e252]{height:%?72?%;border-radius:%?6?%;float:right}.sku-layer .body-item .number-wrap .number uni-button[data-v-59f5e252]{display:inline-block;line-height:%?64?%;height:%?68?%;width:%?60?%;font-size:%?48?%;box-sizing:initial;border:1px solid;padding:0;margin:0;border-radius:0}.sku-layer .body-item .number-wrap .number uni-button.decrease[data-v-59f5e252]{border-right:1px solid #fff!important;border-radius:%?10?%}.sku-layer .body-item .number-wrap .number uni-button.increase[data-v-59f5e252]{border-left:1px solid #fff!important;border-radius:%?10?%}.sku-layer .body-item .number-wrap .number uni-button.disabled[data-v-59f5e252]{color:#e5e5e5}.sku-layer .body-item .number-wrap .number uni-button[data-v-59f5e252]:after{border-radius:0;border:none}.sku-layer .body-item .number-wrap .number uni-input[data-v-59f5e252]{display:inline-block;line-height:%?64?%;height:%?68?%;width:%?72?%;text-align:center;font-weight:700;border:1px solid;margin:0;padding:0;vertical-align:top}.sku-layer .footer[data-v-59f5e252]{height:%?96?%;width:100%;padding:0 7.5%;box-sizing:border-box;position:absolute;bottom:0;color:#fff;z-index:1;display:flex;justify-content:space-between;align-items:center}.sku-layer .footer uni-button[data-v-59f5e252]{width:100%}.sku-layer .footer .footer-btn[data-v-59f5e252]{width:45%;flex-shrink:0}.sku-layer .footer .footer-btn[data-v-59f5e252]:first-child{border-radius:%?40?%}.sku-layer .footer .footer-btn:first-child uni-button[data-v-59f5e252]{color:var(--custom-brand-color);background:var(--custom-brand-color-10);border:%?2?% solid var(--custom-brand-color)}.sku-layer .footer .footer-btn:first-child .btn-disabled[data-v-59f5e252]{border-color:#e5e5e5!important}.sku-layer .footer .footer-btn .btn-confirm[data-v-59f5e252]{background-color:var(--custom-brand-color)!important}.sku-layer .footer .footer-btn uni-button[data-v-59f5e252]{width:100%;margin:0}.cart-footer[data-v-59f5e252]{padding-bottom:0!important}.cart-footer .btn-confirm[data-v-59f5e252]{background-color:var(--custom-brand-color)!important}.position-bottom[data-v-59f5e252]{bottom:%?98?%!important}',""]),t.exports=e},"8cbc":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"UniPopup",props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},custom:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{ani:"",showPopup:!1,callback:null,isIphoneX:!1}},watch:{show:function(t){t?this.open():this.close()}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{clear:function(){},open:function(t){var e=this;t&&(this.callback=t),this.$emit("change",{show:!0}),this.showPopup=!0,this.$nextTick((function(){setTimeout((function(){e.ani="uni-"+e.type}),30)}))},close:function(t,e){var i=this;!this.maskClick&&t||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick((function(){setTimeout((function(){i.showPopup=!1}),300)})),e&&e(),this.callback&&this.callback.call(this))}}};e.default=o},"955e":function(t,e,i){"use strict";i.r(e);var o=i("5479"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},a43d:function(t,e,i){var o=i("8afb");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("60bfff76",o,!0,{sourceMap:!1,shadowMode:!1})},b80d:function(t,e,i){"use strict";i.r(e);var o=i("3922"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},c3f5:function(t,e,i){"use strict";i.r(e);var o=i("de63"),n=i("d4e0");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("1858");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"499e7720",null,!1,o["a"],void 0);e["default"]=r.exports},c881:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7ca63514]{width:100%;text-align:center}.coupon-model[data-v-7ca63514]{display:flex;flex-direction:column;position:relative;z-index:111;width:%?620?%;background-size:cover;background-position:50%}.coupon-model .coupon-header[data-v-7ca63514]{background-size:cover;background-position:50%;margin-bottom:%?117?%}.coupon-model .title[data-v-7ca63514]{font-size:%?40?%;line-height:%?52?%;background-image:-webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);-webkit-background-clip:text;-webkit-text-fill-color:transparent;padding:%?124?% 0 %?14?%;text-align:center;font-weight:700}.coupon-model .tip[data-v-7ca63514]{font-size:%?30?%;line-height:%?32?%;background-image:-webkit-linear-gradient(0deg,#fc5a50,#ff561a 46.75293%,#ff2637);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-align:center}.coupon-model .coupon-box[data-v-7ca63514]{flex:1;padding:0 %?54?% 0;background-size:100% 100%;background-position:50%;position:relative;margin-bottom:28px;overflow-y:auto}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]{display:flex;background-size:cover;background-position:50%;height:%?120?%;margin-bottom:%?20?%;position:relative;z-index:11}.coupon-model .coupon-box .coupon-list[data-v-7ca63514]:last-child{margin-bottom:0}.coupon-model .coupon-box .coupon-list .left[data-v-7ca63514]{width:70px;display:flex;align-items:center;justify-content:center}.coupon-model .coupon-box .coupon-list .left .info[data-v-7ca63514]{display:flex;align-items:baseline}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:first-child{display:inline-block;font-size:%?26?%;color:#eb0000}.coupon-model .coupon-box .coupon-list .left .info > uni-view[data-v-7ca63514]:last-child{display:inline-block;font-size:%?48?%;color:#eb0000;line-height:%?80?%}.coupon-model .coupon-box .coupon-list .left .point-class[data-v-7ca63514]{font-size:%?35?%}.coupon-model .coupon-box .coupon-list .left .money-thousand[data-v-7ca63514]{font-size:%?41?%!important}.coupon-model .coupon-box .coupon-list .left .money-thousand > span[data-v-7ca63514]{font-size:%?29?%}.coupon-model .coupon-box .coupon-list .right[data-v-7ca63514]{width:%?238?%;flex:1;display:flex;align-items:center;position:relative;margin-left:%?18?%}.coupon-model .coupon-box .coupon-list .right > uni-view[data-v-7ca63514]:first-child{flex:1;overflow:hidden;height:100%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .name[data-v-7ca63514]{font-size:%?24?%;line-height:%?36?%;padding:%?24?% 0 %?8?%}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .time[data-v-7ca63514]{font-size:%?18?%;line-height:%?30?%;color:#999;white-space:nowrap}.coupon-model .coupon-box .coupon-list .right > uni-view:first-child .h5-time[data-v-7ca63514]{display:flex;width:119px;margin-left:-13px;-webkit-transform:scale(.78);transform:scale(.78)}.coupon-model .coupon-box .coupon-list .right .btn[data-v-7ca63514]{display:flex;justify-content:center;align-items:center;width:%?94?%;height:%?38?%;background:linear-gradient(90deg,#ffab37,#fff594);border-radius:19px;font-size:%?24?%;color:#822d02;margin:0 %?10?% 0 0}.coupon-model .coupon-box .coupon-list .right .h5-btn[data-v-7ca63514]{-webkit-transform:scale(.8);transform:scale(.8)}.coupon-model .coupon_bg[data-v-7ca63514]{position:absolute;top:%?270?%;width:100%;height:%?153?%;z-index:-1}.coupon-model .coupon_footer[data-v-7ca63514]{width:100%;height:%?51?%}.box1[data-v-7ca63514]{height:%?565?%}.box2[data-v-7ca63514]{height:%?660?%}.box3[data-v-7ca63514]{height:%?800?%}.box4[data-v-7ca63514]{height:%?850?%}.pop-ad-info-close[data-v-7ca63514]{width:%?88?%;height:%?88?%;display:block;margin:%?60?% auto 0}',""]),t.exports=e},c9b2:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniPopup:i("5e99").default,uniIcons:i("de74").default,ydAuthPopup:i("161f").default,nsLogin:i("4f5a").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-discount",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"discountPopup",staticClass:"discountPopup",attrs:{type:"bottom",bottomRadius:!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"discount-popup"},[i("v-uni-view",{staticClass:"discount-popup-title"},[i("v-uni-text"),i("uni-icons",{attrs:{type:"clear",size:"24",color:"rgba(217, 217, 217, 1)"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hide.apply(void 0,arguments)}}})],1),i("v-uni-scroll-view",{staticClass:"coupon-all",staticStyle:{height:"1000rpx","box-sizing":"border-box","padding-bottom":"40rpx","border-radius":"40rpx 40rpx 0 0"},attrs:{"scroll-y":!0,"show-scrollbar":!1,"refresher-enabled":!1},on:{refresherrefresh:function(e){arguments[0]=e=t.$handleEvent(e),t.refresherrefresh.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"discount-warp"},[t.promotion_discount&&t.promotion_discount.length?i("v-uni-view",{staticClass:"warp-type_01"},[i("v-uni-view",{staticClass:"warp-title"},[i("v-uni-view",{staticClass:"title-text"},[t._v("分销商优惠")])],1),t._l(t.promotion_discount,(function(e,o){return i("v-uni-view",{key:o,staticClass:"warp-discount"},[i("v-uni-view",{staticClass:"textbox"},[i("v-uni-text",{staticClass:"money"},[i("v-uni-text",{staticClass:"icon"},[t._v("￥")]),i("v-uni-text",{staticClass:"big"},[t._v(t._s(t.filterNum(e.shop_owner_brokerage,0)))]),t._v(t._s(t.filterNum(e.shop_owner_brokerage,1)?"."+t.filterNum(e.shop_owner_brokerage,1):""))],1)],1),i("v-uni-view",{staticClass:"textbox",staticStyle:{"margin-left":"48rpx"}},[i("v-uni-text",{staticClass:"text"},[t._v("分销商每件立减优惠")])],1)],1)}))],2):t._e(),t.multiple_discounts&&t.multiple_discounts.length?i("v-uni-view",{staticClass:"warp-type_02"},[i("v-uni-view",{staticClass:"warp-title"},[i("v-uni-view",{staticClass:"title-text"},[t._v("折扣活动")]),i("v-uni-view",{staticClass:"title-tips"},[t._v("部分活动不能与券叠加使用")])],1),i("v-uni-view",{staticClass:"warp-discount"},[i("v-uni-view",{staticClass:"warp-discount-list"},t._l(t.multiple_discounts,(function(e,o){return i("v-uni-view",{key:o,staticClass:"warp-discount-item"},[i("v-uni-view",{staticClass:"item-left"},[i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"content-text"},[t._v(t._s(1==e.type?"单商品":"多商品")+"满"+t._s(e.at_least)+"件享"+t._s(Number(e.discount))+"折")]),i("v-uni-view",{staticClass:"content-date"},[t._v("有效期至"+t._s(t.$util.timeStampTurnTime(e.over_time)))])],1)],1),i("v-uni-view",{staticClass:"item-right"},[i("v-uni-view",{staticClass:"discount"},[i("v-uni-text",{staticClass:"num"},[t._v(t._s(Number(e.discount)))]),t._v("折")],1),i("v-uni-view",{staticClass:"check",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/promotionpages/special_offers/special_offers?multiple_discount_id="+e.multiple_discount_id)}}},[t._v("查看")])],1),i("v-uni-view",{staticClass:"tagType"},[t._v("多件折扣")])],1)})),1)],1)],1):t._e(),t.goodscoupons&&t.goodscoupons.length?i("v-uni-view",{staticClass:"warp-type_02"},[i("v-uni-view",{staticClass:"warp-title"},[i("v-uni-view",{staticClass:"title-text"},[t._v("优惠券")])],1),i("v-uni-view",{staticClass:"warp-discount"},[i("v-uni-view",{staticClass:"warp-discount-list"},t._l(t.goodscoupons,(function(e,o){return i("v-uni-view",{key:o,staticClass:"warp-discount-item",class:{"warp-discount-item-use":1===e.is_receive},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),1==e.is_receive&&t.$util.redirectTo("/otherpages/goods/coupon_goods_list/coupon_goods_list",{goodscoupon_type_id:e.goodscoupon_type_id})}}},[i("v-uni-view",{staticClass:"item-left"},[i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"content-text"},[t._v(t._s(e.content))]),1===e.validity_type?i("v-uni-view",{staticClass:"content-date"},[t._v("领取后"+t._s(e.fixed_term)+"天内有效")]):t._e(),0===e.validity_type?i("v-uni-view",{staticClass:"content-date"},[t._v("有效期至"+t._s(t.$util.timeStampTurnTime(e.end_time)))]):t._e()],1)],1),i("v-uni-view",{staticClass:"item-right"},[i("v-uni-text",{staticClass:"money"},[i("v-uni-text",{staticClass:"icon"},[t._v("￥")]),i("v-uni-text",{staticClass:"big"},[t._v(t._s(t.filterNum(e.money,0)))]),t._v(t._s(t.filterNum(e.money,1)?"."+t.filterNum(e.money,1):""))],1),1==e.is_receive?i("v-uni-view",{staticClass:"check"},[t._v("使用")]):i("v-uni-view",{staticClass:"check",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.getCoipon(e)}}},[t._v("领券")])],1),i("v-uni-view",{staticClass:"tagType"},[t._v("满减券")])],1)})),1)],1)],1):t._e()],1)],1)],1)],1),i("yd-auth-popup",{ref:"ydauth"}),i("ns-login",{ref:"login"})],1)},a=[]},d4e0:function(t,e,i){"use strict";i.r(e);var o=i("8cbc"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},de63:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.showPopup?i("v-uni-view",{staticClass:"uni-popup"},[i("v-uni-view",{staticClass:"uni-popup__mask",class:[t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}}),i("v-uni-view",{staticClass:"uni-popup__wrapper",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}},[i("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1)],1):t._e()},n=[]},e70a:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-0fc7107c]{width:100%;text-align:center}[data-v-0fc7107c] .uni-popup__wrapper.bottom-radius{border-radius:%?40?% %?40?% 0 0}.goods-discount[data-v-0fc7107c]{height:auto}.discount-popup[data-v-0fc7107c]{width:100%;box-sizing:border-box;padding:0 %?24?%;border-radius:%?40?% %?40?% 0 0}.discount-popup .discount-popup-title[data-v-0fc7107c]{width:100%;font-size:%?36?%;font-weight:700;line-height:%?43.2?%;color:#383838;padding:%?18?% 0;display:flex;justify-content:space-between;align-items:center}.discount-popup .discount-warp[data-v-0fc7107c]{width:100%;height:100%}.discount-popup .discount-warp .warp-title[data-v-0fc7107c]{width:100%;display:flex;justify-content:space-between;align-items:center}.discount-popup .discount-warp .warp-title .title-text[data-v-0fc7107c]{font-size:%?32?%;font-family:PingFang SC;font-weight:600;line-height:%?44?%;color:#333}.discount-popup .discount-warp .warp-title .title-tips[data-v-0fc7107c]{font-size:%?24?%;font-family:PingFang SC;font-weight:300;line-height:%?34?%;color:#666}.discount-popup .discount-warp .warp-type_01[data-v-0fc7107c]{width:100%;margin-bottom:%?30?%}.discount-popup .discount-warp .warp-type_01 .warp-discount[data-v-0fc7107c]{width:100%;height:%?148?%;margin-top:%?24?%;background:var(--custom-brand-color);border-radius:%?8?%;color:#fff;box-sizing:border-box;padding:%?48?%;display:flex}.discount-popup .discount-warp .warp-type_01 .warp-discount .textbox[data-v-0fc7107c]{height:100%;display:flex;align-items:center}.discount-popup .discount-warp .warp-type_01 .warp-discount .money[data-v-0fc7107c]{font-size:%?32?%;font-weight:600;color:#fff}.discount-popup .discount-warp .warp-type_01 .warp-discount .money .icon[data-v-0fc7107c]{font-size:%?24?%}.discount-popup .discount-warp .warp-type_01 .warp-discount .money .big[data-v-0fc7107c]{font-size:%?48?%}.discount-popup .discount-warp .warp-type_01 .warp-discount .text[data-v-0fc7107c]{font-size:%?30?%;font-weight:500;color:#fff;line-height:%?148?%}.discount-popup .discount-warp .warp-type_02[data-v-0fc7107c]{width:100%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list[data-v-0fc7107c]{width:100%;margin-top:%?24?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item[data-v-0fc7107c]{display:flex;width:100%;height:%?210?%;margin-bottom:%?24?%;border-radius:%?8?%;position:relative;overflow:hidden;background:linear-gradient(135deg,var(--custom-brand-color-80),var(--custom-brand-color))}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-left[data-v-0fc7107c]{flex:1;height:100%;box-sizing:border-box;padding:%?20?%;display:flex;justify-content:space-between;align-items:center;position:relative}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-left[data-v-0fc7107c]::after{content:"";display:block;width:9px;height:9px;position:absolute;top:-7px;right:-6px;background:#fff;border-radius:50%;border:1px solid var(--custom-brand-color-10);clip-path:polygon(50% 0,100% 0,100% 3600%,50% 50%);-webkit-clip-path:polygon(50% 0,100% 0,100% 3600%,50% 50%);-webkit-transform:rotate(90deg);transform:rotate(90deg);z-index:10}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-left[data-v-0fc7107c]::before{content:"";display:block;width:9px;height:9px;position:absolute;bottom:-7px;right:-6px;background:#fff;border-radius:50%;border:1px solid var(--custom-brand-color-10);clip-path:polygon(50% 0,100% 0,100% 3600%,50% 50%);-webkit-clip-path:polygon(50% 0,100% 0,100% 3600%,50% 50%);-webkit-transform:rotate(-90deg);transform:rotate(-90deg);z-index:10}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-left .content-text[data-v-0fc7107c]{font-size:%?32?%;line-height:%?44?%;font-weight:700;color:#fff}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-left .content-date[data-v-0fc7107c]{font-size:%?24?%;line-height:%?36?%;font-weight:400;color:#fff;margin-top:%?6?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right[data-v-0fc7107c]{width:%?248?%;height:100%;position:relative;display:flex;flex-direction:column;justify-content:center;align-items:center;border-left:%?2?% dashed #fff}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .discount[data-v-0fc7107c]{font-size:%?28?%;font-weight:600;color:#fff}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .discount .num[data-v-0fc7107c]{font-size:%?72?%;line-height:%?84?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .money[data-v-0fc7107c]{font-size:%?32?%;font-weight:600;color:#fff}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .money .icon[data-v-0fc7107c]{font-size:%?28?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .money .big[data-v-0fc7107c]{font-size:%?72?%;line-height:%?84?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .item-right .check[data-v-0fc7107c]{font-size:%?32?%;font-weight:400;color:var(--custom-brand-color);position:relative;z-index:99;width:%?169.85?%;height:%?60?%;border-radius:%?40?%;background:#fff;display:flex;justify-content:center;align-items:center}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .tagType[data-v-0fc7107c]{padding:0 %?10?%;height:%?40?%;border-radius:%?8?% 0 %?20?% 0;background:linear-gradient(135deg,var(--custom-brand-color-80),var(--custom-brand-color));position:absolute;top:0;left:0;font-size:%?24?%;font-weight:400;color:#fff;text-align:center;line-height:%?36?%}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .getLogo[data-v-0fc7107c]{width:%?124?%;height:%?104?%;border:3px solid var(--custom-brand-color-50);display:flex;align-items:center;justify-content:center;border-radius:50%;position:absolute;bottom:%?-44?%;right:%?-40?%;-webkit-transform:rotate(-32deg);transform:rotate(-32deg)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item .getLogo .logo-child[data-v-0fc7107c]{width:%?112?%;height:%?92?%;background:var(--custom-brand-color-50);border-radius:50%;text-align:center;line-height:%?60?%;font-size:%?32?%;font-family:PingFang SC;font-weight:600;color:#fff}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use[data-v-0fc7107c]{background:var(--custom-brand-color-10)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use .item-left .content-text[data-v-0fc7107c]{color:var(--custom-brand-color)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use .item-left .content-date[data-v-0fc7107c]{color:var(--custom-brand-color)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use .item-right[data-v-0fc7107c]{border-color:var(--custom-brand-color)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use .item-right .money[data-v-0fc7107c]{color:var(--custom-brand-color)}.discount-popup .discount-warp .warp-type_02 .warp-discount-list .warp-discount-item-use .item-right .check[data-v-0fc7107c]{background:linear-gradient(135deg,var(--custom-brand-color-80),var(--custom-brand-color));color:#fff}.discount-popup .bottom-btn[data-v-0fc7107c]{width:100%;height:%?120?%;background:#fff;position:fixed;bottom:0;left:0;display:flex;align-items:center;justify-content:center;z-index:100}.discount-popup .bottom-btn .btn[data-v-0fc7107c]{width:%?642?%;height:%?82?%;border-radius:%?40?%;background:var(--custom-brand-color);text-align:center;line-height:%?82?%;color:#fff;font-size:%?30?%}.discount-popup .discount-null[data-v-0fc7107c]{width:100%;text-align:center;padding:%?10?% 0}',""]),t.exports=e},ed83:function(t,e,i){"use strict";(function(t){var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.Behavior=function(t){return t},e.Component=function(e){var i=function(e){var i=e.data,o=e.options,n=e.methods,a=e.behaviors,s=e.lifetimes,r=e.observers,d=e.relations,l=e.properties,f=e.pageLifetimes,h=e.externalClasses,m={mixins:[],props:{},watch:{},mpOptions:{mpObservers:[]}};return c(m),u(i,m),function(t,e){if(!t)return;e.mpOptions.options=t}(o,m),D(n,m),z(a,m),function(t,e){if(!t)return;S(t,e)}(s,m),function(t,e){if(!t)return;var i=e.mpOptions.mpObservers;Object.keys(t).forEach((function(e){i.push({paths:T(e),observer:t[e]})}))}(r,m),function(e,i){if(!e)return;Object.keys(e).forEach((function(i){var o=e[i];o.name=i,o.target=o.target?String(o.target):function(t,e){0===e.indexOf("/")&&(t="");var i=t.split("/"),o=e.split("/");i.pop();while(o.length){var n=o.shift();""!==n&&"."!==n&&(".."!==n?i.push(n):i.pop())}return i.join("/")}(t.__wxRoute,i)})),i.mpOptions.relations=e}(d,m),x(l,m),function(t,e){if(!t)return;k.forEach((function(i){var o=t[i];p(o)&&(e[w[i]]=o)}))}(f,m),function(t,e){if(!t)return;Array.isArray(t)||(t=[t]);e.mpOptions.externalClasses=t,e.mpOptions.properties||(e.mpOptions.properties=Object.create(null));t.forEach((function(t){e.mpOptions.properties[v(t)]={type:String,value:""}}))}(h,m),S(e,m),C(e),m}(e);i.mixins.unshift(K),i.mpOptions.path=t.__wxRoute,function(t){t.onServiceAttached||(t.onServiceAttached=[]);t.onServiceAttached.push((function(){M(this,"linked")}))}(i),t.__wxComponents[t.__wxRoute]=i},e.Page=function(e){var i=function(t){var e={mixins:[],mpOptions:{}};return c(e),u(t.data,e),function(t,e){var i=Object.create(null);Object.keys(t).forEach((function(e){var o=t[e];p(o)&&-1===y.indexOf(e)&&(i[e]=o)})),e.methods=i}(t,e),function(t,e){Object.keys(t).forEach((function(i){-1!==y.indexOf(i)&&(e[i]=t[i])}))}(t,e),e}(e);i.mixins.unshift(K),i.mpOptions.path=t.__wxRoute,t.__wxComponents[t.__wxRoute]=i},e.nextTick=e.default=void 0;var n=o(i("39d8")),a=o(i("5de6"));i("bf0f"),i("5c47"),i("a1c1"),i("dc8a"),i("2797"),i("5ef2"),i("aa9c"),i("fd3c"),i("aa77"),i("6a54"),i("473f"),i("f7a5"),i("20f3"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("64aa"),i("c223"),i("d4b5"),i("a03a"),i("8f71"),i("3efd");var s,r=o(i("9b8e"));function u(t,e){t&&(e.mpOptions.data=t)}function c(e){e.components=t.__wxVueOptions.components}var d=Object.prototype.toString,l=Object.prototype.hasOwnProperty;function p(t){return"function"===typeof t}function f(t){return"[object Object]"===d.call(t)}function h(t,e){return l.call(t,e)}function m(){}var g=/-(\w)/g,v=function(t){var e=Object.create(null);return function(i){var o=e[i];return o||(e[i]=t(i))}}((function(t){return t.replace(g,(function(t,e){return e?e.toUpperCase():""}))})),b={created:"onServiceCreated",attached:"onServiceAttached",ready:"mounted",moved:"moved",detached:"destroyed"},_=Object.keys(b),w={show:"onPageShow",hide:"onPageHide",resize:"onPageResize"},k=Object.keys(w),y=["onLoad","onShow","onReady","onHide","onUnload","onPullDownRefresh","onReachBottom","onShareAppMessage","onPageScroll","onResize","onTabItemTap"];function x(t,e){t&&(e.mpOptions.properties=t)}function D(t,e){t&&(t.$emit&&(console.warn('Method "$emit" conflicts with an existing Vue instance method'),delete t.$emit),e.methods=t)}function S(t,e){_.forEach((function(i){h(t,i)&&(e[b[i]]||(e[b[i]]=[])).push(t[i])}))}var $={"wx://form-field":{},"wx://component-export":{}};function C(t,e){(function(t){var e=t.behaviors,i=t.definitionFilter,o=[];if(Array.isArray(e)&&e.forEach((function(e){e="string"===typeof e?$[e]:e,e.definitionFilter&&(o.push(e.definitionFilter),e.definitionFilter.call(null,t,[]))})),p(i));})(t)}var O={"wx://form-field":{beforeCreate:function(){var t=this.$options.mpOptions;t.properties||(t.properties=Object.create(null));var e=t.properties;h(e,"name")||(e.name={type:String}),h(e,"value")||(e.value={type:String})}}};function z(t,e){t&&t.forEach((function(t){"string"===typeof t?O[t]&&e.mixins.push(O[t]):e.mixins.push(function(t){var e=t.data,i=t.methods,o=t.behaviors,n=t.properties,a={watch:{},mpOptions:{mpObservers:[]}};return u(e,a),D(i,a),z(o,a),x(n,a),S(t,a),C(t),a}(t))}))}function T(t){return t.split(",").map((function(t){return function(t){return t.split(".")}(t)}))}function j(t,e,i,o){if(e){var n="_$".concat(t,"Handlers");(o[n]||(o[n]=[])).push((function(){e.call(o,i)}))}}function I(t,e,i){var o=t.name,n=i._$relationNodes||(i._$relationNodes=Object.create(null));(n[o]||(n[o]=[])).push(e),j("linked",t["linked"],e,i)}function P(t,e,i){j("unlinked",t["unlinked"],e,i)}function E(t,e,i){var o=t&&t.$options.mpOptions&&t.$options.mpOptions.relations;if(!o)return[];var n=Object.keys(o).find((function(t){var n=o[t];return n.target===e&&n.type===i}));return n?[o[n],t]:[]}function N(t,e,i){var o=i(t,t.$options.mpOptions.path),n=(0,a.default)(o,2),s=n[0],r=n[1];s&&(I(s,t,r),I(e,r,t),P(s,t,r),P(e,r,t))}function A(t){var e=t.$options.mpOptions||{},i=e.relations;i&&Object.keys(i).forEach((function(e){(function(t,e){var i=t.type;"parent"===i?N(e,t,(function(t,e){return E(t.$parent,e,"child")})):"ancestor"===i&&N(e,t,(function(t,e){var i=t.$parent;while(i){var o=E(i,e,"descendant");if(o.length)return o;i=i.$parent}return[]}))})(i[e],t)}))}function M(t,e){var i=t["_$".concat(e,"Handlers")];i&&i.forEach((function(t){return t()}))}var R={enumerable:!0,configurable:!0,get:m,set:m};function L(t,e,i){R.get=function(){return this[e][i]},R.set=function(t){this[e][i]=t},Object.defineProperty(t,i,R)}function B(t,e){var i=this;f(t)&&(Object.keys(t).forEach((function(e){(function(t,e,i){var o=t.replace(/\[(\d+?)\]/g,".$1").split(".");return o.reduce((function(t,i,n){if(n!==o.length-1)return"undefined"===typeof t[i]&&(t[i]={}),t[i];t[i]=e}),i),1===o.length})(e,t[e],i.data)&&!h(i,e)&&L(i,"__data__",e)})),this.$forceUpdate(),p(e)&&this.$nextTick(e))}var U=Object.prototype.toString,G=function(t){return function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0,o=U.call(i);if("[object Array]"===o)return e=i.slice(0),e;if("[object Object]"===o){for(var n in i)e[n]=t(e[n],i[n]);return e}if("[object Date]"===o)return new Date(i.getTime());if("[object RegExp]"===o){var a=String(i),s=a.lastIndexOf("/");return new RegExp(a.slice(1,s),a.slice(s+1))}return i}("[object Array]"===U.call(t)?[]:{},t)},F=(s={},(0,n.default)(s,String,""),(0,n.default)(s,Number,0),(0,n.default)(s,Boolean,!1),(0,n.default)(s,Object,null),(0,n.default)(s,Array,[]),(0,n.default)(s,null,null),s);function q(t){return F[t]}function X(t){return f(t)?t.type:t}function Y(t,e,i,o){var n=i[t];if(void 0!==n){var a=e[t],s=X(a);n=J(n,s);var r=a&&a.observer;return r&&setTimeout((function(){Z(r,o,n)}),4),n}return function(t){return f(t)?h(t,"value")?t.value:q(t.type):q(t)}(e[t])}function J(t,e){return e===Boolean?!!t:e===String?String(t):t}function Z(t,e,i,o){try{"function"===typeof t?t.call(e,i,o):"string"===typeof t&&"function"===typeof e[t]&&e[t](i,o)}catch(n){console.error("execute observer ".concat(t," callback fail! err: ").concat(n))}}function H(t){var e=t.$options.mpOptions&&t.$options.mpOptions.properties,i=t.$options.propsData;i&&e&&Object.keys(e).forEach((function(o){h(i,o)&&(t[o]=J(i[o],X(e[o])))}))}function V(t){var e=JSON.parse(JSON.stringify(t.$options.mpOptions.data||{}));t["__data__"]=e;var i={get:function(){return t["__data__"]},set:function(e){t["__data__"]=e}};Object.defineProperties(t,{data:i,properties:i}),t.setData=B,function(t,e){var i=t.$options.mpOptions.properties;if(i){var o=G(t.$options.propsData)||{},n=function(n){var a=!!f(i[n])&&i[n].observer,s=Y(n,i,o,t);Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:function(){return s},set:function(e){var i=s;e===s||e!==e&&s!==s||(s=Array.isArray(e)?e.slice(0):e,a&&Z(a,t,e,i),t.$forceUpdate())}})};for(var a in i)n(a)}}(t,e),Object.keys(e).forEach((function(e){L(t,"__data__",e)}))}var K={beforeCreate:function(){this._renderProxy=this,this._$self=this,this._$noop=m},created:function(){V(this),function(t){var e=t.$emit;t.triggerEvent=function(i,o,n){var a={dataset:t.$el.dataset},s={target:a,currentTarget:a,detail:o,preventDefault:m,stopPropagation:m};e.call(t,i,s)},t.$emit=function(){t.triggerEvent.apply(t,arguments)},t.getRelationNodes=function(e){return(t._$relationNodes&&t._$relationNodes[e]||[]).filter((function(t){return!t._isDestroyed}))},t._$updateProperties=H}(this),A(this)},mounted:function(){(function(t){var e=t.$options.watch;e&&Object.keys(e).forEach((function(i){var o=e[i];if(o.mounted){var n=t[i],a=o.handler;"string"===typeof a&&(a=t[a]),a&&a.call(t,n,n)}}))})(this)},destroyed:function(){M(this,"unlinked")}};t.__wxRoute="",t.__wxComponents=Object.create(null),t.__wxVueOptions=Object.create(null);var Q=r.default.nextTick;e.nextTick=Q;var W=uni.__$wx__,tt=W;e.default=tt}).call(this,i("0ee4"))},ee53:function(t,e,i){"use strict";var o=i("a43d"),n=i.n(o);n.a},fc35:function(t,e,i){"use strict";i.r(e);var o=i("c9b2"),n=i("955e");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("77da");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"0fc7107c",null,!1,o["a"],void 0);e["default"]=r.exports}}]);