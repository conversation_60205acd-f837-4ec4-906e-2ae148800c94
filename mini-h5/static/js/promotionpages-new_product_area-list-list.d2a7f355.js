(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-new_product_area-list-list"],{"2d01":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},4202:function(t,e,i){"use strict";i.r(e);var o=i("f9bb"),n=i("6a39");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("b733");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"60492653",null,!1,o["a"],void 0);e["default"]=r.exports},5283:function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("5c47"),i("5ef2"),i("aa9c"),i("c223");var n=o(i("2634")),a=o(i("2fdc")),s={data:function(){return{systemInfo:null,scrollPosition:0,isEmit:!0,postendSkuId:[],tmpSkuId:[]}},onLoad:function(){this.systemInfo=uni.getSystemInfoSync()},onReady:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.againDealWith();case 2:case"end":return e.stop()}}),e)})))()},onPageScroll:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.scrollTouch(t);case 2:case"end":return i.stop()}}),i)})))()},methods:{againDealWith:function(){var t=arguments,e=this;return(0,a.default)((0,n.default)().mark((function i(){var o;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:o=t.length>0&&void 0!==t[0]&&t[0],o&&(e.postendSkuId=[]),setTimeout((0,a.default)((0,n.default)().mark((function t(){return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getExposeGoods();case 2:case"end":return t.stop()}}),t)}))),2e3);case 3:case"end":return i.stop()}}),i)})))()},scrollTouch:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function i(){var o;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(o=t.scrollTop,!(Math.abs(o-e.scrollPosition)>50&&e.isEmit)){i.next=7;break}return e.isEmit=!1,e.scrollPosition=o,i.next=6,e.getExposeGoods();case 6:e.isEmit=!0;case 7:case"end":return i.stop()}}),i)})))()},checkNoneInScreen:function(t){return t.top>-1&&t.top<=this.systemInfo.screenHeight&&t.left>-1&&t.left<=this.systemInfo.screenWidth},getExposeGoods:function(){var t=this,e=uni.createSelectorQuery();return new Promise((function(i,o){e.selectAll(".expose_goods_index").boundingClientRect((function(e){for(var o=0;o<e.length;o++)if(t.checkNoneInScreen(e[o])){var n=e[o].dataset["expose_goods_sku"];-1==t.postendSkuId.indexOf(n)&&-1==t.tmpSkuId.indexOf(n)&&t.tmpSkuId.push(n)}t.toExposes(),i()})).exec()}))},toExposes:function(){for(var t=[],e=0;e<this.tmpSkuId.length;e++)t.push({sku_id:this.tmpSkuId[e]});this.postendSkuId=this.postendSkuId.concat(this.tmpSkuId),this.$buriedPoint.exposeGoods(t,"sku_id"),this.tmpSkuId=[]}}};e.default=s},"59d8":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-60492653]{width:100%;text-align:center}[data-v-60492653] .mescroll-upwarp{padding:0!important;margin-bottom:0;min-height:0;line-height:0}.container[data-v-60492653]{width:100%;background:transparent;border-radius:%?20?% %?20?% 0 0;overflow:hidden;padding-bottom:%?20?%;padding-top:%?20?%;box-sizing:border-box;z-index:1}.container .nav_tab[data-v-60492653]{width:100%;height:%?100?%;line-height:%?100?%;display:flex;align-items:center}.container .nav_tab .tab_item[data-v-60492653]{width:33%;text-align:center;color:#999;font-size:%?28?%}.container .nav_tab .tab_item.active[data-v-60492653]{color:var(--custom-brand-color)}.container .goods_list .goods_item[data-v-60492653]{width:%?690?%;box-sizing:border-box;background:#fff;margin:0 auto;margin-bottom:%?20?%;padding:%?20?% %?24?%;display:flex;align-items:center;justify-content:space-between;border-radius:%?20?%}.container .goods_list .goods_item[data-v-60492653]:last-child{margin-bottom:%?100?%}.container .goods_list .goods_item .thumbImage[data-v-60492653]{position:relative;width:%?240?%;height:%?240?%;border-radius:%?8?%;margin-right:%?26?%;overflow:hidden}.container .goods_list .goods_item .thumbImage uni-image[data-v-60492653]{width:100%;height:100%}.container .goods_list .goods_item .thumbImage .over[data-v-60492653]{width:%?120?%;height:%?120?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.container .goods_list .goods_item .goods_info[data-v-60492653]{width:calc(100% - %?270?%);height:%?240?%;position:relative}.container .goods_list .goods_item .goods_info .goods_name[data-v-60492653]{font-size:%?26?%;color:#333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.container .goods_list .goods_item .goods_info .goods_name .tag[data-v-60492653]{padding:%?5?%;background:linear-gradient(270deg,var(--custom-brand-color-70),var(--custom-brand-color));border-radius:%?8?%;font-size:%?20?%;color:#fff;text-align:center;line-height:%?24?%;margin-right:%?10?%}.container .goods_list .goods_item .goods_info .bottom[data-v-60492653]{position:absolute;bottom:0;width:100%;line-height:1}.container .goods_list .goods_item .goods_info .bottom .distributor uni-text[data-v-60492653]:first-child{height:%?40?%;background:linear-gradient(90deg,var(--custom-brand-color-70),var(--custom-brand-color));border-radius:%?4?% 0 0 %?4?%;padding:0 %?9?% 0 %?4?%;box-sizing:border-box;font-size:%?24?%;font-weight:500;color:#fff;display:inline-block}.container .goods_list .goods_item .goods_info .bottom .distributor uni-text[data-v-60492653]:last-child{height:%?40?%;background:var(--custom-brand-color-10);border-radius:%?0?% %?4?% %?4?% %?0?%;padding:0 %?8?%;box-sizing:border-box;font-size:%?24?%;font-weight:700;color:var(--custom-brand-color);display:inline-block}.container .goods_list .goods_item .goods_info .bottom .sale_price[data-v-60492653]{font-size:%?36?%;color:var(--custom-brand-color);font-weight:700;line-height:1}.container .goods_list .goods_item .goods_info .bottom .sale_price uni-text[data-v-60492653]{font-size:%?26?%;font-weight:400}.container .goods_list .goods_item .goods_info .bottom .market_price[data-v-60492653]{line-height:1;text-decoration:line-through;color:#999;font-size:%?24?%}.container .goods_list .goods_item .goods_info .bottom .buy_btn[data-v-60492653]{position:absolute;right:0;bottom:0;background:linear-gradient(-90deg,var(--custom-brand-color-70),var(--custom-brand-color));height:%?56?%;line-height:%?56?%;border-radius:%?28?%;width:%?148?%;text-align:center;font-size:%?24?%;color:#fff}',""]),t.exports=e},"6a39":function(t,e,i){"use strict";i.r(e);var o=i("87be"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"87be":function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("f7a5"),i("fd3c"),i("c223");var n=o(i("2634")),a=o(i("2fdc")),s=o(i("85bf")),r=o(i("5283")),d=o(i("2d01")),u=i("4b89"),c=o(i("f8de")),l=o(i("d817")),f={data:function(){return{topic_name:"新品专区",dataList:[],headerTop:0,is_shopper:0,loading:!0,isOnXianMaiApp:u.isOnXianMaiApp}},components:{uniNavBar:l.default},mixins:[r.default,d.default,c.default],onLoad:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return u.isOnXianMaiApp&&(t.headerTop=88),e.next=3,s.default.wait_staticLogin_success();case 3:case"end":return e.stop()}}),e)})))()},onShow:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$langConfig.refresh(),uni.setNavigationBarTitle({title:"新品专区列表"}),uni.getStorageSync("is_shopper")&&(t.is_shopper=uni.getStorageSync("is_shopper"));case 3:case"end":return e.stop()}}),e)})))()},methods:{getSharePageParams:function(){var t=this.$util.unifySharePageParams("/promotionpages/new_product_area/list/list",this.topic_name?this.topic_name:"活动已结束","",{},this.$util.img("public/static/youpin/task_share.jpg"));return t},h5Share:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function t(){var i,o;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=e.$util.deepClone(e.getSharePageParams()),o=window.location.origin+e.$router.options.base+i.link.slice(1),i.link=o,i.desc=i.title,i.title="先迈商城",t.next=7,e.$util.publicShare(i);case 7:case"end":return t.stop()}}),t)})))()},getData:function(t){var e=this;this.mescroll=t,1==t.num&&(this.dataList=[]),this.loading=!0,this.$api.sendRequest({url:this.$apiUrl.newProductAreaGoodsList,data:{page_size:t.size,page:t.num,topic_id:93},success:function(i){if(e.loading=!1,e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.h5Share(i),0!=i.code)return e.$util.showToast({title:i.message}),e.loading=!1,void t.endErr();t.endSuccess(i.data.list.length),i.data.list=i.data.list.map((function(t){return t.goods_image=e.$util.imageCdnResize(t.goods_image),t})),e.dataList=e.dataList.concat(i.data.list)},fail:function(i){e.loading=!1,t.endErr(),e.$util.showToast({title:i.message})}})},toProductDetail:function(t){var e=this;this.$util.toProductDetail(t,(function(i){e.$buriedPoint.diyReportTopicPageInteractionEvent({diy_action_type:"click_goods",diy_template_name:"template_1",diy_product_name:t.goods_name,diy_activity_title:e.topic_name,diy_product_link:i})}))},imageError:function(t,e){e&&e[t]&&(e[t].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate())}},onShareAppMessage:function(t){var e=this.getSharePageParams(),i=e.title,o=e.link,n=e.imageUrl;e.query;return this.$buriedPoint.pageShare(o,n,i)},onShareTimeline:function(t){var e=this.getSharePageParams(),i=e.title,o=e.imageUrl,n=e.query;return{title:i,imageUrl:o,query:n,success:function(t){},fail:function(t){}}}};e.default=f},b733:function(t,e,i){"use strict";var o=i("e923"),n=i.n(o);n.a},e923:function(t,e,i){var o=i("59d8");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("021cf7c0",o,!0,{sourceMap:!1,shadowMode:!1})},f8de:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i("4b89"),n={onReady:function(){this.appCurrentPages=getCurrentPages().length},methods:{appGoBack:function(){o.isOnXianMaiApp&&this.appCurrentPages<=1?(0,o.goClosePage)("0"):uni.navigateBack()}}};e.default=n},f9bb:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniNavBar:i("d817").default,nsEmpty:i("dc6c").default,loadingCover:i("5510").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"main",style:[t.themeColorVar]},[t.isOnXianMaiApp?i("uni-nav-bar",{attrs:{"left-icon":"back",border:!1,fixed:!0},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.appGoBack.apply(void 0,arguments)}}},[[i("v-uni-view",{staticClass:"page-title"},[t._v(t._s(t.topic_name))])]],2):t._e(),i("mescroll-uni",{ref:"mescroll",attrs:{size:10,top:t.headerTop},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)},scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollTouch.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"container"},[t.dataList.length?i("v-uni-view",{staticClass:"goods_list"},t._l(t.dataList,(function(e,o){return i("v-uni-view",{key:e.goods_id,staticClass:"goods_item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"thumbImage"},[i("v-uni-image",{staticClass:"expose_goods_index",attrs:{src:t.$util.img(e.goods_image),mode:"aspectFill","data-expose_goods_sku":e.sku_id},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(o,t.dataList)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"goods_info"},[i("v-uni-view",{staticClass:"goods_name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"bottom"},[t.is_shopper?i("v-uni-view",{staticClass:"distributor"},[i("v-uni-text",[t._v("分销商价")]),i("v-uni-text",[t._v("￥"+t._s(e.vip_price))])],1):t._e(),i("v-uni-view",{staticClass:"sale_price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.sale_price))],1),i("v-uni-view",{staticClass:"market_price"},[t._v("￥"+t._s(e.market_price))]),i("v-uni-view",{staticClass:"buy_btn"},[t._v("立即抢购")])],1)],1)],1)})),1):t._e(),t.loading||t.dataList.length?t._e():i("v-uni-view",[i("ns-empty",{attrs:{fixed:!1}})],1)],1)],1)],2),i("loading-cover",{ref:"loadingCover"})],1)},a=[]}}]);