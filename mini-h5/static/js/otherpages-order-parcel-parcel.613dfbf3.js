(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-order-parcel-parcel"],{1377:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"parcel",style:[e.themeColorVar]},[i("v-uni-view",{staticClass:"title"},[e._v("该订单已拆成"+e._s(e.parcelList.length)+"个包裹")]),i("v-uni-view",{staticClass:"parcel-box"},e._l(e.parcelList,(function(t,a){return i("v-uni-view",{key:a,staticClass:"parcel-list"},[i("v-uni-view",{staticClass:"name"},[i("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/parcel.png"),alt:""}}),i("v-uni-view",[e._v(e._s(t.package_name))])],1),i("v-uni-view",{staticClass:"info-box"},[i("v-uni-view",{staticClass:"info"},[e._v(e._s(t.express_company_name)+"："+e._s(t.delivery_no))]),3==e.orderStatus?i("v-uni-view",{staticClass:"btn",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.detailFun(t)}}},[e._v("查看详情")]):i("v-uni-view",{staticClass:"btn",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.copy(t.delivery_no)}}},[e._v("复制")])],1)],1)})),1)],1)},r=[]},"2d01":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"40e6":function(e,t,i){"use strict";var a=i("5caf"),r=i.n(a);r.a},"574e":function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-1bf37618]{width:100%;text-align:center}.parcel .title[data-v-1bf37618]{display:flex;align-items:center;font-size:%?22?%;color:var(--custom-brand-color);height:%?56?%;background-color:var(--custom-brand-color-10);padding-left:%?24?%}.parcel .parcel-box .parcel-list[data-v-1bf37618]{position:relative;background-color:#fff;margin-bottom:%?20?%;padding:%?34?% %?24?% %?26?%}.parcel .parcel-box .parcel-list .name[data-v-1bf37618]{display:flex;align-items:center}.parcel .parcel-box .parcel-list .name uni-image[data-v-1bf37618]{width:%?28?%;height:%?28?%}.parcel .parcel-box .parcel-list .name uni-view[data-v-1bf37618]{font-size:%?24?%;color:#333;line-height:%?34?%;padding-left:%?8?%}.parcel .parcel-box .parcel-list .info-box[data-v-1bf37618]{display:flex;align-items:center;padding:%?16?% 0 0}.parcel .parcel-box .parcel-list .info-box .info[data-v-1bf37618]{font-size:%?24?%;color:#999;line-height:%?34?%}.parcel .parcel-box .parcel-list .info-box .copy[data-v-1bf37618]{font-size:%?24?%;color:var(--custom-brand-color);line-height:%?34?%;margin-left:%?24?%}.parcel .parcel-box .parcel-list .btn[data-v-1bf37618]{position:absolute;bottom:%?30?%;right:%?24?%;display:flex;justify-content:center;align-items:center;width:%?136?%;height:%?48?%;border:1px solid var(--custom-brand-color);border-radius:%?48?%;font-size:%?24?%;color:var(--custom-brand-color)}',""]),e.exports=t},"5caf":function(e,t,i){var a=i("574e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=i("967d").default;r("0939d075",a,!0,{sourceMap:!1,shadowMode:!1})},"8dc3":function(e,t,i){"use strict";i.r(t);var a=i("1377"),r=i("e80e");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("40e6");var o=i("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"1bf37618",null,!1,a["a"],void 0);t["default"]=s.exports},"9b3e":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("2d01")),n={mixins:[r.default],data:function(){return{parcelList:[],order_id:"",orderStatus:""}},onLoad:function(e){var t=e.order_id;this.order_id=t},onShow:function(){var e=this;uni.showLoading({title:"加载中",mask:!0}),this.$api.sendRequest({url:this.$apiUrl.getPackageList,data:{order_id:this.order_id},success:function(t){uni.hideLoading(),0==t.code?(e.parcelList=t.data.list,e.orderStatus=t.data.order_status):e.$util.showToast({title:t.message})},fali:function(e){uni.hideLoading()}})},methods:{detailFun:function(e){this.$util.redirectTo("/pages/order/logistics/logistics",{order_id:this.order_id,delivery_no:e.delivery_no})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),i=t.title,a=t.link,r=t.imageUrl;t.query;return this.$buriedPoint.pageShare(a,r,i)}};t.default=n},e80e:function(e,t,i){"use strict";i.r(t);var a=i("9b3e"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a}}]);