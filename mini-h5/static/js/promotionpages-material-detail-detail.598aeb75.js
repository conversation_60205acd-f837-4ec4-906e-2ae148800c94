(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-material-detail-detail"],{"125e":function(t,e,i){"use strict";var a=i("d653"),n=i.n(a);n.a},1841:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-33946380]{width:100%;text-align:center}.material[data-v-33946380]{padding:%?24?% %?24?%;padding-bottom:%?50?%;box-sizing:border-box}.material-title[data-v-33946380]{font-size:%?30?%;font-weight:700;line-height:%?40?%;color:#333}.material-date[data-v-33946380]{font-size:%?24?%;font-weight:400;line-height:%?42?%;color:#999;margin:0;margin-top:%?20?%}.material-desc[data-v-33946380]{margin-top:%?20?%;border:%?2?% solid #aaa;padding:%?24?%;box-sizing:border-box;display:flex;flex-direction:column;align-items:center;border-radius:%?20?%}.material-desc-text[data-v-33946380]{position:relative;font-size:%?30?%;font-weight:400;line-height:%?40?%;color:#333;text-indent:2em}.material-desc-text .quotes-left[data-v-33946380]{width:%?18?%;height:%?18?%;position:absolute;left:0;top:0}.material-desc-text .quotes-right[data-v-33946380]{width:%?18?%;height:%?18?%;position:absolute;right:0;bottom:%?0?%}.material-desc-op[data-v-33946380]{font-size:%?24?%;font-weight:400;color:#fff;padding:%?4?% %?32?%;box-sizing:border-box;background:#f33;border-radius:%?24?%;display:inline-block;margin-top:%?20?%;line-height:%?40?%}.material-content[data-v-33946380]{margin-top:%?20?%;border:%?2?% solid #aaa;padding:%?24?%;box-sizing:border-box;border-radius:%?20?%}.material-content-imgs-list[data-v-33946380]{margin-left:%?-4?%;display:flex;flex-wrap:wrap}.material-content-imgs-list uni-image[data-v-33946380]{width:%?200?%;height:%?200?%;display:block;margin-left:%?14?%;margin-top:%?14?%}.material-content-video[data-v-33946380]{display:flex;justify-content:center}.material-content-video uni-video[data-v-33946380]{width:%?600?%;height:%?500?%}.material-content-op[data-v-33946380]{margin-top:%?20?%;text-align:center}.material-content-op-download[data-v-33946380]{font-size:%?24?%;font-weight:400;color:#fff;padding:%?4?% %?32?%;box-sizing:border-box;background:#f33;border-radius:%?24?%;display:inline-block;line-height:%?40?%}',""]),t.exports=e},6199:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={loadingCover:i("5510").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"material"},[i("v-uni-view",{staticClass:"material-title"},[t._v(t._s(t.contentDict.title))]),i("v-uni-view",{staticClass:"material-date"},[t._v(t._s(t.contentDict.update_time))]),i("v-uni-view",{staticClass:"material-desc"},[i("v-uni-view",{staticClass:"material-desc-text"},[i("v-uni-image",{staticClass:"quotes-left",attrs:{src:t.$util.img("public/static/youpin/quotes-left.png")}}),t._v(t._s(t.contentDict.content)),i("v-uni-image",{staticClass:"quotes-right",attrs:{src:t.$util.img("public/static/youpin/quotes-right.png")}})],1),i("v-uni-text",{staticClass:"material-desc-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.to_copy_text.apply(void 0,arguments)}}},[t._v("复制文案")])],1),i("v-uni-view",{staticClass:"material-content"},[0==t.type?i("v-uni-view",{staticClass:"material-content-imgs-list"},t._l(t.contentDict.resources,(function(e,a){return i("v-uni-image",{key:a,attrs:{src:e.path,mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}})})),1):t._e(),1==t.type?i("v-uni-view",{staticClass:"material-content-video"},[i("v-uni-video",{attrs:{id:"myVideo",src:t.contentDict.resources[0].path,poster:t.contentDict.resources[0].cover,controls:!0}})],1):t._e(),i("v-uni-view",{staticClass:"material-content-op"})],1)],1),i("loading-cover",{ref:"loadingCover"})],1)},r=[]},8099:function(t,e,i){var a=i("1841");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("47ee4568",a,!0,{sourceMap:!1,shadowMode:!1})},"85ec":function(t,e,i){"use strict";i.r(e);var a=i("95fd"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"922a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-33946380]{width:100%;text-align:center}uni-page-body[data-v-33946380]{background-color:#fff}body.?%PAGE?%[data-v-33946380]{background-color:#fff}',""]),t.exports=e},"95fd":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("f7a5"),i("fd3c");var n=a(i("2634")),r=a(i("2fdc")),o=a(i("7c8d")),s=a(i("85bf")),c=i("d64b"),l={data:function(){return{material_id:null,type:0,contentDict:{}}},onLoad:function(t){var e=this;return(0,r.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:t.scene&&(0,c.scenePare)(!1,t),e.material_id=t.material_id;case 2:case"end":return i.stop()}}),i)})))()},onShow:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,s.default.wait_staticLogin_success();case 3:return e.next=5,t.getData();case 5:return i=t.$util.deepClone(t.getSharePageParams()),a=window.location.origin+t.$router.options.base+i.link.slice(1),i.link=a,e.next=10,t.$util.publicShare(i);case 10:case"end":return e.stop()}}),e)})))()},methods:{getData:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:o.default.materialDetailUrl,async:!1,data:{material_id:t.material_id}});case 2:if(i=e.sent,t.$refs.loadingCover&&t.$refs.loadingCover.hide(),0==i.code){e.next=7;break}return uni.showToast({title:i.message,mask:!0,icon:"none",duration:3e3}),e.abrupt("return");case 7:t.contentDict=i.data,"friend"==t.contentDict.type&&("image"==t.contentDict.resources_type&&(t.type=0),"video"==t.contentDict.resources_type&&(t.type=1));case 9:case"end":return e.stop()}}),e)})))()},imageError:function(t){this.contentDict.resources[t].path=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},to_copy_text:function(){this.$util.copy(this.contentDict.content)},saveFiles:function(t){this.$util.downloadFilesSavePhotosAlbum(this.contentDict.resources.map((function(t){return t.path})),t)},getSharePageParams:function(){var t=this.$util.unifySharePageParams("/promotionpages/material/detail/detail","先迈商城",this.contentDict.title,{material_id:this.material_id},"image"==this.contentDict.resources_type?this.contentDict.resources[0].path:this.contentDict.resources[0].cover);return t}},onShareAppMessage:function(t){var e=this.getSharePageParams();return this.$buriedPoint.pageShare(e.link,e.imageUrl,e.desc)}};e.default=l},c7da:function(t,e,i){"use strict";i.r(e);var a=i("6199"),n=i("85ec");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("125e"),i("d9bc");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"33946380",null,!1,a["a"],void 0);e["default"]=s.exports},d653:function(t,e,i){var a=i("922a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("8cd50f32",a,!0,{sourceMap:!1,shadowMode:!1})},d9bc:function(t,e,i){"use strict";var a=i("8099"),n=i.n(a);n.a}}]);