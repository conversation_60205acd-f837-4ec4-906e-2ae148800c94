(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-real_name_authentication-real_name_authentication"],{"2d01":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"3f14":function(e,t,r){"use strict";var a=r("4dc4"),i=r.n(a);i.a},"4dc4":function(e,t,r){var a=r("d21b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=r("967d").default;i("bd90f54e",a,!0,{sourceMap:!1,shadowMode:!1})},"6b78":function(e,t,r){"use strict";r.r(t);var a=r("9c06"),i=r("8811");for(var n in i)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(n);r("3f14");var s=r("828b"),o=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"6307f1c0",null,!1,a["a"],void 0);t["default"]=o.exports},8469:function(e,t,r){r("23f4"),r("7d2f"),r("5c47"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){for(var r=0;r<t.length;r++){if(!t[r].checkType)return!0;if(!t[r].name)return!0;if(!t[r].errorMsg)return!0;if(!e[t[r].name])return this.error=t[r].errorMsg,!1;switch(t[r].checkType){case"custom":if("function"==typeof t[r].validate&&!t[r].validate(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"string":a=new RegExp("^.{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[r].checkRule+"}$");if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[r].name]))return this.error=t[r].errorMsg,!1;var i=t[r].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[r].name]>i[1]||e[t[r].name]<i[0])return this.error=t[r].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;i=t[r].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[r].name]>i[1]||e[t[r].name]<i[0])return this.error=t[r].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;i=t[r].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[r].name]>i[1]||e[t[r].name]<i[0])return this.error=t[r].errorMsg,!1;break;case"same":if(e[t[r].name]!=t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"notsame":if(e[t[r].name]==t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"phoneno":a=/^[1](([3][0-9])|([4][1,4-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"reg":a=new RegExp(t[r].checkRule);if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"in":if(-1==t[r].checkRule.indexOf(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"notnull":if(0==e[t[r].name]||void 0==e[t[r].name]||null==e[t[r].name]||e[t[r].name].length<1)return this.error=t[r].errorMsg,!1;break;case"lengthMin":if(e[t[r].name].length<t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"lengthMax":if(e[t[r].name].length>t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"bank_account":a=/^([1-9]{1})(\d{15}|\d{18})$/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"idCard":a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!a.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},8811:function(e,t,r){"use strict";r.r(t);var a=r("af67"),i=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=i.a},"9c06":function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return a}));var a={loadingCover:r("5510").default},i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"container",class:e.containerClass,style:[e.themeColorVar]},[r("v-uni-view",{staticClass:"top-tips",class:{"top-tips-not":!e.status}},[e.status?[r("v-uni-image",{staticClass:"top-tips-img",attrs:{src:e.$util.img("public/static/youpin/member/real_name.png")}}),r("v-uni-view",{staticClass:"top-tips-yes-title"},[e._v("实名认证成功")]),r("v-uni-view",{staticClass:"top-tips-yes-desc"},[e._v("实名认证后不可修改")])]:[r("v-uni-view",{staticClass:"top-tips-title"},[e._v("温馨提示：")]),r("v-uni-view",{staticClass:"top-tips-desc"},[e._v("1、请确保实名信息为本人身份信息，若因信息不正确导致充值或者提现不成功，后果由用户本人承担。")]),r("v-uni-view",{staticClass:"top-tips-desc"},[e._v("2、如非法使用他人身份信息进行实名认证，由您本人承担法律责任。")]),r("v-uni-view",{staticClass:"top-tips-desc"},[e._v("3、一经认证，不得修改。")])]],2),r("v-uni-view",{staticClass:"form"},[r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("手机号码")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",disabled:!0,placeholder:"","placeholder-class":"placeholder"},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("真实姓名")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"text",disabled:e.status,placeholder:"请输入真实姓名","placeholder-class":"placeholder"},model:{value:e.formObj.name,callback:function(t){e.$set(e.formObj,"name",t)},expression:"formObj.name"}})],1)],1),r("v-uni-view",{staticClass:"form-item"},[r("v-uni-view",{staticClass:"label"},[e._v("身份证号")]),r("v-uni-view",{staticClass:"item"},[r("v-uni-input",{attrs:{type:"idcard",disabled:e.status,placeholder:"请输入身份证号","placeholder-class":"placeholder"},model:{value:e.formObj.idCard,callback:function(t){e.$set(e.formObj,"idCard",t)},expression:"formObj.idCard"}})],1)],1)],1),e.status?e._e():[e.noValue?r("v-uni-button",{staticClass:"submit disabled-btn",attrs:{type:"primary"}},[e._v("保存")]):r("v-uni-button",{staticClass:"submit",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveAuthentication.apply(void 0,arguments)}}},[e._v("保存")])],r("loading-cover",{ref:"loadingCover"})],2)},n=[]},af67:function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r("2634")),n=a(r("2fdc"));r("c223");var s=a(r("8469")),o=a(r("93d4")),c=a(r("85bf")),u=a(r("2d01")),l={mixins:[u.default],data:function(){return{mobile:"",formObj:{name:"",idCard:""},status:!1}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle},noValue:function(){return!this.formObj.name||!this.formObj.idCard},containerClass:function(){var e="".concat(this.themeStyle," ").concat(this.status?"":"container-not");return e}},onLoad:function(){},onShow:function(){var e=this;return(0,n.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$langConfig.refresh(),t.next=3,c.default.wait_staticLogin_success();case 3:return t.next=5,e.getMemberInfo();case 5:e.getData();case 6:case"end":return t.stop()}}),t)})))()},methods:{getData:function(){var e=this;this.$api.sendRequest({url:this.$apiUrl.memberAuthenticationInfo,success:function(t){var r=t.data;0==t.code&&(1==r.status?(e.formObj.name=o.default.addStar(r.auth_card_name,1,0),e.formObj.idCard=o.default.addStar(r.auth_card_no,4,4),e.status=!0):e.status=!1),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},authenticationValidata:function(){var e;e=[{name:"name",checkType:"required",errorMsg:"请输入真实姓名"},{name:"idCard",checkType:"idCard",errorMsg:"请输入正确的身份证号码"}];var t=s.default.check(this.formObj,e);return!!t||(this.$util.showToast({title:s.default.error}),!1)},saveAuthentication:function(){var e=this;if(this.authenticationValidata()){uni.showLoading({title:"保存信息中",mask:!0});var t={auth_name:e.formObj.name,auth_card_no:e.formObj.idCard};this.$api.sendRequest({url:this.$apiUrl.memberSaveAuthenticationInfo,data:t,success:function(t){uni.hideLoading(),0==t.code?(e.status=!0,e.formObj.name=o.default.addStar(e.formObj.name,1,0),e.formObj.idCard=o.default.addStar(e.formObj.idCard,4,4),e.$util.showToast({title:"实名认证成功"})):e.$util.showToast({title:t.message})}})}},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))},getMemberInfo:function(){var e=this;return(0,n.default)((0,i.default)().mark((function t(){var r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$api.sendRequest({url:"/api/member/info",async:!1});case 3:r=t.sent,0==r.code&&r.data.mobile&&(e.mobile=r.data.mobile.substr(0,3)+"****"+r.data.mobile.substr(7)),t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,null,[[0,7]])})))()}},onShareAppMessage:function(e){var t=this.getSharePageParams(),r=t.title,a=t.link,i=t.imageUrl;t.query;return this.$buriedPoint.pageShare(a,i,r)}};t.default=l},d21b:function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6307f1c0]{width:100%;text-align:center}.container[data-v-6307f1c0]{height:100vh}.container-not[data-v-6307f1c0]{background-color:#fff}.top-tips[data-v-6307f1c0]{width:%?710?%;margin:0 auto;height:%?440?%;display:flex;flex-direction:column;justify-content:center;align-items:center;background-color:#fff;margin-top:%?20?%;border-radius:%?20?%;box-sizing:border-box}.top-tips-not[data-v-6307f1c0]{background:var(--custom-brand-color-10);display:block;height:auto;padding:%?16?% %?20?%}.top-tips-img[data-v-6307f1c0]{width:%?200?%;height:%?200?%;display:block}.top-tips-yes-title[data-v-6307f1c0]{font-size:%?32?%;font-weight:400;line-height:%?37.5?%;color:var(--custom-brand-color);margin-top:%?36?%}.top-tips-yes-desc[data-v-6307f1c0]{font-size:%?28?%;font-weight:400;line-height:%?32.82?%;color:#a6a6a6;margin-top:%?16?%}.top-tips .top-tips-title[data-v-6307f1c0]{font-size:%?30?%;font-weight:700;line-height:%?52?%;color:var(--custom-brand-color)}.top-tips .top-tips-desc[data-v-6307f1c0]{font-size:%?26?%;font-weight:400;line-height:%?40?%;color:var(--custom-brand-color)}.form[data-v-6307f1c0]{margin:0 auto;margin-top:%?20?%;background:#fff;width:%?710?%;padding:0 %?30?%;box-sizing:border-box;border-radius:%?20?%}.form .form-item[data-v-6307f1c0]{display:flex;align-items:center;justify-content:space-between;height:%?116?%}.form .form-item[data-v-6307f1c0]:not(:first-child){border-top:%?2?% solid #f5f5f5}.form .form-item .label[data-v-6307f1c0]{width:%?190?%;font-size:%?30?%;font-weight:400;line-height:%?52?%;color:grey}.form .form-item .item[data-v-6307f1c0]{width:calc(100% - %?190?%);position:relative;line-height:%?116?%}.form .form-item .item uni-input[data-v-6307f1c0]{line-height:%?116?%;height:%?116?%;font-size:%?32?%;font-weight:400;color:#000}.form .form-item .item .picker[data-v-6307f1c0]{color:#9a9a9a;text-align:right}.form .form-item .item .bank-name[data-v-6307f1c0]{display:flex;justify-content:space-between}.form .form-item .item .verification-code[data-v-6307f1c0]{position:absolute;right:0;top:%?26?%;width:%?148?%;height:%?48?%;border:1px solid var(--custom-brand-color);border-radius:24px;line-height:%?48?%;text-align:center;color:var(--custom-brand-color);font-size:%?24?%}.form .form-item .item .verification-code.disabled[data-v-6307f1c0]{border:1px solid #eee;color:#bcbcbc}.form .placeholder[data-v-6307f1c0]{font-size:%?30?%;color:#ccc}.submit[data-v-6307f1c0]{margin-top:%?80?%;line-height:%?80?%}.submit.disabled-btn[data-v-6307f1c0]{background-color:#ccc!important}',""]),e.exports=t}}]);