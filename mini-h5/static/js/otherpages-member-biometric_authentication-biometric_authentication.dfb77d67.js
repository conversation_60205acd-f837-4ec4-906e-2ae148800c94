(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-biometric_authentication-biometric_authentication"],{"2d01":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"3f65":function(t,e,n){"use strict";n.r(e);var i=n("4cd9"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"4cd9":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),r=i(n("2fdc")),o=i(n("2d01")),s={mixins:[o.default],data:function(){return{AUTH_MODE:[],type:"attestation",d_type:null,soter_key:null,league_id:null,user_league_id:null,xm_token:null,league_type:null,plans_id:null,tip_list:["您正在进行新增加盟操作，该操作已开启指纹密码保护，请先完成指纹密码验证 。","您正在申请提前退回加盟操作，该操作已开启指纹密码保护，请先完成指纹密码验证 。","您正在开启指纹密码保护功能，请使用本机开锁指纹完成首次指纹密码验证校验操作 。"],isSuccess:!1,isLoading:!0}},onLoad:function(t){this.d_type=t.d_type||null,this.soter_key=t.soter_key||null,this.league_id=t.league_id||null,this.user_league_id=t.user_league_id||null,this.xm_token=t.xm_token||null,this.league_type=t.league_type||null,this.plans_id=t.plans_id||null,this.startCertification()},onShow:function(){this.$langConfig.refresh(),uni.setNavigationBarTitle({title:"先迈指纹识别"})},methods:{sendRecordResult:function(){var t=arguments,e=this;return(0,r.default)((0,a.default)().mark((function n(){var i;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=t.length>0&&void 0!==t[0]?t[0]:{},n.prev=1,n.next=4,e.$api.sendRequest({url:e.$apiUrl.recrodSoterCheckUrl,async:!1,data:i});case 4:n.sent,n.next=9;break;case 7:n.prev=7,n.t0=n["catch"](1);case 9:case"end":return n.stop()}}),n,null,[[1,7]])})))()},startCertification:function(){this.isLoading=!0,this.startAuth({xm_token:this.xm_token,soter_key:this.soter_key,d_type:this.d_type,league_id:this.league_id,league_type:this.league_type,plans_id:this.plans_id,user_league_id:this.user_league_id})},startSoterAuthentication:function(t){},checkIsEnrolled:function(t){},startAuth:function(t){},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),n=e.title,i=e.link,a=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,a,n)}};e.default=s},5040:function(t,e,n){"use strict";var i=n("9435"),a=n.n(i);a.a},"8bce":function(t,e,n){"use strict";n.r(e);var i=n("f5b4"),a=n("3f65");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("5040");var o=n("828b"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"1029930f",null,!1,i["a"],void 0);e["default"]=s.exports},9435:function(t,e,n){var i=n("d2e4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("c1be5b82",i,!0,{sourceMap:!1,shadowMode:!1})},d2e4:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-1029930f]{width:100%;text-align:center}.main[data-v-1029930f]{background:#fff;width:100%;height:100vh;overflow-y:scroll;-webkit-overflow-scrolling:touch}.main-content[data-v-1029930f]{display:flex;flex-direction:column;align-items:center;padding-top:%?396?%;box-sizing:border-box}.main-content-img[data-v-1029930f]{width:%?74?%;height:%?91?%}.main-content-tip[data-v-1029930f]{width:%?587?%;font-size:%?28?%;text-align:center;margin-top:%?50?%}.main-content-success[data-v-1029930f]{min-width:%?173?%;height:%?59?%;display:flex;justify-content:center;align-items:center;background-color:#00cd77;color:#fff;margin-top:%?70?%}.main-content-success-loading[data-v-1029930f]{background-color:#eee}.main-content-op[data-v-1029930f]{min-width:%?173?%;height:%?59?%;display:flex;justify-content:center;align-items:center;background-color:var(--custom-brand-color);color:#fff;margin-top:%?70?%}.main-content-op-loading[data-v-1029930f]{background-color:#eee}.btn-app[data-v-1029930f]{height:%?100?%;line-height:%?100?%;color:#1aad19;font-size:%?36?%;border:%?1?% solid #eee;background-color:initial}.box[data-v-1029930f]{width:%?580?%;padding-top:%?60?%;background-color:#fff;text-align:center;border-radius:%?20?%;padding-bottom:%?60?%}.pay-text[data-v-1029930f]{color:#000;font-size:%?40?%;margin-bottom:%?60?%}',""]),t.exports=e},f5b4:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uniPopup:n("5e99").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"main",style:[t.themeColorVar]},[n("v-uni-view",{staticClass:"main-content"},[n("v-uni-image",{staticClass:"main-content-img",attrs:{src:t.$util.img("public/static/youpin/safe.svg"),mode:"widthFix"}}),n("v-uni-view",{staticClass:"main-content-tip"},[t._v(t._s(t.d_type?t.tip_list[t.d_type-1]:""))]),t.isSuccess?[n("v-uni-button",{staticClass:"main-content-success",attrs:{"open-type":"launchApp","app-parameter":"wechat",binderror:"launchAppError"}},[t._v("验证成功，点击返回APP")])]:[t.isLoading?n("v-uni-button",{staticClass:"main-content-op main-content-op-loading",attrs:{loading:!0}}):n("v-uni-button",{staticClass:"main-content-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.startCertification.apply(void 0,arguments)}}},[t._v("验证失败,点击重新认证")])]],2),n("uni-popup",{ref:"popup"},[n("v-uni-view",{staticClass:"box"},[n("v-uni-view",{staticClass:"pay-text"},[t._v("认证成功，是否返回到app中")]),n("v-uni-button",{staticClass:"btn-app",attrs:{"open-type":"launchApp","app-parameter":"wechat",binderror:"launchAppError"}},[t._v("点击返回App")])],1)],1)],1)},r=[]}}]);