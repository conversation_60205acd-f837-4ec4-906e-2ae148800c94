(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-diy-diy-diy~otherpages-shop-app-home-app-home~otherpages-shop-home-home"],{"0149":function(t,e,i){"use strict";var a=i("3149"),n=i.n(a);n.a},"0314":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.channels.length>0?i("v-uni-view",{staticClass:"channel"},["horizontal-scroll"==t.scrollSetting?i("v-uni-view",[i("v-uni-swiper",{staticClass:"channel--list",style:{height:t.channels.length<=5?"160rpx":"350rpx"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeChannel.apply(void 0,arguments)}}},t._l(t.list,(function(e,a){return i("v-uni-swiper-item",{key:a,staticClass:"channel--list--item"},t._l(e,(function(e,a){return i("v-uni-view",{key:a,staticClass:"channel--list--item--channel",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.todetail(e)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e.imageUrl)}}),i("v-uni-view",[t._v(t._s(e.title))])],1)})),1)})),1),t.list.length>1?i("v-uni-view",{staticClass:"channel--dot"},t._l(t.list,(function(e,a){return i("v-uni-text",{key:a,staticClass:"channel--dot--index",class:{"channel--dot--active":a==t.channelsIndex}})})),1):t._e()],1):i("v-uni-view",{staticClass:"channel--list",staticStyle:{height:"auto"}},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"channel--list--item"},t._l(e,(function(e,a){return i("v-uni-view",{key:a,staticClass:"channel--list--item--channel",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.todetail(e)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e.imageUrl)}}),i("v-uni-view",[t._v(t._s(e.title))])],1)})),1)})),1)],1):t._e()},n=[]},"04d5":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d4b5"),i("5c47"),i("a1c1"),i("bf0f"),i("2797"),i("c223");var a={name:"diy-slide-show",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{data:{},current:0,scale:.8,previousMargin:"200rpx",nextMargin:"200rpx",autoplay:!0,animating:!1,dx:0,animationData:null}},created:function(){this.handlePx()},methods:{handlePx:function(){var t=JSON.parse(JSON.stringify(this.value));t.backgroundImgSize.width=t.backgroundImgSize.width.replace("px","rpx"),t.backgroundImgSize.height=t.backgroundImgSize.height.replace("px","rpx"),t.imgList.forEach((function(t){t.width=t.width.replace("px","rpx"),t.height=t.height.replace("px","rpx")})),this.data=t},onSwiperChange:function(t){this.animating=!0,this.current=t.detail.current},animationFinish:function(){this.animating=!1,this.dx=0},onTransition:function(t){this.dx=t.detail.dx},getSwiperItemTransform:function(t){return this.animating&&0!==this.dx?t===this.current?"translateY(".concat(.05*Math.abs(this.dx),"rpx) scale(").concat(1-3e-4*Math.abs(this.dx),")"):"scale(".concat(this.scale+3e-4*Math.abs(this.dx),")"):t===this.current?"translateY(-5rpx) scale(1)":"scale(".concat(this.scale,")")},toAd:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"SlideShow",diy_text:"",diy_link:t.link,diy_image:t.imgUrl}),t.link&&this.$util.diyCompateRedirectTo({wap_url:t.link})}}};e.default=a},"050c":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("aa9c");var a={props:{channels:{type:Array,default:[]},scrollSetting:{type:String,default:"horizontal-scroll"}},data:function(){return{channelsIndex:0,list:[]}},created:function(){if(this.channels.length<=10)this.list=[this.channels];else{var t=[[],[]];this.channels.map((function(e,i){i<10?t[0].push(e):t[1].push(e)})),this.list=t}},methods:{changeChannel:function(t){var e=t.detail.current;this.channelsIndex=e},todetail:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"GraphicNav",diy_text:t.title,diy_link:t.link.url,diy_image:t.imageUrl}),t.link.url?this.$util.diyCompateRedirectTo({wap_url:t.link.url}):uni.showToast({title:"网络故障，请联系客服处理",icon:"none"})}}};e.default=a},"0519":function(t,e,i){"use strict";i.r(e);var a=i("8868"),n=i("78fd");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("7676");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"6adbaa0f",null,!1,a["a"],void 0);e["default"]=r.exports},"07ad":function(t,e,i){"use strict";i.r(e);var a=i("fd75"),n=i("9901");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("728f");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"6fcbc350",null,!1,a["a"],void 0);e["default"]=r.exports},"0b48":function(t,e,i){"use strict";i.r(e);var a=i("b5ce"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"0b63":function(t,e,i){"use strict";i.r(e);var a=i("f159"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"0c3a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3851c617]{width:100%;text-align:center}\r\n/*活动广告组件*/.diy-activity-ads[data-v-3851c617]{width:100%;margin-top:%?20?%}.diy-activity-ads-not-padding[data-v-3851c617]{margin-top:0!important}.diy-activity-ads .select_vw[data-v-3851c617]{width:100vw!important;margin-left:%?-20?%}.diy-activity-ads .select_01[data-v-3851c617]{width:100%;text-align:center;display:flex;font-size:%?18?%;justify-content:space-between}.imgs[data-v-3851c617]{width:100%;height:100%;display:block}\r\n/* ----------------------------选择1------------------------------ */.diy-activity-ads .select_01 .select_01_box[data-v-3851c617]{text-align:center;flex:1}.diy-activity-ads .select_01 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_01 .height_type_1[data-v-3851c617]{height:%?162?%;line-height:%?162?%}.diy-activity-ads .select_01 .height_type_2[data-v-3851c617]{height:%?266?%;line-height:%?266?%}.diy-activity-ads .select_01 .height_type_3[data-v-3851c617]{height:%?342?%;line-height:%?342?%}\r\n/* ----------------------------选择2------------------------------ */.diy-activity-ads .select_02[data-v-3851c617]{width:100%;display:flex;text-align:center;font-size:%?18?%;justify-content:space-between}.diy-activity-ads .select_02 .select_02_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_02 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_02 .height_type_1[data-v-3851c617]{height:%?140?%;line-height:%?140?%}.diy-activity-ads .select_02 .height_type_2[data-v-3851c617]{height:%?222?%;line-height:%?222?%}.diy-activity-ads .select_02 .height_type_3[data-v-3851c617]{height:%?296?%;line-height:%?296?%}\r\n/* ----------------------------选择3------------------------------ */.diy-activity-ads .select_03[data-v-3851c617]{width:100%;display:flex;text-align:center;font-size:%?18?%;justify-content:space-between}.diy-activity-ads .select_03 .select_03_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_03 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_03 .height_type_1[data-v-3851c617]{height:%?162?%;line-height:%?162?%}.diy-activity-ads .select_03 .height_type_2[data-v-3851c617]{height:%?128?%;line-height:%?128?%}.diy-activity-ads .select_03 .height_type_3[data-v-3851c617]{height:%?200?%;line-height:%?200?%}\r\n/* ----------------------------选择4------------------------------ */.diy-activity-ads .select_04[data-v-3851c617]{width:100%;text-align:center;font-size:%?18?%}.diy-activity-ads .select_04 .select_04_layer[data-v-3851c617]{width:100%;display:flex;justify-content:space-between}.diy-activity-ads .select_04 .marginTop[data-v-3851c617]{margin-top:%?18?%}.diy-activity-ads .select_04 .select_04_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_04 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_04 .height_type_1[data-v-3851c617]{height:%?194?%;line-height:%?194?%}.diy-activity-ads .select_04 .height_type_2[data-v-3851c617]{height:%?266?%;line-height:%?266?%}.diy-activity-ads .select_04 .height_type_3[data-v-3851c617]{height:%?342?%;line-height:%?342?%}\r\n/* ----------------------------选择5------------------------------ */.diy-activity-ads .select_05[data-v-3851c617]{width:100%;display:flex;justify-content:space-between;text-align:center;font-size:%?18?%}.diy-activity-ads .select_05 .select_05_layer[data-v-3851c617]{flex:1}.diy-activity-ads .select_05 .marginTop[data-v-3851c617]{margin-top:%?18?%}.diy-activity-ads .select_05 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_05 .height_type_1[data-v-3851c617]{height:%?342?%;line-height:%?342?%}.diy-activity-ads .select_05 .height_type_2[data-v-3851c617]{height:%?162?%;line-height:%?162?%}\r\n/* ----------------------------选择6------------------------------ */.diy-activity-ads .select_06[data-v-3851c617]{width:100%;text-align:center;font-size:%?18?%}.diy-activity-ads .select_06 .select_06_layer[data-v-3851c617]{display:flex;justify-content:space-between}.diy-activity-ads .select_06 .marginTop[data-v-3851c617]{margin-top:%?18?%}.diy-activity-ads .select_06 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_06 .select_06_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_06 .height_fixed[data-v-3851c617]{height:%?246?%;line-height:%?246?%}.diy-activity-ads .select_06 .height_type_1[data-v-3851c617]{height:%?194?%;line-height:%?194?%}.diy-activity-ads .select_06 .height_type_2[data-v-3851c617]{height:%?266?%;line-height:%?266?%}.diy-activity-ads .select_06 .height_type_3[data-v-3851c617]{height:%?342?%;line-height:%?342?%}\r\n/* ----------------------------选择7------------------------------ */.diy-activity-ads .select_07[data-v-3851c617]{width:100%;display:flex;justify-content:space-between;text-align:center;font-size:%?18?%}.diy-activity-ads .select_07 .select_07_layer[data-v-3851c617]{flex:1}.diy-activity-ads .select_07 .marginTop[data-v-3851c617]{margin-top:%?18?%}.diy-activity-ads .select_07 .marginRight[data-v-3851c617]{margin-right:%?20?%}.diy-activity-ads .select_07 .select_07_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_07 .deeplayer_box[data-v-3851c617]{flex:1;display:flex}.diy-activity-ads .select_07 .deeplayer_box .deeplayer_item[data-v-3851c617]{flex:1}.diy-activity-ads .select_07 .height_left_box_1[data-v-3851c617]{height:%?342?%;line-height:%?342?%}.diy-activity-ads .select_07 .height_left_box_2[data-v-3851c617]{height:%?218?%;line-height:%?218?%}.diy-activity-ads .select_07 .height_type_1[data-v-3851c617]{height:%?162?%;line-height:%?162?%}.diy-activity-ads .select_07 .height_type_2[data-v-3851c617]{height:%?100?%;line-height:%?100?%}.diy-activity-ads .select_08 uni-image[data-v-3851c617]{width:100%;display:block}.diy-activity-ads .select_08_2[data-v-3851c617]{margin-left:%?-20?%}\r\n/* ----------------------------选择9------------------------------ */.diy-activity-ads .select_09[data-v-3851c617]{width:100%;display:flex;text-align:center;font-size:%?18?%;justify-content:space-between}.diy-activity-ads .select_09 .select_09_box[data-v-3851c617]{flex:1}.diy-activity-ads .select_09 .marginRight[data-v-3851c617]{margin-right:%?20?%}',""]),t.exports=e},"0d6e":function(t,e,i){"use strict";var a=i("76f6"),n=i.n(a);n.a},1351:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-f7512cd4]{width:100%;text-align:center}.diy-seckill[data-v-f7512cd4]{margin-top:%?20?%;border-radius:%?20?%;width:100%;padding:%?20?% 0 %?20?% %?20?%;box-sizing:border-box;background-size:100% 100%;background-repeat:no-repeat}.diy-seckill-top[data-v-f7512cd4]{width:100%;display:flex;justify-content:space-between;align-items:center;padding-right:%?20?%;box-sizing:border-box}.diy-seckill-top .seckill-title[data-v-f7512cd4]{display:flex;align-items:center\r\n  /* .seckill-titie-tit{\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\tbackground-color:$base-color;\r\n\t\t\t\tcolor:#ffffff;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t\tfont-size: $ns-font-size-sm;\r\n\t\t\t} */}.diy-seckill-top .seckill-title-hot[data-v-f7512cd4]{width:%?33?%;height:%?33?%;margin-right:%?14?%}.diy-seckill-top .seckill-title .seckill-title-name[data-v-f7512cd4]{font-size:%?36?%;font-weight:bolder;margin-right:%?20?%}.diy-seckill-top .seckill-more[data-v-f7512cd4]{font-size:%?26?%;display:flex;align-items:center}.diy-seckill-top .seckill-more-icon[data-v-f7512cd4]{border:1px solid;border-radius:50%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.diy-seckill-box[data-v-f7512cd4]{width:100%;\r\n  /*white-space 不能丢  */white-space:nowrap;box-sizing:border-box;margin-top:%?30?%}.seckill-box-item[data-v-f7512cd4]{width:%?188?%;height:100%;vertical-align:top;display:inline-block;background:#fff;border-radius:%?20?%;margin-right:%?20?%;box-sizing:border-box}.seckill-box-item .seckill-item[data-v-f7512cd4]{width:100%;height:100%}.seckill-box-item .seckill-item-image[data-v-f7512cd4]{width:100%;height:%?188?%;border-radius:%?20?% %?20?% 0 0;overflow:hidden;display:flex;justify-content:center;align-items:center;position:relative}.seckill-box-item .seckill-item-image uni-image[data-v-f7512cd4]{padding:0;margin:0}.seckill-box-item .seckill-item-image .goods_img-over[data-v-f7512cd4]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.seckill-box-item .seckill-item-info[data-v-f7512cd4]{padding:0 %?8?%;box-sizing:border-box}.seckill-box-item .seckill-item-info-tip[data-v-f7512cd4]{width:%?60?%;height:%?20?%;border-radius:%?4?%;background:var(--custom-brand-color-10);font-size:%?16?%;font-weight:400;color:var(--custom-brand-color);display:inline-flex;justify-content:center;align-items:center}.seckill-box-item .seckill-item-info-bottom[data-v-f7512cd4]{display:flex;justify-content:space-between;padding-bottom:%?10?%;box-sizing:border-box}.seckill-box-item .seckill-item-info-bottom-img[data-v-f7512cd4]{width:%?58?%;height:%?44?%;background:linear-gradient(90deg,var(--custom-brand-color-60),var(--custom-brand-color));border-radius:%?100?%}.seckill-box-item .seckill-item-new-name[data-v-f7512cd4]{white-space:normal;margin-top:%?12?%;box-sizing:border-box;height:%?66?%;line-height:1.3;font-size:%?26?%;color:#383838;font-weight:400;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.seckill-box-item .seckill-item-new-price[data-v-f7512cd4]{font-size:%?24?%;font-weight:700;line-height:%?30?%;color:var(--custom-brand-color);align-self:flex-end}.seckill-box-item .seckill-item-new-price uni-text[data-v-f7512cd4]:first-child{font-size:%?24?%}.seckill-box-item .seckill-item-new-price-integer[data-v-f7512cd4]{font-size:%?36?%}.seckill-box-item .seckill-item-old-price[data-v-f7512cd4]{font-size:%?24?%;color:#a6a6a6;text-decoration:line-through;line-height:1}.ns-margin-right[data-v-f7512cd4]{font-size:%?22?%;font-weight:500;color:#333;padding-bottom:%?5?%}.last-time[data-v-f7512cd4]{padding:%?4?% 0;font-size:%?20?%!important}.last-time .clockrun[data-v-f7512cd4] .custom{display:flex}.last-time .clockrun[data-v-f7512cd4] .custom :nth-child(odd){background-color:var(--custom-brand-color);width:%?40?%;height:%?40?%;line-height:%?40?%;color:#fff;border-radius:%?6?%;font-size:%?22?%;text-align:center;overflow:hidden}.last-time .clockrun[data-v-f7512cd4] .custom :nth-child(even){padding:0 %?6?%;color:var(--custom-brand-color)}.seckill-two[data-v-f7512cd4]{margin-top:%?20?%}.seckill-two-item[data-v-f7512cd4]{border-radius:%?20?%;background:#fff;box-sizing:border-box}.seckill-two-item[data-v-f7512cd4]:not(:first-child){margin-top:%?20?%}.seckill-two-item-image[data-v-f7512cd4]{position:relative}.seckill-two-item-image-goods[data-v-f7512cd4]{width:100%;border-radius:%?20?% %?20?% 0 0;display:block}.seckill-two-item-image .goods_img-over[data-v-f7512cd4]{width:%?300?%;height:%?300?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.seckill-two-item-image-area[data-v-f7512cd4]{position:absolute;left:0;top:0;display:flex;align-items:center}.seckill-two-item-image-area-left[data-v-f7512cd4]{height:%?50?%;line-height:%?50?%;border-radius:%?20?% 0 0 0;background:var(--custom-brand-color);padding:0 %?18?%;box-sizing:border-box;font-size:%?26?%;font-weight:400;color:#fff}.seckill-two-item-image-area-clockrun[data-v-f7512cd4]{display:flex;align-items:center;padding:0 %?24?% 0 %?18?%;box-sizing:border-box;height:%?50?%;line-height:%?50?%;border-radius:0 0 %?20?% 0;background:rgba(0,0,0,.5)}.seckill-two-item-image-area-clockrun-tip[data-v-f7512cd4]{font-size:%?26?%;font-weight:400;color:#fff;margin-right:%?10?%}.seckill-two-item-info[data-v-f7512cd4]{padding:%?32?% %?14?%;box-sizing:border-box}.seckill-two-item-info-name[data-v-f7512cd4]{font-size:%?32?%;font-weight:700;color:#383838;text-overflow:ellipsis;overflow:hidden;word-break:break-all;white-space:nowrap}.seckill-two-item-info-subname[data-v-f7512cd4]{font-size:%?28?%;font-weight:400;color:#a6a6a6}.seckill-two-item-info-price[data-v-f7512cd4]{display:flex;justify-content:space-between;align-items:baseline}.seckill-two-item-info-price-left[data-v-f7512cd4]{display:flex;align-items:baseline}.seckill-two-item-info-price-left-sell-symbol[data-v-f7512cd4]{font-size:%?24?%;color:var(--custom-brand-color)}.seckill-two-item-info-price-left-sell-price[data-v-f7512cd4]{font-size:%?40?%;font-weight:700;color:var(--custom-brand-color)}.seckill-two-item-info-price-left-original-price[data-v-f7512cd4]{font-size:%?26?%;font-weight:400;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6;margin-left:%?16?%}.seckill-two-item-info-price-right[data-v-f7512cd4]{display:flex;align-items:center}.seckill-two-item-info-price-right-thrift[data-v-f7512cd4]{width:%?154?%;height:%?60?%;line-height:%?60?%;border-radius:%?100?%;background:var(--custom-brand-color);font-size:%?32?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.seckill-two-item-info-keep[data-v-f7512cd4]{width:100%;height:%?80?%;border-radius:%?20?%;background:var(--custom-brand-color-10);display:flex;justify-content:space-between;align-items:center;padding-left:%?16?%;box-sizing:border-box}.seckill-two-item-info-keep-clockrun[data-v-f7512cd4]{display:flex;align-items:center}.seckill-two-item-info-keep-clockrun-tip[data-v-f7512cd4]{font-size:%?32?%;font-weight:400;color:var(--custom-brand-color);margin-right:%?12?%}.seckill-two-item-info-keep-right[data-v-f7512cd4]{width:%?200?%;height:%?80?%;background-repeat:no-repeat;background-size:100% 100%;display:flex;justify-content:center;align-items:center}.seckill-two-item-info-keep-right-rob[data-v-f7512cd4]{width:%?58?%;height:%?64?%;margin-left:%?30?%}.seckill-two-more[data-v-f7512cd4]{width:100%;height:%?72?%;border-radius:%?20?%;background:#fff;display:flex;justify-content:center;align-items:center;margin-top:%?20?%}.seckill-two-item-image-area-clockrun[data-v-f7512cd4] .custom{display:flex}.seckill-two-item-image-area-clockrun[data-v-f7512cd4] .day{font-size:%?26?%;font-weight:400;color:#fff}.seckill-two-item-image-area-clockrun[data-v-f7512cd4] .day-symbol{font-size:%?26?%;font-weight:400;color:#fff;margin:0 %?6?%}.seckill-two-item-image-area-clockrun[data-v-f7512cd4] .hour, .seckill-two-item-image-area-clockrun[data-v-f7512cd4] .minute, .seckill-two-item-image-area-clockrun[data-v-f7512cd4] .second{font-size:%?26?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.seckill-two-item-image-area-clockrun[data-v-f7512cd4] .hour-symbol, .seckill-two-item-image-area-clockrun[data-v-f7512cd4] .minute-symbol, .seckill-two-item-image-area-clockrun[data-v-f7512cd4] .second-symbol{font-size:%?26?%;font-weight:400;color:#fff;margin:0 %?6?%}',""]),t.exports=e},"13c9":function(t,e,i){"use strict";var a=i("1f7c"),n=i.n(a);n.a},"16e3":function(t,e,i){"use strict";var a=i("7bd9"),n=i.n(a);n.a},"188a":function(t,e,i){"use strict";i.r(e);var a=i("b04d"),n=i("e991");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("c161");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"5c283b1f",null,!1,a["a"],void 0);e["default"]=r.exports},"18f7e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7aa735bd]{width:100%;text-align:center}.share-popup .share-title[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-title[data-v-7aa735bd]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center}.share-popup .share-content[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content[data-v-7aa735bd]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%;margin-bottom:%?22?%}.share-popup .share-content .share-box[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-7aa735bd]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-7aa735bd]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-image[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-image[data-v-7aa735bd]{width:%?100?%;height:%?100?%}.share-popup .share-content .share-box .share-btn uni-text[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-7aa735bd]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#333}.share-popup .share-content .share-box .iconfont[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-7aa735bd]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.share-popup .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconpengyouquan[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-content .share-box .iconiconfenxianggeihaoyou[data-v-7aa735bd]{color:#07c160}.share-popup .share-footer[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer[data-v-7aa735bd]{width:%?672?%;height:%?80?%;line-height:%?80?%;text-align:center;color:#fff;border-radius:%?40?%;background:var(--custom-brand-color);margin:0 auto}.share-popup .share-footer-padding[data-v-7aa735bd],\r\n.uni-popup__wrapper-box .share-footer-padding[data-v-7aa735bd]{margin-bottom:%?40?%}.canvas[data-v-7aa735bd]{width:%?620?%;height:%?917?%;margin:0 auto;margin-top:%?70?%;display:block;overflow:hidden}.poster[data-v-7aa735bd]{display:flex;justify-content:center}.canvas1[data-v-7aa735bd]{top:100vh}@-webkit-keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-7aa735bd{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-7aa735bd]{width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:997}.loading-anim[data-v-7aa735bd]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-7aa735bd]{position:relative;width:35px;height:35px;-webkit-perspective:800px;perspective:800px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-7aa735bd]{position:absolute;border-radius:50%;border:3px solid}.loading-anim .out[data-v-7aa735bd]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .6s linear normal infinite;animation:spin-data-v-7aa735bd .6s linear normal infinite}.loading-anim .in[data-v-7aa735bd]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-7aa735bd .8s linear infinite;animation:spin-data-v-7aa735bd .8s linear infinite}.loading-anim .mid[data-v-7aa735bd]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-7aa735bd .6s linear infinite;animation:spin-data-v-7aa735bd .6s linear infinite}',""]),t.exports=e},"1eb6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"diy-activity-ads",class:{"diy-activity-ads-not-padding":t.diyActivityAdsNotPadding}},[1==t.value.currentSelect?i("v-uni-view",{staticClass:"select_01",class:{select_vw:4==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_01_box",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1),i("v-uni-view",{staticClass:"select_01_box",style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1)],1):2==t.value.currentSelect?i("v-uni-view",{staticClass:"select_02",class:{select_vw:4==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_02_box ",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1),i("v-uni-view",{staticClass:"select_02_box ",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"select_02_box ",style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1)],1):3==t.value.currentSelect?i("v-uni-view",{staticClass:"select_03",class:{select_vw:4==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_03_box ",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1),i("v-uni-view",{staticClass:"select_03_box ",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"select_03_box ",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1),i("v-uni-view",{staticClass:"select_03_box ",style:{width:t.image_size_list[3].width,height:t.image_size_list[3].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[3].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[3])}}})],1)],1):4==t.value.currentSelect?i("v-uni-view",{staticClass:"select_04",class:{select_vw:4==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_04_layer"},[i("v-uni-view",{staticClass:"select_04_box",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1),i("v-uni-view",{staticClass:"select_04_box",style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1)],1),i("v-uni-view",{staticClass:"select_04_layer",class:{marginTop:4!=t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_04_box",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1),i("v-uni-view",{staticClass:"select_04_box",style:{width:t.image_size_list[3].width,height:t.image_size_list[3].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[3].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[3])}}})],1)],1)],1):5==t.value.currentSelect?i("v-uni-view",{staticClass:"select_05",class:{select_vw:2==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_05_layer",class:{marginRight:2!=t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_05_box ",style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1)],1),i("v-uni-view",{staticClass:"select_05_layer"},[i("v-uni-view",{staticClass:"select_05_box",style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"select_05_box",class:{marginTop:2!=t.value.currentSelectType},style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1)],1)],1):6==t.value.currentSelect?i("v-uni-view",{staticClass:"select_06",class:{select_vw:4==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_06_layer"},[i("v-uni-view",{staticClass:"select_06_box",style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1)],1),i("v-uni-view",{staticClass:"select_06_layer",class:{marginTop:4!=t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_06_box",class:{marginRight:4!=t.value.currentSelectType},style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"select_06_box",style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1)],1)],1):7==t.value.currentSelect?i("v-uni-view",{staticClass:"select_07",class:{select_vw:3==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_07_layer",class:{marginRight:3!=t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_07_box",style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1)],1),i("v-uni-view",{staticClass:"select_07_layer "},[i("v-uni-view",{staticClass:"select_07_box ",style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"deeplayer_box ",class:{marginTop:3!=t.value.currentSelectType}},[i("v-uni-view",{staticClass:"deeplayer_item",class:{marginRight:3!=t.value.currentSelectType},style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1),i("v-uni-view",{staticClass:"deeplayer_item",style:{width:t.image_size_list[3].width,height:t.image_size_list[3].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[3].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[3])}}})],1)],1)],1)],1):8==t.value.currentSelect?i("v-uni-view",{staticClass:"select_08",class:{select_08_2:2==t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1):9==t.value.currentSelect?i("v-uni-view",{staticClass:"select_09",class:{select_vw:2==t.value.currentSelectType}},[i("v-uni-view",{staticClass:"select_09_box ",class:{marginRight:2!=t.value.currentSelectType},style:{width:t.image_size_list[0].width,height:t.image_size_list[0].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[0].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[0])}}})],1),i("v-uni-view",{staticClass:"select_09_box ",class:{marginRight:2!=t.value.currentSelectType},style:{width:t.image_size_list[1].width,height:t.image_size_list[1].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[1].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[1])}}})],1),i("v-uni-view",{staticClass:"select_09_box ",class:{marginRight:2!=t.value.currentSelectType},style:{width:t.image_size_list[2].width,height:t.image_size_list[2].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[2].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[2])}}})],1),i("v-uni-view",{staticClass:"select_09_box ",class:{marginRight:2!=t.value.currentSelectType},style:{width:t.image_size_list[3].width,height:t.image_size_list[3].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[3].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[3])}}})],1),i("v-uni-view",{staticClass:"select_09_box ",style:{width:t.image_size_list[4].width,height:t.image_size_list[4].height}},[i("v-uni-image",{staticClass:"imgs",attrs:{src:t.$util.img(t.value.list[4].imageUrl),alt:"",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAd(t.value.list[4])}}})],1)],1):t._e()],1)},n=[]},"1f7c":function(t,e,i){var a=i("a84e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f4ba202a",a,!0,{sourceMap:!1,shadowMode:!1})},"1fbf":function(t,e,i){var a=i("0c3a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("48dee6b3",a,!0,{sourceMap:!1,shadowMode:!1})},2131:function(t,e,i){"use strict";var a=i("8528"),n=i.n(a);n.a},2301:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-4bdacb96]{width:100%;text-align:center}.diy-seckill[data-v-4bdacb96]{margin-top:%?20?%;border-radius:%?20?%;width:100%;padding:%?20?%;box-sizing:border-box;background-color:#fff}.diy-seckill-top[data-v-4bdacb96]{width:100%;display:flex;justify-content:space-between;align-items:center}.diy-seckill-top .seckill-title[data-v-4bdacb96]{display:flex;align-items:center\r\n  /* .seckill-titie-tit{\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\tbackground-color:$base-color;\r\n\t\t\t\tcolor:#ffffff;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t\tfont-size: $ns-font-size-sm;\r\n\t\t\t} */}.diy-seckill-top .seckill-title uni-text[data-v-4bdacb96]:first-child{width:%?6?%;height:%?36?%;background:var(--custom-brand-color);border-radius:%?3?%;margin-right:%?17?%;display:inline-block}.diy-seckill-top .seckill-title .seckill-title-name[data-v-4bdacb96]{font-size:%?30?%;font-weight:700;margin-right:%?20?%}.diy-seckill-top .seckill-more[data-v-4bdacb96]{font-size:%?24?%}.diy-seckill-top .seckill-more[data-v-4bdacb96]::after{font-family:iconfont;content:"\\eb93";font-size:%?28?%;line-height:1;position:relative;top:%?2?%;margin-left:%?4?%}.diy-seckill-box[data-v-4bdacb96]{width:100%;\r\n  /*white-space 不能丢  */white-space:nowrap;box-sizing:border-box;margin-top:%?30?%}.seckill-box-item[data-v-4bdacb96]{width:%?205?%;height:100%;vertical-align:top;display:inline-block;background:#fff;margin-right:%?20?%}.seckill-box-item .seckill-item[data-v-4bdacb96]{width:100%;height:100%}.seckill-box-item .seckill-item-image[data-v-4bdacb96]{width:100%;height:%?205?%;border-radius:%?20?%;overflow:hidden;display:flex;justify-content:center;align-items:center;position:relative}.seckill-box-item .seckill-item-image uni-image[data-v-4bdacb96]{width:%?205?%;height:%?205?%;padding:0;margin:0;display:block}.seckill-box-item .seckill-item-image .goods_img-over[data-v-4bdacb96]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.seckill-box-item .seckill-item-new-name[data-v-4bdacb96]{white-space:normal;margin:%?30?% 0 %?20?% 0;height:%?60?%;font-size:%?26?%;color:#333;line-height:1.3;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.seckill-box-item .seckill-item-new-price[data-v-4bdacb96]{font-size:%?36?%;line-height:1;font-weight:700}.seckill-box-item .seckill-item-new-price uni-text[data-v-4bdacb96]:first-child{font-size:%?26?%}.seckill-box-item .seckill-item-old-price[data-v-4bdacb96]{font-size:%?24?%;color:#a6a6a6;text-decoration:line-through;line-height:1}.seckill-box-item .song_maidou[data-v-4bdacb96]{width:100%}.seckill-box-item .song_maidou .huanhang[data-v-4bdacb96]{background:var(--custom-brand-color-10);font-size:%?22?%;border-radius:%?8?%;display:inline-block;margin-top:%?10?%;padding:0 %?10?%}.seckill-box-item .song_maidou .huanhang .song[data-v-4bdacb96]{color:#333}.seckill-box-item .song_maidou .huanhang .maidounum[data-v-4bdacb96]{color:var(--custom-brand-color)}',""]),t.exports=e},2325:function(t,e,i){"use strict";i.r(e);var a=i("409b"),n=i("f0ab");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("13c9");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"384c03a3",null,!1,a["a"],void 0);e["default"]=r.exports},"24d3":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-7b4d0c14]{width:100%;text-align:center}.recommend[data-v-7b4d0c14]{background:#fff;border-radius:%?20?%;padding:%?30?% %?24?% 0 %?24?%;box-sizing:border-box;margin-top:%?24?%}.recommend--title[data-v-7b4d0c14]{height:%?36?%;font-size:%?30?%;font-weight:700;color:#343434;display:flex;align-items:center}.recommend--title uni-text[data-v-7b4d0c14]{width:%?6?%;height:%?36?%;background:var(--custom-brand-color);border-radius:%?3?%;margin-right:%?17?%;display:inline-block}.recommend--products[data-v-7b4d0c14]{display:flex;align-items:flex-start;flex-wrap:wrap;margin-top:%?33?%}.recommend--products--one[data-v-7b4d0c14]{width:%?202?%;margin-bottom:%?30?%}.recommend--products--one--img[data-v-7b4d0c14]{width:100%;height:%?202?%;position:relative}.recommend--products--one--img uni-image[data-v-7b4d0c14]{width:100%;height:100%;border-radius:%?8?%}.recommend--products--one--img uni-image.over[data-v-7b4d0c14]{width:%?120?%;height:%?120?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.recommend--products--one--name[data-v-7b4d0c14]{font-size:%?26?%;font-weight:500;color:#343434;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-top:%?20?%;line-height:1.4}.recommend--products--one--price[data-v-7b4d0c14]{margin-top:%?24?%;font-size:%?36?%;font-weight:700;color:var(--custom-brand-color)}.recommend--products--one--price uni-text[data-v-7b4d0c14]{font-size:%?26?%}.recommend--products--one--primary[data-v-7b4d0c14]{margin-top:%?16?%;font-size:%?24?%;font-weight:500;text-decoration:line-through;color:#9a9a9a}',""]),t.exports=e},"259c":function(t,e,i){var a=i("ecf9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("196d1e9c",a,!0,{sourceMap:!1,shadowMode:!1})},"26cc":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.dataList.list&&t.dataList.list.length?i("v-uni-view",{staticClass:"diy-seckill"},[i("v-uni-view",{staticClass:"diy-seckill-top"},[i("v-uni-view",{staticClass:"seckill-title"},[i("v-uni-text"),i("v-uni-text",{staticClass:"seckill-title-name"},[t._v(t._s(t.name))])],1),i("v-uni-view",{staticClass:"seckill-more ns-text-color",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toMore()}}},[t._v(t._s(t.moreText))])],1),i("v-uni-scroll-view",{staticClass:"diy-seckill-box",attrs:{"scroll-x":"true"}},t._l(t.dataList.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"seckill-box-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"seckill-item"},[i("v-uni-view",{staticClass:"seckill-item-image"},[i("v-uni-view",{staticClass:"goods_img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1)],1),i("v-uni-view",{staticClass:"seckill-item-new-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"seckill-item-new-price ns-text-color"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1),i("v-uni-text",{staticClass:"seckill-item-old-price"},[t._v("￥"+t._s(e.market_price))]),i("v-uni-view",{staticClass:"song_maidou"},[i("v-uni-view",{staticClass:"huanhang"},[i("v-uni-text",{staticClass:"song"},[t._v("送迈豆")]),i("v-uni-text",{staticClass:"maidounum"},[t._v(t._s(e.send_maidou))])],1)],1)],1)],1)})),1)],1):t._e()},n=[]},2870:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("e838");var a={name:"diy-video",props:{value:{type:Object,default:function(){return{videoType:0,showType:0,videoLink:"",addon_name:"",type:"VIDEO",name:"视频",controller:"Video",width:0,height:0}}},siteId:{type:[Number,String],default:0}},data:function(){return{vid:"",borderRadius:"20rpx",isMuted:!0,height:""}},created:function(){1==this.value.showType&&(this.borderRadius=""),this.calculateVideoShowSize(),this.parseTXvideoUrl()},methods:{calculateVideoShowSize:function(){1==this.value.showType?this.height=750/parseFloat(this.value.width)*parseFloat(this.value.height)+"rpx":this.height=710/parseFloat(this.value.width)*parseFloat(this.value.height)+"rpx"},parseTXvideoUrl:function(){if(0==this.value.videoType)try{var t=this.value.videoLink.split(".html")[0].split("/");this.vid=t[t.length-1]}catch(e){}},changeMute:function(){this.isMuted=!this.isMuted}}};e.default=a},"29d0":function(t,e,i){"use strict";var a=i("3e51"),n=i.n(a);n.a},"2e65":function(t,e,i){"use strict";i.r(e);var a=i("bb0b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"2e91":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-46873122]{width:100%;text-align:center}.yd-popup[data-v-46873122]{background:rgba(0,0,0,.4);width:100%;height:100%;z-index:998;position:fixed;top:0;left:0}.yd-popup .share-tip[data-v-46873122]{width:100%;height:%?447?%;display:block}',""]),t.exports=e},"2f73":function(t,e,i){"use strict";i.r(e);var a=i("e71a"),n=i("0b48");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("0149");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"46873122",null,!1,a["a"],void 0);e["default"]=r.exports},3149:function(t,e,i){var a=i("2e91");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("31685291",a,!0,{sourceMap:!1,shadowMode:!1})},"31c3":function(t,e,i){"use strict";i.r(e);var a=i("ae57"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},3722:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniIcons:i("de74").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.dataList&&t.dataList.length?i("v-uni-view",{staticClass:"diy-seeding",style:{backgroundColor:t.value.item.backgroundColor,backgroundImage:"url("+t.value.item.backgroundImg+")"}},[i("v-uni-view",{staticClass:"diy-seeding-top"},[i("v-uni-view",{staticClass:"seeding-title"},[i("v-uni-text",{staticClass:"seeding-title-name",style:{color:t.value.item.textColor}},[t._v(t._s(t.name))])],1),i("v-uni-view",{staticClass:"seeding-more",style:{color:t.value.item.textColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toMore()}}},[t._v(t._s(t.moreText)),i("uni-icons",{staticClass:"seeding-more-icon",attrs:{type:"forward",size:"24rpx",color:t.value.item.textColor}})],1)],1),i("v-uni-scroll-view",{staticClass:"diy-seeding-box",attrs:{"scroll-x":"true"}},t._l(t.dataList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"seeding-box-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"seeding-item"},[i("v-uni-view",{staticClass:"seeding-item-image"},[i("v-uni-image",{staticClass:"seeding-item-image-bg",attrs:{src:t.$util.img(e.image||t.$util.getDefaultImage().default_goods_img),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}}),i("v-uni-view",{staticClass:"seeding-item-image-star"},[i("v-uni-image",{staticClass:"seeding-item-image-star-like",attrs:{src:t.$util.img("public/static/youpin/like-star.png")}}),i("v-uni-text",[t._v(t._s(e.like_num))]),t.playIndex==a?i("v-uni-canvas",{staticClass:"seeding-item-image-star-revenue",attrs:{id:"seeding_revenue_id_"+a,type:"2d"}}):t._e()],1)],1)],1)],1)})),1)],1):t._e()},o=[]},"3e51":function(t,e,i){var a=i("c740");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("4505aade",a,!0,{sourceMap:!1,shadowMode:!1})},"409b":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"video-block",class:{"full-video":1==t.value.showType}},[1==t.value.videoType?i("v-uni-video",{style:{height:t.height,borderRadius:t.borderRadius},attrs:{src:t.value.videoLink,autoplay:!0,loop:!0,muted:t.isMuted}}):t._e(),0==t.value.videoType&&t.vid?[t._t("tx_video",null,{vid:t.vid,height:t.height,borderRadius:t.borderRadius,isMuted:t.isMuted})]:t._e(),i("v-uni-image",{staticClass:"video-block-mute",attrs:{src:t.isMuted?t.$util.img("public/static/youpin/mute.png"):t.$util.img("public/static/youpin/unmute.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeMute.apply(void 0,arguments)}}})],2)},n=[]},"426c":function(t,e,i){"use strict";i.r(e);var a=i("89ee"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"44f8":function(t,e,i){"use strict";i.r(e);var a=i("882e"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"45ef":function(t,e,i){"use strict";i.r(e);var a=i("d530"),n=i("7694");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("0d6e");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"7b4d0c14",null,!1,a["a"],void 0);e["default"]=r.exports},"46d4":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("aa9c");a(i("7c8d"));var n={name:"diy-goods-list",props:{products:{type:Array,default:[]}},data:function(){return{all_products:[],list1:[],list2:[],is_fulfil:!1}},watch:{products:{handler:function(t){var e=this;this.all_products=t.map((function(t){return t.goods_image=e.$util.imageCdnResize(t.goods_image),t})),this.is_fulfil=!0}}},created:function(){},methods:{changeList:function(t){this[t.name].push(t.value)},imageError:function(t,e){e[t]&&(e[t].goods_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()},toProductDetail:function(t){var e=this;this.$util.toProductDetail(t,(function(i){e.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"GoodsList",diy_text:t.goods_name,diy_link:i,diy_image:t.goods_image})}))}}};e.default=n},4710:function(t,e,i){var a=i("1351");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("163fbf78",a,!0,{sourceMap:!1,shadowMode:!1})},"4a67":function(t,e,i){"use strict";i.r(e);var a=i("e89e"),n=i("44f8");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("78a2");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"5aed521c",null,!1,a["a"],void 0);e["default"]=r.exports},"4b88":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223");var n=a(i("2634")),o=a(i("2fdc")),s=a(i("7c8d")),r={name:"diy-tag-product",prop:{},data:function(){return{categoryGoodsList:[],isLoading:!0}},methods:{getCategoryGoodsList:function(t,e){var i=this;return(0,o.default)((0,n.default)().mark((function a(){var o,r,c,l;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(o=e.category_id,r=e.level,o){a.next=4;break}return a.abrupt("return");case 4:return a.next=6,i.$api.sendRequest({url:s.default.goodsListUrl,async:!1,data:{shop_id:i.shop_id,category_id:o,category_level:r,page_size:t.size,page:t.num}});case 6:if(c=a.sent,0==c.code){a.next=10;break}return uni.showToast({title:c.message,mask:!0,icon:"none",duration:3e3}),a.abrupt("return");case 10:l=c.data.list,t.endSuccess(l.length),1==t.num&&(i.categoryGoodsList=[]),i.categoryGoodsList=i.categoryGoodsList.concat(l),i.isLoading=!1,i.$buriedPoint.exposeGoods(l,"sku_id");case 16:case"end":return a.stop()}}),a)})))()},imageError:function(t,e){t instanceof Object&&t[e]&&t[e].goods_image&&(t[e].goods_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()}}};e.default=r},"4f46":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6adbaa0f]{width:100%;text-align:center}.slide-show[data-v-6adbaa0f]{margin-left:%?-20?%;background-size:100% 100%;background-repeat:no-repeat;display:flex;align-items:center;position:relative;overflow:hidden}.swiper[data-v-6adbaa0f]{width:100%}.swiper-item[data-v-6adbaa0f]{display:flex;justify-content:center;align-items:center}.swiper-one[data-v-6adbaa0f]{transition:all .5s cubic-bezier(.215,.61,.355,1);-webkit-transform-origin:center center;transform-origin:center center;border-radius:%?12?%;overflow:hidden;will-change:transform,opacity,box-shadow}.swiper-one.active[data-v-6adbaa0f]{box-shadow:0 %?10?% %?25?% transparent}.swiper-one.inactive[data-v-6adbaa0f]{-webkit-filter:brightness(.92);filter:brightness(.92)}.swiper-one .slide-image[data-v-6adbaa0f]{width:100%;height:100%;transition:all .3s ease;will-change:transform}',""]),t.exports=e},5349:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-72269f14]{width:100%;text-align:center}.channel[data-v-72269f14]{margin-top:%?35?%}.channel--list[data-v-72269f14]{height:%?350?%}.channel--list--item[data-v-72269f14]{display:flex;flex-wrap:wrap;justify-content:flex-start;align-items:flex-start}.channel--list--item--channel[data-v-72269f14]{width:%?138?%;display:flex;flex-direction:column;align-items:center}.channel--list--item--channel uni-image[data-v-72269f14]{width:%?96?%;height:%?96?%;border-radius:50%}.channel--list--item--channel uni-view[data-v-72269f14]{font-size:%?24?%;font-weight:500;color:#9a9a9a;margin-top:%?20?%}.channel--dot[data-v-72269f14]{display:flex;justify-content:center;align-items:center;margin-top:%?15?%}.channel--dot--index[data-v-72269f14]{width:%?43?%;height:%?6?%;background:#d4d8de;border-radius:%?3?%}.channel--dot--active[data-v-72269f14]{background:#f2280c}',""]),t.exports=e},"546f":function(t,e,i){"use strict";i.r(e);var a=i("7a0c"),n=i("d47d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("16e3"),i("9a6e");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"00ccc54c",null,!1,a["a"],void 0);e["default"]=r.exports},5797:function(t,e,i){"use strict";i.r(e);var a=i("6889"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"59c6":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniIcons:i("de74").default,uvWaterfall:i("c7e0").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"product-topic",class:[t.productTopicIndex]},[4!=t.config.selectedTemplate&&5!=t.config.selectedTemplate&&(t.dataList.length||t.originalList.length)?i("v-uni-view",{staticClass:"product-topic-model"},[1==t.config.selectedTemplate?i("v-uni-view",{staticClass:"product-topic-one",style:{backgroundColor:t.config.backgroundColor,backgroundImage:"url("+t.config.backgroundImg+")"}},[i("v-uni-view",{staticClass:"product-topic-one-title",style:{color:t.config.fontColor}},[i("v-uni-text",{staticClass:"product-topic-one-title-text"},[t._v(t._s(t.config.title))]),i("v-uni-view",{staticClass:"product-topic-one-title-more",style:{color:t.config.fontColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toTopicList(null)}}},[t._v(t._s(t.config.moreText)),i("uni-icons",{staticClass:"product-topic-one-title-more-icon",attrs:{type:"forward",size:"24rpx",color:t.config.fontColor}})],1)],1),i("v-uni-view",{staticClass:"product-topic-one-list"},t._l(t.dataList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-topic-one-list-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"product-topic-one-list-item-image"},[i("v-uni-image",{staticClass:"product-topic-one-list-item-image-img",attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.i)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e(),i("v-uni-text",{staticClass:"product-topic-one-list-item-image-serial",style:{backgroundColor:t.serialColor[a]}},[t._v(t._s(a+1))])],1),i("v-uni-view",{staticClass:"product-topic-one-list-item-info"},[i("v-uni-view",{staticClass:"product-topic-one-list-item-info-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"product-topic-one-list-item-info-price"},[i("v-uni-text",{staticClass:"product-topic-one-list-item-info-price-symbol"},[t._v("￥")]),t._v(t._s(e.sale_price))],1),i("v-uni-view",{staticClass:"product-topic-one-list-item-info-original-price"},[t._v("￥"+t._s(e.market_price))])],1)],1)})),1)],1):t._e(),2==t.config.selectedTemplate?i("v-uni-view",{staticClass:"product-topic-two",style:{backgroundColor:t.config.backgroundColor,backgroundImage:"url("+t.config.backgroundImg+")"}},[i("v-uni-view",{staticClass:"product-topic-two-title",style:{color:t.config.fontColor}},[i("v-uni-text",{staticClass:"product-topic-two-title-text"},[t._v(t._s(t.config.title))]),i("v-uni-view",{staticClass:"product-topic-two-title-more",style:{color:t.config.fontColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toTopicList(null)}}},[t._v(t._s(t.config.moreText)),i("uni-icons",{staticClass:"product-topic-two-title-more-icon",attrs:{type:"forward",size:"24rpx",color:t.config.fontColor}})],1)],1),i("v-uni-scroll-view",{staticClass:"product-topic-two-list",attrs:{"scroll-x":!0}},t._l(t.dataList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-topic-two-list-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"product-topic-two-list-item-image"},[i("v-uni-image",{staticClass:"product-topic-two-list-item-image-img",attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.i)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"product-topic-two-list-item-info"},[i("v-uni-view",{staticClass:"product-topic-two-list-item-info-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"product-topic-two-list-item-info-price"},[i("v-uni-text",{staticClass:"product-topic-two-list-item-info-price-symbol"},[t._v("￥")]),t._v(t._s(e.sale_price))],1),i("v-uni-view",{staticClass:"product-topic-two-list-item-info-original-price"},[t._v("￥"+t._s(e.market_price))])],1)],1)})),1)],1):t._e(),3==t.config.selectedTemplate?i("v-uni-view",{staticClass:"product-topic-three"},[i("v-uni-view",{staticClass:"product-topic-three-list"},t._l(t.originalList.slice(0+9*t.showPage,9+9*t.showPage),(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-topic-three-list-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"product-topic-three-list-item-image"},[i("v-uni-image",{staticClass:"product-topic-three-list-item-image-img",attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.i)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"product-topic-three-list-item-info"},[i("v-uni-view",{staticClass:"product-topic-three-list-item-info-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"product-topic-three-list-item-info-price"},[i("v-uni-text",{staticClass:"product-topic-three-list-item-info-price-symbol"},[t._v("￥")]),t._v(t._s(e.sale_price))],1),i("v-uni-view",{staticClass:"product-topic-three-list-item-info-original-price"},[t._v("￥"+t._s(e.market_price))])],1)],1)})),1),i("v-uni-view",{staticClass:"product-topic-three-op",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeData.apply(void 0,arguments)}}},[i("uni-icons",{attrs:{type:"loop"}}),i("v-uni-text",{staticClass:"product-topic-three-op-text"},[t._v("换一批")])],1)],1):t._e()],1):t._e(),4!=t.config.selectedTemplate&&5!=t.config.selectedTemplate||!t.tab_list.length?t._e():i("v-uni-view",{staticClass:"product-topic-four"},[t.is_scroll_to_top?i("v-uni-scroll-view",{staticClass:"product-topic-four-tab",style:{top:t.showTop+"px"},attrs:{"scroll-x":!0,"scroll-with-animation":!0,"scroll-into-view":t.scroll_into_id}},[t._l(t.tab_list,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"product-topic-four-tab-one",class:{"product-topic-four-tab-one-active":e.topic_id==t.currentTopicId},style:{backgroundColor:e.topic_id==t.currentTopicId?t.config.selectBgColor:t.selectBgColorList.length&&t.selectBgColorList[0],color:e.topic_id==t.currentTopicId?"#fff":t.config.selectBgColor},attrs:{id:"scroll_into_topic_id_"+e.topic_id},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeTab(e.topic_id)}}},[t._v(t._s(e.topic_name))])]}))],2):t._e(),t._l(t.tab_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"product-topic-four-area",class:"product-topic-four-area-"+e.topic_id},[t.all_data_dict[e.topic_id]&&t.all_data_dict[e.topic_id].length>0?[i("v-uni-view",{staticClass:"product-topic-four-header",class:{"product-topic-four-header-not-img":!e.topic_adv}},[e.topic_adv?i("v-uni-image",{staticClass:"product-topic-four-header-img",attrs:{mode:"widthFix",src:t.$util.img(e.topic_adv)}}):[i("v-uni-view",{staticClass:"product-topic-four-header-title"},[t._v(t._s(e.topic_name))]),i("v-uni-view",{staticClass:"product-topic-four-header-subtitle"},[t._v(t._s(e.remark))])]],2),4==t.config.selectedTemplate?i("v-uni-view",{staticClass:"product-topic-four-list"},[i("uv-waterfall",{ref:"fallsFlow",refInFor:!0,attrs:{columnGap:"20rpx","add-time":300},on:{changeList:function(i){arguments[0]=i=t.$handleEvent(i),t.changeList(i,e.topic_id)},finish:function(e){arguments[0]=e=t.$handleEvent(e),t.waterfallFinish.apply(void 0,arguments)}},scopedSlots:t._u([{key:"list1",fn:function(){return[t._l(t.list1_dict[e.topic_id],(function(a,n){return[a.type_link?i("v-uni-view",{staticClass:"product-topic-four-list--more",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toTopicList(e.topic_id)}}},[i("v-uni-view",{staticClass:"product-topic-four-list--more--img"},t._l(a.sku_images,(function(e,n){return i("v-uni-image",{key:n,staticClass:"product-topic-four-list--more--img--one",attrs:{src:t.$util.img(e)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.skuImageError(n,a.sku_images)}}})})),1),i("v-uni-view",{staticClass:"product-topic-four-list--more--title"},[t._v(t._s(e.topic_name))]),i("v-uni-view",{staticClass:"product-topic-four-list--more--to"},[t._v("查看更多商品"),i("uni-icons",{staticClass:"product-topic-four-list--more--to--icon",attrs:{type:"arrowthinright",size:"12",color:"#fff"}})],1)],1):i("v-uni-view",{key:n+"_0",staticClass:"product-topic-four-list--one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toProductDetail(a)}}},[i("v-uni-view",{staticClass:"product-topic-four-list--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(a.sku_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.waterfallImageError(n,t.list1_dict[e.topic_id])}}}),0==a.goods_stock&&1==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==a.goods_stock&&0==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"product-topic-four-list--one--row"},[i("v-uni-view",{staticClass:"product-topic-four-list--one--name"},[a.is_recommend?i("v-uni-text",[t._v("推荐")]):a.tags&&a.tags.length>0?t._l(a.tags,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.tag_name))])})):t._e(),t._v(t._s(a.goods_name))],2),i("v-uni-view",{staticClass:"product-topic-four-list--one--price"},[i("v-uni-view",[i("v-uni-text",[t._v("￥")]),t._v(t._s(a.retail_price))],1),i("v-uni-view",[t._v("￥"+t._s(a.market_price))])],1),a.forecast_price&&parseFloat(a.forecast_price)?i("v-uni-view",{staticClass:"product-topic-four-list--one--benefit"},[i("v-uni-view",[i("v-uni-text",{staticClass:"benefit-color"},[t._v("预估收益")]),i("v-uni-text",{staticClass:"product-topic-four-list--one--benefit--price"},[t._v("￥"+t._s(a.forecast_price))])],1)],1):t._e()],1)],1)]}))]},proxy:!0},{key:"list2",fn:function(){return[t._l(t.list2_dict[e.topic_id],(function(a,n){return[a.type_link?i("v-uni-view",{staticClass:"product-topic-four-list--more",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toTopicList(e.topic_id)}}},[i("v-uni-view",{staticClass:"product-topic-four-list--more--img"},t._l(a.sku_images,(function(e,n){return i("v-uni-image",{key:n,staticClass:"product-topic-four-list--more--img--one",attrs:{src:t.$util.img(e)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.skuImageError(n,a.sku_images)}}})})),1),i("v-uni-view",{staticClass:"product-topic-four-list--more--title"},[t._v(t._s(e.topic_name))]),i("v-uni-view",{staticClass:"product-topic-four-list--more--to"},[t._v("查看更多商品"),i("uni-icons",{staticClass:"product-topic-four-list--more--to--icon",attrs:{type:"arrowthinright",size:"12",color:"#fff"}})],1)],1):i("v-uni-view",{key:n+"_0",staticClass:"product-topic-four-list--one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toProductDetail(a)}}},[i("v-uni-view",{staticClass:"product-topic-four-list--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(a.sku_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.waterfallImageError(n,t.list2_dict[e.topic_id])}}}),0==a.goods_stock&&1==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==a.goods_stock&&0==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"product-topic-four-list--one--row"},[i("v-uni-view",{staticClass:"product-topic-four-list--one--name"},[a.is_recommend?i("v-uni-text",[t._v("推荐")]):a.tags&&a.tags.length>0?t._l(a.tags,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.tag_name))])})):t._e(),t._v(t._s(a.goods_name))],2),i("v-uni-view",{staticClass:"product-topic-four-list--one--price"},[i("v-uni-view",[i("v-uni-text",[t._v("￥")]),t._v(t._s(a.retail_price))],1),i("v-uni-view",[t._v("￥"+t._s(a.market_price))])],1),a.forecast_price&&parseFloat(a.forecast_price)?i("v-uni-view",{staticClass:"product-topic-four-list--one--benefit"},[i("v-uni-view",[i("v-uni-text",{staticClass:"benefit-color"},[t._v("预估收益")]),i("v-uni-text",{staticClass:"product-topic-four-list--one--benefit--price"},[t._v("￥"+t._s(a.forecast_price))])],1)],1):t._e()],1)],1)]}))]},proxy:!0}],null,!0),model:{value:t.all_data_dict[e.topic_id],callback:function(i){t.$set(t.all_data_dict,e.topic_id,i)},expression:"all_data_dict[area.topic_id]"}})],1):t._e(),5==t.config.selectedTemplate?i("v-uni-view",{staticClass:"product-topic-five-list"},[t._l(t.all_data_dict[e.topic_id],(function(a,n){return[a.type_link?i("v-uni-view",{staticClass:"product-topic-five-list--more",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toTopicList(e.topic_id)}}},[i("v-uni-view",{staticClass:"product-topic-five-list--more--img"},t._l(a.sku_images,(function(e,n){return i("v-uni-image",{key:n,staticClass:"product-topic-five-list--more--img--one",attrs:{src:t.$util.img(e)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.skuImageError(n,a.sku_images)}}})})),1),i("v-uni-view",{staticClass:"product-topic-five-list--more--right"},[i("v-uni-view",{staticClass:"product-topic-five-list--more--title"},[t._v(t._s(e.topic_name))]),i("v-uni-view",{staticClass:"product-topic-five-list--more--to"},[t._v("查看更多商品"),i("uni-icons",{staticClass:"product-topic-five-list--more--to--icon",attrs:{type:"arrowthinright",size:"12",color:"#fff"}})],1)],1)],1):i("v-uni-view",{key:n+"_0",staticClass:"product-topic-five-list--one",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toProductDetail(a)}}},[i("v-uni-view",{staticClass:"product-topic-five-list--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(a.sku_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.waterfallImageError(n,a)}}}),0==a.goods_stock&&1==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==a.goods_stock&&0==a.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"product-topic-five-list--one--info"},[i("v-uni-view",{staticClass:"product-topic-five-list--one--name"},[a.is_recommend?i("v-uni-text",[t._v("推荐")]):a.tags&&a.tags.length>0?t._l(a.tags,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.tag_name))])})):t._e(),t._v(t._s(a.goods_name))],2),i("v-uni-view",{staticClass:"product-topic-five-list--one--row"},[i("v-uni-view",{staticClass:"product-topic-five-list--one--price"},[i("v-uni-view",{staticClass:"product-topic-five-list--one--price--retail"},[i("v-uni-text",{staticClass:"symbol"},[t._v("￥")]),t._v(t._s(a.retail_price))],1),i("v-uni-view",{staticClass:"product-topic-five-list--one--price--market"},[t._v("原价￥"+t._s(a.market_price))])],1),i("v-uni-text",{staticClass:"product-topic-five-list--one--op"},[t._v("购买")])],1)],1)],1)]}))],2):t._e()]:t._e()],2)}))],2)],1)},o=[]},"5a4e":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uvWaterfall:i("c7e0").default,nsEmpty:i("dc6c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.is_fulfil?i("v-uni-view",[t.all_products.length>0?i("v-uni-view",{staticClass:"all"},[t._e(),i("v-uni-view",{staticClass:"all--products"},[t.all_products.length?i("uv-waterfall",{ref:"fallsFlow",attrs:{columnGap:"20rpx"},on:{changeList:function(e){arguments[0]=e=t.$handleEvent(e),t.changeList.apply(void 0,arguments)}},scopedSlots:t._u([{key:"list1",fn:function(){return t._l(t.list1,(function(e,a){return i("v-uni-view",{key:a,staticClass:"all--products--one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"all--products--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,t.list1)}}}),0==e.goods_stock&&1==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==e.goods_stock&&0==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"all--products--one--row"},[i("v-uni-view",{staticClass:"all--products--one--name"},[e.is_recommend?i("v-uni-text",[t._v("推荐")]):e.tags&&e.tags.length>0?t._l(e.tags,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.tag_name))])})):t._e(),t._v(t._s(e.goods_name))],2),i("v-uni-view",{staticClass:"all--products--one--price"},[i("v-uni-view",[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1),i("v-uni-view",[t._v("￥"+t._s(e.market_price))])],1),e.forecast_price&&parseFloat(e.forecast_price)?i("v-uni-view",{staticClass:"all--products--one--benefit"},[i("v-uni-view",[i("v-uni-text",{staticClass:"benefit-color"},[t._v("预估收益")]),i("v-uni-text",{staticClass:"all--products--one--benefit--price"},[t._v("￥"+t._s(e.forecast_price))])],1)],1):t._e()],1)],1)}))},proxy:!0},{key:"list2",fn:function(){return t._l(t.list2,(function(e,a){return i("v-uni-view",{key:a,staticClass:"all--products--one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"all--products--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,t.list2)}}}),0==e.goods_stock&&1==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==e.goods_stock&&0==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"all--products--one--row"},[i("v-uni-view",{staticClass:"all--products--one--name"},[e.is_recommend?i("v-uni-text",[t._v("推荐")]):e.tags&&e.tags.length>0?t._l(e.tags,(function(e,a){return i("v-uni-text",{key:a},[t._v(t._s(e.tag_name))])})):t._e(),t._v(t._s(e.goods_name))],2),i("v-uni-view",{staticClass:"all--products--one--price"},[i("v-uni-view",[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1),i("v-uni-view",[t._v("￥"+t._s(e.market_price))])],1),e.forecast_price&&parseFloat(e.forecast_price)?i("v-uni-view",{staticClass:"all--products--one--benefit"},[i("v-uni-view",[i("v-uni-text",{staticClass:"benefit-color"},[t._v("预估收益")]),i("v-uni-text",{staticClass:"all--products--one--benefit--price"},[t._v("￥"+t._s(e.forecast_price))])],1)],1):t._e()],1)],1)}))},proxy:!0}],null,!1,48560298),model:{value:t.all_products,callback:function(e){t.all_products=e},expression:"all_products"}}):t._e()],1)],1):i("ns-empty",{attrs:{isIndex:!1}})],1):t._e()},o=[]},"5ad3":function(t,e,i){"use strict";i.r(e);var a=i("7cb2"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"5b08":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={components:{},name:"diy-maidou",props:{value:{type:Object},dataList:{type:Object,default:function(){return{}}}},data:function(){return{name:"迈豆专区",moreText:"查看更多"}},created:function(){if(this.value.item){var t=this.value.item;t.name&&(this.name=t.name),t.moreText&&(this.moreText=t.moreText)}},methods:{toMore:function(){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Maidou",diy_text:this.moreText,diy_link:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/maidou/list/list",diy_image:""}),this.$util.diyCompateRedirectTo({wap_url:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/maidou/list/list"})},toDetail:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Maidou",diy_text:t.goods_name,diy_link:"/pages/goods/detail/detail?sku_id=".concat(t.sku_id),diy_image:this.$util.img(t.sku_image)}),this.$util.diyCompateRedirectTo({wap_url:"/pages/goods/detail/detail?sku_id=".concat(t.sku_id)})},imageError:function(t){this.dataList.list[t].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},onFinish:function(){this.$emit("finish")}}};e.default=a},"5c09":function(t,e,i){var a=i("d1af");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("611b1d62",a,!0,{sourceMap:!1,shadowMode:!1})},"627e":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc")),s={name:"diy-new-product-area",props:{config:{type:Object,default:function(){return{}}}},data:function(){return{serialColor:["rgba(245, 93, 93, 1)","rgba(251, 107, 13, 1)","rgba(255, 195, 0, 1)"],dataList:[]}},created:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getData();case 2:case"end":return e.stop()}}),e)})))()},methods:{getData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.newProductAreaGoodsList,async:!1,data:{page:1,page_size:3}});case 3:i=e.sent,0==i.code&&(t.dataList=i.data.list,t.$buriedPoint.exposeGoods(i.data.list,"sku_id")),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toList:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$util.diyCompateRedirectTo({wap_url:"/promotionpages/new_product_area/list/list"});case 1:case"end":return e.stop()}}),e)})))()},toProductDetail:function(t){var e=this;this.$util.toProductDetail(t,(function(i){e.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"newProductArea",diy_text:t.goods_name,diy_link:i,diy_image:e.$util.img(t.sku_image)})}))},imageError:function(t){this.dataList[t].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}}};e.default=s},6345:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={countdownTimer:i("d3b8").default,uniIcons:i("de74").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 0==t.dataList.type&&t.dataList.action_time>0&&t.showList.length>0?i("v-uni-view",[1==t.value.item.selectedTemplate?[t.showList?i("v-uni-view",{staticClass:"diy-seckill",style:{backgroundColor:t.value.item.backgroundColor,backgroundImage:"url("+t.value.item.backgroundImg+")"}},[i("v-uni-view",{staticClass:"diy-seckill-top"},[i("v-uni-view",{staticClass:"seckill-title"},[i("v-uni-image",{staticClass:"seckill-title-hot",attrs:{src:t.$util.img("public/static/youpin/pintuan/seckill-icon.png")}}),i("v-uni-text",{staticClass:"seckill-title-name",style:{color:t.value.item.textColor}},[t._v(t._s(t.name))]),i("v-uni-view",{staticClass:"last-time flex-start-end"},[i("v-uni-view",{staticClass:"clockrun"},[i("countdown-timer",{ref:"countdown",attrs:{time:t.dataList.action_time,autoStart:!0},on:{finish:function(e){arguments[0]=e=t.$handleEvent(e),t.onFinish.apply(void 0,arguments)}}})],1)],1)],1),i("v-uni-view",{staticClass:"seckill-more",style:{color:t.value.item.textColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toMore(t.dataList.seckill_id)}}},[t._v(t._s(t.moreText)),i("uni-icons",{staticClass:"seckill-more-icon",attrs:{type:"forward",size:"24rpx",color:t.value.item.textColor}})],1)],1),i("v-uni-scroll-view",{staticClass:"diy-seckill-box",attrs:{"scroll-x":"true"}},t._l(t.showList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"seckill-box-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"seckill-item"},[i("v-uni-view",{staticClass:"seckill-item-image"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.showList,a)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"seckill-item-info"},[i("v-uni-view",{staticClass:"seckill-item-new-name"},[t._v(t._s(e.goods_name))]),i("v-uni-text",{staticClass:"seckill-item-info-tip"},[t._v("秒杀价")]),i("v-uni-view",{staticClass:"seckill-item-info-bottom"},[i("v-uni-view",{staticClass:"seckill-item-new-price"},[i("v-uni-text",[t._v("￥")]),i("v-uni-text",{staticClass:"seckill-item-new-price-integer"},[t._v(t._s(t._f("getInteger")(e.seckill_price)))]),i("v-uni-text",{staticClass:"seckill-item-new-price-Decimals"},[t._v(t._s(t._f("getDecimals")(e.seckill_price)))])],1),i("v-uni-image",{staticClass:"seckill-item-info-bottom-img",attrs:{src:t.$util.img("public/static/youpin/rob.png")}})],1)],1)],1)],1)})),1)],1):t._e()]:t._e(),2==t.value.item.selectedTemplate?[i("v-uni-view",{staticClass:"seckill-two"},[t._l(t.showList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"seckill-two-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"seckill-two-item-image"},[i("v-uni-image",{staticClass:"seckill-two-item-image-goods",attrs:{src:t.$util.img(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.showList,a)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e(),i("v-uni-view",{staticClass:"seckill-two-item-image-area"},[i("v-uni-view",{staticClass:"seckill-two-item-image-area-left"},[t._v("立省￥"+t._s(e.thrift_money))]),i("v-uni-view",{staticClass:"seckill-two-item-image-area-clockrun"},[i("v-uni-text",{staticClass:"seckill-two-item-image-area-clockrun-tip"},[t._v("距结束")]),i("countdown-timer",{ref:"countdown",refInFor:!0,attrs:{"show-day-symbol":!0,time:t.dataList.action_time,autoStart:!0},on:{finish:function(e){arguments[0]=e=t.$handleEvent(e),t.onFinish.apply(void 0,arguments)}}})],1)],1)],1),i("v-uni-view",{staticClass:"seckill-two-item-info"},[i("v-uni-view",{staticClass:"seckill-two-item-info-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"seckill-two-item-info-subname"},[t._v(t._s(e.introduction))]),i("v-uni-view",{staticClass:"seckill-two-item-info-price"},[i("v-uni-view",{staticClass:"seckill-two-item-info-price-left"},[i("v-uni-view",{staticClass:"seckill-two-item-info-price-left-sell"},[i("v-uni-text",{staticClass:"seckill-two-item-info-price-left-sell-symbol"},[t._v("￥")]),i("v-uni-text",{staticClass:"seckill-two-item-info-price-left-sell-price"},[t._v(t._s(e.seckill_price))])],1),i("v-uni-view",{staticClass:"seckill-two-item-info-price-left-original"},[i("v-uni-text",{staticClass:"seckill-two-item-info-price-left-original-price"},[t._v("原价￥"+t._s(e.market_price))])],1)],1),i("v-uni-view",{staticClass:"seckill-two-item-info-price-right"},[i("v-uni-text",{staticClass:"seckill-two-item-info-price-right-thrift"},[t._v("抢购")])],1)],1)],1)],1)})),i("v-uni-view",{staticClass:"seckill-two-more",style:{color:t.value.item.textColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toMore(t.dataList.seckill_id)}}},[t._v(t._s(t.moreText)),i("uni-icons",{attrs:{type:"forward",size:"24rpx",color:t.value.item.textColor}})],1)],2)]:t._e()],2):t._e()},o=[]},"64d9":function(t,e,i){"use strict";i.r(e);var a=i("3722"),n=i("5ad3");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("cdff");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"4e071422",null,!1,a["a"],void 0);e["default"]=r.exports},6889:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("fd3c"),i("e838");var a={name:"diy-search",props:{value:{type:Object,default:function(){return{}}},city:{type:String,value:""},siteId:{type:[Number,String],default:0}},computed:{diyActivityAdsNotPadding:function(){return 1==this.value.currentSelect&&4==this.value.currentSelectType||2==this.value.currentSelect&&4==this.value.currentSelectType||3==this.value.currentSelect&&4==this.value.currentSelectType||4==this.value.currentSelect&&4==this.value.currentSelectType||5==this.value.currentSelect&&2==this.value.currentSelectType||6==this.value.currentSelect&&4==this.value.currentSelectType||7==this.value.currentSelect&&3==this.value.currentSelectType||8==this.value.currentSelect&&(2==this.value.currentSelectType||3==this.value.currentSelectType)||9==this.value.currentSelect&&2==this.value.currentSelectType}},data:function(){return{image_size_list:[]}},created:function(){this.transitionSize()},methods:{transitionSize:function(){for(var t=this.value.imgSizeList,e=0;e<t.length;e++)if(t[e].select==this.value.currentSelect){for(var i=t[e].typeList,a=0;a<i.length;a++)if(i[a].type==this.value.currentSelectType){var n=i[a].list;this.image_size_list=n.map((function(t){var e="",i="",a=parseFloat(t.width);e=isNaN(a)?"auto":"".concat(a,"rpx");var n=parseFloat(t.height);return i=isNaN(n)?"auto":"".concat(n,"rpx"),{width:e,height:i}}));break}break}},toAd:function(t){this.$buriedPoint.diyReportAdEvent({diy_material_path:t.imageUrl,diy_ad_type:"image",diy_target_page:t.link,diy_ad_id:t.id,diy_action_type:"click"}),this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:this.value.controller,diy_text:t.title,diy_link:t.link,diy_image:t.imageUrl}),t.link&&this.$util.diyCompateRedirectTo({wap_url:t.link})}}};e.default=a},"6b8e":function(t,e,i){var a=i("9fea");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("5d34620d",a,!0,{sourceMap:!1,shadowMode:!1})},"6c91":function(t,e,i){"use strict";i.r(e);var a=i("5b08"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"6f9d":function(t,e,i){"use strict";var a=i("1fbf"),n=i.n(a);n.a},"728f":function(t,e,i){"use strict";var a=i("98b6"),n=i.n(a);n.a},7641:function(t,e,i){"use strict";i.r(e);var a=i("d58e"),n=i("88d6");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("29d0");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"feaa67e0",null,!1,a["a"],void 0);e["default"]=r.exports},7676:function(t,e,i){"use strict";var a=i("a7a0"),n=i.n(a);n.a},7694:function(t,e,i){"use strict";i.r(e);var a=i("c4ae"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"76d1":function(t,e,i){var a=i("b190");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("389e60da",a,!0,{sourceMap:!1,shadowMode:!1})},"76f6":function(t,e,i){var a=i("24d3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("580a38b6",a,!0,{sourceMap:!1,shadowMode:!1})},"76ff":function(t,e,i){"use strict";i.r(e);var a=i("0314"),n=i("77cd3");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("96ee");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"72269f14",null,!1,a["a"],void 0);e["default"]=r.exports},"77cd3":function(t,e,i){"use strict";i.r(e);var a=i("050c"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"78a2":function(t,e,i){"use strict";var a=i("d754"),n=i.n(a);n.a},"78fd":function(t,e,i){"use strict";i.r(e);var a=i("04d5"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"7a0c":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={diyShareNavigateH5:i("2f73").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return void 0!=t.value.room_id&&t.liveInfo.roomid?i("v-uni-view",{staticClass:"live-box"},[i("v-uni-view",{staticClass:"live-wrap",style:{backgroundColor:t.value.backgroundColor,backgroundImage:"url("+t.value.backgroundImg+")"}},[i("v-uni-view",{staticClass:"live-wrap-left",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.roomFun.apply(void 0,arguments)}}},[103==t.liveInfo.live_status?i("v-uni-video",{staticClass:"live-wrap-left-img",attrs:{src:t.liveInfo.media_url,autoplay:!0,muted:!0,controls:!1,"show-loading":!0,"object-fit":"cover",loop:!0,poster:t.value.coverImg}}):i("v-uni-image",{staticClass:"live-wrap-left-img",attrs:{src:t.value.coverImg}}),103==t.liveInfo.live_status?i("v-uni-image",{staticClass:"live-wrap-left-icon",attrs:{src:t.$util.img("public/static/youpin/live/playback.png"),mode:"widthFix"}}):102==t.liveInfo.live_status?i("v-uni-image",{staticClass:"live-wrap-left-icon",attrs:{src:t.$util.img("public/static/youpin/live/subscribe.png"),mode:"widthFix"}}):i("v-uni-image",{staticClass:"live-wrap-left-icon",attrs:{src:t.$util.img("public/static/youpin/live/live.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"live-wrap-right"},[i("v-uni-view",{staticClass:"live-wrap-right-title",style:{color:t.value.textColor}},[t._v(t._s(t.value.title))]),i("v-uni-view",{staticClass:"live-wrap-right-name",style:{color:t.value.textColor}},[t._v("主播："+t._s(t.liveInfo.anchor_name))]),i("v-uni-view",{staticClass:"live-wrap-right-list"}),i("div",{staticClass:"live-wrap-right-op"},[i("v-uni-button",{staticClass:"live-wrap-right-op-share",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSharePopup(t.liveInfo)}}},[i("v-uni-image",{staticClass:"live-wrap-right-op-share-icon",attrs:{src:t.$util.img("public/static/youpin/live/share.png")}}),t._v("分享")],1),103==t.liveInfo.live_status?i("v-uni-view",{staticClass:"live-wrap-right-op-play",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.roomFun.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"live-wrap-right-op-play-icon",attrs:{src:t.$util.img("public/static/youpin/live/play.png")}}),t._v("回看")],1):102==t.liveInfo.live_status?[i("v-uni-view",{staticClass:"live-wrap-right-op-play"},[i("v-uni-image",{staticClass:"live-wrap-right-op-play-icon",attrs:{src:t.$util.img("public/static/youpin/live/subscribe-icon.png")}}),t._v(t._s(t.is_subscribe?"取消预约":"预约")),i("v-uni-view",{staticClass:"live-wrap-right-op-play-slot"},[t._t("liveSubscribe")],2)],1)]:i("v-uni-view",{staticClass:"live-wrap-right-op-play",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.roomFun.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"live-wrap-right-op-play-icon",attrs:{src:t.$util.img("public/static/youpin/live/play.png")}}),t._v("观看")],1)],2)],1)],1),i("diy-share-navigate-h5",{ref:"shareNavigateH5"})],1):t._e()},o=[]},"7bd9":function(t,e,i){var a=i("e53d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("28ec30c0",a,!0,{sourceMap:!1,shadowMode:!1})},"7ca7":function(t,e,i){"use strict";var a=i("b56d"),n=i.n(a);n.a},"7cb2":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc"));i("e838");var s=a(i("de74")),r=a(i("7bfe")),c=null,l={components:{UniIcons:s.default},name:"diy-seeding",props:{value:{type:Object}},filters:{getInteger:function(t){return String(parseFloat(t)).split(".")[0]},getDecimals:function(t){return"."+String(parseFloat(t).toFixed(2)).split(".")[1]}},data:function(){return{name:"",moreText:"",dataList:[],animationData:{},playIndex:0,playObj:null}},created:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.value.item&&(i=t.value.item,i.name&&(t.name=i.name),i.moreText&&(t.moreText=i.moreText));case 1:case"end":return e.stop()}}),e)})))()},mounted:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getAnimationData("/public/static/youpin/seeding_like.json");case 2:return e.next=4,t.getData();case 4:t.playAnimation();case 5:case"end":return e.stop()}}),e)})))()},beforeDestroy:function(){clearInterval(this.playObj)},methods:{getData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.usershareexperienceListUrl,async:!1,data:{page:1,page_size:4}});case 3:i=e.sent,t.dataList=i.data.list,e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},toMore:function(){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Seeding",diy_text:this.moreText,diy_link:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/seeding/seeding-list/seeding-list",diy_image:""}),this.$util.diyCompateRedirectTo({wap_url:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/seeding/seeding-list/seeding-list"})},toDetail:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Seeding",diy_text:t.title,diy_link:"/promotionpages/seeding/seeding_detail/seeding_detail?id=".concat(t.id),diy_image:this.$util.img(t.image)}),this.$util.diyCompateRedirectTo({wap_url:"/promotionpages/seeding/seeding_detail/seeding_detail?id=".concat(t.id)})},imageError:function(t){this.dataList[t].image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},onFinish:function(){this.$emit("finish")},getAnimationData:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,e.$api.sendRequest({url:t,async:!1});case 3:a=i.sent,e.animationData=a,i.next=9;break;case 7:i.prev=7,i.t0=i["catch"](0);case 9:case"end":return i.stop()}}),i,null,[[0,7]])})))()},playAnimation:function(){var t=this;this.dataList.length&&(this.playObj=setInterval((function(){t.revenueAnimation()}),3e3))},revenueAnimation:function(){var t=this;setTimeout((function(){var e=document.querySelector("#seeding_revenue_id_".concat(t.playIndex," canvas"));e&&e.style&&(e.width=105,e.height=140,c=r.default.loadAnimation({loop:!1,autoplay:!1,container:e,renderer:"canvas",animationData:t.animationData}),c.play(),c.addEventListener("complete",(function(){c.destroy(),t.playIndex<t.dataList.length-1?t.playIndex++:t.playIndex=0})))}),300)}}};e.default=l},"7d2f8":function(t,e,i){var a=i("18f7e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("00c0b360",a,!0,{sourceMap:!1,shadowMode:!1})},"82c2":function(t,e,i){"use strict";i.r(e);var a=i("c01b"),n=i("2e65");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("a24d");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"7aa735bd",null,!1,a["a"],void 0);e["default"]=r.exports},8315:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-ab69d0aa]{width:100%;text-align:center}.all[data-v-ab69d0aa]{background:transparent;border-radius:%?20?%;box-sizing:border-box;margin-top:%?20?%;margin-bottom:%?20?%;margin-bottom:calc(%?20?% + constant(safe-area-inset-bottom));margin-bottom:calc(%?20?% + env(safe-area-inset-bottom))}.all--title[data-v-ab69d0aa]{height:%?36?%;font-size:%?30?%;font-weight:700;color:#343434;display:flex;align-items:center}.all--title uni-text[data-v-ab69d0aa]{width:%?6?%;height:%?36?%;background:var(--custom-brand-color);border-radius:%?3?%;margin-right:%?17?%;display:inline-block}.all--products[data-v-ab69d0aa]{margin-top:%?34?%}.all--products .over[data-v-ab69d0aa]{width:%?120?%;height:%?120?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.all--products--one[data-v-ab69d0aa]{width:%?344?%;padding-bottom:%?20?%;border-radius:%?20?%;box-sizing:border-box;background-color:#fff;margin-bottom:%?20?%}.all--products--one--img[data-v-ab69d0aa]{width:100%;position:relative}.all--products--one--img uni-image[data-v-ab69d0aa]{width:100%;height:100%;border-radius:%?20?%}.all--products--one--row[data-v-ab69d0aa]{padding:0 %?20?%;box-sizing:border-box}.all--products--one--name[data-v-ab69d0aa]{font-size:%?26?%;font-weight:500;color:#343434;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-top:%?20?%;box-sizing:border-box;position:relative;line-height:1.4}.all--products--one--name uni-text[data-v-ab69d0aa]{vertical-align:text-bottom;padding:%?2?% %?10?%;margin-right:%?5?%;box-sizing:border-box;display:inline-block;text-align:center;font-size:%?20?%;font-weight:500;color:#fff;background:linear-gradient(55deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:%?4?%}.all--products--one--price[data-v-ab69d0aa]{display:flex;justify-content:flex-start;align-items:baseline}.all--products--one--price uni-view[data-v-ab69d0aa]:first-child{font-size:%?36?%;font-family:PingFang SC;font-weight:700;color:var(--custom-brand-color)}.all--products--one--price uni-view:first-child uni-text[data-v-ab69d0aa]{font-size:%?26?%}.all--products--one--price uni-view[data-v-ab69d0aa]:last-child{font-size:%?24?%;font-weight:500;text-decoration:line-through;color:#9a9a9a;margin-left:%?8?%}.all--products--one--benefit[data-v-ab69d0aa]{background:var(--custom-brand-color-10);font-size:%?22?%;border-radius:%?8?%;display:inline-block;margin-top:%?10?%;padding:0 %?10?%}.all--products--one--benefit--price[data-v-ab69d0aa]{color:var(--custom-brand-color)}.all--products--one--benefit .benefit-color[data-v-ab69d0aa]{color:#333}',""]),t.exports=e},"834e":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"diy-notice",style:{background:t.value.backgroundColor}},[i("v-uni-image",{staticClass:"pic",attrs:{src:t.imgBg,mode:""}}),i("v-uni-view",{staticClass:"main-wrap"},[i("v-uni-view",{staticClass:"uni-swiper-msg"},[i("v-uni-swiper",{attrs:{vertical:"true",autoplay:"true",circular:"true"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)}}},t._l(t.value.list,(function(e,a){return i("v-uni-swiper-item",{key:a,staticClass:"item",on:{touchmove:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("v-uni-text",{staticClass:"beyond-hiding",style:{color:t.value.textColor,fontSize:2*t.value.fontSize+"rpx"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.redirectTo(e.link)}}},[t._v(t._s(e.title))])],1)})),1)],1)],1),i("v-uni-view",{staticClass:"notice-mark",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo()}}})],1)},n=[]},8528:function(t,e,i){var a=i("dc85");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("704790a2",a,!0,{sourceMap:!1,shadowMode:!1})},"882e":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc"));i("64aa"),i("d4b5"),i("aa9c"),i("5c47");var s=a(i("7c8d")),r={props:{ads:Array,imageError:Function,config:{type:Object,default:function(){return{}}},topHeight:{type:Number,default:0},scrollTop:{type:Number,default:0},autoplay:{type:Boolean,default:function(){return!0}}},data:function(){return{current:0,colorList:[],scrollOverImg:!1}},watch:{ads:{handler:function(t,e){var i=this;return(0,o.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(JSON.stringify(t)==JSON.stringify(e)){a.next=4;break}return i.resetCurrentIndex(),a.next=4,i.generateColorList(!0);case 4:case"end":return a.stop()}}),a)})))()},deep:!0,immediate:!1},scrollTop:function(t,e){"carousel-posters-2"==this.config.selectedTemplate&&this.getPosition()}},methods:{toAd:function(t){this.$buriedPoint.diyReportAdEvent({diy_material_path:t.image_url,diy_ad_type:"image",diy_target_page:t.banner_url,diy_ad_id:t.id,diy_action_type:"click"}),this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"ImageAds",diy_text:t.banner_name,diy_link:t.banner_url,diy_image:t.image_url}),this.$emit("clickBanner",t.id),t.banner_url&&this.$util.diyCompateRedirectTo({wap_url:t.banner_url})},getDominantColor:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.$api.sendRequest({url:s.default.getDominantColorUrl,data:{image_url:t,image_crop:1},async:!1});case 2:if(a=i.sent,0!=a.code){i.next=7;break}return i.abrupt("return",e.$util.generateRGBAColors(a.data.dominantColor)[7]);case 7:return i.abrupt("return","");case 8:case"end":return i.stop()}}),i)})))()},generateColorList:function(){var t=arguments,e=this;return(0,o.default)((0,n.default)().mark((function i(){var a,o,s;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=t.length>0&&void 0!==t[0]&&t[0],"carousel-posters-2"==e.config.selectedTemplate){i.next=3;break}return i.abrupt("return");case 3:if(a||!e.colorList.length){i.next=5;break}return i.abrupt("return");case 5:if(!(e.ads&&e.ads.length>0)){i.next=17;break}e.colorList=[],o=0;case 8:if(!(o<e.ads.length)){i.next=16;break}return i.next=11,e.getDominantColor(e.$util.img(e.ads[o].image_url));case 11:s=i.sent,e.colorList.push(s);case 13:o++,i.next=8;break;case 16:e.toEmit(e.current);case 17:case"end":return i.stop()}}),i)})))()},getPosition:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select(".swiper--not").boundingClientRect((function(e){e&&(e.height-t.topHeight+e.top<=0?t.scrollOverImg||(t.scrollOverImg=!0,t.$emit("headerColorChange","var(--custom-brand-color)")):t.scrollOverImg&&(t.scrollOverImg=!1,t.toEmit(t.current)))})).exec()},change:function(t){"carousel-posters-2"==this.config.selectedTemplate&&this.toEmit(t.detail.current)},animationfinishChange:function(t){this.current=t.detail.current},toEmit:function(t){this.scrollOverImg||this.colorList[t]&&this.$emit("headerColorChange",this.colorList[t])},resetCurrentIndex:function(){this.current=0}}};e.default=r},8868:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"slide-show",style:{width:t.data.backgroundImgSize.width,height:t.data.backgroundImgSize.height,backgroundImage:"url("+t.data.backgroundImg+")"}},[i("v-uni-swiper",{staticClass:"swiper",style:{height:t.data.imgList[0].height},attrs:{"indicator-dots":!1,autoplay:t.autoplay,interval:1e3*t.data.playTime,"previous-margin":t.previousMargin,"next-margin":t.nextMargin,current:t.current,circular:!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onSwiperChange.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationFinish.apply(void 0,arguments)},transition:function(e){arguments[0]=e=t.$handleEvent(e),t.onTransition.apply(void 0,arguments)}}},t._l(t.data.imgList,(function(e,a){return i("v-uni-swiper-item",{key:a,staticClass:"swiper-item"},[i("v-uni-view",{staticClass:"swiper-one",class:{active:a===t.current,inactive:a!==t.current},style:{width:a!=t.current?"calc("+e.width+" * "+t.scale+")":e.width,height:a!=t.current?"calc("+e.height+" * "+t.scale+")":e.height,transform:t.getSwiperItemTransform(a)},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toAd(e)}}},[i("v-uni-image",{staticClass:"slide-image",attrs:{src:e.imgUrl,mode:"aspectFit"}})],1)],1)})),1)],1)},n=[]},"88d6":function(t,e,i){"use strict";i.r(e);var a=i("627e"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"89ee":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("f7a5");a(i("56ed"));var n={name:"diy-notice",props:{value:{type:Object},siteId:{type:[Number,String],default:0}},data:function(){return{index:0,imgBg:""}},created:function(){try{var t=this.$util.colorToHex(this.value.textColor).slice(1);this.imgBg=encodeURI(this.$util.img("api/website/svgChangeFillColor?svg_name=ns-notice&color=".concat(t)))}catch(e){}},methods:{redirectTo:function(){var t=this.value.list[this.index].link.wap_url||"";if(this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:this.value.controller,diy_text:this.value.list[this.index].title,diy_link:t,diy_image:""}),this.value&&this.value.list&&this.value.list[this.index].link){var e=this.value.list[this.index].link;this.siteId&&(e.shop_id=this.siteId),this.$util.diyCompateRedirectTo(e)}},change:function(t){this.index=t.detail.current}}};e.default=n},"8b69":function(t,e,i){var a=i("5349");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("4393ffc3",a,!0,{sourceMap:!1,shadowMode:!1})},"96ee":function(t,e,i){"use strict";var a=i("8b69"),n=i.n(a);n.a},"97e1":function(t,e,i){"use strict";i.r(e);var a=i("834e"),n=i("426c");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("aa04");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"3fcd00a6",null,!1,a["a"],void 0);e["default"]=r.exports},"98b6":function(t,e,i){var a=i("a2a2");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("6f788a86",a,!0,{sourceMap:!1,shadowMode:!1})},"98d5":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("dc8a"),i("d4b5");var a={name:"diy-search",props:{color:{type:String,default:"#999"},sbgc:{type:String,default:""},searchObj:{type:Object},handleName:{type:String,default:""}},data:function(){return{tipName:"搜索你喜欢的商品",objStyle:{},inputStyle:{},inputValue:""}},watch:{searchObj:{handler:function(t){t&&(this.tipName=t.placeholder)},immediate:!0,deep:!0},sbgc:{handler:function(t){if(t){this.inputStyle.height="".concat(32,"px"),this.searchObj.imageUrl&&(this.inputStyle["background-image"]="url("+this.searchObj.imageUrl+")"),this.searchObj.backgroundColor&&(this.inputStyle["background-color"]=this.searchObj.backgroundColor)}},immediate:!0,deep:!0}},onPageShow:function(){console.log("onPageShow"),this.changeBgImage()},activated:function(){this.changeBgImage()},methods:{iconAddParams:function(t){var e=this.$util.GetRequestQuery(t);e["ct"]=(new Date).getTime();var i=t.split("?")[0];return i+"?"+Object.keys(e).map((function(t){return t+"="+e[t]})).join("&")},changeBgImage:function(){if(this.searchObj.imageUrl){var t=JSON.parse(JSON.stringify(this.inputStyle));t["background-image"]="url("+this.iconAddParams(this.searchObj.imageUrl)+")",this.inputStyle=t,console.log(this.inputStyle)}},inputInput:function(t){console.log("inputInput",t)},searchBut:function(){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Search",diy_text:"",diy_link:"",diy_image:""}),"搜索"==this.handleName?this.$emit("searchProduct",this.inputValue):uni.navigateBack({delta:1})},tolink:function(){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Search",diy_text:"",diy_link:this.searchObj.link||"/otherpages/goods/search/search",diy_image:""}),1==this.searchObj.isJump&&this.$util.redirectTo(this.searchObj.link||"/otherpages/goods/search/search")}}};e.default=a},9901:function(t,e,i){"use strict";i.r(e);var a=i("98d5"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"9a08":function(t,e,i){"use strict";var a=i("a870"),n=i.n(a);n.a},"9a6e":function(t,e,i){"use strict";var a=i("6b8e"),n=i.n(a);n.a},"9c24":function(t,e,i){"use strict";var a=i("4710"),n=i.n(a);n.a},"9fea":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".coupon-all[data-v-00ccc54c] .uni-scroll-view::-webkit-scrollbar{display:none}",""]),t.exports=e},a0cd:function(t,e,i){"use strict";i.r(e);var a=i("5a4e"),n=i("b44f");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("7ca7");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"ab69d0aa",null,!1,a["a"],void 0);e["default"]=r.exports},a24d:function(t,e,i){"use strict";var a=i("7d2f8"),n=i.n(a);n.a},a2a2:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6fcbc350]{width:100%;text-align:center}.search_product[data-v-6fcbc350]{width:100%;box-sizing:border-box;display:flex;align-items:center}.search_product uni-input[data-v-6fcbc350]{font-size:%?28?%;height:%?60?%;line-height:%?60?%;width:calc(100% - %?120?%)}.search_product uni-text[data-v-6fcbc350]{font-size:%?40?%;color:#a6a6a6;width:%?64?%;text-align:center}.search_product .text[data-v-6fcbc350]{color:#333;font-size:%?28?%;width:%?115?%;text-align:center}.search_product .search-box[data-v-6fcbc350]{width:100%;display:flex;align-items:center;border-radius:%?40?%;padding:0 %?24?%;background-repeat:no-repeat;background-size:100% 100%}.search_product .search-box .txt[data-v-6fcbc350]{color:#a6a6a6;line-height:1.5}.search_product .search-box uni-image[data-v-6fcbc350]{width:%?28?%;height:%?28?%;margin:%?0?% %?10?% %?0?% %?0?%}',""]),t.exports=e},a7a0:function(t,e,i){var a=i("4f46");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("0e3abcb1",a,!0,{sourceMap:!1,shadowMode:!1})},a7d3:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={nsEmpty:i("dc6c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[t.categoryGoodsList.length>0?i("v-uni-view",{staticClass:"tagproducts"},t._l(t.categoryGoodsList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"tagproducts--one",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"tagproducts--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.categoryGoodsList,a)}}}),0==e.goods_stock&&1==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-over.png")}}):t._e(),0==e.goods_stock&&0==e.is_seckill?i("v-uni-image",{staticClass:"over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),i("v-uni-view",{staticClass:"tagproducts--one--name",style:e.is_recommend?"padding-left: 60rpx;":""},[e.is_recommend?i("v-uni-text",[t._v("推荐")]):t._e(),t._v(t._s(e.goods_name))],1),i("v-uni-view",{staticClass:"tagproducts--one--price"},[i("v-uni-view",[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.retail_price))],1),i("v-uni-view",[t._v("￥"+t._s(e.market_price))])],1)],1)})),1):[t.isLoading?t._e():i("ns-empty",{attrs:{isIndex:!1}})]],2)},o=[]},a84e:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-384c03a3]{width:100%;text-align:center}.video-block[data-v-384c03a3]{margin-top:%?20?%;position:relative}.video-block-mute[data-v-384c03a3]{width:%?48?%;height:%?48?%;position:absolute;top:%?20?%;right:%?20?%;z-index:1}.full-video[data-v-384c03a3]{width:100vw;margin-left:%?-20?%;margin-top:%?0?%}uni-video[data-v-384c03a3]{width:100%}',""]),t.exports=e},a870:function(t,e,i){var a=i("2301");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("8303bdc6",a,!0,{sourceMap:!1,shadowMode:!1})},aa04:function(t,e,i){"use strict";var a=i("259c"),n=i.n(a);n.a},ac31:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"custom flex-start-center"},[t.timeData.day>=1?i("v-uni-view",{staticClass:"day"},[t._v(t._s(t.timeData.day))]):t._e(),t.timeData.day>=1?i("v-uni-view",{staticClass:"day-symbol"},[t._v(t._s(t.showColon||t.showDaySymbol?"天":":"))]):t._e(),i("v-uni-view",{staticClass:"hour"},[t._v(t._s(t._f("fillWithZero")(t.timeData.hour)))]),i("v-uni-view",{staticClass:"hour-symbol"},[t._v(t._s(t.showColon?"时":":"))]),i("v-uni-view",{staticClass:"minute"},[t._v(t._s(t._f("fillWithZero")(t.timeData.minute)))]),i("v-uni-view",{staticClass:"minute-symbol"},[t._v(t._s(t.showColon?"分":":"))]),i("v-uni-view",{staticClass:"second"},[t._v(t._s(t._f("fillWithZero")(t.timeData.second)))]),i("v-uni-view",{staticClass:"second-symbol"},[t._v(t._s(t.showColon?"秒":""))])],1)],1)},n=[]},ada6:function(t,e,i){"use strict";i.r(e);var a=i("1eb6"),n=i("5797");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("6f9d");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"3851c617",null,!1,a["a"],void 0);e["default"]=r.exports},ae57:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("c9b5"),i("bf0f"),i("ab80");var a={props:{time:{type:Number,default:0},autoStart:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},showDaySymbol:{type:Boolean,default:!1}},data:function(){return{timer:null,timeData:{remain:0,day:0,hour:0,minute:0,second:0}}},watch:{time:function(){this.reset()}},filters:{fillWithZero:function(t){var e=t.toString().length;while(e<2)t="0"+t,e++;return t}},methods:{updateTimeData:function(){var t=this.timeData.remain;this.timeData.day=Math.floor(t/1e3/60/60/24),this.timeData.hour=Math.floor(t/1e3/60/60%24),this.timeData.minute=Math.floor(t/1e3/60%60),this.timeData.second=Math.floor(t/1e3%60)},reset:function(){var t=this;this.timer&&this.timer.stop(),this.timeData.remain=this.time,this.updateTimeData(),this.timer=new this.$util.AdjustingInterval((function(){t.timeData.remain-=1e3,t.timeData.remain<=0?(t.timeData.day="00",t.timeData.hour="00",t.timeData.minute="00",t.timeData.second="00"):t.updateTimeData()}),1e3,this.time/1e3,(function(){t.$emit("finish")})),this.autoStart&&this.timer.start()},start:function(){this.timer||this.timer.start()}},mounted:function(){this.reset()},beforeDestroy:function(){}};e.default=a},b04d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniIcons:i("de74").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.dataList&&t.dataList.length?i("v-uni-view",{staticClass:"diy-pintuan",style:{backgroundColor:t.value.item.backgroundColor,backgroundImage:"url("+t.value.item.backgroundImg+")"}},[i("v-uni-view",{staticClass:"diy-pintuan-top"},[i("v-uni-view",{staticClass:"pintuan-title"},[i("v-uni-image",{staticClass:"pintuan-title-hot",attrs:{src:t.$util.img("public/static/youpin/pintuan/pintuan-hot.png")}}),i("v-uni-text",{staticClass:"pintuan-title-name",style:{color:t.value.item.textColor}},[t._v(t._s(t.name))])],1),i("v-uni-view",{staticClass:"pintuan-more",style:{color:t.value.item.textColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toMore()}}},[t._v(t._s(t.moreText)),i("uni-icons",{staticClass:"pintuan-more-icon",attrs:{type:"forward",size:"24rpx",color:t.value.item.textColor}})],1)],1),i("v-uni-scroll-view",{staticClass:"diy-pintuan-box",attrs:{"scroll-x":"true"}},t._l(t.dataList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"pintuan-box-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"pintuan-item"},[i("v-uni-view",{staticClass:"pintuan-item-image"},[i("v-uni-view",{staticClass:"goods_img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e()],1),"new"==e.promotion_type?i("v-uni-text",{staticClass:"pintuan-item-image-new"},[t._v(t._s(e.promotion_type_desc))]):t._e()],1),i("v-uni-view",{staticClass:"pintuan-item-info"},[i("v-uni-view",{staticClass:"pintuan-item-new-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"pintuan-item-tip"},[i("v-uni-text",[t._v(t._s(e.pintuan_num)+"人团")]),i("v-uni-text",[t._v("已开"+t._s(e.order_num)+"团")])],1),i("v-uni-view",{staticClass:"pintuan-item-bottom"},[i("v-uni-view",{staticClass:"pintuan-item-bottom-left"},[i("v-uni-view",{staticClass:"pintuan-item-original-price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(e.market_price))],1),i("v-uni-view",{staticClass:"pintuan-item-new-price"},[i("v-uni-text",[t._v("￥")]),i("v-uni-text",{staticClass:"pintuan-item-new-price-integer"},[t._v(t._s(t._f("getInteger")(e.pintuan_price)))]),i("v-uni-text",{staticClass:"pintuan-item-new-price-Decimals"},[t._v(t._s(t._f("getDecimals")(e.pintuan_price)))])],1)],1),i("uni-icons",{staticClass:"pintuan-item-bottom-right",attrs:{type:"plus-filled",color:"var(--custom-brand-color)",size:"20"}})],1)],1)],1)],1)})),1)],1):t._e()},o=[]},b190:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-4e071422]{width:100%;text-align:center}.diy-seeding[data-v-4e071422]{margin-top:%?20?%;border-radius:%?20?%;width:100%;padding:%?20?% 0 %?20?% %?20?%;box-sizing:border-box;background-size:100% 100%;background-repeat:no-repeat}.diy-seeding-top[data-v-4e071422]{width:100%;display:flex;justify-content:space-between;align-items:center;padding-right:%?20?%;box-sizing:border-box}.diy-seeding-top .seeding-title[data-v-4e071422]{display:flex;align-items:center}.diy-seeding-top .seeding-title .seeding-title-name[data-v-4e071422]{font-size:%?36?%;font-weight:bolder;margin-right:%?20?%}.diy-seeding-top .seeding-more[data-v-4e071422]{font-size:%?26?%;display:flex;align-items:center}.diy-seeding-top .seeding-more-icon[data-v-4e071422]{border:1px solid;border-radius:50%;margin-top:%?2?%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.diy-seeding-box[data-v-4e071422]{width:100%;\r\n  /*white-space 不能丢  */white-space:nowrap;box-sizing:border-box;margin-top:%?28?%}.seeding-box-item[data-v-4e071422]{width:%?210?%;height:100%;vertical-align:top;display:inline-block;background:#fff;border-radius:%?20?%;margin-right:%?12?%;box-sizing:border-box}.seeding-box-item .seeding-item[data-v-4e071422]{width:100%;height:100%}.seeding-box-item .seeding-item-image[data-v-4e071422]{width:100%;height:%?280?%;border-radius:%?20?%;overflow:hidden;display:flex;justify-content:center;align-items:center;position:relative}.seeding-box-item .seeding-item-image-bg[data-v-4e071422]{width:100%;height:100%;padding:0;margin:0;display:block;border-radius:%?20?%}.seeding-box-item .seeding-item-image-star[data-v-4e071422]{height:%?56?%;border-radius:%?100?%;background:rgba(0,0,0,.4);position:absolute;right:%?12?%;bottom:%?12?%;display:flex;justify-content:center;align-items:center;padding:0 %?16?%;box-sizing:border-box}.seeding-box-item .seeding-item-image-star-like[data-v-4e071422]{width:%?38?%;height:%?34?%;margin-right:%?4?%}.seeding-box-item .seeding-item-image-star uni-text[data-v-4e071422]{font-size:%?26?%;font-weight:400;line-height:%?18.62?%;color:#fff;padding-top:%?2?%;box-sizing:border-box}.seeding-box-item .seeding-item-image-star-revenue[data-v-4e071422]{width:%?105?%;height:%?140?%;position:absolute;left:%?-18?%;bottom:%?-2?%}.seeding-box-item .seeding-item-bottom[data-v-4e071422]{display:flex;justify-content:space-between;margin-top:%?14?%}.seeding-box-item .seeding-item-bottom-right[data-v-4e071422]{display:flex;align-items:flex-end;line-height:%?40?%;padding-bottom:%?6?%;box-sizing:border-box}.seeding-box-item .seeding-item-original-price[data-v-4e071422]{font-size:%?18?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}.seeding-box-item .seeding-item-new-price[data-v-4e071422]{font-size:%?24?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.seeding-box-item .seeding-item-new-price uni-text[data-v-4e071422]:first-child{font-size:%?20?%}.seeding-box-item .seeding-item-new-price-integer[data-v-4e071422]{font-size:%?36?%}',""]),t.exports=e},b44f:function(t,e,i){"use strict";i.r(e);var a=i("46d4"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},b49b:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc"));i("64aa"),i("c9b5"),i("bf0f"),i("ab80"),i("e966"),i("fd3c"),i("aa9c"),i("f7a5"),i("c223"),i("5c47"),i("dc8a"),i("4626"),i("5ac7");var s=a(i("de74")),r={name:"diy-product-topic",components:{uniIcons:s.default},props:{config:{type:Object,default:function(){return{}}},topHeight:{type:Number,default:0},showTop:{type:Number,default:0},scrollTop:{type:Number,default:0}},watch:{scrollTop:function(t){4!=this.config.selectedTemplate&&5!=this.config.selectedTemplate||(this.getStickyPosition(),this.getTopicAreaRect())},is_scroll_to_top:function(t,e){var i=this;!e&&t&&setTimeout((function(){i.getTabHeight()}),300)}},created:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.key_index=t.$util.generateRandomString(8),t.productTopicIndex="product-topic-".concat(t.key_index),t.config.selectBgColor&&(t.selectBgColorList=t.$util.generateRGBAColors(t.config.selectBgColor)),t.topic_id_list=t.config.topicId.toString().split(","),4!=t.config.selectedTemplate&&5!=t.config.selectedTemplate){e.next=13;break}return t.page_size=parseInt(t.config.displayGoodsCount),e.next=8,t.checkTopicIds();case 8:t.currentTopicId=t.tab_list.length>0?t.tab_list[0].topic_id:null,t.scroll_into_id="scroll_into_topic_id_".concat(t.currentTopicId),4==t.config.selectedTemplate?(t.tab_list.map((function(e){return t.topic_class_name_list.push("product-topic-four-area-".concat(e.topic_id)),e})),i=t.tab_list.length&&t.tab_list[0].topic_id,i&&t.getData(i,!0)):t.tab_list.map((function(e){return t.topic_class_name_list.push("product-topic-four-area-".concat(e.topic_id)),t.getData(e.topic_id,!0),e})),e.next=16;break;case 13:return t.currentTopicId=t.topic_id_list[0],e.next=16,t.getData(t.currentTopicId);case 16:case"end":return e.stop()}}),e)})))()},data:function(){return{currentTopicId:null,topic_id_list:[],tab_list:[],dataList:[],list1:[],list2:[],serialColor:["rgba(245, 93, 93, 1)","rgba(251, 107, 13, 1)","rgba(255, 195, 0, 1)"],page:1,page_count:1,page_size:10,showPage:0,totalCount:0,originalList:[],is_scroll_to_top:!1,selectBgColorList:[],all_data_dict:{},list1_dict:{},list2_dict:{},topic_class_name_list:[],scroll_into_id:"",tab_height:40,key_index:"",productTopicIndex:""}},methods:{imageError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},waterfallImageError:function(t,e){e[t]&&(e[t].sku_image=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()},skuImageError:function(t,e){e[t]&&(e[t]=this.$util.getDefaultImage().default_goods_img),this.$forceUpdate()},toProductDetail:function(t){var e=this;this.$util.toProductDetail(t,(function(i){e.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"ProductTopic",diy_text:t.goods_name,diy_link:i,diy_image:e.$util.img(t.sku_image)})}))},checkTopicIds:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$api.sendRequest({url:t.$apiUrl.checkTopicIdsUrl,async:!1,data:{all_topic_id:t.topic_id_list}});case 3:i=e.sent,0==i.code&&(t.tab_list=i.data),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})))()},getData:function(t){var e=arguments,i=this;return(0,o.default)((0,n.default)().mark((function a(){var o,s,r,c;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(o=e.length>1&&void 0!==e[1]&&e[1],t){a.next=3;break}return a.abrupt("return");case 3:if(!o){a.next=7;break}i.$api.sendRequest({url:i.$apiUrl.getTopicGoodsListUrl,data:{page_size:i.page_size,page:i.page,topic_id:t},success:function(e){if(0==e.code){switch(e.data.list=e.data.list.map((function(t){return t.sku_image=i.$util.imageCdnResize(t.sku_image,{image_process:"resize,w_700","x-oss-process":"image/resize,w_700"}),t})),i.config.selectedTemplate){case 1:i.dataList=e.data.list.slice(0,3);break;case 2:i.dataList=e.data.list;break;case 3:i.originalList=i.originalList.concat(e.data.list);break;case 4:var a=e.data.list.slice(0,4).map((function(t){return t.sku_image}));e.data.list.push({type_link:"more",sku_images:a}),i.$set(i.all_data_dict,t,e.data.list);break;case 5:var n=e.data.list.slice(0,3).map((function(t){return t.sku_image}));e.data.list.push({type_link:"more",sku_images:n}),i.$set(i.all_data_dict,t,e.data.list);break;default:i.dataList=e.data.list}i.page_count=e.data.page_count,i.totalCount=e.data.count}}}),a.next=37;break;case 7:return a.prev=7,a.next=10,i.$api.sendRequest({url:i.$apiUrl.getTopicGoodsListUrl,async:!1,data:{page_size:i.page_size,page:i.page,topic_id:t}});case 10:if(s=a.sent,0!=s.code){a.next=33;break}s.data.list=s.data.list.map((function(t){return t.sku_image=i.$util.imageCdnResize(t.sku_image,{image_process:"resize,w_700","x-oss-process":"image/resize,w_700"}),t})),a.t0=i.config.selectedTemplate,a.next=1===a.t0?16:2===a.t0?18:3===a.t0?20:4===a.t0?22:5===a.t0?26:30;break;case 16:return i.dataList=s.data.list.slice(0,3),a.abrupt("break",31);case 18:return i.dataList=s.data.list,a.abrupt("break",31);case 20:return i.originalList=i.originalList.concat(s.data.list),a.abrupt("break",31);case 22:return r=s.data.list.slice(0,4).map((function(t){return t.sku_image})),s.data.list.push({type_link:"more",sku_images:r}),i.$set(i.all_data_dict,t,s.data.list),a.abrupt("break",31);case 26:return c=s.data.list.slice(0,3).map((function(t){return t.sku_image})),s.data.list.push({type_link:"more",sku_images:c}),i.$set(i.all_data_dict,t,s.data.list),a.abrupt("break",31);case 30:i.dataList=s.data.list;case 31:i.page_count=s.data.page_count,i.totalCount=s.data.count;case 33:a.next=37;break;case 35:a.prev=35,a.t1=a["catch"](7);case 37:case"end":return a.stop()}}),a,null,[[7,35]])})))()},changeData:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(t.originalList.length<t.totalCount)){e.next=6;break}return t.page+=1,uni.showLoading({title:"加载中",mask:!0}),e.next=5,t.getData(t.currentTopicId);case 5:uni.hideLoading();case 6:9*(t.showPage+1)<t.totalCount?t.showPage+=1:t.showPage=0;case 7:case"end":return e.stop()}}),e)})))()},toTopicList:function(t){this.$util.diyCompateRedirectTo({wap_url:"/promotionpages/task/list/list?topic_id=".concat(t||this.currentTopicId)})},changeTab:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function i(){var a;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:a=uni.createSelectorQuery().in(e),a.select(".".concat(e.productTopicIndex," .product-topic-four-area-").concat(t)).boundingClientRect((function(t){var i=0;i=(t.top,e.topHeight,e.scrollTop+(t.top-e.topHeight-e.tab_height)+5),e.$emit("scrollToPoint",i)})).exec();case 2:case"end":return i.stop()}}),i)})))()},changeList:function(t,e){var i=t.name+"_dict",a=this[i][e]?this[i][e]:[];a.push(t.value),this.$set(this[i],e,a)},waterfallFinish:function(){for(var t=this.tab_list.map((function(t){return t.topic_id})),e=Object.keys(this.all_data_dict).map((function(t){return parseInt(t)})),i=0;i<t.length;i++)if(!e.includes(t[i])){this.getData(t[i],!0);break}},getTabHeight:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select(".".concat(this.productTopicIndex," .product-topic-four-tab")).boundingClientRect((function(e){e&&e.height&&(t.tab_height=e.height)})).exec()},getStickyPosition:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select(".".concat(this.productTopicIndex," .product-topic-four")).boundingClientRect((function(e){e&&(e.top<=t.topHeight&&e.top+e.height>t.topHeight?t.is_scroll_to_top=!0:t.is_scroll_to_top=!1)})).exec()},getTopicAreaRect:function(){var t=this,e=uni.createSelectorQuery().in(this);this.topic_class_name_list.map((function(i){var a=i.split("-").pop();return e.select(".".concat(i)).boundingClientRect((function(e){e.height+e.top>t.topHeight+t.tab_height&&e.top<=t.topHeight+t.tab_height&&t.currentTopicId!=a&&(t.currentTopicId=a,t.scroll_into_id="scroll_into_topic_id_".concat(a))})).exec(),i}))}}};e.default=r},b56d:function(t,e,i){var a=i("8315");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("0b278e66",a,!0,{sourceMap:!1,shadowMode:!1})},b5ce:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("4b89"),n={name:"diy-share-navigate-h5",data:function(){return{isShow:!1,isWeiXin:this.$util.isWeiXin(),isOnXianMaiApp:!1}},methods:{maskClose:function(t){0==t.target.offsetTop&&(this.isShow=!1)},open:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(this.isOnXianMaiApp){var i={title:t.desc?this.$util.shareTitleAddNickname(t.desc):"",desc:t.desc?t.desc:"",webpageUrl:t.webpageUrl?t.webpageUrl:"",thumbImage:t.imageUrl?this.$util.imageCdnResize(t.imageUrl,{image_process:"resize,w_300","x-oss-process":"image/resize,w_300"}):"",path:t.link};(0,a.shareMiniProgramSchemeGo)(i),e&&"function"==typeof e&&e()}else this.isShow=!0,e&&"function"==typeof e&&e()},close:function(){this.isShow=!1}},created:function(){this.isOnXianMaiApp=a.isOnXianMaiApp}};e.default=n},b5ea:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5aed521c]{width:100%;text-align:center}.swiper[data-v-5aed521c]{margin-top:%?20?%;position:relative}.swiper--not[data-v-5aed521c]{margin-top:0}.swiper--list[data-v-5aed521c]{height:%?228?%}.swiper--list--item uni-image[data-v-5aed521c]{width:100%;height:100%;border-radius:%?20?%}.swiper--list--two[data-v-5aed521c]{height:%?1000?%;width:100vw;margin-left:%?-20?%}.swiper--list--two--item uni-image[data-v-5aed521c]{width:100%;height:100%}[data-v-5aed521c] .uni-swiper-dot{width:%?16?%;height:%?6?%;border-radius:%?20?%;background:hsla(0,0%,100%,.5)}.swiper-dots[data-v-5aed521c]{width:100%;position:absolute;left:0;bottom:%?30?%;display:flex;justify-content:center;align-items:center}.swiper-dots-one[data-v-5aed521c]{width:%?20?%;height:%?6?%;border-radius:%?20?%;background:hsla(0,0%,100%,.5)}.swiper-dots-one-active[data-v-5aed521c]{background:var(--custom-brand-color)}.swiper-dots-one[data-v-5aed521c]:not(:last-child){margin-right:%?10?%}',""]),t.exports=e},b9bf:function(t,e,i){"use strict";i.r(e);var a=i("6345"),n=i("0b63");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("9c24");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"f7512cd4",null,!1,a["a"],void 0);e["default"]=r.exports},bb0b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c");var a={props:{sharePopupOptions:{type:Array,default:[]},canvasOptions:{type:Object,default:{width:620,height:917,borderRadius:0}},shareType:{type:String}},data:function(){return{isIPhoneX:!1,ctx:null,counter:-1,drawPathQueue:[],imagePath:"",isShowLoading:!0,height:0,windowHeight:0,windowWidth:0,bottomPadding:0}},computed:{myPx:function(){return 1},drawQueue:function(){return this.sharePopupOptions}},created:function(){this.getSafeArea(),this.ctx=uni.createCanvasContext("myCanvas",this)},watch:{drawPathQueue:function(t,e){if(t.length===this.drawQueue.length)for(var i=0;i<this.drawPathQueue.length;i++)for(var a=0;a<this.drawPathQueue.length;a++){var n=this.drawPathQueue[a];if(n.index===i){if("text"===n.type){if(this.ctx.setFillStyle(n.color||"#000"),this.ctx.setFontSize(n.size*this.myPx),n.textBaseline){var o=n.textBaselineColor||"#999999";this.ctx.strokeStyle=o,this.ctx.moveTo(n.x,n.y-5),this.ctx.lineTo(this.ctx.measureText(n.text).width+20,n.y-5),this.ctx.stroke(),this.ctx.textBaseline=n.textBaseline}if(n.width&&n.text){for(var s=n.text.split(""),r="",c=[],l=n.width,d=n.lineNum||1,u=n.lineHeight||20,p=0;p<s.length;p++)this.ctx.measureText(r).width<l&&this.ctx.measureText(r+s[p]).width<=l?(r+=s[p],p==s.length-1&&c.push(r)):(c.push(r),r=s[p]);if(d<=c.length)for(var f=0;f<d;f++)n.fontWeight?(this.ctx.fillText(c[f],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(c[f],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(c[f],n.x*this.myPx,n.y*this.myPx),n.y=n.y+u;else for(var g=0;g<c.length;g++)n.fontWeight?(this.ctx.fillText(c[g],n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(c[g],n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(c[g],n.x*this.myPx,n.y*this.myPx),n.y=n.y+u}else n.fontWeight?(this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx+.5),this.ctx.fillText(n.text,n.x*this.myPx+.5,n.y*this.myPx)):this.ctx.fillText(n.text,n.x*this.myPx,n.y*this.myPx);this.counter--}if("image"===n.type){if(n.path)if(n.radius){var v=2*n.radius,h=n.x+n.radius,m=n.y+n.radius;this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(h,m,n.radius,0,2*Math.PI),this.ctx.clip(),this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,v*this.myPx,v*this.myPx),this.ctx.restore()}else this.ctx.drawImage(n.path,n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx);else this.ctx.fillStyle=n.background,this.ctx.fillRect(n.x*this.myPx,n.y*this.myPx,n.width*this.myPx,n.height*this.myPx);this.counter--}}}},counter:function(t,e){var i=this;0===t&&(this.ctx.draw(),setTimeout((function(){uni.canvasToTempFilePath({canvasId:"myCanvas",success:function(t){uni.saveFile({tempFilePath:t.tempFilePath,success:function(t){i.imagePath=t.savedFilePath,i.isShowLoading=!1}})},fail:function(t){console.log("err",t)}},i)}),100))}},methods:{open:function(){this.isIPhoneX=this.$util.isIPhoneX(),this.$refs.sharePopup.open(),this.imagePath||(this.isShowLoading=!0,this.generateImg())},closeSharePopup:function(){this.$refs.sharePopup.close()},generateImg:function(){var t=this;this.counter=this.drawQueue.length,this.drawPathQueue=[];for(var e=function(e){var i=t.drawQueue[e];if(i.index=e,"text"===i.type)return t.drawPathQueue.push(i),"continue";i.path?uni.getImageInfo({src:i.path,success:function(e){i.path=e.path,t.drawPathQueue.push(i)},fail:function(t){console.log("imageErr",t)}}):t.drawPathQueue.push(i)},i=0;i<this.drawQueue.length;i++)e(i)},saveImage:function(){wx.saveImageToPhotosAlbum({filePath:this.imagePath,success:function(t){wx.hideLoading(),wx.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(t){"saveImageToPhotosAlbum:fail auth deny"!==t.errMsg&&"saveImageToPhotosAlbum:fail:auth denied"!==t.errMsg||wx.showModal({title:"提示",content:"需要您授权保存相册",showCancel:!1,success:function(t){wx.openSetting({success:function(t){t.authSetting["scope.writePhotosAlbum"]?wx.showModal({title:"提示",content:"获取权限成功,再次点击图片即可保存",showCancel:!1}):wx.showModal({title:"提示",content:"获取权限失败，将无法保存到相册哦~",showCancel:!1})},fail:function(t){console.log("failData",t)},complete:function(t){console.log("finishData",t)}})}})}})},getSafeArea:function(){var t=uni.getSystemInfoSync();this.bottomPadding=t.screenHeight-t.safeArea.bottom}}};e.default=a},be30:function(t,e,i){"use strict";i.r(e);var a=i("26cc"),n=i("6c91");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("9a08");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"4bdacb96",null,!1,a["a"],void 0);e["default"]=r.exports},bea4:function(t,e,i){"use strict";i.r(e);var a=i("4b88"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},c01b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("5e99").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"sharePopup",staticClass:"share-popup",attrs:{type:"bottom","bottom-radius":!0}},[i("template",{attrs:{slot:"container"},slot:"container"},[t.imagePath?t._e():i("v-uni-canvas",{staticClass:"canvas canvas1",style:{width:t.canvasOptions.width+"px",height:t.canvasOptions.height+"px",borderRadius:t.canvasOptions.borderRadius},attrs:{"canvas-id":"myCanvas"}}),i("v-uni-view",{staticClass:"poster",style:{"margin-top":t.isIPhoneX?"80rpx":""}},[t.imagePath?i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:!t.isShowLoading,expression:"!isShowLoading"}],staticClass:"canvas",style:{width:t.canvasOptions.width+"rpx",height:t.canvasOptions.height+"rpx",borderRadius:t.canvasOptions.borderRadius},attrs:{src:t.imagePath,mode:""}}):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowLoading,expression:"isShowLoading"}],staticClass:"loading-layer"},[i("v-uni-view",{staticClass:"loading-anim"},[i("v-uni-view",{staticClass:"box item"},[i("v-uni-view",{staticClass:"border out item ns-border-color-top ns-border-color-left"})],1)],1)],1)],1),i("v-uni-view",[i("v-uni-view",{staticClass:"share-title"},[t._v("分享到")]),i("v-uni-view",{staticClass:"share-content"},[i("v-uni-view",{staticClass:"share-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveImage.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[i("v-uni-image",{attrs:{src:t.$util.img("public/static/youpin/goods/save-image.png"),mode:""}}),i("v-uni-text",[t._v("保存图片")])],1)],1)],1),i("v-uni-view",{staticClass:"share-footer",class:{"share-footer-padding":t.bottomPadding<1},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSharePopup.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("取消分享")])],1)],1)],2)],1)],1)},o=[]},c161:function(t,e,i){"use strict";var a=i("5c09"),n=i.n(a);n.a},c1ea0:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc"));i("64aa");var s=a(i("82c2")),r=a(i("2f73")),c=i("d64b"),l={components:{DiyShareNavigateH5:r.default,sharePopup:s.default},name:"diy-live",props:{value:{type:Object,default:function(){return{}}},siteId:{type:[Number,String]}},data:function(){return{is_subscribe:!1,liveInfo:{goods:[],live_status:101},isShowCanvas:!1,canvasOptions:{width:"634",height:"832",borderRadius:"20rpx"},sharePopupOptions:[]}},created:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getLiveInfo();case 2:case"end":return e.stop()}}),e)})))()},watch:{siteId:function(t,e){}},methods:{changeSubscribe:function(t){var e=requirePlugin("live-player-plugin"),i=t,a=this;e.getSubscribeStatus({room_id:i}).then((function(t){a.is_subscribe=t.is_subscribe})).catch((function(t){console.log("get subscribe status",t)}))},getLiveInfo:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={roomid:t.value.room_id},e.prev=1,e.next=4,t.$api.sendRequest({url:t.$apiUrl.getLiveInfoUrl,data:i,async:!1});case 4:a=e.sent,0==a.code&&a.data&&(t.liveInfo=a.data,102==t.liveInfo.live_status&&t.changeSubscribe(t.liveInfo.roomid)),e.next=10;break;case 8:e.prev=8,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,null,[[1,8]])})))()},roomFun:function(){103==this.liveInfo.live_status?(this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"LiveInfo",diy_text:"",diy_link:"/otherpages/live/end/end?room_id=".concat(this.liveInfo.roomid),diy_image:""}),this.$util.redirectTo("/otherpages/live/end/end?room_id=".concat(this.liveInfo.roomid))):this.playerLive(this.liveInfo.roomid)},playerLive:function(t){var e=this.$util.livePlayerPageUrl(t,!1);this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"LiveInfo",diy_text:"",diy_link:e,diy_image:""}),this.$util.redirectTo(e)},getLiveStatus:function(){},changeLiveStatus:function(t){var e=this;this.$api.sendRequest({url:"/live/api/live/modifyLiveStatus",data:{room_id:this.liveInfo.roomid,status:t},success:function(t){0==t.code&&e.getLiveInfo()}})},openSharePopup:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"LiveInfo",diy_text:"分享",diy_link:"",diy_image:""}),this.$util.showToast({title:"请在小程序使用此功能！"})},newCommQrcode:function(t){var e=this,i={room_id:t.roomid,shop_id:uni.getStorageSync("shop_id")};uni.getStorageSync("member_id")&&(i.recommend_member_id=uni.getStorageSync("member_id"));var a=(0,c.query_to_scene)(i),n=uni.getStorageSync("userInfo");""==uni.getStorageSync("token")&&(n=""),this.isShowCanvas=!1,this.$api.sendRequest({url:"/api/Website/newCommQrcode",data:{path:"pages/live-player-plugin",scene:a},success:function(i){if(0==i.code)if(""==n){var a={};e.$api.sendRequest({url:"/api/member/info",success:function(n){a=0==n.code&&""!=uni.getStorageSync("token")?{headimg:n.data.headimg,nickname:n.data.nickname}:{headimg:e.$util.img("/upload/default/default_img/head.png"),nickname:"请登录"},e.drawCanvas(i.data.qrcodeUrl,t,a),setTimeout((function(){e.$refs.sharePopup&&e.$refs.sharePopup.open()}),0)}})}else e.drawCanvas(i.data.qrcodeUrl,t,n),setTimeout((function(){e.$refs.sharePopup&&e.$refs.sharePopup.open()}),0);else e.$util.showToast({title:i.message})}})},drawCanvas:function(t,e,i){this.sharePopupOptions=[{background:"#fff",x:0,y:0,width:634,height:832,type:"image"},{path:this.$util.img(e.share_img),x:0,y:0,width:634,height:507,type:"image"},{path:i.headimg,radius:36,x:40,y:567,width:56,height:56,type:"image"},{text:i.nickname,size:28,color:"#333",fontWeight:"bold",x:130,y:582,type:"text"},{text:e.name,size:26,color:"#999",x:130,y:630,width:310,lineNum:2,lineHeight:34,type:"text"},{path:t,x:466,y:536,width:128,height:128,type:"image"},{background:"#F8F8F8",x:0,y:692,width:634,height:140,type:"image"},{path:this.$util.img("public/static/youpin/qrcodetips.png"),x:40,y:710,width:554,height:106,type:"image"}],this.isShowCanvas=!0},toShareAppMessage:function(t){var e=this.$util.unifySharePageParams("/pages/live-player-plugin","先迈商城","",{room_id:this.liveInfo.roomid},this.$util.img(this.liveInfo.share_img));return e}}};e.default=l},c4ae:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{recommendGoods:{type:Array,default:[]},imageError:Function},data:function(){return{}},methods:{}};e.default=a},c62f:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e838"),i("c223");var n=a(i("de74")),o={components:{UniIcons:n.default},name:"diy-pintuan",props:{value:{type:Object},dataList:{type:Array,default:function(){return[]}}},filters:{getInteger:function(t){return String(parseFloat(t)).split(".")[0]},getDecimals:function(t){return"."+String(parseFloat(t).toFixed(2)).split(".")[1]}},data:function(){return{name:"拼团好物",moreText:"查看更多"}},watch:{},created:function(){if(this.value.item){var t=this.value.item;t.name&&(this.name=t.name),t.moreText&&(this.moreText=t.moreText)}},methods:{toMore:function(){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Pintuan",diy_text:this.moreText,diy_link:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/pintuan/list/list",diy_image:""}),this.$util.diyCompateRedirectTo({wap_url:this.value.item.moreUrl?this.value.item.moreUrl:"/promotionpages/pintuan/list/list"})},toDetail:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Pintuan",diy_text:t.goods_name,diy_link:"/promotionpages/pintuan/detail/detail?id=".concat(t.pintuan_goods_id,"&goods_id=").concat(t.goods_id),diy_image:this.$util.img(t.sku_image)}),this.$util.diyCompateRedirectTo({wap_url:"/promotionpages/pintuan/detail/detail?id=".concat(t.pintuan_goods_id,"&goods_id=").concat(t.goods_id)})},imageError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},onFinish:function(){this.$emit("finish")}}};e.default=o},c740:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-feaa67e0]{width:100%;text-align:center}.new-product-area[data-v-feaa67e0]{width:100%}.new-product-area-model[data-v-feaa67e0]{margin-top:%?20?%}.new-product-area-one[data-v-feaa67e0]{width:100%;height:%?506?%;overflow:hidden;background-repeat:no-repeat;background-size:100% 100%;border-radius:%?20?%;padding:%?24?% %?20?% 0 %?20?%;box-sizing:border-box}.new-product-area-one-title[data-v-feaa67e0]{display:flex;justify-content:space-between;align-items:center}.new-product-area-one-title-text[data-v-feaa67e0]{font-size:%?36?%;font-weight:700;line-height:%?42.2?%}.new-product-area-one-title-more[data-v-feaa67e0]{font-size:%?26?%;display:flex;align-items:center}.new-product-area-one-title-more-icon[data-v-feaa67e0]{border:1px solid;border-radius:50%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.new-product-area-one-list[data-v-feaa67e0]{margin-top:%?20?%;display:flex}.new-product-area-one-list-item[data-v-feaa67e0]{width:%?212?%;border-radius:%?20?%;background-color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;padding:0 0 %?12?% 0;box-sizing:border-box}.new-product-area-one-list-item[data-v-feaa67e0]:not(:last-child){margin-right:%?20?%}.new-product-area-one-list-item-image[data-v-feaa67e0]{width:%?212?%;height:%?212?%;border-radius:%?20?%;position:relative}.new-product-area-one-list-item-image-img[data-v-feaa67e0]{width:%?212?%;height:%?212?%;border-radius:%?20?%;display:block}.new-product-area-one-list-item-image .goods_img-over[data-v-feaa67e0]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.new-product-area-one-list-item-image-serial[data-v-feaa67e0]{width:%?42?%;height:%?32?%;border-radius:%?20?% 0 %?20?% 0;position:absolute;left:0;top:0;font-size:%?24?%;font-weight:700;line-height:%?24?%;color:#fff;display:flex;justify-content:center;align-items:center}.new-product-area-one-list-item-info[data-v-feaa67e0]{width:%?196?%;margin-top:%?16?%;padding-bottom:%?10?%;box-sizing:border-box}.new-product-area-one-list-item-info-name[data-v-feaa67e0]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#383838;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;min-height:%?64?%}.new-product-area-one-list-item-info-price[data-v-feaa67e0]{margin-top:%?10?%;display:flex;font-size:%?36?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.new-product-area-one-list-item-info-price-symbol[data-v-feaa67e0]{align-self:flex-end;font-size:%?24?%;font-weight:700;line-height:%?44?%}.new-product-area-one-list-item-info-original-price[data-v-feaa67e0]{margin-top:%?10?%;font-size:%?20?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}',""]),t.exports=e},cb61:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-1f02d457]{width:100%;text-align:center}.tagproducts[data-v-1f02d457]{margin-top:%?24?%;display:flex;flex-wrap:wrap;justify-content:space-between;margin-bottom:%?20?%;margin-bottom:calc(%?20?% + constant(safe-area-inset-bottom));margin-bottom:calc(%?20?% + env(safe-area-inset-bottom))}.tagproducts--one[data-v-1f02d457]{width:%?343?%;background:#fff;border-radius:%?8?%;margin-bottom:%?24?%}.tagproducts--one--img[data-v-1f02d457]{width:100%;height:%?343?%;position:relative}.tagproducts--one--img uni-image[data-v-1f02d457]{width:100%;height:100%;border-radius:%?8?% %?8?% 0 0}.tagproducts--one--img uni-image.over[data-v-1f02d457]{width:%?120?%;height:%?120?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.tagproducts--one--name[data-v-1f02d457]{padding:0 %?16?%;box-sizing:border-box;font-size:%?26?%;font-weight:500;color:#343434;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-top:%?20?%;position:relative;line-height:1.4;min-height:%?74?%}.tagproducts--one--name uni-text[data-v-1f02d457]{position:absolute;left:0;top:%?0?%;display:inline-block;width:%?48?%;height:%?32?%;line-height:%?32?%;text-align:center;font-size:%?20?%;font-weight:500;color:#fff;background:linear-gradient(55deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:%?4?%}.tagproducts--one--price[data-v-1f02d457]{padding:0 %?16?%;box-sizing:border-box;display:flex;align-items:center;margin-top:%?30?%}.tagproducts--one--price uni-view[data-v-1f02d457]:first-child{font-size:%?36?%;font-family:PingFang SC;font-weight:700;color:var(--custom-brand-color)}.tagproducts--one--price uni-view:first-child uni-text[data-v-1f02d457]{font-size:%?26?%}.tagproducts--one--price uni-view[data-v-1f02d457]:last-child{font-size:%?24?%;font-weight:500;text-decoration:line-through;color:#9a9a9a;margin-left:%?8?%}',""]),t.exports=e},cbc6:function(t,e,i){var a=i("cb61");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("686bd068",a,!0,{sourceMap:!1,shadowMode:!1})},cdff:function(t,e,i){"use strict";var a=i("76d1"),n=i.n(a);n.a},d1af:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-5c283b1f]{width:100%;text-align:center}.diy-pintuan[data-v-5c283b1f]{margin-top:%?20?%;border-radius:%?20?%;width:100%;padding:%?20?% 0 %?20?% %?20?%;box-sizing:border-box;background-size:100% 100%;background-repeat:no-repeat}.diy-pintuan-top[data-v-5c283b1f]{width:100%;display:flex;justify-content:space-between;align-items:center;padding-right:%?20?%;box-sizing:border-box}.diy-pintuan-top .pintuan-title[data-v-5c283b1f]{display:flex;align-items:center\r\n  /* .pintuan-titie-tit{\r\n      border-radius: 50rpx;\r\n      background-color:$base-color;\r\n      color:#ffffff;\r\n      font-size: 22rpx;\r\n      padding: 0 15rpx;\r\n      line-height: 1.6;\r\n      font-size: $ns-font-size-sm;\r\n    } */}.diy-pintuan-top .pintuan-title-hot[data-v-5c283b1f]{width:%?33?%;height:%?33?%;margin-right:%?14?%}.diy-pintuan-top .pintuan-title .pintuan-title-name[data-v-5c283b1f]{font-size:%?36?%;font-weight:bolder;margin-right:%?20?%}.diy-pintuan-top .pintuan-more[data-v-5c283b1f]{font-size:%?26?%;display:flex;align-items:center}.diy-pintuan-top .pintuan-more-icon[data-v-5c283b1f]{border:1px solid;border-radius:50%;margin-top:%?2?%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.diy-pintuan-box[data-v-5c283b1f]{width:100%;\r\n  /*white-space 不能丢  */white-space:nowrap;box-sizing:border-box;margin-top:%?28?%}.pintuan-box-item[data-v-5c283b1f]{width:%?188?%;height:100%;vertical-align:top;display:inline-block;background:#fff;border-radius:%?20?%;margin-right:%?20?%;box-sizing:border-box}.pintuan-box-item .pintuan-item[data-v-5c283b1f]{width:100%;height:100%}.pintuan-box-item .pintuan-item-image[data-v-5c283b1f]{width:100%;height:%?188?%;border-radius:%?20?% %?20?% 0 0;overflow:hidden;display:flex;justify-content:center;align-items:center;position:relative}.pintuan-box-item .pintuan-item-image .goods_img[data-v-5c283b1f]{position:relative}.pintuan-box-item .pintuan-item-image .goods_img-over[data-v-5c283b1f]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.pintuan-box-item .pintuan-item-image uni-image[data-v-5c283b1f]{width:%?205?%;height:%?205?%;padding:0;margin:0;display:block}.pintuan-box-item .pintuan-item-image-new[data-v-5c283b1f]{width:100%;height:%?40?%;line-height:%?40?%;background:#000;opacity:.5;border-radius:0 0 %?20?% %?20?%;font-size:%?26?%;font-weight:500;color:#fff;box-sizing:border-box;text-align:center;position:absolute;left:0;bottom:0}.pintuan-box-item .pintuan-item-info[data-v-5c283b1f]{padding:0 %?8?%;box-sizing:border-box}.pintuan-box-item .pintuan-item-new-name[data-v-5c283b1f]{white-space:normal;margin-top:%?12?%;box-sizing:border-box;height:%?66?%;line-height:1.3;font-size:%?26?%;color:#383838;font-weight:400;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.pintuan-box-item .pintuan-item-bottom[data-v-5c283b1f]{display:flex;justify-content:space-between;margin-top:%?14?%}.pintuan-box-item .pintuan-item-bottom-right[data-v-5c283b1f]{display:flex;align-items:flex-end;line-height:%?40?%;padding-bottom:%?6?%;box-sizing:border-box}.pintuan-box-item .pintuan-item-original-price[data-v-5c283b1f]{font-size:%?18?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}.pintuan-box-item .pintuan-item-new-price[data-v-5c283b1f]{font-size:%?24?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.pintuan-box-item .pintuan-item-new-price uni-text[data-v-5c283b1f]:first-child{font-size:%?20?%}.pintuan-box-item .pintuan-item-new-price-integer[data-v-5c283b1f]{font-size:%?36?%}.pintuan-box-item .pintuan-item-tip[data-v-5c283b1f]{display:flex;align-items:center;box-sizing:border-box;height:%?26?%;border-radius:%?40?%;background:var(--custom-brand-color-10);margin-top:%?6?%}.pintuan-box-item .pintuan-item-tip uni-text[data-v-5c283b1f]:first-child{height:%?26?%;font-size:%?16?%;font-weight:400;line-height:%?26?%;padding:0 %?16?%;box-sizing:border-box;color:#fcfcfc;border-radius:%?40?%;background:var(--custom-brand-color)}.pintuan-box-item .pintuan-item-tip uni-text[data-v-5c283b1f]:last-child{font-size:%?16?%;font-weight:400;height:%?26?%;line-height:%?26?%;color:var(--custom-brand-color);margin-left:%?6?%}',""]),t.exports=e},d386:function(t,e,i){"use strict";var a=i("cbc6"),n=i.n(a);n.a},d3b8:function(t,e,i){"use strict";i.r(e);var a=i("ac31"),n=i("31c3");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=r.exports},d477:function(t,e,i){"use strict";i.r(e);var a=i("b49b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},d47d:function(t,e,i){"use strict";i.r(e);var a=i("c1ea0"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},d4f7:function(t,e,i){"use strict";i.r(e);var a=i("59c6"),n=i("d477");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("2131");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1ff7cd6f",null,!1,a["a"],void 0);e["default"]=r.exports},d530:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.recommendGoods.length>0?i("v-uni-view",{staticClass:"recommend"},[i("v-uni-view",{staticClass:"recommend--title"},[i("v-uni-text"),t._v("店主推荐")],1),i("v-uni-view",{staticClass:"recommend--products"},t._l(t.recommendGoods,(function(e,a){return i("v-uni-view",{key:a,staticClass:"recommend--products--one",style:(a+1)%3==2?"margin:0 24rpx;":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"recommend--products--one--img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_image)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.recommendGoods,a)}}})],1),i("v-uni-view",{staticClass:"recommend--products--one--name"},[t._v(t._s(e.goods_name))])],1)})),1)],1):t._e()},n=[]},d58e:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniIcons:i("de74").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"new-product-area"},[t.dataList.length?i("v-uni-view",{staticClass:"new-product-area-model"},[1==t.config.selectedTemplate?i("v-uni-view",{staticClass:"new-product-area-one",style:{backgroundColor:t.config.backgroundColor,backgroundImage:"url("+t.config.backgroundImg+")"}},[i("v-uni-view",{staticClass:"new-product-area-one-title",style:{color:t.config.fontColor}},[i("v-uni-text",{staticClass:"new-product-area-one-title-text"},[t._v(t._s(t.config.title))]),i("v-uni-view",{staticClass:"new-product-area-one-title-more",style:{color:t.config.fontColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toList.apply(void 0,arguments)}}},[t._v(t._s(t.config.moreText)),i("uni-icons",{staticClass:"new-product-area-one-title-more-icon",attrs:{type:"forward",size:"24rpx",color:t.config.fontColor}})],1)],1),i("v-uni-view",{staticClass:"new-product-area-one-list"},t._l(t.dataList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"new-product-area-one-list-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toProductDetail(e)}}},[i("v-uni-view",{staticClass:"new-product-area-one-list-item-image"},[i("v-uni-image",{staticClass:"new-product-area-one-list-item-image-img",attrs:{src:t.$util.img(e.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}}),0==e.goods_stock?i("v-uni-image",{staticClass:"goods_img-over",attrs:{src:t.$util.img("public/static/youpin/product-sell-out.png")}}):t._e(),i("v-uni-text",{staticClass:"new-product-area-one-list-item-image-serial",style:{backgroundColor:t.serialColor[a]}},[t._v(t._s(a+1))])],1),i("v-uni-view",{staticClass:"new-product-area-one-list-item-info"},[i("v-uni-view",{staticClass:"new-product-area-one-list-item-info-name"},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"new-product-area-one-list-item-info-price"},[i("v-uni-text",{staticClass:"new-product-area-one-list-item-info-price-symbol"},[t._v("￥")]),t._v(t._s(e.sale_price))],1),i("v-uni-view",{staticClass:"new-product-area-one-list-item-info-original-price"},[t._v("￥"+t._s(e.market_price))])],1)],1)})),1)],1):t._e()],1):t._e()],1)},o=[]},d636:function(t,e,i){"use strict";i.r(e);var a=i("a7d3"),n=i("bea4");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("d386");var s=i("828b"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"1f02d457",null,!1,a["a"],void 0);e["default"]=r.exports},d754:function(t,e,i){var a=i("b5ea");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("51ec9938",a,!0,{sourceMap:!1,shadowMode:!1})},dc85:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-1ff7cd6f]{width:100%;text-align:center}.product-topic[data-v-1ff7cd6f]{width:100%}.product-topic-model[data-v-1ff7cd6f]{margin-top:%?20?%}.product-topic-one[data-v-1ff7cd6f]{width:100%;height:%?506?%;overflow:hidden;background-repeat:no-repeat;background-size:100% 100%;border-radius:%?20?%;padding:%?24?% %?20?% 0 %?20?%;box-sizing:border-box}.product-topic-one-title[data-v-1ff7cd6f]{display:flex;justify-content:space-between;align-items:center}.product-topic-one-title-text[data-v-1ff7cd6f]{font-size:%?36?%;font-weight:700;line-height:%?42.2?%}.product-topic-one-title-more[data-v-1ff7cd6f]{font-size:%?26?%;display:flex;align-items:center}.product-topic-one-title-more-icon[data-v-1ff7cd6f]{border:1px solid;border-radius:50%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.product-topic-one-list[data-v-1ff7cd6f]{margin-top:%?20?%;-webkit-column-count:3;column-count:3;gap:0 %?20?%}.product-topic-one-list-item[data-v-1ff7cd6f]{width:%?212?%;border-radius:%?20?%;background-color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;padding:0 0 %?12?% 0;box-sizing:border-box}.product-topic-one-list-item-image[data-v-1ff7cd6f]{width:%?212?%;height:%?212?%;border-radius:%?20?%;position:relative}.product-topic-one-list-item-image-img[data-v-1ff7cd6f]{width:%?212?%;height:%?212?%;border-radius:%?20?%;display:block}.product-topic-one-list-item-image .goods_img-over[data-v-1ff7cd6f]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.product-topic-one-list-item-image-serial[data-v-1ff7cd6f]{width:%?42?%;height:%?32?%;border-radius:%?20?% 0 %?20?% 0;position:absolute;left:0;top:0;font-size:%?24?%;font-weight:700;line-height:%?24?%;color:#fff;display:flex;justify-content:center;align-items:center}.product-topic-one-list-item-info[data-v-1ff7cd6f]{width:%?196?%;margin-top:%?16?%;padding-bottom:%?10?%;box-sizing:border-box}.product-topic-one-list-item-info-name[data-v-1ff7cd6f]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#383838;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;min-height:%?64?%}.product-topic-one-list-item-info-price[data-v-1ff7cd6f]{margin-top:%?10?%;display:flex;font-size:%?36?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.product-topic-one-list-item-info-price-symbol[data-v-1ff7cd6f]{align-self:flex-end;font-size:%?24?%;font-weight:700;line-height:%?44?%}.product-topic-one-list-item-info-original-price[data-v-1ff7cd6f]{margin-top:%?10?%;font-size:%?20?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}.product-topic-two[data-v-1ff7cd6f]{height:%?506?%;background-repeat:no-repeat;background-size:100% 100%;border-radius:%?20?%;padding:%?24?% %?20?% 0 %?20?%;box-sizing:border-box}.product-topic-two-title[data-v-1ff7cd6f]{display:flex;justify-content:space-between;align-items:center}.product-topic-two-title-text[data-v-1ff7cd6f]{font-size:%?36?%;font-weight:700;line-height:%?42.2?%}.product-topic-two-title-more[data-v-1ff7cd6f]{font-size:%?26?%;display:flex;align-items:center}.product-topic-two-title-more-icon[data-v-1ff7cd6f]{border:1px solid;border-radius:50%;height:%?26?%;width:%?26?%;display:flex;align-items:center;justify-content:center;margin-left:%?6?%}.product-topic-two-list[data-v-1ff7cd6f]{margin-top:%?20?%;white-space:nowrap;width:100%}.product-topic-two-list-item[data-v-1ff7cd6f]{width:%?212?%;border-radius:%?20?%;background-color:#fff;display:inline-flex;flex-direction:column;align-items:center;justify-content:flex-start;padding:0 0 %?12?% 0;box-sizing:border-box;margin-right:%?20?%}.product-topic-two-list-item-image[data-v-1ff7cd6f]{width:%?212?%;height:%?212?%;border-radius:%?20?%;position:relative}.product-topic-two-list-item-image-img[data-v-1ff7cd6f]{width:%?212?%;height:%?212?%;border-radius:%?20?%}.product-topic-two-list-item-image .goods_img-over[data-v-1ff7cd6f]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.product-topic-two-list-item-image-serial[data-v-1ff7cd6f]{width:%?42?%;height:%?32?%;border-radius:%?20?% 0 %?20?% 0;position:absolute;left:0;top:0;font-size:%?24?%;font-weight:700;line-height:%?24?%;color:#fff;display:flex;justify-content:center;align-items:center}.product-topic-two-list-item-info[data-v-1ff7cd6f]{width:%?196?%;margin-top:%?16?%;padding-bottom:%?10?%;box-sizing:border-box}.product-topic-two-list-item-info-name[data-v-1ff7cd6f]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#383838;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;min-height:%?64?%}.product-topic-two-list-item-info-price[data-v-1ff7cd6f]{margin-top:%?10?%;display:flex;font-size:%?36?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.product-topic-two-list-item-info-price-symbol[data-v-1ff7cd6f]{align-self:flex-end;font-size:%?24?%;font-weight:700;line-height:%?44?%;color:var(--custom-brand-color)}.product-topic-two-list-item-info-original-price[data-v-1ff7cd6f]{margin-top:%?10?%;font-size:%?20?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}.product-topic-three[data-v-1ff7cd6f]{width:100%;box-sizing:border-box;background-color:initial}.product-topic-three-list[data-v-1ff7cd6f]{-webkit-column-count:3;column-count:3;margin-bottom:%?-26?%}.product-topic-three-list-item[data-v-1ff7cd6f]{border-radius:%?20?%;background-color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;box-sizing:border-box;margin-bottom:%?26?%}.product-topic-three-list-item-image[data-v-1ff7cd6f]{border-radius:%?20?%;position:relative}.product-topic-three-list-item-image-img[data-v-1ff7cd6f]{width:%?218?%;height:%?218?%;border-radius:%?20?%}.product-topic-three-list-item-image .goods_img-over[data-v-1ff7cd6f]{width:%?100?%;height:%?100?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.product-topic-three-list-item-info[data-v-1ff7cd6f]{width:%?196?%;margin-top:%?16?%;padding-bottom:%?10?%;box-sizing:border-box}.product-topic-three-list-item-info-name[data-v-1ff7cd6f]{font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#383838;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;min-height:%?64?%}.product-topic-three-list-item-info-price[data-v-1ff7cd6f]{margin-top:%?10?%;display:flex;font-size:%?36?%;font-weight:700;line-height:%?36?%;color:var(--custom-brand-color)}.product-topic-three-list-item-info-price-symbol[data-v-1ff7cd6f]{align-self:flex-end;font-size:%?24?%;font-weight:700;line-height:%?44?%;color:var(--custom-brand-color)}.product-topic-three-list-item-info-original-price[data-v-1ff7cd6f]{margin-top:%?10?%;font-size:%?20?%;font-weight:400;line-height:%?20?%;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6}.product-topic-three-op[data-v-1ff7cd6f]{display:flex;justify-content:center;align-items:center;margin:0 auto;margin-top:%?40?%;width:%?148?%;height:%?48?%;border-radius:%?40?%;background:#f0f0f0;font-size:%?24?%;font-weight:400;line-height:%?27.94?%;color:#383838}.product-topic-four[data-v-1ff7cd6f]{margin-top:%?20?%}.product-topic-four-tab[data-v-1ff7cd6f]{width:100vw;margin-left:%?-20?%;padding-left:%?20?%;box-sizing:border-box;white-space:nowrap;position:fixed;top:0;height:%?88?%;line-height:%?88?%;z-index:1;background-color:#fff;display:block!important}.product-topic-four-tab .product-topic-four-tab-one[data-v-1ff7cd6f]{padding:0 %?18?%;box-sizing:border-box;height:%?52?%;border-radius:%?10?%;color:#383838;background:#fff;display:inline-block;margin-right:%?20?%;transition:all 1s}.product-topic-four-tab .product-topic-four-tab-one-active[data-v-1ff7cd6f]{color:#fff}.product-topic-four-header[data-v-1ff7cd6f]{width:100%;min-height:%?160?%;border-radius:%?20?%;margin-bottom:%?20?%}.product-topic-four-header-not-img[data-v-1ff7cd6f]{padding:%?32?% %?100?%;box-sizing:border-box}.product-topic-four-header-img[data-v-1ff7cd6f]{width:100%;height:auto;display:block;border-radius:%?20?%}.product-topic-four-header-title[data-v-1ff7cd6f]{font-size:%?32?%;font-weight:700;color:var(--custom-brand-color);text-align:center}.product-topic-four-header-subtitle[data-v-1ff7cd6f]{font-size:%?26?%;font-weight:400;letter-spacing:%?26?%;text-indent:%?26?%;color:#a6a6a6;text-align:center}.product-topic-four-list .over[data-v-1ff7cd6f]{width:%?120?%;height:%?120?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.product-topic-four-list--one[data-v-1ff7cd6f]{width:%?344?%;padding-bottom:%?20?%;border-radius:%?20?%;box-sizing:border-box;background-color:#fff;margin-bottom:%?20?%}.product-topic-four-list--one--img[data-v-1ff7cd6f]{width:100%;position:relative}.product-topic-four-list--one--img uni-image[data-v-1ff7cd6f]{width:100%;height:100%;border-radius:%?20?%;display:block}.product-topic-four-list--one--row[data-v-1ff7cd6f]{padding:0 %?20?%;box-sizing:border-box}.product-topic-four-list--one--name[data-v-1ff7cd6f]{font-size:%?26?%;font-weight:500;color:#343434;word-break:break-all;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-top:%?20?%;box-sizing:border-box;position:relative;line-height:1.4}.product-topic-four-list--one--name uni-text[data-v-1ff7cd6f]{vertical-align:text-bottom;padding:%?2?% %?10?%;margin-right:%?5?%;box-sizing:border-box;display:inline-block;text-align:center;font-size:%?20?%;font-weight:500;color:#fff;background:linear-gradient(55deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:%?4?%}.product-topic-four-list--one--price[data-v-1ff7cd6f]{display:flex;justify-content:flex-start;align-items:baseline}.product-topic-four-list--one--price uni-view[data-v-1ff7cd6f]:first-child{font-size:%?36?%;font-family:PingFang SC;font-weight:700;color:var(--custom-brand-color)}.product-topic-four-list--one--price uni-view:first-child uni-text[data-v-1ff7cd6f]{font-size:%?26?%}.product-topic-four-list--one--price uni-view[data-v-1ff7cd6f]:last-child{font-size:%?24?%;font-weight:500;text-decoration:line-through;color:#9a9a9a;margin-left:%?8?%}.product-topic-four-list--one--benefit[data-v-1ff7cd6f]{background:var(--custom-brand-color-10);font-size:%?22?%;border-radius:%?8?%;display:inline-block;margin-top:%?10?%;padding:0 %?10?%}.product-topic-four-list--one--benefit--price[data-v-1ff7cd6f]{color:var(--custom-brand-color)}.product-topic-four-list--one--benefit .benefit-color[data-v-1ff7cd6f]{color:#333}.product-topic-four-list--more[data-v-1ff7cd6f]{width:%?344?%;padding:%?10?%;border-radius:%?20?%;box-sizing:border-box;background-color:#fff;margin-bottom:%?20?%}.product-topic-four-list--more--img[data-v-1ff7cd6f]{display:flex;justify-content:space-between;flex-wrap:wrap}.product-topic-four-list--more--img--one[data-v-1ff7cd6f]{width:%?152?%;height:%?152?%;border-radius:%?20?%;margin-bottom:%?20?%}.product-topic-four-list--more--title[data-v-1ff7cd6f]{font-size:%?32?%;font-weight:700;color:var(--custom-brand-color);text-align:left;padding-left:%?10?%;box-sizing:border-box}.product-topic-four-list--more--to[data-v-1ff7cd6f]{font-size:%?26?%;font-weight:400;color:var(--custom-brand-color);text-align:left;padding-left:%?10?%;box-sizing:border-box;display:flex;align-items:center}.product-topic-four-list--more--to--icon[data-v-1ff7cd6f]{width:%?34?%;height:%?34?%;line-height:%?34?%;display:flex;justify-content:center;align-items:center;background-color:var(--custom-brand-color);box-sizing:border-box;border-radius:50%;margin-left:%?10?%}.product-topic-four-more[data-v-1ff7cd6f]{width:100%;height:%?72?%;border-radius:%?20?%;background:#fff;display:flex;justify-content:center;align-items:center}.product-topic-five-list--one[data-v-1ff7cd6f]{border-radius:%?20?%;background:#fff;margin-bottom:%?20?%}.product-topic-five-list--one--img[data-v-1ff7cd6f]{position:relative}.product-topic-five-list--one--img uni-image[data-v-1ff7cd6f]{width:100%;height:auto;border-radius:%?20?% %?20?% 0 0;display:block}.product-topic-five-list--one--img .over[data-v-1ff7cd6f]{width:%?200?%;height:%?200?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.product-topic-five-list--one--info[data-v-1ff7cd6f]{padding:%?20?% %?18?%;box-sizing:border-box}.product-topic-five-list--one--name[data-v-1ff7cd6f]{font-size:%?32?%;font-weight:700;color:#383838;text-overflow:ellipsis;overflow:hidden;word-break:break-all;white-space:nowrap;line-height:%?32?%}.product-topic-five-list--one--name uni-text[data-v-1ff7cd6f]{vertical-align:text-bottom;padding:%?2?% %?10?%;margin-right:%?5?%;box-sizing:border-box;display:inline-block;text-align:center;font-size:%?20?%;font-weight:500;color:#fff;background:linear-gradient(55deg,var(--custom-brand-color-80),var(--custom-brand-color));border-radius:%?4?%}.product-topic-five-list--one--row[data-v-1ff7cd6f]{display:flex;justify-content:space-between;align-items:center;margin-top:%?16?%}.product-topic-five-list--one--price[data-v-1ff7cd6f]{display:flex;align-items:baseline}.product-topic-five-list--one--price--retail[data-v-1ff7cd6f]{font-size:%?40?%;font-weight:700;color:var(--custom-brand-color)}.product-topic-five-list--one--price--retail .symbol[data-v-1ff7cd6f]{font-size:%?24?%}.product-topic-five-list--one--price--market[data-v-1ff7cd6f]{font-size:%?26?%;font-weight:400;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;color:#a6a6a6;margin-left:%?18?%}.product-topic-five-list--one--op[data-v-1ff7cd6f]{width:%?154?%;height:%?60?%;line-height:%?60?%;border-radius:100px;background:var(--custom-brand-color);font-size:%?32?%;font-weight:400;color:#fff;display:flex;justify-content:center;align-items:center}.product-topic-five-list--more[data-v-1ff7cd6f]{border-radius:%?20?%;background:#fff;margin-bottom:%?20?%;padding:%?20?% 0 %?20?% %?20?%;box-sizing:border-box;display:flex;align-items:center}.product-topic-five-list--more--img[data-v-1ff7cd6f]{display:flex;align-items:center}.product-topic-five-list--more--img--one[data-v-1ff7cd6f]{width:%?140?%;height:%?140?%;border-radius:%?20?%;margin-right:%?14?%}.product-topic-five-list--more--title[data-v-1ff7cd6f]{font-size:%?32?%;font-weight:700;color:#383838}.product-topic-five-list--more--to[data-v-1ff7cd6f]{font-size:%?26?%;font-weight:400;color:var(--custom-brand-color);display:flex;align-items:center}.product-topic-five-list--more--to--icon[data-v-1ff7cd6f]{width:%?26?%;height:%?26?%;background:var(--custom-brand-color);border-radius:50%;margin-left:%?10?%;display:flex;justify-content:center;align-items:center}',""]),t.exports=e},e53d:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-00ccc54c]{width:100%;text-align:center}.live-box[data-v-00ccc54c]{margin-top:%?20?%}.live-wrap[data-v-00ccc54c]{width:100%;border-radius:%?20?%;padding:%?20?%;box-sizing:border-box;overflow:hidden;background-size:100% 100%;background-repeat:no-repeat;display:flex}.live-wrap-left[data-v-00ccc54c]{width:%?264?%;height:%?352?%;border-radius:%?20?%;position:relative}.live-wrap-left-img[data-v-00ccc54c]{width:100%;height:100%;border-radius:%?20?%}.live-wrap-left-icon[data-v-00ccc54c]{width:%?92?%;position:absolute;left:%?8?%;top:%?8?%}.live-wrap-right[data-v-00ccc54c]{width:%?424?%;padding-left:%?28?%;box-sizing:border-box;padding-top:%?12?%}.live-wrap-right-title[data-v-00ccc54c]{font-size:%?32?%;font-weight:700;line-height:%?43.9?%}.live-wrap-right-name[data-v-00ccc54c]{font-size:%?24?%;font-weight:400;line-height:%?27.94?%;margin-top:%?8?%}.live-wrap-right-list[data-v-00ccc54c]{height:%?112?%;margin-top:%?30?%}.live-wrap-right-list-one[data-v-00ccc54c]{width:%?112?%;height:%?112?%;border-radius:%?10?%}.live-wrap-right-list-one[data-v-00ccc54c]:not(:first-child){margin-left:%?16?%}.live-wrap-right-op[data-v-00ccc54c]{display:flex;justify-content:space-between;align-items:center;margin-top:%?44?%}.live-wrap-right-op-share[data-v-00ccc54c]{margin:0;width:%?172?%;height:%?64?%;border-radius:%?40?%;background:#f2f2f2;display:flex;justify-content:center;align-items:center;font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#383838}.live-wrap-right-op-share-icon[data-v-00ccc54c]{width:%?28?%;height:%?28?%;margin-right:%?4?%}.live-wrap-right-op-play[data-v-00ccc54c]{width:%?172?%;height:%?64?%;border-radius:%?40?%;background:linear-gradient(225deg,#f6a6ba,#c7c2fc);display:flex;justify-content:center;align-items:center;font-size:%?28?%;font-weight:400;line-height:%?32.6?%;color:#fff;position:relative}.live-wrap-right-op-play-icon[data-v-00ccc54c]{width:%?28?%;height:%?28?%;margin-right:%?4?%}.live-wrap-right-op-play-slot[data-v-00ccc54c]{position:absolute;left:0;top:0}',""]),t.exports=e},e71a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"isShow"}],staticClass:"yd-popup",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.maskClose.apply(void 0,arguments)}}},[t.isWeiXin?i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/weixin-share-tip.png")}}):i("v-uni-image",{staticClass:"share-tip",attrs:{src:t.$util.img("public/static/youpin/browser-share-tip.png")}})],1)},n=[]},e89e:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.ads.length>0?i("v-uni-view",{staticClass:"swiper",class:{"swiper--not":"carousel-posters-2"==t.config.selectedTemplate}},[i("v-uni-swiper",{class:{"swiper--list":"carousel-posters"==t.config.selectedTemplate,"swiper--list--two":"carousel-posters-2"==t.config.selectedTemplate},attrs:{"indicator-dots":!1,"indicator-active-color":"rgba(86, 98, 130, 1)",current:t.current,autoplay:t.autoplay,circular:!0},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinishChange.apply(void 0,arguments)}}},t._l(t.ads,(function(e,a){return i("v-uni-swiper-item",{key:a,class:{"swiper--list--item":"carousel-posters"==t.config.selectedTemplate,"swiper--list--two--item":"carousel-posters-2"==t.config.selectedTemplate},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toAd(e)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e.image_url)},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(t.ads,a)}}})],1)})),1),t.ads.length>1?i("v-uni-view",{staticClass:"swiper-dots"},t._l(t.ads,(function(e,a){return i("v-uni-text",{key:a,staticClass:"swiper-dots-one",class:{"swiper-dots-one-active":a==t.current}})})),1):t._e()],1):t._e()},n=[]},e991:function(t,e,i){"use strict";i.r(e);var a=i("c62f"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},ecf9:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3fcd00a6]{width:100%;text-align:center}.diy-notice[data-v-3fcd00a6]{margin-top:%?20?%;padding:%?20?%;line-height:0;position:relative;display:flex;align-items:center;border-radius:%?20?%}.diy-notice .pic[data-v-3fcd00a6]{width:%?36?%;height:%?36?%;margin-right:%?20?%}.diy-notice .main-wrap[data-v-3fcd00a6]{display:inline-block;width:85%;position:relative}.diy-notice .flag[data-v-3fcd00a6]{border:1px solid;padding:%?4?% %?10?%;border-radius:%?24?%;margin-right:%?20?%;float:left;line-height:normal}.uni-swiper-msg[data-v-3fcd00a6]{padding:0}.uni-swiper-msg uni-swiper[data-v-3fcd00a6]{height:%?50?%}.beyond-hiding[data-v-3fcd00a6]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:block}.notice-mark[data-v-3fcd00a6]{width:100%;height:100%;background:transparent;position:absolute;left:0;top:0}',""]),t.exports=e},f0ab:function(t,e,i){"use strict";i.r(e);var a=i("2870"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},f159:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),o=a(i("2fdc"));i("e838"),i("fd3c"),i("f7a5");var s=a(i("d3b8")),r=a(i("de74")),c=(a(i("56ed")),{components:{UniIcons:r.default,countdownTimer:s.default},name:"diy-seckill",props:{value:{type:Object},dataList:{type:Object,default:function(){return{}}}},filters:{getInteger:function(t){return String(parseFloat(t)).split(".")[0]},getDecimals:function(t){return"."+String(parseFloat(t).toFixed(2)).split(".")[1]}},data:function(){return{name:"限时秒杀",moreText:"查看更多",robBg:"",showList:[]}},watch:{dataList:function(t){var e=this;if(t&&t.list&&t.list.length>0){var i=t.list.map((function(t){return t.goods_image=e.$util.imageCdnResize(t.goods_image,{image_process:"resize,w_700","x-oss-process":"image/resize,w_700"}),t}));this.showList=2==this.value.item.selectedTemplate?i.slice(0,this.value.item.displayGoodsCount):i}}},created:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i,a,o;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.value.item&&(i=t.value.item,i.name&&(t.name=i.name),i.moreText&&(t.moreText=i.moreText)),a=t.$util.colorToHex(t.$store.state.themeColorVar["--custom-brand-color"]).slice(1),t.robBg=encodeURI(t.$util.img("api/website/svgChangeFillColor?svg_name=rob-bg&color=".concat(a))),t.dataList.list&&(o=t.dataList.list.map((function(e){return e.goods_image=t.$util.imageCdnResize(e.goods_image,{image_process:"resize,w_700","x-oss-process":"image/resize,w_700"}),e})),t.showList=2==t.value.item.selectedTemplate?o.slice(0,t.value.item.displayGoodsCount):o);case 4:case"end":return e.stop()}}),e)})))()},methods:{toMore:function(t){this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Seckill",diy_text:this.moreText,diy_link:"/promotionpages/new_seckill/list/list?seckill_id=".concat(t),diy_image:""}),this.$util.diyCompateRedirectTo({wap_url:"/promotionpages/new_seckill/list/list?seckill_id=".concat(t)})},toDetail:function(t){0==this.dataList.type&&(this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Seckill",diy_text:t.goods_name,diy_link:"/promotionpages/new_seckill/detail/detail?sku_id=".concat(t.sku_id),diy_image:this.$util.img(t.sku_image)}),this.$util.diyCompateRedirectTo({wap_url:"/promotionpages/new_seckill/detail/detail?sku_id=".concat(t.sku_id)})),1==this.dataList.type&&(this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:"Seckill",diy_text:t.goods_name,diy_link:"/pages/goods/detail/detail?sku_id=".concat(t.sku_id),diy_image:this.$util.img(t.sku_image)}),this.$util.diyCompateRedirectTo({wap_url:"/pages/goods/detail/detail?sku_id=".concat(t.sku_id)}))},imageError:function(t,e){t[e].goods_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},onFinish:function(){this.$emit("finish")}}});e.default=c},fd75:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"search_product",style:[t.objStyle]},[t.handleName?i("v-uni-view",{staticClass:"search-box",style:[t.inputStyle]},[i("v-uni-text",{staticClass:"iconfont iconsousuo",style:{color:t.searchObj.fontColor||"transparent"}}),i("v-uni-input",{style:{width:"540rpx"},attrs:{maxlength:"50","confirm-type":"search",placeholder:t.tipName,"placeholder-style":"color:"+(t.searchObj.fontColor||"transparent")},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.searchBut()}},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}})],1):i("v-uni-view",{staticClass:"search-box",style:[t.inputStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tolink()}}},[i("v-uni-text",{staticClass:"iconfont iconsousuo",style:{color:t.searchObj.fontColor||"transparent"}}),i("v-uni-view",{staticClass:"txt",style:{color:t.searchObj.fontColor||"transparent"}},[t._v(t._s(t.tipName))])],1),t.handleName?i("v-uni-view",{staticClass:"text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.searchBut()}}},[t._v(t._s(t.handleName))]):t._e()],1)},n=[]}}]);