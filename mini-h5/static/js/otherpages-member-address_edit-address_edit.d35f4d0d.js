(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-member-address_edit-address_edit"],{"16f6":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("5de6")),n=i(a("2634")),s=i(a("2fdc"));a("fd3c"),a("bf0f"),a("2797"),a("aa9c");var o={props:{defaultRegions:{type:Array}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];3===e.length&&(e.join(""),t.join(""))},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var e=this.pickedArr.map((function(e){return e.map((function(e){return e.label}))}));return e}},pickedArr:function(){return this.isInitMultiArray?[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{handleColumnChange:function(e){var t=this;return(0,s.default)((0,n.default)().mark((function a(){var i,r;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:t.isInitMultiArray=!1,i=e.detail.column,r=e.detail.value,t.multiIndex[i]=r,a.t0=i,a.next=0===a.t0?7:1===a.t0?14:2===a.t0?18:19;break;case 7:return a.next=9,t.getAreasAsync(t.pickerValueArray[0][t.multiIndex[i]].value);case 9:return t.cityArr=a.sent,a.next=12,t.getAreasAsync(t.cityArr[0].value);case 12:return t.districtArr=a.sent,a.abrupt("break",19);case 14:return a.next=16,t.getAreasAsync(t.cityArr[t.multiIndex[i]].value);case 16:return t.districtArr=a.sent,a.abrupt("break",19);case 18:return a.abrupt("break",19);case 19:case"end":return a.stop()}}),a)})))()},handleValueChange:function(e){var t=(0,r.default)(e.detail.value,3),a=t[0],i=t[1],n=t[2],s=(0,r.default)(this.pickedArr,3),o=s[0],d=s[1],l=s[2],u=[o[a],d[i],l[n]];this.$emit("getRegions",u)},handleDefaultRegions:function(){var e=this;return(0,s.default)((0,n.default)().mark((function t(){var a,i,r,s;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isLoadDefaultAreas){t.next=2;break}return t.abrupt("return");case 2:e.isInitMultiArray=!1,a=0;case 4:if(!(a<e.defaultRegions.length)){t.next=37;break}i=0;case 6:if(!(i<e.pickerValueArray[a].length)){t.next=34;break}if(e.defaultRegions[a]!=e.pickerValueArray[a][i].value){t.next=31;break}return e.$set(e.multiIndex,0,i),t.next=11,e.getAreasAsync(e.pickerValueArray[a][i].value);case 11:e.cityArr=t.sent,r=0;case 13:if(!(r<e.cityArr.length)){t.next=31;break}if(e.defaultRegions[1]!=e.cityArr[r].value){t.next=28;break}return e.$set(e.multiIndex,1,r),t.next=18,e.getAreasAsync(e.cityArr[r].value);case 18:e.districtArr=t.sent,s=0;case 20:if(!(s<e.districtArr.length)){t.next=28;break}if(e.defaultRegions[2]!=e.districtArr[s].value){t.next=25;break}return e.$set(e.multiIndex,2,s),e.handleValueChange({detail:{value:[i,r,s]}}),t.abrupt("return");case 25:s++,t.next=20;break;case 28:r++,t.next=13;break;case 31:i++,t.next=6;break;case 34:a++,t.next=4;break;case 37:case"end":return t.stop()}}),t)})))()},getDefaultAreas:function(e,t){var a=this;this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(){var e=(0,s.default)((0,n.default)().mark((function e(i){var r,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=i.code){e.next=16;break}if(r=[],s=void 0,i.data.forEach((function(e,a){void 0!=t&&(0==t.level&&void 0!=t.province_id?s=t.province_id:1==t.level&&void 0!=t.city_id?s=t.city_id:2==t.level&&void 0!=t.district_id&&(s=t.district_id)),void 0==s&&0==a&&(s=e.id),r.push({value:e.id,label:e.name})})),a.pickerValueArray[t.level]=r,!(t.level+1<3)){e.next=10;break}t.level++,a.getDefaultAreas(s,t),e.next=16;break;case 10:return a.cityArr=a.pickerValueArray[1],a.districtArr=a.pickerValueArray[2],a.isInitMultiArray=!0,a.isLoadDefaultAreas=!0,e.next=16,a.handleDefaultRegions();case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})},getAreasAsync:function(e){var t=this;return(0,s.default)((0,n.default)().mark((function a(){var i,r;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$api.sendRequest({url:"/api/address/lists",data:{pid:e},async:!1});case 2:if(i=a.sent,0!=i.code){a.next=7;break}return r=[],i.data.forEach((function(e,t){r.push({value:e.id,label:e.name})})),a.abrupt("return",r);case 7:case"end":return a.stop()}}),a)})))()},getAreas:function(e,t){this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var a=[];e.data.forEach((function(e,t){a.push({value:e.id,label:e.name})})),t&&t(a)}}})}}};t.default=o},1858:function(e,t,a){"use strict";var i=a("4086"),r=a.n(i);r.a},"2a5c":function(e,t,a){"use strict";a.r(t);var i=a("3918"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"2d01":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={computed:{Development:function(){return this.$store.state.Development},themeStyleScore:function(){return this.$store.state.themeStyle},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},addonIsExit:function(){return this.$store.state.addonIsExit},wholeSaleNumber:function(){return this.$store.state.wholeSaleNumber},themeColorVar:function(){return this.$store.state.themeColorVar}}}},"327f":function(e,t,a){"use strict";var i=a("a971"),r=a.n(i);r.a},"37ff":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},r=[]},3918:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("0c26"),a("5c47"),a("0506");var r=i(a("2634")),n=i(a("2fdc")),s=i(a("d89a")),o=i(a("8469")),d=i(a("c3f5")),l=i(a("2d01")),u=i(a("de74")),c={components:{UniIcons:u.default,UniPopup:d.default,pickRegions:s.default},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},mixins:[l.default],data:function(){return{loading:!0,formData:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",latitude:0,longitude:0,is_default:0},identification_address:"",address:"",addressValue:"",back:"",redirect:"redirectTo",flag:!1,defaultRegions:[],deleteTimer:null}},onLoad:function(e){e.id?(this.formData.id=e.id,this.getAddressDetail()):this.loading=!1,e.back&&(this.back=e.back),e.redirect&&(this.redirect=e.redirect)},onShow:function(){this.$langConfig.refresh(),this.deleteTimer&&clearTimeout(this.deleteTimer)},onReady:function(){this.$refs.loadingCover.hide()},onHide:function(){this.flag=!1,this.deleteTimer&&clearTimeout(this.deleteTimer)},methods:{getAddressDetail:function(){var e=this;this.$api.sendRequest({url:"/api/memberaddress/info",data:{id:this.formData.id},success:function(t){var a=t.data;null!=a&&(e.formData.name=a.name,e.formData.mobile=a.mobile,e.formData.telephone=a.telephone,e.formData.address=a.address,e.formData.full_address=a.full_address,e.formData.latitude=a.latitude,e.formData.longitude=a.longitude,e.formData.is_default=a.is_default,e.defaultRegions=[a.province_id,a.city_id,a.district_id]),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.loading=!1},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},handleGetRegions:function(e){this.formData.full_address="",this.formData.full_address+=void 0!=e[0]?e[0].label:"",this.formData.full_address+=void 0!=e[1]?" "+e[1].label:"",this.formData.full_address+=void 0!=e[2]?" "+e[2].label:"",this.addressValue="",this.addressValue+=void 0!=e[0]?e[0].value:"",this.addressValue+=void 0!=e[1]?" "+e[1].value:"",this.addressValue+=void 0!=e[2]?" "+e[2].value:""},selectAddress:function(){var e=this;uni.chooseLocation({success:function(){var t=(0,n.default)((0,r.default)().mark((function t(a){var i;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.addressParse(a.address);case 2:i=t.sent,i&&i.area_code&&(e.defaultRegions=[i.province_code,i.city_code,i.area_code],e.formData.address=i.address,e.formData.latitude=a.latitude,e.formData.longitude=a.longitude,e.$refs.pickRegionsRef.getDefaultAreas(0,{level:0}));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),fail:function(e){console.log("err",e)}})},vertify:function(){this.formData.name=this.formData.name.trim(),this.formData.mobile=this.formData.mobile.trim(),this.formData.address=this.formData.address.trim();var e=o.default.check(this.formData,[{name:"name",checkType:"required",errorMsg:"请输入姓名"},{name:"name",checkType:"lengthMax",checkRule:20,errorMsg:"超出的最大允许输入最大长度"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"full_address",checkType:"required",errorMsg:"请选择省市区县"},{name:"address",checkType:"required",errorMsg:"详细地址不能为空"}]);if(e){var t=/[^\u4e00-\u9fa5-^a-zA-Z]+$/g.test(this.formData.name);return 1!=t||(this.$util.showToast({title:"请输入正确收件人姓名"}),void(this.flag=!1))}return this.$util.showToast({title:o.default.error}),this.flag=!1,!1},deleteAddress:function(e){var t=this;this.deleteTimer;uni.showModal({title:"操作提示",content:"确定要删除该地址吗？",success:function(a){a.confirm&&t.$api.sendRequest({url:"/api/memberaddress/delete",data:{id:e},success:function(e){0==e.code?t.$util.showToast({title:"删除成功",success:function(e){uni.removeStorageSync("member_address"),setTimeout((function(){uni.navigateBack({delta:1})}),2e3)}}):t.$util.showToast({title:"删除失败"})},fail:function(e){mescroll.endErr()}})}})},changeDefault:function(e){console.log("switch1 发生 change 事件，携带值为",e.target.value),1==e.target.value?this.formData.is_default=1:0==e.target.value&&(this.formData.is_default=0)},addressParse:function(e){var t=this;return(0,n.default)((0,r.default)().mark((function a(){var i,n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i={},a.prev=1,a.next=4,t.$api.sendRequest({url:t.$apiUrl.addressParseUrl,data:{address:e},async:!1});case 4:n=a.sent,0==n.code&&(i=n.data),a.next=10;break;case 8:a.prev=8,a.t0=a["catch"](1);case 10:return a.abrupt("return",i);case 11:case"end":return a.stop()}}),a,null,[[1,8]])})))()},saveAddress:function(){var e=this;if(!this.flag&&(this.flag=!0,this.vertify())){uni.showLoading({title:"保存中",mask:!0});var t=this.addressValue.split(" "),a={},i="";a={name:this.formData.name,mobile:this.formData.mobile,telephone:this.formData.telephone,province_id:t[0],city_id:t[1],district_id:t[2],community_id:0,address:this.formData.address,full_address:this.formData.full_address,latitude:this.formData.latitude,longitude:this.formData.longitude,is_default:this.formData.is_default},i="add",this.formData.id&&(i="edit",a.id=this.formData.id),this.$api.sendRequest({url:"/api/memberaddress/"+i,data:a,success:function(t){e.flag=!1,0==t.code?(uni.hideLoading(),e.$util.showToast({title:t.message}),uni.navigateBack({delta:1})):(uni.hideLoading(),e.$util.showToast({title:t.message}))},fail:function(t){e.flag=!1,uni.hideLoading()}})}},showTextareaPopup:function(){var e=this;this.identification_address?this.textareaSave():uni.getClipboardData({success:function(t){e.identification_address=t.data,e.textareaSave()},fail:function(t){e.$util.showToast({title:"获取剪贴板内容失败，请自行粘贴！"})}})},textareaClear:function(){this.identification_address="",this.$refs.textareaPopup.close()},textareaSave:function(){var e=this;return(0,n.default)((0,r.default)().mark((function t(){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.addressParse(e.identification_address);case 2:a=t.sent,a&&a.area_code&&(e.defaultRegions=[a.province_code,a.city_code,a.area_code],e.formData.address=a.address,e.formData.latitude=0,e.formData.longitude=0,e.$refs.pickRegionsRef.getDefaultAreas(0,{level:0})),a&&a.name&&(e.formData.name=a.name),a&&a.mobile&&(e.formData.mobile=a.mobile),e.$refs.textareaPopup.close();case 7:case"end":return t.stop()}}),t)})))()},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),a=t.title,i=t.link,r=t.imageUrl;t.query;return this.$buriedPoint.pageShare(i,r,a)}};t.default=c},"3b0e":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-2dac6db0]{width:100%;text-align:center}[data-v-2dac6db0] uni-switch .uni-switch-input.uni-switch-input-checked{border-color:var(--custom-brand-color);background-color:var(--custom-brand-color)}[data-v-2dac6db0] .pick-regions{height:100%;display:flex;align-items:center}[data-v-2dac6db0] .uni-switch-input{width:%?88?%;height:%?44?%}[data-v-2dac6db0] .uni-switch-input:before,[data-v-2dac6db0] .uni-switch-input:after{width:%?40?%;height:%?40?%;box-sizing:border-box}',""]),e.exports=t},4086:function(e,t,a){var i=a("68f7");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("7418f950",i,!0,{sourceMap:!1,shadowMode:!1})},"4d95":function(e,t,a){"use strict";var i=a("9423"),r=a.n(i);r.a},"54c5":function(e,t,a){"use strict";a.r(t);var i=a("8b44"),r=a("2a5c");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("c522"),a("4d95");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"2dac6db0",null,!1,i["a"],void 0);t["default"]=o.exports},"55fb":function(e,t,a){"use strict";a.r(t);var i=a("16f6"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"68f7":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";.uni-popup[data-v-499e7720]{position:fixed;top:0;top:0;bottom:0;left:0;right:0;z-index:999;overflow:hidden}.uni-popup__mask[data-v-499e7720]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:998;background:rgba(0,0,0,.4);opacity:0}.uni-popup__mask.ani[data-v-499e7720]{transition:all .3s}.uni-popup__mask.uni-bottom[data-v-499e7720],\r\n.uni-popup__mask.uni-center[data-v-499e7720],\r\n.uni-popup__mask.uni-right[data-v-499e7720],\r\n.uni-popup__mask.uni-left[data-v-499e7720],\r\n.uni-popup__mask.uni-top[data-v-499e7720]{opacity:1}.uni-popup__wrapper[data-v-499e7720]{position:absolute;z-index:999;box-sizing:border-box\r\n/* \tbackground: #ffffff; */}.uni-popup__wrapper.ani[data-v-499e7720]{transition:all .3s}.uni-popup__wrapper.top[data-v-499e7720]{top:0;left:0;width:100%;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.uni-popup__wrapper.bottom[data-v-499e7720]{background:#fff;\r\n\t/* bottom: -30px; */bottom:0;left:0;width:100%;-webkit-transform:translateY(100%);transform:translateY(100%)}.uni-popup__wrapper.right[data-v-499e7720]{bottom:0;left:0;width:100%;-webkit-transform:translateX(100%);transform:translateX(100%)}.uni-popup__wrapper.left[data-v-499e7720]{bottom:0;left:0;width:100%;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.uni-popup__wrapper.center[data-v-499e7720]{width:100%;height:100%;display:flex;justify-content:center;overflow:hidden;align-items:center;-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}.uni-popup__wrapper-box[data-v-499e7720]{position:relative;box-sizing:border-box}.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box[data-v-499e7720]{background:#fff}.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box[data-v-499e7720]{background:transparent;position:relative;max-width:80%;max-height:80%;overflow-y:scroll;border-radius:%?20?%}.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box[data-v-499e7720],\r\n.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box[data-v-499e7720]{width:100%;\r\n\t/* max-height: 500px; */overflow-y:scroll}.uni-popup__wrapper.uni-bottom[data-v-499e7720],\r\n.uni-popup__wrapper.uni-top[data-v-499e7720]{-webkit-transform:translateY(0);transform:translateY(0);border-radius:%?20?% %?20?% 0 0;overflow:hidden}.uni-popup__wrapper.uni-left[data-v-499e7720],\r\n.uni-popup__wrapper.uni-right[data-v-499e7720]{-webkit-transform:translateX(0);transform:translateX(0)}.uni-popup__wrapper.uni-center[data-v-499e7720]{-webkit-transform:scale(1);transform:scale(1);opacity:1}\r\n\r\n/* isIphoneX系列手机底部安全距离 */.bottom[data-v-499e7720]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}',""]),e.exports=t},8469:function(e,t,a){a("23f4"),a("7d2f"),a("5c47"),a("9c4e"),a("ab80"),a("0506"),a("64aa"),a("5ef2"),e.exports={error:"",check:function(e,t){for(var a=0;a<t.length;a++){if(!t[a].checkType)return!0;if(!t[a].name)return!0;if(!t[a].errorMsg)return!0;if(!e[t[a].name])return this.error=t[a].errorMsg,!1;switch(t[a].checkType){case"custom":if("function"==typeof t[a].validate&&!t[a].validate(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"required":var i=new RegExp("/[S]+/");if(i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"string":i=new RegExp("^.{"+t[a].checkRule+"}$");if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"int":i=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[a].checkRule+"}$");if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[a].name]))return this.error=t[a].errorMsg,!1;var r=t[a].checkRule.split(",");if(r[0]=Number(r[0]),r[1]=Number(r[1]),e[t[a].name]>r[1]||e[t[a].name]<r[0])return this.error=t[a].errorMsg,!1;break;case"betweenD":i=/^-?[1-9][0-9]?$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;r=t[a].checkRule.split(",");if(r[0]=Number(r[0]),r[1]=Number(r[1]),e[t[a].name]>r[1]||e[t[a].name]<r[0])return this.error=t[a].errorMsg,!1;break;case"betweenF":i=/^-?[0-9][0-9]?.+[0-9]+$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;r=t[a].checkRule.split(",");if(r[0]=Number(r[0]),r[1]=Number(r[1]),e[t[a].name]>r[1]||e[t[a].name]<r[0])return this.error=t[a].errorMsg,!1;break;case"same":if(e[t[a].name]!=t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"notsame":if(e[t[a].name]==t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"email":i=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"phoneno":i=/^[1](([3][0-9])|([4][1,4-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"zipcode":i=/^[0-9]{6}$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"reg":i=new RegExp(t[a].checkRule);if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"in":if(-1==t[a].checkRule.indexOf(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"notnull":if(0==e[t[a].name]||void 0==e[t[a].name]||null==e[t[a].name]||e[t[a].name].length<1)return this.error=t[a].errorMsg,!1;break;case"lengthMin":if(e[t[a].name].length<t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"lengthMax":if(e[t[a].name].length>t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"bank_account":i=/^([1-9]{1})(\d{15}|\d{18})$/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"idCard":i=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!i.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"8b44":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var i={pickRegions:a("d89a").default,uniIcons:a("de74").default,uniPopup:a("5e99").default,loadingCover:a("5510").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{class:e.themeStyle,style:[e.themeColorVar]},[a("v-uni-view",{staticClass:"address-edit-content"},[e.loading?e._e():a("v-uni-view",{staticClass:"edit-wrap"},[a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[e._v(e._s(e.$lang("consignee")))]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-style":"color:#CCCCCC",placeholder:e.$lang("consigneePlaceholder"),maxlength:"20",name:"name",value:""},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[e._v(e._s(e.$lang("mobile")))]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number","placeholder-style":"color:#CCCCCC",placeholder:e.$lang("mobilePlaceholder"),maxlength:"11",value:""},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[e._v(e._s(e.$lang("receivingCity")))]),a("v-uni-view",{staticClass:"edit-item-region"},[a("pick-regions",{ref:"pickRegionsRef",staticClass:"picker",attrs:{"default-regions":e.defaultRegions},on:{getRegions:function(t){arguments[0]=t=e.$handleEvent(t),e.handleGetRegions.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"select-address",class:{empty:!e.formData.full_address}},[e._v(e._s(e.formData.full_address?e.formData.full_address:"请选择所在地区"))])],1),a("v-uni-view",{staticClass:"edit-item-region-right"},[a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[e._v(e._s(e.$lang("address")))]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-style":"color:#CCCCCC",placeholder:e.$lang("addressPlaceholder"),maxlength:"50"},model:{value:e.formData.address,callback:function(t){e.$set(e.formData,"address",t)},expression:"formData.address"}}),a("v-uni-view",{staticClass:"edit-item-region-right",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icondizhi"})],1)],1),a("v-uni-view",{staticClass:"edit-item arddress-defalut"},[a("v-uni-text",{staticClass:"tit"},[e._v("设置默认")]),a("v-uni-switch",{attrs:{name:"isDefault",checked:1==e.formData.is_default,color:"var(--custom-brand-color)"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.changeDefault.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"edit-wrap"},[a("v-uni-view",{staticClass:"edit-item-textarea edit-item"},[a("v-uni-textarea",{staticClass:"uni-textarea",attrs:{cols:"30",rows:"3",placeholder:"粘贴整段文本如：广州市天河区体育西路维多利大厦，张三，13988998899","placeholder-class":"uni-textarea-placeholder"},model:{value:e.identification_address,callback:function(t){e.identification_address=t},expression:"identification_address"}}),a("v-uni-view",{staticClass:"edit-item-textarea-op"},[a("v-uni-text",{staticClass:"edit-item-textarea-op-one",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showTextareaPopup.apply(void 0,arguments)}}},[e._v(e._s(e.identification_address?"识别":"粘贴并识别"))])],1),e.identification_address?a("uni-icons",{staticClass:"edit-item-textarea-clear",attrs:{type:"clear",color:"rgba(229, 229, 229, 1)",size:"20"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.textareaClear.apply(void 0,arguments)}}}):e._e()],1)],1),e.formData.full_address?a("v-uni-view",{staticClass:"button-wrap full-address"},[a("v-uni-button",{staticClass:"button delete",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.deleteAddress(e.formData.id)}}},[e._v(e._s(e.$lang("del")))]),a("v-uni-button",{staticClass:"button add",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveAddress.apply(void 0,arguments)}}},[e._v(e._s(e.$lang("save")))])],1):a("v-uni-view",{staticClass:"button-wrap"},[a("v-uni-button",{staticClass:"button add",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveAddress.apply(void 0,arguments)}}},[e._v(e._s(e.$lang("save")))])],1)],1),a("uni-popup",{ref:"textareaPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"textarea-popup"},[a("v-uni-textarea",{attrs:{maxlength:"500","placeholder-class":"identification-address-placeholder",placeholder:"粘贴整段文本如：广州市天河区体育西路维多利大厦，张三，13988998899"},model:{value:e.identification_address,callback:function(t){e.identification_address=t},expression:"identification_address"}}),a("v-uni-view",{staticClass:"textarea-popup-op"},[a("v-uni-button",{staticClass:"button clear",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.textareaClear.apply(void 0,arguments)}}},[e._v("清除")]),a("v-uni-button",{staticClass:"button save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.textareaSave.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)],1),a("loading-cover",{ref:"loadingCover"})],1)},n=[]},"8cbc":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"UniPopup",props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},custom:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{ani:"",showPopup:!1,callback:null,isIphoneX:!1}},watch:{show:function(e){e?this.open():this.close()}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{clear:function(){},open:function(e){var t=this;e&&(this.callback=e),this.$emit("change",{show:!0}),this.showPopup=!0,this.$nextTick((function(){setTimeout((function(){t.ani="uni-"+t.type}),30)}))},close:function(e,t){var a=this;!this.maskClick&&e||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick((function(){setTimeout((function(){a.showPopup=!1}),300)})),t&&t(),this.callback&&this.callback.call(this))}}};t.default=i},"8f68":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},9127:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},9423:function(e,t,a){var i=a("e115");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("f0785bf8",i,!0,{sourceMap:!1,shadowMode:!1})},a971:function(e,t,a){var i=a("8f68");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("2d38abe0",i,!0,{sourceMap:!1,shadowMode:!1})},b8ea:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var r=i(a("9127")),n={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:r.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=n},c3f5:function(e,t,a){"use strict";a.r(t);var i=a("de63"),r=a("d4e0");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("1858");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"499e7720",null,!1,i["a"],void 0);t["default"]=o.exports},c522:function(e,t,a){"use strict";var i=a("e621"),r=a.n(i);r.a},d2ca:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"pick-regions"},[a("v-uni-picker",{attrs:{mode:"multiSelector",value:e.multiIndex,range:e.multiArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleValueChange.apply(void 0,arguments)},columnchange:function(t){arguments[0]=t=e.$handleEvent(t),e.handleColumnChange.apply(void 0,arguments)}}},[e._t("default")],2)],1)},r=[]},d4e0:function(e,t,a){"use strict";a.r(t);var i=a("8cbc"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},d89a:function(e,t,a){"use strict";a.r(t);var i=a("d2ca"),r=a("55fb");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=o.exports},de63:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.showPopup?a("v-uni-view",{staticClass:"uni-popup"},[a("v-uni-view",{staticClass:"uni-popup__mask",class:[e.ani,e.animation?"ani":"",e.custom?"":"uni-custom"],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close(!0)}}}),a("v-uni-view",{staticClass:"uni-popup__wrapper",class:[e.type,e.ani,e.animation?"ani":"",e.custom?"":"uni-custom"],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close(!0)}}},[a("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.clear.apply(void 0,arguments)}}},[e._t("default")],2)],1)],1):e._e()},r=[]},de74:function(e,t,a){"use strict";a.r(t);var i=a("37ff"),r=a("fefc");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("327f");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"65e5b6d2",null,!1,i["a"],void 0);t["default"]=o.exports},e115:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-2dac6db0]{width:100%;text-align:center}.edit-wrap[data-v-2dac6db0]{background:#fff;overflow:hidden;width:%?710?%;margin:0 auto;margin-top:%?20?%;padding:0 %?20?%;box-sizing:border-box;border-radius:%?20?%}.edit-item[data-v-2dac6db0]{display:flex;align-items:center;height:%?116?%;background-color:#fff;box-sizing:border-box}.edit-item.arddress-defalut[data-v-2dac6db0]{justify-content:space-between}.edit-item.arddress-defalut uni-switch[data-v-2dac6db0]{margin-right:%?30?%}.edit-item .tit[data-v-2dac6db0]{width:%?120?%;box-sizing:border-box;font-size:%?28?%}.edit-item .picker[data-v-2dac6db0]{flex:1;display:flex;align-items:center;height:100%}.edit-item .select-address[data-v-2dac6db0]{display:inline-block;font-size:%?28?%;width:%?500?%}.edit-item .select-address.empty[data-v-2dac6db0]{color:#ccc}.edit-item .uni-input[data-v-2dac6db0]{font-size:%?28?%;padding-left:%?20?%;box-sizing:border-box}.edit-item uni-input[data-v-2dac6db0]{flex:1;font-size:%?30?%;padding:0;margin-right:%?20?%}.edit-wrap > .edit-item + .edit-item[data-v-2dac6db0]{border-top:%?2?% solid #f5f5f5}.edit-item-textarea[data-v-2dac6db0]{display:block!important;height:auto!important;padding:%?16?% 0;padding-right:%?32?%;box-sizing:border-box;position:relative}.edit-item-textarea .tit[data-v-2dac6db0]{width:%?200?%;box-sizing:border-box;font-size:%?28?%;padding-left:%?32?%;display:block}.edit-item-textarea .uni-textarea[data-v-2dac6db0]{font-size:%?30?%;font-weight:400;line-height:%?52?%;color:#000;height:%?200?%;width:%?620?%;padding-left:%?16?%;box-sizing:border-box}.edit-item-textarea .uni-textarea-placeholder[data-v-2dac6db0]{font-size:%?30?%;font-weight:400;line-height:%?52?%}.edit-item-textarea-clear[data-v-2dac6db0]{position:absolute;top:0;right:0;z-index:1}.edit-item-textarea-op[data-v-2dac6db0]{display:flex;justify-content:flex-end;align-items:center}.edit-item-textarea-op-one[data-v-2dac6db0]{padding:%?16?% %?32?%;box-sizing:border-box;height:%?64?%;border-radius:%?40?%;background:var(--custom-brand-color);font-size:%?28?%;font-weight:400;line-height:32px;color:#fff;display:flex;align-items:center;z-index:1}[data-v-2dac6db0] .identification-address-placeholder{color:#ccc;font-size:%?26?%}.edit-item-region[data-v-2dac6db0]{display:flex;justify-content:space-between;align-items:center;padding-left:%?20?%;box-sizing:border-box}.edit-item-region-right[data-v-2dac6db0]{display:flex;justify-content:center;align-items:center}.edit-item-region-right .iconright[data-v-2dac6db0]{color:#a6a6a6}.edit-item-region-right .icondizhi[data-v-2dac6db0]{color:var(--custom-brand-color)}.button-wrap[data-v-2dac6db0]{position:fixed;bottom:0;left:0;width:100%;height:%?100?%;display:flex;align-items:center;justify-content:center;border-top:%?1?% solid #eee;padding-bottom:env(safe-area-inset-bottom);background-color:#fff}.button-wrap.full-address[data-v-2dac6db0]{justify-content:space-around}.button-wrap.full-address .button[data-v-2dac6db0]{width:%?316?%}.button-wrap .button[data-v-2dac6db0]{font-size:%?28?%;width:%?654?%;height:%?80?%;line-height:%?80?%;border-radius:%?40?%;margin:0;padding:0}.button-wrap .delete[data-v-2dac6db0]{color:var(--custom-brand-color);background:var(--custom-brand-color-10)!important}.button-wrap .add[data-v-2dac6db0]{color:#fff;background:var(--custom-brand-color)!important}.textarea-popup[data-v-2dac6db0]{background-color:#fff;padding:%?24?%;box-sizing:border-box}.textarea-popup uni-textarea[data-v-2dac6db0]{width:100%;height:%?300?%;font-size:%?28?%;line-height:1.5;color:#333;border:%?1?% solid #eee;border-radius:%?10?%;padding:%?20?%;box-sizing:border-box}.textarea-popup-op[data-v-2dac6db0]{display:flex;justify-content:center;align-items:center;margin-top:%?20?%}.textarea-popup-op uni-button[data-v-2dac6db0]{width:%?200?%;height:%?70?%;font-size:%?28?%;color:#fff;border-radius:%?50?%}.textarea-popup-op .clear[data-v-2dac6db0]{color:#1d1d1d}',""]),e.exports=t},e621:function(e,t,a){var i=a("3b0e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("0d8f5dfa",i,!0,{sourceMap:!1,shadowMode:!1})},fefc:function(e,t,a){"use strict";a.r(t);var i=a("b8ea"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a}}]);