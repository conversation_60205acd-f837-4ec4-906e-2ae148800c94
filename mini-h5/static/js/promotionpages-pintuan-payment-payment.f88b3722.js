(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["promotionpages-pintuan-payment-payment"],{"242e":function(e,t,a){"use strict";var o=a("f1a0"),i=a.n(o);i.a},2514:function(e,t,a){"use strict";a.r(t);var o=a("acfe"),i=a("e49e");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("9c74"),a("ee77"),a("242e");var r=a("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"080e78d5",null,!1,o["a"],void 0);t["default"]=s.exports},"345d":function(e,t,a){var o=a("57cc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("48ff869c",o,!0,{sourceMap:!1,shadowMode:!1})},"35ba":function(e,t,a){var o=a("d2fd");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("5a2d3786",o,!0,{sourceMap:!1,shadowMode:!1})},4210:function(e,t,a){"use strict";var o=a("e133"),i=a.n(o);i.a},"4c32":function(e,t,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(a("2634")),n=o(a("2fdc"));a("22b6"),a("bf0f"),a("2797"),a("5ef2"),a("5c47"),a("dfcf"),a("aa9c"),a("d4b5"),a("fd3c"),a("c9b5"),a("ab80"),a("dc8a"),a("0506"),a("e838");o(a("cbf3")),o(a("cd3a"));var r=o(a("85bf")),s=o(a("7c8d")),d={data:function(){return{orderCreateData:{is_balance:0,pay_password:"",platform_coupon_id:0,buyer_message:{}},errMsg:"",paymentMethod:"WECHAT",orderPaymentData:{member_account:{balance:0,is_pay_password:0},platform_coupon_list:[]},isSub:!1,sitePromotion:[],siteDelivery:{site_id:0,data:[]},siteCoupon:{site_id:0,data:[]},shopCoupon:{},myCoupon:[],isFocus:!1,tempData:null,selectCouponId:"",selectCouponMoney:"0.00",selectCouponHaveChoose:!1,selectPlatCouponId:0,selectPlatCouponMoney:"0.00",limit_pay_tips:"",push_data:{},isSubscribed:!1}},methods:{openPopup:function(e){"PlatcouponPopup"==e&&(this.selectPlatCouponId=this.orderPaymentData.platform_coupon_id,this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_money),this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){this.$util.redirectTo("/otherpages/member/address/address",{back:"/promotionpages/pintuan/payment/payment"})},getOrderPaymentData:function(){var e=this;this.orderCreateData=uni.getStorageSync("pintuanOrderCreateData"),this.orderCreateData?(this.selectCouponId&&(this.orderCreateData.coupon_id=this.selectCouponId),this.$api.sendRequest({url:"/api/PintuanOrder/payment",data:this.orderCreateData,success:function(t){if(t.code>=0){e.orderPaymentData=t.data,e.limit_pay_tips=t.data.limit_pay_tips,uni.setStorageSync("limit_pay_tips",t.data.limit_pay_tips);var a=uni.getStorageSync("member_address");a?a.id==e.orderPaymentData.member_address.id?uni.setStorageSync("member_address",e.orderPaymentData.member_address):e.orderPaymentData.member_address=a:uni.setStorageSync("member_address",e.orderPaymentData.member_address),t.data.coupon_list&&t.data.coupon_list.coupon_id&&!e.selectCouponHaveChoose?(e.orderCreateData.coupon_id=t.data.coupon_list.coupon_id,e.selectCouponId=t.data.coupon_list.coupon_id):(e.orderCreateData.coupon_id=e.selectCouponId,e.selectCouponHaveChoose=!0),e.handlePaymentData(),e.getOrderCouponList();var o=Object.values(t.data.shop_goods_list);o.length>0&&o.forEach((function(t){var a=t.goods_list_str;e.splitfn(a,-1!==a.indexOf(";")?";":":")})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){console.log("if (res.code !>= 0)"),console.log(t),e.$util.redirectTo("/pages/index/index/index",{},"reLaunch")}),1500)}})},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})):uni.showModal({title:"提示",content:"订单信息已过期，请到订单列表查看最新状态",confirmText:"查看订单",showCancel:!1,success:function(t){e.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=all",{},"redirectTo")},fail:function(e){}})},splitfn:function(e,t){var a=[],o=[];";"==t?e.split(t).forEach((function(e){e.split(":").forEach((function(e,t){t%2?o.push(e):a.push(e)}))})):(a=[e.split(":")[0]],o=[e.split(":")[1]]),a.length>0&&o.length>0&&this.$buriedPoint.submitOrderContent({sku_ids:a,num:o,pages:1})},getOrderCouponList:function(){var e=this,t=[],a=[],o=[],i=JSON.parse(JSON.stringify(this.orderPaymentData));for(var n in i.shop_goods_list)o.push(i.shop_goods_list[n]);for(var r=0;r<o.length;r++)o[r].goods_list.map((function(e){t.push(e.sku_id),a.push(e.num)}));this.$api.sendRequest({url:this.$apiUrl.orderCoupon,data:{sku_ids:t.toString(),sku_nums:a.toString()},success:function(t){0==t.code&&(console.log(t.data),e.shopCoupon=t.data,1==e.currentTab?e.myCoupon=e.shopCoupon.enable_list:0==e.currentTab&&(e.myCoupon=e.shopCoupon.disable_list))},fali:function(e){}})},ontabtapCoupon:function(e){this.currentTab=e.status,1==this.currentTab?this.myCoupon=this.shopCoupon.enable_list:0==this.currentTab&&(this.myCoupon=this.shopCoupon.disable_list)},handlePaymentData:function(){var e=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.buyer_message={},this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="",console.log("orderPaymentData",this.orderPaymentData);var t=this.orderPaymentData,a=(new Date).getHours().toString(),o=(new Date).getMinutes().toString();1==a.length&&(a="0"+a),1==o.length&&(o="0"+o);var i=a+":"+o,n=JSON.parse(JSON.stringify(t.shop_goods_list));t.shop_goods_list={},t.shop_goods_list.val=n,Object.keys(t.shop_goods_list).forEach((function(a,o){console.log(a,t.shop_goods_list[a]);var n=t.shop_goods_list[a];e.orderCreateData.delivery[a]={},n.local_config&&(n.local_config.info&&1==n.local_config.info.time_is_open?(e.orderCreateData.delivery[a].showTimeBar=!0,e.orderCreateData.delivery[a].buyer_ask_delivery_time=i):e.orderCreateData.delivery[a].showTimeBar=!1),console.log("data",n.express_type),n.express_type&&void 0!=n.express_type[0]&&(e.orderCreateData.delivery[a].delivery_type=n.express_type[0].name,e.orderCreateData.delivery[a].delivery_type_name=n.express_type[0].title,e.orderCreateData.delivery[a].store_id=0,e.orderCreateData.delivery[a].store_id=0,"store"==n.express_type[0].name&&void 0!=n.express_type[0].store_list[0]&&(e.orderCreateData.delivery[a].store_id=n.express_type[0].store_list[0].store_id)),e.orderCreateData.coupon[a]={},n.coupon_list&&void 0!=n.coupon_list[0]&&(e.orderCreateData.coupon[a].coupon_id=n.coupon_list[0].coupon_id,e.selectCouponId=n.coupon_list[0].coupon_id,e.orderCreateData.coupon[a].coupon_money=n.coupon_list[0].money,e.selectCouponMoney=n.coupon_list[0].coupon_id),e.orderCreateData.buyer_message[a]=""})),this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={mobile:""}),this.orderPaymentData.platform_coupon_list&&this.orderPaymentData.platform_coupon_list.length>0&&(this.orderPaymentData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.orderCreateData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.orderPaymentData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money,this.orderCreateData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money,this.selectPlatCouponId=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id,this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_list[0].money),Object.assign(this.orderPaymentData,this.orderCreateData),this.orderCalculate()},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),t.member_address=t.member_address?JSON.stringify(t.member_address):JSON.stringify([]),t.buyer_message=JSON.stringify(t.buyer_message),this.$api.sendRequest({url:"/api/PintuanOrder/calculate",data:t,success:function(t){if(t.code>=0){e.orderPaymentData.delivery_money=t.data.delivery_money,e.orderPaymentData.coupon_money=t.data.coupon_money,e.orderPaymentData.invoice_money=t.data.invoice_money,e.orderPaymentData.promotion_money=t.data.promotion_money,e.orderPaymentData.order_money=t.data.order_money,e.orderPaymentData.balance_money=t.data.balance_money,e.orderPaymentData.pay_money=t.data.pay_money,e.orderPaymentData.goods_money=t.data.goods_money,e.member_account=t.data.member_account,e.maidou={maidou_tag:t.data.maidou_tag,maidou_pay_num:t.data.maidou_pay_num,maidou_pay:t.data.maidou_pay,canuse_maidou:t.data.member_account.canuse_maidou};var a=e.$util.deepClone(t.data.shop_goods_list);t.data.shop_goods_list={},t.data.shop_goods_list.val=a,Object.keys(t.data.shop_goods_list).forEach((function(a,o){var i=t.data.shop_goods_list[a];e.orderPaymentData.shop_goods_list[a].pay_money=i.pay_money,e.orderPaymentData.shop_goods_list[a].coupon_money=i.coupon_money}))}else e.$util.showToast({title:t.message})}})},orderCreate:function(){var e=this;return(0,n.default)((0,i.default)().mark((function t(){var a,o,r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.verify()){t.next=26;break}if(!e.isSub){t.next=3;break}return t.abrupt("return");case 3:if(e.isSub=!0,e.isSubscribed){t.next=16;break}return a="",a=e.orderCreateData.group_id?"WECHAT"==e.paymentMethod?"join_pintuan_before":"other_join_pintuan_before":"WECHAT"==e.paymentMethod?"open_pintuan_before":"other_pintuan_before",t.prev=7,t.next=10,e.$util.subscribeMessage({source:"out_trade_no",source_id:"",scene_type:a},!0);case 10:e.push_data=t.sent,e.isSubscribed=!0,t.next=16;break;case 14:t.prev=14,t.t0=t["catch"](7);case 16:uni.showLoading({mask:!0,title:"加载中"}),o=uni.getStorageSync("member_address"),r=e.$util.deepClone(e.orderCreateData),r.delivery=JSON.stringify(r.delivery),r.coupon=JSON.stringify(r.coupon),r.member_address=JSON.stringify(o.id),r.buyer_message=JSON.stringify(r.buyer_message),r.pay_type=e.paymentMethod,e.activity_id&&(r.activity_id=e.activity_id,r.activity_type=e.activity_type),e.$api.sendRequest({url:"/api/PintuanOrder/create",data:r,success:function(){var t=(0,n.default)((0,i.default)().mark((function t(a){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(-10100!=a.code){t.next=6;break}uni.hideLoading(),e.$refs.payPassword.close(),uni.showModal({content:"开团名额已满，请参团",showCancel:!1,confirmText:"去参团",success:function(){e.$util.redirectTo("/promotionpages/pintuan/detail/detail?id=".concat(e.orderCreateData.pintuan_goods_id),{},"redirectTo")},fail:function(){}}),t.next=21;break;case 6:if(!(a.code>=0)){t.next=17;break}return uni.removeStorage({key:"pintuanOrderCreateData",success:function(){}}),e.createBuriedPoint(a.data.out_trade_no,3),e.push_data.source_id=a.data.out_trade_no,t.next=12,e.$util.subscribeMessageMethod(e.push_data);case 12:1==a.data.is_free?(e.createBuriedPoint(a.data.out_trade_no,11),e.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=all",{},"redirectTo")):e.orderPayPay(a.data.out_trade_no,a.data.order_id),uni.removeStorageSync("member_address"),uni.removeStorageSync("recommend_member_id"),t.next=21;break;case 17:e.isSub=!1,uni.hideLoading(),e.$refs.payPassword&&(e.isFocus=!1,e.$refs.payPassword.close()),10==a.data.error_code||12==a.data.error_code?uni.showModal({title:"订单未创建",content:a.message,confirmText:"去设置",success:function(t){t.confirm&&e.selectAddress()}}):e.$util.showToast({title:a.message});case 21:e.getCartNumber();case 22:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),fail:function(t){uni.hideLoading(),e.isSub=!1}});case 26:case"end":return t.stop()}}),t,null,[[7,14]])})))()},orderPayPay:function(e,t){var a=this;uni.showLoading({mask:!0,title:"加载中"});this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:e,pay_type:"adapay"},success:function(t){uni.hideLoading(),t.code>=0?a.$util.wechatPay(t.data.pay_type,"adapay"==t.data.pay_type?t.data.payment:t.data.pay_info,(function(t){a.createBuriedPoint(e,11),uni.hideLoading(),a.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=all",{},"redirectTo")}),(function(t){a.createBuriedPoint(e,9001),uni.hideLoading(),a.$refs.popupToList.open()}),(function(e){setTimeout((function(){a.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=0",{},"redirectTo")}),2e3)})):(console.log(t),t.message?a.$util.showToast({title:t.message}):uni.hideLoading(),a.$refs.popupToList&&a.$refs.popupToList.open())},fail:function(e){a.$util.showToast({title:"request:fail"})}})},createBuriedPoint:function(e,t){this.$buriedPoint.orderStatus({out_trade_no:e,status:t,orderType:!0})},verify:function(){var e=this;if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!/^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/.test(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if(!this.orderPaymentData.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;var t=!0;for(var a in this.orderCreateData.delivery){if("{}"==JSON.stringify(this.orderCreateData.delivery[a])){t=!1,this.$util.showToast({title:'店铺"'+this.orderPaymentData.shop_goods_list[a].site_name+'"未设置配送方式'});break}if("store"==this.orderCreateData.delivery[a].delivery_type&&0==this.orderCreateData.delivery[a].store_id){t=!1,this.$util.showToast({title:'店铺"'+this.orderPaymentData.shop_goods_list[a].site_name+'"没有可提货的门店,请选择其他配送方式'});break}}if(!t)return!1}return"BALANCE"==this.paymentMethod&&(this.orderCreateData.is_balance=1),1!=this.orderCreateData.is_balance||""!=this.orderCreateData.pay_password||(setTimeout((function(){e.$refs.input.clear()}),0),this.openPasswordPopup(),!1)},openSitePromotion:function(e){this.sitePromotion=e,this.$refs.sitePromotionPopup.open()},openSiteDelivery:function(e,t){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.siteDelivery.site_id=e,this.siteDelivery.data=t,this.$refs.deliveryPopup.open()},selectDeliveryType:function(e){this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type=e.name,this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type_name=e.title,"store"==e.name&&void 0!=e.store_list[0]&&(this.orderCreateData.delivery[this.siteDelivery.site_id].store_id=e.store_list[0].store_id),Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},selectPickupPoint:function(e){this.orderCreateData.delivery[this.siteDelivery.site_id].store_id=e,Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},openSiteCoupon:function(){this.$refs.couponPopup.open()},selectCoupon:function(e){this.selectCouponId!=e.coupon_id?this.selectCouponId=e.coupon_id:this.selectCouponId="",Object.assign(this.orderPaymentData,this.orderCreateData),this.$forceUpdate()},popupConfirm:function(e,t){this.$refs[e].close(),this.selectCouponHaveChoose=!0,this.getOrderPaymentData()},imageError:function(e,t){this.orderPaymentData.shop_goods_list[e].goods_list[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},getCartNumber:function(){uni.getStorageSync("token")&&this.$store.dispatch("getCartNumber")},openPasswordPopup:function(){var e=this;this.$refs.payPassword.open(),setTimeout((function(){e.isFocus=!0}),500)},setPayPassword:function(){this.$refs.payPassword.close(),this.$util.redirectTo("/otherpages/member/setting/setting_password",{back:"/promotionpages/pintuan/payment/payment",phonenum:this.orderPaymentData.member_account.mobile})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(e){var t=this;this.errMsg="",6==e.length&&(uni.showLoading({title:"加载中...",mask:!0}),this.$api.sendRequest({url:s.default.checkpaypasswordUrl,data:{pay_password:e},success:function(a){a.code>=0?(t.orderCreateData.pay_password=e,t.orderCreate()):(uni.hideLoading(),t.errMsg=a.message)},fail:function(e){uni.hideLoading()}}))},init:function(){this.$langConfig.refresh();var e=uni.getStorageSync("member_address");uni.getStorageSync("token")&&(this.orderPaymentData={member_account:{balance:0}},this.orderCreateData={is_balance:0,pay_password:""},e&&(this.orderCreateData.member_address=e.id),this.isSub||this.getOrderPaymentData())},changePayment:function(e){"BALANCE"==e.key?this.orderCreateData.is_balance=e.disable||1==this.orderCreateData.is_balance?0:1:this.orderCreateData.is_balance=0,e.disable||(this.paymentMethod=e.key),this.$forceUpdate()}},onLoad:function(e){uni.removeStorageSync("member_address")},onShow:function(){var e=this;return(0,n.default)((0,i.default)().mark((function t(){var a;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,r.default.wait_staticLogin_success();case 2:e.ischoiceWechatAdder?a=setInterval((function(){e.postWechatAdder&&(console.log("this.choiceWechatAdderError",e.choiceWechatAdderError),e.choiceWechatAdderError&&e.choiceWechatAdderError&&(e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.$util.showToast({title:"获取微信地址失败，请手动添加地址",success:function(){setTimeout((function(){e.selectAddress()}),1500)}})),e.ischoiceWechatAdder=!1,e.postWechatAdder=!1,e.choiceWechatAdderError=!1,clearInterval(a),e.init())}),100):e.init();case 3:case"end":return t.stop()}}),t)})))()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)},promotion:function(e){var t="";return e&&Object.keys(e).forEach((function(a){t+=e[a].content+"　"})),t}}};t.default=d},"57cc":function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-080e78d5]{width:100%;text-align:center}[data-v-080e78d5] .my-popup-dialog .uni-popup__wrapper-box{max-width:%?540?%;width:%?540?%;border-radius:%?20?%}[data-v-080e78d5] .coupon-instructions .uni-popup__wrapper-box{max-width:%?620?%;width:%?620?%;border-radius:%?20?%}.popup-dialog[data-v-080e78d5]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-080e78d5]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog .popup-dialog-body[data-v-080e78d5]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog .popup-dialog-footer[data-v-080e78d5]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog .popup-dialog-footer .button[data-v-080e78d5]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog .popup-dialog-footer .button.white[data-v-080e78d5]{color:#f2270c;background:#fff;border:%?1?% solid #f2270c}.popup-dialog .popup-dialog-footer .button.red[data-v-080e78d5]{color:#fff;background:#f2270c}.coupon-instructions-popup-body[data-v-080e78d5]{width:%?560?%;height:%?540?%;margin:0 auto}.coupon-instructions .popup-dialog .popup-dialog-body[data-v-080e78d5]{padding:0}.coupon-instructions .popup-dialog .popup-dialog-footer .button[data-v-080e78d5]{width:%?480?%}.cha_close[data-v-080e78d5]{width:%?30?%;height:%?30?%}.pay_money[data-v-080e78d5]{font-size:%?36?%!important}',""]),e.exports=t},"61c4":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"code-box"},[a("v-uni-view",{staticClass:"flex-box"},[a("v-uni-input",{staticClass:"hide-input",attrs:{value:e.inputValue,type:"number",focus:e.autoFocus,maxlength:e.maxlength},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.getVal.apply(void 0,arguments)}}}),e._l(e.ranges,(function(t,o){return[a("v-uni-view",{key:o+"_0",class:["item",{active:e.codeIndex===t,middle:"middle"===e.type,bottom:"bottom"===e.type,box:"box"===e.type}]},["middle"!==e.type?a("v-uni-view",{staticClass:"line"}):e._e(),"middle"===e.type&&e.codeIndex<=t?a("v-uni-view",{staticClass:"bottom-line"}):e._e(),e.isPwd&&e.codeArr.length>=t?[a("v-uni-text",{staticClass:"dot"},[e._v("●")])]:[a("v-uni-text",{staticClass:"number"},[e._v(e._s(e.codeArr[o]?e.codeArr[o]:""))])]],2)]}))],2)],1)},i=[]},"8fca":function(e,t,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var i=o(a("4c32")),n=o(a("5e99")),r=o(a("c8ec")),s=a("4b89"),d={components:{uniPopup:n.default,mypOne:r.default},data:function(){return{timeTip:"选择配送时间",time:null,navHeight:0,couponTitData:[{name:"可用优惠券",status:1},{name:"不可使用优惠券",status:0}],currentTab:1,couponInstructionsContent:'<p style="color:red">好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p><p>好的</p>',choiceWechatAdderError:!1,ischoiceWechatAdder:!1,postWechatAdder:!1,paymentMethod:"WECHAT",isOnXianMaiApp:s.isOnXianMaiApp}},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle},otherPaymentMethods:function(){return[{icon:this.$util.img("public/static/youpin/paySuccess-icon.png"),name:"微信支付",desc:"",key:"WECHAT",disable:!1},{icon:this.$util.img("public/static/youpin/balance.png"),name:"余额支付",desc:"(当前余额￥".concat(this.orderPaymentData.member_account.balance_money,"）"),key:"BALANCE",disable:Number(this.orderPaymentData.member_account.balance_money)<Number(this.orderPaymentData.pay_money)}]}},onLoad:function(){var e=this;uni.getSystemInfo({success:function(t){var a=t.statusBarHeight+46;e.navHeight=a,e.isOnXianMaiApp||(e.navHeight=0)},fail:function(e){console.log(e)}})},onShow:function(){this.limit_pay_tips=uni.getStorageSync("limit_pay_tips")},mixins:[i.default],methods:{bindTimeChange:function(e){this.timeTip=""+e.target.value,this.time=e.target.value,this.orderCreateData.delivery[e.currentTarget.dataset.siteid].buyer_ask_delivery_time=this.time},toShopDetail:function(e){this.$util.redirectTo("/otherpages/shop/index/index",{site_id:e})},openPopup:function(){this.$refs.popup.open()},closePopup:function(){this.$refs.popup.close()},openCouponInstructionsPopup:function(){this.$refs.couponInstructions.open()},closeCouponInstructionsPopup:function(){this.$refs.couponInstructions.close()},close_pay:function(){this.$refs.payPassword.close(),this.errMsg=""},closePopupCoupon:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},toBack:function(){this.closePopup(),uni.navigateBack()},toWaitPayList:function(){this.$refs.popupToList.close(),this.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=0",{},"redirectTo"),uni.removeStorage({key:"orderCreateData",success:function(){}})},getChooseAddress:function(){this.orderPaymentData.member_address,this.selectAddress()},saveAddress:function(e){var t=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:e,success:function(e){e.code>=0||(t.$util.showToast({title:e.message}),t.choiceWechatAdderError=!0)},complete:function(){t.postWechatAdder=!0}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),a=t.title,o=t.link,i=t.imageUrl;t.query;return this.$buriedPoint.pageShare(o,i,a)}};t.default=d},"92c0":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var o={name:"mypOneInput",props:{value:{type:String,default:""},maxlength:{type:Number,default:4},autoFocus:{type:Boolean,default:!1},isPwd:{type:Boolean,default:!1},type:{type:String,default:"bottom"}},watch:{maxlength:{immediate:!0,handler:function(e){this.ranges=6===e?[1,2,3,4,5,6]:[1,2,3,4]}},value:{immediate:!0,handler:function(e){e!==this.inputValue&&(this.inputValue=e,this.toMakeAndCheck(e))}}},data:function(){return{inputValue:"",codeIndex:1,codeArr:[],ranges:[1,2,3,4]}},methods:{getVal:function(e){var t=e.detail.value;this.inputValue=t,this.$emit("input",t),this.toMakeAndCheck(t)},toMakeAndCheck:function(e){var t=e.split("");this.codeIndex=t.length+1,this.codeArr=t,this.codeIndex>Number(this.maxlength)&&this.$emit("finish",this.codeArr.join(""))},set:function(e){this.inputValue=e,this.toMakeAndCheck(e)},clear:function(){this.inputValue="",this.codeArr=[],this.codeIndex=1}}};t.default=o},"9c74":function(e,t,a){"use strict";var o=a("35ba"),i=a.n(o);i.a},a3af:function(e,t,a){"use strict";a.r(t);var o=a("92c0"),i=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);t["default"]=i.a},acfe:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return o}));var o={uniPopup:a("5e99").default,mypOne:a("c8ec").default,loadingCover:a("5510").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"order-container",class:e.themeStyle},[e.isOnXianMaiApp?a("v-uni-view",{staticClass:"nav bg-white",style:{height:e.navHeight+"px"}},[a("v-uni-view",{staticClass:"nav-title"},[a("v-uni-image",{staticClass:"back",attrs:{src:e.$util.img("public/static/youpin/order/back.png"),mode:"aspectFit"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup()}}}),a("v-uni-text",[e._v("确认订单")])],1)],1):e._e(),a("v-uni-view",{staticClass:"wrapper",style:{marginTop:e.navHeight+"px"}},[a("v-uni-view",{staticClass:"address-wrap"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-image",{attrs:{src:e.$util.img("public/static/youpin/order/icon-no-pay-address.png"),mode:""}})],1),a("v-uni-view",{staticClass:"address-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getChooseAddress.apply(void 0,arguments)}}},[e.orderPaymentData.member_address?[a("v-uni-view",{staticClass:"info"},[a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.name))]),a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.mobile))])],1),a("v-uni-view",{staticClass:"detail"},[a("v-uni-text",[e._v(e._s(e.orderPaymentData.member_address.full_address)+" "+e._s(e.orderPaymentData.member_address.address))])],1)]:[a("v-uni-view",{staticClass:"address-empty"},[a("v-uni-text",[e._v("选择收货地址")])],1)],a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont iconright"})],1)],2)],1),e._l(e.orderPaymentData.shop_goods_list,(function(t,o){return e.orderPaymentData.shop_goods_list?a("v-uni-view",{key:o,staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-header",staticStyle:{visibility:"hidden"}},[a("v-uni-view",{staticClass:"iconfont icondianpu"}),a("v-uni-text",{staticClass:"site-name"},[e._v(e._s(t.site_name))])],1),a("v-uni-view",{staticClass:"site-body"},e._l(t.goods_list,(function(t,i){return a("v-uni-view",{key:i,staticClass:"goods-wrap"},[a("v-uni-navigator",{staticClass:"goods-img",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+t.sku_id}},[a("v-uni-image",{attrs:{src:e.$util.img(t.sku_image),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(o,i)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-navigator",{staticClass:"goods-name",attrs:{"hover-class":"none",url:"/pages/goods/detail/detail?sku_id="+t.sku_id}},[e._v(e._s(t.sku_name))]),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",[e._v(e._s(t.spec_name||""))])],1),a("v-uni-view",[a("v-uni-text",[e._v("x"+e._s(t.num))])],1)],1),a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-text",[a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price"},[e._v(e._s(t.price))])],1)],1)],1)],1)})),1),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{},[a("v-uni-text",{staticClass:"ns-font-size-sm"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",[e._v(e._s(e._f("moneyFormat")(t.delivery_money)))])],1)],1)],1)],1),a("v-uni-view",{staticClass:"site-footer"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("订单备注")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-input",{staticClass:"ns-font-size-base",attrs:{type:"text",value:"",placeholder:"请填对本次交易的说明","placeholder-style":"{color:#CCCCCC}",maxlength:"50"},model:{value:e.orderCreateData.buyer_message[t.site_id],callback:function(a){e.$set(e.orderCreateData.buyer_message,t.site_id,a)},expression:"orderCreateData.buyer_message[siteItem.site_id]"}})],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"box align-right order-pay"},[e._v("共"+e._s(t.goods_num)+"件商品"),a("v-uni-text",[e._v("小计："+e._s(e.$lang("common.currencySymbol"))),a("v-uni-text",{staticClass:"pay-money"},[e._v(e._s(e._f("moneyFormat")(t.pay_money)))])],1)],1)],1)],1)],1):e._e()})),a("v-uni-view",{staticClass:"site-wrap payment-methods"},e._l(e.otherPaymentMethods,(function(t){return a("v-uni-view",{staticClass:"item",class:{disable:t.disable},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changePayment(t)}}},[a("v-uni-image",{staticClass:"icon",attrs:{src:t.icon}}),a("v-uni-view",{staticClass:"title"},[a("v-uni-text",{staticClass:"text"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"desc"},[e._v(e._s(t.desc))])],1),a("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:e.paymentMethod==t.key,expression:"paymentMethod==method.key"}],staticClass:"checkbox",attrs:{src:e.$util.img("public/static/youpin/maidou/get.png")}}),a("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:e.paymentMethod!=t.key,expression:"paymentMethod!=method.key"}],staticClass:"checkbox",attrs:{src:e.$util.img("public/static/youpin/maidou/noneget.png")}})],1)})),1),a("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[a("v-uni-view",{staticClass:"order-settlement-info"},[a("v-uni-text",[e._v("实付金额：")]),a("v-uni-text",{staticClass:"text-color"},[e._v(e._s(e.$lang("common.currencySymbol"))),a("v-uni-text",{staticClass:"money pay_money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.pay_money)))])],1)],1),a("v-uni-view",{staticClass:"submit-btn"},[a("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderCreate.apply(void 0,arguments)}}},[e._v("确认支付")])],1)],1)],2),a("uni-popup",{ref:"popup",staticClass:"my-popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("提示")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[e._v("是否放弃本次付款？")]),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-view",{staticClass:"button white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toBack()}}},[e._v("放弃")]),a("v-uni-button",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup()}}},[e._v("继续付款")])],1)],1)],1),a("uni-popup",{ref:"popupToList",staticClass:"my-popup-dialog",attrs:{"mask-click":!1}},[a("v-uni-view",{staticClass:"popup-dialog"},[a("v-uni-view",{staticClass:"popup-dialog-header"},[e._v("提示")]),a("v-uni-view",{staticClass:"popup-dialog-body"},[e._v(e._s(e.limit_pay_tips))]),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-button",{staticClass:"button red",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toWaitPayList()}}},[e._v("我知道了")])],1)],1)],1),a("uni-popup",{ref:"payPassword",attrs:{custom:!0}},[a("v-uni-view",{staticClass:"pay-password"},[0==e.orderPaymentData.member_account.is_pay_password?[a("v-uni-view",{staticClass:"title"},[e._v("为了您的账户安全,请先设置您的支付密码")]),a("v-uni-view",{staticClass:"tips"},[e._v('可到"个人中心-设置-支付密码设置"中设置')]),a("v-uni-view",{staticClass:"btn ns-bg-color ns-border-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("立即设置")]),a("v-uni-view",{staticClass:"btn white ns-border-color ns-text-color",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.noSet.apply(void 0,arguments)}}},[e._v("暂不设置")])]:[a("v-uni-view",{staticClass:"popup-title"},[a("v-uni-image",{staticClass:"cha_close",attrs:{src:e.$util.img("public/static/youpin/maidou/cha.png"),mode:""},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close_pay()}}}),a("v-uni-view",{staticClass:"title"},[e._v("请输入支付密码")])],1),a("v-uni-view",{staticClass:"money-box"},[a("v-uni-view",{staticClass:"total-fee"},[e._v("总金额￥"+e._s(e._f("moneyFormat")(e.orderPaymentData.pay_money)))]),a("v-uni-view",{staticClass:"balance"},[e._v("(当前余额￥"+e._s(e.orderPaymentData.member_account.balance_money)+")")])],1),a("v-uni-view",{staticClass:"password-wrap"},[a("myp-one",{ref:"input",attrs:{maxlength:6,"is-pwd":!0,"auto-focus":e.isFocus,type:"box"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.input.apply(void 0,arguments)}}}),e.errMsg?a("v-uni-view",{staticClass:" ns-text-color ns-font-size-sm forget-password error-tips"},[e._v(e._s(e.errMsg))]):e._e(),a("v-uni-view",{staticClass:"align-right"},[a("v-uni-text",{staticClass:"ns-text-color ns-font-size-sm forget-password",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.setPayPassword.apply(void 0,arguments)}}},[e._v("忘记密码")])],1)],1)]],2)],1),a("loading-cover",{ref:"loadingCover"})],1)},n=[]},b156:function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@-webkit-keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}@keyframes twinkling-data-v-31a1a5ae{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}.code-box[data-v-31a1a5ae]{text-align:center}.flex-box[data-v-31a1a5ae]{display:flex;justify-content:center;flex-wrap:wrap;position:relative}.flex-box .hide-input[data-v-31a1a5ae]{position:absolute;top:0;left:-100%;width:200%;height:100%;text-align:left;z-index:9;opacity:1}.flex-box .item[data-v-31a1a5ae]{position:relative;flex:1;margin-right:%?18?%;font-size:%?70?%;font-weight:700;color:#333;line-height:%?100?%}.flex-box .item[data-v-31a1a5ae]::before{content:"";padding-top:100%;display:block}.flex-box .item[data-v-31a1a5ae]:last-child{margin-right:0}.flex-box .middle[data-v-31a1a5ae]{border:none}.flex-box .box[data-v-31a1a5ae]{box-sizing:border-box;border:%?2?% solid #ccc;border-width:%?2?% 0 %?2?% %?2?%;margin-right:0}.flex-box .box[data-v-31a1a5ae]:first-of-type{border-top-left-radius:%?8?%;border-bottom-left-radius:%?8?%}.flex-box .box[data-v-31a1a5ae]:last-child{border-right:%?2?% solid #ccc;border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.flex-box .bottom[data-v-31a1a5ae]{box-sizing:border-box;border-bottom:1px solid #ddd}.flex-box .active[data-v-31a1a5ae]{border-color:#ddd}.flex-box .active .line[data-v-31a1a5ae]{display:block}.flex-box .line[data-v-31a1a5ae]{display:none;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?2?%;height:%?40?%;background:#333;-webkit-animation:twinkling-data-v-31a1a5ae 1s infinite ease;animation:twinkling-data-v-31a1a5ae 1s infinite ease}.flex-box .dot[data-v-31a1a5ae],\n.flex-box .number[data-v-31a1a5ae]{font-size:%?44?%;line-height:%?40?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.flex-box .bottom-line[data-v-31a1a5ae]{height:4px;background:#000;width:80%;position:absolute;border-radius:2px;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}',""]),e.exports=t},c8ec:function(e,t,a){"use strict";a.r(t);var o=a("61c4"),i=a("a3af");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("4210");var r=a("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"31a1a5ae",null,!1,o["a"],void 0);t["default"]=s.exports},d2fd:function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-080e78d5]{width:100%;text-align:center}[data-v-080e78d5] .uni-navbar--border{border-bottom-width:0}',""]),e.exports=t},d9f9:function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-080e78d5]{width:100%;text-align:center}.uni-list-cell[data-v-080e78d5]{display:flex;justify-content:space-between}.align-right[data-v-080e78d5]{text-align:right}.inline[data-v-080e78d5]{display:inline!important}.order-container[data-v-080e78d5]{padding-bottom:%?120?%}.order-container.safe-area[data-v-080e78d5]{padding-bottom:%?188?%}.address-wrap[data-v-080e78d5]{background:#fff;position:relative;min-height:%?100?%;max-height:%?140?%;display:flex;align-items:center}.address-wrap .icon[data-v-080e78d5]{width:%?94?%;height:%?100?%;display:flex;justify-content:center;align-items:center}.address-wrap .icon uni-image[data-v-080e78d5]{width:%?48?%;height:%?48?%}.address-wrap .address-info[data-v-080e78d5]{width:%?656?%}.address-wrap .address-info .info[data-v-080e78d5]{display:flex;padding-top:%?10?%}.address-wrap .address-info .info uni-text[data-v-080e78d5]{overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;word-break:break-all;font-weight:700}.address-wrap .address-info .info uni-text[data-v-080e78d5]:first-of-type{max-width:%?380?%;margin-right:%?32?%}.address-wrap .address-info .detail[data-v-080e78d5]{width:%?508?%;padding-bottom:%?17?%;line-height:1.3;font-size:%?26?%}.address-wrap .address-empty[data-v-080e78d5]{line-height:%?100?%;color:#999;font-size:%?26?%}.address-wrap .cell-more[data-v-080e78d5]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?20?%}.address-wrap .cell-more .iconfont[data-v-080e78d5]{color:#999}.mobile-wrap[data-v-080e78d5]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.mobile-wrap .form-group .form-item[data-v-080e78d5]{display:flex;line-height:%?50?%}.mobile-wrap .form-group .form-item .text[data-v-080e78d5]{display:inline-block;line-height:%?50?%;padding-right:%?10?%}.mobile-wrap .form-group .form-item .placeholder[data-v-080e78d5]{line-height:%?50?%}.mobile-wrap .form-group .form-item .input[data-v-080e78d5]{flex:1;height:%?50?%;line-height:%?50?%}.order-cell[data-v-080e78d5]{display:flex;margin:%?48?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-080e78d5]{text-align:left}.order-cell .box[data-v-080e78d5]{flex:1;line-height:inherit}.order-cell .box .textarea[data-v-080e78d5]{height:%?40?%}.order-cell .iconfont[data-v-080e78d5]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-080e78d5]{padding:0;font-size:%?26?%;color:#333}.order-cell .order-pay uni-text[data-v-080e78d5]{display:inline-block;margin-left:%?20?%}.order-cell .order-pay .pay-money[data-v-080e78d5]{font-size:%?32?%;font-weight:700;margin-left:%?2?%}.site-wrap[data-v-080e78d5]{margin:%?24?%;padding:0 %?24?%;border-radius:%?20?%;background:#fff;position:relative}.site-wrap .site-header[data-v-080e78d5]{display:flex;align-items:center;height:%?88?%}.site-wrap .site-header .icondianpu[data-v-080e78d5]{display:inline-block;line-height:1;margin-right:%?17?%;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap[data-v-080e78d5]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-080e78d5]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-080e78d5]{width:%?180?%;height:%?180?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-080e78d5]{width:100%;height:100%;border-radius:%?20?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-080e78d5]{flex:1;position:relative;max-width:calc(100% - %?200?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-080e78d5]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;margin-bottom:%?30?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-080e78d5]{width:100%;line-height:1.3;display:flex;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-080e78d5]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-080e78d5]{color:#999;font-size:%?24?%;line-height:1.3}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-080e78d5]:last-of-type{text-align:right}.site-wrap .site-body .goods-wrap .goods-info .goods-price[data-v-080e78d5]{color:#333;text-align:right;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-price .price[data-v-080e78d5]{font-size:%?28?%}.site-wrap .site-footer[data-v-080e78d5]{padding-bottom:%?30?%}.site-wrap .site-footer .order-cell .tit[data-v-080e78d5]{width:%?180?%}.site-wrap .site-footer .order-cell .store-promotion-box[data-v-080e78d5]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.site-wrap .site-footer .order-cell .box uni-input[data-v-080e78d5]{text-align:right;font-size:%?26?%}.site-wrap .site-footer .order-cell .box.text-overflow[data-v-080e78d5]{max-width:calc(100% - %?180?%)}.site-wrap .site-footer .order-cell.my-coupon[data-v-080e78d5]{padding:%?26?% 0;margin:0;text-align:right}.site-wrap .site-footer .order-cell.my-coupon .box.text-overflow[data-v-080e78d5]{max-width:unset}.site-wrap .site-footer .order-cell[data-v-080e78d5]:last-of-type{margin-bottom:0}.order-checkout[data-v-080e78d5]{margin:%?20?%;padding:%?20?%;border-radius:4px;background:#fff;position:relative}.order-checkout .order-cell .iconyuan_checkbox[data-v-080e78d5],\r\n.order-checkout .order-cell .iconyuan_checked[data-v-080e78d5]{font-size:%?38?%}.order-money .order-cell .box[data-v-080e78d5]{padding:0}.order-money .order-cell .box .operator[data-v-080e78d5]{font-size:%?24?%;margin-right:%?6?%}.order-submit[data-v-080e78d5]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?98?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);display:flex;align-items:center}.order-submit.bottom-safe-area[data-v-080e78d5]{padding-bottom:0!important;padding-bottom:constant(safe-area-inset-bottom)!important;padding-bottom:env(safe-area-inset-bottom)!important}.order-submit .order-settlement-info[data-v-080e78d5]{flex:1;height:%?98?%;line-height:%?98?%;padding-left:%?25?%;font-size:%?28?%}.order-submit .order-settlement-info .money[data-v-080e78d5]{font-size:%?48?%}.order-submit .submit-btn[data-v-080e78d5]{height:%?80?%;margin-right:%?24?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-080e78d5]{padding:0;width:%?200?%;background-color:#f2270c!important;height:%?80?%;line-height:%?80?%;font-size:%?28?%;border-radius:%?40?%}.popup[data-v-080e78d5]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-080e78d5]{height:%?90?%;display:flex;align-items:center;padding:0 %?30?%}.popup .popup-header > uni-view[data-v-080e78d5]{flex:1;line-height:1}.popup .popup-header .tit[data-v-080e78d5]{font-size:%?32?%;font-weight:600}.popup .popup-header .vice-tit[data-v-080e78d5]{margin-right:%?20?%}.popup .popup-body[data-v-080e78d5]{height:calc(100% - %?210?%);height:calc(100% - %?210?% - constant(safe-area-inset-bottom));height:calc(100% - %?210?% - env(safe-area-inset-bottom))}.popup .popup-footer[data-v-080e78d5]{padding-bottom:0!important;padding-bottom:constant(safe-area-inset-bottom)!important;padding-bottom:env(safe-area-inset-bottom)!important}.popup .popup-footer .confirm-btn[data-v-080e78d5]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?40?%;border-radius:%?40?%}.invoice-popup[data-v-080e78d5]{height:65vh}.invoice-popup .popup-body .invoice-cell[data-v-080e78d5]{margin:0 %?30?%;padding:%?30?% 0;border-top:1px solid #f5f5f5}.invoice-popup .popup-body .invoice-cell[data-v-080e78d5]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-080e78d5]{font-size:%?28?%;font-weight:600}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-080e78d5]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-080e78d5]{display:inline-block;line-height:1;font-size:%?28?%;padding:%?16?% %?40?%;background:#eee;border:1px solid #eee;border-radius:%?32?%;margin:0 %?20?% %?20?% 0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(1), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(2), .invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(3){margin-bottom:0}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.disabled[data-v-080e78d5]{color:#aaa}.invoice-popup .popup-body .invoice-cell .form-group[data-v-080e78d5]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item[data-v-080e78d5]{display:flex;line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .text[data-v-080e78d5]{display:inline-block;width:%?200?%;line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .placeholder[data-v-080e78d5]{line-height:%?50?%}.invoice-popup .popup-body .invoice-cell .form-group .form-item .input[data-v-080e78d5]{flex:1;height:%?50?%;line-height:%?50?%}.coupon-popup-father[data-v-080e78d5] .uni-popup__wrapper{background:transparent!important}.coupon-popup-father[data-v-080e78d5] .uni-popup__wrapper .uni-popup__wrapper-box{border-radius:%?20?% %?20?% 0 0!important}.coupon-popup[data-v-080e78d5]{border-radius:%?25?% %?25?% 0 0;height:65vh;overflow:hidden}.coupon-popup .popup-body[data-v-080e78d5]{height:calc(100% - %?272?%);height:calc(100% - %?272?% - constant(safe-area-inset-bottom));height:calc(100% - %?272?% - env(safe-area-inset-bottom))}.coupon-popup .uni-tab-item[data-v-080e78d5]{display:inline-block;flex-wrap:nowrap;padding-left:%?24?%;padding-right:%?24?%}.coupon-popup .uni-tab-item-title[data-v-080e78d5]{color:#555;font-size:%?30?%;display:block;height:%?64?%;line-height:%?64?%;padding:0 %?10?%;flex-wrap:nowrap;white-space:nowrap}.coupon-popup .uni-tab-item-title .line[data-v-080e78d5]{width:%?36?%;height:%?6?%;background:transparent;border-radius:%?3?%;margin:0 auto}.coupon-popup .uni-tab-item-title-active[data-v-080e78d5]{display:block;height:%?64?%;padding:0 %?10?%}.coupon-popup .uni-tab-item-title-active .line[data-v-080e78d5]{background:#f2280c}.coupon-popup .popup-body[data-v-080e78d5]{background:#f5f5f5}.coupon-popup .coupon-item[data-v-080e78d5]{position:relative;margin:%?24?%;font-size:0}.coupon-popup .coupon-item .coupon_ysy[data-v-080e78d5]{width:%?702?%;height:%?200?%}.coupon-popup .coupon-item > .iconfont[data-v-080e78d5]{font-size:%?40?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.coupon-popup .coupon-item > .iconyuan_checkbox[data-v-080e78d5]{color:#a6a6a6}.coupon-popup .coupon-item .circular[data-v-080e78d5]{position:absolute;top:50%;left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background:#f5f5f5;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item .coupon-info[data-v-080e78d5]{height:%?200?%;display:flex;width:%?702?%;position:absolute;top:0}.coupon-popup .coupon-item .coupon-info .coupon-money[data-v-080e78d5]{width:%?218?%;height:%?200?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?24?%}.coupon-popup .coupon-item .coupon-info .coupon-money uni-text[data-v-080e78d5]{font-size:%?50?%;line-height:1}.coupon-popup .coupon-item .coupon-info .coupon-money .ns-font-size-sm-left[data-v-080e78d5]{font-size:%?24?%!important;margin-top:%?14?%;margin-left:%?4?%}.coupon-popup .coupon-item .coupon-info .coupon-money .ns-font-size-sm-right[data-v-080e78d5]{font-size:%?24?%!important;margin-top:%?14?%;margin-right:%?4?%}.coupon-popup .coupon-item .coupon-info .info[data-v-080e78d5]{flex:1;max-width:calc(100% - %?240?%)}.coupon-popup .coupon-item .coupon-info .info .coupon-name[data-v-080e78d5]{margin-top:%?18?%;margin-bottom:%?50?%;font-weight:700;color:#333}.coupon-popup .coupon-item .coupon-info .info .coupon-time[data-v-080e78d5]{color:#999;font-size:%?22?%}.coupon-popup .coupon-item .coupon-info .info uni-view[data-v-080e78d5]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.promotion-popup[data-v-080e78d5]{height:65vh}.promotion-popup .order-cell[data-v-080e78d5]{margin:%?20?% %?30?%;display:flex;flex-direction:column;align-items:flex-start}.promotion-popup .order-cell .tit[data-v-080e78d5]{line-height:%?60?%}.promotion-popup .order-cell .box[data-v-080e78d5]{padding:0}.delivery-popup[data-v-080e78d5]{height:80vh}.delivery-popup .delivery-cell[data-v-080e78d5]{margin:0 %?30?%;padding:%?30?% 0;border-top:1px solid #f5f5f5}.delivery-popup .delivery-cell[data-v-080e78d5]:first-of-type{border-top:none}.delivery-popup .delivery-cell .tit[data-v-080e78d5]{font-size:%?28?%;font-weight:600}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-080e78d5]{display:inline-block;line-height:1;font-size:%?28?%;padding:%?16?% %?40?%;background:#eee;border:1px solid #eee;border-radius:%?32?%;margin:0 %?20?% %?20?% 0}.delivery-popup .delivery-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(1), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(2), .delivery-popup .delivery-cell .option-grpup .option-item[data-v-080e78d5]:nth-of-type(3){margin-bottom:0}.delivery-popup .delivery-cell .option-grpup .option-item.disabled[data-v-080e78d5]{color:#aaa}.delivery-popup .delivery-cont[data-v-080e78d5]{height:calc(100% - %?180?%);overflow-y:scroll}.delivery-popup .delivery-cont .pickup-point[data-v-080e78d5]{padding:%?20?% 0;border-top:1px solid #f5f5f5}.delivery-popup .delivery-cont .pickup-point .name[data-v-080e78d5]{display:flex}.delivery-popup .delivery-cont .pickup-point .name .icon[data-v-080e78d5]{flex:1;text-align:right}.delivery-popup .delivery-cont .pickup-point .name .icon .iconfont[data-v-080e78d5]{line-height:1}.delivery-popup .delivery-cont .pickup-point[data-v-080e78d5]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-cont .pickup-point .info[data-v-080e78d5]{line-height:1.2}.delivery-popup .delivery-cont .pickup-point .info .ns-text-color-gray[data-v-080e78d5]:last-of-type{margin-left:%?10?%}.pay-password[data-v-080e78d5]{width:80vw;background:#fff;box-sizing:border-box;border-radius:%?10?%;overflow:hidden;padding:%?30?% %?40?%;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.pay-password .popup-title[data-v-080e78d5]{display:flex;align-items:center}.pay-password .popup-title .title[data-v-080e78d5]{font-size:%?28?%;text-align:center;width:calc(100% - %?40?%);text-align:center}.pay-password .error-tips[data-v-080e78d5]{text-align:center;width:100%}.pay-password .money-box[data-v-080e78d5]{margin-top:%?50?%}.pay-password .money-box .total-fee[data-v-080e78d5]{text-align:center;font-size:%?48?%;font-weight:700;color:#333}.pay-password .money-box .balance[data-v-080e78d5]{font-size:%?24?%;color:#999;text-align:center}.pay-password .tips[data-v-080e78d5]{font-size:%?24?%;color:#999;text-align:center}.pay-password .btn[data-v-080e78d5]{width:60%;margin:0 auto;margin-top:%?30?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;color:#fff;text-align:center;border:1px solid #fff}.pay-password .btn.white[data-v-080e78d5]{margin-top:%?20?%;background-color:#fff!important}.pay-password .password-wrap[data-v-080e78d5]{padding-top:%?20?%;width:90%;margin:0 auto}.pay-password .password-wrap .forget-password[data-v-080e78d5]{margin-top:%?20?%;display:inline-block}.text-color[data-v-080e78d5]{color:#f1270c}.text-color .money[data-v-080e78d5]{font-weight:700;font-size:%?36?%!important}.nav[data-v-080e78d5]{width:100%;overflow:hidden;position:fixed;top:0;left:0;z-index:10;background-color:#fff}.nav-title[data-v-080e78d5]{width:100%;height:%?88?%;line-height:%?88?%;text-align:center;position:absolute;bottom:0;left:0;z-index:10}.nav .back[data-v-080e78d5]{width:%?42?%;height:%?70?%;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?0?%;padding:0 %?24?%}.wrapper[data-v-080e78d5]{padding-top:%?24?%}.coupon-instructions-close[data-v-080e78d5]{display:flex;justify-content:flex-end;align-items:center}.coupon-instructions-btn[data-v-080e78d5]{margin-right:%?20?%;color:#999;font-size:%?24?%}.coupon-close[data-v-080e78d5]{color:#a0a1a7}.coupon-default[data-v-080e78d5]{display:flex;align-items:center;justify-content:center;height:100%}.coupon-default uni-view[data-v-080e78d5]{color:#999}.payment-methods .item[data-v-080e78d5]{display:flex;justify-content:space-between;align-items:center;padding:%?25?% 0}.payment-methods .item.disable .title uni-text[data-v-080e78d5]{color:#ccc}.payment-methods .item .icon[data-v-080e78d5]{width:%?48?%;height:%?48?%}.payment-methods .item .title[data-v-080e78d5]{flex:1;font-size:%?26?%;margin:0 %?10?%}.payment-methods .item .title .desc[data-v-080e78d5]{color:#f2270c;margin-left:%?10?%}.payment-methods .item .checkbox[data-v-080e78d5]{width:%?32?%;height:%?32?%}',""]),e.exports=t},e133:function(e,t,a){var o=a("b156");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("356f4cc6",o,!0,{sourceMap:!1,shadowMode:!1})},e49e:function(e,t,a){"use strict";a.r(t);var o=a("8fca"),i=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);t["default"]=i.a},ee77:function(e,t,a){"use strict";var o=a("345d"),i=a.n(o);i.a},f1a0:function(e,t,a){var o=a("d9f9");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("81d4ae0a",o,!0,{sourceMap:!1,shadowMode:!1})}}]);