(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-cart-cart"],{"0a0a":function(t,e,a){"use strict";a.r(e);var i=a("1f35"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},"0bd3":function(t,e,a){"use strict";var i=a("feda"),o=a.n(i);o.a},"15e0":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"diy-horz-blank",props:{value:{type:Object}},data:function(){return{}},created:function(){},methods:{}};e.default=i},"1f35":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("aa9c"),a("dc8a"),a("64aa"),a("dd2b"),a("d4b5"),a("e966");var o=i(a("2634")),n=i(a("2fdc")),s=i(a("dadc")),r=i(a("dea9")),c=i(a("ecbc")),d=i(a("37cd")),l=i(a("3b27")),u=i(a("4d57")),f=(i(a("7c8d")),i(a("5e99"))),p=i(a("c799")),h=i(a("85bf")),g=i(a("2d01")),v={components:{uniNumberBox:s.default,diyBottomNav:r.default,diyHorzBlank:c.default,toTop:d.default,nsGoodsSku:u.default,uniPopup:f.default,nsGoodsRecommend:p.default},mixins:[l.default,g.default],data:function(){return{navHeight:0,isEdit:!1,startX:0,startY:0,selectSiteIndex:-1,selectCartIndex:-1,siteIndex:-1,cartIndex:-1,selectCartItem:null,shop_id:null,openBottomNav:!1,token:"",cartData:[],checkAll:!1,totalPrice:"0.00",totalCount:0,modifyFlag:!1,isSub:!1,invalidGoods:[],emptyBtn1:{text:"去逛逛"},is_shopper:0,shopOwnerBrokerage:0}},onLoad:function(){var t=this,e=uni.getStorageSync("shop_id");this.shop_id=e,this.openBottomNav=!0,uni.getSystemInfo({success:function(e){var a=e.statusBarHeight+46;t.navHeight=a},fail:function(t){console.log(t)}}),uni.getStorageSync("is_shopper")&&(this.is_shopper=uni.getStorageSync("is_shopper"))},onShow:function(){var t=this;return(0,n.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$langConfig.refresh(),e.next=3,h.default.wait_staticLogin_success();case 3:t.$util.toShowLoginPopup(t,null,"/pages/goods/cart/cart"),uni.getStorageSync("token")&&t.getCartData(),uni.getStorageSync("is_register")&&(t.$util.toShowCouponPopup(t),uni.removeStorageSync("is_register"));case 6:case"end":return e.stop()}}),e)})))()},onReady:function(){uni.getStorageSync("token")||this.$refs.loadingCover&&this.$refs.loadingCover.hide()},computed:{isBottom:function(){return this.cartData.length>0||this.invalidGoods.length>0},themeStyle:function(){return"theme-"+this.$store.state.themeStyle},systemInfo:function(){return uni.getSystemInfoSync()}},onReachBottom:function(){this.$refs.goodrecommend.scrollPage()},methods:{goProductDetail:function(t){this.$util.redirectTo("/pages/goods/detail/detail",{sku_id:t})},getCartData:function(){var t=this;this.$api.sendRequest({url:"/api/cart/lists",success:function(e){e.code>=0?(t.token=uni.getStorageSync("token"),e.data.length?t.handleCartData(e.data):t.cartData=[]):t.token="",t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},handleCartData:function(t){var e=this;this.invalidGoods=[],this.cartData=[];var a={};t.forEach((function(t,i){1==t.goods_state&&1==t.verify_state?(t.checked=!0,t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format)),void 0!=a["site_"+t.site_id]?a["site_"+t.site_id].cartList.push(t):a["site_"+t.site_id]={siteId:t.site_id,siteName:t.site_name,edit:!1,checked:!0,cartList:[t]}):e.invalidGoods.push(t)})),this.cartData=[],Object.keys(a).forEach((function(t){e.cartData.push(a[t])})),this.calculationTotalPrice()},singleElection:function(t,e){this.cartData[t].cartList[e].checked=!this.cartData[t].cartList[e].checked,this.calculationTotalPrice()},siteAllElection:function(t,e){this.cartData[e].checked=t,this.cartData[e].cartList.forEach((function(e){e.checked=t})),this.calculationTotalPrice()},allElection:function(t){var e=this;this.checkAll="boolean"==typeof t?t:!this.checkAll,this.cartData.length&&this.cartData.forEach((function(t){t.checked=e.checkAll,t.cartList.forEach((function(t){t.checked=e.checkAll}))})),this.calculationTotalPrice()},calculationTotalPrice:function(){if(this.cartData.length){var t=0,e=0,a=0,i=0;this.cartData.forEach((function(o){var n=0;o.cartList.forEach((function(a){a.checked&&(n+=1,e+=a.num,t+=a.discount_price*a.num,i+=Number(a.shop_owner_brokerage)*a.num)})),o.cartList.length==n?(o.checked=!0,a+=1):o.checked=!1})),this.shopOwnerBrokerage=i.toFixed(2),this.totalPrice=t.toFixed(2),this.totalCount=e,this.checkAll=this.cartData.length==a}else this.totalPrice="0.00",this.totalCount=0;this.modifyFlag=!1},deleteCart:function(t,e){var a=this,i="",o=[],n=[];if(this.isEdit){if(i=[],this.cartData.forEach((function(t){t.cartList.forEach((function(t){t.checked&&i.push(t.cart_id),t.checked&&o.push(t.sku_id),t.checked&&n.push(t.num)}))})),!i.length)return void this.$util.showToast({title:"请选择要删除的商品"})}else i=t>-1&&this.cartData[t]&&this.cartData[t].cartList&&this.cartData[t].cartList[e]?this.cartData[t].cartList[e].cart_id:"";this.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:i,is_collect:0},success:function(i){if(i.code>=0){if(a.isEdit){var s=JSON.parse(JSON.stringify(a.cartData));a.cartData=[];for(var r=s.length-1;r>=0;r--)if(s[r].checked)s.splice(r,1);else for(var c=s[r].cartList.length-1;c>=0;c--)s[r].cartList[c].checked&&s[r].cartList.splice(c,1);a.cartData=s}else a.cartData[t].cartList.splice(e,1),0==a.cartData[t].cartList.length&&a.cartData.splice(t,1);a.selectSiteIndex=-1,a.selectCartIndex=-1,a.calculationTotalPrice(),a.getCartNumber(),a.$buriedPoint.purchaseGoods({id:o,action_type:1,action_num:n,is_goods_page:0})}else a.$util.showToast({title:i.message});a.$refs.popup.closePopup()}})},openPopup:function(t,e){this.siteIndex=t,this.cartIndex=e,this.$refs.popup.open()},editCollection:function(t,e,a){var i=this;this.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:t.cart_id,is_collect:1},success:function(t){0==t.code&&(i.cartData[e].cartList.splice(a,1),0==i.cartData[e].cartList.length&&i.cartData.splice(e,1),i.selectSiteIndex=-1,i.selectCartIndex=-1,i.calculationTotalPrice(),i.getCartNumber(),i.$util.showToast({title:"收藏成功"}))}})},changeCart:function(t){var e=this;return(0,n.default)((0,o.default)().mark((function a(){var i,n,s,r,c;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.$api.sendRequest({url:"/api/goodssku/detail",async:!1,data:{sku_id:t.sku_id}});case 2:if(i=a.sent,n=i.data,n.goods_sku_detail.goods_spec_format=n.goods_sku_detail.goods_spec_format?JSON.parse(n.goods_sku_detail.goods_spec_format):"",n.goods_sku_detail.show_price=n.goods_sku_detail.discount_price,n.goods_sku_detail.goods_attr_format)for(s=JSON.parse(n.goods_sku_detail.goods_attr_format),n.goods_sku_detail.goods_attr_format=JSON.parse(n.goods_sku_detail.goods_attr_format),n.goods_sku_detail.goods_attr_format=e.$util.unique(n.goods_sku_detail.goods_attr_format,"attr_id"),r=0;r<n.goods_sku_detail.goods_attr_format.length;r++)for(c=0;c<s.length;c++)n.goods_sku_detail.goods_attr_format[r].attr_id==s[c].attr_id&&n.goods_sku_detail.goods_attr_format[r].attr_value_id!=s[c].attr_value_id&&(n.goods_sku_detail.goods_attr_format[r].attr_value_name+="、"+s[c].attr_value_name);if(n.goods_sku_detail.sku_spec_format=n.goods_sku_detail.sku_spec_format?JSON.parse(n.goods_sku_detail.sku_spec_format):[],n.goods_sku_detail.goods_spec_format){a.next=10;break}return a.abrupt("return");case 10:n.goods_sku_detail.cart_id=t.cart_id,n.discount_content&&(n.goods_sku_detail.discount_content=n.discount_content),e.selectCartItem=n.goods_sku_detail,e.chooseSkuspecFormat();case 14:case"end":return a.stop()}}),a)})))()},chooseSkuspecFormat:function(){var t=this;this.$refs.goodsSku.show("update_cart",(function(){t.getCartData()}))},refreshGoodsSkuDetail:function(t){Object.assign(this.selectCartItem,t)},cartNumChange:function(t,e){var a=this,i=this.cartData[e.siteIndex].cartList[e.cartIndex];t<1&&(t=1),this.modifyFlag=!0,this.$api.sendRequest({url:"/api/cart/edit",data:{num:t,cart_id:i.cart_id},success:function(o){if(o.code>=0){var n=i.num;a.cartData[e.siteIndex].cartList[e.cartIndex].num=parseInt(t),a.calculationTotalPrice(),a.$buriedPoint.purchaseGoods({id:i.sku_id,action_type:n<t?0:1,action_num:[n<t?t-n:n-t],is_goods_page:0})}else a.$util.showToast({title:o.message})}})},settlement:function(){var t=this;if(this.totalCount>0){var e=[],a=[],i=[];if(this.cartData.forEach((function(t){t.cartList.forEach((function(t){t.checked&&(e.push(t.cart_id),a.push(t.sku_id),i.push(t.num))}))})),this.$buriedPoint.submitOrderContent({sku_ids:a,num:i,pages:0}),this.isSub)return;this.isSub=!0,uni.setStorage({key:"orderCreateData",data:{cart_ids:e.toString()},success:function(){t.$util.redirectTo("/pages/order/payment/payment"),t.isSub=!1}})}},edit:function(t){this.cartData[t].edit=!this.cartData[t].edit},clearInvalidGoods:function(){var t=this,e=[];this.invalidGoods.forEach((function(t){e.push(t.cart_id)})),e.length&&this.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:e.toString()},success:function(e){e.code>=0?(t.invalidGoods=[],t.getCartNumber()):t.$util.showToast({title:e.message})}})},imageError:function(t,e){this.cartData[t].cartList[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},getCartNumber:function(){uni.getStorageSync("token")&&this.$store.dispatch("getCartNumber")},touchS:function(t){this.isEdit||(this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY)},touchM:function(t){if(!this.isEdit){this.currentX=t.touches[0].clientX,this.currentY=t.touches[0].clientY;var e=this.startX-this.currentX,a=Math.abs(this.startY-this.currentY);e>35&&a<110?(this.selectSiteIndex=t.currentTarget.dataset.siteindex,this.selectCartIndex=t.currentTarget.dataset.cartindex):e<-35&&a<110&&(this.selectSiteIndex=-1,this.selectCartIndex=-1)}},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(t){var e=this.getSharePageParams(),a=e.title,i=e.link,o=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,o,a)}};e.default=v},"2e35":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3548e996]{width:100%;text-align:center}.container[data-v-3548e996]{width:100%;height:100%;padding-bottom:0;padding-bottom:calc(%?120?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?120?% + env(safe-area-inset-bottom));padding-top:calc(%?80?% + env(safe-area-inset-top))}.nav[data-v-3548e996]{width:100%;overflow:hidden;position:fixed;z-index:999;background-color:#fff}.nav-title[data-v-3548e996]{width:100%;height:%?88?%;line-height:%?88?%;position:absolute;bottom:0;left:0;z-index:10;text-align:center}.top-operation[data-v-3548e996]{background:#fff;display:flex;justify-content:flex-end;line-height:%?80?%;padding-right:%?30?%;height:%?80?%;position:fixed;left:0;top:%?128?%;width:100%;box-sizing:border-box;z-index:99}.cart-wrap[data-v-3548e996]{margin:%?20?%;background:#fff;border-radius:4px;overflow:hidden;width:calc(100% - 20px)}.cart-wrap .cart-header[data-v-3548e996]{padding:%?20?%;overflow:hidden;display:flex;align-items:center;line-height:%?40?%}.cart-wrap .cart-header .shop-info[data-v-3548e996]{flex:1;line-height:inherit;font-size:%?28?%;font-weight:700}.cart-wrap .cart-header .iconyuan_checkbox[data-v-3548e996]{font-size:%?36?%;color:#898989;margin-right:%?14?%;line-height:1}.cart-wrap .cart-header .iconyuan_checked[data-v-3548e996]{font-size:%?36?%;margin-right:%?14?%;line-height:1}.cart-wrap .cart-header .icondianpu[data-v-3548e996]{display:inline-block;margin-right:%?10?%;line-height:inherit}.cart-wrap .cart-header .cart-operation[data-v-3548e996]{line-height:inherit}.cart-wrap .cart-goods[data-v-3548e996]{padding:0 %?20?% %?20?% %?20?%;border-radius:4px;background:#fff;box-sizing:border-box;position:relative}.cart-wrap .cart-goods .goods-wrap[data-v-3548e996]{display:flex;position:relative;padding-left:%?52?%;transition:all .3s;width:100%;box-sizing:border-box}.cart-wrap .cart-goods .goods-wrap.show[data-v-3548e996]{margin-left:%?-100?%;width:calc(100% - %?20?%)}.cart-wrap .cart-goods .goods-wrap.show .goods-info[data-v-3548e996]{width:calc(100% - %?400?%)}.cart-wrap .cart-goods .goods-wrap.edit[data-v-3548e996]{-webkit-transform:translateX(%?-120?%);transform:translateX(%?-120?%)}.cart-wrap .cart-goods .goods-wrap > .iconfont[data-v-3548e996]{font-size:%?36?%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cart-wrap .cart-goods .goods-wrap > .iconyuan_checkbox[data-v-3548e996]{color:#a6a6a6}.cart-wrap .cart-goods .goods-wrap .goods-img[data-v-3548e996]{width:%?200?%;height:%?200?%;margin:%?20?% %?20?% 0 0;border-radius:20px;background:hsla(0,0%,85.1%,.2)}.cart-wrap .cart-goods .goods-wrap .goods-img uni-image[data-v-3548e996]{width:100%;height:100%}.cart-wrap .cart-goods .goods-wrap .goods-info[data-v-3548e996]{flex:1;position:relative;padding:%?20?% 0 0 0;max-width:calc(100% - %?200?%)}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-name[data-v-3548e996]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.07;font-size:%?28?%}.cart-wrap .cart-goods .goods-wrap .goods-info .sku[data-v-3548e996]{height:%?44?%;line-height:%?44?%;font-size:%?24?%;font-weight:400;color:#383838;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;border-radius:%?8?%;background:#f5f5f5;padding:0 %?16?%;margin-top:%?14?%;max-width:%?350?%}.cart-wrap .cart-goods .goods-wrap .goods-info .sku .arrow-bottom[data-v-3548e996]{margin-left:%?8?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section[data-v-3548e996]{position:absolute;left:0;bottom:%?-14?%;width:100%;display:flex;justify-content:space-between;align-items:center}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section-shopper[data-v-3548e996]{bottom:%?-2?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section-left[data-v-3548e996]{display:flex;flex-direction:column;justify-content:center;align-items:flex-start}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-3548e996]{font-weight:700;font-size:%?32?%;line-height:%?37?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .goods-price-desc[data-v-3548e996]{font-size:%?20?%;font-weight:400;margin-left:%?10?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .price-distributor[data-v-3548e996]{line-height:0}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .price-distributor uni-text[data-v-3548e996]{height:%?24?%;border-radius:%?4?%;font-size:%?20?%;font-weight:400;line-height:%?23.44?%;color:grey;position:relative;display:flex;justify-content:center;align-items:center}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .unit[data-v-3548e996]{font-size:%?20?%;margin-right:%?8?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996]{float:right}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996] .uni-numbox{overflow:hidden;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0)}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996] .uni-numbox:after{border:none}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996] .uni-numbox .uni-numbox__minus,\r\n.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996] .uni-numbox .uni-numbox__plus{border-radius:%?10?%;width:%?40?%;height:%?40?%;line-height:%?40?%;font-size:%?32?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .cart-number[data-v-3548e996] .uni-numbox .uni-numbox__value{width:%?80?%;height:%?40?%;line-height:%?40?%}.cart-wrap .cart-goods .item-operation[data-v-3548e996]{position:absolute;width:%?100?%;top:0;right:%?-220?%;height:100%;transition:all .3s;display:flex;align-items:center}.cart-wrap .cart-goods .item-operation .item-del[data-v-3548e996],\r\n.cart-wrap .cart-goods .item-operation .item-moveIn[data-v-3548e996]{width:100%;height:100%;color:#fff;flex-shrink:0;text-align:center;vertical-align:middle;display:flex;align-items:center;justify-content:center;padding:0 %?10?%;box-sizing:border-box;flex-wrap:wrap}.cart-wrap .cart-goods .item-operation .item-moveIn[data-v-3548e996]{background:var(--custom-brand-color)}.cart-wrap .cart-goods .item-operation.show[data-v-3548e996]{right:0}.cart-wrap .invalid-goods .invalid-mark[data-v-3548e996]{background:#9a9a9a;color:#fff;padding:%?4?% %?8?%;display:inline-block;line-height:1;font-size:%?24?%;position:absolute;border-radius:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cart-wrap .invalid-goods .goods-name[data-v-3548e996]{color:#9a9a9a}.cart-wrap .invalid-goods .goods-wrap[data-v-3548e996]{padding-left:%?80?%}.cart-bottom[data-v-3548e996]{position:fixed;z-index:5;width:100vw;height:%?120?%;bottom:%?110?%;background:#fff;overflow:hidden;display:flex;justify-content:space-between}.cart-bottom .all-election[data-v-3548e996]{height:%?120?%;position:relative;padding-left:%?20?%;display:inline-block}.cart-bottom .all-election > .iconfont[data-v-3548e996]{font-size:%?36?%;position:absolute;top:50%;left:%?24?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cart-bottom .all-election > uni-text[data-v-3548e996]{margin-left:%?56?%;line-height:%?120?%}.cart-bottom .settlement-info[data-v-3548e996]{flex:1;text-align:right;padding-right:%?20?%;line-height:%?120?%;position:relative}.cart-bottom .settlement-info .total-price[data-v-3548e996]{font-size:%?36?%;font-weight:700;margin-left:%?6?%}.cart-bottom .settlement-info .preferential-price[data-v-3548e996]{position:absolute;top:%?80?%;right:%?20?%;color:var(--custom-brand-color);font-size:%?22?%}.cart-bottom .operation-btn[data-v-3548e996]{width:%?200?%;height:%?80?%;line-height:%?80?%;border-radius:%?40?%;margin-top:%?20?%;margin-right:%?24?%;display:flex;justify-content:center;align-items:center}.cart-bottom .operation-btn uni-button[data-v-3548e996]{width:100%;line-height:%?74?%}.cart-bottom .delete-btn[data-v-3548e996]{height:%?80?%;float:right;width:%?200?%;line-height:%?80?%;text-align:center;font-size:%?28?%;color:#343434;border:%?2?% solid #9a9a9a;border-radius:%?40?%;margin-top:%?10?%;margin-right:%?24?%;box-sizing:border-box}.cart-empty[data-v-3548e996]{text-align:center;font-size:%?24?%;padding:%?80?% %?20?% %?80?% %?20?%}.cart-empty .empty[data-v-3548e996]{padding-top:0;padding-bottom:%?50?%;text-align:center}.cart-empty uni-image[data-v-3548e996]{margin:10px auto;width:%?220?%;height:%?120?%;display:block}.cart-empty uni-navigator[data-v-3548e996]{display:inline}.top-operationa[data-v-3548e996]{position:absolute;left:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}',""]),t.exports=e},31490:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-image",{staticClass:"mescroll-totop",class:[t.value?"mescroll-totop-in":"mescroll-totop-out"],attrs:{src:t.$util.img("public/static/youpin/to-top.png"),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toTopClick.apply(void 0,arguments)}}})},o=[]},"327f":function(t,e,a){"use strict";var i=a("a971"),o=a.n(i);o.a},"37cd":function(t,e,a){"use strict";a.r(e);var i=a("31490"),o=a("a157");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("e0b7");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"0d800a55",null,!1,i["a"],void 0);e["default"]=r.exports},"37ff":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},o=[]},"5e48":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-numbox",class:{small:"small"==t.size}},[a("v-uni-view",{staticClass:"uni-numbox__minus",class:{"uni-numbox--disabled":t.inputValue<=t.min||t.disabled,small:"small"==t.size},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("minus")}}},[t._v("-")]),a("v-uni-input",{staticClass:"uni-numbox__value",class:{small:"small"==t.size},attrs:{disabled:t.disabled,maxlength:String(this.max).length+1,type:"number"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t._onInput.apply(void 0,arguments)}},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}}),a("v-uni-view",{staticClass:"uni-numbox__plus",class:{"uni-numbox--disabled":t.inputValue>=t.max||t.disabled,small:"small"==t.size},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("plus")}}},[t._v("+")])],1)},o=[]},"61fe":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.uni-numbox[data-v-41203276]{display:inline-flex;flex-direction:row;justify-content:flex-start;height:%?70?%;position:relative}.uni-numbox.small[data-v-41203276]{height:%?52?%}.uni-numbox[data-v-41203276]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border:1px solid #eee;border-radius:%?12?%;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox__minus[data-v-41203276],\r\n.uni-numbox__plus[data-v-41203276]{margin:0;background-color:#f8f8f8;width:%?70?%;font-size:%?40?%;height:100%;line-height:%?70?%;text-align:center;display:inline-flex;align-items:center;justify-content:center;color:#333;position:relative}.uni-numbox__value[data-v-41203276]{position:relative;background-color:#fff;width:%?80?%;height:100%;text-align:center;padding:0}.uni-numbox__minus.small[data-v-41203276],\r\n.uni-numbox__plus.small[data-v-41203276]{width:%?50?%;line-height:%?50?%}.uni-numbox__value.small[data-v-41203276]{width:%?60?%;font-size:%?28?%}.uni-numbox__value[data-v-41203276]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-style:solid;border-color:#eee;border-left-width:1px;border-right-width:1px;border-top-width:0;border-bottom-width:0;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox--disabled[data-v-41203276]{color:silver}',""]),t.exports=e},"66de":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("5c47"),a("0506");var i={name:"UniNumberBox",props:{type:{type:String},value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},modifyFlag:{type:Boolean,default:!1},size:{type:String,default:"default"},index:{type:Number,default:-1},minTips:{type:String,default:"最少购买一件哦"},maxTips:{type:String,default:"该商品不能购买更多哦！"}},data:function(){return{inputValue:0,oldNumber:0}},watch:{value:function(t){this.inputValue=+t}},created:function(){this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled&&!this.modifyFlag){var e=this._getDecimalScale(),a=this.inputValue*e,i=this.step*e;if("minus"===t?a-=i:"plus"===t&&(a+=i),a<this.min||a>this.max)return"cart"==this.type&&a<this.min&&this.$util.showToast({title:this.minTips}),void("cart"==this.type&&a>this.max&&this.$util.showToast({title:this.maxTips}));this.inputValue=a/e,this.$emit("change",this.inputValue)}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onInput:function(t){var e=this;setTimeout((function(){var a=t.detail.value;/(^[1-9]\d*$)/.test(a)||(a=e.min),!a||e.max<1?e.inputValue=1:(a=+a,a>e.max?a=e.max:a<e.min&&(a=e.min)),e.inputValue=a,e.oldNumber!==e.inputValue&&(e.$emit("change",e.inputValue),e.oldNumber=e.inputValue)}),0)}}};e.default=i},"6ad5":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{style:{backgroundColor:this.value.backgroundColor,height:2*this.value.height+"rpx"}})},o=[]},"7f2a":function(t,e,a){"use strict";a.r(e);var i=a("8617"),o=a("0a0a");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("0bd3");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"3548e996",null,!1,i["a"],void 0);e["default"]=r.exports},"7fcc":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{value:!0}},methods:{toTopClick:function(){this.$emit("toTop")}}}},8344:function(t,e,a){var i=a("ed85");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("338c05ca",i,!0,{sourceMap:!1,shadowMode:!1})},"84e9":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"diy-uni-popup",components:{},props:{isTitle:{type:Boolean,default:!0},title:{default:"提示"},text:[String],cancleText:{default:"取消"},confirmText:{default:"确定"}},data:function(){return{}},methods:{open:function(){this.$refs.popup.open()},closePopup:function(){this.$refs.popup.close()},cancle:function(){this.closePopup(),this.$emit("cancle")},confirm:function(){this.$emit("confirm")}}};e.default=i},8617:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uniIcons:a("de74").default,uniNumberBox:a("dadc").default,nsEmpty:a("dc6c").default,loadingCover:a("5510").default,diyBottomNav:a("dea9").default,nsGoodsSku:a("4d57").default,diyUniPopup:a("d9b3").default,nsLogin:a("4f5a").default,uniCouponPop:a("8765").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{class:t.themeStyle,style:[t.themeColorVar]},[a("v-uni-view",{staticClass:"nav bg-white",style:{height:t.navHeight+"px"}},[a("v-uni-view",{staticClass:"nav-title"},[t.cartData.length>0?a("v-uni-text",{staticClass:"top-operationa",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.isEdit=!t.isEdit}}},[t._v(t._s(t.isEdit?t.$lang("complete"):t.$lang("edit")))]):t._e(),a("v-uni-text",{staticClass:"cart_title"},[t._v("购物车")])],1)],1),a("v-uni-view",{staticClass:"container",class:t.isBottom?"bottom":"",style:{"padding-top":t.navHeight+"px"}},[t.isBottom?[t._l(t.cartData,(function(e,i){return a("v-uni-view",{key:i,staticClass:"cart-wrap"},[a("v-uni-view",{staticClass:"cart-header",staticStyle:{display:"none"}},[a("v-uni-view",{staticClass:"iconfont",class:e.checked?"iconyuan_checked ns-text-color":"iconyuan_checkbox",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.siteAllElection(!e.checked,i)}}}),a("v-uni-view",{staticClass:"iconfont icondianpu"}),a("v-uni-view",{staticClass:"shop-info"},[a("v-uni-text",[t._v(t._s(e.siteName))])],1)],1),t._l(e.cartList,(function(e,o){return[a("v-uni-view",{key:o+"_0",staticClass:"cart-goods",attrs:{"data-siteIndex":i,"data-cartIndex":o},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchS.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchM.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"goods-wrap",class:{show:t.selectSiteIndex==i&&t.selectCartIndex==o}},[a("v-uni-view",{staticClass:"iconfont",class:e.checked?"iconyuan_checked ns-text-color":"iconyuan_checkbox",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.singleElection(i,o)}}}),a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goProductDetail(e.sku_id)}}},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i,o)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goProductDetail(e.sku_id)}}},[t._v(t._s(e.sku_name))]),e.spec_name?a("v-uni-view",{staticClass:"sku",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.changeCart(e)}}},[t._v(t._s(e.spec_name)),a("uni-icons",{staticClass:"arrow-bottom",attrs:{type:"arrowdown",color:"#333",size:"12"}})],1):t._e(),a("v-uni-view",{staticClass:"goods-sub-section",class:{"goods-sub-section-shopper":t.is_shopper&&"0.00"!=e.shop_owner_brokerage}},[a("v-uni-view",{staticClass:"goods-sub-section-left"},[a("v-uni-view",{staticClass:"goods-price ns-text-color"},[a("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(t.is_shopper?e.vip_price:e.price_format))],1),t.is_shopper&&"0.00"!=e.shop_owner_brokerage?a("v-uni-view",{staticClass:"price-distributor"},[a("v-uni-text",[t._v("分销已减￥"+t._s(e.shop_owner_brokerage))])],1):t._e()],1),a("v-uni-view",{staticClass:"cart-number",staticStyle:{float:"right"}},[a("uni-number-box",{attrs:{min:1,max:e.stock,type:"cart",value:e.num,size:"small",modifyFlag:t.modifyFlag},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.cartNumChange(e,{siteIndex:i,cartIndex:o})}}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"item-operation",class:{show:t.selectSiteIndex==i&&t.selectCartIndex==o}},[a("v-uni-view",{staticClass:"item-del ns-bg-color",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPopup(i,o)}}},[t._v(t._s(t.$lang("del")))])],1)],1)]}))],2)})),t.invalidGoods.length?a("v-uni-view",{staticClass:"cart-wrap"},[a("v-uni-view",{staticClass:"cart-header"},[a("v-uni-view",{staticClass:"shop-info"},[t._v("失效商品")]),a("v-uni-view",{staticClass:"cart-operation ns-text-color ns-font-size-sm",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearInvalidGoods.apply(void 0,arguments)}}},[t._v("清空失效商品")])],1),t._l(t.invalidGoods,(function(e,i){return[a("v-uni-view",{key:i+"_0",staticClass:"cart-goods invalid-goods"},[a("v-uni-view",{staticClass:"invalid-mark"},[t._v("失效")]),a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img"},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFit"}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))])],1)],1)],1)]}))],2):t._e()]:[a("v-uni-view",{staticClass:"cart-empty"},[""!=t.token?a("ns-empty",{attrs:{entrance:"cart",text:t.$lang("emptyTips"),fixed:!1}}):a("ns-empty",{attrs:{text:t.$lang("emptyTips"),emptyBtn:t.emptyBtn1,fixed:!1}})],1)],a("v-uni-view",[a("nsGoodsRecommend",{ref:"goodrecommend"})],1)],2),t.cartData.length||t.invalidGoods.length?a("v-uni-view",{staticClass:"cart-bottom"},[a("v-uni-view",{staticClass:"all-election",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.allElection.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"iconfont",class:t.checkAll?"iconyuan_checked ns-text-color":"iconyuan_checkbox"}),a("v-uni-text",[t._v(t._s(t.$lang("allElection")))])],1),t.isEdit?t._e():a("v-uni-view",{staticClass:"settlement-info"},[a("v-uni-text",[t._v(t._s(t.$lang("total"))+"："),a("v-uni-text",{staticClass:"ns-text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))),a("v-uni-text",{staticClass:"total-price"},[t._v(t._s(t.totalPrice))])],1)],1),t.is_shopper&&"0.00"!=t.shopOwnerBrokerage?a("v-uni-view",{staticClass:"preferential-price"},[t._v("分销合计优惠￥"+t._s(t.shopOwnerBrokerage))]):t._e()],1),t.isEdit?t._e():a("v-uni-view",{staticClass:"operation-btn"},[0!=t.totalCount?a("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.settlement.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("settlement"))+"("+t._s(t.totalCount)+")")]):a("v-uni-button",{attrs:{type:"primary",size:"mini",disabled:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.settlement.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("settlement"))+"("+t._s(t.totalCount)+")")])],1),t.isEdit?a("v-uni-view",{staticClass:"delete-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPopup.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("del")))]):t._e()],1):t._e(),a("loading-cover",{ref:"loadingCover"}),t.openBottomNav?a("diy-bottom-nav",{attrs:{type:"shop","site-id":t.shop_id}}):t._e(),t.showTop?a("to-top",{on:{toTop:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollToTopNative()}}}):t._e(),a("ns-goods-sku",{ref:"goodsSku",attrs:{entrance:"cart","goods-detail":t.selectCartItem},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshGoodsSkuDetail.apply(void 0,arguments)}}}),a("diy-uni-popup",{ref:"popup",attrs:{text:"确定要从购物车删除该商品吗？",cancleText:"我再想想"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteCart(t.siteIndex,t.cartIndex)}}}),a("ns-login",{ref:"login"}),a("uni-coupon-pop",{ref:"couponPop"})],1)},n=[]},"8f68":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-65e5b6d2]{width:100%;text-align:center}@font-face{font-family:uniicons;src:url(https://www.xianmai88.com/static/youpin/uni.ttf) format("truetype")}.uni-icons[data-v-65e5b6d2]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},9127:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},a157:function(t,e,a){"use strict";a.r(e);var i=a("7fcc"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},a8f1:function(t,e,a){var i=a("61fe");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("4e4f523b",i,!0,{sourceMap:!1,shadowMode:!1})},a971:function(t,e,a){var i=a("8f68");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("2d38abe0",i,!0,{sourceMap:!1,shadowMode:!1})},b8ea:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var o=i(a("9127")),n={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:o.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=n},d214:function(t,e,a){var i=a("d9fd");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("471b92d1",i,!0,{sourceMap:!1,shadowMode:!1})},d9b3:function(t,e,a){"use strict";a.r(e);var i=a("e724"),o=a("e865");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("edd6");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"5a643cc4",null,!1,i["a"],void 0);e["default"]=r.exports},d9fd:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* 回到顶部的按钮 */.mescroll-totop[data-v-0d800a55]{z-index:9990;position:fixed!important; /* 加上important避免编译到H5,在多mescroll中定位失效 */right:%?0?%!important;bottom:%?272?%!important;width:%?144?%;height:%?146?%;border-radius:50%;opacity:0;transition:opacity .5s; /* 过渡 */margin-bottom:var(--window-bottom) /* css变量 */}\r\n/* 适配 iPhoneX */.mescroll-safe-bottom[data-v-0d800a55]{margin-bottom:calc(var(--window-bottom) + constant(safe-area-inset-bottom)); /* window-bottom + 适配 iPhoneX */margin-bottom:calc(var(--window-bottom) + env(safe-area-inset-bottom))}\r\n/* 显示 -- 淡入 */.mescroll-totop-in[data-v-0d800a55]{opacity:1}\r\n/* 隐藏 -- 淡出且不接收事件*/.mescroll-totop-out[data-v-0d800a55]{opacity:0;pointer-events:none}",""]),t.exports=e},da0b:function(t,e,a){"use strict";a.r(e);var i=a("15e0"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},dadc:function(t,e,a){"use strict";a.r(e);var i=a("5e48"),o=a("f393");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("e651");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"41203276",null,!1,i["a"],void 0);e["default"]=r.exports},de74:function(t,e,a){"use strict";a.r(e);var i=a("37ff"),o=a("fefc");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("327f");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"65e5b6d2",null,!1,i["a"],void 0);e["default"]=r.exports},e0b7:function(t,e,a){"use strict";var i=a("d214"),o=a.n(i);o.a},e651:function(t,e,a){"use strict";var i=a("a8f1"),o=a.n(i);o.a},e724:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("5e99").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("uni-popup",{ref:"popup"},[a("v-uni-view",{staticClass:"uni-custom"},[a("v-uni-view",{staticClass:"uni-popup__wrapper-box"},[a("v-uni-view",{staticClass:"popup-dialog"},[t.isTitle?a("v-uni-view",{staticClass:"popup-dialog-header"},[t._v(t._s(t.title))]):t._e(),a("v-uni-view",{staticClass:"popup-dialog-body"},[a("v-uni-rich-text",{attrs:{nodes:t.text}})],1),a("v-uni-view",{staticClass:"popup-dialog-footer"},[a("v-uni-view",{staticClass:"button white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v(t._s(t.cancleText))]),a("v-uni-button",{staticClass:"button red",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t._v(t._s(t.confirmText))])],1)],1)],1)],1)],1)},n=[]},e865:function(t,e,a){"use strict";a.r(e);var i=a("84e9"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},ecbc:function(t,e,a){"use strict";a.r(e);var i=a("6ad5"),o=a("da0b");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"1c0b3d32",null,!1,i["a"],void 0);e["default"]=r.exports},ed85:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-5a643cc4] .uni-popup__wrapper-box{max-width:%?540?%;width:%?540?%;border-radius:%?20?%;background:none}.popup-dialog[data-v-5a643cc4]{overflow:hidden;background:#fff;box-sizing:border-box}.popup-dialog .popup-dialog-header[data-v-5a643cc4]{height:%?106?%;line-height:%?106?%;text-align:center;font-size:%?36?%;color:#333;font-weight:700}.popup-dialog-body[data-v-5a643cc4]{color:#656565;text-align:center;padding:0 %?30?%}.popup-dialog-footer[data-v-5a643cc4]{margin:0 %?32?%;height:%?140?%;display:flex;align-items:center;justify-content:space-around}.popup-dialog-footer .button[data-v-5a643cc4]{width:%?220?%;height:%?68?%;line-height:%?68?%;text-align:center;border-radius:%?34?%;box-sizing:border-box;margin:0}.popup-dialog-footer .button.white[data-v-5a643cc4]{color:var(--custom-brand-color);background:#fff;border:1px solid var(--custom-brand-color)}.popup-dialog-footer .button.red[data-v-5a643cc4]{color:#fff;background:var(--custom-brand-color)}",""]),t.exports=e},edd6:function(t,e,a){"use strict";var i=a("8344"),o=a.n(i);o.a},f393:function(t,e,a){"use strict";a.r(e);var i=a("66de"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},feda:function(t,e,a){var i=a("2e35");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("ab7cf996",i,!0,{sourceMap:!1,shadowMode:!1})},fefc:function(t,e,a){"use strict";a.r(e);var i=a("b8ea"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a}}]);