(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["otherpages-live-list-list"],{"49a58":function(e,t,i){"use strict";i.r(t);var a=i("f437"),n=i("f551");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("ab3d");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"6331d048",null,!1,a["a"],void 0);t["default"]=s.exports},7477:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-6331d048]{width:100%;text-align:center}.live-wrap[data-v-6331d048]{margin:%?20?%;background:#fff;border-radius:%?16?%;overflow:hidden}.banner-wrap[data-v-6331d048]{width:100%;position:relative;line-height:1;display:flex}.banner-wrap uni-image[data-v-6331d048]{width:100%}.banner-wrap .shade[data-v-6331d048]{width:100%;height:100%;position:absolute;background:hsla(0,0%,53.3%,.3);left:0;top:0;z-index:5}.banner-wrap .wrap[data-v-6331d048]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:10;padding:%?30?%;box-sizing:border-box}.banner-wrap .wrap .room-name[data-v-6331d048]{font-size:%?32?%;color:#fff;line-height:1;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:flex;align-items:center}.banner-wrap .wrap .room-name .status-name[data-v-6331d048]{display:inline-block;font-size:%?20?%;color:#fff;padding:%?8?% %?20?%;background-color:rgba(0,0,0,.6);border-radius:%?36?%;margin-right:%?20?%}.banner-wrap .wrap .room-name .status-name .iconzhibozhong[data-v-6331d048]{font-size:%?20?%;color:#fff;margin-right:%?4?%}.room-info[data-v-6331d048]{padding:%?20?% %?30?% 0 %?30?%;background:#fff;display:flex}.room-info .anchor-img[data-v-6331d048]{width:%?60?%;height:%?60?%;border-radius:50%;overflow:hidden;margin-right:%?20?%}.room-info .anchor-name[data-v-6331d048], .room-info .goods-text[data-v-6331d048]{font-size:%?28?%;line-height:%?60?%}.room-info .separate[data-v-6331d048]{color:grey;margin:0 %?10?%;line-height:%?56?%}.time[data-v-6331d048]{padding:%?20?% %?30?%;background:#fff;line-height:1}.time .separate[data-v-6331d048]{color:grey;margin:0 %?10?%;line-height:1}',""]),e.exports=t},ab3d:function(e,t,i){"use strict";var a=i("bf3d"),n=i.n(a);n.a},bf3d:function(e,t,i){var a=i("7477");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("9aa9f8f2",a,!0,{sourceMap:!1,shadowMode:!1})},d552:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{liveList:[],isIndex:!1,showEmpty:!1,siteId:0}},onLoad:function(e){e.site_id&&(this.siteId=e.site_id)},onShow:function(){this.$langConfig.refresh(),this.liveList=[],this.$refs.mescroll&&this.$refs.mescroll.refresh()},computed:{themeStyle:function(){return"theme-"+this.$store.state.themeStyle}},methods:{getListData:function(e){var t=this;this.showEmpty=!1;var i={page:e.num,page_size:e.size};this.siteId&&(i.site_id=this.siteId),this.$api.sendRequest({url:"/live/api/live/page",data:i,success:function(i){t.showEmpty=!0;var a=[],n=i.message;0==i.code&&i.data?a=i.data.list:t.$util.showToast({title:n}),e.endSuccess(a.length),1==e.num&&(t.liveList=[]),t.liveList=[{a:1}],t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(i){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},entryRoom:function(e){},imageError:function(e){this.liveList[e].anchor_img=this.$util.getDefaultImage().default_headimg},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},onShareAppMessage:function(e){var t=this.getSharePageParams(),i=t.title,a=t.link,n=t.imageUrl;t.query;return this.$buriedPoint.pageShare(a,n,i)}}},f437:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={nsEmpty:i("dc6c").default},n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"live-content",class:this.themeStyle},[t("ns-empty",{attrs:{text:"直播仅支持在微信小程序内查看",isIndex:this.isIndex}})],1)},r=[]},f551:function(e,t,i){"use strict";i.r(t);var a=i("d552"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a}}]);