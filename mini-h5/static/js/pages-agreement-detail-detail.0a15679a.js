(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-agreement-detail-detail"],{"0d80":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("7c8d")),a={components:{},data:function(){return{id:0,content:""}},onLoad:function(t){t.id&&(this.id=t.id),this.getAggrementInfo()},mounted:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},methods:{getAggrementInfo:function(){var t=this;this.id&&this.$api.sendRequest({url:r.default.aggrementInfoUrl,data:{id:this.id},success:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide(),uni.setNavigationBarTitle({title:e.data.title}),t.content=e.data.content},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getSharePageParams:function(){return this.$util.unifySharePageParams("/otherpages/shop/home/<USER>",this.shareTitle||"先迈商城","",{},this.$util.img(this.shareImg)||this.$util.img("public/static/youpin/home_share.jpg"))}},computed:{},filters:{},onShareAppMessage:function(t){var e=this.getSharePageParams(),n=e.title,i=e.link,r=e.imageUrl;e.query;return this.$buriedPoint.pageShare(i,r,n)}};e.default=a},"4b93":function(t,e,n){var i=n("51df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("967d").default;r("1f1d0974",i,!0,{sourceMap:!1,shadowMode:!1})},"51df":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 文字基本颜色 */\r\n/* 文字尺寸 */.empty-list-text[data-v-3f7505f5]{width:100%;text-align:center}.agreement-detail[data-v-3f7505f5]{min-height:100vh;background-color:#fff;padding:%?30?%;color:#343434;font-size:%?28?%}',""]),t.exports=e},"5e4c":function(t,e,n){"use strict";n.r(e);var i=n("68ed1"),r=n("cec9");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("8bbf");var o=n("828b"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"3f7505f5",null,!1,i["a"],void 0);e["default"]=s.exports},"68ed1":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={loadingCover:n("5510").default},r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"agreement-detail"},[e("v-uni-rich-text",{attrs:{nodes:this.content}}),e("loading-cover",{ref:"loadingCover"})],1)},a=[]},"8bbf":function(t,e,n){"use strict";var i=n("4b93"),r=n.n(i);r.a},cec9:function(t,e,n){"use strict";n.r(e);var i=n("0d80"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a}}]);