<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 山西牛酷信息科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */
return [
	'template' => [
		[
			'name' => 'DIYVIEW_INDEX',
			'title' => '网站主页',
			'value' => '',
			'type' => 'ADMIN',
			'icon' => ''
		],
		[
			'name' => 'DIYVIEW_SHOP_INDEX',
			'title' => '店铺主页',
			'value' => '',
			'type' => 'SHOP',
			'icon' => ''
		],
	],
	'util' => [
		[
			'name' => 'TEXT',
			'title' => '文本',
			'type' => 'SYSTEM',
			'controller' => 'Text',
			'value' => '{ title : "『文本』", subTitle : "", textAlign : "left", backgroundColor : "", "link" : {},"fontSize" : 16 }',
			'sort' => '10000',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'TEXT_NAV',
			'title' => '文本导航',
			'type' => 'SYSTEM',
			'controller' => 'TextNav',
			'value' => '{ fontSize : 14, textColor : "#333333", textAlign : "left", backgroundColor : "", arrangement : "vertical", list : [{ text : "『文本导航』","link" : {}}] }',
			'sort' => '10001',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'NOTICE',
			'title' => '公告',
			'type' => 'SYSTEM',
			'controller' => 'Notice',
			'value' => '{ "backgroundColor": "","textColor": "#333333","fontSize": 14,"list": [{"title": "公告","link": {}},{"title": "公告","link": {}}]}',
			'sort' => '10002',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'GRAPHIC_NAV',
			'title' => '图文导航',
			'type' => 'SYSTEM',
			'controller' => 'GraphicNav',
			'value' => '{ "textColor": "#666666","backgroundColor": "","selectedTemplate": "imageNavigation","scrollSetting": "fixed","imageScale": 100,padding : 0,"list": [{"imageUrl": "","title": "","link": {}},{"imageUrl": "","title": "","link": {}},{"imageUrl": "","title": "","link": {}},{"imageUrl": "","title": "","link": {}}]}',
			'sort' => '10003',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'IMAGE_ADS',
			'title' => '图片广告',
			'type' => 'SYSTEM',
			'controller' => 'ImageAds',
			'value' => '{ selectedTemplate : "carousel-posters", imageClearance : 0, padding : 0, height : 0, list : [ { imageUrl : "", title : "", "link" : {}} ] }',
			'sort' => '10004',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'SEARCH',
			'title' => '商品搜索',
			'type' => 'SYSTEM',
			'controller' => 'Search',
			'value' => '{backgroundColor: ""}',
			'sort' => '10005',
			'support_diy_view' => '',
			'max_count' => 1
		],
		[
			'name' => 'TITLE',
			'title' => '顶部标题',
			'type' => 'SYSTEM',
			'controller' => 'Title',
			'value' => '{ "title": "『顶部标题』","backgroundColor": "","textColor": "#000000","isOpenOperation" : false,"leftLink" : {},"rightLink" : {},"operation_name" : "操作","fontSize" : 16}',
			'sort' => '10006',
			'support_diy_view' => '',
			'max_count' => 1
		],
		[
			'name' => 'RICH_TEXT',
			'title' => '富文本',
			'type' => 'SYSTEM',
			'controller' => 'RichText',
			'value' => '{ "html" : "" }',
			'sort' => '10007',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'RUBIK_CUBE',
			'title' => '魔方',
			'type' => 'SYSTEM',
			'controller' => 'RubikCube',
			'value' => '{"backgroundColor":"", "selectedTemplate": "row1-of2","list": [{ imageUrl : "", link : {} },{ imageUrl : "", link : {} }], "selectedRubikCubeArray" : [] ,"diyHtml": "","customRubikCube": 4,"heightArray": ["74.25px","59px","48.83px","41.56px"],"imageGap": 0}',
			'sort' => '10008',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'CUSTOM_MODULE',
			'title' => '自定义模块',
			'type' => 'SYSTEM',
			'controller' => '',
			'value' => '',
			'sort' => '10009',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'POP_WINDOW',
			'title' => '弹窗广告',
			'type' => 'SYSTEM',
			'controller' => 'PopWindow',
			'value' => '{ "image_url":"","link":{}}',
			'sort' => '10010',
			'support_diy_view' => '',
			'max_count' => 1
		],
		[
			'name' => 'HORZ_LINE',
			'title' => '辅助线',
			'type' => 'SYSTEM',
			'controller' => 'HorzLine',
			'value' => '{ color : "#e5e5e5", padding : "no-padding", borderStyle : "solid" }',
			'sort' => '10011',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'HORZ_BLANK',
			'title' => '辅助空白',
			'type' => 'SYSTEM',
			'controller' => 'HorzBlank',
			'value' => '{ height : 10, backgroundColor : "" }',
			'sort' => '10012',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'VIDEO',
			'title' => '视频',
			'type' => 'SYSTEM',
			'controller' => '',
			'value' => '',
			'sort' => '10013',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'VOICE',
			'title' => '语音',
			'type' => 'SYSTEM',
			'controller' => '',
			'value' => '',
			'sort' => '10014',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'GOODS_LIST',
			'title' => '商品列表',
			'type' => 'SYSTEM',
			'controller' => 'GoodsList',
			'value' => '{ "sources" : "default", skuId : "", "categoryId" : 0, "goodsCount" : "6" }',
			'sort' => '10016',
			'support_diy_view' => '',
			'max_count' => 0
		],
		[
			'name' => 'SHOP_INFO',
			'title' => '店铺信息',
			'type' => 'SYSTEM',
			'controller' => 'ShopInfo',
			'value' => '{ "color" : "#333333" }',
			'sort' => '10017',
			'support_diy_view' => 'DIY_VIEW_SHOP',
			'max_count' => 1
		],
		[
			'name' => 'RANK_LIST',
			'title' => '排行榜',
			'type' => 'SYSTEM',
			'controller' => 'RankList',
			'value' => '{ "sources" : "category", goodsId : "", "categoryId" : 0, "categoryLevel" : 1, "goodsCount" : "6" }',
			'sort' => '10018',
			'support_diy_view' => 'DIY_VIEW_SHOP',
			'max_count' => 1
		],
		[
			'name' => 'SHOP_SEARCH',
			'title' => '店内搜索',
			'type' => 'SYSTEM',
			'controller' => 'ShopSearch',
			'value' => '{}',
			'sort' => '10019',
			'support_diy_view' => 'DIY_VIEW_SHOP',
			'max_count' => 1
		],
		[
			'name' => 'SHOP_STORE',
			'title' => '门店',
			'type' => 'SYSTEM',
			'controller' => 'ShopStore',
			'value' => '{}',
			'sort' => '10020',
			'support_diy_view' => 'DIY_VIEW_SHOP',
			'max_count' => 1
		],
		[
			'name' => 'GOODS_CATEGORY',
			'title' => '商品分类',
			'type' => 'SYSTEM',
			'controller' => 'GoodsCategory',
			'value' => '{"level":"1","template":"1"}',
			'sort' => '10021',
			'support_diy_view' => '',
			'max_count' => 1
		],
	],
	'link' => [
		[
			'name' => 'INDEX',
			'title' => '主页',
			'wap_url' => '/pages/index/index/index',
			'web_url' => ''
		],
		[
			'name' => 'GOODS_CATEGORY',
			'title' => '商品分类',
			'wap_url' => '/pages/goods/category/category',
			'web_url' => '',
		],
		[
			'name' => 'GOODS_CART',
			'title' => '购物车',
			'wap_url' => '/pages/goods/cart/cart',
			'web_url' => '',
		],
		[
			'name' => 'LOGIN',
			'title' => '登录',
			'wap_url' => '/pages/login/login/login',
			'web_url' => ''
		],
		[
			'name' => 'REGISTER',
			'title' => '注册',
			'wap_url' => '/pages/login/register/register',
			'web_url' => ''
		],
		[
			'name' => 'MEMBER_INDEX',
			'title' => '会员中心',
			'wap_url' => '/pages/member/index/index',
			'web_url' => '',
		],
		[
			'name' => 'NOTICE_LIST',
			'title' => '公告列表',
			'wap_url' => '/otherpages/notice/list/list',
			'web_url' => '',
		],
		[
			'name' => 'HELP_LIST',
			'title' => '帮助中心',
			'wap_url' => '/otherpages/help/list/list',
			'web_url' => '',
		],
		[
			'name' => 'BRAND_LIST',
			'title' => '品牌专区',
			'wap_url' => '/otherpages/goods/brand/brand',
			'web_url' => '',
		],
		[
			'name' => 'POINT_INDEX',
			'title' => '积分商城',
			'wap_url' => '/promotionpages/point/list/list',
			'web_url' => '',
		],
		[
			'name' => 'COUPON_LIST',
			'title' => '领券中心',
			'wap_url' => '/otherpages/goods/coupon/coupon',
			'web_url' => '',
		],
		[
			'name' => 'TOPIC_LIST',
			'title' => '专题活动',
			'wap_url' => '/promotionpages/topics/list/list',
			'web_url' => '',
		],
		[
			'name' => 'SHOP_INDEX',
			'title' => '店铺首页',
			'wap_url' => '/otherpages/shop/index/index',
			'web_url' => '',
			'support_diy_view' => 'DIY_VIEW_SHOP',
		],
		[
			'name' => 'SHOP_INTRODUCE',
			'title' => '店铺介绍',
			'wap_url' => '/otherpages/shop/introduce/introduce',
			'web_url' => '',
			'support_diy_view' => 'DIY_VIEW_SHOP',
		],
		[
			'name' => 'SHOP_LIST',
			'title' => '店铺商品列表',
			'wap_url' => '/otherpages/shop/list/list',
			'web_url' => '',
			'support_diy_view' => 'DIY_VIEW_SHOP',
		],
		[
			'name' => 'SHOP_CATEGORY',
			'title' => '店铺商品分类',
			'wap_url' => '/otherpages/shop/category/category',
			'web_url' => '',
			'support_diy_view' => 'DIY_VIEW_SHOP',
		],
		[
			'name' => 'SHOP_LIST',
			'title' => '店铺街',
			'wap_url' => '/otherpages/shop/street/street',
			'web_url' => '',
			'support_diy_view' => '',
		],

	],
];