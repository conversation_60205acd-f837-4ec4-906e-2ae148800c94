<?php

/**
 * 亿企通支付配置
 */

return [
    'dev' => [
        'preUser' => 'dev_',
        'serverRoot' => 'https://openapi.yeepay.com/yop-center',
        // 'yosServerRoot' => 'https://yos.yeepay.com/yop-center',
        'appKey' => 'OPR:***********',
        //子商户对称密钥,可调密钥获取接口获取,下单生成hmac使用
        // 'hmacSecretKey' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhu27YkIVIBllM9/15PrWvX3MJ82do8UeX1shyNnRHCOx+M//J/qRu+tCu9X6DtZBVIDxY1OBguGckCtZQB4GTMB4yi6fPrcrwMtaZpBrFYqPd1CYNdjO46bdwf+rh00hldjk5zpFzeun7aNUuqR1jk8slPjVSzxe5pE9IpeIb3v9g8IzgqMOi4gVt+FAtGzuEk7pEf+Gcdvu0hls/RKuS3MndffJCe4cFFaKUcy8yRHrspQcVZrIskdsawlUlx2odk1YkYZnaNGgV96T+zp32sAZi54oOvAnnMZTyWxJ8b8EVZAdds9KhgXfMWhPWeoWzunwSlbRNXBMDwLaYe/iCQIDAQAB',// 签名
        'debug' => false,
        'connectTimeout' => 30000,
        'readTimeout' => 60000,
        'maxUploadLimit' => 4096000,
        'ENCODING' => 'UTF-8',
        'parentMerchantNo' => '***********',//父商编是*********** OPR:***********
        'merchantNo' => '***********',//商户号：***********
        'fundProcessType' => 'REAL_TIME',//资金处理类型， 可选值：DELAY_SETTLE("延迟结算"),REAL_TIME("实时订单");REAL_TIME_DIVIDE（” 实时 分账” ）SPLIT_ACCOUNT_IN("实时拆分入账");
        'redirectUrl' => '/m/order/yiqitongPaySyncNotify',
        'notifyUrl' => '/api/pay/yiqitongPayNotify',
        // 易宝私钥
        'yopPrivateKey' => 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCDCC2oVe6OYd8ZtuhW9AN8wV9bat5wz3rva5H8iPAv99VQkORANnh6l+a7RNVfN9w+Yii6UeavhSsulgicDUngJdCHaPIsuXRWt26ejSsLeHmxXnWPG2AObZcnyYzUzwZ4MiAWJ6RcRrF7BZGpAPkBGK0kLBeZ9e8Ko8SgRUXzVHmPjg8oF5vV0xMNDj92X0oZBVfzt0rOSqlGVWWgRkgIBz6CZKiy9pmLnKOnpG5qOOdiTdth+DsAR7ABK4lzkPeesAsR1VzP4EqW/TKC64YKhMA3N1ovfMC9EpQ2oCPwvairAsQcB/pvXxHBXttF/BTrTw/Ks9tkh2QMRBvZGHpfAgMBAAECggEABr/1GibTEyKXi4uQjGolg9eyQdNPgiAuBQdVjdzAAriRlITiPSyRKD+K8zqogy8teUk1L+PoLkJ95vhzmRZWJ+XKyC7vyr4C8DSizigXf4/FNQ3YoHaYjCW5E6OeTZgcjTSH0pxYKyi5G809o6cZLKVIxgQ/cv7oQXQOPPNUlyQ/aBl1c1cSDAWbyX7BDduqZmk5BPnyud9vtEOuKAQqFwPfy3/ZfkibilUYcvtNqRSUl/7VinZeAisSXPbKre2qk5ll/YXeavxkBZxdq6/JS5O4ivBtrQy9Fnil+7hBe6Qfw4Lt7Fv5NdObJIVwzq7cMTHGxnUaf3MNRpdkHvJsgQKBgQDrPt7vI5BuMvzM6wILXnxQ76quYPFE9nJ1glYPCpCAirKP5kAEjmH/2mJ4IqTi2uT5pgoPb0zGspL7R1tsJcSgGa98qEgyeG1n+6C9M2a+vmht8VTj0nrZeIIigQH2dF16K8c87H1jgg0N5VrjG+pRKG23dQ0rX4O0B+3MoHUN3QKBgQCOl5hCO2OVvillvvk0Wabll3ytWYZZRN/4COWtDaXY10RkpeBRyDZvAUE9Gyi/ZegfvTfZzV5gPnVFtXqbIEY8u0xD4MQSAuncY4V16cv70cvu4u3xGEZKgzgk8TOfPNxInCWUles6lP451x5B3HIAa63Ii1j3Qd0ceuI8iqT7awKBgQCh8M7Q+r+DTPBANItcvjeAE+yATFXqrmjOweFyS0h8ZH5VlyB8wnNuCKz+nIK7dApqXUXRqEHHCskp1850nW9E80md286Ph91w1oSpmkfhiPwkqxxQFOXi7RVQoVRzj1mGL7rhEr+ij7Vi2n99lgrwwY792sMtF3x3o3mtAsxxtQKBgAf5YFFr4tDP9p6zBFqyHMxAIX/MPuAlIuVLEhUQa1LqDvAV+qp4KNsiVdSl/Sxe9ZE40rPCcWGufH5ufLHKJ0NkMgqlujFLqmphwmfqsDaf7+inFilicyPdnLksJ/fivmrtGIjrrWD0ThdL+WwzeMifPPO3Hz2MmGHsWVSLaFiLAoGBANL7mp+y+J9Olx9LPjR14lanOg4PhnhIJ/CQt41WWgkEbSXfign0LaYwJQ2ly6y8KoaVPN/VeICTQ9RXsvIAwUmy0YB4hvRS6kfsdsP+9MWMooecsnsz+fUgY+Ff6pJL3dhnr0cPqiB0J0xH2gMD80i9QFUfaWAmLD7KvB1y3XA4',
        // 易宝公钥
        'yopPublicKey' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6p0XWjscY+gsyqKRhw9MeLsEmhFdBRhT2emOck/F1Omw38ZWhJxh9kDfs5HzFJMrVozgU+SJFDONxs8UB0wMILKRmqfLcfClG9MyCNuJkkfm0HFQv1hRGdOvZPXj3Bckuwa7FrEXBRYUhK7vJ40afumspthmse6bs6mZxNn/mALZ2X07uznOrrc2rk41Y2HftduxZw6T4EmtWuN2x4CZ8gwSyPAW5ZzZJLQ6tZDojBK4GZTAGhnn3bg5bBsBlw2+FLkCQBuDsJVsFPiGh/b6K/+zGTvWyUcu+LUj2MejYQELDO3i2vQXVDk7lVi2/TcUYefvIcssnzsfCfjaorxsuwIDAQAB',
        // 先迈私钥
        'parentPrivateKey' => 'MIIEpAIBAAKCAQEAhu27YkIVIBllM9/15PrWvX3MJ82do8UeX1shyNnRHCOx+M//J/qRu+tCu9X6DtZBVIDxY1OBguGckCtZQB4GTMB4yi6fPrcrwMtaZpBrFYqPd1CYNdjO46bdwf+rh00hldjk5zpFzeun7aNUuqR1jk8slPjVSzxe5pE9IpeIb3v9g8IzgqMOi4gVt+FAtGzuEk7pEf+Gcdvu0hls/RKuS3MndffJCe4cFFaKUcy8yRHrspQcVZrIskdsawlUlx2odk1YkYZnaNGgV96T+zp32sAZi54oOvAnnMZTyWxJ8b8EVZAdds9KhgXfMWhPWeoWzunwSlbRNXBMDwLaYe/iCQIDAQABAoIBAQCEHT6/JQleVr9bHc29GUcuX4CI8LGL67Sjla3s5in0LZdTkwW5dYVAxUigdxHTeUKapW613MwRBSuxlmBbgYqVmFgtT50pYVz796Faj8nqW7PCFi4Te2iIS5kTHW4smv9po+ft6Ib06+uPqb+KyQE6CJgUfdkLQ9+AT3HW7xzuWrEBd5kCrX1+NfuC9bM5YxDAkMd5uVMlge33y23gwUe8TlXC5jr2guWwGLbaTUUN7c1n/Kv0LFfewqcZhMctxUqy8S78lA7EAjNqYWqyTjBZ6OnT6ZUBpKRSC6rTzCZmGqN1ruPNcRwOSZOIGxa8Y0JgMutU4i1TmX8aqxhgT6VJAoGBAP+AiAGSKL6HRegF4/R6t3rdWwo/T8SwJ9015lM+MdnMedyLXR8dXoXd8Nt/izWYl9PMaeSzU9ghGFRL7nu2HGdhJL6Tm6rDjgAYwG/cpZwuY3Q0At1gaVsZzw6xYKkUAcS3AIGwsZijxRyIaDY2pM5lC1wNaFpAzR/uWM4fcix/AoGBAIcxDBVp/jWj0BMfQR3rig2AoirRsakLABnWYCBT0GI/xf0YHYRI+t5276gXU/g8fVXdA04PF0QkBhmEYV57WmAovz3emJDXdoRfz8LPoK4ZE3kpB1iAHr9V+TSMSvB+X1b2zh/eo6MX75OmegLQrpmds9zGLUn41LnWqPdyBU13AoGAGXYyl418N/h9E/E6VUvBayISB4Rlq6EuZ9IOQO9qwlypLDxLLGWd3NNQRF+CfPXW84lkaCEi0uABb60deACe9gJtSfylv6+7P/E3hC5F5rmMubDRNER+W1DIHJu6mT7gKj1edI4qNJ7lbsF9OSHa7KKPLj47zvq0NmFnQitKEnsCgYAHnjbtGfzuLt3xxegYdIZh/mak/Q4C1ZDa496k07RBZRCWE37OToJ8eL1GfpIHwfjaFssx4d3QxCrn0zLdbRwJoSItrVZxiwKtsOx3MNYAGX+kazBzxzx0UbbqRCGQ7b4Xm27AEv6rmRSyAEaN1A6Sr3VMMoqoyXHLSxJYZz9vPwKBgQClEZqbvoTbMwbPNC5/R56UNHALen35T2L6vCzIxscdeyYzrfS3bcLbKVHDmYHlnptRrH8ZKJ6nVYa8ktfxzZ1Xpbe/YOEjtDY5Sa+kVVE661e9rYvF3XKIF8NW6Ebid+V0ZhqbPgASY+WomeeCVLjZI/jk3RbIX3FFTnpdUfSKcQ==',
        //是否启用
        'pay_status'    => 1,
        //支持端注释
        'support_type'  => '微信小程序',
    ],
    'prod' => [
        'preUser' => 'prod_',
        'serverRoot' => 'https://openapi.yeepay.com/yop-center',
        // 'yosServerRoot' => 'https://yos.yeepay.com/yop-center',
        'appKey' => 'OPR:***********',
        //子商户对称密钥,可调密钥获取接口获取,下单生成hmac使用
        // 'hmacSecretKey' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhu27YkIVIBllM9/15PrWvX3MJ82do8UeX1shyNnRHCOx+M//J/qRu+tCu9X6DtZBVIDxY1OBguGckCtZQB4GTMB4yi6fPrcrwMtaZpBrFYqPd1CYNdjO46bdwf+rh00hldjk5zpFzeun7aNUuqR1jk8slPjVSzxe5pE9IpeIb3v9g8IzgqMOi4gVt+FAtGzuEk7pEf+Gcdvu0hls/RKuS3MndffJCe4cFFaKUcy8yRHrspQcVZrIskdsawlUlx2odk1YkYZnaNGgV96T+zp32sAZi54oOvAnnMZTyWxJ8b8EVZAdds9KhgXfMWhPWeoWzunwSlbRNXBMDwLaYe/iCQIDAQAB',// 签名
        'debug' => false,
        'connectTimeout' => 30000,
        'readTimeout' => 60000,
        'maxUploadLimit' => 4096000,
        'ENCODING' => 'UTF-8',
        'parentMerchantNo' => '***********',//父商编是*********** OPR:***********
        'merchantNo' => '***********',//商户号：***********
        'fundProcessType' => 'REAL_TIME',//资金处理类型， 可选值：DELAY_SETTLE("延迟结算"),REAL_TIME("实时订单");REAL_TIME_DIVIDE（” 实时 分账” ）SPLIT_ACCOUNT_IN("实时拆分入账");
        'redirectUrl' => '/m/order/yiqitongPaySyncNotify',
        'notifyUrl' => '/api/pay/yiqitongPayNotify',
        // 易宝私钥
        'yopPrivateKey' => 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCDCC2oVe6OYd8ZtuhW9AN8wV9bat5wz3rva5H8iPAv99VQkORANnh6l+a7RNVfN9w+Yii6UeavhSsulgicDUngJdCHaPIsuXRWt26ejSsLeHmxXnWPG2AObZcnyYzUzwZ4MiAWJ6RcRrF7BZGpAPkBGK0kLBeZ9e8Ko8SgRUXzVHmPjg8oF5vV0xMNDj92X0oZBVfzt0rOSqlGVWWgRkgIBz6CZKiy9pmLnKOnpG5qOOdiTdth+DsAR7ABK4lzkPeesAsR1VzP4EqW/TKC64YKhMA3N1ovfMC9EpQ2oCPwvairAsQcB/pvXxHBXttF/BTrTw/Ks9tkh2QMRBvZGHpfAgMBAAECggEABr/1GibTEyKXi4uQjGolg9eyQdNPgiAuBQdVjdzAAriRlITiPSyRKD+K8zqogy8teUk1L+PoLkJ95vhzmRZWJ+XKyC7vyr4C8DSizigXf4/FNQ3YoHaYjCW5E6OeTZgcjTSH0pxYKyi5G809o6cZLKVIxgQ/cv7oQXQOPPNUlyQ/aBl1c1cSDAWbyX7BDduqZmk5BPnyud9vtEOuKAQqFwPfy3/ZfkibilUYcvtNqRSUl/7VinZeAisSXPbKre2qk5ll/YXeavxkBZxdq6/JS5O4ivBtrQy9Fnil+7hBe6Qfw4Lt7Fv5NdObJIVwzq7cMTHGxnUaf3MNRpdkHvJsgQKBgQDrPt7vI5BuMvzM6wILXnxQ76quYPFE9nJ1glYPCpCAirKP5kAEjmH/2mJ4IqTi2uT5pgoPb0zGspL7R1tsJcSgGa98qEgyeG1n+6C9M2a+vmht8VTj0nrZeIIigQH2dF16K8c87H1jgg0N5VrjG+pRKG23dQ0rX4O0B+3MoHUN3QKBgQCOl5hCO2OVvillvvk0Wabll3ytWYZZRN/4COWtDaXY10RkpeBRyDZvAUE9Gyi/ZegfvTfZzV5gPnVFtXqbIEY8u0xD4MQSAuncY4V16cv70cvu4u3xGEZKgzgk8TOfPNxInCWUles6lP451x5B3HIAa63Ii1j3Qd0ceuI8iqT7awKBgQCh8M7Q+r+DTPBANItcvjeAE+yATFXqrmjOweFyS0h8ZH5VlyB8wnNuCKz+nIK7dApqXUXRqEHHCskp1850nW9E80md286Ph91w1oSpmkfhiPwkqxxQFOXi7RVQoVRzj1mGL7rhEr+ij7Vi2n99lgrwwY792sMtF3x3o3mtAsxxtQKBgAf5YFFr4tDP9p6zBFqyHMxAIX/MPuAlIuVLEhUQa1LqDvAV+qp4KNsiVdSl/Sxe9ZE40rPCcWGufH5ufLHKJ0NkMgqlujFLqmphwmfqsDaf7+inFilicyPdnLksJ/fivmrtGIjrrWD0ThdL+WwzeMifPPO3Hz2MmGHsWVSLaFiLAoGBANL7mp+y+J9Olx9LPjR14lanOg4PhnhIJ/CQt41WWgkEbSXfign0LaYwJQ2ly6y8KoaVPN/VeICTQ9RXsvIAwUmy0YB4hvRS6kfsdsP+9MWMooecsnsz+fUgY+Ff6pJL3dhnr0cPqiB0J0xH2gMD80i9QFUfaWAmLD7KvB1y3XA4',
        // 易宝公钥
        'yopPublicKey' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6p0XWjscY+gsyqKRhw9MeLsEmhFdBRhT2emOck/F1Omw38ZWhJxh9kDfs5HzFJMrVozgU+SJFDONxs8UB0wMILKRmqfLcfClG9MyCNuJkkfm0HFQv1hRGdOvZPXj3Bckuwa7FrEXBRYUhK7vJ40afumspthmse6bs6mZxNn/mALZ2X07uznOrrc2rk41Y2HftduxZw6T4EmtWuN2x4CZ8gwSyPAW5ZzZJLQ6tZDojBK4GZTAGhnn3bg5bBsBlw2+FLkCQBuDsJVsFPiGh/b6K/+zGTvWyUcu+LUj2MejYQELDO3i2vQXVDk7lVi2/TcUYefvIcssnzsfCfjaorxsuwIDAQAB',
        // 先迈私钥
        'parentPrivateKey' => 'MIIEpAIBAAKCAQEAhu27YkIVIBllM9/15PrWvX3MJ82do8UeX1shyNnRHCOx+M//J/qRu+tCu9X6DtZBVIDxY1OBguGckCtZQB4GTMB4yi6fPrcrwMtaZpBrFYqPd1CYNdjO46bdwf+rh00hldjk5zpFzeun7aNUuqR1jk8slPjVSzxe5pE9IpeIb3v9g8IzgqMOi4gVt+FAtGzuEk7pEf+Gcdvu0hls/RKuS3MndffJCe4cFFaKUcy8yRHrspQcVZrIskdsawlUlx2odk1YkYZnaNGgV96T+zp32sAZi54oOvAnnMZTyWxJ8b8EVZAdds9KhgXfMWhPWeoWzunwSlbRNXBMDwLaYe/iCQIDAQABAoIBAQCEHT6/JQleVr9bHc29GUcuX4CI8LGL67Sjla3s5in0LZdTkwW5dYVAxUigdxHTeUKapW613MwRBSuxlmBbgYqVmFgtT50pYVz796Faj8nqW7PCFi4Te2iIS5kTHW4smv9po+ft6Ib06+uPqb+KyQE6CJgUfdkLQ9+AT3HW7xzuWrEBd5kCrX1+NfuC9bM5YxDAkMd5uVMlge33y23gwUe8TlXC5jr2guWwGLbaTUUN7c1n/Kv0LFfewqcZhMctxUqy8S78lA7EAjNqYWqyTjBZ6OnT6ZUBpKRSC6rTzCZmGqN1ruPNcRwOSZOIGxa8Y0JgMutU4i1TmX8aqxhgT6VJAoGBAP+AiAGSKL6HRegF4/R6t3rdWwo/T8SwJ9015lM+MdnMedyLXR8dXoXd8Nt/izWYl9PMaeSzU9ghGFRL7nu2HGdhJL6Tm6rDjgAYwG/cpZwuY3Q0At1gaVsZzw6xYKkUAcS3AIGwsZijxRyIaDY2pM5lC1wNaFpAzR/uWM4fcix/AoGBAIcxDBVp/jWj0BMfQR3rig2AoirRsakLABnWYCBT0GI/xf0YHYRI+t5276gXU/g8fVXdA04PF0QkBhmEYV57WmAovz3emJDXdoRfz8LPoK4ZE3kpB1iAHr9V+TSMSvB+X1b2zh/eo6MX75OmegLQrpmds9zGLUn41LnWqPdyBU13AoGAGXYyl418N/h9E/E6VUvBayISB4Rlq6EuZ9IOQO9qwlypLDxLLGWd3NNQRF+CfPXW84lkaCEi0uABb60deACe9gJtSfylv6+7P/E3hC5F5rmMubDRNER+W1DIHJu6mT7gKj1edI4qNJ7lbsF9OSHa7KKPLj47zvq0NmFnQitKEnsCgYAHnjbtGfzuLt3xxegYdIZh/mak/Q4C1ZDa496k07RBZRCWE37OToJ8eL1GfpIHwfjaFssx4d3QxCrn0zLdbRwJoSItrVZxiwKtsOx3MNYAGX+kazBzxzx0UbbqRCGQ7b4Xm27AEv6rmRSyAEaN1A6Sr3VMMoqoyXHLSxJYZz9vPwKBgQClEZqbvoTbMwbPNC5/R56UNHALen35T2L6vCzIxscdeyYzrfS3bcLbKVHDmYHlnptRrH8ZKJ6nVYa8ktfxzZ1Xpbe/YOEjtDY5Sa+kVVE661e9rYvF3XKIF8NW6Ebid+V0ZhqbPgASY+WomeeCVLjZI/jk3RbIX3FFTnpdUfSKcQ==',
        //是否启用
        'pay_status'    => 1,
        //支持端注释
        'support_type'  => '微信小程序'
    ],
];
