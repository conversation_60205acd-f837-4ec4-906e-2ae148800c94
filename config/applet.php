<?php
/**
 * 小程序配置文件
 */
return [
    'dev' => [
        'app_id' => 'wx30baa146131ed417',
        'app_secret' => 'd50bc874569ab556e5ecb3c997532bd4',
        'login_url' => 'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code',
        'notify_url' => '',
        'return_url' => '',
        'template_id' => 'QgH63Ao9KADnBvsB64mzc0PT-tn47jbS-SJORVqXF3g',
        'access_token_url' => 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s',
        'uniform_message' => 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s',
        'app_username' => 'gh_039d38216563', //小程序原始id
    ],
    'prod' => [
        'app_id' => 'wx87886602fa1416a7',
        'app_secret' => '3ff7699d20d770ae0713e40c01f594e5',
        'login_url' => 'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code',
        'notify_url' => '',
        'return_url' => '',
        'template_id' => 'XVqQj4w2-XRWiZEZ8s7Avfrtg1z22GzJTANKh5NV-V0',
        'access_token_url' => 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s',
        'uniform_message' => 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s',
        'app_username' => 'gh_949e4c92bf03', //小程序原始id
    ],
    //订阅消息相关配置
    'sub_config' => [
        //发送订阅消息地址
        'send_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s',
        //订阅消息模版id
        'template_id' => [
            // 1 审核结果通知
            1 => 'X6uoSRrozrGd-TLK95GT5_8AaMpVhq2xIe81ZVP4zdA',
            2 => '2321312',
        ],
        //模版id对应的数据排版样式 ['phrase2' =>['value' => '审核结果',],'date3' =>['value' => '2020-09-01',],'thing4' =>['value' => '分销客审核通过',],]
        'template_data' => [
            1 => [
                'phrase2',//审核状态  5个以内纯汉字
                'date3',//审核时间 例如：2019年10月1日，或：2019年10月1日 15:01
                'thing4'//内容 20个以内字符	可汉字、数字、字母或符号组合
            ],
            2 => [
                'aa'=>['value','color'],
                'bb'=>['value','color'],
                'cc'=>['value','color'],
                'dd'=>['value','color'],
            ],
        ]
    ],
    //小程序接口请求地址
    'api_url'=>[
        //生成获取二维码 正方形
        'RCodeUrl'=>'https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s',
        //生成获取小程序二维码
        'appRCodeUrl'=>'https://api.weixin.qq.com/wxa/getwxacode?access_token=%s',
        //生成获取小程序二维码 按场景 通过该接口生成的小程序码，永久有效，数量暂无限制
        'getUnlimitedUrl'=>'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s',
    ],

    'notice_scene'=>[
        'p42IeqvmbOoIg_GY4-lwiFRHBrbN0cvBRQRyGOeypbQ' => 'order_pay',   //订单支付
        'rpEO1rW_Gi96jDYOv8EadtsiTXDiLPh76iMOOBk3Ads' => 'order_refund',   //订单发起退款
        'OXuAe4dflByyZU23O5PX5TYmluMDdeEtxMqGbhvCId8' => 'order_refund_fail',   //订单退款失败
        'BMT7ut_X56U4nADHw-jUJmyHsrDNV4VvPB3p-EeC1YU' => 'order_refund_success',    //订单退款成功
        'd_9RIjlLfr0W9FMBu2oq_waD77dGwmB7-PiSkygT9Tw' => 'pintuan_fail',    //拼团失败
        '3C9Z2egCR_e6onJ_FCFG7JsCvBYvSwpLiu1Ih2LzFeY' => 'pintuan_wait',   //拼团待成团
        'S2p4O4oJ357dK8tVfoZ8Af9tFBPG9F-kaCL-NRiKvTU' => 'pintuan_success', //拼团成功
        'N4QXKC0NBlqb5hHuAGTA7kWhYSDIrWQwxZCZyAzuKho' => 'open_pintuan',  //开团成功
        'P_MazDWg_yBcgB__XlsM6XIc40eQ9syoAxmhik37-JM' => 'achievement',    //销售业绩提醒
        'GfuoKoBi5KsdwCK8SldiM2D8ZOkdJHgvCG7V69UFylM' => 'order_send',  //订单发货
        'hGkbA_JWdw1oduzLLb3op_uft-zCUFangQKk2M_Ayd0' => 'distribution_reward', //佣金到账
    ],
    'notice_scene_group'=>[
        //订单支付前（微信等第三方支付）
        'order_pay_before' => [
            'order_pay',
            'order_send',
        ],
        //订单支付前（余额迈豆等非第三方支付）
        'other_pay_before' => [
            'order_send',
        ],
        //开团支付前（微信等第三方支付）
        'open_pintuan_before' => [
            'open_pintuan',
            'pintuan_success',
            'pintuan_fail'
        ],
        //开团支付前（余额迈豆等非第三方支付）
        'other_pintuan_before' => [
            'pintuan_success',
            'pintuan_fail'
        ],
        'join_pintuan_before' => [
            'pintuan_wait',
            'pintuan_success',
            'pintuan_fail'
        ],
        'other_join_pintuan_before' => [
            'pintuan_fail'
        ],
        'order_refund_before' => [
            'order_refund',
            'order_refund_fail',
            'order_refund_success'
        ],
        'goodscoupon_group' => [
            'goodscoupon_send',
            'goodscoupon_timeout',
        ],
    ],
];
