<?php
/**
 * 企业微信配置文件
 */
return [
    'corp_id' => env('EASYWECHAT.ENTERPRISE_APP_ID','ww3eabee94147401c9'),
    'agent_id' => env('EASYWECHAT.ENTERPRISE_AGENT_ID',1000014), // 如果有 agend_id 则填写
    'secret'   => env('EASYWECHAT.ENTERPRISE_SECRET','D-8IRHRw36LYO12t9A6489zbMVlp_cpOi9eEisDB0iY'),

    // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
    'response_type' => 'array',

    'log' => [
        'level' => 'debug',
        'file' => app()->getRootPath() . 'runtime/easywechat_enterprise/' . date('Y-m-d') . '.log',
    ],

    // server config
    'token' => env('EASYWECHAT.CALLBACK_Token','WnEFH5r6t3Z'),
    'aes_key' => env('EASYWECHAT.CALLBACK_EncodingAESKey','bsiT2VNFmtVCwhBK119CbRuQoEC4rZICEL2vzNaCujH'),
    
    'oauth_redirect_url' => env("ONLINE_DOMAIN").(env("APP_ENV") == "prod" ? "" : env("YP_SERVER_PORT", "")."/") ,

    'kf_config' => [
        'agent_id' => env('EASYWECHAT.ENTERPRISE_AGENT_ID',1000014), // 如果有 agend_id 则填写
        'corp_id' => env('EASYWECHAT.ENTERPRISE_APP_ID','ww3eabee94147401c9'),
        'secret'   => 'qubhVdXa-pttGJ26R8FApyBOhLfulQz6iyfiKPifL88',
        'token' => 'Z3wJpfgfI8U',
        'aes_key' => 'iYO81EYLSfXMcN5gRit2OAITumBm3l7aOvp3BgSDeAW',
    ],

    'default_kf_id' => env('EASYWECHAT.DEFAULT_KF_ID','wkZnvBCwAA5FEmDV1ihbSSMp0vteEQGQ'), // 如果有 agend_id 则填写
];
