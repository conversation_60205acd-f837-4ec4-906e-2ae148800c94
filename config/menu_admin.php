<?php
// +----------------------------------------------------------------------
// | 平台端菜单设置
// +----------------------------------------------------------------------
return [
	[
		'name' => 'INDEX_ROOT',
		'title' => '概况',
		'url' => 'admin/index/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/admin/view/public/img/menu_icon/survey.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/survey_selected.png',
		'sort' => 1,
	],
	[
		'name' => 'GOODS_ROOT',
		'title' => '商品管理',
		'url' => 'admin/goods/lists',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 1,
		'is_icon' => 0,
		'picture' => 'app/admin/view/public/img/menu_icon/commodity_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/commodity_info_selected.png',
		'sort' => 2,
		'child_list' => [
			[
				'name' => 'GOODS_MANAGE',
				'title' => '商品列表',
				'url' => 'admin/goods/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/admin/view/public/img/menu_icon/commodity_manage.png',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'PHYSICAL_GOODS_LOCKUP',
						'title' => '违规下架',
						'url' => 'admin/goods/lockup',
						'sort' => 1,
						'is_show' => 0
					],
					[
						'name' => 'PHYSICAL_GOODS_VERIFY_ON',
						'title' => '审核通过',
						'url' => 'admin/goods/verifyon',
						'sort' => 2,
						'is_show' => 0
					],

				]
			],
			[
				'name' => 'GOODS_CATEGORY',
				'title' => '商品分类',
				'url' => 'admin/goodscategory/lists',
				'is_show' => 1,
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'GOODS_CATEGORY_ADD',
						'title' => '分类添加',
						'url' => 'admin/goodscategory/addcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_EDIT',
						'title' => '分类编辑',
						'url' => 'admin/goodscategory/editcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_DEL',
						'title' => '分类删除',
						'url' => 'admin/goodscategory/deletecategory',
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_ATTR',
				'title' => '商品类型',
				'url' => 'admin/goodsattr/lists',
				'is_show' => 1,
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'GOODS_ATTR_EDIT',
						'title' => '类型编辑',
						'url' => 'admin/goodsattr/editattr',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_ATTR_DEL',
						'title' => '类型删除',
						'url' => 'admin/goodsattr/deleteattr',
						'is_show' => 0
					]
				]
			],

			[
				'name' => 'GOODS_BRAND',
				'title' => '商品品牌',
				'url' => 'admin/goodsbrand/lists',
				'is_show' => 1,
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'GOODS_BRAND_ADD',
						'title' => '品牌添加',
						'url' => 'admin/goodsbrand/addbrand',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_BRAND_EDIT',
						'title' => '品牌编辑',
						'url' => 'admin/goodsbrand/editbrand',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_BRAND_DEL',
						'title' => '品牌删除',
						'url' => 'admin/goodsbrand/deletebrand',
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_EVALUATE',
				'title' => '商品评价',
				'url' => 'admin/goods/evaluatelist',
				'is_show' => 1,
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'GOODS_EVALULATE_DELETE',
						'title' => '评价删除',
						'url' => 'admin/goods/deleteevaluate',
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_INFORM',
				'title' => '商品举报',
				'url' => 'admin/inform/lists',
				'is_show' => 1,
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'GOODS_INFORM_LISTS',
						'title' => '举报列表',
						'url' => 'admin/inform/lists',
						'is_show' => 1,
						'sort' => 1,
					],
					[
						'name' => 'GOODS_INFORM_SUBJECTTYPE',
						'title' => '举报类型',
						'url' => 'admin/inform/subjecttype',
						'is_show' => 1,
						'sort' => 2,
					],
					[
						'name' => 'GOODS_INFORM_INFORMSUBJECT',
						'title' => '举报主题',
						'url' => 'admin/inform/subject',
						'is_show' => 1,
						'sort' => 3,
					],
					[
						'name' => 'GOODS_INFORM_SUBJECTTYPEADD',
						'title' => '添加类型',
						'url' => 'admin/inform/subjecttypeadd',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_SUBJECTADD',
						'title' => '添加主题',
						'url' => 'admin/inform/subjectadd',
						'is_show' => 0,

					], [
						'name' => 'GOODS_INFORM_DELETESUBJECT',
						'title' => '删除主题',
						'url' => 'admin/inform/deletesubject',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_DELETESUBJECTTYPE',
						'title' => '删除类型',
						'url' => 'admin/inform/deletesubjecttype',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_DELETESUBJECTTYPE',
						'title' => '举报详情',
						'url' => 'admin/inform/detail',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_EDITSUBJECTTYPE',
						'title' => '编辑类型',
						'url' => 'admin/inform/editsubjecttype',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_EDITSUBJECT',
						'title' => '编辑主题',
						'url' => 'admin/inform/editsubject',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_EDITSUBJECT',
						'title' => '主题详情',
						'url' => 'admin/inform/subjectinfo',
						'is_show' => 0,
					], [
						'name' => 'GOODS_INFORM_EDITSUBJECT',
						'title' => '类型详情',
						'url' => 'admin/inform/subjecttypeinfo',
						'is_show' => 0,
					],
					[
						'name' => 'GOODS_INFORM_EDITINFORM',
						'title' => '举报处理',
						'url' => 'admin/inform/editinform',
						'is_show' => 0,
					],

				]
			]
		]
	],
	[
		'name' => 'ORDER_ROOT',
		'title' => '订单管理',
		'url' => 'admin/order/lists',
		'parent' => '',
		'is_show' => 1,
		'sort' => 2,
		'picture' => 'app/admin/view/public/img/menu_icon/order_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/order_info_selected.png',
		'child_list' => [
			[
				'name' => 'ORDER_EXPRESS',
				'title' => '订单列表',
				'url' => 'admin/order/lists',
				'is_show' => 1,
				'sort' => 1,
				"child_list" => [
					[
						'name' => 'EXPRESS_ORDER_DETAIL',
						'title' => '订单详情',
						'url' => 'admin/order/detail',
						'is_show' => 0
					],
					[
						'name' => 'STORE_ORDER_DETAIL',
						'title' => '自提订单详情',
						'url' => 'admin/storeorder/detail',
						'is_show' => 0
					],
					[
						'name' => 'LOCAL_ORDER_DETAIL',
						'title' => '外卖订单详情',
						'url' => 'admin/localorder/detail',
						'is_show' => 0
					],
					[
						'name' => 'VIRTUAL_ORDER_DETAIL',
						'title' => '虚拟订单详情',
						'url' => 'admin/virtualorder/detail',
						'is_show' => 0
					],
				]
			],

			[
				'name' => 'ORDER_REFUND',
				'title' => '退款维权',
				'url' => 'admin/refund/lists',
				'is_show' => 1,
				'sort' => 5,
				"child_list" => [
					[
						'name' => 'ORDER_REFUND_DETAIL',
						'title' => '退款详情',
						'url' => 'admin/refund/detail',
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'ORDER_COMPLAIN',
				'title' => '平台维权',
				'url' => 'admin/complain/lists',
				'is_show' => 1,
				'sort' => 6,
				"child_list" => [
					[
						'name' => 'ORDER_COMPLAIN_DETAIL',
						'title' => '维权详情',
						'url' => 'admin/complain/detail',
						'is_show' => 0
					],
				]
			],
		],
	],
	[
		'name' => 'APPLET_ROOT',
		'title' => '网站设置',
		'url' => 'admin/diy/index',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/website_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/website_info_selected.png',
		'sort' => 254,
		'child_list' => [
			[
				'name' => 'WEBSITE_CONFIG',
				'title' => '网站设置',
				'url' => 'admin/help/helplist',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/website_set.png',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => [
					//                    [
					//                        'name' => 'SHOP_STYLE_CONFIG',
					//                        'title' => '商城风格',
					//                        'url' => 'admin/diy/style',
					//                        'is_show' => 1,
					//                        'is_control' => 1,
					//                        'is_icon' => 0,
					//                        'picture' => '',
					//                        'picture_selected' => '',
					//                        'sort' => 5,
					//                    ],
					[
						'name' => 'WEBSITE_HELP_MANAGE',
						'title' => '网站帮助',
						'url' => 'admin/help/helplist',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 6,
						'child_list' => [
							[
								'name' => 'WEBSITE_HELP',
								'title' => '帮助列表',
								'url' => 'admin/help/helplist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'WEBSITE_HELP_ADD',
								'title' => '添加帮助',
								'url' => 'admin/help/addhelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_EDIT',
								'title' => '编辑帮助',
								'url' => 'admin/help/edithelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_DELETE',
								'title' => '删除帮助',
								'url' => 'admin/help/deletehelp',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS',
								'title' => '帮助分类',
								'url' => 'admin/help/classlist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_ADD',
								'title' => '添加分类',
								'url' => 'admin/help/addclass',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_EDIT',
								'title' => '编辑分类',
								'url' => 'admin/help/editclass',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_HELP_CLASS_DELETE',
								'title' => '删除分类',
								'url' => 'admin/help/deleteclass',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'WEBSITE_NOTICE',
						'title' => '网站公告',
						'url' => 'admin/notice/index',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 7,
						'child_list' => [
							[
								'name' => 'WEBSITE_NOTICE_ADD',
								'title' => '添加公告',
								'url' => 'admin/notice/addnotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_EDIT',
								'title' => '编辑公告',
								'url' => 'admin/notice/editnotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_DELETE',
								'title' => '删除公告',
								'url' => 'admin/notice/deletenotice',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_NOTICE_TOP',
								'title' => '公告置顶',
								'url' => 'admin/notice/modifynoticetop',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'WEBSITE_ADV',
						'title' => '广告位管理',
						'url' => 'admin/adv/index',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 8,
						'child_list' => [
							[
								'name' => 'WEBSITE_ADV_POSITION_ADD',
								'title' => '添加广告位',
								'url' => 'admin/adv/addposition',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_ADV_POSITION_EDIT',
								'title' => '编辑广告位',
								'url' => 'admin/adv/editposition',
								'is_show' => 0,
							],
							[
								'name' => 'WEBSITE_ADV_POSITION',
								'title' => '广告位管理',
								'url' => 'admin/adv/index',
								'is_show' => 1,
							],
							[
								'name' => 'WEBSITE_ADV_LISTS',
								'title' => '广告管理',
								'url' => 'admin/adv/lists',
								'is_show' => 1,
								'child_list' => [
									[
										'name' => 'WEBSITE_ADV_ADD',
										'title' => '添加广告',
										'url' => 'admin/adv/addadv',
										'is_show' => 0,
									],
									[
										'name' => 'WEBSITE_ADV_EDIT',
										'title' => '编辑广告',
										'url' => 'admin/adv/editadv',
										'is_show' => 0,
									],
									[
										'name' => 'WEBSITE_ADV_DELETE',
										'title' => '删除广告',
										'url' => 'admin/adv/deleteadv',
										'is_show' => 0,
									],
								],
							],
						],
					]
				],
			],

            [
                'name' => 'WEBSITE_MID_CONFIG',
                'title' => '手机端设置',
                'url' => 'admin/diy/index',
                'is_show' => 1,
                'picture' => 'app/admin/view/public/img/menu_icon/website_set.png',
                'picture_selected' => '',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'WEBSITE_MID_INDEX',
                        'title' => '网站主页',
                        'url' => 'admin/diy/index',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [],
                    ],
                    [
                        'name' => 'WEBSITE_MID_GOODS_CATEGORY',
                        'title' => '分类页面',
                        'url' => 'admin/diy/goodscategory',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                        'child_list' => [],
                    ],
                    [
                        'name' => 'WEBSITE_MID_DIY_LISTS',
                        'title' => '微页面',
                        'url' => 'admin/diy/lists',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'WEBSITE_MID_DIY_EDIT',
                                'title' => '编辑自定义页面',
                                'url' => 'admin/diy/edit',
                                'is_show' => 0,
                            ],
                        ],
                    ],
                    [
                        'name' => 'WEBSITE_MID_DIY_BOTTOM_NAV',
                        'title' => '底部导航',
                        'url' => 'admin/diy/bottomnavdesign',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 4,
                    ],
                ]
            ],
			[
				'name' => 'PC_CONFIG',
				'title' => '电脑端设置',
				'url' => 'admin/pc/floor',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/website_set.png',
				'picture_selected' => '',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'PC_INDEX_FLOOR',
						'title' => '首页楼层',
						'url' => 'admin/pc/floor',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'PC_INDEX_FLOOR_EDIT',
								'title' => '楼层编辑',
								'url' => 'admin/pc/flooredit',
								'is_show' => 0,
							]
						],
					],
					[
						'name' => 'PC_HOT_SEARCH_WORDS',
						'title' => '热门搜索',
						'url' => 'admin/pc/hotsearchwords',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
						'child_list' => [],
					],
					[
						'name' => 'PC_DEFAULT_SEARCH_WORDS',
						'title' => '默认搜索',
						'url' => 'admin/pc/defaultsearchwords',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [],
					],
					[
						'name' => 'PC_FLOAT_LAYER',
						'title' => '首页浮层',
						'url' => 'admin/pc/floatlayer',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
						'child_list' => [],
					],
					[
						'name' => 'PC_NAV_LIST',
						'title' => '导航设置',
						'url' => 'admin/pc/navlist',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
						'child_list' => [
							[
								'name' => 'PC_NAV_ADD',
								'title' => '添加导航',
								'url' => 'admin/pc/addnav',
								'is_show' => 0,
								'sort' => 1,
							],
							[
								'name' => 'PC_NAV_EDIT',
								'title' => '编辑导航',
								'url' => 'admin/pc/editnav',
								'is_show' => 0,
								'sort' => 2,
							],
							[
								'name' => 'PC_NAV_DELETE',
								'title' => '编辑导航',
								'url' => 'admin/pc/deletenav',
								'is_show' => 0,
								'sort' => 3,
							],
						],
					],
					[
						'name' => 'PC_LINK_LIST',
						'title' => '友情链接',
						'url' => 'admin/pc/linklist',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 6,
						'child_list' => [
							[
								'name' => 'PC_LINK_ADD',
								'title' => '添加友情链接',
								'url' => 'admin/pc/addlink',
								'is_show' => 0,
								'sort' => 1,
							],
							[
								'name' => 'PC_LINK_EDIT',
								'title' => '编辑友情链接',
								'url' => 'admin/pc/editlink',
								'is_show' => 0,
								'sort' => 2,
							],
							[
								'name' => 'PC_LINK_DELETE',
								'title' => '删除友情链接',
								'url' => 'admin/pc/deletelink',
								'is_show' => 0,
								'sort' => 3,
							],
						],
					]
				],
			],
		],
	],
	[
		'name' => 'SHOP_ROOT',
		'title' => '店铺管理',
		'url' => 'admin/shop/lists',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/shop_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/shop_info_selected.png',
		'sort' => 3,
		'child_list' => [
			[
				'name' => 'SHOP_INDEX',
				'title' => '店铺管理',
				'url' => 'admin/shop/lists',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/goods_manage.png',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'SHOP_LIST',
						'title' => '店铺列表',
						'url' => 'admin/shop/lists',
						'is_show' => 1,
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'SHOP_DETAIL',
								'title' => '店铺详情',
								'url' => 'admin/shop/shopdetail',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_ADD',
								'title' => '店铺添加',
								'url' => 'admin/shop/addshop',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_BISIC_INFO',
								'title' => '基本信息',
								'url' => 'admin/shop/basicinfo',
								'is_show' => 1,
							],
							[
								'name' => 'SHOP_CERT_INFO',
								'title' => '认证信息',
								'url' => 'admin/shop/certinfo',
								'is_show' => 1,
							],
							[
								'name' => 'SHOP_SETTLEMENT_INFO',
								'title' => '结算账户',
								'url' => 'admin/shop/settlementinfo',
								'is_show' => 1,
							],
							[
								'name' => 'SHOP_ACCOUNT_INFO',
								'title' => '账户信息',
								'url' => 'admin/shop/accountinfo',
								'is_show' => 1,
							],
							[
								'name' => 'SHOP_LOCK',
								'title' => '店铺锁定',
								'url' => 'admin/shop/lockshop',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_UNLOCK',
								'title' => '店铺解锁',
								'url' => 'admin/shop/unlockshop',
								'is_show' => 0,
							],
						]
					],
					[
						'name' => 'SHOP_APPLY',
						'title' => '入驻申请',
						'url' => 'admin/shopapply/apply',
						'is_show' => 1,
						'picture' => '',
						'sort' => 6,
						'child_list' => [
							[
								'name' => 'SHOP_APPLY_DETAIL',
								'title' => '申请详情',
								'url' => 'admin/shopapply/applydetail',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_APPLY_EDIT',
								'title' => '支付信息',
								'url' => 'admin/shopapply/editapply',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_APPLY_PASS',
								'title' => '申请通过',
								'url' => 'admin/shopapply/applypass',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_APPLY_REJECT',
								'title' => '申请拒绝',
								'url' => 'admin/shopapply/applyreject',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_OPEN_SHOP',
								'title' => '入驻通过',
								'url' => 'admin/shopapply/openshop',
								'is_show' => 0,
							],
						]
					],
					[
						'name' => 'SHOP_REPLAY',
						'title' => '续签申请',
						'url' => 'admin/shopreopen/reopen',
						'is_show' => 1,
						'picture' => '',
						'sort' => 6,
						'child_list' => [
							[
								'name' => 'SHOP_REOPEN_DETAIL',
								'title' => '申请详情',
								'url' => 'admin/shopreopen/reopendetail',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_REOPEN_PASS',
								'title' => '申请通过',
								'url' => 'admin/shopreopen/reopenpass',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_REOPEN_FAIL',
								'title' => '申请失败',
								'url' => 'admin/shopreopen/reopenfail',
								'is_show' => 0,
							],
						]
					],
					[
						'name' => 'STORE_LIST',
						'title' => '门店列表',
						'url' => 'admin/store/lists',
						'is_show' => 1,
						'picture' => '',
						'sort' => 7,
						'child_list' => []
					],
					[
						'name' => 'SHOP_USER_LISTS',
						'title' => '商家列表',
						'url' => 'admin/shopuser/lists',
						'is_show' => 1,
						'picture' => '',
						'sort' => 8,
						'child_list' => []
					],
				],
			],
			[
				'name' => 'SHOP_JOIN',
				'title' => '入驻帮助',
				'url' => 'admin/shopjoin/adv',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/help_in.png',
				'picture_selected' => '',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'SHOP_JOIN_SHOPAGREEMENT',
						'title' => '入驻协议',
						'url' => 'admin/shopjoin/shopagreement',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_JOIN_GUIDE',
						'title' => '入驻指南',
						'url' => 'admin/shopjoin/guide',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
						'child_list' => [
							[
								'name' => 'SHOP_JOIN_GUIDE_LIST',
								'title' => '入驻指南',
								'url' => 'admin/shopjoin/guide',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'SHOP_JOIN_ADV',
								'title' => '入驻广告',
								'url' => 'admin/shopjoin/adv',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'SHOP_JOIN_GUIDE_EDIT',
								'title' => '修改指南',
								'url' => 'admin/shopjoin/editguide',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'SHOP_HELP_MANAGE',
						'title' => '入驻帮助',
						'url' => 'admin/shophelp/helplist',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [
							[
								'name' => 'SHOP_HELP',
								'title' => '帮助列表',
								'url' => 'admin/shophelp/helplist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],

							[
								'name' => 'SHOP_HELP_ADD',
								'title' => '添加帮助',
								'url' => 'admin/shophelp/addhelp',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_HELP_EDIT',
								'title' => '编辑帮助',
								'url' => 'admin/shophelp/edithelp',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_HELP_DELETE',
								'title' => '删除帮助',
								'url' => 'admin/shophelp/deletehelp',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_HELP_CLASS',
								'title' => '帮助分类',
								'url' => 'admin/shophelp/classlist',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
							[
								'name' => 'SHOP_HELP_CLASS_ADD',
								'title' => '添加分类',
								'url' => 'admin/shophelp/addclass',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_HELP_CLASS_EDIT',
								'title' => '编辑分类',
								'url' => 'admin/shophelp/editclass',
								'is_show' => 0,
							],
							[
								'name' => 'SHOP_HELP_CLASS_DELETE',
								'title' => '删除分类',
								'url' => 'admin/shophelp/deleteclass',
								'is_show' => 0,
							],
						],
					],
				],
			],
			[
				'name' => 'SHOP_GROUP',
				'title' => '开店套餐',
				'url' => 'admin/shopgroup/lists',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'SHOP_GROUP_ADD',
						'title' => '分组添加',
						'url' => 'admin/shopgroup/addgroup',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_GROUP_EDIT',
						'title' => '分组编辑',
						'url' => 'admin/shopgroup/editgroup',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_GROUP_DELETE',
						'title' => '分组删除',
						'url' => 'admin/shopgroup/deletegroup',
						'is_show' => 0,
					],
				],
			],
			[
				'name' => 'SHOP_CATEGORY',
				'title' => '主营行业',
				'url' => 'admin/shopcategory/lists',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'SHOP_CATEGORY_ADD',
						'title' => '行业添加',
						'url' => 'admin/shopcategory/addcategory',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_CATEGORY_EDIT',
						'title' => '行业编辑',
						'url' => 'admin/shopcategory/editcategory',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_CATEGORY_DELETE',
						'title' => '行业删除',
						'url' => 'admin/shopcategory/deletecategory',
						'is_show' => 0,
					],
				],
			],
			[
				'name' => 'SHOP_SERVICE',
				'title' => '消保服务',
				'url' => 'admin/shopservice/lists',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'SHOP_SERVICE_DETAIL',
						'title' => '申请详情',
						'url' => 'admin/shopservice/detail',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_SERVICE_PASS',
						'title' => '申请通过',
						'url' => 'admin/shopservice/pass',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_SERVICE_REJECT',
						'title' => '申请拒绝',
						'url' => 'admin/shopservice/reject',
						'is_show' => 0,
					],
				],
			],

		],
	],
	[
		'name' => 'MEMBER_ROOT',
		'title' => '会员管理',
		'url' => 'admin/member/memberlist',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/member_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/member_info_selected.png',
		'sort' => 3,
		'child_list' => [
			[
				'name' => 'MEMBER_INDEX',
				'title' => '会员列表',
				'url' => 'admin/member/memberlist',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/member_manage.png',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'MEMBER_LIST',
						'title' => '会员列表',
						'url' => 'admin/member/memberlist',
						'is_show' => 0,
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'MEMBER_ADD',
								'title' => '会员添加',
								'url' => 'admin/member/addmember',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_EDIT',
								'title' => '基础信息',
								'url' => 'admin/member/editmember',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_DELETE',
								'title' => '会员删除',
								'url' => 'admin/member/deletemember',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_ACCOUNT_DETAIL',
								'title' => '账户明细',
								'url' => 'admin/member/accountdetail',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_ORDER',
								'title' => '订单管理',
								'url' => 'admin/member/order',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_ADDRESS',
								'title' => '会员地址',
								'url' => 'admin/member/addressdetail',
								'is_show' => 1,
							],
							[
								'name' => 'MEMBER_DETAIL',
								'title' => '会员详情',
								'url' => 'admin/member/memberdetail',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_LABEL_MODIFY',
								'title' => '修改会员标签',
								'url' => 'admin/member/modifylabel',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_STATUS_MODIFY',
								'title' => '修改会员状态',
								'url' => 'admin/member/midifystatus',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_PASSWORD_MODIFY',
								'title' => '修改会员密码',
								'url' => 'admin/member/midifypassword',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_BALANCE_ADJUST',
								'title' => '余额调整',
								'url' => 'admin/member/adjustbalance',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_POINT_ADJUST',
								'title' => '积分调整',
								'url' => 'admin/member/adjustpoint',
								'is_show' => 0,
							],
							[
								'name' => 'MEMBER_GROWTH_ADJUST',
								'title' => '成长值调整',
								'url' => 'admin/member/adjustgrowth',
								'is_show' => 0,
							],
						]
					],
				],
			],
			/*[
				'name' => 'MEMBER_AUTH',
				'title' => '会员认证',
				'url' => 'admin/memberauth/lists',
				'is_show' => 0,
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'MEMBER_AUTH_DETAIL',
						'title' => '认证详情',
						'url' => 'admin/memberauth/detail',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_AUTH_PASS',
						'title' => '认证通过',
						'url' => 'admin/memberauth/pass',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_AUTH_REJECT',
						'title' => '认证拒绝',
						'url' => 'admin/memberauth/reject',
						'is_show' => 0,
					],

				]
			],*/
			/* 			[
							'name' => 'ACCOUNT_MEMBER',
							'title' => '会员账户',
							'url' => 'admin/memberaccount/point',
							'is_show' => 1,
							'picture' => 'app/admin/view/public/img/menu_icon/member_data.png',
							'sort' => 2,
							'child_list' => [
								[
									'name' => 'ACCOUNT_MEMBER_POINT',
									'title' => '会员积分',
									'url' => 'admin/memberaccount/point',
									'is_show' => 1,
								],
								[
									'name' => 'ACCOUNT_MEMBER_BALANCE',
									'title' => '会员余额',
									'url' => 'admin/memberaccount/balance',
									'is_show' => 1,
								],
								[
									'name' => 'ACCOUNT_MEMBER_GROWTH',
									'title' => '会员成长值',
									'url' => 'admin/memberaccount/growth',
									'is_show' => 1,
								],

							],
						], */
			[
				'name' => 'MEMBER_LEVEL',
				'title' => '会员等级',
				'url' => 'admin/memberlevel/levellist',
				'is_show' => 1,
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'MEMBER_LEVEL_ADD',
						'title' => '等级添加',
						'url' => 'admin/memberlevel/addlevel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LEVEL_EDIT',
						'title' => '等级修改',
						'url' => 'admin/memberlevel/editlevel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LEVEL_DELETE',
						'title' => '等级删除',
						'url' => 'admin/memberlevel/deletelevel',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'MEMBER_LABEL',
				'title' => '会员标签',
				'url' => 'admin/memberlabel/labellist',
				'is_show' => 1,
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'MEMBER_LABEL_ADD',
						'title' => '标签添加',
						'url' => 'admin/memberlabel/addlabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_EDIT',
						'title' => '标签修改',
						'url' => 'admin/memberlabel/editlabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_DELETE',
						'title' => '标签删除',
						'url' => 'admin/memberlabel/deletelabel',
						'is_show' => 0,
					],
					[
						'name' => 'MEMBER_LABEL_SORT_MODIFY',
						'title' => '修改排序',
						'url' => 'admin/memberlabel/modifysort',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'LOGIN_REG_AGREEMENT',
				'title' => '注册协议',
				'url' => 'admin/member/regagreement',
				'is_show' => 1,
				'picture' => '',
				'sort' => 5,
			],
		],
	],
	[
		'name' => 'PROMOTION_ROOT',
		'title' => '营销',
		'url' => 'admin/promotion/config',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/marketing_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/marketing_info_selected.png',
		'sort' => 6,
		'child_list' => [
			[
				'name' => 'PROMOTION_CONFIG',
				'title' => '营销中心',
				'url' => 'admin/promotion/config',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/marketing_center.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/marketing_center_selected.png',
				'sort' => 1,
				'child_list' => [],
			],
			[
				'name' => 'PROMOTION_SHOP',
				'title' => '店铺营销',
				'url' => 'admin/promotion/shop',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/shop_marketing.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/shop_marketing_selected.png',
				'sort' => 1,
				'child_list' => [],
			],
			[
				'name' => 'PROMOTION_PLATFORM',
				'title' => '平台营销',
				'url' => 'admin/promotion/platform',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/platform_marketing.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/platform_marketing_selected.png',
				'sort' => 1,
				'child_list' => [],
			],
			[
				'name' => 'PROMOTION_MEMBER',
				'title' => '会员营销',
				'url' => 'admin/promotion/member',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/affiliate_marketing.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/affiliate_marketing_selected.png',
				'sort' => 1,
				'child_list' => [],
			],
		],
	],
	[
		'name' => 'TOOL_ROOT',
		'title' => '应用',
		'url' => 'admin/promotion/tool',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/marketing_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/marketing_info_selected.png',
		'sort' => 7,
		'child_list' => [
			[
				'name' => 'PROMOTION_TOOL',
				'title' => '应用管理',
				'url' => 'admin/promotion/tool',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/application_tools.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/application_tools_selected.png',
				'sort' => 1,
				'child_list' => [],
			],
		],
	],
	[
		'name' => 'ACCOUNT_ROOT',
		'title' => '财务管理',
		'url' => 'admin/account/index',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/data_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/data_info_selected.png',
		'sort' => 8,
		'child_list' => [
			[
				'name' => 'ACCOUNT_INDEX',
				'title' => '财务概况',
				'url' => 'admin/account/index',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/data_survey.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/data_survey_selected.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'ACCOUNT_LIST',
						'title' => '平台抽成',
						'url' => 'admin/account/lists',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_BALANCE_LIST',
						'title' => '店铺余额',
						'url' => 'admin/account/shopbalance',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_DEPOSIT_LIST',
						'title' => '店铺保证金',
						'url' => 'admin/account/shopdeposit',
						'is_show' => 0,
					],
					[
						'name' => 'SHOP_FEE_LIST',
						'title' => '店铺申请',
						'url' => 'admin/account/shopapplylist',
						'is_show' => 0,
					],
					[
						'name' => 'ACCOUNT_REOPEN_LIST',
						'title' => '店铺续签费用',
						'url' => 'admin/account/shopreopenlist',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'ACCOUNT_SHOP_SETTLEMENT',
				'title' => '店铺结算',
				'url' => 'admin/shopsettlement/lists',
				'is_show' => 1,
				'child_list' => [
					[
						'name' => 'ACCOUNT_SHOP_SETTLEMENT_LIST',
						'title' => '结算列表',
						'url' => 'admin/shopsettlement/lists',
						'is_show' => 0,
					],
					[
						'name' => 'ACCOUNT_SHOP_SETTLEMENT_DETAIL',
						'title' => '结算详情',
						'url' => 'admin/shopsettlement/detail',
						'is_show' => 0,
					],
					[
						'name' => 'ACCOUNT_SHOP_SETTLEMENT_SHOPDETAIL',
						'title' => '店铺结算列表',
						'url' => 'admin/shopsettlement/shopdetail',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'ACCOUNT_SHOP_WITHDRAW',
				'title' => '店铺提现',
				'url' => 'admin/shopaccount/withdrawlist',
				'is_show' => 1,
			],
			[
				'name' => 'MEMBER_WITHDRAW_LIST',
				'title' => '会员提现',
				'url' => 'admin/memberwithdraw/lists',
				'is_show' => 1,
				'child_list' => [
					[
						'name' => 'MEMBER_WITHDRAW_DETAIL',
						'title' => '提现详情',
						'url' => 'admin/memberwithdraw/detail',
						'is_show' => 0,
					],
				]
			],
			[
				'name' => 'ACCOUNT_SHOP_FEE',
				'title' => '入驻费用',
				'url' => 'admin/shopaccount/fee',
				'is_show' => 1,
			],
			//			[
			//				'name' => 'ACCOUNT_SHOP_DEPOSIT',
			//				'title' => '店铺保证金',
			//				'url' => 'admin/shopaccount/deposit',
			//				'is_show' => 1,
			//			],
		],
	],
	[
		'name' => 'STAT_ROOT',
		'title' => '统计',
		'url' => 'admin/stat/index',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/statistics_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/statistics_info_selected.png',
		'sort' => 9,
		'child_list' => [
			[
				'name' => 'STAT_INDEX',
				'title' => '统计概况',
				'url' => 'admin/stat/index',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => []
			],
			[
				'name' => 'STAT_ORDER',
				'title' => '交易分析',
				'url' => 'admin/stat/order',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 2,
				'child_list' => []
			],
			[
				'name' => 'STAT_PRODUCT',
				'title' => '商品分析',
				'url' => 'admin/stat/goods',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 3,
				'child_list' => []
			],
			[
				'name' => 'STAT_MEMBER',
				'title' => '会员分析',
				'url' => 'admin/stat/member',
				'is_show' => 1,
				'picture' => '',
				'picture_selected' => '',
				'sort' => 4,
				'child_list' => []
			],
		],
	],
	[
		'name' => 'CONFIG_ROOT',
		'title' => '系统设置',
		'url' => 'admin/config/webconfig',
		'parent' => '',
		'is_show' => 1,
		'picture' => 'app/admin/view/public/img/menu_icon/set_info.png',
		'picture_selected' => 'app/admin/view/public/img/menu_icon/set_info_selected.png',
		'sort' => 255,
		'child_list' => [
			[
				'name' => 'CONFIG_BASE',
				'title' => '基础设置',
				'url' => 'admin/config/webconfig',
				'parent' => '',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/foundation_setup.png',
				'picture_selected' => '',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'CONFIG_WEB',
						'title' => '站点设置',
						'url' => 'admin/config/webconfig',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_USER',
						'title' => '管理员',
						'url' => 'admin/user/user',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
						'child_list' => [
							[
								'name' => 'CONFIG_USER_INDEX',
								'title' => '用户列表',
								'url' => 'admin/user/user',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'CONFIG_USER_ADD',
								'title' => '添加用户',
								'url' => 'admin/user/adduser',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 0,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_USER_EDIT',
								'title' => '编辑用户',
								'url' => 'admin/user/edituser',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 0,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_USER_GROUP',
								'title' => '用户组',
								'url' => 'admin/user/group',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_USER_GROUP_ADD',
								'title' => '添加用户组',
								'url' => 'admin/user/addgroup',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 0,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_USER_GROUP_EDIT',
								'title' => '编辑用户组',
								'url' => 'admin/user/editgroup',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_USER_GROUP_DELETE',
								'title' => '删除用户组',
								'url' => 'admin/user/deletegroup',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_MANAGE_USERLOG',
								'title' => '操作日志',
								'url' => 'admin/user/userlog',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 3,
								'child_list' => [],
							],
							[
								'name' => 'CONFIG_MANAGE_USERLOG_DELETE',
								'title' => '删除日志',
								'url' => 'admin/user/deleteuserlog',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],

						],
					],
					[
						'name' => 'COPYRIGHT',
						'title' => '版权设置',
						'url' => 'admin/config/copyright',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_PAY',
						'title' => '支付设置',
						'url' => 'admin/config/pay',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_UPLOAD',
						'title' => '上传设置',
						'url' => 'admin/upload/config',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
						'child_list' => [
							[
								'name' => 'CONFIG_UPLOAD_SET',
								'title' => '上传设置',
								'url' => 'admin/upload/config',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'UPLOAD_OSS',
								'title' => '云上传',
								'url' => 'admin/upload/oss',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
						],
					],
					[
						'name' => 'CONFIG_CAPTCHA',
						'title' => '验证码设置',
						'url' => 'admin/config/captcha',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_API',
						'title' => 'api安全',
						'url' => 'admin/config/api',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 6,
					],
					[
						'name' => 'CONFIG_DEFAULT_PICTURE',
						'title' => '默认图设置',
						'url' => 'admin/config/defaultPicture',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 7,
					],
					[
						'name' => 'MESSAGE_LISTS',
						'title' => '消息管理',
						'url' => 'admin/message/lists',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 8,
						'child_list' => [
							[
								'name' => 'MESSAGE_EMAIL_EDIT',
								'title' => '编辑邮件模板',
								'url' => 'admin/message/editEmailMessage',
								'parent' => '',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
                            [
                                'name' => 'MESSAGE_EMAIL_EDIT',
                                'title' => '编辑邮件模板',
                                'url' => 'admin/message/editEmailMessage',
                                'parent' => '',
                                'is_show' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [],
                            ],
							//                            [
							//                                'name' => 'MESSAGE_SMS_EDIT',
							//                                'title' => '编辑短信模板',
							//                                'url' => 'admin/message/editSmsMessage',
							//                                'parent' => '',
							//                                'is_show' => 0,
							//                                'picture' => '',
							//                                'picture_selected' => '',
							//                                'sort' => 1,
							//                                'child_list' => [
							//
							//                                ],
							//                            ],
						],
					],
					[
						'name' => 'SMS_MANAGE',
						'title' => '短信管理',
						'url' => 'admin/message/sms',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 9,
						'child_list' => [
							[
								'name' => 'SMS_LIST',
								'title' => '短信列表',
								'url' => 'admin/message/sms',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
							[
								'name' => 'SMS_RECORDS',
								'title' => '发送记录',
								'url' => 'admin/message/smsrecords',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
						],
					],
					[
						'name' => 'EMAIL_MANAGE',
						'title' => '邮件管理',
						'url' => 'admin/message/email',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 10,
						'child_list' => [
							[
								'name' => 'EMAIL_CONFIG',
								'title' => '邮件配置',
								'url' => 'admin/message/email',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
							[
								'name' => 'EMAIL_RECORDS',
								'title' => '发送记录',
								'url' => 'admin/message/emailrecords',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => [],
							],
						],
					],
				],
			],
			[
				'name' => 'CONFIG_MALL',
				'title' => '商城设置',
				'url' => 'admin/config/receivable',
				'parent' => '',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/order_set.png',
				'picture_selected' => 'app/admin/view/public/img/menu_icon/order_set.png',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'CONFIG_RECEIVABLE',
						'title' => '收款账户',
						'url' => 'admin/config/receivable',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [],
					],
					[
						'name' => 'LOGIN_REG_CONFIG',
						'title' => '会员注册',
						'url' => 'admin/member/regconfig',
						'is_show' => 1,
						'sort' => 2,
					],
					[
						'name' => 'ORDER_CONFIG_SETTING',
						'title' => '交易设置',
						'url' => 'admin/order/config',
						'is_show' => 1,
						'sort' => 3,
					],
					[
						'name' => 'GOODS_VERIFY_CONFIG',
						'title' => '商品审核',
						'url' => 'admin/goods/verifyconfig',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],
					[
						'name' => 'SHOP_ACCOUNT_WITHDRAW_CONFIG',
						'title' => '店铺设置',
						'url' => 'admin/shopaccount/withdrawconfig',
						'is_show' => 1,
						'sort' => 5,
					],
					[
						'name' => 'MEMBER_WITHDRAW_CONFIG',
						'title' => '会员提现',
						'url' => 'admin/memberwithdraw/config',
						'is_show' => 1,
						'sort' => 6,
					],
					[
						'name' => 'CONFIG_AFTERSALE',
						'title' => '售后保障',
						'url' => 'admin/config/aftersale',
						'parent' => '',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 7,
						'child_list' => [],
					],
					[
						'name' => 'DELIVERY_EXPRESS_CONFIG',
						'title' => '配送管理',
						'url' => 'admin/express/expresscompany',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 8,
						'child_list' => [
							[
								'name' => 'DELIVERY_EXPRESS_CONFIG_LIST',
								'title' => '物流公司',
								'url' => 'admin/express/expresscompany',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
								'child_list' => []
							],
							[
								'name' => 'DELIVERY_TRACE',
								'title' => '物流跟踪',
								'url' => 'admin/express/trace',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
								'child_list' => [],
							],
							[
								'name' => 'DELIVERY_EXPRESS_ADD',
								'title' => '添加物流公司',
								'url' => 'admin/express/addcompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 3,
								'child_list' => [],
							],
							[
								'name' => 'DELIVERY_EXPRESS_EDIT',
								'title' => '编辑物流公司',
								'url' => 'admin/express/editcompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 4,
								'child_list' => [],
							],
							[
								'name' => 'DELIVERY_EXPRESS_DELETE',
								'title' => '删除物流公司',
								'url' => 'admin/express/deletecompany',
								'is_show' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 5,
								'child_list' => [],
							],
						],
					],
				],
			],
			[
				'name' => 'CONFIG_SYSTEM',
				'title' => '系统设置',
				'url' => 'admin/system/cache',
				'is_show' => 1,
				'picture' => 'app/admin/view/public/img/menu_icon/system_setup.png',
				'picture_selected' => '',
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'CONFIG_SYSTEM_CACHE',
						'title' => '缓存管理',
						'url' => 'admin/system/cache',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_SYSTEM_ADDON',
						'title' => '插件管理',
						'url' => 'admin/system/addon',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
						'child_list' => [],
					],
					[
						'name' => 'CONFIG_SYSTEM_DATABASE',
						'title' => '数据库管理',
						'url' => 'admin/system/database',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [
							[
								'name' => 'CONFIG_SYSTEM_DATABASE_LIST',
								'title' => '数据备份',
								'url' => 'admin/system/database',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'CONFIG_SYSTEM_IMPORTLIST',
								'title' => '数据还原',
								'url' => 'admin/system/importlist',
								'parent' => '',
								'is_show' => 1,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],
							[
								'name' => 'CONFIG_SYSTEM_BACKUP',
								'title' => '数据备份',
								'url' => 'admin/system/backup',
								'parent' => '',
								'is_show' => 0,
							],
							[
								'name' => 'CONFIG_SYSTEM_DELETEBACKUP',
								'title' => '删除备份文件',
								'url' => 'admin/system/deletebackup',
								'parent' => '',
								'is_show' => 0,
							],
							[
								'name' => 'CONFIG_SYSTEM_TABLEREPAIR',
								'title' => '数据表修复',
								'url' => 'admin/system/tablerepair',
								'parent' => '',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'CONFIG_SYSTEM_AUTH_MANAGE',
						'title' => '授权管理',
						'url' => 'admin/system/auth',
						'is_show' => 1,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
						'child_list' => [
							[
								'name' => 'CONFIG_SYSTEM_AUTH',
								'title' => '授权信息',
								'url' => 'admin/system/auth',
								'parent' => '',
								'is_show' => 1,
							],
							[
								'name' => 'CONFIG_SYSTEM_UPGRADE',
								'title' => '在线更新',
								'url' => 'admin/system/upgrade',
								'parent' => '',
								'is_show' => 1,
								'child_list' => [
									[
										'name' => 'CONFIG_SYSTEM_VERSION',
										'title' => '版本详情',
										'url' => 'admin/system/version',
										'parent' => '',
										'is_show' => 0,
									],
									[
										'name' => 'CONFIG_SYSTEM_VERSION_DOWNLOAD',
										'title' => '文件下载',
										'url' => 'admin/system/versionDownload',
										'parent' => '',
										'is_show' => 0,
									],
									[
										'name' => 'CONFIG_SYSTEM_VERSION_UPGRADE',
										'title' => '在线升级',
										'url' => 'admin/system/versionUpgrade',
										'parent' => '',
										'is_show' => 0,
									],
								]
							],
						]
					],
				],
			],
		],
	]
];
