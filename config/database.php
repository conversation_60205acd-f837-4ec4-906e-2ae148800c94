<?php
use think\facade\Env;

return [
    // 默认使用的数据库连接配置
    'default'         => 'mysql',

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 数据库连接配置信息
    'connections'     => [
        'mysql' => [
            // 数据库类型
            'type'              => env('TYPE', 'mysql'),
            // 服务器地址
            'hostname'          => env('HOSTNAME', '*************'),
            // 数据库名
            'database'          => env('DATABASE', 'xm_youpin'),
            // 用户名
            'username'          => env('USERNAME', 'demo'),
            // 密码
            'password'          => env('PASSWORD', '123'),
            // 端口
            'hostport'          => env('HOSTPORT', '3306'),
            // 数据库连接参数
            'params'            => [],
            // 数据库编码默认采用utf8
            'charset'           => env('CHARSET', 'utf8mb4'),
            'collation'         => env('COLLATION', 'utf8mb4_general_ci'),
            // 数据库表前缀
            'prefix'           => env('PREFIX', 'xm_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'            => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'       => false,
            // 读写分离后 主服务器数量
            'master_num'        => 1,
            // 指定从服务器序号
            'slave_no'          => '',
            // 是否严格检查字段是否存在
            'fields_strict'     => false,
            // 是否需要断线重连
            'break_reconnect'   => false,
            // 监听SQL
            'trigger_sql'       => true,
            // 开启字段缓存
            'fields_cache'      => false,
            // 字段缓存路径
            'schema_cache_path' => app()->getRuntimePath() . 'schema' . DIRECTORY_SEPARATOR,
        ],

        // 更多的数据库配置信息
        'xm_db' => [
            // 数据库类型
            'type'              => env('TYPE', 'mysql'),
            // 服务器地址
            'hostname'          => env('XM_HOSTNAME', '*************'),
            // 数据库名
            'database'          => env('XM_DATABASE', 'xmpt-v2-dev'),
            // 用户名
            'username'          => env('XM_USERNAME', 'demo'),
            // 密码
            'password'          => env('XM_PASSWORD', '123'),
            // 端口
            'hostport'          => env('XM_HOSTPORT', '3306'),
            // 数据库连接参数
            'params'            => [],
            // 数据库编码默认采用utf8
            'charset'           => env('XM_CHARSET', 'utf8mb4'),
            // 数据库表前缀
            'prefix'           => env('XM_PREFIX', 'xm_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'            => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'       => false,
            // 读写分离后 主服务器数量
            'master_num'        => 1,
            // 指定从服务器序号
            'slave_no'          => '',
            // 是否严格检查字段是否存在
            'fields_strict'     => false,
            // 是否需要断线重连
            'break_reconnect'   => false,
            // 监听SQL
            'trigger_sql'       => true,
            // 开启字段缓存
            'fields_cache'      => false,
            // 字段缓存路径
            'schema_cache_path' => app()->getRuntimePath() . 'schema' . DIRECTORY_SEPARATOR,
        ],
        // 更多的数据库配置信息
        'pcrm_db' => [
            // 数据库类型
            'type'              => env('TYPE', 'mysql'),
            // 服务器地址
            'hostname'          => env('PCRM.PCRM_HOSTNAME', 'pcrm.jueke66.com'),
            // 数据库名
            'database'          => env('PCRM.PCRM_DATABASE', 'jkpcrm_5'),
            // 用户名
            'username'          => env('PCRM.PCRM_USERNAME', 'kake'),
            // 密码
            'password'          => env('PCRM.PCRM_PASSWORD', 'Kakels123_2021'),
            // 端口
            'hostport'          => env('PCRM.PCRM_HOSTPORT', '3306'),
            // 数据库连接参数
            'params'            => [],
            // 数据库编码默认采用utf8
            'charset'           => env('PCRM.PCRM_CHARSET', 'utf8mb4'),
            // 数据库表前缀
            'prefix'           => env('PCRM.PCRM_PREFIX', '5kcrm_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'            => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'       => false,
            // 读写分离后 主服务器数量
            'master_num'        => 1,
            // 指定从服务器序号
            'slave_no'          => '',
            // 是否严格检查字段是否存在
            'fields_strict'     => false,
            // 是否需要断线重连
            'break_reconnect'   => false,
            // 监听SQL
            'trigger_sql'       => true,
            // 开启字段缓存
            'fields_cache'      => false,
            // 字段缓存路径
            'schema_cache_path' => app()->getRuntimePath() . 'schema' . DIRECTORY_SEPARATOR,
        ],
    ],
];

