<?php
// +----------------------------------------------------------------------
// | 店铺端菜单设置
// +----------------------------------------------------------------------
return [
	[
		'name' => 'INDEX_ROOT',
		'title' => '概况',
		'url' => 'shop/index/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_survey.png',
		'picture_selected' => '',
		'sort' => 1,
	],
	[
		'name' => 'SHOP_ROOT',
		'title' => '店铺',
		'url' => 'shop/shop/config',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_shop.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_shop_selected.png',
		'sort' => 2,
		'child_list' => [
			[
				'name' => 'SHOP_MANAGE',
				'title' => '店铺信息',
				'url' => 'shop/shop/config',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/shop.png',
				'picture_selected' => 'app/shop/view/public/img/icon/shop.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'SHOP_CONFIG',
						'title' => '店铺信息',
						'url' => 'shop/shop/config',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_CONTACT',
						'title' => '联系地址',
						'url' => 'shop/shop/contact',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'SHOP_CERT',
						'title' => '认证信息',
						'url' => 'shop/shop/cert',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],
					[
						'name' => 'SHOP_SERVICE',
						'title' => '服务保障',
						'url' => 'shop/shopservice/lists',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 5,
						'child_list' => [
							[
								'name' => 'SHOP_SERVICE_APPLY',
								'title' => '申请服务',
								'url' => 'shop/shopservice/apply',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'SHOP_SERVICE_QUIT',
								'title' => '退出服务',
								'url' => 'shop/shopservice/quit',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 2,
							],

						]
					],

				]
			],
			[
				'name' => 'SHOP_DIY',
				'title' => '装修信息',
				'url' => 'shop/diy/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/diy.png',
				'picture_selected' => 'app/shop/view/public/img/icon/diy.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'SHOP_DIY_INDEX',
						'title' => '主页装修',
						'url' => 'shop/diy/index',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_DIY_GOODS_CATEGORY',
						'title' => '分类页面',
						'url' => 'shop/diy/goodscategory',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'SHOP_DIY_LISTS',
						'title' => '微页面',
						'url' => 'shop/diy/lists',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
						'child_list' => [
							[
								'name' => 'SHOP_DIY_EDIT',
								'title' => '编辑自定义页面',
								'url' => 'shop/diy/edit',
								'is_show' => 0,
							],
						],
					],
					[
						'name' => 'SHOP_DIY_BOTTOM_NAV',
						'title' => '底部导航',
						'url' => 'shop/diy/bottomnavdesign',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],

				]
			],
		]
	],
	[
		'name' => 'GOODS_ROOT',
		'title' => '商品',
		'url' => 'shop/goods/lists',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_commodity.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_commodity_selected.png',
		'sort' => 3,
		'child_list' => [
			[
				'name' => 'GOODS_MANAGE',
				'title' => '商品列表',
				'url' => 'shop/goods/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods_list.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods_list.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'PHYSICAL_GOODS_ADD',
						'title' => '发布商品',
						'url' => 'shop/goods/addgoods',
						'sort' => 5,
						'is_show' => 0
					],
					[
						'name' => 'PHYSICAL_GOODS_EDIT',
						'title' => '编辑商品',
						'url' => 'shop/goods/editgoods',
						'sort' => 6,
						'is_show' => 0
					],
					[
						'name' => 'VIRTUAL_GOODS_ADD',
						'title' => '发布商品',
						'url' => 'shop/virtualgoods/addgoods',
						'sort' => 5,
						'is_show' => 0
					],
					[
						'name' => 'VIRTUAL_GOODS_EDIT',
						'title' => '编辑商品',
						'url' => 'shop/virtualgoods/editgoods',
						'sort' => 6,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_OFF',
						'title' => '商品下架',
						'url' => 'shop/goods/offgoods',
						'sort' => 7,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_ON',
						'title' => '商品上架',
						'url' => 'shop/goods/ongoods',
						'sort' => 8,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_DELETE',
						'title' => '商品删除',
						'url' => 'shop/goods/deletegoods',
						'sort' => 9,
						'is_show' => 0
					],
				]
			], [
				'name' => 'PHYSICAL_GOODS_RECYCLE',
				'title' => '回收站',
				'url' => 'shop/goods/recycle',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/recycle.png',
				'picture_selected' => 'app/shop/view/public/img/icon/recycle.png',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'PHYSICAL_GOODS_RECYCLE_DELETE',
						'title' => '回收站删除',
						'url' => 'shop/goods/deleterecycle',
						'sort' => 1,
						'is_show' => 0
					],
					[
						'name' => 'PHYSICAL_GOODS_RECYCLE_RECOVERY',
						'title' => '回收站恢复',
						'url' => 'shop/goods/recoveryrecycle',
						'sort' => 2,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'GOODS_BRAND_MANAGE',
				'title' => '品牌管理',
				'url' => 'shop/goodsbrand/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/brand.png',
				'picture_selected' => 'app/shop/view/public/img/icon/brand.png',
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'GOODS_BRAND_ADD',
						'title' => '品牌添加',
						'url' => 'shop/goodsbrand/addbrand',
						'sort' => 3,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_BRAND_EDIT',
						'title' => '品牌编辑',
						'url' => 'shop/goodsbrand/editbrand',
						'sort' => 4,
						'is_show' => 0
					],
					[
						'name' => 'GOODS_BRAND_DEL',
						'title' => '品牌删除',
						'url' => 'shop/goodsbrand/deletebrand',
						'sort' => 5,
						'is_show' => 0
					]

				]
			],
			[
				'name' => 'GOODS_ATTR',
				'title' => '商品类型',
				'url' => 'shop/goodsattr/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods.png',
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'GOODS_ATTR_EDIT',
						'title' => '类型编辑',
						'url' => 'shop/goodsattr/editattr',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_ATTR_DEL',
						'title' => '类型删除',
						'url' => 'shop/goodsattr/deleteattr',
						'is_show' => 0
					]
				]
			],
			[
				'name' => 'GOODS_CATEGORY',
				'title' => '店内分类',
				'url' => 'shop/goodscategory/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/category.png',
				'picture_selected' => 'app/shop/view/public/img/icon/category.png',
				'sort' => 5,
				'child_list' => [
					[
						'name' => 'GOODS_CATEGORY_ADD',
						'title' => '店内分类添加',
						'url' => 'shop/goodscategory/addcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_EDIT',
						'title' => '店内分类编辑',
						'url' => 'shop/goodscategory/editcategory',
						'is_show' => 0
					],
					[
						'name' => 'GOODS_CATEGORY_DELETE',
						'title' => '店内分类删除',
						'url' => 'shop/goodscategory/deletecategory',
						'is_show' => 0
					],
				]
			],
			//     		[
			//     			'name' => 'GOODS_CONSULT',
			//     			'title' => '商品咨询',
			//     			'url' => 'shop/goods/consult',
			//     			'is_show' => 1,
			//     			'is_control' => 1,
			//     			'is_icon' => 0,
			//     			'picture' => '',
			//     			'picture_selected' => '',
			//     			'sort' => 5,
			//     			'child_list' => [
			//     				[
			//     					'name' => 'GOODS_CONSULT_REPLY',
			//     					'title' => '回复',
			//     					'url' => 'shop/goods/replyconsult',
			//     					'sort' => 1,
			//     					'is_show' => 0
			//     				],
			//     				[
			//     					'name' => 'GOODS_CONSULT_DELETE',
			//     					'title' => '删除',
			//     					'url' => 'shop/goods/deleteconsult',
			//     					'sort' => 1,
			//     					'is_show' => 0
			//     				],
			//     			]
			//     		],
			[
				'name' => 'GOODS_EVALUATE',
				'title' => '商品评价',
				'url' => 'shop/goods/evaluate',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/goods_evaluate.png',
				'picture_selected' => 'app/shop/view/public/img/icon/goods_evaluate.png',
				'sort' => 6,
				'child_list' => [
					[
						'name' => 'GOODS_EVALUATE_DELETE',
						'title' => '删除',
						'url' => 'shop/goods/deleteevaluate',
						'sort' => 1,
						'is_show' => 0
					],
				]
			],
			[
				'name' => 'ALBUM_MANAGE',
				'title' => '相册管理',
				'url' => 'shop/album/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/picture.png',
				'picture_selected' => 'app/shop/view/public/img/icon/picture.png',
				'sort' => 7,
				'child_list' => [
					[
						'name' => 'ALBUM_ADD',
						'title' => '添加相册分组',
						'url' => 'shop/album/addalbum',
						'sort' => 1,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_EDIT',
						'title' => '编辑相册分组',
						'url' => 'shop/album/editalbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_DELETE',
						'title' => '删除相册分组',
						'url' => 'shop/album/deletealbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_MODIFY_PICNAME',
						'title' => '编辑文件名称',
						'url' => 'shop/album/modifypicname',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_MODIFY_ALBUM',
						'title' => '修改文件分组',
						'url' => 'shop/album/modifyfilealbum',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_PIC_DELETE',
						'title' => '删除文件',
						'url' => 'shop/album/deletefile',
						'sort' => 2,
						'is_show' => 0
					],
					[
						'name' => 'ALBUM_BOX',
						'title' => '相册',
						'url' => 'shop/album/album',
						'sort' => 2,
						'is_show' => 0
					],
				]
			],
		]
	],
	[
		'name' => 'ORDER_ROOT',
		'title' => '订单',
		'url' => 'shop/order/lists',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_order.png',
		'picture_selected' => '',
		'sort' => 4,
		'child_list' => [
			[
				'name' => 'ORDER_MANAGE',
				'title' => '订单管理',
				'url' => 'shop/order/lists',
				'parent' => '',
				'is_show' => 1,
				'is_control' => 0,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/order.png',
				'picture_selected' => 'app/shop/view/public/img/icon/order.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'EXPRESS_ORDER_DETAIL',
						'title' => '订单详情',
						'url' => 'shop/order/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_CLOSE',
						'title' => '订单关闭',
						'url' => 'shop/order/close',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_DELIVER',
						'title' => '订单发货',
						'url' => 'shop/order/deliver',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_ADJUST_PRICE',
						'title' => '订单调价',
						'url' => 'shop/order/adjustprice',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'EXPRESS_ORDER_EDIT_ADDRESS',
						'title' => '订单修改收货地址',
						'url' => 'shop/order/editaddress',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'LOCAL_ORDER_DETAIL',
						'title' => '外卖订单详情',
						'url' => 'shop/localorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'LOCAL_ORDER_DELIVER',
						'title' => '外卖订单发货',
						'url' => 'shop/localorder/deliver',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_ORDER_DETAIL',
						'title' => '自提订单详情',
						'url' => 'shop/storeorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'VIRTUAL_ORDER_DETAIL',
						'title' => '虚拟订单详情',
						'url' => 'shop/virtualorder/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					]
				],
			],
            [
                'name' => 'ORDER_DELIVERY',
                'title' => '订单发货',
                'url' => 'shop/delivery/lists',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 0,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/deliver.png',
                'picture_selected' => 'app/shop/view/public/img/icon/deliver.png',
                'sort' => 2,
            ],
			[
				'name' => 'ORDER_REFUND_LIST',
				'title' => '退款维权',
				'url' => 'shop/orderrefund/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/refund.png',
				'picture_selected' => 'app/shop/view/public/img/icon/refund.png',
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'ORDER_REFUND_DETAIL',
						'title' => '维权详情',
						'url' => 'shop/orderrefund/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'ORDER_REFUND_REFUSE',
						'title' => '维权拒绝',
						'url' => 'shop/orderrefund/refuse',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'ORDER_REFUND_AGREE',
						'title' => '维权同意',
						'url' => 'shop/orderrefund/agree',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'ORDER_REFUND_AGREE',
						'title' => '维权收货',
						'url' => 'shop/orderrefund/receive',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'ORDER_REFUND_COMPLETE',
						'title' => '维权通过',
						'url' => 'shop/orderrefund/complete',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],
            [
                'name' => 'ORDER_VERIFY',
                'title' => '订单核销',
                'url' => 'shop/verify/verifycard',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/verify.png',
                'picture_selected' => 'app/shop/view/public/img/icon/verify.png',
                'sort' => 4,
                'child_list' => [
                    [
                        'name' => 'ORDER_VERIFY_CARD',
                        'title' => '核销台',
                        'url' => 'shop/verify/verifycard',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'ORDER_VERIFY_RECORDS',
                        'title' => '核销记录',
                        'url' => 'shop/verify/records',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                    ],
                ]

            ],

		]
	],
	[
		'name' => 'MEMBER_ROOT',
		'title' => '会员',
		'url' => 'shop/member/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_member.png',
		'picture_selected' => '',
		'sort' => 5,
		'child_list' => [
			[
				'name' => 'MEMBER_INDEX',
				'title' => '会员概况',
				'url' => 'shop/member/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member.png',
				'sort' => 1,
			],
			[
				'name' => 'MEMBER_MANAGE',
				'title' => '会员管理',
				'url' => 'shop/member/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member_manager.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member_manager.png',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'MEMBER_DETAIL',
						'title' => '会员详情',
						'url' => 'shop/member/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1
					]
				]
			],
		]
	],
	[
		'name' => 'PROMOTION_ROOT',
		'title' => '营销',
		'url' => 'shop/promotion/index',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_marketing.png',
		'picture_selected' => '',
		'sort' => 6,
		'child_list' => [
			[
				'name' => 'PROMOTION_CENTER',
				'title' => '营销中心',
				'url' => 'shop/promotion/index',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/promotion.png',
				'picture_selected' => 'app/shop/view/public/img/icon/promotion.png',
				'sort' => 1,
			],
			[
				'name' => 'PROMOTION_PLATFORM',
				'title' => '平台推广',
				'url' => 'shop/promotion/platform',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/spread.png',
				'picture_selected' => 'app/shop/view/public/img/icon/spread.png',
				'sort' => 1,
			],
			[
				'name' => 'PROMOTION_MEMBER',
				'title' => '会员互动',
				'url' => 'shop/promotion/member',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/member_chat.png',
				'picture_selected' => 'app/shop/view/public/img/icon/member_chat.png',
				'sort' => 1,
			],
		]
	],
	[
		'name' => 'TOOL_ROOT',
		'title' => '应用',
		'url' => 'shop/promotion/tool',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_marketing.png',
		'picture_selected' => '',
		'sort' => 6,
		'child_list' => [
			[
				'name' => 'PROMOTION_TOOL',
				'title' => '应用管理',
				'url' => 'shop/promotion/tool',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/promotion_tool.png',
				'picture_selected' => 'app/shop/view/public/img/icon/promotion_tool.png',
				'sort' => 1,
			],
		]
	],
	[
		'name' => 'STORE_ROOT',
		'title' => '门店',
		'url' => 'shop/store/lists',
		'is_show' => 1,
		'is_control' => 1,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/icon/store.png',
		'picture_selected' => 'app/shop/view/public/img/icon/store.png',
		'sort' => 7,
		'child_list' => [
			[
				'name' => 'STORE_LIST',
				'title' => '门店列表',
				'url' => 'shop/store/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'sort' => 1,
				'picture' => 'app/shop/view/public/img/icon/store.png',
				'picture_selected' => 'app/shop/view/public/img/icon/store.png',
				'child_list' => [
					[
						'name' => 'STORE_ADD',
						'title' => '添加门店',
						'url' => 'shop/store/addstore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_EDIT',
						'title' => '修改门店',
						'url' => 'shop/store/editstore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'STORE_DELETE',
						'title' => '删除门店',
						'url' => 'shop/store/deletestore',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],

		]
	],
	[
		'name' => 'ACCOUNT_ROOT',
		'title' => '资产',
		'url' => 'shop/account/dashboard',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 1,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_account.png',
		'picture_selected' => '',
		'sort' => 8,
		'child_list' => [
			[
				'name' => 'ACCOUNT_DASHBOARD_INDEX',
				'title' => '资产概况',
				'url' => 'shop/account/dashboard',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/money.png',
				'picture_selected' => 'app/shop/view/public/img/icon/money.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'ACCOUNT_ORDERLIST',
						'title' => '交易记录',
						'url' => 'shop/account/orderlist',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],
			[
				'name' => 'SHOP_WITHDRAW',
				'title' => '提现记录',
				'url' => 'shop/shopwithdraw/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/shop_withdraw.png',
				'picture_selected' => 'app/shop/view/public/img/icon/shop_withdraw.png',
				'sort' => 2,
				'child_list' => [
					[
						'name' => 'SHOP_WITHDRAW_APPLY',
						'title' => '申请提现',
						'url' => 'shop/shopwithdraw/apply',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],
			[
				'name' => 'ACCOUNT_FEE',
				'title' => '入驻费用',
				'url' => 'shop/account/fee',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/fee.png',
				'picture_selected' => 'app/shop/view/public/img/icon/fee.png',
				'sort' => 3,
				'child_list' => [
					[
						'name' => 'SHOP_REOPEN_ADD',
						'title' => '申请续签',
						'url' => 'shop/cert/reopen',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
					[
						'name' => 'SHOP_AUTH_APPLY',
						'title' => '申请续签',
						'url' => 'shop/cert/index',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 2,
					],
					[
						'name' => 'SHOP_REOPEN_EDIT',
						'title' => '编辑续签',
						'url' => 'shop/cert/editreopeninfo',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 3,
					],
					[
						'name' => 'SHOP_REOPEN_LIST',
						'title' => '续签记录',
						'url' => 'shop/account/reopenlist',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 4,
					],

				]
			],
			[
				'name' => 'ACCOUNT_SETTLEMENT',
				'title' => '店铺结算',
				'url' => 'shop/settlement/lists',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/settlement.png',
				'picture_selected' => 'app/shop/view/public/img/icon/settlement.png',
				'sort' => 4,
				'child_list' => [
					[
						'name' => 'ACCOUNT_SETTLEMENT_DETAIL',
						'title' => '结算详情',
						'url' => 'shop/settlement/detail',
						'is_show' => 0,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			],
		]
	],
	[
		'name' => 'STAT_ROOT',
		'title' => '统计',
		'url' => 'shop/stat/shop',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_data.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_data_selected.png',
		'sort' => 9,
		'child_list' => [
			[
				'name' => 'STAT_SHOP',
				'title' => '店铺统计',
				'url' => 'shop/stat/shop',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat.png',
				'sort' => 2,
			],
			[
				'name' => 'STAT_GOODS',
				'title' => '商品统计',
				'url' => 'shop/stat/goods',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_goods.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_goods.png',
				'sort' => 3,
			],
			[
				'name' => 'STAT_ORDER',
				'title' => '交易统计',
				'url' => 'shop/stat/order',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_order.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_order.png',
				'sort' => 4,
			],
			[
				'name' => 'STAT_VISIT',
				'title' => '访问统计',
				'url' => 'shop/stat/visit',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/stat_icon.png',
				'picture_selected' => 'app/shop/view/public/img/icon/stat_icon.png',
				'sort' => 5,
			],
		]
	],
	[
		'name' => 'SYSTEM_ROOT',
		'title' => '设置',
		'url' => 'shop/delivery/express',
		'parent' => '',
		'is_show' => 1,
		'is_control' => 0,
		'is_icon' => 0,
		'picture' => 'app/shop/view/public/img/menu_icon/menu_set.png',
		'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_set_selected.png',
		'sort' => 10,
		'child_list' => [
			[
				'name' => 'SYSTEM_CONFIG',
				'title' => '系统设置',
				'url' => 'shop/delivery/express',
				'is_show' => 1,
				'is_control' => 1,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/sys_config.png',
				'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
				'sort' => 1,
				'child_list' => [
					[
						'name' => 'EXPRESS_CONFIG',
						'title' => '配送设置',
						'url' => 'shop/delivery/express',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'EXPRESS_COMPANY',
								'title' => '物流公司',
								'url' => 'shop/express/expresscompany',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_STORE_CONFIG',
								'title' => '自提设置',
								'url' => 'shop/delivery/storeconfig',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_STORE_STATUS',
								'title' => '自提开关',
								'url' => 'shop/delivery/modifystorestatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_EXPRESS_STATUS',
								'title' => '物流开关',
								'url' => 'shop/delivery/modifyexpressstatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_LOCAL_STATUS',
								'title' => '外卖配送开关',
								'url' => 'shop/delivery/modifylocalstatus',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_LOCALDELIVERY_CONFIG',
								'title' => '外卖配送',
								'url' => 'shop/delivery/localconfig',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_EDIT_PRINT_TEMPLATE',
								'title' => '运单模板',
								'url' => 'shop/express/editprinttemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE',
								'title' => '运费模板',
								'url' => 'shop/express/template',
								'is_show' => 1,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_ADD',
								'title' => '添加运费模板',
								'url' => 'shop/express/addtemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_EDIT',
								'title' => '编辑运费模板',
								'url' => 'shop/express/edittemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'EXPRESS_TEMPLATE_DELETE',
								'title' => '删除运费模板',
								'url' => 'shop/express/deletetemplate',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
                            [
                                'name' => 'EXPRESS_LOCAL',
                                'title' => '编辑外卖配送',
                                'url' => 'shop/local/local',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 10,
                            ],
						]
					],
					[
						'name' => 'ORDER_VERIFY_USER',
						'title' => '核销人员',
						'url' => 'shop/verify/user',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'ORDER_VERIFY_USER_ADD',
								'title' => '添加核销人员',
								'url' => 'shop/verify/adduser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'ORDER_VERIFY_USER_DELETE',
								'title' => '删除核销人员',
								'url' => 'shop/verify/deleteuser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							], [
								'name' => 'ORDER_VERIFY_USER_EDIT',
								'title' => '编辑核销人员',
								'url' => 'shop/verify/edituser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],

				]
			],
			[
				'name' => 'USER_AUTH',
				'title' => '账户权限',
				'url' => 'shop/user/user',
				'parent' => '',
				'is_show' => 1,
				'is_control' => 0,
				'is_icon' => 0,
				'picture' => 'app/shop/view/public/img/icon/account.png',
				'picture_selected' => 'app/shop/view/public/img/icon/account.png',
				'sort' => 7,
				'child_list' => [
					[
						'name' => 'USER_LIST',
						'title' => '用户管理',
						'url' => 'shop/user/user',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'USER_ADD',
								'title' => '用户添加',
								'url' => 'shop/user/adduser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_EDIT',
								'title' => '用户编辑',
								'url' => 'shop/user/edituser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_DELETE',
								'title' => '用户删除',
								'url' => 'shop/user/deleteuser',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_MODIFY_STATUS',
								'title' => '调整用户状态',
								'url' => 'shop/user/modifyuserstatus',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],
					[
						'name' => 'USER_GROUP',
						'title' => '用户组',
						'url' => 'shop/user/group',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
						'child_list' => [
							[
								'name' => 'USER_GROUP_ADD',
								'title' => '用户组添加',
								'url' => 'shop/user/addgroup',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_GROUP_EDIT',
								'title' => '用户组编辑',
								'url' => 'shop/user/editgroup',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_GROUP_DELETE',
								'title' => '用户组删除',
								'url' => 'shop/user/deletegroup',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
							[
								'name' => 'USER_GROUP_MODIFY_STATUS',
								'title' => '调整用户状态',
								'url' => 'shop/user/modifygroupstatus',
								'is_show' => 0,
								'is_control' => 1,
								'is_icon' => 0,
								'picture' => '',
								'picture_selected' => '',
								'sort' => 1,
							],
						]
					],
					[
						'name' => 'USER_LOG',
						'title' => '用户日志',
						'url' => 'shop/user/userlog',
						'is_show' => 1,
						'is_control' => 1,
						'is_icon' => 0,
						'picture' => '',
						'picture_selected' => '',
						'sort' => 1,
					],
				]
			]

		]
	]

];
