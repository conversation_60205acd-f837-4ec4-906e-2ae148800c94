<?php

use Overtrue\EasySms\Strategies\OrderStrategy;

return [
    // HTTP 请求的超时时间（秒）
    'timeout' => 5.0,
    // 默认发送配置
    'default' => [
        // 网关调用策略，默认：顺序调用
        'strategy' => OrderStrategy::class,
        // 默认可用的发送网关
        'gateways' => ['huyi'],
    ],

    // 可用的网关配置
    'gateways' => [
        "huyi" => [
            'api_id' => 'C49630433',
            'api_key' => '68412549b02668892028c181c11027d9',
        ]
    ],
];