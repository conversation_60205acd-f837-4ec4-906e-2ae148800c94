<?php
use think\facade\Env;

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => Env::get('CACHE.DRIVER', 'file'),

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        // Redis
        'redis'    =>    [
            'type'     => 'redis',
            'host'     => Env::get('REDIS.HOST', '127.0.0.1'),
            'port'     => Env::get('REDIS.PORT', '6379'),
            'password' => Env::get('REDIS.PASSWORD', ''),
            'select'   => Env::get('REDIS.SELECT', '0'),
            // 全局缓存有效期（0为永久有效）
            'expire'   => 0,
            // 缓存前缀
            'prefix'   => '',
            'timeout'  => 0,
        ],
        // Session Redis
        'sessionRedis'    =>    [
            'type'     => 'redis',
            'host'     => Env::get('REDIS.HOST', '127.0.0.1'),
            'port'     => Env::get('REDIS.PORT', '6379'),
            'password' => Env::get('REDIS.PASSWORD', ''),
            'select'   => Env::get('REDIS.SELECT', '0') + 2, // 跟默认的岔开，避免 clear 冲突
            // 全局缓存有效期（0为永久有效）
            'expire'   => 0,
            // 缓存前缀
            'prefix'   => '',
            'timeout'  => 0,
        ],
        // 更多的缓存连接
    ],
];
