<?php
/**
 * 小程序配置文件
 */
return [
    'dev'=>[
        'app_id'=>'wx87886602fa1416a7',
        'app_secret'=>'3ff7699d20d770ae0713e40c01f594e5',
        'login_url'=>'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code',
        'notify_url'=>'',
        'return_url'=>'',
        'template_id'=>'QgH63Ao9KADnBvsB64mzc0PT-tn47jbS-SJORVqXF3g',
        'access_token_url'=>'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s',
        'uniform_message'=>'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s',
        'app_username'  => 'gh_949e4c92bf03', //小程序原始id
    ],
    'prod'=>[
        'app_id'=>'wx87886602fa1416a7',
        'app_secret'=>'3ff7699d20d770ae0713e40c01f594e5',
        'login_url'=>'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code',
        'notify_url'=>'',
        'return_url'=>'',
        'template_id'=>'QgH63Ao9KADnBvsB64mzc0PT-tn47jbS-SJORVqXF3g',
        'access_token_url'=>'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s',
        'uniform_message'=>'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s',
        'app_username'  => 'gh_949e4c92bf03', //小程序原始id
    ],
    //订阅消息相关配置
    'sub_config' => [
        //发送订阅消息地址
        'send_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s',
        //订阅消息模版id
        'template_id' => [
            // 1 审核结果通知
            1 => 'XVqQj4w2-XRWiZEZ8s7Avfrtg1z22GzJTANKh5NV-V0',
            2 => '818',
        ],
        //模版id对应的数据排版样式 ['phrase5' =>['value' => '审核结果',],'date2' =>['value' => '2020-09-01',],'thing10' =>['value' => '分销客审核通过',],]
        'template_data' => [
            1 => [
                'phrase2',//审核状态  5个以内纯汉字
                'time3',//审核时间 例如：2019年10月1日，或：2019年10月1日 15:01
                'thing4'//内容 20个以内字符    可汉字、数字、字母或符号组合
            ],
            2 => [
                'aa'=>['value','color'],
                'bb'=>['value','color'],
                'cc'=>['value','color'],
                'dd'=>['value','color'],
            ],
        ]
    ],
    //小程序接口请求地址
    'api_url'=>[
        //生成获取二维码 正方形
        'RCodeUrl'=>'https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s',
        //生成获取小程序二维码
        'appRCodeUrl'=>'https://api.weixin.qq.com/wxa/getwxacode?access_token=%s',
        //生成获取小程序二维码 按场景 通过该接口生成的小程序码，永久有效，数量暂无限制
        'getUnlimitedUrl'=>'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s',
    ],

];
