<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        // 定时任务器
        'crontab' => 'app\command\Crontab',
        'make:dateCommand' => 'app\command\MakeCommand',

        'refund_test' => 'app\command\RefundTest',


        /********************************************/
        // 订单超时自动关闭订单
        'orderClose' => 'app\command\OrderClose',
        // 发送站内信
        'websiteMessage' => 'app\command\WebsiteMessage',
        // 订单自动确认收货
        'orderConfirm' => 'app\command\OrderConfirm',
        // 店铺佣金发放
        'shopOrderCommission' => 'app\command\ShopOrderCommission',
        // 优惠券失效
        'couponBatchOver' => 'app\command\CouponBatchOver',
        // 初始化商品相关
        'initGoods' => 'app\command\InitGoods',
        // 更新先迈店铺等级
        'editShopLevel' => 'app\command\XmShop',
        //修正店主归属
        'updateShopkeeperAffiliation' => 'app\command\manualScript\UpdateShopkeeperAffiliation',
        'resetCommanyRate' => 'app\command\ResetCommanyRate',  // 柚品重置同步先迈的柚品公司返佣比例
        // 柚品重置同步先迈的柚品商品价格
        'editXmGoodsPrice' => 'app\command\EditXmGoodsPrice',  
        // 解决商品价格小数点问题
        'decimalPrice' => 'app\command\DecimalPrice',  
        // 解决先迈新增商品，同步到柚品
        'getXmNewGoods' => 'app\command\SyncXmNewGoods',  
        /// 单独开店流程
        'reOpenShop' => 'app\command\AddShop',
        'initShopPid' => 'app\command\InitShopPid', // 初始化没上级的店铺在先迈时的上下级关系,
        //手动发放迈豆
        'manualGrantMD' => 'app\command\manualScript\ManualGrantMD',
        //手动操作店铺佣金
        'manualGrantShopAccount' => 'app\command\manualScript\ManualGrantShopAccount',
        //重新计算迈豆订单以及修正发现对象
        'recalculateYouliOrder' => 'app\command\manualScript\RecalculateYouliOrder',
        // 每天统计总览
        'mainStatDaily' => 'app\command\MainStatDaily',
        // 每天统计供应商销售金额
        'getSupplierSale' => 'app\command\GetSupplierSale',
        // 每天统计供应商商品营业收入成本
        'getGoodsDailyRevenueCost' => 'app\command\GetGoodsDailyRevenueCost',  
        //店主转移迈豆到余额
        'shopMaidouToBalance'   => 'app\command\ShopMaidouToBalance',

        //同步供应链订单
        'syncPayOrderToChain'   => 'app\command\supplyChain\SyncPayOrderToChain',

        // 同步更新先迈手机号到柚品店主表的手机号
        'syncXmUserMobile' => 'app\command\manualScript\SyncXmUserMobile',  

        //上级分佣
        'shopOrderRecommendCommission' => 'app\command\ShopOrderRecommendCommission',
        // 拼团订单超时自动关闭订单
        'pinOrderClose' => 'app\command\PinOrderClose',
        // 拼团订单未中奖返佣本金
        'pinOrderRefund' => 'app\command\PinOrderRefund',
        //合并店铺账号
        'mergeShopAccount' => 'app\command\manualScript\MergeShopAccount',
        // 修复数据--推荐关系
        'repairRecommendMmunuember'=> 'app\command\RepairRecommendMember',
        //手动撤销支付
        'manualReverse' => 'app\command\manualScript\ManualReverse',
        //升级统计
        'upgradeStatistics' => 'app\command\statistics\UpgradeStatistics',
        // 后台登录
        'adminLogin' => 'app\command\AdminLogin',
        'supplyGoodsRefresh'   => 'app\command\supplyChain\SupplyGoodsRefresh',
        'supplyGoodsAbnormal'  => 'app\command\supplyChain\SupplyGoodsAbnormal',
        // 店主去重、迁单、资金、销售数据迁移
        'shopDeDuplication'    => 'app\command\ShopDeDuplication',
        // 店主迁移后绑定会员
        'shopBindMember'    => 'app\command\ShopBindMember',
        // 强同步先迈推荐关系
        'syncMemberPidByXm'    => 'app\command\SyncMemberPidByXm',
        // 修复用户sync表数据
        'repairMemberSync'    => 'app\command\RepairMemberSync',
        // 修改小程序底部导航栏
        'updateBottomNav' => 'app\command\manualScript\UpdateBottomNav',
        // 买家发起退货退款、换货申请N天后商家未处理，系统将自动同意退货退款、换货
        'agreeApply' => 'app\command\orderAfter\AgreeApply',
        // 商家同意退货/换货或商家未收到货N天后买家未处理（填写退货物流），系统将自动关闭售后
        'closeApply' => 'app\command\orderAfter\CloseApply',
        // 买家已退货N后商家未处理，系统将自动确认收货
        'confirmReturnReceived' => 'app\command\orderAfter\ConfirmReturnReceived',
        // 检测商品优惠券是否过期并处理
        'checkGoodsCouponOverdue' => 'app\command\CheckGoodsCouponOverdue',
        // 商品优惠券临近过期提醒通知
        'sendGoodsCouponOverdueNotice' => 'app\command\SendGoodsCouponOverdueNotice',
        // 检测多件折扣活动过期并处理
        'checkMultipleDiscountOverdue' => 'app\command\CheckMultipleDiscountOverdue',

        // 代办事项提醒
        'sendWaitHanle' => 'app\command\SendWaitHandleNotice',
        // 拉取直播间列表
        'pullBroadcastRoom'             => 'app\command\PullBroadcastRoom',
        'broadcastGoodsStatus'          => 'app\command\BroadcastGoodsStatus',
        //商品优惠券自动派券规则状态自动处理
        'checkGoodsCouponRuleStatus'          => 'app\command\CheckGoodsCouponRuleStatus',

        //批量更新用户地址
        'CompleteMemberAddress'          => 'app\command\CompleteMemberAddress',

 		// 企微可见范围内客户同步
        'enterpriseCustomerSync' => 'app\command\EnterpriseCustomerSync',

        //商品数据统计
        'goodsStat' => 'app\command\statistics\GoodsStat',
        //根据标签规则自动发放优惠券
        'autoSendGoodsCouponByTagRule' => 'app\command\AutoSendGoodsCouponByTagRule',

        //企业同步成员
        'EnterpriseSyncUser' => 'app\command\EnterpriseSyncUser',
        // 变更套餐组合状态
        'comboStatusChange' => 'app\command\ComboStatusChange',
        //加盟任务状态变更
        'TaskStatusChange' => 'app\command\TaskStatusChange',
        //更新加盟任务奖励发放状态
        'UpdateRewardSendStatus' => 'app\command\UpdateRewardSendStatus',
        //更新活动商品标识
        'UpdateActivityGoods' => 'app\command\UpdateActivityGoods',
        //签到活动状态更新
        'SignStatusChange' => 'app\command\SignStatusChange',

        //机器人自动开团
        'AutoCreatePintuanOrder' => 'app\command\AutoCreatePintuanOrder',
        //企微绑定数据拆分
        'SplitEnterpriseWechatData' => 'app\command\SplitEnterpriseWechatData',
        //自动执行全部打标规则
        'AutoExecuteTagRules' => 'app\command\AutoExecuteTagRules',
        //生成用户分析数据
        'MemberAnalysisStat' => 'app\command\MemberAnalysisStat',
        //粉丝任务状态变更
        'FansTaskStatusChange' => 'app\command\FansTaskStatusChange',
        //用户加盟积分清空
        'MemberLeagueTaskPointEmpty' => 'app\command\MemberLeagueTaskPointEmpty',
        //用户加盟积分清空
        'AutoCompletePointsLeagueTask' => 'app\command\AutoCompletePointsLeagueTask',
        //自动问卷活动状态变更
        'AutoQuestionnaireStatusChange' => 'app\command\AutoQuestionnaireStatusChange',
        //数据修复脚本
        'RepairDataCommon' => 'app\command\RepairDataCommon',
        //搜索关键词提取
        'SearchExtraction' => 'app\command\SearchExtraction',
        //活动关键词更新
        'ActivityKeywordUpdate' => 'app\command\ActivityKeywordUpdate',
        //发放一物一码红包
        'SendSecurityCodeRedPack' => 'app\command\pcrm\SendSecurityCodeRedPack',
        //自动结束企微客服接待记录
        'CustomerReceptionAutoEnd' => 'app\command\CustomerReceptionAutoEnd',
        //企微客服在线时长统计
        'CustomerServiceOnlineStat' => 'app\command\CustomerServiceOnlineStat',
        // 发放完成月度销售贡献值
        'SendSaleTaskLeaguePoints' => 'app\command\SendSaleTaskLeaguePoints',
        // 商品上线动销监控定时任务
        'GoodsUpMonitor' => 'app\command\GoodsUpMonitor',
        ],
];
