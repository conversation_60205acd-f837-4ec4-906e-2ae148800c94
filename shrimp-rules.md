# 开发守则

## 项目概述

- YouPin是基于ThinkPHP框架的电商平台，采用模块化/插件化架构
- 项目主要分为core核心系统和addon插件系统两部分
- 目前正在开发customsChinaport插件，用于海关跨境电商数据对接

## 项目架构

### 主要目录结构

- `/app` - 核心应用代码
  - `/api` - API接口
  - `/model` - 数据模型
  - `/service` - 业务逻辑
  - `/event` - 事件监听
  - `/listener` - 事件监听处理
- `/addon` - 插件目录
  - 每个插件有独立文件夹
- `/config` - 全局配置
- `/project_document` - RIPER-5工作流文档
  - `/research` - 研究阶段文档
  - `/proposals` - 创新方案文档
  - `/plans` - 计划阶段文档
  - `/execute` - 执行阶段记录
  - `/review` - 评审阶段文档
- `/tests` - 测试目录

## 插件开发规范

### 插件目录结构

**必须**严格遵循以下目录结构：

```
addon/插件名称/               # 例如alipay
├─admin/                     # 管理后台相关代码
│ ├─controller/              # 后台控制器
│ ├─view/                    # 后台视图
├─api/                       # API接口
│ ├─controller/              # API控制器
├─config/                    # 配置文件目录
│ ├─diy_view.php             # 自定义模板配置文件（必存在）
│ ├─event.php                # 事件配置文件（必存在）
│ ├─info.php                 # 插件配置文件（必存在）
│ ├─menu_shop.php            # 店铺端菜单配置（必存在）
│ ├─menu_admin.php           # 后台菜单配置（必存在）
│ ├─api.php                  # API配置（可选）
│ ├─validate.php             # 验证规则（可选）
├─data/                      # 插件数据
│ ├─install.sql              # 安装SQL
│ ├─uninstall.sql            # 卸载SQL
├─event/                     # 相关事件文件（钩子）
│ ├─Install.php              # 安装插件执行事件（必存在）
│ ├─UnInstall.php            # 卸载插件执行事件（必存在）
│ ├─功能事件.php              # 对应插件执行事件
├─model/                     # 插件模型
├─service/                   # 业务服务
├─icon.png                   # 插件logo
```

### 必需的配置文件

以下配置文件**必须**存在且格式正确：

1. **info.php** - 插件基本信息配置
2. **event.php** - 事件配置
3. **diy_view.php** - 模板与链接配置
4. **menu_shop.php** - 店铺后台菜单配置
5. **menu_admin.php** - 管理后台菜单配置

### 插件安装与卸载

- **必须**提供`event/Install.php`和`event/UnInstall.php`文件
- **必须**正确实现`handle()`方法
- **必须**提供`data/install.sql`和`data/uninstall.sql`文件
- 卸载时**必须**清理所有插件相关数据和配置

### 事件开发

- 事件通过`event('事件名称', [参数数组])`调用
- 事件处理类**必须**实现`handle()`方法
- 事件配置**必须**正确注册在`config/event.php`文件中

## customsChinaport插件特定规则

### 目录结构

**必须**按照以下结构开发：

```
addon/customs_chinaport/
├─admin/
│ ├─controller/
│ │ └─Chinaport.php        # 管理控制器
│ └─view/
│   └─chinaport/
│     ├─index.html         # 概览页
│     ├─config.html        # 配置页
│     └─log.html           # 日志页
├─api/
│ └─controller/
│   └─Api.php              # API控制器
├─config/
│ ├─info.php               # 基本信息
│ ├─config.php             # 配置信息
│ ├─menu_admin.php         # 管理菜单
│ ├─menu_shop.php          # 商店菜单
│ ├─api.php                # API配置
│ └─validate.php           # 验证规则
├─data/
│ └─install.sql            # 安装SQL
├─event/
│ ├─Install.php            # 安装事件
│ └─Uninstall.php          # 卸载事件
├─model/
│ ├─ChinaportLog.php       # 日志模型
│ └─ChinaportReceipt.php   # 回执模型
├─service/
│ ├─ChinaportService.php   # 主业务服务
│ ├─SignatureService.php   # 签名服务
│ └─ReceiptService.php     # 回执处理服务
└─icon.png                 # 插件图标
```

### 功能实现要求

1. **海关数据获取接口（platDataOpen）**
   - **必须**处理海关发起的数据获取请求
   - **必须**实现签名验证
   - **必须**正确返回请求的数据

2. **订单数据提交**
   - **必须**按照海关要求的格式生成订单数据
   - **必须**实现数据签名
   - **必须**记录请求和响应日志

3. **支付数据提交**
   - **必须**按照179格式构建支付数据
   - **必须**实现数据签名
   - **必须**记录请求和响应日志

### 必需的配置项

以下配置项**必须**在install.sql中创建：

- `client_sign_key` - 客户端签名密钥
- `goods_url_prefix` - 商品URL前缀
- `recp_account` - 收款账号
- `recp_name` - 收款人名称

## 测试规范

### 单元测试

- 对于customsChinaport插件，**必须**创建`tests/Customs/ChinaportApiTest.php`测试文件
- 测试文件**必须**继承`tests/AppModuleTestCase.php`基类
- **必须**测试API接口的正确性、签名验证功能和数据格式

### 集成测试

- **必须**测试与订单系统的集成
- **必须**测试与支付系统的集成

## RIPER-5工作流文档规范

所有文档**必须**遵循以下规则：

### 1. 研究阶段文档

- **必须**存放在`/project_document/research/`目录
- **必须**包含技术选型分析、需求分析等内容

### 2. 创新方案文档

- **必须**存放在`/project_document/proposals/`目录
- **必须**包含解决方案设计和架构图

### 3. 计划阶段文档

- **必须**存放在`/project_document/plans/`目录
- **必须**包含详细的任务分解和时间估算

### 4. 执行阶段记录

- **必须**存放在`/project_document/execute/`目录
- **必须**记录开发进度和关键决策

### 5. 评审阶段文档

- **必须**存放在`/project_document/review/`目录
- **必须**包含最终测试结果和审查意见

## 代码规范

### 命名规范

- 类名**必须**使用大驼峰命名法（如`ChinaportService`）
- 方法名**必须**使用小驼峰命名法（如`getOrderData`）
- 变量**必须**使用小驼峰命名法
- 常量**必须**全部大写，单词间用下划线分隔

### 注释规范

- 类注释**必须**包含类的功能说明
- 方法注释**必须**包含参数类型、返回值类型和功能说明
- 复杂逻辑**必须**添加注释说明

## AI决策规范

### 优先级判断

在开发过程中，遵循以下优先级：
1. 插件开发规范 > 个人编码习惯
2. 现有项目风格 > 通用最佳实践
3. 兼容性 > 新特性使用

### 模糊情况处理

- 当遇到命名冲突时，**优先**在类名前添加模块前缀
- 当遇到API路由冲突时，**优先**修改新添加的API路由
- 当需要添加新功能时，**优先**考虑使用事件机制而非直接修改现有代码

## 禁止事项

- **禁止**直接修改核心代码（/app目录）
- **禁止**在插件中使用全局函数（必须使用命名空间）
- **禁止**在插件中引入不必要的第三方依赖
- **禁止**在插件中使用不兼容的PHP特性
- **禁止**忽略SQL注入和XSS攻击防护
- **禁止**提交未经测试的代码 