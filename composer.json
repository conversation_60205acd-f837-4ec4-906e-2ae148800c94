{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "6.0.8", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "topthink/think-view": "^1.0", "liliuwei/thinkphp-jump": "^1.3", "topthink/think-captcha": "^3.0", "overtrue/wechat": "4.2.11", "phpoffice/phpexcel": "^1.8", "overtrue/easy-sms": "^1.1", "php-curl-class/php-curl-class": "^9.4", "phpmailer/phpmailer": "^6.1", "intervention/image": "^2.5", "nesbot/carbon": "^2.27", "ext-json": "*", "workerman/gatewayclient": "3.0.*", "easy-task/easy-task": "^2.4", "sentry/sdk": "2.2.0", "guzzlehttp/guzzle": "6.5.*", "topthink/think-queue": "^3.0", "aliyuncs/oss-sdk-php": "^2.4", "tencentcloud/captcha": "^3.0", "laoqianjunzi/bankcard": "^1.2"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0", "phpunit/phpunit": "^7.5"}, "autoload": {"psr-4": {"app\\": "app", "addon\\": "addon", "extend\\": "extend"}, "psr-0": {"": "extend/"}, "classmap": ["vendor/composer/InstalledVersions.php"]}, "autoload-dev": {"psr-4": {"tests\\": "tests"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}}}