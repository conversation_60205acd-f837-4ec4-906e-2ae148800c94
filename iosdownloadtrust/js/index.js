/**
 * 加载提示图标
 * @param isShow {Boolean} 是否显示
 */
function loading(isShow){
    var loadingTag=document.getElementsByClassName('loading')[0];
    if(isShow){
        loadingTag.style.display='block';
    }else{
        loadingTag.style.display='none';
    }
}

/**
 * 弱提示
 * @param tip {String} 提示文本
 */
function weakHint(tip){
    var weakHintTag=document.getElementsByClassName('weak-hint')[0];
    if(getComputedStyle(weakHintTag).display=='block'){
        weakHintTag.innerHTML=tip;
    }else{
        weakHintTag.innerHTML=tip;
        weakHintTag.style.display='block';
        setTimeout(function () {
            weakHintTag.style.display='none';
        },3000)
    }
}
window.onload=function () {
    var  u = navigator.userAgent || window.navigator.userAgent;
    var  ua = u.toLowerCase();
    var isAndroid = /(android|nexus)/i.test(ua); //安卓家族
    var isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族
    var isWeChat = ua.match(/MicroMessenger/i) == "micromessenger";
    var isQQ = ua.indexOf(' qq')>-1;
    if(isWeChat || isQQ){
        document.getElementById('android-download').addEventListener('click',function (event) {
            var tip=document.getElementById('tip');
            if(isAndroid){
                tip.firstElementChild.src='img/android-tip.png'
            }
            tip.style.display="block";
            event.preventDefault();
        })
        document.getElementById('ios-download').addEventListener('click',function (event) {
            var tip=document.getElementById('tip');
            if(isAndroid){
                tip.firstElementChild.src='img/android-tip.png'
            }
            tip.style.display="block";
            event.preventDefault();
        })
    }else if(isOnApp){
        document.getElementById('android-download').addEventListener('click',function (event) {
            var protocol="xm://xianmai";
            if(isYouPin){
                protocol="yp://youpin";
            }
            var url=protocol+"?openBrowser="+window.location.href
            schemeGo(url)
            event.preventDefault();
        })
        document.getElementById('ios-download').addEventListener('click',function (event) {
            var protocol="xm://xianmai";
            if(isYouPin){
                protocol="yp://youpin";
            }
            var url=protocol+"?openBrowser="+window.location.href
            schemeGo(url)
            event.preventDefault();
        })
    } else{
        if(isIOS){
            document.getElementById('android-download').addEventListener('click',function (event) {
                weakHint("请点击iPhone下载");
                event.preventDefault();
            })
        }
        if(isAndroid){
            document.getElementById('ios-download').addEventListener('click',function (event) {
                weakHint("请点击Android下载");
                event.preventDefault();
            })
        }
    }
    var host=location.origin;
    var androidUrl=host+"/upload/common/app/XMshop-Android.apk";
    var iosUrl="itms-services://?action=download-manifest&url="+host+"/iosdownloadtrust/manifest.plist";
    document.getElementById('android-download').setAttribute('href',androidUrl);
    document.getElementById('ios-download').setAttribute('href',iosUrl);

    // if(XMLHttpRequest || window.XMLHttpRequest){
    //     loading(true);
    //     var request=new XMLHttpRequest();
    //     var host=location.origin;
    //     var url=host+'/api/downloadApp';
    //     request.onreadystatechange=function () {
    //         if(request.readyState==4){
    //             if(request.status==200){
    //                 if(request.response instanceof Object){
    //                     if(request.response.code!=1000){
    //                         alert(request.response.message);
    //                     }else{
    //                         var androidUrl=request.response.data.android;
    //                         var iosUrl=request.response.data.ios;
    //                         document.getElementById('android-download').setAttribute('href',androidUrl);
    //                         document.getElementById('ios-download').setAttribute('href',iosUrl);
    //                     }
    //                 }else{
    //                     alert("获取下载地址失败，数据格式错误");
    //                 }
    //             }else{
    //                 alert("获取下载地址失败");
    //             }
    //             loading(false);
    //         }
    //     }
    //     request.open('GET',url,true);
    //     request.responseType='json';
    //     request.send(null);
    // }else{
    //     alert("你的浏览器不支持XMLHttpRequest对象");
    // }
}
