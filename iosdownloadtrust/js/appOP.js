
//获取访问的user-agent
var ua = navigator.userAgent.toLowerCase() || window.navigator.userAgent.toLowerCase();
//判断user-agent
var isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族
var isAndroid = /(android|nexus)/i.test(ua); //安卓家族
var isXianMai=/xianmai/i.test(ua); //是否在app里面
var isYouPin=/youpin/i.test(ua)
var isOnApp = isXianMai || isYouPin;





// iOS与JS桥接方法
function setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
        return callback(WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
        return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    var WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'https://__bridge_loaded__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function () {
        document.documentElement.removeChild(WVJBIframe)
    }, 0)
}


/**
 * 跳转到app原生页面
 * @param appUrl {string} app原生页面url
 */
function schemeGo(appUrl) {
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('handleScheme', {'scheme':appUrl}, function(response) {
            });
        });
    }else if(isAndroid){
        window.android.handleScheme(JSON.stringify({'scheme':appUrl}));
    }
}
