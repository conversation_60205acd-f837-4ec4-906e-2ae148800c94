html,body{
    margin: 0;
    padding: 0;
}
.trust{
    width: 100%;
    height: auto;
    position: relative;
}
.bg{
    width: 100%;
    height: auto;
    display: block;
}
#android-download{
    width: 18.34375rem;
    height: 5.6875rem;
    position: absolute;
    top: 10.21875rem;
    left: 50%;
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}
#android-download img{
    width: 100%;
    height: 100%;
}
#ios-download{
    width: 18.34375rem;
    height: 5.6875rem;
    position: absolute;
    top: 15.90625rem;
    left: 50%;
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}
#ios-download img{
    width: 100%;
    height: 100%;
}

.tip{
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0,0,0,0.5);
    width: 100vw;
    height: 100vh;
    display: none;
}
.tip img{
    width: 100%;
    height: auto;
    display: block;
}

.loading{
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0,0,0,0.5);
    width: 100vw;
    height: 100vh;
    display: none;
}
.loading img{
    width: 2.5rem;
    height: 2.5rem;
    position: absolute;
    left: 50%;
    top: 50%;
    -ms-transform: translate(-50%,-50%);
    -moz-transform: translate(-50%,-50%);
    -o-transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
}
.weak-hint{
    margin: 0;
    position: fixed;
    left: 50%;
    top: 50%;
    -ms-transform: translate(-50%,-50%);
    -moz-transform: translate(-50%,-50%);
    -o-transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background:rgba(0,0,0,0.7);
    border-radius:0.625rem;
    padding: 0.8125rem 1.5rem;
    font-size:0.875rem;
    font-weight:500;
    color:rgba(255,255,255,1);
    display: none;
}